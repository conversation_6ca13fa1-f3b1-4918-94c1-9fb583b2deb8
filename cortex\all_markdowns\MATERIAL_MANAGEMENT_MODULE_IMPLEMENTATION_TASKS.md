# Material Management Module Implementation Tasks

This document outlines the complete implementation plan for converting the legacy ASP.NET Material Management module to Django. The module contains 110 files organized into 8 functional groups that need to be systematically converted.

## Overview
- **Total Files**: 110 ASP.NET files (.aspx/.aspx.cs pairs + Web.config files)
- **Django App**: `material_management/`
- **Implementation Strategy**: Convert each functional group as a cohesive unit
- **Priority**: Start with supplier management foundation, then core procurement workflow

## Module Characteristics
- **Comprehensive Procurement System**: End-to-end procurement and supplier management
- **Dual Requisition System**: Separate workflows for material (PR) and service (SPR) requisitions
- **Multi-Level Approval**: Sophisticated approval hierarchy (Check → Approve → Authorize)
- **Rate Management Focus**: Advanced pricing and rate locking mechanisms
- **Vendor Performance Tracking**: Comprehensive supplier evaluation and rating
- **Integration Heavy**: Seamless workflow from requisition to purchase order
- **Compliance Ready**: VAT reporting and regulatory compliance features

---

## Task Group 1: Supplier & Vendor Management
**Priority**: HIGH - Foundation for all procurement activities
**Files**: 18 files (9 .aspx + 9 .aspx.cs)

### Files to Convert:
**Masters:**
- `Module/MaterialManagement/Masters/SupplierMaster_New.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Masters/SupplierMaster_Edit.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Masters/SupplierMaster_Edit_Details.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Masters/BusinessNature.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Masters/BusinessType.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Masters/ServiceCoverage.aspx` + `.aspx.cs`

**Transactions:**
- `Module/MaterialManagement/Transactions/PO_Edit_Supplier.aspx` + `.aspx.cs`

**Reports:**
- `Module/MaterialManagement/Reports/VendorRating.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Reports/OverallRating.aspx` + `.aspx.cs`

### Implementation Tasks:
- [ ] **Task 1.1**: Create Supplier Management models in `material_management/models.py`
  - `Supplier` (comprehensive supplier master data)
  - `SupplierContact` (multiple contact persons per supplier)
  - `SupplierAddress` (billing, shipping, registered addresses)
  - `BusinessNature` (supplier business nature classification)
  - `BusinessType` (supplier business type categories)
  - `ServiceCoverage` (geographical/service coverage areas)
  - `SupplierDocument` (certifications, licenses, attachments)
  - `SupplierBankDetails` (payment information)
  - `VendorRating` (performance rating records)
  - `SupplierQualification` (qualification status and history)
- [ ] **Task 1.2**: Create Supplier forms with comprehensive validation
  - `SupplierForm` with address and contact management
  - `SupplierContactFormSet` for multiple contacts
  - `SupplierAddressFormSet` for multiple addresses
  - `BusinessNatureForm` with validation rules
  - `BusinessTypeForm` with hierarchy support
  - `ServiceCoverageForm` with geographic validation
  - `VendorRatingForm` with scoring algorithms
- [ ] **Task 1.3**: Implement Supplier CRUD views
  - `SupplierListView` with advanced filtering and search
  - `SupplierCreateView` with multi-step form wizard
  - `SupplierUpdateView` with tabbed interface
  - `SupplierDetailView` with comprehensive supplier profile
  - `SupplierDeleteView` with dependency checking
  - `BusinessNatureListView` / `CreateView` / `UpdateView`
  - `BusinessTypeListView` / `CreateView` / `UpdateView`
  - `ServiceCoverageListView` / `CreateView` / `UpdateView`
- [ ] **Task 1.4**: Add supplier qualification workflow
  - New → Under Evaluation → Qualified → Active → Suspended → Blacklisted
  - Multi-criteria qualification assessment
  - Document verification and approval process
  - Qualification renewal and periodic review
- [ ] **Task 1.5**: Create vendor rating system
  - Performance scoring algorithms (Quality, Delivery, Price, Service)
  - Weighted rating calculations
  - Historical rating trends and analysis
  - Automated rating updates from transaction data
  - Rating-based supplier categorization (A/B/C suppliers)
- [ ] **Task 1.6**: Add supplier performance tracking
  - Delivery performance metrics
  - Quality performance indicators
  - Price competitiveness analysis
  - Service level agreement (SLA) monitoring
  - Penalty and incentive tracking
- [ ] **Task 1.7**: Implement supplier communication system
  - Automated notifications for rating changes
  - Supplier portal integration points
  - Document sharing and collaboration
  - Tender invitation system
- [ ] **Task 1.8**: Create supplier analytics dashboard
  - Supplier performance dashboard
  - Rating trend analysis
  - Supplier comparison matrices
  - Risk assessment indicators
  - Supplier spend analysis
- [ ] **Task 1.9**: Add supplier compliance management
  - Regulatory compliance tracking
  - Certificate expiry monitoring
  - Audit trail maintenance
  - Compliance score calculation
- [ ] **Task 1.10**: Create supplier templates with HTMX
  - Multi-tab supplier management interface
  - Dynamic contact and address management
  - Real-time rating calculations
  - Interactive supplier dashboard
  - Mobile-responsive supplier directory
- [ ] **Task 1.11**: Add supplier URL patterns
- [ ] **Task 1.12**: Write comprehensive supplier tests

---

## Task Group 2: Purchase Requisition (PR) Management
**Priority**: HIGH - Core material requisition workflow
**Files**: 10 files (5 .aspx + 5 .aspx.cs)

### Files to Convert:
**Transactions:**
- `Module/MaterialManagement/Transactions/PR_New.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/PR_New_Details.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/PR_Edit.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/PR_Edit_Details.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/PR_Items.aspx` + `.aspx.cs`

### Implementation Tasks:
- [ ] **Task 2.1**: Create Purchase Requisition models
  - `PurchaseRequisition` (PR header with approval workflow)
  - `PRLineItem` (individual items in requisition)
  - `PRApproval` (approval history and status)
  - `PRBudgetAllocation` (budget verification and allocation)
  - `RequirementSource` (project/department/work order linking)
  - `PRTemplate` (standard requisition templates)
- [ ] **Task 2.2**: Create PR forms with validation
  - `PurchaseRequisitionForm` with budget validation
  - `PRLineItemFormSet` for multiple items
  - `PRApprovalForm` with approval workflow
  - Dynamic item selection with autocomplete
  - Budget availability checking
- [ ] **Task 2.3**: Implement PR CRUD views
  - `PRListView` with status filtering and search
  - `PRCreateView` with item selection wizard
  - `PRUpdateView` with line item management
  - `PRDetailView` with approval history
  - `PRApprovalView` for approval actions
  - `PRCopyView` for copying existing PRs
- [ ] **Task 2.4**: Add PR workflow management
  - Draft → Submitted → Department Approval → Finance Approval → Approved → Converted to PO
  - Role-based approval routing
  - Escalation for delayed approvals
  - Parallel approval for multiple approvers
  - Conditional approval based on amount thresholds
- [ ] **Task 2.5**: Integrate with Budget system
  - Real-time budget availability checking
  - Budget reservation upon approval
  - Budget release on PR cancellation
  - Multi-year budget allocation support
- [ ] **Task 2.6**: Add PR analytics and tracking
  - PR turnaround time analysis
  - Approval bottleneck identification
  - Requester performance metrics
  - Item demand pattern analysis
- [ ] **Task 2.7**: Create PR template system
  - Standard PR templates by department/project
  - Template-based quick PR creation
  - Template versioning and approval
  - Usage analytics for templates
- [ ] **Task 2.8**: Add PR integration points
  - Link to project management system
  - Integration with inventory for stock checking
  - Connection to MRP planning system
  - Work order requirement integration
- [ ] **Task 2.9**: Create PR templates with HTMX
  - Dynamic item addition/removal
  - Real-time budget checking
  - Approval workflow visualization
  - PR comparison and analysis views
- [ ] **Task 2.10**: Add PR URL patterns
- [ ] **Task 2.11**: Write PR functionality tests

---

## Task Group 3: Service Purchase Requisition (SPR) Management
**Priority**: HIGH - Service procurement workflow
**Files**: 18 files (9 .aspx + 9 .aspx.cs)

### Files to Convert:
**Transactions:**
- `Module/MaterialManagement/Transactions/SPR_New.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/SPR_Edit.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/SPR_Edit_Details.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/SPR_Edit_Details_Item.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/SPR_Check.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/SPR_Approve.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/SPR_Authorize.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/SPR_Clearance.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/SPR_NoCode.aspx` + `.aspx.cs`

### Implementation Tasks:
- [ ] **Task 3.1**: Create Service Purchase Requisition models
  - `ServicePurchaseRequisition` (SPR header)
  - `SPRServiceItem` (service items with specifications)
  - `SPRApprovalWorkflow` (multi-stage approval process)
  - `ServiceSpecification` (detailed service requirements)
  - `SPRClearance` (clearance documentation)
  - `ServiceCategory` (service classification)
  - `ServiceDeliverable` (expected outcomes)
- [ ] **Task 3.2**: Create SPR forms with validation
  - `ServicePurchaseRequisitionForm` with service validation
  - `SPRServiceItemFormSet` for multiple services
  - `SPRApprovalForm` with approval criteria
  - `SPRClearanceForm` with completion verification
  - Service specification builder interface
- [ ] **Task 3.3**: Implement SPR CRUD views
  - `SPRListView` with workflow status filtering
  - `SPRCreateView` with service item wizard
  - `SPRUpdateView` with service detail management
  - `SPRDetailView` with workflow visualization
  - `SPRCheckView` for technical review
  - `SPRApprovalView` for management approval
  - `SPRAuthorizationView` for final authorization
  - `SPRClearanceView` for completion processing
- [ ] **Task 3.4**: Add SPR workflow engine
  - Draft → Technical Check → Department Approval → Management Authorization → Clearance → Converted to PO
  - Service-specific approval criteria
  - Technical competency-based routing
  - Compliance checking for regulated services
  - Vendor prequalification verification
- [ ] **Task 3.5**: Create service specification system
  - Service specification templates
  - Technical requirement definition
  - Service level agreement (SLA) specification
  - Deliverable definition and tracking
  - Performance metric establishment
- [ ] **Task 3.6**: Add SPR compliance management
  - Regulatory compliance checking
  - Service provider qualification verification
  - Contract term compliance
  - Risk assessment integration
- [ ] **Task 3.7**: Implement SPR analytics
  - Service procurement cycle time analysis
  - Service provider performance tracking
  - Cost analysis by service category
  - SLA compliance monitoring
- [ ] **Task 3.8**: Add SPR integration features
  - Link to vendor qualification system
  - Integration with contract management
  - Connection to service delivery tracking
  - Budget verification for services
- [ ] **Task 3.9**: Create SPR templates with HTMX
  - Service-specific forms and workflows
  - Dynamic specification builder
  - Real-time compliance checking
  - Workflow progress visualization
- [ ] **Task 3.10**: Add SPR URL patterns
- [ ] **Task 3.11**: Write SPR functionality tests

---

## Task Group 4: Purchase Order (PO) Management
**Priority**: HIGH - Core procurement execution
**Files**: 22 files (11 .aspx + 11 .aspx.cs)

### Files to Convert:
**Transactions:**
- `Module/MaterialManagement/Transactions/PO_New.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/PO_Edit.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/PO_Edit_Details.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/PO_Edit_Details_PO_Grid.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/PO_Edit_Details_PO_Select.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/PO_Check.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/PO_Approve.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/PO_Authorize.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/PO_Error.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/PO_PR_ItemGrid.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/PO_PR_ItemSelect.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/PO_PR_Items.aspx` + `.aspx.cs`

### Implementation Tasks:
- [ ] **Task 4.1**: Create Purchase Order models
  - `PurchaseOrder` (PO header with comprehensive details)
  - `POLineItem` (individual items with pricing and delivery)
  - `POTermsConditions` (terms, payment conditions, delivery terms)
  - `POApprovalWorkflow` (approval history and routing)
  - `POAmendment` (change orders and revisions)
  - `PODeliverySchedule` (delivery milestones and tracking)
  - `POInvoiceMatching` (three-way matching with GRN and invoice)
  - `POClosure` (PO completion and closure records)
- [ ] **Task 4.2**: Create PO forms with validation
  - `PurchaseOrderForm` with supplier and terms validation
  - `POLineItemFormSet` for multiple items
  - `POTermsConditionsForm` with legal terms
  - `POApprovalForm` with approval criteria
  - `POAmendmentForm` with change tracking
  - Dynamic supplier selection with rate integration
- [ ] **Task 4.3**: Implement PO CRUD views
  - `POListView` with advanced filtering and status tracking
  - `POCreateView` with PR/SPR conversion wizard
  - `POUpdateView` with amendment tracking
  - `PODetailView` with comprehensive PO information
  - `POCheckView` for technical and commercial review
  - `POApprovalView` for management approval
  - `POAuthorizationView` for final authorization
  - `POAmendmentView` for change order processing
  - `POClosureView` for PO completion
- [ ] **Task 4.4**: Add PO workflow management
  - Draft → Technical Check → Commercial Review → Approval → Authorization → Issued → Delivered → Closed
  - Amount-based approval routing
  - Vendor-specific approval requirements
  - Emergency PO fast-track processing
  - Amendment approval workflow
- [ ] **Task 4.5**: Create PR/SPR to PO conversion system
  - Automated PO creation from approved PR/SPR
  - Multi-PR consolidation into single PO
  - Partial PO creation with remaining PR balance
  - Cross-reference tracking between PR/SPR and PO
- [ ] **Task 4.6**: Add PO pricing and rate integration
  - Rate contract integration
  - Dynamic pricing based on quantity breaks
  - Currency conversion and hedging
  - Price comparison and negotiation tracking
- [ ] **Task 4.7**: Implement PO delivery management
  - Delivery schedule planning and tracking
  - Milestone-based deliveries
  - Delivery performance monitoring
  - Expediting and follow-up system
- [ ] **Task 4.8**: Add PO compliance and risk management
  - Contract terms compliance checking
  - Legal review requirements
  - Risk assessment integration
  - Insurance and guarantee management
- [ ] **Task 4.9**: Create PO amendment system
  - Change order processing
  - Price revision management
  - Delivery schedule changes
  - Scope modification tracking
  - Amendment approval workflow
- [ ] **Task 4.10**: Add PO analytics and reporting
  - PO cycle time analysis
  - Vendor performance by PO
  - Cost savings tracking
  - Contract utilization analysis
- [ ] **Task 4.11**: Create PO templates with HTMX
  - Responsive PO management interface
  - Dynamic item grid with real-time calculations
  - Workflow progress visualization
  - Amendment comparison views
  - Mobile-friendly PO approval interface
- [ ] **Task 4.12**: Add PO URL patterns
- [ ] **Task 4.13**: Write comprehensive PO tests

---

## Task Group 5: Purchase Order-Requisition Integration
**Priority**: MEDIUM - Workflow integration
**Files**: 8 files (4 .aspx + 4 .aspx.cs)

### Files to Convert:
**Transactions:**
- `Module/MaterialManagement/Transactions/PO_SPR_ItemGrid.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/PO_SPR_ItemSelect.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Transactions/PO_SPR_Items.aspx` + `.aspx.cs`
- (PR to PO files already covered in Task Group 4)

### Implementation Tasks:
- [ ] **Task 5.1**: Create PR-PO Integration models
  - `PRPOMapping` (requisition to purchase order mapping)
  - `SPRPOMapping` (service requisition to purchase order mapping)
  - `ConsolidationRule` (rules for consolidating multiple PRs into PO)
  - `ConversionHistory` (audit trail of PR/SPR to PO conversions)
- [ ] **Task 5.2**: Create integration forms
  - `PRConversionForm` for PR to PO conversion
  - `SPRConversionForm` for SPR to PO conversion
  - `ConsolidationForm` for multi-PR consolidation
  - Item selection and modification forms
- [ ] **Task 5.3**: Implement integration views
  - `PRToPOConversionView` - convert approved PRs to PO
  - `SPRToPOConversionView` - convert approved SPRs to PO
  - `ConsolidationView` - consolidate multiple PRs
  - `ConversionHistoryView` - track conversion history
  - `PendingConversionView` - view pending conversions
- [ ] **Task 5.4**: Add conversion workflow engine
  - Automatic PO creation from approved PR/SPR
  - Intelligent consolidation based on supplier, delivery date, and terms
  - Partial conversion with remaining balance tracking
  - Exception handling for conversion failures
- [ ] **Task 5.5**: Create conversion rules engine
  - Configurable consolidation rules
  - Automatic supplier selection based on rates
  - Delivery date optimization
  - Terms and conditions standardization
- [ ] **Task 5.6**: Add conversion validation system
  - Budget availability verification
  - Supplier qualification checking
  - Rate contract validation
  - Delivery feasibility assessment
- [ ] **Task 5.7**: Create conversion templates
  - PR/SPR to PO conversion interfaces
  - Consolidation planning views
  - Conversion history tracking
- [ ] **Task 5.8**: Add integration URL patterns
- [ ] **Task 5.9**: Write integration tests

---

## Task Group 6: Rate Management & Pricing
**Priority**: HIGH - Pricing and contract management
**Files**: 14 files (7 .aspx + 7 .aspx.cs)

### Files to Convert:
**Masters:**
- `Module/MaterialManagement/Masters/RateSet.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Masters/RateSet_details.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Masters/Buyer.aspx` + `.aspx.cs`

**Transactions:**
- `Module/MaterialManagement/Transactions/RateLockUnLock.aspx` + `.aspx.cs`

**Reports:**
- `Module/MaterialManagement/Reports/RateLockUnlock.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Reports/RateLockUnlock_Details.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Reports/RateRegister.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Reports/RateRegister_Details.aspx` + `.aspx.cs`

### Implementation Tasks:
- [ ] **Task 6.1**: Create Rate Management models
  - `RateContract` (rate agreements with suppliers)
  - `RateItem` (item-specific rates with validity periods)
  - `RateTier` (quantity-based pricing tiers)
  - `RateLock` (rate locking for specific periods/orders)
  - `RateRevision` (rate change history and approvals)
  - `Buyer` (buyer information for rate negotiations)
  - `RateComparison` (competitive rate analysis)
  - `PriceEscalation` (automatic price adjustment formulas)
- [ ] **Task 6.2**: Create rate management forms
  - `RateContractForm` with validity and terms
  - `RateItemFormSet` for multiple items
  - `RateTierForm` for quantity-based pricing
  - `RateLockForm` with lock period and conditions
  - `BuyerForm` with negotiation authority
  - Rate comparison and analysis forms
- [ ] **Task 6.3**: Implement rate management views
  - `RateContractListView` with filtering and search
  - `RateContractCreateView` with multi-step wizard
  - `RateContractUpdateView` with revision tracking
  - `RateItemDetailView` with pricing history
  - `RateLockListView` with lock status tracking
  - `RateLockCreateView` with validation
  - `BuyerListView` / `CreateView` / `UpdateView`
  - `RateComparisonView` for competitive analysis
- [ ] **Task 6.4**: Add rate validation system
  - Rate overlap detection and resolution
  - Validity period checking
  - Authorization level verification
  - Market rate comparison alerts
- [ ] **Task 6.5**: Create rate locking mechanism
  - Temporary rate locks for quotations
  - Contract-based rate locks
  - Emergency rate lock override
  - Automatic lock expiry handling
- [ ] **Task 6.6**: Add rate negotiation support
  - Negotiation history tracking
  - Competitive bidding support
  - Rate benchmarking tools
  - Negotiation approval workflow
- [ ] **Task 6.7**: Implement rate analytics
  - Price trend analysis
  - Supplier rate comparison
  - Cost savings calculation
  - Rate utilization metrics
- [ ] **Task 6.8**: Create rate escalation system
  - Formula-based price adjustments
  - Index-linked pricing
  - Automatic escalation calculations
  - Escalation approval workflow
- [ ] **Task 6.9**: Add rate compliance monitoring
  - Rate contract compliance checking
  - Usage tracking against contracts
  - Variance analysis and alerts
  - Contract utilization optimization
- [ ] **Task 6.10**: Create rate management templates
  - Rate contract management interface
  - Interactive rate comparison tools
  - Rate lock management dashboard
  - Price trend visualization
- [ ] **Task 6.11**: Add rate management URL patterns
- [ ] **Task 6.12**: Write rate management tests

---

## Task Group 7: Purchase Reporting & Analytics
**Priority**: MEDIUM - Business intelligence and compliance
**Files**: 14 files (7 .aspx + 7 .aspx.cs)

### Files to Convert:
**Reports:**
- `Module/MaterialManagement/Reports/Purchase_Reprt.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Reports/PurchaseVAT_Register.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Reports/InwardOutwardRegister.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Reports/Search.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Reports/SearchViewField.aspx` + `.aspx.cs`

### Implementation Tasks:
- [ ] **Task 7.1**: Create Purchase Reporting models
  - `PurchaseReport` (report templates and configurations)
  - `PurchaseAnalytics` (analytical calculations and metrics)
  - `ReportSchedule` (automated report generation)
  - `ReportDistribution` (report sharing and notifications)
  - `PurchaseKPI` (key performance indicators)
- [ ] **Task 7.2**: Create reporting forms
  - `PurchaseReportForm` with date range and filters
  - `VATRegisterForm` with tax period selection
  - `InwardOutwardRegisterForm` with movement filters
  - `SearchForm` with advanced search criteria
- [ ] **Task 7.3**: Implement reporting views
  - `PurchaseReportView` with customizable filters
  - `PurchaseVATRegisterView` for tax compliance
  - `InwardOutwardRegisterView` for movement tracking
  - `PurchaseAnalyticsView` for trend analysis
  - `PurchaseDashboardView` for executive summary
  - `AdvancedSearchView` for data exploration
- [ ] **Task 7.4**: Add purchase analytics engine
  - Spend analysis by category, supplier, and time period
  - Purchase performance metrics calculation
  - Cost savings identification and tracking
  - Procurement cycle time analysis
  - Supplier performance scorecards
- [ ] **Task 7.5**: Create compliance reporting
  - VAT/GST purchase register with tax calculations
  - Regulatory compliance reports
  - Audit trail reports
  - Contract compliance monitoring
- [ ] **Task 7.6**: Add advanced search capabilities
  - Multi-criteria search across all purchase data
  - Saved search templates
  - Quick search with autocomplete
  - Export search results to various formats
- [ ] **Task 7.7**: Implement report automation
  - Scheduled report generation
  - Automated report distribution
  - Report alerting and notifications
  - Self-service reporting tools
- [ ] **Task 7.8**: Create interactive dashboards
  - Real-time purchase dashboards
  - KPI monitoring and alerts
  - Drill-down capabilities
  - Mobile-responsive analytics
- [ ] **Task 7.9**: Add report export capabilities
  - PDF, Excel, CSV export options
  - Formatted report templates
  - Custom report layouts
  - Bulk export functionality
- [ ] **Task 7.10**: Create reporting templates
  - Interactive report interfaces
  - Dashboard visualization components
  - Search and filter interfaces
  - Export and sharing tools
- [ ] **Task 7.11**: Add reporting URL patterns
- [ ] **Task 7.12**: Write reporting functionality tests

---

## Task Group 8: Material Planning & Forecasting
**Priority**: MEDIUM - Strategic planning and demand management
**Files**: 4 files (2 .aspx + 2 .aspx.cs)

### Files to Convert:
**Reports:**
- `Module/MaterialManagement/Reports/MaterialForecasting.aspx` + `.aspx.cs`
- `Module/MaterialManagement/Reports/ProjectSummary_Shortage.aspx` + `.aspx.cs`

### Implementation Tasks:
- [ ] **Task 8.1**: Create Material Planning models
  - `MaterialForecast` (demand forecasting records)
  - `ForecastMethod` (forecasting algorithm configuration)
  - `DemandPattern` (historical demand analysis)
  - `ShortageAnalysis` (shortage identification and tracking)
  - `ProjectMaterialPlan` (project-specific material planning)
  - `PlanningParameter` (planning configuration)
  - `SafetyStock` (safety stock calculations)
- [ ] **Task 8.2**: Create planning forms
  - `MaterialForecastForm` with forecasting parameters
  - `ShortageAnalysisForm` with shortage criteria
  - `ProjectPlanningForm` with project filters
  - Planning parameter configuration forms
- [ ] **Task 8.3**: Implement planning views
  - `MaterialForecastView` with demand projections
  - `ShortageAnalysisView` with shortage identification
  - `ProjectMaterialPlanView` for project planning
  - `PlanningDashboardView` for planning overview
  - `DemandAnalysisView` for demand pattern analysis
- [ ] **Task 8.4**: Add forecasting engine
  - Multiple forecasting algorithms (moving average, exponential smoothing, etc.)
  - Seasonal demand pattern recognition
  - Trend analysis and projection
  - Forecast accuracy measurement and improvement
- [ ] **Task 8.5**: Create shortage analysis system
  - Real-time shortage identification
  - Critical item monitoring
  - Lead time analysis and optimization
  - Alternative sourcing recommendations
- [ ] **Task 8.6**: Add project-based planning
  - Project material requirement planning
  - Resource allocation optimization
  - Timeline-based material scheduling
  - Cross-project material sharing
- [ ] **Task 8.7**: Implement planning optimization
  - Inventory optimization algorithms
  - EOQ (Economic Order Quantity) calculations
  - Supplier consolidation opportunities
  - Cost optimization recommendations
- [ ] **Task 8.8**: Create planning analytics
  - Planning accuracy metrics
  - Forecast vs. actual analysis
  - Planning performance dashboards
  - Exception reporting and alerts
- [ ] **Task 8.9**: Create planning templates
  - Forecasting interfaces with visualization
  - Shortage monitoring dashboards
  - Project planning tools
  - Planning analytics views
- [ ] **Task 8.10**: Add planning URL patterns
- [ ] **Task 8.11**: Write planning functionality tests

---

## Implementation Guidelines

### Technical Requirements:
1. **Models**: Use existing database with `managed = False`
2. **Views**: Only class-based views (ListView, CreateView, UpdateView, DeleteView)
3. **Frontend**: Django templates + HTMX + Alpine.js + Tailwind CSS + Chart.js for analytics
4. **Forms**: Include CSRF tokens, proper validation, dynamic form handling
5. **Authentication**: All views require `LoginRequiredMixin`
6. **Testing**: Both unit tests and Playwright end-to-end tests

### Implementation Order:
1. **Phase 1**: Task Groups 1, 6 (Supplier Foundation & Rate Management)
2. **Phase 2**: Task Groups 2, 3, 4 (Core Procurement Workflow)
3. **Phase 3**: Task Groups 5, 7 (Integration & Reporting)
4. **Phase 4**: Task Group 8 (Planning & Forecasting)

### Key Models to Create:
```python
# Core Material Management Models
class Supplier(models.Model):
    # Comprehensive supplier master data
    
class PurchaseRequisition(models.Model):
    # Material requisition workflow
    
class ServicePurchaseRequisition(models.Model):
    # Service requisition workflow
    
class PurchaseOrder(models.Model):
    # Purchase order management
    
class RateContract(models.Model):
    # Rate and pricing management
    
class VendorRating(models.Model):
    # Supplier performance tracking
    
class MaterialForecast(models.Model):
    # Demand planning and forecasting
```

### Procurement Workflow:
```
Material Need → PR/SPR Creation → Approval Workflow → 
Rate Verification → PO Creation → PO Approval → 
Supplier Communication → Delivery Tracking → 
Receipt Verification → Invoice Matching → Payment
```

### Integration Architecture:
```
Material Management Integration Map:
├── Inventory Module → Stock Levels & GRN
├── Accounts Module → Budget & Invoice Matching
├── Quality Control Module → Supplier Quality Ratings
├── Design Module → BOM Requirements
├── Project Management Module → Project Material Plans
├── MIS Module → Analytics & Reporting
└── HR Module → Approval Workflows
```

### File Structure:
```
material_management/
├── models.py
├── forms.py
├── views/
│   ├── __init__.py
│   ├── supplier_views.py
│   ├── requisition_views.py
│   ├── purchase_order_views.py
│   ├── rate_views.py
│   └── reporting_views.py
├── workflows/
│   ├── __init__.py
│   ├── approval_engine.py
│   ├── conversion_engine.py
│   └── planning_engine.py
├── analytics/
│   ├── __init__.py
│   ├── procurement_analytics.py
│   ├── supplier_analytics.py
│   └── forecasting_engine.py
├── templates/material_management/
│   ├── suppliers/
│   ├── requisitions/
│   ├── purchase_orders/
│   ├── rates/
│   ├── reports/
│   └── partials/
├── urls.py
├── admin.py
└── tests.py
```

### Success Criteria:
- [ ] All 110 ASP.NET files successfully converted
- [ ] Complete procurement workflow automation
- [ ] Comprehensive supplier management system
- [ ] Advanced rate and pricing management
- [ ] Multi-level approval workflows
- [ ] Real-time analytics and reporting
- [ ] Material planning and forecasting
- [ ] Mobile-responsive procurement interface
- [ ] Performance optimization completed
- [ ] Security best practices implemented
- [ ] Documentation completed

### Business Benefits:
- Streamlined procurement processes
- Automated approval workflows
- Comprehensive supplier performance tracking
- Advanced rate management and cost control
- Real-time procurement analytics
- Efficient material planning and forecasting
- Compliance automation and reporting
- Mobile procurement capabilities
- Integrated supplier collaboration platform

**Total Estimated Tasks**: 102 individual implementation tasks across 8 functional groups

### Key Performance Indicators:
- Procurement cycle time reduction
- Cost savings through better supplier management
- Approval workflow efficiency
- Supplier performance improvements
- Rate contract utilization
- Material shortage reduction
- Compliance adherence rates
- User adoption and satisfaction metrics