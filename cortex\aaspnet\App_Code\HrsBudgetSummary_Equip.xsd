﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="HrsBudgetSummary_Equip" targetNamespace="http://tempuri.org/HrsBudgetSummary_Equip.xsd" xmlns:mstns="http://tempuri.org/HrsBudgetSummary_Equip.xsd" xmlns="http://tempuri.org/HrsBudgetSummary_Equip.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="HrsBudgetSummary_Equip" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="HrsBudgetSummary_Equip" msprop:Generator_DataSetName="HrsBudgetSummary_Equip">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="WoNo" msprop:Generator_UserColumnName="WoNo" msprop:Generator_ColumnVarNameInTable="columnWoNo" msprop:Generator_ColumnPropNameInRow="WoNo" msprop:Generator_ColumnPropNameInTable="WoNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ProjectTitle" msprop:Generator_UserColumnName="ProjectTitle" msprop:Generator_ColumnVarNameInTable="columnProjectTitle" msprop:Generator_ColumnPropNameInRow="ProjectTitle" msprop:Generator_ColumnPropNameInTable="ProjectTitleColumn" type="xs:string" minOccurs="0" />
              <xs:element name="EquipNo" msprop:Generator_UserColumnName="EquipNo" msprop:Generator_ColumnVarNameInTable="columnEquipNo" msprop:Generator_ColumnPropNameInRow="EquipNo" msprop:Generator_ColumnPropNameInTable="EquipNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Description" msprop:Generator_UserColumnName="Description" msprop:Generator_ColumnVarNameInTable="columnDescription" msprop:Generator_ColumnPropNameInRow="Description" msprop:Generator_ColumnPropNameInTable="DescriptionColumn" type="xs:string" minOccurs="0" />
              <xs:element name="MDBHrs" msprop:Generator_UserColumnName="MDBHrs" msprop:Generator_ColumnVarNameInTable="columnMDBHrs" msprop:Generator_ColumnPropNameInRow="MDBHrs" msprop:Generator_ColumnPropNameInTable="MDBHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="MDUHrs" msprop:Generator_UserColumnName="MDUHrs" msprop:Generator_ColumnVarNameInTable="columnMDUHrs" msprop:Generator_ColumnPropNameInRow="MDUHrs" msprop:Generator_ColumnPropNameInTable="MDUHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="MABHrs" msprop:Generator_UserColumnName="MABHrs" msprop:Generator_ColumnVarNameInTable="columnMABHrs" msprop:Generator_ColumnPropNameInRow="MABHrs" msprop:Generator_ColumnPropNameInTable="MABHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="MAUHrs" msprop:Generator_UserColumnName="MAUHrs" msprop:Generator_ColumnVarNameInTable="columnMAUHrs" msprop:Generator_ColumnPropNameInRow="MAUHrs" msprop:Generator_ColumnPropNameInTable="MAUHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="MCBHrs" msprop:Generator_UserColumnName="MCBHrs" msprop:Generator_ColumnVarNameInTable="columnMCBHrs" msprop:Generator_ColumnPropNameInRow="MCBHrs" msprop:Generator_ColumnPropNameInTable="MCBHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="MCUHrs" msprop:Generator_UserColumnName="MCUHrs" msprop:Generator_ColumnVarNameInTable="columnMCUHrs" msprop:Generator_ColumnPropNameInRow="MCUHrs" msprop:Generator_ColumnPropNameInTable="MCUHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="MTBHrs" msprop:Generator_UserColumnName="MTBHrs" msprop:Generator_ColumnVarNameInTable="columnMTBHrs" msprop:Generator_ColumnPropNameInRow="MTBHrs" msprop:Generator_ColumnPropNameInTable="MTBHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="MTUHrs" msprop:Generator_UserColumnName="MTUHrs" msprop:Generator_ColumnVarNameInTable="columnMTUHrs" msprop:Generator_ColumnPropNameInRow="MTUHrs" msprop:Generator_ColumnPropNameInTable="MTUHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="MIBHrs" msprop:Generator_UserColumnName="MIBHrs" msprop:Generator_ColumnVarNameInTable="columnMIBHrs" msprop:Generator_ColumnPropNameInRow="MIBHrs" msprop:Generator_ColumnPropNameInTable="MIBHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="MIUHrs" msprop:Generator_UserColumnName="MIUHrs" msprop:Generator_ColumnVarNameInTable="columnMIUHrs" msprop:Generator_ColumnPropNameInRow="MIUHrs" msprop:Generator_ColumnPropNameInTable="MIUHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="MTRBHrs" msprop:Generator_UserColumnName="MTRBHrs" msprop:Generator_ColumnVarNameInTable="columnMTRBHrs" msprop:Generator_ColumnPropNameInRow="MTRBHrs" msprop:Generator_ColumnPropNameInTable="MTRBHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="MTRUHrs" msprop:Generator_UserColumnName="MTRUHrs" msprop:Generator_ColumnVarNameInTable="columnMTRUHrs" msprop:Generator_ColumnPropNameInRow="MTRUHrs" msprop:Generator_ColumnPropNameInTable="MTRUHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="EDBHrs" msprop:Generator_UserColumnName="EDBHrs" msprop:Generator_ColumnVarNameInTable="columnEDBHrs" msprop:Generator_ColumnPropNameInRow="EDBHrs" msprop:Generator_ColumnPropNameInTable="EDBHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="EDUHrs" msprop:Generator_UserColumnName="EDUHrs" msprop:Generator_ColumnVarNameInTable="columnEDUHrs" msprop:Generator_ColumnPropNameInRow="EDUHrs" msprop:Generator_ColumnPropNameInTable="EDUHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="EABHrs" msprop:Generator_UserColumnName="EABHrs" msprop:Generator_ColumnVarNameInTable="columnEABHrs" msprop:Generator_ColumnPropNameInRow="EABHrs" msprop:Generator_ColumnPropNameInTable="EABHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="EAUHrs" msprop:Generator_UserColumnName="EAUHrs" msprop:Generator_ColumnVarNameInTable="columnEAUHrs" msprop:Generator_ColumnPropNameInRow="EAUHrs" msprop:Generator_ColumnPropNameInTable="EAUHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ECBHrs" msprop:Generator_UserColumnName="ECBHrs" msprop:Generator_ColumnVarNameInTable="columnECBHrs" msprop:Generator_ColumnPropNameInRow="ECBHrs" msprop:Generator_ColumnPropNameInTable="ECBHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ECUHrs" msprop:Generator_UserColumnName="ECUHrs" msprop:Generator_ColumnVarNameInTable="columnECUHrs" msprop:Generator_ColumnPropNameInRow="ECUHrs" msprop:Generator_ColumnPropNameInTable="ECUHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ETBHrs" msprop:Generator_UserColumnName="ETBHrs" msprop:Generator_ColumnVarNameInTable="columnETBHrs" msprop:Generator_ColumnPropNameInRow="ETBHrs" msprop:Generator_ColumnPropNameInTable="ETBHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ETUHrs" msprop:Generator_UserColumnName="ETUHrs" msprop:Generator_ColumnVarNameInTable="columnETUHrs" msprop:Generator_ColumnPropNameInRow="ETUHrs" msprop:Generator_ColumnPropNameInTable="ETUHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="EIBHrs" msprop:Generator_UserColumnName="EIBHrs" msprop:Generator_ColumnVarNameInTable="columnEIBHrs" msprop:Generator_ColumnPropNameInRow="EIBHrs" msprop:Generator_ColumnPropNameInTable="EIBHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="EIUHrs" msprop:Generator_UserColumnName="EIUHrs" msprop:Generator_ColumnVarNameInTable="columnEIUHrs" msprop:Generator_ColumnPropNameInRow="EIUHrs" msprop:Generator_ColumnPropNameInTable="EIUHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ETRBHrs" msprop:Generator_UserColumnName="ETRBHrs" msprop:Generator_ColumnVarNameInTable="columnETRBHrs" msprop:Generator_ColumnPropNameInRow="ETRBHrs" msprop:Generator_ColumnPropNameInTable="ETRBHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ETRUHrs" msprop:Generator_UserColumnName="ETRUHrs" msprop:Generator_ColumnVarNameInTable="columnETRUHrs" msprop:Generator_ColumnPropNameInRow="ETRUHrs" msprop:Generator_ColumnPropNameInTable="ETRUHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="MDIBHrs" msprop:Generator_UserColumnName="MDIBHrs" msprop:Generator_ColumnPropNameInRow="MDIBHrs" msprop:Generator_ColumnVarNameInTable="columnMDIBHrs" msprop:Generator_ColumnPropNameInTable="MDIBHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="MDIUHrs" msprop:Generator_UserColumnName="MDIUHrs" msprop:Generator_ColumnPropNameInRow="MDIUHrs" msprop:Generator_ColumnVarNameInTable="columnMDIUHrs" msprop:Generator_ColumnPropNameInTable="MDIUHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="EDIBHrs" msprop:Generator_UserColumnName="EDIBHrs" msprop:Generator_ColumnPropNameInRow="EDIBHrs" msprop:Generator_ColumnVarNameInTable="columnEDIBHrs" msprop:Generator_ColumnPropNameInTable="EDIBHrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="EDIUHrs" msprop:Generator_UserColumnName="EDIUHrs" msprop:Generator_ColumnPropNameInRow="EDIUHrs" msprop:Generator_ColumnVarNameInTable="columnEDIUHrs" msprop:Generator_ColumnPropNameInTable="EDIUHrsColumn" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>