{% extends "core/base.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg shadow-lg mb-6">
            <div class="px-6 py-4">
                <h1 class="text-2xl font-bold text-white">{{ title }}</h1>
                <p class="text-blue-100 mt-1">Item: {{ item.itemcode }} - {{ item.description }}</p>
            </div>
        </div>

        <form method="post" enctype="multipart/form-data" class="space-y-6">
            {% csrf_token %}
            
            <!-- Machine Details Tab -->
            <div class="bg-white rounded-lg shadow-lg">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                        <button type="button" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm tab-button active" data-tab="details">
                            Machine Details
                        </button>
                        <button type="button" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm tab-button" data-tab="spares">
                            Spare Parts
                        </button>
                        <button type="button" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm tab-button" data-tab="processes">
                            Processes
                        </button>
                    </nav>
                </div>

                <!-- Machine Details Tab Content -->
                <div id="details-tab" class="tab-content p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- Make -->
                        <div>
                            <label for="{{ form.make.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                Make <span class="text-red-500">*</span>
                            </label>
                            {{ form.make }}
                            {% if form.make.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.make.errors.0 }}</p>
                            {% endif %}
                        </div>

                        <!-- Model -->
                        <div>
                            <label for="{{ form.model.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                Model <span class="text-red-500">*</span>
                            </label>
                            {{ form.model }}
                            {% if form.model.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.model.errors.0 }}</p>
                            {% endif %}
                        </div>

                        <!-- Capacity -->
                        <div>
                            <label for="{{ form.capacity.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                Capacity <span class="text-red-500">*</span>
                            </label>
                            {{ form.capacity }}
                            {% if form.capacity.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.capacity.errors.0 }}</p>
                            {% endif %}
                        </div>

                        <!-- Purchase Date -->
                        <div>
                            <label for="{{ form.purchasedate.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                Purchase Date <span class="text-red-500">*</span>
                            </label>
                            {{ form.purchasedate }}
                            {% if form.purchasedate.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.purchasedate.errors.0 }}</p>
                            {% endif %}
                        </div>

                        <!-- Supplier Name -->
                        <div>
                            <label for="{{ form.suppliername.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                Supplier Name <span class="text-red-500">*</span>
                            </label>
                            {{ form.suppliername }}
                            {% if form.suppliername.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.suppliername.errors.0 }}</p>
                            {% endif %}
                        </div>

                        <!-- Cost -->
                        <div>
                            <label for="{{ form.cost.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                Cost <span class="text-red-500">*</span>
                            </label>
                            {{ form.cost }}
                            {% if form.cost.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.cost.errors.0 }}</p>
                            {% endif %}
                        </div>

                        <!-- Warranty Expiry Date -->
                        <div>
                            <label for="{{ form.warrantyexpirydate.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                Warranty Expiry Date <span class="text-red-500">*</span>
                            </label>
                            {{ form.warrantyexpirydate }}
                            {% if form.warrantyexpirydate.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.warrantyexpirydate.errors.0 }}</p>
                            {% endif %}
                        </div>

                        <!-- Life Date -->
                        <div>
                            <label for="{{ form.lifedate.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                Life Date <span class="text-red-500">*</span>
                            </label>
                            {{ form.lifedate }}
                            {% if form.lifedate.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.lifedate.errors.0 }}</p>
                            {% endif %}
                        </div>

                        <!-- Received Date -->
                        <div>
                            <label for="{{ form.receiveddate.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                Received Date <span class="text-red-500">*</span>
                            </label>
                            {{ form.receiveddate }}
                            {% if form.receiveddate.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.receiveddate.errors.0 }}</p>
                            {% endif %}
                        </div>

                        <!-- Put to Use -->
                        <div>
                            <label for="{{ form.puttouse.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                Put to Use <span class="text-red-500">*</span>
                            </label>
                            {{ form.puttouse }}
                            {% if form.puttouse.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.puttouse.errors.0 }}</p>
                            {% endif %}
                        </div>

                        <!-- Incharge -->
                        <div>
                            <label for="{{ form.incharge.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                In-charge <span class="text-red-500">*</span>
                            </label>
                            {{ form.incharge }}
                            {% if form.incharge.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.incharge.errors.0 }}</p>
                            {% endif %}
                        </div>

                        <!-- Location -->
                        <div>
                            <label for="{{ form.location.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                Location <span class="text-red-500">*</span>
                            </label>
                            {{ form.location }}
                            {% if form.location.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.location.errors.0 }}</p>
                            {% endif %}
                        </div>

                        <!-- Insurance -->
                        <div>
                            <label for="{{ form.insurance.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                Insurance <span class="text-red-500">*</span>
                            </label>
                            {{ form.insurance }}
                            {% if form.insurance.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.insurance.errors.0 }}</p>
                            {% endif %}
                        </div>

                        <!-- Insurance Expiry Date (conditional) -->
                        <div id="insurance-fields">
                            <label for="{{ form.insuranceexpirydate.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                Insurance Expiry Date
                            </label>
                            {{ form.insuranceexpirydate }}
                            {% if form.insuranceexpirydate.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.insuranceexpirydate.errors.0 }}</p>
                            {% endif %}
                        </div>

                        <!-- PM Days -->
                        <div>
                            <label for="{{ form.pmdays.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                Preventive Maintenance Days <span class="text-red-500">*</span>
                            </label>
                            {{ form.pmdays }}
                            {% if form.pmdays.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.pmdays.errors.0 }}</p>
                            {% endif %}
                        </div>

                        <!-- File Upload -->
                        <div class="col-span-1 md:col-span-2 lg:col-span-3">
                            <label for="{{ form.filedata.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                Upload File
                            </label>
                            {{ form.filedata }}
                            {% if form.filedata.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.filedata.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Spare Parts Tab Content -->
                <div id="spares-tab" class="tab-content p-6 hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Available Spare Items -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Available Spare Items</h3>
                            <div class="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                                <form hx-post="{% url 'machinery:add_spare_parts' %}" hx-target="#spare-parts-list">
                                    {% csrf_token %}
                                    {% for item in available_spare_items %}
                                    <div class="flex items-center justify-between py-2 border-b border-gray-200 last:border-b-0">
                                        <div class="flex items-center">
                                            <input type="checkbox" name="spare_items" value="{{ item.id }}" class="mr-3">
                                            <div>
                                                <p class="text-sm font-medium text-gray-900">{{ item.itemcode }}</p>
                                                <p class="text-xs text-gray-500">{{ item.description|truncatechars:40 }}</p>
                                            </div>
                                        </div>
                                        <div class="w-20">
                                            <input type="number" name="quantities" step="0.01" min="0.01" placeholder="Qty" 
                                                   class="w-full text-sm border-gray-300 rounded-md">
                                        </div>
                                    </div>
                                    {% endfor %}
                                    <button type="submit" class="mt-4 w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">
                                        Add Selected Items
                                    </button>
                                </form>
                            </div>
                        </div>

                        <!-- Selected Spare Parts -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Selected Spare Parts</h3>
                            <div id="spare-parts-list" class="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                                {% include 'machinery/partials/spare_parts_list.html' %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Processes Tab Content -->
                <div id="processes-tab" class="tab-content p-6 hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Available Processes -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Available Processes</h3>
                            <div class="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                                <form hx-post="{% url 'machinery:add_processes' %}" hx-target="#processes-list">
                                    {% csrf_token %}
                                    {% for process in available_processes %}
                                    <div class="flex items-center py-2 border-b border-gray-200 last:border-b-0">
                                        <input type="checkbox" name="processes" value="{{ process.id }}" class="mr-3">
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">[{{ process.symbol }}] {{ process.processname }}</p>
                                        </div>
                                    </div>
                                    {% endfor %}
                                    <button type="submit" class="mt-4 w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">
                                        Add Selected Processes
                                    </button>
                                </form>
                            </div>
                        </div>

                        <!-- Selected Processes -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Selected Processes</h3>
                            <div id="processes-list" class="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                                {% include 'machinery/partials/processes_list.html' %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex justify-between">
                    <a href="{% url 'machinery:machine_item_selection' %}" 
                       class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">
                        Cancel
                    </a>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Create Machine
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script src="{% static 'js/htmx.min.js' %}"></script>
<script>
// Tab functionality
document.addEventListener('DOMContentLoaded', function() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // Remove active class from all buttons
            tabButtons.forEach(btn => {
                btn.classList.remove('active', 'border-blue-500', 'text-blue-600');
                btn.classList.add('border-transparent', 'text-gray-500');
            });

            // Add active class to clicked button
            this.classList.add('active', 'border-blue-500', 'text-blue-600');
            this.classList.remove('border-transparent', 'text-gray-500');

            // Hide all tab contents
            tabContents.forEach(content => {
                content.classList.add('hidden');
            });

            // Show target tab content
            document.getElementById(targetTab + '-tab').classList.remove('hidden');
        });
    });
});
</script>
{% endblock %}