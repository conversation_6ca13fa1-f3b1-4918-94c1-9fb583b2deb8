<!-- accounts/templates/accounts/invoices/service_tax_invoice_list.html -->
<!-- Service Tax Invoice List Template with HTMX -->
<!-- Task Group 5: Invoicing & Billing - Service Tax Invoice List -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Service Tax Invoices - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-orange-600 to-sap-orange-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="receipt" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Service Tax Invoices</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage service tax invoices and billing</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <div class="text-right mr-4 px-4 py-2 bg-sap-gray-50 rounded-lg border border-sap-gray-200">
                    <p class="text-2xs font-medium text-sap-gray-500 uppercase tracking-wide">Total Invoices</p>
                    <p class="text-lg font-semibold text-sap-orange-600">{{ service_tax_invoices|length }}</p>
                </div>
                <a href="{% url 'accounts:service_tax_invoice_create' %}" 
                   class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    New Service Tax Invoice
                </a>
                <a href="{% url 'accounts:invoicing_dashboard' %}" 
                   class="inline-flex items-center px-4 py-2.5 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 hover:border-sap-blue-300 transition-all duration-200">
                    <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                    Back to Invoicing
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6" x-data="serviceTaxInvoiceList()">
    
    <!-- Search and Filter Section -->
    <div class="mb-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-sap-gray-800">Search & Filter</h3>
                <button type="button" @click="toggleFilters()" class="text-sap-blue-600 hover:text-sap-blue-700">
                    <i data-lucide="filter" class="w-5 h-5"></i>
                </button>
            </div>
            
            <form method="GET" class="space-y-4" x-show="showFilters" x-transition:enter="transition ease-out duration-200"
                  x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-2">Customer Name</label>
                        <input type="text" name="buyer_name" value="{{ request.GET.buyer_name|default:'' }}"
                               placeholder="Search by customer name..."
                               class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-2">Status</label>
                        <select name="status" class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
                            <option value="">All Status</option>
                            <option value="Draft" {% if request.GET.status == "Draft" %}selected{% endif %}>Draft</option>
                            <option value="Submitted" {% if request.GET.status == "Submitted" %}selected{% endif %}>Submitted</option>
                            <option value="Approved" {% if request.GET.status == "Approved" %}selected{% endif %}>Approved</option>
                            <option value="Cancelled" {% if request.GET.status == "Cancelled" %}selected{% endif %}>Cancelled</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-2">Invoice Date From</label>
                        <input type="date" name="date_from" value="{{ request.GET.date_from|default:'' }}"
                               class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-2">Invoice Date To</label>
                        <input type="date" name="date_to" value="{{ request.GET.date_to|default:'' }}"
                               class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
                    </div>
                </div>
                <div class="flex justify-end space-x-3">
                    <a href="{% url 'accounts:service_tax_invoice_list' %}" 
                       class="px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                        Clear
                    </a>
                    <button type="submit" 
                            class="px-4 py-2 bg-sap-orange-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-sap-orange-700">
                        Search
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Service Tax Invoices Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Service Tax Invoices</h3>
                <div class="text-sm text-sap-gray-600">
                    Showing {{ service_tax_invoices|length }} invoices
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Invoice No
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Invoice Date
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Customer Name
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            PO No
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Basic Amount
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Service Tax
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Total Amount
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for invoice in service_tax_invoices %}
                    <tr class="hover:bg-sap-gray-50 transition-colors duration-150">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2">
                                <a href="#" class="text-sap-blue-600 hover:text-sap-blue-900">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                </a>
                                <a href="#" class="text-sap-green-600 hover:text-sap-green-900">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </a>
                                <button type="button" class="text-sap-red-600 hover:text-sap-red-900">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900 font-mono">
                            {{ invoice.invoice_no|default:"Pending" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">
                            {{ invoice.date_of_issue_invoice|default:"-" }}
                        </td>
                        <td class="px-6 py-4 text-sm text-sap-gray-900">
                            <div class="max-w-xs truncate" title="{{ invoice.buyer_name }}">
                                {{ invoice.buyer_name|default:"-" }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900 font-mono">
                            {{ invoice.po_no|default:"-" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900 text-right">
                            {% if invoice.add_amt %}
                                ₹{{ invoice.add_amt|floatformat:2 }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900 text-right">
                            {% if invoice.service_tax %}
                                ₹{{ invoice.service_tax|floatformat:2 }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900 text-right font-medium">
                            {% if invoice.add_amt and invoice.service_tax %}
                                ₹{{ invoice.add_amt|add:invoice.service_tax|floatformat:2 }}
                            {% elif invoice.add_amt %}
                                ₹{{ invoice.add_amt|floatformat:2 }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            {% if invoice.status == "Approved" %}
                                <span class="inline-flex px-2 py-1 text-xs font-medium bg-sap-green-100 text-sap-green-800 rounded-full">
                                    {{ invoice.status }}
                                </span>
                            {% elif invoice.status == "Submitted" %}
                                <span class="inline-flex px-2 py-1 text-xs font-medium bg-sap-blue-100 text-sap-blue-800 rounded-full">
                                    {{ invoice.status }}
                                </span>
                            {% elif invoice.status == "Draft" %}
                                <span class="inline-flex px-2 py-1 text-xs font-medium bg-sap-yellow-100 text-sap-yellow-800 rounded-full">
                                    {{ invoice.status }}
                                </span>
                            {% elif invoice.status == "Cancelled" %}
                                <span class="inline-flex px-2 py-1 text-xs font-medium bg-sap-red-100 text-sap-red-800 rounded-full">
                                    {{ invoice.status }}
                                </span>
                            {% else %}
                                <span class="inline-flex px-2 py-1 text-xs font-medium bg-sap-gray-100 text-sap-gray-800 rounded-full">
                                    Draft
                                </span>
                            {% endif %}
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="9" class="px-6 py-12 text-center text-sm text-sap-gray-500">
                            <i data-lucide="receipt" class="mx-auto h-12 w-12 text-sap-gray-400 mb-4"></i>
                            <p class="text-lg font-medium text-sap-gray-900 mb-2">No Service Tax Invoices Found</p>
                            <p>Create your first service tax invoice to get started.</p>
                            <a href="{% url 'accounts:service_tax_invoice_create' %}" 
                               class="mt-4 inline-flex items-center px-4 py-2 bg-sap-orange-600 hover:bg-sap-orange-700 text-white rounded-lg transition-colors">
                                <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                New Service Tax Invoice
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="mt-6 bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="text-sm text-sap-gray-600">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ paginator.count }} results
                </div>
                <div class="flex space-x-2">
                    {% if page_obj.has_previous %}
                        <a href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            First
                        </a>
                        <a href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Previous
                        </a>
                    {% endif %}
                    
                    <span class="px-3 py-2 bg-sap-orange-50 border border-sap-orange-200 rounded-lg text-sm font-medium text-sap-orange-600">
                        Page {{ page_obj.number }} of {{ paginator.num_pages }}
                    </span>
                    
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Next
                        </a>
                        <a href="?page={{ paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Last
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

</div>

<script>
function serviceTaxInvoiceList() {
    return {
        showFilters: false,
        
        toggleFilters() {
            this.showFilters = !this.showFilters;
        },
        
        init() {
            // Initialize any components needed
        }
    }
}

// Bulk operations placeholder
function bulkOperations() {
    alert('Bulk operations functionality will be implemented here');
}
</script>
{% endblock %}