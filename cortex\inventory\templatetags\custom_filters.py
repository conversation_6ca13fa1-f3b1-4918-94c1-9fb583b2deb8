from django import template
from datetime import datetime

register = template.Library()


@register.filter
def calculate_period_days(from_date, to_date):
    """Calculate the number of days between two date strings in DD-MM-YYYY format"""
    try:
        # Parse dates in DD-MM-YYYY format
        from_dt = datetime.strptime(from_date, '%d-%m-%Y')
        to_dt = datetime.strptime(to_date, '%d-%m-%Y')
        
        # Calculate difference and add 1 to include both dates
        days = (to_dt - from_dt).days + 1
        return days
    except (ValueError, TypeError):
        return 'N/A'


@register.filter  
def format_date_string(date_string):
    """Format a date string from DD-MM-YYYY to a readable format"""
    try:
        dt = datetime.strptime(date_string, '%d-%m-%Y')
        return dt.strftime('%B %d, %Y')
    except (ValueError, TypeError):
        return date_string


@register.filter
def to_currency(value):
    """Format a number as Indian currency"""
    try:
        if value is None:
            return '₹0.00'
        return f'₹{float(value):,.2f}'
    except (ValueError, TypeError):
        return '₹0.00'