from django.shortcuts import render
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView
from django.contrib import messages
from django.urls import reverse_lazy
from django.db.models import Q
from sys_admin.models import Company, FinancialYear
from ..models import ItemLocation, WISTimeConfiguration
from ..forms import ItemLocationForm, WISTimeConfigurationForm, LocationSearchForm


def get_current_company(request=None):
    """Get current company (first one for now)"""
    return Company.objects.first()


def get_current_financial_year(request=None):
    """Get current financial year (first one for now)"""
    return FinancialYear.objects.first()


# Task Group 1: Location & Setup Management Views

class ItemLocationListView(LoginRequiredMixin, ListView):
    """List view for item locations with search and filtering"""
    model = ItemLocation
    template_name = 'inventory/masters/location_list.html'
    context_object_name = 'locations'
    paginate_by = 20
    
    def get_queryset(self):
        company = get_current_company(self.request)
        queryset = ItemLocation.objects.all()
        if company:
            queryset = queryset.filter(company=company)
        
        # Apply search filters
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(location_label__icontains=search) |
                Q(location_no__icontains=search) |
                Q(description__icontains=search)
            )
        
        location_label = self.request.GET.get('location_label')
        if location_label:
            queryset = queryset.filter(location_label=location_label)
        
        return queryset.select_related('company', 'financial_year').order_by('location_no')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = LocationSearchForm(self.request.GET)
        context['company'] = get_current_company(self.request)
        
        # Add statistics
        company = get_current_company(self.request)
        if company:
            context['total_locations'] = ItemLocation.objects.filter(company=company).count()
        else:
            context['total_locations'] = ItemLocation.objects.count()
        
        # Add statistics by label
        context['label_stats'] = {}
        for label in ['A', 'B', 'C', 'D', 'E', 'F']:
            if company:
                count = ItemLocation.objects.filter(company=company, location_label=label).count()
            else:
                count = ItemLocation.objects.filter(location_label=label).count()
            if count > 0:
                context['label_stats'][label] = count
        
        return context
    
    def render_to_response(self, context, **response_kwargs):
        # Handle HTMX requests for real-time search
        if self.request.headers.get('HX-Request'):
            return render(self.request, 'inventory/masters/partials/location_results.html', context)
        return super().render_to_response(context, **response_kwargs)


class ItemLocationCreateView(LoginRequiredMixin, CreateView):
    """Create view for item locations"""
    model = ItemLocation
    form_class = ItemLocationForm
    template_name = 'inventory/masters/location_form.html'
    success_url = reverse_lazy('inventory:location_list')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        kwargs['company'] = get_current_company(self.request)
        return kwargs
    
    def form_valid(self, form):
        form.instance.company = get_current_company(self.request)
        form.instance.financial_year = get_current_financial_year(self.request)
        response = super().form_valid(form)
        messages.success(self.request, f"Location '{form.instance.location_label}-{form.instance.location_no}' created successfully.")
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Create New Location'
        context['submit_text'] = 'Create Location'
        return context


class ItemLocationUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for item locations"""
    model = ItemLocation
    form_class = ItemLocationForm
    template_name = 'inventory/masters/location_form.html'
    success_url = reverse_lazy('inventory:location_list')
    
    def get_queryset(self):
        company = get_current_company(self.request)
        return ItemLocation.objects.filter(company=company)
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        kwargs['company'] = get_current_company(self.request)
        return kwargs
    
    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f"Location '{form.instance.location_label}-{form.instance.location_no}' updated successfully.")
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = f'Edit Location - {self.object.location_label}-{self.object.location_no}'
        context['submit_text'] = 'Update Location'
        return context


class ItemLocationDetailView(LoginRequiredMixin, DetailView):
    """Detail view for item locations"""
    model = ItemLocation
    template_name = 'inventory/masters/location_detail.html'
    context_object_name = 'location'
    
    def get_queryset(self):
        company = get_current_company(self.request)
        if company:
            return ItemLocation.objects.filter(company=company).select_related(
                'company', 'financial_year'
            )
        return ItemLocation.objects.all().select_related('company', 'financial_year')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context


class ItemLocationDeleteView(LoginRequiredMixin, DeleteView):
    """Delete view for item locations"""
    model = ItemLocation
    template_name = 'inventory/masters/location_confirm_delete.html'
    success_url = reverse_lazy('inventory:location_list')
    
    def get_queryset(self):
        company = get_current_company(self.request)
        return ItemLocation.objects.filter(company=company)
    
    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        
        location_id = f"{self.object.location_label}-{self.object.location_no}"
        response = super().delete(request, *args, **kwargs)
        messages.success(request, f"Location '{location_id}' deleted successfully.")
        return response


class WISTimeConfigurationListView(LoginRequiredMixin, ListView):
    """List view for WIS time configurations"""
    model = WISTimeConfiguration
    template_name = 'inventory/masters/wis_config_list.html'
    context_object_name = 'configurations'
    paginate_by = 20
    
    def get_queryset(self):
        company = get_current_company(self.request)
        queryset = WISTimeConfiguration.objects.all()
        if company:
            queryset = queryset.filter(company=company)
        return queryset.order_by('id')


class WISTimeConfigurationDetailView(LoginRequiredMixin, DetailView):
    """Detail view for WIS time configurations"""
    model = WISTimeConfiguration
    template_name = 'inventory/masters/wis_config_detail.html'
    context_object_name = 'configuration'
    
    def get_queryset(self):
        company = get_current_company(self.request)
        queryset = WISTimeConfiguration.objects.all()
        if company:
            queryset = queryset.filter(company=company)
        return queryset


class WISTimeConfigurationCreateView(LoginRequiredMixin, CreateView):
    """Create view for WIS time configurations"""
    model = WISTimeConfiguration
    form_class = WISTimeConfigurationForm
    template_name = 'inventory/masters/wis_config_form.html'
    success_url = reverse_lazy('inventory:wis_config_list')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        kwargs['company'] = get_current_company(self.request)
        return kwargs
    
    def form_valid(self, form):
        form.instance.company = get_current_company(self.request)
        form.instance.financial_year = get_current_financial_year(self.request)
        response = super().form_valid(form)
        messages.success(self.request, "WIS Timer configuration created successfully.")
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Create WIS Time Configuration'
        context['submit_text'] = 'Create Configuration'
        return context


class WISTimeConfigurationUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for WIS time configurations"""
    model = WISTimeConfiguration
    form_class = WISTimeConfigurationForm
    template_name = 'inventory/masters/wis_config_form.html'
    success_url = reverse_lazy('inventory:wis_config_list')
    
    def get_queryset(self):
        company = get_current_company(self.request)
        return WISTimeConfiguration.objects.filter(company=company)
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        kwargs['company'] = get_current_company(self.request)
        return kwargs
    
    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, "WIS Timer configuration updated successfully.")
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = f'Edit WIS Timer - {self.object.id}'
        context['submit_text'] = 'Update Configuration'
        return context


class WISTimeConfigurationDeleteView(LoginRequiredMixin, DeleteView):
    """Delete view for WIS time configurations"""
    model = WISTimeConfiguration
    template_name = 'inventory/masters/wis_config_confirm_delete.html'
    success_url = reverse_lazy('inventory:wis_config_list')
    
    def get_queryset(self):
        company = get_current_company(self.request)
        return WISTimeConfiguration.objects.filter(company=company)
    
    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        config_id = self.object.id
        response = super().delete(request, *args, **kwargs)
        messages.success(request, f"WIS Timer configuration {config_id} deleted successfully.")
        return response