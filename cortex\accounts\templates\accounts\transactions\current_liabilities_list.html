<!-- accounts/templates/accounts/transactions/current_liabilities_list.html -->
<!-- Current Liabilities List View Template -->
<!-- Task Group 7: Capital & Loans Management - Current Liabilities List (Task 7.7) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Current Liabilities Management - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-orange-600 to-sap-orange-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="clipboard-list" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Current Liabilities Management</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Track and manage short-term liabilities and obligations</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:capital_loans_dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to Dashboard
                </a>
                <a href="{% url 'accounts:current_liabilities_create' %}" 
                   class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    New Liability
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Search and Filter Section -->
    <div class="mb-6 bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4">
            <form method="get" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4">
                <!-- Search by Liability Type -->
                <div class="flex-1">
                    <label for="liability_type" class="block text-sm font-medium text-sap-gray-700 mb-1">Search Liability Type</label>
                    <input type="text" name="liability_type" value="{{ request.GET.liability_type }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500"
                           placeholder="Search by liability type...">
                </div>
                
                <!-- Category Filter -->
                <div>
                    <label for="category" class="block text-sm font-medium text-sap-gray-700 mb-1">Category</label>
                    <select name="category" 
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
                        <option value="">All Categories</option>
                        {% for value, label in category_choices %}
                        <option value="{{ value }}" {% if request.GET.category == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-sap-gray-700 mb-1">Status</label>
                    <select name="status" 
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
                        <option value="">All Status</option>
                        {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if request.GET.status == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Search Button -->
                <div>
                    <button type="submit" 
                            class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="search" class="w-4 h-4 inline mr-2"></i>
                        Search
                    </button>
                </div>
                
                <!-- Clear Button -->
                {% if request.GET %}
                <div>
                    <a href="{% url 'accounts:current_liabilities_list' %}" 
                       class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                        Clear
                    </a>
                </div>
                {% endif %}
            </form>
        </div>
    </div>

    <!-- Liabilities Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="clipboard-list" class="w-6 h-6 text-sap-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Liabilities</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ liabilities.count|default:0 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-red-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="alert-triangle" class="w-6 h-6 text-sap-red-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Overdue</p>
                    <p class="text-2xl font-bold text-sap-gray-900">0</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-yellow-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="clock" class="w-6 h-6 text-sap-yellow-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Due This Month</p>
                    <p class="text-2xl font-bold text-sap-gray-900">0</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="dollar-sign" class="w-6 h-6 text-sap-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Amount</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹0.00</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Liabilities Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Current Liabilities</h3>
                <div class="flex items-center space-x-2">
                    <button type="button" onclick="exportLiabilities()" 
                            class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                        Export
                    </button>
                    <button type="button" onclick="generateAgingReport()" 
                            class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="calendar" class="w-4 h-4 inline mr-2"></i>
                        Aging Report
                    </button>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Liability Details
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Creditor
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Amount & Terms
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Due Date
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for liability in liabilities %}
                    <tr class="hover:bg-sap-gray-50">
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-sap-orange-100 rounded-lg flex items-center justify-center mr-3">
                                    <i data-lucide="clipboard-list" class="w-5 h-5 text-sap-orange-600"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-sap-gray-900">
                                        <a href="{% url 'accounts:current_liabilities_detail' liability.id %}" class="text-sap-orange-600 hover:text-sap-orange-900">
                                            {{ liability.liability_code|default:"Liability" }}
                                        </a>
                                    </div>
                                    <div class="text-sm text-sap-gray-500">{{ liability.liability_type|default:"-" }}</div>
                                    {% if liability.reference_number %}
                                    <div class="text-xs text-sap-gray-400">Ref: {{ liability.reference_number }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-sap-gray-900">{{ liability.creditor_name|default:"-" }}</div>
                            {% if liability.creditor_contact %}
                            <div class="text-sm text-sap-gray-500">{{ liability.creditor_contact }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-sap-gray-900">₹{{ liability.liability_amount|floatformat:2 }}</div>
                            {% if liability.outstanding_amount %}
                            <div class="text-sm text-sap-red-600">Outstanding: ₹{{ liability.outstanding_amount|floatformat:2 }}</div>
                            {% endif %}
                            {% if liability.payment_terms %}
                            <div class="text-xs text-sap-gray-400">{{ liability.payment_terms }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            {% if liability.due_date %}
                            <div class="text-sm text-sap-gray-900">{{ liability.due_date|date:"d M Y" }}</div>
                            {% if liability.is_overdue %}
                            <div class="text-xs text-sap-red-600">{{ liability.days_overdue }} days overdue</div>
                            {% elif liability.due_in_days <= 30 %}
                            <div class="text-xs text-sap-yellow-600">Due in {{ liability.due_in_days }} days</div>
                            {% endif %}
                            {% else %}
                            <div class="text-sm text-sap-gray-500">-</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if liability.status == 'active' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-green-100 text-sap-green-800">
                                Active
                            </span>
                            {% elif liability.status == 'overdue' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-red-100 text-sap-red-800">
                                Overdue
                            </span>
                            {% elif liability.status == 'paid' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-gray-100 text-sap-gray-800">
                                Paid
                            </span>
                            {% elif liability.status == 'pending' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-yellow-100 text-sap-yellow-800">
                                Pending
                            </span>
                            {% else %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-blue-100 text-sap-blue-800">
                                {{ liability.get_status_display|default:"Unknown" }}
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <a href="{% url 'accounts:current_liabilities_detail' liability.id %}" 
                                   class="text-sap-orange-600 hover:text-sap-orange-900" title="View Details">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                </a>
                                <a href="{% url 'accounts:current_liabilities_edit' liability.id %}" 
                                   class="text-sap-blue-600 hover:text-sap-blue-900" title="Edit">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </a>
                                <button type="button" onclick="recordPayment({{ liability.id }})" 
                                        class="text-sap-green-600 hover:text-sap-green-900" title="Record Payment">
                                    <i data-lucide="credit-card" class="w-4 h-4"></i>
                                </button>
                                <button type="button" onclick="sendReminder({{ liability.id }})" 
                                        class="text-sap-purple-600 hover:text-sap-purple-900" title="Send Reminder">
                                    <i data-lucide="mail" class="w-4 h-4"></i>
                                </button>
                                <button type="button" onclick="printLiabilityDetails({{ liability.id }})" 
                                        class="text-sap-gray-600 hover:text-sap-gray-900" title="Print">
                                    <i data-lucide="printer" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="px-6 py-8 text-center">
                            <div class="text-center">
                                <i data-lucide="clipboard-list" class="w-12 h-12 text-sap-gray-400 mx-auto mb-4"></i>
                                <h3 class="text-sm font-medium text-sap-gray-900 mb-2">No current liabilities found</h3>
                                <p class="text-sm text-sap-gray-600 mb-4">Get started by adding your first liability.</p>
                                <a href="{% url 'accounts:current_liabilities_create' %}" 
                                   class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                                    Add Liability
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="px-6 py-4 border-t border-sap-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-sap-gray-700">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} entries
                </div>
                <div class="flex space-x-1">
                    {% if page_obj.has_previous %}
                    <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ page_obj.previous_page_number }}" 
                       class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-l-lg hover:bg-sap-gray-50">
                        Previous
                    </a>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <span class="px-3 py-2 text-sm font-medium text-sap-orange-600 bg-sap-orange-50 border border-sap-orange-300">
                            {{ num }}
                        </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ num }}" 
                           class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 hover:bg-sap-gray-50">
                            {{ num }}
                        </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ page_obj.next_page_number }}" 
                       class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-r-lg hover:bg-sap-gray-50">
                        Next
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function exportLiabilities() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = '?' + params.toString();
}

function generateAgingReport() {
    alert('Aging report generation functionality would be implemented here.');
}

function recordPayment(liabilityId) {
    // This would open a modal or redirect to payment recording page
    window.location.href = `/accounts/transactions/current-liabilities/${liabilityId}/record-payment/`;
}

function sendReminder(liabilityId) {
    if (confirm('Send payment reminder to creditor?')) {
        alert(`Payment reminder functionality for liability ID ${liabilityId} would be implemented here.`);
    }
}

function printLiabilityDetails(liabilityId) {
    window.open(`/accounts/transactions/current-liabilities/${liabilityId}/print/`, '_blank');
}

// Initialize lucide icons on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %>