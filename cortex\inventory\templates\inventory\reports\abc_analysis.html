{% extends 'base.html' %}
{% load static %}

{% block title %}ABC Analysis - {{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .abc-category-a { background-color: #dc2626; color: white; }
    .abc-category-b { background-color: #ea580c; color: white; }
    .abc-category-c { background-color: #16a34a; color: white; }
    .analysis-result {
        transition: all 0.2s ease;
    }
    .analysis-result:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    .chart-container {
        height: 400px;
    }
    .progress-bar {
        transition: width 0.3s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">ABC Analysis</h1>
                <p class="mt-2 text-sm text-gray-600">
                    Classify inventory items into A, B, C categories based on value, quantity, or transaction frequency
                </p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'inventory:reports_dashboard' %}" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    Back to Reports
                </a>
                {% if analysis_results %}
                <button type="button" onclick="exportAnalysis()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    Export Results
                </button>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Analysis Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <form method="post" id="abc-analysis-form" class="space-y-6">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Classification Criteria
                    </label>
                    {{ form.classification_criteria }}
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        A Category Threshold (%)
                    </label>
                    {{ form.a_threshold }}
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        B Category Threshold (%)
                    </label>
                    {{ form.b_threshold }}
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Date From
                    </label>
                    {{ form.date_from }}
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Date To
                    </label>
                    {{ form.date_to }}
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Category Filter (Optional)
                    </label>
                    {{ form.category_filter }}
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Location Filter (Optional)
                    </label>
                    {{ form.location_filter }}
                </div>
            </div>

            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    {{ form.include_zero_stock }}
                    <label class="ml-2 text-sm text-gray-700">{{ form.include_zero_stock.help_text }}</label>
                </div>
                <div class="flex items-center">
                    {{ form.exclude_non_moving }}
                    <label class="ml-2 text-sm text-gray-700">{{ form.exclude_non_moving.help_text }}</label>
                </div>
            </div>

            <div class="flex items-center justify-between">
                <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <span class="inline-flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        Generate ABC Analysis
                    </span>
                </button>
                
                <div class="text-sm text-gray-500">
                    <span class="font-medium">Tip:</span> A category typically represents 70% of value, B represents 20%, and C represents 10%
                </div>
            </div>
        </form>
    </div>

    {% if analysis_results %}
    <!-- Analysis Results -->
    <div class="space-y-6">
        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Category A -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Category A Items</h3>
                    <span class="abc-category-a px-3 py-1 rounded-full text-sm font-medium">HIGH VALUE</span>
                </div>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Items Count:</span>
                        <span class="font-medium">{{ analysis_results.category_a.count }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Total Value:</span>
                        <span class="font-medium">₹{{ analysis_results.category_a.value|floatformat:2 }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">% of Total Value:</span>
                        <span class="font-medium">{{ analysis_results.category_a.value_percentage|floatformat:1 }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-red-600 h-2 rounded-full progress-bar" style="width: {{ analysis_results.category_a.value_percentage }}%"></div>
                    </div>
                </div>
            </div>

            <!-- Category B -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Category B Items</h3>
                    <span class="abc-category-b px-3 py-1 rounded-full text-sm font-medium">MEDIUM VALUE</span>
                </div>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Items Count:</span>
                        <span class="font-medium">{{ analysis_results.category_b.count }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Total Value:</span>
                        <span class="font-medium">₹{{ analysis_results.category_b.value|floatformat:2 }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">% of Total Value:</span>
                        <span class="font-medium">{{ analysis_results.category_b.value_percentage|floatformat:1 }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-orange-600 h-2 rounded-full progress-bar" style="width: {{ analysis_results.category_b.value_percentage }}%"></div>
                    </div>
                </div>
            </div>

            <!-- Category C -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Category C Items</h3>
                    <span class="abc-category-c px-3 py-1 rounded-full text-sm font-medium">LOW VALUE</span>
                </div>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Items Count:</span>
                        <span class="font-medium">{{ analysis_results.category_c.count }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Total Value:</span>
                        <span class="font-medium">₹{{ analysis_results.category_c.value|floatformat:2 }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">% of Total Value:</span>
                        <span class="font-medium">{{ analysis_results.category_c.value_percentage|floatformat:1 }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-green-600 h-2 rounded-full progress-bar" style="width: {{ analysis_results.category_c.value_percentage }}%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chart and Analysis Parameters -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Pareto Chart -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">ABC Distribution Chart</h3>
                <div class="chart-container">
                    <canvas id="abcChart"></canvas>
                </div>
            </div>

            <!-- Analysis Parameters -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Analysis Parameters</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Classification Criteria:</span>
                        <span class="font-medium">{{ analysis_results.parameters.criteria|title }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Analysis Period:</span>
                        <span class="font-medium">{{ analysis_results.parameters.date_from }} to {{ analysis_results.parameters.date_to }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">A Threshold:</span>
                        <span class="font-medium">{{ analysis_results.parameters.a_threshold }}%</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">B Threshold:</span>
                        <span class="font-medium">{{ analysis_results.parameters.b_threshold }}%</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Total Items Analyzed:</span>
                        <span class="font-medium">{{ analysis_results.total_items }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Total Value Analyzed:</span>
                        <span class="font-medium">₹{{ analysis_results.total_value|floatformat:2 }}</span>
                    </div>
                </div>

                <!-- Recommendations -->
                <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                    <h4 class="text-sm font-medium text-blue-900 mb-2">Recommendations:</h4>
                    <ul class="text-sm text-blue-800 space-y-1">
                        <li>• Focus on Category A items for tight inventory control</li>
                        <li>• Implement frequent monitoring for high-value items</li>
                        <li>• Consider bulk ordering for Category C items</li>
                        <li>• Review Category B items for optimization opportunities</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Detailed Results Table -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">Detailed Item Classification</h3>
                    <div class="flex space-x-2">
                        <select id="category-filter" class="form-select rounded-md border-gray-300 text-sm">
                            <option value="all">All Categories</option>
                            <option value="A">Category A</option>
                            <option value="B">Category B</option>
                            <option value="C">Category C</option>
                        </select>
                        <input type="text" id="search-items" placeholder="Search items..." class="form-input rounded-md border-gray-300 text-sm">
                    </div>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full table-auto divide-y divide-gray-200" id="results-table">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Item Code
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Description
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Category
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Analysis Value
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Current Stock
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Stock Value
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Cumulative %
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for item in analysis_results.items %}
                        <tr class="analysis-result" data-category="{{ item.abc_category }}">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {{ item.item_code }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ item.item_description|truncatechars:40 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="abc-category-{{ item.abc_category|lower }} px-2 py-1 rounded-full text-xs font-medium">
                                    {{ item.abc_category }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                ₹{{ item.analysis_value|floatformat:2 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                {{ item.current_stock|floatformat:2 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                ₹{{ item.stock_value|floatformat:2 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                {{ item.cumulative_percentage|floatformat:1 }}%
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="px-6 py-4 text-center text-sm text-gray-500">
                                No items found for the selected criteria.
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    {% if analysis_results %}
    // Initialize ABC Distribution Chart
    const ctx = document.getElementById('abcChart').getContext('2d');
    
    const chartData = {
        labels: ['Category A', 'Category B', 'Category C'],
        datasets: [{
            label: 'Value Distribution',
            data: [
                {{ analysis_results.category_a.value_percentage }},
                {{ analysis_results.category_b.value_percentage }},
                {{ analysis_results.category_c.value_percentage }}
            ],
            backgroundColor: [
                '#dc2626',
                '#ea580c',
                '#16a34a'
            ],
            borderWidth: 0
        }, {
            label: 'Item Count',
            data: [
                {{ analysis_results.category_a.count }},
                {{ analysis_results.category_b.count }},
                {{ analysis_results.category_c.count }}
            ],
            backgroundColor: [
                '#dc262680',
                '#ea580c80',
                '#16a34a80'
            ],
            borderWidth: 0,
            yAxisID: 'y1'
        }]
    };
    
    const config = {
        type: 'bar',
        data: chartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Value Percentage'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Item Count'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            },
            plugins: {
                title: {
                    display: true,
                    text: 'ABC Analysis Distribution'
                },
                legend: {
                    display: true
                }
            }
        }
    };
    
    new Chart(ctx, config);

    // Table filtering functionality
    const categoryFilter = document.getElementById('category-filter');
    const searchInput = document.getElementById('search-items');
    const tableRows = document.querySelectorAll('#results-table tbody tr[data-category]');

    function filterTable() {
        const categoryValue = categoryFilter.value;
        const searchValue = searchInput.value.toLowerCase();

        tableRows.forEach(row => {
            const category = row.getAttribute('data-category');
            const itemCode = row.querySelector('td:first-child').textContent.toLowerCase();
            const description = row.querySelector('td:nth-child(2)').textContent.toLowerCase();

            const categoryMatch = categoryValue === 'all' || category === categoryValue;
            const searchMatch = itemCode.includes(searchValue) || description.includes(searchValue);

            if (categoryMatch && searchMatch) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    categoryFilter.addEventListener('change', filterTable);
    searchInput.addEventListener('input', filterTable);
    {% endif %}
});

function exportAnalysis() {
    const form = document.createElement('form');
    form.method = 'get';
    form.action = '{% url "inventory:export_report" %}';
    
    const typeInput = document.createElement('input');
    typeInput.type = 'hidden';
    typeInput.name = 'type';
    typeInput.value = 'abc_analysis';
    
    const formatInput = document.createElement('input');
    formatInput.type = 'hidden';
    formatInput.name = 'format';
    formatInput.value = 'excel';
    
    form.appendChild(typeInput);
    form.appendChild(formatInput);
    
    // Add current form parameters
    const currentForm = document.getElementById('abc-analysis-form');
    const formData = new FormData(currentForm);
    for (let [key, value] of formData.entries()) {
        if (key !== 'csrfmiddlewaretoken') {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = key;
            input.value = value;
            form.appendChild(input);
        }
    }
    
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}
</script>
{% endblock %}