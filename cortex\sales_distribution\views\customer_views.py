from django.shortcuts import render
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.views.generic import ListView, CreateView, UpdateView, DetailView
from django.urls import reverse_lazy
from django.http import JsonResponse, HttpResponse
from django.contrib import messages
from django.db.models import Q
from datetime import datetime

from sys_admin.models import Country, State, City
from ..models import Customer
from ..forms.customer_forms import CustomerForm


class CustomerListView(LoginRequiredMixin, ListView):
    """List all customers with search functionality"""
    model = Customer
    template_name = 'sales_distribution/customer_list.html'
    context_object_name = 'customers'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = Customer.objects.select_related(
            'compid', 'registered_country', 'registered_state', 'registered_city'
        ).order_by('-salesid')
        
        # Search functionality
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(customer_name__icontains=search_query) |
                Q(customerid__icontains=search_query) |
                Q(contact_person__icontains=search_query) |
                Q(email__icontains=search_query)
            )
        
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_query'] = self.request.GET.get('search', '')
        return context


class CustomerCreateView(LoginRequiredMixin, CreateView):
    """Create new customer with all address sections"""
    model = Customer
    form_class = CustomerForm
    template_name = 'sales_distribution/customer_form.html'
    success_url = reverse_lazy('sales_distribution:customer_list')
    
    def form_valid(self, form):
        # Set system fields like in ASP.NET code
        customer = form.save(commit=False)
        
        # System fields (matching ASP.NET logic)
        customer.sysdate = datetime.now().strftime('%Y-%m-%d')
        customer.systime = datetime.now().strftime('%H:%M:%S')
        customer.sessionid = self.request.user.username
        
        # Get company and financial year from global context like ASP.NET session
        company_id = self.request.session.get('company_id')
        if not company_id:
            # Get from global context if not in session
            from core.context_processors.company_context import company_context
            context = company_context(self.request)
            company_id = context['global_company']['company_id']
        
        financial_year_id = self.request.session.get('financial_year_id')
        if not financial_year_id:
            # Get from global context if not in session
            from core.context_processors.company_context import company_context
            context = company_context(self.request)
            financial_year_id = context['global_financial_year']['financial_year_id']
        
        # Set as foreign key objects
        if company_id:
            customer.compid_id = company_id
        if financial_year_id:
            customer.finyearid_id = financial_year_id
        
        # Generate customer ID like in ASP.NET code
        customer_name = customer.customer_name.strip()
        char_str = self._get_customer_char(customer_name)  # Helper method to get first 3 chars
        
        # Get next customer ID number
        existing_customers = Customer.objects.filter(
            customerid__startswith=char_str,
            compid_id=company_id
        ).order_by('-customerid')
        
        if existing_customers.exists():
            last_id = existing_customers.first().customerid
            if len(last_id) > 3:
                try:
                    last_num = int(last_id[3:]) + 1
                    customer.customerid = f"{char_str}{last_num:03d}"
                except (ValueError, IndexError):
                    customer.customerid = f"{char_str}001"
            else:
                customer.customerid = f"{char_str}001"
        else:
            customer.customerid = f"{char_str}001"
        
        customer.customerid = customer.customerid.upper()
        customer.customer_name = customer.customer_name.upper()
        
        customer.save()
        
        # Success message like in ASP.NET
        messages.success(self.request, 'Customer is registered successfully')
        
        # Return HTMX response if it's an HTMX request
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                headers={'HX-Redirect': str(self.success_url)}
            )
        
        return super().form_valid(form)
    
    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return render(self.request, 'sales_distribution/customer_form.html', {
                'form': form,
                'errors': form.errors
            })
        return super().form_invalid(form)
    
    def _get_customer_char(self, customer_name):
        """Generate 3-character prefix from customer name (like ASP.NET getCustChar)"""
        if not customer_name:
            return "CUS"
        
        # Remove common words and get meaningful characters
        words = customer_name.upper().split()
        meaningful_words = [word for word in words if word not in ['THE', 'AND', 'OR', 'OF', 'LTD', 'LIMITED', 'PVT', 'PRIVATE']]
        
        if not meaningful_words:
            meaningful_words = words
        
        if len(meaningful_words) >= 2:
            # Take first letter of first two words and first letter of last word if exists
            chars = meaningful_words[0][:1] + meaningful_words[1][:1]
            if len(meaningful_words) > 2:
                chars += meaningful_words[-1][:1]
            else:
                chars += meaningful_words[0][1:2] if len(meaningful_words[0]) > 1 else "X"
        elif len(meaningful_words) == 1:
            word = meaningful_words[0]
            if len(word) >= 3:
                chars = word[:3]
            else:
                chars = word + "X" * (3 - len(word))
        else:
            chars = "CUS"
        
        return chars[:3].upper()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'New Customer'
        context['countries'] = Country.objects.all()
        return context


class CustomerUpdateView(LoginRequiredMixin, UpdateView):
    """Update existing customer"""
    model = Customer
    form_class = CustomerForm
    template_name = 'sales_distribution/customer_form.html'
    success_url = reverse_lazy('sales_distribution:customer_list')
    
    def form_valid(self, form):
        customer = form.save(commit=False)
        # Update modification fields
        customer.systime = datetime.now().strftime('%H:%M:%S')
        customer.sessionid = self.request.user.username
        customer.save()
        
        messages.success(self.request, 'Customer updated successfully')
        
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                headers={'HX-Redirect': str(self.success_url)}
            )
        
        return super().form_valid(form)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = f'Edit Customer - {self.object.customer_name}'
        context['countries'] = Country.objects.all()
        return context


class CustomerDetailView(LoginRequiredMixin, DetailView):
    """View customer details"""
    model = Customer
    template_name = 'sales_distribution/customer_detail.html'
    context_object_name = 'customer'
    
    def get_queryset(self):
        return Customer.objects.select_related(
            'compid', 'finyearid',
            'registered_country', 'registered_state', 'registered_city',
            'works_country', 'works_state', 'works_city',
            'material_country', 'material_state', 'material_city'
        )


# AJAX Views for Cascading Dropdowns (matching ASP.NET functionality)

@login_required
def ajax_states(request):
    """Get states for selected country (HTMX endpoint)"""
    # Handle both GET and POST requests (HTMX might send POST)
    if request.method == 'POST':
        country_id = request.POST.get('registered_country') or request.POST.get('works_country') or request.POST.get('material_country')
    else:
        country_id = request.GET.get('country_id')
    
    address_type = request.GET.get('address_type', 'registered')
    
    states = []
    if country_id:
        states = list(State.objects.filter(cid_id=country_id).values('sid', 'statename'))
    
    # Return HTML options for HTMX
    html = '<option value="">Select State</option>'
    for state in states:
        html += f'<option value="{state["sid"]}">{state["statename"]}</option>'
    
    return HttpResponse(html)


@login_required
def ajax_cities(request):
    """Get cities for selected state (HTMX endpoint)"""
    # Handle both GET and POST requests (HTMX might send POST)
    if request.method == 'POST':
        state_id = request.POST.get('registered_state') or request.POST.get('works_state') or request.POST.get('material_state')
    else:
        state_id = request.GET.get('state_id')
    
    address_type = request.GET.get('address_type', 'registered')
    
    cities = []
    if state_id:
        cities = list(City.objects.filter(sid_id=state_id).values('cityid', 'cityname'))
    
    # Return HTML options for HTMX
    html = '<option value="">Select City</option>'
    for city in cities:
        html += f'<option value="{city["cityid"]}">{city["cityname"]}</option>'
    
    return HttpResponse(html)


@login_required
def customer_search_ajax(request):
    """AJAX search for customers (for typeahead/autocomplete)"""
    query = request.GET.get('q', '').strip()
    customers = []
    
    if query and len(query) >= 2:
        customers_qs = Customer.objects.filter(
            Q(customer_name__icontains=query) |
            Q(customerid__icontains=query)
        ).values('salesid', 'customer_name', 'customerid')[:10]
        
        customers = list(customers_qs)
    
    return JsonResponse({
        'customers': customers
    })