{% extends "core/base.html" %}
{% load static %}

{% block title %}
{% if object %}Edit Closing Stock Record{% else %}Add Closing Stock Record{% endif %} - Inventory
{% endblock %}

{% block content %}
<div class="sap-page">
    <!-- Page Header -->
    <div class="sap-page-header">
        <div class="sap-page-header-content">
            <div class="sap-page-title">
                <h1>
                    {% if object %}
                        Edit Closing Stock Record
                    {% else %}
                        Add New Closing Stock Record
                    {% endif %}
                </h1>
                <p>
                    {% if object %}
                        Update the closing stock information for this period
                    {% else %}
                        Create a new closing stock record for a specific period
                    {% endif %}
                </p>
            </div>
            <div class="sap-page-actions">
                <a href="{% url 'inventory:closing_stock_list' %}" class="sap-button sap-button--transparent">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to List
                </a>
                {% if object %}
                <a href="{% url 'inventory:closing_stock_detail' object.pk %}" class="sap-button sap-button--transparent">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    View Details
                </a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Form Panel -->
    <div class="sap-panel">
        <div class="sap-panel-header">
            <h3 class="sap-panel-title">Closing Stock Information</h3>
        </div>
        <div class="sap-panel-content">
            {% if form.non_field_errors %}
                <div class="sap-message-strip sap-message-strip--error mb-6">
                    <svg class="w-5 h-5 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <div class="sap-message-strip-text">
                        {% for error in form.non_field_errors %}
                            <div>{{ error }}</div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <form method="post" class="space-y-6" id="closing-stock-form">
                {% csrf_token %}
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- From Date -->
                    <div class="sap-form-group">
                        <label class="sap-form-label required" for="{{ form.from_date.id_for_label }}">
                            From Date
                        </label>
                        {{ form.from_date }}
                        {% if form.from_date.errors %}
                            <div class="sap-form-error">
                                {% for error in form.from_date.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% endif %}
                        {% if form.from_date.help_text %}
                            <div class="sap-form-help">{{ form.from_date.help_text }}</div>
                        {% endif %}
                    </div>

                    <!-- To Date -->
                    <div class="sap-form-group">
                        <label class="sap-form-label required" for="{{ form.to_date.id_for_label }}">
                            To Date
                        </label>
                        {{ form.to_date }}
                        {% if form.to_date.errors %}
                            <div class="sap-form-error">
                                {% for error in form.to_date.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% endif %}
                        {% if form.to_date.help_text %}
                            <div class="sap-form-help">{{ form.to_date.help_text }}</div>
                        {% endif %}
                    </div>
                </div>

                <!-- Closing Stock Value -->
                <div class="sap-form-group">
                    <label class="sap-form-label required" for="{{ form.closing_stock_value.id_for_label }}">
                        Closing Stock Value (₹)
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 text-sm">₹</span>
                        </div>
                        <div class="pl-8">
                            {{ form.closing_stock_value }}
                        </div>
                    </div>
                    {% if form.closing_stock_value.errors %}
                        <div class="sap-form-error">
                            {% for error in form.closing_stock_value.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% endif %}
                    {% if form.closing_stock_value.help_text %}
                        <div class="sap-form-help">{{ form.closing_stock_value.help_text }}</div>
                    {% endif %}
                </div>

                <!-- Period Summary (Dynamic) -->
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-4" id="period-summary" style="display: none;">
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Period Summary</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                            <span class="text-gray-600">From:</span>
                            <span id="summary-from" class="font-medium ml-1">-</span>
                        </div>
                        <div>
                            <span class="text-gray-600">To:</span>
                            <span id="summary-to" class="font-medium ml-1">-</span>
                        </div>
                        <div>
                            <span class="text-gray-600">Duration:</span>
                            <span id="summary-duration" class="font-medium ml-1">-</span>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="sap-form-actions">
                    <button type="submit" class="sap-button sap-button--emphasized">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        {% if object %}Update Record{% else %}Create Record{% endif %}
                    </button>
                    <a href="{% url 'inventory:closing_stock_list' %}" class="sap-button sap-button--transparent">
                        Cancel
                    </a>
                    {% if object %}
                    <a href="{% url 'inventory:closing_stock_delete' object.pk %}" 
                       class="sap-button sap-button--transparent text-red-600 hover:text-red-800 ml-auto"
                       onclick="return confirm('Are you sure you want to delete this closing stock record?')">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        Delete
                    </a>
                    {% endif %}
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fromDateInput = document.getElementById('{{ form.from_date.id_for_label }}');
    const toDateInput = document.getElementById('{{ form.to_date.id_for_label }}');
    const periodSummary = document.getElementById('period-summary');
    const summaryFrom = document.getElementById('summary-from');
    const summaryTo = document.getElementById('summary-to');
    const summaryDuration = document.getElementById('summary-duration');

    function updatePeriodSummary() {
        const fromDate = fromDateInput.value;
        const toDate = toDateInput.value;

        if (fromDate && toDate) {
            const from = new Date(fromDate);
            const to = new Date(toDate);
            
            // Format dates
            const fromFormatted = from.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            const toFormatted = to.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            // Calculate duration
            const timeDiff = to.getTime() - from.getTime();
            const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1; // +1 to include both dates

            // Update summary
            summaryFrom.textContent = fromFormatted;
            summaryTo.textContent = toFormatted;
            summaryDuration.textContent = daysDiff + ' days';
            
            periodSummary.style.display = 'block';
        } else {
            periodSummary.style.display = 'none';
        }
    }

    // Add event listeners
    fromDateInput.addEventListener('change', updatePeriodSummary);
    toDateInput.addEventListener('change', updatePeriodSummary);

    // Initial update
    updatePeriodSummary();

    // Form validation
    document.getElementById('closing-stock-form').addEventListener('submit', function(e) {
        const fromDate = new Date(fromDateInput.value);
        const toDate = new Date(toDateInput.value);

        if (fromDate > toDate) {
            e.preventDefault();
            alert('From date must be before or equal to the To date.');
            fromDateInput.focus();
        }
    });
});
</script>
{% endblock %}