{% load static %}

<div class="overflow-x-auto">
    {% if mrn_list %}
        <table class="w-full table-auto divide-y divide-gray-200 border border-gray-300">
            <thead class="bg-gray-100">
                <tr>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                        SN
                    </th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                        
                    </th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                        FinYear
                    </th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                        Date
                    </th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                        MRN No
                    </th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-700">
                        Gen. By
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for mrn in mrn_list %}
                <tr class="hover:bg-gray-50 border-b border-gray-200">
                    <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">
                        {{ forloop.counter }}
                    </td>
                    <td class="px-4 py-2 text-sm border-r border-gray-300">
                        <a href="{% url 'inventory:mrn_edit_details' mrn.pk %}" 
                           class="text-blue-600 hover:text-blue-900 underline text-sm">
                            Select
                        </a>
                    </td>
                    <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">
                        {{ mrn.financial_year.finyear_name|default:"2022-2023" }}
                    </td>
                    <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">
                        {{ mrn.sysdate|default:"18-11-2022" }}
                    </td>
                    <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">
                        {{ mrn.mrn_no|default:"0000"|stringformat:"04s" }}
                    </td>
                    <td class="px-4 py-2 text-sm text-gray-900">
                        {{ mrn.sessionid|default:"Mr.Madan Balkrishna Kumbhar" }}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Pagination -->
        {% if is_paginated %}
        <div class="px-4 py-3 border-t border-gray-200 bg-gray-50">
            <div class="flex justify-center items-center">
                <div class="flex space-x-1">
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <span class="px-2 py-1 text-sm bg-blue-600 text-white border border-blue-600 rounded">
                                {{ num }}
                            </span>
                        {% else %}
                            <a href="?page={{ num }}{% if request.GET.search_field %}&search_field={{ request.GET.search_field }}{% endif %}{% if request.GET.search_value %}&search_value={{ request.GET.search_value }}{% endif %}" 
                               class="px-2 py-1 text-sm text-blue-600 hover:text-blue-800 border border-gray-300 rounded hover:bg-gray-100">
                                {{ num }}
                            </a>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    {% else %}
        <div class="text-center py-8">
            <p class="text-sm text-gray-500">No material return notes found matching your search criteria.</p>
            <div class="mt-4">
                <a href="{% url 'inventory:mrn_create' %}" 
                   class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                    Create New MRN
                </a>
            </div>
        </div>
    {% endif %}
</div>