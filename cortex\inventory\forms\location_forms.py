from django import forms
from django.core.exceptions import ValidationError
from ..models import ItemLocation, WISTimeConfiguration


class ItemLocationForm(forms.ModelForm):
    """Form for creating and editing item locations"""
    
    class Meta:
        model = ItemLocation
        fields = [
            'location_label', 'location_no', 'description'
        ]
        widgets = {
            'location_label': forms.Select(attrs={
                'class': 'block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
            }, choices=[
                ('', 'Select Location Label'),
                ('A', 'A'), ('B', 'B'), ('C', 'C'), ('D', 'D'), ('E', 'E'),
                ('F', 'F'), ('G', 'G'), ('H', 'H'), ('I', 'I'), ('J', 'J'),
                ('K', 'K'), ('L', 'L'), ('M', 'M'), ('N', 'N'), ('O', 'O'),
                ('P', 'P'), ('Q', 'Q'), ('R', 'R'), ('S', 'S'), ('T', 'T'),
                ('U', 'U'), ('V', 'V'), ('W', 'W'), ('X', 'X'), ('Y', 'Y'), ('Z', 'Z')
            ]),
            'location_no': forms.TextInput(attrs={
                'class': 'block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'placeholder': 'Enter location number'
            }),
            'description': forms.Textarea(attrs={
                'class': 'block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'placeholder': 'Enter location description',
                'rows': 3
            })
        }
        
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)
    
    def clean_location_no(self):
        location_no = self.cleaned_data.get('location_no')
        if location_no:
            location_no = location_no.upper().strip()
            
            # Check for uniqueness within the company
            existing = ItemLocation.objects.filter(
                location_no=location_no,
                company=self.company
            )
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
                
            if existing.exists():
                raise ValidationError(f"Location number '{location_no}' already exists in this company.")
                
        return location_no


class WISTimeConfigurationForm(forms.ModelForm):
    """Form for WIS time configuration management"""
    
    class Meta:
        model = WISTimeConfiguration
        fields = ['auto_issue_time', 'time_to_order']
        widgets = {
            'auto_issue_time': forms.TextInput(attrs={
                'class': 'block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'placeholder': 'Enter time (HH:MM format)',
                'pattern': '[0-9]{2}:[0-9]{2}',
                'title': 'Please enter time in HH:MM format'
            }),
            'time_to_order': forms.TextInput(attrs={
                'class': 'block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'placeholder': 'Enter time to order (HH:MM format)',
                'pattern': '[0-9]{2}:[0-9]{2}',
                'title': 'Please enter time in HH:MM format'
            })
        }
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)
    
    def clean_auto_issue_time(self):
        """Validate time format"""
        time_str = self.cleaned_data.get('auto_issue_time')
        if time_str:
            import re
            if not re.match(r'^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$', time_str):
                raise ValidationError("Please enter time in HH:MM format (e.g., 09:30)")
        return time_str
    
    def clean_time_to_order(self):
        """Validate time format"""
        time_str = self.cleaned_data.get('time_to_order')
        if time_str:
            import re
            if not re.match(r'^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$', time_str):
                raise ValidationError("Please enter time in HH:MM format (e.g., 09:30)")
        return time_str
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        if self.company:
            instance.company = self.company
        
        if commit:
            instance.save()
        return instance


class LocationSearchForm(forms.Form):
    """Form for searching and filtering locations"""
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
            'placeholder': 'Search locations...',
            'hx-get': '',
            'hx-trigger': 'keyup changed delay:300ms',
            'hx-target': '#location-results'
        })
    )
    
    location_label = forms.ChoiceField(
        choices=[('', 'All Labels')] + [(chr(i), chr(i)) for i in range(ord('A'), ord('Z') + 1)],
        required=False,
        widget=forms.Select(attrs={
            'class': 'block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
            'hx-get': '',
            'hx-trigger': 'change',
            'hx-target': '#location-results'
        })
    )