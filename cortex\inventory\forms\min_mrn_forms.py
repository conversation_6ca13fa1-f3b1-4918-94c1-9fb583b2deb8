from django import forms
from django.core.exceptions import ValidationError
from ..models import (
    MaterialIssueNote, MINLineItem, MaterialReturnNote, MRNLineItem
)


class MaterialIssueNoteForm(forms.ModelForm):
    """Form for creating and editing Material Issue Notes"""
    
    class Meta:
        model = MaterialIssueNote
        fields = [
            'sysdate', 'systime', 'sessionid', 'min_no', 'mrs_no', 'mrs_id'
        ]
        widgets = {
            'sysdate': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'System Date'
            }),
            'systime': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'System Time'
            }),
            'sessionid': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Session ID'
            }),
            'min_no': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'MIN Number'
            }),
            'mrs_no': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'MRS Number'
            }),
            'mrs_id': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'MRS ID'
            })
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.company = kwargs.pop('company', None)
        self.financial_year = kwargs.pop('financial_year', None)
        super().__init__(*args, **kwargs)


class MINSearchForm(forms.Form):
    """Search form for Material Issue Notes"""
    search = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Search MIN Number, MRS Number, or Session ID...',
            'hx-get': '',
            'hx-trigger': 'keyup changed delay:300ms',
            'hx-target': '#min-results',
            'hx-swap': 'innerHTML'
        })
    )


class MINLineItemForm(forms.ModelForm):
    """Form for MIN line items"""
    
    class Meta:
        model = MINLineItem
        fields = [
            'min_no', 'mrs_id', 'issue_qty'
        ]
        widgets = {
            'min_no': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'MIN Number'
            }),
            'mrs_id': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'MRS ID'
            }),
            'issue_qty': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            })
        }



class MaterialReturnNoteForm(forms.ModelForm):
    """Form for creating and editing Material Return Notes"""
    
    class Meta:
        model = MaterialReturnNote
        fields = [
            'sysdate', 'systime', 'sessionid', 'mrn_no'
        ]
        widgets = {
            'sysdate': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'System Date'
            }),
            'systime': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'System Time'
            }),
            'sessionid': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Session ID'
            }),
            'mrn_no': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'MRN Number'
            })
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.company = kwargs.pop('company', None)
        self.financial_year = kwargs.pop('financial_year', None)
        super().__init__(*args, **kwargs)
        
        # Auto-populate fields for new forms
        if not self.instance.pk:
            from django.utils import timezone
            now = timezone.now()
            
            self.fields['sysdate'].initial = now.strftime('%Y-%m-%d')
            self.fields['systime'].initial = now.strftime('%H:%M:%S')
            
            # Auto-populate session ID with current user info
            if self.user:
                if hasattr(self.user, 'username'):
                    self.fields['sessionid'].initial = self.user.username
                
            # Make fields readonly if they should be auto-generated
            self.fields['sysdate'].widget.attrs.update({'readonly': True})
            self.fields['systime'].widget.attrs.update({'readonly': True})
            self.fields['sessionid'].widget.attrs.update({'readonly': True})

    def save(self, commit=True):
        instance = super().save(commit=False)
        
        if not instance.pk:
            # Set system fields for new instances
            instance.company = self.company
            instance.financial_year = self.financial_year
            
        if commit:
            instance.save()
        
        return instance


class MRNLineItemForm(forms.ModelForm):
    """Form for MRN line items"""
    
    class Meta:
        model = MRNLineItem
        fields = [
            'min_line_item', 'returned_quantity', 'return_rate', 'location_code',
            'batch_number', 'serial_number', 'condition_on_return', 'return_reason',
            'condition_notes', 'action_required'
        ]
        widgets = {
            'min_line_item': forms.Select(attrs={
                'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'hx-get': '/inventory/api/min-line-item-details/',
                'hx-trigger': 'change',
                'hx-target': '#item-details',
                'hx-swap': 'innerHTML'
            }),
            'returned_quantity': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'return_rate': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'location_code': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Return Location'
            }),
            'batch_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Batch Number'
            }),
            'serial_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Serial Number'
            }),
            'condition_on_return': forms.Select(attrs={
                'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'return_reason': forms.Select(attrs={
                'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'condition_notes': forms.Textarea(attrs={
                'rows': 2,
                'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md',
                'placeholder': 'Condition notes'
            }),
            'action_required': forms.Select(attrs={
                'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            })
        }

    def clean_returned_quantity(self):
        returned_quantity = self.cleaned_data.get('returned_quantity')
        
        if returned_quantity is not None and returned_quantity <= 0:
            raise ValidationError("Returned quantity must be greater than zero.")
        
        return returned_quantity


class MINSearchForm(forms.Form):
    """Search form for Material Issue Notes"""
    
    search = forms.CharField(
        required=False,
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md',
            'placeholder': 'Search by MIN number, MRS number, or session ID...',
            'hx-get': '/inventory/min/',
            'hx-trigger': 'keyup changed delay:300ms',
            'hx-target': '#min-results',
            'hx-swap': 'innerHTML'
        })
    )


class MRNSearchForm(forms.Form):
    """Search form for Material Return Notes"""
    
    search = forms.CharField(
        required=False,
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md',
            'placeholder': 'Search by MRN number or session ID...',
            'hx-get': '/inventory/mrn/',
            'hx-trigger': 'keyup changed delay:300ms',
            'hx-target': '#mrn-results',
            'hx-swap': 'innerHTML'
        })
    )