<!-- accounts/templates/accounts/masters/asset_list.html -->
<!-- Asset List View Template -->
<!-- Task Group 8: Asset Management - Asset List (Task 8.2) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Assets - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-teal-600 to-sap-teal-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="building" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Fixed Assets</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage fixed asset inventory and lifecycle</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:asset_dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to Dashboard
                </a>
                <a href="{% url 'accounts:asset_create' %}" 
                   class="bg-sap-teal-600 hover:bg-sap-teal-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    New Asset
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Search and Filter Section -->
    <div class="mb-6 bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4">
            <form method="get" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4">
                <!-- Search -->
                <div class="flex-1">
                    <label for="search_query" class="block text-sm font-medium text-sap-gray-700 mb-1">Search</label>
                    <input type="text" name="search_query" value="{{ request.GET.search_query }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500"
                           placeholder="Search by asset name, code, serial number...">
                </div>
                
                <!-- Asset Type Filter -->
                <div>
                    <label for="asset_type" class="block text-sm font-medium text-sap-gray-700 mb-1">Asset Type</label>
                    <select name="asset_type" 
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500">
                        <option value="">All Types</option>
                        {% for value, label in asset_type_choices %}
                        <option value="{{ value }}" {% if request.GET.asset_type == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-sap-gray-700 mb-1">Status</label>
                    <select name="status" 
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500">
                        <option value="">All Status</option>
                        {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if request.GET.status == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Location Filter -->
                <div>
                    <label for="location" class="block text-sm font-medium text-sap-gray-700 mb-1">Location</label>
                    <input type="text" name="location" value="{{ request.GET.location }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500"
                           placeholder="Filter by location...">
                </div>
                
                <!-- Search Button -->
                <div>
                    <button type="submit" 
                            class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="search" class="w-4 h-4 inline mr-2"></i>
                        Search
                    </button>
                </div>
                
                <!-- Clear Button -->
                {% if request.GET %}
                <div>
                    <a href="{% url 'accounts:asset_list' %}" 
                       class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                        Clear
                    </a>
                </div>
                {% endif %}
            </form>
        </div>
    </div>

    <!-- Asset Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-teal-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="building" class="w-6 h-6 text-sap-teal-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Assets</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ assets.count|default:0 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="check-circle" class="w-6 h-6 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Active</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ assets|length }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="wrench" class="w-6 h-6 text-sap-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Under Maintenance</p>
                    <p class="text-2xl font-bold text-sap-gray-900">0</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="dollar-sign" class="w-6 h-6 text-sap-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Value</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹0.00</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Assets Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Fixed Assets</h3>
                <div class="flex items-center space-x-2">
                    <button type="button" onclick="exportAssets()" 
                            class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                        Export
                    </button>
                    <button type="button" onclick="bulkActions()" 
                            class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="layers" class="w-4 h-4 inline mr-2"></i>
                        Bulk Actions
                    </button>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            <input type="checkbox" onclick="toggleAll(this)" class="w-4 h-4 text-sap-teal-600 border-sap-gray-300 rounded focus:ring-sap-teal-500">
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Asset Details
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Type & Location
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Purchase Info
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Depreciation
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Warranty
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for asset in assets %}
                    <tr class="hover:bg-sap-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" name="selected_assets" value="{{ asset.id }}" class="w-4 h-4 text-sap-teal-600 border-sap-gray-300 rounded focus:ring-sap-teal-500">
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-sap-teal-100 rounded-lg flex items-center justify-center mr-3">
                                    <i data-lucide="building" class="w-5 h-5 text-sap-teal-600"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-sap-gray-900">
                                        <a href="{% url 'accounts:asset_detail' asset.id %}" class="text-sap-teal-600 hover:text-sap-teal-900">
                                            {{ asset.asset_name }}
                                        </a>
                                    </div>
                                    <div class="text-sm text-sap-gray-500">{{ asset.asset_code }}</div>
                                    {% if asset.serial_number %}
                                    <div class="text-xs text-sap-gray-400">S/N: {{ asset.serial_number }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-sap-gray-900">{{ asset.get_asset_type_display|default:"-" }}</div>
                            <div class="text-sm text-sap-gray-500">{{ asset.location|default:"-" }}</div>
                            {% if asset.department %}
                            <div class="text-xs text-sap-gray-400">{{ asset.department }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-sap-gray-900">₹{{ asset.purchase_cost|floatformat:2 }}</div>
                            {% if asset.purchase_date %}
                            <div class="text-sm text-sap-gray-500">{{ asset.purchase_date|date:"d M Y" }}</div>
                            {% endif %}
                            {% if asset.supplier %}
                            <div class="text-xs text-sap-gray-400">{{ asset.supplier }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-sap-gray-900">{{ asset.get_depreciation_method_display|default:"-" }}</div>
                            {% if asset.depreciation_rate %}
                            <div class="text-sm text-sap-gray-500">{{ asset.depreciation_rate }}% p.a.</div>
                            {% endif %}
                            <div class="text-xs text-sap-gray-400">₹{{ asset.accumulated_depreciation|default:0|floatformat:2 }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if asset.status == 'active' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-green-100 text-sap-green-800">
                                Active
                            </span>
                            {% elif asset.status == 'under_maintenance' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-orange-100 text-sap-orange-800">
                                Maintenance
                            </span>
                            {% elif asset.status == 'disposed' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-red-100 text-sap-red-800">
                                Disposed
                            </span>
                            {% else %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-gray-100 text-sap-gray-800">
                                {{ asset.get_status_display|default:"Unknown" }}
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            {% if asset.warranty_end_date %}
                            <div class="text-sm text-sap-gray-900">Until {{ asset.warranty_end_date|date:"d M Y" }}</div>
                            {% if asset.is_under_warranty %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-green-100 text-sap-green-800">
                                Active
                            </span>
                            {% else %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-red-100 text-sap-red-800">
                                Expired
                            </span>
                            {% endif %}
                            {% else %}
                            <div class="text-sm text-sap-gray-500">No warranty</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <a href="{% url 'accounts:asset_detail' asset.id %}" 
                                   class="text-sap-blue-600 hover:text-sap-blue-900" title="View Details">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                </a>
                                <a href="{% url 'accounts:asset_edit' asset.id %}" 
                                   class="text-sap-green-600 hover:text-sap-green-900" title="Edit">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </a>
                                <button type="button" onclick="generateQRCode({{ asset.id }})" 
                                        class="text-sap-purple-600 hover:text-sap-purple-900" title="Generate QR Code">
                                    <i data-lucide="qr-code" class="w-4 h-4"></i>
                                </button>
                                <button type="button" onclick="assetHistory({{ asset.id }})" 
                                        class="text-sap-orange-600 hover:text-sap-orange-900" title="Asset History">
                                    <i data-lucide="history" class="w-4 h-4"></i>
                                </button>
                                <button type="button" onclick="deleteAsset({{ asset.id }})" 
                                        class="text-sap-red-600 hover:text-sap-red-900" title="Delete">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="px-6 py-8 text-center">
                            <div class="text-center">
                                <i data-lucide="building" class="w-12 h-12 text-sap-gray-400 mx-auto mb-4"></i>
                                <h3 class="text-sm font-medium text-sap-gray-900 mb-2">No assets found</h3>
                                <p class="text-sm text-sap-gray-600 mb-4">Get started by adding your first fixed asset.</p>
                                <a href="{% url 'accounts:asset_create' %}" 
                                   class="bg-sap-teal-600 hover:bg-sap-teal-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                                    Add Asset
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="px-6 py-4 border-t border-sap-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-sap-gray-700">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} entries
                </div>
                <div class="flex space-x-1">
                    {% if page_obj.has_previous %}
                    <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ page_obj.previous_page_number }}" 
                       class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-l-lg hover:bg-sap-gray-50">
                        Previous
                    </a>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <span class="px-3 py-2 text-sm font-medium text-sap-teal-600 bg-sap-teal-50 border border-sap-teal-300">
                            {{ num }}
                        </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ num }}" 
                           class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 hover:bg-sap-gray-50">
                            {{ num }}
                        </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ page_obj.next_page_number }}" 
                       class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-r-lg hover:bg-sap-gray-50">
                        Next
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function exportAssets() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = '?' + params.toString();
}

function bulkActions() {
    const selected = document.querySelectorAll('input[name="selected_assets"]:checked');
    if (selected.length === 0) {
        alert('Please select at least one asset');
        return;
    }
    alert(`Bulk actions for ${selected.length} selected assets would be implemented here.`);
}

function toggleAll(checkbox) {
    const checkboxes = document.querySelectorAll('input[name="selected_assets"]');
    checkboxes.forEach(cb => cb.checked = checkbox.checked);
}

function generateQRCode(assetId) {
    alert(`QR code generation for asset ID ${assetId} would be implemented here.`);
}

function assetHistory(assetId) {
    window.location.href = `/accounts/masters/assets/${assetId}/`;
}

function deleteAsset(assetId) {
    if (confirm('Are you sure you want to delete this asset? This action cannot be undone.')) {
        fetch(`/accounts/masters/assets/${assetId}/delete/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        }).then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Error deleting asset');
            }
        });
    }
}

// Initialize lucide icons on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %>