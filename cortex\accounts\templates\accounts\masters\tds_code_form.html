<!-- accounts/templates/accounts/masters/tds_code_form.html -->
<!-- TDS Code Create/Edit Form Template -->
<!-- Task Group 4: Taxation Management - TDS Code Form (Task 4.5) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{% if object %}Edit TDS Code{% else %}New TDS Code{% endif %} - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-orange-600 to-sap-orange-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="percent" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">
                        {% if object %}Edit TDS Code{% else %}New TDS Code{% endif %}
                    </h1>
                    <p class="text-sm text-sap-gray-600 mt-1">
                        {% if object %}Modify TDS code details{% else %}Add a new TDS deduction code{% endif %}
                    </p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:tds_code_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- TDS Code Form -->
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="edit" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                    TDS Code Information
                </h3>
                <p class="text-sm text-sap-gray-600 mt-1">Configure TDS (Tax Deducted at Source) code details</p>
            </div>
            
            <form method="post" class="p-6" x-data="tdsCodeForm()">
                {% csrf_token %}
                
                <!-- Basic Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="info" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                        Basic Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- TDS Code -->
                        <div>
                            <label for="{{ form.tds_code.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                TDS Code *
                            </label>
                            {{ form.tds_code|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.tds_code.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.tds_code.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">e.g., 194A, 194C, 194J</p>
                        </div>
                        
                        <!-- TDS Percentage -->
                        <div>
                            <label for="{{ form.tds_percentage.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                TDS Percentage *
                            </label>
                            <div class="relative">
                                {{ form.tds_percentage|add_class:"block w-full px-3 py-2 pr-8 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                    <span class="text-sap-gray-500 text-sm">%</span>
                                </div>
                            </div>
                            {% if form.tds_percentage.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.tds_percentage.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Enter the TDS deduction rate</p>
                        </div>
                        
                        <!-- Description -->
                        <div class="md:col-span-2">
                            <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Description *
                            </label>
                            {{ form.description|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.description.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.description.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Detailed description of when this TDS code applies</p>
                        </div>
                    </div>
                </div>
                
                <!-- Calculation Preview -->
                <div class="mb-8 bg-sap-orange-50 border border-sap-orange-200 rounded-lg p-6" x-show="tdsPercentage > 0">
                    <h4 class="text-lg font-medium text-sap-orange-800 mb-4 flex items-center">
                        <i data-lucide="calculator" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                        TDS Calculation Preview
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Sample Amount Input -->
                        <div>
                            <label class="block text-sm font-medium text-sap-orange-700 mb-1">Sample Amount (₹)</label>
                            <input type="number" 
                                   x-model="sampleAmount" 
                                   class="block w-full px-3 py-2 border border-sap-orange-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500"
                                   placeholder="100000">
                        </div>
                        
                        <!-- TDS Amount -->
                        <div class="text-center">
                            <p class="text-sm text-sap-orange-600">TDS Amount</p>
                            <p class="text-xl font-bold text-sap-orange-800" x-text="'₹' + tdsAmount.toFixed(2)"></p>
                        </div>
                        
                        <!-- Net Amount -->
                        <div class="text-center">
                            <p class="text-sm text-sap-orange-600">Net Payable</p>
                            <p class="text-xl font-bold text-sap-orange-800" x-text="'₹' + netAmount.toFixed(2)"></p>
                        </div>
                    </div>
                    
                    <!-- Calculation Breakdown -->
                    <div class="mt-4 p-4 bg-white rounded-lg border border-sap-orange-100">
                        <h5 class="text-sm font-medium text-sap-orange-800 mb-2">Calculation Breakdown:</h5>
                        <div class="text-xs text-sap-orange-700 space-y-1">
                            <p>Gross Amount: ₹<span x-text="sampleAmount.toFixed(2)"></span></p>
                            <p>TDS @ <span x-text="tdsPercentage"></span>%: ₹<span x-text="tdsAmount.toFixed(2)"></span></p>
                            <p class="font-medium border-t border-sap-orange-200 pt-1">Net Payable: ₹<span x-text="netAmount.toFixed(2)"></span></p>
                        </div>
                    </div>
                </div>
                
                <!-- TDS Guidelines Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="book-open" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Common TDS Codes & Rates
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div class="bg-sap-blue-50 border border-sap-blue-200 rounded-lg p-4">
                            <h5 class="font-medium text-sap-blue-800">Section 194A</h5>
                            <p class="text-sm text-sap-blue-700">Interest other than on securities: 10%</p>
                        </div>
                        <div class="bg-sap-green-50 border border-sap-green-200 rounded-lg p-4">
                            <h5 class="font-medium text-sap-green-800">Section 194C</h5>
                            <p class="text-sm text-sap-green-700">Payments to contractors: 1-2%</p>
                        </div>
                        <div class="bg-sap-purple-50 border border-sap-purple-200 rounded-lg p-4">
                            <h5 class="font-medium text-sap-purple-800">Section 194J</h5>
                            <p class="text-sm text-sap-purple-700">Professional/technical services: 10%</p>
                        </div>
                        <div class="bg-sap-yellow-50 border border-sap-yellow-200 rounded-lg p-4">
                            <h5 class="font-medium text-sap-yellow-800">Section 194H</h5>
                            <p class="text-sm text-sap-yellow-700">Commission/brokerage: 5%</p>
                        </div>
                        <div class="bg-sap-red-50 border border-sap-red-200 rounded-lg p-4">
                            <h5 class="font-medium text-sap-red-800">Section 194I</h5>
                            <p class="text-sm text-sap-red-700">Rent payments: 10%</p>
                        </div>
                        <div class="bg-sap-indigo-50 border border-sap-indigo-200 rounded-lg p-4">
                            <h5 class="font-medium text-sap-indigo-800">Section 194O</h5>
                            <p class="text-sm text-sap-indigo-700">E-commerce payments: 1%</p>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="resetForm()" 
                                class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="rotate-ccw" class="w-4 h-4 inline mr-2"></i>
                            Reset
                        </button>
                        <button type="button" onclick="validateTDSCode()" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check-circle" class="w-4 h-4 inline mr-2"></i>
                            Validate
                        </button>
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="{% url 'accounts:tds_code_list' %}" 
                           class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                            {% if object %}Update TDS Code{% else %}Save TDS Code{% endif %}
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- TDS Guidelines -->
        <div class="mt-6 bg-sap-blue-50 border border-sap-blue-200 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i data-lucide="info" class="w-5 h-5 text-sap-blue-600"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-sap-blue-800">TDS Code Guidelines</h3>
                    <div class="mt-2 text-sm text-sap-blue-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>TDS codes should follow Income Tax Act sections (e.g., 194A, 194C)</li>
                            <li>Ensure the percentage matches current government rates</li>
                            <li>Provide clear descriptions for easy identification</li>
                            <li>Regularly update rates based on budget changes</li>
                            <li>Maintain compliance with current tax regulations</li>
                            <li>Document any special conditions or thresholds</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function tdsCodeForm() {
    return {
        tdsPercentage: {{ object.tds_percentage|default:0 }},
        sampleAmount: 100000,
        
        get tdsAmount() {
            return (this.sampleAmount * this.tdsPercentage) / 100;
        },
        
        get netAmount() {
            return this.sampleAmount - this.tdsAmount;
        },
        
        init() {
            this.updateTDSPercentage();
        },
        
        updateTDSPercentage() {
            const percentageField = document.getElementById('{{ form.tds_percentage.id_for_label }}');
            if (percentageField) {
                this.tdsPercentage = parseFloat(percentageField.value) || 0;
            }
        }
    }
}

function resetForm() {
    if (confirm('Are you sure you want to reset the form? All unsaved changes will be lost.')) {
        document.querySelector('form').reset();
    }
}

function validateTDSCode() {
    const codeInput = document.getElementById('{{ form.tds_code.id_for_label }}');
    const code = codeInput.value.trim();
    
    // Common TDS section patterns
    const validPatterns = [
        /^194[A-Z]$/i,      // Section 194A, 194B, etc.
        /^195$/i,           // Section 195
        /^196[A-D]$/i,      // Section 196A, 196B, etc.
        /^206[A-Z]+$/i      // New sections like 206AA, 206AB
    ];
    
    const isValid = validPatterns.some(pattern => pattern.test(code));
    
    if (isValid) {
        alert('✓ TDS code format appears valid');
        codeInput.classList.remove('border-red-500');
        codeInput.classList.add('border-green-500');
    } else {
        alert('⚠ Please verify the TDS code format (e.g., 194A, 194C, 194J)');
        codeInput.classList.remove('border-green-500');
        codeInput.classList.add('border-yellow-500');
    }
}

// Real-time updates
document.addEventListener('DOMContentLoaded', function() {
    const percentageField = document.getElementById('{{ form.tds_percentage.id_for_label }}');
    
    if (percentageField) {
        percentageField.addEventListener('input', function() {
            const alpineData = Alpine.$data(document.querySelector('[x-data="tdsCodeForm()"]'));
            if (alpineData) {
                alpineData.updateTDSPercentage();
            }
        });
    }
    
    lucide.createIcons();
});
</script>
{% endblock %}