{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="bg-indigo-600 text-white px-4 py-2 rounded-t-lg">
        <h1 class="text-lg font-semibold">{{ title }}</h1>
    </div>
    
    <!-- Work Order Information -->
    <div class="bg-white border border-gray-300 p-4">
        <div class="mb-4">
            <span class="font-semibold">WO No:</span> 
            <span class="font-bold text-green-600">{{ wono }}</span>
        </div>
    </div>

    <!-- BOM Components List -->
    <div class="bg-white border-l border-r border-b border-gray-300 p-4">
        {% if enhanced_items %}
        <div class="overflow-x-auto">
            <table class="min-w-full border border-gray-300">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-2 text-xs font-medium text-gray-500 uppercase border-b">SN</th>
                        <th class="px-4 py-2 text-xs font-medium text-gray-500 uppercase border-b">Item Code</th>
                        <th class="px-4 py-2 text-xs font-medium text-gray-500 uppercase border-b">Description</th>
                        <th class="px-4 py-2 text-xs font-medium text-gray-500 uppercase border-b">UOM</th>
                        <th class="px-4 py-2 text-xs font-medium text-gray-500 uppercase border-b">BOM Qty</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item_data in enhanced_items %}
                    <tr class="border-b hover:bg-gray-50">
                        <td class="px-4 py-2 text-center">{{ forloop.counter }}</td>
                        <td class="px-4 py-2 text-center">
                            <a href="{% url 'machinery:schedule_items' wono=wono item_id=item_data.bom_item.itemid.id %}"
                               class="text-blue-600 hover:text-blue-800 font-medium">
                                {{ item_data.item.itemcode|default:"N/A" }}
                            </a>
                        </td>
                        <td class="px-4 py-2">{{ item_data.item.description|default:"N/A" }}</td>
                        <td class="px-4 py-2 text-center">{{ item_data.item.uombasic|default:"N/A" }}</td>
                        <td class="px-4 py-2 text-center">{{ item_data.total_qty }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-8">
            <p class="text-gray-500 text-lg">No BOM components found for split scheduling.</p>
            <p class="text-gray-400 text-sm">This item may not have child components.</p>
        </div>
        {% endif %}
    </div>

    <!-- Action Buttons -->
    <div class="bg-white border-l border-r border-b border-gray-300 p-4 text-center">
        <a href="{% url 'machinery:schedule_create_detail' wono=wono %}" 
           class="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700">
            Cancel
        </a>
    </div>
</div>
{% endblock %}