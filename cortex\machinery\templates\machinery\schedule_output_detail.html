{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="bg-indigo-600 text-white px-4 py-2 rounded-t-lg">
        <h1 class="text-lg font-semibold">{{ title }}</h1>
    </div>
    
    <!-- Item Information -->
    <div class="bg-white border border-gray-300 p-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <span class="font-semibold">Item Code:</span> 
                <span class="font-bold text-blue-600">{{ item.itemcode }}</span>
            </div>
            <div>
                <span class="font-semibold">WO No:</span> 
                <span class="font-bold text-green-600">{{ wono }}</span>
            </div>
        </div>
        <div class="mt-2">
            <span class="font-semibold">Description:</span> 
            <span class="font-bold">{{ item.description }}</span>
        </div>
    </div>

    <!-- Scheduled Jobs -->
    <div class="bg-white border-l border-r border-b border-gray-300 p-4">
        <h3 class="text-lg font-semibold mb-4">Scheduled Jobs for Output Recording</h3>
        
        {% if schedule_details %}
        <div class="overflow-x-auto">
            <table class="min-w-full border border-gray-300">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-2 text-xs font-medium text-gray-500 uppercase border-b">SN</th>
                        <th class="px-4 py-2 text-xs font-medium text-gray-500 uppercase border-b">Machine</th>
                        <th class="px-4 py-2 text-xs font-medium text-gray-500 uppercase border-b">Process</th>
                        <th class="px-4 py-2 text-xs font-medium text-gray-500 uppercase border-b">Batch No</th>
                        <th class="px-4 py-2 text-xs font-medium text-gray-500 uppercase border-b">Scheduled Qty</th>
                        <th class="px-4 py-2 text-xs font-medium text-gray-500 uppercase border-b">From Date</th>
                        <th class="px-4 py-2 text-xs font-medium text-gray-500 uppercase border-b">To Date</th>
                        <th class="px-4 py-2 text-xs font-medium text-gray-500 uppercase border-b">Output Qty</th>
                        <th class="px-4 py-2 text-xs font-medium text-gray-500 uppercase border-b">UOM</th>
                        <th class="px-4 py-2 text-xs font-medium text-gray-500 uppercase border-b">Action</th>
                    </tr>
                </thead>
                <tbody>
                    {% for detail in schedule_details %}
                    <tr class="border-b hover:bg-gray-50">
                        <td class="px-4 py-2 text-center">{{ forloop.counter }}</td>
                        <td class="px-4 py-2">Machine {{ detail.machineid }}</td>
                        <td class="px-4 py-2 text-center">{{ detail.process }}</td>
                        <td class="px-4 py-2 text-center">{{ detail.batchno }}</td>
                        <td class="px-4 py-2 text-center">{{ detail.qty }}</td>
                        <td class="px-4 py-2 text-center">{{ detail.fromdate }}</td>
                        <td class="px-4 py-2 text-center">{{ detail.todate }}</td>
                        <td class="px-4 py-2">
                            <form hx-post="{% url 'machinery:record_job_output' %}"
                                  hx-target="#job-completions-{{ detail.id }}"
                                  class="flex space-x-2">
                                {% csrf_token %}
                                <input type="hidden" name="schedule_detail_id" value="{{ detail.id }}">
                                <input type="number" 
                                       name="output_qty" 
                                       step="0.01" 
                                       placeholder="Qty"
                                       class="w-20 border rounded px-2 py-1 text-sm"
                                       required>
                            </form>
                        </td>
                        <td class="px-4 py-2">
                            <select name="uom" form="output-form-{{ detail.id }}" class="w-full border rounded px-2 py-1 text-sm">
                                <option value="1">{{ item.uombasic }}</option>
                            </select>
                        </td>
                        <td class="px-4 py-2 text-center">
                            <button type="submit" 
                                    form="output-form-{{ detail.id }}"
                                    class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700">
                                Record
                            </button>
                        </td>
                    </tr>
                    <tr id="job-completions-{{ detail.id }}">
                        <td colspan="10" class="px-4 py-2">
                            {% include 'machinery/partials/job_completions.html' with completions=job_completions schedule_detail=detail %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-8">
            <p class="text-gray-500 text-lg">No scheduled jobs found for output recording.</p>
            <p class="text-gray-400 text-sm">Please ensure jobs are scheduled and not yet released.</p>
        </div>
        {% endif %}
    </div>

    <!-- Action Buttons -->
    <div class="bg-white border-l border-r border-b border-gray-300 p-4 text-center">
        <a href="{% url 'machinery:schedule_output' %}" 
           class="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700">
            Back to Output Selection
        </a>
    </div>
</div>
{% endblock %}