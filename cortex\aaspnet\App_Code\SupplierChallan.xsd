﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="SupplierChallan" targetNamespace="http://tempuri.org/SupplierChallan.xsd" xmlns:mstns="http://tempuri.org/SupplierChallan.xsd" xmlns="http://tempuri.org/SupplierChallan.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="SupplierChallan" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="SupplierChallan" msprop:Generator_DataSetName="SupplierChallan">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" msprop:Generator_UserColumnName="Id" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInTable="IdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="SysTime" msprop:Generator_UserColumnName="SysTime" msprop:Generator_ColumnPropNameInRow="SysTime" msprop:Generator_ColumnVarNameInTable="columnSysTime" msprop:Generator_ColumnPropNameInTable="SysTimeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="SCDate" msprop:Generator_UserColumnName="SCDate" msprop:Generator_ColumnPropNameInRow="SCDate" msprop:Generator_ColumnVarNameInTable="columnSCDate" msprop:Generator_ColumnPropNameInTable="SCDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="SCNo" msprop:Generator_UserColumnName="SCNo" msprop:Generator_ColumnPropNameInRow="SCNo" msprop:Generator_ColumnVarNameInTable="columnSCNo" msprop:Generator_ColumnPropNameInTable="SCNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ItemCode" msprop:Generator_UserColumnName="ItemCode" msprop:Generator_ColumnPropNameInRow="ItemCode" msprop:Generator_ColumnVarNameInTable="columnItemCode" msprop:Generator_ColumnPropNameInTable="ItemCodeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ManfDesc" msprop:Generator_UserColumnName="ManfDesc" msprop:Generator_ColumnPropNameInRow="ManfDesc" msprop:Generator_ColumnVarNameInTable="columnManfDesc" msprop:Generator_ColumnPropNameInTable="ManfDescColumn" type="xs:string" minOccurs="0" />
              <xs:element name="UOM" msprop:Generator_UserColumnName="UOM" msprop:Generator_ColumnPropNameInRow="UOM" msprop:Generator_ColumnVarNameInTable="columnUOM" msprop:Generator_ColumnPropNameInTable="UOMColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ChallanQty" msprop:Generator_UserColumnName="ChallanQty" msprop:Generator_ColumnPropNameInRow="ChallanQty" msprop:Generator_ColumnVarNameInTable="columnChallanQty" msprop:Generator_ColumnPropNameInTable="ChallanQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Rate" msprop:Generator_UserColumnName="Rate" msprop:Generator_ColumnPropNameInRow="Rate" msprop:Generator_ColumnVarNameInTable="columnRate" msprop:Generator_ColumnPropNameInTable="RateColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Remarks" msprop:Generator_UserColumnName="Remarks" msprop:Generator_ColumnPropNameInRow="Remarks" msprop:Generator_ColumnVarNameInTable="columnRemarks" msprop:Generator_ColumnPropNameInTable="RemarksColumn" type="xs:string" minOccurs="0" />
              <xs:element name="VehicleNo" msprop:Generator_UserColumnName="VehicleNo" msprop:Generator_ColumnPropNameInRow="VehicleNo" msprop:Generator_ColumnVarNameInTable="columnVehicleNo" msprop:Generator_ColumnPropNameInTable="VehicleNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Transpoter" msprop:Generator_UserColumnName="Transpoter" msprop:Generator_ColumnPropNameInRow="Transpoter" msprop:Generator_ColumnVarNameInTable="columnTranspoter" msprop:Generator_ColumnPropNameInTable="TranspoterColumn" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>