<!-- accounts/templates/accounts/transactions/credit_note_form.html -->
<!-- Credit Note Create/Edit Form Template -->
<!-- Task Group 6: Credit & Debit Management - Credit Note Form (Task 6.3) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{{ form_action }} Credit Note - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-indigo-600 to-sap-indigo-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="minus-circle" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">{{ form_action }} Credit Note</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Create or modify credit note for customer adjustments</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:credit_note_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
                {% if form_action == "Update" and credit_note %}
                <a href="{% url 'accounts:credit_note_detail' credit_note.id %}" 
                   class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="eye" class="w-4 h-4 inline mr-2"></i>
                    View Credit Note
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6" x-data="creditNoteManager()">
    
    <!-- Credit Note Guidelines -->
    <div class="mb-6 bg-sap-indigo-50 border border-sap-indigo-200 rounded-lg p-4">
        <div class="flex">
            <i data-lucide="info" class="w-5 h-5 text-sap-indigo-400 mr-3"></i>
            <div class="text-sm text-sap-indigo-800">
                <p class="font-medium mb-1">Credit Note Guidelines</p>
                <p>Credit notes are issued to reduce customer debt due to returns, discounts, or billing adjustments. Ensure all details are accurate before submission.</p>
            </div>
        </div>
    </div>

    <form method="post" class="space-y-8" @submit="submitCreditNote">
        {% csrf_token %}
        
        <!-- Error Messages -->
        {% if form.non_field_errors %}
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <i data-lucide="alert-circle" class="w-5 h-5 text-red-400 mr-3"></i>
                <div>
                    <h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                    <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                        {% for error in form.non_field_errors %}
                        <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
            
            <!-- Main Credit Note Form -->
            <div class="xl:col-span-2 space-y-6">
                
                <!-- Credit Note Header Information -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                            <i data-lucide="file-text" class="w-5 h-5 mr-2 text-sap-indigo-600"></i>
                            Credit Note Details
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Credit Note Number -->
                            <div>
                                <label for="{{ form.credit_note_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.credit_note_no.label }}
                                    <span class="text-red-500">*</span>
                                </label>
                                {{ form.credit_note_no|add_class:"block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-indigo-500 focus:border-sap-indigo-500" }}
                                {% if form.credit_note_no.errors %}
                                <p class="text-sm text-red-600 mt-1">{{ form.credit_note_no.errors.0 }}</p>
                                {% endif %}
                            </div>

                            <!-- Credit Date -->
                            <div>
                                <label for="{{ form.credit_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.credit_date.label }}
                                    <span class="text-red-500">*</span>
                                </label>
                                {{ form.credit_date|add_class:"block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-indigo-500 focus:border-sap-indigo-500" }}
                                {% if form.credit_date.errors %}
                                <p class="text-sm text-red-600 mt-1">{{ form.credit_date.errors.0 }}</p>
                                {% endif %}
                            </div>

                            <!-- Customer Name -->
                            <div>
                                <label for="{{ form.customer_name.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.customer_name.label }}
                                    <span class="text-red-500">*</span>
                                </label>
                                {{ form.customer_name|add_class:"block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-indigo-500 focus:border-sap-indigo-500" }}
                                {% if form.customer_name.errors %}
                                <p class="text-sm text-red-600 mt-1">{{ form.customer_name.errors.0 }}</p>
                                {% endif %}
                            </div>

                            <!-- Customer Code -->
                            <div>
                                <label for="{{ form.customer_code.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.customer_code.label }}
                                </label>
                                {{ form.customer_code|add_class:"block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-indigo-500 focus:border-sap-indigo-500" }}
                            </div>

                            <!-- Reference Invoice Number -->
                            <div>
                                <label for="{{ form.reference_invoice_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.reference_invoice_no.label }}
                                </label>
                                {{ form.reference_invoice_no|add_class:"block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-indigo-500 focus:border-sap-indigo-500" }}
                            </div>

                            <!-- Credit Amount -->
                            <div>
                                <label for="{{ form.credit_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.credit_amount.label }}
                                    <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-sap-gray-500">₹</span>
                                    {{ form.credit_amount|add_class:"block w-full pl-8 pr-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-indigo-500 focus:border-sap-indigo-500" }}
                                </div>
                                {% if form.credit_amount.errors %}
                                <p class="text-sm text-red-600 mt-1">{{ form.credit_amount.errors.0 }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Credit Note Reason and Details -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                            <i data-lucide="message-square" class="w-5 h-5 mr-2 text-sap-indigo-600"></i>
                            Reason and Description
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-6">
                            <!-- Reason -->
                            <div>
                                <label for="{{ form.reason.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.reason.label }}
                                    <span class="text-red-500">*</span>
                                </label>
                                {{ form.reason|add_class:"block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-indigo-500 focus:border-sap-indigo-500" }}
                                {% if form.reason.errors %}
                                <p class="text-sm text-red-600 mt-1">{{ form.reason.errors.0 }}</p>
                                {% endif %}
                            </div>

                            <!-- Description -->
                            <div>
                                <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.description.label }}
                                </label>
                                {{ form.description|add_class:"block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-indigo-500 focus:border-sap-indigo-500" }}
                                {% if form.description.errors %}
                                <p class="text-sm text-red-600 mt-1">{{ form.description.errors.0 }}</p>
                                {% endif %}
                                <p class="text-xs text-sap-gray-500 mt-1">Provide detailed explanation for the credit note</p>
                            </div>

                            <!-- Approved By (if editing) -->
                            {% if form_action == "Update" and form.approved_by %}
                            <div>
                                <label for="{{ form.approved_by.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.approved_by.label }}
                                </label>
                                {{ form.approved_by|add_class:"block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-indigo-500 focus:border-sap-indigo-500" }}
                            </div>
                            {% endif %}

                            <!-- Approval Date (if editing) -->
                            {% if form_action == "Update" and form.approval_date %}
                            <div>
                                <label for="{{ form.approval_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.approval_date.label }}
                                </label>
                                {{ form.approval_date|add_class:"block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-indigo-500 focus:border-sap-indigo-500" }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Credit Note Summary and Additional Information -->
            <div class="space-y-6">
                
                <!-- Credit Note Summary -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                            <i data-lucide="calculator" class="w-5 h-5 mr-2 text-sap-indigo-600"></i>
                            Credit Note Summary
                        </h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <!-- Credit Amount Display -->
                        <div class="bg-sap-indigo-50 rounded-lg p-4">
                            <div class="text-center">
                                <p class="text-sm text-sap-indigo-600 mb-1">Credit Amount</p>
                                <p class="text-2xl font-bold text-sap-indigo-800" x-text="formatCurrency(creditAmount)">₹0.00</p>
                            </div>
                        </div>
                        
                        <!-- Status Information -->
                        {% if form_action == "Update" and credit_note %}
                        <div class="border-t border-sap-gray-200 pt-4">
                            <div class="text-sm">
                                <div class="flex justify-between mb-2">
                                    <span class="text-sap-gray-600">Status:</span>
                                    <span class="font-medium capitalize">{{ credit_note.status|default:"Draft" }}</span>
                                </div>
                                {% if credit_note.created_date %}
                                <div class="flex justify-between mb-2">
                                    <span class="text-sap-gray-600">Created:</span>
                                    <span class="font-medium">{{ credit_note.created_date|date:"d M Y" }}</span>
                                </div>
                                {% endif %}
                                {% if credit_note.approved_by %}
                                <div class="flex justify-between">
                                    <span class="text-sap-gray-600">Approved By:</span>
                                    <span class="font-medium">{{ credit_note.approved_by }}</span>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Credit Note Guidelines -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <h3 class="text-lg font-medium text-sap-gray-800">Guidelines</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3 text-sm text-sap-gray-600">
                            <div class="flex items-start space-x-2">
                                <i data-lucide="check-circle" class="w-4 h-4 text-sap-green-600 mt-0.5 flex-shrink-0"></i>
                                <span>Ensure the credit amount does not exceed the original invoice amount</span>
                            </div>
                            <div class="flex items-start space-x-2">
                                <i data-lucide="check-circle" class="w-4 h-4 text-sap-green-600 mt-0.5 flex-shrink-0"></i>
                                <span>Provide clear reason for the credit note issuance</span>
                            </div>
                            <div class="flex items-start space-x-2">
                                <i data-lucide="check-circle" class="w-4 h-4 text-sap-green-600 mt-0.5 flex-shrink-0"></i>
                                <span>Reference the original invoice number if applicable</span>
                            </div>
                            <div class="flex items-start space-x-2">
                                <i data-lucide="check-circle" class="w-4 h-4 text-sap-green-600 mt-0.5 flex-shrink-0"></i>
                                <span>Credit notes require approval before processing</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <h3 class="text-lg font-medium text-sap-gray-800">Quick Actions</h3>
                    </div>
                    <div class="p-6 space-y-3">
                        <button type="button" @click="calculateTaxImpact"
                                class="w-full bg-sap-blue-100 hover:bg-sap-blue-200 text-sap-blue-800 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="calculator" class="w-4 h-4 inline mr-2"></i>
                            Calculate Tax Impact
                        </button>
                        <button type="button" @click="loadInvoiceDetails"
                                class="w-full bg-sap-green-100 hover:bg-sap-green-200 text-sap-green-800 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="search" class="w-4 h-4 inline mr-2"></i>
                            Load Invoice Details
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
            <div class="flex items-center space-x-4">
                <a href="{% url 'accounts:credit_note_list' %}" 
                   class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-6 py-2 rounded-lg font-medium transition-colors">
                    Cancel
                </a>
                <button type="button" @click="saveDraft"
                        class="bg-sap-yellow-600 hover:bg-sap-yellow-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="save" class="w-4 h-4 inline mr-2"></i>
                    Save as Draft
                </button>
            </div>
            
            <div class="flex items-center space-x-3">
                <button type="button" @click="previewCreditNote"
                        class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="eye" class="w-4 h-4 inline mr-2"></i>
                    Preview
                </button>
                <button type="submit" 
                        class="bg-sap-indigo-600 hover:bg-sap-indigo-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                    {{ form_action }} Credit Note
                </button>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
function creditNoteManager() {
    return {
        creditAmount: {{ form.credit_amount.value|default:0 }},
        
        init() {
            lucide.createIcons();
            this.updateCreditAmount();
        },
        
        updateCreditAmount() {
            const amountInput = document.querySelector('input[name="credit_amount"]');
            if (amountInput) {
                amountInput.addEventListener('input', (e) => {
                    this.creditAmount = parseFloat(e.target.value) || 0;
                });
            }
        },
        
        formatCurrency(amount) {
            return '₹' + (amount || 0).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        },
        
        calculateTaxImpact() {
            console.log('Calculating tax impact...');
            // Implementation for tax impact calculation
        },
        
        loadInvoiceDetails() {
            const invoiceNo = document.querySelector('input[name="reference_invoice_no"]').value;
            if (!invoiceNo) {
                alert('Please enter a reference invoice number first.');
                return;
            }
            console.log('Loading invoice details for:', invoiceNo);
            // Implementation for loading invoice details
        },
        
        saveDraft() {
            console.log('Saving draft...');
            // Implementation for saving draft
        },
        
        previewCreditNote() {
            console.log('Previewing credit note...');
            // Implementation for preview modal
        },
        
        submitCreditNote(event) {
            if (!this.creditAmount || this.creditAmount <= 0) {
                event.preventDefault();
                alert('Please enter a valid credit amount.');
                return false;
            }
            
            const reason = document.querySelector('input[name="reason"]').value;
            if (!reason || reason.trim() === '') {
                event.preventDefault();
                alert('Please provide a reason for the credit note.');
                return false;
            }
            
            return true;
        }
    }
}

// Initialize lucide icons on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}