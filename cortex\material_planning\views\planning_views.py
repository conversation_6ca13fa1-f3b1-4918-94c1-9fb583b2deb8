"""
Material Planning Views - Task Group 2
Core planning functionality views with comprehensive workflow management
"""

from django.shortcuts import redirect
from django.views.generic import ListView, UpdateView, DetailView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.db.models import Q
from django.urls import reverse

from ..models import Material, MaterialDetail


class MaterialPlanListView(LoginRequiredMixin, ListView):
    """List all material plans with advanced filtering and search"""
    model = Material
    template_name = 'material_planning/planning/plan_list.html'
    context_object_name = 'plans'
    paginate_by = 25

    def get_queryset(self):
        queryset = Material.objects.all().select_related('company', 'finyear')
        
        # Search functionality
        search_query = self.request.GET.get('search')
        if search_query:
            queryset = queryset.filter(
                Q(plno__icontains=search_query) |
                Q(wono__icontains=search_query)
            )
        
        return queryset.order_by('-id')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_query'] = self.request.GET.get('search', '')
        
        # Calculate plan statistics
        all_plans = Material.objects.all()
        context['total_plans'] = all_plans.count()
        context['active_plans'] = all_plans.count()  # All are considered active for now
        context['draft_plans'] = 0  # No draft status in existing model
        context['overdue_plans'] = 0  # No overdue status in existing model
        
        return context


class MaterialPlanDetailView(LoginRequiredMixin, DetailView):
    """Display detailed view of a material plan"""
    model = Material
    template_name = 'material_planning/planning/plan_detail.html'
    context_object_name = 'plan'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Add related material details
        context['material_details'] = MaterialDetail.objects.filter(material=self.object)
        return context


class MaterialPlanUpdateView(LoginRequiredMixin, UpdateView):
    """Edit an existing material plan"""
    model = Material
    template_name = 'material_planning/planning/plan_form.html'
    fields = ['plno', 'wono']
    
    def get_success_url(self):
        return reverse('material_planning:plan_detail', kwargs={'pk': self.object.pk})

    def form_valid(self, form):
        messages.success(self.request, 'Material plan updated successfully.')
        return super().form_valid(form)


class MaterialPlanCopyView(LoginRequiredMixin, DetailView):
    """Copy an existing material plan"""
    model = Material
    template_name = 'material_planning/planning/plan_copy.html'
    context_object_name = 'original_plan'

    def post(self, request, *args, **kwargs):
        original = self.get_object()
        
        # Create a copy of the material plan
        new_plan = Material.objects.create(
            sysdate=timezone.now().strftime('%Y-%m-%d'),
            systime=timezone.now().strftime('%H:%M:%S'),
            company=original.company,
            session=request.user,
            finyear=original.finyear,
            plno=f"{original.plno}_COPY",
            wono=f"{original.wono}_COPY"
        )
        
        # Copy related material details
        for detail in MaterialDetail.objects.filter(material=original):
            MaterialDetail.objects.create(
                material=new_plan,
                itemid=detail.itemid,
                rm=detail.rm,
                pro=detail.pro,
                fin=detail.fin
            )
        
        messages.success(request, f'Material plan copied successfully. New plan number: {new_plan.plno}')
        return redirect('material_planning:plan_detail', pk=new_plan.pk)