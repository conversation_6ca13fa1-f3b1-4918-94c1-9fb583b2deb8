from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import timedelta
from ..models import (
    WISMaster, WISActualRunMaterial, WISActualRunAssembly,
    WISDryRun, WISReleaseDetails
)


class WISMasterForm(forms.ModelForm):
    """Form for creating and editing WIS Master records"""
    
    class Meta:
        model = WISMaster
        fields = [
            'work_order_number', 'session_id'
        ]
        widgets = {
            'work_order_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Work Order Number',
                'list': 'work-order-list'
            }),
            'session_id': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Session ID'
            })
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.company = kwargs.pop('company', None)
        self.financial_year = kwargs.pop('financial_year', None)
        super().__init__(*args, **kwargs)

    def clean_work_order_number(self):
        work_order_number = self.cleaned_data.get('work_order_number')
        if not work_order_number:
            raise ValidationError("Work order number is required.")
        return work_order_number.upper().strip()

    def save(self, commit=True):
        instance = super().save(commit=False)
        
        if not instance.pk:
            # Set system fields for new instances
            instance.company = self.company
            instance.financial_year = self.financial_year
            # Set defaults for sys_date and sys_time
            import datetime
            now = timezone.now()
            instance.sys_date = now.strftime('%Y-%m-%d')
            instance.sys_time = now.strftime('%H:%M:%S')
            
        if commit:
            instance.save()
        
        return instance


class WISActualRunMaterialForm(forms.ModelForm):
    """Form for WIS actual run material processing"""
    
    class Meta:
        model = WISActualRunMaterial
        fields = [
            'sequence_number', 'item_code', 'item_description', 'unit_of_measure',
            'location_code', 'planned_quantity', 'actual_quantity', 'consumed_quantity',
            'wastage_quantity', 'rate_per_unit', 'batch_number', 'lot_number',
            'quality_check_required', 'remarks'
        ]
        widgets = {
            'sequence_number': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'min': '1'
            }),
            'item_code': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Item Code',
                'list': 'item-code-list'
            }),
            'item_description': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Item Description'
            }),
            'unit_of_measure': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'UOM'
            }),
            'location_code': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Storage Location',
                'list': 'location-list'
            }),
            'planned_quantity': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'actual_quantity': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'consumed_quantity': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'wastage_quantity': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'rate_per_unit': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'batch_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Batch Number'
            }),
            'lot_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Lot Number'
            }),
            'quality_check_required': forms.CheckboxInput(attrs={
                'class': 'rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500'
            }),
            'remarks': forms.Textarea(attrs={
                'rows': 2,
                'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md',
                'placeholder': 'Processing remarks or notes'
            })
        }

    def clean_planned_quantity(self):
        planned_quantity = self.cleaned_data.get('planned_quantity')
        if planned_quantity is not None and planned_quantity <= 0:
            raise ValidationError("Planned quantity must be greater than zero.")
        return planned_quantity

    def clean_item_code(self):
        item_code = self.cleaned_data.get('item_code')
        if not item_code:
            raise ValidationError("Item code is required.")
        return item_code.upper().strip()

    def clean(self):
        cleaned_data = super().clean()
        planned_quantity = cleaned_data.get('planned_quantity', 0)
        actual_quantity = cleaned_data.get('actual_quantity', 0)
        consumed_quantity = cleaned_data.get('consumed_quantity', 0)
        wastage_quantity = cleaned_data.get('wastage_quantity', 0)
        
        # Validate quantities make sense
        if consumed_quantity + wastage_quantity > actual_quantity:
            raise ValidationError("Consumed quantity + wastage quantity cannot exceed actual quantity.")
        
        return cleaned_data


class WISActualRunAssemblyForm(forms.ModelForm):
    """Form for WIS actual run assembly processing"""
    
    class Meta:
        model = WISActualRunAssembly
        fields = [
            'sequence_number', 'assembly_code', 'assembly_description', 'level_number',
            'planned_quantity', 'actual_quantity', 'completed_quantity', 'rejected_quantity',
            'work_center_code', 'operation_sequence', 'setup_time_planned', 'processing_time_planned',
            'operator_id', 'machine_id', 'quality_check_required', 'remarks'
        ]
        widgets = {
            'sequence_number': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'min': '1'
            }),
            'assembly_code': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Assembly Code',
                'list': 'assembly-code-list'
            }),
            'assembly_description': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Assembly Description'
            }),
            'level_number': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'min': '1',
                'max': '10'
            }),
            'planned_quantity': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'actual_quantity': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'completed_quantity': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'rejected_quantity': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'work_center_code': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Work Center Code',
                'list': 'workcenter-list'
            }),
            'operation_sequence': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'min': '1'
            }),
            'setup_time_planned': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Setup time (minutes)',
                'step': '0.1',
                'min': '0'
            }),
            'processing_time_planned': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Processing time (minutes)',
                'step': '0.1',
                'min': '0'
            }),
            'operator_id': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Operator ID'
            }),
            'machine_id': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Machine ID'
            }),
            'quality_check_required': forms.CheckboxInput(attrs={
                'class': 'rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500'
            }),
            'remarks': forms.Textarea(attrs={
                'rows': 2,
                'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md',
                'placeholder': 'Assembly processing remarks'
            })
        }

    def clean_planned_quantity(self):
        planned_quantity = self.cleaned_data.get('planned_quantity')
        if planned_quantity is not None and planned_quantity <= 0:
            raise ValidationError("Planned quantity must be greater than zero.")
        return planned_quantity

    def clean_assembly_code(self):
        assembly_code = self.cleaned_data.get('assembly_code')
        if not assembly_code:
            raise ValidationError("Assembly code is required.")
        return assembly_code.upper().strip()

    def clean(self):
        cleaned_data = super().clean()
        completed_quantity = cleaned_data.get('completed_quantity', 0)
        rejected_quantity = cleaned_data.get('rejected_quantity', 0)
        actual_quantity = cleaned_data.get('actual_quantity', 0)
        
        # Validate quantities make sense
        if completed_quantity + rejected_quantity > actual_quantity:
            raise ValidationError("Completed quantity + rejected quantity cannot exceed actual quantity.")
        
        return cleaned_data


class WISDryRunForm(forms.ModelForm):
    """Form for WIS dry run simulation"""
    
    class Meta:
        model = WISDryRun
        fields = [
            'work_order_number', 'dry_run_number', 'simulation_type', 'simulation_status', 'remarks'
        ]
        widgets = {
            'work_order_number': forms.TextInput(attrs={
                'class': 'sap-input',
                'placeholder': 'Work Order Number',
                'list': 'work-order-list'
            }),
            'dry_run_number': forms.TextInput(attrs={
                'class': 'sap-input',
                'placeholder': 'Auto-generated if left blank'
            }),
            'simulation_type': forms.Select(attrs={
                'class': 'sap-select'
            }),
            'simulation_status': forms.Select(attrs={
                'class': 'sap-select'
            }),
            'remarks': forms.Textarea(attrs={
                'rows': 3,
                'class': 'sap-input',
                'placeholder': 'Simulation remarks and assumptions'
            })
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.company = kwargs.pop('company', None)
        self.financial_year = kwargs.pop('financial_year', None)
        super().__init__(*args, **kwargs)

    def clean_work_order_number(self):
        work_order_number = self.cleaned_data.get('work_order_number')
        if not work_order_number:
            raise ValidationError("Work order number is required.")
        return work_order_number.upper().strip()

    def save(self, commit=True):
        instance = super().save(commit=False)
        
        if not instance.pk:
            # Set system fields for new instances
            instance.company = self.company
            instance.financial_year = self.financial_year
            instance.created_by = self.user
            
            # Set required fields with defaults
            instance.dry_run_date = timezone.now().date()
            instance.planned_start_date = timezone.now().date() + timedelta(days=1)
            instance.planned_end_date = timezone.now().date() + timedelta(days=7)
            instance.planned_quantity = 1.0
            
        if commit:
            instance.save()
            # Generate dry run number if not set
            if not instance.dry_run_number:
                instance.generate_dry_run_number()
                instance.save()
        
        return instance


class WISReleaseDetailsForm(forms.ModelForm):
    """Form for WIS release details"""
    
    class Meta:
        model = WISReleaseDetails
        fields = [
            'release_date', 'release_time', 'work_order_number', 'release_type',
            'release_phase', 'quantity_released', 'priority_override', 'override_reason',
            'estimated_completion_time', 'remarks'
        ]
        widgets = {
            'release_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md'
            }),
            'release_time': forms.TimeInput(attrs={
                'type': 'time',
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md'
            }),
            'work_order_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Work Order Number'
            }),
            'release_type': forms.Select(attrs={
                'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'release_phase': forms.Select(attrs={
                'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'quantity_released': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'priority_override': forms.CheckboxInput(attrs={
                'class': 'rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500'
            }),
            'override_reason': forms.Textarea(attrs={
                'rows': 2,
                'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md',
                'placeholder': 'Reason for priority override'
            }),
            'estimated_completion_time': forms.DateTimeInput(attrs={
                'type': 'datetime-local',
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md'
            }),
            'remarks': forms.Textarea(attrs={
                'rows': 3,
                'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md',
                'placeholder': 'Release remarks and instructions'
            })
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.company = kwargs.pop('company', None)
        self.financial_year = kwargs.pop('financial_year', None)
        super().__init__(*args, **kwargs)
        
        # Set default values
        if not self.instance.pk:
            now = timezone.now()
            self.fields['release_date'].initial = now.date()
            self.fields['release_time'].initial = now.time()

    def clean_quantity_released(self):
        quantity_released = self.cleaned_data.get('quantity_released')
        if quantity_released is not None and quantity_released <= 0:
            raise ValidationError("Quantity released must be greater than zero.")
        return quantity_released

    def clean(self):
        cleaned_data = super().clean()
        priority_override = cleaned_data.get('priority_override')
        override_reason = cleaned_data.get('override_reason')
        
        if priority_override and not override_reason:
            raise ValidationError("Override reason is required when priority override is enabled.")
        
        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        
        if not instance.pk:
            # Set system fields for new instances
            instance.company = self.company
            instance.financial_year = self.financial_year
            instance.released_by = self.user
            
        if commit:
            instance.save()
        
        return instance


# Search forms commented out due to simplified model
# class WISSearchForm(forms.Form):
#     pass

# class WISDryRunSearchForm(forms.Form):
#     pass