{% extends "core/base.html" %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
/* SAP Fiori-inspired CSS for Enquiry Detail View */
.sap-detail-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
}

.sap-detail-header {
    background: linear-gradient(135deg, #0070f3 0%, #0052cc 100%);
    color: white;
    padding: 32px 0;
    margin-bottom: 24px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 112, 243, 0.15);
}

.sap-detail-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.sap-breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
    font-size: 0.875rem;
}

.sap-breadcrumb a {
    color: #0070f3;
    text-decoration: none;
}

.sap-breadcrumb a:hover {
    text-decoration: underline;
}

.sap-action-buttons {
    display: flex;
    gap: 12px;
}

.sap-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.sap-btn-primary {
    background: linear-gradient(135deg, #0070f3 0%, #0052cc 100%);
    color: white;
}

.sap-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 112, 243, 0.3);
}

.sap-btn-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.sap-btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(245, 158, 11, 0.3);
}

.sap-btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.sap-btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(16, 185, 129, 0.3);
}

.sap-btn-secondary {
    background: #6b7280;
    color: white;
}

.sap-btn-secondary:hover {
    background: #4b5563;
}

.sap-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;
    margin-bottom: 24px;
}

.sap-info-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid #e5e7eb;
    overflow: hidden;
}

.sap-info-header {
    background: #f8fafc;
    padding: 16px 20px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    gap: 10px;
}

.sap-info-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
}

.sap-info-body {
    padding: 20px;
}

.sap-info-row {
    display: flex;
    justify-content: space-between;
    align-items: start;
    padding: 8px 0;
    border-bottom: 1px solid #f3f4f6;
}

.sap-info-row:last-child {
    border-bottom: none;
}

.sap-info-label {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
    min-width: 140px;
}

.sap-info-value {
    color: #1f2937;
    font-size: 0.875rem;
    flex: 1;
    text-align: right;
}

.sap-status-display {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.875rem;
}

.sap-status-success {
    background: #d1fae5;
    color: #065f46;
}

.sap-status-warning {
    background: #fef3c7;
    color: #92400e;
}

.sap-status-info {
    background: #dbeafe;
    color: #1e40af;
}

.sap-attachments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;
}

.sap-attachment-card {
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.2s ease;
}

.sap-attachment-card:hover {
    background: #f1f5f9;
    border-color: #0070f3;
}

.sap-attachment-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    background: white;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e5e7eb;
}

.sap-attachment-info {
    flex: 1;
}

.sap-attachment-name {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.875rem;
    margin-bottom: 2px;
}

.sap-attachment-size {
    color: #6b7280;
    font-size: 0.75rem;
}

.sap-attachment-actions {
    display: flex;
    gap: 8px;
}

.sap-attachment-btn {
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.sap-attachment-download {
    background: #0070f3;
    color: white;
}

.sap-attachment-delete {
    background: #dc2626;
    color: white;
}

.sap-quotations-table {
    width: 100%;
    border-collapse: collapse;
}

.sap-quotations-table th {
    background: #f8fafc;
    padding: 12px;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
    font-size: 0.875rem;
}

.sap-quotations-table td {
    padding: 12px;
    border-bottom: 1px solid #f3f4f6;
    font-size: 0.875rem;
}

.sap-quotation-link {
    color: #0070f3;
    text-decoration: none;
    font-weight: 600;
}

.sap-quotation-link:hover {
    text-decoration: underline;
}

.enquiry-summary-card {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 2px solid #0ea5e9;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
}

.enquiry-summary-text {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #0f172a;
    margin: 0;
}

.empty-state {
    text-align: center;
    padding: 40px;
    color: #6b7280;
}

.empty-state-icon {
    font-size: 3rem;
    margin-bottom: 16px;
    opacity: 0.5;
    display: block;
}

/* Responsive design */
@media (max-width: 768px) {
    .sap-info-grid {
        grid-template-columns: 1fr;
    }
    
    .sap-detail-nav {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .sap-action-buttons {
        justify-content: center;
    }
    
    .sap-info-row {
        flex-direction: column;
        gap: 4px;
    }
    
    .sap-info-value {
        text-align: left;
    }
    
    .sap-attachments-grid {
        grid-template-columns: 1fr;
    }
}

/* Animation for smooth loading */
.fade-in {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
{% endblock %}

{% block content %}
<div class="sap-detail-container">
    <!-- Header Section -->
    <div class="sap-detail-header">
        <div class="text-center">
            <h1>{{ page_title }}</h1>
            <p>{{ page_subtitle }}</p>
        </div>
    </div>

    <!-- Navigation and Actions -->
    <div class="sap-detail-nav">
        <div class="sap-breadcrumb">
            <a href="{% url 'sales_distribution:enquiry_list' %}">📋 Enquiries</a>
            <span>›</span>
            <span>Enquiry #{{ enquiry.enqid }}</span>
        </div>
        
        <div class="sap-action-buttons">
            {% if can_edit %}
                <a href="{% url 'sales_distribution:enquiry_edit' enquiry.enqid %}" class="sap-btn sap-btn-warning">
                    ✏️ Edit Enquiry
                </a>
            {% endif %}
            
            {% if can_create_quotation %}
                <a href="{% url 'sales_distribution:quotation_create' enquiry.enqid %}" class="sap-btn sap-btn-success">
                    💰 Create Quotation
                </a>
            {% endif %}
            
            <a href="{% url 'sales_distribution:enquiry_list' %}" class="sap-btn sap-btn-secondary">
                ← Back to List
            </a>
        </div>
    </div>

    <!-- Status Display -->
    <div class="fade-in" style="margin-bottom: 24px;">
        <div class="sap-status-display {{ status_display.class }}">
            {{ status_display.icon }} {{ status_display.status }}
            <span style="margin-left: 8px; font-weight: normal; opacity: 0.8;">
                {{ status_display.description }}
            </span>
        </div>
    </div>

    <!-- Enquiry Summary -->
    {% if enquiry.enquiryfor %}
    <div class="enquiry-summary-card fade-in">
        <h3 style="margin: 0 0 12px; color: #0f172a; display: flex; align-items: center; gap: 8px;">
            <span>📝</span>
            Enquiry Description
        </h3>
        <p class="enquiry-summary-text">{{ enquiry.enquiryfor }}</p>
        {% if enquiry.remark %}
            <div style="margin-top: 16px; padding-top: 16px; border-top: 1px solid #0ea5e9;">
                <strong style="color: #0f172a;">Additional Remarks:</strong>
                <p style="margin: 4px 0 0; color: #374151;">{{ enquiry.remark }}</p>
            </div>
        {% endif %}
    </div>
    {% endif %}

    <!-- Information Cards Grid -->
    <div class="sap-info-grid">
        <!-- Customer Information -->
        <div class="sap-info-card fade-in">
            <div class="sap-info-header">
                <span style="font-size: 1.2rem;">👤</span>
                <h3>Customer Information</h3>
            </div>
            <div class="sap-info-body">
                <div class="sap-info-row">
                    <div class="sap-info-label">Customer Name:</div>
                    <div class="sap-info-value">{{ enquiry.customername|default:"—" }}</div>
                </div>
                <div class="sap-info-row">
                    <div class="sap-info-label">Customer ID:</div>
                    <div class="sap-info-value">{{ enquiry.customerid|default:"Auto-generated" }}</div>
                </div>
                <div class="sap-info-row">
                    <div class="sap-info-label">Contact Person:</div>
                    <div class="sap-info-value">{{ enquiry.contactperson|default:"—" }}</div>
                </div>
                <div class="sap-info-row">
                    <div class="sap-info-label">Email:</div>
                    <div class="sap-info-value">
                        {% if enquiry.email %}
                            <a href="mailto:{{ enquiry.email }}" style="color: #0070f3;">{{ enquiry.email }}</a>
                        {% else %}
                            —
                        {% endif %}
                    </div>
                </div>
                <div class="sap-info-row">
                    <div class="sap-info-label">Contact Number:</div>
                    <div class="sap-info-value">{{ enquiry.contactno|default:"—" }}</div>
                </div>
            </div>
        </div>

        <!-- System Information -->
        <div class="sap-info-card fade-in">
            <div class="sap-info-header">
                <span style="font-size: 1.2rem;">📊</span>
                <h3>System Information</h3>
            </div>
            <div class="sap-info-body">
                <div class="sap-info-row">
                    <div class="sap-info-label">Enquiry ID:</div>
                    <div class="sap-info-value">#{{ enquiry.enqid }}</div>
                </div>
                <div class="sap-info-row">
                    <div class="sap-info-label">Created Date:</div>
                    <div class="sap-info-value">{{ enquiry.sysdate }}</div>
                </div>
                <div class="sap-info-row">
                    <div class="sap-info-label">Created Time:</div>
                    <div class="sap-info-value">{{ enquiry.systime }}</div>
                </div>
                <div class="sap-info-row">
                    <div class="sap-info-label">Created By:</div>
                    <div class="sap-info-value">{{ enquiry.sessionid|default:"System" }}</div>
                </div>
                <div class="sap-info-row">
                    <div class="sap-info-label">Customer Type:</div>
                    <div class="sap-info-value">
                        {% if enquiry.flag == 1 %}
                            👥 Existing Customer
                        {% else %}
                            🆕 New Customer
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Address Information -->
    <div class="sap-info-card fade-in">
        <div class="sap-info-header">
            <span style="font-size: 1.2rem;">🏢</span>
            <h3>Address Information</h3>
        </div>
        <div class="sap-info-body">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 24px;">
                <!-- Registered Office -->
                <div>
                    <h4 style="margin: 0 0 12px; color: #374151; display: flex; align-items: center; gap: 6px;">
                        <span>🏛️</span> Registered Office
                    </h4>
                    {% if enquiry.regdaddress %}
                        <div style="background: #f8fafc; padding: 12px; border-radius: 6px; border-left: 3px solid #0070f3;">
                            <div>{{ enquiry.regdaddress }}</div>
                            {% if enquiry.regdcity or enquiry.regdstate or enquiry.regdcountry %}
                                <div style="margin-top: 4px; font-size: 0.875rem; color: #6b7280;">
                                    {% if enquiry.regdcity %}{{ enquiry.regdcity.cityname }}{% endif %}{% if enquiry.regdstate %}, {{ enquiry.regdstate.statename }}{% endif %}{% if enquiry.regdcountry %}, {{ enquiry.regdcountry.countryname }}{% endif %}
                                    {% if enquiry.regdpinno %} - {{ enquiry.regdpinno }}{% endif %}
                                </div>
                            {% endif %}
                            {% if enquiry.regdcontactno %}
                                <div style="margin-top: 4px; font-size: 0.875rem; color: #6b7280;">
                                    📞 {{ enquiry.regdcontactno }}
                                </div>
                            {% endif %}
                        </div>
                    {% else %}
                        <div style="color: #9ca3af; font-style: italic;">Not provided</div>
                    {% endif %}
                </div>

                <!-- Works/Factory -->
                <div>
                    <h4 style="margin: 0 0 12px; color: #374151; display: flex; align-items: center; gap: 6px;">
                        <span>🏭</span> Works/Factory
                    </h4>
                    {% if enquiry.workaddress %}
                        <div style="background: #f8fafc; padding: 12px; border-radius: 6px; border-left: 3px solid #10b981;">
                            <div>{{ enquiry.workaddress }}</div>
                            {% if enquiry.workcity or enquiry.workstate or enquiry.workcountry %}
                                <div style="margin-top: 4px; font-size: 0.875rem; color: #6b7280;">
                                    {% if enquiry.workcity %}{{ enquiry.workcity.cityname }}{% endif %}{% if enquiry.workstate %}, {{ enquiry.workstate.statename }}{% endif %}{% if enquiry.workcountry %}, {{ enquiry.workcountry.countryname }}{% endif %}
                                    {% if enquiry.workpinno %} - {{ enquiry.workpinno }}{% endif %}
                                </div>
                            {% endif %}
                            {% if enquiry.workcontactno %}
                                <div style="margin-top: 4px; font-size: 0.875rem; color: #6b7280;">
                                    📞 {{ enquiry.workcontactno }}
                                </div>
                            {% endif %}
                        </div>
                    {% else %}
                        <div style="color: #9ca3af; font-style: italic;">Not provided</div>
                    {% endif %}
                </div>

                <!-- Material Delivery -->
                <div>
                    <h4 style="margin: 0 0 12px; color: #374151; display: flex; align-items: center; gap: 6px;">
                        <span>🚚</span> Material Delivery
                    </h4>
                    {% if enquiry.materialdeladdress %}
                        <div style="background: #f8fafc; padding: 12px; border-radius: 6px; border-left: 3px solid #f59e0b;">
                            <div>{{ enquiry.materialdeladdress }}</div>
                            {% if enquiry.materialdelcity or enquiry.materialdelstate or enquiry.materialdelcountry %}
                                <div style="margin-top: 4px; font-size: 0.875rem; color: #6b7280;">
                                    {% if enquiry.materialdelcity %}{{ enquiry.materialdelcity.cityname }}{% endif %}{% if enquiry.materialdelstate %}, {{ enquiry.materialdelstate.statename }}{% endif %}{% if enquiry.materialdelcountry %}, {{ enquiry.materialdelcountry.countryname }}{% endif %}
                                    {% if enquiry.materialdelpinno %} - {{ enquiry.materialdelpinno }}{% endif %}
                                </div>
                            {% endif %}
                            {% if enquiry.materialdelcontactno %}
                                <div style="margin-top: 4px; font-size: 0.875rem; color: #6b7280;">
                                    📞 {{ enquiry.materialdelcontactno }}
                                </div>
                            {% endif %}
                        </div>
                    {% else %}
                        <div style="color: #9ca3af; font-style: italic;">Not provided</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Business Information -->
    {% if enquiry.juridictioncode or enquiry.eccno or enquiry.panno or enquiry.tinvatno %}
    <div class="sap-info-card fade-in">
        <div class="sap-info-header">
            <span style="font-size: 1.2rem;">💼</span>
            <h3>Business Information</h3>
        </div>
        <div class="sap-info-body">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 16px;">
                {% if enquiry.juridictioncode %}
                    <div class="sap-info-row">
                        <div class="sap-info-label">Jurisdiction Code:</div>
                        <div class="sap-info-value">{{ enquiry.juridictioncode }}</div>
                    </div>
                {% endif %}
                {% if enquiry.eccno %}
                    <div class="sap-info-row">
                        <div class="sap-info-label">ECC Number:</div>
                        <div class="sap-info-value">{{ enquiry.eccno }}</div>
                    </div>
                {% endif %}
                {% if enquiry.panno %}
                    <div class="sap-info-row">
                        <div class="sap-info-label">PAN Number:</div>
                        <div class="sap-info-value">{{ enquiry.panno }}</div>
                    </div>
                {% endif %}
                {% if enquiry.tinvatno %}
                    <div class="sap-info-row">
                        <div class="sap-info-label">TIN/VAT Number:</div>
                        <div class="sap-info-value">{{ enquiry.tinvatno }}</div>
                    </div>
                {% endif %}
                {% if enquiry.tincstno %}
                    <div class="sap-info-row">
                        <div class="sap-info-label">TIN/CST Number:</div>
                        <div class="sap-info-value">{{ enquiry.tincstno }}</div>
                    </div>
                {% endif %}
                {% if enquiry.tdscode %}
                    <div class="sap-info-row">
                        <div class="sap-info-label">TDS Code:</div>
                        <div class="sap-info-value">{{ enquiry.tdscode }}</div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Attachments -->
    <div class="sap-info-card fade-in">
        <div class="sap-info-header">
            <span style="font-size: 1.2rem;">📎</span>
            <h3>Attachments ({{ attachments.count }})</h3>
        </div>
        <div class="sap-info-body">
            {% if attachments %}
                <div class="sap-attachments-grid">
                    {% for attachment in attachments %}
                        <div class="sap-attachment-card">
                            <div class="sap-attachment-icon">
                                {% if ".pdf" in attachment.filename|lower %}
                                    📄
                                {% elif ".doc" in attachment.filename|lower or ".docx" in attachment.filename|lower %}
                                    📝
                                {% elif ".xls" in attachment.filename|lower or ".xlsx" in attachment.filename|lower %}
                                    📊
                                {% elif ".jpg" in attachment.filename|lower or ".jpeg" in attachment.filename|lower or ".png" in attachment.filename|lower %}
                                    🖼️
                                {% else %}
                                    📁
                                {% endif %}
                            </div>
                            <div class="sap-attachment-info">
                                <div class="sap-attachment-name">{{ attachment.filename }}</div>
                                <div class="sap-attachment-size">
                                    {% if attachment.filesize %}
                                        {{ attachment.filesize|filesizeformat }}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="sap-attachment-actions">
                                <button class="sap-attachment-btn sap-attachment-download" 
                                        onclick="downloadAttachment({{ attachment.id }})">
                                    💾 Download
                                </button>
                                {% if can_edit %}
                                    <button class="sap-attachment-btn sap-attachment-delete"
                                            onclick="deleteAttachment({{ attachment.id }})"
                                            hx-delete="{% url 'sales_distribution:attachment_delete' attachment.id %}"
                                            hx-confirm="Are you sure you want to delete this attachment?">
                                        🗑️ Delete
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <span class="empty-state-icon">📎</span>
                    <p>No attachments found for this enquiry.</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Related Quotations -->
    {% if quotations %}
    <div class="sap-info-card fade-in">
        <div class="sap-info-header">
            <span style="font-size: 1.2rem;">💰</span>
            <h3>Related Quotations ({{ quotations.count }})</h3>
        </div>
        <div class="sap-info-body">
            <table class="sap-quotations-table">
                <thead>
                    <tr>
                        <th>Quotation #</th>
                        <th>Date</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for quotation in quotations %}
                        <tr>
                            <td>
                                <a href="{% url 'sales_distribution:quotation_detail' quotation.id %}" 
                                   class="sap-quotation-link">
                                    #{{ quotation.quotationno }}
                                </a>
                            </td>
                            <td>{{ quotation.sysdate }}</td>
                            <td>
                                {% if quotation.authorize == 1 %}
                                    <span class="sap-status-display sap-status-success">✅ Authorized</span>
                                {% elif quotation.approve == 1 %}
                                    <span class="sap-status-display sap-status-warning">⏳ Approved</span>
                                {% elif quotation.checked == 1 %}
                                    <span class="sap-status-display sap-status-info">📋 Checked</span>
                                {% else %}
                                    <span class="sap-status-display sap-status-info">📝 Draft</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{% url 'sales_distribution:quotation_detail' quotation.id %}" 
                                   class="sap-btn sap-btn-primary" style="padding: 4px 8px; font-size: 0.75rem;">
                                    👁️ View
                                </a>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}
</div>

<script>
function downloadAttachment(attachmentId) {
    // Implement download functionality
    window.open(`/sales-distribution/attachments/${attachmentId}/download/`, '_blank');
}

function deleteAttachment(attachmentId) {
    // This will be handled by HTMX
    console.log('Deleting attachment:', attachmentId);
}
</script>
{% endblock %}
