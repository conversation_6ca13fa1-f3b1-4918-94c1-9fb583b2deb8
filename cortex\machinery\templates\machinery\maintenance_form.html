{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{{ title|default:"Maintenance Form" }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-2xl font-semibold text-gray-900">{{ title|default:"Maintenance Form" }}</h1>
        <p class="mt-2 text-sm text-gray-700">Record preventive or breakdown maintenance details.</p>
    </div>

    <!-- Form -->
    <form method="post" class="space-y-8">
        {% csrf_token %}
        
        <!-- Basic Information -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Basic Information</h3>
                
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label for="{{ form.machineid.id_for_label }}" class="block text-sm font-medium text-gray-700">Machine</label>
                        {{ form.machineid|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.machineid.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.machineid.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.pmbm.id_for_label }}" class="block text-sm font-medium text-gray-700">Maintenance Type</label>
                        {{ form.pmbm|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.pmbm.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.pmbm.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Schedule Information -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Schedule Information</h3>
                
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label for="{{ form.fromdate.id_for_label }}" class="block text-sm font-medium text-gray-700">From Date</label>
                        {{ form.fromdate|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.fromdate.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.fromdate.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.todate.id_for_label }}" class="block text-sm font-medium text-gray-700">To Date</label>
                        {{ form.todate|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.todate.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.todate.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.fromtime.id_for_label }}" class="block text-sm font-medium text-gray-700">From Time</label>
                        {{ form.fromtime|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.fromtime.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.fromtime.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.totime.id_for_label }}" class="block text-sm font-medium text-gray-700">To Time</label>
                        {{ form.totime|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.totime.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.totime.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Service Provider Information -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Service Provider Information</h3>
                
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label for="{{ form.nameofagency.id_for_label }}" class="block text-sm font-medium text-gray-700">Agency Name</label>
                        {{ form.nameofagency|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.nameofagency.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.nameofagency.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.nameofengineer.id_for_label }}" class="block text-sm font-medium text-gray-700">Engineer Name</label>
                        {{ form.nameofengineer|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.nameofengineer.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.nameofengineer.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Next Maintenance Schedule -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Next Maintenance Schedule</h3>
                
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label for="{{ form.nextpmdueon.id_for_label }}" class="block text-sm font-medium text-gray-700">Next PM Due On</label>
                        {{ form.nextpmdueon|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.nextpmdueon.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.nextpmdueon.errors.0 }}</p>
                        {% endif %}
                        <p class="mt-2 text-sm text-gray-500">Preventive maintenance due date</p>
                    </div>
                    
                    <div>
                        <label for="{{ form.nextbmdueon.id_for_label }}" class="block text-sm font-medium text-gray-700">Next BM Due On</label>
                        {{ form.nextbmdueon|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.nextbmdueon.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.nextbmdueon.errors.0 }}</p>
                        {% endif %}
                        <p class="mt-2 text-sm text-gray-500">Breakdown maintenance due date</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Remarks -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Remarks</h3>
                
                <div>
                    <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">Maintenance Remarks</label>
                    {{ form.remarks|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                    {% if form.remarks.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.remarks.errors.0 }}</p>
                    {% endif %}
                    <p class="mt-2 text-sm text-gray-500">Detailed maintenance notes, observations, and recommendations</p>
                </div>
            </div>
        </div>

        <!-- Maintenance Details -->
        {% if maintenance_details %}
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Maintenance Details</h3>
                
                <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                    <table class="min-w-full divide-y divide-gray-300">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Spare Part</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                <th class="relative px-6 py-3"><span class="sr-only">Actions</span></th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for detail in maintenance_details %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    Spare ID: {{ detail.spareid }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ detail.qty }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <button type="button" class="text-red-600 hover:text-red-900">Remove</button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3">
            <a href="{% url 'machinery:maintenance_list' %}" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Cancel
            </a>
            <button type="submit" 
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Save Maintenance Record
            </button>
        </div>
    </form>
</div>

<script>
// Auto-calculate next PM date based on machine's PM days
document.addEventListener('DOMContentLoaded', function() {
    const machineSelect = document.getElementById('{{ form.machineid.id_for_label }}');
    const fromDateInput = document.getElementById('{{ form.fromdate.id_for_label }}');
    const nextPMInput = document.getElementById('{{ form.nextpmdueon.id_for_label }}');
    
    function calculateNextPM() {
        if (fromDateInput.value && machineSelect.value) {
            // In a real implementation, you would fetch the machine's PM days
            // and calculate the next due date
            const fromDate = new Date(fromDateInput.value);
            fromDate.setDate(fromDate.getDate() + 30); // Default 30 days
            nextPMInput.value = fromDate.toISOString().split('T')[0];
        }
    }
    
    fromDateInput.addEventListener('change', calculateNextPM);
    machineSelect.addEventListener('change', calculateNextPM);
});
</script>
{% endblock %}