"""
Material Planning URL Configuration
Comprehensive routing for all material planning functionality
"""

from django.urls import path, include
from django.shortcuts import redirect
from . import views
from .views import process_views, planning_views, reporting_views, inline_process_views, debug_views

app_name = 'material_planning'

# Process Management URLs (Task Group 1)
process_patterns = [
    # Redirect categories to inline interface (primary interface)
    path('categories/', lambda request: redirect('material_planning:process_inline_list'), name='category_list'),
    
    # Debug Interface (for testing)
    path('debug/', debug_views.DebugProcessView.as_view(), name='process_debug'),
    
    # Inline Process Management (ASP.NET GridView equivalent) - PRIMARY INTERFACE
    path('inline/', inline_process_views.ProcessInlineListView.as_view(), name='process_inline_list'),
    path('create/', inline_process_views.process_create_ajax, name='process_create_ajax'),
    path('<int:process_id>/update/', inline_process_views.process_update_ajax, name='process_update_ajax'),
    path('<int:process_id>/delete/', inline_process_views.process_delete_ajax, name='process_delete_ajax'),
    path('<int:process_id>/delete-confirm/', inline_process_views.process_delete_confirm, name='process_delete_confirm'),
    
    # Legacy Process Categories (keep for backward compatibility)
    path('categories/legacy/', process_views.ProcessCategoryListView.as_view(), name='category_list_legacy'),
    path('categories/create/', process_views.ProcessCategoryCreateView.as_view(), name='category_create'),
    path('categories/<int:pk>/edit/', process_views.ProcessCategoryUpdateView.as_view(), name='category_edit'),
    path('categories/<int:pk>/delete/', process_views.ProcessCategoryDeleteView.as_view(), name='category_delete'),
]

# Material Planning URLs (Task Group 2) 
planning_patterns = [
    # Material Plans - using simplified views that actually exist
    path('plans/', planning_views.MaterialPlanListView.as_view(), name='plan_list'),
    path('plans/<int:pk>/', planning_views.MaterialPlanDetailView.as_view(), name='plan_detail'),
    path('plans/<int:pk>/edit/', planning_views.MaterialPlanUpdateView.as_view(), name='plan_edit'),
    path('plans/<int:pk>/copy/', planning_views.MaterialPlanCopyView.as_view(), name='plan_copy'),
    # (Other complex views don't exist in current implementation)
]

urlpatterns = [
    # Dashboard
    path('', views.index, name='index'),
    
    # Process Management (Task Group 1)
    path('processes/', include(process_patterns)),
    
    # Material Planning (Task Group 2)
    path('planning/', include(planning_patterns)),
    
    # Planning Details URLs (Task Group 3) - implemented in models, views can be added later
    # path('details/', include('material_planning.urls.details')),
    
    # Planning Reports URLs (Task Group 4)
    path('reports/', include([
        # Dashboard - using simplified views that actually exist
        path('dashboard/', reporting_views.PlanningDashboardView.as_view(), name='dashboard'),
        
        # HTMX Views for Reporting - simplified
        path('htmx/dashboard-refresh/', reporting_views.dashboard_refresh, name='dashboard_refresh_htmx'),
        path('htmx/kpi/<int:kpi_id>/update/', reporting_views.kpi_update, name='kpi_update_htmx'),
        path('htmx/exception-chart/', reporting_views.exception_chart_data, name='exception_chart_htmx'),
        path('htmx/supplier-performance-chart/', reporting_views.supplier_performance_chart, name='supplier_performance_chart_htmx'),
        path('htmx/plan/<int:plan_id>/cost-breakdown/', reporting_views.planning_cost_breakdown, name='cost_breakdown_htmx'),
        path('htmx/report-preview/', reporting_views.generate_report_preview, name='report_preview_htmx'),
    ])),
]