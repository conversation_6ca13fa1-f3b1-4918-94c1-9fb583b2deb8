# Generated by Django 5.2.1 on 2025-06-13 20:53

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("sys_admin", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="BudgetCode",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                (
                    "description",
                    models.TextField(blank=True, db_column="Description", null=True),
                ),
                ("symbol", models.TextField(blank=True, db_column="Symbol", null=True)),
            ],
            options={
                "db_table": "tblMIS_BudgetCode",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="BudgetCodeTime",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                (
                    "description",
                    models.TextField(blank=True, db_column="Description", null=True),
                ),
                ("symbol", models.TextField(blank=True, db_column="Symbol", null=True)),
            ],
            options={
                "db_table": "tblMIS_BudgetCode_Time",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="BudgetHoursFieldCategory",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                (
                    "category",
                    models.TextField(blank=True, db_column="Category", null=True),
                ),
                ("symbol", models.TextField(blank=True, db_column="Symbol", null=True)),
            ],
            options={
                "db_table": "tblMIS_BudgetHrs_Field_Category",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="BudgetHoursFieldSubcategory",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                (
                    "subcategory",
                    models.TextField(blank=True, db_column="SubCategory", null=True),
                ),
                ("symbol", models.TextField(blank=True, db_column="Symbol", null=True)),
            ],
            options={
                "db_table": "tblMIS_BudgetHrs_Field_SubCategory",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="BudgetTransaction",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                (
                    "sysdate",
                    models.TextField(blank=True, db_column="SysDate", null=True),
                ),
                (
                    "systime",
                    models.TextField(blank=True, db_column="SysTime", null=True),
                ),
                (
                    "session_id",
                    models.TextField(blank=True, db_column="SessionId", null=True),
                ),
                (
                    "budget_code",
                    models.TextField(blank=True, db_column="BudgetCode", null=True),
                ),
                (
                    "amount",
                    models.FloatField(blank=True, db_column="Amount", null=True),
                ),
            ],
            options={
                "verbose_name": "Budget Transaction",
                "verbose_name_plural": "Budget Transactions",
                "db_table": "tblACC_Budget_Transactions",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="DepartmentBudget",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                (
                    "sysdate",
                    models.TextField(blank=True, db_column="SysDate", null=True),
                ),
                (
                    "systime",
                    models.TextField(blank=True, db_column="SysTime", null=True),
                ),
                (
                    "session_id",
                    models.TextField(blank=True, db_column="SessionId", null=True),
                ),
                (
                    "dept_id",
                    models.IntegerField(blank=True, db_column="DeptId", null=True),
                ),
                (
                    "acc_id",
                    models.IntegerField(blank=True, db_column="AccId", null=True),
                ),
                (
                    "amount",
                    models.FloatField(blank=True, db_column="Amount", null=True),
                ),
                ("bg_id", models.IntegerField(blank=True, db_column="BGId", null=True)),
            ],
            options={
                "verbose_name": "Department Budget",
                "verbose_name_plural": "Department Budgets",
                "db_table": "tblACC_Budget_Dept",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="WorkOrderBudget",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                (
                    "sysdate",
                    models.TextField(blank=True, db_column="SysDate", null=True),
                ),
                (
                    "systime",
                    models.TextField(blank=True, db_column="SysTime", null=True),
                ),
                (
                    "session_id",
                    models.TextField(blank=True, db_column="SessionId", null=True),
                ),
                ("wo_id", models.IntegerField(blank=True, db_column="WOId", null=True)),
                (
                    "acc_id",
                    models.IntegerField(blank=True, db_column="AccId", null=True),
                ),
                (
                    "amount",
                    models.FloatField(blank=True, db_column="Amount", null=True),
                ),
                ("bg_id", models.IntegerField(blank=True, db_column="BGId", null=True)),
            ],
            options={
                "verbose_name": "Work Order Budget",
                "verbose_name_plural": "Work Order Budgets",
                "db_table": "tblACC_Budget_WO",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="BudgetPeriod",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "name",
                    models.CharField(
                        help_text="Budget period name (e.g., Q1 2024, H1 2024)",
                        max_length=100,
                    ),
                ),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="budget_periods",
                        to="sys_admin.company",
                    ),
                ),
                (
                    "financial_year",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="budget_periods",
                        to="sys_admin.financialyear",
                    ),
                ),
            ],
            options={
                "verbose_name": "Budget Period",
                "verbose_name_plural": "Budget Periods",
                "db_table": "mis_budget_period",
                "managed": True,
                "unique_together": {("name", "financial_year", "company")},
            },
        ),
        migrations.CreateModel(
            name="BudgetAllocation",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "allocation_type",
                    models.CharField(
                        choices=[
                            ("department", "Department Budget"),
                            ("project", "Project Budget"),
                            ("work_order", "Work Order Budget"),
                            ("general", "General Budget"),
                        ],
                        default="general",
                        max_length=20,
                    ),
                ),
                (
                    "allocated_amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=15,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "utilized_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("submitted", "Submitted"),
                            ("reviewed", "Under Review"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("active", "Active"),
                            ("expired", "Expired"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                (
                    "department_id",
                    models.IntegerField(
                        blank=True,
                        help_text="Department ID for department budgets",
                        null=True,
                    ),
                ),
                (
                    "project_id",
                    models.IntegerField(
                        blank=True,
                        help_text="Project ID for project budgets",
                        null=True,
                    ),
                ),
                (
                    "work_order_id",
                    models.IntegerField(
                        blank=True, help_text="Work Order ID for WO budgets", null=True
                    ),
                ),
                ("submitted_at", models.DateTimeField(blank=True, null=True)),
                ("approved_at", models.DateTimeField(blank=True, null=True)),
                (
                    "notes",
                    models.TextField(
                        blank=True, help_text="Additional notes or justification"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="budget_approvals",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "budget_code",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="allocations",
                        to="mis.budgetcode",
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="budget_allocations",
                        to="sys_admin.company",
                    ),
                ),
                (
                    "financial_year",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="budget_allocations",
                        to="sys_admin.financialyear",
                    ),
                ),
                (
                    "requested_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="budget_requests",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "budget_period",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="allocations",
                        to="mis.budgetperiod",
                    ),
                ),
            ],
            options={
                "verbose_name": "Budget Allocation",
                "verbose_name_plural": "Budget Allocations",
                "db_table": "mis_budget_allocation",
                "managed": True,
            },
        ),
        migrations.CreateModel(
            name="DepartmentBudgetMaster",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "budget_number",
                    models.CharField(
                        help_text="Auto-generated budget number",
                        max_length=50,
                        unique=True,
                    ),
                ),
                (
                    "department_id",
                    models.IntegerField(help_text="Department ID from HR module"),
                ),
                (
                    "department_name",
                    models.CharField(
                        help_text="Department name for reference", max_length=100
                    ),
                ),
                (
                    "budget_type",
                    models.CharField(
                        choices=[
                            ("operational", "Operational Budget"),
                            ("capital", "Capital Expenditure"),
                            ("project", "Project Budget"),
                            ("maintenance", "Maintenance Budget"),
                            ("training", "Training Budget"),
                            ("travel", "Travel Budget"),
                            ("other", "Other"),
                        ],
                        default="operational",
                        max_length=20,
                    ),
                ),
                (
                    "total_budget_amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=15,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "allocated_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "utilized_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "committed_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("submitted", "Submitted"),
                            ("dept_review", "Department Review"),
                            ("mgmt_review", "Management Review"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("active", "Active"),
                            ("locked", "Locked"),
                            ("archived", "Archived"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("critical", "Critical"),
                        ],
                        default="medium",
                        max_length=10,
                    ),
                ),
                ("submitted_at", models.DateTimeField(blank=True, null=True)),
                ("dept_reviewed_at", models.DateTimeField(blank=True, null=True)),
                ("finance_reviewed_at", models.DateTimeField(blank=True, null=True)),
                ("approved_at", models.DateTimeField(blank=True, null=True)),
                (
                    "business_justification",
                    models.TextField(
                        help_text="Business justification for budget request"
                    ),
                ),
                (
                    "expected_outcomes",
                    models.TextField(
                        blank=True, help_text="Expected outcomes and benefits"
                    ),
                ),
                (
                    "risk_assessment",
                    models.TextField(
                        blank=True, help_text="Risk assessment and mitigation"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="dept_budget_approvals",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "budget_period",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="department_budgets",
                        to="mis.budgetperiod",
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="department_budgets",
                        to="sys_admin.company",
                    ),
                ),
                (
                    "department_head",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="dept_budget_head_reviews",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "finance_reviewer",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="dept_budget_finance_reviews",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "financial_year",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="department_budgets",
                        to="sys_admin.financialyear",
                    ),
                ),
                (
                    "requested_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="dept_budget_requests",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Department Budget",
                "verbose_name_plural": "Department Budgets",
                "db_table": "mis_department_budget_master",
                "managed": True,
            },
        ),
        migrations.CreateModel(
            name="DepartmentBudgetHistory",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "action",
                    models.CharField(
                        choices=[
                            ("created", "Budget Created"),
                            ("modified", "Budget Modified"),
                            ("submitted", "Submitted for Review"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("transferred", "Budget Transferred"),
                            ("reallocated", "Budget Reallocated"),
                            ("locked", "Budget Locked"),
                            ("archived", "Budget Archived"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "description",
                    models.TextField(help_text="Description of what changed"),
                ),
                (
                    "old_values",
                    models.JSONField(
                        blank=True, help_text="Old values in JSON format", null=True
                    ),
                ),
                (
                    "new_values",
                    models.JSONField(
                        blank=True, help_text="New values in JSON format", null=True
                    ),
                ),
                (
                    "old_amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=15, null=True
                    ),
                ),
                (
                    "new_amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=15, null=True
                    ),
                ),
                (
                    "reason",
                    models.TextField(blank=True, help_text="Reason for the change"),
                ),
                (
                    "approval_reference",
                    models.CharField(
                        blank=True,
                        help_text="Reference number for approval",
                        max_length=100,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "performed_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="budget_history_actions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "department_budget",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="history",
                        to="mis.departmentbudgetmaster",
                    ),
                ),
            ],
            options={
                "verbose_name": "Department Budget History",
                "verbose_name_plural": "Department Budget History",
                "db_table": "mis_department_budget_history",
                "managed": True,
            },
        ),
        migrations.CreateModel(
            name="DepartmentBudgetDetails",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "line_number",
                    models.IntegerField(help_text="Line item sequence number"),
                ),
                (
                    "expense_category",
                    models.CharField(
                        choices=[
                            ("salaries", "Salaries & Benefits"),
                            ("travel", "Travel & Transportation"),
                            ("training", "Training & Development"),
                            ("equipment", "Equipment & Tools"),
                            ("supplies", "Office Supplies"),
                            ("utilities", "Utilities"),
                            ("maintenance", "Maintenance & Repairs"),
                            ("software", "Software & Licenses"),
                            ("consulting", "Consulting Services"),
                            ("marketing", "Marketing & Promotion"),
                            ("other", "Other Expenses"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.CharField(max_length=255)),
                ("detailed_description", models.TextField(blank=True)),
                (
                    "requested_amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=15,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "approved_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "actual_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "committed_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                ("planned_start_date", models.DateField(blank=True, null=True)),
                ("planned_end_date", models.DateField(blank=True, null=True)),
                ("actual_start_date", models.DateField(blank=True, null=True)),
                ("actual_end_date", models.DateField(blank=True, null=True)),
                (
                    "business_justification",
                    models.TextField(help_text="Justification for this line item"),
                ),
                (
                    "success_metrics",
                    models.TextField(
                        blank=True, help_text="How success will be measured"
                    ),
                ),
                (
                    "is_critical",
                    models.BooleanField(
                        default=False,
                        help_text="Mark as critical for business operations",
                    ),
                ),
                (
                    "is_recurring",
                    models.BooleanField(default=False, help_text="Recurring expense"),
                ),
                ("is_approved", models.BooleanField(default=False)),
                ("approved_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="line_item_approvals",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "department_budget",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="details",
                        to="mis.departmentbudgetmaster",
                    ),
                ),
            ],
            options={
                "verbose_name": "Department Budget Detail",
                "verbose_name_plural": "Department Budget Details",
                "db_table": "mis_department_budget_details",
                "managed": True,
            },
        ),
        migrations.CreateModel(
            name="DepartmentBudgetTimeTracking",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "tracking_period",
                    models.CharField(
                        choices=[
                            ("monthly", "Monthly"),
                            ("quarterly", "Quarterly"),
                            ("weekly", "Weekly"),
                            ("daily", "Daily"),
                        ],
                        default="monthly",
                        max_length=10,
                    ),
                ),
                ("period_start", models.DateField()),
                ("period_end", models.DateField()),
                (
                    "planned_amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=15,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "actual_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "committed_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "planned_milestones",
                    models.TextField(
                        blank=True, help_text="Planned milestones for this period"
                    ),
                ),
                (
                    "achieved_milestones",
                    models.TextField(
                        blank=True, help_text="Actually achieved milestones"
                    ),
                ),
                (
                    "performance_notes",
                    models.TextField(
                        blank=True, help_text="Performance notes and observations"
                    ),
                ),
                (
                    "forecast_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Forecasted amount for next period",
                        max_digits=15,
                        null=True,
                    ),
                ),
                (
                    "forecast_notes",
                    models.TextField(
                        blank=True, help_text="Forecasting assumptions and notes"
                    ),
                ),
                (
                    "is_locked",
                    models.BooleanField(
                        default=False, help_text="Lock period data from further changes"
                    ),
                ),
                ("locked_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "budget_detail",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="time_tracking",
                        to="mis.departmentbudgetdetails",
                    ),
                ),
                (
                    "department_budget",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="time_tracking",
                        to="mis.departmentbudgetmaster",
                    ),
                ),
                (
                    "locked_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="locked_budget_periods",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Department Budget Time Tracking",
                "verbose_name_plural": "Department Budget Time Tracking",
                "db_table": "mis_department_budget_time",
                "managed": True,
            },
        ),
        migrations.CreateModel(
            name="DepartmentBudgetTransfer",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "transfer_number",
                    models.CharField(
                        help_text="Auto-generated transfer number",
                        max_length=50,
                        unique=True,
                    ),
                ),
                (
                    "transfer_amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=15,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "transfer_reason",
                    models.TextField(help_text="Reason for budget transfer"),
                ),
                (
                    "urgency",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("urgent", "Urgent"),
                        ],
                        default="medium",
                        max_length=10,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Approval"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("requested_at", models.DateTimeField(auto_now_add=True)),
                ("approved_at", models.DateTimeField(blank=True, null=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "business_impact",
                    models.TextField(help_text="Impact on business operations"),
                ),
                (
                    "expected_completion_date",
                    models.DateField(help_text="When transfer should be completed"),
                ),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="budget_transfer_approvals",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="budget_transfers",
                        to="sys_admin.company",
                    ),
                ),
                (
                    "financial_year",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="budget_transfers",
                        to="sys_admin.financialyear",
                    ),
                ),
                (
                    "from_department_budget",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="outgoing_transfers",
                        to="mis.departmentbudgetmaster",
                    ),
                ),
                (
                    "requested_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="budget_transfer_requests",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "to_department_budget",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="incoming_transfers",
                        to="mis.departmentbudgetmaster",
                    ),
                ),
            ],
            options={
                "verbose_name": "Department Budget Transfer",
                "verbose_name_plural": "Department Budget Transfers",
                "db_table": "mis_department_budget_transfer",
                "managed": True,
            },
        ),
        migrations.CreateModel(
            name="TaxCompliance",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("compliance_number", models.CharField(max_length=50, unique=True)),
                (
                    "compliance_type",
                    models.CharField(
                        choices=[
                            ("monthly_return", "Monthly Return"),
                            ("quarterly_return", "Quarterly Return"),
                            ("annual_return", "Annual Return"),
                            ("audit", "Tax Audit"),
                            ("assessment", "Tax Assessment"),
                            ("refund", "Tax Refund"),
                            ("payment", "Tax Payment"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "tax_type",
                    models.CharField(
                        choices=[
                            ("excise", "Excise Duty"),
                            ("vat", "Value Added Tax (VAT)"),
                            ("gst", "Goods and Services Tax (GST)"),
                            ("cst", "Central Sales Tax (CST)"),
                            ("service_tax", "Service Tax"),
                            ("tds", "Tax Deducted at Source (TDS)"),
                            ("customs", "Customs Duty"),
                            ("cess", "Cess"),
                        ],
                        max_length=20,
                    ),
                ),
                ("period_from", models.DateField()),
                ("period_to", models.DateField()),
                ("due_date", models.DateField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("submitted", "Submitted"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("overdue", "Overdue"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                (
                    "tax_liability",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "tax_paid",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "penalty_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "interest_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "return_file_path",
                    models.CharField(
                        blank=True, help_text="Path to return file", max_length=500
                    ),
                ),
                ("acknowledgment_number", models.CharField(blank=True, max_length=50)),
                ("filing_date", models.DateTimeField(blank=True, null=True)),
                ("compliance_notes", models.TextField(blank=True)),
                ("rejection_reason", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "assigned_to",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tax_compliance_assigned",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tax_compliance",
                        to="sys_admin.company",
                    ),
                ),
                (
                    "completed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="tax_compliance_completed",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "financial_year",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tax_compliance",
                        to="sys_admin.financialyear",
                    ),
                ),
            ],
            options={
                "verbose_name": "Tax Compliance",
                "verbose_name_plural": "Tax Compliance",
                "db_table": "mis_tax_compliance",
                "managed": True,
            },
        ),
        migrations.CreateModel(
            name="TaxComputation",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "computation_number",
                    models.CharField(
                        help_text="Auto-generated computation number",
                        max_length=50,
                        unique=True,
                    ),
                ),
                (
                    "transaction_type",
                    models.CharField(
                        choices=[
                            ("sales_invoice", "Sales Invoice"),
                            ("purchase_invoice", "Purchase Invoice"),
                            ("service_invoice", "Service Invoice"),
                            ("proforma_invoice", "Proforma Invoice"),
                            ("credit_note", "Credit Note"),
                            ("debit_note", "Debit Note"),
                            ("import_invoice", "Import Invoice"),
                            ("export_invoice", "Export Invoice"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "transaction_id",
                    models.IntegerField(help_text="Reference to source transaction"),
                ),
                (
                    "transaction_number",
                    models.CharField(
                        help_text="Source transaction number", max_length=50
                    ),
                ),
                ("transaction_date", models.DateField()),
                ("party_name", models.CharField(max_length=255)),
                ("party_address", models.TextField()),
                (
                    "party_gstin",
                    models.CharField(
                        blank=True, help_text="GSTIN/PAN of party", max_length=15
                    ),
                ),
                (
                    "party_state_code",
                    models.CharField(
                        blank=True, help_text="State code for GST", max_length=2
                    ),
                ),
                (
                    "base_amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=15,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "discount_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "taxable_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "excise_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "vat_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "cst_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "service_tax_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "gst_cgst_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "gst_sgst_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "gst_igst_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "gst_cess_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "tds_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "total_tax_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "grand_total_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("calculated", "Calculated"),
                            ("verified", "Verified"),
                            ("approved", "Approved"),
                            ("posted", "Posted to Accounts"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                ("computed_at", models.DateTimeField(auto_now_add=True)),
                ("verified_at", models.DateTimeField(blank=True, null=True)),
                ("approved_at", models.DateTimeField(blank=True, null=True)),
                ("remarks", models.TextField(blank=True)),
                (
                    "reversal_reason",
                    models.TextField(
                        blank=True, help_text="Reason for tax reversal/cancellation"
                    ),
                ),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="tax_approvals",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tax_computations",
                        to="sys_admin.company",
                    ),
                ),
                (
                    "computed_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tax_computations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "financial_year",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tax_computations",
                        to="sys_admin.financialyear",
                    ),
                ),
                (
                    "verified_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="tax_verifications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Tax Computation",
                "verbose_name_plural": "Tax Computations",
                "db_table": "mis_tax_computation",
                "managed": True,
            },
        ),
        migrations.CreateModel(
            name="TaxComputationDetails",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("line_number", models.IntegerField()),
                ("item_code", models.CharField(blank=True, max_length=50)),
                ("item_description", models.CharField(max_length=255)),
                (
                    "hsn_code",
                    models.CharField(
                        blank=True, help_text="HSN/SAC Code", max_length=10
                    ),
                ),
                (
                    "quantity",
                    models.DecimalField(decimal_places=3, default=1, max_digits=15),
                ),
                ("unit_price", models.DecimalField(decimal_places=2, max_digits=15)),
                ("line_amount", models.DecimalField(decimal_places=2, max_digits=15)),
                (
                    "discount_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "taxable_amount",
                    models.DecimalField(decimal_places=2, max_digits=15),
                ),
                (
                    "line_excise_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "line_vat_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "line_cst_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "line_service_tax_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "line_gst_cgst_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "line_gst_sgst_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "line_gst_igst_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "line_gst_cess_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "line_tds_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "line_total_tax_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "line_grand_total",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "tax_computation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="details",
                        to="mis.taxcomputation",
                    ),
                ),
            ],
            options={
                "verbose_name": "Tax Computation Detail",
                "verbose_name_plural": "Tax Computation Details",
                "db_table": "mis_tax_computation_details",
                "managed": True,
            },
        ),
        migrations.CreateModel(
            name="TaxConfiguration",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "tax_type",
                    models.CharField(
                        choices=[
                            ("excise", "Excise Duty"),
                            ("vat", "Value Added Tax (VAT)"),
                            ("gst", "Goods and Services Tax (GST)"),
                            ("cst", "Central Sales Tax (CST)"),
                            ("service_tax", "Service Tax"),
                            ("tds", "Tax Deducted at Source (TDS)"),
                            ("customs", "Customs Duty"),
                            ("cess", "Cess"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "tax_code",
                    models.CharField(
                        help_text="Unique tax code (e.g., EXCISE_12.5)",
                        max_length=20,
                        unique=True,
                    ),
                ),
                (
                    "tax_name",
                    models.CharField(help_text="Display name for tax", max_length=100),
                ),
                (
                    "description",
                    models.TextField(help_text="Detailed description of tax"),
                ),
                (
                    "calculation_method",
                    models.CharField(
                        choices=[
                            ("percentage", "Percentage Based"),
                            ("fixed_amount", "Fixed Amount"),
                            ("slab_based", "Slab Based"),
                            ("formula_based", "Formula Based"),
                        ],
                        default="percentage",
                        max_length=20,
                    ),
                ),
                (
                    "tax_rate",
                    models.DecimalField(
                        decimal_places=4,
                        default=0,
                        help_text="Tax rate (e.g., 12.5000 for 12.5%)",
                        max_digits=8,
                    ),
                ),
                (
                    "fixed_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Fixed tax amount",
                        max_digits=15,
                    ),
                ),
                (
                    "minimum_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Minimum tax amount",
                        max_digits=15,
                    ),
                ),
                (
                    "maximum_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Maximum tax amount",
                        max_digits=15,
                        null=True,
                    ),
                ),
                (
                    "applicable_from",
                    models.DateField(help_text="Tax applicable from date"),
                ),
                (
                    "applicable_to",
                    models.DateField(
                        blank=True, help_text="Tax applicable till date", null=True
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "formula",
                    models.TextField(
                        blank=True,
                        help_text="Custom calculation formula (for formula-based taxes)",
                    ),
                ),
                (
                    "exemption_threshold",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Amount below which tax is exempted",
                        max_digits=15,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tax_configurations",
                        to="sys_admin.company",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tax_config_created",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Tax Configuration",
                "verbose_name_plural": "Tax Configurations",
                "db_table": "mis_tax_configuration",
                "managed": True,
            },
        ),
        migrations.CreateModel(
            name="TaxComputationDetailTax",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "applicable_rate",
                    models.DecimalField(
                        decimal_places=4,
                        help_text="Rate applied for this computation",
                        max_digits=8,
                    ),
                ),
                (
                    "tax_amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Tax amount calculated",
                        max_digits=15,
                    ),
                ),
                (
                    "computation_detail",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="mis.taxcomputationdetails",
                    ),
                ),
                (
                    "tax_configuration",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="mis.taxconfiguration",
                    ),
                ),
            ],
            options={
                "db_table": "mis_tax_computation_detail_tax",
                "managed": True,
            },
        ),
        migrations.AddField(
            model_name="taxcomputationdetails",
            name="tax_configurations",
            field=models.ManyToManyField(
                through="mis.TaxComputationDetailTax", to="mis.taxconfiguration"
            ),
        ),
        migrations.CreateModel(
            name="TaxReport",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("report_number", models.CharField(max_length=50, unique=True)),
                (
                    "report_type",
                    models.CharField(
                        choices=[
                            ("excise_register", "Excise Register"),
                            ("vat_register", "VAT Register"),
                            ("service_tax_register", "Service Tax Register"),
                            ("gst_register", "GST Register"),
                            ("tds_register", "TDS Register"),
                            ("tax_summary", "Tax Summary Report"),
                            ("compliance_status", "Compliance Status Report"),
                        ],
                        max_length=30,
                    ),
                ),
                ("report_name", models.CharField(max_length=255)),
                ("period_from", models.DateField()),
                ("period_to", models.DateField()),
                (
                    "tax_types",
                    models.JSONField(
                        default=list, help_text="List of tax types included in report"
                    ),
                ),
                (
                    "filters",
                    models.JSONField(
                        default=dict, help_text="Additional filters applied"
                    ),
                ),
                (
                    "report_format",
                    models.CharField(
                        choices=[
                            ("excel", "Excel Format"),
                            ("pdf", "PDF Format"),
                            ("csv", "CSV Format"),
                            ("xml", "XML Format"),
                            ("json", "JSON Format"),
                        ],
                        default="excel",
                        max_length=10,
                    ),
                ),
                ("file_path", models.CharField(blank=True, max_length=500)),
                (
                    "file_size",
                    models.BigIntegerField(default=0, help_text="File size in bytes"),
                ),
                (
                    "generation_status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("generating", "Generating"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("generated_at", models.DateTimeField(blank=True, null=True)),
                ("download_count", models.IntegerField(default=0)),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tax_reports",
                        to="sys_admin.company",
                    ),
                ),
                (
                    "financial_year",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tax_reports",
                        to="sys_admin.financialyear",
                    ),
                ),
                (
                    "generated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tax_reports",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Tax Report",
                "verbose_name_plural": "Tax Reports",
                "db_table": "mis_tax_report",
                "managed": True,
            },
        ),
        migrations.CreateModel(
            name="BudgetDistribution",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("po", "Purchase Orders"),
                            ("cash_pay", "Cash Payments"),
                            ("cash_rec", "Cash Receipts"),
                            ("tax", "Tax Payments"),
                            ("labor", "Labor Costs"),
                            ("material", "Material Costs"),
                            ("overhead", "Overhead Costs"),
                            ("other", "Other Expenses"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "allocated_amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=15,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "actual_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "percentage",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Percentage of total budget allocation",
                        max_digits=5,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                ("description", models.CharField(blank=True, max_length=255)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "budget_allocation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="distributions",
                        to="mis.budgetallocation",
                    ),
                ),
            ],
            options={
                "verbose_name": "Budget Distribution",
                "verbose_name_plural": "Budget Distributions",
                "db_table": "mis_budget_distribution",
                "managed": True,
                "unique_together": {("budget_allocation", "category")},
            },
        ),
        migrations.AddIndex(
            model_name="budgetallocation",
            index=models.Index(
                fields=["budget_code", "budget_period"],
                name="mis_budget__budget__e99d09_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="budgetallocation",
            index=models.Index(
                fields=["status", "allocation_type"],
                name="mis_budget__status_bcda68_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="budgetallocation",
            index=models.Index(
                fields=["company", "financial_year"],
                name="mis_budget__company_f8f686_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="departmentbudgetmaster",
            index=models.Index(
                fields=["department_id", "budget_period"],
                name="mis_departm_departm_2bd8ad_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="departmentbudgetmaster",
            index=models.Index(
                fields=["status", "budget_type"], name="mis_departm_status_4df1b8_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="departmentbudgetmaster",
            index=models.Index(
                fields=["company", "financial_year"],
                name="mis_departm_company_f29bcf_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="departmentbudgethistory",
            index=models.Index(
                fields=["department_budget", "action"],
                name="mis_departm_departm_d596fd_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="departmentbudgethistory",
            index=models.Index(
                fields=["performed_by", "created_at"],
                name="mis_departm_perform_f0bc10_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="departmentbudgetdetails",
            index=models.Index(
                fields=["expense_category", "is_critical"],
                name="mis_departm_expense_71602f_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="departmentbudgetdetails",
            index=models.Index(
                fields=["planned_start_date", "planned_end_date"],
                name="mis_departm_planned_f3fc0e_idx",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="departmentbudgetdetails",
            unique_together={("department_budget", "line_number")},
        ),
        migrations.AddIndex(
            model_name="departmentbudgettimetracking",
            index=models.Index(
                fields=["period_start", "period_end"],
                name="mis_departm_period__5a9572_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="departmentbudgettimetracking",
            index=models.Index(
                fields=["tracking_period", "is_locked"],
                name="mis_departm_trackin_1783ad_idx",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="departmentbudgettimetracking",
            unique_together={("department_budget", "period_start", "period_end")},
        ),
        migrations.AddIndex(
            model_name="departmentbudgettransfer",
            index=models.Index(
                fields=["status", "urgency"], name="mis_departm_status_f71f18_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="departmentbudgettransfer",
            index=models.Index(
                fields=["requested_at", "expected_completion_date"],
                name="mis_departm_request_8597c0_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="taxcompliance",
            index=models.Index(
                fields=["compliance_type", "due_date"],
                name="mis_tax_com_complia_186b8a_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="taxcompliance",
            index=models.Index(
                fields=["status", "assigned_to"], name="mis_tax_com_status_5be424_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="taxcompliance",
            index=models.Index(
                fields=["tax_type", "period_from", "period_to"],
                name="mis_tax_com_tax_typ_9abd5a_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="taxcomputation",
            index=models.Index(
                fields=["transaction_type", "transaction_date"],
                name="mis_tax_com_transac_3ba831_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="taxcomputation",
            index=models.Index(
                fields=["status", "computed_at"], name="mis_tax_com_status_032141_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="taxcomputation",
            index=models.Index(
                fields=["party_gstin", "transaction_date"],
                name="mis_tax_com_party_g_d5f82f_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="taxconfiguration",
            index=models.Index(
                fields=["tax_type", "is_active"], name="mis_tax_con_tax_typ_e6a1d3_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="taxconfiguration",
            index=models.Index(
                fields=["applicable_from", "applicable_to"],
                name="mis_tax_con_applica_064e55_idx",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="taxcomputationdetailtax",
            unique_together={("computation_detail", "tax_configuration")},
        ),
        migrations.AlterUniqueTogether(
            name="taxcomputationdetails",
            unique_together={("tax_computation", "line_number")},
        ),
        migrations.AddIndex(
            model_name="taxreport",
            index=models.Index(
                fields=["report_type", "period_from", "period_to"],
                name="mis_tax_rep_report__ec35ca_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="taxreport",
            index=models.Index(
                fields=["generation_status", "generated_at"],
                name="mis_tax_rep_generat_534830_idx",
            ),
        ),
    ]
