<!-- SAP S/4 HANA Inspired Product Table -->
<div class="overflow-hidden">
    <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <!-- Serial Number -->
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div class="flex items-center space-x-1">
                        <span>SN</span>
                    </div>
                </th>
                <!-- Product Name -->
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div class="flex items-center space-x-1">
                        <span>Product Name</span>
                        <i data-lucide="arrow-up-down" class="w-3 h-3 text-gray-400 cursor-pointer hover:text-gray-600"></i>
                    </div>
                </th>
                <!-- Actions -->
                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <span>Actions</span>
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for product in products %}
                {% include 'sales_distribution/partials/product_row.html' %}
            {% empty %}
                <!-- Empty State - SAP Style -->
                <tr class="empty-state">
                    <td colspan="3" class="px-6 py-16 text-center">
                        <div class="flex flex-col items-center justify-center">
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                <i data-lucide="package" class="w-8 h-8 text-gray-400"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No Products Found</h3>
                            <p class="text-sm text-gray-500 mb-6">
                                {% if request.GET.search %}
                                    No products match your search criteria "{{ request.GET.search }}". Try adjusting your search terms.
                                {% else %}
                                    Get started by creating your first product.
                                {% endif %}
                            </p>
                            <button hx-get="{% url 'sales_distribution:product_create' %}" 
                                    hx-target="#product-form-container"
                                    hx-swap="innerHTML"
                                    class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                {% if request.GET.search %}
                                    Clear Search & Add Product
                                {% else %}
                                    Add First Product
                                {% endif %}
                            </button>
                        </div>
                    </td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- SAP Style Pagination -->
{% if page_obj.has_other_pages %}
    <div class="bg-white border-t border-gray-200 px-6 py-3">
        <div class="flex items-center justify-between">
            <div class="flex items-center text-sm text-gray-500">
                <span>
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} entries
                </span>
            </div>
            <div class="flex items-center space-x-2">
                <!-- Previous Page -->
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" 
                       class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded hover:bg-gray-50 hover:text-gray-700">
                        <i data-lucide="chevron-left" class="w-4 h-4 mr-1"></i>
                        Previous
                    </a>
                {% else %}
                    <span class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-white border border-gray-300 rounded cursor-not-allowed">
                        <i data-lucide="chevron-left" class="w-4 h-4 mr-1"></i>
                        Previous
                    </span>
                {% endif %}
                
                <!-- Page Numbers -->
                <div class="flex items-center space-x-1">
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <span class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600 rounded">
                                {{ num }}
                            </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <a href="?page={{ num }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" 
                               class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded hover:bg-gray-50 hover:text-gray-700">
                                {{ num }}
                            </a>
                        {% endif %}
                    {% endfor %}
                </div>
                
                <!-- Next Page -->
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" 
                       class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded hover:bg-gray-50 hover:text-gray-700">
                        Next
                        <i data-lucide="chevron-right" class="w-4 h-4 ml-1"></i>
                    </a>
                {% else %}
                    <span class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-white border border-gray-300 rounded cursor-not-allowed">
                        Next
                        <i data-lucide="chevron-right" class="w-4 h-4 ml-1"></i>
                    </span>
                {% endif %}
            </div>
        </div>
    </div>
{% endif %}