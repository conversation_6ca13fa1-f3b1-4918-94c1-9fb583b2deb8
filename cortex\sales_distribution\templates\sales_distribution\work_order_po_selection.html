{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/umd/lucide.min.css">
<script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
{% endblock %}

{% block extra_js %}
<script>
// Customer autocomplete functionality - matches ASP.NET logic
let customerAutocompleteTimeout;

function setupCustomerAutocomplete() {
    const searchInput = document.getElementById('id_search_value');
    const datalist = document.getElementById('customerList');
    
    if (searchInput && datalist) {
        searchInput.addEventListener('input', function() {
            clearTimeout(customerAutocompleteTimeout);
            const query = this.value.trim();
            
            if (query.length >= 1) {
                customerAutocompleteTimeout = setTimeout(() => {
                    fetchCustomers(query, datalist);
                }, 300);
            } else {
                datalist.innerHTML = '';
            }
        });
    }
}

function fetchCustomers(query, datalist) {
    fetch(`{% url 'sales_distribution:work_order_customer_autocomplete' %}?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            datalist.innerHTML = '';
            data.customers.forEach(customer => {
                const option = document.createElement('option');
                option.value = customer.text;
                datalist.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error fetching customers:', error);
        });
}

function toggleSearchFields() {
    const searchBy = document.getElementById('id_search_by').value;
    const searchValueGroup = document.getElementById('search_value_group');
    const enquiryPoGroup = document.getElementById('enquiry_po_group');
    const searchValue = document.getElementById('id_search_value');
    const enquiryPoValue = document.getElementById('id_enquiry_po_value');
    
    if (searchBy === '0') {
        // Customer Name search
        searchValueGroup.style.display = 'block';
        enquiryPoGroup.style.display = 'none';
        searchValue.placeholder = 'Search Customer Name';
        searchValue.required = true;
        enquiryPoValue.required = false;
    } else if (searchBy === '1') {
        // Enquiry No search
        searchValueGroup.style.display = 'none';
        enquiryPoGroup.style.display = 'block';
        enquiryPoValue.placeholder = 'Enter Enquiry Number';
        searchValue.required = false;
        enquiryPoValue.required = true;
    } else if (searchBy === '2') {
        // PO No search
        searchValueGroup.style.display = 'none';
        enquiryPoGroup.style.display = 'block';
        enquiryPoValue.placeholder = 'Enter PO Number';
        searchValue.required = false;
        enquiryPoValue.required = true;
    }
}

function selectPO(poNo, customerId, enqId, poId) {
    // Navigate to work order creation form
    const baseUrl = "{% url 'sales_distribution:work_order_create_from_po' po_no='__PO__' customer_id='__CUSTOMER__' enq_id=0 po_id=0 %}";
    const finalUrl = baseUrl
        .replace('__PO__', poNo)
        .replace('__CUSTOMER__', customerId)
        .replace('/0/', '/' + enqId + '/')
        .replace('/0/', '/' + poId + '/');
    window.location.href = finalUrl;
}

document.addEventListener('DOMContentLoaded', function() {
    setupCustomerAutocomplete();
    toggleSearchFields();
});
</script>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-sap-gray-50 font-sap">
    <!-- SAP Fiori Header -->
    <div class="bg-white shadow-sm border-b border-sap-gray-200">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-sap-blue-600 rounded-lg flex items-center justify-center">
                            <i data-lucide="package" class="w-5 h-5 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-xl font-semibold text-sap-gray-900">{{ page_title }}</h1>
                            <p class="text-sm text-sap-gray-600">Create work orders from purchase orders</p>
                        </div>
                    </div>
                </div>
                <nav class="flex items-center space-x-2 text-sm text-sap-gray-500">
                    <a href="{% url 'sales_distribution:dashboard' %}" class="hover:text-sap-blue-600 transition-colors">Sales & Distribution</a>
                    <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    <span class="text-sap-gray-900 font-medium">Work Order Creation</span>
                </nav>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-6 py-8 space-y-6">
        <!-- Work Order Creation Mode Selection -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- With PO (Formal) Mode -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8 hover:shadow-md transition-all duration-200 group">
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-500 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-105 transition-transform duration-200">
                        <i data-lucide="file-text" class="w-8 h-8 text-white"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">With PO (Formal)</h3>
                    <p class="text-gray-600 mb-6">Create work order from existing purchase order with complete documentation</p>
                    <button onclick="document.getElementById('search-section').scrollIntoView({behavior: 'smooth'})" 
                            class="w-full inline-flex items-center justify-center px-6 py-3 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                        <i data-lucide="search" class="w-4 h-4 mr-2"></i>
                        Search Purchase Orders
                    </button>
                </div>
            </div>

            <!-- Without PO (Verbal) Mode -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8 hover:shadow-md transition-all duration-200 group">
                <div class="text-center">
                    <div class="w-16 h-16 bg-orange-500 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-105 transition-transform duration-200">
                        <i data-lucide="message-circle" class="w-8 h-8 text-white"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Without PO (Verbal)</h3>
                    <p class="text-gray-600 mb-6">Create work order based on verbal approval with temporary PO reference</p>
                    <a href="{% url 'sales_distribution:work_order_create_verbal' %}" 
                       class="w-full inline-flex items-center justify-center px-6 py-3 text-sm font-medium text-white bg-orange-600 border border-transparent rounded-lg hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200">
                        <i data-lucide="plus-circle" class="w-4 h-4 mr-2"></i>
                        Create Verbal Work Order
                    </a>
                </div>
            </div>
        </div>

        <!-- Divider -->
        <div class="flex items-center justify-center">
            <div class="flex-1 border-t border-gray-300"></div>
            <div class="px-4 text-sm text-gray-500 bg-white">OR</div>
            <div class="flex-1 border-t border-gray-300"></div>
        </div>

        <!-- Search Card -->
        <div id="search-section" class="bg-white rounded-xl shadow-sm border border-gray-200 p-6" x-data="{ searchExpanded: true }">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="search" class="w-5 h-5 text-blue-600"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900">Search Purchase Orders</h2>
                        <p class="text-sm text-gray-600">Filter available purchase orders for work order creation</p>
                    </div>
                </div>
                <button @click="searchExpanded = !searchExpanded" 
                        class="p-2 rounded-lg text-gray-500 hover:bg-gray-100 hover:text-blue-600 transition-all duration-200"
                        :class="searchExpanded ? 'rotate-180' : ''">
                    <i data-lucide="chevron-up" class="w-5 h-5 transition-transform duration-200"></i>
                </button>
            </div>

            <div x-show="searchExpanded" x-transition:enter="transition ease-out duration-200" 
                 x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100"
                 x-transition:leave="transition ease-in duration-150" 
                 x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-95">
                <form method="get" class="space-y-6">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Search Type -->
                        <div class="space-y-2">
                            <label for="id_search_by" class="block text-sm font-medium text-sap-gray-700">Search By</label>
                            <div class="relative">
                                {{ search_form.search_by }}
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <i data-lucide="chevron-down" class="w-4 h-4 text-sap-gray-400"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Customer Name Search -->
                        <div id="search_value_group" class="space-y-2 {% if search_by != '0' %}hidden{% endif %}">
                            <label for="id_search_value" class="block text-sm font-medium text-sap-gray-700">Customer Name</label>
                            <div class="relative">
                                {{ search_form.search_value }}
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <i data-lucide="user" class="w-4 h-4 text-sap-gray-400"></i>
                                </div>
                                <datalist id="customerList"></datalist>
                            </div>
                        </div>

                        <!-- Enquiry/PO Value Search -->
                        <div id="enquiry_po_group" class="space-y-2 {% if search_by == '0' %}hidden{% endif %}">
                            <label for="id_enquiry_po_value" class="block text-sm font-medium text-sap-gray-700">
                                {% if search_by == '1' %}Enquiry Number{% elif search_by == '2' %}PO Number{% else %}Value{% endif %}
                            </label>
                            <div class="relative">
                                {{ search_form.enquiry_po_value }}
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <i data-lucide="hash" class="w-4 h-4 text-sap-gray-400"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                        <div class="flex items-center space-x-3">
                            <button type="submit" class="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                                <i data-lucide="search" class="w-4 h-4 mr-2"></i>
                                Search Purchase Orders
                            </button>
                            <a href="{% url 'sales_distribution:work_order_po_selection' %}" 
                               class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200">
                                <i data-lucide="x-circle" class="w-4 h-4 mr-2"></i>
                                Clear Search
                            </a>
                        </div>
                        <div class="text-sm text-gray-500 flex items-center">
                            <i data-lucide="info" class="w-4 h-4 mr-1"></i>
                            Only POs without existing work orders are shown
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Purchase Orders Table Card -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="clipboard-list" class="w-5 h-5 text-green-600"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900">Available Purchase Orders</h2>
                        <p class="text-sm text-gray-600">{{ purchase_orders|length }} purchase order{{ purchase_orders|length|pluralize }} available for work order creation</p>
                    </div>
                </div>
                {% if purchase_orders %}
                <div class="flex items-center space-x-2 text-sm text-gray-500">
                    <i data-lucide="layers" class="w-4 h-4"></i>
                    <span>{{ purchase_orders|length }} item{{ purchase_orders|length|pluralize }}</span>
                </div>
                {% endif %}
            </div>

        {% if purchase_orders %}
            <div class="overflow-hidden rounded-lg border border-gray-200">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <div class="flex items-center space-x-2">
                                        <i data-lucide="file-text" class="w-4 h-4"></i>
                                        <span>PO Number</span>
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <div class="flex items-center space-x-2">
                                        <i data-lucide="building-2" class="w-4 h-4"></i>
                                        <span>Customer</span>
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <div class="flex items-center space-x-2">
                                        <i data-lucide="calendar" class="w-4 h-4"></i>
                                        <span>PO Date</span>
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <div class="flex items-center space-x-2">
                                        <i data-lucide="clock" class="w-4 h-4"></i>
                                        <span>System Date</span>
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <div class="flex items-center space-x-2">
                                        <i data-lucide="calendar-days" class="w-4 h-4"></i>
                                        <span>Financial Year</span>
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <div class="flex items-center space-x-2">
                                        <i data-lucide="user" class="w-4 h-4"></i>
                                        <span>Employee</span>
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <div class="flex items-center space-x-2">
                                        <i data-lucide="settings" class="w-4 h-4"></i>
                                        <span>Action</span>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for po in purchase_orders %}
                            <tr class="hover:bg-gray-50 transition-colors duration-150 cursor-pointer group" 
                                data-po-no="{{ po.pono }}" data-customer-id="{{ po.customerid }}" 
                                data-enq-id="{{ po.enqid.enqid|default:0 }}" data-po-id="{{ po.poid }}">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                            <i data-lucide="file-text" class="w-4 h-4 text-blue-600"></i>
                                        </div>
                                        <div class="text-sm font-semibold text-blue-600 group-hover:text-blue-700">
                                            {{ po.pono|default:"N/A" }}
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ po.customer_name|default:"Unknown Customer" }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-600">{{ po.formatted_po_date|default:"N/A" }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-600">{{ po.formatted_date|default:"N/A" }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        {{ po.financial_year|default:"N/A" }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center mr-2">
                                            <i data-lucide="user" class="w-3 h-3 text-gray-600"></i>
                                        </div>
                                        <div class="text-sm text-gray-600">{{ po.employee_name|default:"N/A" }}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <button 
                                        onclick="selectPO('{{ po.pono }}', '{{ po.customerid }}', '{{ po.enqid.enqid|default:0 }}', '{{ po.poid }}')"
                                        class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:scale-105">
                                        <i data-lucide="plus-circle" class="w-4 h-4 mr-2"></i>
                                        Create Work Order
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
            <div class="px-6 py-4 bg-sap-gray-50 border-t border-sap-gray-200 rounded-b-xl">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2 text-sm text-sap-gray-600">
                        <i data-lucide="eye" class="w-4 h-4"></i>
                        <span>Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ paginator.count }} purchase orders</span>
                    </div>
                    <div class="flex items-center space-x-1">
                        {% if page_obj.has_previous %}
                            <a href="?page=1{% if request.GET.search_by %}&search_by={{ request.GET.search_by }}{% endif %}{% if request.GET.search_value %}&search_value={{ request.GET.search_value }}{% endif %}{% if request.GET.enquiry_po_value %}&enquiry_po_value={{ request.GET.enquiry_po_value }}{% endif %}" 
                               class="inline-flex items-center px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-lg hover:bg-sap-gray-50 hover:text-sap-gray-700 transition-colors">
                                <i data-lucide="chevrons-left" class="w-4 h-4 mr-1"></i>
                                First
                            </a>
                            <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search_by %}&search_by={{ request.GET.search_by }}{% endif %}{% if request.GET.search_value %}&search_value={{ request.GET.search_value }}{% endif %}{% if request.GET.enquiry_po_value %}&enquiry_po_value={{ request.GET.enquiry_po_value }}{% endif %}" 
                               class="inline-flex items-center px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-lg hover:bg-sap-gray-50 hover:text-sap-gray-700 transition-colors">
                                <i data-lucide="chevron-left" class="w-4 h-4 mr-1"></i>
                                Previous
                            </a>
                        {% endif %}
                        
                        <span class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-sap-blue-600 border border-sap-blue-600 rounded-lg">
                            Page {{ page_obj.number }}
                        </span>
                        
                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search_by %}&search_by={{ request.GET.search_by }}{% endif %}{% if request.GET.search_value %}&search_value={{ request.GET.search_value }}{% endif %}{% if request.GET.enquiry_po_value %}&enquiry_po_value={{ request.GET.enquiry_po_value }}{% endif %}" 
                               class="inline-flex items-center px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-lg hover:bg-sap-gray-50 hover:text-sap-gray-700 transition-colors">
                                Next
                                <i data-lucide="chevron-right" class="w-4 h-4 ml-1"></i>
                            </a>
                            <a href="?page={{ paginator.num_pages }}{% if request.GET.search_by %}&search_by={{ request.GET.search_by }}{% endif %}{% if request.GET.search_value %}&search_value={{ request.GET.search_value }}{% endif %}{% if request.GET.enquiry_po_value %}&enquiry_po_value={{ request.GET.enquiry_po_value }}{% endif %}" 
                               class="inline-flex items-center px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-lg hover:bg-sap-gray-50 hover:text-sap-gray-700 transition-colors">
                                Last
                                <i data-lucide="chevrons-right" class="w-4 h-4 ml-1"></i>
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}

        {% else %}
            <div class="text-center py-16">
                <div class="w-20 h-20 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="inbox" class="w-10 h-10 text-gray-400"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">No Purchase Orders Found</h3>
                <p class="text-gray-600 max-w-md mx-auto mb-6">
                    {% if request.GET.search_by or request.GET.search_value or request.GET.enquiry_po_value %}
                        Try adjusting your search criteria or clear the search to see all available purchase orders.
                    {% else %}
                        There are no purchase orders available for work order creation at this time.
                    {% endif %}
                </p>
                {% if request.GET.search_by or request.GET.search_value or request.GET.enquiry_po_value %}
                    <a href="{% url 'sales_distribution:work_order_po_selection' %}" 
                       class="inline-flex items-center px-6 py-3 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200">
                        <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                        Clear Search and View All
                    </a>
                {% endif %}
            </div>
        {% endif %}
        </div>
    </div>
</div>

<script>
// Initialize Lucide icons
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
    setupCustomerAutocomplete();
    toggleSearchFields();
});
</script>
{% endblock %}