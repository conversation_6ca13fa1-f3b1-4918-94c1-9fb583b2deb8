﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="TotalIssueAndShortage" targetNamespace="http://tempuri.org/TotalIssueAndShortage.xsd" xmlns:mstns="http://tempuri.org/TotalIssueAndShortage.xsd" xmlns="http://tempuri.org/TotalIssueAndShortage.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="TotalIssueAndShortage" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="TotalIssueAndShortage" msprop:Generator_DataSetName="TotalIssueAndShortage">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ItemCode" msprop:Generator_UserColumnName="ItemCode" msprop:Generator_ColumnPropNameInRow="ItemCode" msprop:Generator_ColumnVarNameInTable="columnItemCode" msprop:Generator_ColumnPropNameInTable="ItemCodeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ManfDesc" msprop:Generator_UserColumnName="ManfDesc" msprop:Generator_ColumnPropNameInRow="ManfDesc" msprop:Generator_ColumnVarNameInTable="columnManfDesc" msprop:Generator_ColumnPropNameInTable="ManfDescColumn" type="xs:string" minOccurs="0" />
              <xs:element name="UOMBasic" msprop:Generator_UserColumnName="UOMBasic" msprop:Generator_ColumnPropNameInRow="UOMBasic" msprop:Generator_ColumnVarNameInTable="columnUOMBasic" msprop:Generator_ColumnPropNameInTable="UOMBasicColumn" type="xs:string" minOccurs="0" />
              <xs:element name="BOMQty" msprop:Generator_UserColumnName="BOMQty" msprop:Generator_ColumnPropNameInRow="BOMQty" msprop:Generator_ColumnVarNameInTable="columnBOMQty" msprop:Generator_ColumnPropNameInTable="BOMQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="IssuedQty" msprop:Generator_UserColumnName="IssuedQty" msprop:Generator_ColumnPropNameInRow="IssuedQty" msprop:Generator_ColumnVarNameInTable="columnIssuedQty" msprop:Generator_ColumnPropNameInTable="IssuedQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ShortageQty" msprop:Generator_UserColumnName="ShortageQty" msprop:Generator_ColumnPropNameInRow="ShortageQty" msprop:Generator_ColumnVarNameInTable="columnShortageQty" msprop:Generator_ColumnPropNameInTable="ShortageQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="AC" msprop:Generator_UserColumnName="AC" msprop:Generator_ColumnPropNameInRow="AC" msprop:Generator_ColumnVarNameInTable="columnAC" msprop:Generator_ColumnPropNameInTable="ACColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="WONo" msprop:Generator_UserColumnName="WONo" msprop:Generator_ColumnPropNameInRow="WONo" msprop:Generator_ColumnVarNameInTable="columnWONo" msprop:Generator_ColumnPropNameInTable="WONoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Rate" msprop:Generator_UserColumnName="Rate" msprop:Generator_ColumnVarNameInTable="columnRate" msprop:Generator_ColumnPropNameInRow="Rate" msprop:Generator_ColumnPropNameInTable="RateColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Amount" msprop:Generator_UserColumnName="Amount" msprop:Generator_ColumnVarNameInTable="columnAmount" msprop:Generator_ColumnPropNameInRow="Amount" msprop:Generator_ColumnPropNameInTable="AmountColumn" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>