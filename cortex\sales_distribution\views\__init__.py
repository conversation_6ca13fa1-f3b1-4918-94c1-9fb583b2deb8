# sales_distribution/views/__init__.py
# Import all views for easy access

# Import main views from main_views.py file
from .main_views import (
    SalesDistributionDashboardView,
    WorkOrderCategoryListView,
    WorkOrderCategoryCreateView,
    CategoryNewView,
    WorkOrderCategoryUpdateView,
    WorkOrderCategoryDeleteView,
    WorkOrderCategoryEditRowView,
    WorkOrderCategoryCancelEditView,
    WorkOrderSubcategoryListView,
    WorkOrderSubcategoryCreateView,
    WorkOrderSubcategoryUpdateView,
    WorkOrderSubcategoryDeleteView,
    WorkOrderSubcategoryEditRowView,
    WorkOrderSubcategoryCancelEditView,
    WOTypeListView,
    WOTypeCreateView,
    WOTypeUpdateView,
    WOTypeDeleteView,
    WOTypeEditRowView,
    WOTypeCancelEditView,
    CustomerListView,
    CustomerCreateView,
    CustomerUpdateView,
    CustomerPOListView,
    CustomerPOCreateView,
    CustomerPOUpdateView,
    CustomerPODetailView,
    ProductListView,
    ProductCreateView,
    ProductUpdateView,
    ProductDeleteView,
    ProductEditRowView,
    ProductCancelEditView,
    QuotationListView,
    QuotationSelectionView,
    QuotationCreateFromEnquiryView,
    QuotationCreateView,
    QuotationDetailView,
    QuotationUpdateView,
    QuotationItemAddView,
    QuotationItemDeleteView,
    QuotationCheckListView,
    QuotationApproveListView,
    QuotationAuthorizeListView,
    QuotationCheckView,
    QuotationApproveView,
    QuotationAuthorizeView,
    GetStatesAjaxView,
    GetCitiesAjaxView,
    # Work Order views
    WorkOrderListView,
    WorkOrderCreateView,
    WorkOrderDetailView,
    WorkOrderUpdateView,
    WorkOrderReleaseListView,
    WorkOrderReleaseView,
    WorkOrderDispatchListView,
    WorkOrderDispatchView,
    DispatchGunRailListView,
    DispatchGunRailCreateView,
    WorkOrderOpenCloseListView,
    WorkOrderOpenCloseView,
    WOReleaseDispatchAuthorityListView,
    WOReleaseDispatchAuthorityCreateView,
)

# Import specialized enquiry views from enquiry_views.py file
from .enquiry_views import (
    EnquiryCreateView,
    EnquiryListView,
    EnquiryDetailView,
    EnquiryUpdateView,
    CustomerAutocompleteView,
    GetCustomerDetailsView,
    AttachmentDeleteView,
    EnquiryStatsView,
)

__all__ = [
    # Dashboard
    'SalesDistributionDashboardView',
    # Category views
    'WorkOrderCategoryListView',
    'WorkOrderCategoryCreateView',
    'CategoryNewView',
    'WorkOrderCategoryUpdateView',
    'WorkOrderCategoryDeleteView',
    'WorkOrderCategoryEditRowView',
    'WorkOrderCategoryCancelEditView',
    # Subcategory views
    'WorkOrderSubcategoryListView',
    'WorkOrderSubcategoryCreateView',
    'WorkOrderSubcategoryUpdateView',
    'WorkOrderSubcategoryDeleteView',
    'WorkOrderSubcategoryEditRowView',
    'WorkOrderSubcategoryCancelEditView',
    # WOType views
    'WOTypeListView',
    'WOTypeCreateView',
    'WOTypeUpdateView',
    'WOTypeDeleteView',
    'WOTypeEditRowView',
    'WOTypeCancelEditView',
    # Customer views
    'CustomerListView',
    'CustomerCreateView',
    'CustomerUpdateView',
    # Customer PO views
    'CustomerPOListView',
    'CustomerPOCreateView',
    'CustomerPOUpdateView',
    'CustomerPODetailView',
    # Product views  
    'ProductListView',
    'ProductCreateView',
    'ProductUpdateView',
    'ProductDeleteView',
    'ProductEditRowView',
    'ProductCancelEditView',
    # Quotation views
    'QuotationListView',
    'QuotationSelectionView',
    'QuotationCreateFromEnquiryView',
    'QuotationCreateView',
    'QuotationDetailView',
    'QuotationUpdateView',
    'QuotationItemAddView',
    'QuotationItemDeleteView',
    'QuotationCheckListView',
    'QuotationApproveListView',
    'QuotationAuthorizeListView',
    'QuotationCheckView',
    'QuotationApproveView',
    'QuotationAuthorizeView',
    # AJAX views
    'GetStatesAjaxView',
    'GetCitiesAjaxView',
    # Enquiry views (from separate enquiry_views.py)
    'EnquiryCreateView',
    'EnquiryListView', 
    'EnquiryDetailView',
    'EnquiryUpdateView',
    'CustomerAutocompleteView',
    'GetCustomerDetailsView',
    'AttachmentDeleteView',
    'EnquiryStatsView',
    # Work Order views
    'WorkOrderListView',
    'WorkOrderCreateView',
    'WorkOrderDetailView',
    'WorkOrderUpdateView',
    'WorkOrderReleaseListView',
    'WorkOrderReleaseView',
    'WorkOrderDispatchListView',
    'WorkOrderDispatchView',
    'DispatchGunRailListView',
    'DispatchGunRailCreateView',
    'WorkOrderOpenCloseListView',
    'WorkOrderOpenCloseView',
    'WOReleaseDispatchAuthorityListView',
    'WOReleaseDispatchAuthorityCreateView',
]
