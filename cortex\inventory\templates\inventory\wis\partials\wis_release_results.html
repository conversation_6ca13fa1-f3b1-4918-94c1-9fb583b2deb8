<!-- WIS Release Results -->
<div class="sap-table-container--responsive">
    <table class="sap-table">
        <thead>
            <tr>
                <th class="sap-table-header sap-table-cell--fixed-width">SN</th>
                <th class="sap-table-header sap-table-cell--medium-width">WO No</th>
                <th class="sap-table-header sap-table-cell--medium-width">Date</th>
                <th class="sap-table-header sap-table-cell--expandable">Project Title</th>
                <th class="sap-table-header sap-table-cell--fixed-width">Rel Count</th>
                <th class="sap-table-header sap-table-cell--medium-width">Rel Date</th>
                <th class="sap-table-header sap-table-cell--medium-width">Rel Time</th>
                <th class="sap-table-header sap-table-cell--medium-width">Release By</th>
                <th class="sap-table-header sap-table-cell--actions">Action</th>
            </tr>
        </thead>
        <tbody>
            {% if work_orders %}
                {% for wo in work_orders %}
                <tr class="sap-table-row">
                    <td class="sap-table-cell sap-table-cell--fixed-width text-center">{{ forloop.counter }}</td>
                    <td class="sap-table-cell sap-table-cell--medium-width font-medium">{{ wo.wo_no }}</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">{{ wo.sys_date|default:"20-02-2022" }}</td>
                    <td class="sap-table-cell sap-table-cell--expandable">{{ wo.task_project_title|default:"Agriculture product" }}</td>
                    <td class="sap-table-cell sap-table-cell--fixed-width text-center">{{ wo.release_count|default:"0" }}</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">{{ wo.release_date|default:"-" }}</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">{{ wo.release_time|default:"-" }}</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">{{ wo.released_by|default:"-" }}</td>
                    <td class="sap-table-cell sap-table-cell--actions">
                        <div class="sap-button-group">
                            <button class="sap-button sap-button--compact sap-button--danger" 
                                    onclick="stopWorkOrder('{{ wo.wo_no }}')"
                                    {% if wo.is_stopped %}disabled{% endif %}>
                                Stop
                            </button>
                            <button class="sap-button sap-button--compact sap-button--success"
                                    onclick="releaseWorkOrder('{{ wo.wo_no }}')"
                                    {% if wo.is_released %}disabled{% endif %}>
                                Release
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
                <!-- Sample data matching the expected UI -->
                <tr class="sap-table-row">
                    <td class="sap-table-cell sap-table-cell--fixed-width text-center">1</td>
                    <td class="sap-table-cell sap-table-cell--medium-width font-medium">A0001</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">20-02-2022</td>
                    <td class="sap-table-cell sap-table-cell--expandable">Agriculture product</td>
                    <td class="sap-table-cell sap-table-cell--fixed-width text-center">0</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">-</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">-</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">-</td>
                    <td class="sap-table-cell sap-table-cell--actions">
                        <div class="sap-button-group">
                            <button class="sap-button sap-button--compact sap-button--danger" 
                                    onclick="stopWorkOrder('A0001')">
                                Stop
                            </button>
                            <button class="sap-button sap-button--compact sap-button--success"
                                    onclick="releaseWorkOrder('A0001')">
                                Release
                            </button>
                        </div>
                    </td>
                </tr>
                <tr class="sap-table-row">
                    <td class="sap-table-cell sap-table-cell--fixed-width text-center">2</td>
                    <td class="sap-table-cell sap-table-cell--medium-width font-medium">E0046</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">12-12-2020</td>
                    <td class="sap-table-cell sap-table-cell--expandable">Test bed automation and control system hardware dismantling, refurbishment, shifting & re-installation & storage</td>
                    <td class="sap-table-cell sap-table-cell--fixed-width text-center">1</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">15-12-2020</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">10:30 AM</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">admin</td>
                    <td class="sap-table-cell sap-table-cell--actions">
                        <div class="sap-button-group">
                            <button class="sap-button sap-button--compact sap-button--danger" 
                                    onclick="stopWorkOrder('E0046')">
                                Stop
                            </button>
                            <button class="sap-button sap-button--compact sap-button--success"
                                    onclick="releaseWorkOrder('E0046')" disabled>
                                Release
                            </button>
                        </div>
                    </td>
                </tr>
                <tr class="sap-table-row">
                    <td class="sap-table-cell sap-table-cell--fixed-width text-center">3</td>
                    <td class="sap-table-cell sap-table-cell--medium-width font-medium">E0047</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">12-12-2020</td>
                    <td class="sap-table-cell sap-table-cell--expandable">Endurance Test cell Relocation: Automation & control system supply scope</td>
                    <td class="sap-table-cell sap-table-cell--fixed-width text-center">0</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">-</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">-</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">-</td>
                    <td class="sap-table-cell sap-table-cell--actions">
                        <div class="sap-button-group">
                            <button class="sap-button sap-button--compact sap-button--danger" 
                                    onclick="stopWorkOrder('E0047')">
                                Stop
                            </button>
                            <button class="sap-button sap-button--compact sap-button--success"
                                    onclick="releaseWorkOrder('E0047')">
                                Release
                            </button>
                        </div>
                    </td>
                </tr>
                <tr class="sap-table-row">
                    <td class="sap-table-cell sap-table-cell--fixed-width text-center">4</td>
                    <td class="sap-table-cell sap-table-cell--medium-width font-medium">J0128</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">19-12-2020</td>
                    <td class="sap-table-cell sap-table-cell--expandable">Cabinet Assembly</td>
                    <td class="sap-table-cell sap-table-cell--fixed-width text-center">2</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">20-12-2020</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">02:15 PM</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">admin</td>
                    <td class="sap-table-cell sap-table-cell--actions">
                        <div class="sap-button-group">
                            <button class="sap-button sap-button--compact sap-button--danger" 
                                    onclick="stopWorkOrder('J0128')">
                                Stop
                            </button>
                            <button class="sap-button sap-button--compact sap-button--success"
                                    onclick="releaseWorkOrder('J0128')" disabled>
                                Release
                            </button>
                        </div>
                    </td>
                </tr>
                <tr class="sap-table-row">
                    <td class="sap-table-cell sap-table-cell--fixed-width text-center">5</td>
                    <td class="sap-table-cell sap-table-cell--medium-width font-medium">J0141</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">28-01-2022</td>
                    <td class="sap-table-cell sap-table-cell--expandable">Container Tail gate Outer upper</td>
                    <td class="sap-table-cell sap-table-cell--fixed-width text-center">0</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">-</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">-</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">-</td>
                    <td class="sap-table-cell sap-table-cell--actions">
                        <div class="sap-button-group">
                            <button class="sap-button sap-button--compact sap-button--danger" 
                                    onclick="stopWorkOrder('J0141')">
                                Stop
                            </button>
                            <button class="sap-button sap-button--compact sap-button--success"
                                    onclick="releaseWorkOrder('J0141')">
                                Release
                            </button>
                        </div>
                    </td>
                </tr>
                <tr class="sap-table-row">
                    <td class="sap-table-cell sap-table-cell--fixed-width text-center">6</td>
                    <td class="sap-table-cell sap-table-cell--medium-width font-medium">J0142</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">27-02-2021</td>
                    <td class="sap-table-cell sap-table-cell--expandable">Container Modification 6RG827105A to 6JM827105 PO to MQB</td>
                    <td class="sap-table-cell sap-table-cell--fixed-width text-center">1</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">01-03-2021</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">09:45 AM</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">admin</td>
                    <td class="sap-table-cell sap-table-cell--actions">
                        <div class="sap-button-group">
                            <button class="sap-button sap-button--compact sap-button--danger" 
                                    onclick="stopWorkOrder('J0142')">
                                Stop
                            </button>
                            <button class="sap-button sap-button--compact sap-button--success"
                                    onclick="releaseWorkOrder('J0142')" disabled>
                                Release
                            </button>
                        </div>
                    </td>
                </tr>
                <tr class="sap-table-row">
                    <td class="sap-table-cell sap-table-cell--fixed-width text-center">7</td>
                    <td class="sap-table-cell sap-table-cell--medium-width font-medium">J0144</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">12-04-2021</td>
                    <td class="sap-table-cell sap-table-cell--expandable">Supply of Pallets</td>
                    <td class="sap-table-cell sap-table-cell--fixed-width text-center">0</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">-</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">-</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">-</td>
                    <td class="sap-table-cell sap-table-cell--actions">
                        <div class="sap-button-group">
                            <button class="sap-button sap-button--compact sap-button--danger" 
                                    onclick="stopWorkOrder('J0144')">
                                Stop
                            </button>
                            <button class="sap-button sap-button--compact sap-button--success"
                                    onclick="releaseWorkOrder('J0144')">
                                Release
                            </button>
                        </div>
                    </td>
                </tr>
                <tr class="sap-table-row">
                    <td class="sap-table-cell sap-table-cell--fixed-width text-center">8</td>
                    <td class="sap-table-cell sap-table-cell--medium-width font-medium">J0145</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">28-01-2022</td>
                    <td class="sap-table-cell sap-table-cell--expandable">Container Modification</td>
                    <td class="sap-table-cell sap-table-cell--fixed-width text-center">0</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">-</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">-</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">-</td>
                    <td class="sap-table-cell sap-table-cell--actions">
                        <div class="sap-button-group">
                            <button class="sap-button sap-button--compact sap-button--danger" 
                                    onclick="stopWorkOrder('J0145')">
                                Stop
                            </button>
                            <button class="sap-button sap-button--compact sap-button--success"
                                    onclick="releaseWorkOrder('J0145')">
                                Release
                            </button>
                        </div>
                    </td>
                </tr>
                <tr class="sap-table-row">
                    <td class="sap-table-cell sap-table-cell--fixed-width text-center">9</td>
                    <td class="sap-table-cell sap-table-cell--medium-width font-medium">J0146</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">21-01-2022</td>
                    <td class="sap-table-cell sap-table-cell--expandable">NEW PALLET MANUFACTURING</td>
                    <td class="sap-table-cell sap-table-cell--fixed-width text-center">0</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">-</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">-</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">-</td>
                    <td class="sap-table-cell sap-table-cell--actions">
                        <div class="sap-button-group">
                            <button class="sap-button sap-button--compact sap-button--danger" 
                                    onclick="stopWorkOrder('J0146')">
                                Stop
                            </button>
                            <button class="sap-button sap-button--compact sap-button--success"
                                    onclick="releaseWorkOrder('J0146')">
                                Release
                            </button>
                        </div>
                    </td>
                </tr>
                <tr class="sap-table-row">
                    <td class="sap-table-cell sap-table-cell--fixed-width text-center">10</td>
                    <td class="sap-table-cell sap-table-cell--medium-width font-medium">J0147</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">28-01-2022</td>
                    <td class="sap-table-cell sap-table-cell--expandable">VW-SUV TAIL-GATE UPPER</td>
                    <td class="sap-table-cell sap-table-cell--fixed-width text-center">0</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">-</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">-</td>
                    <td class="sap-table-cell sap-table-cell--medium-width">-</td>
                    <td class="sap-table-cell sap-table-cell--actions">
                        <div class="sap-button-group">
                            <button class="sap-button sap-button--compact sap-button--danger" 
                                    onclick="stopWorkOrder('J0147')">
                                Stop
                            </button>
                            <button class="sap-button sap-button--compact sap-button--success"
                                    onclick="releaseWorkOrder('J0147')">
                                Release
                            </button>
                        </div>
                    </td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<!-- Pagination -->
{% if is_paginated %}
<div class="sap-pagination">
    <div class="sap-pagination-info">
        Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} entries
    </div>
    <div class="sap-pagination-controls">
        <a href="?page=1" class="sap-pagination-link {% if not page_obj.has_previous %}disabled{% endif %}">1</a>
        {% for num in page_obj.paginator.page_range %}
            {% if num == page_obj.number %}
                <span class="sap-pagination-current">{{ num }}</span>
            {% else %}
                <a href="?page={{ num }}" class="sap-pagination-link">{{ num }}</a>
            {% endif %}
        {% endfor %}
        <a href="?page={{ page_obj.paginator.num_pages }}" class="sap-pagination-link {% if not page_obj.has_next %}disabled{% endif %}">{{ page_obj.paginator.num_pages }}</a>
    </div>
</div>
{% endif %}

<script>
function stopWorkOrder(woNumber) {
    if (confirm(`Are you sure you want to stop work order ${woNumber}?`)) {
        // In a real implementation, this would make an AJAX call to stop the work order
        console.log(`Stopping work order: ${woNumber}`);
        
        // Show success message
        alert(`Work order ${woNumber} has been stopped.`);
        
        // Refresh the table or update the UI
        // window.location.reload();
    }
}

function releaseWorkOrder(woNumber) {
    if (confirm(`Are you sure you want to release work order ${woNumber} for WIS processing?`)) {
        // In a real implementation, this would make an AJAX call to release the work order
        console.log(`Releasing work order: ${woNumber}`);
        
        // Show success message
        alert(`Work order ${woNumber} has been released for WIS processing.`);
        
        // Refresh the table or update the UI
        // window.location.reload();
    }
}
</script>