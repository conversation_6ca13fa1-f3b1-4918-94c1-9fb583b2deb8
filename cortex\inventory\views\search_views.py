from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.views.generic import TemplateView
from django.http import JsonResponse
from django.db.models import Q, Sum, Count, F, Max
from datetime import datetime
import json

from ..models import (
    StockLedger, MaterialRequisitionSlip, MaterialIssueNote, MaterialReturnNote,
    GoodsInwardNote, GoodsReceivedReceipt, MaterialCreditNote, 
    MaterialServiceNote, ItemLocation
)
from ..forms import UniversalSearchForm, AdvancedFilterForm, SavedSearchForm


class UniversalSearchView(LoginRequiredMixin, TemplateView):
    """Universal search across all inventory records"""
    template_name = 'inventory/search/universal_search.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Universal Inventory Search'
        context['search_form'] = UniversalSearchForm()
        context['advanced_form'] = AdvancedFilterForm()
        
        # If this is a search request, perform search
        if self.request.GET:
            search_form = UniversalSearchForm(self.request.GET)
            if search_form.is_valid():
                context['search_form'] = search_form
                context['search_results'] = self.perform_search(search_form.cleaned_data)
                context['show_results'] = True
        
        return context
    
    def perform_search(self, search_params):
        """Perform comprehensive search across all inventory data"""
        try:
            query = search_params.get('query', '').strip()
            search_type = search_params.get('search_type', 'all')
            
            results = {
                'items': [],
                'transactions': [],
                'documents': [],
                'locations': [],
                'summary': {
                    'total_results': 0,
                    'items_count': 0,
                    'transactions_count': 0,
                    'documents_count': 0,
                    'locations_count': 0
                }
            }
            
            # Search based on type
            if search_type in ['all', 'items']:
                results['items'] = self.search_items(query, search_params)
                results['summary']['items_count'] = len(results['items'])
            
            if search_type in ['all', 'transactions']:
                results['transactions'] = self.search_transactions(query, search_params)
                results['summary']['transactions_count'] = len(results['transactions'])
            
            if search_type in ['all', 'documents']:
                results['documents'] = self.search_documents(query, search_params)
                results['summary']['documents_count'] = len(results['documents'])
            
            if search_type in ['all', 'locations']:
                results['locations'] = self.search_locations(query, search_params)
                results['summary']['locations_count'] = len(results['locations'])
            
            # Calculate total results
            results['summary']['total_results'] = (
                results['summary']['items_count'] +
                results['summary']['transactions_count'] +
                results['summary']['documents_count'] +
                results['summary']['locations_count']
            )
            
            # Sort results if needed
            self.sort_results(results, search_params.get('sort_order', 'relevance'))
            
            return results
            
        except Exception as e:
            return {
                'error': f"Search error: {str(e)}",
                'items': [],
                'transactions': [],
                'documents': [],
                'locations': [],
                'summary': {'total_results': 0}
            }
    
    def search_items(self, query, params):
        """Search inventory items"""
        items = []
        
        try:
            # Search in stock ledger for items
            stock_query = StockLedger.objects.all()
            
            if query:
                q_obj = Q()
                if params.get('exact_match'):
                    q_obj |= Q(item_code__iexact=query)
                    q_obj |= Q(item_description__iexact=query)
                else:
                    # Split query into words for better matching
                    words = query.split()
                    for word in words:
                        if params.get('case_sensitive'):
                            q_obj |= Q(item_code__icontains=word)
                            q_obj |= Q(item_description__icontains=word)
                        else:
                            q_obj |= Q(item_code__icontains=word)
                            q_obj |= Q(item_description__icontains=word)
                
                stock_query = stock_query.filter(q_obj)
            
            # Apply additional filters
            self.apply_common_filters(stock_query, params)
            
            # Get unique items with aggregated data
            stock_items = stock_query.values(
                'item_code', 'item_description'
            ).annotate(
                total_stock=Sum('available_stock'),
                total_value=Sum(F('available_stock') * F('unit_rate')),
                location_count=Count('location_code', distinct=True),
                last_transaction=Max('created_date')
            ).order_by('item_code')
            
            for item in stock_items:
                items.append({
                    'type': 'item',
                    'item_code': item['item_code'],
                    'item_description': item['item_description'],
                    'total_stock': item['total_stock'] or 0,
                    'total_value': item['total_value'] or 0,
                    'location_count': item['location_count'],
                    'last_transaction': item['last_transaction'],
                    'url': f"/inventory/search/?query={item['item_code']}&search_type=transactions"
                })
        
        except Exception:
            # Log error in production
            pass
        
        return items
    
    def search_transactions(self, query, params):
        """Search transaction records"""
        transactions = []
        
        try:
            if query:
                # Search in various transaction tables
                models_to_search = [
                    (MaterialRequisitionSlip, 'mrs_number', 'MRS'),
                    (MaterialIssueNote, 'min_number', 'MIN'),
                    (MaterialReturnNote, 'mrn_number', 'MRN'),
                    (GoodsInwardNote, 'gin_number', 'GIN'),
                    (GoodsReceivedReceipt, 'grr_number', 'GRR'),
                    (MaterialCreditNote, 'mcn_number', 'MCN'),
                    (MaterialServiceNote, 'gsnno', 'SN'),
                ]
                
                for model, number_field, doc_type in models_to_search:
                    try:
                        q_obj = Q()
                        if hasattr(model, number_field):
                            q_obj |= Q(**{f'{number_field}__icontains': query})
                        
                        # Search in description/remarks fields if they exist
                        if hasattr(model, 'remarks'):
                            q_obj |= Q(remarks__icontains=query)
                        if hasattr(model, 'description'):
                            q_obj |= Q(description__icontains=query)
                        
                        results = model.objects.filter(q_obj)
                        self.apply_common_filters(results, params)
                        
                        for item in results[:10]:  # Limit results per type
                            transaction_data = {
                                'type': 'transaction',
                                'document_type': doc_type,
                                'document_number': getattr(item, number_field, 'N/A'),
                                'date': getattr(item, f'{doc_type.lower()}_date', None),
                                'status': getattr(item, 'status', 'N/A'),
                                'total_value': getattr(item, 'total_amount', 0) or getattr(item, 'total_value', 0),
                                'created_by': getattr(item, 'created_by', None),
                                'url': f"/inventory/{doc_type.lower()}/{item.pk}/"
                            }
                            transactions.append(transaction_data)
                    
                    except Exception:
                        continue
        
        except Exception:
            # Log error in production
            pass
        
        return transactions
    
    def search_documents(self, query, params):
        """Search in document content and metadata"""
        documents = []
        
        # This would search through document attachments, notes, specifications
        # For now, we'll search in relevant text fields across models
        try:
            document_types = params.get('document_type')
            
            # Search logic similar to transactions but focused on document content
            # This is a placeholder - in a real implementation you might have
            # a separate document indexing system
            
            # For now, return empty list
            pass
        
        except Exception:
            pass
        
        return documents
    
    def search_locations(self, query, params):
        """Search location information"""
        locations = []
        
        try:
            if query:
                location_query = ItemLocation.objects.filter(
                    Q(location_code__icontains=query) |
                    Q(location_name__icontains=query) |
                    Q(warehouse_code__icontains=query) |
                    Q(zone_code__icontains=query) |
                    Q(bin_code__icontains=query)
                )
                
                for location in location_query:
                    locations.append({
                        'type': 'location',
                        'location_code': location.location_code,
                        'location_name': location.location_name,
                        'location_type': location.location_type,
                        'warehouse_code': location.warehouse_code,
                        'current_stock': location.current_stock,
                        'capacity': location.capacity,
                        'utilization': location.utilization_percentage,
                        'url': f"/inventory/locations/{location.pk}/"
                    })
        
        except Exception:
            pass
        
        return locations
    
    def apply_common_filters(self, queryset, params):
        """Apply common filters to any queryset"""
        try:
            # Date range filter
            if params.get('date_from'):
                if hasattr(queryset.model, 'created_date'):
                    queryset = queryset.filter(created_date__gte=params['date_from'])
            
            if params.get('date_to'):
                if hasattr(queryset.model, 'created_date'):
                    queryset = queryset.filter(created_date__lte=params['date_to'])
            
            # Location filter
            if params.get('location_filter'):
                if hasattr(queryset.model, 'location_code'):
                    queryset = queryset.filter(location_code__icontains=params['location_filter'])
            
            # Status filter
            if params.get('status_filter'):
                if hasattr(queryset.model, 'status'):
                    queryset = queryset.filter(status__icontains=params['status_filter'])
            
            # Include archived filter
            if not params.get('include_archived'):
                if hasattr(queryset.model, 'status'):
                    queryset = queryset.exclude(status__in=['CANCELLED', 'ARCHIVED', 'DELETED'])
        
        except Exception:
            pass
        
        return queryset
    
    def sort_results(self, results, sort_order):
        """Sort search results based on specified order"""
        try:
            if sort_order == 'date_desc':
                for key in results:
                    if key != 'summary' and isinstance(results[key], list):
                        results[key].sort(
                            key=lambda x: x.get('date') or x.get('last_transaction') or datetime.min.date(),
                            reverse=True
                        )
            elif sort_order == 'date_asc':
                for key in results:
                    if key != 'summary' and isinstance(results[key], list):
                        results[key].sort(
                            key=lambda x: x.get('date') or x.get('last_transaction') or datetime.min.date()
                        )
            elif sort_order == 'value_desc':
                for key in results:
                    if key != 'summary' and isinstance(results[key], list):
                        results[key].sort(
                            key=lambda x: x.get('total_value', 0),
                            reverse=True
                        )
            elif sort_order == 'alpha_asc':
                for key in results:
                    if key != 'summary' and isinstance(results[key], list):
                        results[key].sort(
                            key=lambda x: x.get('item_code') or x.get('document_number') or x.get('location_code', '')
                        )
        
        except Exception:
            pass


@login_required
def search_suggestions_api(request):
    """API endpoint for search autocomplete suggestions"""
    query = request.GET.get('q', '').strip()
    search_type = request.GET.get('type', 'all')
    
    if len(query) < 2:
        return JsonResponse({'suggestions': []})
    
    suggestions = []
    
    try:
        # Item code suggestions
        if search_type in ['all', 'items']:
            item_codes = StockLedger.objects.filter(
                item_code__icontains=query
            ).values_list('item_code', flat=True).distinct()[:5]
            
            for code in item_codes:
                suggestions.append({
                    'text': code,
                    'type': 'item_code',
                    'category': 'Items'
                })
        
        # Document number suggestions
        if search_type in ['all', 'documents']:
            mrs_numbers = MaterialRequisitionSlip.objects.filter(
                mrs_number__icontains=query
            ).values_list('mrs_number', flat=True)[:3]
            
            for number in mrs_numbers:
                suggestions.append({
                    'text': number,
                    'type': 'document',
                    'category': 'MRS'
                })
        
        # Location suggestions
        if search_type in ['all', 'locations']:
            locations = ItemLocation.objects.filter(
                Q(location_code__icontains=query) |
                Q(location_name__icontains=query)
            ).values_list('location_code', 'location_name')[:3]
            
            for code, name in locations:
                suggestions.append({
                    'text': f"{code} - {name}",
                    'type': 'location',
                    'category': 'Locations'
                })
    
    except Exception:
        # Log error in production
        pass
    
    return JsonResponse({'suggestions': suggestions})


@login_required
def quick_search_api(request):
    """API endpoint for quick search results"""
    query = request.GET.get('q', '').strip()
    
    if len(query) < 3:
        return JsonResponse({'results': []})
    
    results = []
    
    try:
        # Quick item search
        items = StockLedger.objects.filter(
            Q(item_code__icontains=query) |
            Q(item_description__icontains=query)
        ).values('item_code', 'item_description', 'available_stock')[:5]
        
        for item in items:
            results.append({
                'title': item['item_code'],
                'subtitle': item['item_description'],
                'value': f"Stock: {item['available_stock']}",
                'type': 'item',
                'url': f"/inventory/search/?query={item['item_code']}"
            })
        
        # Quick document search
        mrs_docs = MaterialRequisitionSlip.objects.filter(
            mrs_number__icontains=query
        ).values('mrs_number', 'mrs_date', 'status')[:3]
        
        for doc in mrs_docs:
            results.append({
                'title': doc['mrs_number'],
                'subtitle': f"MRS - {doc['mrs_date']}",
                'value': doc['status'],
                'type': 'document',
                'url': f"/inventory/mrs/?mrs_number={doc['mrs_number']}"
            })
    
    except Exception:
        # Log error in production
        pass
    
    return JsonResponse({'results': results})


@login_required  
def save_search_api(request):
    """API endpoint for saving search queries"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            form = SavedSearchForm(data)
            
            if form.is_valid():
                # Here you would save the search to a SavedSearch model
                # For now, just return success
                return JsonResponse({
                    'success': True,
                    'message': 'Search saved successfully'
                })
            else:
                return JsonResponse({
                    'success': False,
                    'errors': form.errors
                })
        
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            })
    
    return JsonResponse({'error': 'Invalid request method'})