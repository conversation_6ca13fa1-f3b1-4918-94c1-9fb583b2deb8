<!-- accounts/templates/accounts/reports/vat_register_report.html -->
<!-- VAT Register Report Template -->
<!-- Task Group 4: Taxation Management - VAT Register Report (Task 4.7) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}VAT Register Report - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-green-600 to-sap-green-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">VAT Register Report</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Comprehensive VAT collection and payment analysis</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="printReport()" 
                        class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="printer" class="w-4 h-4 inline mr-2"></i>
                    Print
                </button>
                <button onclick="exportReport()" 
                        class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                    Export
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Report Filters -->
    <div class="mb-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-sap-gray-800">Report Filters</h3>
                <button type="button" id="toggle-filters" class="text-sap-blue-600 hover:text-sap-blue-700">
                    <i data-lucide="filter" class="w-5 h-5"></i>
                </button>
            </div>
            
            <form method="get" class="space-y-4" id="filter-form">
                <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <!-- Date Range -->
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-1">From Date</label>
                        <input type="date" name="from_date" value="{{ request.GET.from_date }}"
                               class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-1">To Date</label>
                        <input type="date" name="to_date" value="{{ request.GET.to_date }}"
                               class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                    </div>
                    
                    <!-- VAT Rate Filter -->
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-1">VAT Rate</label>
                        <select name="vat_rate" class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                            <option value="">All Rates</option>
                            {% for rate in vat_rates %}
                            <option value="{{ rate.id }}" {% if request.GET.vat_rate == rate.id|stringformat:"s" %}selected{% endif %}>
                                {{ rate.vat_percentage }}% - {{ rate.description }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <!-- Transaction Type -->
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-1">Transaction Type</label>
                        <select name="transaction_type" class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                            <option value="">All Types</option>
                            <option value="SALES" {% if request.GET.transaction_type == "SALES" %}selected{% endif %}>Sales</option>
                            <option value="PURCHASE" {% if request.GET.transaction_type == "PURCHASE" %}selected{% endif %}>Purchases</option>
                        </select>
                    </div>
                    
                    <!-- Report Type -->
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-1">Report Type</label>
                        <select name="report_type" class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                            <option value="DETAILED" {% if request.GET.report_type == "DETAILED" %}selected{% endif %}>Detailed</option>
                            <option value="SUMMARY" {% if request.GET.report_type == "SUMMARY" %}selected{% endif %}>Summary</option>
                            <option value="CONSOLIDATED" {% if request.GET.report_type == "CONSOLIDATED" %}selected{% endif %}>Consolidated</option>
                        </select>
                    </div>
                </div>
                
                <div class="flex items-center space-x-3">
                    <button type="submit" class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="search" class="w-4 h-4 inline mr-2"></i>
                        Generate Report
                    </button>
                    <a href="{% url 'accounts:vat_register_report' %}" 
                       class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="x" class="w-4 h-4 inline mr-2"></i>
                        Clear Filters
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Summary Dashboard -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="trending-up" class="w-5 h-5 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-sap-gray-600">Sales VAT Collected</p>
                    <p class="text-xl font-semibold text-sap-gray-800">₹{{ sales_vat_total|floatformat:2 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="trending-down" class="w-5 h-5 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-sap-gray-600">Purchase VAT Paid</p>
                    <p class="text-xl font-semibold text-sap-gray-800">₹{{ purchase_vat_total|floatformat:2 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="balance-scale" class="w-5 h-5 text-sap-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-sap-gray-600">Net VAT Liability</p>
                    <p class="text-xl font-semibold">
                        {% if net_vat_liability >= 0 %}
                        <span class="text-sap-red-600">₹{{ net_vat_liability|floatformat:2 }}</span>
                        {% else %}
                        <span class="text-sap-green-600">₹{{ net_vat_liability|abs|floatformat:2 }}</span>
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="file-text" class="w-5 h-5 text-sap-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-sap-gray-600">Total Transactions</p>
                    <p class="text-xl font-semibold text-sap-gray-800">{{ total_transactions }}</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- VAT Rate-wise Analysis -->
    <div class="mb-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800">VAT Rate-wise Analysis</h3>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-sap-gray-200">
                    <thead class="bg-sap-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">VAT Rate</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Sales Base Amount</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Sales VAT Amount</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Purchase Base Amount</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Purchase VAT Amount</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Net VAT</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-sap-gray-200">
                        {% for rate_data in vat_rate_analysis %}
                        <tr class="hover:bg-sap-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-sap-gray-900">{{ rate_data.vat_rate }}%</div>
                                <div class="text-xs text-sap-gray-500">{{ rate_data.description }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-gray-900">
                                ₹{{ rate_data.sales_base_amount|floatformat:2 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-green-600">
                                ₹{{ rate_data.sales_vat_amount|floatformat:2 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-gray-900">
                                ₹{{ rate_data.purchase_base_amount|floatformat:2 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-blue-600">
                                ₹{{ rate_data.purchase_vat_amount|floatformat:2 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                {% if rate_data.net_vat >= 0 %}
                                <span class="text-sap-red-600">₹{{ rate_data.net_vat|floatformat:2 }}</span>
                                {% else %}
                                <span class="text-sap-green-600">₹{{ rate_data.net_vat|abs|floatformat:2 }}</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center text-sap-gray-400">
                                <i data-lucide="inbox" class="w-12 h-12 mx-auto mb-4"></i>
                                <p class="text-lg font-medium">No VAT data found</p>
                                <p class="text-sm">Adjust your filters to see results</p>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Detailed Transaction Register -->
    {% if request.GET.report_type != "SUMMARY" %}
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Detailed VAT Register</h3>
                <div class="text-sm text-sap-gray-600">
                    Showing {{ transactions.count }} transactions
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Invoice/Document</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Party</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Type</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Base Amount</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">VAT Rate</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">VAT Amount</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Total Amount</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for transaction in transactions %}
                    <tr class="hover:bg-sap-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">
                            {{ transaction.transaction_date|date:"M d, Y" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-sap-gray-900">{{ transaction.document_number }}</div>
                            <div class="text-xs text-sap-gray-500">{{ transaction.document_type }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-sap-gray-900">{{ transaction.party_name }}</div>
                            {% if transaction.party_gstin %}
                            <div class="text-xs text-sap-gray-500">GSTIN: {{ transaction.party_gstin }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if transaction.transaction_type == 'SALES' %}bg-sap-green-100 text-sap-green-800
                                {% else %}bg-sap-blue-100 text-sap-blue-800{% endif %}">
                                {{ transaction.transaction_type }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-gray-900">
                            ₹{{ transaction.base_amount|floatformat:2 }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-gray-900">
                            {{ transaction.vat_rate }}%
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            {% if transaction.transaction_type == 'SALES' %}
                            <span class="text-sap-green-600">₹{{ transaction.vat_amount|floatformat:2 }}</span>
                            {% else %}
                            <span class="text-sap-blue-600">₹{{ transaction.vat_amount|floatformat:2 }}</span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-gray-900">
                            ₹{{ transaction.total_amount|floatformat:2 }}
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="px-6 py-12 text-center text-sap-gray-400">
                            <i data-lucide="inbox" class="w-12 h-12 mx-auto mb-4"></i>
                            <p class="text-lg font-medium">No transactions found</p>
                            <p class="text-sm">Adjust your filters to see results</p>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="bg-white px-4 py-3 border-t border-sap-gray-200 sm:px-6">
            <div class="flex items-center justify-between">
                <div class="flex-1 flex justify-between sm:hidden">
                    {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}" 
                       class="relative inline-flex items-center px-4 py-2 border border-sap-gray-300 text-sm font-medium rounded-md text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                        Previous
                    </a>
                    {% endif %}
                    {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}" 
                       class="ml-3 relative inline-flex items-center px-4 py-2 border border-sap-gray-300 text-sm font-medium rounded-md text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                        Next
                    </a>
                    {% endif %}
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-sap-gray-700">
                            Showing <span class="font-medium">{{ page_obj.start_index }}</span> to 
                            <span class="font-medium">{{ page_obj.end_index }}</span> of 
                            <span class="font-medium">{{ page_obj.paginator.count }}</span> results
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                            {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}" 
                               class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-500 hover:bg-sap-gray-50">
                                <i data-lucide="chevron-left" class="w-5 h-5"></i>
                            </a>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                            <span class="relative inline-flex items-center px-4 py-2 border border-sap-green-500 bg-sap-green-50 text-sm font-medium text-sap-green-600">
                                {{ num }}
                            </span>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <a href="?page={{ num }}" 
                               class="relative inline-flex items-center px-4 py-2 border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-700 hover:bg-sap-gray-50">
                                {{ num }}
                            </a>
                            {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}" 
                               class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-500 hover:bg-sap-gray-50">
                                <i data-lucide="chevron-right" class="w-5 h-5"></i>
                            </a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function printReport() {
    window.print();
}

function exportReport() {
    // Get current search parameters
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    
    window.location.href = '{% url "accounts:vat_register_report" %}?' + params.toString();
}

// Auto-submit form on filter changes
document.addEventListener('DOMContentLoaded', function() {
    const filterInputs = document.querySelectorAll('#filter-form input, #filter-form select');
    
    filterInputs.forEach(input => {
        input.addEventListener('change', function() {
            // Don't auto-submit for date inputs to allow user to set both dates
            if (this.type === 'date') {
                return;
            }
            
            setTimeout(() => {
                document.getElementById('filter-form').submit();
            }, 100);
        });
    });
    
    lucide.createIcons();
});

// Print styles
const printStyles = `
    @media print {
        .no-print { display: none !important; }
        body { -webkit-print-color-adjust: exact; }
        .bg-sap-green-50 { background-color: #f0f9ff !important; }
        .bg-sap-blue-50 { background-color: #eff6ff !important; }
        table { page-break-inside: auto; }
        tr { page-break-inside: avoid; page-break-after: auto; }
        thead { display: table-header-group; }
        tfoot { display: table-footer-group; }
    }
`;

// Add print styles to head
const style = document.createElement('style');
style.textContent = printStyles;
document.head.appendChild(style);
</script>
{% endblock %}