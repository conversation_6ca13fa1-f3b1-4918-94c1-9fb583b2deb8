﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="Vat_Sales" targetNamespace="http://tempuri.org/Vat_Sales.xsd" xmlns:mstns="http://tempuri.org/Vat_Sales.xsd" xmlns="http://tempuri.org/Vat_Sales.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="Vat_Sales" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="Vat_Sales" msprop:Generator_DataSetName="Vat_Sales">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" msprop:Generator_UserColumnName="Id" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnPropNameInTable="IdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="SysDate" msprop:Generator_UserColumnName="SysDate" msprop:Generator_ColumnVarNameInTable="columnSysDate" msprop:Generator_ColumnPropNameInRow="SysDate" msprop:Generator_ColumnPropNameInTable="SysDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="InvoiceNo" msprop:Generator_UserColumnName="InvoiceNo" msprop:Generator_ColumnVarNameInTable="columnInvoiceNo" msprop:Generator_ColumnPropNameInRow="InvoiceNo" msprop:Generator_ColumnPropNameInTable="InvoiceNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CustomerCode" msprop:Generator_UserColumnName="CustomerCode" msprop:Generator_ColumnVarNameInTable="columnCustomerCode" msprop:Generator_ColumnPropNameInRow="CustomerCode" msprop:Generator_ColumnPropNameInTable="CustomerCodeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CustomerName" msprop:Generator_UserColumnName="CustomerName" msprop:Generator_ColumnVarNameInTable="columnCustomerName" msprop:Generator_ColumnPropNameInRow="CustomerName" msprop:Generator_ColumnPropNameInTable="CustomerNameColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Total" msprop:Generator_UserColumnName="Total" msprop:Generator_ColumnPropNameInRow="Total" msprop:Generator_ColumnVarNameInTable="columnTotal" msprop:Generator_ColumnPropNameInTable="TotalColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ExciseTerms" msprop:Generator_UserColumnName="ExciseTerms" msprop:Generator_ColumnPropNameInRow="ExciseTerms" msprop:Generator_ColumnVarNameInTable="columnExciseTerms" msprop:Generator_ColumnPropNameInTable="ExciseTermsColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ExciseValues" msprop:Generator_UserColumnName="ExciseValues" msprop:Generator_ColumnPropNameInRow="ExciseValues" msprop:Generator_ColumnVarNameInTable="columnExciseValues" msprop:Generator_ColumnPropNameInTable="ExciseValuesColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PFType" msprop:Generator_UserColumnName="PFType" msprop:Generator_ColumnPropNameInRow="PFType" msprop:Generator_ColumnVarNameInTable="columnPFType" msprop:Generator_ColumnPropNameInTable="PFTypeColumn" type="xs:int" minOccurs="0" />
              <xs:element name="PF" msprop:Generator_UserColumnName="PF" msprop:Generator_ColumnPropNameInRow="PF" msprop:Generator_ColumnVarNameInTable="columnPF" msprop:Generator_ColumnPropNameInTable="PFColumn" type="xs:double" minOccurs="0" />
              <xs:element name="AccessableValue" msprop:Generator_UserColumnName="AccessableValue" msprop:Generator_ColumnPropNameInRow="AccessableValue" msprop:Generator_ColumnVarNameInTable="columnAccessableValue" msprop:Generator_ColumnPropNameInTable="AccessableValueColumn" type="xs:double" minOccurs="0" />
              <xs:element name="EDUCess" msprop:Generator_UserColumnName="EDUCess" msprop:Generator_ColumnPropNameInRow="EDUCess" msprop:Generator_ColumnVarNameInTable="columnEDUCess" msprop:Generator_ColumnPropNameInTable="EDUCessColumn" type="xs:double" minOccurs="0" />
              <xs:element name="SHECess" msprop:Generator_UserColumnName="SHECess" msprop:Generator_ColumnPropNameInRow="SHECess" msprop:Generator_ColumnVarNameInTable="columnSHECess" msprop:Generator_ColumnPropNameInTable="SHECessColumn" type="xs:double" minOccurs="0" />
              <xs:element name="FreightType" msprop:Generator_UserColumnName="FreightType" msprop:Generator_ColumnVarNameInTable="columnFreightType" msprop:Generator_ColumnPropNameInRow="FreightType" msprop:Generator_ColumnPropNameInTable="FreightTypeColumn" type="xs:int" minOccurs="0" />
              <xs:element name="Freight" msprop:Generator_UserColumnName="Freight" msprop:Generator_ColumnVarNameInTable="columnFreight" msprop:Generator_ColumnPropNameInRow="Freight" msprop:Generator_ColumnPropNameInTable="FreightColumn" type="xs:double" minOccurs="0" />
              <xs:element name="VATCSTTerms" msprop:Generator_UserColumnName="VATCSTTerms" msprop:Generator_ColumnVarNameInTable="columnVATCSTTerms" msprop:Generator_ColumnPropNameInRow="VATCSTTerms" msprop:Generator_ColumnPropNameInTable="VATCSTTermsColumn" type="xs:string" minOccurs="0" />
              <xs:element name="VATCST" msprop:Generator_UserColumnName="VATCST" msprop:Generator_ColumnVarNameInTable="columnVATCST" msprop:Generator_ColumnPropNameInRow="VATCST" msprop:Generator_ColumnPropNameInTable="VATCSTColumn" type="xs:double" minOccurs="0" />
              <xs:element name="TotAmt" msprop:Generator_UserColumnName="TotAmt" msprop:Generator_ColumnVarNameInTable="columnTotAmt" msprop:Generator_ColumnPropNameInRow="TotAmt" msprop:Generator_ColumnPropNameInTable="TotAmtColumn" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="DataTable11" msprop:Generator_UserTableName="DataTable11" msprop:Generator_RowDeletedName="DataTable11RowDeleted" msprop:Generator_TableClassName="DataTable11DataTable" msprop:Generator_RowChangedName="DataTable11RowChanged" msprop:Generator_RowClassName="DataTable11Row" msprop:Generator_RowChangingName="DataTable11RowChanging" msprop:Generator_RowEvArgName="DataTable11RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable11RowChangeEventHandler" msprop:Generator_TablePropName="DataTable11" msprop:Generator_TableVarName="tableDataTable11" msprop:Generator_RowDeletingName="DataTable11RowDeleting">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" msprop:Generator_UserColumnName="Id" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInTable="IdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="SysDate" msprop:Generator_UserColumnName="SysDate" msprop:Generator_ColumnPropNameInRow="SysDate" msprop:Generator_ColumnVarNameInTable="columnSysDate" msprop:Generator_ColumnPropNameInTable="SysDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="InvoiceNo" msprop:Generator_UserColumnName="InvoiceNo" msprop:Generator_ColumnPropNameInRow="InvoiceNo" msprop:Generator_ColumnVarNameInTable="columnInvoiceNo" msprop:Generator_ColumnPropNameInTable="InvoiceNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CustomerCode" msprop:Generator_UserColumnName="CustomerCode" msprop:Generator_ColumnPropNameInRow="CustomerCode" msprop:Generator_ColumnVarNameInTable="columnCustomerCode" msprop:Generator_ColumnPropNameInTable="CustomerCodeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CustomerName" msprop:Generator_UserColumnName="CustomerName" msprop:Generator_ColumnPropNameInRow="CustomerName" msprop:Generator_ColumnVarNameInTable="columnCustomerName" msprop:Generator_ColumnPropNameInTable="CustomerNameColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Total" msprop:Generator_UserColumnName="Total" msprop:Generator_ColumnPropNameInRow="Total" msprop:Generator_ColumnVarNameInTable="columnTotal" msprop:Generator_ColumnPropNameInTable="TotalColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ExciseTerms" msprop:Generator_UserColumnName="ExciseTerms" msprop:Generator_ColumnPropNameInRow="ExciseTerms" msprop:Generator_ColumnVarNameInTable="columnExciseTerms" msprop:Generator_ColumnPropNameInTable="ExciseTermsColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ExciseValues" msprop:Generator_UserColumnName="ExciseValues" msprop:Generator_ColumnPropNameInRow="ExciseValues" msprop:Generator_ColumnVarNameInTable="columnExciseValues" msprop:Generator_ColumnPropNameInTable="ExciseValuesColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PFType" msprop:Generator_UserColumnName="PFType" msprop:Generator_ColumnPropNameInRow="PFType" msprop:Generator_ColumnVarNameInTable="columnPFType" msprop:Generator_ColumnPropNameInTable="PFTypeColumn" type="xs:int" minOccurs="0" />
              <xs:element name="PF" msprop:Generator_UserColumnName="PF" msprop:Generator_ColumnPropNameInRow="PF" msprop:Generator_ColumnVarNameInTable="columnPF" msprop:Generator_ColumnPropNameInTable="PFColumn" type="xs:double" minOccurs="0" />
              <xs:element name="AccessableValue" msprop:Generator_UserColumnName="AccessableValue" msprop:Generator_ColumnPropNameInRow="AccessableValue" msprop:Generator_ColumnVarNameInTable="columnAccessableValue" msprop:Generator_ColumnPropNameInTable="AccessableValueColumn" type="xs:double" minOccurs="0" />
              <xs:element name="EDUCess" msprop:Generator_UserColumnName="EDUCess" msprop:Generator_ColumnPropNameInRow="EDUCess" msprop:Generator_ColumnVarNameInTable="columnEDUCess" msprop:Generator_ColumnPropNameInTable="EDUCessColumn" type="xs:double" minOccurs="0" />
              <xs:element name="SHECess" msprop:Generator_UserColumnName="SHECess" msprop:Generator_ColumnPropNameInRow="SHECess" msprop:Generator_ColumnVarNameInTable="columnSHECess" msprop:Generator_ColumnPropNameInTable="SHECessColumn" type="xs:double" minOccurs="0" />
              <xs:element name="FreightType" msprop:Generator_UserColumnName="FreightType" msprop:Generator_ColumnPropNameInRow="FreightType" msprop:Generator_ColumnVarNameInTable="columnFreightType" msprop:Generator_ColumnPropNameInTable="FreightTypeColumn" type="xs:int" minOccurs="0" />
              <xs:element name="Freight" msprop:Generator_UserColumnName="Freight" msprop:Generator_ColumnPropNameInRow="Freight" msprop:Generator_ColumnVarNameInTable="columnFreight" msprop:Generator_ColumnPropNameInTable="FreightColumn" type="xs:double" minOccurs="0" />
              <xs:element name="VATCSTTerms" msprop:Generator_UserColumnName="VATCSTTerms" msprop:Generator_ColumnPropNameInRow="VATCSTTerms" msprop:Generator_ColumnVarNameInTable="columnVATCSTTerms" msprop:Generator_ColumnPropNameInTable="VATCSTTermsColumn" type="xs:string" minOccurs="0" />
              <xs:element name="VATCST" msprop:Generator_UserColumnName="VATCST" msprop:Generator_ColumnPropNameInRow="VATCST" msprop:Generator_ColumnVarNameInTable="columnVATCST" msprop:Generator_ColumnPropNameInTable="VATCSTColumn" type="xs:double" minOccurs="0" />
              <xs:element name="TotAmt" msprop:Generator_UserColumnName="TotAmt" msprop:Generator_ColumnPropNameInRow="TotAmt" msprop:Generator_ColumnVarNameInTable="columnTotAmt" msprop:Generator_ColumnPropNameInTable="TotAmtColumn" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>