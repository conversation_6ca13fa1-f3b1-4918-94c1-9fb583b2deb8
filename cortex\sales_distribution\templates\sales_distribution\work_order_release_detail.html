{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }} - Sales Distribution{% endblock %}

{% block extra_css %}
<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/umd/lucide.min.css">
<script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-sap-gray-50 font-sap">
    <!-- SAP Fiori Header -->
    <div class="bg-white shadow-sm border-b border-sap-gray-200">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                            <i data-lucide="play-circle" class="w-5 h-5 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-xl font-semibold text-sap-gray-900">{{ page_title }}</h1>
                            <p class="text-sm text-sap-gray-600">Review and release work order for production</p>
                        </div>
                    </div>
                </div>
                <nav class="flex items-center space-x-2 text-sm text-sap-gray-500">
                    <a href="{% url 'sales_distribution:dashboard' %}" class="hover:text-sap-blue-600 transition-colors">Sales & Distribution</a>
                    <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    <a href="{% url 'sales_distribution:work_order_release_list' %}" class="hover:text-sap-blue-600 transition-colors">Work Order Release</a>
                    <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    <span class="text-sap-gray-900 font-medium">{{ work_order.wono|default:"Work Order" }}</span>
                </nav>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-6 py-8">
        <!-- Work Order Information Card -->
        <div class="sap-card mb-6">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-sap-blue-100 rounded-xl flex items-center justify-center">
                        <i data-lucide="file-text" class="w-5 h-5 text-sap-blue-600"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-semibold text-sap-gray-900">Work Order Details</h2>
                        <p class="text-sm text-sap-gray-600">Review work order information before release</p>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        <i data-lucide="clock" class="w-3 h-3 mr-1"></i>
                        Pending Release
                    </span>
                </div>
            </div>

            <!-- Work Order Information Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Left Column -->
                <div class="space-y-4">
                    <div class="bg-sap-gray-50 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-sap-gray-700 mb-3">Basic Information</h3>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-sm text-sap-gray-600">Work Order No:</span>
                                <span class="text-sm font-medium text-sap-gray-900">{{ work_order.wono|default:"-" }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-sap-gray-600">Customer ID:</span>
                                <span class="text-sm font-medium text-sap-gray-900">{{ work_order.customerid|default:"-" }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-sap-gray-600">PO Number:</span>
                                <span class="text-sm font-medium text-sap-gray-900">{{ work_order.pono|default:"-" }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-sap-gray-600">Work Order Date:</span>
                                <span class="text-sm font-medium text-sap-gray-900">{{ work_order.taskworkorderdate|default:"-" }}</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-sap-gray-50 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-sap-gray-700 mb-3">Project Information</h3>
                        <div class="space-y-3">
                            <div>
                                <span class="text-sm text-sap-gray-600">Project Title:</span>
                                <p class="text-sm font-medium text-sap-gray-900 mt-1">{{ work_order.taskprojecttitle|default:"-" }}</p>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-sap-gray-600">Project Leader:</span>
                                <span class="text-sm font-medium text-sap-gray-900">{{ work_order.taskprojectleader|default:"-" }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-sap-gray-600">Category:</span>
                                <span class="text-sm font-medium text-sap-gray-900">{{ work_order.cid.cname|default:"-" }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="space-y-4">
                    <div class="bg-sap-gray-50 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-sap-gray-700 mb-3">Timeline Information</h3>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-sm text-sap-gray-600">Target DAP Date:</span>
                                <span class="text-sm font-medium text-sap-gray-900">
                                    {% if work_order.tasktargetdap_fdate and work_order.tasktargetdap_tdate %}
                                        {{ work_order.tasktargetdap_fdate }} to {{ work_order.tasktargetdap_tdate }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-sap-gray-600">Target Manufacturing:</span>
                                <span class="text-sm font-medium text-sap-gray-900">
                                    {% if work_order.tasktargetmanufg_fdate and work_order.tasktargetmanufg_tdate %}
                                        {{ work_order.tasktargetmanufg_fdate }} to {{ work_order.tasktargetmanufg_tdate }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-sap-gray-600">Target Despatch:</span>
                                <span class="text-sm font-medium text-sap-gray-900">
                                    {% if work_order.tasktargetdespach_fdate and work_order.tasktargetdespach_tdate %}
                                        {{ work_order.tasktargetdespach_fdate }} to {{ work_order.tasktargetdespach_tdate }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-sap-gray-50 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-sap-gray-700 mb-3">Shipping Information</h3>
                        <div class="space-y-3">
                            <div>
                                <span class="text-sm text-sap-gray-600">Shipping Address:</span>
                                <p class="text-sm font-medium text-sap-gray-900 mt-1">{{ work_order.shippingadd|default:"-" }}</p>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-sap-gray-600">Contact Person:</span>
                                <span class="text-sm font-medium text-sap-gray-900">{{ work_order.shippingcontactperson1|default:"-" }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Release Confirmation Card -->
        <div class="sap-card">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                        <i data-lucide="check-circle" class="w-5 h-5 text-green-600"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-semibold text-sap-gray-900">Release Confirmation</h2>
                        <p class="text-sm text-sap-gray-600">Confirm release of this work order for production</p>
                    </div>
                </div>
            </div>

            <!-- Release Information -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <i data-lucide="info" class="w-5 h-5 text-green-600 mt-0.5"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-green-800">Release Checklist</h3>
                        <div class="mt-2 text-sm text-green-700">
                            <ul class="list-disc list-inside space-y-1">
                                <li>All work order information has been verified</li>
                                <li>Customer requirements are clearly defined</li>
                                <li>Timeline and deadlines are confirmed</li>
                                <li>Material requirements are available</li>
                                <li>Production resources are allocated</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-4">
                <a href="{% url 'sales_distribution:work_order_release_list' %}" 
                   class="inline-flex items-center px-6 py-3 text-sm font-medium text-sap-gray-700 bg-white border border-sap-gray-300 rounded-lg hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 transition-all duration-200">
                    <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                    Back to List
                </a>
                
                <form method="post" class="inline">
                    {% csrf_token %}
                    <button type="submit" 
                            onclick="return confirm('Are you sure you want to release this work order for production? This action cannot be undone.')"
                            class="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 transform hover:scale-105">
                        <i data-lucide="play" class="w-4 h-4 mr-2"></i>
                        Release Work Order
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize Lucide icons
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}