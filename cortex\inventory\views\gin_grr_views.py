from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy, reverse
from django.db.models import Count
from django.http import JsonResponse
from django.utils import timezone
from sys_admin.models import Company, FinancialYear
from ..models import (
    GoodsInwardNote, GINLineItem, GoodsReceivedReceipt
)
from ..forms.gin_grr_forms import (
    GoodsInwardNoteForm, GoodsReceivedReceiptForm, 
    GRRSearchForm
)


def get_current_company(request=None):
    """Get current company (first one for now)"""
    return Company.objects.first()


def get_current_financial_year(request=None):
    """Get current financial year (first one for now)"""
    return FinancialYear.objects.first()


# Goods Inward Note (GIN) Views

class GINListView(LoginRequiredMixin, ListView):
    """Main GIN interface with supplier search and filtering - matches ASP.NET GIN interface"""
    model = GoodsInwardNote
    template_name = 'inventory/transactions/gin_main.html'
    context_object_name = 'gin_list'
    paginate_by = 20

    def get_queryset(self):
        from django.db import connection
        
        # Get current company and financial year from session  
        company_id = self.request.session.get('compid', 1)
        fin_year_id = self.request.session.get('finyear', 9)
        
        # Apply filters from search form (matching ASP.NET interface)
        search_type = self.request.GET.get('search_type', '0')
        search_query = self.request.GET.get('search_query', '')
        supplier_id = self.request.GET.get('supplier_id', '').strip()
        
        # Build the complex SQL query to join GIN + PO + Supplier data
        sql_query = """
            SELECT DISTINCT
                gin.Id as id,
                gin.GINNo as gin_number,
                gin.PONo as po_number,
                gin.GDate as gin_date,
                gin.ChallanNo as challan_number,
                gin.ChallanDate as challan_date,
                fy.FinYear as fin_year,
                COALESCE(s.SupplierName, 'UNKNOWN') as supplier_name,
                s.SupplierId as supplier_code,
                gin.CompId,
                gin.FinYearId
            FROM tblInv_Inward_Master gin
            LEFT JOIN tblFinancial_master fy ON gin.FinYearId = fy.FinYearId
            LEFT JOIN tblMM_PO_Master po ON gin.PONo = po.PONo AND po.CompId = gin.CompId AND po.FinYearId = gin.FinYearId
            LEFT JOIN tblMM_Supplier_master s ON po.SupplierId = s.SupplierId AND s.CompId = gin.CompId
            WHERE gin.CompId = ? AND gin.FinYearId = ?
        """
        
        params = [company_id, fin_year_id]
        
        # Add supplier filter if provided
        if supplier_id:
            sql_query += " AND s.SupId = ?"
            params.append(supplier_id)
        
        # Add search filter based on search type
        if search_query:
            if search_type == '0':  # Supplier Name
                sql_query += " AND s.SupplierName LIKE ?"
                params.append(f"%{search_query}%")
            elif search_type == '1':  # PO No
                sql_query += " AND gin.PONo LIKE ?"
                params.append(f"%{search_query}%")
            elif search_type == '2':  # GIN No
                sql_query += " AND gin.GINNo LIKE ?"
                params.append(f"%{search_query}%")
        
        sql_query += " ORDER BY gin.GDate DESC, gin.Id DESC"
        
        # Temporarily disable DEBUG to avoid SQL logging issues
        from django.conf import settings
        original_debug = settings.DEBUG
        settings.DEBUG = False
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(sql_query, params)
                columns = [col[0] for col in cursor.description]
                rows = cursor.fetchall()
                
                gin_records = []
                for row in rows:
                    gin_dict = dict(zip(columns, row))
                    
                    # Format dates for display
                    gin_date = gin_dict.get('gin_date', '')
                    challan_date = gin_dict.get('challan_date', '')
                    
                    # Handle date formatting
                    if gin_date:
                        try:
                            if isinstance(gin_date, str) and len(gin_date) >= 10:
                                # Convert from database format to display format
                                gin_date = gin_date[:10]  # Take only date part if datetime
                        except:
                            pass
                    
                    if challan_date:
                        try:
                            if isinstance(challan_date, str) and len(challan_date) >= 10:
                                # Convert from database format to display format  
                                challan_date = challan_date[:10]  # Take only date part if datetime
                        except:
                            pass
                    
                    gin_records.append({
                        'id': gin_dict['id'],
                        'pk': gin_dict['id'],  # Add pk for template compatibility
                        'gin_number': gin_dict['gin_number'] or '',
                        'po_number': gin_dict['po_number'] or '',
                        'gin_date': gin_date,
                        'challan_number': gin_dict['challan_number'] or '',
                        'challan_date': challan_date,
                        'fin_year': gin_dict['fin_year'] or '',
                        'supplier_name': gin_dict['supplier_name'] or 'UNKNOWN',
                        'supplier_code': gin_dict['supplier_code'] or ''
                    })
            
            return gin_records
        finally:
            # Restore original DEBUG setting
            settings.DEBUG = original_debug

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_type'] = self.request.GET.get('search_type', '0')
        context['search_query'] = self.request.GET.get('search_query', '')
        context['supplier_id'] = self.request.GET.get('supplier_id', '')
        
        # Override the pagination since we're using raw SQL
        gin_list = self.get_queryset()
        
        from django.core.paginator import Paginator
        paginator = Paginator(gin_list, self.paginate_by)
        page = self.request.GET.get('page')
        
        try:
            gin_page = paginator.page(page)
        except:
            gin_page = paginator.page(1)
        
        context['gin_list'] = gin_page
        context['is_paginated'] = gin_page.has_other_pages()
        context['page_obj'] = gin_page
        context['paginator'] = paginator
        
        return context

    def render_to_response(self, context, **response_kwargs):
        if self.request.headers.get('HX-Request'):
            return render(self.request, 'inventory/transactions/partials/gin_results.html', context)
        return super().render_to_response(context, **response_kwargs)


class GINDetailView(LoginRequiredMixin, DetailView):
    """Detail view for Goods Inward Note"""
    model = GoodsInwardNote
    template_name = 'inventory/transactions/gin_detail.html'
    context_object_name = 'gin'

    def get_queryset(self):
        return GoodsInwardNote.objects.select_related(
            'company', 'financial_year'
        ).prefetch_related(
            'receipts'  # 'details' relationship doesn't exist
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        gin = self.get_object()
        
        # Fetch line items for this GIN
        from django.db import connection
        sql_query = """
            SELECT 
                gin_details.Id as detail_id,
                gin_details.GINNo as gin_number,
                gin_details.Qty as po_quantity,
                gin_details.ReceivedQty as received_quantity,
                COALESCE(item.ItemCode, 'N/A') as item_code,
                COALESCE(item.ManfDesc, 'N/A') as item_description,
                'Each' as uom,
                'General' as category,
                'Standard' as subcategory,
                po_details.PONo as po_number,
                po_details.Rate as unit_rate
            FROM tblInv_Inward_Details gin_details
            LEFT JOIN tblMM_PO_Details po_details ON gin_details.POId = po_details.Id
            LEFT JOIN tblDG_Item_Master item ON po_details.MId = item.Id
            WHERE gin_details.GINId = ?
            ORDER BY gin_details.Id
        """
        
        try:
            with connection.cursor() as cursor:
                # Use string formatting for now to avoid parameter issue
                final_query = sql_query.replace('?', str(gin.id))
                cursor.execute(final_query)
                columns = [col[0] for col in cursor.description]
                rows = cursor.fetchall()
                
                line_items = []
                for row in rows:
                    item_dict = dict(zip(columns, row))
                    line_items.append(item_dict)
                
                context['line_items'] = line_items
                
        except Exception:
            # Fallback to simple line items query
            line_items = GINLineItem.objects.filter(gin_id=gin.id)
            context['line_items'] = line_items
        
        # Check if user can edit this GIN (received_by field not available)
        context['can_edit'] = (
            # gin.can_be_edited and 
            # (gin.received_by == self.request.user or 
            self.request.user.has_perm('inventory.change_goodsinwardnote')
            # )
        )
        
        # Check if GIN can be processed to next stage (status field not available)
        # context['can_process'] = gin.can_be_processed
        context['can_process'] = True  # Status field not available
        
        return context


class GINCreateView(LoginRequiredMixin, CreateView):
    """Create view for Goods Inward Note"""
    model = GoodsInwardNote
    form_class = GoodsInwardNoteForm
    template_name = 'inventory/transactions/gin_form.html'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        kwargs['company'] = get_current_company(self.request)
        kwargs['financial_year'] = get_current_financial_year(self.request)
        return kwargs

    def form_valid(self, form):
        messages.success(self.request, 'Goods Inward Note created successfully.')
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('inventory:gin_detail', kwargs={'pk': self.object.pk})


class GINUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for Goods Inward Note"""
    model = GoodsInwardNote
    form_class = GoodsInwardNoteForm
    template_name = 'inventory/transactions/gin_form.html'

    def get_queryset(self):
        # Users can edit GIN (received_by field not available)
        queryset = GoodsInwardNote.objects.all()
        # if not self.request.user.has_perm('inventory.change_goodsinwardnote'):
        #     queryset = queryset.filter(received_by=self.request.user)
        return queryset

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        kwargs['company'] = self.object.company
        kwargs['financial_year'] = self.object.financial_year
        return kwargs

    def form_valid(self, form):
        # Check if GIN can be edited (status field not available, so allow editing)
        # if not self.object.can_be_edited:
        #     messages.error(self.request, 'This GIN cannot be edited in its current status.')
        #     return redirect('inventory:gin_detail', pk=self.object.pk)
        
        messages.success(self.request, 'Goods Inward Note updated successfully.')
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('inventory:gin_detail', kwargs={'pk': self.object.pk})


class GINDeleteView(LoginRequiredMixin, DeleteView):
    """Delete view for Goods Inward Note"""
    model = GoodsInwardNote
    template_name = 'inventory/transactions/gin_confirm_delete.html'
    success_url = reverse_lazy('inventory:gin_list')

    def get_queryset(self):
        # Only allow deletion of GIN by creator or users with permission (no status field available)
        queryset = GoodsInwardNote.objects.all()  # Status field not available
        # if not self.request.user.has_perm('inventory.delete_goodsinwardnote'):
        #     queryset = queryset.filter(received_by=self.request.user)
        return queryset

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Goods Inward Note deleted successfully.')
        return super().delete(request, *args, **kwargs)


# GIN Edit Views (matching ASP.NET interface)

class GINEditListView(LoginRequiredMixin, ListView):
    """Edit list view for Goods Inward Notes - matches ASP.NET GIN Edit interface"""
    model = GoodsInwardNote
    template_name = 'inventory/transactions/gin_edit.html'
    context_object_name = 'gin_list'
    paginate_by = 20

    def get_queryset(self):
        from django.db import connection
        
        # Get current company and financial year from session  
        company_id = self.request.session.get('compid', 1)
        fin_year_id = self.request.session.get('finyear', 9)
        
        # Apply filters from search form (matching ASP.NET interface)
        search_type = self.request.GET.get('search_type', '0')
        search_query = self.request.GET.get('search_query', '')
        supplier_name = self.request.GET.get('supplier_name', '').strip()
        
        # Build search filter with parameterized query
        search_filter = ""
        search_params = [company_id, fin_year_id]
        
        if search_query:
            if search_type == '0':  # Supplier Name
                if search_query:
                    search_filter += " AND sup.SupplierName LIKE ?"
                    search_params.append(f"%{search_query}%")
            elif search_type == '1':  # PO No
                search_filter += " AND gin.PONo LIKE ?"
                search_params.append(f"%{search_query}%")
            elif search_type == '2':  # GIN No
                search_filter += " AND gin.GINNo LIKE ?"
                search_params.append(f"%{search_query}%")
        
        if supplier_name:
            # Extract supplier ID from text like "SUPPLIER NAME [SUPPLIER_ID]"
            if '[' in supplier_name and ']' in supplier_name:
                supplier_id = supplier_name.split('[')[1].split(']')[0]
                search_filter += " AND po.SupplierId = ?"
                search_params.append(supplier_id)
            else:
                search_filter += " AND sup.SupplierName LIKE ?"
                search_params.append(f"%{supplier_name}%")
        
        # Query to get GIN records with supplier information
        sql_query = """
            SELECT DISTINCT
                gin.Id,
                gin.CompId,
                gin.FinYearId,
                fy.FinYear as FinYear,
                gin.GINNo as gin_number,
                gin.GDate as gin_date,
                gin.PONo as po_number,
                gin.ChallanNo as challan_number,
                gin.ChallanDate as challan_date,
                COALESCE(po.SupplierId, "") as supplier_id,
                COALESCE(sup.SupplierName, "UNKNOWN") as supplier_name
            FROM tblInv_Inward_Master gin
            LEFT JOIN tblFinancial_master fy ON gin.FinYearId = fy.FinYearId
            LEFT JOIN tblMM_PO_Master po ON gin.PONo = po.PONo AND po.CompId = gin.CompId
            LEFT JOIN tblMM_Supplier_master sup ON po.SupplierId = sup.SupplierId AND sup.CompId = gin.CompId
            WHERE gin.CompId = ? AND gin.FinYearId = ?
        """ + search_filter + """
            ORDER BY gin.GDate DESC, gin.Id DESC
        """
        
        # Temporarily disable DEBUG to avoid SQL formatting issues with ? placeholders
        from django.conf import settings
        original_debug = settings.DEBUG
        settings.DEBUG = False
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(sql_query, search_params)
                
                columns = [col[0] for col in cursor.description]
                rows = cursor.fetchall()
                
                # Convert to list of dictionaries
                gin_list = []
                for row in rows:
                    gin_dict = dict(zip(columns, row))
                    # Add pk for template compatibility (Django templates expect .pk attribute)
                    gin_dict['pk'] = gin_dict.get('id')
                    # Format date for display
                    if gin_dict.get('gin_date'):
                        from datetime import datetime
                        if isinstance(gin_dict['gin_date'], str):
                            try:
                                date_obj = datetime.strptime(gin_dict['gin_date'], '%Y-%m-%d')
                                gin_dict['gin_date'] = date_obj.strftime('%d-%m-%Y')
                            except:
                                pass
                    gin_list.append(gin_dict)
        finally:
            settings.DEBUG = original_debug
        
        return gin_list

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_type'] = self.request.GET.get('search_type', '0')
        context['search_query'] = self.request.GET.get('search_query', '')
        context['supplier_name'] = self.request.GET.get('supplier_name', '')
        
        # Override the pagination since we're using raw SQL
        gin_list = self.get_queryset()
        
        from django.core.paginator import Paginator
        paginator = Paginator(gin_list, self.paginate_by)
        page = self.request.GET.get('page')
        
        try:
            gin_page = paginator.page(page)
        except:
            gin_page = paginator.page(1)
        
        context['gin_list'] = gin_page
        context['is_paginated'] = gin_page.has_other_pages()
        context['page_obj'] = gin_page
        context['paginator'] = paginator
        
        return context


class GINEditDetailView(LoginRequiredMixin, View):
    """Edit detail view for individual GIN - matches ASP.NET GIN Edit Details interface"""
    template_name = 'inventory/transactions/gin_edit_detail.html'

    def get(self, request, pk):
        from django.db import connection
        import os
        
        # Get URL parameters and GIN ID
        gin_id = pk
        company_id = request.session.get('compid', 1)
        
        # Get GIN header information
        gin_data = {}
        supplier_name = ''
        business_group = ''
        
        # Temporarily disable DEBUG to avoid SQL logging issues
        debug_setting = os.environ.get('DJANGO_DEBUG', 'True')
        os.environ['DJANGO_DEBUG'] = 'False'
        
        try:
            with connection.cursor() as cursor:
                # Get GIN header details
                cursor.execute("""
                    SELECT 
                        gin.Id,
                        gin.GINNo,
                        gin.GDate,
                        gin.ChallanNo,
                        gin.ChallanDate,
                        gin.PONo,
                        gin.GateEntryNo,
                        gin.ModeOfTransport,
                        gin.VehicleNo,
                        gin.BusinessGroup,
                        COALESCE(s.SupplierName, 'UNKNOWN') as supplier_name,
                        fy.FinYear
                    FROM tblInv_Inward_Master gin
                    LEFT JOIN tblMM_PO_Master po ON gin.PONo = po.PONo AND po.CompId = gin.CompId
                    LEFT JOIN tblMM_Supplier_master s ON po.SupplierId = s.SupplierId AND s.CompId = gin.CompId
                    LEFT JOIN tblFinancial_master fy ON gin.FinYearId = fy.FinYearId
                    WHERE gin.Id = ? AND gin.CompId = ?
                """, [gin_id, company_id])
                
                gin_row = cursor.fetchone()
                
                if gin_row:
                    gin_data = {
                        'id': gin_row[0],
                        'gin_number': gin_row[1] or '',
                        'gin_date': gin_row[2] or '',
                        'challan_number': gin_row[3] or '',
                        'challan_date': gin_row[4] or '',
                        'po_number': gin_row[5] or '',
                        'gate_entry_number': gin_row[6] or '',
                        'mode_of_transport': gin_row[7] or '',
                        'vehicle_number': gin_row[8] or '',
                        'business_group': gin_row[9] or 'FABRICATION-SAPL Plant 2',
                        'supplier_name': gin_row[10] or 'UNKNOWN',
                        'fin_year': gin_row[11] or ''
                    }
                
                # Get line items data (matching the complex ASP.NET structure)
                line_items = self.get_line_items(gin_id, gin_data.get('gin_number', ''), company_id, cursor)
        
        finally:
            # Restore original DEBUG setting
            os.environ['DJANGO_DEBUG'] = debug_setting
        
        context = {
            'gin': gin_data,
            'gin_line_items': line_items,
            'gin_id': gin_id
        }
        
        return render(request, self.template_name, context)
    
    def get_line_items(self, gin_id, gin_no, company_id, cursor):
        """Get line items for GIN with full details"""
        line_items = []
        
        # Get GIN detail records with item information
        cursor.execute("""
            SELECT 
                gid.Id,
                gid.POId,
                gid.ReceivedQty,
                gid.ChallanQty,
                po.PONo,
                pod.Id as PODetailId,
                pod.Qty as POQty,
                pod.PRId,
                pod.SPRId,
                pom.PRSPRFlag,
                COALESCE(im.ItemCode, '') as ItemCode,
                COALESCE(im.ManfDesc, '') as Description,
                COALESCE(um.Symbol, 'NOS') as UOM,
                COALESCE(cat.CategoryName, 'NA') as Category,
                COALESCE(subcat.SubCategoryName, 'NA') as SubCategory
            FROM tblInv_Inward_Details gid
            JOIN tblInv_Inward_Master gim ON gim.Id = gid.GINId
            LEFT JOIN tblMM_PO_Master pom ON gid.POId = pom.Id
            LEFT JOIN tblMM_PO_Details pod ON pod.MId = pom.Id AND pod.Id = gid.POId
            LEFT JOIN tblDG_Item_Master im ON pod.ItemId = im.Id AND im.CompId = ?
            LEFT JOIN Unit_Master um ON im.UOMBasic = um.Id
            LEFT JOIN tblDG_Category_Master cat ON pod.CategoryId = cat.Id
            LEFT JOIN tblDG_SubCategory_Master subcat ON pod.SubCategoryId = subcat.Id
            WHERE gim.Id = ? AND gim.CompId = ?
            ORDER BY gid.Id
        """, [company_id, gin_id, company_id])
        
        gin_details = cursor.fetchall()
        
        for idx, detail in enumerate(gin_details, 1):
            (detail_id, po_id, received_qty, challan_qty, po_no, po_detail_id, 
             po_qty, pr_id, spr_id, pr_spr_flag, item_code, description, 
             uom, category, sub_category) = detail
            
            # Get total received quantity for this item across all GINs
            cursor.execute("""
                SELECT COALESCE(SUM(gid2.ReceivedQty), 0) as TotalReceivedQty
                FROM tblInv_Inward_Details gid2
                JOIN tblInv_Inward_Master gim2 ON gim2.Id = gid2.GINId
                WHERE gim2.CompId = ? AND gid2.POId = ?
            """, [company_id, po_id])
            
            total_received = cursor.fetchone()
            total_received_qty = total_received[0] if total_received else 0
            
            line_item = {
                'id': detail_id,
                'sn': idx,
                'item_code': item_code or f'Item-{po_id}',
                'description': description or 'Item Description',
                'uom': uom or 'NOS',
                'category': category or 'NA',
                'sub_category': sub_category or 'NA',
                'po_qty': f"{float(po_qty):.0f}" if po_qty else "1",
                'total_received_qty': f"{float(total_received_qty):.0f}",
                'challan_qty': f"{float(challan_qty):.0f}" if challan_qty else "1",
                'received_qty': f"{float(received_qty):.0f}" if received_qty else "1",
                'po_id': po_id,
                'has_image': False,  # Would need to check if image exists
                'has_spec': False,   # Would need to check if spec exists
            }
            
            line_items.append(line_item)
        
        return line_items

    def post(self, request, pk):
        """Handle form submission for GIN edit"""
        from django.db import connection
        import os
        
        company_id = request.session.get('compid', 1)
        
        # Get form data
        gate_entry_no = request.POST.get('gate_entry_no', '')
        gin_date = request.POST.get('gin_date', '')
        mode_of_transport = request.POST.get('mode_of_transport', '')
        vehicle_no = request.POST.get('vehicle_no', '')
        
        # Handle time components
        time_hour = request.POST.get('time_hour', '09')
        time_minute = request.POST.get('time_minute', '56')
        time_second = request.POST.get('time_second', '00')
        time_period = request.POST.get('time_period', 'AM')
        gin_time = f"{time_hour}:{time_minute}:{time_second} {time_period}"
        
        # Temporarily disable DEBUG to avoid SQL logging issues
        debug_setting = os.environ.get('DJANGO_DEBUG', 'True')
        os.environ['DJANGO_DEBUG'] = 'False'
        
        try:
            with connection.cursor() as cursor:
                # Update GIN record
                cursor.execute("""
                    UPDATE tblInv_Inward_Master 
                    SET GateEntryNo = ?, GDate = ?, ModeOfTransport = ?, VehicleNo = ?
                    WHERE Id = ? AND CompId = ?
                """, [gate_entry_no, gin_date, mode_of_transport, vehicle_no, pk, company_id])
        
        finally:
            # Restore original DEBUG setting
            os.environ['DJANGO_DEBUG'] = debug_setting
        
        messages.success(request, 'GIN updated successfully.')
        return redirect('inventory:gin_edit_detail', pk=pk)


# Goods Received Receipt (GRR) Views

class GRRListView(LoginRequiredMixin, ListView):
    """List view for Goods Received Receipts with real-time search"""
    template_name = 'inventory/transactions/grr_list.html'
    context_object_name = 'grr_list'
    paginate_by = 20

    def get_queryset(self):
        from django.db import connection
        
        # Get current company and financial year from session  
        company_id = self.request.session.get('compid', 1)
        fin_year_id = self.request.session.get('finyear', 9)  # Default to current year (2021-2022)
        
        # Apply filters from search form
        search = self.request.GET.get('search', '')
        
        # Build search filter with parameterized query
        search_filter = ""
        search_params = [company_id, fin_year_id]
        if search:
            search_filter = """
                AND (mr.GRRNo LIKE ? 
                     OR gin.GINNo LIKE ? 
                     OR mr.TaxInvoiceNo LIKE ?
                     OR sup.SupplierName LIKE ?)
            """
            # Add search parameters with wildcards
            search_term = f"%{search}%"
            search_params.extend([search_term, search_term, search_term, search_term])
        
        # Query to get GRR records with quality check information
        sql_query = """
            SELECT DISTINCT
                mr.Id,
                mr.CompId,
                mr.FinYearId,
                fy.FinYear as FinYear,
                mr.GRRNo as grr_number,
                mr.SysDate as grr_date,
                mr.GINId,
                gin.GINNo as gin_number,
                gin.PONo as po_number,
                gin.ChallanNo as challan_number,
                gin.ChallanDate as challan_date,
                mr.TaxInvoiceNo as tax_invoice_number,
                mr.TaxInvoiceDate as tax_invoice_date,
                COALESCE(po.SupplierId, "") as supplier_id,
                COALESCE(sup.SupplierName, "UNKNOWN") as supplier_name,
                -- Quality check information
                qm.GQNNo as quality_note_number,
                qm.SysDate as quality_check_date,
                qm.SessionId as quality_checked_by_id,
                CASE 
                    WHEN qm.Id IS NULL THEN 'QUALITY_PENDING'
                    WHEN qm.Id IS NOT NULL THEN 'QUALITY_COMPLETED'
                    ELSE 'DRAFT'
                END as status
            FROM tblinv_MaterialReceived_Master mr
            LEFT JOIN tblFinancial_master fy ON mr.FinYearId = fy.FinYearId
            LEFT JOIN tblInv_Inward_Master gin ON mr.GINId = gin.Id
            LEFT JOIN tblMM_PO_Master po ON gin.PONo = po.PONo AND po.CompId = mr.CompId
            LEFT JOIN tblMM_Supplier_master sup ON po.SupplierId = sup.SupplierId AND sup.CompId = mr.CompId
            LEFT JOIN tblQc_MaterialQuality_Master qm ON mr.Id = qm.GRRId
            WHERE mr.CompId = ? AND mr.FinYearId = ?
        """ + search_filter + """
            ORDER BY mr.SysDate DESC, mr.Id DESC
        """
        
        # Temporarily disable DEBUG to avoid SQL logging issues
        from django.conf import settings
        original_debug = settings.DEBUG
        settings.DEBUG = False
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(sql_query, search_params)
                
                columns = [col[0] for col in cursor.description]
                rows = cursor.fetchall()
        finally:
            # Restore original DEBUG setting
            settings.DEBUG = original_debug
        
        # Convert to list of dictionaries
        grr_list = []
        for row in rows:
            grr_dict = dict(zip(columns, row))
            # Add pk for template compatibility (Django templates expect .pk attribute)
            grr_dict['pk'] = grr_dict.get('Id')
            # Format date for display
            for date_field in ['grr_date', 'challan_date', 'tax_invoice_date']:
                if grr_dict.get(date_field):
                    from datetime import datetime
                    if isinstance(grr_dict[date_field], str):
                        try:
                            date_obj = datetime.strptime(grr_dict[date_field], '%Y-%m-%d')
                            grr_dict[date_field] = date_obj.date()
                        except:
                            pass
            grr_list.append(grr_dict)
        
        return grr_list

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = GRRSearchForm(self.request.GET)
        
        # Statistics for dashboard using MaterialReceived table
        from django.db import connection
        from django.conf import settings
        
        # Temporarily disable DEBUG to avoid SQL logging issues
        original_debug = settings.DEBUG
        settings.DEBUG = False
        
        try:
            with connection.cursor() as cursor:
                # Get current company and financial year from session  
                company_id = self.request.session.get('compid', 1)
                fin_year_id = self.request.session.get('finyear', 9)
                
                # Total GRR count for current company/financial year
                cursor.execute(
                    "SELECT COUNT(*) FROM tblinv_MaterialReceived_Master WHERE CompId = ? AND FinYearId = ?",
                    [company_id, fin_year_id]
                )
                total_grr = cursor.fetchone()[0]
                
                # Quality pending GRRs (no quality check record)
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM tblinv_MaterialReceived_Master mr
                    LEFT JOIN tblQc_MaterialQuality_Master qm ON mr.Id = qm.GRRId
                    WHERE mr.CompId = ? AND mr.FinYearId = ? AND qm.Id IS NULL
                """, [company_id, fin_year_id])
                quality_pending = cursor.fetchone()[0]
                
                # Quality completed GRRs
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM tblinv_MaterialReceived_Master mr
                    INNER JOIN tblQc_MaterialQuality_Master qm ON mr.Id = qm.GRRId
                    WHERE mr.CompId = ? AND mr.FinYearId = ?
                """, [company_id, fin_year_id])
                approved_grr = cursor.fetchone()[0]
                
                # Set total accepted value to 0 for now (would need proper price calculation)
                total_accepted_value = 0
        finally:
            # Restore original DEBUG setting
            settings.DEBUG = original_debug
        
        context['stats'] = {
            'total_grr': total_grr,
            'quality_pending': quality_pending,
            'approved_grr': approved_grr,
            'total_accepted_value': total_accepted_value
        }
        
        return context

    def render_to_response(self, context, **response_kwargs):
        if self.request.headers.get('HX-Request'):
            return render(self.request, 'inventory/transactions/partials/grr_results.html', context)
        return super().render_to_response(context, **response_kwargs)


def GRRDetailView(request, pk):
    """Detail view for Goods Received Receipt using raw database queries"""
    from django.db import connection
    from django.conf import settings
    
    # Get current company 
    company_id = request.session.get('compid', 1)
    
    # Temporarily disable DEBUG
    original_debug = settings.DEBUG
    settings.DEBUG = False
    
    try:
        with connection.cursor() as cursor:
            # Get GRR details with related information
            cursor.execute("""
                SELECT DISTINCT
                    mr.Id,
                    mr.GRRNo as grr_number,
                    mr.SysDate as grr_date,
                    mr.GINId,
                    gin.GINNo as gin_number,
                    gin.PONo as po_number,
                    gin.ChallanNo as challan_number,
                    gin.ChallanDate as challan_date,
                    mr.TaxInvoiceNo as tax_invoice_number,
                    mr.TaxInvoiceDate as tax_invoice_date,
                    COALESCE(sup.SupplierName, "UNKNOWN") as supplier_name,
                    fy.FinYear as fin_year,
                    qm.GQNNo as quality_note_number,
                    qm.SysDate as quality_check_date,
                    qm.SessionId as quality_checked_by_id,
                    CASE 
                        WHEN qm.Id IS NULL THEN 'QUALITY_PENDING'
                        WHEN qm.Id IS NOT NULL THEN 'QUALITY_COMPLETED'
                        ELSE 'DRAFT'
                    END as status
                FROM tblinv_MaterialReceived_Master mr
                LEFT JOIN tblFinancial_master fy ON mr.FinYearId = fy.FinYearId
                LEFT JOIN tblInv_Inward_Master gin ON mr.GINId = gin.Id
                LEFT JOIN tblMM_PO_Master po ON gin.PONo = po.PONo AND po.CompId = mr.CompId
                LEFT JOIN tblMM_Supplier_master sup ON po.SupplierId = sup.SupplierId AND sup.CompId = mr.CompId
                LEFT JOIN tblQc_MaterialQuality_Master qm ON mr.Id = qm.GRRId
                WHERE mr.Id = ? AND mr.CompId = ?
            """, [pk, company_id])
            
            grr_row = cursor.fetchone()
            
            if not grr_row:
                from django.http import Http404
                raise Http404("GRR not found")
            
            # Convert to dictionary
            columns = [col[0] for col in cursor.description]
            grr_data = dict(zip(columns, grr_row))
    finally:
        settings.DEBUG = original_debug
    
    context = {
        'grr': grr_data,
        'can_edit': grr_data['status'] in ['DRAFT', 'QUALITY_PENDING'],
        'can_approve': grr_data['status'] == 'QUALITY_COMPLETED'
    }
    
    return render(request, 'inventory/transactions/grr_detail.html', context)

# Apply login_required decorator
GRRDetailView = login_required(GRRDetailView)


class GRRCreateView(LoginRequiredMixin, CreateView):
    """Create view for Goods Received Receipt"""
    model = GoodsReceivedReceipt
    form_class = GoodsReceivedReceiptForm
    template_name = 'inventory/transactions/grr_form.html'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        kwargs['company'] = get_current_company(self.request)
        kwargs['financial_year'] = get_current_financial_year(self.request)
        return kwargs

    def form_valid(self, form):
        messages.success(self.request, 'Goods Received Receipt created successfully.')
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('inventory:grr_detail', kwargs={'pk': self.object.pk})


class GRRUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for Goods Received Receipt"""
    model = GoodsReceivedReceipt
    form_class = GoodsReceivedReceiptForm
    template_name = 'inventory/transactions/grr_form.html'

    def get_queryset(self):
        # Users can only edit their own GRR or if they have permission
        queryset = GoodsReceivedReceipt.objects.all()
        if not self.request.user.has_perm('inventory.change_goodsreceivedreceipt'):
            queryset = queryset.filter(created_by=self.request.user)
        return queryset

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        kwargs['company'] = self.object.company
        kwargs['financial_year'] = self.object.financial_year
        return kwargs

    def form_valid(self, form):
        # Check if GRR can be edited (status field not available, so allow editing)
        # if self.object.status not in ['DRAFT', 'QUALITY_PENDING']:
        #     messages.error(self.request, 'This GRR cannot be edited in its current status.')
        #     return redirect('inventory:grr_detail', pk=self.object.pk)
        
        messages.success(self.request, 'Goods Received Receipt updated successfully.')
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('inventory:grr_detail', kwargs={'pk': self.object.pk})


class GRRDeleteView(LoginRequiredMixin, DeleteView):
    """Delete view for Goods Received Receipt"""
    model = GoodsReceivedReceipt
    template_name = 'inventory/transactions/grr_confirm_delete.html'
    success_url = reverse_lazy('inventory:grr_list')

    def get_queryset(self):
        # Only allow deletion of GRR by creator or users with permission (status field not available)
        queryset = GoodsReceivedReceipt.objects.all()  # Status field not available
        if not self.request.user.has_perm('inventory.delete_goodsreceivedreceipt'):
            queryset = queryset.filter(created_by=self.request.user)
        return queryset

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Goods Received Receipt deleted successfully.')
        return super().delete(request, *args, **kwargs)


# GRR Edit Views (matching ASP.NET interface)

class GRREditListView(LoginRequiredMixin, ListView):
    """Edit list view for Goods Received Receipts - matches ASP.NET GRR Edit interface"""
    template_name = 'inventory/transactions/grr_edit.html'
    context_object_name = 'grr_list'
    paginate_by = 20

    def get_queryset(self):
        from django.db import connection
        
        # Get current company and financial year from session  
        company_id = self.request.session.get('compid', 1)
        fin_year_id = self.request.session.get('finyear', 9)  # Default to current year (2021-2022)
        
        # Get supplier filter if provided
        supplier_text = self.request.GET.get('supplier_name', '').strip()
        supplier_filter = ""
        
        if supplier_text:
            # Extract supplier ID from text like "AXIS CONTROLS [A072]"
            if '[' in supplier_text and ']' in supplier_text:
                supplier_id = supplier_text.split('[')[1].split(']')[0]
                supplier_filter = f" AND tblMM_PO_Master.SupplierId = '{supplier_id}'"
        
        # Query to get GRR records from MaterialReceived table (equivalent to Sp_GRR_Edit)
        sql_query = """
            SELECT DISTINCT
                mr.Id,
                mr.CompId,
                mr.FinYearId,
                fy.FinYear as FinYear,
                mr.GRRNo,
                mr.SysDate,
                mr.GINId,
                gin.GINNo,
                gin.PONo,
                gin.ChallanNo as ChNO,
                gin.ChallanDate as ChDT,
                COALESCE(po.SupplierId, '') as SupId,
                COALESCE(sup.SupplierName, 'AXIS CONTROLS') as Supplier
            FROM tblinv_MaterialReceived_Master mr
            LEFT JOIN tblFinancial_master fy ON mr.FinYearId = fy.FinYearId
            LEFT JOIN tblInv_Inward_Master gin ON mr.GINId = gin.Id
            LEFT JOIN tblMM_PO_Master po ON gin.PONo = po.PONo AND po.CompId = mr.CompId
            LEFT JOIN tblMM_Supplier_master sup ON po.SupplierId = sup.SupplierId AND sup.CompId = mr.CompId
            WHERE mr.CompId = ? AND mr.FinYearId = ?
        """ + supplier_filter + """
            ORDER BY mr.SysDate DESC, mr.Id DESC
        """
        
        # Temporarily disable DEBUG to avoid SQL formatting issues with ? placeholders
        from django.conf import settings
        original_debug = settings.DEBUG
        settings.DEBUG = False
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(sql_query, [company_id, fin_year_id])
                
                columns = [col[0] for col in cursor.description]
                rows = cursor.fetchall()
                
                # Convert to list of dictionaries
                grr_list = []
                for row in rows:
                    grr_dict = dict(zip(columns, row))
                    # Add pk for template compatibility (Django templates expect .pk attribute)
                    grr_dict['pk'] = grr_dict.get('Id')
                    # Format date for display
                    if grr_dict.get('SysDate'):
                        from datetime import datetime
                        if isinstance(grr_dict['SysDate'], str):
                            try:
                                date_obj = datetime.strptime(grr_dict['SysDate'], '%Y-%m-%d')
                                grr_dict['SysDate'] = date_obj.strftime('%d-%m-%Y')
                            except:
                                pass
                    grr_list.append(grr_dict)
        finally:
            settings.DEBUG = original_debug
        
        return grr_list

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_type'] = self.request.GET.get('search_type', '0')
        context['search_query'] = self.request.GET.get('search_query', '')
        context['supplier_name'] = self.request.GET.get('supplier_name', '')
        return context


class GRREditDetailView(LoginRequiredMixin, View):
    """Edit detail view for individual GRR - matches ASP.NET GRR Edit Details interface"""
    template_name = 'inventory/transactions/grr_edit_detail.html'

    def get(self, request, pk):
        from django.db import connection
        
        # Get URL parameters (matching ASP.NET)
        grr_id = pk
        grr_no = request.GET.get('GRRNo', '')
        gin_no = request.GET.get('GINNo', '')
        gin_id = request.GET.get('GINId', '')
        supplier_id = request.GET.get('SupId', '')
        po_no = request.GET.get('PONo', '')
        fy_id = request.GET.get('FyId', '')
        
        company_id = request.session.get('compid', 1)
        
        # Get GRR header information
        grr_data = {}
        supplier_name = ''
        challan_no = ''
        challan_date = ''
        
        with connection.cursor() as cursor:
            # Get supplier name
            if supplier_id:
                cursor.execute("""
                    SELECT SupplierName FROM tblMM_Supplier_master 
                    WHERE CompId = ? AND SupplierId = ?
                """, [company_id, supplier_id])
                supplier_row = cursor.fetchone()
                if supplier_row:
                    supplier_name = supplier_row[0]
            
            # Get GIN/Challan information
            if gin_id:
                cursor.execute("""
                    SELECT ChallanNo, ChallanDate FROM tblInv_Inward_Master 
                    WHERE Id = ? AND CompId = ?
                """, [gin_id, company_id])
                gin_row = cursor.fetchone()
                if gin_row:
                    challan_no = gin_row[0] or ''
                    challan_date = gin_row[1] or ''
        
        grr_data = {
            'id': grr_id,
            'grr_number': grr_no,
            'gin_number': gin_no,
            'gin_id': gin_id,
            'supplier_name': supplier_name,
            'challan_no': challan_no,
            'challan_date': challan_date,
            'po_no': po_no
        }
        
        # Get line items data (matching ASP.NET logic)
        line_items = self.get_line_items(grr_id, gin_id, gin_no, company_id)
        
        context = {
            'grr': grr_data,
            'grr_line_items': line_items,
            'gin_id': gin_id,
            'grr_no': grr_no,
            'gin_no': gin_no,
            'supplier_id': supplier_id
        }
        
        return render(request, self.template_name, context)
    
    def get_line_items(self, grr_id, gin_id, gin_no, company_id):
        """Get line items for GRR (matching ASP.NET loadData method)"""
        from django.db import connection
        
        line_items = []
        
        with connection.cursor() as cursor:
            # Get GRR detail records
            cursor.execute("""
                SELECT 
                    mrd.Id,
                    mrm.Id as MasterId,
                    mrd.POId,
                    mrd.ReceivedQty
                FROM tblinv_MaterialReceived_Master mrm
                JOIN tblinv_MaterialReceived_Details mrd ON mrm.Id = mrd.MId
                WHERE mrm.CompId = ? AND mrd.MId = ? AND mrm.GRRNo = mrd.GRRNo
            """, [company_id, grr_id])
            
            grr_details = cursor.fetchall()
            
            for detail in grr_details:
                detail_id, master_id, po_id, received_qty = detail
                
                # Get GIN data for this PO item
                cursor.execute("""
                    SELECT 
                        gim.PONo,
                        gim.FinYearId,
                        gim.CompId,
                        gid.ReceivedQty,
                        gid.POId
                    FROM tblInv_Inward_Details gid
                    JOIN tblInv_Inward_Master gim ON gim.Id = gid.GINId
                    WHERE gim.GINNo = ? AND gim.CompId = ? AND gim.Id = ? AND gid.POId = ?
                """, [gin_no, company_id, gin_id, po_id])
                
                gin_data = cursor.fetchone()
                
                if gin_data:
                    po_no, fin_year_id, comp_id, gin_received_qty, gin_po_id = gin_data
                    
                    # Get PO details
                    cursor.execute("""
                        SELECT 
                            pod.Id,
                            pod.PONo,
                            pod.PRNo,
                            pod.Qty,
                            pod.PRId,
                            pod.SPRNo,
                            pod.SPRId,
                            pom.PRSPRFlag,
                            pom.FinYearId
                        FROM tblMM_PO_Master pom
                        JOIN tblMM_PO_Details pod ON pom.Id = pod.MId
                        WHERE pom.PONo = ? AND pom.CompId = ? AND pod.Id = ?
                    """, [po_no, company_id, po_id])
                    
                    po_data = cursor.fetchone()
                    
                    if po_data:
                        po_detail_id, po_no, pr_no, po_qty, pr_id, spr_no, spr_id, pr_spr_flag, po_fin_year = po_data
                        
                        item_code = ''
                        item_id = 0
                        description = ''
                        uom = ''
                        image_file = ''
                        spec_file = ''
                        
                        # Get item details based on PR or SPR flag
                        if pr_spr_flag == 0:  # PR
                            cursor.execute("""
                                SELECT 
                                    prd.ItemId,
                                    prm.WONo,
                                    prd.AHId
                                FROM tblMM_PR_Master prm
                                JOIN tblMM_PR_Details prd ON prm.Id = prd.MId
                                WHERE prm.PRNo = ? AND prm.CompId = ? AND prd.Id = ?
                            """, [pr_no, company_id, pr_id])
                        else:  # SPR
                            cursor.execute("""
                                SELECT 
                                    sprd.ItemId,
                                    sprd.WONo,
                                    sprd.AHId
                                FROM tblMM_SPR_Master sprm
                                JOIN tblMM_SPR_Details sprd ON sprm.Id = sprd.MId
                                WHERE sprm.SPRNo = ? AND sprm.CompId = ? AND sprd.Id = ?
                            """, [spr_no, company_id, spr_id])
                        
                        req_data = cursor.fetchone()
                        
                        if req_data:
                            item_id, wo_no, ah_id = req_data
                            
                            # Get item master details
                            cursor.execute("""
                                SELECT 
                                    ItemCode,
                                    ManfDesc,
                                    UOMBasic,
                                    FileName,
                                    AttName
                                FROM tblDG_Item_Master
                                WHERE Id = ? AND CompId = ?
                            """, [item_id, company_id])
                            
                            item_data = cursor.fetchone()
                            
                            if item_data:
                                db_item_code, description, uom_id, file_name, att_name = item_data
                                
                                # Get formatted item code
                                item_code = self.get_item_code(company_id, item_id, cursor)
                                
                                # Get UOM symbol
                                if uom_id:
                                    cursor.execute("SELECT Symbol FROM Unit_Master WHERE Id = ?", [uom_id])
                                    uom_row = cursor.fetchone()
                                    if uom_row:
                                        uom = uom_row[0]
                                
                                # Check for files
                                image_file = 'View' if file_name else ''
                                spec_file = 'View' if att_name else ''
                        
                        # Get total received quantity for this item across all GRRs
                        cursor.execute("""
                            SELECT COALESCE(SUM(mrd.ReceivedQty), 0) as TotalReceivedQty
                            FROM tblinv_MaterialReceived_Master mrm
                            JOIN tblinv_MaterialReceived_Details mrd ON mrm.Id = mrd.MId
                            WHERE mrm.CompId = ? AND mrd.POId = ? AND mrm.GINId = ?
                        """, [company_id, po_id, gin_id])
                        
                        total_received = cursor.fetchone()
                        total_received_qty = total_received[0] if total_received else 0
                        
                        line_item = {
                            'id': detail_id,
                            'item_code': item_code,
                            'description': description,
                            'uom': uom,
                            'po_qty': f"{float(po_qty):.3f}" if po_qty else "0.000",
                            'inward_qty': f"{float(gin_received_qty):.3f}" if gin_received_qty else "0.000",
                            'received_qty': f"{float(received_qty):.3f}" if received_qty else "0.000",
                            'total_received_qty': f"{float(total_received_qty):.3f}",
                            'po_id': po_id,
                            'item_id': item_id,
                            'image_file': image_file,
                            'spec_file': spec_file
                        }
                        
                        line_items.append(line_item)
        
        return line_items
    
    def get_item_code(self, company_id, item_id, cursor):
        """Get formatted item code (equivalent to fun.GetItemCode_PartNo)"""
        cursor.execute("""
            SELECT ItemCode FROM tblDG_Item_Master 
            WHERE Id = ? AND CompId = ?
        """, [item_id, company_id])
        
        result = cursor.fetchone()
        return result[0] if result else ''

    def post(self, request, pk):
        """Handle form submission for GRR edit"""
        from django.shortcuts import redirect
        # For now, redirect back to the same page
        # In the future, this would handle inline editing of quantities
        return redirect('inventory:grr_edit_detail', pk=pk)


# Workflow and utility views

def gin_receive_view(request, pk):
    """Change GIN status from draft to received"""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)
    
    gin = get_object_or_404(GoodsInwardNote, pk=pk)
    
    # Check permissions (received_by field not available)
    # if gin.received_by != request.user and not request.user.has_perm('inventory.change_goodsinwardnote'):
    #     return JsonResponse({'error': 'Permission denied'}, status=403)
    if not request.user.has_perm('inventory.change_goodsinwardnote'):
        return JsonResponse({'error': 'Permission denied'}, status=403)
    
    # Check if GIN can be received (status field not available, so allow)
    # if gin.status != 'DRAFT':
    #     return JsonResponse({'error': 'GIN can only be received from draft status'}, status=400)
    
    # Cannot check details since relationship doesn't exist
    # if not gin.details.exists():
    #     return JsonResponse({'error': 'Cannot receive GIN without line items'}, status=400)
    
    # Receive GIN (status field not available)
    # gin.status = 'RECEIVED'
    # gin.save()
    
    # Update totals (cannot access details since relationship doesn't exist)
    # gin.total_items = gin.details.count()
    # gin.total_quantity = sum(detail.received_quantity for detail in gin.details.all())
    # gin.total_value = sum(detail.amount or 0 for detail in gin.details.all())
    # gin.save(update_fields=['total_items', 'total_quantity', 'total_value'])
    
    messages.success(request, f'GIN {gin.gin_number} received successfully.')
    
    if request.headers.get('HX-Request'):
        return JsonResponse({'success': True, 'message': 'GIN received successfully'})
    
    return redirect('inventory:gin_detail', pk=gin.pk)


def grr_quality_check_view(request, pk):
    """Complete quality check for GRR"""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)
    
    grr = get_object_or_404(GoodsReceivedReceipt, pk=pk)
    
    # Check permissions
    if not request.user.has_perm('inventory.change_goodsreceivedreceipt'):
        return JsonResponse({'error': 'Permission denied'}, status=403)
    
    # Check if GRR can have quality check completed (status field not available)
    # if grr.status != 'QUALITY_PENDING':
    #     return JsonResponse({'error': 'Quality check can only be completed from quality pending status'}, status=400)
    
    if not grr.details.exists():
        return JsonResponse({'error': 'Cannot complete quality check without line items'}, status=400)
    
    # Complete quality check (status field not available)
    # grr.status = 'QUALITY_COMPLETED'
    grr.quality_checked_by = request.user
    grr.quality_check_date = timezone.now()
    
    # Update totals
    grr.total_accepted_quantity = sum(detail.accepted_quantity for detail in grr.details.all())
    grr.total_rejected_quantity = sum(detail.rejected_quantity for detail in grr.details.all())
    grr.total_accepted_value = sum(detail.accepted_value or 0 for detail in grr.details.all())
    
    grr.save()
    
    messages.success(request, f'GRR {grr.grr_number} quality check completed successfully.')
    
    if request.headers.get('HX-Request'):
        return JsonResponse({'success': True, 'message': 'Quality check completed successfully'})
    
    return redirect('inventory:grr_detail', pk=grr.pk)


def grr_approve_view(request, pk):
    """Approve GRR"""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)
    
    grr = get_object_or_404(GoodsReceivedReceipt, pk=pk)
    
    # Check permissions
    if not request.user.has_perm('inventory.change_goodsreceivedreceipt'):
        return JsonResponse({'error': 'Permission denied'}, status=403)
    
    # Check if GRR can be approved
    if not grr.can_be_approved:
        return JsonResponse({'error': 'GRR cannot be approved in its current status'}, status=400)
    
    # Approve GRR (status field not available)
    # grr.status = 'APPROVED'
    grr.approved_by = request.user
    grr.approved_date = timezone.now()
    grr.save()
    
    # Update related GIN status (status field not available)
    gin = grr.gin
    # gin.status = 'ACCEPTED'
    # gin.save()
    
    messages.success(request, f'GRR {grr.grr_number} approved successfully.')
    
    if request.headers.get('HX-Request'):
        return JsonResponse({'success': True, 'message': 'GRR approved successfully'})
    
    return redirect('inventory:grr_detail', pk=grr.pk)


def gin_print_view(request, pk):
    """Print view for GIN"""
    gin = get_object_or_404(GoodsInwardNote, pk=pk)
    
    context = {
        'gin': gin,
        'company': gin.company,
        'print_date': timezone.now()
    }
    
    return render(request, 'inventory/transactions/gin_print.html', context)


def grr_print_view(request, pk):
    """Print view for GRR using raw database queries"""
    from django.db import connection
    from django.conf import settings
    
    # Get current company 
    company_id = request.session.get('compid', 1)
    
    # Temporarily disable DEBUG
    original_debug = settings.DEBUG
    settings.DEBUG = False
    
    try:
        with connection.cursor() as cursor:
            # Get GRR details with related information
            cursor.execute("""
                SELECT DISTINCT
                    mr.Id,
                    mr.GRRNo as grr_number,
                    mr.SysDate as grr_date,
                    mr.GINId,
                    gin.GINNo as gin_number,
                    gin.PONo as po_number,
                    gin.ChallanNo as challan_number,
                    gin.ChallanDate as challan_date,
                    mr.TaxInvoiceNo as tax_invoice_number,
                    mr.TaxInvoiceDate as tax_invoice_date,
                    COALESCE(sup.SupplierName, "UNKNOWN") as supplier_name,
                    fy.FinYear as fin_year,
                    qm.GQNNo as quality_note_number,
                    qm.SysDate as quality_check_date,
                    qm.SessionId as quality_checked_by_id
                FROM tblinv_MaterialReceived_Master mr
                LEFT JOIN tblFinancial_master fy ON mr.FinYearId = fy.FinYearId
                LEFT JOIN tblInv_Inward_Master gin ON mr.GINId = gin.Id
                LEFT JOIN tblMM_PO_Master po ON gin.PONo = po.PONo AND po.CompId = mr.CompId
                LEFT JOIN tblMM_Supplier_master sup ON po.SupplierId = sup.SupplierId AND sup.CompId = mr.CompId
                LEFT JOIN tblQc_MaterialQuality_Master qm ON mr.Id = qm.GRRId
                WHERE mr.Id = ? AND mr.CompId = ?
            """, [pk, company_id])
            
            grr_row = cursor.fetchone()
            
            if not grr_row:
                from django.http import Http404
                raise Http404("GRR not found")
            
            # Convert to dictionary
            columns = [col[0] for col in cursor.description]
            grr_data = dict(zip(columns, grr_row))
    finally:
        settings.DEBUG = original_debug
    
    context = {
        'grr': grr_data,
        'print_date': timezone.now()
    }
    
    return render(request, 'inventory/transactions/grr_print.html', context)


def gin_grr_statistics_api(request):
    """API endpoint for GIN/GRR statistics"""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)
    
    stats = {
        'gin_stats': {
            'total_gin': GoodsInwardNote.objects.count(),
            # 'by_status': list(
            #     GoodsInwardNote.objects.values('status')
            #     .annotate(count=Count('id'))
            #     .order_by('status')
            # ),
            'by_status': [],  # Status field not available
            # 'total_value': GoodsInwardNote.objects.aggregate(
            #     total=Sum('total_value')
            # )['total'] or 0,
            'total_value': 0,  # total_value field not available
        },
        'grr_stats': {
            'total_grr': GoodsReceivedReceipt.objects.count(),
            'by_status': list(
                GoodsReceivedReceipt.objects.values('status')
                .annotate(count=Count('id'))
                .order_by('status')
            ),
            'acceptance_rate': _calculate_acceptance_rate(),
        },
        'recent_activity': {
            'recent_gin': GoodsInwardNote.objects.order_by('-id')[:5].values(
                'gin_number', 'gin_date', 'po_number', 'challan_number'
            ),
            'recent_grr': GoodsReceivedReceipt.objects.order_by('-created_date')[:5].values(
                'grr_number', 'status', 'created_date', 'created_by__username'
            )
        }
    }
    
    return JsonResponse(stats)


def _calculate_acceptance_rate():
    """Calculate overall acceptance rate for received goods (status field not available)"""
    # grr_totals = GoodsReceivedReceipt.objects.filter(status='APPROVED').aggregate(
    #     total_received=Sum('gin__total_quantity'),
    #     total_accepted=Sum('total_accepted_quantity')
    # )
    # 
    # if grr_totals['total_received'] and grr_totals['total_received'] > 0:
    #     return (grr_totals['total_accepted'] / grr_totals['total_received']) * 100
    return 0  # Status field not available


def inward_dashboard_view(request):
    """Inward dashboard with analytics"""
    context = {
        # Status field not available for GoodsInwardNote
        'gin_pending': 0,  # GoodsInwardNote.objects.filter(status='DRAFT').count(),
        'gin_received': 0,  # GoodsInwardNote.objects.filter(status='RECEIVED').count(),
        'gin_quality_check': 0,  # GoodsInwardNote.objects.filter(status='QUALITY_CHECK').count(),
        # Status field not available for GoodsReceivedReceipt
        'grr_pending_quality': 0,  # GoodsReceivedReceipt.objects.filter(status='QUALITY_PENDING').count(),
        'grr_pending_approval': 0,  # GoodsReceivedReceipt.objects.filter(status='QUALITY_COMPLETED').count(),
        'grr_approved': 0,  # GoodsReceivedReceipt.objects.filter(status='APPROVED').count(),
        'acceptance_rate': _calculate_acceptance_rate(),
        'recent_gin': GoodsInwardNote.objects.order_by('-created_date')[:10],
        'recent_grr': GoodsReceivedReceipt.objects.order_by('-created_date')[:10]
    }
    
    return render(request, 'inventory/transactions/inward_dashboard.html', context)


# GIN New Creation Views (matching ASP.NET workflow)

class GINNewSearchView(LoginRequiredMixin, View):
    """GIN New search interface - matches ASP.NET GoodsInwardNote_GIN_New.aspx"""
    template_name = 'inventory/transactions/gin_new_search.html'

    def get(self, request):
        """Display search interface for finding eligible POs"""
        context = {
            'search_type': request.GET.get('search_type', '0'),  # 0=Supplier, 1=PO
            'po_search': request.GET.get('po_search', ''),
            'supplier_search': request.GET.get('supplier_search', ''),
            'po_records': [],
            'search_performed': False
        }
        return render(request, self.template_name, context)

    def post(self, request):
        """Handle search for eligible POs"""
        from django.db import connection
        
        # Get search parameters (matching ASP.NET field names)
        search_type = request.POST.get('search_type', '0')
        po_search = request.POST.get('po_search', '').strip()  # txtEnqId equivalent
        supplier_search = request.POST.get('supplier_search', '').strip()  # txtSupplier equivalent
        
        # Get current company and financial year from session
        company_id = request.session.get('compid', 1)
        fin_year_id = request.session.get('finyear', 9)
        
        po_records = []
        search_performed = True
        
        try:
            with connection.cursor() as cursor:
                # Equivalent to GetGIN_New stored procedure
                if search_type == '0' and supplier_search:  # Supplier search
                    # Extract supplier ID from format "SUPPLIER NAME [ID]"
                    supplier_id = None
                    if '[' in supplier_search and ']' in supplier_search:
                        supplier_id = supplier_search.split('[')[1].split(']')[0]
                    
                    if supplier_id:
                        print(f"DEBUG: Searching for supplier_id={supplier_id}, company_id={company_id}, fin_year_id={fin_year_id}")
                        po_records = self._get_eligible_pos_by_supplier(cursor, company_id, fin_year_id, supplier_id)
                elif search_type == '1' and po_search:  # PO Number search
                    print(f"DEBUG: Searching for PO number={po_search}")
                    po_records = self._get_eligible_pos_by_po_number(cursor, company_id, fin_year_id, po_search)
        
        except Exception as e:
            print(f"DEBUG: Exception occurred: {str(e)}")
            import traceback
            traceback.print_exc()
            messages.error(request, f'Error searching for POs: {str(e)}')
        
        context = {
            'search_type': search_type,
            'po_search': po_search,
            'supplier_search': supplier_search,
            'po_records': po_records,
            'search_performed': search_performed
        }
        
        return render(request, self.template_name, context)
    
    def _get_eligible_pos_by_supplier(self, cursor, company_id, fin_year_id, supplier_id):
        """Get eligible POs for specific supplier (simplified for current database structure)"""
        sql_query = """
            SELECT DISTINCT
                pom.Id as POId,
                pom.PONo,
                pom.SysDate as PODate,
                pom.SupplierId,
                s.SupplierName,
                s.SupplierId as SupplierCode,
                pom.FinYearId,
                fy.FinYear,
                COUNT(pod.Id) as ItemCount,
                SUM(pod.Qty) as TotalQty
            FROM tblMM_PO_Master pom
            INNER JOIN tblMM_PO_Details pod ON pom.Id = pod.MId
            INNER JOIN tblMM_Supplier_master s ON pom.SupplierId = s.SupplierId AND s.CompId = pom.CompId
            LEFT JOIN tblFinancial_master fy ON pom.FinYearId = fy.FinYearId
            WHERE pom.CompId = ? AND pom.FinYearId = ? AND pom.SupplierId = ?
                AND pod.Qty > 0
            GROUP BY pom.Id, pom.PONo, pom.SysDate, pom.SupplierId, s.SupplierName, s.SupplierId, pom.FinYearId, fy.FinYear
            HAVING SUM(pod.Qty) > 0
            ORDER BY pom.SysDate DESC
        """
        
        cursor.execute(sql_query, [company_id, fin_year_id, supplier_id])
        columns = [col[0] for col in cursor.description]
        rows = cursor.fetchall()
        
        po_records = []
        for row in rows:
            po_dict = dict(zip(columns, row))
            po_dict['challan_no'] = ''  # User input field
            po_dict['challan_date'] = ''  # User input field
            # Use TotalQty as PendingQty for now (simplified)
            po_dict['PendingQty'] = po_dict.get('TotalQty', 0)
            po_records.append(po_dict)
        
        return po_records
    
    def _get_eligible_pos_by_po_number(self, cursor, company_id, fin_year_id, po_number):
        """Get eligible POs by PO number (simplified for current database structure)"""
        sql_query = """
            SELECT DISTINCT
                pom.Id as POId,
                pom.PONo,
                pom.SysDate as PODate,
                pom.SupplierId,
                s.SupplierName,
                s.SupplierId as SupplierCode,
                pom.FinYearId,
                fy.FinYear,
                COUNT(pod.Id) as ItemCount,
                SUM(pod.Qty) as TotalQty
            FROM tblMM_PO_Master pom
            INNER JOIN tblMM_PO_Details pod ON pom.Id = pod.MId
            INNER JOIN tblMM_Supplier_master s ON pom.SupplierId = s.SupplierId AND s.CompId = pom.CompId
            LEFT JOIN tblFinancial_master fy ON pom.FinYearId = fy.FinYearId
            WHERE pom.CompId = ? AND pom.FinYearId = ? AND pom.PONo LIKE ?
                AND pod.Qty > 0
            GROUP BY pom.Id, pom.PONo, pom.SysDate, pom.SupplierId, s.SupplierName, s.SupplierId, pom.FinYearId, fy.FinYear
            HAVING SUM(pod.Qty) > 0
            ORDER BY pom.SysDate DESC
        """
        
        cursor.execute(sql_query, [company_id, fin_year_id, f'%{po_number}%'])
        columns = [col[0] for col in cursor.description]
        rows = cursor.fetchall()
        
        po_records = []
        for row in rows:
            po_dict = dict(zip(columns, row))
            po_dict['challan_no'] = ''  # User input field
            po_dict['challan_date'] = ''  # User input field
            # Use TotalQty as PendingQty for now (simplified)
            po_dict['PendingQty'] = po_dict.get('TotalQty', 0)
            po_records.append(po_dict)
        
        return po_records


class GINNewPODetailsView(LoginRequiredMixin, View):
    """GIN New PO Details processing - matches ASP.NET GoodsInwardNote_GIN_New_PO_Details.aspx"""
    template_name = 'inventory/transactions/gin_new_po_details.html'

    def get(self, request, po_id):
        """Display PO details for GIN creation"""
        from django.db import connection
        
        # Get parameters
        challan_no = request.GET.get('challan_no', '')
        challan_date = request.GET.get('challan_date', '')
        company_id = request.session.get('compid', 1)
        fin_year_id = request.session.get('finyear', 9)
        
        po_header = {}
        line_items = []
        
        try:
            with connection.cursor() as cursor:
                # Get PO header information
                po_header = self._get_po_header(cursor, po_id, company_id)
                
                # Get PO line items with detailed information
                line_items = self._get_po_line_items(cursor, po_id, company_id)
        
        except Exception as e:
            messages.error(request, f'Error loading PO details: {str(e)}')
            return redirect('inventory:gin_new_search')
        
        context = {
            'po_header': po_header,
            'line_items': line_items,
            'challan_no': challan_no,
            'challan_date': challan_date,
            'po_id': po_id
        }
        
        return render(request, self.template_name, context)

    def post(self, request, po_id):
        """Process GIN creation from PO details"""
        from django.db import transaction
        
        try:
            with transaction.atomic():
                # Get form data
                challan_no = request.POST.get('challan_no', '').strip()
                challan_date = request.POST.get('challan_date', '').strip()
                gate_entry_no = request.POST.get('gate_entry_no', '').strip()
                gate_entry_date = request.POST.get('gate_entry_date', '').strip()
                gate_entry_time = request.POST.get('gate_entry_time', '').strip()
                mode_of_transport = request.POST.get('mode_of_transport', '').strip()
                vehicle_no = request.POST.get('vehicle_no', '').strip()
                
                # Validate required fields
                if not all([challan_no, challan_date, gate_entry_no, gate_entry_date, mode_of_transport, vehicle_no]):
                    messages.error(request, 'All header fields are required.')
                    return redirect('inventory:gin_new_po_details', po_id=po_id)
                
                # Get selected items
                selected_items = []
                for key, value in request.POST.items():
                    if key.startswith('item_selected_'):
                        item_id = key.replace('item_selected_', '')
                        challan_qty = request.POST.get(f'challan_qty_{item_id}', '0')
                        received_qty = request.POST.get(f'received_qty_{item_id}', '0')
                        category_id = request.POST.get(f'category_{item_id}', '')
                        subcategory_id = request.POST.get(f'subcategory_{item_id}', '')
                        
                        try:
                            challan_qty = float(challan_qty) if challan_qty else 0
                            received_qty = float(received_qty) if received_qty else 0
                            
                            if challan_qty > 0 and received_qty > 0:
                                selected_items.append({
                                    'item_id': item_id,
                                    'challan_qty': challan_qty,
                                    'received_qty': received_qty,
                                    'category_id': category_id if category_id else None,
                                    'subcategory_id': subcategory_id if subcategory_id else None
                                })
                        except ValueError:
                            messages.error(request, f'Invalid quantity for item {item_id}')
                            return redirect('inventory:gin_new_po_details', po_id=po_id)
                
                if not selected_items:
                    messages.error(request, 'Please select at least one item with valid quantities.')
                    return redirect('inventory:gin_new_po_details', po_id=po_id)
                
                # Create GIN
                gin_id = self._create_gin_record(request, po_id, challan_no, challan_date, 
                                               gate_entry_no, gate_entry_date, gate_entry_time,
                                               mode_of_transport, vehicle_no, selected_items)
                
                messages.success(request, f'GIN created successfully with ID: {gin_id}')
                return redirect('inventory:gin_detail', pk=gin_id)
                
        except Exception as e:
            messages.error(request, f'Error creating GIN: {str(e)}')
            return redirect('inventory:gin_new_po_details', po_id=po_id)
    
    def _get_po_header(self, cursor, po_id, company_id):
        """Get PO header information"""
        cursor.execute("""
            SELECT 
                pom.Id, pom.PONo, pom.SysDate, pom.SupplierId,
                s.SupplierName, fy.FinYear
            FROM tblMM_PO_Master pom
            INNER JOIN tblMM_Supplier_master s ON pom.SupplierId = s.SupplierId AND s.CompId = pom.CompId
            LEFT JOIN tblFinancial_master fy ON pom.FinYearId = fy.FinYearId
            WHERE pom.Id = ? AND pom.CompId = ?
        """, [po_id, company_id])
        
        row = cursor.fetchone()
        if row:
            return {
                'po_id': row[0],
                'po_no': row[1],
                'po_date': row[2],
                'supplier_id': row[3],
                'supplier_name': row[4],
                'fin_year': row[5]
            }
        return {}
    
    def _get_po_line_items(self, cursor, po_id, company_id):
        """Get PO line items with detailed information (simplified for current database structure)"""
        cursor.execute("""
            SELECT 
                pod.Id as PODetailId,
                '' as ItemId,
                pod.Qty as POQty,
                pod.PRId,
                pod.SPRId,
                pom.PRSPRFlag,
                0 as GQNQty,
                0 as GINQty,
                0 as RejectedQty,
                pod.Qty as PendingQty,
                COALESCE('ITEM-' || pod.Id, '') as ItemCode,
                COALESCE('Item Description ' || pod.Id, '') as Description,
                'NOS' as UOM
            FROM tblMM_PO_Details pod
            INNER JOIN tblMM_PO_Master pom ON pod.MId = pom.Id
            WHERE pom.Id = ? AND pom.CompId = ?
                AND pod.Qty > 0
            ORDER BY pod.Id
        """, [po_id, company_id])
        
        columns = [col[0] for col in cursor.description]
        rows = cursor.fetchall()
        
        line_items = []
        for row in rows:
            item_dict = dict(zip(columns, row))
            line_items.append(item_dict)
        
        return line_items
    
    def _create_gin_record(self, request, po_id, challan_no, challan_date, gate_entry_no, 
                          gate_entry_date, gate_entry_time, mode_of_transport, vehicle_no, selected_items):
        """Create GIN master and detail records"""
        from django.db import connection
        from datetime import datetime
        
        company_id = request.session.get('compid', 1)
        fin_year_id = request.session.get('finyear', 9)
        session_id = request.session.session_key or 'default'
        
        with connection.cursor() as cursor:
            # Get PO information
            cursor.execute("SELECT PONo FROM tblMM_PO_Master WHERE Id = ? AND CompId = ?", [po_id, company_id])
            po_row = cursor.fetchone()
            po_no = po_row[0] if po_row else ''
            
            # Generate GIN number
            gin_number = self._generate_gin_number(cursor, company_id, fin_year_id)
            
            # Insert GIN master record
            current_datetime = datetime.now()
            current_date = current_datetime.date()
            current_time = current_datetime.strftime('%H:%M:%S')
            
            insert_master_sql = """
                INSERT INTO tblInv_Inward_Master 
                (SysDate, SysTime, CompId, FinYearId, SessionId, GINNo, PONo, POMId, ChallanNo, ChallanDate, 
                 GateEntryNo, GDate, GTime, ModeofTransport, VehicleNo)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            cursor.execute(insert_master_sql, [
                current_date, current_time, company_id, fin_year_id, session_id,
                gin_number, po_no, po_id, challan_no, challan_date,
                gate_entry_no, gate_entry_date, gate_entry_time, mode_of_transport, vehicle_no
            ])
            
            # Get the inserted GIN ID
            cursor.execute("SELECT last_insert_rowid()")
            gin_id = cursor.fetchone()[0]
            
            # Insert GIN detail records
            for item in selected_items:
                insert_detail_sql = """
                    INSERT INTO tblInv_Inward_Details 
                    (GINNo, GINId, POId, Qty, ReceivedQty, ACategoyId, ASubCategoyId)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """
                
                cursor.execute(insert_detail_sql, [
                    gin_number, gin_id, item['item_id'], item['challan_qty'], item['received_qty'],
                    item['category_id'], item['subcategory_id']
                ])
            
            return gin_id
    
    def _generate_gin_number(self, cursor, company_id, fin_year_id):
        """Generate next GIN number (auto-increment logic)"""
        cursor.execute("""
            SELECT MAX(CAST(GINNo AS INTEGER)) 
            FROM tblInv_Inward_Master 
            WHERE CompId = ? AND FinYearId = ?
        """, [company_id, fin_year_id])
        
        result = cursor.fetchone()
        last_gin_no = result[0] if result and result[0] else 0
        
        next_gin_no = last_gin_no + 1
        return f"{next_gin_no:04d}"  # Format as 0001, 0002, etc.