<!-- accounts/templates/accounts/vat_return_form.html -->
<!-- VAT Return Form Template -->
<!-- Task Group 4: Taxation Management - VAT Return Form Preparation -->

{% extends 'core/base.html' %}
{% load static %}

{% block title %}VAT Return Form - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-orange-500 to-sap-orange-600 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">VAT Return Form</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Prepare and generate VAT returns</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:vat_register_dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Dashboard
                </a>
                <button @click="exportReturn('pdf')" 
                        x-show="vatReturnData"
                        class="bg-sap-red-600 hover:bg-sap-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="file-text" class="w-4 h-4 inline mr-2"></i>
                    Export PDF
                </button>
                <button @click="printReturn()" 
                        x-show="vatReturnData"
                        class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="printer" class="w-4 h-4 inline mr-2"></i>
                    Print
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6 space-y-6" x-data="vatReturnForm()">
    
    <!-- Return Period Selection -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <h3 class="text-lg font-medium text-sap-gray-800">Return Period Configuration</h3>
            <p class="text-sm text-sap-gray-600 mt-1">Select the period for VAT return preparation</p>
        </div>
        <div class="p-6">
            <form hx-get="{% url 'accounts:vat_return_form' %}" 
                  hx-target="#vat-return-content" 
                  hx-trigger="change delay:300ms, submit"
                  hx-indicator="#loading-indicator">
                {% csrf_token %}
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <!-- Return Period Type -->
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-3">Return Period</label>
                        <div class="space-y-2">
                            <div class="flex items-center">
                                <input type="radio" name="return_period" id="monthly" value="monthly" 
                                       {% if not return_period or return_period == 'monthly' %}checked{% endif %}
                                       class="h-4 w-4 text-sap-orange-600 focus:ring-sap-orange-500 border-sap-gray-300">
                                <label for="monthly" class="ml-2 text-sm text-sap-gray-700">Monthly</label>
                            </div>
                            <div class="flex items-center">
                                <input type="radio" name="return_period" id="quarterly" value="quarterly"
                                       {% if return_period == 'quarterly' %}checked{% endif %}
                                       class="h-4 w-4 text-sap-orange-600 focus:ring-sap-orange-500 border-sap-gray-300">
                                <label for="quarterly" class="ml-2 text-sm text-sap-gray-700">Quarterly</label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Year Selection -->
                    <div>
                        <label for="period_year" class="block text-sm font-medium text-sap-gray-700 mb-2">Year</label>
                        <select name="period_year" id="period_year" 
                                class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
                            {% for year in "2020 2021 2022 2023 2024 2025"|make_list %}
                            <option value="{{ year }}" {% if year == period_year|stringformat:"s" %}selected{% endif %}>
                                {{ year }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <!-- Month Selection (for monthly returns) -->
                    <div x-show="document.querySelector('input[name=return_period]:checked').value === 'monthly'">
                        <label for="period_month" class="block text-sm font-medium text-sap-gray-700 mb-2">Month</label>
                        <select name="period_month" id="period_month" 
                                class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
                            <option value="1" {% if period_month == "1" %}selected{% endif %}>January</option>
                            <option value="2" {% if period_month == "2" %}selected{% endif %}>February</option>
                            <option value="3" {% if period_month == "3" %}selected{% endif %}>March</option>
                            <option value="4" {% if period_month == "4" %}selected{% endif %}>April</option>
                            <option value="5" {% if period_month == "5" %}selected{% endif %}>May</option>
                            <option value="6" {% if period_month == "6" %}selected{% endif %}>June</option>
                            <option value="7" {% if period_month == "7" %}selected{% endif %}>July</option>
                            <option value="8" {% if period_month == "8" %}selected{% endif %}>August</option>
                            <option value="9" {% if period_month == "9" %}selected{% endif %}>September</option>
                            <option value="10" {% if period_month == "10" %}selected{% endif %}>October</option>
                            <option value="11" {% if period_month == "11" %}selected{% endif %}>November</option>
                            <option value="12" {% if period_month == "12" %}selected{% endif %}>December</option>
                        </select>
                    </div>
                    
                    <!-- Quarter Selection (for quarterly returns) -->
                    <div x-show="document.querySelector('input[name=return_period]:checked').value === 'quarterly'">
                        <label for="period_quarter" class="block text-sm font-medium text-sap-gray-700 mb-2">Quarter</label>
                        <select name="period_quarter" id="period_quarter" 
                                class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
                            <option value="1" {% if period_quarter == "1" %}selected{% endif %}>Q1 (Jan-Mar)</option>
                            <option value="2" {% if period_quarter == "2" %}selected{% endif %}>Q2 (Apr-Jun)</option>
                            <option value="3" {% if period_quarter == "3" %}selected{% endif %}>Q3 (Jul-Sep)</option>
                            <option value="4" {% if period_quarter == "4" %}selected{% endif %}>Q4 (Oct-Dec)</option>
                        </select>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Company Selection -->
                    <div>
                        <label for="company_id" class="block text-sm font-medium text-sap-gray-700 mb-2">Company</label>
                        <select name="company_id" id="company_id" 
                                class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
                            {% for company in companies %}
                            <option value="{{ company.id }}" {% if company.id|stringformat:"s" == company_id %}selected{% endif %}>
                                {{ company.company_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <!-- Financial Year -->
                    <div>
                        <label for="financial_year_id" class="block text-sm font-medium text-sap-gray-700 mb-2">Financial Year</label>
                        <select name="financial_year_id" id="financial_year_id" 
                                class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
                            {% for fy in financial_years %}
                            <option value="{{ fy.id }}" {% if fy.id|stringformat:"s" == financial_year_id %}selected{% endif %}>
                                {{ fy.year_code }} ({{ fy.start_date|date:'d/m/Y' }} - {{ fy.end_date|date:'d/m/Y' }})
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                
                <div class="mt-6">
                    <button type="submit" 
                            class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="calculator" class="w-4 h-4 inline mr-2"></i>
                        Generate VAT Return
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div id="loading-indicator" class="htmx-indicator">
        <div class="flex items-center justify-center py-8">
            <div class="flex items-center text-sap-gray-600">
                <i data-lucide="loader" class="w-6 h-6 animate-spin mr-3"></i>
                Preparing VAT return data...
            </div>
        </div>
    </div>

    <!-- VAT Return Content -->
    <div id="vat-return-content">
        {% if vat_return_data %}
        <div x-data="{ vatReturnData: true }">
            <!-- Company Header -->
            <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm mb-6">
                <div class="px-6 py-4 border-b border-sap-gray-100">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-xl font-bold text-sap-gray-900">{{ company.company_name }}</h3>
                            <p class="text-sm text-sap-gray-600 mt-1">VAT Return for {{ vat_return_data.period.period_description }}</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm text-sap-gray-600">Return Period</p>
                            <p class="text-lg font-semibold text-sap-gray-900">
                                {{ vat_return_data.period.start_date|date:"d/m/Y" }} - {{ vat_return_data.period.end_date|date:"d/m/Y" }}
                            </p>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 bg-sap-gray-50">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        {% if company.registered_address_line1 %}
                        <div>
                            <p class="font-medium text-sap-gray-800">Registered Address:</p>
                            <p class="text-sap-gray-600">
                                {{ company.registered_address_line1 }}{% if company.registered_address_line2 %}, {{ company.registered_address_line2 }}{% endif %}
                            </p>
                        </div>
                        {% endif %}
                        {% if company.tin_no %}
                        <div>
                            <p class="font-medium text-sap-gray-800">TIN Number:</p>
                            <p class="text-sap-gray-600">{{ company.tin_no }}</p>
                        </div>
                        {% endif %}
                        {% if company.vat_no %}
                        <div>
                            <p class="font-medium text-sap-gray-800">VAT Number:</p>
                            <p class="text-sap-gray-600">{{ company.vat_no }}</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- VAT Return Summary -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                <!-- Sales Summary -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <h4 class="text-lg font-medium text-sap-gray-800">Sales (Output VAT)</h4>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-sap-gray-600">Total Taxable Sales:</span>
                            <span class="font-semibold text-sap-gray-900">₹{{ vat_return_data.sales.total_taxable_sales|floatformat:2 }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-sap-gray-600">Output VAT:</span>
                            <span class="font-semibold text-sap-blue-600">₹{{ vat_return_data.sales.total_output_vat|floatformat:2 }}</span>
                        </div>
                        <div class="flex justify-between items-center border-t border-sap-gray-200 pt-4">
                            <span class="text-sm font-medium text-sap-gray-800">Total Sales Value:</span>
                            <span class="font-bold text-sap-gray-900">₹{{ vat_return_data.sales.total_sales_value|floatformat:2 }}</span>
                        </div>
                    </div>
                </div>

                <!-- Purchase Summary -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <h4 class="text-lg font-medium text-sap-gray-800">Purchases (Input VAT)</h4>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-sap-gray-600">Total Taxable Purchases:</span>
                            <span class="font-semibold text-sap-gray-900">₹{{ vat_return_data.purchases.total_taxable_purchases|floatformat:2 }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-sap-gray-600">Input VAT Credit:</span>
                            <span class="font-semibold text-sap-green-600">₹{{ vat_return_data.purchases.total_input_vat|floatformat:2 }}</span>
                        </div>
                        <div class="flex justify-between items-center border-t border-sap-gray-200 pt-4">
                            <span class="text-sm font-medium text-sap-gray-800">Total Purchase Value:</span>
                            <span class="font-bold text-sap-gray-900">₹{{ vat_return_data.purchases.total_purchase_value|floatformat:2 }}</span>
                        </div>
                    </div>
                </div>

                <!-- Net VAT Liability -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <h4 class="text-lg font-medium text-sap-gray-800">Net VAT Position</h4>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-sap-gray-600">Output VAT:</span>
                            <span class="font-semibold text-sap-blue-600">₹{{ vat_return_data.sales.total_output_vat|floatformat:2 }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-sap-gray-600">Less: Input VAT Credit:</span>
                            <span class="font-semibold text-sap-green-600">-₹{{ vat_return_data.purchases.total_input_vat|floatformat:2 }}</span>
                        </div>
                        <div class="border-t border-sap-gray-200 pt-4">
                            {% if vat_return_data.net_vat_payable > 0 %}
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-sap-gray-800">VAT Payable:</span>
                                <span class="font-bold text-sap-red-600 text-lg">₹{{ vat_return_data.net_vat_liability|floatformat:2 }}</span>
                            </div>
                            {% else %}
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-sap-gray-800">VAT Refundable:</span>
                                <span class="font-bold text-sap-green-600 text-lg">₹{{ vat_return_data.net_vat_refundable|floatformat:2 }}</span>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- VAT Return Form Details -->
            <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                <div class="px-6 py-4 border-b border-sap-gray-100">
                    <h3 class="text-lg font-medium text-sap-gray-800">VAT Return Form</h3>
                    <p class="text-sm text-sap-gray-600 mt-1">Detailed breakdown for VAT return filing</p>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-sap-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Section</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Description</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Taxable Value</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">VAT Amount</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-sap-gray-200">
                            <!-- Sales/Output VAT Section -->
                            <tr class="bg-sap-blue-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-sap-gray-900">A</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-sap-gray-900">Output VAT on Sales</td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-gray-900"></td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-gray-900"></td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-500">A1</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">Taxable Sales within State</td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-gray-900">₹{{ vat_return_data.sales.total_taxable_sales|floatformat:2 }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-blue-600">₹{{ vat_return_data.sales.total_output_vat|floatformat:2 }}</td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-500">A2</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">Inter-state Sales (CST)</td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-gray-900">₹0.00</td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-gray-900">₹0.00</td>
                            </tr>
                            <tr class="bg-sap-blue-25">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-sap-gray-900"></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-sap-gray-900">Total Output VAT</td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-bold text-sap-gray-900">₹{{ vat_return_data.sales.total_taxable_sales|floatformat:2 }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-bold text-sap-blue-600">₹{{ vat_return_data.sales.total_output_vat|floatformat:2 }}</td>
                            </tr>

                            <!-- Purchases/Input VAT Section -->
                            <tr class="bg-sap-green-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-sap-gray-900">B</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-sap-gray-900">Input VAT on Purchases</td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-gray-900"></td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-gray-900"></td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-500">B1</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">Purchases within State</td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-gray-900">₹{{ vat_return_data.purchases.total_taxable_purchases|floatformat:2 }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-green-600">₹{{ vat_return_data.purchases.total_input_vat|floatformat:2 }}</td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-500">B2</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">Inter-state Purchases</td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-gray-900">₹0.00</td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-gray-900">₹0.00</td>
                            </tr>
                            <tr class="bg-sap-green-25">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-sap-gray-900"></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-sap-gray-900">Total Input VAT Credit</td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-bold text-sap-gray-900">₹{{ vat_return_data.purchases.total_taxable_purchases|floatformat:2 }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-bold text-sap-green-600">₹{{ vat_return_data.purchases.total_input_vat|floatformat:2 }}</td>
                            </tr>

                            <!-- Net VAT Section -->
                            <tr class="bg-sap-orange-50 border-t-2 border-sap-orange-200">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-sap-gray-900">C</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-sap-gray-900">Net VAT Calculation</td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-gray-900"></td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-gray-900"></td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-500">C1</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">Output VAT (A)</td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-gray-900"></td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-blue-600">₹{{ vat_return_data.sales.total_output_vat|floatformat:2 }}</td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-500">C2</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">Less: Input VAT Credit (B)</td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-gray-900"></td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-green-600">-₹{{ vat_return_data.purchases.total_input_vat|floatformat:2 }}</td>
                            </tr>
                            <tr class="bg-sap-orange-100 border-t border-sap-orange-200">
                                <td class="px-6 py-4 whitespace-nowrap text-lg font-bold text-sap-gray-900">C3</td>
                                <td class="px-6 py-4 whitespace-nowrap text-lg font-bold text-sap-gray-900">
                                    {% if vat_return_data.net_vat_payable > 0 %}
                                    Net VAT Payable
                                    {% else %}
                                    Net VAT Refundable
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-sap-gray-900"></td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-lg font-bold 
                                    {% if vat_return_data.net_vat_payable > 0 %}text-sap-red-600{% else %}text-sap-green-600{% endif %}">
                                    ₹{% if vat_return_data.net_vat_payable > 0 %}{{ vat_return_data.net_vat_liability|floatformat:2 }}{% else %}{{ vat_return_data.net_vat_refundable|floatformat:2 }}{% endif %}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Declaration Section -->
            <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm mt-6">
                <div class="px-6 py-4 border-b border-sap-gray-100">
                    <h3 class="text-lg font-medium text-sap-gray-800">Declaration</h3>
                </div>
                <div class="p-6">
                    <div class="bg-sap-gray-50 rounded-lg p-4 mb-6">
                        <p class="text-sm text-sap-gray-700 mb-2">
                            I/We hereby certify that the information given above is true and correct to the best of my/our knowledge and belief and nothing has been concealed or suppressed therefrom.
                        </p>
                        <p class="text-sm text-sap-gray-700">
                            I/We further certify that the VAT shown as payable is correct and the input tax credit claimed is in accordance with the VAT Act and Rules made thereunder.
                        </p>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div>
                            <p class="text-sm text-sap-gray-600 mb-2">Date: {{ "now"|date:"d/m/Y" }}</p>
                            <p class="text-sm text-sap-gray-600">Place: {{ company.registered_city.city_name|default:"_____________" }}</p>
                        </div>
                        <div class="text-right">
                            <div class="border-b border-sap-gray-300 w-48 ml-auto mb-2"></div>
                            <p class="text-sm text-sap-gray-600">Signature of Authorized Signatory</p>
                            <p class="text-xs text-sap-gray-500 mt-1">Name & Designation</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <!-- Welcome Message -->
        <div class="text-center py-12 bg-white shadow-lg rounded-lg">
            <i data-lucide="file-text" class="w-16 h-16 text-sap-gray-300 mx-auto mb-4"></i>
            <h3 class="text-xl font-bold text-sap-gray-900 mb-2">VAT Return Form</h3>
            <p class="text-sap-gray-600 mb-6">
                Select a return period above to generate the VAT return form with detailed calculations.
            </p>
            <div class="flex justify-center space-x-4">
                <button @click="loadCurrentMonth()" 
                        class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-6 py-3 rounded-lg font-medium">
                    Current Month
                </button>
                <button @click="loadCurrentQuarter()" 
                        class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-6 py-3 rounded-lg font-medium">
                    Current Quarter
                </button>
            </div>
        </div>
        {% endif %}
    </div>

</div>
{% endblock %}

{% block extra_js %}
<script>
function vatReturnForm() {
    return {
        vatReturnData: {{ vat_return_data|yesno:"true,false" }},
        
        init() {
            lucide.createIcons();
            
            // Add event listeners for return period type changes
            document.querySelectorAll('input[name="return_period"]').forEach(radio => {
                radio.addEventListener('change', this.updatePeriodFields);
            });
        },
        
        updatePeriodFields() {
            // This will be handled by Alpine.js x-show directives
        },
        
        loadCurrentMonth() {
            const today = new Date();
            document.getElementById('return_period_monthly').checked = true;
            document.getElementById('period_year').value = today.getFullYear();
            document.getElementById('period_month').value = today.getMonth() + 1;
            
            // Trigger form submission
            htmx.trigger(document.querySelector('form'), 'submit');
        },
        
        loadCurrentQuarter() {
            const today = new Date();
            const quarter = Math.ceil((today.getMonth() + 1) / 3);
            
            document.getElementById('return_period_quarterly').checked = true;
            document.getElementById('period_year').value = today.getFullYear();
            document.getElementById('period_quarter').value = quarter;
            
            // Trigger form submission
            htmx.trigger(document.querySelector('form'), 'submit');
        },
        
        exportReturn(format) {
            if (format === 'pdf') {
                // Generate PDF export
                const params = new URLSearchParams({
                    'return_period': document.querySelector('input[name="return_period"]:checked').value,
                    'period_year': document.getElementById('period_year').value,
                    'period_month': document.getElementById('period_month').value,
                    'period_quarter': document.getElementById('period_quarter').value,
                    'company_id': document.getElementById('company_id').value,
                    'financial_year_id': document.getElementById('financial_year_id').value,
                    'format': 'pdf'
                });
                
                window.open(`{% url 'accounts:vat_return_form' %}?${params.toString()}&export=pdf`);
            }
        },
        
        printReturn() {
            window.print();
        }
    }
}

// Print styles for VAT return
document.addEventListener('DOMContentLoaded', function() {
    const style = document.createElement('style');
    style.textContent = `
        @media print {
            .no-print { display: none !important; }
            .print-break { page-break-before: always; }
            body { background: white !important; }
            .bg-white { background: white !important; }
            .bg-sap-gray-50 { background: #f9f9f9 !important; }
            .bg-sap-blue-50 { background: #eff6ff !important; }
            .bg-sap-green-50 { background: #f0fdf4 !important; }
            .bg-sap-orange-50 { background: #fff7ed !important; }
            .shadow-sm { box-shadow: none !important; }
            .rounded-lg { border-radius: 0 !important; }
            .border { border: 1px solid #000 !important; }
            .text-sap-blue-600, .text-sap-green-600, .text-sap-orange-600, .text-sap-red-600 { 
                color: #000 !important; 
            }
            @page {
                margin: 1in;
                size: A4;
            }
        }
    `;
    document.head.appendChild(style);
    
    lucide.createIcons();
});
</script>
{% endblock %}