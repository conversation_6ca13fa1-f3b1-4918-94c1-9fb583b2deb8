﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="GRN" targetNamespace="http://tempuri.org/GRN.xsd" xmlns:mstns="http://tempuri.org/GRN.xsd" xmlns="http://tempuri.org/GRN.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="GRN" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="GRN" msprop:Generator_DataSetName="GRN">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" msprop:Generator_UserColumnName="Id" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnPropNameInTable="IdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="ItemCode" msprop:Generator_UserColumnName="ItemCode" msprop:Generator_ColumnVarNameInTable="columnItemCode" msprop:Generator_ColumnPropNameInRow="ItemCode" msprop:Generator_ColumnPropNameInTable="ItemCodeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Desc" msprop:Generator_UserColumnName="Desc" msprop:Generator_ColumnVarNameInTable="columnDesc" msprop:Generator_ColumnPropNameInRow="Desc" msprop:Generator_ColumnPropNameInTable="DescColumn" type="xs:string" minOccurs="0" />
              <xs:element name="UOM" msprop:Generator_UserColumnName="UOM" msprop:Generator_ColumnVarNameInTable="columnUOM" msprop:Generator_ColumnPropNameInRow="UOM" msprop:Generator_ColumnPropNameInTable="UOMColumn" type="xs:string" minOccurs="0" />
              <xs:element name="POQty" msprop:Generator_UserColumnName="POQty" msprop:Generator_ColumnVarNameInTable="columnPOQty" msprop:Generator_ColumnPropNameInRow="POQty" msprop:Generator_ColumnPropNameInTable="POQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="InvQty" msprop:Generator_UserColumnName="InvQty" msprop:Generator_ColumnVarNameInTable="columnInvQty" msprop:Generator_ColumnPropNameInRow="InvQty" msprop:Generator_ColumnPropNameInTable="InvQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="RecedQty" msprop:Generator_UserColumnName="RecedQty" msprop:Generator_ColumnVarNameInTable="columnRecedQty" msprop:Generator_ColumnPropNameInRow="RecedQty" msprop:Generator_ColumnPropNameInTable="RecedQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="AcceptedQty" msprop:Generator_UserColumnName="AcceptedQty" msprop:Generator_ColumnVarNameInTable="columnAcceptedQty" msprop:Generator_ColumnPropNameInRow="AcceptedQty" msprop:Generator_ColumnPropNameInTable="AcceptedQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="RejReason" msprop:Generator_UserColumnName="RejReason" msprop:Generator_ColumnVarNameInTable="columnRejReason" msprop:Generator_ColumnPropNameInRow="RejReason" msprop:Generator_ColumnPropNameInTable="RejReasonColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Remarks" msprop:Generator_UserColumnName="Remarks" msprop:Generator_ColumnVarNameInTable="columnRemarks" msprop:Generator_ColumnPropNameInRow="Remarks" msprop:Generator_ColumnPropNameInTable="RemarksColumn" type="xs:string" minOccurs="0" />
              <xs:element name="RejectedQty" msprop:Generator_UserColumnName="RejectedQty" msprop:Generator_ColumnVarNameInTable="columnRejectedQty" msprop:Generator_ColumnPropNameInRow="RejectedQty" msprop:Generator_ColumnPropNameInTable="RejectedQtyColumn" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>