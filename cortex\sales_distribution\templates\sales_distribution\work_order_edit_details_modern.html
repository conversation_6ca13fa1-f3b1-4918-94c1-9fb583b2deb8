{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }} - Sales Distribution{% endblock %}

{% block extra_css %}
<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/umd/lucide.min.css">
<script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-sap-gray-50 font-sap">
    <!-- SAP Fiori Header -->
    <div class="bg-white shadow-sm border-b border-sap-gray-200">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                            <i data-lucide="edit-3" class="w-5 h-5 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-xl font-semibold text-sap-gray-900">{{ page_title }}</h1>
                            <p class="text-sm text-sap-gray-600">Comprehensive work order management and editing</p>
                        </div>
                    </div>
                </div>
                <nav class="flex items-center space-x-2 text-sm text-sap-gray-500">
                    <a href="{% url 'sales_distribution:dashboard' %}" class="hover:text-sap-blue-600 transition-colors">Sales & Distribution</a>
                    <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    <a href="{% url 'sales_distribution:work_order_edit_list' %}" class="hover:text-sap-blue-600 transition-colors">Work Orders</a>
                    <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    <span class="text-sap-gray-900 font-medium">{{ work_order.wono|default:"Edit Work Order" }}</span>
                </nav>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-6 py-8">
        <!-- Work Order Overview Card -->
        <div class="sap-card mb-6">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-sap-blue-100 rounded-xl flex items-center justify-center">
                        <i data-lucide="file-text" class="w-5 h-5 text-sap-blue-600"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-semibold text-sap-gray-900">Work Order Overview</h2>
                        <p class="text-sm text-sap-gray-600">Essential work order information and current status</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i data-lucide="circle" class="w-3 h-3 mr-1 fill-current"></i>
                            Active
                        </span>
                    </div>
                    <a href="{% url 'sales_distribution:work_order_edit_list' %}" 
                       class="inline-flex items-center px-4 py-2 text-sm font-medium text-sap-gray-700 bg-white border border-sap-gray-300 rounded-lg hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 transition-all duration-200">
                        <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                        Back to List
                    </a>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-sap-gray-50 rounded-lg p-4">
                    <div class="text-xs font-medium text-sap-gray-500 mb-1">Customer Name</div>
                    <div class="text-sm font-semibold text-sap-gray-900">{{ customer.customer_name|default:"-" }}</div>
                </div>
                <div class="bg-sap-gray-50 rounded-lg p-4">
                    <div class="text-xs font-medium text-sap-gray-500 mb-1">Work Order No</div>
                    <div class="text-sm font-semibold text-sap-blue-600">{{ work_order.wono|default:"-" }}</div>
                </div>
                <div class="bg-sap-gray-50 rounded-lg p-4">
                    <div class="text-xs font-medium text-sap-gray-500 mb-1">PO Number</div>
                    <div class="text-sm font-semibold text-sap-gray-900">{{ work_order.pono|default:"-" }}</div>
                </div>
                <div class="bg-sap-gray-50 rounded-lg p-4">
                    <div class="text-xs font-medium text-sap-gray-500 mb-1">Enquiry No</div>
                    <div class="text-sm font-semibold text-sap-gray-900">{{ work_order.enqid.enqid|default:"-" }}</div>
                </div>
            </div>
        </div>

        <!-- Modern SAP Tabbed Interface -->
        <div class="sap-card" x-data="{ activeTab: '{{ active_tab|default:'0' }}' }">
            <!-- Tab Navigation -->
            <div class="border-b border-sap-gray-200 mb-8">
                <nav class="flex space-x-8">
                    <button type="button" @click="activeTab = '0'" 
                            :class="activeTab === '0' ? 'border-sap-blue-600 text-sap-blue-600' : 'border-transparent text-sap-gray-500 hover:text-sap-gray-700 hover:border-sap-gray-300'"
                            class="flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                        <i data-lucide="briefcase" class="w-4 h-4 mr-2"></i>
                        Task Execution
                    </button>
                    <button type="button" @click="activeTab = '1'"
                            :class="activeTab === '1' ? 'border-sap-blue-600 text-sap-blue-600' : 'border-transparent text-sap-gray-500 hover:text-sap-gray-700 hover:border-sap-gray-300'"
                            class="flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                        <i data-lucide="truck" class="w-4 h-4 mr-2"></i>
                        Shipping
                    </button>
                    <button type="button" @click="activeTab = '2'"
                            :class="activeTab === '2' ? 'border-sap-blue-600 text-sap-blue-600' : 'border-transparent text-sap-gray-500 hover:text-sap-gray-700 hover:border-sap-gray-300'"
                            class="flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                        <i data-lucide="package" class="w-4 h-4 mr-2"></i>
                        Products
                    </button>
                    <button type="button" @click="activeTab = '3'"
                            :class="activeTab === '3' ? 'border-sap-blue-600 text-sap-blue-600' : 'border-transparent text-sap-gray-500 hover:text-sap-gray-700 hover:border-sap-gray-300'"
                            class="flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                        <i data-lucide="clipboard-list" class="w-4 h-4 mr-2"></i>
                        Instructions
                    </button>
                </nav>
            </div>

            <!-- Form Content -->
            <form method="post" id="work-order-form">
                {% csrf_token %}
                
                <!-- Task Execution Tab -->
                <div x-show="activeTab === '0'" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100">
                    <div class="space-y-8">
                        <!-- Basic Information Section -->
                        <div>
                            <div class="flex items-center space-x-2 mb-6">
                                <div class="w-6 h-6 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="info" class="w-4 h-4 text-sap-blue-600"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-sap-gray-900">Basic Information</h3>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                <div class="space-y-2">
                                    <label for="{{ task_form.work_order_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                        Work Order Date <span class="text-red-500">*</span>
                                    </label>
                                    <div class="relative">
                                        <i data-lucide="calendar" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-sap-gray-400"></i>
                                        {{ task_form.work_order_date|add_class:"sap-input pl-10" }}
                                    </div>
                                    {% if task_form.work_order_date.errors %}
                                        <div class="text-red-500 text-xs">{{ task_form.work_order_date.errors }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="space-y-2">
                                    <label for="{{ task_form.category.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                        Category <span class="text-red-500">*</span>
                                    </label>
                                    {{ task_form.category|add_class:"sap-input" }}
                                    {% if task_form.category.errors %}
                                        <div class="text-red-500 text-xs">{{ task_form.category.errors }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="space-y-2">
                                    <label for="{{ task_form.subcategory.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                        Sub Category
                                    </label>
                                    {{ task_form.subcategory|add_class:"sap-input" }}
                                    {% if task_form.subcategory.errors %}
                                        <div class="text-red-500 text-xs">{{ task_form.subcategory.errors }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="space-y-2">
                                    <label for="{{ task_form.business_group.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                        Business Group
                                    </label>
                                    {{ task_form.business_group|add_class:"sap-input" }}
                                    {% if task_form.business_group.errors %}
                                        <div class="text-red-500 text-xs">{{ task_form.business_group.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 gap-6 mt-6">
                                <div class="space-y-2">
                                    <label for="{{ task_form.project_title.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                        Project Title <span class="text-red-500">*</span>
                                    </label>
                                    {{ task_form.project_title|add_class:"sap-input" }}
                                    {% if task_form.project_title.errors %}
                                        <div class="text-red-500 text-xs">{{ task_form.project_title.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                                <div class="space-y-2">
                                    <label for="{{ task_form.project_leader.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                        Project Leader <span class="text-red-500">*</span>
                                    </label>
                                    <div class="relative">
                                        <i data-lucide="user" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-sap-gray-400"></i>
                                        {{ task_form.project_leader|add_class:"sap-input pl-10" }}
                                    </div>
                                    {% if task_form.project_leader.errors %}
                                        <div class="text-red-500 text-xs">{{ task_form.project_leader.errors }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="space-y-2">
                                    <label for="{{ task_form.buyer.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                        Material Buyer
                                    </label>
                                    <div class="relative">
                                        <i data-lucide="shopping-cart" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-sap-gray-400"></i>
                                        {{ task_form.buyer|add_class:"sap-input pl-10" }}
                                    </div>
                                    {% if task_form.buyer.errors %}
                                        <div class="text-red-500 text-xs">{{ task_form.buyer.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Target Dates Section -->
                        <div>
                            <div class="flex items-center space-x-2 mb-6">
                                <div class="w-6 h-6 bg-orange-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="clock" class="w-4 h-4 text-orange-600"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-sap-gray-900">Target Dates & Timeline</h3>
                            </div>
                            
                            <div class="space-y-6">
                                <!-- Target DAP Date -->
                                <div class="bg-sap-gray-50 rounded-lg p-4">
                                    <label class="block text-sm font-medium text-sap-gray-700 mb-3">Target DAP Date</label>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div class="space-y-2">
                                            <label class="block text-xs text-sap-gray-500">From Date</label>
                                            {{ task_form.target_dap_from_date|add_class:"sap-input" }}
                                            {% if task_form.target_dap_from_date.errors %}
                                                <div class="text-red-500 text-xs">{{ task_form.target_dap_from_date.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="space-y-2">
                                            <label class="block text-xs text-sap-gray-500">To Date</label>
                                            {{ task_form.target_dap_to_date|add_class:"sap-input" }}
                                            {% if task_form.target_dap_to_date.errors %}
                                                <div class="text-red-500 text-xs">{{ task_form.target_dap_to_date.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <!-- Design Finalization Date -->
                                <div class="bg-sap-gray-50 rounded-lg p-4">
                                    <label class="block text-sm font-medium text-sap-gray-700 mb-3">Design Finalization Date</label>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div class="space-y-2">
                                            <label class="block text-xs text-sap-gray-500">From Date</label>
                                            {{ task_form.design_finalization_from_date|add_class:"sap-input" }}
                                            {% if task_form.design_finalization_from_date.errors %}
                                                <div class="text-red-500 text-xs">{{ task_form.design_finalization_from_date.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="space-y-2">
                                            <label class="block text-xs text-sap-gray-500">To Date</label>
                                            {{ task_form.design_finalization_to_date|add_class:"sap-input" }}
                                            {% if task_form.design_finalization_to_date.errors %}
                                                <div class="text-red-500 text-xs">{{ task_form.design_finalization_to_date.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <!-- Target Manufacturing Date -->
                                <div class="bg-sap-gray-50 rounded-lg p-4">
                                    <label class="block text-sm font-medium text-sap-gray-700 mb-3">Target Manufacturing Date</label>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div class="space-y-2">
                                            <label class="block text-xs text-sap-gray-500">From Date</label>
                                            {{ task_form.target_manufacturing_from_date|add_class:"sap-input" }}
                                            {% if task_form.target_manufacturing_from_date.errors %}
                                                <div class="text-red-500 text-xs">{{ task_form.target_manufacturing_from_date.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="space-y-2">
                                            <label class="block text-xs text-sap-gray-500">To Date</label>
                                            {{ task_form.target_manufacturing_to_date|add_class:"sap-input" }}
                                            {% if task_form.target_manufacturing_to_date.errors %}
                                                <div class="text-red-500 text-xs">{{ task_form.target_manufacturing_to_date.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <!-- Additional Date Fields with Consistent Pattern -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="bg-sap-gray-50 rounded-lg p-4">
                                        <label class="block text-sm font-medium text-sap-gray-700 mb-3">Target Try-out Date</label>
                                        <div class="space-y-3">
                                            <div class="space-y-2">
                                                <label class="block text-xs text-sap-gray-500">From Date</label>
                                                {{ task_form.target_tryout_from_date|add_class:"sap-input" }}
                                            </div>
                                            <div class="space-y-2">
                                                <label class="block text-xs text-sap-gray-500">To Date</label>
                                                {{ task_form.target_tryout_to_date|add_class:"sap-input" }}
                                            </div>
                                        </div>
                                    </div>

                                    <div class="bg-sap-gray-50 rounded-lg p-4">
                                        <label class="block text-sm font-medium text-sap-gray-700 mb-3">Target Despatch Date</label>
                                        <div class="space-y-3">
                                            <div class="space-y-2">
                                                <label class="block text-xs text-sap-gray-500">From Date</label>
                                                {{ task_form.target_despatch_from_date|add_class:"sap-input" }}
                                            </div>
                                            <div class="space-y-2">
                                                <label class="block text-xs text-sap-gray-500">To Date</label>
                                                {{ task_form.target_despatch_to_date|add_class:"sap-input" }}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Material Procurement Section -->
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                    <div class="flex items-center space-x-2 mb-4">
                                        <i data-lucide="package" class="w-5 h-5 text-blue-600"></i>
                                        <h4 class="text-sm font-semibold text-blue-800">Material Procurement Dates</h4>
                                    </div>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div class="space-y-2">
                                            <label for="{{ task_form.manufacturing_material_date.id_for_label }}" class="block text-sm font-medium text-blue-700">
                                                Manufacturing Material Date
                                            </label>
                                            {{ task_form.manufacturing_material_date|add_class:"sap-input" }}
                                        </div>
                                        
                                        <div class="space-y-2">
                                            <label for="{{ task_form.boughtout_material_date.id_for_label }}" class="block text-sm font-medium text-blue-700">
                                                Boughtout Material Date
                                            </label>
                                            {{ task_form.boughtout_material_date|add_class:"sap-input" }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-4">
                            <button type="button" @click="activeTab = '1'" class="sap-button-primary">
                                <i data-lucide="arrow-right" class="w-4 h-4 mr-2"></i>
                                Next: Shipping
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Shipping Tab -->
                <div x-show="activeTab === '1'" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100">
                    <div class="space-y-8">
                        <!-- Shipping Address Section -->
                        <div>
                            <div class="flex items-center space-x-2 mb-6">
                                <div class="w-6 h-6 bg-green-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="map-pin" class="w-4 h-4 text-green-600"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-sap-gray-900">Shipping Address</h3>
                            </div>
                            
                            <div class="space-y-6">
                                <div class="space-y-2">
                                    <label for="{{ shipping_form.shipping_address.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                        Complete Shipping Address
                                    </label>
                                    {{ shipping_form.shipping_address|add_class:"sap-input min-h-20" }}
                                    {% if shipping_form.shipping_address.errors %}
                                        <div class="text-red-500 text-xs">{{ shipping_form.shipping_address.errors }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div class="space-y-2">
                                        <label for="{{ shipping_form.shipping_country.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                            Country
                                        </label>
                                        {{ shipping_form.shipping_country|add_class:"sap-input" }}
                                    </div>
                                    
                                    <div class="space-y-2">
                                        <label for="{{ shipping_form.shipping_state.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                            State
                                        </label>
                                        {{ shipping_form.shipping_state|add_class:"sap-input" }}
                                    </div>
                                    
                                    <div class="space-y-2">
                                        <label for="{{ shipping_form.shipping_city.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                            City
                                        </label>
                                        {{ shipping_form.shipping_city|add_class:"sap-input" }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Information Section -->
                        <div>
                            <div class="flex items-center space-x-2 mb-6">
                                <div class="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="users" class="w-4 h-4 text-blue-600"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-sap-gray-900">Contact Information</h3>
                            </div>
                            
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                <!-- Primary Contact -->
                                <div class="bg-sap-gray-50 rounded-lg p-6">
                                    <h4 class="text-sm font-semibold text-sap-gray-700 mb-4">Primary Contact</h4>
                                    <div class="space-y-4">
                                        <div class="space-y-2">
                                            <label for="{{ shipping_form.contact_person_1.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                                Contact Person
                                            </label>
                                            <div class="relative">
                                                <i data-lucide="user" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-sap-gray-400"></i>
                                                {{ shipping_form.contact_person_1|add_class:"sap-input pl-10" }}
                                            </div>
                                        </div>
                                        
                                        <div class="space-y-2">
                                            <label for="{{ shipping_form.contact_no_1.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                                Phone Number
                                            </label>
                                            <div class="relative">
                                                <i data-lucide="phone" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-sap-gray-400"></i>
                                                {{ shipping_form.contact_no_1|add_class:"sap-input pl-10" }}
                                            </div>
                                        </div>
                                        
                                        <div class="space-y-2">
                                            <label for="{{ shipping_form.email_1.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                                Email Address
                                            </label>
                                            <div class="relative">
                                                <i data-lucide="mail" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-sap-gray-400"></i>
                                                {{ shipping_form.email_1|add_class:"sap-input pl-10" }}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Secondary Contact -->
                                <div class="bg-sap-gray-50 rounded-lg p-6">
                                    <h4 class="text-sm font-semibold text-sap-gray-700 mb-4">Secondary Contact</h4>
                                    <div class="space-y-4">
                                        <div class="space-y-2">
                                            <label for="{{ shipping_form.contact_person_2.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                                Contact Person
                                            </label>
                                            <div class="relative">
                                                <i data-lucide="user" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-sap-gray-400"></i>
                                                {{ shipping_form.contact_person_2|add_class:"sap-input pl-10" }}
                                            </div>
                                        </div>
                                        
                                        <div class="space-y-2">
                                            <label for="{{ shipping_form.contact_no_2.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                                Phone Number
                                            </label>
                                            <div class="relative">
                                                <i data-lucide="phone" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-sap-gray-400"></i>
                                                {{ shipping_form.contact_no_2|add_class:"sap-input pl-10" }}
                                            </div>
                                        </div>
                                        
                                        <div class="space-y-2">
                                            <label for="{{ shipping_form.email_2.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                                Email Address
                                            </label>
                                            <div class="relative">
                                                <i data-lucide="mail" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-sap-gray-400"></i>
                                                {{ shipping_form.email_2|add_class:"sap-input pl-10" }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Additional Details -->
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
                                <div class="space-y-2">
                                    <label for="{{ shipping_form.fax_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                        Fax Number
                                    </label>
                                    <div class="relative">
                                        <i data-lucide="printer" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-sap-gray-400"></i>
                                        {{ shipping_form.fax_no|add_class:"sap-input pl-10" }}
                                    </div>
                                </div>
                                
                                <div class="space-y-2">
                                    <label for="{{ shipping_form.ecc_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                        ECC Number
                                    </label>
                                    {{ shipping_form.ecc_no|add_class:"sap-input" }}
                                </div>
                                
                                <div class="space-y-2">
                                    <label for="{{ shipping_form.tin_cst_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                        TIN/CST Number
                                    </label>
                                    {{ shipping_form.tin_cst_no|add_class:"sap-input" }}
                                </div>
                                
                                <div class="space-y-2">
                                    <label for="{{ shipping_form.tin_vat_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                        TIN/VAT Number
                                    </label>
                                    {{ shipping_form.tin_vat_no|add_class:"sap-input" }}
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-between">
                            <button type="button" @click="activeTab = '0'" class="inline-flex items-center px-6 py-3 text-sm font-medium text-sap-gray-700 bg-white border border-sap-gray-300 rounded-lg hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 transition-all duration-200">
                                <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                                Previous: Task Execution
                            </button>
                            <button type="button" @click="activeTab = '2'" class="sap-button-primary">
                                <i data-lucide="arrow-right" class="w-4 h-4 mr-2"></i>
                                Next: Products
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Products Tab -->
                <div x-show="activeTab === '2'" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100">
                    <div class="space-y-8">
                        <!-- Add Product Section -->
                        <div>
                            <div class="flex items-center space-x-2 mb-6">
                                <div class="w-6 h-6 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="plus" class="w-4 h-4 text-purple-600"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-sap-gray-900">Add New Product</h3>
                            </div>
                            
                            <div class="bg-purple-50 border border-purple-200 rounded-lg p-6">
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div class="space-y-2">
                                        <label for="{{ product_form.item_code.id_for_label }}" class="block text-sm font-medium text-purple-700">
                                            Item Code
                                        </label>
                                        <div class="relative">
                                            <i data-lucide="hash" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-purple-400"></i>
                                            {{ product_form.item_code|add_class:"sap-input pl-10 border-purple-300 focus:border-purple-500 focus:ring-purple-500" }}
                                        </div>
                                        {% if product_form.item_code.errors %}
                                            <div class="text-red-500 text-xs">{{ product_form.item_code.errors }}</div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="space-y-2">
                                        <label for="{{ product_form.description.id_for_label }}" class="block text-sm font-medium text-purple-700">
                                            Description
                                        </label>
                                        {{ product_form.description|add_class:"sap-input border-purple-300 focus:border-purple-500 focus:ring-purple-500" }}
                                        {% if product_form.description.errors %}
                                            <div class="text-red-500 text-xs">{{ product_form.description.errors }}</div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="space-y-2">
                                        <label for="{{ product_form.quantity.id_for_label }}" class="block text-sm font-medium text-purple-700">
                                            Quantity
                                        </label>
                                        <div class="relative">
                                            <i data-lucide="package" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-purple-400"></i>
                                            {{ product_form.quantity|add_class:"sap-input pl-10 border-purple-300 focus:border-purple-500 focus:ring-purple-500" }}
                                        </div>
                                        {% if product_form.quantity.errors %}
                                            <div class="text-red-500 text-xs">{{ product_form.quantity.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <div class="mt-6 flex justify-end">
                                    <button type="submit" name="action" value="add_product" class="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 transform hover:scale-105">
                                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                        Add Product
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Products List Section -->
                        <div>
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-green-100 rounded-lg flex items-center justify-center">
                                        <i data-lucide="list" class="w-4 h-4 text-green-600"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-sap-gray-900">Current Products</h3>
                                </div>
                                {% if products %}
                                <div class="text-sm text-sap-gray-600">
                                    {{ products|length }} product{{ products|length|pluralize }} added
                                </div>
                                {% endif %}
                            </div>

                            {% if products %}
                            <div class="overflow-hidden rounded-xl border border-sap-gray-200">
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-sap-gray-200">
                                        <thead class="bg-sap-gray-50">
                                            <tr>
                                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                                    <div class="flex items-center space-x-2">
                                                        <i data-lucide="hash" class="w-4 h-4"></i>
                                                        <span>SN</span>
                                                    </div>
                                                </th>
                                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                                    <div class="flex items-center space-x-2">
                                                        <i data-lucide="tag" class="w-4 h-4"></i>
                                                        <span>Item Code</span>
                                                    </div>
                                                </th>
                                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                                    <div class="flex items-center space-x-2">
                                                        <i data-lucide="file-text" class="w-4 h-4"></i>
                                                        <span>Description</span>
                                                    </div>
                                                </th>
                                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                                    <div class="flex items-center space-x-2">
                                                        <i data-lucide="package" class="w-4 h-4"></i>
                                                        <span>Quantity</span>
                                                    </div>
                                                </th>
                                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                                    <div class="flex items-center space-x-2">
                                                        <i data-lucide="settings" class="w-4 h-4"></i>
                                                        <span>Actions</span>
                                                    </div>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-sap-gray-200">
                                            {% for product in products %}
                                            <tr class="hover:bg-sap-gray-50 transition-colors duration-150">
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm font-medium text-sap-gray-900">{{ forloop.counter }}</div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="flex items-center">
                                                        <div class="flex-shrink-0 w-8 h-8 bg-sap-blue-100 rounded-lg flex items-center justify-center mr-3">
                                                            <i data-lucide="tag" class="w-4 h-4 text-sap-blue-600"></i>
                                                        </div>
                                                        <div class="text-sm font-semibold text-sap-blue-600">{{ product.item_code }}</div>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4">
                                                    <div class="text-sm text-sap-gray-600">{{ product.description|truncatechars:50 }}</div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm font-medium text-sap-gray-900 text-right">{{ product.quantity|floatformat:3 }}</div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <button type="submit" name="action" value="delete_product" 
                                                            onclick="document.getElementById('delete_product_id').value='{{ product.id }}'; return confirm('Are you sure you want to delete this product?');"
                                                            class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200 transform hover:scale-105">
                                                        <i data-lucide="trash-2" class="w-4 h-4 mr-1"></i>
                                                        Delete
                                                    </button>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% else %}
                            <div class="text-center py-16">
                                <div class="w-20 h-20 bg-sap-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                    <i data-lucide="package" class="w-10 h-10 text-sap-gray-400"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-sap-gray-900 mb-2">No Products Added</h3>
                                <p class="text-sap-gray-600 max-w-md mx-auto">
                                    Use the form above to add products to this work order. You can add multiple products with different quantities.
                                </p>
                            </div>
                            {% endif %}

                            <input type="hidden" id="delete_product_id" name="product_id" value="">
                        </div>

                        <div class="flex justify-between">
                            <button type="button" @click="activeTab = '1'" class="inline-flex items-center px-6 py-3 text-sm font-medium text-sap-gray-700 bg-white border border-sap-gray-300 rounded-lg hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 transition-all duration-200">
                                <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                                Previous: Shipping
                            </button>
                            <button type="button" @click="activeTab = '3'" class="sap-button-primary">
                                <i data-lucide="arrow-right" class="w-4 h-4 mr-2"></i>
                                Next: Instructions
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Instructions Tab -->
                <div x-show="activeTab === '3'" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100">
                    <div class="space-y-8">
                        <!-- Special Instructions Section -->
                        <div>
                            <div class="flex items-center space-x-2 mb-6">
                                <div class="w-6 h-6 bg-yellow-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="alert-circle" class="w-4 h-4 text-yellow-600"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-sap-gray-900">Special Instructions & Requirements</h3>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Processing Instructions -->
                                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                                    <h4 class="text-sm font-semibold text-yellow-800 mb-4">Processing Requirements</h4>
                                    <div class="space-y-4">
                                        <label class="flex items-center space-x-3 cursor-pointer">
                                            {{ instructions_form.primer_painting }}
                                            <span class="text-sm font-medium text-yellow-700">Primer Painting to be done</span>
                                        </label>
                                        
                                        <label class="flex items-center space-x-3 cursor-pointer">
                                            {{ instructions_form.painting }}
                                            <span class="text-sm font-medium text-yellow-700">Painting to be done</span>
                                        </label>
                                        
                                        <label class="flex items-center space-x-3 cursor-pointer">
                                            {{ instructions_form.self_certification_report }}
                                            <span class="text-sm font-medium text-yellow-700">Self Certification Report to be submitted</span>
                                        </label>
                                    </div>
                                </div>

                                <!-- Additional Details -->
                                <div class="space-y-6">
                                    <div class="space-y-2">
                                        <label for="{{ instructions_form.other_instructions.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                            Other Instructions
                                        </label>
                                        <div class="relative">
                                            <i data-lucide="edit" class="absolute left-3 top-3 w-4 h-4 text-sap-gray-400"></i>
                                            {{ instructions_form.other_instructions|add_class:"sap-input pl-10 min-h-24" }}
                                        </div>
                                        {% if instructions_form.other_instructions.errors %}
                                            <div class="text-red-500 text-xs">{{ instructions_form.other_instructions.errors }}</div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="space-y-2">
                                        <label for="{{ instructions_form.export_case_mark.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                            Export Case Mark
                                        </label>
                                        <div class="relative">
                                            <i data-lucide="tag" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-sap-gray-400"></i>
                                            {{ instructions_form.export_case_mark|add_class:"sap-input pl-10" }}
                                        </div>
                                        {% if instructions_form.export_case_mark.errors %}
                                            <div class="text-red-500 text-xs">{{ instructions_form.export_case_mark.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Packing Instructions -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                            <div class="flex items-center space-x-2 mb-4">
                                <i data-lucide="package" class="w-5 h-5 text-blue-600"></i>
                                <h4 class="text-sm font-semibold text-blue-800">Standard Packing Instructions</h4>
                            </div>
                            <p class="text-sm text-blue-700">
                                <strong>Important:</strong> Export Seaworthy / Wooden / Corrugated packaging must be completed 7 days before dispatch to ensure compliance with shipping requirements.
                            </p>
                        </div>

                        <!-- Final Actions -->
                        <div class="bg-sap-gray-50 rounded-lg p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-sm font-semibold text-sap-gray-900">Ready to Update Work Order?</h4>
                                    <p class="text-sm text-sap-gray-600 mt-1">Please review all tabs before saving your changes.</p>
                                </div>
                                <div class="flex space-x-4">
                                    <button type="button" @click="activeTab = '2'" class="inline-flex items-center px-6 py-3 text-sm font-medium text-sap-gray-700 bg-white border border-sap-gray-300 rounded-lg hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 transition-all duration-200">
                                        <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                                        Previous: Products
                                    </button>
                                    <button type="submit" name="action" value="update_work_order" class="inline-flex items-center px-8 py-3 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 transform hover:scale-105">
                                        <i data-lucide="save" class="w-4 h-4 mr-2"></i>
                                        Update Work Order
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Initialize Lucide icons
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
    
    // Form validation
    document.getElementById('work-order-form').addEventListener('submit', function(e) {
        const action = e.submitter ? e.submitter.value : '';
        
        if (action === 'update_work_order') {
            // Basic validation
            const requiredFields = ['work_order_date', 'project_title', 'project_leader'];
            let isValid = true;
            let firstInvalidField = null;
            
            requiredFields.forEach(fieldName => {
                const field = document.querySelector(`[name="${fieldName}"]`);
                if (field && !field.value.trim()) {
                    isValid = false;
                    field.classList.add('border-red-500');
                    if (!firstInvalidField) {
                        firstInvalidField = field;
                    }
                } else if (field) {
                    field.classList.remove('border-red-500');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                // Focus on first invalid field and show Task Execution tab
                Alpine.store('activeTab', '0');
                if (firstInvalidField) {
                    firstInvalidField.focus();
                }
                // Show toast notification
                const notification = document.createElement('div');
                notification.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-lg z-50';
                notification.innerHTML = `
                    <div class="flex items-center">
                        <i data-lucide="alert-circle" class="w-4 h-4 mr-2"></i>
                        <span>Please fill in all required fields marked with *</span>
                    </div>
                `;
                document.body.appendChild(notification);
                lucide.createIcons();
                
                setTimeout(() => {
                    notification.remove();
                }, 5000);
            }
        }
    });
});

// Enhanced tab navigation with keyboard support
document.addEventListener('keydown', function(e) {
    if (e.altKey) {
        switch(e.key) {
            case '1':
                Alpine.store('activeTab', '0');
                e.preventDefault();
                break;
            case '2':
                Alpine.store('activeTab', '1');
                e.preventDefault();
                break;
            case '3':
                Alpine.store('activeTab', '2');
                e.preventDefault();
                break;
            case '4':
                Alpine.store('activeTab', '3');
                e.preventDefault();
                break;
        }
    }
});
</script>
{% endblock %}