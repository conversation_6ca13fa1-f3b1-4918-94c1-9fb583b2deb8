{% extends 'core/base.html' %}
{% load static %}

{% block title %}Payment & Receipt Against Categories{% endblock %}

{% block content %}
<div class="sap-page-container">
    <!-- Page Header -->
    <div class="sap-page-header">
        <div class="sap-breadcrumb">
            <span class="sap-breadcrumb-item">
                <a href="{% url 'accounts:dashboard' %}">Accounts</a>
            </span>
            <span class="sap-breadcrumb-separator">/</span>
            <span class="sap-breadcrumb-item">Masters</span>
            <span class="sap-breadcrumb-separator">/</span>
            <span class="sap-breadcrumb-item sap-breadcrumb-current">
                Payment & Receipt Against
            </span>
        </div>

        <div class="sap-page-title">
            <h1 class="sap-page-title-main">Payment & Receipt Against Categories</h1>
            <p class="sap-page-title-sub">Manage payment and receipt category classifications</p>
        </div>

        <div class="sap-page-actions">
            <button class="sap-btn sap-btn-primary" 
                    hx-get="{% url 'accounts:payment_against_create' %}"
                    hx-target="#modal-container"
                    hx-trigger="click">
                <i class="sap-icon sap-icon-add"></i>
                Add Payment Category
            </button>
            <button class="sap-btn sap-btn-secondary" 
                    hx-get="{% url 'accounts:receipt_against_create' %}"
                    hx-target="#modal-container"
                    hx-trigger="click">
                <i class="sap-icon sap-icon-add"></i>
                Add Receipt Category
            </button>
        </div>
    </div>

    <!-- Payment Categories Section -->
    <div class="sap-section">
        <div class="sap-section-header">
            <h2 class="sap-section-title">Payment Against Categories</h2>
            <div class="sap-section-toolbar">
                <div class="sap-search-field">
                    <input type="text" 
                           class="sap-input" 
                           placeholder="Search payment categories..."
                           hx-get="{% url 'accounts:payment_receipt_against_list' %}"
                           hx-target="#payment-categories-table"
                           hx-trigger="keyup changed delay:300ms"
                           name="payment_search">
                    <i class="sap-icon sap-icon-search"></i>
                </div>
            </div>
        </div>

        <div class="sap-table-container">
            <table class="sap-table" id="payment-categories-table">
                <thead>
                    <tr>
                        <th class="sap-table-header">Category Name</th>
                        <th class="sap-table-header">Description</th>
                        <th class="sap-table-header">Status</th>
                        <th class="sap-table-header">Created Date</th>
                        <th class="sap-table-header sap-table-header-actions">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for category in payment_categories %}
                    <tr class="sap-table-row">
                        <td class="sap-table-cell">
                            <span class="sap-text-strong">{{ category.description }}</span>
                        </td>
                        <td class="sap-table-cell">
                            <span class="sap-text-muted">{{ category.description|default:"-" }}</span>
                        </td>
                        <td class="sap-table-cell">
                            <span class="sap-status sap-status-success">Active</span>
                        </td>
                        <td class="sap-table-cell">
                            <span class="sap-text-muted">-</span>
                        </td>
                        <td class="sap-table-cell sap-table-cell-actions">
                            <div class="sap-btn-group">
                                <button class="sap-btn sap-btn-transparent sap-btn-icon" 
                                        title="Edit"
                                        hx-get="{% url 'accounts:payment_against_create' %}?id={{ category.id }}"
                                        hx-target="#modal-container">
                                    <i class="sap-icon sap-icon-edit"></i>
                                </button>
                                <button class="sap-btn sap-btn-transparent sap-btn-icon sap-btn-danger" 
                                        title="Delete"
                                        onclick="confirmDelete('{{ category.description }}', '{{ category.id }}', 'payment')">
                                    <i class="sap-icon sap-icon-delete"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="sap-table-cell sap-table-cell-empty">
                            <div class="sap-empty-state">
                                <i class="sap-icon sap-icon-payment"></i>
                                <h3>No Payment Categories</h3>
                                <p>Create your first payment category to get started.</p>
                                <button class="sap-btn sap-btn-primary" 
                                        hx-get="{% url 'accounts:payment_against_create' %}"
                                        hx-target="#modal-container">
                                    <i class="sap-icon sap-icon-add"></i>
                                    Add Payment Category
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Receipt Categories Section -->
    <div class="sap-section">
        <div class="sap-section-header">
            <h2 class="sap-section-title">Receipt Against Categories</h2>
            <div class="sap-section-toolbar">
                <div class="sap-search-field">
                    <input type="text" 
                           class="sap-input" 
                           placeholder="Search receipt categories..."
                           hx-get="{% url 'accounts:payment_receipt_against_list' %}"
                           hx-target="#receipt-categories-table"
                           hx-trigger="keyup changed delay:300ms"
                           name="receipt_search">
                    <i class="sap-icon sap-icon-search"></i>
                </div>
            </div>
        </div>

        <div class="sap-table-container">
            <table class="sap-table" id="receipt-categories-table">
                <thead>
                    <tr>
                        <th class="sap-table-header">Category Name</th>
                        <th class="sap-table-header">Description</th>
                        <th class="sap-table-header">Status</th>
                        <th class="sap-table-header">Created Date</th>
                        <th class="sap-table-header sap-table-header-actions">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for category in receipt_categories %}
                    <tr class="sap-table-row">
                        <td class="sap-table-cell">
                            <span class="sap-text-strong">{{ category.description }}</span>
                        </td>
                        <td class="sap-table-cell">
                            <span class="sap-text-muted">{{ category.description|default:"-" }}</span>
                        </td>
                        <td class="sap-table-cell">
                            <span class="sap-status sap-status-success">Active</span>
                        </td>
                        <td class="sap-table-cell">
                            <span class="sap-text-muted">-</span>
                        </td>
                        <td class="sap-table-cell sap-table-cell-actions">
                            <div class="sap-btn-group">
                                <button class="sap-btn sap-btn-transparent sap-btn-icon" 
                                        title="Edit"
                                        hx-get="{% url 'accounts:receipt_against_create' %}?id={{ category.id }}"
                                        hx-target="#modal-container">
                                    <i class="sap-icon sap-icon-edit"></i>
                                </button>
                                <button class="sap-btn sap-btn-transparent sap-btn-icon sap-btn-danger" 
                                        title="Delete"
                                        onclick="confirmDelete('{{ category.description }}', '{{ category.id }}', 'receipt')">
                                    <i class="sap-icon sap-icon-delete"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="sap-table-cell sap-table-cell-empty">
                            <div class="sap-empty-state">
                                <i class="sap-icon sap-icon-receipt"></i>
                                <h3>No Receipt Categories</h3>
                                <p>Create your first receipt category to get started.</p>
                                <button class="sap-btn sap-btn-primary" 
                                        hx-get="{% url 'accounts:receipt_against_create' %}"
                                        hx-target="#modal-container">
                                    <i class="sap-icon sap-icon-add"></i>
                                    Add Receipt Category
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal Container -->
<div id="modal-container"></div>

<script>
function confirmDelete(categoryName, categoryId, type) {
    if (confirm(`Are you sure you want to delete the ${type} category "${categoryName}"?`)) {
        // Implement delete functionality via HTMX
        htmx.ajax('DELETE', `/accounts/masters/${type}-against/${categoryId}/delete/`, {
            target: type === 'payment' ? '#payment-categories-table' : '#receipt-categories-table',
            swap: 'outerHTML'
        });
    }
}
</script>
{% endblock %}