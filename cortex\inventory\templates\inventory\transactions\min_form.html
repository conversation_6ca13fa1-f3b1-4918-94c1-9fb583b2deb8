{% extends 'core/base.html' %}
{% load static %}

{% block title %}
    {% if object %}Edit MIN - {{ object.min_number }}{% else %}New Material Issue Note{% endif %}
{% endblock %}

{% block content %}
<div class="bg-white shadow-sm">
    <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
        <div class="flex justify-between items-center">
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                    {% if object %}Edit MIN - {{ object.min_number }}{% else %}New Material Issue Note{% endif %}
                </h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                    Issue materials against approved MRS
                </p>
            </div>
            <a href="{% url 'inventory:min_list' %}" 
               class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to List
            </a>
        </div>
    </div>

    <div class="px-4 py-5 sm:px-6">
        <!-- Form Errors -->
        {% if form.non_field_errors %}
            <div class="rounded-md bg-red-50 p-4 mb-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">Form Errors</h3>
                        <div class="mt-2 text-sm text-red-700">
                            <ul class="list-disc pl-5 space-y-1">
                                {% for error in form.non_field_errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        <form method="post" hx-post="{% if object %}{% url 'inventory:min_update' object.pk %}{% else %}{% url 'inventory:min_create' %}{% endif %}" 
              hx-target="#form-container" hx-swap="outerHTML">
            <div id="form-container">
                {% csrf_token %}
                
                <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <!-- MIN Date -->
                    <div class="sm:col-span-2">
                        <label for="{{ form.min_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            MIN Date <span class="text-red-500">*</span>
                        </label>
                        {{ form.min_date }}
                        {% if form.min_date.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.min_date.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- MRS -->
                    <div class="sm:col-span-4">
                        <label for="{{ form.mrs.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Material Requisition Slip (MRS) <span class="text-red-500">*</span>
                        </label>
                        {{ form.mrs }}
                        {% if form.mrs.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.mrs.errors.0 }}</p>
                        {% endif %}
                        <div id="mrs-details" class="mt-2"></div>
                    </div>

                    <!-- Department ID -->
                    <div class="sm:col-span-2">
                        <label for="{{ form.department_id.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Department ID
                        </label>
                        {{ form.department_id }}
                        {% if form.department_id.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.department_id.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- Work Order Number -->
                    <div class="sm:col-span-2">
                        <label for="{{ form.work_order_number.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Work Order Number
                        </label>
                        {{ form.work_order_number }}
                        {% if form.work_order_number.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.work_order_number.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- Project Code -->
                    <div class="sm:col-span-2">
                        <label for="{{ form.project_code.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Project Code
                        </label>
                        {{ form.project_code }}
                        {% if form.project_code.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.project_code.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- Issue Type -->
                    <div class="sm:col-span-2">
                        <label for="{{ form.issue_type.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Issue Type <span class="text-red-500">*</span>
                        </label>
                        {{ form.issue_type }}
                        {% if form.issue_type.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.issue_type.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- Issued To Person -->
                    <div class="sm:col-span-3">
                        <label for="{{ form.issued_to_person.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Issued To (Person)
                        </label>
                        {{ form.issued_to_person }}
                        {% if form.issued_to_person.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.issued_to_person.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- Issued To Department -->
                    <div class="sm:col-span-3">
                        <label for="{{ form.issued_to_department.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Issued To (Department)
                        </label>
                        {{ form.issued_to_department }}
                        {% if form.issued_to_department.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.issued_to_department.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- Return Expected Date -->
                    <div class="sm:col-span-3">
                        <label for="{{ form.return_expected_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Return Expected Date
                        </label>
                        {{ form.return_expected_date }}
                        {% if form.return_expected_date.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.return_expected_date.errors.0 }}</p>
                        {% endif %}
                        <p class="mt-1 text-sm text-gray-500">Required for returnable items</p>
                    </div>

                    <!-- Remarks -->
                    <div class="sm:col-span-6">
                        <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Remarks
                        </label>
                        {{ form.remarks }}
                        {% if form.remarks.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.remarks.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="pt-8">
                    <div class="flex justify-end space-x-3">
                        <a href="{% url 'inventory:min_list' %}" 
                           class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            {% if object %}Update MIN{% else %}Create MIN{% endif %}
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
// Auto-enable/disable return expected date based on issue type
document.addEventListener('DOMContentLoaded', function() {
    const issueTypeField = document.getElementById('{{ form.issue_type.id_for_label }}');
    const returnDateField = document.getElementById('{{ form.return_expected_date.id_for_label }}');
    
    function toggleReturnDate() {
        if (issueTypeField.value === 'RETURNABLE') {
            returnDateField.required = true;
            returnDateField.closest('div').classList.remove('opacity-50');
        } else {
            returnDateField.required = false;
            returnDateField.value = '';
            returnDateField.closest('div').classList.add('opacity-50');
        }
    }
    
    issueTypeField.addEventListener('change', toggleReturnDate);
    toggleReturnDate(); // Initial call
});
</script>
{% endblock %}