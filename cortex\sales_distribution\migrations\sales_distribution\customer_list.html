<!-- sales_distribution/templates/sales_distribution/customer_list.html -->
<!-- Professional Customer List - SAP S/4HANA Inspired Design -->
<!-- Replaces ASP.NET CustomerMaster_Edit.aspx functionality -->

{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }} - Cortex{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-sap-purple-500 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="users" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Customer Master</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Search and manage customer database with real-time filtering</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <div class="text-right mr-4 px-4 py-2 bg-sap-gray-50 rounded-lg border border-sap-gray-200">
                    <p class="text-2xs font-medium text-sap-gray-500 uppercase tracking-wide">Total Customers</p>
                    <p class="text-lg font-semibold text-sap-purple-600" id="customer-count">{{ customers|length }}</p>
                </div>
                <a href="{% url 'sales_distribution:customer_new' %}" 
                   class="inline-flex items-center px-4 py-2.5 border border-sap-purple-300 rounded-lg text-sm font-medium text-white bg-sap-purple-600 hover:bg-sap-purple-700 transition-all duration-200">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    Add Customer
                </a>
                <a href="{% url 'sales_distribution:dashboard' %}" 
                   class="inline-flex items-center px-4 py-2.5 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 hover:border-sap-blue-300 transition-all duration-200">
                    <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-2 py-4 space-y-4">
    
    <!-- Real-time Search Card - Replaces ASP.NET TxtSearchValue AutoComplete -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="search" class="w-4 h-4 text-sap-blue-600"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-sap-gray-800">Customer Search</h3>
                        <p class="text-sm text-sap-gray-600">Search customers by name, ID, or address</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="px-6 py-4">
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i data-lucide="search" class="w-4 h-4 text-sap-gray-400"></i>
                </div>
                <input type="text" 
                       id="real-time-search"
                       name="search" 
                       placeholder="Search customers in real-time..." 
                       value="{{ request.GET.search|default:'' }}"
                       hx-get="{% url 'sales_distribution:customer_list' %}"
                       hx-target="#customer-table"
                       hx-swap="outerHTML"
                       hx-trigger="keyup changed delay:300ms, search"
                       class="block w-full pl-10 pr-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm placeholder-sap-gray-500 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
            </div>
            <div class="mt-2 text-xs text-sap-gray-500">
                <i data-lucide="info" class="w-3 h-3 inline mr-1"></i>
                Search results update automatically as you type - no need to press Enter
            </div>
        </div>
    </div>

    <!-- Customer Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Customer List</h3>
                <div class="text-sm text-sap-gray-600" id="search-status">
                    {% if request.GET.search %}
                        Showing {{ customers|length }} customers matching "{{ request.GET.search }}"
                    {% else %}
                        Showing {{ customers|length }} customers
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div id="customer-table">
            {% include 'sales_distribution/partials/customer_table.html' %}
        </div>
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="text-sm text-sap-gray-600">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ paginator.count }} results
                </div>
                <div class="flex space-x-2">
                    {% if page_obj.has_previous %}
                        <a href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            First
                        </a>
                        <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Previous
                        </a>
                    {% endif %}
                    
                    <span class="px-3 py-2 bg-sap-purple-50 border border-sap-purple-200 rounded-lg text-sm font-medium text-sap-purple-600">
                        Page {{ page_obj.number }} of {{ paginator.num_pages }}
                    </span>
                    
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Next
                        </a>
                        <a href="?page={{ paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Last
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

</div>

<script>
// Clear search function
function clearSearch() {
    document.getElementById('real-time-search').value = '';
    // Trigger HTMX to reload with cleared filters
    htmx.trigger('#real-time-search', 'keyup');
}

// Update search status dynamically
document.addEventListener('htmx:afterSwap', function(e) {
    if (e.target.id === 'customer-table') {
        const searchValue = document.getElementById('real-time-search').value;
        const statusElement = document.getElementById('search-status');
        const rows = e.target.querySelectorAll('tbody tr');
        
        if (searchValue) {
            statusElement.textContent = `Showing ${rows.length} customers matching "${searchValue}"`;
        } else {
            statusElement.textContent = `Showing ${rows.length} customers`;
        }
    }
});
</script>
{% endblock %}