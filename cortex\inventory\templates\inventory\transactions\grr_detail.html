{% extends 'core/base.html' %}
{% load static %}

{% block title %}GRR {{ grr.grr_number }} - Details{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-gray-700 text-white px-4 py-2">
        <h1 class="text-lg font-bold">Goods Received Receipt [GRR] - {{ grr.grr_number }}</h1>
    </div>
    
    <div class="p-4">
        <!-- GRR Header Information -->
        <div class="bg-white rounded-lg shadow-sm border mb-4">
            <div class="p-4 border-b">
                <h2 class="text-lg font-semibold text-gray-900">GRR Details</h2>
            </div>
            <div class="p-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-500">GRR Number</label>
                        <p class="text-sm text-gray-900">{{ grr.grr_number }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">GRR Date</label>
                        <p class="text-sm text-gray-900">{{ grr.grr_date|date:"d M Y" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Financial Year</label>
                        <p class="text-sm text-gray-900">{{ grr.fin_year|default:"N/A" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">GIN Number</label>
                        <p class="text-sm text-gray-900">
                            {% if grr.gin_number %}
                                <a href="{% url 'inventory:gin_detail' grr.GINId %}" class="text-blue-600 hover:underline">
                                    {{ grr.gin_number }}
                                </a>
                            {% else %}
                                N/A
                            {% endif %}
                        </p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">PO Number</label>
                        <p class="text-sm text-gray-900">{{ grr.po_number|default:"N/A" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Supplier</label>
                        <p class="text-sm text-gray-900">{{ grr.supplier_name }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Invoice Information -->
        <div class="bg-white rounded-lg shadow-sm border mb-4">
            <div class="p-4 border-b">
                <h2 class="text-lg font-semibold text-gray-900">Invoice Details</h2>
            </div>
            <div class="p-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Tax Invoice Number</label>
                        <p class="text-sm text-gray-900">{{ grr.tax_invoice_number|default:"Not provided" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Tax Invoice Date</label>
                        <p class="text-sm text-gray-900">
                            {% if grr.tax_invoice_date %}
                                {{ grr.tax_invoice_date|date:"d M Y" }}
                            {% else %}
                                Not provided
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Challan Information -->
        <div class="bg-white rounded-lg shadow-sm border mb-4">
            <div class="p-4 border-b">
                <h2 class="text-lg font-semibold text-gray-900">Challan Details</h2>
            </div>
            <div class="p-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Challan Number</label>
                        <p class="text-sm text-gray-900">{{ grr.challan_number|default:"Not provided" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Challan Date</label>
                        <p class="text-sm text-gray-900">
                            {% if grr.challan_date %}
                                {{ grr.challan_date|date:"d M Y" }}
                            {% else %}
                                Not provided
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quality Check Information -->
        <div class="bg-white rounded-lg shadow-sm border mb-4">
            <div class="p-4 border-b">
                <h2 class="text-lg font-semibold text-gray-900">Quality Check Status</h2>
            </div>
            <div class="p-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Status</label>
                        {% if grr.status == 'QUALITY_PENDING' %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                Quality Pending
                            </span>
                        {% elif grr.status == 'QUALITY_COMPLETED' %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Quality Completed
                            </span>
                        {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                {{ grr.status|default:"Unknown" }}
                            </span>
                        {% endif %}
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Quality Checked By</label>
                        <p class="text-sm text-gray-900">
                            {% if grr.quality_checked_by_id %}
                                User {{ grr.quality_checked_by_id }}
                            {% else %}
                                Not checked
                            {% endif %}
                        </p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Quality Check Date</label>
                        <p class="text-sm text-gray-900">
                            {% if grr.quality_check_date %}
                                {{ grr.quality_check_date|date:"d M Y" }}
                            {% else %}
                                Not checked
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="bg-white rounded-lg shadow-sm border">
            <div class="p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'inventory:grr_list' %}" 
                           class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded text-sm font-medium transition-colors">
                            Back to GRR List
                        </a>
                        
                        {% if can_edit %}
                        <a href="{% url 'inventory:grr_update' grr.Id %}" 
                           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm font-medium transition-colors">
                            Edit GRR
                        </a>
                        {% endif %}
                        
                        <a href="{% url 'inventory:grr_print' grr.Id %}" 
                           class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded text-sm font-medium transition-colors">
                            Print GRR
                        </a>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        {% if grr.status == 'QUALITY_PENDING' %}
                        <button type="button" 
                                hx-post="{% url 'inventory:grr_quality_check' grr.Id %}"
                                hx-confirm="Complete quality check for {{ grr.grr_number }}?"
                                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm font-medium transition-colors">
                            Complete Quality Check
                        </button>
                        {% endif %}
                        
                        {% if can_approve %}
                        <button type="button" 
                                hx-post="{% url 'inventory:grr_approve' grr.Id %}"
                                hx-confirm="Approve {{ grr.grr_number }}?"
                                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm font-medium transition-colors">
                            Approve GRR
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}