<!-- accounts/templates/accounts/transactions/proforma_invoice_with_items_form.html -->
<!-- Enhanced Proforma Invoice Form with Dynamic Item Grid and HTMX -->
<!-- Task Group 5: Sales & Service Tax Invoicing - Proforma Invoice Templates with HTMX (Task 5.14) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{{ page_title }} - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-purple-600 to-sap-purple-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="file-plus" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">{{ page_title }}</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Professional proforma invoice generation with validity management</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:proforma_invoice_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
                {% if form_action == "Update" and proforma_invoice %}
                <a href="{% url 'accounts:proforma_invoice_detail' proforma_invoice.pk %}" 
                   class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="eye" class="w-4 h-4 inline mr-2"></i>
                    View Proforma
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6" x-data="proformaInvoiceManager()">
    
    <!-- Proforma Invoice Alert -->
    <div class="mb-6 bg-sap-purple-50 border border-sap-purple-200 rounded-lg p-4">
        <div class="flex">
            <i data-lucide="info" class="w-5 h-5 text-sap-purple-400 mr-3"></i>
            <div class="text-sm text-sap-purple-800">
                <p class="font-medium mb-1">Proforma Invoice Guidelines</p>
                <p>This document is a preliminary invoice sent to buyers before shipment of goods. It includes terms, conditions, and validity period. It is not a demand for payment.</p>
            </div>
        </div>
    </div>

    <form method="post" class="space-y-8" @submit="submitProformaInvoice">
        {% csrf_token %}
        
        <!-- Error Messages -->
        {% if form.non_field_errors %}
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <i data-lucide="alert-circle" class="w-5 h-5 text-red-400 mr-3"></i>
                <div>
                    <h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                    <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                        {% for error in form.non_field_errors %}
                        <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
            
            <!-- Main Proforma Form -->
            <div class="xl:col-span-2 space-y-6">
                
                <!-- Proforma Header Information -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                            <i data-lucide="file-text" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                            Proforma Invoice Details
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Customer Name -->
                            <div>
                                <label for="{{ form.customer_name.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.customer_name.label }}
                                    <span class="text-red-500">*</span>
                                </label>
                                {{ form.customer_name|add_class:"block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500" }}
                                {% if form.customer_name.errors %}
                                <p class="text-sm text-red-600 mt-1">{{ form.customer_name.errors.0 }}</p>
                                {% endif %}
                            </div>

                            <!-- PO Details -->
                            <div>
                                <label for="{{ form.po_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.po_no.label }}
                                </label>
                                {{ form.po_no|add_class:"block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500" }}
                            </div>

                            <!-- PO Date -->
                            <div>
                                <label for="{{ form.po_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.po_date.label }}
                                </label>
                                {{ form.po_date|add_class:"block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500" }}
                            </div>

                            <!-- Work Order Number -->
                            <div>
                                <label for="{{ form.work_order_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.work_order_no.label }}
                                </label>
                                {{ form.work_order_no|add_class:"block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500" }}
                            </div>

                            <!-- Validity Days -->
                            <div class="md:col-span-2">
                                <label for="{{ form.validity_days.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.validity_days.label }}
                                    <span class="text-sap-gray-500 text-xs">(Default: 30 days)</span>
                                </label>
                                <div class="flex items-center space-x-4">
                                    {{ form.validity_days|add_class:"block w-32 px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500" }}
                                    <span class="text-sm text-sap-gray-600">days from issue date</span>
                                    <div class="text-sm text-sap-purple-600" x-show="validityDays > 0">
                                        <span class="font-medium">Valid until:</span>
                                        <span x-text="getValidityDate()"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dynamic Proforma Items Grid -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                                <i data-lucide="package" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                                Proforma Items
                            </h3>
                            <div class="flex items-center space-x-2">
                                <button type="button" @click="addBulkItems" 
                                        class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                                    <i data-lucide="upload" class="w-4 h-4 inline mr-2"></i>
                                    Bulk Add
                                </button>
                                <button type="button" @click="addNewItem" 
                                        class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                                    Add Item
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="p-6">
                        <!-- Items Grid -->
                        <div class="overflow-x-auto">
                            <table class="min-w-full">
                                <thead>
                                    <tr class="bg-sap-gray-50">
                                        <th class="px-3 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">#</th>
                                        <th class="px-3 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Item Details</th>
                                        <th class="px-3 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Specifications</th>
                                        <th class="px-3 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Pricing</th>
                                        <th class="px-3 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-sap-gray-200">
                                    <!-- Dynamic Item Rows -->
                                    <template x-for="(item, index) in proformaItems" :key="index">
                                        <tr class="hover:bg-sap-gray-50">
                                            <td class="px-3 py-4 text-sm font-medium text-sap-gray-900" x-text="index + 1"></td>
                                            <td class="px-3 py-4">
                                                <div class="space-y-2">
                                                    <input type="text" 
                                                           x-model="item.item_code"
                                                           @blur="validateItem(index)"
                                                           class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-purple-500"
                                                           placeholder="Item Code">
                                                    <textarea x-model="item.item_description"
                                                              rows="2"
                                                              class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-purple-500"
                                                              placeholder="Item Description"></textarea>
                                                </div>
                                            </td>
                                            <td class="px-3 py-4">
                                                <div class="space-y-2">
                                                    <div class="grid grid-cols-2 gap-2">
                                                        <input type="text" 
                                                               x-model="item.uom"
                                                               class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-purple-500"
                                                               placeholder="UOM">
                                                        <input type="number" 
                                                               x-model.number="item.quantity"
                                                               @input="calculateLineTotal(index)"
                                                               step="0.001" min="0"
                                                               class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-purple-500"
                                                               placeholder="Qty">
                                                    </div>
                                                    <textarea x-model="item.specifications"
                                                              rows="2"
                                                              class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-purple-500"
                                                              placeholder="Technical specifications..."></textarea>
                                                </div>
                                            </td>
                                            <td class="px-3 py-4">
                                                <div class="space-y-2">
                                                    <input type="number" 
                                                           x-model.number="item.rate"
                                                           @input="calculateLineTotal(index)"
                                                           step="0.01" min="0"
                                                           class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-purple-500"
                                                           placeholder="Unit Price">
                                                    <div class="text-xs text-sap-gray-600">
                                                        Amount: <span class="font-medium" x-text="formatCurrency(item.amount)"></span>
                                                    </div>
                                                    <input type="number" 
                                                           x-model.number="item.tax_percentage"
                                                           @input="calculateLineTotal(index)"
                                                           step="0.01" min="0" max="100"
                                                           class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-purple-500"
                                                           placeholder="Tax %">
                                                    <div class="text-sm font-bold text-sap-purple-600">
                                                        Total: <span x-text="formatCurrency(item.line_total)"></span>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-3 py-4 text-right">
                                                <div class="flex items-center justify-end space-x-1">
                                                    <button type="button" @click="duplicateItem(index)"
                                                            class="text-sap-blue-600 hover:text-sap-blue-700 p-1" title="Duplicate Item">
                                                        <i data-lucide="copy" class="w-4 h-4"></i>
                                                    </button>
                                                    <button type="button" @click="removeItem(index)"
                                                            class="text-red-600 hover:text-red-700 p-1" title="Remove Item">
                                                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </template>
                                    
                                    <!-- Empty State -->
                                    <tr x-show="proformaItems.length === 0">
                                        <td colspan="5" class="px-6 py-8 text-center">
                                            <div class="text-center">
                                                <i data-lucide="package" class="w-12 h-12 text-sap-gray-400 mx-auto mb-4"></i>
                                                <h3 class="text-sm font-medium text-sap-gray-900 mb-2">No proforma items added</h3>
                                                <p class="text-sm text-sap-gray-600 mb-4">Start building your proforma invoice by adding items.</p>
                                                <button type="button" @click="addNewItem"
                                                        class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                                                    Add First Item
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Proforma Summary and Actions -->
            <div class="space-y-6">
                
                <!-- Proforma Summary -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                            <i data-lucide="calculator" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                            Proforma Summary
                        </h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <!-- Basic Amount -->
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-sap-gray-600">Basic Amount:</span>
                            <span class="font-medium" x-text="formatCurrency(totals.basic_amount)"></span>
                        </div>
                        
                        <!-- Total Tax -->
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-sap-gray-600">Total Tax:</span>
                            <span class="font-medium" x-text="formatCurrency(totals.total_tax)"></span>
                        </div>
                        
                        <!-- Grand Total -->
                        <div class="border-t border-sap-gray-200 pt-4">
                            <div class="flex justify-between items-center bg-sap-purple-50 rounded-lg p-3">
                                <span class="text-lg font-bold text-sap-gray-800">Grand Total:</span>
                                <span class="text-xl font-bold text-sap-purple-600" x-text="formatCurrency(totals.grand_total)"></span>
                            </div>
                        </div>
                        
                        <!-- Validity Information -->
                        <div class="border-t border-sap-gray-200 pt-4">
                            <div class="bg-sap-yellow-50 rounded-lg p-3">
                                <div class="text-xs font-medium text-sap-yellow-800 mb-1">Validity Period</div>
                                <div class="text-sm text-sap-yellow-700" x-text="getValidityText()"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Terms and Conditions -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <h3 class="text-lg font-medium text-sap-gray-800">Terms & Conditions</h3>
                    </div>
                    <div class="p-6">
                        <textarea x-model="termsConditions" rows="6"
                                  class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500"
                                  placeholder="Enter standard terms and conditions for this proforma invoice..."></textarea>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <h3 class="text-lg font-medium text-sap-gray-800">Quick Actions</h3>
                    </div>
                    <div class="p-6 space-y-3">
                        <button type="button" @click="convertToInvoice"
                                :disabled="proformaItems.length === 0"
                                class="w-full bg-sap-green-100 hover:bg-sap-green-200 text-sap-green-800 px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                            <i data-lucide="file-text" class="w-4 h-4 inline mr-2"></i>
                            Convert to Invoice
                        </button>
                        <button type="button" @click="loadStandardTerms"
                                class="w-full bg-sap-blue-100 hover:bg-sap-blue-200 text-sap-blue-800 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="clipboard" class="w-4 h-4 inline mr-2"></i>
                            Load Standard Terms
                        </button>
                        <button type="button" @click="generatePDF"
                                :disabled="proformaItems.length === 0"
                                class="w-full bg-sap-purple-100 hover:bg-sap-purple-200 text-sap-purple-800 px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                            <i data-lucide="file-down" class="w-4 h-4 inline mr-2"></i>
                            Download PDF
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
            <div class="flex items-center space-x-4">
                <a href="{% url 'accounts:proforma_invoice_list' %}" 
                   class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-6 py-2 rounded-lg font-medium transition-colors">
                    Cancel
                </a>
                <button type="button" @click="saveDraft"
                        class="bg-sap-yellow-600 hover:bg-sap-yellow-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="save" class="w-4 h-4 inline mr-2"></i>
                    Save as Draft
                </button>
            </div>
            
            <div class="flex items-center space-x-3">
                <button type="button" @click="previewProforma"
                        :disabled="proformaItems.length === 0"
                        class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                    <i data-lucide="eye" class="w-4 h-4 inline mr-2"></i>
                    Preview
                </button>
                <button type="submit" 
                        class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                        :disabled="proformaItems.length === 0">
                    <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                    {{ form_action }} Proforma
                </button>
            </div>
        </div>

        <!-- Hidden Fields for Items Data -->
        <input type="hidden" name="items_data" :value="JSON.stringify(proformaItems)">
        <input type="hidden" name="terms_conditions" :value="termsConditions">
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
function proformaInvoiceManager() {
    return {
        proformaItems: [
            {% if existing_items %}
                {% for item in existing_items %}
                {
                    item_code: '{{ item.item_code|default:"" }}',
                    item_description: '{{ item.item_description|default:"" }}',
                    uom: '{{ item.uom|default:"" }}',
                    quantity: {{ item.quantity|default:0 }},
                    rate: {{ item.rate|default:0 }},
                    tax_percentage: {{ item.vat_percentage|default:0 }},
                    specifications: '{{ item.specifications|default:"" }}',
                    amount: {{ item.amount|default:0 }},
                    tax_amount: {{ item.vat_amount|default:0 }},
                    line_total: {{ item.line_total|default:0 }}
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
            {% endif %}
        ],
        
        validityDays: {{ form.validity_days.value|default:30 }},
        
        termsConditions: `1. Prices are valid for ${this.validityDays} days from the date of this proforma invoice.
2. Payment terms: 50% advance, balance on delivery.
3. Delivery period: 4-6 weeks from receipt of confirmed order and advance payment.
4. This proforma invoice is subject to our standard terms and conditions.
5. All prices are exclusive of applicable taxes unless otherwise mentioned.`,
        
        totals: {
            basic_amount: 0,
            total_tax: 0,
            grand_total: 0
        },
        
        init() {
            lucide.createIcons();
            this.calculateAllTotals();
        },
        
        addNewItem() {
            this.proformaItems.push({
                item_code: '',
                item_description: '',
                uom: 'Nos',
                quantity: 1,
                rate: 0,
                tax_percentage: 0,
                specifications: '',
                amount: 0,
                tax_amount: 0,
                line_total: 0
            });
        },
        
        removeItem(index) {
            this.proformaItems.splice(index, 1);
            this.calculateAllTotals();
        },
        
        duplicateItem(index) {
            const item = JSON.parse(JSON.stringify(this.proformaItems[index]));
            this.proformaItems.push(item);
            this.calculateAllTotals();
        },
        
        calculateLineTotal(index) {
            const item = this.proformaItems[index];
            
            // Calculate basic amount
            item.amount = (item.quantity || 0) * (item.rate || 0);
            
            // Calculate tax amount
            item.tax_amount = (item.amount * (item.tax_percentage || 0)) / 100;
            
            // Calculate line total
            item.line_total = item.amount + item.tax_amount;
            
            this.calculateAllTotals();
        },
        
        calculateAllTotals() {
            this.totals.basic_amount = this.proformaItems.reduce((sum, item) => sum + (item.amount || 0), 0);
            this.totals.total_tax = this.proformaItems.reduce((sum, item) => sum + (item.tax_amount || 0), 0);
            this.totals.grand_total = this.totals.basic_amount + this.totals.total_tax;
        },
        
        getValidityDate() {
            const today = new Date();
            const validUntil = new Date(today.getTime() + (this.validityDays * 24 * 60 * 60 * 1000));
            return validUntil.toLocaleDateString();
        },
        
        getValidityText() {
            return `Valid for ${this.validityDays} days (until ${this.getValidityDate()})`;
        },
        
        formatCurrency(amount) {
            return '₹' + (amount || 0).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        },
        
        validateItem(index) {
            const item = this.proformaItems[index];
            // Add validation logic here
        },
        
        addBulkItems() {
            console.log('Adding bulk items...');
            // Implementation for bulk item addition
        },
        
        convertToInvoice() {
            if (this.proformaItems.length === 0) {
                alert('Please add items before converting to invoice.');
                return;
            }
            console.log('Converting to invoice...');
            // Implementation to convert proforma to actual invoice
        },
        
        loadStandardTerms() {
            this.termsConditions = `1. Prices are valid for ${this.validityDays} days from the date of this proforma invoice.
2. Payment terms: 50% advance, balance on delivery.
3. Delivery period: 4-6 weeks from receipt of confirmed order and advance payment.
4. This proforma invoice is subject to our standard terms and conditions.
5. All prices are exclusive of applicable taxes unless otherwise mentioned.
6. Goods once sold will not be taken back or exchanged.
7. Any dispute arising will be subject to local jurisdiction only.`;
        },
        
        generatePDF() {
            if (this.proformaItems.length === 0) {
                alert('Please add items before generating PDF.');
                return;
            }
            console.log('Generating PDF...');
            // Implementation for PDF generation
        },
        
        saveDraft() {
            console.log('Saving draft...');
            // Implementation for saving draft
        },
        
        previewProforma() {
            if (this.proformaItems.length === 0) {
                alert('Please add items before previewing.');
                return;
            }
            console.log('Previewing proforma...');
            // Implementation for preview
        },
        
        submitProformaInvoice(event) {
            if (this.proformaItems.length === 0) {
                event.preventDefault();
                alert('Please add at least one proforma item.');
                return false;
            }
            
            return true;
        }
    }
}

// Initialize lucide icons on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}