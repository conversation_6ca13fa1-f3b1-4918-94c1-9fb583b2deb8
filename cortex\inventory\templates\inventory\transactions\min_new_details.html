{% extends 'core/base.html' %}
{% load static %}

{% block title %}Material Issue Note [MIN] - New{% endblock %}

{% block content %}
<div class="bg-white shadow-sm">
    <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
        <div class="flex justify-between items-center">
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                    Material Issue Note [MIN] - New
                </h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                    Select items and quantities to issue from MRS {{ mrs.mrs_no }}
                </p>
            </div>
            <a href="{% url 'inventory:min_new_search' %}" 
               class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Search
            </a>
        </div>
    </div>

    <!-- MRS Information -->
    <div class="px-4 py-4 bg-gray-50 border-b border-gray-200">
        <dl class="grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-4">
            <div>
                <dt class="text-sm font-medium text-gray-500">MRS Number</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ mrs.mrs_number|default:"N/A" }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Financial Year</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ mrs.financial_year.finyear|default:"N/A" }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Date</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ mrs.sys_date|default:"N/A" }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Generated By</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ mrs.session_id|default:"N/A" }}</dd>
            </div>
        </dl>
    </div>

    <!-- Item Selection Form -->
    <div class="px-4 py-5 sm:px-6">
        <form method="post" id="min-generation-form">
            {% csrf_token %}
            
            {% if mrs_items %}
            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                <table class="w-full table-auto divide-y divide-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide sm:pl-6">
                                <input type="checkbox" id="select-all" 
                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            </th>
                            <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                                SN
                            </th>
                            <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                                Item Code
                            </th>
                            <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                                Description
                            </th>
                            <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                                UOM
                            </th>
                            <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                                BG Group
                            </th>
                            <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                                WO No
                            </th>
                            <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                                Stk Qty
                            </th>
                            <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                                Req. Qty
                            </th>
                            <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                                Issued Qty
                            </th>
                            <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                                Remarks
                            </th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 bg-white">
                        {% for item in mrs_items %}
                        <tr class="hover:bg-gray-50">
                            <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                                <input type="checkbox" name="selected_items" value="{{ item.detail.id }}" 
                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded item-checkbox">
                            </td>
                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                {{ forloop.counter }}
                            </td>
                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                {{ item.item_code }}
                            </td>
                            <td class="px-3 py-4 text-sm text-gray-500">
                                <div class="max-w-xs truncate" title="{{ item.description }}">
                                    {{ item.description }}
                                </div>
                            </td>
                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                {{ item.uom }}
                            </td>
                            <td class="px-3 py-4 text-sm text-gray-500">
                                <div class="max-w-xs truncate" title="{{ item.detail.department_id|default:'FABRICATION-SAPL Plant 2' }}">
                                    {{ item.detail.department_id|default:"FABRICATION-SAPL Plant 2" }}
                                </div>
                            </td>
                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                {{ item.detail.work_order_number|default:"NA" }}
                            </td>
                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                {{ item.stock_qty|floatformat:2 }}
                            </td>
                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                {{ item.req_qty|floatformat:2 }}
                            </td>
                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                <input type="number" name="issued_qty_{{ item.detail.id }}" 
                                       value="{{ item.req_qty|floatformat:2 }}" 
                                       step="0.01" min="0" max="{{ item.req_qty }}"
                                       class="w-20 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            </td>
                            <td class="px-3 py-4 text-sm text-gray-500">
                                <input type="text" name="remarks_{{ item.detail.id }}" 
                                       placeholder="Enter remarks"
                                       class="w-32 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Action Buttons -->
            <div class="mt-6 flex justify-center space-x-4">
                <button type="submit" 
                        class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Generate MIN
                </button>
                <a href="{% url 'inventory:min_new_search' %}" 
                   class="inline-flex items-center px-6 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Cancel
                </a>
            </div>

            {% else %}
            <div class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No items found</h3>
                <p class="mt-1 text-sm text-gray-500">This MRS has no line items to issue.</p>
            </div>
            {% endif %}
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select All functionality
    const selectAllCheckbox = document.getElementById('select-all');
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');
    
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            itemCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
        });
    }
    
    // Update issued quantity when checkbox is clicked
    itemCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const row = checkbox.closest('tr');
            const issuedQtyInput = row.querySelector('input[name^="issued_qty_"]');
            const reqQtyCell = row.querySelector('td:nth-child(9)').textContent.trim();
            
            if (checkbox.checked) {
                issuedQtyInput.value = parseFloat(reqQtyCell).toFixed(2);
            } else {
                issuedQtyInput.value = '0.00';
            }
        });
    });
    
    // Form validation
    const form = document.getElementById('min-generation-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const checkedItems = document.querySelectorAll('.item-checkbox:checked');
            
            if (checkedItems.length === 0) {
                e.preventDefault();
                alert('Please select at least one item to issue.');
                return false;
            }
            
            // Validate issued quantities
            let hasInvalidQuantity = false;
            checkedItems.forEach(checkbox => {
                const row = checkbox.closest('tr');
                const issuedQtyInput = row.querySelector('input[name^="issued_qty_"]');
                const issuedQty = parseFloat(issuedQtyInput.value) || 0;
                
                if (issuedQty <= 0) {
                    hasInvalidQuantity = true;
                }
            });
            
            if (hasInvalidQuantity) {
                e.preventDefault();
                alert('Please enter valid quantities for all selected items.');
                return false;
            }
        });
    }
});
</script>
{% endblock %}