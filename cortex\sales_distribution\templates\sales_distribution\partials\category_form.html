<!-- sales_distribution/templates/sales_distribution/partials/category_form.html -->
<!-- Category creation form partial -->

<form hx-post="{% url 'sales_distribution:category_create' %}" 
      hx-target="#category-table" 
      hx-swap="outerHTML"
      class="space-y-4">
    {% csrf_token %}
    
    {% if error %}
    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex">
            <i data-lucide="alert-circle" class="w-5 h-5 text-red-400 mr-2 mt-0.5"></i>
            <div class="text-sm text-red-600">{{ error }}</div>
        </div>
    </div>
    {% endif %}
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
            <label for="{{ form.cname.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                Category Name <span class="text-red-500">*</span>
            </label>
            {{ form.cname }}
            {% if form.cname.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.cname.errors.0 }}</p>
            {% endif %}
        </div>
        <div>
            <label for="{{ form.symbol.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                Symbol
            </label>
            {{ form.symbol }}
            {% if form.symbol.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.symbol.errors.0 }}</p>
            {% endif %}
        </div>
        <div class="flex items-center space-x-3 pt-6">
            <div class="flex items-center">
                {{ form.hassubcat }}
                <label for="{{ form.hassubcat.id_for_label }}" class="ml-2 text-sm font-medium text-sap-gray-700">
                    Has Sub-Categories
                </label>
            </div>
        </div>
    </div>
    
    <div class="flex justify-end space-x-3 pt-4">
        <button type="button" onclick="hideForm()" 
                class="px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
            Cancel
        </button>
        <button type="submit" 
                class="px-4 py-2 bg-sap-blue-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-sap-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-blue-500">
            Add Category
        </button>
    </div>
</form>