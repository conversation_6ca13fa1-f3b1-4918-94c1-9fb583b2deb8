<!-- accounts/templates/accounts/reports/partials/recent_vat_activity.html -->
<!-- Recent VAT Activity Partial Template for HTMX Updates -->

{% if recent_activities %}
<div class="space-y-3">
    {% for activity in recent_activities %}
    <div class="flex items-center justify-between p-3 bg-sap-gray-50 rounded-lg hover:bg-sap-gray-100 transition-colors">
        <div class="flex items-center flex-1">
            <div class="w-8 h-8 
                {% if activity.type == 'sales' %}bg-sap-blue-100{% elif activity.type == 'purchase' %}bg-sap-green-100{% else %}bg-sap-orange-100{% endif %} 
                rounded-lg flex items-center justify-center mr-3">
                {% if activity.type == 'sales' %}
                    <i data-lucide="trending-up" class="w-4 h-4 text-sap-blue-600"></i>
                {% elif activity.type == 'purchase' %}
                    <i data-lucide="trending-down" class="w-4 h-4 text-sap-green-600"></i>
                {% else %}
                    <i data-lucide="file-text" class="w-4 h-4 text-sap-orange-600"></i>
                {% endif %}
            </div>
            <div class="flex-1">
                <div class="flex items-center justify-between">
                    <p class="text-sm font-medium text-sap-gray-900">{{ activity.description }}</p>
                    <span class="text-xs text-sap-gray-500">{{ activity.timestamp|timesince }} ago</span>
                </div>
                <div class="flex items-center justify-between mt-1">
                    <p class="text-xs text-sap-gray-600">{{ activity.reference_no }}</p>
                    <span class="text-sm font-medium 
                        {% if activity.type == 'sales' %}text-sap-blue-600{% elif activity.type == 'purchase' %}text-sap-green-600{% else %}text-sap-orange-600{% endif %}">
                        ₹{{ activity.vat_amount|floatformat:0 }}
                    </span>
                </div>
            </div>
        </div>
        <button onclick="viewActivity('{{ activity.type }}', '{{ activity.id }}')" 
                class="text-sap-gray-400 hover:text-sap-gray-600 ml-2">
            <i data-lucide="external-link" class="w-4 h-4"></i>
        </button>
    </div>
    {% endfor %}
</div>

<div class="mt-4 text-center">
    <a href="{% url 'accounts:vat_register_dashboard' %}" 
       class="text-sap-blue-600 hover:text-sap-blue-700 text-sm font-medium">
        View All Activity <i data-lucide="arrow-right" class="w-3 h-3 inline ml-1"></i>
    </a>
</div>

{% else %}
<div class="text-center py-8">
    <i data-lucide="activity" class="w-12 h-12 text-sap-gray-300 mx-auto mb-4"></i>
    <p class="text-lg font-medium text-sap-gray-900 mb-2">No Recent Activity</p>
    <p class="text-sm text-sap-gray-600 mb-4">
        VAT transactions will appear here as they are processed.
    </p>
    <div class="flex justify-center space-x-3">
        <a href="{% url 'accounts:sales_vat_register' %}" 
           class="text-sap-blue-600 hover:text-sap-blue-700 text-sm font-medium">
            Sales VAT
        </a>
        <span class="text-sap-gray-300">|</span>
        <a href="{% url 'accounts:purchase_vat_register' %}" 
           class="text-sap-green-600 hover:text-sap-green-700 text-sm font-medium">
            Purchase VAT
        </a>
    </div>
</div>
{% endif %}

<script>
// Re-initialize icons after HTMX update
lucide.createIcons();

// Function to view activity details
function viewActivity(type, id) {
    if (type === 'sales') {
        window.open(`/accounts/sales-invoice/${id}/view/`, '_blank');
    } else if (type === 'purchase') {
        window.open(`/accounts/bill-booking/${id}/view/`, '_blank');
    } else {
        window.open(`/accounts/vat-return/${id}/view/`, '_blank');
    }
}
</script>