{% extends "core/base.html" %}
{% load static %}

{% block title %}Auto WIS Timer - Inventory Management{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header with ASP.NET style -->
    <div class="bg-gray-600 text-white px-4 py-2 mb-4">
        <h1 class="text-lg font-bold">Auto WIS Timer</h1>
    </div>

    <!-- Auto WIS Timer Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-4 py-5 sm:p-6">
            <!-- Insert Button -->
            <div class="mb-4">
                <a href="{% url 'inventory:wis_config_create' %}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded text-sm">
                    Insert
                </a>
            </div>
            
            {% if configurations %}
                <div class="overflow-x-auto">
                    <table class="w-full table-auto divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    SN
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Time
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for config in configurations %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ forloop.counter }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ config.auto_issue_time|default:"—" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-left text-sm font-medium">
                                    <a href="{% url 'inventory:wis_config_update' config.pk %}" 
                                       class="text-blue-600 hover:text-blue-900 mr-4">
                                        Edit
                                    </a>
                                    <a href="{% url 'inventory:wis_config_delete' config.pk %}" 
                                       class="text-red-600 hover:text-red-900">
                                        Delete
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <div class="px-6 py-3 border-t border-gray-200">
                    <div class="flex justify-between items-center">
                        <div class="text-sm text-gray-700">
                            Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} configurations
                        </div>
                        <div class="flex space-x-1">
                            {% if page_obj.has_previous %}
                                <a href="?page={{ page_obj.previous_page_number }}" 
                                   class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 border border-gray-300 rounded-md">
                                    Previous
                                </a>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <span class="px-3 py-2 text-sm bg-indigo-600 text-white border border-indigo-600 rounded-md">
                                        {{ num }}
                                    </span>
                                {% else %}
                                    <a href="?page={{ num }}" 
                                       class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 border border-gray-300 rounded-md">
                                        {{ num }}
                                    </a>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                                <a href="?page={{ page_obj.next_page_number }}" 
                                   class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 border border-gray-300 rounded-md">
                                    Next
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}
            {% else %}
                <div class="text-center py-12">
                    <div class="mx-auto h-12 w-12 text-gray-400">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No timer configurations found</h3>
                    <p class="mt-1 text-sm text-gray-500">Get started by creating a new Auto WIS Timer configuration.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}