{% extends 'core/base.html' %}
{% load static %}

{% block title %}Stock Statement - {{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .stock-summary-card {
        transition: all 0.2s ease;
    }
    .stock-summary-card:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    .stock-level-low { color: #dc2626; background-color: #fee2e2; }
    .stock-level-medium { color: #ea580c; background-color: #fed7aa; }
    .stock-level-high { color: #16a34a; background-color: #dcfce7; }
    .stock-level-zero { color: #6b7280; background-color: #f3f4f6; }
    .print-friendly {
        display: none;
    }
    @media print {
        .no-print { display: none !important; }
        .print-friendly { display: block !important; }
        .container { max-width: none !important; }
    }
    .group-header {
        background-color: #f8fafc;
        font-weight: 600;
    }
    .total-row {
        background-color: #f1f5f9;
        font-weight: 600;
        border-top: 2px solid #e2e8f0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="mb-6 no-print">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Stock Statement</h1>
                <p class="mt-2 text-sm text-gray-600">
                    Comprehensive stock position report with valuation and grouping options
                </p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'inventory:reports_dashboard' %}" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    Back to Reports
                </a>
                {% if statement_data %}
                <button type="button" onclick="window.print()" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    Print
                </button>
                <button type="button" onclick="exportStatement()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    Export
                </button>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Print Header -->
    <div class="print-friendly mb-6">
        <div class="text-center">
            <h1 class="text-2xl font-bold">{{ company_name|default:"Company Name" }}</h1>
            <h2 class="text-xl font-semibold mt-2">Stock Statement</h2>
            <p class="text-gray-600 mt-1">As on {{ statement_data.as_of_date|date:"F d, Y" }}</p>
        </div>
    </div>

    <!-- Statement Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6 no-print">
        <form method="post" id="stock-statement-form" class="space-y-6">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        As of Date
                    </label>
                    {{ form.as_of_date }}
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Group By
                    </label>
                    {{ form.group_by }}
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Valuation Method
                    </label>
                    {{ form.valuation_method }}
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Category Filter (Optional)
                    </label>
                    {{ form.category_filter }}
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Location Filter (Optional)
                    </label>
                    {{ form.location_filter }}
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Minimum Stock Value
                    </label>
                    {{ form.min_stock_value }}
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Maximum Stock Value
                    </label>
                    {{ form.max_stock_value }}
                </div>
            </div>

            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    {{ form.include_zero_stock }}
                    <label class="ml-2 text-sm text-gray-700">{{ form.include_zero_stock.help_text }}</label>
                </div>
                <div class="flex items-center">
                    {{ form.show_negative_stock }}
                    <label class="ml-2 text-sm text-gray-700">{{ form.show_negative_stock.help_text }}</label>
                </div>
                <div class="flex items-center">
                    {{ form.include_item_details }}
                    <label class="ml-2 text-sm text-gray-700">{{ form.include_item_details.help_text }}</label>
                </div>
            </div>

            <div class="flex items-center justify-between">
                <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <span class="inline-flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Generate Stock Statement
                    </span>
                </button>
                
                <div class="text-sm text-gray-500">
                    <span class="font-medium">Tip:</span> Use grouping to organize the statement by category, location, or item type
                </div>
            </div>
        </form>
    </div>

    {% if statement_data %}
    <!-- Statement Results -->
    <div class="space-y-6">
        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 no-print">
            <div class="stock-summary-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Items</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ statement_data.summary.total_items }}</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="stock-summary-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Stock Value</dt>
                            <dd class="text-lg font-medium text-gray-900">₹{{ statement_data.summary.total_value|floatformat:2 }}</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="stock-summary-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Items with Stock</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ statement_data.summary.items_with_stock }}</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="stock-summary-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Zero/Negative Stock</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ statement_data.summary.zero_negative_items }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statement Parameters -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 print-friendly">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                    <span class="font-medium text-gray-500">As of Date:</span>
                    <span class="ml-2">{{ statement_data.parameters.as_of_date|date:"F d, Y" }}</span>
                </div>
                <div>
                    <span class="font-medium text-gray-500">Grouping:</span>
                    <span class="ml-2">{{ statement_data.parameters.group_by|title }}</span>
                </div>
                <div>
                    <span class="font-medium text-gray-500">Valuation:</span>
                    <span class="ml-2">{{ statement_data.parameters.valuation_method|title }}</span>
                </div>
                <div>
                    <span class="font-medium text-gray-500">Generated:</span>
                    <span class="ml-2">{{ statement_data.generated_date|date:"M d, Y H:i" }}</span>
                </div>
            </div>
        </div>

        <!-- Statement Table -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 no-print">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">Stock Details</h3>
                    <div class="flex space-x-2">
                        <select id="group-filter" class="form-select rounded-md border-gray-300 text-sm">
                            <option value="all">All Groups</option>
                            {% for group in statement_data.groups %}
                            <option value="{{ group.name }}">{{ group.name }}</option>
                            {% endfor %}
                        </select>
                        <input type="text" id="search-items" placeholder="Search items..." class="form-input rounded-md border-gray-300 text-sm">
                    </div>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full table-auto divide-y divide-gray-200" id="statement-table">
                    <thead class="bg-gray-50">
                        <tr>
                            {% if statement_data.parameters.group_by != 'none' %}
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {{ statement_data.parameters.group_by|title }}
                            </th>
                            {% endif %}
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Item Code
                            </th>
                            {% if statement_data.parameters.include_item_details %}
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Description
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Unit
                            </th>
                            {% endif %}
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Stock Qty
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Rate (₹)
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Stock Value (₹)
                            </th>
                            {% if statement_data.parameters.include_item_details %}
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Location
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Last Transaction
                            </th>
                            {% endif %}
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for group in statement_data.groups %}
                        {% if statement_data.parameters.group_by != 'none' %}
                        <tr class="group-header">
                            <td colspan="{% if statement_data.parameters.include_item_details %}9{% else %}5{% endif %}" class="px-6 py-3 text-sm font-medium text-gray-900">
                                {{ group.name }} ({{ group.items|length }} items, ₹{{ group.total_value|floatformat:2 }})
                            </td>
                        </tr>
                        {% endif %}
                        
                        {% for item in group.items %}
                        <tr data-group="{{ group.name }}" data-item-code="{{ item.item_code }}" data-description="{{ item.item_description }}">
                            {% if statement_data.parameters.group_by != 'none' %}
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {% if forloop.first %}{{ group.name }}{% endif %}
                            </td>
                            {% endif %}
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {{ item.item_code }}
                            </td>
                            {% if statement_data.parameters.include_item_details %}
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ item.item_description|truncatechars:40 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ item.unit_of_measure }}
                            </td>
                            {% endif %}
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                <span class="{% if item.available_stock <= 0 %}stock-level-zero{% elif item.available_stock < item.reorder_level %}stock-level-low{% elif item.available_stock < item.max_stock_level %}stock-level-medium{% else %}stock-level-high{% endif %} px-2 py-1 rounded">
                                    {{ item.available_stock|floatformat:2 }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                ₹{{ item.unit_rate|floatformat:2 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right font-medium">
                                ₹{{ item.stock_value|floatformat:2 }}
                            </td>
                            {% if statement_data.parameters.include_item_details %}
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ item.location_code|default:"N/A" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ item.last_transaction_date|date:"M d, Y"|default:"Never" }}
                            </td>
                            {% endif %}
                        </tr>
                        {% endfor %}
                        
                        {% if statement_data.parameters.group_by != 'none' and not forloop.last %}
                        <tr class="total-row">
                            <td colspan="{% if statement_data.parameters.include_item_details %}8{% else %}4{% endif %}" class="px-6 py-3 text-sm font-medium text-gray-900 text-right">
                                {{ group.name }} Total:
                            </td>
                            <td class="px-6 py-3 text-sm font-medium text-gray-900 text-right">
                                ₹{{ group.total_value|floatformat:2 }}
                            </td>
                            {% if statement_data.parameters.include_item_details %}
                            <td colspan="2"></td>
                            {% endif %}
                        </tr>
                        {% endif %}
                        {% endfor %}
                        
                        <!-- Grand Total -->
                        <tr class="total-row">
                            <td colspan="{% if statement_data.parameters.include_item_details %}8{% else %}4{% endif %}" class="px-6 py-4 text-base font-bold text-gray-900 text-right">
                                Grand Total:
                            </td>
                            <td class="px-6 py-4 text-base font-bold text-gray-900 text-right">
                                ₹{{ statement_data.summary.total_value|floatformat:2 }}
                            </td>
                            {% if statement_data.parameters.include_item_details %}
                            <td colspan="2"></td>
                            {% endif %}
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Statement Notes -->
        <div class="bg-gray-50 rounded-lg p-6">
            <h4 class="text-sm font-medium text-gray-900 mb-3">Statement Notes:</h4>
            <ul class="text-sm text-gray-600 space-y-1">
                <li>• Stock values calculated using {{ statement_data.parameters.valuation_method|title }} method</li>
                <li>• Statement generated as of {{ statement_data.parameters.as_of_date|date:"F d, Y" }} at {{ statement_data.generated_date|date:"H:i" }}</li>
                <li>• Color coding: <span class="stock-level-zero px-2 py-1 rounded text-xs">Zero Stock</span> <span class="stock-level-low px-2 py-1 rounded text-xs">Low Stock</span> <span class="stock-level-medium px-2 py-1 rounded text-xs">Normal Stock</span> <span class="stock-level-high px-2 py-1 rounded text-xs">High Stock</span></li>
                {% if not statement_data.parameters.include_zero_stock %}
                <li>• Zero stock items excluded from this statement</li>
                {% endif %}
                {% if not statement_data.parameters.show_negative_stock %}
                <li>• Negative stock items excluded from this statement</li>
                {% endif %}
                <li>• This statement includes {{ statement_data.summary.total_items }} items across {{ statement_data.summary.total_locations|default:"multiple" }} locations</li>
            </ul>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    {% if statement_data %}
    // Table filtering functionality
    const groupFilter = document.getElementById('group-filter');
    const searchInput = document.getElementById('search-items');
    const tableRows = document.querySelectorAll('#statement-table tbody tr[data-group]');

    function filterTable() {
        const groupValue = groupFilter.value;
        const searchValue = searchInput.value.toLowerCase();

        tableRows.forEach(row => {
            const group = row.getAttribute('data-group');
            const itemCode = row.getAttribute('data-item-code').toLowerCase();
            const description = row.getAttribute('data-description').toLowerCase();

            const groupMatch = groupValue === 'all' || group === groupValue;
            const searchMatch = itemCode.includes(searchValue) || description.includes(searchValue);

            if (groupMatch && searchMatch) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });

        // Hide/show group headers and totals based on filtering
        const groupHeaders = document.querySelectorAll('#statement-table .group-header');
        const totalRows = document.querySelectorAll('#statement-table .total-row:not(:last-child)');
        
        groupHeaders.forEach((header, index) => {
            const groupName = header.textContent.split(' (')[0].trim();
            const hasVisibleItems = Array.from(tableRows).some(row => 
                row.getAttribute('data-group') === groupName && 
                row.style.display !== 'none'
            );
            
            header.style.display = hasVisibleItems ? '' : 'none';
            if (totalRows[index]) {
                totalRows[index].style.display = hasVisibleItems ? '' : 'none';
            }
        });
    }

    if (groupFilter) groupFilter.addEventListener('change', filterTable);
    if (searchInput) searchInput.addEventListener('input', filterTable);
    {% endif %}
});

function exportStatement() {
    const form = document.createElement('form');
    form.method = 'get';
    form.action = '{% url "inventory:export_report" %}';
    
    const typeInput = document.createElement('input');
    typeInput.type = 'hidden';
    typeInput.name = 'type';
    typeInput.value = 'stock_statement';
    
    const formatInput = document.createElement('input');
    formatInput.type = 'hidden';
    formatInput.name = 'format';
    formatInput.value = 'excel';
    
    form.appendChild(typeInput);
    form.appendChild(formatInput);
    
    // Add current form parameters
    const currentForm = document.getElementById('stock-statement-form');
    const formData = new FormData(currentForm);
    for (let [key, value] of formData.entries()) {
        if (key !== 'csrfmiddlewaretoken') {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = key;
            input.value = value;
            form.appendChild(input);
        }
    }
    
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}
</script>
{% endblock %}