<!-- WIS Dry/Actual Run Results -->
<div class="sap-table-container">
    <table class="sap-table">
        <thead>
            <tr>
                <th class="sap-table-header">SN</th>
                <th class="sap-table-header">Type</th>
                <th class="sap-table-header">Action</th>
                <th class="sap-table-header">WO No</th>
                <th class="sap-table-header">Date</th>
                <th class="sap-table-header">Project Title</th>
            </tr>
        </thead>
        <tbody>
            {% if work_orders %}
                {% for wo in work_orders %}
                <tr class="sap-table-row">
                    <td class="sap-table-cell">{{ forloop.counter }}</td>
                    <td class="sap-table-cell">
                        <span class="sap-tag sap-tag--information">Dry Run</span>
                    </td>
                    <td class="sap-table-cell">
                        <div class="sap-button-group">
                            <select class="sap-select sap-select--compact" onchange="handleActionChange(this, '{{ wo.wo_no }}')">
                                <option value="">Transaction wise Issue</option>
                                <option value="material_issue">Material Issue</option>
                                <option value="assembly_issue">Assembly Issue</option>
                                <option value="both_issue">Both Material & Assembly</option>
                            </select>
                            <button class="sap-button sap-button--emphasized sap-button--compact"
                                    onclick="runDryRun('{{ wo.wo_no }}')">
                                View
                            </button>
                        </div>
                    </td>
                    <td class="sap-table-cell">{{ wo.wo_no }}</td>
                    <td class="sap-table-cell">{{ wo.sys_date|default:"20-02-2022" }}</td>
                    <td class="sap-table-cell">{{ wo.task_project_title|default:"Agriculture product" }}</td>
                </tr>
                {% endfor %}
            {% else %}
                <!-- Sample data matching the screenshot -->
                <tr class="sap-table-row">
                    <td class="sap-table-cell">1</td>
                    <td class="sap-table-cell">
                        <span class="sap-tag sap-tag--information">Dry Run</span>
                    </td>
                    <td class="sap-table-cell">
                        <div class="sap-button-group">
                            <select class="sap-select sap-select--compact" onchange="handleActionChange(this, 'A0001')">
                                <option value="">Transaction wise Issue</option>
                                <option value="material_issue">Material Issue</option>
                                <option value="assembly_issue">Assembly Issue</option>
                                <option value="both_issue">Both Material & Assembly</option>
                            </select>
                            <button class="sap-button sap-button--emphasized sap-button--compact"
                                    onclick="runDryRun('A0001')">
                                View
                            </button>
                        </div>
                    </td>
                    <td class="sap-table-cell">A0001</td>
                    <td class="sap-table-cell">20-02-2022</td>
                    <td class="sap-table-cell">Agriculture product</td>
                </tr>
                <tr class="sap-table-row">
                    <td class="sap-table-cell">2</td>
                    <td class="sap-table-cell">
                        <span class="sap-tag sap-tag--information">Dry Run</span>
                    </td>
                    <td class="sap-table-cell">
                        <div class="sap-button-group">
                            <select class="sap-select sap-select--compact" onchange="handleActionChange(this, 'E0046')">
                                <option value="">Transaction wise Issue</option>
                                <option value="material_issue">Material Issue</option>
                                <option value="assembly_issue">Assembly Issue</option>
                                <option value="both_issue">Both Material & Assembly</option>
                            </select>
                            <button class="sap-button sap-button--emphasized sap-button--compact"
                                    onclick="runDryRun('E0046')">
                                View
                            </button>
                        </div>
                    </td>
                    <td class="sap-table-cell">E0046</td>
                    <td class="sap-table-cell">12-12-2020</td>
                    <td class="sap-table-cell">Test bed automation and control system hardware dismantling, refurbishment, shifting & re-installation & storage</td>
                </tr>
                <tr class="sap-table-row">
                    <td class="sap-table-cell">3</td>
                    <td class="sap-table-cell">
                        <span class="sap-tag sap-tag--information">Dry Run</span>
                    </td>
                    <td class="sap-table-cell">
                        <div class="sap-button-group">
                            <select class="sap-select sap-select--compact" onchange="handleActionChange(this, 'E0047')">
                                <option value="">Transaction wise Issue</option>
                                <option value="material_issue">Material Issue</option>
                                <option value="assembly_issue">Assembly Issue</option>
                                <option value="both_issue">Both Material & Assembly</option>
                            </select>
                            <button class="sap-button sap-button--emphasized sap-button--compact"
                                    onclick="runDryRun('E0047')">
                                View
                            </button>
                        </div>
                    </td>
                    <td class="sap-table-cell">E0047</td>
                    <td class="sap-table-cell">12-12-2020</td>
                    <td class="sap-table-cell">Endurance Test cell Relocation: Automation & control system supply scope</td>
                </tr>
                <tr class="sap-table-row">
                    <td class="sap-table-cell">4</td>
                    <td class="sap-table-cell">
                        <span class="sap-tag sap-tag--information">Dry Run</span>
                    </td>
                    <td class="sap-table-cell">
                        <div class="sap-button-group">
                            <select class="sap-select sap-select--compact" onchange="handleActionChange(this, 'J0128')">
                                <option value="">Transaction wise Issue</option>
                                <option value="material_issue">Material Issue</option>
                                <option value="assembly_issue">Assembly Issue</option>
                                <option value="both_issue">Both Material & Assembly</option>
                            </select>
                            <button class="sap-button sap-button--emphasized sap-button--compact"
                                    onclick="runDryRun('J0128')">
                                View
                            </button>
                        </div>
                    </td>
                    <td class="sap-table-cell">J0128</td>
                    <td class="sap-table-cell">19-12-2020</td>
                    <td class="sap-table-cell">Cabinet Assembly</td>
                </tr>
                <tr class="sap-table-row">
                    <td class="sap-table-cell">5</td>
                    <td class="sap-table-cell">
                        <span class="sap-tag sap-tag--information">Dry Run</span>
                    </td>
                    <td class="sap-table-cell">
                        <div class="sap-button-group">
                            <select class="sap-select sap-select--compact" onchange="handleActionChange(this, 'J0141')">
                                <option value="">Transaction wise Issue</option>
                                <option value="material_issue">Material Issue</option>
                                <option value="assembly_issue">Assembly Issue</option>
                                <option value="both_issue">Both Material & Assembly</option>
                            </select>
                            <button class="sap-button sap-button--emphasized sap-button--compact"
                                    onclick="runDryRun('J0141')">
                                View
                            </button>
                        </div>
                    </td>
                    <td class="sap-table-cell">J0141</td>
                    <td class="sap-table-cell">28-01-2022</td>
                    <td class="sap-table-cell">Container Tail gate Outer upper</td>
                </tr>
                <tr class="sap-table-row">
                    <td class="sap-table-cell">6</td>
                    <td class="sap-table-cell">
                        <span class="sap-tag sap-tag--information">Dry Run</span>
                    </td>
                    <td class="sap-table-cell">
                        <div class="sap-button-group">
                            <select class="sap-select sap-select--compact" onchange="handleActionChange(this, 'J0142')">
                                <option value="">Transaction wise Issue</option>
                                <option value="material_issue">Material Issue</option>
                                <option value="assembly_issue">Assembly Issue</option>
                                <option value="both_issue">Both Material & Assembly</option>
                            </select>
                            <button class="sap-button sap-button--emphasized sap-button--compact"
                                    onclick="runDryRun('J0142')">
                                View
                            </button>
                        </div>
                    </td>
                    <td class="sap-table-cell">J0142</td>
                    <td class="sap-table-cell">27-02-2021</td>
                    <td class="sap-table-cell">Container Modification 6RG827105A to 6JM827105 PO to MQB</td>
                </tr>
                <tr class="sap-table-row">
                    <td class="sap-table-cell">7</td>
                    <td class="sap-table-cell">
                        <span class="sap-tag sap-tag--information">Dry Run</span>
                    </td>
                    <td class="sap-table-cell">
                        <div class="sap-button-group">
                            <select class="sap-select sap-select--compact" onchange="handleActionChange(this, 'J0144')">
                                <option value="">Transaction wise Issue</option>
                                <option value="material_issue">Material Issue</option>
                                <option value="assembly_issue">Assembly Issue</option>
                                <option value="both_issue">Both Material & Assembly</option>
                            </select>
                            <button class="sap-button sap-button--emphasized sap-button--compact"
                                    onclick="runDryRun('J0144')">
                                View
                            </button>
                        </div>
                    </td>
                    <td class="sap-table-cell">J0144</td>
                    <td class="sap-table-cell">12-04-2021</td>
                    <td class="sap-table-cell">Supply of Pallets</td>
                </tr>
                <tr class="sap-table-row">
                    <td class="sap-table-cell">8</td>
                    <td class="sap-table-cell">
                        <span class="sap-tag sap-tag--information">Dry Run</span>
                    </td>
                    <td class="sap-table-cell">
                        <div class="sap-button-group">
                            <select class="sap-select sap-select--compact" onchange="handleActionChange(this, 'J0145')">
                                <option value="">Transaction wise Issue</option>
                                <option value="material_issue">Material Issue</option>
                                <option value="assembly_issue">Assembly Issue</option>
                                <option value="both_issue">Both Material & Assembly</option>
                            </select>
                            <button class="sap-button sap-button--emphasized sap-button--compact"
                                    onclick="runDryRun('J0145')">
                                View
                            </button>
                        </div>
                    </td>
                    <td class="sap-table-cell">J0145</td>
                    <td class="sap-table-cell">28-01-2022</td>
                    <td class="sap-table-cell">Container Modification</td>
                </tr>
                <tr class="sap-table-row">
                    <td class="sap-table-cell">9</td>
                    <td class="sap-table-cell">
                        <span class="sap-tag sap-tag--information">Dry Run</span>
                    </td>
                    <td class="sap-table-cell">
                        <div class="sap-button-group">
                            <select class="sap-select sap-select--compact" onchange="handleActionChange(this, 'J0146')">
                                <option value="">Transaction wise Issue</option>
                                <option value="material_issue">Material Issue</option>
                                <option value="assembly_issue">Assembly Issue</option>
                                <option value="both_issue">Both Material & Assembly</option>
                            </select>
                            <button class="sap-button sap-button--emphasized sap-button--compact"
                                    onclick="runDryRun('J0146')">
                                View
                            </button>
                        </div>
                    </td>
                    <td class="sap-table-cell">J0146</td>
                    <td class="sap-table-cell">21-01-2022</td>
                    <td class="sap-table-cell">NEW PALLET MANUFACTURING</td>
                </tr>
                <tr class="sap-table-row">
                    <td class="sap-table-cell">10</td>
                    <td class="sap-table-cell">
                        <span class="sap-tag sap-tag--information">Dry Run</span>
                    </td>
                    <td class="sap-table-cell">
                        <div class="sap-button-group">
                            <select class="sap-select sap-select--compact" onchange="handleActionChange(this, 'J0147')">
                                <option value="">Transaction wise Issue</option>
                                <option value="material_issue">Material Issue</option>
                                <option value="assembly_issue">Assembly Issue</option>
                                <option value="both_issue">Both Material & Assembly</option>
                            </select>
                            <button class="sap-button sap-button--emphasized sap-button--compact"
                                    onclick="runDryRun('J0147')">
                                View
                            </button>
                        </div>
                    </td>
                    <td class="sap-table-cell">J0147</td>
                    <td class="sap-table-cell">28-01-2022</td>
                    <td class="sap-table-cell">VW-SUV TAIL-GATE UPPER</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<!-- Pagination -->
{% if is_paginated %}
<div class="sap-pagination">
    <div class="sap-pagination-info">
        Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} entries
    </div>
    <div class="sap-pagination-controls">
        <a href="?page=1" class="sap-pagination-link {% if not page_obj.has_previous %}disabled{% endif %}">1</a>
        {% for num in page_obj.paginator.page_range %}
            {% if num == page_obj.number %}
                <span class="sap-pagination-current">{{ num }}</span>
            {% else %}
                <a href="?page={{ num }}" class="sap-pagination-link">{{ num }}</a>
            {% endif %}
        {% endfor %}
        <a href="?page={{ page_obj.paginator.num_pages }}" class="sap-pagination-link {% if not page_obj.has_next %}disabled{% endif %}">{{ page_obj.paginator.num_pages }}</a>
    </div>
</div>
{% endif %}

<script>
function handleActionChange(selectElement, woNumber) {
    const action = selectElement.value;
    if (action) {
        console.log(`Selected action: ${action} for WO: ${woNumber}`);
        // Here you can add specific handling for different action types
    }
}

function runDryRun(woNumber) {
    // Navigate to the assembly BOM detail view
    window.location.href = `/inventory/wis/assembly-bom/${woNumber}/`;
}
</script>