<!-- accounts/partials/account_head_form.html -->
<!-- HTMX partial for Account Head creation form -->
<!-- Replaces ASP.NET form fields with modern form design -->

{% load static %}

<form hx-post="{% url 'accounts:account_head_create' %}" 
      hx-target="#account-head-table" 
      hx-swap="outerHTML"
      hx-trigger="submit"
      class="space-y-4">
    {% csrf_token %}
    
    {% if error %}
    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i data-lucide="alert-circle" class="w-5 h-5 text-red-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Error</h3>
                <div class="mt-2 text-sm text-red-700">{{ error }}</div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Form validation errors -->
    {% if form.non_field_errors %}
    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i data-lucide="alert-circle" class="w-5 h-5 text-red-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Form Errors</h3>
                <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                    {% for error in form.non_field_errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
    {% endif %}
    
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- Category Field -->
        <div>
            <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                Category <span class="text-red-500">*</span>
            </label>
            {{ form.category }}
            {% if form.category.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.category.errors.0 }}</p>
            {% endif %}
        </div>
        
        <!-- Description Field -->
        <div>
            <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                Description <span class="text-red-500">*</span>
            </label>
            {{ form.description }}
            {% if form.description.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.description.errors.0 }}</p>
            {% endif %}
        </div>
        
        <!-- Symbol Field -->
        <div>
            <label for="{{ form.symbol.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                Symbol <span class="text-red-500">*</span>
            </label>
            {{ form.symbol }}
            {% if form.symbol.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.symbol.errors.0 }}</p>
            {% endif %}
        </div>
        
        <!-- Abbreviation Field -->
        <div>
            <label for="{{ form.abbreviation.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                Abbreviation <span class="text-red-500">*</span>
            </label>
            {{ form.abbreviation }}
            {% if form.abbreviation.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.abbreviation.errors.0 }}</p>
            {% endif %}
        </div>
    </div>
    
    <!-- Form Actions -->
    <div class="flex justify-end space-x-3 pt-4">
        <button type="button" 
                onclick="hideForm()" 
                class="px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-gray-500 transition-colors duration-200">
            Cancel
        </button>
        <button type="submit" 
                class="px-4 py-2 bg-sap-blue-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-sap-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-blue-500 transition-colors duration-200">
            <i data-lucide="plus" class="w-4 h-4 mr-2 inline"></i>
            Insert Record
        </button>
    </div>
</form>