{% extends 'core/base.html' %}
{% load static %}

{% block title %}Service Note {{ service_note.gsnno }} - Details{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-gray-700 text-white px-4 py-2">
        <h1 class="text-lg font-bold">Material Service Note - {{ service_note.gsnno }}</h1>
    </div>
    
    <div class="p-4">
        <!-- Service Note Information -->
        <div class="bg-white rounded-lg shadow-sm border mb-4">
            <div class="p-4 border-b">
                <h2 class="text-lg font-semibold text-gray-900">Service Note Details</h2>
            </div>
            <div class="p-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-500">GSN Number</label>
                        <p class="text-sm text-gray-900">{{ service_note.gsnno|default:"N/A" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">GIN ID</label>
                        <p class="text-sm text-gray-900">{{ service_note.ginid|default:"N/A" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">GIN Number</label>
                        <p class="text-sm text-gray-900">
                            {% if service_note.ginno %}
                                <a href="{% url 'inventory:gin_detail' service_note.ginid %}" class="text-blue-600 hover:underline">
                                    {{ service_note.ginno }}
                                </a>
                            {% else %}
                                N/A
                            {% endif %}
                        </p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Company</label>
                        <p class="text-sm text-gray-900">{{ service_note.company.name|default:"N/A" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Financial Year</label>
                        <p class="text-sm text-gray-900">{{ service_note.financial_year.finyear|default:"N/A" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Created Date</label>
                        <p class="text-sm text-gray-900">{{ service_note.sysdate|default:"N/A" }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tax Invoice Information -->
        {% if service_note.taxinvoiceno or service_note.taxinvoicedate %}
        <div class="bg-white rounded-lg shadow-sm border mb-4">
            <div class="p-4 border-b">
                <h2 class="text-lg font-semibold text-gray-900">Tax Invoice Details</h2>
            </div>
            <div class="p-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Tax Invoice Number</label>
                        <p class="text-sm text-gray-900">{{ service_note.taxinvoiceno|default:"Not provided" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500">Tax Invoice Date</label>
                        <p class="text-sm text-gray-900">{{ service_note.taxinvoicedate|default:"Not provided" }}</p>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Action Buttons -->
        <div class="bg-white rounded-lg shadow-sm border">
            <div class="p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'inventory:service_note_list' %}" 
                           class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded text-sm font-medium transition-colors">
                            Back to Service Notes
                        </a>
                        
                        <a href="{% url 'inventory:service_note_update' service_note.pk %}" 
                           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm font-medium transition-colors">
                            Edit Service Note
                        </a>
                        
                        <a href="{% url 'inventory:service_note_print' service_note.pk %}" 
                           class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded text-sm font-medium transition-colors">
                            Print Service Note
                        </a>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <button type="button" 
                                hx-post="{% url 'inventory:service_note_submit' service_note.pk %}"
                                hx-confirm="Submit service note {{ service_note.gsnno }} for approval?"
                                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm font-medium transition-colors">
                            Submit for Approval
                        </button>
                        
                        <button type="button" 
                                hx-post="{% url 'inventory:service_note_approve' service_note.pk %}"
                                hx-confirm="Approve service note {{ service_note.gsnno }}?"
                                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm font-medium transition-colors">
                            Approve
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}