# sales_distribution/views/enquiry_views.py
# Enhanced SAP Fiori-inspired Enquiry Views
# Replaces ASP.NET CustEnquiry_New.aspx functionality with modern UI/UX

from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, CreateView, View
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.db.models import Q
from django.urls import reverse_lazy
from django.views.decorators.http import require_http_methods
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db import transaction
from django.core.exceptions import ValidationError
from datetime import datetime

from ..models import Enquiry, EnquiryAttachment, Customer
from ..forms.enquiry_forms import EnquiryForm, ExistingCustomerSelectionForm, EnquiryFilterForm
from ..forms.enquiry_simple_forms import SimpleEnquiryForm
from sys_admin.models import State, City


class EnquiryCreateView(LoginRequiredMixin, View):
    """
    Enhanced SAP Fiori-inspired Customer Enquiry Creation View
    Replaces ASP.NET CustEnquiry_New.aspx with modern design and better UX
    """
    
    template_name = "sales_distribution/enquiry/enquiry_create.html"

    def get(self, request):
        """Display modern enquiry creation form"""
        form = EnquiryForm()
        existing_customer_form = ExistingCustomerSelectionForm()
        
        context = {
            'form': form,
            'existing_customer_form': existing_customer_form,
            'page_title': 'Create Customer Enquiry',
            'page_subtitle': 'Create a new customer enquiry with comprehensive details',
        }
        return render(request, self.template_name, context)

    def post(self, request):
        """Handle enquiry creation with enhanced validation and error handling"""
        try:
            with transaction.atomic():
                # Create enquiry object manually to match ASP.NET logic
                enquiry = Enquiry()
                
                # Auto-populate system fields
                self._populate_system_fields(enquiry, request)
                
                # Handle customer type logic (New vs Existing)
                customer_type = request.POST.get('customer_type', 'new')
                self._handle_customer_type_and_data(enquiry, customer_type, request)
                
                # Validate required fields
                validation_errors = self._validate_enquiry_data(request)
                if validation_errors:
                    for error in validation_errors:
                        messages.error(request, f"❌ {error}")
                    raise ValidationError("Validation failed")
                
                enquiry.save()
                
                # Handle file attachments
                if request.FILES.getlist('attachments'):
                    self._handle_file_attachments(request, enquiry)
                
                # Success message
                messages.success(
                    request, 
                    f"✅ Enquiry is generated! ID: #{enquiry.enqid}"
                )
                
                # Redirect with message like ASP.NET
                return redirect('sales_distribution:enquiry_create')
                
        except Exception as e:
            messages.error(request, f"❌ Error creating enquiry: {str(e)}")
        
        # Re-render form - create fresh form for retry
        form = EnquiryForm()
        existing_customer_form = ExistingCustomerSelectionForm()
        
        context = {
            'form': form,
            'existing_customer_form': existing_customer_form,
            'page_title': 'Create Customer Enquiry',
            'page_subtitle': 'Create a new customer enquiry with comprehensive details',
        }
        return render(request, self.template_name, context)

    def _populate_system_fields(self, enquiry, request):
        """Populate system fields like ASP.NET logic"""
        enquiry.sysdate = datetime.now().strftime("%d-%m-%Y")
        enquiry.systime = datetime.now().strftime("%H:%M:%S")
        enquiry.sessionid = getattr(request.user, 'username', 'system')
        
        # TODO: Set from actual session/context
        enquiry.compid_id = 1
        enquiry.finyearid_id = 1

    def _handle_customer_type_and_data(self, enquiry, customer_type, request):
        """Handle new vs existing customer logic and populate all form data"""
        if customer_type == 'existing':
            # Handle existing customer logic
            existing_customer_name = request.POST.get('existing_customer_name', '').strip()
            if existing_customer_name and '[' in existing_customer_name:
                # Extract customer code from "Customer Name [CODE]" format
                parts = existing_customer_name.split('[')
                if len(parts) > 1:
                    customer_code = parts[-1].replace(']', '').strip()
                    customer_name = parts[0].strip()
                    enquiry.customerid = customer_code
                    enquiry.customername = customer_name
                    enquiry.flag = 1  # Existing customer
                else:
                    enquiry.flag = 0  # New customer
            else:
                enquiry.flag = 0  # New customer
        else:
            # Handle new customer logic
            customer_name = request.POST.get('customername', '').strip()
            enquiry.customername = customer_name.upper() if customer_name else ''
            enquiry.flag = 0  # New customer
        
        # Populate all form fields from POST data
        self._populate_form_fields(enquiry, request)

    def _populate_form_fields(self, enquiry, request):
        """Populate all enquiry fields from POST data"""
        # Contact Information
        enquiry.contactperson = request.POST.get('contactperson', '').strip()
        enquiry.email = request.POST.get('email', '').strip()
        enquiry.contactno = request.POST.get('contactno', '').strip()
        
        # Registered Office Address  
        enquiry.regdaddress = request.POST.get('regdaddress', '').strip()
        enquiry.regdcountry_id = request.POST.get('regdcountry') or None
        enquiry.regdstate_id = request.POST.get('regdstate') or None
        enquiry.regdcity_id = request.POST.get('regdcity') or None
        enquiry.regdpinno = request.POST.get('regdpinno', '').strip()
        enquiry.regdcontactno = request.POST.get('regdcontactno', '').strip()
        enquiry.regdfaxno = request.POST.get('regdfaxno', '') or ''  # Hidden field with default
        
        # Works/Factory Address
        enquiry.workaddress = request.POST.get('workaddress', '').strip()
        enquiry.workcountry_id = request.POST.get('workcountry') or None
        enquiry.workstate_id = request.POST.get('workstate') or None
        enquiry.workcity_id = request.POST.get('workcity') or None
        enquiry.workpinno = request.POST.get('workpinno', '').strip()
        enquiry.workcontactno = request.POST.get('workcontactno', '').strip()
        enquiry.workfaxno = request.POST.get('workfaxno', '') or ''  # Hidden field with default
        
        # Material Delivery Address
        enquiry.materialdeladdress = request.POST.get('materialdeladdress', '').strip()
        enquiry.materialdelcountry_id = request.POST.get('materialdelcountry') or None
        enquiry.materialdelstate_id = request.POST.get('materialdelstate') or None
        enquiry.materialdelcity_id = request.POST.get('materialdelcity') or None
        enquiry.materialdelpinno = request.POST.get('materialdelpinno', '').strip()
        enquiry.materialdelcontactno = request.POST.get('materialdelcontactno', '').strip()
        enquiry.materialdelfaxno = request.POST.get('materialdelfaxno', '') or ''  # Hidden field with default
        
        # Business Information - Hidden fields with default values
        enquiry.juridictioncode = request.POST.get('juridictioncode', '') or ''
        enquiry.commissionurate = request.POST.get('commissionurate', '') or ''
        enquiry.tinvatno = request.POST.get('tinvatno', '') or ''
        enquiry.eccno = request.POST.get('eccno', '') or ''
        enquiry.divn = request.POST.get('divn', '') or ''
        enquiry.tincstno = request.POST.get('tincstno', '').strip()  # Visible field
        enquiry.range = request.POST.get('range', '') or ''
        panno_value = request.POST.get('panno', '').strip()
        enquiry.panno = panno_value.upper() if panno_value else ''
        enquiry.tdscode = request.POST.get('tdscode', '') or ''
        
        # Enquiry Details
        enquiry.enquiryfor = request.POST.get('enquiryfor', '').strip()
        enquiry.remark = request.POST.get('remark', '').strip()

    def _validate_enquiry_data(self, request):
        """Validate enquiry data like ASP.NET validation"""
        errors = []
        
        # Customer type specific validation
        customer_type = request.POST.get('customer_type', 'new')
        
        if customer_type == 'new':
            if not request.POST.get('customername', '').strip():
                errors.append("Customer name is required for new customer")
        else:
            if not request.POST.get('existing_customer_name', '').strip():
                errors.append("Please select an existing customer")
        
        # Required field validation - ONLY essential fields for enquiry creation
        # Basic enquiry should only require customer info and enquiry description
        
        # Essential fields that are always required
        essential_fields = [
            ('enquiryfor', 'Enquiry description'),
        ]
        
        # Customer contact fields (required if provided)
        contact_fields = [
            ('contactperson', 'Contact person'),
            ('email', 'Email'), 
            ('contactno', 'Contact number'),
        ]
        
        # Address fields are optional for enquiry - only validate if user starts filling them
        # This allows for basic enquiry creation without full address details
        optional_address_fields = [
            ('regdaddress', 'Registered office address'),
            ('regdcountry', 'Registered office country'),
            ('regdstate', 'Registered office state'),
            ('regdcity', 'Registered office city'),
            ('regdpinno', 'Registered office PIN'),
            ('regdcontactno', 'Registered office contact number'),
            ('workaddress', 'Works/Factory address'),
            ('workcountry', 'Works/Factory country'),
            ('workstate', 'Works/Factory state'),
            ('workcity', 'Works/Factory city'),
            ('workpinno', 'Works/Factory PIN'),
            ('workcontactno', 'Works/Factory contact number'),
            ('materialdeladdress', 'Material delivery address'),
            ('materialdelcountry', 'Material delivery country'),
            ('materialdelstate', 'Material delivery state'),
            ('materialdelcity', 'Material delivery city'),
            ('materialdelpinno', 'Material delivery PIN'),
            ('materialdelcontactno', 'Material delivery contact number'),
            ('tincstno', 'TIN/CST number'),
        ]
        
        # Validate essential fields (always required)
        for field_name, field_label in essential_fields:
            value = request.POST.get(field_name, '').strip()
            if not value:
                errors.append(f"{field_label} is required")
        
        # Validate contact fields (at least one contact method required)
        has_contact_info = any(
            request.POST.get(field_name, '').strip() 
            for field_name, _ in contact_fields
        )
        if not has_contact_info:
            errors.append("At least one contact method (person, email, or phone) is required")
        
        # Address validation is relaxed - only validate if form has address data
        # This allows basic enquiry creation without requiring full address details
        
        # Email validation
        email = request.POST.get('email', '').strip()
        if email:
            import re
            email_pattern = r'\w+([-+.\']\w+)*@\w+([-\.]\w+)*\.\w+([-\.]\w+)*'
            if not re.match(email_pattern, email):
                errors.append("Please enter a valid email address")
        
        # PIN code validation (6 digits)
        pin_fields = ['regdpinno', 'workpinno', 'materialdelpinno']
        for pin_field in pin_fields:
            pin_value = request.POST.get(pin_field, '').strip()
            if pin_value and (not pin_value.isdigit() or len(pin_value) != 6):
                errors.append(f"{pin_field.replace('pinno', ' PIN')} should be 6 digits")
        
        # PAN validation
        pan = request.POST.get('panno', '').strip()
        if pan:
            import re
            pan_pattern = r'^[A-Z]{5}[0-9]{4}[A-Z]{1}$'
            if not re.match(pan_pattern, pan.upper()):
                errors.append("PAN number format should be: **********")
        
        return errors

    def _handle_file_attachments(self, request, enquiry):
        """Handle file attachments with validation"""
        files = request.FILES.getlist('attachments')
        max_file_size = 10 * 1024 * 1024  # 10MB limit
        allowed_extensions = ['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png', '.xls', '.xlsx']
        
        for uploaded_file in files:
            if uploaded_file:
                # Validate file size
                if uploaded_file.size > max_file_size:
                    messages.warning(
                        request, 
                        f"⚠️ File {uploaded_file.name} exceeds 10MB limit and was skipped"
                    )
                    continue
                
                # Validate file extension
                file_ext = uploaded_file.name.lower()
                if not any(file_ext.endswith(ext) for ext in allowed_extensions):
                    messages.warning(
                        request,
                        f"⚠️ File {uploaded_file.name} has unsupported format and was skipped"
                    )
                    continue
                
                try:
                    # Create attachment record
                    attachment = EnquiryAttachment()
                    attachment.enqid = enquiry
                    attachment.compid_id = enquiry.compid_id
                    attachment.sessionid = enquiry.sessionid
                    attachment.finyearid_id = enquiry.finyearid_id
                    attachment.filename = uploaded_file.name
                    attachment.filesize = uploaded_file.size
                    attachment.contenttype = uploaded_file.content_type
                    attachment.filedata = uploaded_file.read()
                    attachment.save()
                    
                except Exception as e:
                    messages.warning(
                        request,
                        f"⚠️ Error saving attachment {uploaded_file.name}: {str(e)}"
                    )


class EnquiryListView(LoginRequiredMixin, ListView):
    """
    Enhanced enquiry list view with modern filtering and pagination
    SAP Fiori-inspired data table with real-time search
    """
    
    model = Enquiry
    template_name = "sales_distribution/enquiry/enquiry_list.html"
    context_object_name = "enquiries"
    paginate_by = 25

    def get_queryset(self):
        """Enhanced queryset with optimized queries and filtering"""
        queryset = Enquiry.objects.select_related(
            'regdcountry', 'regdstate', 'regdcity', 'compid', 'finyearid'
        ).prefetch_related(
            'enquiryattachment_set'
        ).order_by('-enqid')

        # Apply search filter
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(customername__icontains=search) |
                Q(customerid__icontains=search) |
                Q(enquiryfor__icontains=search) |
                Q(contactperson__icontains=search) |
                Q(email__icontains=search)
            )

        # Apply status filter  
        status = self.request.GET.get('status')
        if status == 'new':
            queryset = queryset.filter(postatus__isnull=True)
        elif status == 'quoted':
            queryset = queryset.filter(postatus=1)
        elif status == 'converted':
            queryset = queryset.filter(postatus=2)

        # Apply country filter
        country = self.request.GET.get('country')
        if country:
            queryset = queryset.filter(regdcountry_id=country)

        # Apply date range filters
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        if date_from:
            queryset = queryset.extra(
                where=["STR_TO_DATE(sysdate, '%%d-%%m-%%Y') >= %s"],
                params=[date_from]
            )
        if date_to:
            queryset = queryset.extra(
                where=["STR_TO_DATE(sysdate, '%%d-%%m-%%Y') <= %s"],
                params=[date_to]
            )

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['filter_form'] = EnquiryFilterForm(self.request.GET)
        context['page_title'] = 'Customer Enquiries'
        context['page_subtitle'] = 'Manage and track all customer enquiries'
        
        # Add statistics
        context['stats'] = {
            'total_enquiries': Enquiry.objects.count(),
            'new_enquiries': Enquiry.objects.filter(postatus__isnull=True).count(),
            'quoted_enquiries': Enquiry.objects.filter(postatus=1).count(),
            'converted_enquiries': Enquiry.objects.filter(postatus=2).count(),
        }
        
        # Add attachment counts for each enquiry
        for enquiry in context['enquiries']:
            enquiry.attachment_count = enquiry.enquiryattachment_set.count()
            enquiry.status_display = self._get_status_display(enquiry)
        
        return context

    def _get_status_display(self, enquiry):
        """Get human-readable status with SAP-like styling"""
        if enquiry.postatus == 2:
            return {"status": "Converted", "class": "sap-status-success", "icon": "✅"}
        elif enquiry.postatus == 1:
            return {"status": "Quoted", "class": "sap-status-warning", "icon": "💰"}
        else:
            return {"status": "New", "class": "sap-status-info", "icon": "🆕"}

    def get(self, request, *args, **kwargs):
        """Handle HTMX requests for real-time filtering"""
        response = super().get(request, *args, **kwargs)
        
        if request.headers.get('HX-Request'):
            # Return partial template for HTMX updates
            context = self.get_context_data()
            return render(
                request, 
                "sales_distribution/enquiry/partials/enquiry_table.html", 
                context
            )
        
        return response


class EnquiryDetailView(LoginRequiredMixin, View):
    """
    Enhanced enquiry detail view with comprehensive information display
    SAP Fiori-inspired detailed view with action buttons
    """
    
    template_name = "sales_distribution/enquiry_detail.html"

    def get(self, request, enqid):
        """Display comprehensive enquiry details"""
        enquiry = get_object_or_404(Enquiry, enqid=enqid)
        attachments = EnquiryAttachment.objects.filter(enqid=enquiry)
        
        # Check if customer exists in Customer master
        customer = None
        if enquiry.customerid:
            try:
                customer = Customer.objects.get(customerid=enquiry.customerid)
            except Customer.DoesNotExist:
                pass
        
        # Get related quotations if any
        from ..models import Quotation
        quotations = Quotation.objects.filter(enqid=enquiry).order_by('-id')
        
        context = {
            'enquiry': enquiry,
            'customer': customer,
            'attachments': attachments,
            'quotations': quotations,
            'page_title': f'Enquiry #{enquiry.enqid}',
            'page_subtitle': f'Customer: {enquiry.customername}',
            'status_display': self._get_status_display(enquiry),
            'can_edit': self._can_edit_enquiry(enquiry),
            'can_create_quotation': self._can_create_quotation(enquiry),
        }
        return render(request, self.template_name, context)

    def _get_status_display(self, enquiry):
        """Get comprehensive status information"""
        if enquiry.postatus == 2:
            return {
                "status": "Converted to Order", 
                "class": "sap-status-success", 
                "icon": "✅",
                "description": "This enquiry has been successfully converted to a customer order"
            }
        elif enquiry.postatus == 1:
            return {
                "status": "Quoted", 
                "class": "sap-status-warning", 
                "icon": "💰",
                "description": "Quotation has been generated for this enquiry"
            }
        else:
            return {
                "status": "New Enquiry", 
                "class": "sap-status-info", 
                "icon": "🆕",
                "description": "Fresh enquiry awaiting action"
            }

    def _can_edit_enquiry(self, enquiry):
        """Check if enquiry can be edited"""
        return enquiry.postatus != 2  # Cannot edit converted enquiries

    def _can_create_quotation(self, enquiry):
        """Check if quotation can be created"""
        return enquiry.postatus != 2  # Cannot quote converted enquiries


class EnquiryUpdateView(LoginRequiredMixin, View):
    """
    Enhanced enquiry update view with validation
    """
    
    template_name = "sales_distribution/enquiry/enquiry_update.html"

    def get(self, request, enqid):
        """Display enquiry edit form"""
        enquiry = get_object_or_404(Enquiry, enqid=enqid)
        
        # Check if enquiry can be edited
        if enquiry.postatus == 2:
            messages.error(request, "❌ Cannot edit converted enquiries")
            return redirect('sales_distribution:enquiry_detail', enqid=enqid)
        
        form = EnquiryForm(instance=enquiry)
        
        context = {
            'form': form,
            'enquiry': enquiry,
            'page_title': f'Edit Enquiry #{enquiry.enqid}',
            'page_subtitle': f'Modify enquiry details for {enquiry.customername}',
        }
        return render(request, self.template_name, context)

    def post(self, request, enqid):
        """Handle enquiry update"""
        enquiry = get_object_or_404(Enquiry, enqid=enqid)
        
        if enquiry.postatus == 2:
            messages.error(request, "❌ Cannot edit converted enquiries")
            return redirect('sales_distribution:enquiry_detail', enqid=enqid)
        
        form = EnquiryForm(request.POST, request.FILES, instance=enquiry)
        
        if form.is_valid():
            try:
                with transaction.atomic():
                    updated_enquiry = form.save(commit=False)
                    
                    # Update system fields
                    updated_enquiry.sysdate = datetime.now().strftime("%d-%m-%Y")
                    updated_enquiry.systime = datetime.now().strftime("%H:%M:%S")
                    updated_enquiry.sessionid = getattr(request.user, 'username', 'system')
                    
                    updated_enquiry.save()
                    
                    # Handle new attachments
                    if request.FILES.getlist('attachments'):
                        self._handle_file_attachments(request, updated_enquiry)
                    
                    messages.success(
                        request, 
                        f"✅ Enquiry #{enquiry.enqid} updated successfully!"
                    )
                    return redirect('sales_distribution:enquiry_detail', enqid=enqid)
                    
            except Exception as e:
                messages.error(request, f"❌ Error updating enquiry: {str(e)}")
        
        context = {
            'form': form,
            'enquiry': enquiry,
            'page_title': f'Edit Enquiry #{enquiry.enqid}',
            'page_subtitle': f'Modify enquiry details for {enquiry.customername}',
        }
        return render(request, self.template_name, context)

    def _handle_file_attachments(self, request, enquiry):
        """Handle new file attachments during update"""
        files = request.FILES.getlist('attachments')
        for uploaded_file in files:
            if uploaded_file:
                try:
                    attachment = EnquiryAttachment()
                    attachment.enqid = enquiry
                    attachment.compid = enquiry.compid
                    attachment.sessionid = enquiry.sessionid
                    attachment.finyearid = enquiry.finyearid
                    attachment.filename = uploaded_file.name
                    attachment.filesize = uploaded_file.size
                    attachment.contenttype = uploaded_file.content_type
                    attachment.filedata = uploaded_file.read()
                    attachment.save()
                except Exception as e:
                    messages.warning(
                        request,
                        f"⚠️ Error saving attachment {uploaded_file.name}: {str(e)}"
                    )


# HTMX and AJAX Support Views

class CustomerAutocompleteView(LoginRequiredMixin, View):
    """
    Enhanced customer autocomplete with modern UI
    Replaces ASP.NET AutoCompleteExtender functionality
    """

    def get(self, request):
        """Return JSON data for customer autocomplete"""
        search_term = request.GET.get('q', '').strip()
        customers = []

        if search_term and len(search_term) >= 2:
            # Search existing customers
            customer_queryset = Customer.objects.filter(
                Q(customer_name__icontains=search_term) |
                Q(customerid__icontains=search_term)
            ).order_by('customer_name')[:10]

            for customer in customer_queryset:
                customers.append({
                    'id': customer.customerid,
                    'text': f"{customer.customer_name} [{customer.customerid}]",
                    'email': customer.email or '',
                    'phone': customer.contact_no or '',
                })

        return JsonResponse({
            'customers': customers,
            'count': len(customers)
        })


class GetCustomerDetailsView(LoginRequiredMixin, View):
    """
    Enhanced customer details retrieval for existing customer selection
    Replaces ASP.NET btnView_Click functionality with better UX
    """

    def get(self, request):
        """Return customer details as JSON"""
        customer_text = request.GET.get('customer_name', '').strip()
        
        if '[' in customer_text and ']' in customer_text:
            # Extract customer code from "Customer Name [CODE]" format
            parts = customer_text.split('[')
            if len(parts) > 1:
                customer_code = parts[-1].replace(']', '').strip()
                
                try:
                    customer = Customer.objects.select_related(
                        'registered_country', 'registered_state', 'registered_city',
                        'works_country', 'works_state', 'works_city',
                        'material_country', 'material_state', 'material_city'
                    ).get(customerid=customer_code)
                    
                    # Return comprehensive customer data
                    customer_data = {
                        'success': True,
                        'customer': {
                            # Contact Information
                            'contact_person': customer.contact_person or '',
                            'email': customer.email or '',
                            'contact_no': customer.contact_no or '',
                            
                            # Registered Address
                            'registered_address': customer.registered_address or '',
                            'registered_country': customer.registered_country_id if customer.registered_country else '',
                            'registered_state': customer.registered_state_id if customer.registered_state else '',
                            'registered_city': customer.registered_city_id if customer.registered_city else '',
                            'registered_pin': customer.registered_pin or '',
                            'registered_contact_no': customer.registered_contact_no or '',
                            'regdfaxno': customer.regdfaxno or '',
                            
                            # Works Address
                            'works_address': customer.works_address or '',
                            'works_country': customer.works_country_id if customer.works_country else '',
                            'works_state': customer.works_state_id if customer.works_state else '',
                            'works_city': customer.works_city_id if customer.works_city else '',
                            'works_pin': customer.works_pin or '',
                            'works_contact_no': customer.works_contact_no or '',
                            'workfaxno': customer.workfaxno or '',
                            
                            # Material Delivery Address
                            'material_address': customer.material_address or '',
                            'material_country': customer.material_country_id if customer.material_country else '',
                            'material_state': customer.material_state_id if customer.material_state else '',
                            'material_city': customer.material_city_id if customer.material_city else '',
                            'material_pin': customer.material_pin or '',
                            'material_contact_no': customer.material_contact_no or '',
                            'materialdelfaxno': customer.materialdelfaxno or '',
                            
                            # Business Information
                            'juridictioncode': customer.juridictioncode or '',
                            'commissionurate': customer.commissionurate or '',
                            'tinvatno': customer.tinvatno or '',
                            'eccno': customer.eccno or '',
                            'divn': customer.divn or '',
                            'tincstno': customer.tincstno or '',
                            'range': customer.range or '',
                            'panno': customer.panno or '',
                            'tdscode': customer.tdscode or '',
                        }
                    }
                    
                    return JsonResponse(customer_data)
                    
                except Customer.DoesNotExist:
                    pass
        
        return JsonResponse({
            'success': False,
            'error': 'Customer not found'
        })


class GetStatesAjaxView(LoginRequiredMixin, View):
    """
    Enhanced HTMX view for cascading state dropdowns
    Supports all three address types with better UX
    """

    def get(self, request):
        """Return HTML options for state dropdown based on country selection"""
        # Determine which address section is being updated
        country_id = None
        address_type = None

        # Check all possible country field names
        for field_name in ['regdcountry', 'workcountry', 'materialdelcountry']:
            if request.GET.get(field_name):
                country_id = request.GET.get(field_name)
                address_type = field_name.replace('country', '')
                break

        states = []
        if country_id and country_id != "" and country_id != "Select":
            states = State.objects.filter(cid=country_id).order_by('statename')

        # Return modern HTML options with icons
        html = '<option value="">📍 Select State</option>'
        for state in states:
            html += f'<option value="{state.sid}">📍 {state.statename}</option>'

        return HttpResponse(html)


class GetCitiesAjaxView(LoginRequiredMixin, View):
    """
    Enhanced HTMX view for cascading city dropdowns
    Supports all three address types with better UX
    """

    def get(self, request):
        """Return HTML options for city dropdown based on state selection"""
        # Determine which address section is being updated
        state_id = None
        address_type = None

        # Check all possible state field names
        for field_name in ['regdstate', 'workstate', 'materialdelstate']:
            if request.GET.get(field_name):
                state_id = request.GET.get(field_name)
                address_type = field_name.replace('state', '')
                break

        cities = []
        if state_id and state_id != "" and state_id != "Select":
            cities = City.objects.filter(sid=state_id).order_by('cityname')

        # Return modern HTML options with icons
        html = '<option value="">🏙️ Select City</option>'
        for city in cities:
            html += f'<option value="{city.cityid}">🏙️ {city.cityname}</option>'

        return HttpResponse(html)


class AttachmentDeleteView(LoginRequiredMixin, View):
    """
    HTMX view for deleting enquiry attachments
    """

    def delete(self, request, attachment_id):
        """Delete an enquiry attachment"""
        try:
            attachment = get_object_or_404(EnquiryAttachment, id=attachment_id)
            attachment.delete()
            
            return HttpResponse(
                '<div class="sap-success-message">📎 Attachment deleted successfully</div>',
                headers={'HX-Trigger': 'attachmentDeleted'}
            )
        except Exception as e:
            return HttpResponse(
                f'<div class="sap-error-message">❌ Error: {str(e)}</div>',
                status=400
            )


class EnquiryStatsView(LoginRequiredMixin, View):
    """
    HTMX view for live enquiry statistics
    """

    def get(self, request):
        """Return live enquiry statistics"""
        stats = {
            'total': Enquiry.objects.count(),
            'new': Enquiry.objects.filter(postatus__isnull=True).count(),
            'quoted': Enquiry.objects.filter(postatus=1).count(),
            'converted': Enquiry.objects.filter(postatus=2).count(),
        }
        
        return render(
            request, 
            'sales_distribution/enquiry/partials/enquiry_stats.html',
            {'stats': stats}
        )



class SimpleEnquiryCreateView(LoginRequiredMixin, CreateView):
    """
    Simple Customer-Like Enquiry Creation View
    Matches customer form layout exactly - only enqid and enquiry_for different
    """
    
    model = Enquiry
    form_class = SimpleEnquiryForm
    template_name = "sales_distribution/enquiry_new_customer_layout.html"
    success_url = reverse_lazy('sales_distribution:enquiry_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': 'Customer Master - New',
            'page_subtitle': 'Create a new customer enquiry',
        })
        return context

    def form_valid(self, form):
        """Handle successful form submission"""
        try:
            with transaction.atomic():
                # Set required system fields
                form.instance.sysdate = datetime.now().strftime("%Y-%m-%d")
                form.instance.systime = datetime.now().strftime("%H:%M:%S")
                form.instance.sessionid = self.request.session.session_key or 'default'
                
                # Set company and financial year from global context
                if hasattr(self.request, 'global_company') and self.request.global_company:
                    form.instance.compid = self.request.global_company
                if hasattr(self.request, 'global_financial_year') and self.request.global_financial_year:
                    form.instance.finyearid = self.request.global_financial_year
                
                # Save the enquiry
                response = super().form_valid(form)
                
                messages.success(
                    self.request,
                    f"✅ Enquiry {form.instance.enqid} created successfully for {form.instance.customername}!"
                )
                
                return response
                
        except Exception as e:
            messages.error(
                self.request,
                f"❌ Error creating enquiry: {str(e)}"
            )
            return self.form_invalid(form)

    def form_invalid(self, form):
        """Handle form validation errors"""
        messages.error(
            self.request,
            "❌ Please correct the errors below and try again."
        )
        return super().form_invalid(form)


# HTMX Ajax Views for Simple Enquiry Form (same as customer form)
@require_http_methods(["GET"])
def get_states_ajax(request):
    """HTMX endpoint to get states for selected country"""
    country_id = request.GET.get('regdcountry') or request.GET.get('workcountry') or request.GET.get('materialdelcountry') or request.GET.get('registered_country') or request.GET.get('works_country') or request.GET.get('material_country')
    
    if country_id:
        states = State.objects.filter(cid=country_id).order_by('statename')
        state_options = '<option value="">Select State</option>'
        for state in states:
            state_options += f'<option value="{state.sid}">{state.statename}</option>'
        return HttpResponse(state_options)
    
    return HttpResponse('<option value="">Select State</option>')


@require_http_methods(["GET"])
def get_cities_ajax(request):
    """HTMX endpoint to get cities for selected state"""
    state_id = request.GET.get('regdstate') or request.GET.get('workstate') or request.GET.get('materialdelstate') or request.GET.get('registered_state') or request.GET.get('works_state') or request.GET.get('material_state')
    
    if state_id:
        cities = City.objects.filter(sid=state_id).order_by('cityname')
        city_options = '<option value="">Select City</option>'
        for city in cities:
            city_options += f'<option value="{city.cityid}">{city.cityname}</option>'
        return HttpResponse(city_options)
    
    return HttpResponse('<option value="">Select City</option>')
