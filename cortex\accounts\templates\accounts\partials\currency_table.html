<!-- accounts/partials/currency_table.html -->
<!-- HTMX partial for Currency table - SAP S/4HANA inspired -->
<!-- Replaces ASP.NET GridView with modern responsive table -->

{% load static %}

<div class="overflow-hidden">
    {% if currencies %}
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-sap-gray-200">
            <thead class="bg-sap-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        ID
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        Country
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        Currency Name
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        Symbol
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-sap-gray-200" id="currency-tbody">
                {% for currency in currencies %}
                <tr class="hover:bg-sap-gray-50 transition-colors duration-150" id="currency-row-{{ currency.id }}">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-sap-gray-900">
                        {{ currency.id }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-sap-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <i data-lucide="globe" class="w-4 h-4 text-sap-blue-600"></i>
                            </div>
                            <div class="text-sm font-medium text-sap-gray-900">
                                {% if currency.country %}{{ currency.country.country_name }}{% else %}N/A{% endif %}
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-sap-gray-900">{{ currency.name|default:"N/A" }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-3 py-1 text-sm font-mono bg-sap-orange-100 text-sap-orange-800 rounded-full">
                            {{ currency.symbol|default:"N/A" }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex justify-end space-x-2">
                            <!-- Edit Button -->
                            <button type="button"
                                    hx-get="{% url 'accounts:currency_edit' currency.id %}"
                                    hx-target="#currency-row-{{ currency.id }}"
                                    hx-swap="outerHTML"
                                    class="inline-flex items-center px-3 py-1.5 border border-sap-orange-300 rounded text-xs font-medium text-sap-orange-700 bg-sap-orange-50 hover:bg-sap-orange-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-orange-500 transition-colors duration-200">
                                <i data-lucide="edit" class="w-3 h-3 mr-1"></i>
                                Edit
                            </button>
                            <!-- Delete Button -->
                            <button type="button"
                                    hx-delete="{% url 'accounts:currency_delete' currency.id %}"
                                    hx-target="#currency-row-{{ currency.id }}"
                                    hx-swap="outerHTML"
                                    hx-confirm="Are you sure you want to delete this currency? This action cannot be undone."
                                    class="inline-flex items-center px-3 py-1.5 border border-sap-red-300 rounded text-xs font-medium text-sap-red-700 bg-sap-red-50 hover:bg-sap-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-red-500 transition-colors duration-200">
                                <i data-lucide="trash-2" class="w-3 h-3 mr-1"></i>
                                Delete
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="text-center py-12">
        <div class="w-16 h-16 bg-sap-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i data-lucide="coins" class="w-8 h-8 text-sap-gray-400"></i>
        </div>
        <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No currencies found</h3>
        <p class="text-sm text-sap-gray-600 mb-4">
            {% if request.GET.search %}
                Try adjusting your search criteria.
            {% else %}
                Get started by creating your first currency entry.
            {% endif %}
        </p>
        {% if request.GET.search %}
        <button type="button" 
                onclick="clearSearch()"
                class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
            <i data-lucide="x" class="w-4 h-4 mr-2"></i>
            Clear Search
        </button>
        {% endif %}
    </div>
    {% endif %}
</div>