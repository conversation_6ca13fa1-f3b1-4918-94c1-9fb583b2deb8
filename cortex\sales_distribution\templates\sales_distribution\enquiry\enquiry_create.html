{% extends "core/base.html" %}
{% load static %}

{% block title %}Customer Enquiry - New{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 py-6" x-data="enquiryForm()">
    <!-- Header -->
    <div class="bg-white border border-gray-200 rounded-lg shadow-sm mb-6">
        <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-4 px-6 rounded-t-lg">
            <h1 class="text-2xl font-bold">Customer Enquiry - New</h1>
        </div>
    </div>

    <!-- Main Form -->
    <form method="post" enctype="multipart/form-data" @submit="handleSubmit">
        {% csrf_token %}
        
        <!-- Tab Container (mimicking ASP.NET TabContainer) -->
        <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
            <!-- Tab Headers -->
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex">
                    <button type="button" @click="activeTab = 'details'" 
                            class="py-2 px-4 border-b-2 font-medium text-sm" 
                            :class="activeTab === 'details' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'">
                        Details
                    </button>
                    <button type="button" @click="activeTab = 'attachments'"
                            class="py-2 px-4 border-b-2 font-medium text-sm"
                            :class="activeTab === 'attachments' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'">
                        Attachments
                    </button>
                </nav>
            </div>

            <!-- Tab Content -->
            <div class="p-6">
                <!-- Details Tab -->
                <div x-show="activeTab === 'details'">
                    <!-- Customer Name Selection Row -->
                    <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-6 mb-4">
                            <label class="text-sm font-semibold text-gray-700">Customer's Name</label>
                            <div class="flex items-center space-x-4">
                                <label class="flex items-center">
                                    <input type="radio" name="customer_type" value="new" x-model="customerType" @change="handleCustomerTypeChange()" 
                                           class="text-blue-600 border-gray-300 focus:ring-blue-500" checked>
                                    <span class="ml-2 text-sm text-gray-700">New</span>
                                </label>
                                <input type="text" x-show="customerType === 'new'" 
                                       class="w-80 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="Enter customer name" name="customername" id="id_customername">
                                <span class="text-red-500" x-show="customerType === 'new'">*</span>
                                
                                <label class="flex items-center">
                                    <input type="radio" name="customer_type" value="existing" x-model="customerType" @change="handleCustomerTypeChange()"
                                           class="text-blue-600 border-gray-300 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">Existing</span>
                                </label>
                                <div x-show="customerType === 'existing'" class="relative">
                                    <input type="text" 
                                           class="w-80 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="Start typing customer name..." 
                                           x-model="existingCustomerName"
                                           @input.debounce.300ms="searchCustomers($event.target.value)"
                                           @focus="showCustomerDropdown = true"
                                           autocomplete="off">
                                    
                                    <!-- Customer Autocomplete Dropdown -->
                                    <div x-show="showCustomerDropdown && customerResults.length > 0" 
                                         class="absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-md shadow-lg max-h-48 overflow-y-auto z-10"
                                         @click.away="showCustomerDropdown = false">
                                        <template x-for="customer in customerResults" :key="customer.id">
                                            <div class="px-3 py-2 hover:bg-gray-100 cursor-pointer" @click="selectCustomer(customer)">
                                                <div x-text="customer.text" class="font-medium"></div>
                                                <div x-text="customer.email" class="text-sm text-gray-600"></div>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                                <span class="text-red-500" x-show="customerType === 'existing'">*</span>
                                <button type="button" x-show="customerType === 'existing'" :disabled="!existingCustomerName"
                                        class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                                        @click="searchCustomerDetails()">Search</button>
                            </div>
                        </div>
                    </div>

                    <!-- Address Details Table -->
                    <div class="overflow-x-auto">
                        <table class="w-full border-collapse">
                            <!-- Address Headers -->
                            <thead>
                                <tr class="bg-gray-100">
                                    <th class="text-center py-3 px-4 border border-gray-300 text-sm font-semibold text-gray-700">Address/Details</th>
                                    <th class="text-center py-3 px-4 border border-gray-300 text-sm font-semibold text-gray-700">REGD. OFFICE</th>
                                    <th class="text-center py-3 px-4 border border-gray-300 text-sm font-semibold text-gray-700">WORKS/FACTORY</th>
                                    <th class="text-center py-3 px-4 border border-gray-300 text-sm font-semibold text-gray-700">MATERIAL DELIVERY</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Address Row -->
                                <tr>
                                    <th class="text-left py-3 px-4 border border-gray-300 text-sm font-semibold text-gray-700 bg-gray-50">Address</th>
                                    <td class="py-3 px-4 border border-gray-300">
                                        <textarea name="regdaddress" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" rows="3" placeholder="Registered office address"></textarea>
                                        <span class="text-red-500">*</span>
                                    </td>
                                    <td class="py-3 px-4 border border-gray-300">
                                        <textarea name="workaddress" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" rows="3" placeholder="Works/Factory address"></textarea>
                                        <span class="text-red-500">*</span>
                                    </td>
                                    <td class="py-3 px-4 border border-gray-300">
                                        <textarea name="materialdeladdress" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" rows="3" placeholder="Material delivery address"></textarea>
                                        <span class="text-red-500">*</span>
                                    </td>
                                </tr>

                                <!-- Country Row -->
                                <tr>
                                    <th class="text-left py-3 px-4 border border-gray-300 text-sm font-semibold text-gray-700 bg-gray-50">Country</th>
                                    <td class="py-3 px-4 border border-gray-300">
                                        <select name="regdcountry" id="id_regdcountry" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                                                hx-get="/sales-distribution/ajax/get-states/" hx-target="#id_regdstate" hx-trigger="change" hx-include="[name='regdcountry']">
                                            {% for value, label in form.regdcountry.field.choices %}
                                                <option value="{{ value }}">{{ label }}</option>
                                            {% endfor %}
                                        </select>
                                        <span class="text-red-500">*</span>
                                    </td>
                                    <td class="py-3 px-4 border border-gray-300">
                                        <select name="workcountry" id="id_workcountry" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                                                hx-get="/sales-distribution/ajax/get-states/" hx-target="#id_workstate" hx-trigger="change" hx-include="[name='workcountry']">
                                            {% for value, label in form.workcountry.field.choices %}
                                                <option value="{{ value }}">{{ label }}</option>
                                            {% endfor %}
                                        </select>
                                        <span class="text-red-500">*</span>
                                    </td>
                                    <td class="py-3 px-4 border border-gray-300">
                                        <select name="materialdelcountry" id="id_materialdelcountry" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                                                hx-get="/sales-distribution/ajax/get-states/" hx-target="#id_materialdelstate" hx-trigger="change" hx-include="[name='materialdelcountry']">
                                            {% for value, label in form.materialdelcountry.field.choices %}
                                                <option value="{{ value }}">{{ label }}</option>
                                            {% endfor %}
                                        </select>
                                        <span class="text-red-500">*</span>
                                    </td>
                                </tr>

                                <!-- State Row -->
                                <tr>
                                    <th class="text-left py-3 px-4 border border-gray-300 text-sm font-semibold text-gray-700 bg-gray-50">State</th>
                                    <td class="py-3 px-4 border border-gray-300">
                                        <select name="regdstate" id="id_regdstate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                                                hx-get="/sales-distribution/ajax/get-cities/" hx-target="#id_regdcity" hx-trigger="change" hx-include="[name='regdstate']">
                                            <option value="">Select State</option>
                                        </select>
                                        <span class="text-red-500">*</span>
                                    </td>
                                    <td class="py-3 px-4 border border-gray-300">
                                        <select name="workstate" id="id_workstate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                                                hx-get="/sales-distribution/ajax/get-cities/" hx-target="#id_workcity" hx-trigger="change" hx-include="[name='workstate']">
                                            <option value="">Select State</option>
                                        </select>
                                        <span class="text-red-500">*</span>
                                    </td>
                                    <td class="py-3 px-4 border border-gray-300">
                                        <select name="materialdelstate" id="id_materialdelstate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                                                hx-get="/sales-distribution/ajax/get-cities/" hx-target="#id_materialdelcity" hx-trigger="change" hx-include="[name='materialdelstate']">
                                            <option value="">Select State</option>
                                        </select>
                                        <span class="text-red-500">*</span>
                                    </td>
                                </tr>

                                <!-- City Row -->
                                <tr>
                                    <th class="text-left py-3 px-4 border border-gray-300 text-sm font-semibold text-gray-700 bg-gray-50">City</th>
                                    <td class="py-3 px-4 border border-gray-300">
                                        <select name="regdcity" id="id_regdcity" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white">
                                            <option value="">Select City</option>
                                        </select>
                                        <span class="text-red-500">*</span>
                                    </td>
                                    <td class="py-3 px-4 border border-gray-300">
                                        <select name="workcity" id="id_workcity" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white">
                                            <option value="">Select City</option>
                                        </select>
                                        <span class="text-red-500">*</span>
                                    </td>
                                    <td class="py-3 px-4 border border-gray-300">
                                        <select name="materialdelcity" id="id_materialdelcity" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white">
                                            <option value="">Select City</option>
                                        </select>
                                        <span class="text-red-500">*</span>
                                    </td>
                                </tr>

                                <!-- PIN No Row -->
                                <tr>
                                    <th class="text-left py-3 px-4 border border-gray-300 text-sm font-semibold text-gray-700 bg-gray-50">PIN No.</th>
                                    <td class="py-3 px-4 border border-gray-300">
                                        <input type="text" name="regdpinno" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="PIN Code" maxlength="6" pattern="[0-9]{6}">
                                        <span class="text-red-500">*</span>
                                    </td>
                                    <td class="py-3 px-4 border border-gray-300">
                                        <input type="text" name="workpinno" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="PIN Code" maxlength="6" pattern="[0-9]{6}">
                                        <span class="text-red-500">*</span>
                                    </td>
                                    <td class="py-3 px-4 border border-gray-300">
                                        <input type="text" name="materialdelpinno" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="PIN Code" maxlength="6" pattern="[0-9]{6}">
                                        <span class="text-red-500">*</span>
                                    </td>
                                </tr>

                                <!-- Contact No Row -->
                                <tr>
                                    <th class="text-left py-3 px-4 border border-gray-300 text-sm font-semibold text-gray-700 bg-gray-50">Contact No.</th>
                                    <td class="py-3 px-4 border border-gray-300">
                                        <input type="text" name="regdcontactno" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Contact number">
                                        <span class="text-red-500">*</span>
                                    </td>
                                    <td class="py-3 px-4 border border-gray-300">
                                        <input type="text" name="workcontactno" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Contact number">
                                        <span class="text-red-500">*</span>
                                    </td>
                                    <td class="py-3 px-4 border border-gray-300">
                                        <input type="text" name="materialdelcontactno" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Contact number">
                                        <span class="text-red-500">*</span>
                                    </td>
                                </tr>

                                <!-- Fax No Row - REMOVED -->

                            </tbody>
                        </table>
                    </div>

                    <!-- Additional Details (Contact Person, Email, etc.) -->
                    <div class="mt-6 overflow-x-auto">
                        <table class="w-full">
                            <tr>
                                <td class="py-3 px-4 text-sm font-semibold text-gray-700">Contact person</td>
                                <td class="py-3 px-4">
                                    <input type="text" name="contactperson" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Contact person name">
                                    <span class="text-red-500">*</span>
                                </td>
                                <td class="py-3 px-4 text-sm font-semibold text-gray-700">E-mail</td>
                                <td class="py-3 px-4">
                                    <input type="email" name="email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="<EMAIL>">
                                    <span class="text-red-500">*</span>
                                </td>
                                <td class="py-3 px-4 text-sm font-semibold text-gray-700">Contact No.</td>
                                <td class="py-3 px-4">
                                    <input type="text" name="contactno" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Contact number">
                                    <span class="text-red-500">*</span>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <!-- Business Details - Only TIN/CST No. -->
                    <div class="mt-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label class="block text-sm font-semibold text-gray-700 mb-2">TIN/CST No.</label>
                                <input type="text" name="tincstno" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="TIN/CST number">
                                <span class="text-red-500">*</span>
                            </div>
                        </div>

                        <!-- Hidden fields for data preservation -->
                        <div style="display: none;">
                            {{ form.juridictioncode }}
                            {{ form.commissionurate }}
                            {{ form.tinvatno }}
                            {{ form.eccno }}
                            {{ form.divn }}
                            {{ form.range }}
                            {{ form.panno }}
                            {{ form.tdscode }}
                            {{ form.regdfaxno }}
                            {{ form.workfaxno }}
                            {{ form.materialdelfaxno }}
                        </div>
                    </div>

                    <!-- Enquiry For and Remarks -->
                    <div class="mt-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-semibold text-gray-700 mb-2">Enquiry For</label>
                                <textarea name="enquiryfor" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" rows="4" placeholder="Describe your enquiry in detail..."></textarea>
                                <span class="text-red-500">*</span>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-gray-700 mb-2">Remarks</label>
                                <textarea name="remark" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" rows="4" placeholder="Additional remarks..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Attachments Tab -->
                <div x-show="activeTab === 'attachments'">
                    <div class="space-y-4">
                        <div class="flex items-center space-x-4">
                            <label class="text-sm font-semibold text-gray-700">Attachment</label>
                            <input type="file" name="attachments" 
                                   class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                                   accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.xls,.xlsx">
                            <button type="button" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700" onclick="return confirmationUpload()">Upload</button>
                        </div>
                        
                        <!-- Attachments Grid (placeholder for future implementation) -->
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="text-center text-gray-500">
                                <p>No attachments uploaded yet.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="mt-6 text-center space-x-4">
            <button type="submit" class="px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 font-semibold" onclick="return confirmationAdd()" :class="{ 'opacity-50 cursor-not-allowed': isSubmitting }" :disabled="isSubmitting">
                <span x-show="!isSubmitting">Submit</span>
                <span x-show="isSubmitting">Creating...</span>
            </button>
            <a href="{% url 'sales_distribution:enquiry_list' %}" class="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 font-semibold">Cancel</a>
            <div x-show="message" class="mt-4 p-3 rounded-md" :class="messageType === 'error' ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'" x-text="message"></div>
        </div>
    </form>
</div>

<script>
function enquiryForm() {
    return {
        activeTab: 'details',
        customerType: 'new',
        existingCustomerName: '',
        showCustomerDropdown: false,
        customerResults: [],
        isSubmitting: false,
        message: '',
        messageType: 'success',

        handleCustomerTypeChange() {
            if (this.customerType === 'new') {
                this.existingCustomerName = '';
                this.customerResults = [];
                this.showCustomerDropdown = false;
                // Clear existing customer form data
                this.clearAllFormFields();
            } else {
                // Clear new customer name field
                document.querySelector('input[name="customername"]').value = '';
            }
        },

        async searchCustomers(query) {
            if (query.length < 2) {
                this.customerResults = [];
                return;
            }

            try {
                const response = await fetch(`/sales-distribution/ajax/customer-autocomplete/?q=${encodeURIComponent(query)}`);
                const data = await response.json();
                this.customerResults = data.customers || [];
                this.showCustomerDropdown = true;
            } catch (error) {
                console.error('Error searching customers:', error);
                this.customerResults = [];
            }
        },

        async selectCustomer(customer) {
            this.existingCustomerName = customer.text;
            this.showCustomerDropdown = false;
        },

        async searchCustomerDetails() {
            if (!this.existingCustomerName) return;
            
            try {
                const response = await fetch(`/sales-distribution/ajax/get-customer-details/?customer_name=${encodeURIComponent(this.existingCustomerName)}`);
                const data = await response.json();
                
                if (data.success && data.customer) {
                    // Populate all form fields with customer data
                    Object.keys(data.customer).forEach(key => {
                        const field = document.querySelector(`[name="${key}"]`);
                        if (field && data.customer[key]) {
                            field.value = data.customer[key];
                            
                            // Trigger change event for dropdowns to update dependent fields
                            if (field.tagName === 'SELECT') {
                                field.dispatchEvent(new Event('change'));
                            }
                        }
                    });
                    
                    this.message = 'Customer details loaded successfully';
                    this.messageType = 'success';
                    setTimeout(() => { this.message = ''; }, 3000);
                } else {
                    this.message = 'Customer not found or error loading details';
                    this.messageType = 'error';
                    setTimeout(() => { this.message = ''; }, 3000);
                }
            } catch (error) {
                console.error('Error fetching customer details:', error);
                this.message = 'Error loading customer details';
                this.messageType = 'error';
                setTimeout(() => { this.message = ''; }, 3000);
            }
        },

        clearAllFormFields() {
            // Clear all form fields
            const formFields = document.querySelectorAll('input, textarea, select');
            formFields.forEach(field => {
                if (field.name && field.name !== 'customer_type' && field.name !== 'csrfmiddlewaretoken') {
                    if (field.type === 'checkbox' || field.type === 'radio') {
                        field.checked = false;
                    } else {
                        field.value = '';
                    }
                }
            });
        },

        handleSubmit(event) {
            this.isSubmitting = true;
            
            // Basic validation
            if (this.customerType === 'new') {
                const customerName = document.querySelector('input[name="customername"]').value;
                if (!customerName.trim()) {
                    event.preventDefault();
                    this.message = 'Customer name is required';
                    this.messageType = 'error';
                    this.isSubmitting = false;
                    return;
                }
            } else {
                if (!this.existingCustomerName.trim()) {
                    event.preventDefault();
                    this.message = 'Please select an existing customer';
                    this.messageType = 'error';
                    this.isSubmitting = false;
                    return;
                }
            }
        }
    }
}

// JavaScript functions from original ASP.NET code
function confirmationAdd() {
    return confirm('Are you sure you want to submit this enquiry?');
}

function confirmationUpload() {
    return confirm('Are you sure you want to upload this file?');
}
</script>
{% endblock %}
