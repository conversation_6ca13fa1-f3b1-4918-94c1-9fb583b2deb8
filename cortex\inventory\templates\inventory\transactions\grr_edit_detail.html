{% extends 'core/base.html' %}
{% load static %}

{% block title %}Goods Received Receipt [GRR] - Edit Detail{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <div class="bg-blue-600 text-white px-4 py-2">
        <h1 class="text-lg font-bold">Goods Received Receipt [GRR] - Edit Detail</h1>
    </div>
    
    <div class="p-4">
        <div class="bg-white rounded-lg shadow-sm border max-w-6xl mx-auto">
            <div class="p-6">
                <form method="post" class="space-y-6">
                    {% csrf_token %}
                    
                    {% if messages %}
                        {% for message in messages %}
                            <div class="bg-green-50 border border-green-200 rounded-md p-4">
                                <div class="text-green-800 text-sm">{{ message }}</div>
                            </div>
                        {% endfor %}
                    {% endif %}
                    
                    <!-- GRR Header Information -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 border-b pb-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">GRR Number</label>
                            <input type="text" value="{{ grr.grr_number }}" readonly
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">GIN Number</label>
                            <input type="text" value="{{ grr.gin_number }}" readonly
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Supplier Name</label>
                            <input type="text" value="{{ grr.supplier_name }}" readonly
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Challan No</label>
                            <input type="text" value="{{ grr.challan_no }}" readonly
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Challan Date</label>
                            <input type="text" value="{{ grr.challan_date }}" readonly
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">PO No</label>
                            <input type="text" value="{{ grr.po_no }}" readonly
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600">
                        </div>
                    </div>
                    
                    <!-- GRR Line Items -->
                    <div class="border-t pt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">GRR Line Items</h3>
                        <div class="overflow-x-auto">
                            <table class="w-full table-auto divide-y divide-gray-200 border">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">SN</th>
                                        <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">Edit</th>
                                        <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">Image</th>
                                        <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">Spec. Sheet</th>
                                        <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">Item Code</th>
                                        <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                        <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">UOM</th>
                                        <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">PO Qty</th>
                                        <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">Inward Qty</th>
                                        <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">Tot Reccd Qty</th>
                                        <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">Recd Qty</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    {% for item in grr_line_items %}
                                    <tr>
                                        <td class="px-2 py-3 text-sm text-gray-900 text-center">{{ forloop.counter }}</td>
                                        <td class="px-2 py-3 text-sm">
                                            <a href="#" class="text-blue-600 hover:text-blue-800 underline text-xs">Edit</a>
                                        </td>
                                        <td class="px-2 py-3 text-sm text-gray-900 text-center">
                                            {% if item.image_file %}
                                                <a href="#" class="text-blue-600 hover:text-blue-800 underline text-xs">{{ item.image_file }}</a>
                                            {% endif %}
                                        </td>
                                        <td class="px-2 py-3 text-sm text-gray-900 text-center">
                                            {% if item.spec_file %}
                                                <a href="#" class="text-blue-600 hover:text-blue-800 underline text-xs">{{ item.spec_file }}</a>
                                            {% endif %}
                                        </td>
                                        <td class="px-2 py-3 text-sm text-gray-900 text-xs">{{ item.item_code }}</td>
                                        <td class="px-2 py-3 text-sm text-gray-900 text-xs">{{ item.description }}</td>
                                        <td class="px-2 py-3 text-sm text-gray-900 text-center text-xs">{{ item.uom }}</td>
                                        <td class="px-2 py-3 text-sm text-gray-900 text-center text-xs">{{ item.po_qty }}</td>
                                        <td class="px-2 py-3 text-sm text-gray-900 text-center text-xs">{{ item.inward_qty }}</td>
                                        <td class="px-2 py-3 text-sm text-gray-900 text-center text-xs">{{ item.total_received_qty }}</td>
                                        <td class="px-2 py-3 text-sm text-gray-900 text-center text-xs">
                                            <input type="number" value="{{ item.received_qty }}" step="0.001" 
                                                   class="w-16 px-2 py-1 border border-gray-300 rounded text-xs text-center">
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="11" class="px-4 py-8 text-center">
                                            <div class="text-gray-500">
                                                <p class="text-lg font-medium text-red-900">No data to display !</p>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Cancel Button as shown in screenshot -->
                        <div class="mt-4 text-center">
                            <a href="{% url 'inventory:grr_edit_list' %}" 
                               class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded text-sm font-medium">
                                Cancel
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

{% endblock %}