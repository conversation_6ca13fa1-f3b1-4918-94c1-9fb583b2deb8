<!-- accounts/templates/accounts/masters/asset_detail.html -->
<!-- Asset Detail View Template -->
<!-- Task Group 8: Asset Management - Asset Detail (Task 8.4) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{{ asset.asset_name }} - Asset Details{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-teal-600 to-sap-teal-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="building" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">{{ asset.asset_name }}</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Asset Code: {{ asset.asset_code }} | {{ asset.get_asset_type_display }}</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:asset_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
                <a href="{% url 'accounts:asset_edit' asset.id %}" 
                   class="bg-sap-teal-600 hover:bg-sap-teal-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="edit" class="w-4 h-4 inline mr-2"></i>
                    Edit Asset
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Asset Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <!-- Purchase Cost -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="dollar-sign" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Purchase Cost</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ asset.purchase_cost|floatformat:2 }}</p>
                    {% if asset.purchase_date %}
                    <p class="text-xs text-sap-blue-600 mt-1">{{ asset.purchase_date|date:"d M Y" }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Current Book Value -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="trending-up" class="w-6 h-6 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Current Book Value</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ current_book_value|floatformat:2 }}</p>
                    <p class="text-xs text-sap-green-600 mt-1">As of today</p>
                </div>
            </div>
        </div>
        
        <!-- Accumulated Depreciation -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-red-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="trending-down" class="w-6 h-6 text-sap-red-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Accumulated Depreciation</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ asset.accumulated_depreciation|default:0|floatformat:2 }}</p>
                    <p class="text-xs text-sap-red-600 mt-1">{{ asset.get_depreciation_method_display|default:"No method" }}</p>
                </div>
            </div>
        </div>
        
        <!-- Remaining Life -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="clock" class="w-6 h-6 text-sap-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Remaining Life</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ remaining_useful_life|default:0 }}</p>
                    <p class="text-xs text-sap-purple-600 mt-1">years</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Asset Information Sections -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        
        <!-- Basic Information -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="info" class="w-5 h-5 mr-2 text-sap-teal-600"></i>
                    Basic Information
                </h3>
            </div>
            <div class="p-6 space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Asset Name:</span>
                    <span class="text-sm text-sap-gray-900">{{ asset.asset_name }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Asset Code:</span>
                    <span class="text-sm text-sap-gray-900">{{ asset.asset_code }}</span>
                </div>
                {% if asset.serial_number %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Serial Number:</span>
                    <span class="text-sm text-sap-gray-900">{{ asset.serial_number }}</span>
                </div>
                {% endif %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Asset Type:</span>
                    <span class="text-sm text-sap-gray-900">{{ asset.get_asset_type_display }}</span>
                </div>
                {% if asset.category %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Category:</span>
                    <span class="text-sm text-sap-gray-900">{{ asset.category }}</span>
                </div>
                {% endif %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Status:</span>
                    <span class="text-sm">
                        {% if asset.status == 'active' %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-green-100 text-sap-green-800">
                            Active
                        </span>
                        {% elif asset.status == 'under_maintenance' %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-orange-100 text-sap-orange-800">
                            Maintenance
                        </span>
                        {% elif asset.status == 'disposed' %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-red-100 text-sap-red-800">
                            Disposed
                        </span>
                        {% else %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-gray-100 text-sap-gray-800">
                            {{ asset.get_status_display }}
                        </span>
                        {% endif %}
                    </span>
                </div>
            </div>
        </div>

        <!-- Location & Assignment -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="map-pin" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                    Location & Assignment
                </h3>
            </div>
            <div class="p-6 space-y-4">
                {% if asset.location %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Location:</span>
                    <span class="text-sm text-sap-gray-900">{{ asset.location }}</span>
                </div>
                {% endif %}
                {% if asset.department %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Department:</span>
                    <span class="text-sm text-sap-gray-900">{{ asset.department }}</span>
                </div>
                {% endif %}
                {% if asset.assigned_to %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Assigned To:</span>
                    <span class="text-sm text-sap-gray-900">{{ asset.assigned_to }}</span>
                </div>
                {% endif %}
                {% if not asset.location and not asset.department and not asset.assigned_to %}
                <div class="text-center py-4">
                    <i data-lucide="map-pin" class="w-8 h-8 text-sap-gray-400 mx-auto mb-2"></i>
                    <p class="text-sm text-sap-gray-500">No location or assignment information</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Warranty & Insurance Status -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="shield" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                    Warranty & Insurance
                </h3>
            </div>
            <div class="p-6 space-y-4">
                <!-- Warranty Status -->
                <div>
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-sap-gray-600">Warranty Status:</span>
                        {% if is_under_warranty %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-green-100 text-sap-green-800">
                            Active
                        </span>
                        {% else %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-red-100 text-sap-red-800">
                            Expired/None
                        </span>
                        {% endif %}
                    </div>
                    {% if asset.warranty_end_date %}
                    <div class="text-xs text-sap-gray-500">
                        Valid until: {{ asset.warranty_end_date|date:"d M Y" }}
                    </div>
                    {% endif %}
                </div>
                
                <!-- Insurance Status -->
                <div>
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-sap-gray-600">Insurance Status:</span>
                        {% if is_insured %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-green-100 text-sap-green-800">
                            Active
                        </span>
                        {% else %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-red-100 text-sap-red-800">
                            Expired/None
                        </span>
                        {% endif %}
                    </div>
                    {% if asset.insurance_end_date %}
                    <div class="text-xs text-sap-gray-500">
                        Valid until: {{ asset.insurance_end_date|date:"d M Y" }}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Purchase Information and Depreciation -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        
        <!-- Purchase Information -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="shopping-cart" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                    Purchase Information
                </h3>
            </div>
            <div class="p-6 space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Purchase Cost:</span>
                    <span class="text-sm font-medium text-sap-gray-900">₹{{ asset.purchase_cost|floatformat:2 }}</span>
                </div>
                {% if asset.purchase_date %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Purchase Date:</span>
                    <span class="text-sm text-sap-gray-900">{{ asset.purchase_date|date:"d M Y" }}</span>
                </div>
                {% endif %}
                {% if asset.supplier %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Supplier:</span>
                    <span class="text-sm text-sap-gray-900">{{ asset.supplier }}</span>
                </div>
                {% endif %}
                {% if asset.invoice_number %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Invoice Number:</span>
                    <span class="text-sm text-sap-gray-900">{{ asset.invoice_number }}</span>
                </div>
                {% endif %}
                {% if asset.po_number %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">PO Number:</span>
                    <span class="text-sm text-sap-gray-900">{{ asset.po_number }}</span>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Depreciation Information -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="trending-down" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                    Depreciation Information
                </h3>
            </div>
            <div class="p-6 space-y-4">
                {% if asset.depreciation_method %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Method:</span>
                    <span class="text-sm text-sap-gray-900">{{ asset.get_depreciation_method_display }}</span>
                </div>
                {% endif %}
                {% if asset.depreciation_rate %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Rate:</span>
                    <span class="text-sm text-sap-gray-900">{{ asset.depreciation_rate }}% per annum</span>
                </div>
                {% endif %}
                {% if asset.useful_life_years %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Useful Life:</span>
                    <span class="text-sm text-sap-gray-900">{{ asset.useful_life_years }} years</span>
                </div>
                {% endif %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Accumulated Depreciation:</span>
                    <span class="text-sm font-medium text-sap-red-600">₹{{ asset.accumulated_depreciation|default:0|floatformat:2 }}</span>
                </div>
                {% if asset.residual_value %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Residual Value:</span>
                    <span class="text-sm text-sap-gray-900">₹{{ asset.residual_value|floatformat:2 }}</span>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Description and Notes -->
    {% if asset.description or asset.notes %}
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {% if asset.description %}
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="file-text" class="w-5 h-5 mr-2 text-sap-gray-600"></i>
                    Description
                </h3>
            </div>
            <div class="p-6">
                <p class="text-sm text-sap-gray-700">{{ asset.description }}</p>
            </div>
        </div>
        {% endif %}

        {% if asset.notes %}
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="sticky-note" class="w-5 h-5 mr-2 text-sap-yellow-600"></i>
                    Notes
                </h3>
            </div>
            <div class="p-6">
                <p class="text-sm text-sap-gray-700">{{ asset.notes }}</p>
            </div>
        </div>
        {% endif %}
    </div>
    {% endif %}

    <!-- Depreciation Schedule and Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        
        <!-- Depreciation Schedule -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                        <i data-lucide="calendar" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                        Depreciation Schedule
                    </h3>
                    <button type="button" onclick="downloadDepreciationSchedule()" 
                            class="text-sap-purple-600 hover:text-sap-purple-900 text-sm font-medium">
                        <i data-lucide="download" class="w-4 h-4 inline mr-1"></i>
                        Download
                    </button>
                </div>
            </div>
            <div class="p-6">
                {% if depreciation_schedule %}
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="border-b border-sap-gray-200">
                                <th class="text-left text-xs font-medium text-sap-gray-500 uppercase py-2">Year</th>
                                <th class="text-right text-xs font-medium text-sap-gray-500 uppercase py-2">Depreciation</th>
                                <th class="text-right text-xs font-medium text-sap-gray-500 uppercase py-2">Book Value</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-sap-gray-100">
                            {% for year_data in depreciation_schedule|slice:":5" %}
                            <tr>
                                <td class="py-2 text-sm text-sap-gray-900">Year {{ year_data.year }}</td>
                                <td class="py-2 text-sm text-sap-gray-900 text-right">₹{{ year_data.depreciation|floatformat:2 }}</td>
                                <td class="py-2 text-sm text-sap-gray-900 text-right">₹{{ year_data.book_value|floatformat:2 }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% if depreciation_schedule|length > 5 %}
                    <div class="text-center mt-4">
                        <button type="button" onclick="showFullSchedule()" class="text-sap-purple-600 hover:text-sap-purple-900 text-sm font-medium">
                            Show All {{ depreciation_schedule|length }} Years
                        </button>
                    </div>
                    {% endif %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i data-lucide="calendar" class="w-8 h-8 text-sap-gray-400 mx-auto mb-2"></i>
                    <p class="text-sm text-sap-gray-500">No depreciation schedule available</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Recent Register Entries -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                        <i data-lucide="activity" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Recent Activity
                    </h3>
                    <a href="{% url 'accounts:asset_register_list' %}?asset={{ asset.id }}" 
                       class="text-sap-blue-600 hover:text-sap-blue-900 text-sm font-medium">
                        View All
                    </a>
                </div>
            </div>
            <div class="p-6">
                {% if register_entries %}
                <div class="space-y-4">
                    {% for entry in register_entries %}
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="text-sm font-medium text-sap-gray-900">{{ entry.get_transaction_type_display }}</div>
                            <div class="text-xs text-sap-gray-500">{{ entry.transaction_date|date:"d M Y" }}</div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-sap-gray-900">₹{{ entry.transaction_amount|floatformat:2 }}</div>
                            {% if entry.reference_number %}
                            <div class="text-xs text-sap-gray-500">Ref: {{ entry.reference_number }}</div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i data-lucide="activity" class="w-8 h-8 text-sap-gray-400 mx-auto mb-2"></i>
                    <p class="text-sm text-sap-gray-500">No recent activity</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="mt-8 flex items-center justify-center space-x-4">
        <a href="{% url 'accounts:asset_register_create' %}?asset={{ asset.id }}" 
           class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
            <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
            Add Register Entry
        </a>
        <button type="button" onclick="generateQRCode()" 
                class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
            <i data-lucide="qr-code" class="w-4 h-4 inline mr-2"></i>
            Generate QR Code
        </button>
        <button type="button" onclick="printAssetLabel()" 
                class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
            <i data-lucide="printer" class="w-4 h-4 inline mr-2"></i>
            Print Asset Label
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function downloadDepreciationSchedule() {
    window.open(`/accounts/masters/assets/{{ asset.id }}/depreciation-schedule/`, '_blank');
}

function showFullSchedule() {
    alert('Full depreciation schedule display functionality would be implemented here.');
}

function generateQRCode() {
    alert('QR code generation functionality would be implemented here.');
}

function printAssetLabel() {
    window.print();
}

// Initialize lucide icons on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}