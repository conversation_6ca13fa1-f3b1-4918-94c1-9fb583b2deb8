"""
Material Planning Forms - Task Group 2
Core planning functionality forms with validation and HTMX integration
"""

from django import forms
from django.forms import inlineformset_factory
from django.core.validators import RegexValidator
from django.core.exceptions import ValidationError
import json

from ..models import (
    MaterialPlan, PlanningLineItem, PlanningSupplier, PlanningSchedule
)


class MaterialPlanForm(forms.ModelForm):
    """Form for material plan creation and editing with comprehensive validation"""
    
    class Meta:
        model = MaterialPlan
        fields = [
            'plan_number', 'plan_name', 'plan_type', 'work_order_number',
            'customer_name', 'enquiry_number', 'purchase_order_number',
            'plan_description', 'technical_requirements', 'quality_specifications',
            'delivery_requirements', 'plan_start_date', 'plan_end_date',
            'required_delivery_date', 'total_quantity', 'estimated_total_cost',
            'approved_budget', 'priority', 'risk_level', 'constraints',
            'critical_path_items', 'plan_manager', 'planning_team',
            'bom_reference', 'project_reference', 'contract_reference'
        ]
        
        widgets = {
            'plan_number': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Plan Number (e.g., MP-2024-001)',
                'required': True,
                'x-model': 'planData.plan_number'
            }),
            'plan_name': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Plan Name',
                'required': True,
                'x-model': 'planData.plan_name'
            }),
            'plan_type': forms.Select(attrs={
                'class': 'form-select',
                'x-model': 'planData.plan_type'
            }),
            'work_order_number': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Work Order Number',
                'required': True,
                'x-model': 'planData.work_order_number'
            }),
            'customer_name': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Customer Name',
                'x-model': 'planData.customer_name'
            }),
            'enquiry_number': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Enquiry Number',
                'x-model': 'planData.enquiry_number'
            }),
            'purchase_order_number': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Purchase Order Number',
                'x-model': 'planData.purchase_order_number'
            }),
            'plan_description': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 4,
                'placeholder': 'Detailed plan description',
                'required': True
            }),
            'technical_requirements': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 3,
                'placeholder': 'Technical requirements and specifications'
            }),
            'quality_specifications': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 3,
                'placeholder': 'Quality specifications and standards'
            }),
            'delivery_requirements': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 3,
                'placeholder': 'Delivery requirements and constraints'
            }),
            'plan_start_date': forms.DateInput(attrs={
                'class': 'form-input',
                'type': 'date',
                'required': True,
                'x-on:change': 'validateDateRange'
            }),
            'plan_end_date': forms.DateInput(attrs={
                'class': 'form-input',
                'type': 'date',
                'required': True,
                'x-on:change': 'validateDateRange'
            }),
            'required_delivery_date': forms.DateInput(attrs={
                'class': 'form-input',
                'type': 'date',
                'required': True,
                'x-on:change': 'validateDeliveryDate'
            }),
            'total_quantity': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.001',
                'placeholder': 'Total quantity',
                'required': True,
                'x-on:change': 'calculateCosts'
            }),
            'estimated_total_cost': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.01',
                'placeholder': 'Estimated total cost',
                'x-on:change': 'validateBudget'
            }),
            'approved_budget': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.01',
                'placeholder': 'Approved budget',
                'x-on:change': 'validateBudget'
            }),
            'priority': forms.Select(attrs={
                'class': 'form-select',
                'x-model': 'planData.priority'
            }),
            'risk_level': forms.Select(attrs={
                'class': 'form-select',
                'x-model': 'planData.risk_level'
            }),
            'constraints': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 3,
                'placeholder': 'Constraints and limitations (JSON format)'
            }),
            'critical_path_items': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 3,
                'placeholder': 'Critical path items (JSON array)'
            }),
            'plan_manager': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Plan Manager',
                'required': True
            }),
            'planning_team': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 2,
                'placeholder': 'Planning team members (JSON array)'
            }),
            'bom_reference': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'BOM Reference'
            }),
            'project_reference': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Project Reference'
            }),
            'contract_reference': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Contract Reference'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Add risk level choices
        self.fields['risk_level'].choices = [
            ('low', 'Low Risk'),
            ('medium', 'Medium Risk'),
            ('high', 'High Risk'),
            ('critical', 'Critical Risk'),
        ]
        
        # Add plan number validation
        self.fields['plan_number'].validators.append(
            RegexValidator(
                regex=r'^[A-Z0-9\-_]+$',
                message="Plan number must contain only uppercase letters, numbers, hyphens and underscores"
            )
        )

    def clean_plan_number(self):
        plan_number = self.cleaned_data.get('plan_number')
        if plan_number:
            plan_number = plan_number.upper()
        return plan_number

    def clean(self):
        cleaned_data = super().clean()
        
        # Validate date range
        plan_start_date = cleaned_data.get('plan_start_date')
        plan_end_date = cleaned_data.get('plan_end_date')
        required_delivery_date = cleaned_data.get('required_delivery_date')
        
        if plan_start_date and plan_end_date:
            if plan_end_date <= plan_start_date:
                raise ValidationError("Plan end date must be after start date.")
        
        if plan_end_date and required_delivery_date:
            if required_delivery_date < plan_end_date:
                self.add_error('required_delivery_date', 
                              "Required delivery date should not be before plan end date.")
        
        # Validate budget
        estimated_cost = cleaned_data.get('estimated_total_cost', 0)
        approved_budget = cleaned_data.get('approved_budget', 0)
        
        if estimated_cost > 0 and approved_budget > 0:
            if estimated_cost > approved_budget * 1.1:  # 10% tolerance
                self.add_error('estimated_total_cost', 
                              "Estimated cost significantly exceeds approved budget.")
        
        # Validate JSON fields
        json_fields = ['constraints', 'critical_path_items', 'planning_team']
        for field_name in json_fields:
            field_value = cleaned_data.get(field_name)
            if field_value:
                try:
                    json.loads(field_value)
                except json.JSONDecodeError:
                    self.add_error(field_name, f"{field_name.replace('_', ' ').title()} must be valid JSON format.")
        
        return cleaned_data


class PlanningLineItemForm(forms.ModelForm):
    """Form for planning line items with comprehensive validation"""
    
    class Meta:
        model = PlanningLineItem
        fields = [
            'line_number', 'item_code', 'item_description', 'item_type',
            'item_category', 'technical_specification', 'quality_standards',
            'material_grade', 'dimensional_requirements', 'required_quantity',
            'unit_of_measure', 'estimated_unit_cost', 'required_date',
            'lead_time_days', 'bom_item_reference', 'bom_level',
            'parent_assembly', 'assembly_position', 'is_critical_path',
            'is_long_lead_item', 'requires_inspection', 'supply_risk_level',
            'alternative_sources', 'risk_mitigation_plan'
        ]
        
        widgets = {
            'line_number': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '1',
                'required': True,
                'x-model': 'itemData.line_number'
            }),
            'item_code': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Item Code',
                'required': True,
                'x-model': 'itemData.item_code'
            }),
            'item_description': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 2,
                'placeholder': 'Item description',
                'required': True
            }),
            'item_type': forms.Select(attrs={
                'class': 'form-select',
                'x-model': 'itemData.item_type'
            }),
            'item_category': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Item category'
            }),
            'technical_specification': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 3,
                'placeholder': 'Technical specifications'
            }),
            'quality_standards': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 2,
                'placeholder': 'Quality standards'
            }),
            'material_grade': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Material grade'
            }),
            'dimensional_requirements': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 2,
                'placeholder': 'Dimensional requirements'
            }),
            'required_quantity': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.001',
                'placeholder': 'Required quantity',
                'required': True,
                'x-on:change': 'calculateLineCost'
            }),
            'unit_of_measure': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Unit of measure',
                'required': True
            }),
            'estimated_unit_cost': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.0001',
                'placeholder': 'Estimated unit cost',
                'x-on:change': 'calculateLineCost'
            }),
            'required_date': forms.DateInput(attrs={
                'class': 'form-input',
                'type': 'date',
                'required': True
            }),
            'lead_time_days': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'value': '0',
                'placeholder': 'Lead time (days)'
            }),
            'bom_item_reference': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'BOM item reference'
            }),
            'bom_level': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '1',
                'value': '1',
                'placeholder': 'BOM level'
            }),
            'parent_assembly': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Parent assembly'
            }),
            'assembly_position': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Assembly position'
            }),
            'is_critical_path': forms.CheckboxInput(attrs={
                'class': 'form-checkbox'
            }),
            'is_long_lead_item': forms.CheckboxInput(attrs={
                'class': 'form-checkbox'
            }),
            'requires_inspection': forms.CheckboxInput(attrs={
                'class': 'form-checkbox'
            }),
            'supply_risk_level': forms.Select(attrs={
                'class': 'form-select'
            }),
            'alternative_sources': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 2,
                'placeholder': 'Alternative sources (JSON array)'
            }),
            'risk_mitigation_plan': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 3,
                'placeholder': 'Risk mitigation plan'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Add supply risk level choices
        self.fields['supply_risk_level'].choices = [
            ('low', 'Low Risk'),
            ('medium', 'Medium Risk'),
            ('high', 'High Risk'),
            ('critical', 'Critical Risk'),
        ]

    def clean(self):
        cleaned_data = super().clean()
        
        # Calculate budgeted total cost
        required_quantity = cleaned_data.get('required_quantity', 0)
        estimated_unit_cost = cleaned_data.get('estimated_unit_cost', 0)
        
        if required_quantity and estimated_unit_cost:
            cleaned_data['budgeted_total_cost'] = required_quantity * estimated_unit_cost
        
        # Validate alternative sources JSON
        alternative_sources = cleaned_data.get('alternative_sources')
        if alternative_sources:
            try:
                json.loads(alternative_sources)
            except json.JSONDecodeError:
                self.add_error('alternative_sources', "Alternative sources must be valid JSON format.")
        
        return cleaned_data


class PlanningSupplierForm(forms.ModelForm):
    """Form for supplier assignments with evaluation metrics"""
    
    class Meta:
        model = PlanningSupplier
        fields = [
            'supplier_code', 'supplier_name', 'supplier_category', 'supplier_type',
            'contact_person', 'contact_email', 'contact_phone', 'supplier_address',
            'capability_description', 'capacity_per_month', 'technology_level',
            'certifications', 'quality_rating', 'delivery_rating', 'cost_rating',
            'service_rating', 'on_time_delivery_percentage', 'quality_acceptance_percentage',
            'cost_competitiveness_index', 'financial_stability', 'supply_chain_risk',
            'geographic_risk', 'technology_risk', 'payment_terms', 'currency',
            'incoterms', 'warranty_terms', 'is_preferred_supplier', 'is_strategic_partner'
        ]
        
        widgets = {
            'supplier_code': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Supplier Code',
                'required': True
            }),
            'supplier_name': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Supplier Name',
                'required': True
            }),
            'supplier_category': forms.Select(attrs={
                'class': 'form-select',
                'required': True,
                'x-model': 'supplierData.category'
            }),
            'supplier_type': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Supplier Type'
            }),
            'contact_person': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Contact Person'
            }),
            'contact_email': forms.EmailInput(attrs={
                'class': 'form-input',
                'placeholder': 'Contact Email'
            }),
            'contact_phone': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Contact Phone'
            }),
            'supplier_address': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 3,
                'placeholder': 'Supplier Address'
            }),
            'capability_description': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 3,
                'placeholder': 'Capability description'
            }),
            'capacity_per_month': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.01',
                'placeholder': 'Capacity per month'
            }),
            'technology_level': forms.Select(attrs={
                'class': 'form-select'
            }),
            'certifications': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 2,
                'placeholder': 'Certifications (JSON array)'
            }),
            'quality_rating': forms.Select(attrs={
                'class': 'form-select'
            }),
            'delivery_rating': forms.Select(attrs={
                'class': 'form-select'
            }),
            'cost_rating': forms.Select(attrs={
                'class': 'form-select'
            }),
            'service_rating': forms.Select(attrs={
                'class': 'form-select'
            }),
            'on_time_delivery_percentage': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'max': '100',
                'step': '0.01',
                'placeholder': 'On-time delivery %'
            }),
            'quality_acceptance_percentage': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'max': '100',
                'step': '0.01',
                'placeholder': 'Quality acceptance %'
            }),
            'cost_competitiveness_index': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.01',
                'value': '100',
                'placeholder': 'Cost competitiveness index'
            }),
            'financial_stability': forms.Select(attrs={
                'class': 'form-select'
            }),
            'supply_chain_risk': forms.Select(attrs={
                'class': 'form-select'
            }),
            'geographic_risk': forms.Select(attrs={
                'class': 'form-select'
            }),
            'technology_risk': forms.Select(attrs={
                'class': 'form-select'
            }),
            'payment_terms': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Payment terms'
            }),
            'currency': forms.TextInput(attrs={
                'class': 'form-input',
                'value': 'INR',
                'placeholder': 'Currency'
            }),
            'incoterms': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Incoterms'
            }),
            'warranty_terms': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 2,
                'placeholder': 'Warranty terms'
            }),
            'is_preferred_supplier': forms.CheckboxInput(attrs={
                'class': 'form-checkbox'
            }),
            'is_strategic_partner': forms.CheckboxInput(attrs={
                'class': 'form-checkbox'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Add choice options for various fields
        risk_choices = [
            ('low', 'Low'),
            ('medium', 'Medium'),
            ('high', 'High'),
            ('critical', 'Critical'),
        ]
        
        stability_choices = [
            ('stable', 'Stable'),
            ('moderate', 'Moderate'),
            ('unstable', 'Unstable'),
            ('critical', 'Critical'),
        ]
        
        technology_choices = [
            ('basic', 'Basic'),
            ('standard', 'Standard'),
            ('advanced', 'Advanced'),
            ('cutting_edge', 'Cutting Edge'),
        ]
        
        self.fields['technology_level'].choices = technology_choices
        self.fields['financial_stability'].choices = stability_choices
        self.fields['supply_chain_risk'].choices = risk_choices
        self.fields['geographic_risk'].choices = risk_choices
        self.fields['technology_risk'].choices = risk_choices

    def clean(self):
        cleaned_data = super().clean()
        
        # Validate certifications JSON
        certifications = cleaned_data.get('certifications')
        if certifications:
            try:
                json.loads(certifications)
            except json.JSONDecodeError:
                self.add_error('certifications', "Certifications must be valid JSON format.")
        
        return cleaned_data


class PlanningScheduleForm(forms.ModelForm):
    """Form for planning schedule and milestones"""
    
    class Meta:
        model = PlanningSchedule
        fields = [
            'milestone_code', 'milestone_name', 'milestone_type', 'milestone_description',
            'planned_start_date', 'planned_end_date', 'dependency_type', 'lag_days',
            'assigned_resources', 'resource_requirements', 'estimated_effort_hours',
            'risk_factors', 'mitigation_actions', 'is_critical_path',
            'is_baseline_milestone', 'priority_level', 'responsible_person',
            'quality_gate_required', 'quality_criteria'
        ]
        
        widgets = {
            'milestone_code': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Milestone Code',
                'required': True
            }),
            'milestone_name': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Milestone Name',
                'required': True
            }),
            'milestone_type': forms.Select(attrs={
                'class': 'form-select',
                'required': True
            }),
            'milestone_description': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 3,
                'placeholder': 'Milestone description'
            }),
            'planned_start_date': forms.DateInput(attrs={
                'class': 'form-input',
                'type': 'date',
                'required': True
            }),
            'planned_end_date': forms.DateInput(attrs={
                'class': 'form-input',
                'type': 'date',
                'required': True
            }),
            'dependency_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'lag_days': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'value': '0',
                'placeholder': 'Lag days'
            }),
            'assigned_resources': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 2,
                'placeholder': 'Assigned resources (JSON array)'
            }),
            'resource_requirements': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 3,
                'placeholder': 'Resource requirements'
            }),
            'estimated_effort_hours': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.01',
                'placeholder': 'Estimated effort (hours)'
            }),
            'risk_factors': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 3,
                'placeholder': 'Risk factors (JSON array)'
            }),
            'mitigation_actions': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 3,
                'placeholder': 'Mitigation actions'
            }),
            'is_critical_path': forms.CheckboxInput(attrs={
                'class': 'form-checkbox'
            }),
            'is_baseline_milestone': forms.CheckboxInput(attrs={
                'class': 'form-checkbox'
            }),
            'priority_level': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '1',
                'max': '5',
                'value': '3',
                'placeholder': 'Priority (1=High, 5=Low)'
            }),
            'responsible_person': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Responsible person'
            }),
            'quality_gate_required': forms.CheckboxInput(attrs={
                'class': 'form-checkbox'
            }),
            'quality_criteria': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 2,
                'placeholder': 'Quality criteria'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Add dependency type choices
        self.fields['dependency_type'].choices = [
            ('finish_to_start', 'Finish to Start'),
            ('start_to_start', 'Start to Start'),
            ('finish_to_finish', 'Finish to Finish'),
            ('start_to_finish', 'Start to Finish'),
        ]

    def clean(self):
        cleaned_data = super().clean()
        
        # Validate date range
        start_date = cleaned_data.get('planned_start_date')
        end_date = cleaned_data.get('planned_end_date')
        
        if start_date and end_date and end_date <= start_date:
            raise ValidationError("End date must be after start date.")
        
        # Validate JSON fields
        json_fields = ['assigned_resources', 'risk_factors']
        for field_name in json_fields:
            field_value = cleaned_data.get(field_name)
            if field_value:
                try:
                    json.loads(field_value)
                except json.JSONDecodeError:
                    self.add_error(field_name, f"{field_name.replace('_', ' ').title()} must be valid JSON format.")
        
        return cleaned_data


class MaterialPlanSearchForm(forms.Form):
    """Advanced material plan search form"""
    
    search_query = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input',
            'placeholder': 'Search plans...',
            'x-model': 'searchQuery'
        })
    )
    
    work_order_number = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input',
            'placeholder': 'Work Order Number'
        })
    )
    
    customer_name = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input',
            'placeholder': 'Customer Name'
        })
    )
    
    enquiry_number = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input',
            'placeholder': 'Enquiry Number'
        })
    )
    
    purchase_order_number = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input',
            'placeholder': 'Purchase Order Number'
        })
    )
    
    plan_type = forms.ChoiceField(
        choices=[('', 'All Plan Types')] + MaterialPlan.PLAN_TYPE,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    status = forms.ChoiceField(
        choices=[('', 'All Statuses')] + MaterialPlan.PLAN_STATUS,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    priority = forms.ChoiceField(
        choices=[('', 'All Priorities')] + MaterialPlan.PLAN_PRIORITY,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-input', 'type': 'date'})
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-input', 'type': 'date'})
    )


# Formsets for inline editing
PlanningLineItemFormSet = inlineformset_factory(
    MaterialPlan, PlanningLineItem,
    form=PlanningLineItemForm,
    extra=3,
    can_delete=True
)

PlanningSupplierFormSet = inlineformset_factory(
    MaterialPlan, PlanningSupplier,
    form=PlanningSupplierForm,
    extra=1,
    can_delete=True
)

PlanningScheduleFormSet = inlineformset_factory(
    MaterialPlan, PlanningSchedule,
    form=PlanningScheduleForm,
    extra=2,
    can_delete=True
)