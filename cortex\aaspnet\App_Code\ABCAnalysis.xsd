﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="ABCAnalysis" targetNamespace="http://tempuri.org/ABCAnalysis.xsd" xmlns:mstns="http://tempuri.org/ABCAnalysis.xsd" xmlns="http://tempuri.org/ABCAnalysis.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="ABCAnalysis" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="ABCAnalysis" msprop:Generator_DataSetName="ABCAnalysis">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" msprop:Generator_UserColumnName="Id" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInTable="IdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="ItemCode" msprop:Generator_UserColumnName="ItemCode" msprop:Generator_ColumnPropNameInRow="ItemCode" msprop:Generator_ColumnVarNameInTable="columnItemCode" msprop:Generator_ColumnPropNameInTable="ItemCodeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ManfDesc" msprop:Generator_UserColumnName="ManfDesc" msprop:Generator_ColumnPropNameInRow="ManfDesc" msprop:Generator_ColumnVarNameInTable="columnManfDesc" msprop:Generator_ColumnPropNameInTable="ManfDescColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Category" msprop:Generator_UserColumnName="Category" msprop:Generator_ColumnPropNameInRow="Category" msprop:Generator_ColumnVarNameInTable="columnCategory" msprop:Generator_ColumnPropNameInTable="CategoryColumn" type="xs:string" minOccurs="0" />
              <xs:element name="SubCategory" msprop:Generator_UserColumnName="SubCategory" msprop:Generator_ColumnPropNameInRow="SubCategory" msprop:Generator_ColumnVarNameInTable="columnSubCategory" msprop:Generator_ColumnPropNameInTable="SubCategoryColumn" type="xs:string" minOccurs="0" />
              <xs:element name="UOM" msprop:Generator_UserColumnName="UOM" msprop:Generator_ColumnPropNameInRow="UOM" msprop:Generator_ColumnVarNameInTable="columnUOM" msprop:Generator_ColumnPropNameInTable="UOMColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="GQNQTY" msprop:Generator_UserColumnName="GQNQTY" msprop:Generator_ColumnPropNameInRow="GQNQTY" msprop:Generator_ColumnVarNameInTable="columnGQNQTY" msprop:Generator_ColumnPropNameInTable="GQNQTYColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ISSUEQTY" msprop:Generator_UserColumnName="ISSUEQTY" msprop:Generator_ColumnPropNameInRow="ISSUEQTY" msprop:Generator_ColumnVarNameInTable="columnISSUEQTY" msprop:Generator_ColumnPropNameInTable="ISSUEQTYColumn" type="xs:double" minOccurs="0" />
              <xs:element name="OPENINGQTY" msprop:Generator_UserColumnName="OPENINGQTY" msprop:Generator_ColumnPropNameInRow="OPENINGQTY" msprop:Generator_ColumnVarNameInTable="columnOPENINGQTY" msprop:Generator_ColumnPropNameInTable="OPENINGQTYColumn" type="xs:double" minOccurs="0" />
              <xs:element name="CLOSINGQTY" msprop:Generator_UserColumnName="CLOSINGQTY" msprop:Generator_ColumnPropNameInRow="CLOSINGQTY" msprop:Generator_ColumnVarNameInTable="columnCLOSINGQTY" msprop:Generator_ColumnPropNameInTable="CLOSINGQTYColumn" type="xs:double" minOccurs="0" />
              <xs:element name="RateReg" msprop:Generator_UserColumnName="RateReg" msprop:Generator_ColumnPropNameInRow="RateReg" msprop:Generator_ColumnVarNameInTable="columnRateReg" msprop:Generator_ColumnPropNameInTable="RateRegColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Amount" msprop:Generator_UserColumnName="Amount" msprop:Generator_ColumnPropNameInRow="Amount" msprop:Generator_ColumnVarNameInTable="columnAmount" msprop:Generator_ColumnPropNameInTable="AmountColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Type" msprop:Generator_UserColumnName="Type" msprop:Generator_ColumnVarNameInTable="columnType" msprop:Generator_ColumnPropNameInRow="Type" msprop:Generator_ColumnPropNameInTable="TypeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="AP" msprop:Generator_UserColumnName="AP" msprop:Generator_ColumnVarNameInTable="columnAP" msprop:Generator_ColumnPropNameInRow="AP" msprop:Generator_ColumnPropNameInTable="APColumn" type="xs:double" minOccurs="0" />
              <xs:element name="CP" msprop:Generator_UserColumnName="CP" msprop:Generator_ColumnVarNameInTable="columnCP" msprop:Generator_ColumnPropNameInRow="CP" msprop:Generator_ColumnPropNameInTable="CPColumn" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>