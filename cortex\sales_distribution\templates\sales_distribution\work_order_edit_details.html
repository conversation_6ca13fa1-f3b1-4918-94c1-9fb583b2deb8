{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }} - Sales Distribution{% endblock %}

{% block extra_css %}
<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/umd/lucide.min.css">
<script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-sap-gray-50 font-sap">
    <!-- SAP Fiori Header -->
    <div class="bg-white shadow-sm border-b border-sap-gray-200">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                            <i data-lucide="edit-3" class="w-5 h-5 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-xl font-semibold text-sap-gray-900">{{ page_title }}</h1>
                            <p class="text-sm text-sap-gray-600">Comprehensive work order management and editing</p>
                        </div>
                    </div>
                </div>
                <nav class="flex items-center space-x-2 text-sm text-sap-gray-500">
                    <a href="{% url 'sales_distribution:dashboard' %}" class="hover:text-sap-blue-600 transition-colors">Sales & Distribution</a>
                    <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    <a href="{% url 'sales_distribution:work_order_edit_list' %}" class="hover:text-sap-blue-600 transition-colors">Work Orders</a>
                    <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    <span class="text-sap-gray-900 font-medium">{{ work_order.wono|default:"Edit Work Order" }}</span>
                </nav>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-6 py-8">
        <!-- Work Order Overview Card -->
        <div class="sap-card mb-6">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-sap-blue-100 rounded-xl flex items-center justify-center">
                        <i data-lucide="file-text" class="w-5 h-5 text-sap-blue-600"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-semibold text-sap-gray-900">Work Order Overview</h2>
                        <p class="text-sm text-sap-gray-600">Essential work order information and current status</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i data-lucide="circle" class="w-3 h-3 mr-1 fill-current"></i>
                            Active
                        </span>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-sap-gray-50 rounded-lg p-4">
                    <div class="text-xs font-medium text-sap-gray-500 mb-1">Customer Name</div>
                    <div class="text-sm font-semibold text-sap-gray-900">{{ customer.customer_name|default:"-" }}</div>
                </div>
                <div class="bg-sap-gray-50 rounded-lg p-4">
                    <div class="text-xs font-medium text-sap-gray-500 mb-1">Work Order No</div>
                    <div class="text-sm font-semibold text-sap-blue-600">{{ work_order.wono|default:"-" }}</div>
                </div>
                <div class="bg-sap-gray-50 rounded-lg p-4">
                    <div class="text-xs font-medium text-sap-gray-500 mb-1">PO Number</div>
                    <div class="text-sm font-semibold text-sap-gray-900">{{ work_order.pono|default:"-" }}</div>
                </div>
                <div class="bg-sap-gray-50 rounded-lg p-4">
                    <div class="text-xs font-medium text-sap-gray-500 mb-1">Enquiry No</div>
                    <div class="text-sm font-semibold text-sap-gray-900">{{ work_order.enqid.enqid|default:"-" }}</div>
                </div>
            </div>
        </div>

        <!-- Modern SAP Tabbed Interface -->
        <div class="sap-card" x-data="{ activeTab: '{{ active_tab|default:'0' }}' }">
            <!-- Tab Navigation -->
            <div class="border-b border-sap-gray-200 mb-8">
                <nav class="flex space-x-8">
                    <button type="button" @click="activeTab = '0'" 
                            :class="activeTab === '0' ? 'border-sap-blue-600 text-sap-blue-600' : 'border-transparent text-sap-gray-500 hover:text-sap-gray-700 hover:border-sap-gray-300'"
                            class="flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                        <i data-lucide="briefcase" class="w-4 h-4 mr-2"></i>
                        Task Execution
                    </button>
                    <button type="button" @click="activeTab = '1'"
                            :class="activeTab === '1' ? 'border-sap-blue-600 text-sap-blue-600' : 'border-transparent text-sap-gray-500 hover:text-sap-gray-700 hover:border-sap-gray-300'"
                            class="flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                        <i data-lucide="truck" class="w-4 h-4 mr-2"></i>
                        Shipping
                    </button>
                    <button type="button" @click="activeTab = '2'"
                            :class="activeTab === '2' ? 'border-sap-blue-600 text-sap-blue-600' : 'border-transparent text-sap-gray-500 hover:text-sap-gray-700 hover:border-sap-gray-300'"
                            class="flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                        <i data-lucide="package" class="w-4 h-4 mr-2"></i>
                        Products
                    </button>
                    <button type="button" @click="activeTab = '3'"
                            :class="activeTab === '3' ? 'border-sap-blue-600 text-sap-blue-600' : 'border-transparent text-sap-gray-500 hover:text-sap-gray-700 hover:border-sap-gray-300'"
                            class="flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                        <i data-lucide="clipboard-list" class="w-4 h-4 mr-2"></i>
                        Instructions
                    </button>
                </nav>
            </div>

            <!-- Form Content -->
            <form method="post" id="work-order-form">
                {% csrf_token %}
                
                <!-- Task Execution Tab -->
                <div x-show="activeTab === '0'" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100">
                    <div class="space-y-8">
                        <!-- Basic Information Section -->
                        <div>
                            <div class="flex items-center space-x-2 mb-6">
                                <div class="w-6 h-6 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="info" class="w-4 h-4 text-sap-blue-600"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-sap-gray-900">Basic Information</h3>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                <div class="space-y-2">
                                    <label for="{{ task_form.work_order_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                        Work Order Date <span class="text-red-500">*</span>
                                    </label>
                                    <div class="relative">
                                        <i data-lucide="calendar" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-sap-gray-400"></i>
                                        {{ task_form.work_order_date|add_class:"sap-input pl-10" }}
                                    </div>
                                    {% if task_form.work_order_date.errors %}
                                        <div class="text-red-500 text-xs">{{ task_form.work_order_date.errors }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="space-y-2">
                                    <label for="{{ task_form.category.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                        Category <span class="text-red-500">*</span>
                                    </label>
                                    {{ task_form.category|add_class:"sap-input" }}
                                    {% if task_form.category.errors %}
                                        <div class="text-red-500 text-xs">{{ task_form.category.errors }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="space-y-2">
                                    <label for="{{ task_form.subcategory.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                        Sub Category
                                    </label>
                                    {{ task_form.subcategory|add_class:"sap-input" }}
                                    {% if task_form.subcategory.errors %}
                                        <div class="text-red-500 text-xs">{{ task_form.subcategory.errors }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="space-y-2">
                                    <label for="{{ task_form.business_group.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                        Business Group
                                    </label>
                                    {{ task_form.business_group|add_class:"sap-input" }}
                                    {% if task_form.business_group.errors %}
                                        <div class="text-red-500 text-xs">{{ task_form.business_group.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 gap-6 mt-6">
                                <div class="space-y-2">
                                    <label for="{{ task_form.project_title.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                        Project Title <span class="text-red-500">*</span>
                                    </label>
                                    {{ task_form.project_title|add_class:"sap-input" }}
                                    {% if task_form.project_title.errors %}
                                        <div class="text-red-500 text-xs">{{ task_form.project_title.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                                <div class="space-y-2">
                                    <label for="{{ task_form.project_leader.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                        Project Leader <span class="text-red-500">*</span>
                                    </label>
                                    <div class="relative">
                                        <i data-lucide="user" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-sap-gray-400"></i>
                                        {{ task_form.project_leader|add_class:"sap-input pl-10" }}
                                    </div>
                                    {% if task_form.project_leader.errors %}
                                        <div class="text-red-500 text-xs">{{ task_form.project_leader.errors }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="space-y-2">
                                    <label for="{{ task_form.buyer.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                        Material Buyer
                                    </label>
                                    <div class="relative">
                                        <i data-lucide="shopping-cart" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-sap-gray-400"></i>
                                        {{ task_form.buyer|add_class:"sap-input pl-10" }}
                                    </div>
                                    {% if task_form.buyer.errors %}
                                        <div class="text-red-500 text-xs">{{ task_form.buyer.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                    <!-- Date Ranges Section -->
                    <h4 class="text-md font-semibold text-gray-700 mt-6 mb-4">Target Dates</h4>
                    
                    <!-- Target DAP Date -->
                    <div class="form-group">
                        <label>Target DAP Date</label>
                        <div class="date-range">
                            <div>
                                {{ task_form.target_dap_from_date }}
                                {% if task_form.target_dap_from_date.errors %}
                                    <div class="text-red-500 text-sm mt-1">{{ task_form.target_dap_from_date.errors }}</div>
                                {% endif %}
                            </div>
                            <span class="separator">To</span>
                            <div>
                                {{ task_form.target_dap_to_date }}
                                {% if task_form.target_dap_to_date.errors %}
                                    <div class="text-red-500 text-sm mt-1">{{ task_form.target_dap_to_date.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Design Finalization Date -->
                    <div class="form-group">
                        <label>Design Finalization Date</label>
                        <div class="date-range">
                            <div>
                                {{ task_form.design_finalization_from_date }}
                                {% if task_form.design_finalization_from_date.errors %}
                                    <div class="text-red-500 text-sm mt-1">{{ task_form.design_finalization_from_date.errors }}</div>
                                {% endif %}
                            </div>
                            <span class="separator">To</span>
                            <div>
                                {{ task_form.design_finalization_to_date }}
                                {% if task_form.design_finalization_to_date.errors %}
                                    <div class="text-red-500 text-sm mt-1">{{ task_form.design_finalization_to_date.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Target Manufacturing Date -->
                    <div class="form-group">
                        <label>Target Manufacturing Date</label>
                        <div class="date-range">
                            <div>
                                {{ task_form.target_manufacturing_from_date }}
                                {% if task_form.target_manufacturing_from_date.errors %}
                                    <div class="text-red-500 text-sm mt-1">{{ task_form.target_manufacturing_from_date.errors }}</div>
                                {% endif %}
                            </div>
                            <span class="separator">To</span>
                            <div>
                                {{ task_form.target_manufacturing_to_date }}
                                {% if task_form.target_manufacturing_to_date.errors %}
                                    <div class="text-red-500 text-sm mt-1">{{ task_form.target_manufacturing_to_date.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Target Try-out Date -->
                    <div class="form-group">
                        <label>Target Try-out Date</label>
                        <div class="date-range">
                            <div>
                                {{ task_form.target_tryout_from_date }}
                                {% if task_form.target_tryout_from_date.errors %}
                                    <div class="text-red-500 text-sm mt-1">{{ task_form.target_tryout_from_date.errors }}</div>
                                {% endif %}
                            </div>
                            <span class="separator">To</span>
                            <div>
                                {{ task_form.target_tryout_to_date }}
                                {% if task_form.target_tryout_to_date.errors %}
                                    <div class="text-red-500 text-sm mt-1">{{ task_form.target_tryout_to_date.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Target Despatch Date -->
                    <div class="form-group">
                        <label>Target Despatch Date</label>
                        <div class="date-range">
                            <div>
                                {{ task_form.target_despatch_from_date }}
                                {% if task_form.target_despatch_from_date.errors %}
                                    <div class="text-red-500 text-sm mt-1">{{ task_form.target_despatch_from_date.errors }}</div>
                                {% endif %}
                            </div>
                            <span class="separator">To</span>
                            <div>
                                {{ task_form.target_despatch_to_date }}
                                {% if task_form.target_despatch_to_date.errors %}
                                    <div class="text-red-500 text-sm mt-1">{{ task_form.target_despatch_to_date.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Target Assembly Date -->
                    <div class="form-group">
                        <label>Target Assembly Date</label>
                        <div class="date-range">
                            <div>
                                {{ task_form.target_assembly_from_date }}
                                {% if task_form.target_assembly_from_date.errors %}
                                    <div class="text-red-500 text-sm mt-1">{{ task_form.target_assembly_from_date.errors }}</div>
                                {% endif %}
                            </div>
                            <span class="separator">To</span>
                            <div>
                                {{ task_form.target_assembly_to_date }}
                                {% if task_form.target_assembly_to_date.errors %}
                                    <div class="text-red-500 text-sm mt-1">{{ task_form.target_assembly_to_date.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Target Installation Date -->
                    <div class="form-group">
                        <label>Target Installation Date</label>
                        <div class="date-range">
                            <div>
                                {{ task_form.target_installation_from_date }}
                                {% if task_form.target_installation_from_date.errors %}
                                    <div class="text-red-500 text-sm mt-1">{{ task_form.target_installation_from_date.errors }}</div>
                                {% endif %}
                            </div>
                            <span class="separator">To</span>
                            <div>
                                {{ task_form.target_installation_to_date }}
                                {% if task_form.target_installation_to_date.errors %}
                                    <div class="text-red-500 text-sm mt-1">{{ task_form.target_installation_to_date.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Customer Inspection Date -->
                    <div class="form-group">
                        <label>Customer Inspection Date</label>
                        <div class="date-range">
                            <div>
                                {{ task_form.customer_inspection_from_date }}
                                {% if task_form.customer_inspection_from_date.errors %}
                                    <div class="text-red-500 text-sm mt-1">{{ task_form.customer_inspection_from_date.errors }}</div>
                                {% endif %}
                            </div>
                            <span class="separator">To</span>
                            <div>
                                {{ task_form.customer_inspection_to_date }}
                                {% if task_form.customer_inspection_to_date.errors %}
                                    <div class="text-red-500 text-sm mt-1">{{ task_form.customer_inspection_to_date.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Material Procurement Section -->
                    <h4 class="text-md font-semibold text-gray-700 mt-6 mb-4">Material Procurement</h4>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="{{ task_form.manufacturing_material_date.id_for_label }}">Manufacturing Material Date</label>
                            {{ task_form.manufacturing_material_date }}
                            {% if task_form.manufacturing_material_date.errors %}
                                <div class="text-red-500 text-sm mt-1">{{ task_form.manufacturing_material_date.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ task_form.boughtout_material_date.id_for_label }}">Boughtout Material Date</label>
                            {{ task_form.boughtout_material_date }}
                            {% if task_form.boughtout_material_date.errors %}
                                <div class="text-red-500 text-sm mt-1">{{ task_form.boughtout_material_date.errors }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="btn-group">
                        <button type="button" class="btn btn-primary" onclick="nextTab()">Next</button>
                    </div>
                </div>

                <!-- Shipping Tab -->
                <div class="tab-pane {% if active_tab == '1' %}active{% endif %}" id="shipping">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Shipping Information</h3>
                    
                    <div class="form-grid">
                        <div class="form-group" style="grid-column: 1 / 3;">
                            <label for="{{ shipping_form.shipping_address.id_for_label }}">Shipping Address</label>
                            {{ shipping_form.shipping_address }}
                            {% if shipping_form.shipping_address.errors %}
                                <div class="text-red-500 text-sm mt-1">{{ shipping_form.shipping_address.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ shipping_form.shipping_country.id_for_label }}">Country</label>
                            {{ shipping_form.shipping_country }}
                            {% if shipping_form.shipping_country.errors %}
                                <div class="text-red-500 text-sm mt-1">{{ shipping_form.shipping_country.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ shipping_form.shipping_state.id_for_label }}">State</label>
                            {{ shipping_form.shipping_state }}
                            {% if shipping_form.shipping_state.errors %}
                                <div class="text-red-500 text-sm mt-1">{{ shipping_form.shipping_state.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ shipping_form.shipping_city.id_for_label }}">City</label>
                            {{ shipping_form.shipping_city }}
                            {% if shipping_form.shipping_city.errors %}
                                <div class="text-red-500 text-sm mt-1">{{ shipping_form.shipping_city.errors }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <h4 class="text-md font-semibold text-gray-700 mt-6 mb-4">Contact Information</h4>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="{{ shipping_form.contact_person_1.id_for_label }}">Contact Person 1</label>
                            {{ shipping_form.contact_person_1 }}
                            {% if shipping_form.contact_person_1.errors %}
                                <div class="text-red-500 text-sm mt-1">{{ shipping_form.contact_person_1.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ shipping_form.contact_no_1.id_for_label }}">Contact No 1</label>
                            {{ shipping_form.contact_no_1 }}
                            {% if shipping_form.contact_no_1.errors %}
                                <div class="text-red-500 text-sm mt-1">{{ shipping_form.contact_no_1.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ shipping_form.email_1.id_for_label }}">Email 1</label>
                            {{ shipping_form.email_1 }}
                            {% if shipping_form.email_1.errors %}
                                <div class="text-red-500 text-sm mt-1">{{ shipping_form.email_1.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ shipping_form.contact_person_2.id_for_label }}">Contact Person 2</label>
                            {{ shipping_form.contact_person_2 }}
                            {% if shipping_form.contact_person_2.errors %}
                                <div class="text-red-500 text-sm mt-1">{{ shipping_form.contact_person_2.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ shipping_form.contact_no_2.id_for_label }}">Contact No 2</label>
                            {{ shipping_form.contact_no_2 }}
                            {% if shipping_form.contact_no_2.errors %}
                                <div class="text-red-500 text-sm mt-1">{{ shipping_form.contact_no_2.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ shipping_form.email_2.id_for_label }}">Email 2</label>
                            {{ shipping_form.email_2 }}
                            {% if shipping_form.email_2.errors %}
                                <div class="text-red-500 text-sm mt-1">{{ shipping_form.email_2.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ shipping_form.fax_no.id_for_label }}">Fax No</label>
                            {{ shipping_form.fax_no }}
                            {% if shipping_form.fax_no.errors %}
                                <div class="text-red-500 text-sm mt-1">{{ shipping_form.fax_no.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ shipping_form.ecc_no.id_for_label }}">ECC No</label>
                            {{ shipping_form.ecc_no }}
                            {% if shipping_form.ecc_no.errors %}
                                <div class="text-red-500 text-sm mt-1">{{ shipping_form.ecc_no.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ shipping_form.tin_cst_no.id_for_label }}">TIN/CST No</label>
                            {{ shipping_form.tin_cst_no }}
                            {% if shipping_form.tin_cst_no.errors %}
                                <div class="text-red-500 text-sm mt-1">{{ shipping_form.tin_cst_no.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ shipping_form.tin_vat_no.id_for_label }}">TIN/VAT No</label>
                            {{ shipping_form.tin_vat_no }}
                            {% if shipping_form.tin_vat_no.errors %}
                                <div class="text-red-500 text-sm mt-1">{{ shipping_form.tin_vat_no.errors }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="btn-group">
                        <button type="button" class="btn btn-secondary" onclick="prevTab()">Previous</button>
                        <button type="button" class="btn btn-primary" onclick="nextTab()">Next</button>
                    </div>
                </div>

                <!-- Products Tab -->
                <div class="tab-pane {% if active_tab == '2' %}active{% endif %}" id="products">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Products</h3>
                    
                    <!-- Add Product Form -->
                    <div class="bg-gray-50 p-4 rounded-lg mb-6">
                        <h4 class="text-md font-semibold text-gray-700 mb-3">Add New Product</h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="{{ product_form.item_code.id_for_label }}">Item Code</label>
                                {{ product_form.item_code }}
                                {% if product_form.item_code.errors %}
                                    <div class="text-red-500 text-sm mt-1">{{ product_form.item_code.errors }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="form-group">
                                <label for="{{ product_form.description.id_for_label }}">Description</label>
                                {{ product_form.description }}
                                {% if product_form.description.errors %}
                                    <div class="text-red-500 text-sm mt-1">{{ product_form.description.errors }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="form-group">
                                <label for="{{ product_form.quantity.id_for_label }}">Quantity</label>
                                {{ product_form.quantity }}
                                {% if product_form.quantity.errors %}
                                    <div class="text-red-500 text-sm mt-1">{{ product_form.quantity.errors }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="submit" name="action" value="add_product" class="btn btn-primary">Add Product</button>
                            </div>
                        </div>
                    </div>

                    <!-- Products List -->
                    {% if products %}
                    <table class="product-table">
                        <thead>
                            <tr>
                                <th>SN</th>
                                <th>Item Code</th>
                                <th>Description</th>
                                <th>Quantity</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td>{{ product.item_code }}</td>
                                <td>{{ product.description }}</td>
                                <td class="text-right">{{ product.quantity }}</td>
                                <td>
                                    <button type="submit" name="action" value="delete_product" 
                                            onclick="document.getElementById('delete_product_id').value='{{ product.id }}';"
                                            class="btn btn-danger btn-sm">Delete</button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <div class="text-center text-gray-500 py-8">
                        <p>No products added yet. Use the form above to add products.</p>
                    </div>
                    {% endif %}

                    <input type="hidden" id="delete_product_id" name="product_id" value="">

                    <div class="btn-group">
                        <button type="button" class="btn btn-secondary" onclick="prevTab()">Previous</button>
                        <button type="button" class="btn btn-primary" onclick="nextTab()">Next</button>
                    </div>
                </div>

                <!-- Instructions Tab -->
                <div class="tab-pane {% if active_tab == '3' %}active{% endif %}" id="instructions">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Instructions</h3>
                    
                    <div class="space-y-4">
                        <div class="checkbox-group">
                            {{ instructions_form.primer_painting }}
                            <label for="{{ instructions_form.primer_painting.id_for_label }}">Primer Painting to be done</label>
                        </div>
                        
                        <div class="checkbox-group">
                            {{ instructions_form.painting }}
                            <label for="{{ instructions_form.painting.id_for_label }}">Painting to be done</label>
                        </div>
                        
                        <div class="checkbox-group">
                            {{ instructions_form.self_certification_report }}
                            <label for="{{ instructions_form.self_certification_report.id_for_label }}">Self Certification Report to be submitted</label>
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ instructions_form.other_instructions.id_for_label }}">Other Instructions</label>
                            {{ instructions_form.other_instructions }}
                            {% if instructions_form.other_instructions.errors %}
                                <div class="text-red-500 text-sm mt-1">{{ instructions_form.other_instructions.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ instructions_form.export_case_mark.id_for_label }}">Export Case Mark</label>
                            {{ instructions_form.export_case_mark }}
                            {% if instructions_form.export_case_mark.errors %}
                                <div class="text-red-500 text-sm mt-1">{{ instructions_form.export_case_mark.errors }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6">
                        <p class="text-blue-800 text-sm">
                            <strong>Packing Instructions:</strong> Export Seaworthy / Wooden / Corrugated 7 day before desp.
                        </p>
                    </div>

                    <div class="btn-group">
                        <button type="button" class="btn btn-secondary" onclick="prevTab()">Previous</button>
                        <button type="submit" name="action" value="update_work_order" class="btn btn-primary">Update Work Order</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Tab functionality
function showTab(tabId) {
    // Hide all tab panes
    document.querySelectorAll('.tab-pane').forEach(pane => {
        pane.classList.remove('active');
    });
    
    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active');
    });
    
    // Show selected tab pane
    document.getElementById(tabId).classList.add('active');
    
    // Add active class to selected tab button
    document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');
}

// Tab navigation
document.querySelectorAll('.tab-button').forEach(button => {
    button.addEventListener('click', function() {
        const tabId = this.getAttribute('data-tab');
        showTab(tabId);
    });
});

// Next/Previous tab functions
function nextTab() {
    const tabs = ['task-execution', 'shipping', 'products', 'instructions'];
    const currentTab = document.querySelector('.tab-pane.active').id;
    const currentIndex = tabs.indexOf(currentTab);
    
    if (currentIndex < tabs.length - 1) {
        showTab(tabs[currentIndex + 1]);
    }
}

function prevTab() {
    const tabs = ['task-execution', 'shipping', 'products', 'instructions'];
    const currentTab = document.querySelector('.tab-pane.active').id;
    const currentIndex = tabs.indexOf(currentTab);
    
    if (currentIndex > 0) {
        showTab(tabs[currentIndex - 1]);
    }
}

// Form validation
document.getElementById('work-order-form').addEventListener('submit', function(e) {
    const action = e.submitter ? e.submitter.value : '';
    
    if (action === 'update_work_order') {
        // Basic validation
        const requiredFields = ['work_order_date', 'project_title', 'project_leader'];
        let isValid = true;
        
        requiredFields.forEach(fieldName => {
            const field = document.querySelector(`[name="${fieldName}"]`);
            if (field && !field.value.trim()) {
                isValid = false;
                field.classList.add('border-red-500');
            } else if (field) {
                field.classList.remove('border-red-500');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
            showTab('task-execution'); // Go to first tab
        }
    }
});

// Country/State/City cascading dropdowns (if needed)
document.addEventListener('DOMContentLoaded', function() {
    // Add any additional initialization here
});
</script>
{% endblock %}
