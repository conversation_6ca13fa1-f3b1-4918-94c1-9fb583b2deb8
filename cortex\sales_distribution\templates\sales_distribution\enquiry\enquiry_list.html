{% extends "core/base.html" %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-sap-blue-500 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Customer Enquiries</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage and track customer enquiries</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <div class="text-right mr-4 px-4 py-2 bg-sap-gray-50 rounded-lg border border-sap-gray-200">
                    <p class="text-2xs font-medium text-sap-gray-500 uppercase tracking-wide">Total Enquiries</p>
                    <p class="text-lg font-semibold text-sap-blue-600">{{ enquiries|length }}</p>
                </div>
                <a href="{% url 'sales_distribution:enquiry_create' %}" 
                   class="inline-flex items-center px-4 py-2.5 border border-transparent rounded-lg text-sm font-medium text-white bg-sap-blue-600 hover:bg-sap-blue-700 transition-all duration-200">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    Create New Enquiry
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-2 py-4 space-y-2">
    
    <!-- Search and Filter Card -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4">
            <div class="flex items-center space-x-4">
                <div class="flex-1">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i data-lucide="search" class="w-4 h-4 text-sap-gray-400"></i>
                        </div>
                        <input type="text" 
                               id="real-time-search"
                               name="search" 
                               placeholder="Search enquiries by customer name or enquiry number..." 
                               value="{{ request.GET.search|default:'' }}"
                               class="block w-full pl-10 pr-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm placeholder-sap-gray-500 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                    </div>
                </div>
                <div class="w-48">
                    <select name="financial_year" 
                            id="financial-year-filter"
                            class="block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        <option value="">All Financial Years</option>
                        <option value="2022-2023" {% if request.GET.financial_year == "2022-2023" %}selected{% endif %}>2022-2023</option>
                        <option value="2023-2024" {% if request.GET.financial_year == "2023-2024" %}selected{% endif %}>2023-2024</option>
                    </select>
                </div>
                <div class="flex space-x-2">
                    <button type="button" 
                            onclick="clearSearch()"
                            class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                        <i data-lucide="x" class="w-4 h-4 mr-2"></i>
                        Clear
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Enquiries Card Table -->
    <div class="sap-card">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-sap-blue-500 rounded-lg flex items-center justify-center">
                        <i data-lucide="database" class="w-4 h-4 text-white"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-sap-gray-800">Customer Enquiries Management</h3>
                        <p class="text-sm text-sap-gray-600">Comprehensive enquiry tracking and conversion system</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="text-right px-3 py-1.5 bg-sap-blue-50 rounded-lg border border-sap-blue-200">
                        <p class="text-xs font-medium text-sap-blue-600 uppercase">Active Records</p>
                        <p class="text-lg font-bold text-sap-blue-700">{{ enquiries|length }}</p>
                    </div>
                    {% if stats %}
                    <div class="text-right px-3 py-1.5 bg-green-50 rounded-lg border border-green-200">
                        <p class="text-xs font-medium text-green-600 uppercase">Converted</p>
                        <p class="text-lg font-bold text-green-700">{{ stats.converted_enquiries|default:0 }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            {% if enquiries %}
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-gradient-to-r from-sap-gray-50 to-sap-blue-50">
                    <tr>
                        <th scope="col" class="px-4 py-4 text-center text-xs font-semibold text-sap-gray-700 uppercase tracking-wider w-16">
                            <div class="flex flex-col items-center">
                                <i data-lucide="hash" class="w-3 h-3 mb-1 text-sap-gray-500"></i>
                                <span>SN</span>
                            </div>
                        </th>
                        <th scope="col" class="px-4 py-4 text-center text-xs font-semibold text-sap-gray-700 uppercase tracking-wider w-24">
                            <div class="flex flex-col items-center">
                                <i data-lucide="calendar" class="w-3 h-3 mb-1 text-sap-gray-500"></i>
                                <span>Fin Year</span>
                            </div>
                        </th>
                        <th scope="col" class="px-4 py-4 text-left text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">
                            <div class="flex items-center">
                                <i data-lucide="user" class="w-3 h-3 mr-2 text-sap-gray-500"></i>
                                <span>Customer Information</span>
                            </div>
                        </th>
                        <th scope="col" class="px-4 py-4 text-center text-xs font-semibold text-sap-gray-700 uppercase tracking-wider w-32">
                            <div class="flex flex-col items-center">
                                <i data-lucide="file-text" class="w-3 h-3 mb-1 text-sap-gray-500"></i>
                                <span>Enquiry Details</span>
                            </div>
                        </th>
                        <th scope="col" class="px-4 py-4 text-center text-xs font-semibold text-sap-gray-700 uppercase tracking-wider w-28">
                            <div class="flex flex-col items-center">
                                <i data-lucide="phone" class="w-3 h-3 mb-1 text-sap-gray-500"></i>
                                <span>Contact Info</span>
                            </div>
                        </th>
                        <th scope="col" class="px-4 py-4 text-center text-xs font-semibold text-sap-gray-700 uppercase tracking-wider w-24">
                            <div class="flex flex-col items-center">
                                <i data-lucide="activity" class="w-3 h-3 mb-1 text-sap-gray-500"></i>
                                <span>Status</span>
                            </div>
                        </th>
                        <th scope="col" class="px-4 py-4 text-center text-xs font-semibold text-sap-gray-700 uppercase tracking-wider w-40">
                            <div class="flex flex-col items-center">
                                <i data-lucide="settings" class="w-3 h-3 mb-1 text-sap-gray-500"></i>
                                <span>Actions & Convert</span>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for enquiry in enquiries %}
                    <tr class="hover:bg-sap-gray-50 transition-all duration-200 group">
                        <!-- Serial Number -->
                        <td class="px-4 py-4 whitespace-nowrap text-center">
                            <div class="w-8 h-8 bg-sap-blue-100 rounded-full flex items-center justify-center mx-auto">
                                <span class="text-sm font-semibold text-sap-blue-700">{{ forloop.counter }}</span>
                            </div>
                        </td>
                        
                        <!-- Financial Year -->
                        <td class="px-4 py-4 whitespace-nowrap text-center">
                            <div class="bg-sap-gray-100 rounded-lg px-3 py-1">
                                <span class="text-xs font-medium text-sap-gray-700">
                                    {% if enquiry.finyearid %}
                                        {{ enquiry.finyearid.finyear|default:"2022-23" }}
                                    {% else %}
                                        2022-23
                                    {% endif %}
                                </span>
                            </div>
                        </td>
                        
                        <!-- Customer Information -->
                        <td class="px-4 py-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-sap-blue-400 to-sap-blue-600 rounded-lg flex items-center justify-center">
                                    <span class="text-white font-semibold text-sm">
                                        {{ enquiry.customername|first|default:"?" }}
                                    </span>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <a href="{% url 'sales_distribution:enquiry_detail' enquiry.enqid %}" 
                                       class="block text-sm font-semibold text-sap-blue-600 hover:text-sap-blue-800 transition-colors duration-150">
                                        {{ enquiry.customername|default:"UNKNOWN CUSTOMER" }}
                                    </a>
                                    <div class="flex items-center space-x-2 mt-1">
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-sap-gray-100 text-sap-gray-700">
                                            <i data-lucide="tag" class="w-3 h-3 mr-1"></i>
                                            {{ enquiry.customerid|default:"No Code" }}
                                        </span>
                                        {% if enquiry.flag == 1 %}
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-700">
                                            <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                            Existing
                                        </span>
                                        {% else %}
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-700">
                                            <i data-lucide="plus-circle" class="w-3 h-3 mr-1"></i>
                                            New
                                        </span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </td>
                        
                        <!-- Enquiry Details -->
                        <td class="px-4 py-4 text-center">
                            <div class="space-y-1">
                                <div class="bg-sap-blue-50 rounded-lg px-3 py-1 border border-sap-blue-200">
                                    <span class="text-sm font-bold text-sap-blue-700">ENQ #{{ enquiry.enqid }}</span>
                                </div>
                                <div class="text-xs text-sap-gray-600">
                                    <i data-lucide="calendar-days" class="w-3 h-3 inline mr-1"></i>
                                    {{ enquiry.sysdate|default:"N/A" }}
                                </div>
                                {% if enquiry.enquiryfor %}
                                <div class="text-xs text-sap-gray-600 truncate max-w-24" title="{{ enquiry.enquiryfor }}">
                                    {{ enquiry.enquiryfor|truncatechars:20 }}
                                </div>
                                {% endif %}
                            </div>
                        </td>
                        
                        <!-- Contact Information -->
                        <td class="px-4 py-4 text-center">
                            <div class="space-y-1">
                                {% if enquiry.contactperson %}
                                <div class="text-xs font-medium text-sap-gray-700 truncate max-w-24" title="{{ enquiry.contactperson }}">
                                    <i data-lucide="user-check" class="w-3 h-3 inline mr-1"></i>
                                    {{ enquiry.contactperson|truncatechars:15 }}
                                </div>
                                {% endif %}
                                {% if enquiry.email %}
                                <div class="text-xs text-sap-gray-600 truncate max-w-24" title="{{ enquiry.email }}">
                                    <i data-lucide="mail" class="w-3 h-3 inline mr-1"></i>
                                    {{ enquiry.email|truncatechars:15 }}
                                </div>
                                {% endif %}
                                {% if enquiry.contactno %}
                                <div class="text-xs text-sap-gray-600">
                                    <i data-lucide="phone" class="w-3 h-3 inline mr-1"></i>
                                    {{ enquiry.contactno }}
                                </div>
                                {% endif %}
                            </div>
                        </td>
                        
                        <!-- Status -->
                        <td class="px-4 py-4 text-center">
                            {% if enquiry.postatus == 2 %}
                                <span class="sap-status-success">
                                    <i data-lucide="check-circle-2" class="w-3 h-3 mr-1"></i>
                                    Converted
                                </span>
                            {% elif enquiry.postatus == 1 %}
                                <span class="sap-status-warning">
                                    <i data-lucide="clock" class="w-3 h-3 mr-1"></i>
                                    Quoted
                                </span>
                            {% else %}
                                <span class="sap-status-info">
                                    <i data-lucide="plus-circle" class="w-3 h-3 mr-1"></i>
                                    New
                                </span>
                            {% endif %}
                        </td>
                        
                        <!-- Actions & Convert -->
                        <td class="px-4 py-4 text-center">
                            <div class="flex items-center justify-center space-x-1">
                                <!-- View Details -->
                                <a href="{% url 'sales_distribution:enquiry_detail' enquiry.enqid %}" 
                                   class="inline-flex items-center p-1.5 border border-sap-blue-300 rounded-lg text-xs font-medium text-sap-blue-600 bg-sap-blue-50 hover:bg-sap-blue-100 transition-all duration-150"
                                   title="View Details">
                                    <i data-lucide="eye" class="w-3 h-3"></i>
                                </a>
                                
                                <!-- Edit -->
                                {% if enquiry.postatus != 2 %}
                                <a href="{% url 'sales_distribution:enquiry_edit' enquiry.enqid %}" 
                                   class="inline-flex items-center p-1.5 border border-orange-300 rounded-lg text-xs font-medium text-orange-600 bg-orange-50 hover:bg-orange-100 transition-all duration-150"
                                   title="Edit Enquiry">
                                    <i data-lucide="edit" class="w-3 h-3"></i>
                                </a>
                                {% endif %}
                                
                                <!-- Convert to Quotation -->
                                {% if enquiry.postatus != 2 %}
                                <a href="#" 
                                   onclick="convertToQuotation({{ enquiry.enqid }})" 
                                   class="sap-button-primary py-1 px-2 text-xs"
                                   title="Convert to Quotation">
                                    <i data-lucide="file-plus" class="w-3 h-3 mr-1"></i>
                                    Convert
                                </a>
                                {% else %}
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-700">
                                    <i data-lucide="check" class="w-3 h-3 mr-1"></i>
                                    Done
                                </span>
                                {% endif %}
                            </div>
                            
                            <!-- Generated By Info -->
                            <div class="mt-1 text-xs text-sap-gray-500 truncate max-w-32" title="{{ enquiry.sessionid|default:'System' }}">
                                <i data-lucide="user-cog" class="w-3 h-3 inline mr-1"></i>
                                {{ enquiry.sessionid|default:"System"|truncatechars:12 }}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <div class="px-8 py-16 text-center">
                <div class="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-sap-blue-100 to-sap-blue-200 rounded-2xl flex items-center justify-center">
                    <i data-lucide="search-x" class="w-10 h-10 text-sap-blue-500"></i>
                </div>
                <h3 class="text-xl font-semibold text-sap-gray-800 mb-3">No Customer Enquiries Found</h3>
                <p class="text-sm text-sap-gray-600 mb-8 max-w-md mx-auto">Start building your customer database by creating your first enquiry. Track leads, manage communications, and convert prospects to customers.</p>
                <div class="flex items-center justify-center space-x-4">
                    <a href="{% url 'sales_distribution:enquiry_create' %}" 
                       class="sap-button-primary">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Create First Enquiry
                    </a>
                    <a href="#" 
                       class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 transition-all duration-200">
                        <i data-lucide="help-circle" class="w-4 h-4 mr-2"></i>
                        Help Guide
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="text-sm text-sap-gray-600">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ paginator.count }} results
                </div>
                <div class="flex space-x-2">
                    {% if page_obj.has_previous %}
                        <a href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.financial_year %}&financial_year={{ request.GET.financial_year }}{% endif %}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            First
                        </a>
                        <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.financial_year %}&financial_year={{ request.GET.financial_year }}{% endif %}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Previous
                        </a>
                    {% endif %}
                    
                    <span class="px-3 py-2 bg-sap-blue-50 border border-sap-blue-200 rounded-lg text-sm font-medium text-sap-blue-600">
                        Page {{ page_obj.number }} of {{ paginator.num_pages }}
                    </span>
                    
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.financial_year %}&financial_year={{ request.GET.financial_year }}{% endif %}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Next
                        </a>
                        <a href="?page={{ paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.financial_year %}&financial_year={{ request.GET.financial_year }}{% endif %}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Last
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

</div>

<script>
// Clear search function
function clearSearch() {
    document.getElementById('real-time-search').value = '';
    document.getElementById('financial-year-filter').value = '';
    // Reload page without parameters
    window.location.href = window.location.pathname;
}

// Convert to Quotation function
function convertToQuotation(enquiryId) {
    if (confirm('🔄 Convert this enquiry to a quotation?\n\nThis will create a new quotation record and mark the enquiry as quoted.')) {
        // Show loading state
        const button = event.target.closest('a');
        const originalContent = button.innerHTML;
        button.innerHTML = '<i data-lucide="loader-2" class="w-3 h-3 mr-1 animate-spin"></i>Converting...';
        button.disabled = true;
        
        // Navigate to quotation creation page with enquiry data
        window.location.href = `/sales-distribution/quotations/create/?from_enquiry=${enquiryId}`;
    }
}

// Enhanced search functionality
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit search on Enter key
    const searchInput = document.getElementById('real-time-search');
    const yearFilter = document.getElementById('financial-year-filter');
    
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    }
    
    if (yearFilter) {
        yearFilter.addEventListener('change', function() {
            performSearch();
        });
    }
    
    function performSearch() {
        const search = searchInput ? searchInput.value : '';
        const year = yearFilter ? yearFilter.value : '';
        
        let url = window.location.pathname + '?';
        const params = [];
        
        if (search) params.push('search=' + encodeURIComponent(search));
        if (year) params.push('financial_year=' + encodeURIComponent(year));
        
        url += params.join('&');
        window.location.href = url;
    }
    
    // Add enhanced row hover effects
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.classList.add('transform', 'scale-[1.01]', 'shadow-md');
        });
        
        row.addEventListener('mouseleave', function() {
            this.classList.remove('transform', 'scale-[1.01]', 'shadow-md');
        });
    });
    
    // Add loading animation for action buttons
    const actionButtons = document.querySelectorAll('[title]');
    actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (this.href && !this.href.includes('#')) {
                const icon = this.querySelector('i');
                if (icon) {
                    icon.classList.add('animate-pulse');
                }
            }
        });
    });
});
</script>
{% endblock %}