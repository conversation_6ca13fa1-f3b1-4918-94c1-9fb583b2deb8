{% extends 'core/base.html' %}
{% load static %}

{% block title %}Customer Master - Enhanced - {{ global_company.company_name }}{% endblock %}

{% block content %}
<div class="h-full overflow-y-auto">
    <div class="p-6 space-y-6">
        <!-- Enhanced SAP S/4 HANA Style Header -->
        <div class="bg-white rounded-xl shadow-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-4">
                    <div class="w-16 h-16 bg-gradient-to-br from-sap-blue-500 to-sap-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                        <i data-lucide="users" class="w-8 h-8 text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-sap-gray-900">Customer Master</h1>
                        <p class="text-sap-gray-600 mt-1">Advanced customer relationship management</p>
                    </div>
                </div>
                
                <!-- Quick Stats -->
                <div class="flex items-center space-x-6">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-sap-blue-600">{{ customers|length }}</div>
                        <div class="text-sm text-sap-gray-600">Active</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-sap-green-600">{{ customers|length }}</div>
                        <div class="text-sm text-sap-gray-600">Total</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-sap-orange-600">0</div>
                        <div class="text-sm text-sap-gray-600">Inactive</div>
                    </div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <a href="{% url 'sales_distribution:customer_new' %}" 
                       class="sap-button-primary">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        New Customer
                    </a>
                    <button class="px-4 py-2 border border-sap-gray-300 rounded-lg text-sm text-sap-gray-700 hover:bg-sap-gray-50 transition-colors">
                        <i data-lucide="upload" class="w-4 h-4 mr-2"></i>
                        Import
                    </button>
                    <button class="px-4 py-2 border border-sap-gray-300 rounded-lg text-sm text-sap-gray-700 hover:bg-sap-gray-50 transition-colors">
                        <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                        Export
                    </button>
                </div>
                
                <!-- View Options -->
                <div class="flex items-center space-x-2" x-data="{ view: 'table' }">
                    <button @click="view = 'table'" 
                            :class="view === 'table' ? 'bg-sap-blue-500 text-white' : 'bg-white text-sap-gray-700 border border-sap-gray-300'"
                            class="px-3 py-2 rounded-lg text-sm transition-colors">
                        <i data-lucide="list" class="w-4 h-4"></i>
                    </button>
                    <button @click="view = 'cards'" 
                            :class="view === 'cards' ? 'bg-sap-blue-500 text-white' : 'bg-white text-sap-gray-700 border border-sap-gray-300'"
                            class="px-3 py-2 rounded-lg text-sm transition-colors">
                        <i data-lucide="grid" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Advanced Search and Filters -->
        <div class="bg-white rounded-xl shadow-sm border border-sap-gray-200">
            <div class="p-6 border-b border-sap-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-sap-gray-900">Search & Filter</h3>
                    <button class="text-sm text-sap-blue-600 hover:text-sap-blue-800" x-data @click="$refs.advancedFilters.classList.toggle('hidden')">
                        Advanced Filters
                        <i data-lucide="chevron-down" class="w-4 h-4 inline ml-1"></i>
                    </button>
                </div>
            </div>
            
            <!-- Basic Search -->
            <div class="p-6 space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <!-- Global Search -->
                    <div class="md:col-span-2">
                        <div class="relative">
                            <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-sap-gray-400"></i>
                            <input type="text" 
                                   id="search" 
                                   name="search" 
                                   value="{{ request.GET.search }}"
                                   placeholder="Search customers by name, code, address, email..."
                                   class="pl-10 block w-full px-4 py-3 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"
                                   hx-get="{% url 'sales_distribution:customer_list' %}"
                                   hx-target="#customer-table-container"
                                   hx-trigger="keyup changed delay:300ms"
                                   hx-include="[name='search'], [name='filter'], [name='city'], [name='state']">
                        </div>
                    </div>
                    
                    <!-- Quick Filter -->
                    <div>
                        <select id="filter" 
                                name="filter" 
                                class="block w-full px-4 py-3 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"
                                hx-get="{% url 'sales_distribution:customer_list' %}"
                                hx-target="#customer-table-container"
                                hx-trigger="change"
                                hx-include="[name='search'], [name='filter'], [name='city'], [name='state']">
                            <option value="">All Customers</option>
                            <option value="recent" {% if request.GET.filter == 'recent' %}selected{% endif %}>Recent (30 days)</option>
                            <option value="active" {% if request.GET.filter == 'active' %}selected{% endif %}>Active</option>
                            <option value="inactive" {% if request.GET.filter == 'inactive' %}selected{% endif %}>Inactive</option>
                        </select>
                    </div>
                    
                    <!-- Clear Filters -->
                    <div class="flex items-end">
                        <a href="{% url 'sales_distribution:customer_list' %}" 
                           class="w-full px-4 py-3 border border-sap-gray-300 rounded-lg text-sm text-center text-sap-gray-700 hover:bg-sap-gray-50 transition-colors">
                            <i data-lucide="x" class="w-4 h-4 inline mr-1"></i>
                            Clear All
                        </a>
                    </div>
                </div>
                
                <!-- Advanced Filters (Hidden by default) -->
                <div ref="advancedFilters" class="hidden border-t border-sap-gray-200 pt-4 mt-4">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-2">State</label>
                            <select name="state" class="block w-full px-4 py-3 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                                <option value="">All States</option>
                                <!-- Add state options dynamically -->
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-2">City</label>
                            <select name="city" class="block w-full px-4 py-3 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                                <option value="">All Cities</option>
                                <!-- Add city options dynamically -->
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-2">Date Range</label>
                            <input type="date" class="block w-full px-4 py-3 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Customer Table -->
        <div id="customer-table-container" class="bg-white rounded-xl shadow-sm border border-sap-gray-200 overflow-hidden">
            <!-- Table Header -->
            <div class="bg-sap-gray-50 px-6 py-4 border-b border-sap-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <h3 class="text-lg font-semibold text-sap-gray-900">Customer List</h3>
                        <div class="flex items-center space-x-2 text-sm text-sap-gray-600">
                            <i data-lucide="users" class="w-4 h-4"></i>
                            <span>{{ customers|length }} customers</span>
                            {% if request.GET.search or request.GET.filter %}
                                <span class="text-sap-blue-600">• Filtered</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Table Controls -->
                    <div class="flex items-center space-x-2">
                        <button class="p-2 text-sap-gray-600 hover:text-sap-blue-600 hover:bg-sap-blue-50 rounded-lg transition-colors" title="Refresh">
                            <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                        </button>
                        <button class="p-2 text-sap-gray-600 hover:text-sap-blue-600 hover:bg-sap-blue-50 rounded-lg transition-colors" title="Column settings">
                            <i data-lucide="settings" class="w-4 h-4"></i>
                        </button>
                        <button class="p-2 text-sap-gray-600 hover:text-sap-blue-600 hover:bg-sap-blue-50 rounded-lg transition-colors" title="Full screen">
                            <i data-lucide="maximize" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Enhanced Table Content -->
            {% include 'sales_distribution/partials/customer_table_enhanced.html' %}
        </div>

        <!-- Enhanced Pagination -->
        {% if is_paginated %}
        <div class="bg-white rounded-xl shadow-sm border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="text-sm text-sap-gray-600">
                        Showing <span class="font-medium text-sap-gray-900">{{ page_obj.start_index }}</span> to 
                        <span class="font-medium text-sap-gray-900">{{ page_obj.end_index }}</span> of 
                        <span class="font-medium text-sap-gray-900">{{ paginator.count }}</span> customers
                    </div>
                    
                    <!-- Page Size Selector -->
                    <div class="flex items-center space-x-2">
                        <label class="text-sm text-sap-gray-600">Show:</label>
                        <select class="px-3 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500">
                            <option value="25" selected>25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span class="text-sm text-sap-gray-600">per page</span>
                    </div>
                </div>
                
                <!-- Advanced Pagination Controls -->
                <div class="flex items-center space-x-2">
                    {% if page_obj.has_previous %}
                        <a href="?page=1" class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm text-sap-gray-700 hover:bg-sap-gray-50 flex items-center">
                            <i data-lucide="chevrons-left" class="w-4 h-4 mr-1"></i>
                            First
                        </a>
                        <a href="?page={{ page_obj.previous_page_number }}" class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm text-sap-gray-700 hover:bg-sap-gray-50 flex items-center">
                            <i data-lucide="chevron-left" class="w-4 h-4 mr-1"></i>
                            Previous
                        </a>
                    {% endif %}
                    
                    <div class="flex items-center space-x-1">
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <span class="px-3 py-2 bg-sap-blue-500 text-white rounded-lg text-sm font-medium">
                                    {{ num }}
                                </span>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <a href="?page={{ num }}" class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm text-sap-gray-700 hover:bg-sap-gray-50">
                                    {{ num }}
                                </a>
                            {% endif %}
                        {% endfor %}
                    </div>
                    
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}" class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm text-sap-gray-700 hover:bg-sap-gray-50 flex items-center">
                            Next
                            <i data-lucide="chevron-right" class="w-4 h-4 ml-1"></i>
                        </a>
                        <a href="?page={{ paginator.num_pages }}" class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm text-sap-gray-700 hover:bg-sap-gray-50 flex items-center">
                            Last
                            <i data-lucide="chevrons-right" class="w-4 h-4 ml-1"></i>
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Enhanced customer list functionality
    document.addEventListener('DOMContentLoaded', function() {
        console.log('✅ Enhanced customer list initialized');
        
        // Initialize icons
        setTimeout(() => {
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        }, 100);
        
        // Advanced search functionality
        initializeAdvancedSearch();
        
        // Keyboard shortcuts
        initializeKeyboardShortcuts();
        
        // Auto-save search preferences
        initializeSearchPreferences();
    });
    
    function initializeAdvancedSearch() {
        // Real-time search with debouncing
        let searchTimeout;
        const searchInput = document.getElementById('search');
        
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    // Trigger HTMX search
                    htmx.trigger(this, 'keyup');
                }, 300);
            });
        }
        
        // Advanced filter toggles
        const filterButtons = document.querySelectorAll('[data-filter]');
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                const filter = this.dataset.filter;
                applyQuickFilter(filter);
            });
        });
    }
    
    function initializeKeyboardShortcuts() {
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + K to focus search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                document.getElementById('search').focus();
            }
            
            // Escape to clear search
            if (e.key === 'Escape') {
                const searchInput = document.getElementById('search');
                if (searchInput && searchInput === document.activeElement) {
                    searchInput.value = '';
                    htmx.trigger(searchInput, 'keyup');
                }
            }
        });
    }
    
    function initializeSearchPreferences() {
        // Save search state to localStorage
        const searchInput = document.getElementById('search');
        const filterSelect = document.getElementById('filter');
        
        if (searchInput) {
            searchInput.addEventListener('change', function() {
                localStorage.setItem('customer_search', this.value);
            });
        }
        
        if (filterSelect) {
            filterSelect.addEventListener('change', function() {
                localStorage.setItem('customer_filter', this.value);
            });
        }
        
        // Restore search state on page load
        const savedSearch = localStorage.getItem('customer_search');
        const savedFilter = localStorage.getItem('customer_filter');
        
        if (savedSearch && searchInput) {
            searchInput.value = savedSearch;
        }
        
        if (savedFilter && filterSelect) {
            filterSelect.value = savedFilter;
        }
    }
    
    function applyQuickFilter(filter) {
        const filterSelect = document.getElementById('filter');
        if (filterSelect) {
            filterSelect.value = filter;
            htmx.trigger(filterSelect, 'change');
        }
    }
    
    // Re-initialize after HTMX updates
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    });
    
    // Export functionality
    function exportCustomers(format) {
        const params = new URLSearchParams(window.location.search);
        params.set('export', format);
        window.open('?' + params.toString(), '_blank');
    }
    
    // Bulk actions
    function selectAll() {
        const checkboxes = document.querySelectorAll('input[name="customer_ids"]');
        checkboxes.forEach(cb => cb.checked = true);
        updateBulkActions();
    }
    
    function selectNone() {
        const checkboxes = document.querySelectorAll('input[name="customer_ids"]');
        checkboxes.forEach(cb => cb.checked = false);
        updateBulkActions();
    }
    
    function updateBulkActions() {
        const selected = document.querySelectorAll('input[name="customer_ids"]:checked').length;
        const bulkActions = document.getElementById('bulk-actions');
        
        if (bulkActions) {
            bulkActions.style.display = selected > 0 ? 'block' : 'none';
        }
    }
</script>
{% endblock %}
