{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">Material Service Notes</h1>
                    <span class="ml-4 text-sm text-gray-500">Manage service requests, maintenance activities, and contractor services</span>
                </div>
                <div class="flex items-center">
                    <button class="sap-button-primary">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        New Service Note
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Cards Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="grid grid-cols-2 md:grid-cols-6 gap-4 mb-6">
            <div class="sap-card p-4 text-center">
                <div class="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg mx-auto mb-2">
                    <i data-lucide="file-text" class="w-5 h-5 text-blue-600"></i>
                </div>
                <div class="text-sm font-medium text-sap-gray-900">Total SNs</div>
                <div class="text-xl font-bold text-sap-gray-900">{{ gsn_records|length|default:"1317" }}</div>
            </div>
            
            <div class="sap-card p-4 text-center">
                <div class="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-lg mx-auto mb-2">
                    <i data-lucide="edit-3" class="w-5 h-5 text-gray-600"></i>
                </div>
                <div class="text-sm font-medium text-sap-gray-900">Draft</div>
                <div class="text-xl font-bold text-sap-gray-900">0</div>
            </div>
            
            <div class="sap-card p-4 text-center">
                <div class="flex items-center justify-center w-10 h-10 bg-yellow-100 rounded-lg mx-auto mb-2">
                    <i data-lucide="clock" class="w-5 h-5 text-yellow-600"></i>
                </div>
                <div class="text-sm font-medium text-sap-gray-900">Pending</div>
                <div class="text-xl font-bold text-sap-gray-900">0</div>
            </div>
            
            <div class="sap-card p-4 text-center">
                <div class="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg mx-auto mb-2">
                    <i data-lucide="check-circle" class="w-5 h-5 text-blue-600"></i>
                </div>
                <div class="text-sm font-medium text-sap-gray-900">Approved</div>
                <div class="text-xl font-bold text-sap-gray-900">0</div>
            </div>
            
            <div class="sap-card p-4 text-center">
                <div class="flex items-center justify-center w-10 h-10 bg-orange-100 rounded-lg mx-auto mb-2">
                    <i data-lucide="settings" class="w-5 h-5 text-orange-600"></i>
                </div>
                <div class="text-sm font-medium text-sap-gray-900">In Progress</div>
                <div class="text-xl font-bold text-sap-gray-900">0</div>
            </div>
            
            <div class="sap-card p-4 text-center">
                <div class="flex items-center justify-center w-10 h-10 bg-green-100 rounded-lg mx-auto mb-2">
                    <i data-lucide="check-circle-2" class="w-5 h-5 text-green-600"></i>
                </div>
                <div class="text-sm font-medium text-sap-gray-900">Completed</div>
                <div class="text-xl font-bold text-sap-gray-900">0</div>
            </div>
        </div>
    </div>

    <!-- Search Filter Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="sap-card p-4 mb-6">
            <h3 class="text-lg font-medium text-sap-gray-900 mb-4">Search & Filter</h3>
            <form method="GET" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label for="gsn_number" class="block text-sm font-medium text-sap-gray-700 mb-1">GSN Number</label>
                        <input type="text" 
                               id="gsn_number" 
                               name="gsn_number" 
                               value="{{ request.GET.gsn_number }}" 
                               class="sap-input"
                               placeholder="Search by GSN Number">
                    </div>
                    <div>
                        <label for="supplier" class="block text-sm font-medium text-sap-gray-700 mb-1">Supplier</label>
                        <input type="text" 
                               id="supplier" 
                               name="supplier" 
                               value="{{ supplier_filter }}" 
                               class="sap-input"
                               placeholder="Search by supplier name">
                    </div>
                    <div class="flex items-end space-x-2">
                        <button type="submit" class="sap-button-primary">
                            <i data-lucide="search" class="w-4 h-4 mr-2"></i>
                            Search
                        </button>
                        <button type="button" onclick="window.location.href='{% url 'inventory:gsn_edit_list' %}'" class="px-4 py-2 text-sm font-medium text-sap-gray-700 bg-white border border-sap-gray-300 rounded-lg hover:bg-sap-gray-50">
                            Clear
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Service Notes List Section -->
        <div class="space-y-4">
            {% for record in gsn_records %}
            <div class="sap-card p-6 hover:shadow-lg transition-shadow duration-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg">
                            <i data-lucide="file-text" class="w-5 h-5 text-blue-600"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-sap-gray-900">{{ record.gsnno }}</h3>
                            <p class="text-sm text-sap-gray-600">
                                GIN: {{ record.ginno }} | Company: {{ record.supplier_name|default:"N/A" }}
                            </p>
                            <p class="text-xs text-sap-gray-500">
                                Invoice: {{ record.taxinvoiceno|default:"818" }} ({{ record.sn_date|date:"Y-m-d"|default:"2022-10-15" }})
                            </p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button onclick="window.location.href='{% url 'inventory:gsn_edit_details' pk=record.id %}'" 
                                class="px-4 py-2 text-sm font-medium text-sap-blue-600 hover:text-sap-blue-800 hover:bg-sap-blue-50 rounded-lg transition-colors">
                            View Details
                        </button>
                        <button onclick="window.location.href='{% url 'inventory:gsn_edit_details' pk=record.id %}'" 
                                class="px-4 py-2 text-sm font-medium text-sap-blue-600 hover:text-sap-blue-800 hover:bg-sap-blue-50 rounded-lg transition-colors">
                            Edit
                        </button>
                        <button class="px-4 py-2 text-sm font-medium text-sap-gray-600 hover:text-sap-gray-800 hover:bg-sap-gray-50 rounded-lg transition-colors">
                            Print
                        </button>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="sap-card p-12 text-center">
                <div class="flex flex-col items-center">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                        <i data-lucide="file-text" class="w-8 h-8 text-gray-400"></i>
                    </div>
                    <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No Service Notes Found</h3>
                    <p class="text-sap-gray-600 mb-4">Try adjusting your search criteria or create a new service note.</p>
                    <button class="sap-button-primary">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Create New Service Note
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Results Info -->
        {% if gsn_records %}
        <div class="sap-card p-4 mt-6">
            <div class="text-sm text-sap-gray-700">
                Showing {{ gsn_records|length }} GSN record{{ gsn_records|length|pluralize }}
                {% if supplier_filter %}
                    filtered by supplier: <span class="font-medium">{{ supplier_filter }}</span>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>

<script>
// Initialize Lucide icons
document.addEventListener('DOMContentLoaded', function() {
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});
</script>
{% endblock %}