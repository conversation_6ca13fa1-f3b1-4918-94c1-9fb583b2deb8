<!-- accounts/partials/account_head_table.html -->
<!-- HTMX partial for Account Head table - SAP S/4HANA inspired -->
<!-- Replaces ASP.NET GridView with modern responsive table -->

{% load static %}

<div class="overflow-hidden">
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-sap-gray-200" id="account-head-table">
            <thead class="bg-sap-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        Edit
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        Delete
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        SN
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        Category
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        Description
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        Symbol
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        Abbreviation
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-sap-gray-200" id="account-head-tbody">
                {% if account_heads %}
                    {% for account_head in account_heads %}
                        {% include 'accounts/partials/account_head_row.html' with account_head=account_head forloop=forloop %}
                    {% endfor %}
                {% else %}
                    <!-- Empty State Row -->
                    <tr>
                        <td colspan="7" class="px-6 py-12 text-center">
                            <div class="w-16 h-16 bg-sap-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="search" class="w-8 h-8 text-sap-gray-400"></i>
                            </div>
                            <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No account heads found</h3>
                            <p class="text-sm text-sap-gray-600 mb-4">
                                {% if request.GET.search or request.GET.category %}
                                    Try adjusting your search criteria or filters.
                                {% else %}
                                    Get started by creating your first account head using the form below.
                                {% endif %}
                            </p>
                        </td>
                    </tr>
                {% endif %}
            </tbody>
            
            <!-- Footer Insert Row - Matches ASP.NET GridView FooterTemplate -->
            <tfoot class="bg-sap-green-50">
                <tr>
                    <td class="px-6 py-4">
                        <form hx-post="{% url 'accounts:account_head_create' %}" 
                              hx-target="#account-head-table" 
                              hx-swap="outerHTML"
                              hx-trigger="submit"
                              class="contents"
                              id="footer-insert-form">
                            {% csrf_token %}
                            
                            <button type="submit" 
                                    onclick="return confirm('Are you sure you want to add this account head?')"
                                    class="inline-flex items-center px-3 py-1.5 border border-sap-green-600 rounded text-xs font-medium text-white bg-sap-green-600 hover:bg-sap-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-green-500">
                                Insert
                            </button>
                    </td>
                    <td class="px-6 py-4"></td>
                    <td class="px-6 py-4">
                        <!-- SN auto-calculated -->
                    </td>
                    
                    <!-- Category Dropdown -->
                    <td class="px-6 py-4">
                        <select name="category" 
                                class="block w-full px-3 py-2 border border-sap-green-300 rounded-md shadow-sm text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500 bg-white"
                                required>
                            <option value="Labour">Labour</option>
                            <option value="With Material">With Material</option>
                            <option value="Expenses">Expenses</option>
                            <option value="Service Provider">Service Provider</option>
                        </select>
                    </td>
                    
                    <!-- Description Input -->
                    <td class="px-6 py-4">
                        <input type="text" 
                               name="description" 
                               placeholder="Description"
                               class="block w-full px-3 py-2 border border-sap-green-300 rounded-md shadow-sm text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500"
                               required>
                    </td>
                    
                    <!-- Symbol Input -->
                    <td class="px-6 py-4">
                        <input type="text" 
                               name="symbol" 
                               placeholder="Symbol"
                               class="block w-full px-3 py-2 border border-sap-green-300 rounded-md shadow-sm text-sm font-mono focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500"
                               required>
                    </td>
                    
                    <!-- Abbreviation Input -->
                    <td class="px-6 py-4">
                        <input type="text" 
                               name="abbreviation" 
                               placeholder="Abbreviation"
                               class="block w-full px-3 py-2 border border-sap-green-300 rounded-md shadow-sm text-sm font-mono focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500"
                               required>
                    </td>
                        </form>
                </tr>
            </tfoot>
        </table>
    </div>
</div>