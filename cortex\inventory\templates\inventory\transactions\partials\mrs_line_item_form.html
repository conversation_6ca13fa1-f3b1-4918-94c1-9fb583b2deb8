<!-- MRS Line Item Form Partial -->
<div class="bg-white shadow rounded-lg" id="line-item-form" x-data="lineItemForm()">
    <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Add Item to Requisition</h3>
        
        <form method="post" @submit.prevent="submitLineItem">
            {% csrf_token %}
            
            <div class="space-y-6">
                <!-- Item Search Section -->
                <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
                    <!-- Item Code Search -->
                    <div>
                        <label for="item_code_search" class="block text-sm font-medium text-gray-700">Search by Item Code</label>
                        <div class="mt-1 relative">
                            <input type="text" 
                                   id="item_code_search" 
                                   x-model="searchTerm" 
                                   @input="searchItems"
                                   placeholder="Enter item code or description"
                                   class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            
                            <!-- Search Results Dropdown -->
                            <div x-show="showResults && searchResults.length > 0" 
                                 class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-200 max-h-60 overflow-auto">
                                <template x-for="item in searchResults" :key="item.id">
                                    <div @click="selectItem(item)" 
                                         class="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900" x-text="item.item_code"></div>
                                                <div class="text-sm text-gray-500" x-text="item.description"></div>
                                                <div class="text-xs text-gray-400">
                                                    UOM: <span x-text="item.unit_of_measure"></span> | 
                                                    Stock: <span x-text="item.available_stock" 
                                                              :class="item.available_stock > 0 ? 'text-green-600' : 'text-red-600'"></span>
                                                </div>
                                            </div>
                                            <div class="text-xs text-gray-500" x-text="item.category_name"></div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>

                    <!-- Category Filter -->
                    <div>
                        <label for="category_filter" class="block text-sm font-medium text-gray-700">Filter by Category</label>
                        <select id="category_filter" 
                                x-model="selectedCategory" 
                                @change="filterByCategory"
                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            <option value="">All Categories</option>
                            <template x-for="category in categories" :key="category.id">
                                <option :value="category.id" x-text="category.name"></option>
                            </template>
                        </select>
                    </div>
                </div>

                <!-- Selected Item Details -->
                <div x-show="selectedItem" class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Selected Item Details</h4>
                    
                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                        <div>
                            <dt class="text-xs font-medium text-gray-500">Item Code</dt>
                            <dd class="mt-1 text-sm text-gray-900" x-text="selectedItem?.item_code"></dd>
                        </div>
                        <div>
                            <dt class="text-xs font-medium text-gray-500">Description</dt>
                            <dd class="mt-1 text-sm text-gray-900" x-text="selectedItem?.description"></dd>
                        </div>
                        <div>
                            <dt class="text-xs font-medium text-gray-500">Unit of Measure</dt>
                            <dd class="mt-1 text-sm text-gray-900" x-text="selectedItem?.unit_of_measure"></dd>
                        </div>
                        <div>
                            <dt class="text-xs font-medium text-gray-500">Available Stock</dt>
                            <dd class="mt-1 text-sm" 
                                :class="selectedItem?.available_stock > 0 ? 'text-green-600' : 'text-red-600'"
                                x-text="selectedItem?.available_stock"></dd>
                        </div>
                    </div>
                </div>

                <!-- Quantity and Purpose -->
                <div x-show="selectedItem" class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label for="requested_quantity" class="block text-sm font-medium text-gray-700">Requested Quantity *</label>
                        <input type="number" 
                               id="requested_quantity" 
                               x-model="requestedQuantity"
                               @input="validateQuantity"
                               min="0.01" 
                               step="0.01"
                               class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                               required>
                        <!-- Stock Warning -->
                        <div x-show="showStockWarning" class="mt-2 text-sm text-red-600">
                            <svg class="inline w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                            Requested quantity exceeds available stock. Approval may be required.
                        </div>
                    </div>

                    <div>
                        <label for="purpose" class="block text-sm font-medium text-gray-700">Purpose / Specification</label>
                        <textarea id="purpose" 
                                  x-model="purpose"
                                  rows="3"
                                  class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                  placeholder="Specify the purpose or technical specifications"></textarea>
                    </div>
                </div>

                <!-- Location Filter (if applicable) -->
                <div x-show="selectedItem && locations.length > 0" class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label for="preferred_location" class="block text-sm font-medium text-gray-700">Preferred Location</label>
                        <select id="preferred_location" 
                                x-model="preferredLocation"
                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            <option value="">Any Location</option>
                            <template x-for="location in locations" :key="location.id">
                                <option :value="location.id" x-text="location.display_name"></option>
                            </template>
                        </select>
                    </div>

                    <div>
                        <label for="urgency_level" class="block text-sm font-medium text-gray-700">Urgency Level</label>
                        <select id="urgency_level" 
                                x-model="urgencyLevel"
                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            <option value="normal">Normal</option>
                            <option value="urgent">Urgent</option>
                            <option value="critical">Critical</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="mt-6 flex justify-end space-x-3">
                <button type="button" 
                        @click="resetForm"
                        class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Reset
                </button>
                <button type="submit" 
                        :disabled="!canSubmit"
                        :class="canSubmit ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-400 cursor-not-allowed'"
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    Add to Requisition
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function lineItemForm() {
    return {
        // Search and selection
        searchTerm: '',
        selectedCategory: '',
        selectedItem: null,
        searchResults: [],
        categories: [],
        locations: [],
        showResults: false,
        
        // Form fields
        requestedQuantity: '',
        purpose: '',
        preferredLocation: '',
        urgencyLevel: 'normal',
        
        // Validation
        showStockWarning: false,
        
        init() {
            this.loadCategories();
            this.loadLocations();
        },
        
        get canSubmit() {
            return this.selectedItem && this.requestedQuantity && parseFloat(this.requestedQuantity) > 0;
        },
        
        async searchItems() {
            if (this.searchTerm.length < 2) {
                this.searchResults = [];
                this.showResults = false;
                return;
            }
            
            try {
                const response = await fetch(`/inventory/api/item-search/?q=${encodeURIComponent(this.searchTerm)}&category=${this.selectedCategory}`);
                if (response.ok) {
                    this.searchResults = await response.json();
                    this.showResults = true;
                }
            } catch (error) {
                console.error('Error searching items:', error);
            }
        },
        
        async filterByCategory() {
            if (this.searchTerm) {
                await this.searchItems();
            }
        },
        
        selectItem(item) {
            this.selectedItem = item;
            this.searchTerm = item.item_code;
            this.showResults = false;
            this.validateQuantity();
        },
        
        validateQuantity() {
            if (this.selectedItem && this.requestedQuantity) {
                const requested = parseFloat(this.requestedQuantity);
                const available = parseFloat(this.selectedItem.available_stock);
                this.showStockWarning = requested > available;
            } else {
                this.showStockWarning = false;
            }
        },
        
        async loadCategories() {
            try {
                const response = await fetch('/inventory/api/categories/');
                if (response.ok) {
                    this.categories = await response.json();
                }
            } catch (error) {
                console.error('Error loading categories:', error);
            }
        },
        
        async loadLocations() {
            try {
                const response = await fetch('/inventory/api/locations/');
                if (response.ok) {
                    this.locations = await response.json();
                }
            } catch (error) {
                console.error('Error loading locations:', error);
            }
        },
        
        async submitLineItem() {
            if (!this.canSubmit) return;
            
            const formData = {
                item_id: this.selectedItem.id,
                item_code: this.selectedItem.item_code,
                item_description: this.selectedItem.description,
                unit_of_measure: this.selectedItem.unit_of_measure,
                requested_quantity: this.requestedQuantity,
                purpose: this.purpose,
                preferred_location: this.preferredLocation,
                urgency_level: this.urgencyLevel,
                available_stock: this.selectedItem.available_stock
            };
            
            try {
                const response = await fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(formData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        // Refresh the line items list
                        this.$dispatch('line-item-added', result.line_item);
                        this.resetForm();
                        
                        // Show success message
                        this.showSuccessMessage('Item added to requisition successfully');
                    } else {
                        this.showErrorMessage(result.error || 'Failed to add item');
                    }
                } else {
                    this.showErrorMessage('Failed to add item to requisition');
                }
            } catch (error) {
                console.error('Error submitting line item:', error);
                this.showErrorMessage('An error occurred while adding the item');
            }
        },
        
        resetForm() {
            this.searchTerm = '';
            this.selectedItem = null;
            this.requestedQuantity = '';
            this.purpose = '';
            this.preferredLocation = '';
            this.urgencyLevel = 'normal';
            this.showStockWarning = false;
            this.searchResults = [];
            this.showResults = false;
        },
        
        showSuccessMessage(message) {
            // Implement success notification
            console.log('Success:', message);
        },
        
        showErrorMessage(message) {
            // Implement error notification
            console.error('Error:', message);
        }
    }
}
</script>