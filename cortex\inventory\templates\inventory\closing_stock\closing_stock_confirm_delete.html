{% extends "core/base.html" %}
{% load static %}

{% block title %}Delete Closing Stock Record #{{ object.id }} - Inventory{% endblock %}

{% block content %}
<div class="sap-page">
    <!-- Page Header -->
    <div class="sap-page-header">
        <div class="sap-page-header-content">
            <div class="sap-page-title">
                <h1>Delete Closing Stock Record</h1>
                <p>Confirm deletion of closing stock record #{{ object.id }}</p>
            </div>
        </div>
    </div>

    <!-- Confirmation Panel -->
    <div class="sap-panel">
        <div class="sap-panel-content">
            <div class="sap-message-strip sap-message-strip--warning mb-6">
                <svg class="w-5 h-5 text-yellow-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <div class="sap-message-strip-text">
                    <strong>Warning:</strong> This action cannot be undone. The closing stock record and all associated data will be permanently deleted from the system.
                </div>
            </div>

            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Record Details</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <span class="text-sm font-medium text-gray-700">Record ID:</span>
                        <span class="text-sm text-gray-900 ml-2 font-mono">#{{ object.id }}</span>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-700">Closing Stock Value:</span>
                        <span class="text-sm text-gray-900 ml-2 font-semibold">
                            {% if object.clstock %}₹{{ object.clstock|floatformat:2 }}{% else %}₹0.00{% endif %}
                        </span>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-700">From Date:</span>
                        <span class="text-sm text-gray-900 ml-2">
                            {% if from_date_formatted %}
                                {{ from_date_formatted }}
                                <span class="text-xs text-gray-500">({{ object.fromdt }})</span>
                            {% else %}
                                {{ object.fromdt|default:"Not specified" }}
                            {% endif %}
                        </span>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-700">To Date:</span>
                        <span class="text-sm text-gray-900 ml-2">
                            {% if to_date_formatted %}
                                {{ to_date_formatted }}
                                <span class="text-xs text-gray-500">({{ object.todt }})</span>
                            {% else %}
                                {{ object.todt|default:"Not specified" }}
                            {% endif %}
                        </span>
                    </div>
                </div>

                {% if from_date_formatted and to_date_formatted %}
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <div class="flex items-center space-x-2 text-sm">
                        <span class="text-gray-600">Period:</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {{ from_date_formatted }}
                        </span>
                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                        </svg>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {{ to_date_formatted }}
                        </span>
                    </div>
                </div>
                {% endif %}
            </div>

            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                <div class="flex">
                    <svg class="w-5 h-5 text-red-400 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <div>
                        <h4 class="text-sm font-medium text-red-800">Consequences of Deletion:</h4>
                        <ul class="mt-2 text-sm text-red-700 list-disc list-inside space-y-1">
                            <li>This closing stock record will be permanently removed from the database</li>
                            <li>Historical inventory reports may be affected</li>
                            <li>Any calculations or reports dependent on this data will need to be updated</li>
                            <li>This action cannot be reversed</li>
                        </ul>
                    </div>
                </div>
            </div>

            <form method="post">
                {% csrf_token %}
                <div class="sap-form-actions">
                    <button type="submit" class="sap-button sap-button--danger">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        Yes, Delete Record
                    </button>
                    <a href="{% url 'inventory:closing_stock_detail' object.pk %}" class="sap-button sap-button--transparent">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add confirmation before submitting the form
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const confirmed = confirm(
            'Are you absolutely sure you want to delete this closing stock record?\n\n' +
            'Record ID: #{{ object.id }}\n' +
            'Value: {% if object.clstock %}₹{{ object.clstock|floatformat:2 }}{% else %}₹0.00{% endif %}\n' +
            'Period: {{ from_date_formatted|default:object.fromdt }} to {{ to_date_formatted|default:object.todt }}\n\n' +
            'This action cannot be undone!'
        );
        
        if (!confirmed) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}