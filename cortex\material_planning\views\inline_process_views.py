"""
Inline Process Views - ASP.NET GridView equivalent functionality
Supports AJAX-based CRUD operations for Process Categories
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
import json

from ..models import Process
from ..forms.simple_process_forms import ProcessInlineForm, ProcessCreateForm


class ProcessInlineListView(LoginRequiredMixin, ListView):
    """List view with inline editing capabilities - matches ASP.NET GridView"""
    model = Process
    template_name = 'material_planning/processes/category_inline_edit.html'
    context_object_name = 'processes'
    paginate_by = 15

    def get_queryset(self):
        return Process.objects.all().order_by('processname')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Convert processes to JSON for Alpine.js
        processes_data = []
        for process in self.get_queryset():
            processes_data.append({
                'id': process.id,
                'processname': process.processname or '',
                'symbol': process.symbol or ''
            })
        
        # Convert to JSON string for the template
        import json
        context['processes'] = json.dumps(processes_data)
        return context


@login_required
@require_http_methods(["POST"])
def process_create_ajax(request):
    """Create new process via AJAX - matches ASP.NET GridView footer insert"""
    try:
        data = json.loads(request.body)
        
        # Create form with the received data
        form = ProcessCreateForm(data)
        
        if form.is_valid():
            process = form.save()
            
            return JsonResponse({
                'success': True,
                'message': f'Process "{process.processname}" created successfully.',
                'process': {
                    'id': process.id,
                    'processname': process.processname,
                    'symbol': process.symbol
                }
            })
        else:
            errors = {}
            for field, error_list in form.errors.items():
                errors[field] = error_list[0]
            
            return JsonResponse({
                'success': False,
                'message': 'Validation failed.',
                'errors': errors
            })
    
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': 'Invalid JSON data.'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'An error occurred: {str(e)}'
        })


@login_required
@require_http_methods(["POST"])
def process_update_ajax(request, process_id):
    """Update process via AJAX - matches ASP.NET GridView inline editing"""
    try:
        process = get_object_or_404(Process, id=process_id)
        data = json.loads(request.body)
        
        # Create form with the received data
        form = ProcessInlineForm(data, instance=process)
        
        if form.is_valid():
            updated_process = form.save()
            
            return JsonResponse({
                'success': True,
                'message': f'Process "{updated_process.processname}" updated successfully.',
                'process': {
                    'id': updated_process.id,
                    'processname': updated_process.processname,
                    'symbol': updated_process.symbol
                }
            })
        else:
            errors = {}
            for field, error_list in form.errors.items():
                errors[field] = error_list[0]
            
            return JsonResponse({
                'success': False,
                'message': 'Validation failed.',
                'errors': errors
            })
    
    except Process.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'Process not found.'
        })
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': 'Invalid JSON data.'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'An error occurred: {str(e)}'
        })


@login_required
@require_http_methods(["POST"])
def process_delete_ajax(request, process_id):
    """Delete process via AJAX - matches ASP.NET GridView delete functionality"""
    try:
        process = get_object_or_404(Process, id=process_id)
        process_name = process.processname
        
        # Delete the process
        process.delete()
        
        return JsonResponse({
            'success': True,
            'message': f'Process "{process_name}" deleted successfully.'
        })
    
    except Process.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'Process not found.'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'An error occurred: {str(e)}'
        })


# Alternative view for confirmation-based deletion (like ASP.NET confirmation)
@login_required
def process_delete_confirm(request, process_id):
    """Delete confirmation page - matches ASP.NET confirmation dialog"""
    process = get_object_or_404(Process, id=process_id)
    
    if request.method == 'POST':
        process_name = process.processname
        process.delete()
        
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': True,
                'message': f'Process "{process_name}" deleted successfully.'
            })
        else:
            # Redirect for non-AJAX requests
            return redirect('material_planning:process_inline_list')
    
    return render(request, 'material_planning/processes/delete_confirm.html', {
        'process': process
    })