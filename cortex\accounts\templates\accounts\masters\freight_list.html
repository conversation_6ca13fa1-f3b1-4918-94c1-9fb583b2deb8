<!-- accounts/templates/accounts/masters/freight_list.html -->
<!-- Freight Terms Management List Template -->
<!-- Simple freight terms management matching actual database structure -->

{% extends 'core/base.html' %}

{% block title %}Freight Terms - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-blue-600 to-sap-blue-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="truck" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Freight Terms Management</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage freight terms and conditions for shipments</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Accounts
                </a>
                <a href="{% url 'accounts:freight_create' %}" 
                   class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    Add Freight Term
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Statistics Card -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Total Freight Terms</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ freight_rates.count }}</p>
                </div>
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="truck" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Search -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm mb-6">
        <div class="p-6">
            <form method="get" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="md:col-span-2">
                    <label for="search" class="block text-sm font-medium text-sap-gray-700 mb-2">Search Freight Terms</label>
                    <input type="text" name="search" id="search" value="{{ request.GET.search }}"
                           placeholder="Search freight terms..."
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                </div>
                <div class="flex items-end space-x-2">
                    <button type="submit" 
                            class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="search" class="w-4 h-4 inline mr-2"></i>
                        Search
                    </button>
                    <a href="{% url 'accounts:freight_list' %}" 
                       class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="x" class="w-4 h-4 inline mr-2"></i>
                        Clear
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Freight Terms Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Freight Terms</h3>
                <span class="text-sm text-sap-gray-600">{{ freight_rates.count }} term{{ freight_rates.count|pluralize }}</span>
            </div>
        </div>
        
        {% if freight_rates %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            ID
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Freight Terms
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for term in freight_rates %}
                    <tr class="hover:bg-sap-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-sap-gray-900">{{ term.id }}</div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-sap-gray-900">{{ term.terms }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <a href="{% url 'accounts:freight_edit' term.pk %}" 
                                   class="text-sap-blue-600 hover:text-sap-blue-700 p-1" title="Edit">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </a>
                                <a href="{% url 'accounts:freight_delete' term.pk %}" 
                                   class="text-red-600 hover:text-red-700 p-1" title="Delete"
                                   onclick="return confirm('Are you sure you want to delete this freight term?')">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="px-6 py-4 border-t border-sap-gray-100">
            <div class="flex items-center justify-between">
                <div class="text-sm text-sap-gray-700">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} entries
                </div>
                <div class="flex items-center space-x-2">
                    {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" 
                           class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-3 py-1 rounded font-medium">Previous</a>
                    {% endif %}
                    
                    <span class="text-sm text-sap-gray-600">
                        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                    </span>
                    
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" 
                           class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-3 py-1 rounded font-medium">Next</a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
        
        {% else %}
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="w-24 h-24 bg-sap-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i data-lucide="truck" class="w-12 h-12 text-sap-blue-600"></i>
            </div>
            <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No Freight Terms Found</h3>
            <p class="text-sap-gray-600 mb-6">
                {% if request.GET.search %}
                    No freight terms match your search criteria.
                {% else %}
                    Get started by adding your first freight term.
                {% endif %}
            </p>
            {% if request.GET.search %}
                <a href="{% url 'accounts:freight_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors mr-3">
                    Clear Search
                </a>
            {% endif %}
            <a href="{% url 'accounts:freight_create' %}" 
               class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                Add Freight Term
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}