{% extends "core/base.html" %}
{% load static %}

{% block title %}Supplier Challan {{ object.scno }} - Inventory Management{% endblock %}

{% block content %}
<div class="sap-page">
    <!-- Page Header -->
    <div class="sap-page-header">
        <div class="sap-page-header-content">
            <div class="sap-page-title">
                <h1>Supplier Challan {{ object.scno }}</h1>
                <p>View and manage supplier challan details</p>
            </div>
            <div class="sap-page-actions">
                <a href="{% url 'inventory:supplier_challan_list' %}" class="sap-button sap-button--transparent">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to List
                </a>
                {% if can_edit %}
                <a href="{% url 'inventory:supplier_challan_update' object.pk %}" class="sap-button sap-button--emphasized">
                    Edit Challan
                </a>
                {% endif %}
                <a href="{% url 'inventory:supplier_challan_print' object.pk %}" class="sap-button sap-button--transparent">
                    Print
                </a>
            </div>
        </div>
    </div>

    <!-- Challan Information -->
    <div class="sap-panel">
        <div class="sap-panel-header">
            <h3 class="sap-panel-title">Challan Information</h3>
        </div>
        <div class="sap-panel-content">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Challan Number</label>
                    <p class="mt-1 text-sm text-gray-900 font-medium">{{ object.scno }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Challan Date</label>
                    <p class="mt-1 text-sm text-gray-900">{{ object.challan_date|date:"M d, Y" }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Status</label>
                    <span class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                        {% if object.status == 'RECEIVED' %}bg-green-100 text-green-800{% else %}bg-yellow-100 text-yellow-800{% endif %}">
                        {{ object.status }}
                    </span>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Supplier ID</label>
                    <p class="mt-1 text-sm text-gray-900">{{ object.supplierid }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Supplier Name</label>
                    <p class="mt-1 text-sm text-gray-900">{{ object.supplier_name }}</p>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Vehicle Number</label>
                    <p class="mt-1 text-sm text-gray-900">{{ object.vehicleno|default:"-" }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Transporter</label>
                    <p class="mt-1 text-sm text-gray-900">{{ object.transpoter|default:"-" }}</p>
                </div>
            </div>

            {% if object.remarks %}
            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-700">Remarks</label>
                <p class="mt-1 text-sm text-gray-900">{{ object.remarks }}</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- System Information -->
    <div class="sap-panel">
        <div class="sap-panel-header">
            <h3 class="sap-panel-title">System Information</h3>
        </div>
        <div class="sap-panel-content">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700">System Date</label>
                    <p class="mt-1 text-sm text-gray-900">{{ object.sysdate|default:"-" }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">System Time</label>
                    <p class="mt-1 text-sm text-gray-900">{{ object.systime|default:"-" }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Created By</label>
                    <p class="mt-1 text-sm text-gray-900">{{ object.user.username|default:"-" }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Company</label>
                    <p class="mt-1 text-sm text-gray-900">{{ object.company.company_name|default:"-" }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-center space-x-4 mt-6">
        {% if can_receive %}
        <a href="{% url 'inventory:supplier_challan_receive' object.pk %}" 
           class="sap-button sap-button--emphasized">
            Mark as Received
        </a>
        {% endif %}
        
        {% if can_clear %}
        <button class="sap-button sap-button--transparent" disabled>
            Clear Challan (Coming Soon)
        </button>
        {% endif %}
    </div>
</div>
{% endblock %}