{% extends "base.html" %}
{% load widget_tweaks %}

{% block title %}{{ page_title|default:"Create Bank Reconciliation" }}{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .section-header {
        border-bottom: 2px solid #e5e7eb;
        padding-bottom: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #374151;
        margin: 0;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
    }
    
    .required-field {
        color: #dc2626;
    }
    
    .btn-create {
        background: linear-gradient(45deg, #4f46e5, #7c3aed);
        border: none;
        border-radius: 8px;
        padding: 12px 32px;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-create:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(79, 70, 229, 0.4);
        color: white;
    }
    
    .difference-display {
        padding: 1rem;
        background: #f9fafb;
        border-radius: 8px;
        border-left: 4px solid #3b82f6;
        margin-top: 1rem;
    }
    
    .auto-calculate {
        background: #fef3c7;
        border: 1px solid #fbbf24;
        border-radius: 6px;
        padding: 0.75rem;
        font-size: 0.875rem;
        color: #92400e;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1 text-gray-800">{{ page_title|default:"Create Bank Reconciliation" }}</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'accounts:dashboard' %}">Accounts</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{% url 'accounts:bank_reconciliation_master_list' %}">Bank Reconciliations</a>
                    </li>
                    <li class="breadcrumb-item active">Create New</li>
                </ol>
            </nav>
        </div>
        <a href="{% url 'accounts:bank_reconciliation_master_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to List
        </a>
    </div>

    <form method="post" hx-post="{% url 'accounts:bank_reconciliation_master_create' %}" 
          hx-target="#form-container" hx-swap="outerHTML">
        {% csrf_token %}
        
        <div id="form-container">
            <!-- Basic Information Section -->
            <div class="form-section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="fas fa-info-circle me-2 text-primary"></i>
                        Basic Information
                    </h3>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.reconciliation_date.id_for_label }}" class="form-label">
                                Reconciliation Date <span class="required-field">*</span>
                            </label>
                            {{ form.reconciliation_date|add_class:"form-control" }}
                            {% if form.reconciliation_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.reconciliation_date.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.bank.id_for_label }}" class="form-label">
                                Bank Account <span class="required-field">*</span>
                            </label>
                            {{ form.bank|add_class:"form-select" }}
                            {% if form.bank.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.bank.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                Select the bank account for reconciliation
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.status.id_for_label }}" class="form-label">
                                Status
                            </label>
                            {{ form.status|add_class:"form-select" }}
                            {% if form.status.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.status.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Balance Information Section -->
            <div class="form-section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="fas fa-balance-scale me-2 text-success"></i>
                        Balance Information
                    </h3>
                </div>
                
                <div class="auto-calculate mb-3">
                    <i class="fas fa-calculator me-2"></i>
                    The difference amount will be calculated automatically when you enter both balances.
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.book_balance.id_for_label }}" class="form-label">
                                Book Balance <span class="required-field">*</span>
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">₹</span>
                                {{ form.book_balance|add_class:"form-control" }}
                            </div>
                            {% if form.book_balance.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.book_balance.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                Balance as per your accounting records
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.bank_statement_balance.id_for_label }}" class="form-label">
                                Bank Statement Balance <span class="required-field">*</span>
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">₹</span>
                                {{ form.bank_statement_balance|add_class:"form-control" }}
                            </div>
                            {% if form.bank_statement_balance.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.bank_statement_balance.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                Balance as per bank statement
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Difference Display -->
                <div id="difference-display" class="difference-display" style="display: none;">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-0">
                                <i class="fas fa-equals me-2"></i>
                                Calculated Difference
                            </h5>
                            <p class="text-muted mb-0">
                                Book Balance - Bank Statement Balance
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <h4 id="difference-amount" class="mb-0">₹0.00</h4>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Information Section -->
            <div class="form-section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="fas fa-sticky-note me-2 text-warning"></i>
                        Additional Information
                    </h3>
                </div>
                
                <div class="form-group">
                    <label for="{{ form.remarks.id_for_label }}" class="form-label">
                        Remarks
                    </label>
                    {{ form.remarks|add_class:"form-control" }}
                    {% if form.remarks.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.remarks.errors.0 }}
                        </div>
                    {% endif %}
                    <div class="form-text">
                        Optional notes about this reconciliation
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="d-flex justify-content-end gap-3">
                <a href="{% url 'accounts:bank_reconciliation_master_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-2"></i>Cancel
                </a>
                <button type="submit" class="btn btn-create">
                    <i class="fas fa-save me-2"></i>Create Reconciliation
                </button>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const bookBalanceInput = document.getElementById('{{ form.book_balance.id_for_label }}');
        const bankBalanceInput = document.getElementById('{{ form.bank_statement_balance.id_for_label }}');
        const differenceDisplay = document.getElementById('difference-display');
        const differenceAmount = document.getElementById('difference-amount');
        
        function calculateDifference() {
            const bookBalance = parseFloat(bookBalanceInput.value) || 0;
            const bankBalance = parseFloat(bankBalanceInput.value) || 0;
            
            if (bookBalance !== 0 || bankBalance !== 0) {
                const difference = bookBalance - bankBalance;
                differenceAmount.textContent = `₹${difference.toFixed(2)}`;
                
                // Color coding for difference
                if (difference > 0) {
                    differenceAmount.className = 'mb-0 text-danger';
                    differenceAmount.innerHTML = `<i class="fas fa-arrow-up me-1"></i>₹${difference.toFixed(2)}`;
                } else if (difference < 0) {
                    differenceAmount.className = 'mb-0 text-success';
                    differenceAmount.innerHTML = `<i class="fas fa-arrow-down me-1"></i>₹${Math.abs(difference).toFixed(2)}`;
                } else {
                    differenceAmount.className = 'mb-0 text-muted';
                    differenceAmount.innerHTML = `<i class="fas fa-check me-1"></i>₹0.00`;
                }
                
                differenceDisplay.style.display = 'block';
            } else {
                differenceDisplay.style.display = 'none';
            }
        }
        
        // Add event listeners for real-time calculation
        bookBalanceInput.addEventListener('input', calculateDifference);
        bankBalanceInput.addEventListener('input', calculateDifference);
        
        // Calculate on page load if values exist
        calculateDifference();
        
        // Set default date to today
        if (!bookBalanceInput.value) {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('{{ form.reconciliation_date.id_for_label }}').value = today;
        }
    });
    
    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const bankSelect = document.getElementById('{{ form.bank.id_for_label }}');
        const reconciliationDate = document.getElementById('{{ form.reconciliation_date.id_for_label }}');
        const bookBalance = document.getElementById('{{ form.book_balance.id_for_label }}');
        const bankBalance = document.getElementById('{{ form.bank_statement_balance.id_for_label }}');
        
        let isValid = true;
        
        if (!bankSelect.value) {
            bankSelect.classList.add('is-invalid');
            isValid = false;
        } else {
            bankSelect.classList.remove('is-invalid');
        }
        
        if (!reconciliationDate.value) {
            reconciliationDate.classList.add('is-invalid');
            isValid = false;
        } else {
            reconciliationDate.classList.remove('is-invalid');
        }
        
        if (!bookBalance.value) {
            bookBalance.classList.add('is-invalid');
            isValid = false;
        } else {
            bookBalance.classList.remove('is-invalid');
        }
        
        if (!bankBalance.value) {
            bankBalance.classList.add('is-invalid');
            isValid = false;
        } else {
            bankBalance.classList.remove('is-invalid');
        }
        
        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
        }
    });
</script>
{% endblock %}