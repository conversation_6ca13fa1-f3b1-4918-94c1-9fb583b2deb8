<!-- accounts/templates/accounts/mail_merge/partials/data_source_records.html -->
<!-- HTMX Partial for Mail Merge Data Source Records -->
<!-- Task Group 11: Financial Reporting & Analysis - Mail Merge Data Loading (Task 11.4) -->

<div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
    <table class="min-w-full divide-y divide-sap-gray-300">
        <thead class="bg-sap-gray-50">
            <tr>
                <th scope="col" class="relative px-6 py-3 text-left">
                    <input type="checkbox" 
                           class="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-sap-gray-300 text-sap-purple-600 focus:ring-sap-purple-500"
                           onchange="toggleAllRecords(this)">
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    Record Details
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    Key Information
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    Status
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    Actions
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-sap-gray-200">
            
            <!-- Customer Records -->
            {% if data_source == 'customers' %}
                {% for customer in records %}
                <tr class="hover:bg-sap-gray-50">
                    <td class="relative px-6 py-4 whitespace-nowrap">
                        <input type="checkbox" 
                               class="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-sap-gray-300 text-sap-purple-600 focus:ring-sap-purple-500"
                               value="{{ customer.id }}"
                               onchange="toggleRecord(this)">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-sap-blue-100 rounded-full flex items-center justify-center">
                                <i data-lucide="user" class="w-5 h-5 text-sap-blue-600"></i>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-sap-gray-900">{{ customer.customer_name }}</div>
                                <div class="text-sm text-sap-gray-500">{{ customer.customer_code }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-sap-gray-900">{{ customer.contact_person|default:"N/A" }}</div>
                        <div class="text-sm text-sap-gray-500">{{ customer.email|default:"No email" }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if customer.is_active %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-green-100 text-sap-green-800">
                            Active
                        </span>
                        {% else %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-red-100 text-sap-red-800">
                            Inactive
                        </span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button type="button" class="text-sap-purple-600 hover:text-sap-purple-700" 
                                onclick="previewRecord('{{ customer.id }}', 'customer')">
                            <i data-lucide="eye" class="w-4 h-4"></i>
                        </button>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="5" class="px-6 py-8 text-center text-sm text-sap-gray-500">
                        No customers found matching the criteria.
                    </td>
                </tr>
                {% endfor %}
            {% endif %}

            <!-- Invoice Records -->
            {% if data_source == 'invoices' %}
                {% for invoice in records %}
                <tr class="hover:bg-sap-gray-50">
                    <td class="relative px-6 py-4 whitespace-nowrap">
                        <input type="checkbox" 
                               class="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-sap-gray-300 text-sap-purple-600 focus:ring-sap-purple-500"
                               value="{{ invoice.id }}"
                               onchange="toggleRecord(this)">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-sap-green-100 rounded-full flex items-center justify-center">
                                <i data-lucide="file-text" class="w-5 h-5 text-sap-green-600"></i>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-sap-gray-900">{{ invoice.invoice_no }}</div>
                                <div class="text-sm text-sap-gray-500">{{ invoice.customer_name }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-sap-gray-900">₹{{ invoice.total_amount|floatformat:2 }}</div>
                        <div class="text-sm text-sap-gray-500">{{ invoice.invoice_date }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if invoice.payment_status == 'paid' %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-green-100 text-sap-green-800">
                            Paid
                        </span>
                        {% elif invoice.payment_status == 'partial' %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-yellow-100 text-sap-yellow-800">
                            Partial
                        </span>
                        {% else %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-red-100 text-sap-red-800">
                            Outstanding
                        </span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button type="button" class="text-sap-purple-600 hover:text-sap-purple-700" 
                                onclick="previewRecord('{{ invoice.id }}', 'invoice')">
                            <i data-lucide="eye" class="w-4 h-4"></i>
                        </button>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="5" class="px-6 py-8 text-center text-sm text-sap-gray-500">
                        No invoices found matching the criteria.
                    </td>
                </tr>
                {% endfor %}
            {% endif %}

            <!-- Credit Note Records -->
            {% if data_source == 'credit_notes' %}
                {% for credit_note in records %}
                <tr class="hover:bg-sap-gray-50">
                    <td class="relative px-6 py-4 whitespace-nowrap">
                        <input type="checkbox" 
                               class="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-sap-gray-300 text-sap-purple-600 focus:ring-sap-purple-500"
                               value="{{ credit_note.id }}"
                               onchange="toggleRecord(this)">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-sap-indigo-100 rounded-full flex items-center justify-center">
                                <i data-lucide="minus-circle" class="w-5 h-5 text-sap-indigo-600"></i>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-sap-gray-900">{{ credit_note.credit_note_no }}</div>
                                <div class="text-sm text-sap-gray-500">{{ credit_note.customer_name }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-sap-gray-900">₹{{ credit_note.credit_amount|floatformat:2 }}</div>
                        <div class="text-sm text-sap-gray-500">{{ credit_note.credit_date }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if credit_note.status == 'approved' %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-green-100 text-sap-green-800">
                            Approved
                        </span>
                        {% elif credit_note.status == 'pending' %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-yellow-100 text-sap-yellow-800">
                            Pending
                        </span>
                        {% else %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-red-100 text-sap-red-800">
                            Rejected
                        </span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button type="button" class="text-sap-purple-600 hover:text-sap-purple-700" 
                                onclick="previewRecord('{{ credit_note.id }}', 'credit_note')">
                            <i data-lucide="eye" class="w-4 h-4"></i>
                        </button>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="5" class="px-6 py-8 text-center text-sm text-sap-gray-500">
                        No credit notes found matching the criteria.
                    </td>
                </tr>
                {% endfor %}
            {% endif %}

            <!-- Debit Note Records -->
            {% if data_source == 'debit_notes' %}
                {% for debit_note in records %}
                <tr class="hover:bg-sap-gray-50">
                    <td class="relative px-6 py-4 whitespace-nowrap">
                        <input type="checkbox" 
                               class="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-sap-gray-300 text-sap-purple-600 focus:ring-sap-purple-500"
                               value="{{ debit_note.id }}"
                               onchange="toggleRecord(this)">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-sap-orange-100 rounded-full flex items-center justify-center">
                                <i data-lucide="plus-circle" class="w-5 h-5 text-sap-orange-600"></i>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-sap-gray-900">{{ debit_note.debit_note_no }}</div>
                                <div class="text-sm text-sap-gray-500">{{ debit_note.customer_name }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-sap-gray-900">₹{{ debit_note.debit_amount|floatformat:2 }}</div>
                        <div class="text-sm text-sap-gray-500">{{ debit_note.debit_date }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if debit_note.status == 'approved' %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-green-100 text-sap-green-800">
                            Approved
                        </span>
                        {% elif debit_note.status == 'pending' %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-yellow-100 text-sap-yellow-800">
                            Pending
                        </span>
                        {% else %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-red-100 text-sap-red-800">
                            Rejected
                        </span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button type="button" class="text-sap-purple-600 hover:text-sap-purple-700" 
                                onclick="previewRecord('{{ debit_note.id }}', 'debit_note')">
                            <i data-lucide="eye" class="w-4 h-4"></i>
                        </button>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="5" class="px-6 py-8 text-center text-sm text-sap-gray-500">
                        No debit notes found matching the criteria.
                    </td>
                </tr>
                {% endfor %}
            {% endif %}

            <!-- Outstanding Invoices Records -->
            {% if data_source == 'outstanding_invoices' %}
                {% for invoice in records %}
                <tr class="hover:bg-sap-gray-50">
                    <td class="relative px-6 py-4 whitespace-nowrap">
                        <input type="checkbox" 
                               class="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-sap-gray-300 text-sap-purple-600 focus:ring-sap-purple-500"
                               value="{{ invoice.id }}"
                               onchange="toggleRecord(this)">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-sap-red-100 rounded-full flex items-center justify-center">
                                <i data-lucide="alert-triangle" class="w-5 h-5 text-sap-red-600"></i>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-sap-gray-900">{{ invoice.invoice_no }}</div>
                                <div class="text-sm text-sap-gray-500">{{ invoice.customer_name }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-sap-gray-900">₹{{ invoice.outstanding_amount|floatformat:2 }}</div>
                        <div class="text-sm text-sap-gray-500">Due: {{ invoice.due_date }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if invoice.days_overdue > 60 %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-red-100 text-sap-red-800">
                            {{ invoice.days_overdue }}+ days
                        </span>
                        {% elif invoice.days_overdue > 30 %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-orange-100 text-sap-orange-800">
                            {{ invoice.days_overdue }} days
                        </span>
                        {% else %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-yellow-100 text-sap-yellow-800">
                            {{ invoice.days_overdue }} days
                        </span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button type="button" class="text-sap-purple-600 hover:text-sap-purple-700" 
                                onclick="previewRecord('{{ invoice.id }}', 'outstanding_invoice')">
                            <i data-lucide="eye" class="w-4 h-4"></i>
                        </button>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="5" class="px-6 py-8 text-center text-sm text-sap-gray-500">
                        No outstanding invoices found matching the criteria.
                    </td>
                </tr>
                {% endfor %}
            {% endif %}
        </tbody>
    </table>
</div>

<!-- Record Selection Summary -->
<div class="mt-4 flex items-center justify-between">
    <div class="text-sm text-sap-gray-700">
        <span id="selected-count">0</span> of <span id="total-count">{{ records|length }}</span> records selected
    </div>
    <div class="flex items-center space-x-2">
        <button type="button" onclick="selectAllRecords()" 
                class="text-sm text-sap-blue-600 hover:text-sap-blue-700 font-medium">
            Select All
        </button>
        <span class="text-sap-gray-300">|</span>
        <button type="button" onclick="clearSelection()" 
                class="text-sm text-sap-red-600 hover:text-sap-red-700 font-medium">
            Clear Selection
        </button>
    </div>
</div>

<script>
function toggleAllRecords(checkbox) {
    const recordCheckboxes = document.querySelectorAll('input[type="checkbox"][value]');
    recordCheckboxes.forEach(cb => {
        cb.checked = checkbox.checked;
    });
    updateSelectedCount();
}

function toggleRecord(checkbox) {
    updateSelectedCount();
    
    // Update main checkbox state
    const allCheckboxes = document.querySelectorAll('input[type="checkbox"][value]');
    const checkedCheckboxes = document.querySelectorAll('input[type="checkbox"][value]:checked');
    const mainCheckbox = document.querySelector('input[type="checkbox"]:not([value])');
    
    if (checkedCheckboxes.length === 0) {
        mainCheckbox.indeterminate = false;
        mainCheckbox.checked = false;
    } else if (checkedCheckboxes.length === allCheckboxes.length) {
        mainCheckbox.indeterminate = false;
        mainCheckbox.checked = true;
    } else {
        mainCheckbox.indeterminate = true;
    }
}

function updateSelectedCount() {
    const checkedCheckboxes = document.querySelectorAll('input[type="checkbox"][value]:checked');
    document.getElementById('selected-count').textContent = checkedCheckboxes.length;
}

function selectAllRecords() {
    const recordCheckboxes = document.querySelectorAll('input[type="checkbox"][value]');
    recordCheckboxes.forEach(cb => {
        cb.checked = true;
    });
    document.querySelector('input[type="checkbox"]:not([value])').checked = true;
    updateSelectedCount();
}

function clearSelection() {
    const recordCheckboxes = document.querySelectorAll('input[type="checkbox"]');
    recordCheckboxes.forEach(cb => {
        cb.checked = false;
        cb.indeterminate = false;
    });
    updateSelectedCount();
}

function previewRecord(id, type) {
    console.log(`Previewing ${type} record with ID: ${id}`);
    // Implementation for record preview modal
}

// Initialize icons and update count
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
    updateSelectedCount();
});
</script>