﻿using System;
using System.Collections;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Xml.Linq;
using System.Data.SqlClient;
using CrystalDecisions.CrystalReports.Engine;
using System.Text.RegularExpressions;

public partial class Module_Accounts_Transactions_BillBooking_Print_Details : System.Web.UI.Page
{
    clsFunctions fun = new clsFunctions();
    string sId = "";
    int FinYearId = 0;
    int CompId = 0;
    int Mid = 0;
    string address = "";
    ReportDocument cryRpt = new ReportDocument();
    string Type = "";
    string Payterms = "";
    int pageFlag = 0;
    string sessKey = string.Empty;
    string key = string.Empty;
    string SupId = string.Empty;
    string lnkFor = string.Empty;

    protected void Page_Init(object sender, EventArgs e)
    {
        string connStr = fun.Connection();
        SqlConnection con = new SqlConnection(connStr);
        
        try
        {  
            DataTable dt = new DataTable();
            DataTable dt2 = new DataTable();
            DataSet DSSql = new DataSet();
            con.Open();

            if (!IsPostBack)
            {
                sId = Session["username"].ToString();
                FinYearId = Convert.ToInt32(Session["finyear"]);
                CompId = Convert.ToInt32(Session["compid"]);
                Mid = Convert.ToInt32(Request.QueryString["Id"]);

                string StrSql = fun.select("tblACC_BillBooking_Master.Id ,tblACC_BillBooking_Master.TDSCode,tblACC_BillBooking_Master.SessionId ,tblACC_BillBooking_Master.AuthorizeBy,tblACC_BillBooking_Master.AuthorizeDate, tblACC_BillBooking_Master.SysDate , tblACC_BillBooking_Master.SysTime  , tblACC_BillBooking_Master.SessionId, tblACC_BillBooking_Master.CompId , tblACC_BillBooking_Master.FinYearId, tblACC_BillBooking_Master.PVEVNo, tblACC_BillBooking_Master.SupplierId , tblACC_BillBooking_Details.PODId, tblACC_BillBooking_Master.BillNo, tblACC_BillBooking_Master.BillDate , tblACC_BillBooking_Master.CENVATEntryNo, tblACC_BillBooking_Master.CENVATEntryDate, tblACC_BillBooking_Master.OtherCharges, tblACC_BillBooking_Master.OtherChaDesc, tblACC_BillBooking_Master.Narration , tblACC_BillBooking_Master.DebitAmt , tblACC_BillBooking_Master.DiscountType, tblACC_BillBooking_Master.Discount,tblACC_BillBooking_Details.GQNId,tblACC_BillBooking_Details.GSNId, tblACC_BillBooking_Details.ItemId,tblACC_BillBooking_Details.PFAmt,tblACC_BillBooking_Details.ExStBasicInPer ,tblACC_BillBooking_Details.ExStEducessInPer ,tblACC_BillBooking_Details.ExStShecessInPer,tblACC_BillBooking_Details.ExStBasic ,tblACC_BillBooking_Details.ExStEducess ,tblACC_BillBooking_Details.ExStShecess ,tblACC_BillBooking_Details.CustomDuty ,tblACC_BillBooking_Details.VAT ,tblACC_BillBooking_Details.CST ,tblACC_BillBooking_Details.Freight ,tblACC_BillBooking_Details.TarrifNo,tblACC_BillBooking_Details.DebitType,tblACC_BillBooking_Details.DebitValue,tblACC_BillBooking_Details.BCDOpt,tblACC_BillBooking_Details.BCD,tblACC_BillBooking_Details.BCDValue,tblACC_BillBooking_Details.ValueForCVD,tblACC_BillBooking_Details.ValueForEdCessCD,tblACC_BillBooking_Details.EdCessOnCDOpt,tblACC_BillBooking_Details.EdCessOnCD,tblACC_BillBooking_Details.EdCessOnCDValue,tblACC_BillBooking_Details.SHEDCessOpt,tblACC_BillBooking_Details.SHEDCess,tblACC_BillBooking_Details.SHEDCessValue,tblACC_BillBooking_Details.TotDuty,tblACC_BillBooking_Details.TotDutyEDSHED,tblACC_BillBooking_Details.Insurance,tblACC_BillBooking_Details.ValueWithDuty", "tblACC_BillBooking_Master,tblACC_BillBooking_Details", "tblACC_BillBooking_Master.CompId='" + CompId + "'  And tblACC_BillBooking_Master.Id='" + Mid + "' And tblACC_BillBooking_Master.Id=tblACC_BillBooking_Details.MId AND tblACC_BillBooking_Master.FinYearId<='" + FinYearId + "'");

                SqlCommand cmdSql = new SqlCommand(StrSql, con);
                SqlDataAdapter daSql = new SqlDataAdapter(cmdSql);
                daSql.Fill(DSSql);

                dt.Columns.Add(new System.Data.DataColumn("Id", typeof(int)));//0
                dt.Columns.Add(new System.Data.DataColumn("GQNNo", typeof(string)));//1
                dt.Columns.Add(new System.Data.DataColumn("GSNNo", typeof(string)));//2
                dt.Columns.Add(new System.Data.DataColumn("ItemCode", typeof(string)));//3
                dt.Columns.Add(new System.Data.DataColumn("Descr", typeof(string)));//4
                dt.Columns.Add(new System.Data.DataColumn("UOM", typeof(string)));//5
                dt.Columns.Add(new System.Data.DataColumn("Amt", typeof(double)));//6
                dt.Columns.Add(new System.Data.DataColumn("PFAmt", typeof(double)));//7
                dt.Columns.Add(new System.Data.DataColumn("ExStBasicInPer", typeof(string)));//8
                dt.Columns.Add(new System.Data.DataColumn("ExStEducessInPer", typeof(string)));//9
                dt.Columns.Add(new System.Data.DataColumn("ExStShecessInPer", typeof(string)));//10
                dt.Columns.Add(new System.Data.DataColumn("ExStBasic", typeof(double)));//11
                dt.Columns.Add(new System.Data.DataColumn("ExStEducess", typeof(double)));//12
                dt.Columns.Add(new System.Data.DataColumn("ExStShecess", typeof(double)));//13
                dt.Columns.Add(new System.Data.DataColumn("CustomDuty", typeof(double)));//14
                dt.Columns.Add(new System.Data.DataColumn("VAT", typeof(double)));//15
                dt.Columns.Add(new System.Data.DataColumn("CST", typeof(double)));//16
                dt.Columns.Add(new System.Data.DataColumn("Freight", typeof(double)));//17
                dt.Columns.Add(new System.Data.DataColumn("TarrifNo", typeof(string)));//18
                dt.Columns.Add(new System.Data.DataColumn("FinYear", typeof(string)));//19
                dt.Columns.Add(new System.Data.DataColumn("PVEVNo", typeof(string)));//20
                dt.Columns.Add(new System.Data.DataColumn("SysDate", typeof(string)));//21
                dt.Columns.Add(new System.Data.DataColumn("PONo", typeof(string)));//22
                dt.Columns.Add(new System.Data.DataColumn("PODate", typeof(string)));//23
                dt.Columns.Add(new System.Data.DataColumn("SupplierName", typeof(string)));//24
                dt.Columns.Add(new System.Data.DataColumn("CompId", typeof(int)));//25
                dt.Columns.Add(new System.Data.DataColumn("SupplierId", typeof(string)));//26
                dt.Columns.Add(new System.Data.DataColumn("OtherCharges", typeof(double)));//27
                dt.Columns.Add(new System.Data.DataColumn("OtherChaDesc", typeof(string)));//28
                dt.Columns.Add(new System.Data.DataColumn("Narration", typeof(string)));//29
                dt.Columns.Add(new System.Data.DataColumn("DebitAmt", typeof(double)));//30
                dt.Columns.Add(new System.Data.DataColumn("DiscountType", typeof(int)));//31
                dt.Columns.Add(new System.Data.DataColumn("Discount", typeof(double)));//32
                dt.Columns.Add(new System.Data.DataColumn("BillNo", typeof(string)));//33
                dt.Columns.Add(new System.Data.DataColumn("BillDate", typeof(string)));//34
                dt.Columns.Add(new System.Data.DataColumn("CENVATEntryNo", typeof(string)));//35
                dt.Columns.Add(new System.Data.DataColumn("CENVATEntryDate", typeof(string)));//36
                dt.Columns.Add(new System.Data.DataColumn("AccHead", typeof(string)));//37
                dt.Columns.Add(new System.Data.DataColumn("WODept", typeof(string)));//38
                dt.Columns.Add(new System.Data.DataColumn("POQty", typeof(double)));//39
                dt.Columns.Add(new System.Data.DataColumn("PORate", typeof(double)));//40
                dt.Columns.Add(new System.Data.DataColumn("Disc", typeof(double)));//41
                dt.Columns.Add(new System.Data.DataColumn("AccQty", typeof(double)));//42
                dt.Columns.Add(new System.Data.DataColumn("PFTerm", typeof(string)));//43
                dt.Columns.Add(new System.Data.DataColumn("ExciseTerm", typeof(string)));//44
                dt.Columns.Add(new System.Data.DataColumn("VATTerm", typeof(string)));//45
                dt.Columns.Add(new System.Data.DataColumn("IsVATCST", typeof(string)));//46
                dt.Columns.Add(new System.Data.DataColumn("DebitType", typeof(Int32)));//47
                dt.Columns.Add(new System.Data.DataColumn("DebitValue", typeof(double)));//48
                //dt.Columns.Add(new System.Data.DataColumn("PreparedBy", typeof(string)));//47
                //dt.Columns.Add(new System.Data.DataColumn("AuthorizeBy", typeof(string)));//48
                dt.Columns.Add(new System.Data.DataColumn("PFid", typeof(int)));//49
                dt.Columns.Add(new System.Data.DataColumn("ExciseId", typeof(int)));//50
                dt.Columns.Add(new System.Data.DataColumn("VATCSTid", typeof(int)));//51
                dt.Columns.Add(new System.Data.DataColumn("BasicAmt", typeof(double)));//52
                dt.Columns.Add(new System.Data.DataColumn("BCDOpt", typeof(string)));//53
                dt.Columns.Add(new System.Data.DataColumn("BCD", typeof(double)));//54
                dt.Columns.Add(new System.Data.DataColumn("BCDValue", typeof(double)));//55
                dt.Columns.Add(new System.Data.DataColumn("ValueForCVD", typeof(double)));//56
                dt.Columns.Add(new System.Data.DataColumn("ValueForEdCessCD", typeof(double)));//57
                dt.Columns.Add(new System.Data.DataColumn("EdCessOnCDOpt", typeof(string)));//58
                dt.Columns.Add(new System.Data.DataColumn("EdCessOnCD", typeof(double)));//59
                dt.Columns.Add(new System.Data.DataColumn("EdCessOnCDValue", typeof(double)));//60
                dt.Columns.Add(new System.Data.DataColumn("SHEDCessOpt", typeof(string)));//61
                dt.Columns.Add(new System.Data.DataColumn("SHEDCess", typeof(double)));//62
                dt.Columns.Add(new System.Data.DataColumn("SHEDCessValue", typeof(double)));//63
                dt.Columns.Add(new System.Data.DataColumn("TotDuty", typeof(double)));//64
                dt.Columns.Add(new System.Data.DataColumn("TotDutyEDSHED", typeof(double)));//65
                dt.Columns.Add(new System.Data.DataColumn("Insurance", typeof(double)));//66
                dt.Columns.Add(new System.Data.DataColumn("ValueWithDuty", typeof(double)));//67
                dt.Columns.Add(new System.Data.DataColumn("SectionNo", typeof(string)));//68
                dt.Columns.Add(new System.Data.DataColumn("TDSPerCentage", typeof(double)));//69
                dt.Columns.Add(new System.Data.DataColumn("PaymentRange", typeof(double)));//70
                dt.Columns.Add(new System.Data.DataColumn("TDSCode", typeof(int)));//71
                dt.Columns.Add(new System.Data.DataColumn("BookedBillTotal", typeof(double)));//72

                DataSet BillBook = new DataSet();
                DataRow dr;
                string POIdGroup = string.Empty;

                for (int i = 0; i < DSSql.Tables[0].Rows.Count; i++)
                {
                    dr = dt.NewRow();
                    if (DSSql.Tables[0].Rows.Count > 0)
                    {
                        double Discount = 0;
                        double Amt = 0;
                        double Rate = 0;
                        string wodept = "";
                        string AccHead = "";

                        POIdGroup += "'" + DSSql.Tables[0].Rows[i]["PODId"].ToString() + "',";

                        string StrSql11 = fun.select("tblMM_PO_Details.Qty,tblMM_PO_Details.Rate,tblMM_PO_Details.Discount,tblMM_PO_Master.PRSPRFlag,tblMM_PO_Details.PRNo,tblMM_PO_Details.PRId,tblMM_PO_Details.SPRNo,tblMM_PO_Details.SPRId,tblMM_PO_Details.PF,tblMM_PO_Details.ExST,tblMM_PO_Details.VAT", "tblMM_PO_Details,tblMM_PO_Master", "tblMM_PO_Details.Id='" + DSSql.Tables[0].Rows[i]["PODId"].ToString() + "' AND   tblMM_PO_Master.Id=tblMM_PO_Details.MId AND tblMM_PO_Master.CompId='" + CompId + "'  AND tblMM_PO_Master.PONo=tblMM_PO_Details.PONo");
                        SqlCommand cmdPo11 = new SqlCommand(StrSql11, con);
                        SqlDataAdapter DAPo11 = new SqlDataAdapter(cmdPo11);
                        DataSet DSSql11 = new DataSet();
                        DAPo11.Fill(DSSql11);

                        if (DSSql11.Tables[0].Rows.Count > 0)
                        {
                            Rate = Convert.ToDouble(decimal.Parse((DSSql11.Tables[0].Rows[0]["Rate"]).ToString()).ToString("N2"));
                            Discount = Convert.ToDouble(decimal.Parse((DSSql11.Tables[0].Rows[0]["Discount"]).ToString()).ToString("N2"));

                            if (DSSql11.Tables[0].Rows[0]["PRSPRFlag"].ToString() == "0")
                            {
                                string StrFlag = fun.select("tblMM_PR_Master.WONo,tblMM_PR_Details.AHId", "tblMM_PR_Details,tblMM_PR_Master", "tblMM_PR_Master.PRNo='" + DSSql11.Tables[0].Rows[0]["PRNo"].ToString() + "'  AND tblMM_PR_Master.PRNo=tblMM_PR_Details.PRNo AND tblMM_PR_Details.Id='" + DSSql11.Tables[0].Rows[0]["PRId"].ToString() + "'");

                                SqlCommand cmdFlag = new SqlCommand(StrFlag, con);
                                SqlDataAdapter daFlag = new SqlDataAdapter(cmdFlag);
                                DataSet DSFlag = new DataSet();
                                daFlag.Fill(DSFlag);

                                // For WO No
                                if (DSFlag.Tables[0].Rows.Count > 0)
                                {
                                    string sqlacc1 = fun.select("Symbol AS AccHead", "AccHead", "Id ='" + DSFlag.Tables[0].Rows[0]["AHId"].ToString() + "' ");
                                    SqlCommand cmdacc1 = new SqlCommand(sqlacc1, con);
                                    SqlDataAdapter daacc1 = new SqlDataAdapter(cmdacc1);
                                    DataSet DSacc1 = new DataSet();
                                    daacc1.Fill(DSacc1);
                                    if (DSacc1.Tables[0].Rows.Count > 0)
                                    {
                                        AccHead = DSacc1.Tables[0].Rows[0]["AccHead"].ToString();
                                    }
                                    wodept = DSFlag.Tables[0].Rows[0]["WONo"].ToString();
                                }
                            }
                            else if (DSSql11.Tables[0].Rows[0]["PRSPRFlag"].ToString() == "1")
                            {
                                string StrFlag1 = fun.select("tblMM_SPR_Details.WONo,tblMM_SPR_Details.DeptId,tblMM_SPR_Details.AHId", "tblMM_SPR_Details,tblMM_SPR_Master", "tblMM_SPR_Master.SPRNo='" + DSSql11.Tables[0].Rows[0]["SPRNo"].ToString() + "'  AND tblMM_SPR_Master.SPRNo=tblMM_SPR_Details.SPRNo AND tblMM_SPR_Details.Id='" + DSSql11.Tables[0].Rows[0]["SPRId"].ToString() + "'");

                                SqlCommand cmdFlag1 = new SqlCommand(StrFlag1, con);
                                SqlDataAdapter daFlag1 = new SqlDataAdapter(cmdFlag1);
                                DataSet DSFlag1 = new DataSet();
                                daFlag1.Fill(DSFlag1);


                                if (DSFlag1.Tables[0].Rows.Count > 0)
                                {
                                    string sqlacc = fun.select("Symbol AS AccHead", "AccHead", "Id ='" + DSFlag1.Tables[0].Rows[0]["AHId"].ToString() + "' ");
                                    SqlCommand cmdacc = new SqlCommand(sqlacc, con);
                                    SqlDataAdapter daacc = new SqlDataAdapter(cmdacc);
                                    DataSet DSacc = new DataSet();
                                    daacc.Fill(DSacc);
                                    if (DSacc.Tables[0].Rows.Count > 0)
                                    {
                                        AccHead = DSacc.Tables[0].Rows[0]["AccHead"].ToString();
                                    }

                                    if (DSFlag1.Tables[0].Rows[0]["DeptId"].ToString() == "0")
                                    {
                                        // For WO No
                                        wodept = DSFlag1.Tables[0].Rows[0]["WONo"].ToString();
                                    }
                                    else
                                    {
                                        // For Dept Name
                                        string sqlDeptName = fun.select("Symbol AS Dept", "tblHR_Departments", "Id ='" + Convert.ToInt32(DSFlag1.Tables[0].Rows[0]["DeptId"].ToString()) + "' ");
                                        SqlCommand cmdDeptName = new SqlCommand(sqlDeptName, con);
                                        SqlDataAdapter daDeptName = new SqlDataAdapter(cmdDeptName);
                                        DataSet DSDeptName = new DataSet();
                                        daDeptName.Fill(DSDeptName);

                                        if (DSDeptName.Tables[0].Rows.Count > 0)
                                        {
                                            wodept = DSDeptName.Tables[0].Rows[0]["Dept"].ToString();
                                        }
                                    }
                                }
                            }

                            string StrPF = fun.select("Terms,Id", "tblPacking_Master", "Id='" + DSSql11.Tables[0].Rows[0]["PF"].ToString() + "'");
                            SqlCommand cmdPF = new SqlCommand(StrPF, con);
                            SqlDataAdapter daPF = new SqlDataAdapter(cmdPF);
                            DataSet DSPF = new DataSet();
                            daPF.Fill(DSPF, "tblPacking_Master");

                            dr[43] = DSPF.Tables[0].Rows[0]["Terms"].ToString();
                            dr[49] = Convert.ToInt32(DSPF.Tables[0].Rows[0]["Id"]);

                            string StrEx = fun.select("Terms,Id", "tblExciseser_Master", "Id='" + DSSql11.Tables[0].Rows[0]["ExST"].ToString() + "'");
                            SqlCommand cmdEx = new SqlCommand(StrEx, con);
                            SqlDataAdapter daEx = new SqlDataAdapter(cmdEx);
                            DataSet DSEx = new DataSet();
                            daEx.Fill(DSEx, "tblExciseser_Master");

                            dr[44] = DSEx.Tables[0].Rows[0]["Terms"].ToString();
                            dr[50] = Convert.ToInt32(DSEx.Tables[0].Rows[0]["Id"]);

                            string StrVATCST = fun.select("Terms,IsVAT,IsCST,Id", "tblVAT_Master", "Id='" + DSSql11.Tables[0].Rows[0]["VAT"].ToString() + "'");
                            SqlCommand cmdVATCST = new SqlCommand(StrVATCST, con);
                            SqlDataAdapter daVATCST = new SqlDataAdapter(cmdVATCST);
                            DataSet DSVATCST = new DataSet();
                            daVATCST.Fill(DSVATCST, "tblVAT_Master");

                            dr[45] = DSVATCST.Tables[0].Rows[0]["Terms"].ToString();
                            dr[51] = Convert.ToInt32(DSVATCST.Tables[0].Rows[0]["Id"]);

                            if (DSVATCST.Tables[0].Rows[0]["IsVAT"].ToString() == "1")
                            {
                                dr[46] = "VAT";
                            }
                            else if (DSVATCST.Tables[0].Rows[0]["IsCST"].ToString() == "1")
                            {
                                dr[46] = "CST";
                            }

                            if (DSVATCST.Tables[0].Rows[0]["IsVAT"].ToString() == "0" && DSVATCST.Tables[0].Rows[0]["IsCST"].ToString() == "0")
                            {
                                dr[46] = "VAT/CST";
                            }
                        }

                        if (DSSql.Tables[0].Rows[i]["GQNId"].ToString() != "0")
                        {
                            string Strgqn = fun.select("tblQc_MaterialQuality_Master.Id,tblQc_MaterialQuality_Master.GQNNo,tblQc_MaterialQuality_Details.AcceptedQty", "tblQc_MaterialQuality_Master,tblQc_MaterialQuality_Details", "tblQc_MaterialQuality_Master.Id=tblQc_MaterialQuality_Details.MId AND tblQc_MaterialQuality_Details.Id='" + DSSql.Tables[0].Rows[i]["GQNId"].ToString() + "' AND tblQc_MaterialQuality_Master.CompId='" + CompId + "'");

                            SqlCommand cmdgqn = new SqlCommand(Strgqn, con);
                            SqlDataAdapter dagqn = new SqlDataAdapter(cmdgqn);
                            DataSet DSgqn = new DataSet();
                            dagqn.Fill(DSgqn);

                            if (DSgqn.Tables[0].Rows.Count > 0)
                            {
                                dr[1] = DSgqn.Tables[0].Rows[0]["GQNNo"].ToString();

                                double AccQty = 0;
                                AccQty = Convert.ToDouble(DSgqn.Tables[0].Rows[0]["AcceptedQty"]);
                                Amt = ((Rate - (Rate * Discount) / 100) * AccQty);
                                dr[42] = AccQty;

                            }
                            else
                            {
                                dr[1] = "";
                            }

                        }
                        else if (DSSql.Tables[0].Rows[i]["GSNId"].ToString() != "0")
                        {
                            string Strgsn = fun.select("tblinv_MaterialServiceNote_Master.Id,tblinv_MaterialServiceNote_Master.GSNNo,tblinv_MaterialServiceNote_Details.ReceivedQty", "tblinv_MaterialServiceNote_Master,tblinv_MaterialServiceNote_Details", "tblinv_MaterialServiceNote_Details.Id='" + DSSql.Tables[0].Rows[i]["GSNId"].ToString() + "' AND tblinv_MaterialServiceNote_Master.CompId='" + CompId + "' AND tblinv_MaterialServiceNote_Master.Id=tblinv_MaterialServiceNote_Details.MId");

                            SqlCommand cmdgsn = new SqlCommand(Strgsn, con);
                            SqlDataAdapter dagsn = new SqlDataAdapter(cmdgsn);
                            DataSet DSgsn = new DataSet();
                            dagsn.Fill(DSgsn);

                            if (DSgsn.Tables[0].Rows.Count > 0)
                            {
                                dr[1] = DSgsn.Tables[0].Rows[0]["GSNNo"].ToString();

                                double AccQty = 0;
                                AccQty = Convert.ToDouble(DSgsn.Tables[0].Rows[0]["ReceivedQty"]);
                                Amt = ((Rate - (Rate * Discount) / 100) * AccQty);
                                dr[42] = AccQty;
                            }
                            else
                            {
                                dr[1] = "";
                            }
                        }

                        dr[0] = DSSql.Tables[0].Rows[i]["Id"].ToString();

                        string StrIcode1 = fun.select("ItemCode,ManfDesc As Descr,UOMBasic ", "tblDG_Item_Master", "Id='" + DSSql.Tables[0].Rows[i]["ItemId"].ToString() + "'");
                        SqlCommand cmdIcode1 = new SqlCommand(StrIcode1, con);
                        SqlDataAdapter daIcode1 = new SqlDataAdapter(cmdIcode1);
                        DataSet DSIcode1 = new DataSet();
                        daIcode1.Fill(DSIcode1);

                        if (DSIcode1.Tables[0].Rows.Count > 0)
                        {
                            dr[3] = DSIcode1.Tables[0].Rows[0]["ItemCode"].ToString();
                            dr[4] = DSIcode1.Tables[0].Rows[0]["Descr"].ToString();

                            // for UOM Basic  from Unit Master table
                            string sqlPurch1 = fun.select("Symbol As UOM ", "Unit_Master", "Id='" + DSIcode1.Tables[0].Rows[0]["UOMBasic"].ToString() + "' ");
                            SqlCommand cmdPurch1 = new SqlCommand(sqlPurch1, con);
                            SqlDataAdapter daPurch1 = new SqlDataAdapter(cmdPurch1);
                            DataSet DSPurch1 = new DataSet();
                            daPurch1.Fill(DSPurch1);

                            if (DSPurch1.Tables[0].Rows.Count > 0)
                            {
                                dr[5] = DSPurch1.Tables[0].Rows[0][0].ToString();
                            }
                        }

                        dr[6] = Amt;
                        
                        dr[7] = DSSql.Tables[0].Rows[i]["PFAmt"].ToString();
                        dr[8] = DSSql.Tables[0].Rows[i]["ExStBasicInPer"].ToString();
                        dr[9] = DSSql.Tables[0].Rows[i]["ExStEducessInPer"].ToString();
                        dr[10] = DSSql.Tables[0].Rows[i]["ExStShecessInPer"].ToString();
                        dr[11] = DSSql.Tables[0].Rows[i]["ExStBasic"].ToString();
                        dr[12] = DSSql.Tables[0].Rows[i]["ExStEducess"].ToString();
                        dr[13] = DSSql.Tables[0].Rows[i]["ExStShecess"].ToString();
                        dr[14] = DSSql.Tables[0].Rows[i]["CustomDuty"].ToString();
                        dr[15] = DSSql.Tables[0].Rows[i]["VAT"].ToString();
                        dr[16] = DSSql.Tables[0].Rows[i]["CST"].ToString();
                        dr[17] = DSSql.Tables[0].Rows[i]["Freight"].ToString();
                        dr[18] = DSSql.Tables[0].Rows[i]["TarrifNo"].ToString();

                        string SqlFin = fun.select("FinYear", "tblFinancial_master", "FinYearId='" + DSSql.Tables[0].Rows[i]["FinYearId"].ToString() + "' And CompId='" + CompId + "'");
                        SqlCommand CmdFin = new SqlCommand(SqlFin, con);
                        SqlDataAdapter DaFin = new SqlDataAdapter(CmdFin);
                        DataSet DsFin = new DataSet();
                        DaFin.Fill(DsFin);

                        dr[19] = DsFin.Tables[0].Rows[0]["FinYear"].ToString();
                        dr[20] = DSSql.Tables[0].Rows[i]["PVEVNo"].ToString();
                        dr[21] = fun.FromDateDMY(DSSql.Tables[0].Rows[i]["SysDate"].ToString());

                        string Strpo = fun.select("tblMM_PO_Master.PONo,tblMM_PO_Master.PaymentTerms,tblMM_PO_Master.SysDate As PODate", "tblMM_PO_Master,tblMM_PO_Details", "tblMM_PO_Master.CompId='" + CompId + "' And tblMM_PO_Details.Id='" + DSSql.Tables[0].Rows[i]["PODId"].ToString() + "' AND tblMM_PO_Master.Id=tblMM_PO_Details.MId");

                        SqlCommand cmdpo = new SqlCommand(Strpo, con);
                        SqlDataAdapter dapo = new SqlDataAdapter(cmdpo);
                        DataSet DSpo = new DataSet();
                        dapo.Fill(DSpo);

                        if (DSpo.Tables[0].Rows.Count > 0)
                        {
                            dr[22] = DSpo.Tables[0].Rows[0]["PONo"].ToString();
                            dr[23] = fun.FromDateDMY(DSpo.Tables[0].Rows[0]["PODate"].ToString());
                        }

                        string sqlSup = fun.select("*", "tblMM_Supplier_master", "SupplierId='" + DSSql.Tables[0].Rows[i]["SupplierId"].ToString() + "'");
                        SqlCommand cmdSupId = new SqlCommand(sqlSup, con);
                        SqlDataAdapter daSupId = new SqlDataAdapter(cmdSupId);
                        DataSet DSSupId = new DataSet();
                        daSupId.Fill(DSSupId);

                        string PANNo = string.Empty;

                        if (DSSupId.Tables[0].Rows.Count > 0)
                        {
                            dr[24] = DSSupId.Tables[0].Rows[0]["SupplierName"].ToString();

                            string strcmd1 = fun.select("CountryName", "tblcountry", "CId='" + DSSupId.Tables[0].Rows[0]["RegdCountry"] + "'");
                            SqlCommand Cmd1 = new SqlCommand(strcmd1, con);
                            SqlDataAdapter DA1 = new SqlDataAdapter(Cmd1);
                            DataSet ds1 = new DataSet();
                            DA1.Fill(ds1, "tblCountry");
                            string strcmd2 = fun.select("StateName", "tblState", "SId='" + DSSupId.Tables[0].Rows[0]["RegdState"] + "'");
                            SqlCommand Cmd2 = new SqlCommand(strcmd2, con);
                            SqlDataAdapter DA2 = new SqlDataAdapter(Cmd2);
                            DataSet ds2 = new DataSet();
                            DA2.Fill(ds2, "tblState");
                            string strcmd3 = fun.select("CityName", "tblCity", "CityId='" + DSSupId.Tables[0].Rows[0]["RegdCity"] + "'");
                            SqlCommand Cmd3 = new SqlCommand(strcmd3, con);
                            SqlDataAdapter DA3 = new SqlDataAdapter(Cmd3);
                            DataSet ds3 = new DataSet();
                            DA3.Fill(ds3, "tblcity");

                            address = DSSupId.Tables[0].Rows[0]["RegdAddress"].ToString() + ",\n" + ds3.Tables[0].Rows[0]["CityName"].ToString() + "," + ds2.Tables[0].Rows[0]["StateName"].ToString() + ",\n" + ds1.Tables[0].Rows[0]["CountryName"].ToString() + ".\n" + DSSupId.Tables[0].Rows[0]["RegdPinNo"].ToString() + "." + "\n";

                            PANNo = DSSupId.Tables[0].Rows[0]["PanNo"].ToString();
                        }


                        string sqlpay = fun.select("Terms", "tblPayment_Master", "Id='" + DSpo.Tables[0].Rows[0]["PaymentTerms"].ToString() + "'");
                        SqlCommand cmdpay = new SqlCommand(sqlpay, con);
                        SqlDataAdapter dapay = new SqlDataAdapter(cmdpay);
                        DataSet DSpay = new DataSet();
                        dapay.Fill(DSpay);

                        Payterms = DSpay.Tables[0].Rows[0]["Terms"].ToString();
                        dr[25] = DSSql.Tables[0].Rows[i]["CompId"].ToString();
                        dr[26] = DSSql.Tables[0].Rows[i]["SupplierId"].ToString();
                        dr[27] = DSSql.Tables[0].Rows[i]["OtherCharges"].ToString();
                        dr[28] = DSSql.Tables[0].Rows[i]["OtherChaDesc"].ToString();
                        dr[29] = DSSql.Tables[0].Rows[i]["Narration"].ToString();

                        double DebitAmt = Convert.ToDouble(DSSql.Tables[0].Rows[i]["DebitAmt"]);
                        dr[30] = DebitAmt;
                        dr[31] = Convert.ToInt32(DSSql.Tables[0].Rows[i]["DiscountType"]);

                        double CalBasicAmt = 0;

                        switch (Convert.ToInt32(DSSql.Tables[0].Rows[i]["DiscountType"]))
                        {
                            case 0:
                                CalBasicAmt = Amt - DebitAmt;
                                break;
                            case 1:
                                CalBasicAmt = Amt - (Amt * DebitAmt / 100);
                                break;
                            case 2:
                                CalBasicAmt = Amt;
                                break;
                        }

                        dr[52] = CalBasicAmt;

                        int t = 0;
                        t = Convert.ToInt32(DSSql.Tables[0].Rows[i]["DiscountType"]);

                        if (t == 0)
                        {
                            Type = "In Amt";
                        }
                        else if (t == 1)
                        {
                            Type = "In Percent";
                        }

                        dr[32] = Convert.ToDouble(decimal.Parse((DSSql.Tables[0].Rows[i]["Discount"]).ToString()).ToString("N2"));
                        dr[33] = (DSSql.Tables[0].Rows[i]["BillNo"].ToString());
                        dr[34] = fun.FromDateDMY(DSSql.Tables[0].Rows[i]["BillDate"].ToString());
                        dr[35] = (DSSql.Tables[0].Rows[i]["CENVATEntryNo"].ToString());
                        dr[36] = fun.FromDateDMY(DSSql.Tables[0].Rows[i]["CENVATEntryDate"].ToString());
                        dr[37] = AccHead;
                        dr[38] = wodept;
                        dr[39] = Convert.ToDouble(decimal.Parse((DSSql11.Tables[0].Rows[0]["Qty"]).ToString()).ToString("N3"));
                        dr[40] = Rate;
                        dr[41] = Discount;
                        dr[47] = Convert.ToDouble(DSSql.Tables[0].Rows[i]["DebitType"]);
                        dr[48] = Convert.ToDouble(DSSql.Tables[0].Rows[i]["DebitValue"]);

                        if (DSSql.Tables[0].Rows[i]["BCDOpt"].ToString() == "1")
                        {
                            dr[53] = "Amt";
                        }
                        else if (DSSql.Tables[0].Rows[i]["BCDOpt"].ToString() == "2")
                        {
                            dr[53] = "%";
                        }

                        dr[54]=Convert.ToDouble(DSSql.Tables[0].Rows[i]["BCD"]);
                        dr[55]=Convert.ToDouble(DSSql.Tables[0].Rows[i]["BCDValue"]);
                        dr[56]=Convert.ToDouble(DSSql.Tables[0].Rows[i]["ValueForCVD"]);
                        dr[57]=Convert.ToDouble(DSSql.Tables[0].Rows[i]["ValueForEdCessCD"]);                        
                       
                        if (DSSql.Tables[0].Rows[i]["EdCessOnCDOpt"].ToString() == "1")
                        {
                            dr[58] = "Amt";
                        }
                        else if (DSSql.Tables[0].Rows[i]["EdCessOnCDOpt"].ToString() == "2")
                        {
                            dr[58] = "%";
                        }

                        dr[59]=Convert.ToDouble(DSSql.Tables[0].Rows[i]["EdCessOnCD"]);
                        dr[60]=Convert.ToDouble(DSSql.Tables[0].Rows[i]["EdCessOnCDValue"]);
                        
                        if (DSSql.Tables[0].Rows[i]["SHEDCessOpt"].ToString() == "1")
                        {
                            dr[61] = "Amt";
                        }
                        else if (DSSql.Tables[0].Rows[i]["SHEDCessOpt"].ToString() == "2")
                        {
                            dr[61] = "%";
                        }

                        dr[62]=Convert.ToDouble(DSSql.Tables[0].Rows[i]["SHEDCess"]);
                        dr[63]=Convert.ToDouble(DSSql.Tables[0].Rows[i]["SHEDCessValue"]);
                        dr[64]=Convert.ToDouble(DSSql.Tables[0].Rows[i]["TotDuty"]);
                        dr[65]=Convert.ToDouble(DSSql.Tables[0].Rows[i]["TotDutyEDSHED"]);
                        dr[66]=Convert.ToDouble(DSSql.Tables[0].Rows[i]["Insurance"]);
                        dr[67] = Convert.ToDouble(DSSql.Tables[0].Rows[i]["ValueWithDuty"]);

                        //TDS Calculation

                        string SqlTDSCode = fun.select("*", "tblACC_TDSCode_Master","Id='"+ Convert.ToInt32(DSSql.Tables[0].Rows[i]["TDSCode"])+"'");
                        SqlCommand cmdTDSCode = new SqlCommand(SqlTDSCode, con);
                        SqlDataReader DSSqlTDSCode = cmdTDSCode.ExecuteReader();
                        DSSqlTDSCode.Read();
                       
                        double TDSPaymentRange=0;

                        if(DSSqlTDSCode.HasRows==true)
                        { 
                            dr[68] = DSSqlTDSCode["SectionNo"].ToString();

                            var regexItem = new Regex("^[a-zA-Z0-9 ]*$");

                            if (regexItem.IsMatch(PANNo))
                            {
                                //dr[69] = Convert.ToInt32(DSSqlTDSCode["PayToIndividual"].ToString());
                               dr[69] = Convert.ToInt32(DSSqlTDSCode["Others"].ToString());
                            }
                            else
                            {
                                dr[69] = Convert.ToInt32(DSSqlTDSCode["WithOutPAN"].ToString());
                            }

                            TDSPaymentRange= Convert.ToDouble(DSSqlTDSCode["PaymentRange"].ToString());
                        }
                        
                        dr[70] =TDSPaymentRange;
                        dr[71] = Convert.ToInt32(DSSql.Tables[0].Rows[i]["TDSCode"]);
                        dr[72] = fun.Check_TDSAmt(CompId, FinYearId, SupId, Convert.ToInt32(DSSql.Tables[0].Rows[i]["TDSCode"]));                        
                    }
                    
                    dt.Rows.Add(dr);
                    dt.AcceptChanges();
                }

                //Summary

                dt2.Columns.Add(new System.Data.DataColumn("Basic", typeof(double)));//0
                dt2.Columns.Add(new System.Data.DataColumn("PF", typeof(string)));//1
                dt2.Columns.Add(new System.Data.DataColumn("PFAmt", typeof(double)));//2
                dt2.Columns.Add(new System.Data.DataColumn("ExSerTax", typeof(string)));//3
                dt2.Columns.Add(new System.Data.DataColumn("ExSerAmt", typeof(double)));//4
                dt2.Columns.Add(new System.Data.DataColumn("EDU", typeof(double)));//5
                dt2.Columns.Add(new System.Data.DataColumn("SHE", typeof(double)));//6
                dt2.Columns.Add(new System.Data.DataColumn("VATCST", typeof(string)));//7
                dt2.Columns.Add(new System.Data.DataColumn("VATCSTAmt", typeof(double)));//8
                dt2.Columns.Add(new System.Data.DataColumn("Freight", typeof(double)));//9
                dt2.Columns.Add(new System.Data.DataColumn("Total", typeof(double)));//10

                DataRow Dr2;

                var data = from p in dt.AsEnumerable()                           
                           
                           group p by new
                           {
                               y = p.Field<int>("PFid"),
                               x = p.Field<int>("ExciseId"),
                               z = p.Field<int>("VATCSTid")
                           } into grp
                           let row1 = grp.First()
                           select new
                           {
                               PF = row1.Field<string>("PFTerm"),
                               ExSerTax = row1.Field<string>("ExciseTerm"),
                               VATCST = row1.Field<string>("VATTerm"),
                               Basic = grp.Sum(r => r.Field<double>("Amt")),
                               PFAmt = grp.Sum(r => r.Field<double>("PFAmt")),
                               ExSerBasic = grp.Sum(r => r.Field<double>("ExStBasic")),
                               EDU = grp.Sum(r => r.Field<double>("ExStEducess")),
                               SHE = grp.Sum(r => r.Field<double>("ExStShecess")),
                               VAT = grp.Sum(r => r.Field<double>("VAT")),
                               CST = grp.Sum(r => r.Field<double>("CST")),
                               Freight = grp.Sum(r => r.Field<double>("Freight"))
                           };

                foreach (var d in data)
                {
                    Dr2 = dt2.NewRow();                    
                    Dr2[0] = d.Basic;
                    Dr2[1] = d.PF;
                    Dr2[2] = d.PFAmt;
                    Dr2[3] = d.ExSerTax;
                    Dr2[4] = d.ExSerBasic;
                    Dr2[5] = d.EDU;
                    Dr2[6] = d.SHE;
                    Dr2[7] = d.VATCST;
                    Dr2[8] = d.VAT + d.CST;
                    Dr2[9] = d.Freight;
                    Dr2[10] = d.Basic + d.PFAmt + d.ExSerBasic + d.EDU + d.SHE + d.VAT + d.CST + d.Freight;

                    dt2.Rows.Add(Dr2);
                    dt2.AcceptChanges();
                }

                BillBook.Tables.Add(dt);
                BillBook.Tables.Add(dt2);
                BillBook.AcceptChanges();

                DataSet Xads = new BillBooking();
                Xads.Tables[0].Merge(BillBook.Tables[0]);
                Xads.Tables[1].Merge(BillBook.Tables[1]);
                Xads.AcceptChanges();

                cryRpt = new ReportDocument();
                cryRpt.Load(Server.MapPath("~/Module/Accounts/Reports/BillBooking.rpt"));
                cryRpt.SetDataSource(Xads);
                cryRpt.SetParameterValue("address", address);
                cryRpt.SetParameterValue("Type", Type);
                cryRpt.SetParameterValue("Terms", Payterms);

                string PreparedBy = string.Empty;
                string PreparedDate = string.Empty;
                string AuthorizeDate = string.Empty;

                string StrPre = fun.select("Title+' '+EmployeeName As Name", "tblHR_OfficeStaff", "EmpId='" + DSSql.Tables[0].Rows[0]["SessionId"].ToString() + "' AND CompId='" + CompId + "'");

                SqlCommand cmdPre = new SqlCommand(StrPre, con);
                SqlDataAdapter daPre = new SqlDataAdapter(cmdPre);
                DataSet DSPre = new DataSet();
                daPre.Fill(DSPre);

                if (DSPre.Tables[0].Rows.Count > 0)
                {
                    PreparedBy = DSPre.Tables[0].Rows[0]["Name"].ToString();
                    PreparedDate = fun.FromDateDMY(DSSql.Tables[0].Rows[0]["SysDate"].ToString());
                }

                string AuthorizeBy = string.Empty;
                string StrAuth = fun.select("Title+' '+EmployeeName As Name", "tblHR_OfficeStaff", "EmpId='" + DSSql.Tables[0].Rows[0]["AuthorizeBy"].ToString() + "' AND CompId='" + CompId + "'");

                SqlCommand cmdAuth = new SqlCommand(StrAuth, con);
                SqlDataAdapter daAuth = new SqlDataAdapter(cmdAuth);
                DataSet DSAuth = new DataSet();
                daAuth.Fill(DSAuth);

                if (DSAuth.Tables[0].Rows.Count > 0)
                {
                    AuthorizeBy = DSAuth.Tables[0].Rows[0]["Name"].ToString();
                    AuthorizeDate = fun.FromDateDMY(DSSql.Tables[0].Rows[0]["AuthorizeDate"].ToString());
                }

                cryRpt.SetParameterValue("PreparedBy", PreparedBy);
                cryRpt.SetParameterValue("PreparedDate", PreparedDate);
                cryRpt.SetParameterValue("AuthorizeBy", AuthorizeBy);
                cryRpt.SetParameterValue("AuthorizeDate", AuthorizeDate);

                string CompAdd = fun.CompAdd(CompId);
                cryRpt.SetParameterValue("CompAdd", CompAdd);
                CrystalReportViewer1.ReportSource = cryRpt;
                Session[sessKey] = cryRpt;
            }
            else
            {
                ReportDocument doc = (ReportDocument)Session[sessKey];
                CrystalReportViewer1.ReportSource = doc;
            }
        }
        catch (Exception ex)
        {

        }
        finally
        {
            con.Close();
            con.Dispose();
        }

    }
    
    protected void Page_Load(object sender, EventArgs e)
    {
        pageFlag = Convert.ToInt32(Request.QueryString["f"]);

        if (!string.IsNullOrEmpty(Request.QueryString["Key"]))
        {
            key = Request.QueryString["Key"].ToString();
        }

        if (!string.IsNullOrEmpty(Request.QueryString["SupId"]))
        {
            SupId = Request.QueryString["SupId"].ToString();
        }
        if (!string.IsNullOrEmpty(Request.QueryString["lnkFor"]))
        {
            lnkFor = Request.QueryString["lnkFor"].ToString();
        }

        cryRpt = new ReportDocument();
    }
    
    protected void btnCancel_Click(object sender, EventArgs e)
    {
        switch (pageFlag)
        {
            case 1:
                Response.Redirect("BillBooking_Print.aspx?ModId=11&SubModId=62");
                break;
            case 2:
                Response.Redirect("BillBooking_Authorize.aspx?ModId=11&SubModId=62");
                break;
            case 3:
               
                Response.Redirect("CreditorsDebitors_InDetailList.aspx?SupId="+SupId+"&ModId=11&SubModId=135&Key=" + key + "");
                break;
            case 4:
                Response.Redirect("SundryCreditors_InDetailList.aspx?SupId=" + SupId + "&ModId=11&SubModId=135&Key=" + key + "&lnkFor=" + lnkFor + "");
                break;
        }
    }
    
    protected void Page_UnLoad(object sender, EventArgs e)
    {
        this.CrystalReportViewer1.Dispose();
        this.CrystalReportViewer1 = null;
        cryRpt.Close();
        cryRpt.Dispose();
        GC.Collect();
    }



}