{% extends "core/base.html" %}
{% load static %}

{% block title %}Material Return Note [MRN] - New{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Material Return Note [MRN] - New</h1>
            </div>
            <div class="flex space-x-2">
                <a href="{% url 'inventory:mrn_list' %}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm">
                    Back to List
                </a>
            </div>
        </div>
    </div>

    <!-- Tab Navigation -->
    <div class="bg-white shadow rounded-lg mb-6" x-data="{ activeTab: 'item-master', selectedItemsCount: 0 }">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6">
                <button @click="activeTab = 'item-master'" 
                        class="py-3 px-1 border-b-2 font-medium text-sm"
                        :class="activeTab === 'item-master' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                        id="item-master-tab">
                    Item Master
                </button>
                <button @click="activeTab = 'selected-items'" 
                        class="py-3 px-1 border-b-2 font-medium text-sm relative"
                        :class="activeTab === 'selected-items' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                        id="selected-items-tab">
                    Selected Items
                    <span x-show="selectedItemsCount > 0" 
                          x-text="'(' + selectedItemsCount + ')'"
                          class="ml-1 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    </span>
                </button>
            </nav>
        </div>

        <!-- Item Master Tab -->
        <div x-show="activeTab === 'item-master'" id="item-master-content" class="p-6">
            <div class="mb-4">
                <div class="flex items-center space-x-4">
                    <div class="flex-1">
                        <select name="search_field" 
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                            <option value="item_code">Select</option>
                            <option value="item_name">Item Name</option>
                            <option value="category">Category</option>
                        </select>
                    </div>
                    <div>
                        <button type="button" 
                                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded text-sm font-medium">
                            Search
                        </button>
                    </div>
                </div>
            </div>

            <!-- Sample Item Master Table -->
            <div class="overflow-x-auto">
                <table class="w-full table-auto divide-y divide-gray-200 border border-gray-300">
                    <thead class="bg-gray-100">
                        <tr>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                                Item Code
                            </th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                                Description
                            </th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                                UOM
                            </th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                                Business Group
                            </th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for item in sample_items %}
                        <tr class="hover:bg-gray-50 border-b border-gray-200">
                            <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">{{ item.item_code }}</td>
                            <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">{{ item.description }}</td>
                            <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">{{ item.uom }}</td>
                            <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">{{ item.business_group }}</td>
                            <td class="px-4 py-2 text-sm">
                                <button type="button" 
                                        class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium transition-all duration-150 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                                        onclick="addToSelectedItems('{{ item.item_code }}', '{{ item.description }}', '{{ item.uom }}', '{{ item.business_group }}'); this.style.transform='scale(0.95)'; setTimeout(() => this.style.transform='scale(1)', 100);">
                                    Add
                                </button>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5" class="px-4 py-8 text-center text-gray-500">
                                No items available. Please check item master data.
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Selected Items Tab -->
        <div x-show="activeTab === 'selected-items'" id="selected-items-content" class="p-6">
            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <div class="overflow-x-auto">
                    <table class="w-full table-auto divide-y divide-gray-200 border border-gray-300" id="selected-items-table">
                        <thead class="bg-gray-100">
                            <tr>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                                    Item Code
                                </th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                                    Description
                                </th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                                    UOM
                                </th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                                    BG Group/WONo
                                </th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                                    Work Order No
                                </th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                                    Return Qty
                                </th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                                    Remarks
                                </th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-700">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200" id="selected-items-body">
                            <!-- Items will be added dynamically via JavaScript -->
                        </tbody>
                    </table>
                </div>

                <!-- MRN Header Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 pt-6 border-t border-gray-200">
                    <div>
                        <label for="id_sysdate" class="block text-sm font-medium text-gray-700">Date</label>
                        {{ form.sysdate }}
                    </div>
                    <div>
                        <label for="id_systime" class="block text-sm font-medium text-gray-700">Time</label>
                        {{ form.systime }}
                    </div>
                    <div>
                        <label for="id_sessionid" class="block text-sm font-medium text-gray-700">Session ID</label>
                        {{ form.sessionid }}
                    </div>
                    <div>
                        <label for="id_mrn_no" class="block text-sm font-medium text-gray-700">MRN Number</label>
                        {{ form.mrn_no }}
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-4 pt-4 border-t border-gray-200">
                    <a href="{% url 'inventory:mrn_list' %}" 
                       class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm">
                        Create MRN
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
<script>
// Track selected items count for unique naming
let selectedItemsCount = 0;

// Add item to selected items
function addToSelectedItems(itemCode, description, uom, bgGroup) {
    // Check if item already exists
    const existingRows = document.querySelectorAll('#selected-items-body tr');
    for (let existingRow of existingRows) {
        const existingItemCode = existingRow.querySelector('td:first-child').textContent.trim();
        if (existingItemCode === itemCode) {
            showNotification('Item already added!', 'warning');
            return;
        }
    }
    
    const tbody = document.getElementById('selected-items-body');
    
    const row = document.createElement('tr');
    row.className = 'hover:bg-gray-50 border-b border-gray-200 animate-pulse';
    row.innerHTML = `
        <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">
            ${itemCode}
            <input type="hidden" name="item_code_${selectedItemsCount}" value="${itemCode}">
        </td>
        <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">${description}</td>
        <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">${uom}</td>
        <td class="px-4 py-2 border-r border-gray-300">
            <select name="bg_group_${selectedItemsCount}" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                <option value="">Select/BG Group/WONo</option>
                <option value="BG Group">BG Group</option>
                <option value="WONo">WONo</option>
            </select>
        </td>
        <td class="px-4 py-2 border-r border-gray-300">
            <input type="text" name="wo_no_${selectedItemsCount}" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
        </td>
        <td class="px-4 py-2 border-r border-gray-300">
            <input type="number" name="return_qty_${selectedItemsCount}" step="0.001" min="0" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
        </td>
        <td class="px-4 py-2 border-r border-gray-300">
            <input type="text" name="remarks_${selectedItemsCount}" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
        </td>
        <td class="px-4 py-2 text-sm">
            <button type="button" class="text-red-600 hover:text-red-900 text-sm" onclick="removeSelectedItem(this)">Remove</button>
        </td>
    `;
    
    tbody.appendChild(row);
    selectedItemsCount++;
    
    // Remove animation after a short delay
    setTimeout(() => {
        row.classList.remove('animate-pulse');
        row.classList.add('bg-green-50');
        setTimeout(() => {
            row.classList.remove('bg-green-50');
        }, 2000);
    }, 500);
    
    // Update Alpine.js reactive data
    const alpineEl = document.querySelector('[x-data]');
    if (alpineEl && alpineEl._x_dataStack) {
        alpineEl._x_dataStack[0].selectedItemsCount = selectedItemsCount;
        alpineEl._x_dataStack[0].activeTab = 'selected-items';
    }
    
    // Show success notification
    showNotification(`Item ${itemCode} added successfully!`, 'success');
}

// Remove item from selected items
function removeSelectedItem(button) {
    const row = button.closest('tr');
    const itemCode = row.querySelector('td:first-child').textContent.trim();
    
    // Add fade out animation
    row.style.transition = 'opacity 0.3s ease-out';
    row.style.opacity = '0';
    
    setTimeout(() => {
        row.remove();
        selectedItemsCount--;
        
        // Update Alpine.js reactive data
        const alpineEl = document.querySelector('[x-data]');
        if (alpineEl && alpineEl._x_dataStack) {
            alpineEl._x_dataStack[0].selectedItemsCount = document.querySelectorAll('#selected-items-body tr').length;
        }
        
        // Show removal notification
        showNotification(`Item ${itemCode} removed successfully!`, 'info');
    }, 300);
}

// Show notification function
function showNotification(message, type = 'success') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification-toast');
    existingNotifications.forEach(notif => notif.remove());
    
    // Create notification
    const notification = document.createElement('div');
    notification.className = `notification-toast fixed top-4 right-4 z-50 max-w-sm w-full shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 transform transition-all duration-300 ease-in-out`;
    
    // Initial position (off-screen)
    notification.style.transform = 'translateX(100%)';
    notification.style.opacity = '0';
    
    let bgColor, textColor, icon;
    switch(type) {
        case 'success':
            bgColor = 'bg-green-50';
            textColor = 'text-green-800';
            icon = `<svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>`;
            break;
        case 'warning':
            bgColor = 'bg-yellow-50';
            textColor = 'text-yellow-800';
            icon = `<svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>`;
            break;
        case 'info':
            bgColor = 'bg-blue-50';
            textColor = 'text-blue-800';
            icon = `<svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>`;
            break;
    }
    
    notification.innerHTML = `
        <div class="${bgColor} p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    ${icon}
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium ${textColor}">
                        ${message}
                    </p>
                </div>
                <div class="ml-auto pl-3">
                    <div class="-mx-1.5 -my-1.5">
                        <button type="button" class="${textColor} hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-green-50 focus:ring-green-600 inline-flex p-1.5 rounded-md" onclick="this.closest('.notification-toast').remove()">
                            <span class="sr-only">Dismiss</span>
                            <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
        notification.style.opacity = '1';
    }, 100);
    
    // Auto remove after 4 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        notification.style.opacity = '0';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 4000);
}

// Form initialization and validation
document.addEventListener('DOMContentLoaded', function() {
    // Auto-populate fields are handled by Django form initial values
    console.log('MRN form initialized');
    
    // Add smooth transitions for tab switching
    const tabButtons = document.querySelectorAll('[x-data] button');
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Add loading state for smoother UX
            this.style.opacity = '0.7';
            setTimeout(() => {
                this.style.opacity = '1';
            }, 150);
        });
    });
});
</script>
{% endblock %}