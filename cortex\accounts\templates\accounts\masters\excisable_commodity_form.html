{% extends 'core/base.html' %}
{% load static %}

{% block title %}
{% if object %}Edit Excisable Commodity{% else %}Add New Excisable Commodity{% endif %}
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header Section -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">
                        {% if object %}Edit Excisable Commodity{% else %}Add New Excisable Commodity{% endif %}
                    </h1>
                    <p class="text-gray-600 mt-1">
                        {% if object %}Update excisable commodity information{% else %}Create a new excisable commodity entry{% endif %}
                    </p>
                </div>
                <a href="{% url 'accounts:excisable_commodity_list' %}" 
                   class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to List
                </a>
            </div>
        </div>
    </div>

    <!-- Form Section -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Commodity Information</h2>
        </div>
        
        <form method="post" class="p-6">
            {% csrf_token %}
            
            {% if form.non_field_errors %}
            <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                        <div class="mt-2 text-sm text-red-700">
                            {{ form.non_field_errors }}
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <div class="grid grid-cols-1 gap-6">
                <!-- Terms Field -->
                <div>
                    <label for="{{ form.terms.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ form.terms.label }}
                        <span class="text-red-500">*</span>
                    </label>
                    {{ form.terms }}
                    {% if form.terms.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {{ form.terms.errors }}
                    </div>
                    {% endif %}
                    <p class="mt-1 text-sm text-gray-500">
                        Enter a detailed description of the excisable commodity for tax classification purposes.
                    </p>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 mt-6">
                <a href="{% url 'accounts:excisable_commodity_list' %}" 
                   class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Cancel
                </a>
                <button type="submit" 
                        class="bg-blue-600 border border-transparent rounded-md shadow-sm py-2 px-4 inline-flex justify-center text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    {% if object %}Update Commodity{% else %}Create Commodity{% endif %}
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus the first form field
    const firstInput = document.querySelector('input[type="text"]');
    if (firstInput) {
        firstInput.focus();
    }
});
</script>
{% endblock %}