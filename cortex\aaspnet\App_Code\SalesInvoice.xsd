﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="SalesInvoice" targetNamespace="http://tempuri.org/SalesInvoice.xsd" xmlns:mstns="http://tempuri.org/SalesInvoice.xsd" xmlns="http://tempuri.org/SalesInvoice.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="SalesInvoice" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="SalesInvoice" msprop:Generator_DataSetName="SalesInvoice">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" msprop:Generator_UserColumnName="Id" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInTable="IdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="SysDate" msprop:Generator_UserColumnName="SysDate" msprop:Generator_ColumnPropNameInRow="SysDate" msprop:Generator_ColumnVarNameInTable="columnSysDate" msprop:Generator_ColumnPropNameInTable="SysDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="InvoiceNo" msprop:Generator_UserColumnName="InvoiceNo" msprop:Generator_ColumnPropNameInRow="InvoiceNo" msprop:Generator_ColumnVarNameInTable="columnInvoiceNo" msprop:Generator_ColumnPropNameInTable="InvoiceNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PONo" msprop:Generator_UserColumnName="PONo" msprop:Generator_ColumnPropNameInRow="PONo" msprop:Generator_ColumnVarNameInTable="columnPONo" msprop:Generator_ColumnPropNameInTable="PONoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="WONo" msprop:Generator_UserColumnName="WONo" msprop:Generator_ColumnPropNameInRow="WONo" msprop:Generator_ColumnVarNameInTable="columnWONo" msprop:Generator_ColumnPropNameInTable="WONoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="InvoiceMode" msprop:Generator_UserColumnName="InvoiceMode" msprop:Generator_ColumnPropNameInRow="InvoiceMode" msprop:Generator_ColumnVarNameInTable="columnInvoiceMode" msprop:Generator_ColumnPropNameInTable="InvoiceModeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="DateOfIssueInvoice" msprop:Generator_UserColumnName="DateOfIssueInvoice" msprop:Generator_ColumnPropNameInRow="DateOfIssueInvoice" msprop:Generator_ColumnVarNameInTable="columnDateOfIssueInvoice" msprop:Generator_ColumnPropNameInTable="DateOfIssueInvoiceColumn" type="xs:string" minOccurs="0" />
              <xs:element name="TimeOfIssueInvoice" msprop:Generator_UserColumnName="TimeOfIssueInvoice" msprop:Generator_ColumnPropNameInRow="TimeOfIssueInvoice" msprop:Generator_ColumnVarNameInTable="columnTimeOfIssueInvoice" msprop:Generator_ColumnPropNameInTable="TimeOfIssueInvoiceColumn" type="xs:string" minOccurs="0" />
              <xs:element name="TimeOfRemoval" msprop:Generator_UserColumnName="TimeOfRemoval" msprop:Generator_ColumnPropNameInRow="TimeOfRemoval" msprop:Generator_ColumnVarNameInTable="columnTimeOfRemoval" msprop:Generator_ColumnPropNameInTable="TimeOfRemovalColumn" type="xs:string" minOccurs="0" />
              <xs:element name="NatureOfRemoval" msprop:Generator_UserColumnName="NatureOfRemoval" msprop:Generator_ColumnPropNameInRow="NatureOfRemoval" msprop:Generator_ColumnVarNameInTable="columnNatureOfRemoval" msprop:Generator_ColumnPropNameInTable="NatureOfRemovalColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Commodity" msprop:Generator_UserColumnName="Commodity" msprop:Generator_ColumnPropNameInRow="Commodity" msprop:Generator_ColumnVarNameInTable="columnCommodity" msprop:Generator_ColumnPropNameInTable="CommodityColumn" type="xs:string" minOccurs="0" />
              <xs:element name="TariffHeading" msprop:Generator_UserColumnName="TariffHeading" msprop:Generator_ColumnPropNameInRow="TariffHeading" msprop:Generator_ColumnVarNameInTable="columnTariffHeading" msprop:Generator_ColumnPropNameInTable="TariffHeadingColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ModeOfTransport" msprop:Generator_UserColumnName="ModeOfTransport" msprop:Generator_ColumnPropNameInRow="ModeOfTransport" msprop:Generator_ColumnVarNameInTable="columnModeOfTransport" msprop:Generator_ColumnPropNameInTable="ModeOfTransportColumn" type="xs:string" minOccurs="0" />
              <xs:element name="RRGCNo" msprop:Generator_UserColumnName="RRGCNo" msprop:Generator_ColumnPropNameInRow="RRGCNo" msprop:Generator_ColumnVarNameInTable="columnRRGCNo" msprop:Generator_ColumnPropNameInTable="RRGCNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="VehiRegNo" msprop:Generator_UserColumnName="VehiRegNo" msprop:Generator_ColumnPropNameInRow="VehiRegNo" msprop:Generator_ColumnVarNameInTable="columnVehiRegNo" msprop:Generator_ColumnPropNameInTable="VehiRegNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="DutyRate" msprop:Generator_UserColumnName="DutyRate" msprop:Generator_ColumnPropNameInRow="DutyRate" msprop:Generator_ColumnVarNameInTable="columnDutyRate" msprop:Generator_ColumnPropNameInTable="DutyRateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CustomerCode" msprop:Generator_UserColumnName="CustomerCode" msprop:Generator_ColumnPropNameInRow="CustomerCode" msprop:Generator_ColumnVarNameInTable="columnCustomerCode" msprop:Generator_ColumnPropNameInTable="CustomerCodeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CustomerCategory" msprop:Generator_UserColumnName="CustomerCategory" msprop:Generator_ColumnPropNameInRow="CustomerCategory" msprop:Generator_ColumnVarNameInTable="columnCustomerCategory" msprop:Generator_ColumnPropNameInTable="CustomerCategoryColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Buyer_name" msprop:Generator_UserColumnName="Buyer_name" msprop:Generator_ColumnPropNameInRow="Buyer_name" msprop:Generator_ColumnVarNameInTable="columnBuyer_name" msprop:Generator_ColumnPropNameInTable="Buyer_nameColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Buyer_cotper" msprop:Generator_UserColumnName="Buyer_cotper" msprop:Generator_ColumnPropNameInRow="Buyer_cotper" msprop:Generator_ColumnVarNameInTable="columnBuyer_cotper" msprop:Generator_ColumnPropNameInTable="Buyer_cotperColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Buyer_ph" msprop:Generator_UserColumnName="Buyer_ph" msprop:Generator_ColumnPropNameInRow="Buyer_ph" msprop:Generator_ColumnVarNameInTable="columnBuyer_ph" msprop:Generator_ColumnPropNameInTable="Buyer_phColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Buyer_email" msprop:Generator_UserColumnName="Buyer_email" msprop:Generator_ColumnPropNameInRow="Buyer_email" msprop:Generator_ColumnVarNameInTable="columnBuyer_email" msprop:Generator_ColumnPropNameInTable="Buyer_emailColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Buyer_ecc" msprop:Generator_UserColumnName="Buyer_ecc" msprop:Generator_ColumnPropNameInRow="Buyer_ecc" msprop:Generator_ColumnVarNameInTable="columnBuyer_ecc" msprop:Generator_ColumnPropNameInTable="Buyer_eccColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Buyer_tin" msprop:Generator_UserColumnName="Buyer_tin" msprop:Generator_ColumnPropNameInRow="Buyer_tin" msprop:Generator_ColumnVarNameInTable="columnBuyer_tin" msprop:Generator_ColumnPropNameInTable="Buyer_tinColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Buyer_mob" msprop:Generator_UserColumnName="Buyer_mob" msprop:Generator_ColumnPropNameInRow="Buyer_mob" msprop:Generator_ColumnVarNameInTable="columnBuyer_mob" msprop:Generator_ColumnPropNameInTable="Buyer_mobColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Buyer_fax" msprop:Generator_UserColumnName="Buyer_fax" msprop:Generator_ColumnPropNameInRow="Buyer_fax" msprop:Generator_ColumnVarNameInTable="columnBuyer_fax" msprop:Generator_ColumnPropNameInTable="Buyer_faxColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Buyer_vat" msprop:Generator_UserColumnName="Buyer_vat" msprop:Generator_ColumnPropNameInRow="Buyer_vat" msprop:Generator_ColumnVarNameInTable="columnBuyer_vat" msprop:Generator_ColumnPropNameInTable="Buyer_vatColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Cong_name" msprop:Generator_UserColumnName="Cong_name" msprop:Generator_ColumnPropNameInRow="Cong_name" msprop:Generator_ColumnVarNameInTable="columnCong_name" msprop:Generator_ColumnPropNameInTable="Cong_nameColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Cong_cotper" msprop:Generator_UserColumnName="Cong_cotper" msprop:Generator_ColumnPropNameInRow="Cong_cotper" msprop:Generator_ColumnVarNameInTable="columnCong_cotper" msprop:Generator_ColumnPropNameInTable="Cong_cotperColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Cong_ph" msprop:Generator_UserColumnName="Cong_ph" msprop:Generator_ColumnPropNameInRow="Cong_ph" msprop:Generator_ColumnVarNameInTable="columnCong_ph" msprop:Generator_ColumnPropNameInTable="Cong_phColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Cong_email" msprop:Generator_UserColumnName="Cong_email" msprop:Generator_ColumnPropNameInRow="Cong_email" msprop:Generator_ColumnVarNameInTable="columnCong_email" msprop:Generator_ColumnPropNameInTable="Cong_emailColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Cong_ecc" msprop:Generator_UserColumnName="Cong_ecc" msprop:Generator_ColumnPropNameInRow="Cong_ecc" msprop:Generator_ColumnVarNameInTable="columnCong_ecc" msprop:Generator_ColumnPropNameInTable="Cong_eccColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Cong_tin" msprop:Generator_UserColumnName="Cong_tin" msprop:Generator_ColumnPropNameInRow="Cong_tin" msprop:Generator_ColumnVarNameInTable="columnCong_tin" msprop:Generator_ColumnPropNameInTable="Cong_tinColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Cong_mob" msprop:Generator_UserColumnName="Cong_mob" msprop:Generator_ColumnPropNameInRow="Cong_mob" msprop:Generator_ColumnVarNameInTable="columnCong_mob" msprop:Generator_ColumnPropNameInTable="Cong_mobColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Cong_fax" msprop:Generator_UserColumnName="Cong_fax" msprop:Generator_ColumnPropNameInRow="Cong_fax" msprop:Generator_ColumnVarNameInTable="columnCong_fax" msprop:Generator_ColumnPropNameInTable="Cong_faxColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Cong_vat" msprop:Generator_UserColumnName="Cong_vat" msprop:Generator_ColumnPropNameInRow="Cong_vat" msprop:Generator_ColumnVarNameInTable="columnCong_vat" msprop:Generator_ColumnPropNameInTable="Cong_vatColumn" type="xs:string" minOccurs="0" />
              <xs:element name="AddType" msprop:Generator_UserColumnName="AddType" msprop:Generator_ColumnPropNameInRow="AddType" msprop:Generator_ColumnVarNameInTable="columnAddType" msprop:Generator_ColumnPropNameInTable="AddTypeColumn" type="xs:int" minOccurs="0" />
              <xs:element name="AddAmt" msprop:Generator_UserColumnName="AddAmt" msprop:Generator_ColumnPropNameInRow="AddAmt" msprop:Generator_ColumnVarNameInTable="columnAddAmt" msprop:Generator_ColumnPropNameInTable="AddAmtColumn" type="xs:double" minOccurs="0" />
              <xs:element name="DeductionType" msprop:Generator_UserColumnName="DeductionType" msprop:Generator_ColumnPropNameInRow="DeductionType" msprop:Generator_ColumnVarNameInTable="columnDeductionType" msprop:Generator_ColumnPropNameInTable="DeductionTypeColumn" type="xs:int" minOccurs="0" />
              <xs:element name="Deduction" msprop:Generator_UserColumnName="Deduction" msprop:Generator_ColumnPropNameInRow="Deduction" msprop:Generator_ColumnVarNameInTable="columnDeduction" msprop:Generator_ColumnPropNameInTable="DeductionColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PFType" msprop:Generator_UserColumnName="PFType" msprop:Generator_ColumnPropNameInRow="PFType" msprop:Generator_ColumnVarNameInTable="columnPFType" msprop:Generator_ColumnPropNameInTable="PFTypeColumn" type="xs:int" minOccurs="0" />
              <xs:element name="PF" msprop:Generator_UserColumnName="PF" msprop:Generator_ColumnPropNameInRow="PF" msprop:Generator_ColumnVarNameInTable="columnPF" msprop:Generator_ColumnPropNameInTable="PFColumn" type="xs:double" minOccurs="0" />
              <xs:element name="CENVAT" msprop:Generator_UserColumnName="CENVAT" msprop:Generator_ColumnPropNameInRow="CENVAT" msprop:Generator_ColumnVarNameInTable="columnCENVAT" msprop:Generator_ColumnPropNameInTable="CENVATColumn" type="xs:int" minOccurs="0" />
              <xs:element name="SED" msprop:Generator_UserColumnName="SED" msprop:Generator_ColumnPropNameInRow="SED" msprop:Generator_ColumnVarNameInTable="columnSED" msprop:Generator_ColumnPropNameInTable="SEDColumn" type="xs:double" minOccurs="0" />
              <xs:element name="AED" msprop:Generator_UserColumnName="AED" msprop:Generator_ColumnPropNameInRow="AED" msprop:Generator_ColumnVarNameInTable="columnAED" msprop:Generator_ColumnPropNameInTable="AEDColumn" type="xs:double" minOccurs="0" />
              <xs:element name="VAT" msprop:Generator_UserColumnName="VAT" msprop:Generator_ColumnPropNameInRow="VAT" msprop:Generator_ColumnVarNameInTable="columnVAT" msprop:Generator_ColumnPropNameInTable="VATColumn" type="xs:int" minOccurs="0" />
              <xs:element name="SelectedCST" msprop:Generator_UserColumnName="SelectedCST" msprop:Generator_ColumnPropNameInRow="SelectedCST" msprop:Generator_ColumnVarNameInTable="columnSelectedCST" msprop:Generator_ColumnPropNameInTable="SelectedCSTColumn" type="xs:int" minOccurs="0" />
              <xs:element name="CST" msprop:Generator_UserColumnName="CST" msprop:Generator_ColumnPropNameInRow="CST" msprop:Generator_ColumnVarNameInTable="columnCST" msprop:Generator_ColumnPropNameInTable="CSTColumn" type="xs:double" minOccurs="0" />
              <xs:element name="FreightType" msprop:Generator_UserColumnName="FreightType" msprop:Generator_ColumnPropNameInRow="FreightType" msprop:Generator_ColumnVarNameInTable="columnFreightType" msprop:Generator_ColumnPropNameInTable="FreightTypeColumn" type="xs:int" minOccurs="0" />
              <xs:element name="Freight" msprop:Generator_UserColumnName="Freight" msprop:Generator_ColumnPropNameInRow="Freight" msprop:Generator_ColumnVarNameInTable="columnFreight" msprop:Generator_ColumnPropNameInTable="FreightColumn" type="xs:double" minOccurs="0" />
              <xs:element name="InsuranceType" msprop:Generator_UserColumnName="InsuranceType" msprop:Generator_ColumnPropNameInRow="InsuranceType" msprop:Generator_ColumnVarNameInTable="columnInsuranceType" msprop:Generator_ColumnPropNameInTable="InsuranceTypeColumn" type="xs:int" minOccurs="0" />
              <xs:element name="Insurance" msprop:Generator_UserColumnName="Insurance" msprop:Generator_ColumnPropNameInRow="Insurance" msprop:Generator_ColumnVarNameInTable="columnInsurance" msprop:Generator_ColumnPropNameInTable="InsuranceColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PODate" msprop:Generator_UserColumnName="PODate" msprop:Generator_ColumnVarNameInTable="columnPODate" msprop:Generator_ColumnPropNameInRow="PODate" msprop:Generator_ColumnPropNameInTable="PODateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="AEDType" msprop:Generator_UserColumnName="AEDType" msprop:Generator_ColumnVarNameInTable="columnAEDType" msprop:Generator_ColumnPropNameInRow="AEDType" msprop:Generator_ColumnPropNameInTable="AEDTypeColumn" type="xs:int" minOccurs="0" />
              <xs:element name="SEDType" msprop:Generator_UserColumnName="SEDType" msprop:Generator_ColumnVarNameInTable="columnSEDType" msprop:Generator_ColumnPropNameInRow="SEDType" msprop:Generator_ColumnPropNameInTable="SEDTypeColumn" type="xs:int" minOccurs="0" />
              <xs:element name="DateOfRemoval" msprop:Generator_UserColumnName="DateOfRemoval" msprop:Generator_ColumnPropNameInRow="DateOfRemoval" msprop:Generator_ColumnVarNameInTable="columnDateOfRemoval" msprop:Generator_ColumnPropNameInTable="DateOfRemovalColumn" type="xs:string" minOccurs="0" />
              <xs:element name="POId" msprop:Generator_UserColumnName="POId" msprop:Generator_ColumnVarNameInTable="columnPOId" msprop:Generator_ColumnPropNameInRow="POId" msprop:Generator_ColumnPropNameInTable="POIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="OtherAmt" msprop:Generator_UserColumnName="OtherAmt" msprop:Generator_ColumnPropNameInRow="OtherAmt" msprop:Generator_ColumnVarNameInTable="columnOtherAmt" msprop:Generator_ColumnPropNameInTable="OtherAmtColumn" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>