<!-- Product Row Template -->
<tr class="hover:bg-gray-50 transition-colors duration-150" id="product-row-{{ product.id }}">
    <!-- Serial Number -->
    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
        {% if page_obj %}
            {{ forloop.counter|add:page_obj.start_index|add:"-1" }}
        {% else %}
            {{ forloop.counter }}
        {% endif %}
    </td>
    
    <!-- Product Name -->
    <td class="px-6 py-4 whitespace-nowrap">
        <div class="flex items-center">
            <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                <i data-lucide="package" class="w-4 h-4 text-blue-600"></i>
            </div>
            <div>
                {% if product.name %}
                    <div class="text-sm font-medium text-gray-900">{{ product.name }}</div>
                    <div class="text-xs text-gray-500">ID: {{ product.id }}</div>
                {% else %}
                    <span class="text-sm text-gray-400">Unnamed Product</span>
                {% endif %}
            </div>
        </div>
    </td>
    
    <!-- Actions -->
    <td class="px-6 py-4 whitespace-nowrap text-center">
        <div class="flex items-center justify-center space-x-2">
            <!-- Edit Button -->
            <button type="button"
                    hx-get="{% url 'sales_distribution:product_edit_row' product.id %}"
                    hx-target="#product-row-{{ product.id }}"
                    hx-swap="outerHTML"
                    title="Edit Product"
                    class="inline-flex items-center p-1.5 border border-blue-300 rounded-lg text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors duration-200">
                <i data-lucide="edit-2" class="w-3 h-3"></i>
            </button>
            
            <!-- Delete Button -->
            <button type="button"
                    hx-delete="{% url 'sales_distribution:product_delete' product.id %}"
                    hx-target="#product-row-{{ product.id }}"
                    hx-swap="outerHTML"
                    hx-confirm="Are you sure you want to delete this product? This action cannot be undone."
                    title="Delete Product"
                    class="inline-flex items-center p-1.5 border border-red-300 rounded-lg text-red-600 bg-red-50 hover:bg-red-100 transition-colors duration-200">
                <i data-lucide="trash-2" class="w-3 h-3"></i>
            </button>
        </div>
    </td>
</tr>