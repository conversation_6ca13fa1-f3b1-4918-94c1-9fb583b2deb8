<!-- accounts/templates/accounts/transactions/service_tax_invoice_with_items_form.html -->
<!-- Enhanced Service Tax Invoice Form with Dynamic Service Grid and HTMX -->
<!-- Task Group 5: Sales & Service Tax Invoicing - Service Tax Invoice Templates with HTMX (Task 5.14) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{{ page_title }} - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-green-600 to-sap-green-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="briefcase" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">{{ page_title }}</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Professional service tax invoice with service-specific features</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:service_tax_invoice_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
                {% if form_action == "Update" and service_tax_invoice %}
                <a href="{% url 'accounts:service_tax_invoice_detail' service_tax_invoice.pk %}" 
                   class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="eye" class="w-4 h-4 inline mr-2"></i>
                    View Invoice
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6" x-data="serviceTaxInvoiceManager()">
    
    <!-- Service Tax Information -->
    <div class="mb-6 bg-sap-green-50 border border-sap-green-200 rounded-lg p-4">
        <div class="flex">
            <i data-lucide="info" class="w-5 h-5 text-sap-green-400 mr-3"></i>
            <div class="text-sm text-sap-green-800">
                <p class="font-medium mb-1">Service Tax Invoice</p>
                <p>This invoice is specifically for services rendered. Service tax calculation and compliance features are built-in for accuracy.</p>
            </div>
        </div>
    </div>

    <form method="post" class="space-y-8" @submit="submitServiceTaxInvoice">
        {% csrf_token %}
        
        <!-- Error Messages -->
        {% if form.non_field_errors %}
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <i data-lucide="alert-circle" class="w-5 h-5 text-red-400 mr-3"></i>
                <div>
                    <h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                    <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                        {% for error in form.non_field_errors %}
                        <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
            
            <!-- Main Service Invoice Form -->
            <div class="xl:col-span-2 space-y-6">
                
                <!-- Service Invoice Header -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                            <i data-lucide="briefcase" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                            Service Invoice Details
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Buyer Name -->
                            <div>
                                <label for="{{ form.buyer_name.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.buyer_name.label }}
                                    <span class="text-red-500">*</span>
                                </label>
                                {{ form.buyer_name|add_class:"block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                                {% if form.buyer_name.errors %}
                                <p class="text-sm text-red-600 mt-1">{{ form.buyer_name.errors.0 }}</p>
                                {% endif %}
                            </div>

                            <!-- Customer Code -->
                            <div>
                                <label for="{{ form.customer_code.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.customer_code.label }}
                                    <span class="text-red-500">*</span>
                                </label>
                                {{ form.customer_code|add_class:"block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            </div>

                            <!-- Service Period -->
                            <div>
                                <label class="block text-sm font-medium text-sap-gray-700 mb-2">Service Period</label>
                                <div class="grid grid-cols-2 gap-2">
                                    <input type="date" x-model="servicePeriod.from"
                                           class="block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                                    <input type="date" x-model="servicePeriod.to"
                                           class="block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                                </div>
                            </div>

                            <!-- PO Number -->
                            <div>
                                <label for="{{ form.po_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.po_no.label }}
                                </label>
                                {{ form.po_no|add_class:"block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            </div>

                            <!-- Work Order Number -->
                            <div>
                                <label for="{{ form.wo_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.wo_no.label }}
                                </label>
                                {{ form.wo_no|add_class:"block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            </div>
                        </div>

                        <!-- Buyer Address (full width) -->
                        <div class="mt-6">
                            <label for="{{ form.buyer_add.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                {{ form.buyer_add.label }}
                            </label>
                            {{ form.buyer_add|add_class:"block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.buyer_add.errors %}
                            <p class="text-sm text-red-600 mt-1">{{ form.buyer_add.errors.0 }}</p>
                            {% endif %}

                            <!-- Service Location -->
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-sap-gray-700 mb-2">Service Location</label>
                                <textarea x-model="serviceLocation" rows="2"
                                          class="block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500"
                                          placeholder="Location where services were rendered..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dynamic Service Items Grid -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                                <i data-lucide="list" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                                Services Rendered
                            </h3>
                            <div class="flex items-center space-x-2">
                                <button type="button" @click="addTimeBasedService" 
                                        class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                                    <i data-lucide="clock" class="w-4 h-4 inline mr-2"></i>
                                    Time-based
                                </button>
                                <button type="button" @click="addFixedService" 
                                        class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                                    Fixed Service
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="p-6">
                        <!-- Services Grid -->
                        <div class="space-y-4">
                            <!-- Dynamic Service Rows -->
                            <template x-for="(service, index) in serviceItems" :key="index">
                                <div class="bg-sap-gray-50 rounded-lg p-4 border">
                                    <div class="grid grid-cols-1 lg:grid-cols-12 gap-4 items-end">
                                        <!-- Service Code & Description -->
                                        <div class="lg:col-span-4">
                                            <label class="block text-xs font-medium text-sap-gray-700 mb-1">Service Details</label>
                                            <input type="text" 
                                                   x-model="service.service_code"
                                                   class="block w-full px-2 py-1.5 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-green-500 mb-2"
                                                   placeholder="Service Code">
                                            <textarea x-model="service.service_description" rows="2"
                                                      class="block w-full px-2 py-1.5 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-green-500"
                                                      placeholder="Detailed service description..."></textarea>
                                        </div>

                                        <!-- Service Type & UOM -->
                                        <div class="lg:col-span-2">
                                            <label class="block text-xs font-medium text-sap-gray-700 mb-1">Type & Unit</label>
                                            <select x-model="service.service_type" @change="updateServiceType(index)"
                                                    class="block w-full px-2 py-1.5 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-green-500 mb-2">
                                                <option value="fixed">Fixed Rate</option>
                                                <option value="hourly">Hourly</option>
                                                <option value="daily">Daily</option>
                                                <option value="monthly">Monthly</option>
                                            </select>
                                            <input type="text" x-model="service.uom"
                                                   class="block w-full px-2 py-1.5 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-green-500"
                                                   placeholder="UOM">
                                        </div>

                                        <!-- Quantity/Duration -->
                                        <div class="lg:col-span-2">
                                            <label class="block text-xs font-medium text-sap-gray-700 mb-1">Quantity/Duration</label>
                                            <input type="number" 
                                                   x-model.number="service.quantity"
                                                   @input="calculateServiceTotal(index)"
                                                   step="0.01" min="0"
                                                   class="block w-full px-2 py-1.5 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-green-500"
                                                   placeholder="0.00">
                                            <div class="text-xs text-sap-gray-500 mt-1" x-show="service.service_type === 'hourly'">
                                                Hours worked
                                            </div>
                                        </div>

                                        <!-- Rate -->
                                        <div class="lg:col-span-2">
                                            <label class="block text-xs font-medium text-sap-gray-700 mb-1">Rate</label>
                                            <input type="number" 
                                                   x-model.number="service.rate"
                                                   @input="calculateServiceTotal(index)"
                                                   step="0.01" min="0"
                                                   class="block w-full px-2 py-1.5 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-green-500"
                                                   placeholder="0.00">
                                            <div class="text-xs text-sap-green-600 font-medium mt-1">
                                                ₹<span x-text="(service.rate || 0).toFixed(2)"></span>/<span x-text="service.uom || 'unit'"></span>
                                            </div>
                                        </div>

                                        <!-- Tax % -->
                                        <div class="lg:col-span-1">
                                            <label class="block text-xs font-medium text-sap-gray-700 mb-1">Tax %</label>
                                            <input type="number" 
                                                   x-model.number="service.service_tax_percentage"
                                                   @input="calculateServiceTotal(index)"
                                                   step="0.01" min="0" max="100"
                                                   class="block w-full px-2 py-1.5 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-green-500"
                                                   placeholder="15">
                                        </div>

                                        <!-- Actions -->
                                        <div class="lg:col-span-1 flex items-center justify-end space-x-1">
                                            <button type="button" @click="duplicateService(index)"
                                                    class="text-sap-blue-600 hover:text-sap-blue-700 p-1" title="Duplicate">
                                                <i data-lucide="copy" class="w-4 h-4"></i>
                                            </button>
                                            <button type="button" @click="removeService(index)"
                                                    class="text-red-600 hover:text-red-700 p-1" title="Remove">
                                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Service Totals Row -->
                                    <div class="mt-3 pt-3 border-t border-sap-gray-200">
                                        <div class="grid grid-cols-3 gap-4 text-sm">
                                            <div>
                                                <span class="text-sap-gray-600">Service Amount:</span>
                                                <span class="font-medium ml-2" x-text="formatCurrency(service.amount)"></span>
                                            </div>
                                            <div>
                                                <span class="text-sap-gray-600">Service Tax:</span>
                                                <span class="font-medium ml-2" x-text="formatCurrency(service.service_tax_amount)"></span>
                                            </div>
                                            <div>
                                                <span class="text-sap-gray-600 font-medium">Service Total:</span>
                                                <span class="font-bold ml-2 text-sap-green-600" x-text="formatCurrency(service.line_total)"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                            
                            <!-- Empty State -->
                            <div x-show="serviceItems.length === 0" class="text-center py-8">
                                <i data-lucide="briefcase" class="w-12 h-12 text-sap-gray-400 mx-auto mb-4"></i>
                                <h3 class="text-sm font-medium text-sap-gray-900 mb-2">No services added</h3>
                                <p class="text-sm text-sap-gray-600 mb-4">Start adding services rendered to create the invoice.</p>
                                <div class="space-x-2">
                                    <button type="button" @click="addTimeBasedService"
                                            class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                        <i data-lucide="clock" class="w-4 h-4 inline mr-2"></i>
                                        Add Time-based Service
                                    </button>
                                    <button type="button" @click="addFixedService"
                                            class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                        <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                                        Add Fixed Service
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Service Invoice Summary -->
            <div class="space-y-6">
                
                <!-- Service Summary -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                            <i data-lucide="calculator" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                            Service Invoice Summary
                        </h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <!-- Service Amount -->
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-sap-gray-600">Service Amount:</span>
                            <span class="font-medium" x-text="formatCurrency(totals.service_amount)"></span>
                        </div>
                        
                        <!-- Total Service Tax -->
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-sap-gray-600">Total Service Tax:</span>
                            <span class="font-medium" x-text="formatCurrency(totals.total_service_tax)"></span>
                        </div>
                        
                        <!-- Total Hours (if applicable) -->
                        <div x-show="totals.total_hours > 0" class="flex justify-between items-center text-sm text-sap-blue-600">
                            <span>Total Hours:</span>
                            <span class="font-medium" x-text="totals.total_hours.toFixed(2) + ' hrs'"></span>
                        </div>
                        
                        <!-- Grand Total -->
                        <div class="border-t border-sap-gray-200 pt-4">
                            <div class="flex justify-between items-center bg-sap-green-50 rounded-lg p-3">
                                <span class="text-lg font-bold text-sap-gray-800">Grand Total:</span>
                                <span class="text-xl font-bold text-sap-green-600" x-text="formatCurrency(totals.grand_total)"></span>
                            </div>
                        </div>
                        
                        <!-- Service Tax Compliance -->
                        <div class="border-t border-sap-gray-200 pt-4">
                            <div class="bg-sap-blue-50 rounded-lg p-3">
                                <div class="text-xs font-medium text-sap-blue-800 mb-1">Service Tax Compliance</div>
                                <div class="text-sm text-sap-blue-700">
                                    <div>Tax Rate: <span class="font-medium" x-text="getAverageServiceTaxRate()"></span>%</div>
                                    <div>Tax Amount: <span class="font-medium" x-text="formatCurrency(totals.total_service_tax)"></span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Service Categories -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <h3 class="text-lg font-medium text-sap-gray-800">Service Categories</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            <div x-show="getServicesByType('hourly').length > 0">
                                <div class="text-sm font-medium text-sap-gray-700">Time-based Services</div>
                                <div class="text-xs text-sap-gray-600" x-text="getServicesByType('hourly').length + ' services'"></div>
                            </div>
                            <div x-show="getServicesByType('fixed').length > 0">
                                <div class="text-sm font-medium text-sap-gray-700">Fixed Services</div>
                                <div class="text-xs text-sap-gray-600" x-text="getServicesByType('fixed').length + ' services'"></div>
                            </div>
                            <div x-show="getServicesByType('monthly').length > 0">
                                <div class="text-sm font-medium text-sap-gray-700">Monthly Services</div>
                                <div class="text-xs text-sap-gray-600" x-text="getServicesByType('monthly').length + ' services'"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <h3 class="text-lg font-medium text-sap-gray-800">Quick Actions</h3>
                    </div>
                    <div class="p-6 space-y-3">
                        <button type="button" @click="loadServiceTemplates"
                                class="w-full bg-sap-blue-100 hover:bg-sap-blue-200 text-sap-blue-800 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="template" class="w-4 h-4 inline mr-2"></i>
                            Load Service Templates
                        </button>
                        <button type="button" @click="generateTimesheet"
                                :disabled="getServicesByType('hourly').length === 0"
                                class="w-full bg-sap-green-100 hover:bg-sap-green-200 text-sap-green-800 px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                            <i data-lucide="clock" class="w-4 h-4 inline mr-2"></i>
                            Generate Timesheet
                        </button>
                        <button type="button" @click="exportServiceReport"
                                :disabled="serviceItems.length === 0"
                                class="w-full bg-sap-gray-100 hover:bg-sap-gray-200 text-sap-gray-800 px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                            <i data-lucide="file-down" class="w-4 h-4 inline mr-2"></i>
                            Export Service Report
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
            <div class="flex items-center space-x-4">
                <a href="{% url 'accounts:service_tax_invoice_list' %}" 
                   class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-6 py-2 rounded-lg font-medium transition-colors">
                    Cancel
                </a>
                <button type="button" @click="saveDraft"
                        class="bg-sap-yellow-600 hover:bg-sap-yellow-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="save" class="w-4 h-4 inline mr-2"></i>
                    Save as Draft
                </button>
            </div>
            
            <div class="flex items-center space-x-3">
                <button type="button" @click="previewServiceInvoice"
                        :disabled="serviceItems.length === 0"
                        class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                    <i data-lucide="eye" class="w-4 h-4 inline mr-2"></i>
                    Preview
                </button>
                <button type="submit" 
                        class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                        :disabled="serviceItems.length === 0">
                    <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                    {{ form_action }} Service Invoice
                </button>
            </div>
        </div>

        <!-- Hidden Fields for Service Data -->
        <input type="hidden" name="items_data" :value="JSON.stringify(serviceItems)">
        <input type="hidden" name="service_period" :value="JSON.stringify(servicePeriod)">
        <input type="hidden" name="service_location" :value="serviceLocation">
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
function serviceTaxInvoiceManager() {
    return {
        serviceItems: [
            {% if existing_items %}
                {% for item in existing_items %}
                {
                    service_code: '{{ item.service_code|default:"" }}',
                    service_description: '{{ item.service_description|default:"" }}',
                    service_type: 'fixed',
                    uom: '{{ item.uom|default:"" }}',
                    quantity: {{ item.quantity|default:0 }},
                    rate: {{ item.rate|default:0 }},
                    service_tax_percentage: {{ item.service_tax_percentage|default:15 }},
                    amount: {{ item.amount|default:0 }},
                    service_tax_amount: {{ item.service_tax_amount|default:0 }},
                    line_total: {{ item.line_total|default:0 }}
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
            {% endif %}
        ],
        
        servicePeriod: {
            from: '',
            to: ''
        },
        
        serviceLocation: '',
        
        totals: {
            service_amount: 0,
            total_service_tax: 0,
            grand_total: 0,
            total_hours: 0
        },
        
        init() {
            lucide.createIcons();
            this.calculateAllTotals();
        },
        
        addFixedService() {
            this.serviceItems.push({
                service_code: '',
                service_description: '',
                service_type: 'fixed',
                uom: 'Service',
                quantity: 1,
                rate: 0,
                service_tax_percentage: 15,
                amount: 0,
                service_tax_amount: 0,
                line_total: 0
            });
        },
        
        addTimeBasedService() {
            this.serviceItems.push({
                service_code: '',
                service_description: '',
                service_type: 'hourly',
                uom: 'Hours',
                quantity: 8,
                rate: 0,
                service_tax_percentage: 15,
                amount: 0,
                service_tax_amount: 0,
                line_total: 0
            });
        },
        
        removeService(index) {
            this.serviceItems.splice(index, 1);
            this.calculateAllTotals();
        },
        
        duplicateService(index) {
            const service = JSON.parse(JSON.stringify(this.serviceItems[index]));
            this.serviceItems.push(service);
            this.calculateAllTotals();
        },
        
        updateServiceType(index) {
            const service = this.serviceItems[index];
            const uomMap = {
                'fixed': 'Service',
                'hourly': 'Hours',
                'daily': 'Days',
                'monthly': 'Months'
            };
            service.uom = uomMap[service.service_type] || 'Service';
            this.calculateServiceTotal(index);
        },
        
        calculateServiceTotal(index) {
            const service = this.serviceItems[index];
            
            // Calculate service amount
            service.amount = (service.quantity || 0) * (service.rate || 0);
            
            // Calculate service tax amount
            service.service_tax_amount = (service.amount * (service.service_tax_percentage || 0)) / 100;
            
            // Calculate line total
            service.line_total = service.amount + service.service_tax_amount;
            
            this.calculateAllTotals();
        },
        
        calculateAllTotals() {
            this.totals.service_amount = this.serviceItems.reduce((sum, service) => sum + (service.amount || 0), 0);
            this.totals.total_service_tax = this.serviceItems.reduce((sum, service) => sum + (service.service_tax_amount || 0), 0);
            this.totals.grand_total = this.totals.service_amount + this.totals.total_service_tax;
            
            // Calculate total hours for hourly services
            this.totals.total_hours = this.serviceItems
                .filter(service => service.service_type === 'hourly')
                .reduce((sum, service) => sum + (service.quantity || 0), 0);
        },
        
        getServicesByType(type) {
            return this.serviceItems.filter(service => service.service_type === type);
        },
        
        getAverageServiceTaxRate() {
            if (this.serviceItems.length === 0) return 0;
            const totalRate = this.serviceItems.reduce((sum, service) => sum + (service.service_tax_percentage || 0), 0);
            return (totalRate / this.serviceItems.length).toFixed(2);
        },
        
        formatCurrency(amount) {
            return '₹' + (amount || 0).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        },
        
        loadServiceTemplates() {
            console.log('Loading service templates...');
            // Implementation for loading service templates
        },
        
        generateTimesheet() {
            console.log('Generating timesheet...');
            // Implementation for timesheet generation
        },
        
        exportServiceReport() {
            console.log('Exporting service report...');
            // Implementation for service report export
        },
        
        saveDraft() {
            console.log('Saving draft...');
            // Implementation for saving draft
        },
        
        previewServiceInvoice() {
            if (this.serviceItems.length === 0) {
                alert('Please add services before previewing.');
                return;
            }
            console.log('Previewing service invoice...');
            // Implementation for preview
        },
        
        submitServiceTaxInvoice(event) {
            if (this.serviceItems.length === 0) {
                event.preventDefault();
                alert('Please add at least one service.');
                return false;
            }
            
            return true;
        }
    }
}

// Initialize lucide icons on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}