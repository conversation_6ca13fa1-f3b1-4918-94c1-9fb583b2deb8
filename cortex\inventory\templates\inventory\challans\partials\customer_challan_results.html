{% load static %}

<div class="overflow-x-auto">
    {% if challans %}
        <table class="w-full table-auto divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Challan Number
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Customer
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Work Order
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Delivery Type
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total Items
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for challan in challans %}
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        <a href="{% url 'inventory:customer_challan_detail' challan.pk %}" class="text-indigo-600 hover:text-indigo-900">
                            {{ challan.challan_number }}
                        </a>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {{ challan.challan_date|date:"M d, Y" }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {{ challan.customer_name }}
                        {% if challan.customer_code %}
                            <span class="block text-xs text-gray-400">{{ challan.customer_code }}</span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {{ challan.work_order_number|default:"-" }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {{ challan.get_delivery_type_display }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {{ challan.total_items }}
                        {% if challan.total_quantity %}
                            <span class="block text-xs text-gray-400">Qty: {{ challan.total_quantity|floatformat:2 }}</span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if challan.status == 'DRAFT' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                Draft
                            </span>
                        {% elif challan.status == 'PENDING' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                Pending
                            </span>
                        {% elif challan.status == 'DISPATCHED' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                Dispatched
                            </span>
                        {% elif challan.status == 'DELIVERED' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                Delivered
                            </span>
                        {% elif challan.status == 'CANCELLED' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                Cancelled
                            </span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex justify-end space-x-2">
                            <a href="{% url 'inventory:customer_challan_detail' challan.pk %}" 
                               class="text-indigo-600 hover:text-indigo-900 text-sm">
                                View
                            </a>
                            {% if challan.can_be_edited %}
                                <a href="{% url 'inventory:customer_challan_update' challan.pk %}" 
                                   class="text-green-600 hover:text-green-900 text-sm">
                                    Edit
                                </a>
                            {% endif %}
                            {% if challan.status == 'PENDING' %}
                                <a href="{% url 'inventory:customer_challan_dispatch' challan.pk %}" 
                                   class="text-blue-600 hover:text-blue-900 text-sm">
                                    Dispatch
                                </a>
                            {% endif %}
                            <a href="{% url 'inventory:customer_challan_print' challan.pk %}" 
                               class="text-gray-600 hover:text-gray-900 text-sm">
                                Print
                            </a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Pagination -->
        {% if is_paginated %}
        <div class="px-6 py-3 border-t border-gray-200">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-700">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} challans
                </div>
                <div class="flex space-x-1">
                    {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}" 
                           class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 border border-gray-300 rounded-md">
                            Previous
                        </a>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <span class="px-3 py-2 text-sm bg-indigo-600 text-white border border-indigo-600 rounded-md">
                                {{ num }}
                            </span>
                        {% else %}
                            <a href="?page={{ num }}" 
                               class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 border border-gray-300 rounded-md">
                                {{ num }}
                            </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}" 
                           class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 border border-gray-300 rounded-md">
                            Next
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    {% else %}
        <div class="text-center py-12">
            <div class="mx-auto h-12 w-12 text-gray-400">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
            </div>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No customer challans found</h3>
            <p class="mt-1 text-sm text-gray-500">Get started by creating a new customer challan.</p>
            <div class="mt-6">
                <a href="{% url 'inventory:customer_challan_create' %}" 
                   class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    Create Customer Challan
                </a>
            </div>
        </div>
    {% endif %}
</div>