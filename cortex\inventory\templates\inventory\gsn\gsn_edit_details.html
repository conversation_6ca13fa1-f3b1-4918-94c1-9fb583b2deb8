{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center">
                    <a href="{% url 'inventory:gsn_edit_list' %}" class="mr-4 text-sap-gray-600 hover:text-sap-blue-600">
                        <i data-lucide="arrow-left" class="w-5 h-5"></i>
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">{{ page_title }}</h1>
                </div>
            </div>
        </div>
    </div>

    <!-- Form Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="sap-card p-6">
            <form method="post">
                {% csrf_token %}
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.gsnno.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            GSN Number
                        </label>
                        {{ form.gsnno }}
                        {% if form.gsnno.errors %}
                            <div class="mt-1 text-sm text-red-600">{{ form.gsnno.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.ginid.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            GIN ID
                        </label>
                        {{ form.ginid }}
                        {% if form.ginid.errors %}
                            <div class="mt-1 text-sm text-red-600">{{ form.ginid.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.ginno.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            GIN Number
                        </label>
                        {{ form.ginno }}
                        {% if form.ginno.errors %}
                            <div class="mt-1 text-sm text-red-600">{{ form.ginno.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.taxinvoiceno.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Tax Invoice Number
                        </label>
                        {{ form.taxinvoiceno }}
                        {% if form.taxinvoiceno.errors %}
                            <div class="mt-1 text-sm text-red-600">{{ form.taxinvoiceno.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.taxinvoicedate.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Tax Invoice Date
                        </label>
                        {{ form.taxinvoicedate }}
                        {% if form.taxinvoicedate.errors %}
                            <div class="mt-1 text-sm text-red-600">{{ form.taxinvoicedate.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>

                <div class="mt-8 flex items-center justify-end space-x-3">
                    <a href="{% url 'inventory:gsn_edit_list' %}" 
                       class="px-4 py-2 text-sm font-medium text-sap-gray-700 bg-white border border-sap-gray-300 rounded-lg hover:bg-sap-gray-50 transition-colors">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="sap-button-primary">
                        <i data-lucide="save" class="w-4 h-4 mr-2"></i>
                        Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Initialize Lucide icons
document.addEventListener('DOMContentLoaded', function() {
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});
</script>
{% endblock %}