{% extends 'core/base.html' %}
{% load static %}

{% block title %}Delete Service Note {{ object.gsnno }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-gray-700 text-white px-4 py-2">
        <h1 class="text-lg font-bold">Delete Service Note - {{ object.gsnno }}</h1>
    </div>
    
    <div class="p-4">
        <div class="bg-white rounded-lg shadow-sm border max-w-md mx-auto">
            <div class="p-4 border-b">
                <h2 class="text-lg font-semibold text-gray-900">Confirm Deletion</h2>
            </div>
            
            <div class="p-4">
                <div class="flex items-center mb-4">
                    <div class="flex-shrink-0">
                        <svg class="h-10 w-10 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.996-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900">Delete Service Note</h3>
                        <p class="text-sm text-gray-500">This action cannot be undone.</p>
                    </div>
                </div>
                
                <div class="bg-gray-50 p-4 rounded-md mb-4">
                    <p class="text-sm text-gray-700">
                        Are you sure you want to delete service note <strong>{{ object.gsnno }}</strong>?
                    </p>
                    {% if object.ginno %}
                    <p class="text-sm text-gray-600 mt-1">
                        Related to GIN: {{ object.ginno }}
                    </p>
                    {% endif %}
                </div>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="flex items-center justify-end space-x-3">
                        <a href="{% url 'inventory:service_note_detail' object.pk %}" 
                           class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded text-sm font-medium transition-colors">
                            Cancel
                        </a>
                        
                        <button type="submit" 
                                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded text-sm font-medium transition-colors">
                            Delete Service Note
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}