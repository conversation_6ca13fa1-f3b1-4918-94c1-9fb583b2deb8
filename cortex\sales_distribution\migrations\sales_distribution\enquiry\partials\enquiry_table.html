<!-- Enquiry Table Partial for HTMX Updates -->
{% if enquiries %}
<table class="w-full border-collapse text-sm">
    <thead>
        <tr class="bg-gray-50">
            <th class="px-3 py-4 text-left font-semibold text-gray-700 border-b border-gray-200 text-xs uppercase tracking-wider">Enquiry #</th>
            <th class="px-3 py-4 text-left font-semibold text-gray-700 border-b border-gray-200 text-xs uppercase tracking-wider">Customer Details</th>
            <th class="px-3 py-4 text-left font-semibold text-gray-700 border-b border-gray-200 text-xs uppercase tracking-wider">Contact Info</th>
            <th class="px-3 py-4 text-left font-semibold text-gray-700 border-b border-gray-200 text-xs uppercase tracking-wider">Enquiry Summary</th>
            <th class="px-3 py-4 text-left font-semibold text-gray-700 border-b border-gray-200 text-xs uppercase tracking-wider">Status</th>
            <th class="px-3 py-4 text-left font-semibold text-gray-700 border-b border-gray-200 text-xs uppercase tracking-wider">Date</th>
            <th class="px-3 py-4 text-left font-semibold text-gray-700 border-b border-gray-200 text-xs uppercase tracking-wider">Attachments</th>
            <th class="px-3 py-4 text-left font-semibold text-gray-700 border-b border-gray-200 text-xs uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for enquiry in enquiries %}
        <tr class="hover:bg-gray-50 transition-colors duration-150">
            <td class="px-3 py-4 border-b border-gray-100 align-top">
                <div class="font-semibold text-blue-600">
                    #{{ enquiry.enqid }}
                </div>
                <div class="text-xs text-gray-500">
                    ID: {{ enquiry.customerid|default:"Auto" }}
                </div>
            </td>
            
            <td class="px-3 py-4 border-b border-gray-100 align-top">
                <div class="flex flex-col gap-0.5">
                    <div class="font-semibold text-gray-900">{{ enquiry.customername }}</div>
                    {% if enquiry.regdcity %}
                        <div class="text-xs text-gray-500">
                            📍 {{ enquiry.regdcity.cityname }}{% if enquiry.regdstate %}, {{ enquiry.regdstate.statename }}{% endif %}
                        </div>
                    {% endif %}
                    {% if enquiry.regdcountry %}
                        <div class="text-xs text-gray-500">
                            🌍 {{ enquiry.regdcountry.countryname }}
                        </div>
                    {% endif %}
                </div>
            </td>
            
            <td class="px-3 py-4 border-b border-gray-100 align-top">
                {% if enquiry.contactperson %}
                    <div class="font-medium">👤 {{ enquiry.contactperson }}</div>
                {% endif %}
                {% if enquiry.email %}
                    <div class="text-xs text-gray-500">
                        📧 {{ enquiry.email }}
                    </div>
                {% endif %}
                {% if enquiry.contactno %}
                    <div class="text-xs text-gray-500">
                        📞 {{ enquiry.contactno }}
                    </div>
                {% endif %}
            </td>
            
            <td class="px-3 py-4 border-b border-gray-100 align-top">
                <div class="max-w-xs overflow-hidden text-ellipsis line-clamp-2">
                    {{ enquiry.enquiryfor|truncatewords:15 }}
                </div>
                {% if enquiry.remark %}
                    <div class="text-xs text-gray-500 mt-1">
                        💬 {{ enquiry.remark|truncatewords:10 }}
                    </div>
                {% endif %}
            </td>
            
            <td class="px-3 py-4 border-b border-gray-100 align-top">
                {% if enquiry.status_display.status == 'Quoted' %}
                    <span class="inline-flex items-center gap-1 px-3 py-1 rounded-md text-xs font-semibold uppercase tracking-wide bg-green-100 text-green-800">
                        {{ enquiry.status_display.icon }} {{ enquiry.status_display.status }}
                    </span>
                {% elif enquiry.status_display.status == 'New' %}
                    <span class="inline-flex items-center gap-1 px-3 py-1 rounded-md text-xs font-semibold uppercase tracking-wide bg-blue-100 text-blue-800">
                        {{ enquiry.status_display.icon }} {{ enquiry.status_display.status }}
                    </span>
                {% else %}
                    <span class="inline-flex items-center gap-1 px-3 py-1 rounded-md text-xs font-semibold uppercase tracking-wide bg-yellow-100 text-yellow-800">
                        {{ enquiry.status_display.icon }} {{ enquiry.status_display.status }}
                    </span>
                {% endif %}
            </td>
            
            <td class="px-3 py-4 border-b border-gray-100 align-top">
                <div class="font-medium">📅 {{ enquiry.sysdate }}</div>
                <div class="text-xs text-gray-500">
                    🕐 {{ enquiry.systime }}
                </div>
            </td>
            
            <td class="px-3 py-4 border-b border-gray-100 align-top">
                {% if enquiry.attachment_count > 0 %}
                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-semibold bg-gray-100 text-gray-700">
                        📎 {{ enquiry.attachment_count }}
                    </span>
                {% else %}
                    <span class="text-gray-400">—</span>
                {% endif %}
            </td>
            
            <td class="px-3 py-4 border-b border-gray-100 align-top">
                <div class="flex gap-2">
                    <a href="{% url 'sales_distribution:enquiry_detail' enquiry.enqid %}" 
                       class="inline-flex items-center gap-1 px-3 py-1.5 rounded-md text-xs font-semibold bg-blue-500 text-white hover:bg-blue-600 transition-colors"
                       title="View Details">
                        👁️ View
                    </a>
                    
                    {% if enquiry.postatus != 2 %}
                        <a href="{% url 'sales_distribution:enquiry_edit' enquiry.enqid %}" 
                           class="inline-flex items-center gap-1 px-3 py-1.5 rounded-md text-xs font-semibold bg-amber-500 text-white hover:bg-amber-600 transition-colors"
                           title="Edit Enquiry">
                            ✏️ Edit
                        </a>
                    {% endif %}
                </div>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<!-- Pagination -->
{% if is_paginated %}
<div class="flex justify-center items-center gap-2 py-6 px-6 bg-gray-50 border-t border-gray-200">
    {% if page_obj.has_previous %}
        <a href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.country %}country={{ request.GET.country }}&{% endif %}{% if request.GET.date_from %}date_from={{ request.GET.date_from }}&{% endif %}{% if request.GET.date_to %}date_to={{ request.GET.date_to }}&{% endif %}page=1"
           class="px-3 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium transition-colors">
            « First
        </a>
        <a href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.country %}country={{ request.GET.country }}&{% endif %}{% if request.GET.date_from %}date_from={{ request.GET.date_from }}&{% endif %}{% if request.GET.date_to %}date_to={{ request.GET.date_to }}&{% endif %}page={{ page_obj.previous_page_number }}"
           class="px-3 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium transition-colors">
            ‹ Previous
        </a>
    {% endif %}

    <span class="px-3 py-2 bg-blue-600 text-white rounded-md font-medium">
        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
    </span>

    {% if page_obj.has_next %}
        <a href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.country %}country={{ request.GET.country }}&{% endif %}{% if request.GET.date_from %}date_from={{ request.GET.date_from }}&{% endif %}{% if request.GET.date_to %}date_to={{ request.GET.date_to }}&{% endif %}page={{ page_obj.next_page_number }}"
           class="px-3 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium transition-colors">
            Next ›
        </a>
        <a href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.country %}country={{ request.GET.country }}&{% endif %}{% if request.GET.date_from %}date_from={{ request.GET.date_from }}&{% endif %}{% if request.GET.date_to %}date_to={{ request.GET.date_to }}&{% endif %}page={{ page_obj.paginator.num_pages }}"
           class="px-3 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium transition-colors">
            Last »
        </a>
    {% endif %}
</div>
{% endif %}

{% else %}
<!-- No data state -->
<div class="text-center py-16 px-6 text-gray-500">
    <span class="text-6xl block mb-4 opacity-50">🔍</span>
    <h3 class="text-lg font-semibold text-gray-700 mb-2">No enquiries found</h3>
    <p class="text-gray-500 mb-4">
        {% if request.GET.search or request.GET.status or request.GET.country %}
            Try adjusting your search criteria or filters.
        {% else %}
            Start by creating your first customer enquiry.
        {% endif %}
    </p>
    {% if not request.GET.search and not request.GET.status and not request.GET.country %}
        <a href="{% url 'sales_distribution:enquiry_create' %}" 
           class="inline-flex items-center gap-2 bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-2 rounded-lg font-medium hover:-translate-y-0.5 hover:shadow-lg transition-all duration-200">
            ➕ Create First Enquiry
        </a>
    {% endif %}
</div>
{% endif %}
