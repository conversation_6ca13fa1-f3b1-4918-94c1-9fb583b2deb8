{% extends "core/base.html" %}
{% load static %}

{% block title %}Release WO for WIS - Inventory Management{% endblock %}

{% block content %}
<div class="sap-page">
    <!-- Page Header -->
    <div class="sap-page-header">
        <div class="sap-page-header-content">
            <div class="sap-page-title">
                <h1>Release WO for WIS</h1>
                <p>Work order release management for Workshop Information System</p>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="sap-panel mb-4">
        <div class="sap-panel-header">
            <h3 class="sap-panel-title">Filter Work Orders</h3>
        </div>
        <div class="sap-panel-content">
            <form method="get" class="sap-form">
                <div class="sap-form-row">
                    <div class="sap-form-group">
                        <label class="sap-form-label">WO Category</label>
                        <select name="wo_category" class="sap-select"
                                hx-get="{% url 'inventory:wis_release_list' %}"
                                hx-trigger="change"
                                hx-target="#wis-release-results"
                                hx-include="form">
                            <option value="">All Categories</option>
                            <option value="STANDARD" {% if request.GET.wo_category == 'STANDARD' %}selected{% endif %}>Standard Product</option>
                            <option value="NON_STANDARD" {% if request.GET.wo_category == 'NON_STANDARD' %}selected{% endif %}>Non-Standard Product</option>
                            <option value="ERECTION" {% if request.GET.wo_category == 'ERECTION' %}selected{% endif %}>Erection & Commissioning</option>
                            <option value="SPARES" {% if request.GET.wo_category == 'SPARES' %}selected{% endif %}>Spares</option>
                            <option value="JOB_WORK" {% if request.GET.wo_category == 'JOB_WORK' %}selected{% endif %}>Job Work</option>
                            <option value="AUTOMATION" {% if request.GET.wo_category == 'AUTOMATION' %}selected{% endif %}>Automation & SPM</option>
                            <option value="AGRICULTURE" {% if request.GET.wo_category == 'AGRICULTURE' %}selected{% endif %}>Agriculture Products</option>
                        </select>
                    </div>
                    <div class="sap-form-group">
                        <label class="sap-form-label">WO No</label>
                        <input type="text" name="wo_number" value="{{ request.GET.wo_number }}"
                               class="sap-input" placeholder="Work Order Number"
                               hx-get="{% url 'inventory:wis_release_list' %}"
                               hx-trigger="keyup changed delay:300ms"
                               hx-target="#wis-release-results"
                               hx-include="form">
                    </div>
                    <div class="sap-form-group">
                        <button type="submit" class="sap-button sap-button--emphasized">Search</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Work Orders Release Table -->
    <div class="sap-panel">
        <div class="sap-panel-header">
            <h3 class="sap-panel-title">Work Orders for Release</h3>
        </div>
        <div class="sap-panel-content">
            <div id="wis-release-results">
                {% include 'inventory/wis/partials/wis_release_results.html' %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/htmx.org@1.9.6"></script>
{% endblock %}