{% extends "core/base.html" %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{{ page_title }}</h1>
                        <p class="text-gray-600 mt-1">Edit enquiry #{{ enquiry.enqid }}</p>
                    </div>
                    <div class="flex space-x-3">
                        <a href="{% url 'sales_distribution:enquiry_detail' enquiry.enqid %}" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            View Details
                        </a>
                        <a href="{% url 'sales_distribution:enquiry_list' %}" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                            Back to List
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Form -->
        <form method="post" enctype="multipart/form-data" id="enquiry-edit-form">
            {% csrf_token %}

            <!-- Customer Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-t-lg">
                    <h3 class="text-lg font-medium">Customer Information</h3>
                </div>
                <div class="px-6 py-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.customername.id_for_label }}">
                                Customer Name <span class="text-red-500">*</span>
                            </label>
                            {{ form.customername }}
                            {% if form.customername.errors %}
                                <div class="text-red-600 text-sm mt-1">
                                    {% for error in form.customername.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.contactperson.id_for_label }}">
                                Contact Person
                            </label>
                            {{ form.contactperson }}
                        </div>

                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.email.id_for_label }}">
                                Email Address
                            </label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="text-red-600 text-sm mt-1">
                                    {% for error in form.email.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.contactno.id_for_label }}">
                                Contact Number
                            </label>
                            {{ form.contactno }}
                        </div>

                        <!-- Hidden fields -->
                        {{ form.customerid }}
                    </div>
                </div>
            </div>

            <!-- Registered Office Address -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-green-600 to-teal-600 text-white">
                    <h3 class="text-lg font-medium">Registered Office Address</h3>
                </div>
                <div class="px-6 py-6">
                    <div class="grid grid-cols-1 gap-6">
                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.regdaddress.id_for_label }}">
                                Address <span class="text-red-500">*</span>
                            </label>
                            {{ form.regdaddress }}
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.regdcountry.id_for_label }}">
                                    Country
                                </label>
                                {{ form.regdcountry }}
                            </div>

                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.regdstate.id_for_label }}">
                                    State
                                </label>
                                {{ form.regdstate }}
                            </div>

                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.regdcity.id_for_label }}">
                                    City
                                </label>
                                {{ form.regdcity }}
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.regdpinno.id_for_label }}">
                                    PIN Code
                                </label>
                                {{ form.regdpinno }}
                            </div>

                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.regdcontactno.id_for_label }}">
                                    Contact Number
                                </label>
                                {{ form.regdcontactno }}
                            </div>

                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.regdfaxno.id_for_label }}">
                                    Fax Number
                                </label>
                                {{ form.regdfaxno }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Works/Factory Address -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-orange-600 to-red-600 text-white">
                    <h3 class="text-lg font-medium">Works/Factory Address</h3>
                </div>
                <div class="px-6 py-6">
                    <div class="grid grid-cols-1 gap-6">
                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.workaddress.id_for_label }}">
                                Address
                            </label>
                            {{ form.workaddress }}
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.workcountry.id_for_label }}">
                                    Country
                                </label>
                                {{ form.workcountry }}
                            </div>

                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.workstate.id_for_label }}">
                                    State
                                </label>
                                {{ form.workstate }}
                            </div>

                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.workcity.id_for_label }}">
                                    City
                                </label>
                                {{ form.workcity }}
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.workpinno.id_for_label }}">
                                    PIN Code
                                </label>
                                {{ form.workpinno }}
                            </div>

                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.workcontactno.id_for_label }}">
                                    Contact Number
                                </label>
                                {{ form.workcontactno }}
                            </div>

                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.workfaxno.id_for_label }}">
                                    Fax Number
                                </label>
                                {{ form.workfaxno }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Material Delivery Address -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-purple-600 to-pink-600 text-white">
                    <h3 class="text-lg font-medium">Material Delivery Address</h3>
                </div>
                <div class="px-6 py-6">
                    <div class="grid grid-cols-1 gap-6">
                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.materialdeladdress.id_for_label }}">
                                Address
                            </label>
                            {{ form.materialdeladdress }}
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.materialdelcountry.id_for_label }}">
                                    Country
                                </label>
                                {{ form.materialdelcountry }}
                            </div>

                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.materialdelstate.id_for_label }}">
                                    State
                                </label>
                                {{ form.materialdelstate }}
                            </div>

                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.materialdelcity.id_for_label }}">
                                    City
                                </label>
                                {{ form.materialdelcity }}
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.materialdelpinno.id_for_label }}">
                                    PIN Code
                                </label>
                                {{ form.materialdelpinno }}
                            </div>

                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.materialdelcontactno.id_for_label }}">
                                    Contact Number
                                </label>
                                {{ form.materialdelcontactno }}
                            </div>

                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.materialdelfaxno.id_for_label }}">
                                    Fax Number
                                </label>
                                {{ form.materialdelfaxno }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Business Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-indigo-600 to-blue-600 text-white">
                    <h3 class="text-lg font-medium">Business Information</h3>
                </div>
                <div class="px-6 py-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.juridictioncode.id_for_label }}">
                                Jurisdiction Code
                            </label>
                            {{ form.juridictioncode }}
                        </div>

                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.commissionurate.id_for_label }}">
                                Commission Rate
                            </label>
                            {{ form.commissionurate }}
                        </div>

                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.tinvatno.id_for_label }}">
                                TIN/VAT Number
                            </label>
                            {{ form.tinvatno }}
                        </div>

                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.eccno.id_for_label }}">
                                ECC Number
                            </label>
                            {{ form.eccno }}
                        </div>

                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.divn.id_for_label }}">
                                Division
                            </label>
                            {{ form.divn }}
                        </div>

                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.tincstno.id_for_label }}">
                                TIN/CST Number
                            </label>
                            {{ form.tincstno }}
                        </div>

                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.range.id_for_label }}">
                                Range
                            </label>
                            {{ form.range }}
                        </div>

                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.panno.id_for_label }}">
                                PAN Number
                            </label>
                            {{ form.panno }}
                        </div>

                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.tdscode.id_for_label }}">
                                TDS Code
                            </label>
                            {{ form.tdscode }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enquiry Details -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-600 to-gray-800 text-white">
                    <h3 class="text-lg font-medium">Enquiry Details</h3>
                </div>
                <div class="px-6 py-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.enquiryfor.id_for_label }}">
                                Enquiry Description <span class="text-red-500">*</span>
                            </label>
                            {{ form.enquiryfor }}
                            {% if form.enquiryfor.errors %}
                                <div class="text-red-600 text-sm mt-1">
                                    {% for error in form.enquiryfor.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="{{ form.remark.id_for_label }}">
                                Remarks
                            </label>
                            {{ form.remark }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- New File Attachments -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-yellow-600 to-orange-600 text-white">
                    <h3 class="text-lg font-medium">Add New Attachments</h3>
                </div>
                <div class="px-6 py-6">
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                        <svg class="w-12 h-12 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        <p class="text-gray-600 text-sm mb-2">Drop files here or click to browse</p>
                        <input type="file" name="new_attachments" multiple id="file-input" class="hidden" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif">
                        <button type="button" onclick="document.getElementById('file-input').click()" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                            Choose Files
                        </button>
                    </div>
                    <div id="file-list" class="mt-4 space-y-2"></div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 flex justify-between items-center">
                    <a href="{% url 'sales_distribution:enquiry_detail' enquiry.enqid %}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        Cancel
                    </a>
                    
                    <button type="submit" 
                            class="inline-flex items-center px-6 py-2 bg-blue-600 border border-transparent rounded-lg font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Update Enquiry
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // File upload handling
    const fileInput = document.getElementById('file-input');
    const fileList = document.getElementById('file-list');

    fileInput.addEventListener('change', function() {
        displayFileList();
    });

    function displayFileList() {
        fileList.innerHTML = '';
        
        if (fileInput.files.length > 0) {
            Array.from(fileInput.files).forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg border';
                fileItem.innerHTML = `
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span class="text-sm text-gray-700">${file.name}</span>
                        <span class="text-xs text-gray-500 ml-2">(${formatFileSize(file.size)})</span>
                    </div>
                    <button type="button" onclick="removeFile(${index})" class="text-red-600 hover:text-red-700">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                `;
                fileList.appendChild(fileItem);
            });
        }
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Make removeFile function global
    window.removeFile = function(index) {
        const dt = new DataTransfer();
        const files = Array.from(fileInput.files);
        
        files.forEach((file, i) => {
            if (i !== index) {
                dt.items.add(file);
            }
        });
        
        fileInput.files = dt.files;
        displayFileList();
    };
});
</script>

{% endblock %}