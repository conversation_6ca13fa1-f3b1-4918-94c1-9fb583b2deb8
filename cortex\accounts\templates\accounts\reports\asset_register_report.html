<!-- accounts/templates/accounts/reports/asset_register_report.html -->
<!-- Asset Register Report - SAP S/4HANA Inspired Design -->
<!-- Replaces ASP.NET AssetRegister_Report.aspx functionality -->

{% extends 'core/base.html' %}
{% load static %}

{% block title %}Asset Register Report - {{ page_title }} - Cortex{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-sap-orange-500 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="package" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Asset Register Report</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Asset tracking with depreciation and maintenance history</p>
                </div>
            </div>
            <div class="flex space-x-3">
                <button 
                    hx-get="{% url 'accounts:asset_register_list' %}?export=pdf" 
                    hx-indicator="#export-indicator"
                    class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:ring-offset-2 transition-colors duration-200">
                    <i data-lucide="file-text" class="w-4 h-4 mr-2"></i>
                    Export PDF
                </button>
                <button 
                    hx-get="{% url 'accounts:asset_register_list' %}?export=excel" 
                    hx-indicator="#export-indicator"
                    class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:ring-offset-2 transition-colors duration-200">
                    <i data-lucide="sheet" class="w-4 h-4 mr-2"></i>
                    Export Excel
                </button>
                <a 
                    href="{% url 'accounts:asset_register_create' %}"
                    class="inline-flex items-center px-4 py-2 bg-sap-orange-500 text-white rounded-lg hover:bg-sap-orange-600 focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:ring-offset-2 transition-colors duration-200">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    Add Asset
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-2 py-6 space-y-6" id="asset-register-content">
    
    <!-- Export Indicator -->
    <div id="export-indicator" class="htmx-indicator">
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 flex items-center space-x-4">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-sap-orange-500"></div>
                <span class="text-sap-gray-700">Generating export...</span>
            </div>
        </div>
    </div>

    <!-- Filter Controls -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
        <h3 class="text-lg font-semibold text-sap-gray-800 mb-4 flex items-center">
            <i data-lucide="filter" class="w-5 h-5 mr-2 text-sap-blue-500"></i>
            Filter Options
        </h3>
        <form 
            hx-get="{% url 'accounts:asset_register_list' %}" 
            hx-target="#asset-results" 
            hx-indicator="#filter-indicator"
            hx-trigger="input changed delay:500ms from:input, change from:select"
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            
            <!-- Asset Category -->
            <div>
                <label for="category" class="block text-sm font-medium text-sap-gray-700 mb-2">Asset Category</label>
                <select 
                    name="category" 
                    id="category"
                    class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
                    <option value="">All Categories</option>
                    {% for category in asset_categories %}
                    <option value="{{ category.id }}" {% if request.GET.category == category.id|stringformat:"s" %}selected{% endif %}>
                        {{ category.category_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <!-- Location -->
            <div>
                <label for="location" class="block text-sm font-medium text-sap-gray-700 mb-2">Location</label>
                <select 
                    name="location" 
                    id="location"
                    class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
                    <option value="">All Locations</option>
                    {% for location in asset_locations %}
                    <option value="{{ location.id }}" {% if request.GET.location == location.id|stringformat:"s" %}selected{% endif %}>
                        {{ location.location_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <!-- Status -->
            <div>
                <label for="status" class="block text-sm font-medium text-sap-gray-700 mb-2">Asset Status</label>
                <select 
                    name="status" 
                    id="status"
                    class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
                    <option value="">All Status</option>
                    <option value="active" {% if request.GET.status == "active" %}selected{% endif %}>Active</option>
                    <option value="disposed" {% if request.GET.status == "disposed" %}selected{% endif %}>Disposed</option>
                    <option value="under_maintenance" {% if request.GET.status == "under_maintenance" %}selected{% endif %}>Under Maintenance</option>
                </select>
            </div>
            
            <!-- Purchase Date Range -->
            <div>
                <label for="purchase_date_from" class="block text-sm font-medium text-sap-gray-700 mb-2">Purchase From</label>
                <input 
                    type="date" 
                    name="purchase_date_from" 
                    id="purchase_date_from"
                    value="{{ request.GET.purchase_date_from }}"
                    class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
            </div>
            
            <!-- Search Box -->
            <div class="md:col-span-2">
                <label for="search" class="block text-sm font-medium text-sap-gray-700 mb-2">Search Asset</label>
                <input 
                    type="text" 
                    name="search" 
                    id="search"
                    value="{{ request.GET.search }}"
                    placeholder="Search by asset name, code, or description..."
                    class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
            </div>
            
            <!-- Clear Filters -->
            <div class="flex items-end">
                <button 
                    type="button"
                    onclick="document.querySelector('form').reset(); htmx.trigger(document.querySelector('form'), 'submit')"
                    class="w-full px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-sap-gray-500 focus:ring-offset-2 transition-colors duration-200">
                    <i data-lucide="x-circle" class="w-4 h-4 mr-2 inline"></i>
                    Clear Filters
                </button>
            </div>
        </form>
        
        <!-- Filter Indicator -->
        <div id="filter-indicator" class="htmx-indicator mt-4">
            <div class="flex items-center justify-center py-2">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-sap-orange-500 mr-2"></div>
                <span class="text-sm text-sap-gray-600">Loading...</span>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="package" class="w-5 h-5 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-semibold text-sap-gray-900">{{ total_assets|default:"0" }}</p>
                    <p class="text-sm font-medium text-sap-gray-600">Total Assets</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="trending-up" class="w-5 h-5 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-semibold text-sap-gray-900">₹ {{ total_purchase_value|floatformat:2|default:"0.00" }}</p>
                    <p class="text-sm font-medium text-sap-gray-600">Purchase Value</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-red-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="trending-down" class="w-5 h-5 text-sap-red-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-semibold text-sap-gray-900">₹ {{ total_depreciation|floatformat:2|default:"0.00" }}</p>
                    <p class="text-sm font-medium text-sap-gray-600">Total Depreciation</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="calculator" class="w-5 h-5 text-sap-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-semibold text-sap-gray-900">₹ {{ current_book_value|floatformat:2|default:"0.00" }}</p>
                    <p class="text-sm font-medium text-sap-gray-600">Current Value</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Asset Register Results -->
    <div id="asset-results">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-200 bg-sap-gray-50">
                <h3 class="text-lg font-semibold text-sap-gray-800 flex items-center">
                    <i data-lucide="list" class="w-5 h-5 mr-2 text-sap-orange-500"></i>
                    Asset Register
                </h3>
            </div>
            
            <!-- Table Container -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-sap-gray-200">
                    <thead class="bg-sap-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Asset Details
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Category
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Purchase Info
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Purchase Value
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Depreciation
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Book Value
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-sap-gray-200">
                        {% for asset in assets %}
                        <tr class="hover:bg-sap-gray-50 transition-colors duration-150">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-sap-orange-100 rounded-lg flex items-center justify-center mr-3">
                                        <i data-lucide="package" class="w-5 h-5 text-sap-orange-600"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-sap-gray-900">{{ asset.asset_name }}</div>
                                        <div class="text-sm text-sap-gray-500">{{ asset.asset_code|default:"-" }}</div>
                                        <div class="text-2xs text-sap-gray-400">{{ asset.location|default:"-" }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-sap-gray-900">{{ asset.category.category_name|default:"-" }}</div>
                                <div class="text-2xs text-sap-gray-500">{{ asset.subcategory.subcategory_name|default:"-" }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-sap-gray-900">{{ asset.purchase_date|date:"d/m/Y"|default:"-" }}</div>
                                <div class="text-2xs text-sap-gray-500">{{ asset.supplier|default:"-" }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-right font-mono text-sap-gray-900">
                                ₹ {{ asset.purchase_value|floatformat:2|default:"0.00" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-right">
                                <div class="font-mono text-sap-red-600">₹ {{ asset.accumulated_depreciation|floatformat:2|default:"0.00" }}</div>
                                <div class="text-2xs text-sap-gray-500">{{ asset.depreciation_rate|default:"0" }}% p.a.</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-right font-mono text-sap-gray-900">
                                ₹ {{ asset.current_book_value|floatformat:2|default:"0.00" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                {% if asset.status == 'active' %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-sap-green-100 text-sap-green-800">
                                        <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                        Active
                                    </span>
                                {% elif asset.status == 'disposed' %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-sap-red-100 text-sap-red-800">
                                        <i data-lucide="x-circle" class="w-3 h-3 mr-1"></i>
                                        Disposed
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-sap-orange-100 text-sap-orange-800">
                                        <i data-lucide="tool" class="w-3 h-3 mr-1"></i>
                                        Maintenance
                                    </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                <div class="flex items-center justify-center space-x-2">
                                    <a 
                                        href="{% url 'accounts:asset_register_create' %}?edit={{ asset.id }}"
                                        class="text-sap-blue-600 hover:text-sap-blue-800 transition-colors duration-150"
                                        title="Edit Asset">
                                        <i data-lucide="edit-2" class="w-4 h-4"></i>
                                    </a>
                                    <button 
                                        hx-get="{% url 'accounts:asset_register_list' %}?details={{ asset.id }}"
                                        hx-target="#asset-details-modal"
                                        hx-trigger="click"
                                        class="text-sap-green-600 hover:text-sap-green-800 transition-colors duration-150"
                                        title="View Details">
                                        <i data-lucide="eye" class="w-4 h-4"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="px-6 py-8 text-center">
                                <div class="flex flex-col items-center justify-center">
                                    <i data-lucide="package" class="w-12 h-12 text-sap-gray-400 mb-4"></i>
                                    <p class="text-lg font-medium text-sap-gray-900 mb-2">No assets found</p>
                                    <p class="text-sm text-sap-gray-500">Try adjusting your filters or add your first asset</p>
                                    <a 
                                        href="{% url 'accounts:asset_register_create' %}"
                                        class="mt-4 inline-flex items-center px-4 py-2 bg-sap-orange-500 text-white rounded-lg hover:bg-sap-orange-600 transition-colors duration-200">
                                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                        Add First Asset
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if assets.has_other_pages %}
            <div class="px-6 py-4 border-t border-sap-gray-200 bg-sap-gray-50">
                <div class="flex items-center justify-between">
                    <div class="flex items-center text-sm text-sap-gray-700">
                        Showing {{ assets.start_index }} to {{ assets.end_index }} of {{ assets.paginator.count }} results
                    </div>
                    <div class="flex space-x-2">
                        {% if assets.has_previous %}
                        <button 
                            hx-get="?page={{ assets.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}"
                            hx-target="#asset-results"
                            class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Previous
                        </button>
                        {% endif %}
                        
                        <span class="px-3 py-2 text-sm font-medium text-sap-gray-700">
                            Page {{ assets.number }} of {{ assets.paginator.num_pages }}
                        </span>
                        
                        {% if assets.has_next %}
                        <button 
                            hx-get="?page={{ assets.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}"
                            hx-target="#asset-results"
                            class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Next
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Depreciation Chart (Expandable) -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm" x-data="{ expanded: false }">
        <div class="px-6 py-4 border-b border-sap-gray-200 cursor-pointer" @click="expanded = !expanded">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-sap-gray-800 flex items-center">
                    <i data-lucide="trending-down" class="w-5 h-5 mr-2 text-sap-red-500"></i>
                    Depreciation Analysis
                </h3>
                <i data-lucide="chevron-down" class="w-5 h-5 text-sap-gray-500 transition-transform duration-200" 
                   :class="{ 'transform rotate-180': expanded }"></i>
            </div>
        </div>
        <div x-show="expanded" x-transition class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="relative">
                    <canvas id="depreciationChart" width="400" height="200"></canvas>
                </div>
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center p-4 bg-sap-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-sap-blue-600">{{ total_assets|default:"0" }}</div>
                            <div class="text-sm text-sap-gray-600">Total Assets</div>
                        </div>
                        <div class="text-center p-4 bg-sap-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-sap-red-600">{{ fully_depreciated_assets|default:"0" }}</div>
                            <div class="text-sm text-sap-gray-600">Fully Depreciated</div>
                        </div>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-sm text-sap-gray-600">Average Asset Age:</span>
                            <span class="text-sm font-medium">{{ average_asset_age|default:"0" }} years</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-sap-gray-600">Total Depreciation Rate:</span>
                            <span class="text-sm font-medium">{{ average_depreciation_rate|default:"0" }}%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Asset Details Modal -->
<div id="asset-details-modal"></div>

<!-- Chart.js for visualizations -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
    
    // Depreciation Chart
    const ctx = document.getElementById('depreciationChart');
    if (ctx) {
        const chart = new Chart(ctx.getContext('2d'), {
            type: 'line',
            data: {
                labels: ['Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5'],
                datasets: [{
                    label: 'Cumulative Depreciation',
                    data: [10, 18, 25, 31, 36], // Sample data
                    borderColor: '#f44336',
                    backgroundColor: 'rgba(244, 67, 54, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ₹' + context.parsed.y.toLocaleString('en-IN') + ' Lacs';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Amount (₹ Lacs)'
                        }
                    }
                }
            }
        });
    }
});

// Print Styles
const printStyles = `
    @media print {
        .no-print { display: none !important; }
        body { print-color-adjust: exact; }
        .bg-white { background: white !important; }
        .text-white { color: black !important; }
        .border { border: 1px solid #000 !important; }
        table { border-collapse: collapse; }
        th, td { border: 1px solid #000 !important; }
    }
`;
const styleSheet = document.createElement("style");
styleSheet.innerText = printStyles;
document.head.appendChild(styleSheet);
</script>
{% endblock %}