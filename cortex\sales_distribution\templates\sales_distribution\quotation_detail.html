{% extends "core/base.html" %}
{% load static %}

{% block title %}{{ page_title }} - {{ global_company.company_name }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-slate-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- Breadcrumb Navigation -->
        <nav class="flex items-center space-x-2 text-sm mb-8" aria-label="Breadcrumb">
            <div class="flex items-center space-x-2 bg-white/80 backdrop-blur-sm rounded-lg px-4 py-2 border border-slate-200/60 shadow-sm">
                <a href="{% url 'sales_distribution:dashboard' %}" 
                   class="text-blue-600 hover:text-blue-700 transition-colors duration-200 font-medium flex items-center space-x-1">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                    </svg>
                    <span>Sales Distribution</span>
                </a>
                <svg class="w-4 h-4 text-slate-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
                <a href="{% url 'sales_distribution:quotation_list' %}" 
                   class="text-blue-600 hover:text-blue-700 transition-colors duration-200 font-medium flex items-center space-x-1">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    <span>Quotations</span>
                </a>
                <svg class="w-4 h-4 text-slate-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
                <span class="text-slate-700 font-semibold">{{ quotation.quotationno }}</span>
            </div>
        </nav>

        <!-- Main Header Card -->
        <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200/60 mb-8 overflow-hidden">
            <div class="bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 px-8 py-10">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="mb-6 lg:mb-0">
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                </svg>
                            </div>
                            <h1 class="text-4xl font-bold text-white tracking-tight">
                                {{ quotation.quotationno }}
                            </h1>
                        </div>
                        <p class="text-blue-100 text-lg leading-relaxed max-w-2xl">
                            Customer: {{ quotation.customerid }}
                            {% if quotation.enqid %}
                                • Enquiry: ENQ-{{ quotation.enqid.enqid }}
                            {% endif %}
                        </p>
                    </div>
                    <div class="flex flex-col lg:items-end space-y-4">
                        <div class="flex items-center space-x-4">
                            <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold {{ status_display.class }}">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                {{ status_display.status }}
                            </span>
                        </div>
                        <div class="flex space-x-3">
                            <a href="{% url 'sales_distribution:quotation_edit' quotation.id %}" 
                               class="inline-flex items-center px-4 py-2 bg-white/20 text-white border border-white/30 rounded-lg font-medium hover:bg-white/30 focus:outline-none focus:ring-2 focus:ring-white/50 transition-all duration-200 backdrop-blur-sm">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                </svg>
                                Edit Quotation
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Grid -->
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
            
            <!-- Main Content -->
            <div class="xl:col-span-2 space-y-8">
                
                <!-- Quotation Details -->
                <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 overflow-hidden">
                    <div class="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-slate-200/60">
                        <h2 class="text-xl font-bold text-slate-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                            </svg>
                            Quotation Items
                        </h2>
                    </div>
                    
                    {% if quotation_details %}
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-slate-200">
                            <thead class="bg-slate-50">
                                <tr>
                                    <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">
                                        Item Description
                                    </th>
                                    <th scope="col" class="px-6 py-4 text-center text-xs font-semibold text-slate-600 uppercase tracking-wider">
                                        Quantity
                                    </th>
                                    <th scope="col" class="px-6 py-4 text-center text-xs font-semibold text-slate-600 uppercase tracking-wider">
                                        Unit
                                    </th>
                                    <th scope="col" class="px-6 py-4 text-right text-xs font-semibold text-slate-600 uppercase tracking-wider">
                                        Rate
                                    </th>
                                    <th scope="col" class="px-6 py-4 text-right text-xs font-semibold text-slate-600 uppercase tracking-wider">
                                        Discount
                                    </th>
                                    <th scope="col" class="px-6 py-4 text-right text-xs font-semibold text-slate-600 uppercase tracking-wider">
                                        Amount
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-slate-200">
                                {% for detail in quotation_details %}
                                <tr class="hover:bg-slate-50 transition-colors duration-200">
                                    <td class="px-6 py-4">
                                        <div class="text-sm font-medium text-slate-900">
                                            {{ detail.itemdesc }}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 text-center">
                                        <div class="text-sm text-slate-900 font-medium">
                                            {{ detail.totalqty|floatformat:2 }}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 text-center">
                                        <div class="text-sm text-slate-700">
                                            {{ detail.unit.unitname }}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 text-right">
                                        <div class="text-sm font-medium text-slate-900">
                                            ₹{{ detail.rate|floatformat:2 }}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 text-right">
                                        <div class="text-sm text-slate-700">
                                            {% if detail.discount %}
                                                ₹{{ detail.discount|floatformat:2 }}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 text-right">
                                        <div class="text-sm font-bold text-slate-900">
                                            ₹{{ detail.line_amount|floatformat:2 }}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot class="bg-slate-50">
                                <tr>
                                    <td colspan="5" class="px-6 py-4 text-right text-sm font-bold text-slate-900">
                                        Total Amount:
                                    </td>
                                    <td class="px-6 py-4 text-right text-lg font-bold text-blue-600">
                                        ₹{{ line_total|floatformat:2 }}
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    {% else %}
                    <div class="px-6 py-12 text-center">
                        <svg class="mx-auto h-12 w-12 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-slate-900">No items found</h3>
                        <p class="mt-1 text-sm text-slate-500">No quotation items have been added yet.</p>
                    </div>
                    {% endif %}
                </div>

                <!-- Terms and Conditions -->
                {% if quotation.paymentterms or quotation.deliveryterms or quotation.validity or quotation.warrenty %}
                <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 overflow-hidden">
                    <div class="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-slate-200/60">
                        <h2 class="text-xl font-bold text-slate-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                            Terms & Conditions
                        </h2>
                    </div>
                    <div class="px-6 py-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {% if quotation.paymentterms %}
                            <div class="bg-slate-50 rounded-lg p-4">
                                <h3 class="text-sm font-semibold text-slate-700 mb-2">Payment Terms</h3>
                                <p class="text-sm text-slate-900">{{ quotation.paymentterms }}</p>
                            </div>
                            {% endif %}
                            
                            {% if quotation.deliveryterms %}
                            <div class="bg-slate-50 rounded-lg p-4">
                                <h3 class="text-sm font-semibold text-slate-700 mb-2">Delivery Terms</h3>
                                <p class="text-sm text-slate-900">{{ quotation.deliveryterms }}</p>
                            </div>
                            {% endif %}
                            
                            {% if quotation.validity %}
                            <div class="bg-slate-50 rounded-lg p-4">
                                <h3 class="text-sm font-semibold text-slate-700 mb-2">Validity</h3>
                                <p class="text-sm text-slate-900">{{ quotation.validity }}</p>
                            </div>
                            {% endif %}
                            
                            {% if quotation.warrenty %}
                            <div class="bg-slate-50 rounded-lg p-4">
                                <h3 class="text-sm font-semibold text-slate-700 mb-2">Warranty</h3>
                                <p class="text-sm text-slate-900">{{ quotation.warrenty }}</p>
                            </div>
                            {% endif %}
                        </div>
                        
                        {% if quotation.remarks %}
                        <div class="mt-6 bg-blue-50 rounded-lg p-4">
                            <h3 class="text-sm font-semibold text-blue-700 mb-2">Remarks</h3>
                            <p class="text-sm text-blue-900">{{ quotation.remarks }}</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Sidebar -->
            <div class="space-y-8">
                
                <!-- Quotation Info -->
                <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 overflow-hidden">
                    <div class="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-slate-200/60">
                        <h2 class="text-lg font-bold text-slate-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            Quotation Details
                        </h2>
                    </div>
                    <div class="px-6 py-6 space-y-4">
                        <div class="flex justify-between items-center py-2 border-b border-slate-100">
                            <span class="text-sm font-medium text-slate-600">Quotation No.</span>
                            <span class="text-sm text-slate-900 font-semibold">{{ quotation.quotationno }}</span>
                        </div>
                        
                        <div class="flex justify-between items-center py-2 border-b border-slate-100">
                            <span class="text-sm font-medium text-slate-600">Date</span>
                            <span class="text-sm text-slate-900">{{ quotation.sysdate }}</span>
                        </div>
                        
                        {% if quotation.duedate %}
                        <div class="flex justify-between items-center py-2 border-b border-slate-100">
                            <span class="text-sm font-medium text-slate-600">Due Date</span>
                            <span class="text-sm text-slate-900">{{ quotation.duedate }}</span>
                        </div>
                        {% endif %}
                        
                        <div class="flex justify-between items-center py-2 border-b border-slate-100">
                            <span class="text-sm font-medium text-slate-600">Customer</span>
                            <span class="text-sm text-slate-900 font-medium">{{ quotation.customerid }}</span>
                        </div>
                        
                        {% if quotation.enqid %}
                        <div class="flex justify-between items-center py-2 border-b border-slate-100">
                            <span class="text-sm font-medium text-slate-600">Enquiry</span>
                            <a href="{% url 'sales_distribution:enquiry_detail' quotation.enqid.enqid %}" 
                               class="text-sm text-blue-600 hover:text-blue-700 font-medium">
                                ENQ-{{ quotation.enqid.enqid }}
                            </a>
                        </div>
                        {% endif %}
                        
                        {% if quotation.transport %}
                        <div class="flex justify-between items-center py-2">
                            <span class="text-sm font-medium text-slate-600">Transport</span>
                            <span class="text-sm text-slate-900">{{ quotation.transport }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Workflow Status -->
                <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 overflow-hidden">
                    <div class="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-slate-200/60">
                        <h2 class="text-lg font-bold text-slate-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            Workflow Status
                        </h2>
                    </div>
                    <div class="px-6 py-6 space-y-4">
                        <!-- Checked Status -->
                        <div class="flex items-center justify-between py-2">
                            <div class="flex items-center space-x-2">
                                {% if quotation.checked == 1 %}
                                <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                                {% else %}
                                <svg class="w-5 h-5 text-slate-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12z" clip-rule="evenodd"/>
                                </svg>
                                {% endif %}
                                <span class="text-sm font-medium text-slate-700">Checked</span>
                            </div>
                            {% if quotation.checked == 1 %}
                            <div class="text-xs text-slate-500">
                                <div>{{ quotation.checkedby }}</div>
                                <div>{{ quotation.checkeddate }}</div>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Approved Status -->
                        <div class="flex items-center justify-between py-2">
                            <div class="flex items-center space-x-2">
                                {% if quotation.approve == 1 %}
                                <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                                {% else %}
                                <svg class="w-5 h-5 text-slate-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12z" clip-rule="evenodd"/>
                                </svg>
                                {% endif %}
                                <span class="text-sm font-medium text-slate-700">Approved</span>
                            </div>
                            {% if quotation.approve == 1 %}
                            <div class="text-xs text-slate-500">
                                <div>{{ quotation.approvedby }}</div>
                                <div>{{ quotation.approvedate }}</div>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Authorized Status -->
                        <div class="flex items-center justify-between py-2">
                            <div class="flex items-center space-x-2">
                                {% if quotation.authorize == 1 %}
                                <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                                {% else %}
                                <svg class="w-5 h-5 text-slate-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12z" clip-rule="evenodd"/>
                                </svg>
                                {% endif %}
                                <span class="text-sm font-medium text-slate-700">Authorized</span>
                            </div>
                            {% if quotation.authorize == 1 %}
                            <div class="text-xs text-slate-500">
                                <div>{{ quotation.authorizedby }}</div>
                                <div>{{ quotation.authorizedate }}</div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 overflow-hidden">
                    <div class="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-slate-200/60">
                        <h2 class="text-lg font-bold text-slate-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                            Quick Actions
                        </h2>
                    </div>
                    <div class="px-6 py-6 space-y-3">
                        <!-- Edit Action -->
                        <a href="{% url 'sales_distribution:quotation_edit' quotation.id %}" 
                           class="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                            </svg>
                            Edit Quotation
                        </a>

                        <!-- Workflow Actions -->
                        {% if quotation.checked != 1 %}
                        <form method="post" action="{% url 'sales_distribution:quotation_check' quotation.id %}" class="w-full">
                            {% csrf_token %}
                            <button type="submit" 
                                    class="w-full inline-flex items-center justify-center px-4 py-2 bg-green-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                                Check Quotation
                            </button>
                        </form>
                        {% elif quotation.checked == 1 and quotation.approve != 1 %}
                        <form method="post" action="{% url 'sales_distribution:quotation_approve' quotation.id %}" class="w-full">
                            {% csrf_token %}
                            <button type="submit"
                                    class="w-full inline-flex items-center justify-center px-4 py-2 bg-purple-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                Approve Quotation
                            </button>
                        </form>
                        {% elif quotation.approve == 1 and quotation.authorize != 1 %}
                        <form method="post" action="{% url 'sales_distribution:quotation_authorize' quotation.id %}" class="w-full">
                            {% csrf_token %}
                            <button type="submit"
                                    class="w-full inline-flex items-center justify-center px-4 py-2 bg-indigo-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                                </svg>
                                Authorize Quotation
                            </button>
                        </form>
                        {% endif %}

                        <!-- Back to List -->
                        <a href="{% url 'sales_distribution:quotation_list' %}" 
                           class="w-full inline-flex items-center justify-center px-4 py-2 bg-slate-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                            </svg>
                            Back to Quotations
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
