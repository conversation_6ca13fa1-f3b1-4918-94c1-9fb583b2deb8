<!-- accounts/templates/accounts/excise_duty_list.html -->
<!-- Excise Duty List Template - Replaces ASP.NET Excise.aspx -->
<!-- Task Group 4: Taxation Management - Excise Duty Templates -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Excise/Service Tax Management{% endblock %}

{% block extra_css %}
<style>
    .tax-rate-badge {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.875rem;
        font-weight: 600;
    }
    
    .default-badge {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 0.5rem;
        font-size: 0.75rem;
        font-weight: 600;
    }
    
    .calculation-card {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        border-radius: 1rem;
        padding: 1.5rem;
        margin: 1rem 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-6 py-8">
    <!-- Header Section -->
    <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Excise/Service Tax Management</h1>
                <p class="mt-2 text-gray-600">Manage excise duties and service tax rates with comprehensive calculation support</p>
            </div>
            <div class="mt-4 sm:mt-0 flex space-x-3">
                <a href="{% url 'accounts:excise_duty_quick_create' %}" 
                   class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    Quick Create
                </a>
                <a href="{% url 'accounts:excise_duty_create' %}" 
                   class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    Create New
                </a>
                <a href="{% url 'accounts:excise_calculator' %}" 
                   class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    Tax Calculator
                </a>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    {% if summary %}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow-lg rounded-lg border-l-4 border-blue-500">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-calculator text-blue-600"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Duties</dt>
                            <dd class="text-lg font-semibold text-gray-900">{{ summary.total_duties }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-lg border-l-4 border-green-500">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-check-circle text-green-600"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Active Duties</dt>
                            <dd class="text-lg font-semibold text-gray-900">{{ summary.active_duties }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-lg border-l-4 border-yellow-500">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-percentage text-yellow-600"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Average Rate</dt>
                            <dd class="text-lg font-semibold text-gray-900">{{ summary.avg_rate|floatformat:2 }}%</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-lg border-l-4 border-purple-500">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-star text-purple-600"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Default Excise</dt>
                            <dd class="text-sm font-semibold text-gray-900">
                                {% if summary.default_excise %}
                                    {{ summary.default_excise.terms|truncatechars:20 }}
                                {% else %}
                                    Not Set
                                {% endif %}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Search and Filter Section -->
    <div class="bg-white shadow-lg rounded-lg p-6 mb-8">
        <form method="get" class="space-y-4" hx-get="{% url 'accounts:excise_duty_list' %}" hx-target="#excise-duties-table" hx-trigger="input delay:500ms, change">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                    <input type="text" name="search" id="search" value="{{ request.GET.search }}"
                           placeholder="Search by terms, rate..."
                           class="block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label for="duty_type" class="block text-sm font-medium text-gray-700 mb-2">Type Filter</label>
                    <select name="duty_type" id="duty_type"
                            class="block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Types</option>
                        <option value="excise" {% if request.GET.duty_type == 'excise' %}selected{% endif %}>Excise Duties</option>
                        <option value="service_tax" {% if request.GET.duty_type == 'service_tax' %}selected{% endif %}>Service Taxes</option>
                        <option value="both" {% if request.GET.duty_type == 'both' %}selected{% endif %}>Both Defaults</option>
                    </select>
                </div>
                
                <div>
                    <label for="rate_range" class="block text-sm font-medium text-gray-700 mb-2">Rate Range</label>
                    <select name="rate_range" id="rate_range"
                            class="block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Rates</option>
                        <option value="0-5" {% if request.GET.rate_range == '0-5' %}selected{% endif %}>0-5%</option>
                        <option value="5-10" {% if request.GET.rate_range == '5-10' %}selected{% endif %}>5-10%</option>
                        <option value="10-15" {% if request.GET.rate_range == '10-15' %}selected{% endif %}>10-15%</option>
                        <option value="15-20" {% if request.GET.rate_range == '15-20' %}selected{% endif %}>15-20%</option>
                        <option value="20+" {% if request.GET.rate_range == '20+' %}selected{% endif %}>20%+</option>
                    </select>
                </div>
                
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select name="status" id="status"
                            class="block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Status</option>
                        <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>Active</option>
                        <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>Inactive</option>
                    </select>
                </div>
            </div>
        </form>
    </div>

    <!-- Excise Duties Table -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden" id="excise-duties-table">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">
                    Excise/Service Tax Rates
                    {% if excise_duties %}
                        <span class="text-sm text-gray-500 ml-2">({{ excise_duties|length }} of {{ page_obj.paginator.count }} records)</span>
                    {% endif %}
                </h3>
                <div class="flex space-x-2">
                    <a href="{% url 'accounts:excise_duty_export' %}?{{ request.GET.urlencode }}" 
                       class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                        <i class="fas fa-download mr-1"></i>Export CSV
                    </a>
                    <a href="{% url 'accounts:excise_duty_report' %}" 
                       class="text-purple-600 hover:text-purple-900 text-sm font-medium">
                        <i class="fas fa-chart-bar mr-1"></i>Analytics
                    </a>
                </div>
            </div>
        </div>

        {% if excise_duties %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input type="checkbox" id="select-all" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Terms
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Basic Rate
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Accessible Value
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            EDU Cess
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            SHE Cess
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Total Rate
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Defaults
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for excise_duty in excise_duties %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" name="selected_duties" value="{{ excise_duty.id }}" 
                                   class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded duty-checkbox">
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-gray-900">{{ excise_duty.terms }}</div>
                            <div class="text-xs text-gray-500">ID: {{ excise_duty.id }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="tax-rate-badge">{{ excise_duty.value|floatformat:3 }}%</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ excise_duty.accessible_value|floatformat:3 }}%
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ excise_duty.edu_cess|floatformat:3 }}%
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ excise_duty.she_cess|floatformat:3 }}%
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ excise_duty.total_excise_rate|floatformat:3 }}%
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex flex-col space-y-1">
                                {% if excise_duty.is_default_excise %}
                                    <span class="default-badge">Default Excise</span>
                                {% endif %}
                                {% if excise_duty.is_default_service_tax %}
                                    <span class="default-badge">Default Service Tax</span>
                                {% endif %}
                                {% if not excise_duty.is_default_excise and not excise_duty.is_default_service_tax %}
                                    <span class="text-xs text-gray-500">-</span>
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if excise_duty.is_active %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Active
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    Inactive
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-3">
                                <a href="{% url 'accounts:excise_duty_edit' excise_duty.pk %}" 
                                   class="text-indigo-600 hover:text-indigo-900" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button onclick="calculateTaxExample({{ excise_duty.id }}, '{{ excise_duty.terms }}')"
                                        class="text-green-600 hover:text-green-900" title="Calculate">
                                    <i class="fas fa-calculator"></i>
                                </button>
                                <a href="{% url 'accounts:excise_duty_delete' excise_duty.pk %}" 
                                   class="text-red-600 hover:text-red-900" title="Delete"
                                   onclick="return confirm('Are you sure you want to delete this excise duty?')">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Bulk Operations -->
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-700">Bulk Operations:</span>
                    <form method="post" action="{% url 'accounts:excise_bulk_operations' %}" class="flex items-center space-x-2">
                        {% csrf_token %}
                        <input type="hidden" name="selected_duties" id="bulk-selected-duties">
                        <select name="action" class="px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <option value="">Select Action</option>
                            <option value="activate">Activate Selected</option>
                            <option value="deactivate">Deactivate Selected</option>
                            <option value="clear_defaults">Clear Default Flags</option>
                        </select>
                        <button type="submit" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            Apply
                        </button>
                    </form>
                </div>
                <div class="text-sm text-gray-500">
                    <span id="selected-count">0</span> selected
                </div>
            </div>
        </div>

        <!-- Pagination -->
        {% if page_obj %}
        <div class="px-6 py-4 bg-white border-t border-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} results
                </div>
                <div class="flex space-x-2">
                    {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}&{{ request.GET.urlencode }}"
                           class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                            Previous
                        </a>
                    {% endif %}
                    
                    <span class="px-3 py-2 text-sm text-gray-700">
                        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                    </span>
                    
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}&{{ request.GET.urlencode }}"
                           class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                            Next
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}

        {% else %}
        <div class="text-center py-12">
            <i class="fas fa-calculator text-gray-300 text-6xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No excise duties found</h3>
            <p class="text-gray-500 mb-6">Get started by creating your first excise duty or service tax rate.</p>
            <div class="flex justify-center space-x-4">
                <a href="{% url 'accounts:excise_duty_quick_create' %}" 
                   class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-medium">
                    Quick Create
                </a>
                <a href="{% url 'accounts:excise_duty_create' %}" 
                   class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium">
                    Create New
                </a>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Tax Calculation Modal -->
<div id="tax-calculation-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-bold text-gray-900 mb-4" id="modal-title">Tax Calculation</h3>
            <div class="mb-4">
                <label for="calculation-amount" class="block text-sm font-medium text-gray-700 mb-2">Base Amount</label>
                <input type="number" id="calculation-amount" step="0.01" min="0" value="10000"
                       class="block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div id="calculation-result" class="calculation-card hidden">
                <!-- Results will be populated here -->
            </div>
            <div class="flex justify-end space-x-3 mt-6">
                <button onclick="closeCalculationModal()" 
                        class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-lg font-medium">
                    Close
                </button>
                <button onclick="performCalculation()" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium">
                    Calculate
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentExciseDutyId = null;

// Bulk operations
document.getElementById('select-all').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.duty-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateSelectedCount();
});

document.querySelectorAll('.duty-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updateSelectedCount);
});

function updateSelectedCount() {
    const selectedCheckboxes = document.querySelectorAll('.duty-checkbox:checked');
    const count = selectedCheckboxes.length;
    document.getElementById('selected-count').textContent = count;
    
    // Update bulk operations form
    const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.value);
    document.getElementById('bulk-selected-duties').value = selectedIds.join(',');
}

// Tax calculation modal
function calculateTaxExample(exciseDutyId, terms) {
    currentExciseDutyId = exciseDutyId;
    document.getElementById('modal-title').textContent = `Tax Calculation - ${terms}`;
    document.getElementById('tax-calculation-modal').classList.remove('hidden');
    
    // Trigger initial calculation
    performCalculation();
}

function closeCalculationModal() {
    document.getElementById('tax-calculation-modal').classList.add('hidden');
    currentExciseDutyId = null;
}

function performCalculation() {
    if (!currentExciseDutyId) return;
    
    const amount = document.getElementById('calculation-amount').value;
    if (!amount || amount <= 0) return;
    
    fetch(`{% url 'accounts:ajax_calculate_excise' %}?duty_id=${currentExciseDutyId}&amount=${amount}`)
        .then(response => response.json())
        .then(data => {
            if (data.calculation) {
                displayCalculationResult(data.calculation, data.duty_info);
            } else {
                alert('Error calculating tax: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error calculating tax');
        });
}

function displayCalculationResult(calculation, dutyInfo) {
    const resultDiv = document.getElementById('calculation-result');
    
    resultDiv.innerHTML = `
        <h4 class="text-lg font-semibold mb-3">Calculation Result</h4>
        <div class="space-y-2">
            <div class="flex justify-between">
                <span>Accessible Amount:</span>
                <span class="font-medium">₹${calculation.accessible_amount.toLocaleString('en-IN', {minimumFractionDigits: 2})}</span>
            </div>
            <div class="flex justify-between">
                <span>Basic Excise:</span>
                <span class="font-medium">₹${calculation.basic_excise.toLocaleString('en-IN', {minimumFractionDigits: 2})}</span>
            </div>
            <div class="flex justify-between">
                <span>Education Cess:</span>
                <span class="font-medium">₹${calculation.edu_cess.toLocaleString('en-IN', {minimumFractionDigits: 2})}</span>
            </div>
            <div class="flex justify-between">
                <span>SHE Cess:</span>
                <span class="font-medium">₹${calculation.she_cess.toLocaleString('en-IN', {minimumFractionDigits: 2})}</span>
            </div>
            <hr class="my-3">
            <div class="flex justify-between text-lg font-bold">
                <span>Total Excise:</span>
                <span class="text-green-600">₹${calculation.total_excise.toLocaleString('en-IN', {minimumFractionDigits: 2})}</span>
            </div>
            <div class="text-sm text-gray-600 mt-2">
                Effective Rate: ${dutyInfo.effective_rate.toFixed(3)}%
            </div>
        </div>
    `;
    
    resultDiv.classList.remove('hidden');
}

// Real-time calculation on amount change
document.getElementById('calculation-amount').addEventListener('input', function() {
    if (currentExciseDutyId) {
        clearTimeout(this.calculationTimer);
        this.calculationTimer = setTimeout(performCalculation, 500);
    }
});
</script>
{% endblock %}