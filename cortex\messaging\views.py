from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.views.generic import ListView, CreateView, UpdateView, DetailView
from django.urls import reverse_lazy
from django.http import JsonResponse
from django.db.models import Q, Count, Max
from django.utils import timezone
from django.contrib.auth.models import User
from .models import (
    ChatRoom, ChatMessage, ChatUser, RoomMembership, 
    PrivateMessageInvitation
)
from .forms import (
    ChatRoomForm, ChatMessageForm, PrivateMessageForm, 
    ChatUserForm, MessageSearchForm
)
import datetime


# Dashboard Views
class MessagingDashboardView(LoginRequiredMixin, ListView):
    """Main dashboard for messaging module."""
    template_name = 'messaging/dashboard.html'
    context_object_name = 'recent_rooms'
    paginate_by = 5

    def get_queryset(self):
        """Get user's recent chat rooms."""
        return RoomMembership.objects.filter(
            user=self.request.user,
            is_active=True
        ).select_related('room').order_by('-last_activity')[:5]

    def get_context_data(self, **kwargs):
        """Add dashboard statistics."""
        context = super().get_context_data(**kwargs)
        
        # Get statistics
        context['total_rooms'] = ChatRoom.objects.filter(is_active=True).count()
        context['user_rooms_count'] = RoomMembership.objects.filter(
            user=self.request.user, is_active=True
        ).count()
        context['unread_messages'] = self.get_unread_messages_count()
        context['online_users'] = self.get_online_users_count()
        
        # Recent messages
        context['recent_messages'] = ChatMessage.objects.filter(
            room__logged_users__user=self.request.user,
            room__logged_users__is_active=True
        ).select_related('sender', 'room').order_by('-timestamp')[:5]
        
        # Private message invitations
        context['pending_invitations'] = PrivateMessageInvitation.objects.filter(
            to_user=self.request.user,
            is_accepted=False
        ).count()
        
        return context

    def get_unread_messages_count(self):
        """Get count of unread messages for user."""
        # This is a simplified version - in real implementation,
        # you'd track read/unread status per user
        user_rooms = RoomMembership.objects.filter(
            user=self.request.user, is_active=True
        ).values_list('room_id', flat=True)
        
        return ChatMessage.objects.filter(
            room_id__in=user_rooms,
            timestamp__gt=timezone.now() - datetime.timedelta(hours=24)
        ).exclude(sender=self.request.user).count()

    def get_online_users_count(self):
        """Get count of currently online users."""
        return ChatUser.objects.filter(is_online=True).count()


# Chat Room Management Views
class ChatRoomListView(LoginRequiredMixin, ListView):
    """List view for chat rooms."""
    model = ChatRoom
    template_name = 'messaging/room_list.html'
    context_object_name = 'rooms'
    paginate_by = 20

    def get_queryset(self):
        """Get active chat rooms with user membership info."""
        return ChatRoom.objects.filter(is_active=True).annotate(
            member_count=Count('logged_users', filter=Q(logged_users__is_active=True))
        ).order_by('-created_at')

    def get_context_data(self, **kwargs):
        """Add user membership info."""
        context = super().get_context_data(**kwargs)
        
        # Add membership info for each room
        user_memberships = RoomMembership.objects.filter(
            user=self.request.user,
            is_active=True
        ).values_list('room_id', flat=True)
        
        for room in context['rooms']:
            room.is_member = room.id in user_memberships
            room.can_join = room.member_count < room.max_users and not room.is_member
        
        return context


class ChatRoomCreateView(LoginRequiredMixin, CreateView):
    """Create view for chat rooms."""
    model = ChatRoom
    form_class = ChatRoomForm
    template_name = 'messaging/room_form.html'
    success_url = reverse_lazy('messaging:room_list')

    def form_valid(self, form):
        """Save room with creator info."""
        form.instance.created_by = self.request.user
        response = super().form_valid(form)
        
        # Automatically join the creator to the room
        RoomMembership.objects.create(
            room=form.instance,
            user=self.request.user,
            is_active=True
        )
        
        messages.success(self.request, f'Chat room "{form.instance.name}" created successfully!')
        return response


class ChatRoomDetailView(LoginRequiredMixin, DetailView):
    """Detail view for chat rooms with messaging."""
    model = ChatRoom
    template_name = 'messaging/room_detail.html'
    context_object_name = 'room'

    def get_queryset(self):
        """Only show active rooms."""
        return ChatRoom.objects.filter(is_active=True)

    def get_context_data(self, **kwargs):
        """Add messages and membership info."""
        context = super().get_context_data(**kwargs)
        room = context['room']
        
        # Check if user is a member
        try:
            membership = RoomMembership.objects.get(
                room=room,
                user=self.request.user,
                is_active=True
            )
            context['is_member'] = True
            
            # Update last activity
            membership.last_activity = timezone.now()
            membership.save()
            
        except RoomMembership.DoesNotExist:
            context['is_member'] = False
        
        if context['is_member']:
            # Get recent messages
            context['messages'] = ChatMessage.objects.filter(
                room=room
            ).select_related('sender').order_by('-timestamp')[:50]
            
            # Get online members
            context['online_members'] = RoomMembership.objects.filter(
                room=room,
                is_active=True,
                user__chatuser__is_online=True
            ).select_related('user')
            
            # Message form
            context['message_form'] = ChatMessageForm()
        
        # Room stats
        context['total_members'] = room.logged_users.filter(is_active=True).count()
        context['can_join'] = (
            not context['is_member'] and 
            context['total_members'] < room.max_users
        )
        
        return context

    def post(self, request, *args, **kwargs):
        """Handle message posting."""
        room = self.get_object()
        
        # Check if user is a member
        if not RoomMembership.objects.filter(
            room=room,
            user=request.user,
            is_active=True
        ).exists():
            messages.error(request, "You must be a member to send messages.")
            return redirect('messaging:room_detail', pk=room.pk)
        
        form = ChatMessageForm(request.POST)
        if form.is_valid():
            message = form.save(commit=False)
            message.room = room
            message.sender = request.user
            message.is_private = False
            message.save()
            
            return redirect('messaging:room_detail', pk=room.pk)
        
        # If form is invalid, reload the page with form errors
        context = self.get_context_data()
        context['message_form'] = form
        return self.render_to_response(context)


# Room Membership Management
@login_required
def join_room(request, pk):
    """Join a chat room."""
    room = get_object_or_404(ChatRoom, pk=pk, is_active=True)
    
    # Check if already a member
    membership, created = RoomMembership.objects.get_or_create(
        room=room,
        user=request.user,
        defaults={'is_active': True}
    )
    
    if not created:
        if membership.is_active:
            messages.info(request, f'You are already a member of "{room.name}".')
        else:
            membership.is_active = True
            membership.save()
            messages.success(request, f'Rejoined "{room.name}" successfully!')
    else:
        # Check room capacity
        current_members = room.logged_users.filter(is_active=True).count()
        if current_members >= room.max_users:
            membership.delete()
            messages.error(request, f'Room "{room.name}" is full.')
            return redirect('messaging:room_list')
        
        messages.success(request, f'Joined "{room.name}" successfully!')
    
    return redirect('messaging:room_detail', pk=room.pk)


@login_required
def leave_room(request, pk):
    """Leave a chat room."""
    room = get_object_or_404(ChatRoom, pk=pk)
    
    try:
        membership = RoomMembership.objects.get(
            room=room,
            user=request.user,
            is_active=True
        )
        membership.is_active = False
        membership.save()
        messages.success(request, f'Left "{room.name}" successfully!')
    except RoomMembership.DoesNotExist:
        messages.info(request, f'You are not a member of "{room.name}".')
    
    return redirect('messaging:room_list')


# Private Messaging Views
class PrivateMessageListView(LoginRequiredMixin, ListView):
    """List view for private message conversations."""
    template_name = 'messaging/private_list.html'
    context_object_name = 'conversations'
    paginate_by = 20

    def get_queryset(self):
        """Get private message conversations for the user."""
        # Get users who have exchanged private messages with current user
        user_conversations = ChatMessage.objects.filter(
            Q(sender=self.request.user, is_private=True) |
            Q(recipient=self.request.user, is_private=True)
        ).values(
            'sender', 'recipient'
        ).annotate(
            last_message_time=Max('timestamp')
        ).order_by('-last_message_time')
        
        # Extract unique conversation partners
        conversation_partners = set()
        for conv in user_conversations:
            if conv['sender'] != self.request.user.id:
                conversation_partners.add(conv['sender'])
            if conv['recipient'] and conv['recipient'] != self.request.user.id:
                conversation_partners.add(conv['recipient'])
        
        return User.objects.filter(id__in=conversation_partners)

    def get_context_data(self, **kwargs):
        """Add conversation details."""
        context = super().get_context_data(**kwargs)
        
        # Add last message info for each conversation
        for user in context['conversations']:
            last_message = ChatMessage.objects.filter(
                Q(sender=self.request.user, recipient=user, is_private=True) |
                Q(sender=user, recipient=self.request.user, is_private=True)
            ).order_by('-timestamp').first()
            
            user.last_message = last_message
        
        # Pending invitations
        context['pending_invitations'] = PrivateMessageInvitation.objects.filter(
            to_user=self.request.user,
            is_accepted=False
        ).select_related('from_user')
        
        return context


class PrivateMessageDetailView(LoginRequiredMixin, DetailView):
    """Detail view for private message conversation."""
    model = User
    template_name = 'messaging/private_detail.html'
    context_object_name = 'recipient'

    def get_context_data(self, **kwargs):
        """Add conversation messages."""
        context = super().get_context_data(**kwargs)
        recipient = context['recipient']
        
        # Get conversation messages
        context['messages'] = ChatMessage.objects.filter(
            Q(sender=self.request.user, recipient=recipient, is_private=True) |
            Q(sender=recipient, recipient=self.request.user, is_private=True)
        ).order_by('-timestamp')[:50]
        
        # Message form
        context['message_form'] = ChatMessageForm()
        
        return context

    def post(self, request, *args, **kwargs):
        """Handle private message sending."""
        recipient = self.get_object()
        
        if recipient == request.user:
            messages.error(request, "You cannot send messages to yourself.")
            return redirect('messaging:private_list')
        
        form = ChatMessageForm(request.POST)
        if form.is_valid():
            message = form.save(commit=False)
            message.sender = request.user
            message.recipient = recipient
            message.is_private = True
            message.save()
            
            # Create invitation if this is first message
            PrivateMessageInvitation.objects.get_or_create(
                from_user=request.user,
                to_user=recipient,
                defaults={'is_accepted': True}
            )
            
            return redirect('messaging:private_detail', pk=recipient.pk)
        
        # If form is invalid, reload the page with form errors
        context = self.get_context_data()
        context['message_form'] = form
        return self.render_to_response(context)


@login_required
def start_private_chat(request):
    """Start a new private chat."""
    if request.method == 'POST':
        form = PrivateMessageForm(request.user, request.POST)
        if form.is_valid():
            recipient = form.cleaned_data['recipient']
            message_text = form.cleaned_data['message']
            
            # Create the message
            ChatMessage.objects.create(
                sender=request.user,
                recipient=recipient,
                text=message_text,
                is_private=True
            )
            
            # Create invitation
            PrivateMessageInvitation.objects.get_or_create(
                from_user=request.user,
                to_user=recipient,
                defaults={'is_accepted': True}
            )
            
            messages.success(request, f'Private message sent to {recipient.username}!')
            return redirect('messaging:private_detail', pk=recipient.pk)
    else:
        form = PrivateMessageForm(request.user)
    
    return render(request, 'messaging/start_private_chat.html', {
        'form': form
    })


# User Profile Management
class ChatUserUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for chat user profile."""
    model = ChatUser
    form_class = ChatUserForm
    template_name = 'messaging/profile_form.html'
    success_url = reverse_lazy('messaging:dashboard')

    def get_object(self):
        """Get or create chat user profile."""
        chat_user, created = ChatUser.objects.get_or_create(
            user=self.request.user,
            defaults={
                'employee_name': self.request.user.get_full_name() or self.request.user.username,
                'emp_id': self.request.user.username,
                'gender': 'M'
            }
        )
        return chat_user

    def form_valid(self, form):
        """Update profile with success message."""
        response = super().form_valid(form)
        messages.success(self.request, 'Profile updated successfully!')
        return response


# HTMX Views for Real-time Updates
@login_required
def load_room_messages(request, room_id):
    """HTMX endpoint to load room messages."""
    room = get_object_or_404(ChatRoom, pk=room_id, is_active=True)
    
    # Check if user is a member
    if not RoomMembership.objects.filter(
        room=room,
        user=request.user,
        is_active=True
    ).exists():
        return JsonResponse({'error': 'Not authorized'}, status=403)
    
    # Get messages with optional timestamp filter for real-time updates
    last_timestamp = request.GET.get('last_timestamp')
    messages_queryset = ChatMessage.objects.filter(room=room)
    
    if last_timestamp:
        try:
            from django.utils.dateparse import parse_datetime
            timestamp = parse_datetime(last_timestamp)
            if timestamp:
                messages_queryset = messages_queryset.filter(timestamp__gt=timestamp)
        except:
            pass
    
    messages_list = messages_queryset.select_related('sender').order_by('-timestamp')[:20]
    
    return render(request, 'messaging/partials/message_list.html', {
        'messages': messages_list,
        'room': room
    })


@login_required
def load_private_messages(request, user_id):
    """HTMX endpoint to load private messages."""
    other_user = get_object_or_404(User, pk=user_id)
    
    # Get messages between the two users
    last_timestamp = request.GET.get('last_timestamp')
    messages_queryset = ChatMessage.objects.filter(
        Q(sender=request.user, recipient=other_user, is_private=True) |
        Q(sender=other_user, recipient=request.user, is_private=True)
    )
    
    if last_timestamp:
        try:
            from django.utils.dateparse import parse_datetime
            timestamp = parse_datetime(last_timestamp)
            if timestamp:
                messages_queryset = messages_queryset.filter(timestamp__gt=timestamp)
        except:
            pass
    
    messages_list = messages_queryset.select_related('sender').order_by('-timestamp')[:20]
    
    return render(request, 'messaging/partials/private_message_list.html', {
        'messages': messages_list,
        'other_user': other_user
    })


@login_required
def online_users(request):
    """HTMX endpoint to get online users."""
    online_users = ChatUser.objects.filter(
        is_online=True
    ).select_related('user')
    
    return render(request, 'messaging/partials/online_users.html', {
        'online_users': online_users
    })


@login_required
def search_messages(request):
    """HTMX endpoint for message search."""
    form = MessageSearchForm(request.GET)
    messages_list = ChatMessage.objects.none()
    
    if form.is_valid():
        # Build search query
        search_text = form.cleaned_data.get('search_text')
        date_from = form.cleaned_data.get('date_from')
        date_to = form.cleaned_data.get('date_to')
        sender = form.cleaned_data.get('sender')
        
        # Get user's accessible messages (from rooms they're in or private messages)
        user_rooms = RoomMembership.objects.filter(
            user=request.user,
            is_active=True
        ).values_list('room_id', flat=True)
        
        messages_queryset = ChatMessage.objects.filter(
            Q(room_id__in=user_rooms) |
            Q(sender=request.user, is_private=True) |
            Q(recipient=request.user, is_private=True)
        )
        
        # Apply filters
        if search_text:
            messages_queryset = messages_queryset.filter(text__icontains=search_text)
        
        if date_from:
            messages_queryset = messages_queryset.filter(timestamp__date__gte=date_from)
        
        if date_to:
            messages_queryset = messages_queryset.filter(timestamp__date__lte=date_to)
        
        if sender:
            messages_queryset = messages_queryset.filter(sender=sender)
        
        messages_list = messages_queryset.select_related('sender', 'room').order_by('-timestamp')[:50]
    
    return render(request, 'messaging/partials/search_results.html', {
        'messages': messages_list,
        'search_form': form
    })


@login_required
def messaging_stats(request):
    """HTMX endpoint for messaging statistics."""
    total_rooms = ChatRoom.objects.filter(is_active=True).count()
    user_rooms_count = RoomMembership.objects.filter(
        user=request.user, is_active=True
    ).count()
    online_users = ChatUser.objects.filter(is_online=True).count()
    
    return JsonResponse({
        'total_rooms': total_rooms,
        'user_rooms_count': user_rooms_count,
        'online_users': online_users,
    })