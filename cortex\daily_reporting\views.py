from django.shortcuts import render
from django.urls import reverse_lazy
from django.views.generic import (
    ListView, CreateView, UpdateView, DeleteView, DetailView
)
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.db.models import Q, Count
from django.utils import timezone
from datetime import datetime, timedelta

from .models import (
    DailyReportingTracker, DesignPlan, ManufacturingPlan, VendorPlan,
    ManPowerPlanning, ProjectPlanningMainSheet
)
from .forms import (
    DailyReportingTrackerForm, DesignPlanForm, ManufacturingPlanForm,
    VendorPlanForm, ManPowerPlanningForm, DashboardFilterForm, ProjectReportFilterForm
)


class DashboardView(LoginRequiredMixin, ListView):
    """Main dashboard for Daily Reporting System"""
    template_name = 'daily_reporting/dashboard.html'
    context_object_name = 'recent_reports'
    paginate_by = 10

    def get_queryset(self):
        return DailyReportingTracker.objects.select_related().order_by('-sys_date')[:10]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Dashboard statistics
        today = timezone.now().date()
        week_ago = today - timedelta(days=7)
        
        context.update({
            'total_reports_today': DailyReportingTracker.objects.filter(
                date_of_reporting=today
            ).count(),
            'total_reports_week': DailyReportingTracker.objects.filter(
                date_of_reporting__gte=week_ago
            ).count(),
            'total_design_plans': DesignPlan.objects.count(),
            'total_manufacturing_plans': ManufacturingPlan.objects.count(),
            'total_vendor_plans': VendorPlan.objects.count(),
            'active_work_orders': DailyReportingTracker.objects.values('wo_number').distinct().count(),
            
            # Recent activities
            'recent_design_plans': DesignPlan.objects.order_by('-id')[:5],
            'recent_manufacturing_plans': ManufacturingPlan.objects.order_by('-sys_date')[:5],
            'recent_vendor_plans': VendorPlan.objects.order_by('-sys_date')[:5],
            
            # Filter form
            'filter_form': DashboardFilterForm()
        })
        
        return context


# Daily Reporting Tracker Views
class DailyReportingTrackerListView(LoginRequiredMixin, ListView):
    """List view for Daily Reporting Tracker"""
    model = DailyReportingTracker
    template_name = 'daily_reporting/tracker_list.html'
    context_object_name = 'reports'
    paginate_by = 20
    ordering = ['-sys_date']

    def get_queryset(self):
        queryset = super().get_queryset()
        search = self.request.GET.get('search', '')
        
        if search:
            queryset = queryset.filter(
                Q(employee_name__icontains=search) |
                Q(wo_number__icontains=search) |
                Q(department__icontains=search) |
                Q(activity__icontains=search)
            )
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search'] = self.request.GET.get('search', '')
        context['filter_form'] = DashboardFilterForm(self.request.GET)
        return context


class DailyReportingTrackerCreateView(LoginRequiredMixin, CreateView):
    """Create view for Daily Reporting Tracker"""
    model = DailyReportingTracker
    form_class = DailyReportingTrackerForm
    template_name = 'daily_reporting/tracker_form.html'
    success_url = reverse_lazy('daily_reporting:tracker_list')

    def form_valid(self, form):
        # Set system fields
        form.instance.comp_id = getattr(self.request.user, 'company_id', 1)
        form.instance.session_id = self.request.session.session_key
        form.instance.fin_year_id = getattr(self.request.user, 'financial_year_id', 1)
        
        messages.success(self.request, 'Daily report created successfully.')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'New Daily Report'
        return context


class DailyReportingTrackerUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for Daily Reporting Tracker"""
    model = DailyReportingTracker
    form_class = DailyReportingTrackerForm
    template_name = 'daily_reporting/tracker_form.html'
    success_url = reverse_lazy('daily_reporting:tracker_list')

    def form_valid(self, form):
        messages.success(self.request, 'Daily report updated successfully.')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Edit Daily Report'
        return context


class DailyReportingTrackerDetailView(LoginRequiredMixin, DetailView):
    """Detail view for Daily Reporting Tracker"""
    model = DailyReportingTracker
    template_name = 'daily_reporting/tracker_detail.html'
    context_object_name = 'report'


class DailyReportingTrackerDeleteView(LoginRequiredMixin, DeleteView):
    """Delete view for Daily Reporting Tracker"""
    model = DailyReportingTracker
    template_name = 'daily_reporting/tracker_confirm_delete.html'
    success_url = reverse_lazy('daily_reporting:tracker_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Daily report deleted successfully.')
        return super().delete(request, *args, **kwargs)


# Design Plan Views
class DesignPlanListView(LoginRequiredMixin, ListView):
    """List view for Design Plans"""
    model = DesignPlan
    template_name = 'daily_reporting/design_plan_list.html'
    context_object_name = 'design_plans'
    paginate_by = 20
    ordering = ['-id']

    def get_queryset(self):
        queryset = super().get_queryset()
        search = self.request.GET.get('search', '')
        
        if search:
            queryset = queryset.filter(
                Q(activities__icontains=search)
            )
        
        return queryset


class DesignPlanCreateView(LoginRequiredMixin, CreateView):
    """Create view for Design Plan"""
    model = DesignPlan
    form_class = DesignPlanForm
    template_name = 'daily_reporting/design_plan_form.html'
    success_url = reverse_lazy('daily_reporting:design_plan_list')

    def form_valid(self, form):
        messages.success(self.request, 'Design plan created successfully.')
        return super().form_valid(form)


class DesignPlanUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for Design Plan"""
    model = DesignPlan
    form_class = DesignPlanForm
    template_name = 'daily_reporting/design_plan_form.html'
    success_url = reverse_lazy('daily_reporting:design_plan_list')

    def form_valid(self, form):
        messages.success(self.request, 'Design plan updated successfully.')
        return super().form_valid(form)


class DesignPlanDetailView(LoginRequiredMixin, DetailView):
    """Detail view for Design Plan"""
    model = DesignPlan
    template_name = 'daily_reporting/design_plan_detail.html'
    context_object_name = 'design_plan'


# Manufacturing Plan Views
class ManufacturingPlanListView(LoginRequiredMixin, ListView):
    """List view for Manufacturing Plans"""
    model = ManufacturingPlan
    template_name = 'daily_reporting/manufacturing_plan_list.html'
    context_object_name = 'manufacturing_plans'
    paginate_by = 20
    ordering = ['-sys_date']

    def get_queryset(self):
        queryset = super().get_queryset()
        search = self.request.GET.get('search', '')
        
        if search:
            queryset = queryset.filter(
                Q(wo_number__icontains=search) |
                Q(item_number__icontains=search) |
                Q(description__icontains=search)
            )
        
        return queryset


class ManufacturingPlanCreateView(LoginRequiredMixin, CreateView):
    """Create view for Manufacturing Plan"""
    model = ManufacturingPlan
    form_class = ManufacturingPlanForm
    template_name = 'daily_reporting/manufacturing_plan_form.html'
    success_url = reverse_lazy('daily_reporting:manufacturing_plan_list')

    def form_valid(self, form):
        messages.success(self.request, 'Manufacturing plan created successfully.')
        return super().form_valid(form)


class ManufacturingPlanUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for Manufacturing Plan"""
    model = ManufacturingPlan
    form_class = ManufacturingPlanForm
    template_name = 'daily_reporting/manufacturing_plan_form.html'
    success_url = reverse_lazy('daily_reporting:manufacturing_plan_list')

    def form_valid(self, form):
        messages.success(self.request, 'Manufacturing plan updated successfully.')
        return super().form_valid(form)


class ManufacturingPlanDetailView(LoginRequiredMixin, DetailView):
    """Detail view for Manufacturing Plan"""
    model = ManufacturingPlan
    template_name = 'daily_reporting/manufacturing_plan_detail.html'
    context_object_name = 'manufacturing_plan'


# Vendor Plan Views
class VendorPlanListView(LoginRequiredMixin, ListView):
    """List view for Vendor Plans"""
    model = VendorPlan
    template_name = 'daily_reporting/vendor_plan_list.html'
    context_object_name = 'vendor_plans'
    paginate_by = 20
    ordering = ['-sys_date']

    def get_queryset(self):
        queryset = super().get_queryset()
        search = self.request.GET.get('search', '')
        
        if search:
            queryset = queryset.filter(
                Q(wo_number__icontains=search) |
                Q(fixture_number__icontains=search) |
                Q(serial_number__icontains=search)
            )
        
        return queryset


class VendorPlanCreateView(LoginRequiredMixin, CreateView):
    """Create view for Vendor Plan"""
    model = VendorPlan
    form_class = VendorPlanForm
    template_name = 'daily_reporting/vendor_plan_form.html'
    success_url = reverse_lazy('daily_reporting:vendor_plan_list')

    def form_valid(self, form):
        messages.success(self.request, 'Vendor plan created successfully.')
        return super().form_valid(form)


class VendorPlanUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for Vendor Plan"""
    model = VendorPlan
    form_class = VendorPlanForm
    template_name = 'daily_reporting/vendor_plan_form.html'
    success_url = reverse_lazy('daily_reporting:vendor_plan_list')

    def form_valid(self, form):
        messages.success(self.request, 'Vendor plan updated successfully.')
        return super().form_valid(form)


class VendorPlanDetailView(LoginRequiredMixin, DetailView):
    """Detail view for Vendor Plan"""
    model = VendorPlan
    template_name = 'daily_reporting/vendor_plan_detail.html'
    context_object_name = 'vendor_plan'


# ManPower Planning Views
class ManPowerPlanningListView(LoginRequiredMixin, ListView):
    """List view for ManPower Planning"""
    model = ManPowerPlanning
    template_name = 'daily_reporting/manpower_planning_list.html'
    context_object_name = 'manpower_plans'
    paginate_by = 20
    ordering = ['-sys_date']


class ManPowerPlanningCreateView(LoginRequiredMixin, CreateView):
    """Create view for ManPower Planning"""
    model = ManPowerPlanning
    form_class = ManPowerPlanningForm
    template_name = 'daily_reporting/manpower_planning_form.html'
    success_url = reverse_lazy('daily_reporting:manpower_planning_list')

    def form_valid(self, form):
        # Set system fields
        form.instance.comp_id = getattr(self.request.user, 'company_id', 1)
        form.instance.session_id = self.request.session.session_key
        form.instance.fin_year_id = getattr(self.request.user, 'financial_year_id', 1)
        form.instance.sys_date = timezone.now().strftime('%Y-%m-%d')
        form.instance.sys_time = timezone.now().strftime('%H:%M:%S')
        
        messages.success(self.request, 'ManPower planning created successfully.')
        return super().form_valid(form)


# Report Views
class ProjectReportView(LoginRequiredMixin, ListView):
    """Project report view with multiple report types"""
    template_name = 'daily_reporting/project_reports.html'
    context_object_name = 'reports'
    paginate_by = 50

    def get_queryset(self):
        report_type = self.request.GET.get('report_type', 'departmental_plan')
        
        if report_type == 'departmental_plan':
            return DailyReportingTracker.objects.filter(
                date_of_reporting__gte=timezone.now().date() - timedelta(days=30)
            ).order_by('department', '-date_of_reporting')
        
        elif report_type == 'individual_plan':
            return DailyReportingTracker.objects.filter(
                date_of_reporting__gte=timezone.now().date() - timedelta(days=7)
            ).order_by('employee_name', '-date_of_reporting')
        
        elif report_type == 'project_summary':
            return ProjectPlanningMainSheet.objects.all().order_by('-sys_date')
        
        elif report_type == 'wo_wise':
            wo_number = self.request.GET.get('work_order', '')
            queryset = DailyReportingTracker.objects.all()
            if wo_number:
                queryset = queryset.filter(wo_number__icontains=wo_number)
            return queryset.order_by('wo_number', '-date_of_reporting')
        
        return DailyReportingTracker.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['filter_form'] = ProjectReportFilterForm(self.request.GET)
        context['report_type'] = self.request.GET.get('report_type', 'departmental_plan')
        return context


# HTMX and AJAX Views
def search_reports(request):
    """HTMX search endpoint for reports"""
    search_term = request.GET.get('search', '')
    search_type = request.GET.get('search_type', 'wo_no')
    
    if not search_term:
        return JsonResponse({'results': []})
    
    queryset = DailyReportingTracker.objects.all()
    
    if search_type == 'wo_no':
        queryset = queryset.filter(wo_number__icontains=search_term)
    elif search_type == 'employee':
        queryset = queryset.filter(employee_name__icontains=search_term)
    elif search_type == 'department':
        queryset = queryset.filter(department__icontains=search_term)
    elif search_type == 'date':
        try:
            search_date = datetime.strptime(search_term, '%Y-%m-%d').date()
            queryset = queryset.filter(date_of_reporting=search_date)
        except ValueError:
            queryset = queryset.none()
    
    results = []
    for report in queryset[:10]:  # Limit to 10 results
        results.append({
            'id': report.id,
            'employee_name': report.employee_name,
            'wo_number': report.wo_number,
            'department': report.department,
            'date_of_reporting': report.date_of_reporting.strftime('%Y-%m-%d'),
            'activity': report.activity[:50] + '...' if len(report.activity) > 50 else report.activity,
            'percentage_completed': report.percentage_completed
        })
    
    if request.headers.get('HX-Request'):
        return render(request, 'daily_reporting/partials/search_results.html', {
            'results': results,
            'search_term': search_term
        })
    
    return JsonResponse({'results': results})


def get_employee_details(request):
    """HTMX endpoint to get employee details"""
    employee_name = request.GET.get('employee_name', '')
    
    # This would typically fetch from an Employee model
    # For now, return mock data
    employee_data = {
        'designation': 'Software Engineer',
        'department': 'IT Department'
    }
    
    if request.headers.get('HX-Request'):
        return render(request, 'daily_reporting/partials/employee_details.html', {
            'employee_data': employee_data
        })
    
    return JsonResponse(employee_data)


def get_work_order_activities(request):
    """HTMX endpoint to get activities for a work order"""
    wo_number = request.GET.get('wo_number', '')
    
    activities = DailyReportingTracker.objects.filter(
        wo_number=wo_number
    ).values_list('activity', flat=True).distinct()[:10]
    
    if request.headers.get('HX-Request'):
        return render(request, 'daily_reporting/partials/wo_activities.html', {
            'activities': list(activities),
            'wo_number': wo_number
        })
    
    return JsonResponse({'activities': list(activities)})


def dashboard_stats_api(request):
    """API endpoint for dashboard statistics"""
    today = timezone.now().date()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)
    
    stats = {
        'reports_today': DailyReportingTracker.objects.filter(date_of_reporting=today).count(),
        'reports_week': DailyReportingTracker.objects.filter(date_of_reporting__gte=week_ago).count(),
        'reports_month': DailyReportingTracker.objects.filter(date_of_reporting__gte=month_ago).count(),
        'design_plans': DesignPlan.objects.count(),
        'manufacturing_plans': ManufacturingPlan.objects.count(),
        'vendor_plans': VendorPlan.objects.count(),
        'active_work_orders': DailyReportingTracker.objects.values('wo_number').distinct().count(),
        
        # Department-wise stats
        'department_stats': list(
            DailyReportingTracker.objects.filter(
                date_of_reporting__gte=week_ago
            ).values('department').annotate(
                count=Count('id')
            ).order_by('-count')[:5]
        ),
        
        # Recent activity
        'recent_activity': [
            {
                'employee': report.employee_name,
                'activity': report.activity[:50] + '...' if len(report.activity) > 50 else report.activity,
                'date': report.date_of_reporting.strftime('%Y-%m-%d'),
                'percentage': report.percentage_completed
            }
            for report in DailyReportingTracker.objects.order_by('-sys_date')[:5]
        ]
    }
    
    return JsonResponse(stats)


def export_reports(request):
    """Export reports to Excel/CSV"""
    import csv
    
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="daily_reports.csv"'
    
    writer = csv.writer(response)
    writer.writerow([
        'Date', 'Employee', 'Department', 'WO Number', 'Activity', 
        'Status', 'Percentage Completed', 'Remarks'
    ])
    
    reports = DailyReportingTracker.objects.order_by('-date_of_reporting')
    
    for report in reports:
        writer.writerow([
            report.date_of_reporting,
            report.employee_name,
            report.department,
            report.wo_number,
            report.activity,
            report.status,
            report.percentage_completed,
            report.remarks
        ])
    
    return response