<!-- accounts/templates/accounts/tools/partials/tax_calculation_result.html -->
<!-- Tax Calculation Result Partial Template for HTMX Updates -->

{% if calculation_result %}
<div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
    <h3 class="text-lg font-medium text-sap-gray-800 mb-4">
        <i data-lucide="calculator" class="w-5 h-5 inline mr-2"></i>
        Calculation Result
    </h3>
    
    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div class="text-center p-4 bg-sap-blue-50 rounded-lg">
            <div class="text-2xl font-bold text-sap-blue-600 mb-1">
                ₹{{ calculation_result.base_amount|floatformat:2 }}
            </div>
            <div class="text-sm text-sap-gray-600">Base Amount</div>
        </div>
        
        <div class="text-center p-4 bg-sap-orange-50 rounded-lg">
            <div class="text-2xl font-bold text-sap-orange-600 mb-1">
                ₹{{ calculation_result.total_tax_amount|floatformat:2 }}
            </div>
            <div class="text-sm text-sap-gray-600">Total Tax</div>
        </div>
        
        <div class="text-center p-4 bg-sap-green-50 rounded-lg">
            <div class="text-2xl font-bold text-sap-green-600 mb-1">
                ₹{{ calculation_result.total_amount|floatformat:2 }}
            </div>
            <div class="text-sm text-sap-gray-600">Final Amount</div>
        </div>
    </div>
    
    <!-- Effective Rate -->
    <div class="text-center mb-6">
        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-sap-gray-100 text-sap-gray-800">
            <i data-lucide="percent" class="w-4 h-4 mr-1"></i>
            Effective Tax Rate: {{ calculation_result.effective_tax_rate|floatformat:3 }}%
        </span>
    </div>
    
    <!-- Tax Components Breakdown -->
    {% if calculation_summary.tax_components %}
    <div class="space-y-4">
        <h4 class="text-md font-medium text-sap-gray-800 border-b border-sap-gray-200 pb-2">
            Tax Components Breakdown
        </h4>
        
        {% for component in calculation_summary.tax_components %}
        <div class="border border-sap-gray-200 rounded-lg p-4">
            <div class="flex justify-between items-center mb-3">
                <h5 class="text-sm font-medium text-sap-gray-900 capitalize">
                    <i data-lucide="tag" class="w-4 h-4 inline mr-1"></i>
                    {{ component.type|title }} Tax
                </h5>
                <span class="text-lg font-bold text-sap-green-600">
                    ₹{{ component.total_amount|floatformat:2 }}
                </span>
            </div>
            
            <div class="space-y-2">
                {% for subcomponent in component.components %}
                <div class="flex justify-between items-center text-sm">
                    <span class="text-sap-gray-700">{{ subcomponent.name }}</span>
                    <div class="flex items-center space-x-3">
                        <span class="text-sap-gray-500">{{ subcomponent.rate|floatformat:3 }}%</span>
                        <span class="font-medium">₹{{ subcomponent.amount|floatformat:2 }}</span>
                    </div>
                </div>
                {% if subcomponent.is_compound %}
                <div class="text-xs text-sap-blue-600 ml-4">
                    <i data-lucide="link" class="w-3 h-3 inline mr-1"></i>
                    Calculated on {{ subcomponent.calculation_base }}
                </div>
                {% endif %}
                {% endfor %}
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    <!-- Calculation Flow -->
    {% if calculation_result.calculation_details.tax_sequence %}
    <div class="mt-6">
        <h4 class="text-md font-medium text-sap-gray-800 border-b border-sap-gray-200 pb-2 mb-4">
            <i data-lucide="workflow" class="w-4 h-4 inline mr-1"></i>
            Calculation Flow
        </h4>
        
        <div class="flex items-center space-x-2 overflow-x-auto pb-2">
            {% for step in calculation_result.calculation_details.tax_sequence %}
            <div class="flex-shrink-0 bg-sap-gray-50 rounded-lg p-3 min-w-40">
                <div class="text-sm font-medium text-sap-gray-900 capitalize">{{ step.tax_type|title }}</div>
                <div class="text-xs text-sap-gray-600 mt-1">
                    Base: ₹{{ step.base_amount|floatformat:0 }}
                </div>
                <div class="text-xs text-sap-gray-600">
                    Tax: ₹{{ step.tax_amount|floatformat:0 }}
                </div>
            </div>
            {% if not forloop.last %}
            <div class="flex-shrink-0 text-sap-gray-400">
                <i data-lucide="arrow-right" class="w-4 h-4"></i>
            </div>
            {% endif %}
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- Action Buttons -->
    <div class="flex justify-center space-x-3 mt-6 pt-6 border-t border-sap-gray-200">
        <button onclick="saveCalculation()" 
                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium text-sm transition-colors">
            <i data-lucide="save" class="w-4 h-4 inline mr-1"></i>
            Save Calculation
        </button>
        
        <button onclick="exportCalculation('pdf')" 
                class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium text-sm transition-colors">
            <i data-lucide="download" class="w-4 h-4 inline mr-1"></i>
            Export PDF
        </button>
        
        <button onclick="shareCalculation()" 
                class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-4 py-2 rounded-lg font-medium text-sm transition-colors">
            <i data-lucide="share" class="w-4 h-4 inline mr-1"></i>
            Share
        </button>
    </div>
</div>

{% elif calculation_errors %}
<div class="bg-sap-red-50 border border-sap-red-200 rounded-lg p-6">
    <div class="flex items-center mb-4">
        <i data-lucide="alert-circle" class="w-6 h-6 text-sap-red-600 mr-3"></i>
        <h3 class="text-lg font-medium text-sap-red-800">Calculation Errors</h3>
    </div>
    <ul class="space-y-2">
        {% for error in calculation_errors %}
        <li class="text-sap-red-700 text-sm">{{ error }}</li>
        {% endfor %}
    </ul>
    <div class="mt-4">
        <button onclick="clearForm()" 
                class="bg-sap-red-600 hover:bg-sap-red-700 text-white px-4 py-2 rounded-lg font-medium text-sm transition-colors">
            Clear & Retry
        </button>
    </div>
</div>

{% else %}
<div class="text-center py-8 bg-sap-gray-50 rounded-lg">
    <i data-lucide="calculator" class="w-12 h-12 text-sap-gray-300 mx-auto mb-4"></i>
    <p class="text-lg font-medium text-sap-gray-900 mb-2">Ready to Calculate</p>
    <p class="text-sm text-sap-gray-600">
        Enter your amounts and tax rates above to see detailed calculations.
    </p>
</div>
{% endif %}

<script>
// Re-initialize icons after HTMX update
lucide.createIcons();

// Function implementations
function saveCalculation() {
    // Implementation for saving calculation
    const calculation = {
        timestamp: new Date().toISOString(),
        base_amount: {{ calculation_result.base_amount|default:0 }},
        total_tax: {{ calculation_result.total_tax_amount|default:0 }},
        total_amount: {{ calculation_result.total_amount|default:0 }},
        effective_rate: {{ calculation_result.effective_tax_rate|default:0 }}
    };
    
    let saved = JSON.parse(localStorage.getItem('saved_tax_calculations') || '[]');
    saved.unshift(calculation);
    saved = saved.slice(0, 20); // Keep last 20
    localStorage.setItem('saved_tax_calculations', JSON.stringify(saved));
    
    alert('Calculation saved successfully!');
}

function exportCalculation(format) {
    // Implementation for exporting calculation
    if (format === 'pdf') {
        window.print();
    }
}

function shareCalculation() {
    // Implementation for sharing calculation
    if (navigator.share) {
        navigator.share({
            title: 'Tax Calculation Result',
            text: `Tax calculation: ₹{{ calculation_result.base_amount|default:0 }} + ₹{{ calculation_result.total_tax_amount|default:0 }} = ₹{{ calculation_result.total_amount|default:0 }}`,
            url: window.location.href
        });
    } else {
        // Fallback to clipboard
        const text = `Tax Calculation Result\nBase: ₹{{ calculation_result.base_amount|default:0 }}\nTax: ₹{{ calculation_result.total_tax_amount|default:0 }}\nTotal: ₹{{ calculation_result.total_amount|default:0 }}`;
        navigator.clipboard.writeText(text).then(() => {
            alert('Calculation copied to clipboard!');
        });
    }
}

function clearForm() {
    // Implementation for clearing form
    location.reload();
}
</script>