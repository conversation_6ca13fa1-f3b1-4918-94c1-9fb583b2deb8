# accounts/cheque_views.py
# Django views for Cheque Series management in Accounts module
# Task Group 2: Banking & Cash Management - Cheque Series Views

from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.http import JsonResponse
from django.db.models import Q, F, Sum
from datetime import datetime
import json

from .models import (
    ChequeSeries, Bank, BankVoucherMaster
)
from .cheque_forms import (
    ChequeSeriesForm
)


class ChequeSeriesListView(LoginRequiredMixin, ListView):
    """
    Enhanced list view for Cheque Series
    Replaces ASP.NET Cheque_series.aspx functionality with advanced features
    """
    model = ChequeSeries
    template_name = 'accounts/masters/cheque_series_list_simple.html'
    context_object_name = 'cheque_series'
    paginate_by = 20

    def get_queryset(self):
        queryset = ChequeSeries.objects.annotate(
            total_cheques=F('end_no') - F('start_no') + 1
        ).order_by('-id')
        
        # Filter by bank
        bank_id = self.request.GET.get('bank')
        if bank_id:
            queryset = queryset.filter(bank_id=bank_id)
        
        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(bank_id__icontains=search) |
                Q(start_no__icontains=search) |
                Q(end_no__icontains=search)
            )
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['banks'] = Bank.objects.all()
        context['status_choices'] = [
            ('active', 'Active'),
            ('exhausted', 'Exhausted'),
            ('inactive', 'Inactive'),
        ]
        
        # Summary statistics
        context['summary'] = {
            'total_series': ChequeSeries.objects.count(),
            'total_cheques': ChequeSeries.objects.aggregate(
                total=Sum(F('end_no') - F('start_no') + 1)
            )['total'] or 0,
        }
        
        return context


class ChequeSeriesCreateView(LoginRequiredMixin, CreateView):
    """
    Enhanced create view for Cheque Series with validation
    """
    model = ChequeSeries
    form_class = ChequeSeriesForm
    template_name = 'accounts/cheque_series_form.html'
    success_url = reverse_lazy('accounts:cheque_series_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = 'Create Cheque Series'
        context['action'] = 'create'
        return context

    def form_valid(self, form):
        # Simplified validation for actual database structure
        bank_id = form.cleaned_data['bank_id']
        start_no = form.cleaned_data['start_no']
        end_no = form.cleaned_data['end_no']
        
        # Basic overlap check using simplified structure
        overlapping_series = ChequeSeries.objects.filter(
            bank_id=bank_id
        ).filter(
            Q(start_no__lte=start_no, end_no__gte=start_no) |
            Q(start_no__lte=end_no, end_no__gte=end_no) |
            Q(start_no__gte=start_no, end_no__lte=end_no)
        )
        
        if overlapping_series.exists():
            messages.error(
                self.request, 
                f'Cheque range {start_no}-{end_no} overlaps with existing series for Bank ID {bank_id}'
            )
            return self.form_invalid(form)
        
        messages.success(
            self.request, 
            f'Cheque series created for Bank ID {bank_id}: {start_no}-{end_no}'
        )
        return super().form_valid(form)


class ChequeSeriesUpdateView(LoginRequiredMixin, UpdateView):
    """
    Enhanced update view for Cheque Series
    """
    model = ChequeSeries
    form_class = ChequeSeriesForm
    template_name = 'accounts/cheque_series_form.html'
    success_url = reverse_lazy('accounts:cheque_series_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = f'Edit Cheque Series - {self.object.bank.name}'
        context['action'] = 'edit'
        
        # Add usage statistics
        context['usage_stats'] = self.get_usage_statistics()
        
        return context

    def get_usage_statistics(self):
        """Get usage statistics for this cheque series"""
        cheque_series = self.object
        
        # Get used cheques from bank vouchers
        used_cheques = BankVoucherMaster.objects.filter(
            bank=cheque_series.bank,
            cheque_no__gte=cheque_series.cheque_from,
            cheque_no__lte=cheque_series.cheque_to
        ).values('cheque_no').distinct()
        
        return {
            'total_cheques': cheque_series.cheque_to - cheque_series.cheque_from + 1,
            'current_position': cheque_series.current_cheque_no,
            'used_count': used_cheques.count(),
            'remaining_count': cheque_series.cheque_to - cheque_series.current_cheque_no,
            'usage_percentage': round(
                ((cheque_series.current_cheque_no - cheque_series.cheque_from) / 
                 (cheque_series.cheque_to - cheque_series.cheque_from + 1)) * 100, 2
            ) if cheque_series.cheque_to > cheque_series.cheque_from else 0,
        }

    def form_valid(self, form):
        # Validate that current cheque number is not moved backward if cheques are already used
        old_current = self.object.current_cheque_no
        new_current = form.cleaned_data['current_cheque_no']
        
        if new_current < old_current:
            # Check if there are any vouchers using cheques in the range being reset
            used_cheques = BankVoucherMaster.objects.filter(
                bank=self.object.bank,
                cheque_no__gte=new_current,
                cheque_no__lt=old_current
            ).exists()
            
            if used_cheques:
                messages.error(
                    self.request, 
                    f'Cannot move current cheque number backward. Cheques {new_current}-{old_current-1} are already used.'
                )
                return self.form_invalid(form)
        
        form.instance.updated_by = self.request.user
        
        messages.success(
            self.request, 
            f'Cheque series updated for {form.instance.bank.name}'
        )
        return super().form_valid(form)


class ChequeSeriesDeleteView(LoginRequiredMixin, DeleteView):
    """
    Delete view for Cheque Series with safety checks
    """
    model = ChequeSeries
    template_name = 'accounts/cheque_series_confirm_delete.html'
    success_url = reverse_lazy('accounts:cheque_series_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Check if any cheques from this series are used
        used_cheques = BankVoucherMaster.objects.filter(
            bank=self.object.bank,
            cheque_no__gte=self.object.cheque_from,
            cheque_no__lte=self.object.cheque_to
        ).count()
        
        context['used_cheques_count'] = used_cheques
        context['can_delete'] = used_cheques == 0
        
        return context

    def delete(self, request, *args, **kwargs):
        cheque_series = self.get_object()
        
        # Final safety check
        used_cheques = BankVoucherMaster.objects.filter(
            bank=cheque_series.bank,
            cheque_no__gte=cheque_series.cheque_from,
            cheque_no__lte=cheque_series.cheque_to
        ).exists()
        
        if used_cheques:
            messages.error(
                request, 
                'Cannot delete cheque series. Some cheques from this series are already used.'
            )
            return redirect('accounts:cheque_series_list')
        
        messages.success(
            request, 
            f'Cheque series for {cheque_series.bank.name} deleted successfully.'
        )
        return super().delete(request, *args, **kwargs)


class ChequeUsageReportView(LoginRequiredMixin, View):
    """
    View for generating cheque usage reports
    """
    
    def get(self, request):
        """Display cheque usage report"""
        bank_id = request.GET.get('bank')
        date_from = request.GET.get('date_from')
        date_to = request.GET.get('date_to')
        
        context = {
            'banks': Bank.objects.all(),
            'selected_bank': bank_id,
            'date_from': date_from,
            'date_to': date_to,
        }
        
        if bank_id and date_from and date_to:
            context['usage_data'] = self.get_usage_data(bank_id, date_from, date_to)
        
        return render(request, 'accounts/cheque_usage_report.html', context)
    
    def get_usage_data(self, bank_id, date_from, date_to):
        """Get detailed cheque usage data"""
        try:
            bank = Bank.objects.get(id=bank_id)
            from_date = datetime.strptime(date_from, '%Y-%m-%d').date()
            to_date = datetime.strptime(date_to, '%Y-%m-%d').date()
            
            # Get cheque series for the bank
            cheque_series = ChequeSeries.objects.filter(bank=bank)
            
            # Get used cheques in date range
            used_cheques = BankVoucherMaster.objects.filter(
                bank=bank,
                voucher_date__range=[from_date, to_date],
                cheque_no__isnull=False
            ).order_by('cheque_no')
            
            # Get series usage summary
            series_summary = []
            for series in cheque_series:
                used_in_series = used_cheques.filter(
                    cheque_no__gte=series.cheque_from,
                    cheque_no__lte=series.cheque_to
                ).count()
                
                series_summary.append({
                    'series': series,
                    'used_in_period': used_in_series,
                    'total_used': series.current_cheque_no - series.cheque_from,
                    'total_available': series.cheque_to - series.cheque_from + 1,
                    'remaining': series.cheque_to - series.current_cheque_no,
                })
            
            return {
                'bank': bank,
                'from_date': from_date,
                'to_date': to_date,
                'used_cheques': used_cheques,
                'series_summary': series_summary,
                'total_used_in_period': used_cheques.count(),
            }
        
        except Exception:
            return None


class ChequeSeriesDetailView(LoginRequiredMixin, View):
    """
    Detailed view for a specific cheque series with cheque-by-cheque breakdown
    """
    
    def get(self, request, pk):
        """Display detailed cheque series information"""
        cheque_series = get_object_or_404(ChequeSeries, pk=pk)
        
        # Get all used cheques from this series
        used_cheques = BankVoucherMaster.objects.filter(
            bank=cheque_series.bank,
            cheque_no__gte=cheque_series.cheque_from,
            cheque_no__lte=cheque_series.cheque_to,
            cheque_no__isnull=False
        ).select_related('account_head').order_by('cheque_no')
        
        # Create a comprehensive cheque status list
        cheque_status = []
        for cheque_no in range(cheque_series.cheque_from, cheque_series.cheque_to + 1):
            used_voucher = used_cheques.filter(cheque_no=str(cheque_no)).first()
            
            if cheque_no < cheque_series.current_cheque_no:
                if used_voucher:
                    status = 'used'
                    status_detail = f'Used in {used_voucher.voucher_no} on {used_voucher.voucher_date}'
                else:
                    status = 'skipped'
                    status_detail = 'Skipped/Cancelled'
            elif cheque_no == cheque_series.current_cheque_no:
                status = 'current'
                status_detail = 'Next available'
            else:
                status = 'available'
                status_detail = 'Available for use'
            
            cheque_status.append({
                'cheque_no': cheque_no,
                'status': status,
                'status_detail': status_detail,
                'voucher': used_voucher,
            })
        
        context = {
            'cheque_series': cheque_series,
            'cheque_status': cheque_status,
            'used_cheques': used_cheques,
            'summary': {
                'total_cheques': cheque_series.cheque_to - cheque_series.cheque_from + 1,
                'used_count': used_cheques.count(),
                'current_position': cheque_series.current_cheque_no,
                'remaining_count': cheque_series.cheque_to - cheque_series.current_cheque_no,
            }
        }
        
        return render(request, 'accounts/cheque_series_detail.html', context)


# AJAX Views for Dynamic Functionality

def get_next_available_cheque(request):
    """
    AJAX view to get the next available cheque number for a bank
    Enhanced version of existing functionality
    """
    bank_id = request.GET.get('bank_id')
    
    if not bank_id:
        return JsonResponse({'error': 'Bank ID required'}, status=400)
    
    try:
        # Get active cheque series for the bank
        cheque_series = ChequeSeries.objects.filter(
            bank_id=bank_id,
            is_active=True,
            current_cheque_no__lt=F('cheque_to')
        ).order_by('cheque_from').first()
        
        if cheque_series:
            next_cheque = cheque_series.current_cheque_no
            remaining = cheque_series.cheque_to - cheque_series.current_cheque_no
            
            return JsonResponse({
                'next_cheque': next_cheque,
                'remaining_cheques': remaining,
                'series_id': cheque_series.id,
                'series_range': f"{cheque_series.cheque_from}-{cheque_series.cheque_to}"
            })
        else:
            return JsonResponse({
                'error': 'No active cheque series found for this bank',
                'suggestion': 'Please create a new cheque series for this bank'
            }, status=404)
    
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


def update_current_cheque(request):
    """
    AJAX view to update current cheque number when a cheque is used
    """
    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)
    
    try:
        data = json.loads(request.body)
        series_id = data.get('series_id')
        used_cheque_no = data.get('cheque_no')
        
        if not all([series_id, used_cheque_no]):
            return JsonResponse({'error': 'Missing required parameters'}, status=400)
        
        cheque_series = ChequeSeries.objects.get(id=series_id)
        used_cheque_no = int(used_cheque_no)
        
        # Update current cheque number if the used cheque is the current one
        if used_cheque_no == cheque_series.current_cheque_no:
            cheque_series.current_cheque_no = used_cheque_no + 1
            cheque_series.save()
            
            return JsonResponse({
                'success': True,
                'new_current_cheque': cheque_series.current_cheque_no,
                'remaining_cheques': cheque_series.cheque_to - cheque_series.current_cheque_no
            })
        else:
            return JsonResponse({
                'warning': f'Cheque {used_cheque_no} is not the current cheque. Current: {cheque_series.current_cheque_no}'
            })
    
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


def cheque_series_summary(request):
    """
    AJAX view to get summary data for all cheque series
    """
    try:
        bank_id = request.GET.get('bank_id')
        
        queryset = ChequeSeries.objects.select_related('bank')
        if bank_id:
            queryset = queryset.filter(bank_id=bank_id)
        
        series_data = []
        for series in queryset:
            series_data.append({
                'id': series.id,
                'bank_name': series.bank.name,
                'range': f"{series.cheque_from}-{series.cheque_to}",
                'current': series.current_cheque_no,
                'total_cheques': series.cheque_to - series.cheque_from + 1,
                'used_cheques': series.current_cheque_no - series.cheque_from,
                'remaining_cheques': series.cheque_to - series.current_cheque_no,
                'is_active': series.is_active,
                'is_exhausted': series.current_cheque_no >= series.cheque_to,
            })
        
        return JsonResponse({'series_data': series_data})
    
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)