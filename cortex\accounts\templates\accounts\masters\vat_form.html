<!-- accounts/templates/accounts/masters/vat_form.html -->
<!-- VAT Create/Edit Form Template -->
<!-- Task Group 4: Taxation Management - VAT Form (Task 4.3) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{% if object %}Edit VAT Rate{% else %}New VAT Rate{% endif %} - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-green-600 to-sap-green-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="calculator" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">
                        {% if object %}Edit VAT Rate{% else %}New VAT Rate{% endif %}
                    </h1>
                    <p class="text-sm text-sap-gray-600 mt-1">
                        {% if object %}Modify VAT rate configuration{% else %}Add a new VAT tax rate{% endif %}
                    </p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:vat_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- VAT Form -->
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="edit" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                    VAT Rate Information
                </h3>
                <p class="text-sm text-sap-gray-600 mt-1">Configure Value Added Tax (VAT) rate details</p>
            </div>
            
            <form method="post" class="p-6" x-data="vatForm()">
                {% csrf_token %}
                
                <!-- Basic Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="info" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                        Basic Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- VAT Percentage -->
                        <div>
                            <label for="{{ form.vat_percentage.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                VAT Percentage *
                            </label>
                            <div class="relative">
                                {{ form.vat_percentage|add_class:"block w-full px-3 py-2 pr-8 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                    <span class="text-sap-gray-500 text-sm">%</span>
                                </div>
                            </div>
                            {% if form.vat_percentage.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.vat_percentage.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Enter the VAT rate percentage</p>
                        </div>
                        
                        <!-- VAT Category -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">VAT Category</label>
                            <select x-model="vatCategory" class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                                <option value="">Select Category</option>
                                <option value="STANDARD">Standard Rate</option>
                                <option value="REDUCED">Reduced Rate</option>
                                <option value="ZERO">Zero Rate</option>
                                <option value="EXEMPT">Exempt</option>
                            </select>
                            <p class="text-xs text-sap-gray-500 mt-1">Classification for this VAT rate</p>
                        </div>
                        
                        <!-- Description -->
                        <div class="md:col-span-2">
                            <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Description *
                            </label>
                            {{ form.description|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.description.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.description.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Detailed description of when this VAT rate applies</p>
                        </div>
                    </div>
                </div>
                
                <!-- VAT Calculation Preview -->
                <div class="mb-8 bg-sap-green-50 border border-sap-green-200 rounded-lg p-6" x-show="vatPercentage > 0">
                    <h4 class="text-lg font-medium text-sap-green-800 mb-4 flex items-center">
                        <i data-lucide="calculator" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                        VAT Calculation Preview
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <!-- Sample Amount Input -->
                        <div>
                            <label class="block text-sm font-medium text-sap-green-700 mb-1">Sample Amount (₹)</label>
                            <input type="number" 
                                   x-model="sampleAmount" 
                                   class="block w-full px-3 py-2 border border-sap-green-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500"
                                   placeholder="10000">
                        </div>
                        
                        <!-- VAT Amount -->
                        <div class="text-center">
                            <p class="text-sm text-sap-green-600">VAT Amount</p>
                            <p class="text-xl font-bold text-sap-green-800" x-text="'₹' + vatAmount.toFixed(2)"></p>
                        </div>
                        
                        <!-- Total Amount -->
                        <div class="text-center">
                            <p class="text-sm text-sap-green-600">Total Amount</p>
                            <p class="text-xl font-bold text-sap-green-800" x-text="'₹' + totalAmount.toFixed(2)"></p>
                        </div>
                        
                        <!-- Effective Rate -->
                        <div class="text-center">
                            <p class="text-sm text-sap-green-600">Effective Rate</p>
                            <p class="text-xl font-bold text-sap-green-800" x-text="vatPercentage + '%'"></p>
                        </div>
                    </div>
                    
                    <!-- Calculation Methods -->
                    <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Inclusive VAT -->
                        <div class="bg-white rounded-lg border border-sap-green-100 p-4">
                            <h5 class="text-sm font-medium text-sap-green-800 mb-2">VAT Inclusive Calculation</h5>
                            <div class="text-xs text-sap-green-700 space-y-1">
                                <p>Total Amount (VAT Inclusive): ₹<span x-text="sampleAmount.toFixed(2)"></span></p>
                                <p>VAT Component: ₹<span x-text="vatInclusiveAmount.toFixed(2)"></span></p>
                                <p class="font-medium border-t border-sap-green-200 pt-1">Base Amount: ₹<span x-text="baseInclusiveAmount.toFixed(2)"></span></p>
                            </div>
                        </div>
                        
                        <!-- Exclusive VAT -->
                        <div class="bg-white rounded-lg border border-sap-green-100 p-4">
                            <h5 class="text-sm font-medium text-sap-green-800 mb-2">VAT Exclusive Calculation</h5>
                            <div class="text-xs text-sap-green-700 space-y-1">
                                <p>Base Amount: ₹<span x-text="sampleAmount.toFixed(2)"></span></p>
                                <p>VAT @ <span x-text="vatPercentage"></span>%: ₹<span x-text="vatAmount.toFixed(2)"></span></p>
                                <p class="font-medium border-t border-sap-green-200 pt-1">Total Amount: ₹<span x-text="totalAmount.toFixed(2)"></span></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Common VAT Rates Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="trending-up" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Common VAT Rates (Reference)
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div class="bg-sap-blue-50 border border-sap-blue-200 rounded-lg p-4 cursor-pointer" onclick="setVATRate(0, 'Zero Rate')">
                            <h5 class="font-medium text-sap-blue-800">Zero Rate</h5>
                            <p class="text-lg font-bold text-sap-blue-900">0%</p>
                            <p class="text-xs text-sap-blue-700">Essential goods, exports</p>
                        </div>
                        <div class="bg-sap-green-50 border border-sap-green-200 rounded-lg p-4 cursor-pointer" onclick="setVATRate(5, 'Reduced Rate')">
                            <h5 class="font-medium text-sap-green-800">Reduced Rate</h5>
                            <p class="text-lg font-bold text-sap-green-900">5%</p>
                            <p class="text-xs text-sap-green-700">Basic necessities</p>
                        </div>
                        <div class="bg-sap-yellow-50 border border-sap-yellow-200 rounded-lg p-4 cursor-pointer" onclick="setVATRate(12, 'Standard Rate')">
                            <h5 class="font-medium text-sap-yellow-800">Standard Rate</h5>
                            <p class="text-lg font-bold text-sap-yellow-900">12%</p>
                            <p class="text-xs text-sap-yellow-700">Most goods & services</p>
                        </div>
                        <div class="bg-sap-red-50 border border-sap-red-200 rounded-lg p-4 cursor-pointer" onclick="setVATRate(18, 'Higher Rate')">
                            <h5 class="font-medium text-sap-red-800">Higher Rate</h5>
                            <p class="text-lg font-bold text-sap-red-900">18%</p>
                            <p class="text-xs text-sap-red-700">Luxury items, services</p>
                        </div>
                    </div>
                    <p class="text-xs text-sap-gray-500 mt-2">Click on any rate to apply it to the form</p>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="resetForm()" 
                                class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="rotate-ccw" class="w-4 h-4 inline mr-2"></i>
                            Reset
                        </button>
                        <button type="button" onclick="validateVATRate()" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check-circle" class="w-4 h-4 inline mr-2"></i>
                            Validate
                        </button>
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="{% url 'accounts:vat_list' %}" 
                           class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                            {% if object %}Update VAT Rate{% else %}Save VAT Rate{% endif %}
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- VAT Guidelines -->
        <div class="mt-6 bg-sap-blue-50 border border-sap-blue-200 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i data-lucide="info" class="w-5 h-5 text-sap-blue-600"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-sap-blue-800">VAT Configuration Guidelines</h3>
                    <div class="mt-2 text-sm text-sap-blue-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Ensure VAT rates comply with current tax regulations</li>
                            <li>Maintain separate rates for different product categories</li>
                            <li>Update rates promptly when government changes tax slabs</li>
                            <li>Use clear descriptions to avoid confusion during invoicing</li>
                            <li>Regularly review and audit VAT calculations for accuracy</li>
                            <li>Keep historical rates for compliance and reporting purposes</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function vatForm() {
    return {
        vatPercentage: {{ object.vat_percentage|default:0 }},
        sampleAmount: 10000,
        vatCategory: '{{ object.vat_category|default:"" }}',
        
        get vatAmount() {
            return (this.sampleAmount * this.vatPercentage) / 100;
        },
        
        get totalAmount() {
            return this.sampleAmount + this.vatAmount;
        },
        
        get vatInclusiveAmount() {
            return (this.sampleAmount * this.vatPercentage) / (100 + this.vatPercentage);
        },
        
        get baseInclusiveAmount() {
            return this.sampleAmount - this.vatInclusiveAmount;
        },
        
        init() {
            this.updateVATPercentage();
        },
        
        updateVATPercentage() {
            const percentageField = document.getElementById('{{ form.vat_percentage.id_for_label }}');
            if (percentageField) {
                this.vatPercentage = parseFloat(percentageField.value) || 0;
            }
        }
    }
}

function setVATRate(rate, description) {
    const percentageField = document.getElementById('{{ form.vat_percentage.id_for_label }}');
    const descriptionField = document.getElementById('{{ form.description.id_for_label }}');
    
    if (percentageField) {
        percentageField.value = rate;
        percentageField.dispatchEvent(new Event('input'));
    }
    
    if (descriptionField && !descriptionField.value) {
        descriptionField.value = description + ' - ' + rate + '% VAT';
    }
    
    // Update Alpine.js
    const alpineData = Alpine.$data(document.querySelector('[x-data="vatForm()"]'));
    if (alpineData) {
        alpineData.updateVATPercentage();
    }
}

function resetForm() {
    if (confirm('Are you sure you want to reset the form? All unsaved changes will be lost.')) {
        document.querySelector('form').reset();
    }
}

function validateVATRate() {
    const percentageField = document.getElementById('{{ form.vat_percentage.id_for_label }}');
    const percentage = parseFloat(percentageField.value);
    
    if (isNaN(percentage) || percentage < 0) {
        alert('⚠ Please enter a valid VAT percentage (0 or greater)');
        percentageField.focus();
        return;
    }
    
    if (percentage > 50) {
        if (!confirm('⚠ VAT rate is unusually high (' + percentage + '%). Are you sure this is correct?')) {
            percentageField.focus();
            return;
        }
    }
    
    // Common VAT rates validation
    const commonRates = [0, 5, 12, 18, 28];
    if (!commonRates.includes(percentage)) {
        alert('ℹ Non-standard VAT rate. Please verify this rate is correct for your jurisdiction.');
    } else {
        alert('✓ VAT rate validated successfully');
    }
}

// Real-time updates
document.addEventListener('DOMContentLoaded', function() {
    const percentageField = document.getElementById('{{ form.vat_percentage.id_for_label }}');
    
    if (percentageField) {
        percentageField.addEventListener('input', function() {
            const alpineData = Alpine.$data(document.querySelector('[x-data="vatForm()"]'));
            if (alpineData) {
                alpineData.updateVATPercentage();
            }
        });
    }
    
    lucide.createIcons();
});
</script>
{% endblock %}