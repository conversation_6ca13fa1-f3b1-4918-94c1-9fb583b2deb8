{% extends 'core/base.html' %}
{% load static %}

{% block title %}Goods Inward Note [GIN] - New PO Details{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50" x-data="ginPODetailsApp()">
    <!-- Header -->
    <div class="bg-blue-600 text-white px-4 py-2">
        <h1 class="text-lg font-bold">Goods Inward Note [GIN] - New PO Details</h1>
        <p class="text-sm opacity-90">Processing PO: {{ po_header.po_no }} | Supplier: {{ po_header.supplier_name }}</p>
    </div>
    
    <div class="p-4">
        <!-- PO Header Information -->
        <div class="bg-white rounded-lg shadow-sm border mb-4">
            <div class="px-4 py-3 border-b border-gray-200 bg-gray-50">
                <h2 class="text-lg font-semibold text-gray-900">Purchase Order Information</h2>
            </div>
            <div class="p-4">
                <div class="grid grid-cols-6 gap-4 text-sm">
                    <div>
                        <span class="font-medium text-gray-700">PO No:</span>
                        <div class="text-gray-900">{{ po_header.po_no }}</div>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">PO Date:</span>
                        <div class="text-gray-900">{{ po_header.po_date|date:"d-m-Y" }}</div>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">Supplier:</span>
                        <div class="text-gray-900">{{ po_header.supplier_name }}</div>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">Financial Year:</span>
                        <div class="text-gray-900">{{ po_header.fin_year }}</div>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">Challan No:</span>
                        <div class="text-gray-900 font-medium">{{ challan_no }}</div>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">Challan Date:</span>
                        <div class="text-gray-900 font-medium">{{ challan_date }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- GIN Header Form -->
        <div class="bg-white rounded-lg shadow-sm border mb-4">
            <div class="px-4 py-3 border-b border-gray-200 bg-gray-50">
                <h2 class="text-lg font-semibold text-gray-900">GIN Header Information</h2>
                <p class="text-sm text-gray-600 mt-1">Enter gate entry details and transportation information</p>
            </div>
            <div class="p-4">
                <form method="post" @submit.prevent="submitGIN">
                    {% csrf_token %}
                    <input type="hidden" name="challan_no" value="{{ challan_no }}">
                    <input type="hidden" name="challan_date" value="{{ challan_date }}">
                    
                    <div class="grid grid-cols-2 gap-6">
                        <!-- Left Column -->
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Gate Entry No <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="gate_entry_no" x-model="formData.gate_entry_no" 
                                       placeholder="Enter gate entry number"
                                       class="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       required>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Gate Entry Date <span class="text-red-500">*</span>
                                </label>
                                <input type="date" name="gate_entry_date" x-model="formData.gate_entry_date"
                                       class="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       required>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Gate Entry Time <span class="text-red-500">*</span>
                                </label>
                                <input type="time" name="gate_entry_time" x-model="formData.gate_entry_time"
                                       class="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       required>
                            </div>
                        </div>
                        
                        <!-- Right Column -->
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Mode of Transport <span class="text-red-500">*</span>
                                </label>
                                <select name="mode_of_transport" x-model="formData.mode_of_transport"
                                        class="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        required>
                                    <option value="">Select Mode of Transport</option>
                                    <option value="Road">Road</option>
                                    <option value="Rail">Rail</option>
                                    <option value="Air">Air</option>
                                    <option value="Sea">Sea</option>
                                    <option value="Courier">Courier</option>
                                    <option value="By Hand">By Hand</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Vehicle Number <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="vehicle_no" x-model="formData.vehicle_no" 
                                       placeholder="Enter vehicle number"
                                       class="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       required>
                            </div>
                        </div>
                    </div>

                    <!-- Line Items Table -->
                    <div class="mt-6">
                        <div class="bg-gray-50 rounded-lg border p-4">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-semibold text-gray-900">Purchase Order Line Items</h3>
                                <div class="text-sm text-gray-600">
                                    <span class="font-medium">Total Items:</span> {{ line_items|length }}
                                </div>
                            </div>
                            
                            <div class="overflow-x-auto">
                                <table class="w-full table-auto divide-y divide-gray-200">
                                    <thead class="bg-gray-100">
                                        <tr>
                                            <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                                                <input type="checkbox" @change="toggleAllItems" x-model="selectAll"
                                                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                            </th>
                                            <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-8">SN</th>
                                            <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">Item Code</th>
                                            <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                            <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">UOM</th>
                                            <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">PO Qty</th>
                                            <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">Pending Qty</th>
                                            <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">Challan Qty <span class="text-red-500">*</span></th>
                                            <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">Received Qty <span class="text-red-500">*</span></th>
                                            <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">Category</th>
                                            <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">Sub-Category</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        {% for item in line_items %}
                                        <tr class="hover:bg-gray-50" x-data="{ selected: false, challanQty: '', receivedQty: '' }">
                                            <td class="px-2 py-3 text-sm text-center">
                                                <input type="checkbox" x-model="selected" 
                                                       name="item_selected_{{ item.PODetailId }}"
                                                       @change="updateItemSelection({{ item.PODetailId }}, selected)"
                                                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                            </td>
                                            <td class="px-2 py-3 text-sm text-gray-900 text-center">{{ forloop.counter }}</td>
                                            <td class="px-2 py-3 text-sm text-gray-900 text-center font-mono">{{ item.ItemCode|default:"N/A" }}</td>
                                            <td class="px-2 py-3 text-sm text-gray-900">{{ item.Description|default:"Item Description" }}</td>
                                            <td class="px-2 py-3 text-sm text-gray-900 text-center">{{ item.UOM|default:"NOS" }}</td>
                                            <td class="px-2 py-3 text-sm text-gray-900 text-right">{{ item.POQty|floatformat:2 }}</td>
                                            <td class="px-2 py-3 text-sm text-gray-900 text-right">{{ item.PendingQty|floatformat:2 }}</td>
                                            <td class="px-2 py-3 text-sm">
                                                <input type="number" step="0.001" min="0" max="{{ item.PendingQty }}"
                                                       x-model="challanQty" 
                                                       name="challan_qty_{{ item.PODetailId }}"
                                                       @input="receivedQty = challanQty"
                                                       :disabled="!selected"
                                                       class="w-full border border-gray-300 rounded px-2 py-1 text-sm text-right focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:bg-gray-100">
                                            </td>
                                            <td class="px-2 py-3 text-sm">
                                                <input type="number" step="0.001" min="0" max="{{ item.PendingQty }}"
                                                       x-model="receivedQty"
                                                       name="received_qty_{{ item.PODetailId }}"
                                                       :disabled="!selected"
                                                       class="w-full border border-gray-300 rounded px-2 py-1 text-sm text-right focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:bg-gray-100">
                                            </td>
                                            <td class="px-2 py-3 text-sm">
                                                <select name="category_{{ item.PODetailId }}" 
                                                        :disabled="!selected"
                                                        class="w-full border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:bg-gray-100">
                                                    <option value="">Select</option>
                                                    <option value="1">General</option>
                                                    <option value="33">Asset</option>
                                                </select>
                                            </td>
                                            <td class="px-2 py-3 text-sm">
                                                <select name="subcategory_{{ item.PODetailId }}"
                                                        :disabled="!selected"
                                                        class="w-full border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:bg-gray-100">
                                                    <option value="">Select</option>
                                                    <option value="1">Standard</option>
                                                    <option value="2">Special</option>
                                                </select>
                                            </td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="11" class="px-4 py-8 text-center">
                                                <div class="text-gray-500">
                                                    <p class="text-lg font-medium text-red-900">No line items found!</p>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="mt-6 flex justify-center gap-4">
                        <button type="submit" 
                                :disabled="loading || !hasSelectedItems"
                                class="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-8 py-2 rounded font-medium transition-colors">
                            <span x-show="!loading">Create GIN</span>
                            <span x-show="loading">Creating GIN...</span>
                        </button>
                        
                        <a href="{% url 'inventory:gin_new_search' %}" 
                           class="bg-gray-500 hover:bg-gray-600 text-white px-8 py-2 rounded font-medium transition-colors">
                            Back to Search
                        </a>
                        
                        <button type="button" 
                                onclick="window.history.back()" 
                                class="bg-red-500 hover:bg-red-600 text-white px-8 py-2 rounded font-medium transition-colors">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function ginPODetailsApp() {
    return {
        formData: {
            gate_entry_no: '',
            gate_entry_date: new Date().toISOString().split('T')[0], // Today's date
            gate_entry_time: '09:00',
            mode_of_transport: '',
            vehicle_no: ''
        },
        
        selectedItems: new Set(),
        selectAll: false,
        loading: false,
        hasSelectedItems: false,
        
        init() {
            // Initialize with current time
            const now = new Date();
            this.formData.gate_entry_time = now.toTimeString().slice(0, 5);
        },
        
        toggleAllItems() {
            const checkboxes = document.querySelectorAll('input[name^="item_selected_"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.selectAll;
                // Trigger change event to update item selection
                checkbox.dispatchEvent(new Event('change'));
            });
        },
        
        updateItemSelection(itemId, selected) {
            if (selected) {
                this.selectedItems.add(itemId);
            } else {
                this.selectedItems.delete(itemId);
            }
            
            this.hasSelectedItems = this.selectedItems.size > 0;
            
            // Update selectAll checkbox
            const totalCheckboxes = document.querySelectorAll('input[name^="item_selected_"]').length;
            this.selectAll = this.selectedItems.size === totalCheckboxes;
        },
        
        submitGIN() {
            // Validate form data
            if (!this.formData.gate_entry_no.trim()) {
                alert('Gate Entry Number is required');
                return;
            }
            
            if (!this.formData.gate_entry_date) {
                alert('Gate Entry Date is required');
                return;
            }
            
            if (!this.formData.mode_of_transport) {
                alert('Mode of Transport is required');
                return;
            }
            
            if (!this.formData.vehicle_no.trim()) {
                alert('Vehicle Number is required');
                return;
            }
            
            if (this.selectedItems.size === 0) {
                alert('Please select at least one item to create GIN');
                return;
            }
            
            // Validate selected items have quantities
            let hasValidQuantities = false;
            this.selectedItems.forEach(itemId => {
                const challanQty = parseFloat(document.querySelector(`input[name="challan_qty_${itemId}"]`).value || 0);
                const receivedQty = parseFloat(document.querySelector(`input[name="received_qty_${itemId}"]`).value || 0);
                
                if (challanQty > 0 && receivedQty > 0) {
                    hasValidQuantities = true;
                }
            });
            
            if (!hasValidQuantities) {
                alert('Please enter valid quantities for selected items');
                return;
            }
            
            // Submit the form
            this.loading = true;
            document.querySelector('form').submit();
        }
    }
}
</script>
{% endblock %}