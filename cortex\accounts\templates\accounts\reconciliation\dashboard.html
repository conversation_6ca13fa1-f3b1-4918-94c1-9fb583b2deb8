{% extends "base.html" %}
{% load widget_tweaks %}

{% block title %}Bank Reconciliation Dashboard{% endblock %}

{% block extra_css %}
<style>
    .reconciliation-card {
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
    }
    .reconciliation-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    .status-pending { border-left-color: #f59e0b; }
    .status-in-progress { border-left-color: #3b82f6; }
    .status-completed { border-left-color: #10b981; }
    
    .search-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        color: white;
    }
    
    .stats-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
    }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    .btn-create-reconciliation {
        background: linear-gradient(45deg, #4f46e5, #7c3aed);
        border: none;
        border-radius: 8px;
        padding: 12px 24px;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-create-reconciliation:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(79, 70, 229, 0.4);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1 text-gray-800">Bank Reconciliation Dashboard</h1>
            <p class="text-muted">Manage and track bank reconciliation processes</p>
        </div>
        <a href="{% url 'accounts:bank_reconciliation_master_create' %}" class="btn btn-create-reconciliation">
            <i class="fas fa-plus me-2"></i>Create New Reconciliation
        </a>
    </div>

    <!-- Search and Statistics Section -->
    <div class="search-section">
        <div class="row">
            <div class="col-lg-8">
                <h4 class="mb-3">
                    <i class="fas fa-search me-2"></i>Search & Filter Reconciliations
                </h4>
                <form method="get" hx-get="{% url 'accounts:bank_reconciliation_master_list' %}" 
                      hx-target="#reconciliation-results" hx-trigger="input delay:500ms, change">
                    <div class="row g-3">
                        <div class="col-md-3">
                            {{ search_form.bank|add_class:"form-select text-dark" }}
                        </div>
                        <div class="col-md-3">
                            {{ search_form.from_date|add_class:"form-control text-dark" }}
                        </div>
                        <div class="col-md-3">
                            {{ search_form.to_date|add_class:"form-control text-dark" }}
                        </div>
                        <div class="col-md-3">
                            {{ search_form.status|add_class:"form-select text-dark" }}
                        </div>
                        <div class="col-md-6">
                            {{ search_form.reconciliation_no|add_class:"form-control text-dark" }}
                        </div>
                        <div class="col-md-6 d-flex align-items-end">
                            <button type="submit" class="btn btn-light btn-sm me-2">
                                <i class="fas fa-search me-1"></i>Search
                            </button>
                            <a href="{% url 'accounts:bank_reconciliation_master_list' %}" class="btn btn-outline-light btn-sm">
                                <i class="fas fa-times me-1"></i>Clear
                            </a>
                        </div>
                    </div>
                </form>
            </div>
            <div class="col-lg-4">
                <h4 class="mb-3">
                    <i class="fas fa-chart-pie me-2"></i>Reconciliation Statistics
                </h4>
                <div class="row g-3">
                    <div class="col-6">
                        <div class="stats-card">
                            <div class="stats-number">{{ total_reconciliations }}</div>
                            <div class="small">Total</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stats-card">
                            <div class="stats-number">{{ pending_reconciliations }}</div>
                            <div class="small">Pending</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stats-card">
                            <div class="stats-number">{{ in_progress_reconciliations }}</div>
                            <div class="small">In Progress</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stats-card">
                            <div class="stats-number">{{ completed_reconciliations }}</div>
                            <div class="small">Completed</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reconciliation Results -->
    <div id="reconciliation-results">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-balance-scale me-2 text-primary"></i>Bank Reconciliations
                    </h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="exportReconciliations('excel')">
                            <i class="fas fa-file-excel me-1"></i>Export Excel
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="exportReconciliations('pdf')">
                            <i class="fas fa-file-pdf me-1"></i>Export PDF
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                {% if reconciliations %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Reconciliation No.</th>
                                    <th>Bank</th>
                                    <th>Date</th>
                                    <th>Book Balance</th>
                                    <th>Bank Balance</th>
                                    <th>Difference</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for reconciliation in reconciliations %}
                                <tr class="reconciliation-card status-{{ reconciliation.status|lower|slugify }}">
                                    <td>
                                        <a href="{% url 'accounts:bank_reconciliation_detail' reconciliation.id %}" 
                                           class="text-decoration-none fw-bold">
                                            {{ reconciliation.reconciliation_no }}
                                        </a>
                                    </td>
                                    <td>
                                        <i class="fas fa-university me-2 text-muted"></i>
                                        {{ reconciliation.bank.bank_name|default:"N/A" }}
                                    </td>
                                    <td>{{ reconciliation.reconciliation_date|date:"d/m/Y"|default:"-" }}</td>
                                    <td class="text-end">
                                        {% if reconciliation.book_balance %}
                                            <span class="badge bg-info text-dark">
                                                ₹{{ reconciliation.book_balance|floatformat:2 }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-end">
                                        {% if reconciliation.bank_statement_balance %}
                                            <span class="badge bg-warning text-dark">
                                                ₹{{ reconciliation.bank_statement_balance|floatformat:2 }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-end">
                                        {% if reconciliation.difference_amount %}
                                            <span class="badge {% if reconciliation.difference_amount > 0 %}bg-danger{% elif reconciliation.difference_amount < 0 %}bg-success{% else %}bg-secondary{% endif %}">
                                                ₹{{ reconciliation.difference_amount|floatformat:2 }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if reconciliation.status == "Pending" %}
                                            <span class="badge bg-warning text-dark">
                                                <i class="fas fa-clock me-1"></i>Pending
                                            </span>
                                        {% elif reconciliation.status == "In Progress" %}
                                            <span class="badge bg-primary">
                                                <i class="fas fa-spinner me-1"></i>In Progress
                                            </span>
                                        {% elif reconciliation.status == "Completed" %}
                                            <span class="badge bg-success">
                                                <i class="fas fa-check me-1"></i>Completed
                                            </span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ reconciliation.status|default:"Unknown" }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'accounts:bank_reconciliation_detail' reconciliation.id %}" 
                                               class="btn btn-outline-primary btn-sm" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'accounts:bank_reconciliation_master_edit' reconciliation.id %}" 
                                               class="btn btn-outline-secondary btn-sm" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger btn-sm" 
                                                    onclick="deleteReconciliation({{ reconciliation.id }})" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if is_paginated %}
                    <div class="card-footer bg-white border-top-0">
                        <nav aria-label="Reconciliation pagination">
                            <ul class="pagination justify-content-center mb-0">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">First</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">Previous</a>
                                    </li>
                                {% endif %}

                                <li class="page-item active">
                                    <span class="page-link">
                                        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">Next</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">Last</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-balance-scale fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No Reconciliations Found</h4>
                        <p class="text-muted">Start by creating your first bank reconciliation.</p>
                        <a href="{% url 'accounts:bank_reconciliation_master_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Create New Reconciliation
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this bank reconciliation? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function deleteReconciliation(reconciliationId) {
        $('#deleteConfirmModal').modal('show');
        
        $('#confirmDeleteBtn').off('click').on('click', function() {
            // TODO: Implement delete functionality
            console.log('Delete reconciliation:', reconciliationId);
            $('#deleteConfirmModal').modal('hide');
        });
    }
    
    function exportReconciliations(format) {
        // TODO: Implement export functionality
        console.log('Export reconciliations as:', format);
    }
    
    // Auto-refresh search results
    document.addEventListener('DOMContentLoaded', function() {
        // Enable real-time search functionality
        const searchInputs = document.querySelectorAll('form input, form select');
        searchInputs.forEach(input => {
            input.addEventListener('input', function() {
                // HTMX will handle the search automatically
            });
        });
    });
</script>
{% endblock %}