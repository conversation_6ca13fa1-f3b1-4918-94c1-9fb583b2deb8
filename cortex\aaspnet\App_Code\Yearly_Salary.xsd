﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="Yearly_Salary" targetNamespace="http://tempuri.org/Yearly_Salary.xsd" xmlns:mstns="http://tempuri.org/Yearly_Salary.xsd" xmlns="http://tempuri.org/Yearly_Salary.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="Yearly_Salary" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="Yearly_Salary" msprop:Generator_DataSetName="Yearly_Salary">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="EmpId" msprop:Generator_UserColumnName="EmpId" msprop:Generator_ColumnPropNameInRow="EmpId" msprop:Generator_ColumnVarNameInTable="columnEmpId" msprop:Generator_ColumnPropNameInTable="EmpIdColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="EmployeeName" msprop:Generator_UserColumnName="EmployeeName" msprop:Generator_ColumnPropNameInRow="EmployeeName" msprop:Generator_ColumnVarNameInTable="columnEmployeeName" msprop:Generator_ColumnPropNameInTable="EmployeeNameColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Month" msprop:Generator_UserColumnName="Month" msprop:Generator_ColumnPropNameInRow="Month" msprop:Generator_ColumnVarNameInTable="columnMonth" msprop:Generator_ColumnPropNameInTable="MonthColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Year" msprop:Generator_UserColumnName="Year" msprop:Generator_ColumnPropNameInRow="Year" msprop:Generator_ColumnVarNameInTable="columnYear" msprop:Generator_ColumnPropNameInTable="YearColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Dept" msprop:Generator_UserColumnName="Dept" msprop:Generator_ColumnPropNameInRow="Dept" msprop:Generator_ColumnVarNameInTable="columnDept" msprop:Generator_ColumnPropNameInTable="DeptColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Designation" msprop:Generator_UserColumnName="Designation" msprop:Generator_ColumnPropNameInRow="Designation" msprop:Generator_ColumnVarNameInTable="columnDesignation" msprop:Generator_ColumnPropNameInTable="DesignationColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Status" msprop:Generator_UserColumnName="Status" msprop:Generator_ColumnPropNameInRow="Status" msprop:Generator_ColumnVarNameInTable="columnStatus" msprop:Generator_ColumnPropNameInTable="StatusColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Grade" msprop:Generator_UserColumnName="Grade" msprop:Generator_ColumnPropNameInRow="Grade" msprop:Generator_ColumnVarNameInTable="columnGrade" msprop:Generator_ColumnPropNameInTable="GradeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Basic" msprop:Generator_UserColumnName="Basic" msprop:Generator_ColumnPropNameInRow="Basic" msprop:Generator_ColumnVarNameInTable="columnBasic" msprop:Generator_ColumnPropNameInTable="BasicColumn" type="xs:double" minOccurs="0" />
              <xs:element name="DA" msprop:Generator_UserColumnName="DA" msprop:Generator_ColumnPropNameInRow="DA" msprop:Generator_ColumnVarNameInTable="columnDA" msprop:Generator_ColumnPropNameInTable="DAColumn" type="xs:double" minOccurs="0" />
              <xs:element name="HRA" msprop:Generator_UserColumnName="HRA" msprop:Generator_ColumnPropNameInRow="HRA" msprop:Generator_ColumnVarNameInTable="columnHRA" msprop:Generator_ColumnPropNameInTable="HRAColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Conveyance" msprop:Generator_UserColumnName="Conveyance" msprop:Generator_ColumnPropNameInRow="Conveyance" msprop:Generator_ColumnVarNameInTable="columnConveyance" msprop:Generator_ColumnPropNameInTable="ConveyanceColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Education" msprop:Generator_UserColumnName="Education" msprop:Generator_ColumnPropNameInRow="Education" msprop:Generator_ColumnVarNameInTable="columnEducation" msprop:Generator_ColumnPropNameInTable="EducationColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Medical" msprop:Generator_UserColumnName="Medical" msprop:Generator_ColumnPropNameInRow="Medical" msprop:Generator_ColumnVarNameInTable="columnMedical" msprop:Generator_ColumnPropNameInTable="MedicalColumn" type="xs:double" minOccurs="0" />
              <xs:element name="SundayP" msprop:Generator_UserColumnName="SundayP" msprop:Generator_ColumnPropNameInRow="SundayP" msprop:Generator_ColumnVarNameInTable="columnSundayP" msprop:Generator_ColumnPropNameInTable="SundayPColumn" type="xs:double" minOccurs="0" />
              <xs:element name="GrossTotal" msprop:Generator_UserColumnName="GrossTotal" msprop:Generator_ColumnPropNameInRow="GrossTotal" msprop:Generator_ColumnVarNameInTable="columnGrossTotal" msprop:Generator_ColumnPropNameInTable="GrossTotalColumn" type="xs:double" minOccurs="0" />
              <xs:element name="AttendanceBonus" msprop:Generator_UserColumnName="AttendanceBonus" msprop:Generator_ColumnPropNameInRow="AttendanceBonus" msprop:Generator_ColumnVarNameInTable="columnAttendanceBonus" msprop:Generator_ColumnPropNameInTable="AttendanceBonusColumn" type="xs:double" minOccurs="0" />
              <xs:element name="SpecialAllowance" msprop:Generator_UserColumnName="SpecialAllowance" msprop:Generator_ColumnPropNameInRow="SpecialAllowance" msprop:Generator_ColumnVarNameInTable="columnSpecialAllowance" msprop:Generator_ColumnPropNameInTable="SpecialAllowanceColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ExGratia" msprop:Generator_UserColumnName="ExGratia" msprop:Generator_ColumnPropNameInRow="ExGratia" msprop:Generator_ColumnVarNameInTable="columnExGratia" msprop:Generator_ColumnPropNameInTable="ExGratiaColumn" type="xs:double" minOccurs="0" />
              <xs:element name="TravellingAllowance" msprop:Generator_UserColumnName="TravellingAllowance" msprop:Generator_ColumnPropNameInRow="TravellingAllowance" msprop:Generator_ColumnVarNameInTable="columnTravellingAllowance" msprop:Generator_ColumnPropNameInTable="TravellingAllowanceColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Miscellaneous" msprop:Generator_UserColumnName="Miscellaneous" msprop:Generator_ColumnPropNameInRow="Miscellaneous" msprop:Generator_ColumnVarNameInTable="columnMiscellaneous" msprop:Generator_ColumnPropNameInTable="MiscellaneousColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Total" msprop:Generator_UserColumnName="Total" msprop:Generator_ColumnPropNameInRow="Total" msprop:Generator_ColumnVarNameInTable="columnTotal" msprop:Generator_ColumnPropNameInTable="TotalColumn" type="xs:double" minOccurs="0" />
              <xs:element name="NetPay" msprop:Generator_UserColumnName="NetPay" msprop:Generator_ColumnPropNameInRow="NetPay" msprop:Generator_ColumnVarNameInTable="columnNetPay" msprop:Generator_ColumnPropNameInTable="NetPayColumn" type="xs:double" minOccurs="0" />
              <xs:element name="WorkingDays" msprop:Generator_UserColumnName="WorkingDays" msprop:Generator_ColumnPropNameInRow="WorkingDays" msprop:Generator_ColumnVarNameInTable="columnWorkingDays" msprop:Generator_ColumnPropNameInTable="WorkingDaysColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PreasentDays" msprop:Generator_UserColumnName="PreasentDays" msprop:Generator_ColumnPropNameInRow="PreasentDays" msprop:Generator_ColumnVarNameInTable="columnPreasentDays" msprop:Generator_ColumnPropNameInTable="PreasentDaysColumn" type="xs:double" minOccurs="0" />
              <xs:element name="AbsentDays" msprop:Generator_UserColumnName="AbsentDays" msprop:Generator_ColumnPropNameInRow="AbsentDays" msprop:Generator_ColumnVarNameInTable="columnAbsentDays" msprop:Generator_ColumnPropNameInTable="AbsentDaysColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Sunday" msprop:Generator_UserColumnName="Sunday" msprop:Generator_ColumnPropNameInRow="Sunday" msprop:Generator_ColumnVarNameInTable="columnSunday" msprop:Generator_ColumnPropNameInTable="SundayColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Holiday" msprop:Generator_UserColumnName="Holiday" msprop:Generator_ColumnPropNameInRow="Holiday" msprop:Generator_ColumnVarNameInTable="columnHoliday" msprop:Generator_ColumnPropNameInTable="HolidayColumn" type="xs:double" minOccurs="0" />
              <xs:element name="LateIn" msprop:Generator_UserColumnName="LateIn" msprop:Generator_ColumnPropNameInRow="LateIn" msprop:Generator_ColumnVarNameInTable="columnLateIn" msprop:Generator_ColumnPropNameInTable="LateInColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Coff" msprop:Generator_UserColumnName="Coff" msprop:Generator_ColumnPropNameInRow="Coff" msprop:Generator_ColumnVarNameInTable="columnCoff" msprop:Generator_ColumnPropNameInTable="CoffColumn" type="xs:double" minOccurs="0" />
              <xs:element name="HalfDays" msprop:Generator_UserColumnName="HalfDays" msprop:Generator_ColumnPropNameInRow="HalfDays" msprop:Generator_ColumnVarNameInTable="columnHalfDays" msprop:Generator_ColumnPropNameInTable="HalfDaysColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PL" msprop:Generator_UserColumnName="PL" msprop:Generator_ColumnPropNameInRow="PL" msprop:Generator_ColumnVarNameInTable="columnPL" msprop:Generator_ColumnPropNameInTable="PLColumn" type="xs:double" minOccurs="0" />
              <xs:element name="LWP" msprop:Generator_UserColumnName="LWP" msprop:Generator_ColumnPropNameInRow="LWP" msprop:Generator_ColumnVarNameInTable="columnLWP" msprop:Generator_ColumnPropNameInTable="LWPColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PFofEmployee" msprop:Generator_UserColumnName="PFofEmployee" msprop:Generator_ColumnPropNameInRow="PFofEmployee" msprop:Generator_ColumnVarNameInTable="columnPFofEmployee" msprop:Generator_ColumnPropNameInTable="PFofEmployeeColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PTax" msprop:Generator_UserColumnName="PTax" msprop:Generator_ColumnPropNameInRow="PTax" msprop:Generator_ColumnVarNameInTable="columnPTax" msprop:Generator_ColumnPropNameInTable="PTaxColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PersonalLoanInstall" msprop:Generator_UserColumnName="PersonalLoanInstall" msprop:Generator_ColumnPropNameInRow="PersonalLoanInstall" msprop:Generator_ColumnVarNameInTable="columnPersonalLoanInstall" msprop:Generator_ColumnPropNameInTable="PersonalLoanInstallColumn" type="xs:double" minOccurs="0" />
              <xs:element name="MobileBill" msprop:Generator_UserColumnName="MobileBill" msprop:Generator_ColumnPropNameInRow="MobileBill" msprop:Generator_ColumnVarNameInTable="columnMobileBill" msprop:Generator_ColumnPropNameInTable="MobileBillColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Miscellaneous2" msprop:Generator_UserColumnName="Miscellaneous2" msprop:Generator_ColumnPropNameInRow="Miscellaneous2" msprop:Generator_ColumnVarNameInTable="columnMiscellaneous2" msprop:Generator_ColumnPropNameInTable="Miscellaneous2Column" type="xs:double" minOccurs="0" />
              <xs:element name="Total2" msprop:Generator_UserColumnName="Total2" msprop:Generator_ColumnPropNameInRow="Total2" msprop:Generator_ColumnVarNameInTable="columnTotal2" msprop:Generator_ColumnPropNameInTable="Total2Column" type="xs:double" minOccurs="0" />
              <xs:element name="EmpACNo" msprop:Generator_UserColumnName="EmpACNo" msprop:Generator_ColumnPropNameInRow="EmpACNo" msprop:Generator_ColumnVarNameInTable="columnEmpACNo" msprop:Generator_ColumnPropNameInTable="EmpACNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Date" msprop:Generator_UserColumnName="Date" msprop:Generator_ColumnPropNameInRow="Date" msprop:Generator_ColumnVarNameInTable="columnDate" msprop:Generator_ColumnPropNameInTable="DateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="BasicCal" msprop:Generator_UserColumnName="BasicCal" msprop:Generator_ColumnPropNameInRow="BasicCal" msprop:Generator_ColumnVarNameInTable="columnBasicCal" msprop:Generator_ColumnPropNameInTable="BasicCalColumn" type="xs:double" minOccurs="0" />
              <xs:element name="DACal" msprop:Generator_UserColumnName="DACal" msprop:Generator_ColumnPropNameInRow="DACal" msprop:Generator_ColumnVarNameInTable="columnDACal" msprop:Generator_ColumnPropNameInTable="DACalColumn" type="xs:double" minOccurs="0" />
              <xs:element name="HRACal" msprop:Generator_UserColumnName="HRACal" msprop:Generator_ColumnPropNameInRow="HRACal" msprop:Generator_ColumnVarNameInTable="columnHRACal" msprop:Generator_ColumnPropNameInTable="HRACalColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ConveyanceCal" msprop:Generator_UserColumnName="ConveyanceCal" msprop:Generator_ColumnPropNameInRow="ConveyanceCal" msprop:Generator_ColumnVarNameInTable="columnConveyanceCal" msprop:Generator_ColumnPropNameInTable="ConveyanceCalColumn" type="xs:double" minOccurs="0" />
              <xs:element name="EducationCal" msprop:Generator_UserColumnName="EducationCal" msprop:Generator_ColumnPropNameInRow="EducationCal" msprop:Generator_ColumnVarNameInTable="columnEducationCal" msprop:Generator_ColumnPropNameInTable="EducationCalColumn" type="xs:double" minOccurs="0" />
              <xs:element name="MedicalCal" msprop:Generator_UserColumnName="MedicalCal" msprop:Generator_ColumnPropNameInRow="MedicalCal" msprop:Generator_ColumnVarNameInTable="columnMedicalCal" msprop:Generator_ColumnPropNameInTable="MedicalCalColumn" type="xs:double" minOccurs="0" />
              <xs:element name="GrossTotalCal" msprop:Generator_UserColumnName="GrossTotalCal" msprop:Generator_ColumnPropNameInRow="GrossTotalCal" msprop:Generator_ColumnVarNameInTable="columnGrossTotalCal" msprop:Generator_ColumnPropNameInTable="GrossTotalCalColumn" type="xs:double" minOccurs="0" />
              <xs:element name="AttBonusType" msprop:Generator_UserColumnName="AttBonusType" msprop:Generator_ColumnPropNameInRow="AttBonusType" msprop:Generator_ColumnVarNameInTable="columnAttBonusType" msprop:Generator_ColumnPropNameInTable="AttBonusTypeColumn" type="xs:double" minOccurs="0" />
              <xs:element name="AttBonusAmt" msprop:Generator_UserColumnName="AttBonusAmt" msprop:Generator_ColumnPropNameInRow="AttBonusAmt" msprop:Generator_ColumnVarNameInTable="columnAttBonusAmt" msprop:Generator_ColumnPropNameInTable="AttBonusAmtColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PFNo" msprop:Generator_UserColumnName="PFNo" msprop:Generator_ColumnPropNameInRow="PFNo" msprop:Generator_ColumnVarNameInTable="columnPFNo" msprop:Generator_ColumnPropNameInTable="PFNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PANNo" msprop:Generator_UserColumnName="PANNo" msprop:Generator_ColumnPropNameInRow="PANNo" msprop:Generator_ColumnVarNameInTable="columnPANNo" msprop:Generator_ColumnPropNameInTable="PANNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="OTHrs1" msprop:Generator_UserColumnName="OTHrs1" msprop:Generator_ColumnPropNameInRow="OTHrs1" msprop:Generator_ColumnVarNameInTable="columnOTHrs1" msprop:Generator_ColumnPropNameInTable="OTHrs1Column" type="xs:double" minOccurs="0" />
              <xs:element name="OTRate" msprop:Generator_UserColumnName="OTRate" msprop:Generator_ColumnPropNameInRow="OTRate" msprop:Generator_ColumnVarNameInTable="columnOTRate" msprop:Generator_ColumnPropNameInTable="OTRateColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Path" msprop:Generator_UserColumnName="Path" msprop:Generator_ColumnPropNameInRow="Path" msprop:Generator_ColumnVarNameInTable="columnPath" msprop:Generator_ColumnPropNameInTable="PathColumn" type="xs:string" minOccurs="0" />
              <xs:element name="SN" msprop:Generator_UserColumnName="SN" msprop:Generator_ColumnVarNameInTable="columnSN" msprop:Generator_ColumnPropNameInRow="SN" msprop:Generator_ColumnPropNameInTable="SNColumn" type="xs:int" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>