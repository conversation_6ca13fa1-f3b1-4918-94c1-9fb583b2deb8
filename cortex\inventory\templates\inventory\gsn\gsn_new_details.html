{% extends 'base.html' %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="bg-white shadow-lg rounded-lg p-6">
    <!-- Header -->
    <div class="bg-blue-600 text-white py-3 px-4 rounded-t-lg mb-6">
        <h1 class="text-xl font-bold">{{ page_title }}</h1>
    </div>

    <!-- GIN Information -->
    <div class="mb-6 bg-gray-50 p-4 rounded-lg">
        <div class="grid grid-cols-4 gap-4 text-sm">
            <div>
                <span class="font-medium">GSN No:</span>
                <span class="text-gray-600">(Will be auto-generated)</span>
            </div>
            <div>
                <span class="font-medium">GIN No:</span>
                <span class="text-gray-900">{{ gin_no }}</span>
            </div>
            <div>
                <span class="font-medium">Challan No:</span>
                <span class="text-gray-900">{{ gin_challan_no|default:"-" }}</span>
            </div>
            <div>
                <span class="font-medium">Date:</span>
                <span class="text-gray-900">{{ gin_date|default:"-" }}</span>
            </div>
        </div>
        <div class="mt-2">
            <span class="font-medium">Supplier:</span>
            <span class="text-gray-900">{{ supplier_name|default:"-" }}</span>
        </div>
    </div>

    <!-- Service Note Form -->
    <form method="post" id="gsnForm">
        {% csrf_token %}
        
        <!-- Basic Information -->
        <div class="mb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Service Note Information</h3>
            <div class="grid grid-cols-2 gap-4">
                {{ form.sn_date.label_tag }}
                {{ form.sn_date }}
                
                {{ form.service_description.label_tag }}
                {{ form.service_description }}
            </div>
        </div>

        <!-- Service Items Table -->
        <div class="mb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Service Items</h3>
            <div class="overflow-x-auto">
                <table class="w-full table-auto bg-white border border-gray-200">
                    <thead class="bg-gray-100">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">SN</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Item Code</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Description</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">UOM</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Stock Qty</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">PO Qty</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Inward Qty</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Tot Reced Qty</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Received Qty</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        {% for item in gin_items %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-4 py-3 text-sm text-right">{{ forloop.counter }}</td>
                            <td class="px-4 py-3 text-sm text-center">{{ item.item_code }}</td>
                            <td class="px-4 py-3 text-sm">{{ item.description }}</td>
                            <td class="px-4 py-3 text-sm text-center">{{ item.uom }}</td>
                            <td class="px-4 py-3 text-sm text-right">{{ item.stock_qty|floatformat:3 }}</td>
                            <td class="px-4 py-3 text-sm text-right">{{ item.po_qty|floatformat:3 }}</td>
                            <td class="px-4 py-3 text-sm text-right">{{ item.gin_qty|floatformat:3 }}</td>
                            <td class="px-4 py-3 text-sm text-right">{{ item.total_gsn_qty|floatformat:3 }}</td>
                            <td class="px-4 py-3 text-sm text-center">
                                <input type="number" 
                                       name="received_qty[]" 
                                       step="0.001" 
                                       min="0" 
                                       max="{{ item.balance_qty }}"
                                       placeholder="0.000"
                                       class="w-20 px-2 py-1 border border-gray-300 rounded text-right focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <input type="hidden" name="po_id[]" value="{{ item.po_id }}">
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="9" class="px-4 py-8 text-center text-gray-500">
                                <div class="text-lg font-medium">No service items found!</div>
                                <div class="text-sm">No service items available for this GIN record.</div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-4 pt-4 border-t border-gray-200">
            <a href="{% url 'inventory:gsn_new_list' %}" 
               class="px-6 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 font-medium">
                Cancel
            </a>
            <button type="submit" 
                    class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 font-medium">
                Create GSN
            </button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('gsnForm');
    const qtyInputs = document.querySelectorAll('input[name="received_qty[]"]');
    
    form.addEventListener('submit', function(e) {
        let hasQuantity = false;
        qtyInputs.forEach(input => {
            if (parseFloat(input.value || 0) > 0) {
                hasQuantity = true;
            }
        });
        
        if (!hasQuantity) {
            e.preventDefault();
            alert('Please enter at least one received quantity.');
            return false;
        }
    });
    
    // Auto-focus first quantity input
    if (qtyInputs.length > 0) {
        qtyInputs[0].focus();
    }
});
</script>
{% endblock %}