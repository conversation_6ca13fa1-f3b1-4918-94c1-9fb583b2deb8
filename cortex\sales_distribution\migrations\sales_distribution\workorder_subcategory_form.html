<!-- sales_distribution/templates/sales_distribution/workorder_subcategory_form.html -->
<!-- Work Order Sub-Category Form - SAP S/4HANA Inspired Design -->

{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }} - Cortex{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-sap-purple-500 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="git-branch" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">{{ form_title }}</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Configure work order sub-category details</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'sales_distribution:subcategory_list' %}" 
                   class="inline-flex items-center px-4 py-2.5 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 hover:border-sap-purple-300 transition-all duration-200">
                    <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                    Back to Sub-Categories
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-2 py-4">
    <div class="max-w-2xl mx-auto">
        
        <!-- Sub-Category Form Card -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="git-branch" class="w-4 h-4 text-sap-purple-600"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-sap-gray-800">{{ form_title }}</h3>
                        <p class="text-sm text-sap-gray-600">Enter sub-category information below</p>
                    </div>
                </div>
            </div>
            
            <form method="post" class="px-6 py-6">
                {% csrf_token %}
                
                {% if form.errors %}
                <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i data-lucide="alert-circle" class="w-5 h-5 text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">
                                Please correct the following errors:
                            </h3>
                            <div class="mt-2 text-sm text-red-700">
                                <ul class="list-disc pl-5 space-y-1">
                                    {% for field, errors in form.errors.items %}
                                        {% for error in errors %}
                                            <li>{{ field|title }}: {{ error }}</li>
                                        {% endfor %}
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <div class="space-y-6">
                    
                    <!-- Parent Category Field -->
                    <div>
                        <label for="{{ form.cid.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Parent Category <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            {{ form.cid }}
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i data-lucide="chevron-down" class="w-4 h-4 text-sap-gray-400"></i>
                            </div>
                        </div>
                        {% if form.cid.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.cid.errors.0 }}</p>
                        {% endif %}
                        <p class="mt-1 text-xs text-sap-gray-500">Select the parent category for this sub-category</p>
                    </div>
                    
                    <!-- Sub-Category Name Field -->
                    <div>
                        <label for="{{ form.scname.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Sub-Category Name <span class="text-red-500">*</span>
                        </label>
                        {{ form.scname }}
                        {% if form.scname.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.scname.errors.0 }}</p>
                        {% endif %}
                        <p class="mt-1 text-xs text-sap-gray-500">Enter a descriptive name for this sub-category</p>
                    </div>
                    
                </div>
                
                <!-- Form Actions -->
                <div class="flex items-center justify-end space-x-3 pt-8 border-t border-sap-gray-100 mt-8">
                    <a href="{% url 'sales_distribution:subcategory_list' %}" 
                       class="px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 transition-colors duration-200">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="px-6 py-2 bg-sap-purple-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-sap-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-purple-500 transition-colors duration-200">
                        {% if object %}
                            <i data-lucide="save" class="w-4 h-4 mr-2 inline"></i>
                            Update Sub-Category
                        {% else %}
                            <i data-lucide="plus" class="w-4 h-4 mr-2 inline"></i>
                            Create Sub-Category
                        {% endif %}
                    </button>
                </div>
                
            </form>
        </div>
        
    </div>
</div>

<script>
// Form validation and enhancements
document.addEventListener('DOMContentLoaded', function() {
    // Focus on first input field
    const firstInput = document.querySelector('input, select');
    if (firstInput) {
        firstInput.focus();
    }
    
    // Add form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const categoryField = document.getElementById('{{ form.cid.id_for_label }}');
        const nameField = document.getElementById('{{ form.scname.id_for_label }}');
        
        if (!categoryField.value) {
            e.preventDefault();
            alert('Please select a parent category.');
            categoryField.focus();
            return false;
        }
        
        if (!nameField.value.trim()) {
            e.preventDefault();
            alert('Please enter a sub-category name.');
            nameField.focus();
            return false;
        }
        
        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 mr-2 inline animate-spin"></i>Saving...';
    });
});
</script>
{% endblock %}