{% extends "core/base.html" %}
{% load static %}

{% block title %}{{ title }} - Inventory Management{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-4">
                <li>
                    <div>
                        <a href="{% url 'inventory:location_list' %}" class="text-gray-400 hover:text-gray-500">
                            <svg class="flex-shrink-0 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                            </svg>
                            <span class="sr-only">Home</span>
                        </a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                        </svg>
                        <a href="{% url 'inventory:location_list' %}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">Locations</a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                        </svg>
                        <span class="ml-4 text-sm font-medium text-gray-500">{{ title }}</span>
                    </div>
                </li>
            </ol>
        </nav>
        <h1 class="mt-2 text-3xl font-bold text-gray-900">{{ title }}</h1>
    </div>

    <!-- Form -->
    <div class="bg-white shadow rounded-lg">
        <form method="post" class="space-y-6 p-6">
            {% csrf_token %}
            
            {% if form.non_field_errors %}
                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">There were errors with your submission</h3>
                            <div class="mt-2 text-sm text-red-700">
                                <ul class="list-disc pl-5 space-y-1">
                                    {% for error in form.non_field_errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <!-- Location Code -->
                <div>
                    <label for="{{ form.location_code.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Location Code <span class="text-red-500">*</span>
                    </label>
                    <div class="mt-1">
                        {{ form.location_code }}
                        {% if form.location_code.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.location_code.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Location Name -->
                <div>
                    <label for="{{ form.location_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Location Name <span class="text-red-500">*</span>
                    </label>
                    <div class="mt-1">
                        {{ form.location_name }}
                        {% if form.location_name.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.location_name.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Location Type -->
                <div>
                    <label for="{{ form.location_type.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Location Type <span class="text-red-500">*</span>
                    </label>
                    <div class="mt-1">
                        {{ form.location_type }}
                        {% if form.location_type.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.location_type.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Parent Location -->
                <div>
                    <label for="{{ form.parent_location.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Parent Location
                    </label>
                    <div class="mt-1">
                        {{ form.parent_location }}
                        {% if form.parent_location.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.parent_location.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Warehouse Code -->
                <div>
                    <label for="{{ form.warehouse_code.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Warehouse Code
                    </label>
                    <div class="mt-1">
                        {{ form.warehouse_code }}
                        {% if form.warehouse_code.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.warehouse_code.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Zone Code -->
                <div>
                    <label for="{{ form.zone_code.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Zone Code
                    </label>
                    <div class="mt-1">
                        {{ form.zone_code }}
                        {% if form.zone_code.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.zone_code.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Bin Code -->
                <div>
                    <label for="{{ form.bin_code.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Bin Code
                    </label>
                    <div class="mt-1">
                        {{ form.bin_code }}
                        {% if form.bin_code.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.bin_code.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Capacity -->
                <div>
                    <label for="{{ form.capacity.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Capacity
                    </label>
                    <div class="mt-1">
                        {{ form.capacity }}
                        {% if form.capacity.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.capacity.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Active Status -->
            <div class="flex items-center">
                <div class="flex items-center h-5">
                    {{ form.is_active }}
                </div>
                <div class="ml-3 text-sm">
                    <label for="{{ form.is_active.id_for_label }}" class="font-medium text-gray-700">
                        Active
                    </label>
                    <p class="text-gray-500">Enable this location for inventory operations</p>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{% url 'inventory:location_list' %}" 
                   class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Cancel
                </a>
                <button type="submit" 
                        class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    {{ submit_text }}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const locationTypeField = document.getElementById('{{ form.location_type.id_for_label }}');
        const parentLocationField = document.getElementById('{{ form.parent_location.id_for_label }}');
        const warehouseCodeField = document.getElementById('{{ form.warehouse_code.id_for_label }}');
        const zoneCodeField = document.getElementById('{{ form.zone_code.id_for_label }}');
        const binCodeField = document.getElementById('{{ form.bin_code.id_for_label }}');

        function updateFieldsBasedOnType() {
            const selectedType = locationTypeField.value;
            
            // Reset required indicators
            document.querySelectorAll('.required-indicator').forEach(el => el.remove());
            
            if (selectedType === 'WAREHOUSE') {
                parentLocationField.disabled = true;
                warehouseCodeField.required = true;
                zoneCodeField.required = false;
                binCodeField.required = false;
                
                // Add visual indicators
                warehouseCodeField.parentElement.parentElement.querySelector('label').innerHTML += 
                    ' <span class="text-red-500 required-indicator">*</span>';
            } else if (selectedType === 'ZONE') {
                parentLocationField.disabled = false;
                warehouseCodeField.required = false;
                zoneCodeField.required = true;
                binCodeField.required = false;
                
                zoneCodeField.parentElement.parentElement.querySelector('label').innerHTML += 
                    ' <span class="text-red-500 required-indicator">*</span>';
            } else if (selectedType === 'BIN') {
                parentLocationField.disabled = false;
                warehouseCodeField.required = false;
                zoneCodeField.required = false;
                binCodeField.required = true;
                
                binCodeField.parentElement.parentElement.querySelector('label').innerHTML += 
                    ' <span class="text-red-500 required-indicator">*</span>';
            }
        }

        locationTypeField.addEventListener('change', updateFieldsBasedOnType);
        updateFieldsBasedOnType(); // Run on page load
    });
</script>
{% endblock %}