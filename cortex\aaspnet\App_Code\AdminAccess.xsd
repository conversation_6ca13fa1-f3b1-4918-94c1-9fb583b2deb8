﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="AdminAccess" targetNamespace="http://tempuri.org/AdminAccess.xsd" xmlns:mstns="http://tempuri.org/AdminAccess.xsd" xmlns="http://tempuri.org/AdminAccess.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="AdminAccess" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="AdminAccess" msprop:Generator_DataSetName="AdminAccess">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" msprop:Generator_UserColumnName="Id" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnPropNameInTable="IdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="FinYear" msprop:Generator_UserColumnName="FinYear" msprop:Generator_ColumnVarNameInTable="columnFinYear" msprop:Generator_ColumnPropNameInRow="FinYear" msprop:Generator_ColumnPropNameInTable="FinYearColumn" type="xs:string" minOccurs="0" />
              <xs:element name="EmployeeName" msprop:Generator_UserColumnName="EmployeeName" msprop:Generator_ColumnVarNameInTable="columnEmployeeName" msprop:Generator_ColumnPropNameInRow="EmployeeName" msprop:Generator_ColumnPropNameInTable="EmployeeNameColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Department" msprop:Generator_UserColumnName="Department" msprop:Generator_ColumnVarNameInTable="columnDepartment" msprop:Generator_ColumnPropNameInRow="Department" msprop:Generator_ColumnPropNameInTable="DepartmentColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Desination" msprop:Generator_UserColumnName="Desination" msprop:Generator_ColumnVarNameInTable="columnDesination" msprop:Generator_ColumnPropNameInRow="Desination" msprop:Generator_ColumnPropNameInTable="DesinationColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ContactNo" msprop:Generator_UserColumnName="ContactNo" msprop:Generator_ColumnVarNameInTable="columnContactNo" msprop:Generator_ColumnPropNameInRow="ContactNo" msprop:Generator_ColumnPropNameInTable="ContactNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CompanyEmail" msprop:Generator_UserColumnName="CompanyEmail" msprop:Generator_ColumnVarNameInTable="columnCompanyEmail" msprop:Generator_ColumnPropNameInRow="CompanyEmail" msprop:Generator_ColumnPropNameInTable="CompanyEmailColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ExtNo" msprop:Generator_UserColumnName="ExtNo" msprop:Generator_ColumnVarNameInTable="columnExtNo" msprop:Generator_ColumnPropNameInRow="ExtNo" msprop:Generator_ColumnPropNameInTable="ExtNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ModName" msprop:Generator_UserColumnName="ModName" msprop:Generator_ColumnVarNameInTable="columnModName" msprop:Generator_ColumnPropNameInRow="ModName" msprop:Generator_ColumnPropNameInTable="ModNameColumn" type="xs:string" minOccurs="0" />
              <xs:element name="SubModName" msprop:Generator_UserColumnName="SubModName" msprop:Generator_ColumnVarNameInTable="columnSubModName" msprop:Generator_ColumnPropNameInRow="SubModName" msprop:Generator_ColumnPropNameInTable="SubModNameColumn" type="xs:string" minOccurs="0" />
              <xs:element name="AccessType" msprop:Generator_UserColumnName="AccessType" msprop:Generator_ColumnVarNameInTable="columnAccessType" msprop:Generator_ColumnPropNameInRow="AccessType" msprop:Generator_ColumnPropNameInTable="AccessTypeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Access" msprop:Generator_UserColumnName="Access" msprop:Generator_ColumnVarNameInTable="columnAccess" msprop:Generator_ColumnPropNameInRow="Access" msprop:Generator_ColumnPropNameInTable="AccessColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Type" msprop:Generator_UserColumnName="Type" msprop:Generator_ColumnPropNameInRow="Type" msprop:Generator_ColumnVarNameInTable="columnType" msprop:Generator_ColumnPropNameInTable="TypeColumn" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>