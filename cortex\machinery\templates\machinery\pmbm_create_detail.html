{% extends "core/base.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-gradient-to-r from-green-600 to-green-800 rounded-lg shadow-lg mb-6">
            <div class="px-6 py-4">
                <h1 class="text-2xl font-bold text-white">{{ title }}</h1>
                <p class="text-green-100 mt-1">Machine: {{ machine.make }} {{ machine.model }} | Location: {{ machine.location }}</p>
            </div>
        </div>

        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <!-- Machine Information -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4">Machine Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Item Code</label>
                        <div class="mt-1 p-2 bg-gray-50 border border-gray-200 rounded-md text-sm">
                            {{ item.itemcode }}
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Description</label>
                        <div class="mt-1 p-2 bg-gray-50 border border-gray-200 rounded-md text-sm">
                            {{ item.description|truncatechars:50 }}
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">PM Days</label>
                        <div class="mt-1 p-2 bg-gray-50 border border-gray-200 rounded-md text-sm">
                            {{ machine.pmdays|default:"Not set" }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Maintenance Details -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4">Maintenance Details</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- PMBM Type -->
                    <div>
                        <label for="{{ form.pmbm.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            Maintenance Type <span class="text-red-500">*</span>
                        </label>
                        {{ form.pmbm }}
                        {% if form.pmbm.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.pmbm.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- From Date -->
                    <div>
                        <label for="{{ form.fromdate.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            From Date <span class="text-red-500">*</span>
                        </label>
                        {{ form.fromdate }}
                        {% if form.fromdate.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.fromdate.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- To Date -->
                    <div>
                        <label for="{{ form.todate.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            To Date <span class="text-red-500">*</span>
                        </label>
                        {{ form.todate }}
                        {% if form.todate.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.todate.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- From Time -->
                    <div>
                        <label for="{{ form.fromtime.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            From Time
                        </label>
                        {{ form.fromtime }}
                        {% if form.fromtime.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.fromtime.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- To Time -->
                    <div>
                        <label for="{{ form.totime.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            To Time
                        </label>
                        {{ form.totime }}
                        {% if form.totime.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.totime.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- Name of Agency -->
                    <div>
                        <label for="{{ form.nameofagency.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            Name of Agency
                        </label>
                        {{ form.nameofagency }}
                        {% if form.nameofagency.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.nameofagency.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- Name of Engineer -->
                    <div>
                        <label for="{{ form.nameofengineer.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            Name of Engineer
                        </label>
                        {{ form.nameofengineer }}
                        {% if form.nameofengineer.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.nameofengineer.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- Next PM Due On -->
                    <div>
                        <label for="{{ form.nextpmdueon.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            Next PM Due On
                        </label>
                        {{ form.nextpmdueon }}
                        {% if form.nextpmdueon.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.nextpmdueon.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- Next BM Due On -->
                    <div>
                        <label for="{{ form.nextbmdueon.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            Next BM Due On
                        </label>
                        {{ form.nextbmdueon }}
                        {% if form.nextbmdueon.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.nextbmdueon.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- Remarks -->
                    <div class="md:col-span-2 lg:col-span-3">
                        <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            Remarks
                        </label>
                        {{ form.remarks }}
                        {% if form.remarks.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.remarks.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Machine Spare Parts -->
            {% if machine_spares %}
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4">Machine Spare Parts</h2>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Item Code
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Description
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Standard Qty
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Used Qty
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for spare in machine_spares %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ spare.item.itemcode|default:"-" }}
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-900">
                                    {{ spare.item.description|default:"-"|truncatechars:60 }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                                    {{ spare.qty }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="number" 
                                           name="used_qty_{{ spare.id }}"
                                           step="0.01"
                                           min="0"
                                           class="w-20 text-sm border-gray-300 rounded-md"
                                           placeholder="0.00">
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            {% endif %}

            <!-- Maintenance History -->
            {% if maintenance_history %}
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4">Recent Maintenance History</h2>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Date
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Type
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Agency
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Engineer
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Remarks
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for maintenance in maintenance_history %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ maintenance.sysdate|date:"d/m/Y" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {% if maintenance.pmbm == 0 %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Preventive
                                        </span>
                                    {% else %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            Breakdown
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ maintenance.nameofagency|default:"-" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ maintenance.nameofengineer|default:"-" }}
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500">
                                    {{ maintenance.remarks|default:"-"|truncatechars:50 }}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            {% endif %}

            <!-- Form Actions -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex justify-between">
                    <a href="{% url 'machinery:pmbm_list' %}" 
                       class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">
                        Cancel
                    </a>
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        Create Maintenance Record
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script src="{% static 'js/htmx.min.js' %}"></script>
<script>
// Auto-calculate next PM due date
document.addEventListener('DOMContentLoaded', function() {
    const fromDateInput = document.querySelector('input[name="fromdate"]');
    const nextPmInput = document.querySelector('input[name="nextpmdueon"]');
    const pmDays = {{ machine.pmdays|default:0 }};
    
    if (fromDateInput && nextPmInput && pmDays > 0) {
        fromDateInput.addEventListener('change', function() {
            if (this.value) {
                const fromDate = new Date(this.value);
                fromDate.setDate(fromDate.getDate() + pmDays);
                nextPmInput.value = fromDate.toISOString().split('T')[0];
            }
        });
    }
});
</script>
{% endblock %}