{% extends "core/base.html" %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
/* SAP Fiori-inspired CSS for Enquiry Update - Inherits styles from create template */
.sap-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
}

.sap-header {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    padding: 32px 0;
    margin-bottom: 24px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);
}

.sap-header h1 {
    font-size: 2.5rem;
    font-weight: 300;
    margin: 0;
    letter-spacing: -0.02em;
}

.sap-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 8px 0 0;
}

.sap-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid #e5e7eb;
    overflow: hidden;
    margin-bottom: 24px;
}

.sap-card-header {
    background: #fef3c7;
    padding: 20px 24px;
    border-bottom: 1px solid #f59e0b;
    display: flex;
    align-items: center;
    gap: 12px;
}

.sap-card-header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #92400e;
}

.sap-card-body {
    padding: 24px;
}

.sap-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
}

.sap-form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.sap-form-group label {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.sap-input, .sap-select, .sap-textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: white;
}

.sap-input:focus, .sap-select:focus, .sap-textarea:focus {
    outline: none;
    border-color: #f59e0b;
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

.sap-input:disabled {
    background: #f3f4f6;
    color: #6b7280;
    cursor: not-allowed;
}

.sap-textarea {
    resize: vertical;
    min-height: 80px;
}

.address-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;
    margin-top: 24px;
}

.address-card {
    background: #fafbfc;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    padding: 20px;
}

.address-card h3 {
    margin: 0 0 16px;
    font-size: 1.1rem;
    font-weight: 700;
    color: #364574;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.address-card .sap-form-group {
    margin-bottom: 16px;
}

.business-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.enquiry-details {
    grid-column: 1 / -1;
}

.file-upload-section {
    background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
    border: 2px dashed #fb923c;
    border-radius: 12px;
    padding: 32px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-upload-section:hover {
    background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
    border-color: #f97316;
}

.file-upload-section .upload-icon {
    font-size: 2.5rem;
    margin-bottom: 12px;
    display: block;
}

.file-upload-section p {
    margin: 0;
    font-weight: 600;
    color: #9a3412;
}

.existing-attachments {
    margin-bottom: 24px;
}

.attachment-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;
}

.attachment-item {
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.attachment-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    background: white;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e5e7eb;
}

.attachment-info {
    flex: 1;
}

.attachment-name {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.875rem;
    margin-bottom: 2px;
}

.attachment-size {
    color: #6b7280;
    font-size: 0.75rem;
}

.attachment-actions {
    display: flex;
    gap: 8px;
}

.attachment-btn {
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    color: white;
}

.attachment-download {
    background: #0070f3;
}

.attachment-delete {
    background: #dc2626;
}

.file-list {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.file-item {
    background: #1f2937;
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.file-item .remove-file {
    cursor: pointer;
    font-weight: bold;
    opacity: 0.7;
}

.file-item .remove-file:hover {
    opacity: 1;
}

.sap-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    padding: 32px 0;
}

.sap-btn {
    padding: 12px 32px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.sap-btn-primary {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.sap-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(245, 158, 11, 0.4);
}

.sap-btn-secondary {
    background: #6b7280;
    color: white;
}

.sap-btn-secondary:hover {
    background: #4b5563;
    transform: translateY(-1px);
}

.error-message {
    color: #dc2626;
    font-size: 0.875rem;
    margin-top: 4px;
}

.required-field::after {
    content: " *";
    color: #dc2626;
    font-weight: bold;
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}

.success-message {
    background: #10b981;
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-section {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.edit-notice {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border: 2px solid #f59e0b;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.edit-notice-icon {
    font-size: 1.5rem;
    color: #d97706;
}

.edit-notice-text {
    color: #92400e;
    margin: 0;
    font-weight: 600;
}

.navigation-breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 24px;
    color: #6b7280;
    font-size: 0.875rem;
}

.navigation-breadcrumb a {
    color: #f59e0b;
    text-decoration: none;
}

.navigation-breadcrumb a:hover {
    text-decoration: underline;
}

/* Responsive design */
@media (max-width: 768px) {
    .sap-form-grid {
        grid-template-columns: 1fr;
    }
    
    .address-sections {
        grid-template-columns: 1fr;
    }
    
    .business-info-grid {
        grid-template-columns: 1fr;
    }
    
    .attachment-list {
        grid-template-columns: 1fr;
    }
    
    .sap-actions {
        flex-direction: column;
        align-items: center;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="sap-container" x-data="enquiryUpdateForm()">
    <!-- Navigation Breadcrumb -->
    <div class="navigation-breadcrumb">
        <a href="{% url 'sales_distribution:enquiry_list' %}">📋 Enquiries</a>
        <span>›</span>
        <a href="{% url 'sales_distribution:enquiry_detail' enquiry.enqid %}">Enquiry #{{ enquiry.enqid }}</a>
        <span>›</span>
        <span>Edit</span>
    </div>

    <!-- Header Section -->
    <div class="sap-header">
        <div class="text-center">
            <h1>{{ page_title }}</h1>
            <p>{{ page_subtitle }}</p>
        </div>
    </div>

    <!-- Edit Notice -->
    <div class="edit-notice form-section">
        <div class="edit-notice-icon">⚠️</div>
        <div>
            <p class="edit-notice-text">You are editing Enquiry #{{ enquiry.enqid }}</p>
            <p style="margin: 4px 0 0; color: #92400e; font-size: 0.875rem; font-weight: normal;">
                Make sure to review all changes before submitting.
            </p>
        </div>
    </div>

    <!-- Main Form -->
    <form method="post" enctype="multipart/form-data" @submit="handleSubmit">
        {% csrf_token %}
        
        <!-- Customer Information (Read-only for existing enquiries) -->
        <div class="sap-card form-section">
            <div class="sap-card-header">
                <span style="font-size: 1.5rem;">👤</span>
                <h2>Customer Information</h2>
            </div>
            <div class="sap-card-body">
                <div class="sap-form-grid">
                    <div class="sap-form-group">
                        <label for="{{ form.customername.id_for_label }}" class="required-field">Customer Name</label>
                        {{ form.customername }}
                        {% if form.customername.errors %}
                            <div class="error-message">{{ form.customername.errors.0 }}</div>
                        {% endif %}
                        <small style="color: #6b7280; font-size: 0.75rem;">
                            💡 Customer name can be modified
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="sap-card form-section">
            <div class="sap-card-header">
                <span style="font-size: 1.5rem;">📞</span>
                <h2>Contact Details</h2>
            </div>
            <div class="sap-card-body">
                <div class="sap-form-grid">
                    <div class="sap-form-group">
                        <label for="{{ form.contactperson.id_for_label }}">Contact Person</label>
                        {{ form.contactperson }}
                    </div>
                    
                    <div class="sap-form-group">
                        <label for="{{ form.email.id_for_label }}">Email Address</label>
                        {{ form.email }}
                        {% if form.email.errors %}
                            <div class="error-message">{{ form.email.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="sap-form-group">
                        <label for="{{ form.contactno.id_for_label }}">Contact Number</label>
                        {{ form.contactno }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Address Details -->
        <div class="sap-card form-section">
            <div class="sap-card-header">
                <span style="font-size: 1.5rem;">🏢</span>
                <h2>Address Details</h2>
            </div>
            <div class="sap-card-body">
                <div class="address-sections">
                    <!-- Registered Office -->
                    <div class="address-card">
                        <h3>🏛️ REGD. OFFICE</h3>
                        
                        <div class="sap-form-group">
                            <label for="{{ form.regdaddress.id_for_label }}" class="required-field">Address</label>
                            {{ form.regdaddress }}
                            {% if form.regdaddress.errors %}
                                <div class="error-message">{{ form.regdaddress.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="sap-form-group">
                            <label for="{{ form.regdcountry.id_for_label }}">Country</label>
                            {{ form.regdcountry }}
                        </div>
                        
                        <div class="sap-form-group">
                            <label for="{{ form.regdstate.id_for_label }}">State</label>
                            {{ form.regdstate }}
                        </div>
                        
                        <div class="sap-form-group">
                            <label for="{{ form.regdcity.id_for_label }}">City</label>
                            {{ form.regdcity }}
                        </div>
                        
                        <div class="sap-form-group">
                            <label for="{{ form.regdpinno.id_for_label }}">PIN Code</label>
                            {{ form.regdpinno }}
                        </div>
                        
                        <div class="sap-form-group">
                            <label for="{{ form.regdcontactno.id_for_label }}">Contact Number</label>
                            {{ form.regdcontactno }}
                        </div>
                        
                        <div class="sap-form-group">
                            <label for="{{ form.regdfaxno.id_for_label }}">Fax Number</label>
                            {{ form.regdfaxno }}
                        </div>
                    </div>

                    <!-- Works/Factory -->
                    <div class="address-card">
                        <h3>🏭 WORKS/FACTORY</h3>
                        
                        <div class="sap-form-group">
                            <label for="{{ form.workaddress.id_for_label }}">Address</label>
                            {{ form.workaddress }}
                        </div>
                        
                        <div class="sap-form-group">
                            <label for="{{ form.workcountry.id_for_label }}">Country</label>
                            {{ form.workcountry }}
                        </div>
                        
                        <div class="sap-form-group">
                            <label for="{{ form.workstate.id_for_label }}">State</label>
                            {{ form.workstate }}
                        </div>
                        
                        <div class="sap-form-group">
                            <label for="{{ form.workcity.id_for_label }}">City</label>
                            {{ form.workcity }}
                        </div>
                        
                        <div class="sap-form-group">
                            <label for="{{ form.workpinno.id_for_label }}">PIN Code</label>
                            {{ form.workpinno }}
                        </div>
                        
                        <div class="sap-form-group">
                            <label for="{{ form.workcontactno.id_for_label }}">Contact Number</label>
                            {{ form.workcontactno }}
                        </div>
                        
                        <div class="sap-form-group">
                            <label for="{{ form.workfaxno.id_for_label }}">Fax Number</label>
                            {{ form.workfaxno }}
                        </div>
                    </div>

                    <!-- Material Delivery -->
                    <div class="address-card">
                        <h3>🚚 MATERIAL DELIVERY</h3>
                        
                        <div class="sap-form-group">
                            <label for="{{ form.materialdeladdress.id_for_label }}">Address</label>
                            {{ form.materialdeladdress }}
                        </div>
                        
                        <div class="sap-form-group">
                            <label for="{{ form.materialdelcountry.id_for_label }}">Country</label>
                            {{ form.materialdelcountry }}
                        </div>
                        
                        <div class="sap-form-group">
                            <label for="{{ form.materialdelstate.id_for_label }}">State</label>
                            {{ form.materialdelstate }}
                        </div>
                        
                        <div class="sap-form-group">
                            <label for="{{ form.materialdelcity.id_for_label }}">City</label>
                            {{ form.materialdelcity }}
                        </div>
                        
                        <div class="sap-form-group">
                            <label for="{{ form.materialdelpinno.id_for_label }}">PIN Code</label>
                            {{ form.materialdelpinno }}
                        </div>
                        
                        <div class="sap-form-group">
                            <label for="{{ form.materialdelcontactno.id_for_label }}">Contact Number</label>
                            {{ form.materialdelcontactno }}
                        </div>
                        
                        <div class="sap-form-group">
                            <label for="{{ form.materialdelfaxno.id_for_label }}">Fax Number</label>
                            {{ form.materialdelfaxno }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Business Information -->
        <div class="sap-card form-section">
            <div class="sap-card-header">
                <span style="font-size: 1.5rem;">💼</span>
                <h2>Business Information</h2>
            </div>
            <div class="sap-card-body">
                <div class="business-info-grid">
                    <div class="sap-form-group">
                        <label for="{{ form.juridictioncode.id_for_label }}">Jurisdiction Code</label>
                        {{ form.juridictioncode }}
                    </div>
                    
                    <div class="sap-form-group">
                        <label for="{{ form.eccno.id_for_label }}">ECC Number</label>
                        {{ form.eccno }}
                    </div>
                    
                    <div class="sap-form-group">
                        <label for="{{ form.range.id_for_label }}">Range</label>
                        {{ form.range }}
                    </div>
                    
                    <div class="sap-form-group">
                        <label for="{{ form.commissionurate.id_for_label }}">Commission Rate</label>
                        {{ form.commissionurate }}
                    </div>
                    
                    <div class="sap-form-group">
                        <label for="{{ form.divn.id_for_label }}">Division</label>
                        {{ form.divn }}
                    </div>
                    
                    <div class="sap-form-group">
                        <label for="{{ form.panno.id_for_label }}">PAN Number</label>
                        {{ form.panno }}
                        {% if form.panno.errors %}
                            <div class="error-message">{{ form.panno.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="sap-form-group">
                        <label for="{{ form.tinvatno.id_for_label }}">TIN/VAT Number</label>
                        {{ form.tinvatno }}
                    </div>
                    
                    <div class="sap-form-group">
                        <label for="{{ form.tincstno.id_for_label }}">TIN/CST Number</label>
                        {{ form.tincstno }}
                    </div>
                    
                    <div class="sap-form-group">
                        <label for="{{ form.tdscode.id_for_label }}">TDS Code</label>
                        {{ form.tdscode }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Enquiry Details -->
        <div class="sap-card form-section">
            <div class="sap-card-header">
                <span style="font-size: 1.5rem;">📝</span>
                <h2>Enquiry Details</h2>
            </div>
            <div class="sap-card-body">
                <div class="enquiry-details">
                    <div class="sap-form-group">
                        <label for="{{ form.enquiryfor.id_for_label }}" class="required-field">Enquiry Description</label>
                        {{ form.enquiryfor }}
                        {% if form.enquiryfor.errors %}
                            <div class="error-message">{{ form.enquiryfor.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="sap-form-group">
                        <label for="{{ form.remark.id_for_label }}">Additional Remarks</label>
                        {{ form.remark }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Existing Attachments and New File Upload -->
        <div class="sap-card form-section">
            <div class="sap-card-header">
                <span style="font-size: 1.5rem;">📎</span>
                <h2>Attachments</h2>
            </div>
            <div class="sap-card-body">
                <!-- Existing Attachments -->
                {% if enquiry.enquiryattachment_set.all %}
                <div class="existing-attachments">
                    <h4 style="margin: 0 0 16px; color: #374151;">Existing Attachments:</h4>
                    <div class="attachment-list">
                        {% for attachment in enquiry.enquiryattachment_set.all %}
                        <div class="attachment-item">
                            <div class="attachment-icon">
                                {% if ".pdf" in attachment.filename|lower %}
                                    📄
                                {% elif ".doc" in attachment.filename|lower or ".docx" in attachment.filename|lower %}
                                    📝
                                {% elif ".xls" in attachment.filename|lower or ".xlsx" in attachment.filename|lower %}
                                    📊
                                {% elif ".jpg" in attachment.filename|lower or ".jpeg" in attachment.filename|lower or ".png" in attachment.filename|lower %}
                                    🖼️
                                {% else %}
                                    📁
                                {% endif %}
                            </div>
                            <div class="attachment-info">
                                <div class="attachment-name">{{ attachment.filename }}</div>
                                <div class="attachment-size">
                                    {% if attachment.filesize %}
                                        {{ attachment.filesize|filesizeformat }}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="attachment-actions">
                                <a href="#" class="attachment-btn attachment-download">💾 Download</a>
                                <button type="button" 
                                        class="attachment-btn attachment-delete"
                                        hx-delete="{% url 'sales_distribution:attachment_delete' attachment.id %}"
                                        hx-confirm="Are you sure you want to delete this attachment?">
                                    🗑️ Delete
                                </button>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                
                <!-- New File Upload -->
                <div class="file-upload-section" @click="$refs.fileInput.click()">
                    <span class="upload-icon">📎</span>
                    <p>Click to upload additional files or drag and drop</p>
                    <small>Supported: PDF, DOC, DOCX, JPG, PNG, XLS, XLSX (Max 10MB each)</small>
                </div>
                
                {{ form.attachments }}
                
                <div x-show="selectedFiles.length > 0" class="file-list">
                    <template x-for="(file, index) in selectedFiles" :key="index">
                        <div class="file-item">
                            <span x-text="file.name"></span>
                            <span class="remove-file" @click.stop="removeFile(index)">×</span>
                        </div>
                    </template>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="sap-actions">
            <button type="submit" class="sap-btn sap-btn-primary" :class="{ 'loading': isSubmitting }">
                <span x-show="!isSubmitting">💾 Update Enquiry</span>
                <span x-show="isSubmitting">⏳ Updating...</span>
            </button>
            
            <a href="{% url 'sales_distribution:enquiry_detail' enquiry.enqid %}" class="sap-btn sap-btn-secondary">
                ❌ Cancel
            </a>
        </div>
    </form>
</div>

<script>
function enquiryUpdateForm() {
    return {
        selectedFiles: [],
        isSubmitting: false,

        handleFileSelect(event) {
            const files = Array.from(event.target.files);
            this.selectedFiles = files;
        },

        removeFile(index) {
            this.selectedFiles.splice(index, 1);
            // Update the file input
            const fileInput = this.$refs.fileInput;
            const dt = new DataTransfer();
            this.selectedFiles.forEach(file => dt.items.add(file));
            fileInput.files = dt.files;
        },

        handleSubmit(event) {
            this.isSubmitting = true;
        }
    }
}
</script>
{% endblock %}
