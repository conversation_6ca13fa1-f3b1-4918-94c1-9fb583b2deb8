<!-- accounts/templates/accounts/reports/partials/vat_register_table.html -->
<!-- VAT Register Table Partial Template for HTMX Updates -->

<div class="overflow-x-auto">
    <table class="w-full">
        <thead class="bg-sap-gray-50">
            <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    {% if register_type == 'purchase' %}Bill Details{% else %}Invoice Details{% endif %}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    {% if register_type == 'purchase' %}Supplier{% else %}Customer{% endif %}
                </th>
                <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Basic Amount</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">VAT Amount</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Other Taxes</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Total Amount</th>
                <th class="px-6 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-sap-gray-200">
            {% for record in vat_register_data %}
            <tr class="hover:bg-sap-gray-50 transition-colors" 
                x-data="{ expanded: false }">
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div>
                            <div class="text-sm font-medium text-sap-gray-900">
                                {% if register_type == 'purchase' %}
                                    {{ record.bill.bill_no }}
                                {% else %}
                                    {{ record.invoice.invoice_no }}
                                {% endif %}
                            </div>
                            <div class="text-sm text-sap-gray-500">
                                {% if register_type == 'purchase' %}
                                    {{ record.bill.bill_date|date:"d/m/Y" }}
                                {% else %}
                                    {{ record.invoice.invoice_date|date:"d/m/Y" }}
                                {% endif %}
                            </div>
                            {% if register_type == 'purchase' and record.bill.supplier_bill_no %}
                            <div class="text-xs text-sap-green-600">
                                Supplier Bill: {{ record.bill.supplier_bill_no }}
                            </div>
                            {% elif register_type == 'sales' and record.invoice.invoice_type %}
                            <div class="text-xs text-sap-blue-600">
                                {{ record.invoice.invoice_type }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </td>
                
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-sap-gray-900">
                        {% if register_type == 'purchase' %}
                            {{ record.bill.supplier_name|truncatechars:25 }}
                        {% else %}
                            {{ record.invoice.customer_name|truncatechars:25 }}
                        {% endif %}
                    </div>
                    {% if register_type == 'purchase' and record.bill.supplier_id %}
                    <div class="text-sm text-sap-gray-500">
                        ID: {{ record.bill.supplier_id }}
                    </div>
                    {% elif register_type == 'sales' and record.invoice.customer_id %}
                    <div class="text-sm text-sap-gray-500">
                        ID: {{ record.invoice.customer_id }}
                    </div>
                    {% endif %}
                </td>
                
                <td class="px-6 py-4 whitespace-nowrap text-right">
                    <div class="text-sm font-medium text-sap-gray-900">
                        ₹{{ record.total_basic|floatformat:2 }}
                    </div>
                </td>
                
                <td class="px-6 py-4 whitespace-nowrap text-right">
                    <div class="text-sm font-medium {% if register_type == 'purchase' %}text-sap-green-600{% else %}text-sap-orange-600{% endif %}">
                        ₹{{ record.total_vat|floatformat:2 }}
                    </div>
                    {% if record.vat_breakdown and show_vat_breakdown %}
                    <div class="text-xs text-sap-gray-500 mt-1">
                        {% for rate, amounts in record.vat_breakdown.items %}
                        {{ rate }}%: ₹{{ amounts.vat_amount|floatformat:0 }}{% if not forloop.last %}, {% endif %}
                        {% endfor %}
                    </div>
                    {% endif %}
                </td>
                
                <td class="px-6 py-4 whitespace-nowrap text-right">
                    <div class="text-sm text-sap-gray-900">
                        {% if register_type == 'purchase' %}
                            {% with cst=record.bill.cst_amount|default:0 excise=record.bill.excise_amount|default:0 service_tax=record.bill.service_tax_amount|default:0 %}
                            ₹{{ cst|add:excise|add:service_tax|floatformat:2 }}
                            {% endwith %}
                        {% else %}
                            {% with cst=record.invoice.cst_amount|default:0 excise=record.invoice.excise_amount|default:0 service_tax=record.invoice.service_tax_amount|default:0 %}
                            ₹{{ cst|add:excise|add:service_tax|floatformat:2 }}
                            {% endwith %}
                        {% endif %}
                    </div>
                    <div class="text-xs text-sap-gray-500">
                        {% if register_type == 'purchase' %}
                            {% if record.bill.cst_amount %}CST: ₹{{ record.bill.cst_amount|floatformat:0 }}{% endif %}
                            {% if record.bill.excise_amount %}{% if record.bill.cst_amount %}, {% endif %}Excise: ₹{{ record.bill.excise_amount|floatformat:0 }}{% endif %}
                        {% else %}
                            {% if record.invoice.cst_amount %}CST: ₹{{ record.invoice.cst_amount|floatformat:0 }}{% endif %}
                            {% if record.invoice.excise_amount %}{% if record.invoice.cst_amount %}, {% endif %}Excise: ₹{{ record.invoice.excise_amount|floatformat:0 }}{% endif %}
                        {% endif %}
                    </div>
                </td>
                
                <td class="px-6 py-4 whitespace-nowrap text-right">
                    <div class="text-sm font-bold text-sap-gray-900">
                        ₹{{ record.total_amount|floatformat:2 }}
                    </div>
                </td>
                
                <td class="px-6 py-4 whitespace-nowrap text-center">
                    <div class="flex items-center justify-center space-x-2">
                        <button @click="expanded = !expanded" 
                                class="{% if register_type == 'purchase' %}text-sap-green-600 hover:text-sap-green-700{% else %}text-sap-blue-600 hover:text-sap-blue-700{% endif %} text-sm">
                            <i data-lucide="eye" class="w-4 h-4"></i>
                        </button>
                        {% if register_type == 'purchase' %}
                        <button onclick="viewBill('{{ record.bill.id }}')" 
                                class="text-sap-blue-600 hover:text-sap-blue-700 text-sm">
                            <i data-lucide="external-link" class="w-4 h-4"></i>
                        </button>
                        {% else %}
                        <button onclick="viewInvoice('{{ record.invoice.id }}')" 
                                class="text-sap-green-600 hover:text-sap-green-700 text-sm">
                            <i data-lucide="external-link" class="w-4 h-4"></i>
                        </button>
                        {% endif %}
                    </div>
                </td>
            </tr>
            
            <!-- Expanded Details Row -->
            <tr x-show="expanded" x-transition class="{% if register_type == 'purchase' %}bg-sap-green-50{% else %}bg-sap-blue-50{% endif %}">
                <td colspan="7" class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                            <h5 class="text-sm font-medium text-sap-gray-800 mb-2">
                                {% if register_type == 'purchase' %}Bill Information{% else %}Invoice Information{% endif %}
                            </h5>
                            <div class="text-sm text-sap-gray-600 space-y-1">
                                {% if register_type == 'purchase' %}
                                    {% if record.bill.po_no %}<p><strong>PO Number:</strong> {{ record.bill.po_no }}</p>{% endif %}
                                    {% if record.bill.grr_no %}<p><strong>GRR Number:</strong> {{ record.bill.grr_no }}</p>{% endif %}
                                    <p><strong>Status:</strong> 
                                        <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full 
                                            {% if record.bill.status == 'authorized' %}bg-sap-green-100 text-sap-green-800{% else %}bg-sap-orange-100 text-sap-orange-800{% endif %}">
                                            {{ record.bill.status|title }}
                                        </span>
                                    </p>
                                {% else %}
                                    {% if record.invoice.po_no %}<p><strong>PO Number:</strong> {{ record.invoice.po_no }}</p>{% endif %}
                                    {% if record.invoice.work_order_no %}<p><strong>Work Order:</strong> {{ record.invoice.work_order_no }}</p>{% endif %}
                                    <p><strong>Status:</strong> 
                                        <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full 
                                            {% if record.invoice.status == 'approved' %}bg-sap-green-100 text-sap-green-800{% else %}bg-sap-orange-100 text-sap-orange-800{% endif %}">
                                            {{ record.invoice.status|title }}
                                        </span>
                                    </p>
                                {% endif %}
                            </div>
                        </div>
                        
                        {% if record.vat_breakdown %}
                        <div>
                            <h5 class="text-sm font-medium text-sap-gray-800 mb-2">VAT Breakdown</h5>
                            <div class="text-sm text-sap-gray-600 space-y-1">
                                {% for rate, amounts in record.vat_breakdown.items %}
                                <div class="flex justify-between">
                                    <span>{{ rate }}% VAT:</span>
                                    <span class="font-medium">₹{{ amounts.vat_amount|floatformat:2 }}</span>
                                </div>
                                <div class="text-xs text-sap-gray-500 ml-4">
                                    Basic: ₹{{ amounts.basic_amount|floatformat:2 }}
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                        
                        <div>
                            <h5 class="text-sm font-medium text-sap-gray-800 mb-2">Additional Charges</h5>
                            <div class="text-sm text-sap-gray-600 space-y-1">
                                {% if register_type == 'purchase' %}
                                    {% if record.bill.freight_amount %}<p>Freight: ₹{{ record.bill.freight_amount|floatformat:2 }}</p>{% endif %}
                                    {% if record.bill.discount_amount %}<p>Discount: ₹{{ record.bill.discount_amount|floatformat:2 }}</p>{% endif %}
                                    {% if record.bill.packing_forwarding_amount %}<p>P&F: ₹{{ record.bill.packing_forwarding_amount|floatformat:2 }}</p>{% endif %}
                                {% else %}
                                    {% if record.invoice.freight_amount %}<p>Freight: ₹{{ record.invoice.freight_amount|floatformat:2 }}</p>{% endif %}
                                    {% if record.invoice.discount_amount %}<p>Discount: ₹{{ record.invoice.discount_amount|floatformat:2 }}</p>{% endif %}
                                    {% if record.invoice.packing_forwarding_amount %}<p>P&F: ₹{{ record.invoice.packing_forwarding_amount|floatformat:2 }}</p>{% endif %}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="px-6 py-8 text-center">
                    <div class="text-sap-gray-500">
                        <i data-lucide="inbox" class="w-12 h-12 mx-auto mb-4 text-sap-gray-300"></i>
                        <p class="text-lg font-medium">No VAT records found</p>
                        <p class="text-sm">Try adjusting your search criteria or date range</p>
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// Re-initialize icons and Alpine.js components after HTMX update
lucide.createIcons();

// Functions for viewing records
function viewInvoice(invoiceId) {
    window.open(`/accounts/sales-invoice/${invoiceId}/view/`, '_blank');
}

function viewBill(billId) {
    window.open(`/accounts/bill-booking/${billId}/view/`, '_blank');
}
</script>