<!-- sales_distribution/templates/sales_distribution/dashboard.html -->
<!-- Sales Distribution Dashboard - SAP S/4HANA Inspired Design -->

{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }} - Cortex{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-sap-blue-500 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="truck" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Sales Distribution</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage customers, categories, quotations, and work orders</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-2 py-4 space-y-6">
    
    <!-- Statistics Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="layers" class="w-5 h-5 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-semibold text-sap-gray-900">{{ category_count }}</p>
                    <p class="text-sm font-medium text-sap-gray-600">Categories</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="git-branch" class="w-5 h-5 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-semibold text-sap-gray-900">{{ subcategory_count }}</p>
                    <p class="text-sm font-medium text-sap-gray-600">Sub-Categories</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="users" class="w-5 h-5 text-sap-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-semibold text-sap-gray-900">{{ customer_count }}</p>
                    <p class="text-sm font-medium text-sap-gray-600">Customers</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="help-circle" class="w-5 h-5 text-sap-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-semibold text-sap-gray-900">{{ enquiry_count }}</p>
                    <p class="text-sm font-medium text-sap-gray-600">Enquiries</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-teal-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="file-text" class="w-5 h-5 text-sap-teal-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-semibold text-sap-gray-900">{{ quotation_count }}</p>
                    <p class="text-sm font-medium text-sap-gray-600">Quotations</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-red-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="briefcase" class="w-5 h-5 text-sap-red-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-semibold text-sap-gray-900">{{ workorder_count }}</p>
                    <p class="text-sm font-medium text-sap-gray-600">Work Orders</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <h3 class="text-lg font-medium text-sap-gray-800">Quick Actions</h3>
            <p class="text-sm text-sap-gray-600 mt-1">Common sales distribution tasks</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <a href="{% url 'sales_distribution:category_new' %}" 
                   class="flex items-center p-4 border border-sap-gray-200 rounded-lg hover:bg-sap-green-50 hover:border-sap-green-300 transition-all duration-200">
                    <div class="w-8 h-8 bg-sap-green-100 rounded-lg flex items-center justify-center mr-3">
                        <i data-lucide="plus-circle" class="w-4 h-4 text-sap-green-600"></i>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-sap-gray-900">Add New Category</h4>
                        <p class="text-xs text-sap-gray-600">Create work order category</p>
                    </div>
                </a>

                <a href="{% url 'sales_distribution:category_list' %}" 
                   class="flex items-center p-4 border border-sap-gray-200 rounded-lg hover:bg-sap-blue-50 hover:border-sap-blue-300 transition-all duration-200">
                    <div class="w-8 h-8 bg-sap-blue-100 rounded-lg flex items-center justify-center mr-3">
                        <i data-lucide="layers" class="w-4 h-4 text-sap-blue-600"></i>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-sap-gray-900">Manage Categories</h4>
                        <p class="text-xs text-sap-gray-600">Edit work order categories</p>
                    </div>
                </a>

                <a href="{% url 'sales_distribution:subcategory_list' %}" 
                   class="flex items-center p-4 border border-sap-gray-200 rounded-lg hover:bg-sap-green-50 hover:border-sap-green-300 transition-all duration-200">
                    <div class="w-8 h-8 bg-sap-green-100 rounded-lg flex items-center justify-center mr-3">
                        <i data-lucide="git-branch" class="w-4 h-4 text-sap-green-600"></i>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-sap-gray-900">Sub-Categories</h4>
                        <p class="text-xs text-sap-gray-600">Category subdivisions</p>
                    </div>
                </a>

                <a href="{% url 'sales_distribution:customer_list' %}" 
                   class="flex items-center p-4 border border-sap-gray-200 rounded-lg hover:bg-sap-purple-50 hover:border-sap-purple-300 transition-all duration-200">
                    <div class="w-8 h-8 bg-sap-purple-100 rounded-lg flex items-center justify-center mr-3">
                        <i data-lucide="users" class="w-4 h-4 text-sap-purple-600"></i>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-sap-gray-900">Customer Management</h4>
                        <p class="text-xs text-sap-gray-600">Customer database</p>
                    </div>
                </a>

                <div class="flex items-center p-4 border border-sap-gray-200 rounded-lg bg-sap-gray-50 opacity-50">
                    <div class="w-8 h-8 bg-sap-gray-100 rounded-lg flex items-center justify-center mr-3">
                        <i data-lucide="file-text" class="w-4 h-4 text-sap-gray-400"></i>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-sap-gray-500">Quotations</h4>
                        <p class="text-xs text-sap-gray-400">Coming soon</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <h3 class="text-lg font-medium text-sap-gray-800">Module Information</h3>
            <p class="text-sm text-sap-gray-600 mt-1">Sales Distribution module features</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-sm font-medium text-sap-gray-900 mb-3">Master Data</h4>
                    <ul class="space-y-2 text-sm text-sap-gray-600">
                        <li class="flex items-center">
                            <i data-lucide="check" class="w-4 h-4 text-sap-green-500 mr-2"></i>
                            Work Order Categories
                        </li>
                        <li class="flex items-center">
                            <i data-lucide="check" class="w-4 h-4 text-sap-green-500 mr-2"></i>
                            Work Order Sub-Categories
                        </li>
                        <li class="flex items-center">
                            <i data-lucide="check" class="w-4 h-4 text-sap-green-500 mr-2"></i>
                            Customer Master Data
                        </li>
                        <li class="flex items-center">
                            <i data-lucide="clock" class="w-4 h-4 text-sap-orange-500 mr-2"></i>
                            Unit Management (Planned)
                        </li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-sap-gray-900 mb-3">Transactions</h4>
                    <ul class="space-y-2 text-sm text-sap-gray-600">
                        <li class="flex items-center">
                            <i data-lucide="clock" class="w-4 h-4 text-sap-orange-500 mr-2"></i>
                            Customer Enquiries (Planned)
                        </li>
                        <li class="flex items-center">
                            <i data-lucide="clock" class="w-4 h-4 text-sap-orange-500 mr-2"></i>
                            Quotation Management (Planned)
                        </li>
                        <li class="flex items-center">
                            <i data-lucide="clock" class="w-4 h-4 text-sap-orange-500 mr-2"></i>
                            Purchase Orders (Planned)
                        </li>
                        <li class="flex items-center">
                            <i data-lucide="clock" class="w-4 h-4 text-sap-orange-500 mr-2"></i>
                            Work Orders (Planned)
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

</div>
{% endblock %}