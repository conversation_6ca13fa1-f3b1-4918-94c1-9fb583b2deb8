{% extends "core/base.html" %}
{% load static %}

{% block title %}Customer PO - Create{% endblock %}

{% block content %}
<div class="min-h-screen bg-sap-gray-50 py-6" x-data="customerPOForm()">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Header Section -->
        <div class="sap-card mb-6">
            <div class="px-6 py-4 bg-sap-blue-600 text-white rounded-t-xl flex justify-between items-center">
                <h1 class="text-xl font-bold">Create Customer Purchase Order</h1>
                <a href="{% url 'sales_distribution:customer_po_list' %}" 
                   class="inline-flex items-center px-4 py-2 bg-white text-sap-blue-600 font-medium rounded-lg hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-sap-blue-600 transition-all duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to List
                </a>
            </div>
        </div>

        <!-- Enquiry Selection Section -->
        {% if not enquiry %}
        <div class="sap-card mb-6">
            <div class="px-6 py-4 border-b border-sap-gray-200">
                <h2 class="text-lg font-semibold text-sap-gray-900">Step 1: Select Enquiry</h2>
                <p class="text-sm text-sap-gray-600 mt-1">Choose an enquiry to create a purchase order against</p>
            </div>
            
            <div class="px-6 py-4">
                <form method="get" class="flex flex-wrap items-center gap-4" x-data="enquirySearch()">
                    <!-- Search Type Dropdown -->
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-sap-gray-700">Search by:</label>
                        <select name="search_type" x-model="searchType" @change="toggleSearchFields()"
                                class="sap-input w-auto">
                            <option value="">Select Type</option>
                            <option value="customer_name">Customer Name</option>
                            <option value="enquiry_no">Enquiry No</option>
                        </select>
                        
                        <!-- Customer Name Search -->
                        <input type="text" name="customer_search" x-show="searchType === 'customer_name'" x-transition
                               class="sap-input w-80" placeholder="Search customer name..."
                               hx-get="{% url 'sales_distribution:customer_po_create' %}"
                               hx-target="#enquiry-list"
                               hx-trigger="input changed delay:500ms"
                               hx-include="[name='search_type']">
                        
                        <!-- Enquiry Number Search -->
                        <input type="text" name="enquiry_search" x-show="searchType === 'enquiry_no'" x-transition
                               class="sap-input w-32" placeholder="Enquiry No">
                    </div>
                    
                    <button type="submit" class="sap-button-primary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Search
                    </button>
                </form>
            </div>
            
            <!-- Enquiry List -->
            <div id="enquiry-list" class="px-6 pb-6">
                {% if enquiries %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-sap-gray-200">
                        <thead class="bg-sap-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">SN</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">Fin Year</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">Customer Name</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">Code</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">Enquiry No</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">Gen Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">Gen By</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">Action</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-sap-gray-200">
                            {% for enq_data in enquiries %}
                            <tr class="hover:bg-sap-gray-50 transition-colors duration-200">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">{{ forloop.counter }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">{{ enq_data.fin_year|default:"-" }}</td>
                                <td class="px-6 py-4 text-sm text-sap-gray-900">{{ enq_data.customer_name|default:"-" }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">{{ enq_data.enquiry.customerid|default:"-" }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">{{ enq_data.enquiry.enqid|default:"-" }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">{{ enq_data.formatted_date|default:"-" }}</td>
                                <td class="px-6 py-4 text-sm text-sap-gray-900">{{ enq_data.employee_name|default:"-" }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <a href="?enq_id={{ enq_data.enquiry.enqid }}" 
                                       class="text-sap-blue-600 hover:text-sap-blue-800 font-medium transition-colors duration-200">
                                        Select
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-8">
                    <div class="text-sap-gray-400 mb-2">
                        <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <p class="text-sap-gray-500">Search for enquiries to create purchase orders</p>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- PO Creation Form -->
        {% if enquiry %}
        <form method="post" enctype="multipart/form-data" class="space-y-6">
            {% csrf_token %}
            
            <!-- Tab Navigation -->
            <div class="sap-card">
                <div class="border-b border-sap-gray-200">
                    <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                        <button type="button" @click="activeTab = 'customer'" 
                                :class="activeTab === 'customer' ? 'border-sap-blue-500 text-sap-blue-600' : 'border-transparent text-sap-gray-500 hover:text-sap-gray-700 hover:border-sap-gray-300'"
                                class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                            Customer Details
                        </button>
                        <button type="button" @click="activeTab = 'goods'" 
                                :class="activeTab === 'goods' ? 'border-sap-blue-500 text-sap-blue-600' : 'border-transparent text-sap-gray-500 hover:text-sap-gray-700 hover:border-sap-gray-300'"
                                class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                            Goods Details
                        </button>
                        <button type="button" @click="activeTab = 'terms'" 
                                :class="activeTab === 'terms' ? 'border-sap-blue-500 text-sap-blue-600' : 'border-transparent text-sap-gray-500 hover:text-sap-gray-700 hover:border-sap-gray-300'"
                                class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                            Terms & Conditions
                        </button>
                    </nav>
                </div>

                <!-- Tab 1: Customer Details -->
                <div x-show="activeTab === 'customer'" x-transition class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        
                        <!-- Left Column: Customer Information (Read-only) -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-medium text-sap-gray-900 border-b border-sap-gray-200 pb-2">
                                Customer Information
                            </h3>
                            
                            <div>
                                <label class="block text-sm font-medium text-sap-gray-700">Customer Name</label>
                                <div class="mt-1 p-3 bg-sap-gray-50 rounded-lg text-sm text-sap-gray-900">
                                    {{ customer_name|default:"Loading..." }}
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-sap-gray-700">Registered Office Address</label>
                                <div class="mt-1 p-3 bg-sap-gray-50 rounded-lg text-sm text-sap-gray-900">
                                    {{ customer_address|default:"Loading..." }}
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-sap-gray-700">Enquiry Number</label>
                                <div class="mt-1 p-3 bg-sap-gray-50 rounded-lg text-sm text-sap-gray-900">
                                    {{ enquiry.enqid }}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Right Column: PO Details -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-medium text-sap-gray-900 border-b border-sap-gray-200 pb-2">
                                Purchase Order Details
                            </h3>
                            
                            <div>
                                <label for="{{ form.quotationno.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                    Quotation No <span class="text-red-500">*</span>
                                </label>
                                {{ form.quotationno }}
                                {% if form.quotationno.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.quotationno.errors.0 }}</p>
                                {% endif %}
                            </div>
                            
                            <div>
                                <label for="{{ form.pono.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                    PO No <span class="text-red-500">*</span>
                                </label>
                                {{ form.pono }}
                                {% if form.pono.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.pono.errors.0 }}</p>
                                {% endif %}
                            </div>
                            
                            <div>
                                <label for="{{ form.podate.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                    PO Date <span class="text-red-500">*</span>
                                </label>
                                {{ form.podate }}
                                {% if form.podate.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.podate.errors.0 }}</p>
                                {% endif %}
                            </div>
                            
                            <div>
                                <label for="{{ form.poreceiveddate.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                    PO Received Date <span class="text-red-500">*</span>
                                </label>
                                {{ form.poreceiveddate }}
                                {% if form.poreceiveddate.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.poreceiveddate.errors.0 }}</p>
                                {% endif %}
                            </div>
                            
                            <div>
                                <label for="{{ form.vendorcode.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                    Our Vendor Code <span class="text-red-500">*</span>
                                </label>
                                {{ form.vendorcode }}
                                {% if form.vendorcode.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.vendorcode.errors.0 }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6 flex justify-end">
                        <button type="button" @click="activeTab = 'goods'" 
                                class="sap-button-primary">
                            Next: Goods Details
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Tab 2: Goods Details -->
                <div x-show="activeTab === 'goods'" x-transition class="p-6">
                    <h3 class="text-lg font-medium text-sap-gray-900 border-b border-sap-gray-200 pb-2 mb-6">
                        Goods Details
                    </h3>
                    
                    <div id="goods-formset">
                        {{ formset.management_form }}
                        
                        {% for form in formset %}
                        <div class="formset-form bg-sap-gray-50 rounded-lg p-4 mb-4" data-form-index="{{ forloop.counter0 }}">
                            <div class="flex justify-between items-center mb-4">
                                <h4 class="text-md font-medium text-sap-gray-900">Item {{ forloop.counter }}</h4>
                                {% if not forloop.first %}
                                <button type="button" class="text-red-600 hover:text-red-800 transition-colors duration-200" 
                                        onclick="removeForm(this)">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                                {% endif %}
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                                <div class="lg:col-span-2">
                                    <label class="block text-sm font-medium text-sap-gray-700">
                                        Description & Specification <span class="text-red-500">*</span>
                                    </label>
                                    {{ form.itemdesc }}
                                    {% if form.itemdesc.errors %}
                                        <p class="mt-1 text-sm text-red-600">{{ form.itemdesc.errors.0 }}</p>
                                    {% endif %}
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-sap-gray-700">
                                        Total Qty <span class="text-red-500">*</span>
                                    </label>
                                    {{ form.totalqty }}
                                    {% if form.totalqty.errors %}
                                        <p class="mt-1 text-sm text-red-600">{{ form.totalqty.errors.0 }}</p>
                                    {% endif %}
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-sap-gray-700">
                                        Unit <span class="text-red-500">*</span>
                                    </label>
                                    {{ form.unit }}
                                    {% if form.unit.errors %}
                                        <p class="mt-1 text-sm text-red-600">{{ form.unit.errors.0 }}</p>
                                    {% endif %}
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-sap-gray-700">
                                        Rate per unit <span class="text-red-500">*</span>
                                    </label>
                                    {{ form.rate }}
                                    {% if form.rate.errors %}
                                        <p class="mt-1 text-sm text-red-600">{{ form.rate.errors.0 }}</p>
                                    {% endif %}
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-sap-gray-700">
                                        Discount % <span class="text-red-500">*</span>
                                    </label>
                                    {{ form.discount }}
                                    {% if form.discount.errors %}
                                        <p class="mt-1 text-sm text-red-600">{{ form.discount.errors.0 }}</p>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- Hidden DELETE field -->
                            {% if form.DELETE %}
                                {{ form.DELETE }}
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                    
                    <div class="flex justify-between items-center mt-6">
                        <button type="button" id="add-form" 
                                class="inline-flex items-center px-4 py-2 border border-sap-blue-600 text-sap-blue-600 rounded-lg hover:bg-sap-blue-50 transition-colors duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add Item
                        </button>
                        
                        <div class="flex space-x-4">
                            <button type="button" @click="activeTab = 'customer'" 
                                    class="inline-flex items-center px-4 py-2 border border-sap-gray-300 text-sap-gray-700 rounded-lg hover:bg-sap-gray-50 transition-colors duration-200">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                                Previous
                            </button>
                            <button type="button" @click="activeTab = 'terms'" 
                                    class="sap-button-primary">
                                Next: Terms & Conditions
                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Tab 3: Terms & Conditions -->
                <div x-show="activeTab === 'terms'" x-transition class="p-6">
                    <h3 class="text-lg font-medium text-sap-gray-900 border-b border-sap-gray-200 pb-2 mb-6">
                        Terms & Conditions
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Left Column -->
                        <div class="space-y-4">
                            <div>
                                <label for="{{ form.paymentterms.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                    Payment Terms <span class="text-red-500">*</span>
                                </label>
                                {{ form.paymentterms }}
                            </div>
                            
                            <div>
                                <label for="{{ form.pf.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                    Packing & Forwarding (P&F) <span class="text-red-500">*</span>
                                </label>
                                {{ form.pf }}
                            </div>
                            
                            <!-- Legacy Tax Fields - Hidden with default values -->
                            {{ form.excise }}
                            {{ form.vat }}
                            {{ form.octroi }}
                            
                            <div>
                                <label for="{{ form.warrenty.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                    Warranty <span class="text-red-500">*</span>
                                </label>
                                {{ form.warrenty }}
                            </div>
                            
                            <div>
                                <label for="{{ form.insurance.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                    Insurance <span class="text-red-500">*</span>
                                </label>
                                {{ form.insurance }}
                            </div>
                        </div>
                        
                        <!-- Right Column -->
                        <div class="space-y-4">
                            <div>
                                <label for="{{ form.transport.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                    Mode of Transport <span class="text-red-500">*</span>
                                </label>
                                {{ form.transport }}
                            </div>
                            
                            <div>
                                <label for="{{ form.noteno.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                    R.R./G.C. Note No. <span class="text-red-500">*</span>
                                </label>
                                {{ form.noteno }}
                            </div>
                            
                            <div>
                                <label for="{{ form.registrationno.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                    Vehicle Registration No.
                                </label>
                                {{ form.registrationno }}
                            </div>
                            
                            <div>
                                <label for="{{ form.freight.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                    Freight <span class="text-red-500">*</span>
                                </label>
                                {{ form.freight }}
                            </div>
                            
                            <div>
                                <label for="{{ form.cst.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                    GST <span class="text-red-500">*</span>
                                </label>
                                {{ form.cst }}
                            </div>
                            
                            <div>
                                <label for="{{ form.validity.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                    Validity Period <span class="text-red-500">*</span>
                                </label>
                                {{ form.validity }}
                            </div>
                            
                            <div>
                                <label for="{{ form.othercharges.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                    Other Charges <span class="text-red-500">*</span>
                                </label>
                                {{ form.othercharges }}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Full width fields -->
                    <div class="mt-6 space-y-4">
                        <div>
                            <label for="{{ form.attachment.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                Attachment
                            </label>
                            {{ form.attachment }}
                            <p class="mt-1 text-sm text-sap-gray-500">Upload PO documents (PDF, DOC, XLS, images)</p>
                        </div>
                        
                        <div>
                            <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-sap-gray-700">
                                Remarks
                            </label>
                            {{ form.remarks }}
                        </div>
                    </div>
                    
                    <div class="mt-6 flex justify-between">
                        <button type="button" @click="activeTab = 'goods'" 
                                class="inline-flex items-center px-4 py-2 border border-sap-gray-300 text-sap-gray-700 rounded-lg hover:bg-sap-gray-50 transition-colors duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                            Previous
                        </button>
                        
                        <button type="submit" 
                                class="inline-flex items-center px-6 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all duration-200">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Create Purchase Order
                        </button>
                    </div>
                </div>
            </div>
        </form>
        {% endif %}
    </div>
</div>

<script>
// Alpine.js data for Customer PO form
function customerPOForm() {
    return {
        activeTab: 'customer'
    }
}

// Alpine.js data for enquiry search
function enquirySearch() {
    return {
        searchType: '',
        
        toggleSearchFields() {
            // Alpine handles visibility automatically with x-show
        }
    }
}

// Formset management for goods details
document.addEventListener('DOMContentLoaded', function() {
    const addButton = document.getElementById('add-form');
    const formset = document.getElementById('goods-formset');
    
    if (addButton && formset) {
        addButton.addEventListener('click', function() {
            const totalForms = document.querySelector('#id_form-TOTAL_FORMS');
            const formNum = parseInt(totalForms.value);
            
            // Clone the first form
            const firstForm = document.querySelector('.formset-form');
            const newForm = firstForm.cloneNode(true);
            
            // Update form index and clear values
            newForm.setAttribute('data-form-index', formNum);
            newForm.querySelector('h4').textContent = `Item ${formNum + 1}`;
            
            // Update field names and IDs
            const inputs = newForm.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                const name = input.getAttribute('name');
                const id = input.getAttribute('id');
                
                if (name) {
                    input.setAttribute('name', name.replace('-0-', `-${formNum}-`));
                }
                if (id) {
                    input.setAttribute('id', id.replace('-0-', `-${formNum}-`));
                }
                
                // Clear values
                if (input.type !== 'hidden') {
                    input.value = '';
                }
            });
            
            // Add remove button if it's not the first form
            if (formNum > 0) {
                const removeBtn = newForm.querySelector('button[onclick="removeForm(this)"]');
                if (!removeBtn) {
                    const header = newForm.querySelector('.flex.justify-between');
                    header.innerHTML += `
                        <button type="button" class="text-red-600 hover:text-red-800 transition-colors duration-200" 
                                onclick="removeForm(this)">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    `;
                }
            }
            
            // Insert before add button
            formset.insertBefore(newForm, addButton.parentElement);
            
            // Update total forms
            totalForms.value = formNum + 1;
        });
    }
});

function removeForm(button) {
    const form = button.closest('.formset-form');
    const deleteField = form.querySelector('input[name$="-DELETE"]');
    
    if (deleteField) {
        deleteField.checked = true;
        form.style.display = 'none';
    } else {
        form.remove();
        
        // Update total forms count
        const totalForms = document.querySelector('#id_form-TOTAL_FORMS');
        totalForms.value = parseInt(totalForms.value) - 1;
    }
}
</script>
{% endblock %}