{% if spare_parts %}
    {% for spare in spare_parts %}
    <div class="flex items-center justify-between py-2 border-b border-gray-200 last:border-b-0">
        <div>
            <p class="text-sm font-medium text-gray-900">{{ spare.item.itemcode }}</p>
            <p class="text-xs text-gray-500">{{ spare.item.description|truncatechars:40 }}</p>
        </div>
        <div class="flex items-center space-x-2">
            <span class="text-sm text-gray-600">Qty: {{ spare.qty }}</span>
            <button type="button" 
                    hx-delete="{% url 'machinery:remove_spare_part' spare.id %}"
                    hx-target="#spare-parts-list"
                    class="text-red-600 hover:text-red-800">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    </div>
    {% endfor %}
{% else %}
    <div class="text-center py-8">
        <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        <p class="text-gray-500">No spare parts selected</p>
        <p class="text-sm text-gray-400">Select items from the left panel</p>
    </div>
{% endif %}