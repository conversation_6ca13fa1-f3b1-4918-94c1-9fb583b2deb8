<!-- accounts/templates/accounts/composite_tax_calculator.html -->
<!-- Composite Tax Calculator Template -->
<!-- Task Group 4: Taxation Management - Comprehensive Tax Calculator -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Composite Tax Calculator{% endblock %}

{% block extra_css %}
<style>
    .tax-section {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .result-summary {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        border-radius: 1rem;
        padding: 2rem;
        margin: 1.5rem 0;
    }
    
    .tax-component {
        background: white;
        border-radius: 0.75rem;
        padding: 1rem;
        margin: 0.5rem 0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .tax-amount {
        font-size: 1.25rem;
        font-weight: bold;
        color: #059669;
    }
    
    .rate-indicator {
        background: rgba(59, 130, 246, 0.1);
        color: #1d4ed8;
        padding: 0.25rem 0.75rem;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        font-weight: 600;
    }
    
    .input-section {
        background: #f8fafc;
        border-radius: 1rem;
        padding: 1.5rem;
        border: 2px solid #e2e8f0;
    }
    
    .calculation-flow {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 1rem 0;
    }
    
    .flow-arrow {
        color: #6b7280;
        font-size: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-6 py-8">
    <!-- Header Section -->
    <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Composite Tax Calculator</h1>
                <p class="mt-2 text-gray-600">Calculate multiple taxes including Excise, VAT, and TDS in one go</p>
            </div>
            <div class="mt-4 sm:mt-0 flex space-x-3">
                <a href="{% url 'accounts:excise_calculator' %}" 
                   class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    Excise Calculator
                </a>
                <a href="{% url 'accounts:reverse_tax_calculator' %}" 
                   class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    Reverse Calculator
                </a>
                <a href="{% url 'accounts:tax_comparison' %}" 
                   class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    Compare Scenarios
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
        <!-- Input Section -->
        <div class="xl:col-span-1">
            <form method="post" id="composite-tax-form" class="space-y-6">
                {% csrf_token %}
                
                <!-- Base Amount Input -->
                <div class="input-section">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">
                        <i class="fas fa-rupee-sign mr-2"></i>Base Amount
                    </h3>
                    
                    <div class="mb-4">
                        <label for="base_amount" class="block text-sm font-medium text-gray-700 mb-2">
                            Amount (₹)
                        </label>
                        <input type="number" name="base_amount" id="base_amount" 
                               value="{{ form_data.base_amount|default:'100000' }}" 
                               step="0.01" min="0" required
                               class="block w-full px-4 py-3 text-lg border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-3">Calculation Method</label>
                        <div class="space-y-2">
                            <div class="flex items-center">
                                <input type="radio" name="calculation_method" id="exclusive" value="exclusive" 
                                       {% if not form_data.calculation_method or form_data.calculation_method == 'exclusive' %}checked{% endif %}
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                <label for="exclusive" class="ml-2 text-sm text-gray-700">
                                    Exclusive (Tax added to amount)
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input type="radio" name="calculation_method" id="inclusive" value="inclusive"
                                       {% if form_data.calculation_method == 'inclusive' %}checked{% endif %}
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                <label for="inclusive" class="ml-2 text-sm text-gray-700">
                                    Inclusive (Tax included in amount)
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Amount Buttons -->
                    <div class="grid grid-cols-2 gap-2">
                        <button type="button" onclick="setQuickAmount(10000)" 
                                class="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50">
                            ₹10K
                        </button>
                        <button type="button" onclick="setQuickAmount(100000)" 
                                class="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50">
                            ₹1L
                        </button>
                        <button type="button" onclick="setQuickAmount(1000000)" 
                                class="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50">
                            ₹10L
                        </button>
                        <button type="button" onclick="setQuickAmount(10000000)" 
                                class="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50">
                            ₹1Cr
                        </button>
                    </div>
                </div>

                <!-- Excise Duty Section -->
                <div class="input-section">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">
                        <i class="fas fa-industry mr-2"></i>Excise Duty
                    </h3>
                    
                    <div class="mb-4">
                        <label for="excise_duty_id" class="block text-sm font-medium text-gray-700 mb-2">
                            Select Excise/Service Tax
                        </label>
                        <select name="excise_duty_id" id="excise_duty_id"
                                class="block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">None</option>
                            {% for duty in excise_duties %}
                                <option value="{{ duty.id }}" 
                                        {% if form_data.excise_duty_id == duty.id|stringformat:"s" %}selected{% endif %}
                                        data-rate="{{ duty.value }}" 
                                        data-edu="{{ duty.edu_cess }}" 
                                        data-she="{{ duty.she_cess }}">
                                    {{ duty.terms }} ({{ duty.total_excise_rate|floatformat:3 }}%)
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <!-- Default buttons -->
                    <div class="flex space-x-2">
                        {% if default_rates.default_excise %}
                        <button type="button" onclick="selectDefaultExcise({{ default_rates.default_excise.id }})"
                                class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                            Default Excise
                        </button>
                        {% endif %}
                        {% if default_rates.default_service_tax %}
                        <button type="button" onclick="selectDefaultServiceTax({{ default_rates.default_service_tax.id }})"
                                class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                            Default Service Tax
                        </button>
                        {% endif %}
                    </div>
                </div>

                <!-- VAT Section -->
                <div class="input-section">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">
                        <i class="fas fa-percentage mr-2"></i>VAT/Sales Tax
                    </h3>
                    
                    <div class="mb-4">
                        <label for="vat_rate" class="block text-sm font-medium text-gray-700 mb-2">
                            VAT Rate (%)
                        </label>
                        <input type="number" name="vat_rate" id="vat_rate" 
                               value="{{ form_data.vat_rate }}" 
                               step="0.001" min="0" max="100" placeholder="0.000"
                               class="block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <!-- Common VAT rates -->
                    <div class="flex flex-wrap gap-1">
                        {% for vat in vat_rates %}
                        <button type="button" onclick="setVATRate({{ vat.vat_percentage }})"
                                class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded hover:bg-gray-200">
                            {{ vat.vat_percentage }}%
                        </button>
                        {% endfor %}
                    </div>
                </div>

                <!-- TDS Section -->
                <div class="input-section">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">
                        <i class="fas fa-cut mr-2"></i>TDS (Tax Deducted at Source)
                    </h3>
                    
                    <div class="mb-4">
                        <label for="tds_rate" class="block text-sm font-medium text-gray-700 mb-2">
                            TDS Rate (%)
                        </label>
                        <input type="number" name="tds_rate" id="tds_rate" 
                               value="{{ form_data.tds_rate }}" 
                               step="0.001" min="0" max="100" placeholder="0.000"
                               class="block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div class="mb-4">
                        <label for="tds_threshold" class="block text-sm font-medium text-gray-700 mb-2">
                            TDS Threshold (₹)
                        </label>
                        <input type="number" name="tds_threshold" id="tds_threshold" 
                               value="{{ form_data.tds_threshold }}" 
                               step="0.01" min="0" placeholder="Optional"
                               class="block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>

                <!-- Calculate Button -->
                <button type="submit" 
                        class="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-4 px-6 rounded-lg font-medium text-lg transition-all transform hover:scale-105">
                    <i class="fas fa-calculator mr-2"></i>Calculate All Taxes
                </button>
            </form>
        </div>

        <!-- Results Section -->
        <div class="xl:col-span-2">
            {% if calculation_result %}
            <!-- Summary Result -->
            <div class="result-summary">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">
                    <i class="fas fa-chart-pie mr-2"></i>Tax Calculation Summary
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600">
                            ₹{{ calculation_result.base_amount|floatformat:2 }}
                        </div>
                        <div class="text-sm text-gray-600 mt-1">Base Amount</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-red-600">
                            ₹{{ calculation_result.total_tax_amount|floatformat:2 }}
                        </div>
                        <div class="text-sm text-gray-600 mt-1">Total Tax</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600">
                            ₹{{ calculation_result.total_amount|floatformat:2 }}
                        </div>
                        <div class="text-sm text-gray-600 mt-1">Final Amount</div>
                    </div>
                </div>
                
                <div class="text-center">
                    <span class="rate-indicator">
                        Effective Tax Rate: {{ calculation_result.effective_tax_rate|floatformat:3 }}%
                    </span>
                </div>
            </div>

            <!-- Tax Components Breakdown -->
            {% if calculation_summary.tax_components %}
            <div class="space-y-4">
                {% for component in calculation_summary.tax_components %}
                <div class="tax-component">
                    <div class="flex justify-between items-center mb-3">
                        <h3 class="text-lg font-bold text-gray-900 capitalize">
                            <i class="fas fa-tag mr-2"></i>{{ component.type|title }} Tax
                        </h3>
                        <span class="text-xl font-bold text-green-600">
                            ₹{{ component.total_amount|floatformat:2 }}
                        </span>
                    </div>
                    
                    <div class="space-y-2">
                        {% for subcomponent in component.components %}
                        <div class="flex justify-between items-center text-sm">
                            <span class="text-gray-700">{{ subcomponent.name }}</span>
                            <div class="flex items-center space-x-3">
                                <span class="text-gray-500">{{ subcomponent.rate|floatformat:3 }}%</span>
                                <span class="font-medium">₹{{ subcomponent.amount|floatformat:2 }}</span>
                            </div>
                        </div>
                        {% if subcomponent.is_compound %}
                        <div class="text-xs text-blue-600 ml-4">
                            <i class="fas fa-link mr-1"></i>Calculated on {{ subcomponent.calculation_base }}
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <!-- Calculation Flow -->
            {% if calculation_result.calculation_details.tax_sequence %}
            <div class="mt-8 bg-white shadow-lg rounded-lg p-6">
                <h3 class="text-lg font-bold text-gray-900 mb-4">
                    <i class="fas fa-route mr-2"></i>Calculation Flow
                </h3>
                
                <div class="space-y-4">
                    {% for step in calculation_result.calculation_details.tax_sequence %}
                    <div class="calculation-flow">
                        <div class="flex-1 bg-gray-50 rounded-lg p-3">
                            <div class="font-medium text-gray-900 capitalize">{{ step.tax_type|title }}</div>
                            <div class="text-sm text-gray-600">
                                Base: ₹{{ step.base_amount|floatformat:2 }} 
                                → Tax: ₹{{ step.tax_amount|floatformat:2 }}
                            </div>
                        </div>
                        {% if not forloop.last %}
                        <div class="flow-arrow px-3">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Error Messages -->
            {% elif calculation_errors %}
            <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                <h3 class="text-lg font-bold text-red-800 mb-4">
                    <i class="fas fa-exclamation-triangle mr-2"></i>Calculation Errors
                </h3>
                <ul class="space-y-2">
                    {% for error in calculation_errors %}
                    <li class="text-red-700">{{ error }}</li>
                    {% endfor %}
                </ul>
            </div>

            {% else %}
            <!-- Welcome Message -->
            <div class="text-center py-12 bg-white shadow-lg rounded-lg">
                <i class="fas fa-calculator text-gray-300 text-6xl mb-4"></i>
                <h3 class="text-xl font-bold text-gray-900 mb-2">Composite Tax Calculator</h3>
                <p class="text-gray-600 mb-6">
                    Enter your base amount and select applicable taxes to calculate comprehensive tax liability.
                </p>
                <div class="flex justify-center space-x-4">
                    <button onclick="loadExampleCalculation()" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium">
                        Load Example
                    </button>
                    <a href="{% url 'accounts:tax_rate_analysis' %}" 
                       class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium">
                        View Tax Rates
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Saved Calculations -->
    <div class="mt-8 bg-white shadow-lg rounded-lg p-6">
        <h3 class="text-lg font-bold text-gray-900 mb-4">
            <i class="fas fa-bookmark mr-2"></i>Saved Calculations
        </h3>
        <div id="saved-calculations" class="space-y-3">
            <!-- Saved calculations will be populated here -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Quick amount setting
function setQuickAmount(amount) {
    document.getElementById('base_amount').value = amount;
    autoCalculate();
}

// VAT rate setting
function setVATRate(rate) {
    document.getElementById('vat_rate').value = rate;
    autoCalculate();
}

// Default tax selection
function selectDefaultExcise(dutyId) {
    document.getElementById('excise_duty_id').value = dutyId;
    autoCalculate();
}

function selectDefaultServiceTax(dutyId) {
    document.getElementById('excise_duty_id').value = dutyId;
    autoCalculate();
}

// Load example calculation
function loadExampleCalculation() {
    document.getElementById('base_amount').value = 100000;
    {% if default_rates.default_excise %}
    document.getElementById('excise_duty_id').value = {{ default_rates.default_excise.id }};
    {% endif %}
    document.getElementById('vat_rate').value = 5;
    document.getElementById('tds_rate').value = 2;
    autoCalculate();
}

// Auto-calculate on changes
function autoCalculate() {
    // Debounced auto-calculation
    clearTimeout(window.autoCalculateTimer);
    window.autoCalculateTimer = setTimeout(() => {
        const form = document.getElementById('composite-tax-form');
        const formData = new FormData(form);
        
        // Convert to JSON for AJAX
        const data = {
            'base_amount': formData.get('base_amount'),
            'calculation_method': formData.get('calculation_method'),
            'tax_config': {}
        };
        
        // Add excise if selected
        if (formData.get('excise_duty_id')) {
            data.tax_config.excise = {'duty_id': formData.get('excise_duty_id')};
        }
        
        // Add VAT if entered
        if (formData.get('vat_rate')) {
            data.tax_config.vat = {'rate': parseFloat(formData.get('vat_rate'))};
        }
        
        // Add TDS if entered
        if (formData.get('tds_rate')) {
            data.tax_config.tds = {
                'rate': parseFloat(formData.get('tds_rate')),
                'threshold': formData.get('tds_threshold') ? parseFloat(formData.get('tds_threshold')) : null
            };
        }
        
        // Only calculate if we have at least base amount and one tax
        if (data.base_amount && Object.keys(data.tax_config).length > 0) {
            fetch('{% url "accounts:ajax_calculate_tax" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updatePreviewResults(data.result);
                }
            })
            .catch(error => console.error('Auto-calculation error:', error));
        }
    }, 500);
}

// Update preview results
function updatePreviewResults(result) {
    // Implementation would update a preview section
    // This is a simplified version
}

// Save calculation
function saveCalculation(name) {
    const calculation = {
        name: name || `Calculation ${new Date().toLocaleString()}`,
        timestamp: new Date().toISOString(),
        form_data: Object.fromEntries(new FormData(document.getElementById('composite-tax-form'))),
        // Add result data if available
    };
    
    let savedCalculations = JSON.parse(localStorage.getItem('composite_tax_calculations') || '[]');
    savedCalculations.unshift(calculation);
    savedCalculations = savedCalculations.slice(0, 20); // Keep last 20
    
    localStorage.setItem('composite_tax_calculations', JSON.stringify(savedCalculations));
    updateSavedCalculationsDisplay();
}

// Update saved calculations display
function updateSavedCalculationsDisplay() {
    const savedCalculations = JSON.parse(localStorage.getItem('composite_tax_calculations') || '[]');
    const container = document.getElementById('saved-calculations');
    
    if (savedCalculations.length === 0) {
        container.innerHTML = '<p class="text-gray-500 text-center py-4">No saved calculations</p>';
        return;
    }
    
    container.innerHTML = savedCalculations.map((calc, index) => `
        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
            <div class="flex-1">
                <div class="font-medium text-gray-900">${calc.name}</div>
                <div class="text-sm text-gray-600">${new Date(calc.timestamp).toLocaleString()}</div>
            </div>
            <div class="flex space-x-2">
                <button onclick="loadCalculation(${index})" 
                        class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    Load
                </button>
                <button onclick="deleteCalculation(${index})" 
                        class="text-red-600 hover:text-red-800 text-sm font-medium">
                    Delete
                </button>
            </div>
        </div>
    `).join('');
}

// Load saved calculation
function loadCalculation(index) {
    const savedCalculations = JSON.parse(localStorage.getItem('composite_tax_calculations') || '[]');
    const calculation = savedCalculations[index];
    
    if (calculation && calculation.form_data) {
        Object.keys(calculation.form_data).forEach(key => {
            const element = document.querySelector(`[name="${key}"]`);
            if (element) {
                if (element.type === 'radio') {
                    document.querySelector(`[name="${key}"][value="${calculation.form_data[key]}"]`).checked = true;
                } else {
                    element.value = calculation.form_data[key];
                }
            }
        });
        
        autoCalculate();
    }
}

// Delete saved calculation
function deleteCalculation(index) {
    if (confirm('Are you sure you want to delete this calculation?')) {
        let savedCalculations = JSON.parse(localStorage.getItem('composite_tax_calculations') || '[]');
        savedCalculations.splice(index, 1);
        localStorage.setItem('composite_tax_calculations', JSON.stringify(savedCalculations));
        updateSavedCalculationsDisplay();
    }
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    updateSavedCalculationsDisplay();
    
    // Add auto-calculate listeners
    document.getElementById('base_amount').addEventListener('input', autoCalculate);
    document.getElementById('excise_duty_id').addEventListener('change', autoCalculate);
    document.getElementById('vat_rate').addEventListener('input', autoCalculate);
    document.getElementById('tds_rate').addEventListener('input', autoCalculate);
    document.getElementById('tds_threshold').addEventListener('input', autoCalculate);
    document.querySelectorAll('input[name="calculation_method"]').forEach(radio => {
        radio.addEventListener('change', autoCalculate);
    });
    
    // Add save calculation button
    if (document.querySelector('.result-summary')) {
        const saveButton = document.createElement('button');
        saveButton.textContent = 'Save Calculation';
        saveButton.className = 'bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium text-sm';
        saveButton.onclick = () => {
            const name = prompt('Enter a name for this calculation:');
            if (name) saveCalculation(name);
        };
        document.querySelector('.result-summary').appendChild(saveButton);
    }
});
</script>
{% endblock %}