# accounts/urls.py
# URL configuration for Accounts module
# Task Group 1: Chart of Accounts & General Setup

from django.urls import path
from . import views
from .voucher_views import (
    CashVoucherListView, CashVoucherCreateView, CashVoucherUpdateView,
    BankVoucherListView, BankVoucherCreateView, BankVoucherUpdateView,
    ContraEntryListView, ContraEntryCreateView,
    BankReconciliationListView, BankReconciliationCreateView,
    get_next_cheque_number, load_account_heads, load_tds_codes
)
from .cheque_views import (
    ChequeSeriesListView, ChequeSeriesCreateView, ChequeSeriesUpdateView, ChequeSeriesDeleteView,
    ChequeSeriesDetailView, ChequeUsageReportView, 
    get_next_available_cheque, update_current_cheque, cheque_series_summary
)
from .register_views import (
    CashBankRegisterView, RegisterSummaryView, ExportRegisterView,
    get_register_summary_data, get_bank_balance_summary
)
from .reconciliation_views import (
    BankReconciliationMasterListView, BankReconciliationMasterCreateView, 
    BankReconciliationMasterUpdateView, BankReconciliationDetailView,
    BankStatementUploadView, load_unreconciled_transactions, export_reconciliation_report
)
from . import excise_views
from . import tax_calculator_views
from . import vat_register_views
from . import package5_views

app_name = 'accounts'

urlpatterns = [
    # Dashboard
    path('', views.AccountsDashboardView.as_view(), name='dashboard'),
    
    # Account Head URLs (replaces AccHead.aspx functionality)
    path('masters/account-heads/', views.AccountHeadListView.as_view(), name='account_head_list'),
    path('masters/account-heads/create/', views.AccountHeadCreateView.as_view(), name='account_head_create'),
    path('masters/account-heads/<int:id>/edit/', views.AccountHeadUpdateView.as_view(), name='account_head_edit'),
    path('masters/account-heads/<int:id>/delete/', views.AccountHeadDeleteView.as_view(), name='account_head_delete'),
    path('masters/account-heads/<int:id>/edit-row/', views.AccountHeadEditRowView.as_view(), name='account_head_edit_row'),
    path('masters/account-heads/<int:id>/cancel-edit/', views.AccountHeadCancelEditView.as_view(), name='account_head_cancel_edit'),
    
    # Task Group 2: Banking & Cash Management URLs
    path('masters/banks/', views.BankListView.as_view(), name='bank_list'),
    path('masters/banks/create/', views.BankCreateView.as_view(), name='bank_create'),
    path('masters/banks/<int:id>/edit/', views.BankUpdateView.as_view(), name='bank_edit'),
    path('masters/banks/<int:id>/delete/', views.BankDeleteView.as_view(), name='bank_delete'),
    
    path('masters/currencies/', views.CurrencyListView.as_view(), name='currency_list'),
    path('masters/currencies/create/', views.CurrencyCreateView.as_view(), name='currency_create'),
    path('masters/currencies/<int:id>/edit/', views.CurrencyUpdateView.as_view(), name='currency_edit'),
    path('masters/currencies/<int:id>/delete/', views.CurrencyDeleteView.as_view(), name='currency_delete'),
    
    # Task Group 3: Payment Management URLs
    path('masters/payment-modes/', views.PaymentModeListView.as_view(), name='payment_mode_list'),
    path('masters/payment-modes/create/', views.PaymentModeCreateView.as_view(), name='payment_mode_create'),
    path('masters/payment-modes/<int:id>/edit/', views.PaymentModeUpdateView.as_view(), name='payment_mode_edit'),
    path('masters/payment-modes/<int:id>/delete/', views.PaymentModeDeleteView.as_view(), name='payment_mode_delete'),
    
    path('masters/payment-terms/', views.PaymentTermsListView.as_view(), name='payment_terms_list'),
    path('masters/payment-terms/create/', views.PaymentTermsCreateView.as_view(), name='payment_terms_create'),
    path('masters/payment-terms/<int:id>/edit/', views.PaymentTermsUpdateView.as_view(), name='payment_terms_edit'),
    path('masters/payment-terms/<int:id>/delete/', views.PaymentTermsDeleteView.as_view(), name='payment_terms_delete'),
    
    path('masters/paid-types/', views.PaidTypeListView.as_view(), name='paid_type_list'),
    path('masters/paid-types/create/', views.PaidTypeCreateView.as_view(), name='paid_type_create'),
    path('masters/paid-types/<int:id>/edit/', views.PaidTypeUpdateView.as_view(), name='paid_type_edit'),
    path('masters/paid-types/<int:id>/delete/', views.PaidTypeDeleteView.as_view(), name='paid_type_delete'),
    
    # Task Group 4: Taxation Management URLs
    path('masters/vat/', views.VATListView.as_view(), name='vat_list'),
    path('masters/vat/create/', views.VATCreateView.as_view(), name='vat_create'),
    path('masters/vat/<int:id>/edit/', views.VATUpdateView.as_view(), name='vat_edit'),
    path('masters/vat/<int:id>/delete/', views.VATDeleteView.as_view(), name='vat_delete'),
    
    path('masters/tds-codes/', views.TDSCodeListView.as_view(), name='tds_code_list'),
    path('masters/tds-codes/create/', views.TDSCodeCreateView.as_view(), name='tds_code_create'),
    path('masters/tds-codes/<int:id>/edit/', views.TDSCodeUpdateView.as_view(), name='tds_code_edit'),
    path('masters/tds-codes/<int:id>/delete/', views.TDSCodeDeleteView.as_view(), name='tds_code_delete'),
    
    # Task Group 11: Financial Reporting & Analysis URLs
    path('reports/', views.ReportingDashboardView.as_view(), name='reporting_dashboard'),
    path('reports/balance-sheet/', views.BalanceSheetView.as_view(), name='balance_sheet'),
    path('reports/search/', views.AdvancedSearchView.as_view(), name='advanced_search'),
    path('reports/search-results/', views.SearchResultsView.as_view(), name='search_results'),
    path('reports/advice-payments/', views.AdvicePaymentListView.as_view(), name='advice_payment_list'),
    path('reports/advice-payments/create/', views.AdvicePaymentCreateView.as_view(), name='advice_payment_create'),
    
    # Task Group 5: Invoicing & Billing URLs
    # Invoice Against master URLs
    path('masters/invoice-against/', views.InvoiceAgainstListView.as_view(), name='invoice_against_list'),
    path('masters/invoice-against/create/', views.InvoiceAgainstCreateView.as_view(), name='invoice_against_create'),
    path('masters/invoice-against/<int:id>/delete/', views.InvoiceAgainstDeleteView.as_view(), name='invoice_against_delete'),
    
    # Invoicing Dashboard
    path('invoices/', views.InvoicingDashboardView.as_view(), name='invoicing_dashboard'),
    
    # Sales Invoice URLs
    path('invoices/sales/', views.SalesInvoiceListView.as_view(), name='sales_invoice_list'),
    path('invoices/sales/create/', views.SalesInvoiceCreateView.as_view(), name='sales_invoice_create'),
    path('invoices/sales/<int:id>/', views.SalesInvoiceDetailView.as_view(), name='sales_invoice_detail'),
    path('invoices/sales/<int:id>/edit/', views.SalesInvoiceUpdateView.as_view(), name='sales_invoice_edit'),
    path('invoices/sales/<int:id>/delete/', views.SalesInvoiceDeleteView.as_view(), name='sales_invoice_delete'),
    
    # Proforma Invoice URLs
    path('invoices/proforma/', views.ProformaInvoiceListView.as_view(), name='proforma_invoice_list'),
    path('invoices/proforma/create/', views.ProformaInvoiceCreateView.as_view(), name='proforma_invoice_create'),
    
    # Service Tax Invoice URLs
    path('invoices/service-tax/', views.ServiceTaxInvoiceListView.as_view(), name='service_tax_invoice_list'),
    path('invoices/service-tax/create/', views.ServiceTaxInvoiceCreateView.as_view(), name='service_tax_invoice_create'),
    
    # Bill Booking URLs
    path('invoices/bill-booking/', views.BillBookingListView.as_view(), name='bill_booking_list'),
    path('invoices/bill-booking/create/', views.BillBookingCreateView.as_view(), name='bill_booking_create'),
    path('invoices/bill-booking/<int:id>/authorize/', views.BillBookingAuthorizeView.as_view(), name='bill_booking_authorize'),
    
    # Task Group 2: Banking & Cash Management - Voucher Processing URLs
    
    # Enhanced Cheque Series URLs (replaces Cheque_series.aspx functionality)
    path('masters/cheque-series/', ChequeSeriesListView.as_view(), name='cheque_series_list'),
    path('masters/cheque-series/create/', ChequeSeriesCreateView.as_view(), name='cheque_series_create'),
    path('masters/cheque-series/<int:pk>/edit/', ChequeSeriesUpdateView.as_view(), name='cheque_series_edit'),
    path('masters/cheque-series/<int:pk>/delete/', ChequeSeriesDeleteView.as_view(), name='cheque_series_delete'),
    path('masters/cheque-series/<int:pk>/detail/', ChequeSeriesDetailView.as_view(), name='cheque_series_detail'),
    path('reports/cheque-usage/', ChequeUsageReportView.as_view(), name='cheque_usage_report'),
    
    # Cash/Bank Register Reports URLs (replaces Cash_Bank_Register.aspx functionality)
    path('reports/cash-bank-register/', CashBankRegisterView.as_view(), name='cash_bank_register'),
    path('reports/register-summary/', RegisterSummaryView.as_view(), name='register_summary'),
    path('reports/export-register/', ExportRegisterView.as_view(), name='export_register'),
    
    # Cash Voucher URLs (replaces CashVoucher_New.aspx functionality)
    path('transactions/cash-vouchers/', CashVoucherListView.as_view(), name='cash_voucher_list'),
    path('transactions/cash-vouchers/create/', CashVoucherCreateView.as_view(), name='cash_voucher_create'),
    path('transactions/cash-vouchers/<int:pk>/edit/', CashVoucherUpdateView.as_view(), name='cash_voucher_edit'),
    
    # Bank Voucher URLs (replaces BankVoucher.aspx functionality)
    path('transactions/bank-vouchers/', BankVoucherListView.as_view(), name='bank_voucher_list'),
    path('transactions/bank-vouchers/create/', BankVoucherCreateView.as_view(), name='bank_voucher_create'),
    path('transactions/bank-vouchers/<int:pk>/edit/', BankVoucherUpdateView.as_view(), name='bank_voucher_edit'),
    
    # Contra Entry URLs (replaces ContraEntry.aspx functionality)
    path('transactions/contra-entries/', ContraEntryListView.as_view(), name='contra_entry_list'),
    path('transactions/contra-entries/create/', ContraEntryCreateView.as_view(), name='contra_entry_create'),
    
    # Bank Reconciliation URLs (replaces BankReconciliation_New.aspx functionality)
    path('transactions/bank-reconciliations/', BankReconciliationListView.as_view(), name='bank_reconciliation_list'),
    path('transactions/bank-reconciliations/create/', BankReconciliationCreateView.as_view(), name='bank_reconciliation_create'),
    
    # Enhanced Bank Reconciliation URLs with full workflow
    path('reconciliation/masters/', BankReconciliationMasterListView.as_view(), name='bank_reconciliation_master_list'),
    path('reconciliation/masters/create/', BankReconciliationMasterCreateView.as_view(), name='bank_reconciliation_master_create'),
    path('reconciliation/masters/<int:pk>/edit/', BankReconciliationMasterUpdateView.as_view(), name='bank_reconciliation_master_edit'),
    path('reconciliation/masters/<int:pk>/detail/', BankReconciliationDetailView.as_view(), name='bank_reconciliation_detail'),
    path('reconciliation/upload/', BankStatementUploadView.as_view(), name='bank_statement_upload'),
    
    # AJAX URLs for Voucher Processing
    path('ajax/next-cheque-number/', get_next_cheque_number, name='ajax_next_cheque_number'),
    path('ajax/load-account-heads/', load_account_heads, name='ajax_load_account_heads'),
    path('ajax/load-tds-codes/', load_tds_codes, name='ajax_load_tds_codes'),
    
    # AJAX URLs for Bank Reconciliation
    path('ajax/load-unreconciled-transactions/', load_unreconciled_transactions, name='ajax_load_unreconciled_transactions'),
    path('ajax/export-reconciliation-report/', export_reconciliation_report, name='ajax_export_reconciliation_report'),
    
    # AJAX URLs for Enhanced Cheque Management
    path('ajax/next-available-cheque/', get_next_available_cheque, name='ajax_next_available_cheque'),
    path('ajax/update-current-cheque/', update_current_cheque, name='ajax_update_current_cheque'),
    path('ajax/cheque-series-summary/', cheque_series_summary, name='ajax_cheque_series_summary'),
    
    # AJAX URLs for Register Reports
    path('ajax/register-summary-data/', get_register_summary_data, name='ajax_register_summary_data'),
    path('ajax/bank-balance-summary/', get_bank_balance_summary, name='ajax_bank_balance_summary'),
    
    # Task Group 4: Taxation Management - Excise Duty URLs (replaces Excise.aspx functionality)
    path('masters/excise-duties/', excise_views.ExciseDutyListView.as_view(), name='excise_duty_list'),
    path('masters/excise-duties/create/', excise_views.ExciseDutyCreateView.as_view(), name='excise_duty_create'),
    path('masters/excise-duties/<int:pk>/edit/', excise_views.ExciseDutyUpdateView.as_view(), name='excise_duty_edit'),
    path('masters/excise-duties/<int:pk>/delete/', excise_views.ExciseDutyDeleteView.as_view(), name='excise_duty_delete'),
    path('masters/excise-duties/quick-create/', excise_views.QuickExciseDutyCreateView.as_view(), name='excise_duty_quick_create'),
    path('tools/excise-calculator/', excise_views.ExciseCalculatorView.as_view(), name='excise_calculator'),
    path('masters/excise-duties/bulk-operations/', excise_views.ExciseBulkOperationsView.as_view(), name='excise_bulk_operations'),
    path('reports/excise-duty-report/', excise_views.ExciseDutyReportView.as_view(), name='excise_duty_report'),
    path('reports/export-excise-duties/', excise_views.ExportExciseDutyView.as_view(), name='excise_duty_export'),
    
    # AJAX URLs for Excise Duty Management
    path('ajax/excise-duty-details/', excise_views.get_excise_duty_details, name='ajax_excise_duty_details'),
    path('ajax/calculate-excise/', excise_views.calculate_excise_amount, name='ajax_calculate_excise'),
    path('ajax/default-excise-duties/', excise_views.get_default_excise_duties, name='ajax_default_excise_duties'),
    path('ajax/excise-duty-summary/', excise_views.get_excise_duty_summary, name='ajax_excise_duty_summary'),
    
    # Task Group 4: Tax Calculation Engine URLs
    path('tools/tax-calculator/', tax_calculator_views.TaxCalculatorDashboardView.as_view(), name='tax_calculator_dashboard'),
    path('tools/composite-tax-calculator/', tax_calculator_views.CompositeInvoiceTaxCalculatorView.as_view(), name='composite_tax_calculator'),
    path('tools/reverse-calculator/', tax_calculator_views.ReverseCalculatorView.as_view(), name='reverse_tax_calculator'),
    path('tools/tax-comparison/', tax_calculator_views.TaxComparisonView.as_view(), name='tax_comparison'),
    path('reports/tax-rate-analysis/', tax_calculator_views.TaxRateAnalysisView.as_view(), name='tax_rate_analysis'),
    
    # AJAX URLs for Tax Calculator
    path('ajax/calculate-tax/', tax_calculator_views.ajax_calculate_tax, name='ajax_calculate_tax'),
    path('ajax/tax-breakdown/', tax_calculator_views.ajax_get_tax_breakdown, name='ajax_tax_breakdown'),
    path('ajax/compare-tax-scenarios/', tax_calculator_views.ajax_compare_tax_scenarios, name='ajax_compare_tax_scenarios'),
    
    # Task Group 4: VAT Register Reports URLs (replaces Vat_Register.aspx and PurchaseVAT_Register.aspx)
    path('reports/vat-register/', vat_register_views.VATRegisterDashboardView.as_view(), name='vat_register_dashboard'),
    path('reports/sales-vat-register/', vat_register_views.SalesVATRegisterView.as_view(), name='sales_vat_register'),
    path('reports/purchase-vat-register/', vat_register_views.PurchaseVATRegisterView.as_view(), name='purchase_vat_register'),
    
    # Aliases for navigation compatibility
    path('reports/vat-register-sales/', vat_register_views.SalesVATRegisterView.as_view(), name='vat_register_sales'),
    path('reports/vat-register-purchase/', vat_register_views.PurchaseVATRegisterView.as_view(), name='vat_register_purchase'),
    path('reports/vat-return-form/', vat_register_views.VATReturnFormView.as_view(), name='vat_return_form'),
    path('reports/export-vat-register/', vat_register_views.ExportVATRegisterView.as_view(), name='export_vat_register'),
    
    # AJAX URLs for VAT Register
    path('ajax/vat-summary/', vat_register_views.ajax_get_vat_summary, name='ajax_vat_summary'),
    path('ajax/vat-rate-breakdown/', vat_register_views.ajax_get_vat_rate_breakdown, name='ajax_vat_rate_breakdown'),
    
    # Task Group 6: Credit & Debit Management URLs (replaces Credit_Note.aspx, Debit_Note.aspx, etc.)
    # Credit & Debit Management Dashboard
    path('credit-debit/', views.CreditDebitDashboardView.as_view(), name='credit_debit_dashboard'),
    
    # Credit Note URLs
    path('transactions/credit-notes/', views.CreditNoteListView.as_view(), name='credit_note_list'),
    path('transactions/credit-notes/create/', views.CreditNoteCreateView.as_view(), name='credit_note_create'),
    path('transactions/credit-notes/<int:id>/', views.CreditNoteDetailView.as_view(), name='credit_note_detail'),
    path('transactions/credit-notes/<int:id>/edit/', views.CreditNoteUpdateView.as_view(), name='credit_note_edit'),
    path('transactions/credit-notes/<int:id>/delete/', views.CreditNoteDeleteView.as_view(), name='credit_note_delete'),
    path('transactions/credit-notes/<int:id>/approve/', views.CreditNoteApproveView.as_view(), name='credit_note_approve'),
    
    # Debit Note URLs
    path('transactions/debit-notes/', views.DebitNoteListView.as_view(), name='debit_note_list'),
    path('transactions/debit-notes/create/', views.DebitNoteCreateView.as_view(), name='debit_note_create'),
    path('transactions/debit-notes/<int:id>/', views.DebitNoteDetailView.as_view(), name='debit_note_detail'),
    path('transactions/debit-notes/<int:id>/edit/', views.DebitNoteUpdateView.as_view(), name='debit_note_edit'),
    path('transactions/debit-notes/<int:id>/delete/', views.DebitNoteDeleteView.as_view(), name='debit_note_delete'),
    path('transactions/debit-notes/<int:id>/approve/', views.DebitNoteApproveView.as_view(), name='debit_note_approve'),
    
    # Creditors/Debitors Management URLs
    path('transactions/creditors-debitors/', views.CreditorsDebitorsListView.as_view(), name='creditors_debitors_list'),
    path('transactions/creditors-debitors/<int:id>/', views.CreditorsDebitorsDetailView.as_view(), name='creditors_debitors_detail'),
    path('transactions/creditors-debitors/<int:id>/transactions/', views.CreditorsDebitorsTransactionListView.as_view(), name='creditors_debitors_transactions'),
    
    # Sundry Creditors URLs  
    path('masters/sundry-creditors/', views.SundryCreditorListView.as_view(), name='sundry_creditor_list'),
    path('masters/sundry-creditors/create/', views.SundryCreditorCreateView.as_view(), name='sundry_creditor_create'),
    path('masters/sundry-creditors/<int:id>/', views.SundryCreditorDetailView.as_view(), name='sundry_creditor_detail'),
    path('masters/sundry-creditors/<int:id>/edit/', views.SundryCreditorUpdateView.as_view(), name='sundry_creditor_edit'),
    path('masters/sundry-creditors/<int:id>/delete/', views.SundryCreditorDeleteView.as_view(), name='sundry_creditor_delete'),
    path('masters/sundry-creditors/<int:id>/transactions/', views.SundryCreditorTransactionListView.as_view(), name='sundry_creditor_transactions'),
    
    # Sundry Customers URLs
    path('masters/sundry-customers/', views.SundryCustomerListView.as_view(), name='sundry_customer_list'),
    path('masters/sundry-customers/create/', views.SundryCustomerCreateView.as_view(), name='sundry_customer_create'),
    path('masters/sundry-customers/<int:id>/', views.SundryCustomerDetailView.as_view(), name='sundry_customer_detail'),
    path('masters/sundry-customers/<int:id>/edit/', views.SundryCustomerUpdateView.as_view(), name='sundry_customer_edit'),
    path('masters/sundry-customers/<int:id>/delete/', views.SundryCustomerDeleteView.as_view(), name='sundry_customer_delete'),
    path('masters/sundry-customers/<int:id>/transactions/', views.SundryCustomerTransactionListView.as_view(), name='sundry_customer_transactions'),
    
    # Credit & Debit Management Reports
    path('reports/outstanding-amounts/', views.OutstandingAmountsReportView.as_view(), name='outstanding_amounts_report'),
    path('reports/credit-debit-summary/', views.CreditDebitSummaryReportView.as_view(), name='credit_debit_summary_report'),
    path('reports/aging-analysis/', views.AgingAnalysisReportView.as_view(), name='aging_analysis_report'),
    
    # Task Group 7: Capital & Loans Management URLs (replaces LoanType.aspx, IntrestType.aspx, Capital.aspx, ACC_LoanMaster.aspx, CurrentLiabilities.aspx)
    # Capital & Loans Management Dashboard
    path('capital-loans/', views.CapitalLoansDashboardView.as_view(), name='capital_loans_dashboard'),
    
    # Loan Type URLs
    path('masters/loan-types/', views.LoanTypeListView.as_view(), name='loan_type_list'),
    path('masters/loan-types/create/', views.LoanTypeCreateView.as_view(), name='loan_type_create'),
    path('masters/loan-types/<int:id>/delete/', views.LoanTypeDeleteView.as_view(), name='loan_type_delete'),
    
    # Interest Type URLs
    path('masters/interest-types/', views.InterestTypeListView.as_view(), name='interest_type_list'),
    path('masters/interest-types/create/', views.InterestTypeCreateView.as_view(), name='interest_type_create'),
    path('masters/interest-types/<int:id>/delete/', views.InterestTypeDeleteView.as_view(), name='interest_type_delete'),
    
    # Capital Management URLs
    path('transactions/capital/', views.CapitalListView.as_view(), name='capital_list'),
    path('transactions/capital/create/', views.CapitalCreateView.as_view(), name='capital_create'),
    path('transactions/capital/<int:id>/edit/', views.CapitalUpdateView.as_view(), name='capital_edit'),
    
    # Loan Master URLs
    path('transactions/loans/', views.LoanMasterListView.as_view(), name='loan_master_list'),
    path('transactions/loans/create/', views.LoanMasterCreateView.as_view(), name='loan_master_create'),
    path('transactions/loans/<int:id>/', views.LoanMasterDetailView.as_view(), name='loan_master_detail'),
    
    # Current Liabilities URLs
    path('transactions/current-liabilities/', views.CurrentLiabilitiesListView.as_view(), name='current_liabilities_list'),
    path('transactions/current-liabilities/create/', views.CurrentLiabilitiesCreateView.as_view(), name='current_liabilities_create'),
    path('transactions/current-liabilities/<int:id>/edit/', views.CurrentLiabilitiesUpdateView.as_view(), name='current_liabilities_edit'),
    
    # Task Group 8: Asset Management URLs (replaces Asset.aspx, Asset_Register.aspx, Asset_Register1.aspx, AssetRegister_Report.aspx, Acc_Bal_CurrAssets.aspx)
    # Asset Management Dashboard
    path('assets/', views.AssetManagementDashboardView.as_view(), name='asset_dashboard'),
    
    # Asset Master URLs
    path('masters/assets/', views.AssetListView.as_view(), name='asset_list'),
    path('masters/assets/create/', views.AssetCreateView.as_view(), name='asset_create'),
    path('masters/assets/<int:id>/', views.AssetDetailView.as_view(), name='asset_detail'),
    path('masters/assets/<int:id>/edit/', views.AssetUpdateView.as_view(), name='asset_edit'),
    
    # Asset Register URLs
    path('transactions/asset-register/', views.AssetRegisterListView.as_view(), name='asset_register_list'),
    path('transactions/asset-register/create/', views.AssetRegisterCreateView.as_view(), name='asset_register_create'),
    
    # Current Assets URLs
    path('transactions/current-assets/', views.CurrentAssetsListView.as_view(), name='current_assets_list'),
    path('transactions/current-assets/create/', views.CurrentAssetsCreateView.as_view(), name='current_assets_create'),
    path('transactions/current-assets/<int:id>/edit/', views.CurrentAssetsUpdateView.as_view(), name='current_assets_edit'),
    
    # Asset Reports URLs
    path('reports/asset-depreciation/', views.AssetDepreciationReportView.as_view(), name='asset_depreciation_report'),
    path('reports/asset-maintenance/', views.AssetMaintenanceReportView.as_view(), name='asset_maintenance_report'),
    
    # Task Group 9: Expense & Tour Management URLs (replaces TourExpencess.aspx, IOU_Reasons.aspx, TourVoucher.aspx, TourVoucher_Edit.aspx, TourVoucher_Details.aspx, TourVoucher_Edit_Details.aspx)
    # Expense & Tour Management Dashboard
    path('expense-tour/', views.ExpenseTourDashboardView.as_view(), name='expense_tour_dashboard'),
    
    # Tour Expense Master URLs
    path('masters/tour-expenses/', views.TourExpenseListView.as_view(), name='tour_expense_list'),
    path('masters/tour-expenses/create/', views.TourExpenseCreateView.as_view(), name='tour_expense_create'),
    path('masters/tour-expenses/<int:id>/edit/', views.TourExpenseUpdateView.as_view(), name='tour_expense_edit'),
    path('masters/tour-expenses/<int:id>/delete/', views.TourExpenseDeleteView.as_view(), name='tour_expense_delete'),
    
    # IOU Reasons Master URLs
    path('masters/iou-reasons/', views.IOUReasonsListView.as_view(), name='iou_reasons_list'),
    path('masters/iou-reasons/create/', views.IOUReasonsCreateView.as_view(), name='iou_reasons_create'),
    path('masters/iou-reasons/<int:id>/edit/', views.IOUReasonsUpdateView.as_view(), name='iou_reasons_edit'),
    path('masters/iou-reasons/<int:id>/delete/', views.IOUReasonsDeleteView.as_view(), name='iou_reasons_delete'),
    
    # Tour Voucher Transaction URLs
    path('transactions/tour-vouchers/', views.TourVoucherListView.as_view(), name='tour_voucher_list'),
    path('transactions/tour-vouchers/create/', views.TourVoucherCreateView.as_view(), name='tour_voucher_create'),
    path('transactions/tour-vouchers/<int:id>/', views.TourVoucherDetailView.as_view(), name='tour_voucher_detail'),
    path('transactions/tour-vouchers/<int:id>/edit/', views.TourVoucherUpdateView.as_view(), name='tour_voucher_edit'),
    path('transactions/tour-vouchers/<int:id>/delete/', views.TourVoucherDeleteView.as_view(), name='tour_voucher_delete'),
    
    # Tour Voucher Details URLs
    path('transactions/tour-vouchers/<int:voucher_id>/details/', views.TourVoucherDetailsListView.as_view(), name='tour_voucher_details'),
    path('transactions/tour-vouchers/<int:voucher_id>/details/create/', views.TourVoucherDetailsCreateView.as_view(), name='tour_voucher_details_create'),
    path('transactions/tour-voucher-details/<int:detail_id>/delete/', views.TourVoucherDetailsDeleteView.as_view(), name='tour_voucher_details_delete'),
    
    # Tour Voucher Workflow URLs
    path('transactions/tour-vouchers/<int:id>/submit/', views.TourVoucherSubmitView.as_view(), name='tour_voucher_submit'),
    path('transactions/tour-vouchers/<int:id>/approve/', views.TourVoucherApproveView.as_view(), name='tour_voucher_approve'),
    path('transactions/tour-vouchers/<int:id>/return/', views.TourVoucherReturnView.as_view(), name='tour_voucher_return'),
    
    # Task Group 11: Financial Reporting & Analysis - Mail Merge URLs (Task 11.4)
    # Mail Merge Template Management URLs
    path('mail-merge/templates/', views.MailMergeTemplateListView.as_view(), name='mail_merge_template_list'),
    path('mail-merge/templates/create/', views.MailMergeTemplateCreateView.as_view(), name='mail_merge_template_create'),
    path('mail-merge/templates/<int:id>/edit/', views.MailMergeTemplateUpdateView.as_view(), name='mail_merge_template_edit'),
    path('mail-merge/templates/<int:id>/delete/', views.MailMergeTemplateDeleteView.as_view(), name='mail_merge_template_delete'),
    
    # Mail Merge Creation and Processing URLs
    path('mail-merge/', views.MailMergeCreateView.as_view(), name='mail_merge_create'),
    path('mail-merge/preview/', views.MailMergePreviewView.as_view(), name='mail_merge_preview'),
    path('mail-merge/process/', views.MailMergeProcessView.as_view(), name='mail_merge_process'),
    path('mail-merge/status/', views.MailMergeStatusView.as_view(), name='mail_merge_status'),
    
    # HTMX URLs for Mail Merge
    path('ajax/mail-merge-data-source/', views.MailMergeDataSourceView.as_view(), name='mail_merge_data_source'),
    path('ajax/mail-merge-template-preview/', views.MailMergeTemplatePreviewView.as_view(), name='mail_merge_template_preview'),
    path('ajax/mail-merge-record-preview/', views.MailMergeRecordPreviewView.as_view(), name='mail_merge_record_preview'),
    
    # Task Group 10: Freight & Logistics Management URLs (Task 10.1-10.8)
    # Freight & Logistics Dashboard
    path('freight-logistics/', views.FreightLogisticsDashboardView.as_view(), name='freight_logistics_dashboard'),
    
    # Freight Rate Management URLs
    path('masters/freight/', views.FreightListView.as_view(), name='freight_list'),
    path('masters/freight/create/', views.FreightCreateView.as_view(), name='freight_create'),
    path('masters/freight/<int:pk>/edit/', views.FreightUpdateView.as_view(), name='freight_edit'),
    path('masters/freight/<int:pk>/delete/', views.FreightDeleteView.as_view(), name='freight_delete'),
    
    # Packing & Forwarding Management URLs
    path('masters/packing-forwarding/', views.PackingForwardingListView.as_view(), name='packing_forwarding_list'),
    path('masters/packing-forwarding/create/', views.PackingForwardingCreateView.as_view(), name='packing_forwarding_create'),
    path('masters/packing-forwarding/<int:pk>/edit/', views.PackingForwardingEditView.as_view(), name='packing_forwarding_edit'),
    path('masters/packing-forwarding/<int:pk>/delete/', views.PackingForwardingDeleteView.as_view(), name='packing_forwarding_delete'),
    
    # Warranty Terms Management URLs
    path('masters/warranty-terms/', views.WarrantyTermsListView.as_view(), name='warranty_terms_list'),
    path('masters/warranty-terms/create/', views.WarrantyTermsCreateView.as_view(), name='warranty_terms_create'),
    path('masters/warranty-terms/<int:pk>/edit/', views.WarrantyTermsUpdateView.as_view(), name='warranty_terms_edit'),
    path('masters/warranty-terms/<int:pk>/delete/', views.WarrantyTermsDeleteView.as_view(), name='warranty_terms_delete'),

    # Package 5: Missing Implementation URLs - Complete Development Needed
    
    # Group A: Advanced Taxation URLs
    path('masters/excisable-commodities/', package5_views.ExcisableCommodityMasterListView.as_view(), name='excisable_commodity_list'),
    path('masters/excisable-commodities/create/', package5_views.ExcisableCommodityMasterCreateView.as_view(), name='excisable_commodity_create'),
    path('masters/excisable-commodities/<int:pk>/edit/', package5_views.ExcisableCommodityMasterUpdateView.as_view(), name='excisable_commodity_edit'),
    path('masters/excisable-commodities/<int:pk>/delete/', package5_views.ExcisableCommodityMasterDeleteView.as_view(), name='excisable_commodity_delete'),
    
    path('masters/octori/', package5_views.OctoriMasterListView.as_view(), name='octori_list'),
    path('masters/octori/create/', package5_views.OctoriMasterCreateView.as_view(), name='octori_create'),
    path('masters/octori/<int:pk>/edit/', package5_views.OctoriMasterUpdateView.as_view(), name='octori_edit'),
    path('masters/octori/<int:pk>/delete/', package5_views.OctoriMasterDeleteView.as_view(), name='octori_delete'),
    
    path('masters/payment-receipt-against/', package5_views.PaymentReceiptAgainstListView.as_view(), name='payment_receipt_against_list'),
    path('masters/payment-against/create/', package5_views.PaymentAgainstCreateView.as_view(), name='payment_against_create'),
    path('masters/receipt-against/create/', package5_views.ReceiptAgainstCreateView.as_view(), name='receipt_against_create'),
    
    path('reports/purchase-report/', package5_views.PurchaseReportView.as_view(), name='purchase_report'),
    
    # Group B: Advanced Asset Management URLs
    path('transactions/asset-register-primary/', package5_views.AssetRegisterPrimaryListView.as_view(), name='asset_register_primary_list'),
    path('transactions/asset-register-primary/create/', package5_views.AssetRegisterPrimaryCreateView.as_view(), name='asset_register_primary_create'),
    path('transactions/asset-register-primary/<int:pk>/edit/', package5_views.AssetRegisterPrimaryUpdateView.as_view(), name='asset_register_primary_edit'),
    
    path('transactions/asset-register-secondary/', package5_views.AssetRegisterSecondaryListView.as_view(), name='asset_register_secondary_list'),
    path('transactions/asset-register-secondary/create/', package5_views.AssetRegisterSecondaryCreateView.as_view(), name='asset_register_secondary_create'),
    
    path('transactions/current-assets-balance/', package5_views.CurrentAssetsBalanceListView.as_view(), name='current_assets_balance_list'),
    
    # Group C: Capital Structure Management URLs
    path('transactions/capital-particulars/', package5_views.CapitalParticularsListView.as_view(), name='capital_particulars_list'),
    path('transactions/capital-particulars/create/', package5_views.CapitalParticularsCreateView.as_view(), name='capital_particulars_create'),
    
    path('transactions/capital-partner-details/', package5_views.CapitalPartnerDetailsListView.as_view(), name='capital_partner_details_list'),
    path('transactions/capital-partner-details/create/', package5_views.CapitalPartnerDetailsCreateView.as_view(), name='capital_partner_details_create'),
    
    path('transactions/loan-particulars/', package5_views.LoanParticularsListView.as_view(), name='loan_particulars_list'),
    path('transactions/loan-particulars/create/', package5_views.LoanParticularsCreateView.as_view(), name='loan_particulars_create'),
    path('transactions/loan-particulars/<int:pk>/edit/', package5_views.LoanParticularsUpdateView.as_view(), name='loan_particulars_edit'),
    
    # Group D: Advanced Transaction Processing URLs
    path('transactions/creditors-debitors-detail-list/', package5_views.CreditorsDebitorsInDetailListView.as_view(), name='creditors_debitors_detail_list'),
    path('transactions/creditors-debitors-detail-view/<int:pk>/', package5_views.CreditorsDebitorsInDetailView.as_view(), name='creditors_debitors_detail_view'),
    path('transactions/sundry-creditors-detail-list/', package5_views.SundryCreditorInDetailListView.as_view(), name='sundry_creditors_detail_list'),
    path('transactions/sundry-creditors-detail-view/<int:pk>/', package5_views.SundryCreditorInDetailView.as_view(), name='sundry_creditors_detail_view'),
    
    # AJAX URLs for Package 5 Enhanced Functionality
    path('ajax/asset-depreciation-schedule/<int:asset_id>/', package5_views.ajax_asset_depreciation_schedule, name='ajax_asset_depreciation_schedule'),
    path('ajax/loan-emi-calculator/', package5_views.ajax_loan_emi_calculator, name='ajax_loan_emi_calculator'),
    path('ajax/capital-analysis/', package5_views.ajax_capital_analysis, name='ajax_capital_analysis'),
]