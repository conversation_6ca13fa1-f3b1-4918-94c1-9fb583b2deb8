<!-- accounts/templates/accounts/masters/sundry_customer_list.html -->
<!-- Sundry Customer List View Template -->
<!-- Task Package 4: Customer & Creditor Management Templates - Sundry Customer List -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Sundry Customers - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-teal-600 to-sap-teal-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="user-check" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Sundry Customers</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage customer relationships and outstanding receivables</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to Dashboard
                </a>
                <a href="{% url 'accounts:sundry_customer_create' %}" 
                   class="bg-sap-teal-600 hover:bg-sap-teal-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    New Customer
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Search and Filter Section -->
    <div class="mb-6 bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4">
            <form method="get" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4" 
                  hx-get="{% url 'accounts:sundry_customer_list' %}" 
                  hx-target="#customer-table-container" 
                  hx-trigger="keyup changed delay:300ms from:input, change from:select"
                  hx-push-url="true">
                
                <!-- Search -->
                <div class="flex-1">
                    <label for="search" class="block text-sm font-medium text-sap-gray-700 mb-1">Search</label>
                    <input type="text" name="search" value="{{ request.GET.search }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500"
                           placeholder="Search by customer name, code, contact person, email...">
                </div>
                
                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-sap-gray-700 mb-1">Status</label>
                    <select name="status" 
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500">
                        <option value="">All Status</option>
                        <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>Active</option>
                        <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>Inactive</option>
                    </select>
                </div>
                
                <!-- City Filter -->
                <div>
                    <label for="city" class="block text-sm font-medium text-sap-gray-700 mb-1">City</label>
                    <input type="text" name="city" value="{{ request.GET.city }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500"
                           placeholder="Filter by city...">
                </div>
                
                <!-- Customer Category Filter -->
                <div>
                    <label for="category" class="block text-sm font-medium text-sap-gray-700 mb-1">Category</label>
                    <select name="category" 
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500">
                        <option value="">All Categories</option>
                        <option value="premium" {% if request.GET.category == 'premium' %}selected{% endif %}>Premium</option>
                        <option value="regular" {% if request.GET.category == 'regular' %}selected{% endif %}>Regular</option>
                        <option value="new" {% if request.GET.category == 'new' %}selected{% endif %}>New</option>
                    </select>
                </div>
                
                <!-- Clear Button -->
                {% if request.GET %}
                <div>
                    <a href="{% url 'accounts:sundry_customer_list' %}" 
                       class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                        Clear
                    </a>
                </div>
                {% endif %}
            </form>
        </div>
    </div>

    <!-- Customer Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-teal-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="user-check" class="w-6 h-6 text-sap-teal-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Customers</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ total_customers|default:0 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="check-circle" class="w-6 h-6 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Active</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ active_customers|default:0 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="credit-card" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Credit Limit</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ total_credit_limit|floatformat:2|default:"0.00" }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="trending-up" class="w-6 h-6 text-sap-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Outstanding Receivables</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ total_receivables|floatformat:2|default:"0.00" }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Performance Chart -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm mb-6">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <h3 class="text-lg font-medium text-sap-gray-800">Customer Performance Overview</h3>
        </div>
        <div class="px-6 py-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="text-center p-4 bg-sap-blue-50 rounded-lg">
                    <p class="text-2xl font-bold text-sap-blue-600">₹0.00</p>
                    <p class="text-sm text-sap-gray-600">This Month Sales</p>
                </div>
                <div class="text-center p-4 bg-sap-green-50 rounded-lg">
                    <p class="text-2xl font-bold text-sap-green-600">₹0.00</p>
                    <p class="text-sm text-sap-gray-600">Collected This Month</p>
                </div>
                <div class="text-center p-4 bg-sap-yellow-50 rounded-lg">
                    <p class="text-2xl font-bold text-sap-yellow-600">0</p>
                    <p class="text-sm text-sap-gray-600">New Customers</p>
                </div>
                <div class="text-center p-4 bg-sap-purple-50 rounded-lg">
                    <p class="text-2xl font-bold text-sap-purple-600">85%</p>
                    <p class="text-sm text-sap-gray-600">Customer Satisfaction</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Customers Table Container -->
    <div id="customer-table-container">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-sap-gray-800">Sundry Customers</h3>
                    <div class="flex items-center space-x-2">
                        <button type="button" onclick="exportCustomers()" 
                                class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                            Export
                        </button>
                        <button type="button" onclick="bulkInvoice()" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="file-text" class="w-4 h-4 inline mr-2"></i>
                            Bulk Invoice
                        </button>
                        <button type="button" onclick="customerAnalytics()" 
                                class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="bar-chart" class="w-4 h-4 inline mr-2"></i>
                            Analytics
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-sap-gray-200">
                    <thead class="bg-sap-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                <input type="checkbox" id="select-all" class="rounded border-sap-gray-300 text-sap-teal-600 focus:ring-sap-teal-500">
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Customer Details
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Contact Information
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Financial Details
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Credit Score
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-sap-gray-200">
                        {% for customer in sundry_customers %}
                        <tr class="hover:bg-sap-gray-50 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" name="selected_customers" value="{{ customer.id }}" 
                                       class="rounded border-sap-gray-300 text-sap-teal-600 focus:ring-sap-teal-500">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-sap-teal-400 to-sap-teal-600 rounded-lg flex items-center justify-center text-white font-semibold text-sm">
                                        {{ customer.customer_name|first|upper|default:"C" }}
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-sap-gray-900">
                                            <a href="{% url 'accounts:sundry_customer_detail' customer.id %}" 
                                               class="hover:text-sap-teal-600 transition-colors">
                                                {{ customer.customer_name|default:"N/A" }}
                                            </a>
                                        </div>
                                        <div class="text-sm text-sap-gray-500">{{ customer.customer_code|default:"N/A" }}</div>
                                        <div class="text-xs text-sap-gray-400">{{ customer.contact_person|default:"N/A" }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-sap-gray-900">
                                    {% if customer.email %}
                                        <div class="flex items-center mb-1">
                                            <i data-lucide="mail" class="w-3 h-3 mr-1 text-sap-gray-400"></i>
                                            <a href="mailto:{{ customer.email }}" class="hover:text-sap-teal-600">{{ customer.email }}</a>
                                        </div>
                                    {% endif %}
                                    {% if customer.mobile %}
                                        <div class="flex items-center mb-1">
                                            <i data-lucide="phone" class="w-3 h-3 mr-1 text-sap-gray-400"></i>
                                            <a href="tel:{{ customer.mobile }}" class="hover:text-sap-teal-600">{{ customer.mobile }}</a>
                                        </div>
                                    {% endif %}
                                    {% if customer.city %}
                                        <div class="flex items-center">
                                            <i data-lucide="map-pin" class="w-3 h-3 mr-1 text-sap-gray-400"></i>
                                            <span class="text-xs">{{ customer.city.city_name }}</span>
                                        </div>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-sap-gray-900">
                                    <div class="flex items-center justify-between mb-1">
                                        <span class="text-xs text-sap-gray-500">Credit Limit:</span>
                                        <span class="font-medium">₹{{ customer.credit_limit|floatformat:2|default:"0.00" }}</span>
                                    </div>
                                    <div class="flex items-center justify-between mb-1">
                                        <span class="text-xs text-sap-gray-500">Outstanding:</span>
                                        <span class="font-medium {% if customer.current_balance_type == 'Debit' %}text-sap-red-600{% else %}text-sap-green-600{% endif %}">
                                            ₹{{ customer.current_balance|floatformat:2|default:"0.00" }}
                                        </span>
                                    </div>
                                    {% if customer.payment_terms %}
                                        <div class="text-xs text-sap-gray-400">{{ customer.payment_terms|truncatechars:20 }}</div>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    {% comment %} Credit score calculation would be based on payment history {% endcomment %}
                                    {% with score=85 %}
                                    <div class="w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold
                                        {% if score >= 80 %}bg-sap-green-100 text-sap-green-800
                                        {% elif score >= 60 %}bg-sap-yellow-100 text-sap-yellow-800
                                        {% else %}bg-sap-red-100 text-sap-red-800{% endif %}">
                                        {{ score }}
                                    </div>
                                    <div class="ml-2">
                                        <div class="text-xs font-medium text-sap-gray-900">
                                            {% if score >= 80 %}Excellent
                                            {% elif score >= 60 %}Good
                                            {% else %}Poor{% endif %}
                                        </div>
                                        <div class="text-xs text-sap-gray-500">Credit Score</div>
                                    </div>
                                    {% endwith %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if customer.is_active %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-green-100 text-sap-green-800">
                                        <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                        Active
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-red-100 text-sap-red-800">
                                        <i data-lucide="x-circle" class="w-3 h-3 mr-1"></i>
                                        Inactive
                                    </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <!-- Quick Actions Dropdown -->
                                    <div class="relative" x-data="{ open: false }">
                                        <button @click="open = !open" 
                                                class="bg-sap-teal-600 hover:bg-sap-teal-700 text-white px-3 py-1 rounded-lg text-xs font-medium transition-colors">
                                            <i data-lucide="more-horizontal" class="w-4 h-4 inline mr-1"></i>
                                            Actions
                                        </button>
                                        <div x-show="open" @click.away="open = false" 
                                             class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-sap-gray-200 z-10"
                                             x-transition:enter="transition ease-out duration-200"
                                             x-transition:enter-start="opacity-0 scale-95"
                                             x-transition:enter-end="opacity-100 scale-100"
                                             x-transition:leave="transition ease-in duration-75"
                                             x-transition:leave-start="opacity-100 scale-100"
                                             x-transition:leave-end="opacity-0 scale-95">
                                            <div class="py-1">
                                                <a href="{% url 'accounts:sundry_customer_detail' customer.id %}" 
                                                   class="flex items-center px-4 py-2 text-sm text-sap-gray-700 hover:bg-sap-gray-100">
                                                    <i data-lucide="eye" class="w-4 h-4 mr-2"></i>
                                                    View Details
                                                </a>
                                                <a href="{% url 'accounts:sundry_customer_edit' customer.id %}" 
                                                   class="flex items-center px-4 py-2 text-sm text-sap-gray-700 hover:bg-sap-gray-100">
                                                    <i data-lucide="edit" class="w-4 h-4 mr-2"></i>
                                                    Edit
                                                </a>
                                                <a href="{% url 'accounts:sundry_customer_transactions' customer.id %}" 
                                                   class="flex items-center px-4 py-2 text-sm text-sap-gray-700 hover:bg-sap-gray-100">
                                                    <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                                                    View Transactions
                                                </a>
                                                <button type="button" onclick="createInvoice({{ customer.id }})" 
                                                        class="flex items-center w-full px-4 py-2 text-sm text-sap-gray-700 hover:bg-sap-gray-100">
                                                    <i data-lucide="file-text" class="w-4 h-4 mr-2"></i>
                                                    Create Invoice
                                                </button>
                                                <button type="button" onclick="recordPayment({{ customer.id }})" 
                                                        class="flex items-center w-full px-4 py-2 text-sm text-sap-gray-700 hover:bg-sap-gray-100">
                                                    <i data-lucide="credit-card" class="w-4 h-4 mr-2"></i>
                                                    Record Payment
                                                </button>
                                                <button type="button" onclick="sendEmail({{ customer.id }})" 
                                                        class="flex items-center w-full px-4 py-2 text-sm text-sap-gray-700 hover:bg-sap-gray-100">
                                                    <i data-lucide="mail" class="w-4 h-4 mr-2"></i>
                                                    Send Email
                                                </button>
                                                <div class="border-t border-sap-gray-100"></div>
                                                <a href="{% url 'accounts:sundry_customer_delete' customer.id %}" 
                                                   class="flex items-center px-4 py-2 text-sm text-sap-red-600 hover:bg-sap-red-50">
                                                    <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
                                                    Delete
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center justify-center">
                                    <i data-lucide="user-check" class="w-12 h-12 text-sap-gray-400 mb-4"></i>
                                    <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No customers found</h3>
                                    <p class="text-sap-gray-500 mb-4">Get started by adding your first customer.</p>
                                    <a href="{% url 'accounts:sundry_customer_create' %}" 
                                       class="bg-sap-teal-600 hover:bg-sap-teal-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                        <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                                        Add First Customer
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
            <div class="px-6 py-4 border-t border-sap-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <p class="text-sm text-sap-gray-700">
                            Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} customers
                        </p>
                    </div>
                    <div class="flex items-center space-x-2">
                        {% if page_obj.has_previous %}
                            <a href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.city %}city={{ request.GET.city }}&{% endif %}{% if request.GET.category %}category={{ request.GET.category }}&{% endif %}page={{ page_obj.previous_page_number }}"
                               class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-3 py-2 rounded-lg text-sm font-medium transition-colors">
                                Previous
                            </a>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <span class="bg-sap-teal-600 text-white px-3 py-2 rounded-lg text-sm font-medium">{{ num }}</span>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <a href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.city %}city={{ request.GET.city }}&{% endif %}{% if request.GET.category %}category={{ request.GET.category }}&{% endif %}page={{ num }}"
                                   class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-3 py-2 rounded-lg text-sm font-medium transition-colors">{{ num }}</a>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <a href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.city %}city={{ request.GET.city }}&{% endif %}{% if request.GET.category %}category={{ request.GET.category }}&{% endif %}page={{ page_obj.next_page_number }}"
                               class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-3 py-2 rounded-lg text-sm font-medium transition-colors">
                                Next
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- JavaScript for Interactive Features -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select All Functionality
    const selectAllCheckbox = document.getElementById('select-all');
    const customerCheckboxes = document.querySelectorAll('input[name="selected_customers"]');
    
    selectAllCheckbox?.addEventListener('change', function() {
        customerCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
    
    // Export functionality
    window.exportCustomers = function() {
        const selectedCustomers = Array.from(document.querySelectorAll('input[name="selected_customers"]:checked'))
                                       .map(cb => cb.value);
        
        if (selectedCustomers.length === 0) {
            alert('Please select customers to export');
            return;
        }
        
        // Create export form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{% url "accounts:sundry_customer_export" %}';
        
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value;
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrfmiddlewaretoken';
            csrfInput.value = csrfToken;
            form.appendChild(csrfInput);
        }
        
        selectedCustomers.forEach(id => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'customer_ids';
            input.value = id;
            form.appendChild(input);
        });
        
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    };
    
    // Bulk invoice functionality
    window.bulkInvoice = function() {
        const selectedCustomers = Array.from(document.querySelectorAll('input[name="selected_customers"]:checked'))
                                       .map(cb => cb.value);
        
        if (selectedCustomers.length === 0) {
            alert('Please select customers for bulk invoicing');
            return;
        }
        
        window.location.href = `/accounts/invoices/bulk/?customer_ids=${selectedCustomers.join(',')}`;
    };
    
    // Customer analytics functionality
    window.customerAnalytics = function() {
        window.open('/accounts/reports/customer-analytics/', '_blank');
    };
    
    // Individual action functions
    window.createInvoice = function(customerId) {
        window.location.href = `/accounts/invoices/create/?customer_id=${customerId}`;
    };
    
    window.recordPayment = function(customerId) {
        window.location.href = `/accounts/payments/create/?customer_id=${customerId}`;
    };
    
    window.sendEmail = function(customerId) {
        window.location.href = `/accounts/communication/email/?customer_id=${customerId}`;
    };
});
</script>
{% endblock %}