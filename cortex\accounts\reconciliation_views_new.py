# accounts/reconciliation_views_new.py
# Django views for Bank Reconciliation functionality
# Task Group 2: Banking Operations - Bank Reconciliation Management

from django.views.generic import ListView, CreateView, DetailView
from django.contrib import messages
from django.urls import reverse_lazy
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db import transaction

from .models import (
    BankReconciliationMaster, BankReconciliationDetails
)
from .forms import (
    BankReconciliationMasterForm, BankReconciliationDetailsForm, 
    BankReconciliationSearchForm
)


class BankReconciliationDashboardView(LoginRequiredMixin, ListView):
    """Bank Reconciliation Dashboard - Main landing page"""
    model = BankReconciliationMaster
    template_name = 'accounts/reconciliation/dashboard.html'
    context_object_name = 'reconciliations'
    paginate_by = 20

    def get_queryset(self):
        queryset = BankReconciliationMaster.objects.select_related('bank', 'company', 'financial_year').order_by('-reconciliation_date', '-id')
        
        # Apply search filters
        search_form = BankReconciliationSearchForm(self.request.GET)
        if search_form.is_valid():
            if search_form.cleaned_data.get('bank'):
                queryset = queryset.filter(bank=search_form.cleaned_data['bank'])
            if search_form.cleaned_data.get('from_date'):
                queryset = queryset.filter(reconciliation_date__gte=search_form.cleaned_data['from_date'])
            if search_form.cleaned_data.get('to_date'):
                queryset = queryset.filter(reconciliation_date__lte=search_form.cleaned_data['to_date'])
            if search_form.cleaned_data.get('status'):
                queryset = queryset.filter(status=search_form.cleaned_data['status'])
            if search_form.cleaned_data.get('reconciliation_no'):
                queryset = queryset.filter(reconciliation_no__icontains=search_form.cleaned_data['reconciliation_no'])
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = BankReconciliationSearchForm(self.request.GET)
        
        # Summary statistics
        context['total_reconciliations'] = BankReconciliationMaster.objects.count()
        context['pending_reconciliations'] = BankReconciliationMaster.objects.filter(status='Pending').count()
        context['in_progress_reconciliations'] = BankReconciliationMaster.objects.filter(status='In Progress').count()
        context['completed_reconciliations'] = BankReconciliationMaster.objects.filter(status='Completed').count()
        
        return context


class BankReconciliationCreateView(LoginRequiredMixin, CreateView):
    """Create new Bank Reconciliation"""
    model = BankReconciliationMaster
    form_class = BankReconciliationMasterForm
    template_name = 'accounts/reconciliation/create.html'
    success_url = reverse_lazy('accounts:bank_reconciliation_dashboard')

    def form_valid(self, form):
        with transaction.atomic():
            # Set company and financial year from session/context
            # TODO: Get from company context processor
            form.instance.status = 'In Progress'
            
            # Generate reconciliation number
            last_reconciliation = BankReconciliationMaster.objects.order_by('-id').first()
            if last_reconciliation and last_reconciliation.reconciliation_no:
                last_no = int(last_reconciliation.reconciliation_no.split('-')[-1])
                form.instance.reconciliation_no = f"BR-{last_no + 1:04d}"
            else:
                form.instance.reconciliation_no = "BR-0001"
            
            # Calculate difference amount
            if form.cleaned_data.get('difference_amount'):
                form.instance.difference_amount = form.cleaned_data['difference_amount']
            
            response = super().form_valid(form)
            messages.success(self.request, f'Bank reconciliation {form.instance.reconciliation_no} created successfully.')
            return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Create Bank Reconciliation'
        return context


class BankReconciliationDetailView(LoginRequiredMixin, DetailView):
    """Bank Reconciliation Detail View with inline editing"""
    model = BankReconciliationMaster
    template_name = 'accounts/reconciliation/detail.html'
    context_object_name = 'reconciliation'
    pk_url_kwarg = 'id'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        reconciliation = self.get_object()
        
        # Get reconciliation details
        context['reconciliation_details'] = BankReconciliationDetails.objects.filter(
            reconciliation_master=reconciliation
        ).order_by('transaction_date', 'id')
        
        # Forms for adding new details
        context['details_form'] = BankReconciliationDetailsForm()
        
        # Summary calculations
        details = context['reconciliation_details']
        context['total_book_amount'] = sum(detail.book_amount or 0 for detail in details)
        context['total_bank_amount'] = sum(detail.bank_amount or 0 for detail in details)
        context['total_difference'] = context['total_book_amount'] - context['total_bank_amount']
        context['reconciled_count'] = details.filter(is_reconciled=True).count()
        context['unreconciled_count'] = details.filter(is_reconciled=False).count()
        
        return context


def update_reconciliation_totals(reconciliation):
    """Update reconciliation master totals based on details"""
    details = BankReconciliationDetails.objects.filter(reconciliation_master=reconciliation)
    
    reconciled_amount = sum(detail.book_amount or 0 for detail in details.filter(is_reconciled=True))
    outstanding_amount = sum(detail.book_amount or 0 for detail in details.filter(is_reconciled=False))
    
    reconciliation.reconciled_amount = reconciled_amount
    reconciliation.outstanding_amount = outstanding_amount
    
    # Auto-update status based on reconciliation progress
    if outstanding_amount == 0 and reconciled_amount > 0:
        reconciliation.status = 'Completed'
    elif reconciled_amount > 0:
        reconciliation.status = 'In Progress'
    else:
        reconciliation.status = 'Pending'
    
    reconciliation.save()