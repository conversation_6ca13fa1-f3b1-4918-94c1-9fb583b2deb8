﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="HrsBudgetSummary" targetNamespace="http://tempuri.org/HrsBudgetSummary.xsd" xmlns:mstns="http://tempuri.org/HrsBudgetSummary.xsd" xmlns="http://tempuri.org/HrsBudgetSummary.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="HrsBudgetSummary" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="HrsBudgetSummary" msprop:Generator_DataSetName="HrsBudgetSummary">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="WONo" msprop:Generator_UserColumnName="WONo" msprop:Generator_ColumnPropNameInRow="WONo" msprop:Generator_ColumnVarNameInTable="columnWONo" msprop:Generator_ColumnPropNameInTable="WONoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="ProjectTitle" msprop:Generator_UserColumnName="ProjectTitle" msprop:Generator_ColumnVarNameInTable="columnProjectTitle" msprop:Generator_ColumnPropNameInRow="ProjectTitle" msprop:Generator_ColumnPropNameInTable="ProjectTitleColumn" type="xs:string" minOccurs="0" />
              <xs:element name="MDesign" msprop:Generator_UserColumnName="MDesign" msprop:Generator_ColumnVarNameInTable="columnMDesign" msprop:Generator_ColumnPropNameInRow="MDesign" msprop:Generator_ColumnPropNameInTable="MDesignColumn" type="xs:double" minOccurs="0" />
              <xs:element name="MAssly" msprop:Generator_UserColumnName="MAssly" msprop:Generator_ColumnVarNameInTable="columnMAssly" msprop:Generator_ColumnPropNameInRow="MAssly" msprop:Generator_ColumnPropNameInTable="MAsslyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="MCert" msprop:Generator_UserColumnName="MCert" msprop:Generator_ColumnVarNameInTable="columnMCert" msprop:Generator_ColumnPropNameInRow="MCert" msprop:Generator_ColumnPropNameInTable="MCertColumn" type="xs:double" minOccurs="0" />
              <xs:element name="MTrials" msprop:Generator_UserColumnName="MTrials" msprop:Generator_ColumnVarNameInTable="columnMTrials" msprop:Generator_ColumnPropNameInRow="MTrials" msprop:Generator_ColumnPropNameInTable="MTrialsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="MDisp" msprop:Generator_UserColumnName="MDisp" msprop:Generator_ColumnVarNameInTable="columnMDisp" msprop:Generator_ColumnPropNameInRow="MDisp" msprop:Generator_ColumnPropNameInTable="MDispColumn" type="xs:double" minOccurs="0" />
              <xs:element name="MIC" msprop:Generator_UserColumnName="MIC" msprop:Generator_ColumnVarNameInTable="columnMIC" msprop:Generator_ColumnPropNameInRow="MIC" msprop:Generator_ColumnPropNameInTable="MICColumn" type="xs:double" minOccurs="0" />
              <xs:element name="MTryOut" msprop:Generator_UserColumnName="MTryOut" msprop:Generator_ColumnVarNameInTable="columnMTryOut" msprop:Generator_ColumnPropNameInRow="MTryOut" msprop:Generator_ColumnPropNameInTable="MTryOutColumn" type="xs:double" minOccurs="0" />
              <xs:element name="DDesign" msprop:Generator_UserColumnName="DDesign" msprop:Generator_ColumnVarNameInTable="columnDDesign" msprop:Generator_ColumnPropNameInRow="DDesign" msprop:Generator_ColumnPropNameInTable="DDesignColumn" type="xs:double" minOccurs="0" />
              <xs:element name="DAssly" msprop:Generator_UserColumnName="DAssly" msprop:Generator_ColumnVarNameInTable="columnDAssly" msprop:Generator_ColumnPropNameInRow="DAssly" msprop:Generator_ColumnPropNameInTable="DAsslyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="DCert" msprop:Generator_UserColumnName="DCert" msprop:Generator_ColumnVarNameInTable="columnDCert" msprop:Generator_ColumnPropNameInRow="DCert" msprop:Generator_ColumnPropNameInTable="DCertColumn" type="xs:double" minOccurs="0" />
              <xs:element name="DTrials" msprop:Generator_UserColumnName="DTrials" msprop:Generator_ColumnVarNameInTable="columnDTrials" msprop:Generator_ColumnPropNameInRow="DTrials" msprop:Generator_ColumnPropNameInTable="DTrialsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="DDisp" msprop:Generator_UserColumnName="DDisp" msprop:Generator_ColumnVarNameInTable="columnDDisp" msprop:Generator_ColumnPropNameInRow="DDisp" msprop:Generator_ColumnPropNameInTable="DDispColumn" type="xs:double" minOccurs="0" />
              <xs:element name="DIC" msprop:Generator_UserColumnName="DIC" msprop:Generator_ColumnVarNameInTable="columnDIC" msprop:Generator_ColumnPropNameInRow="DIC" msprop:Generator_ColumnPropNameInTable="DICColumn" type="xs:double" minOccurs="0" />
              <xs:element name="DTryOut" msprop:Generator_UserColumnName="DTryOut" msprop:Generator_ColumnVarNameInTable="columnDTryOut" msprop:Generator_ColumnPropNameInRow="DTryOut" msprop:Generator_ColumnPropNameInTable="DTryOutColumn" type="xs:double" minOccurs="0" />
              <xs:element name="HyrLink" msprop:Generator_UserColumnName="HyrLink" msprop:Generator_ColumnPropNameInRow="HyrLink" msprop:Generator_ColumnVarNameInTable="columnHyrLink" msprop:Generator_ColumnPropNameInTable="HyrLinkColumn" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>