# Generated by Django 5.2.1 on 2025-06-13 20:56

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="ManPowerPlanning",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                (
                    "sys_date",
                    models.TextField(blank=True, db_column="SysDate", null=True),
                ),
                (
                    "sys_time",
                    models.TextField(blank=True, db_column="SysTime", null=True),
                ),
                (
                    "comp_id",
                    models.IntegerField(blank=True, db_column="CompId", null=True),
                ),
                (
                    "session_id",
                    models.TextField(blank=True, db_column="SessionId", null=True),
                ),
                (
                    "fin_year_id",
                    models.IntegerField(blank=True, db_column="FinYearId", null=True),
                ),
                (
                    "emp_id",
                    models.IntegerField(blank=True, db_column="EmpId", null=True),
                ),
                ("date", models.TextField(blank=True, db_column="Date", null=True)),
                ("wo_no", models.TextField(blank=True, db_column="WONo", null=True)),
                ("dept", models.TextField(blank=True, db_column="Dept", null=True)),
                ("types", models.TextField(blank=True, db_column="Types", null=True)),
                (
                    "amendment_no",
                    models.IntegerField(blank=True, db_column="AmendmentNo", null=True),
                ),
            ],
            options={
                "db_table": "tblPM_ManPowerPlanning",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ManPowerPlanningDetails",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("m_id", models.IntegerField(blank=True, db_column="MId", null=True)),
                (
                    "equip_id",
                    models.IntegerField(blank=True, db_column="EquipId", null=True),
                ),
                (
                    "category",
                    models.TextField(blank=True, db_column="Category", null=True),
                ),
                (
                    "sub_category",
                    models.TextField(blank=True, db_column="SubCategory", null=True),
                ),
                (
                    "planned_desc",
                    models.TextField(blank=True, db_column="PlannedDesc", null=True),
                ),
                (
                    "actual_desc",
                    models.TextField(blank=True, db_column="ActualDesc", null=True),
                ),
                ("hour", models.TextField(blank=True, db_column="Hour", null=True)),
            ],
            options={
                "db_table": "tblPM_ManPowerPlanning_Details",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ProjectManufacturingAssemblyDetail",
            fields=[
                (
                    "id",
                    models.IntegerField(
                        db_column="Id", primary_key=True, serialize=False
                    ),
                ),
                ("m_id", models.IntegerField(blank=True, db_column="MId", null=True)),
                (
                    "fixture_no",
                    models.TextField(blank=True, db_column="FixtureNo", null=True),
                ),
                (
                    "item_no",
                    models.TextField(blank=True, db_column="ItemNo", null=True),
                ),
                (
                    "description",
                    models.TextField(blank=True, db_column="Description", null=True),
                ),
                ("qty", models.TextField(blank=True, db_column="Qty", null=True)),
                (
                    "detailing",
                    models.TextField(blank=True, db_column="Detailing", null=True),
                ),
                (
                    "tpl_entry",
                    models.TextField(blank=True, db_column="TplEntry", null=True),
                ),
                (
                    "flame_cut",
                    models.TextField(blank=True, db_column="FlameCut", null=True),
                ),
                (
                    "c_flame_cut",
                    models.TextField(blank=True, db_column="CFlameCut", null=True),
                ),
                (
                    "channel",
                    models.TextField(blank=True, db_column="Channlel", null=True),
                ),
                (
                    "list_field",
                    models.TextField(blank=True, db_column="List", null=True),
                ),
                (
                    "receive",
                    models.TextField(blank=True, db_column="Receive", null=True),
                ),
                (
                    "fabrication",
                    models.TextField(blank=True, db_column="Fabrication", null=True),
                ),
                ("c_sr", models.TextField(blank=True, db_column="CSR", null=True)),
                ("mc_ing", models.TextField(blank=True, db_column="MCIng", null=True)),
                (
                    "tapping",
                    models.TextField(blank=True, db_column="Tapping", null=True),
                ),
                (
                    "painting",
                    models.TextField(blank=True, db_column="Painting", null=True),
                ),
            ],
            options={
                "db_table": "tblPM_Project_Manufacturing_Assemly_Detail",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ProjectManufacturingAssemblyMaster",
            fields=[
                (
                    "id",
                    models.IntegerField(
                        db_column="Id", primary_key=True, serialize=False
                    ),
                ),
                (
                    "sys_date",
                    models.TextField(blank=True, db_column="SysDate", null=True),
                ),
                (
                    "sys_time",
                    models.TextField(blank=True, db_column="SysTime", null=True),
                ),
                (
                    "comp_id",
                    models.IntegerField(blank=True, db_column="CompId", null=True),
                ),
                (
                    "session_id",
                    models.TextField(blank=True, db_column="SessionId", null=True),
                ),
                (
                    "fin_year_id",
                    models.IntegerField(blank=True, db_column="FinYearId", null=True),
                ),
                ("wo_no", models.TextField(blank=True, db_column="WONo", null=True)),
                (
                    "assembly_date",
                    models.TextField(blank=True, db_column="AssemblyDate", null=True),
                ),
            ],
            options={
                "db_table": "tblPM_Project_Manufacturing_Assemly_Master",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ProjectManufacturingPlanDetail",
            fields=[
                (
                    "id",
                    models.IntegerField(
                        db_column="Id", primary_key=True, serialize=False
                    ),
                ),
                (
                    "prjctno",
                    models.TextField(blank=True, db_column="PRJCTNO", null=True),
                ),
                (
                    "item_code",
                    models.TextField(blank=True, db_column="ItemCode", null=True),
                ),
                (
                    "description",
                    models.TextField(blank=True, db_column="Description", null=True),
                ),
                ("uom", models.TextField(blank=True, db_column="UOM", null=True)),
                ("bomq", models.TextField(blank=True, db_column="BOMQ", null=True)),
                ("design", models.TextField(blank=True, db_column="Design", null=True)),
                (
                    "vendor_plan_date",
                    models.TextField(blank=True, db_column="VendorPlanDate", null=True),
                ),
                (
                    "vendor_act",
                    models.TextField(blank=True, db_column="VendorAct", null=True),
                ),
                ("wo_no", models.TextField(blank=True, db_column="WONo", null=True)),
                (
                    "vendor_plan",
                    models.TextField(blank=True, db_column="VendorPlan", null=True),
                ),
            ],
            options={
                "db_table": "tblPM_Project_Manufacturing_Plan_Detail",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ProjectPlanningDesign",
            fields=[
                (
                    "id",
                    models.IntegerField(
                        db_column="Id", primary_key=True, serialize=False
                    ),
                ),
                (
                    "activities",
                    models.TextField(blank=True, db_column="Activities", null=True),
                ),
            ],
            options={
                "db_table": "tblPM_ProjectPlanning_Design",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ProjectPlanningDesigner",
            fields=[
                (
                    "id",
                    models.IntegerField(
                        db_column="Id", primary_key=True, serialize=False
                    ),
                ),
                (
                    "name_proj",
                    models.TextField(blank=True, db_column="Name_Proj", null=True),
                ),
                ("wo_no", models.TextField(blank=True, db_column="Wo_No", null=True)),
                (
                    "no_fix_rqu",
                    models.TextField(blank=True, db_column="No_Fix_Rqu", null=True),
                ),
                (
                    "des_lea",
                    models.TextField(blank=True, db_column="Des_Lea", null=True),
                ),
                (
                    "des_mem",
                    models.TextField(blank=True, db_column="Des_Mem", null=True),
                ),
                ("sr_no", models.TextField(blank=True, db_column="Sr_No", null=True)),
                (
                    "name_act",
                    models.TextField(blank=True, db_column="Name_Act", null=True),
                ),
                ("rev_no", models.TextField(blank=True, db_column="Rev_No", null=True)),
                (
                    "no_days",
                    models.TextField(blank=True, db_column="No_Days", null=True),
                ),
                (
                    "as_plan_from",
                    models.TextField(blank=True, db_column="As_Plan_From", null=True),
                ),
                (
                    "as_plan_to",
                    models.TextField(blank=True, db_column="As_Plan_To", null=True),
                ),
                (
                    "ac_from",
                    models.TextField(blank=True, db_column="Ac_From", null=True),
                ),
            ],
            options={
                "db_table": "tblPM_ProjectPlanning_Designer",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ProjectPlanningMainSheet",
            fields=[
                (
                    "id",
                    models.IntegerField(
                        db_column="Id", primary_key=True, serialize=False
                    ),
                ),
                (
                    "sys_date",
                    models.TextField(blank=True, db_column="SysDate", null=True),
                ),
                (
                    "sys_time",
                    models.TextField(blank=True, db_column="SysTime", null=True),
                ),
                (
                    "comp_id",
                    models.IntegerField(blank=True, db_column="CompId", null=True),
                ),
                (
                    "session_id",
                    models.TextField(blank=True, db_column="SessionId", null=True),
                ),
                (
                    "fin_year_id",
                    models.IntegerField(blank=True, db_column="FinYearId", null=True),
                ),
                (
                    "project_no",
                    models.TextField(blank=True, db_column="ProjectNo", null=True),
                ),
                (
                    "project_leader",
                    models.TextField(blank=True, db_column="ProjectLeader", null=True),
                ),
                (
                    "customer_name",
                    models.TextField(blank=True, db_column="CustomerName", null=True),
                ),
                (
                    "project_title",
                    models.TextField(blank=True, db_column="ProjectTitle", null=True),
                ),
                (
                    "activity_1",
                    models.TextField(blank=True, db_column="Activity1", null=True),
                ),
                (
                    "activity_2",
                    models.TextField(blank=True, db_column="Activity2", null=True),
                ),
                (
                    "activity_3",
                    models.TextField(blank=True, db_column="Activity3", null=True),
                ),
                (
                    "activity_4",
                    models.TextField(blank=True, db_column="Activity4", null=True),
                ),
                (
                    "activity_5",
                    models.TextField(blank=True, db_column="Activity5", null=True),
                ),
                (
                    "delay_reason",
                    models.TextField(blank=True, db_column="DelayReason", null=True),
                ),
            ],
            options={
                "db_table": "tblPM_ProjectPlanning_MainSheet",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ProjectPlanningMaster",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                (
                    "sys_date",
                    models.TextField(blank=True, db_column="SysDate", null=True),
                ),
                (
                    "sys_time",
                    models.TextField(blank=True, db_column="SysTime", null=True),
                ),
                (
                    "comp_id",
                    models.IntegerField(blank=True, db_column="CompId", null=True),
                ),
                (
                    "session_id",
                    models.TextField(blank=True, db_column="SessionId", null=True),
                ),
                (
                    "fin_year_id",
                    models.IntegerField(blank=True, db_column="FinYearId", null=True),
                ),
                ("wo_no", models.TextField(blank=True, db_column="WONo", null=True)),
                (
                    "file_name",
                    models.TextField(blank=True, db_column="FileName", null=True),
                ),
                (
                    "file_size",
                    models.TextField(blank=True, db_column="FileSize", null=True),
                ),
                (
                    "content_type",
                    models.TextField(blank=True, db_column="ContentType", null=True),
                ),
                (
                    "file_data",
                    models.BinaryField(blank=True, db_column="FileData", null=True),
                ),
            ],
            options={
                "db_table": "tblPM_ProjectPlanning_Master",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ProjectStatus",
            fields=[
                (
                    "id",
                    models.IntegerField(
                        db_column="Id", primary_key=True, serialize=False
                    ),
                ),
                (
                    "sys_date",
                    models.TextField(blank=True, db_column="SysDate", null=True),
                ),
                (
                    "sys_time",
                    models.TextField(blank=True, db_column="SysTime", null=True),
                ),
                (
                    "comp_id",
                    models.IntegerField(blank=True, db_column="CompId", null=True),
                ),
                (
                    "session_id",
                    models.TextField(blank=True, db_column="SessionId", null=True),
                ),
                (
                    "fin_year_id",
                    models.IntegerField(blank=True, db_column="FinYearId", null=True),
                ),
                ("wo_no", models.TextField(blank=True, db_column="WONo", null=True)),
                (
                    "activity",
                    models.TextField(blank=True, db_column="Activity", null=True),
                ),
            ],
            options={
                "db_table": "tblPM_ProjectStatus",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ProjectVendorPlanDetail",
            fields=[
                (
                    "id",
                    models.IntegerField(
                        db_column="Id", primary_key=True, serialize=False
                    ),
                ),
                ("m_id", models.IntegerField(blank=True, db_column="MId", null=True)),
                ("sr_no", models.TextField(blank=True, db_column="SrNo", null=True)),
                (
                    "fixture_no",
                    models.TextField(blank=True, db_column="FixtureNo", null=True),
                ),
                (
                    "no_parts_manufacturing",
                    models.TextField(
                        blank=True, db_column="NoPartsManufacturing", null=True
                    ),
                ),
                (
                    "planning",
                    models.TextField(blank=True, db_column="Planning", null=True),
                ),
                (
                    "flame_cut_loading",
                    models.TextField(
                        blank=True, db_column="FlameCutLoading", null=True
                    ),
                ),
                (
                    "premach_ineing",
                    models.TextField(blank=True, db_column="PremachIneing", null=True),
                ),
                (
                    "weldment_fabrication",
                    models.TextField(
                        blank=True, db_column="WeldmentFabrication", null=True
                    ),
                ),
                (
                    "weldment_loading",
                    models.TextField(
                        blank=True, db_column="WeldmentLoading", null=True
                    ),
                ),
                (
                    "no_parts_received",
                    models.TextField(
                        blank=True, db_column="NoPartsReceived", null=True
                    ),
                ),
                (
                    "no_accepted_parts",
                    models.TextField(
                        blank=True, db_column="NoAcceptedParts", null=True
                    ),
                ),
                (
                    "pending_mfg_parts",
                    models.TextField(
                        blank=True, db_column="PendingMfgParts", null=True
                    ),
                ),
                (
                    "brought_parts",
                    models.TextField(blank=True, db_column="BroughoutParts", null=True),
                ),
                (
                    "pending_bo_parts",
                    models.TextField(blank=True, db_column="PendingBOParts", null=True),
                ),
                (
                    "no_pending_challan",
                    models.TextField(
                        blank=True, db_column="NoPendingChallan", null=True
                    ),
                ),
                (
                    "no_parts_received_after_processing",
                    models.TextField(
                        blank=True,
                        db_column="NoPartsReceivedAfterProcessing",
                        null=True,
                    ),
                ),
            ],
            options={
                "db_table": "tblPM_Project_Vendor_Plan_Detail",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="DailyReportingTracker",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("sys_date", models.DateTimeField(auto_now_add=True)),
                ("sys_time", models.TimeField(auto_now_add=True)),
                ("comp_id", models.IntegerField(blank=True, null=True)),
                ("session_id", models.CharField(blank=True, max_length=255, null=True)),
                ("fin_year_id", models.IntegerField(blank=True, null=True)),
                ("employee_name", models.CharField(max_length=255)),
                ("designation", models.CharField(max_length=255)),
                ("department", models.CharField(max_length=255)),
                ("date_of_reporting", models.DateField()),
                ("significant_achievements_last_week", models.TextField()),
                ("activities_task_current_week", models.TextField()),
                ("activities_planned_completed", models.TextField()),
                ("activities_planned_not_completed", models.TextField()),
                ("activities_unplanned_completed", models.TextField()),
                ("plan_next_week", models.TextField()),
                ("activity_date", models.DateField()),
                ("wo_number", models.CharField(max_length=50)),
                ("activity", models.TextField()),
                ("estimated_time", models.CharField(max_length=50)),
                ("status", models.CharField(max_length=255)),
                ("percentage_completed", models.IntegerField(default=0)),
                ("remarks", models.TextField()),
            ],
            options={
                "db_table": "daily_reporting_tracker",
                "ordering": ["-sys_date"],
            },
        ),
        migrations.CreateModel(
            name="DesignPlan",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("sys_date", models.DateTimeField(auto_now_add=True)),
                ("wo_number", models.CharField(max_length=50)),
                ("fixture_number", models.CharField(max_length=50)),
                ("concept_design", models.CharField(max_length=255)),
                ("internal_review", models.CharField(max_length=255)),
                ("dap_send", models.CharField(max_length=255)),
                ("dap_received", models.CharField(max_length=255)),
                ("correction", models.CharField(max_length=255)),
                ("final_dap", models.CharField(max_length=255)),
                ("bought_list", models.CharField(max_length=255)),
                ("drawing_release_detailing", models.CharField(max_length=255)),
                ("tpl_entry", models.CharField(max_length=255)),
                ("flame_cut", models.CharField(max_length=255)),
                ("cnc_data", models.CharField(max_length=255)),
                ("cmm_data", models.CharField(max_length=255)),
                ("fit_list", models.CharField(max_length=255)),
                ("manual", models.CharField(max_length=255)),
            ],
            options={
                "db_table": "design_plan",
                "ordering": ["-sys_date"],
            },
        ),
        migrations.CreateModel(
            name="ManufacturingPlan",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("sys_date", models.DateTimeField(auto_now_add=True)),
                ("wo_number", models.CharField(max_length=50)),
                ("fixture_number", models.CharField(max_length=50)),
                ("item_number", models.CharField(max_length=100)),
                ("description", models.TextField()),
                ("quantity", models.CharField(max_length=50)),
                ("detailing", models.CharField(max_length=255)),
                ("tpl_entry", models.CharField(max_length=255)),
                ("flame_cut", models.CharField(max_length=255)),
                ("cutting_flame_cut", models.CharField(max_length=255)),
                ("channel", models.CharField(max_length=255)),
                ("raw_material_list", models.CharField(max_length=255)),
                ("raw_material_receive", models.CharField(max_length=255)),
                ("fabrication", models.CharField(max_length=255)),
                ("sr", models.CharField(max_length=255)),
                ("machining", models.CharField(max_length=255)),
                ("tapping", models.CharField(max_length=255)),
                ("painting", models.CharField(max_length=255)),
            ],
            options={
                "db_table": "manufacturing_plan",
                "ordering": ["-sys_date"],
            },
        ),
        migrations.CreateModel(
            name="VendorPlan",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("sys_date", models.DateTimeField(auto_now_add=True)),
                ("wo_number", models.CharField(max_length=50)),
                ("serial_number", models.CharField(max_length=50)),
                ("fixture_number", models.CharField(max_length=50)),
                ("number_parts_manufacturing", models.CharField(max_length=50)),
                ("planning", models.CharField(max_length=255)),
                ("flame_cut_loading", models.CharField(max_length=255)),
                ("premach_ineing", models.CharField(max_length=255)),
                ("weldment_fabrication", models.CharField(max_length=255)),
                ("weldment_loading", models.CharField(max_length=255)),
                ("number_parts_received", models.CharField(max_length=50)),
                ("number_accepted_parts", models.CharField(max_length=50)),
                ("pending_mfg_parts", models.CharField(max_length=50)),
                ("brought_parts", models.CharField(max_length=50)),
                ("pending_bo_parts", models.CharField(max_length=50)),
                ("number_pending_challan", models.CharField(max_length=50)),
                (
                    "number_parts_received_after_processing",
                    models.CharField(max_length=50),
                ),
            ],
            options={
                "db_table": "vendor_plan",
                "ordering": ["-sys_date"],
            },
        ),
    ]
