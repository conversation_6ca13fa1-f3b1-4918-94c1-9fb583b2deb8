{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }} - Sales Distribution{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800">{{ page_title }}</h1>
            <p class="text-gray-600 mt-2">Work order details and status</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Work Order No</label>
                    <div class="mt-1 text-lg text-gray-900">{{ work_order.wono|default:"-" }}</div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700">Customer ID</label>
                    <div class="mt-1 text-lg text-gray-900">{{ work_order.customerid }}</div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700">Project Title</label>
                    <div class="mt-1 text-lg text-gray-900">{{ work_order.taskprojecttitle|default:"-" }}</div>
                </div>
            </div>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Work Order Date</label>
                    <div class="mt-1 text-lg text-gray-900">{{ work_order.taskworkorderdate|default:"-" }}</div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700">Category</label>
                    <div class="mt-1 text-lg text-gray-900">{{ work_order.cid.categoryname|default:"-" }}</div>
                </div>
            </div>
        </div>

        <div class="mt-8 flex justify-end space-x-4">
            <a href="{% url 'sales_distribution:work_order_list' %}" 
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                Back to List
            </a>
            <a href="{% url 'sales_distribution:work_order_edit' work_order.pk %}" 
               class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                Edit Work Order
            </a>
        </div>
    </div>
</div>
{% endblock %}