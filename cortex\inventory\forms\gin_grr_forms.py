from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from ..models import (
    GoodsInwardNote, GINLineItem, GoodsReceivedReceipt, GRRLineItem
)


class GoodsInwardNoteForm(forms.ModelForm):
    """Form for creating and editing Goods Inward Notes"""
    
    class Meta:
        model = GoodsInwardNote
        fields = [
            'gin_date', 'po_number', 'challan_number', 
            'challan_date', 'gate_entry_number', 'vehicle_number', 
            'mode_of_transport'
        ]
        widgets = {
            'gin_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md'
            }),
            'po_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Purchase Order Number',
                'hx-get': '/inventory/api/po-details/',
                'hx-trigger': 'blur',
                'hx-target': '#po-details',
                'hx-swap': 'innerHTML'
            }),
            'challan_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Supplier Challan Number'
            }),
            'challan_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md'
            }),
            'gate_entry_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Gate Entry Number'
            }),
            'vehicle_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Vehicle Number'
            }),
            'mode_of_transport': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Mode of Transport'
            })
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.company = kwargs.pop('company', None)
        self.financial_year = kwargs.pop('financial_year', None)
        super().__init__(*args, **kwargs)
        
        # Set default dates
        if not self.instance.pk:
            self.fields['gin_date'].initial = timezone.now().date()

    def clean_gin_date(self):
        gin_date = self.cleaned_data.get('gin_date')
        if gin_date and gin_date > timezone.now().date():
            raise ValidationError("GIN date cannot be in the future.")
        return gin_date

    def clean_challan_date(self):
        challan_date = self.cleaned_data.get('challan_date')
        gin_date = self.cleaned_data.get('gin_date')
        
        if challan_date and gin_date and challan_date > gin_date:
            raise ValidationError("Challan date cannot be after GIN date.")
        
        return challan_date

    def clean_po_number(self):
        po_number = self.cleaned_data.get('po_number')
        if po_number:
            return po_number.strip()
        return po_number

    def save(self, commit=True):
        instance = super().save(commit=False)
        
        if not instance.pk:
            # Set system fields for new instances
            instance.company = self.company
            instance.financial_year = self.financial_year
            from django.utils import timezone
            instance.sysdate = timezone.now().strftime('%Y-%m-%d')
            instance.systime = timezone.now().strftime('%H:%M:%S')
            instance.sessionid = f"session_{timezone.now().timestamp()}"
            
        if commit:
            instance.save()
            # Generate GIN number if not set
            if not instance.gin_number:
                instance.generate_gin_number()
                instance.save()
        
        return instance


class GINLineItemForm(forms.ModelForm):
    """Form for GIN line items"""
    
    class Meta:
        model = GINLineItem
        fields = [
            'gin_number', 'gin_id', 'po_id', 'quantity', 'received_quantity', 
            'acategory_id', 'asubcategory_id'
        ]
        widgets = {
            'gin_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'GIN Number'
            }),
            'gin_id': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'GIN ID'
            }),
            'po_id': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'PO ID'
            }),
            'quantity': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'received_quantity': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'acategory_id': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Category ID',
                'min': '0'
            }),
            'asubcategory_id': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Subcategory ID',
                'min': '0'
            })
        }

    def clean_received_quantity(self):
        received_quantity = self.cleaned_data.get('received_quantity')
        if received_quantity is not None and received_quantity <= 0:
            raise ValidationError("Received quantity must be greater than zero.")
        return received_quantity

    def clean_quantity(self):
        quantity = self.cleaned_data.get('quantity')
        if quantity is not None and quantity <= 0:
            raise ValidationError("Quantity must be greater than zero.")
        return quantity


class GoodsReceivedReceiptForm(forms.ModelForm):
    """Form for creating and editing Goods Received Receipts"""
    
    class Meta:
        model = GoodsReceivedReceipt
        fields = [
            'grr_date', 'gin', 'tax_invoice_number', 'tax_invoice_date',
            'vat_applicable', 'vat_amount', 'remarks'
        ]
        widgets = {
            'grr_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md'
            }),
            'gin': forms.Select(attrs={
                'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'hx-get': '/inventory/api/gin-details/',
                'hx-trigger': 'change',
                'hx-target': '#gin-details',
                'hx-swap': 'innerHTML'
            }),
            'tax_invoice_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Tax Invoice Number'
            }),
            'tax_invoice_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md'
            }),
            'vat_applicable': forms.CheckboxInput(attrs={
                'class': 'rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500'
            }),
            'vat_amount': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'remarks': forms.Textarea(attrs={
                'rows': 3,
                'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md',
                'placeholder': 'Enter any additional remarks or instructions'
            })
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.company = kwargs.pop('company', None)
        self.financial_year = kwargs.pop('financial_year', None)
        super().__init__(*args, **kwargs)
        
        # Set default dates
        if not self.instance.pk:
            self.fields['grr_date'].initial = timezone.now().date()
        
        # Filter GIN to only show available ones (status field not available)
        if self.company:
            self.fields['gin'].queryset = GoodsInwardNote.objects.filter(
                company=self.company
                # status='RECEIVED'  # Status field not available
            ).order_by('-gin_date')

    def clean_grr_date(self):
        grr_date = self.cleaned_data.get('grr_date')
        if grr_date and grr_date > timezone.now().date():
            raise ValidationError("GRR date cannot be in the future.")
        return grr_date

    def clean_tax_invoice_date(self):
        tax_invoice_date = self.cleaned_data.get('tax_invoice_date')
        grr_date = self.cleaned_data.get('grr_date')
        
        if tax_invoice_date and grr_date and tax_invoice_date > grr_date:
            raise ValidationError("Tax invoice date cannot be after GRR date.")
        
        return tax_invoice_date

    def clean(self):
        cleaned_data = super().clean()
        vat_applicable = cleaned_data.get('vat_applicable')
        vat_amount = cleaned_data.get('vat_amount')
        
        if vat_applicable and not vat_amount:
            raise ValidationError("VAT amount is required when VAT is applicable.")
        
        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        
        if not instance.pk:
            # Set system fields for new instances
            instance.company = self.company
            instance.financial_year = self.financial_year
            instance.created_by = self.user
            
        if commit:
            instance.save()
            # Generate GRR number if not set
            if not instance.grr_number:
                instance.generate_grr_number()
                instance.save()
        
        return instance


class GRRLineItemForm(forms.ModelForm):
    """Form for GRR line items"""
    
    class Meta:
        model = GRRLineItem
        fields = [
            'gin_line_item', 'received_quantity', 'accepted_quantity', 'rejected_quantity', 
            'rate', 'accepted_value', 'quality_remarks', 'status'
        ]
        widgets = {
            'gin_line_item': forms.Select(attrs={
                'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'received_quantity': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'accepted_quantity': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'rejected_quantity': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'rate': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'accepted_value': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'readonly': True
            }),
            'quality_remarks': forms.Textarea(attrs={
                'rows': 3,
                'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md',
                'placeholder': 'Quality remarks and observations'
            }),
            'status': forms.Select(attrs={
                'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            })
        }

    def clean(self):
        cleaned_data = super().clean()
        received_quantity = cleaned_data.get('received_quantity')
        accepted_quantity = cleaned_data.get('accepted_quantity')
        rejected_quantity = cleaned_data.get('rejected_quantity')
        
        if received_quantity and accepted_quantity and rejected_quantity:
            total_quantity = (accepted_quantity or 0) + (rejected_quantity or 0)
            if total_quantity > received_quantity:
                raise ValidationError(
                    f"Total of accepted and rejected quantities cannot exceed received quantity ({received_quantity})."
                )
        
        return cleaned_data

    def clean_accepted_quantity(self):
        accepted_quantity = self.cleaned_data.get('accepted_quantity')
        if accepted_quantity is not None and accepted_quantity < 0:
            raise ValidationError("Accepted quantity cannot be negative.")
        return accepted_quantity

    def clean_rejected_quantity(self):
        rejected_quantity = self.cleaned_data.get('rejected_quantity')
        if rejected_quantity is not None and rejected_quantity < 0:
            raise ValidationError("Rejected quantity cannot be negative.")
        return rejected_quantity


class GINSearchForm(forms.Form):
    """Search form for Goods Inward Notes"""
    
    search = forms.CharField(
        required=False,
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md',
            'placeholder': 'Search by GIN number, PO number, or supplier name...',
            'hx-get': '/inventory/gin/',
            'hx-trigger': 'keyup changed delay:300ms',
            'hx-target': '#gin-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    status = forms.ChoiceField(
        required=False,
        choices=[('', 'All Status'), ('DRAFT', 'Draft'), ('RECEIVED', 'Received'), ('CANCELLED', 'Cancelled')],
        widget=forms.Select(attrs={
            'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/inventory/gin/',
            'hx-trigger': 'change',
            'hx-target': '#gin-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
            'hx-get': '/inventory/gin/',
            'hx-trigger': 'change',
            'hx-target': '#gin-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
            'hx-get': '/inventory/gin/',
            'hx-trigger': 'change',
            'hx-target': '#gin-results',
            'hx-swap': 'innerHTML'
        })
    )


class GINEditSearchForm(forms.Form):
    """Search form for GIN Edit interface - matching ASP.NET interface"""
    
    SEARCH_TYPE_CHOICES = [
        ('0', 'Supplier Name'),
        ('1', 'PO No'),
        ('2', 'GIN No'),
    ]
    
    search_type = forms.ChoiceField(
        choices=SEARCH_TYPE_CHOICES,
        required=False,
        initial='0',
        widget=forms.Select(attrs={
            'class': 'border border-gray-300 rounded px-3 py-2 text-sm w-48'
        })
    )
    
    search_query = forms.CharField(
        required=False,
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'border border-gray-300 rounded px-3 py-2 text-sm w-40',
            'placeholder': 'Enter search value'
        })
    )
    
    supplier_name = forms.CharField(
        required=False,
        max_length=200,
        widget=forms.TextInput(attrs={
            'class': 'border border-gray-300 rounded px-3 py-2 text-sm flex-1',
            'placeholder': 'Supplier name...',
            'id': 'supplier-autocomplete',
            'autocomplete': 'off',
            'data-api-url': '/inventory/api/suppliers/'
        })
    )


class GRRSearchForm(forms.Form):
    """Search form for Goods Received Receipts"""
    
    search = forms.CharField(
        required=False,
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md',
            'placeholder': 'Search by GRR number, GIN number, or tax invoice...',
            'hx-get': '/inventory/grr/',
            'hx-trigger': 'keyup changed delay:300ms',
            'hx-target': '#grr-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    status = forms.ChoiceField(
        required=False,
        choices=[('', 'All Status')] + GoodsReceivedReceipt._meta.get_field('status').choices,
        widget=forms.Select(attrs={
            'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/inventory/grr/',
            'hx-trigger': 'change',
            'hx-target': '#grr-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    quality_pending = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
            'hx-get': '/inventory/grr/',
            'hx-trigger': 'change',
            'hx-target': '#grr-results',
            'hx-swap': 'innerHTML'
        })
    )


class GINNewSearchForm(forms.Form):
    """Search form for GIN New - finding eligible POs"""
    
    SEARCH_TYPE_CHOICES = [
        ('0', 'Supplier Name'),
        ('1', 'PO Number'),
    ]
    
    search_type = forms.ChoiceField(
        choices=SEARCH_TYPE_CHOICES,
        required=True,
        initial='0',
        widget=forms.Select(attrs={
            'class': 'border border-gray-300 rounded px-3 py-2 text-sm w-40 focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )
    
    search_query = forms.CharField(
        required=False,
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'border border-gray-300 rounded px-3 py-2 text-sm w-48 focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Enter PO number'
        })
    )
    
    supplier_search = forms.CharField(
        required=False,
        max_length=200,
        widget=forms.TextInput(attrs={
            'class': 'w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Type supplier name to search...',
            'autocomplete': 'off'
        })
    )

    def clean(self):
        cleaned_data = super().clean()
        search_type = cleaned_data.get('search_type')
        search_query = cleaned_data.get('search_query')
        supplier_search = cleaned_data.get('supplier_search')
        
        if search_type == '1' and not search_query:
            raise ValidationError("PO number is required when searching by PO number.")
        
        if search_type == '0' and not supplier_search:
            raise ValidationError("Supplier name is required when searching by supplier.")
        
        return cleaned_data


class GINNewDetailsForm(forms.Form):
    """Form for GIN creation details - header information"""
    
    TRANSPORT_MODES = [
        ('', 'Select Mode of Transport'),
        ('Road', 'Road'),
        ('Rail', 'Rail'),
        ('Air', 'Air'),
        ('Sea', 'Sea'),
        ('Courier', 'Courier'),
        ('By Hand', 'By Hand'),
    ]
    
    challan_no = forms.CharField(
        max_length=50,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Enter challan number'
        })
    )
    
    challan_date = forms.DateField(
        required=True,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )
    
    gate_entry_no = forms.CharField(
        max_length=50,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Enter gate entry number'
        })
    )
    
    gate_entry_date = forms.DateField(
        required=True,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )
    
    gate_entry_time = forms.TimeField(
        required=True,
        widget=forms.TimeInput(attrs={
            'type': 'time',
            'class': 'w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )
    
    mode_of_transport = forms.ChoiceField(
        choices=TRANSPORT_MODES,
        required=True,
        widget=forms.Select(attrs={
            'class': 'w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )
    
    vehicle_no = forms.CharField(
        max_length=50,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Enter vehicle number'
        })
    )

    def clean_challan_date(self):
        challan_date = self.cleaned_data.get('challan_date')
        if challan_date and challan_date > timezone.now().date():
            raise ValidationError("Challan date cannot be in the future.")
        return challan_date

    def clean_gate_entry_date(self):
        gate_entry_date = self.cleaned_data.get('gate_entry_date')
        if gate_entry_date and gate_entry_date > timezone.now().date():
            raise ValidationError("Gate entry date cannot be in the future.")
        return gate_entry_date

    def clean(self):
        cleaned_data = super().clean()
        challan_date = cleaned_data.get('challan_date')
        gate_entry_date = cleaned_data.get('gate_entry_date')
        
        if challan_date and gate_entry_date and challan_date > gate_entry_date:
            raise ValidationError("Challan date cannot be after gate entry date.")
        
        return cleaned_data