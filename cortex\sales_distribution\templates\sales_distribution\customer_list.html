{% extends 'core/base.html' %}
{% load static %}

{% block title %}Customer Master - {{ global_company.company_name }}{% endblock %}

{% block content %}
<div class="h-full overflow-y-auto">
    <div class="p-6 space-y-6">
        <!-- Page Header with SAP Fiori Design -->
        <div class="bg-white rounded-xl shadow-sm border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-sap-blue-500 rounded-lg flex items-center justify-center">
                        <i data-lucide="users" class="w-6 h-6 text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-sap-gray-900">Customer Master</h1>
                        <p class="text-sm text-sap-gray-600">Manage customer information and contacts</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <!-- Total Count Badge -->
                    <div class="bg-sap-blue-50 text-sap-blue-700 px-3 py-1 rounded-full text-sm font-medium">
                        <i data-lucide="database" class="w-4 h-4 inline mr-1"></i>
                        {{ customers|length }} Customers
                    </div>
                    <!-- Add New Customer Button -->
                    <a href="{% url 'sales_distribution:customer_new' %}" 
                       class="sap-button-primary">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Add Customer
                    </a>
                </div>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="bg-white rounded-xl shadow-sm border border-sap-gray-200 p-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Real-time Search -->
                <div class="md:col-span-2">
                    <label for="search" class="block text-sm font-medium text-sap-gray-700 mb-2">
                        <i data-lucide="search" class="w-4 h-4 inline mr-1"></i>
                        Search Customers
                    </label>
                    <input type="text" 
                           id="search" 
                           name="search" 
                           value="{{ request.GET.search }}"
                           placeholder="Search by name, ID, or address..."
                           class="block w-full px-4 py-3 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"
                           hx-get="{% url 'sales_distribution:customer_list' %}"
                           hx-target="#customer-table-container"
                           hx-trigger="keyup changed delay:300ms"
                           hx-include="[name='search'], [name='filter']">
                </div>

                <!-- Quick Filters -->
                <div>
                    <label for="filter" class="block text-sm font-medium text-sap-gray-700 mb-2">
                        <i data-lucide="filter" class="w-4 h-4 inline mr-1"></i>
                        Filter
                    </label>
                    <select id="filter" 
                            name="filter" 
                            class="block w-full px-4 py-3 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"
                            hx-get="{% url 'sales_distribution:customer_list' %}"
                            hx-target="#customer-table-container"
                            hx-trigger="change"
                            hx-include="[name='search'], [name='filter']">
                        <option value="">All Customers</option>
                        <option value="recent" {% if request.GET.filter == 'recent' %}selected{% endif %}>Recent (30 days)</option>
                        <option value="active" {% if request.GET.filter == 'active' %}selected{% endif %}>Active</option>
                    </select>
                </div>

                <!-- Export Options -->
                <div class="flex items-end">
                    <div class="flex space-x-2 w-full">
                        <button class="flex-1 px-3 py-3 border border-sap-gray-300 rounded-lg text-sm text-sap-gray-700 hover:bg-sap-gray-50 transition-colors">
                            <i data-lucide="download" class="w-4 h-4 inline mr-1"></i>
                            Export
                        </button>
                        <button class="flex-1 px-3 py-3 border border-sap-gray-300 rounded-lg text-sm text-sap-gray-700 hover:bg-sap-gray-50 transition-colors">
                            <i data-lucide="printer" class="w-4 h-4 inline mr-1"></i>
                            Print
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Table Section -->
        <div id="customer-table-container" class="bg-white rounded-xl shadow-sm border border-sap-gray-200">
            {% include 'sales_distribution/partials/customer_table.html' %}
        </div>

        <!-- Pagination Section -->
        {% if is_paginated %}
        <div class="bg-white rounded-xl shadow-sm border border-sap-gray-200 p-4">
            <div class="flex items-center justify-between">
                <div class="text-sm text-sap-gray-600">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ paginator.count }} customers
                </div>
                <div class="flex items-center space-x-2">
                    {% if page_obj.has_previous %}
                        <a href="?page=1" class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm text-sap-gray-700 hover:bg-sap-gray-50">
                            <i data-lucide="chevrons-left" class="w-4 h-4"></i>
                        </a>
                        <a href="?page={{ page_obj.previous_page_number }}" class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm text-sap-gray-700 hover:bg-sap-gray-50">
                            <i data-lucide="chevron-left" class="w-4 h-4"></i>
                        </a>
                    {% endif %}
                    
                    <span class="px-3 py-2 bg-sap-blue-500 text-white rounded-lg text-sm font-medium">
                        {{ page_obj.number }}
                    </span>
                    
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}" class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm text-sap-gray-700 hover:bg-sap-gray-50">
                            <i data-lucide="chevron-right" class="w-4 h-4"></i>
                        </a>
                        <a href="?page={{ paginator.num_pages }}" class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm text-sap-gray-700 hover:bg-sap-gray-50">
                            <i data-lucide="chevrons-right" class="w-4 h-4"></i>
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize page functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize icons after page load
        setTimeout(() => {
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        }, 100);
        
        // Clear search functionality
        const searchInput = document.getElementById('search');
        if (searchInput) {
            // Add clear button functionality
            searchInput.addEventListener('input', function() {
                if (this.value === '') {
                    // Trigger search on empty to show all results
                    htmx.trigger(this, 'keyup');
                }
            });
        }
        
        console.log('✅ Customer list page initialized successfully');
    });
    
    // Re-initialize icons after HTMX updates
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    });
    
    // Handle export functionality
    function exportCustomers(format) {
        const searchTerm = document.getElementById('search').value;
        const filter = document.getElementById('filter').value;
        
        let url = '{% url "sales_distribution:customer_list" %}export/' + format + '/';
        const params = new URLSearchParams();
        
        if (searchTerm) params.append('search', searchTerm);
        if (filter) params.append('filter', filter);
        
        if (params.toString()) {
            url += '?' + params.toString();
        }
        
        window.open(url, '_blank');
    }
</script>
{% endblock %}
