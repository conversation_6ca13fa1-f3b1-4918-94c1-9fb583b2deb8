{% load static %}

<!-- Data Table -->
<div class="sap-table-container--responsive">
    <table class="sap-table">
        <thead>
            <tr>
                <th class="sap-table-header">ID</th>
                <th class="sap-table-header">From Date</th>
                <th class="sap-table-header">To Date</th>
                <th class="sap-table-header">Period Days</th>
                <th class="sap-table-header">Closing Stock Value</th>
                <th class="sap-table-header">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for stock in closing_stocks %}
            <tr class="sap-table-row">
                <td class="sap-table-cell">
                    <a href="{% url 'inventory:closing_stock_detail' stock.pk %}" class="text-blue-600 hover:text-blue-800 font-medium">
                        #{{ stock.id }}
                    </a>
                </td>
                <td class="sap-table-cell">
                    {% if stock.fromdt %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {{ stock.fromdt }}
                        </span>
                    {% else %}
                        <span class="text-gray-400">N/A</span>
                    {% endif %}
                </td>
                <td class="sap-table-cell">
                    {% if stock.todt %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {{ stock.todt }}
                        </span>
                    {% else %}
                        <span class="text-gray-400">N/A</span>
                    {% endif %}
                </td>
                <td class="sap-table-cell">
                    {% if stock.fromdt and stock.todt %}
                        {% load custom_filters %}
                        <span class="text-sm text-gray-600">
                            {{ stock.fromdt|calculate_period_days:stock.todt }} days
                        </span>
                    {% else %}
                        <span class="text-gray-400">N/A</span>
                    {% endif %}
                </td>
                <td class="sap-table-cell">
                    {% if stock.clstock %}
                        <span class="font-medium text-green-600">
                            ₹{{ stock.clstock|floatformat:2 }}
                        </span>
                    {% else %}
                        <span class="text-gray-400">₹0.00</span>
                    {% endif %}
                </td>
                <td class="sap-table-cell">
                    <div class="flex space-x-2">
                        <a href="{% url 'inventory:closing_stock_detail' stock.pk %}" 
                           class="sap-button sap-button--transparent sap-button--compact"
                           title="View Details">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                        </a>
                        <a href="{% url 'inventory:closing_stock_update' stock.pk %}" 
                           class="sap-button sap-button--transparent sap-button--compact"
                           title="Edit Record">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </a>
                        <a href="{% url 'inventory:closing_stock_delete' stock.pk %}" 
                           class="sap-button sap-button--transparent sap-button--compact text-red-600 hover:text-red-800"
                           title="Delete Record"
                           onclick="return confirm('Are you sure you want to delete this closing stock record?')">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </a>
                    </div>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="sap-table-cell text-center py-8">
                    <div class="flex flex-col items-center justify-center text-gray-500">
                        <svg class="w-12 h-12 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                        </svg>
                        <p class="text-lg font-medium">No closing stock records found</p>
                        <p class="text-sm">Get started by creating your first closing stock record.</p>
                        <a href="{% url 'inventory:closing_stock_create' %}" class="mt-4 sap-button sap-button--emphasized">
                            Add New Record
                        </a>
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- Pagination -->
{% if is_paginated %}
<div class="sap-pagination">
    <div class="sap-pagination-info">
        <span class="text-sm text-gray-700">
            Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} results
        </span>
    </div>
    <div class="sap-pagination-controls">
        {% if page_obj.has_previous %}
            <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page=1" 
               class="sap-pagination-button">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                </svg>
            </a>
            <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}" 
               class="sap-pagination-button">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
            </a>
        {% endif %}

        <span class="sap-pagination-current">
            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
        </span>

        {% if page_obj.has_next %}
            <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}" 
               class="sap-pagination-button">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
            </a>
            <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.paginator.num_pages }}" 
               class="sap-pagination-button">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    <path fill-rule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
            </a>
        {% endif %}
    </div>
</div>
{% endif %}