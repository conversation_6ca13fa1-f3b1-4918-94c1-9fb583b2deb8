﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="ProformaInvoice" targetNamespace="http://tempuri.org/ProformaInvoice.xsd" xmlns:mstns="http://tempuri.org/ProformaInvoice.xsd" xmlns="http://tempuri.org/ProformaInvoice.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="ProformaInvoice" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="ProformaInvoice" msprop:Generator_DataSetName="ProformaInvoice">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TablePropName="DataTable1" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" msprop:Generator_UserColumnName="Id" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInTable="IdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="SysDate" msprop:Generator_UserColumnName="SysDate" msprop:Generator_ColumnPropNameInRow="SysDate" msprop:Generator_ColumnVarNameInTable="columnSysDate" msprop:Generator_ColumnPropNameInTable="SysDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="InvoiceNo" msprop:Generator_UserColumnName="InvoiceNo" msprop:Generator_ColumnPropNameInRow="InvoiceNo" msprop:Generator_ColumnVarNameInTable="columnInvoiceNo" msprop:Generator_ColumnPropNameInTable="InvoiceNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PONo" msprop:Generator_UserColumnName="PONo" msprop:Generator_ColumnPropNameInRow="PONo" msprop:Generator_ColumnVarNameInTable="columnPONo" msprop:Generator_ColumnPropNameInTable="PONoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="WONo" msprop:Generator_UserColumnName="WONo" msprop:Generator_ColumnPropNameInRow="WONo" msprop:Generator_ColumnVarNameInTable="columnWONo" msprop:Generator_ColumnPropNameInTable="WONoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="InvoiceMode" msprop:Generator_UserColumnName="InvoiceMode" msprop:Generator_ColumnPropNameInRow="InvoiceMode" msprop:Generator_ColumnVarNameInTable="columnInvoiceMode" msprop:Generator_ColumnPropNameInTable="InvoiceModeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="DateOfIssueInvoice" msprop:Generator_UserColumnName="DateOfIssueInvoice" msprop:Generator_ColumnPropNameInRow="DateOfIssueInvoice" msprop:Generator_ColumnVarNameInTable="columnDateOfIssueInvoice" msprop:Generator_ColumnPropNameInTable="DateOfIssueInvoiceColumn" type="xs:string" minOccurs="0" />
              <xs:element name="TimeOfIssueInvoice" msprop:Generator_UserColumnName="TimeOfIssueInvoice" msprop:Generator_ColumnPropNameInRow="TimeOfIssueInvoice" msprop:Generator_ColumnVarNameInTable="columnTimeOfIssueInvoice" msprop:Generator_ColumnPropNameInTable="TimeOfIssueInvoiceColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CustomerCode" msprop:Generator_UserColumnName="CustomerCode" msprop:Generator_ColumnPropNameInRow="CustomerCode" msprop:Generator_ColumnVarNameInTable="columnCustomerCode" msprop:Generator_ColumnPropNameInTable="CustomerCodeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Buyer_name" msprop:Generator_UserColumnName="Buyer_name" msprop:Generator_ColumnPropNameInRow="Buyer_name" msprop:Generator_ColumnVarNameInTable="columnBuyer_name" msprop:Generator_ColumnPropNameInTable="Buyer_nameColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Buyer_cotper" msprop:Generator_UserColumnName="Buyer_cotper" msprop:Generator_ColumnPropNameInRow="Buyer_cotper" msprop:Generator_ColumnVarNameInTable="columnBuyer_cotper" msprop:Generator_ColumnPropNameInTable="Buyer_cotperColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Buyer_ph" msprop:Generator_UserColumnName="Buyer_ph" msprop:Generator_ColumnPropNameInRow="Buyer_ph" msprop:Generator_ColumnVarNameInTable="columnBuyer_ph" msprop:Generator_ColumnPropNameInTable="Buyer_phColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Buyer_email" msprop:Generator_UserColumnName="Buyer_email" msprop:Generator_ColumnPropNameInRow="Buyer_email" msprop:Generator_ColumnVarNameInTable="columnBuyer_email" msprop:Generator_ColumnPropNameInTable="Buyer_emailColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Buyer_ecc" msprop:Generator_UserColumnName="Buyer_ecc" msprop:Generator_ColumnPropNameInRow="Buyer_ecc" msprop:Generator_ColumnVarNameInTable="columnBuyer_ecc" msprop:Generator_ColumnPropNameInTable="Buyer_eccColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Buyer_tin" msprop:Generator_UserColumnName="Buyer_tin" msprop:Generator_ColumnPropNameInRow="Buyer_tin" msprop:Generator_ColumnVarNameInTable="columnBuyer_tin" msprop:Generator_ColumnPropNameInTable="Buyer_tinColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Buyer_mob" msprop:Generator_UserColumnName="Buyer_mob" msprop:Generator_ColumnPropNameInRow="Buyer_mob" msprop:Generator_ColumnVarNameInTable="columnBuyer_mob" msprop:Generator_ColumnPropNameInTable="Buyer_mobColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Buyer_fax" msprop:Generator_UserColumnName="Buyer_fax" msprop:Generator_ColumnPropNameInRow="Buyer_fax" msprop:Generator_ColumnVarNameInTable="columnBuyer_fax" msprop:Generator_ColumnPropNameInTable="Buyer_faxColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Buyer_vat" msprop:Generator_UserColumnName="Buyer_vat" msprop:Generator_ColumnPropNameInRow="Buyer_vat" msprop:Generator_ColumnVarNameInTable="columnBuyer_vat" msprop:Generator_ColumnPropNameInTable="Buyer_vatColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Cong_name" msprop:Generator_UserColumnName="Cong_name" msprop:Generator_ColumnPropNameInRow="Cong_name" msprop:Generator_ColumnVarNameInTable="columnCong_name" msprop:Generator_ColumnPropNameInTable="Cong_nameColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Cong_cotper" msprop:Generator_UserColumnName="Cong_cotper" msprop:Generator_ColumnPropNameInRow="Cong_cotper" msprop:Generator_ColumnVarNameInTable="columnCong_cotper" msprop:Generator_ColumnPropNameInTable="Cong_cotperColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Cong_ph" msprop:Generator_UserColumnName="Cong_ph" msprop:Generator_ColumnPropNameInRow="Cong_ph" msprop:Generator_ColumnVarNameInTable="columnCong_ph" msprop:Generator_ColumnPropNameInTable="Cong_phColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Cong_email" msprop:Generator_UserColumnName="Cong_email" msprop:Generator_ColumnPropNameInRow="Cong_email" msprop:Generator_ColumnVarNameInTable="columnCong_email" msprop:Generator_ColumnPropNameInTable="Cong_emailColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Cong_ecc" msprop:Generator_UserColumnName="Cong_ecc" msprop:Generator_ColumnPropNameInRow="Cong_ecc" msprop:Generator_ColumnVarNameInTable="columnCong_ecc" msprop:Generator_ColumnPropNameInTable="Cong_eccColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Cong_tin" msprop:Generator_UserColumnName="Cong_tin" msprop:Generator_ColumnPropNameInRow="Cong_tin" msprop:Generator_ColumnVarNameInTable="columnCong_tin" msprop:Generator_ColumnPropNameInTable="Cong_tinColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Cong_mob" msprop:Generator_UserColumnName="Cong_mob" msprop:Generator_ColumnPropNameInRow="Cong_mob" msprop:Generator_ColumnVarNameInTable="columnCong_mob" msprop:Generator_ColumnPropNameInTable="Cong_mobColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Cong_fax" msprop:Generator_UserColumnName="Cong_fax" msprop:Generator_ColumnPropNameInRow="Cong_fax" msprop:Generator_ColumnVarNameInTable="columnCong_fax" msprop:Generator_ColumnPropNameInTable="Cong_faxColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Cong_vat" msprop:Generator_UserColumnName="Cong_vat" msprop:Generator_ColumnPropNameInRow="Cong_vat" msprop:Generator_ColumnVarNameInTable="columnCong_vat" msprop:Generator_ColumnPropNameInTable="Cong_vatColumn" type="xs:string" minOccurs="0" />
              <xs:element name="AddType" msprop:Generator_UserColumnName="AddType" msprop:Generator_ColumnPropNameInRow="AddType" msprop:Generator_ColumnVarNameInTable="columnAddType" msprop:Generator_ColumnPropNameInTable="AddTypeColumn" type="xs:int" minOccurs="0" />
              <xs:element name="AddAmt" msprop:Generator_UserColumnName="AddAmt" msprop:Generator_ColumnPropNameInRow="AddAmt" msprop:Generator_ColumnVarNameInTable="columnAddAmt" msprop:Generator_ColumnPropNameInTable="AddAmtColumn" type="xs:double" minOccurs="0" />
              <xs:element name="DeductionType" msprop:Generator_UserColumnName="DeductionType" msprop:Generator_ColumnPropNameInRow="DeductionType" msprop:Generator_ColumnVarNameInTable="columnDeductionType" msprop:Generator_ColumnPropNameInTable="DeductionTypeColumn" type="xs:int" minOccurs="0" />
              <xs:element name="Deduction" msprop:Generator_UserColumnName="Deduction" msprop:Generator_ColumnPropNameInRow="Deduction" msprop:Generator_ColumnVarNameInTable="columnDeduction" msprop:Generator_ColumnPropNameInTable="DeductionColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PODate" msprop:Generator_UserColumnName="PODate" msprop:Generator_ColumnPropNameInRow="PODate" msprop:Generator_ColumnVarNameInTable="columnPODate" msprop:Generator_ColumnPropNameInTable="PODateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="POId" msprop:Generator_UserColumnName="POId" msprop:Generator_ColumnPropNameInRow="POId" msprop:Generator_ColumnVarNameInTable="columnPOId" msprop:Generator_ColumnPropNameInTable="POIdColumn" type="xs:int" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>