<!-- accounts/templates/accounts/masters/iou_reasons_form.html -->
<!-- IOU Reasons Form Template -->
<!-- Simplified version matching actual database structure -->

{% extends 'core/base.html' %}

{% block title %}{{ page_title }} - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-red-600 to-sap-red-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="clipboard-plus" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">{{ page_title }}</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage IOU reason information</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:iou_reasons_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800">IOU Reason Details</h3>
            </div>
            
            <form method="post" class="p-6">
                {% csrf_token %}
                
                <!-- Form Errors -->
                {% if form.non_field_errors %}
                <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i data-lucide="alert-circle" class="w-5 h-5 text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">
                                Please correct the following errors:
                            </h3>
                            <div class="mt-2 text-sm text-red-700">
                                <ul class="list-disc pl-5 space-y-1">
                                    {% for error in form.non_field_errors %}
                                    <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <!-- Terms Field -->
                <div class="mb-6">
                    <label for="{{ form.terms.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                        IOU Terms <span class="text-red-500">*</span>
                    </label>
                    {{ form.terms }}
                    {% if form.terms.errors %}
                    <div class="mt-2 text-sm text-red-600">
                        {% for error in form.terms.errors %}
                        <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                    {% endif %}
                    <p class="mt-2 text-sm text-sap-gray-500">
                        Enter the IOU reason or terms (e.g., "Against Salary", "Self", "For Company Use")
                    </p>
                </div>
                
                <!-- Form Actions -->
                <div class="flex items-center justify-end space-x-3 pt-6 border-t border-sap-gray-100">
                    <a href="{% url 'accounts:iou_reasons_list' %}" 
                       class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-sap-red-600 hover:bg-sap-red-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="save" class="w-4 h-4 inline mr-2"></i>
                        Save IOU Reason
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}