{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{{ title|default:"Schedule Form" }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-2xl font-semibold text-gray-900">{{ title|default:"Schedule Form" }}</h1>
        <p class="mt-2 text-sm text-gray-700">Create or edit job schedules for machine operations.</p>
    </div>

    <!-- Form -->
    <form method="post" class="space-y-8">
        {% csrf_token %}
        
        <!-- Basic Information -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Basic Information</h3>
                
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label for="{{ form.jobno.id_for_label }}" class="block text-sm font-medium text-gray-700">Job Number</label>
                        {{ form.jobno|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.jobno.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.jobno.errors.0 }}</p>
                        {% endif %}
                        <p class="mt-2 text-sm text-gray-500">Unique job identifier</p>
                    </div>
                    
                    <div>
                        <label for="{{ form.wono.id_for_label }}" class="block text-sm font-medium text-gray-700">Work Order Number</label>
                        {{ form.wono|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.wono.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.wono.errors.0 }}</p>
                        {% endif %}
                        <p class="mt-2 text-sm text-gray-500">Associated work order</p>
                    </div>
                    
                    <div class="sm:col-span-2">
                        <label for="{{ form.itemid.id_for_label }}" class="block text-sm font-medium text-gray-700">Item</label>
                        {{ form.itemid|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.itemid.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.itemid.errors.0 }}</p>
                        {% endif %}
                        <p class="mt-2 text-sm text-gray-500">Item to be processed in this job</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Work Order Details -->
        <div id="work-order-details" class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Work Order Details</h3>
                
                <div class="bg-gray-50 rounded-lg p-4">
                    <p class="text-sm text-gray-600">
                        Work order details will be loaded automatically when you enter a work order number.
                    </p>
                </div>
            </div>
        </div>

        <!-- Schedule Details -->
        {% if schedule_details %}
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Schedule Details</h3>
                
                <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                    <table class="min-w-full divide-y divide-gray-300">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Machine</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Shift</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">In-charge</th>
                                <th class="relative px-6 py-3"><span class="sr-only">Actions</span></th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for detail in schedule_details %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    Machine {{ detail.machineid }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ detail.shift|default:"Not set" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <div>{{ detail.fromdate|default:"Not set" }}</div>
                                    {% if detail.todate and detail.todate != detail.fromdate %}
                                        <div class="text-xs text-gray-400">to {{ detail.todate }}</div>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <div>{{ detail.fromtime|default:"Not set" }}</div>
                                    {% if detail.totime %}
                                        <div class="text-xs text-gray-400">to {{ detail.totime }}</div>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ detail.qty|default:"Not set" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <div>{{ detail.incharge|default:"Not assigned" }}</div>
                                    {% if detail.operator %}
                                        <div class="text-xs text-gray-400">Op: {{ detail.operator }}</div>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button type="button" class="text-indigo-600 hover:text-indigo-900">Edit</button>
                                        <button type="button" class="text-red-600 hover:text-red-900">Remove</button>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="7" class="px-6 py-4 text-center text-sm text-gray-500">
                                    No schedule details added yet.
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-4">
                    <button type="button" 
                            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Add Schedule Detail
                    </button>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Additional Information -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Additional Information</h3>
                
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Created Date</label>
                        <div class="mt-1 text-sm text-gray-900">
                            {% if object %}
                                {{ object.sysdate|default:"Not available" }}
                            {% else %}
                                Will be set automatically
                            {% endif %}
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Created By</label>
                        <div class="mt-1 text-sm text-gray-900">
                            {% if object %}
                                {{ object.user.username|default:"System" }}
                            {% else %}
                                {{ request.user.username }}
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3">
            <a href="{% url 'machinery:schedule_list' %}" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Cancel
            </a>
            <button type="submit" 
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Save Schedule
            </button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const woInput = document.getElementById('{{ form.wono.id_for_label }}');
    const workOrderDetails = document.getElementById('work-order-details');
    
    // Load work order details when work order number changes
    woInput.addEventListener('input', function() {
        const woNo = this.value;
        if (woNo.length > 3) {
            // In a real implementation, this would make an HTMX request
            // to load work order details
            fetch(`/machinery/ajax/work-orders/?search_type=work_order&search_value=${woNo}`)
                .then(response => response.text())
                .then(html => {
                    // Update work order details section
                    // This would be handled by HTMX in practice
                });
        }
    });
    
    // Auto-generate job number if not provided
    const jobNoInput = document.getElementById('{{ form.jobno.id_for_label }}');
    if (!jobNoInput.value && woInput.value) {
        // Generate job number based on work order
        jobNoInput.value = 'JOB-' + woInput.value + '-' + Date.now().toString().slice(-6);
    }
});
</script>
{% endblock %}