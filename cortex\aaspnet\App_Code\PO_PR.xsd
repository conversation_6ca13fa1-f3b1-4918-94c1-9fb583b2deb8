﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="PO_PR" targetNamespace="http://tempuri.org/PO_PR.xsd" xmlns:mstns="http://tempuri.org/PO_PR.xsd" xmlns="http://tempuri.org/PO_PR.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="PO_PR" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="PO_PR" msprop:Generator_DataSetName="PO_PR">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" msprop:Generator_UserColumnName="Id" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnPropNameInTable="IdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="PONo" msprop:Generator_UserColumnName="PONo" msprop:Generator_ColumnVarNameInTable="columnPONo" msprop:Generator_ColumnPropNameInRow="PONo" msprop:Generator_ColumnPropNameInTable="PONoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="POQty" msprop:Generator_UserColumnName="POQty" msprop:Generator_ColumnVarNameInTable="columnPOQty" msprop:Generator_ColumnPropNameInRow="POQty" msprop:Generator_ColumnPropNameInTable="POQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Rate" msprop:Generator_UserColumnName="Rate" msprop:Generator_ColumnVarNameInTable="columnRate" msprop:Generator_ColumnPropNameInRow="Rate" msprop:Generator_ColumnPropNameInTable="RateColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Discount" msprop:Generator_UserColumnName="Discount" msprop:Generator_ColumnVarNameInTable="columnDiscount" msprop:Generator_ColumnPropNameInRow="Discount" msprop:Generator_ColumnPropNameInTable="DiscountColumn" type="xs:double" minOccurs="0" />
              <xs:element name="DelDate" msprop:Generator_UserColumnName="DelDate" msprop:Generator_ColumnVarNameInTable="columnDelDate" msprop:Generator_ColumnPropNameInRow="DelDate" msprop:Generator_ColumnPropNameInTable="DelDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="AmdNo" msprop:Generator_UserColumnName="AmdNo" msprop:Generator_ColumnVarNameInTable="columnAmdNo" msprop:Generator_ColumnPropNameInRow="AmdNo" msprop:Generator_ColumnPropNameInTable="AmdNoColumn" type="xs:int" minOccurs="0" />
              <xs:element name="RefDesc" msprop:Generator_UserColumnName="RefDesc" msprop:Generator_ColumnVarNameInTable="columnRefDesc" msprop:Generator_ColumnPropNameInRow="RefDesc" msprop:Generator_ColumnPropNameInTable="RefDescColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ModeOfDispatch" msprop:Generator_UserColumnName="ModeOfDispatch" msprop:Generator_ColumnVarNameInTable="columnModeOfDispatch" msprop:Generator_ColumnPropNameInRow="ModeOfDispatch" msprop:Generator_ColumnPropNameInTable="ModeOfDispatchColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Inspection" msprop:Generator_UserColumnName="Inspection" msprop:Generator_ColumnVarNameInTable="columnInspection" msprop:Generator_ColumnPropNameInRow="Inspection" msprop:Generator_ColumnPropNameInTable="InspectionColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ShipTo" msprop:Generator_UserColumnName="ShipTo" msprop:Generator_ColumnVarNameInTable="columnShipTo" msprop:Generator_ColumnPropNameInRow="ShipTo" msprop:Generator_ColumnPropNameInTable="ShipToColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Remarks" msprop:Generator_UserColumnName="Remarks" msprop:Generator_ColumnVarNameInTable="columnRemarks" msprop:Generator_ColumnPropNameInRow="Remarks" msprop:Generator_ColumnPropNameInTable="RemarksColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PRNo" msprop:Generator_UserColumnName="PRNo" msprop:Generator_ColumnVarNameInTable="columnPRNo" msprop:Generator_ColumnPropNameInRow="PRNo" msprop:Generator_ColumnPropNameInTable="PRNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="WONo" msprop:Generator_UserColumnName="WONo" msprop:Generator_ColumnVarNameInTable="columnWONo" msprop:Generator_ColumnPropNameInRow="WONo" msprop:Generator_ColumnPropNameInTable="WONoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="SuplierName" msprop:Generator_UserColumnName="SuplierName" msprop:Generator_ColumnVarNameInTable="columnSuplierName" msprop:Generator_ColumnPropNameInRow="SuplierName" msprop:Generator_ColumnPropNameInTable="SuplierNameColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ContactPerson" msprop:Generator_UserColumnName="ContactPerson" msprop:Generator_ColumnVarNameInTable="columnContactPerson" msprop:Generator_ColumnPropNameInRow="ContactPerson" msprop:Generator_ColumnPropNameInTable="ContactPersonColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Email" msprop:Generator_UserColumnName="Email" msprop:Generator_ColumnVarNameInTable="columnEmail" msprop:Generator_ColumnPropNameInRow="Email" msprop:Generator_ColumnPropNameInTable="EmailColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ContactNo" msprop:Generator_UserColumnName="ContactNo" msprop:Generator_ColumnVarNameInTable="columnContactNo" msprop:Generator_ColumnPropNameInRow="ContactNo" msprop:Generator_ColumnPropNameInTable="ContactNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ItemCode" msprop:Generator_UserColumnName="ItemCode" msprop:Generator_ColumnVarNameInTable="columnItemCode" msprop:Generator_ColumnPropNameInRow="ItemCode" msprop:Generator_ColumnPropNameInTable="ItemCodeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="manfDesc" msprop:Generator_UserColumnName="manfDesc" msprop:Generator_ColumnVarNameInTable="columnmanfDesc" msprop:Generator_ColumnPropNameInRow="manfDesc" msprop:Generator_ColumnPropNameInTable="manfDescColumn" type="xs:string" minOccurs="0" />
              <xs:element name="UOMBasic" msprop:Generator_UserColumnName="UOMBasic" msprop:Generator_ColumnVarNameInTable="columnUOMBasic" msprop:Generator_ColumnPropNameInRow="UOMBasic" msprop:Generator_ColumnPropNameInTable="UOMBasicColumn" type="xs:string" minOccurs="0" />
              <xs:element name="AcHead" msprop:Generator_UserColumnName="AcHead" msprop:Generator_ColumnVarNameInTable="columnAcHead" msprop:Generator_ColumnPropNameInRow="AcHead" msprop:Generator_ColumnPropNameInTable="AcHeadColumn" type="xs:string" minOccurs="0" />
              <xs:element name="OctriTerm" msprop:Generator_UserColumnName="OctriTerm" msprop:Generator_ColumnVarNameInTable="columnOctriTerm" msprop:Generator_ColumnPropNameInRow="OctriTerm" msprop:Generator_ColumnPropNameInTable="OctriTermColumn" type="xs:string" minOccurs="0" />
              <xs:element name="OctriValue" msprop:Generator_UserColumnName="OctriValue" msprop:Generator_ColumnVarNameInTable="columnOctriValue" msprop:Generator_ColumnPropNameInRow="OctriValue" msprop:Generator_ColumnPropNameInTable="OctriValueColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PackagingTerm" msprop:Generator_UserColumnName="PackagingTerm" msprop:Generator_ColumnVarNameInTable="columnPackagingTerm" msprop:Generator_ColumnPropNameInRow="PackagingTerm" msprop:Generator_ColumnPropNameInTable="PackagingTermColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PackagingValue" msprop:Generator_UserColumnName="PackagingValue" msprop:Generator_ColumnVarNameInTable="columnPackagingValue" msprop:Generator_ColumnPropNameInRow="PackagingValue" msprop:Generator_ColumnPropNameInTable="PackagingValueColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PaymentTerm" msprop:Generator_UserColumnName="PaymentTerm" msprop:Generator_ColumnVarNameInTable="columnPaymentTerm" msprop:Generator_ColumnPropNameInRow="PaymentTerm" msprop:Generator_ColumnPropNameInTable="PaymentTermColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ExciseTerm" msprop:Generator_UserColumnName="ExciseTerm" msprop:Generator_ColumnVarNameInTable="columnExciseTerm" msprop:Generator_ColumnPropNameInRow="ExciseTerm" msprop:Generator_ColumnPropNameInTable="ExciseTermColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ExciseValue" msprop:Generator_UserColumnName="ExciseValue" msprop:Generator_ColumnVarNameInTable="columnExciseValue" msprop:Generator_ColumnPropNameInRow="ExciseValue" msprop:Generator_ColumnPropNameInTable="ExciseValueColumn" type="xs:double" minOccurs="0" />
              <xs:element name="VatTerm" msprop:Generator_UserColumnName="VatTerm" msprop:Generator_ColumnVarNameInTable="columnVatTerm" msprop:Generator_ColumnPropNameInRow="VatTerm" msprop:Generator_ColumnPropNameInTable="VatTermColumn" type="xs:string" minOccurs="0" />
              <xs:element name="VatValue" msprop:Generator_UserColumnName="VatValue" msprop:Generator_ColumnVarNameInTable="columnVatValue" msprop:Generator_ColumnPropNameInRow="VatValue" msprop:Generator_ColumnPropNameInTable="VatValueColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Warranty" msprop:Generator_UserColumnName="Warranty" msprop:Generator_ColumnVarNameInTable="columnWarranty" msprop:Generator_ColumnPropNameInRow="Warranty" msprop:Generator_ColumnPropNameInTable="WarrantyColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Fright" msprop:Generator_UserColumnName="Fright" msprop:Generator_ColumnVarNameInTable="columnFright" msprop:Generator_ColumnPropNameInRow="Fright" msprop:Generator_ColumnPropNameInTable="FrightColumn" type="xs:string" minOccurs="0" />
              <xs:element name="RefPODesc" msprop:Generator_UserColumnName="RefPODesc" msprop:Generator_ColumnVarNameInTable="columnRefPODesc" msprop:Generator_ColumnPropNameInRow="RefPODesc" msprop:Generator_ColumnPropNameInTable="RefPODescColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Indentor" msprop:Generator_UserColumnName="Indentor" msprop:Generator_ColumnVarNameInTable="columnIndentor" msprop:Generator_ColumnPropNameInRow="Indentor" msprop:Generator_ColumnPropNameInTable="IndentorColumn" type="xs:string" minOccurs="0" />
              <xs:element name="BudgetCode" msprop:Generator_UserColumnName="BudgetCode" msprop:Generator_ColumnPropNameInRow="BudgetCode" msprop:Generator_ColumnVarNameInTable="columnBudgetCode" msprop:Generator_ColumnPropNameInTable="BudgetCodeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="SupplierId" msprop:Generator_UserColumnName="SupplierId" msprop:Generator_ColumnVarNameInTable="columnSupplierId" msprop:Generator_ColumnPropNameInRow="SupplierId" msprop:Generator_ColumnPropNameInTable="SupplierIdColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Insurance" msprop:Generator_UserColumnName="Insurance" msprop:Generator_ColumnPropNameInRow="Insurance" msprop:Generator_ColumnVarNameInTable="columnInsurance" msprop:Generator_ColumnPropNameInTable="InsuranceColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Symbol" msprop:Generator_UserColumnName="Symbol" msprop:Generator_ColumnPropNameInRow="Symbol" msprop:Generator_ColumnVarNameInTable="columnSymbol" msprop:Generator_ColumnPropNameInTable="SymbolColumn" type="xs:string" minOccurs="0" />
              <xs:element name="SuplierName_x002A_" msprop:Generator_UserColumnName="SuplierName*" msprop:Generator_ColumnVarNameInTable="_columnSuplierName_" msprop:Generator_ColumnPropNameInRow="_SuplierName_" msprop:Generator_ColumnPropNameInTable="_SuplierName_Column" type="xs:string" minOccurs="0" />
              <xs:element name="VenderCode_x002A_" msprop:Generator_UserColumnName="VenderCode*" msprop:Generator_ColumnVarNameInTable="_columnVenderCode_" msprop:Generator_ColumnPropNameInRow="_VenderCode_" msprop:Generator_ColumnPropNameInTable="_VenderCode_Column" type="xs:string" minOccurs="0" />
              <xs:element name="RefDate_x002A_" msprop:Generator_UserColumnName="RefDate*" msprop:Generator_ColumnVarNameInTable="_columnRefDate_" msprop:Generator_ColumnPropNameInRow="_RefDate_" msprop:Generator_ColumnPropNameInTable="_RefDate_Column" type="xs:string" minOccurs="0" />
              <xs:element name="RefDesc_x002A_" msprop:Generator_UserColumnName="RefDesc*" msprop:Generator_ColumnVarNameInTable="_columnRefDesc_" msprop:Generator_ColumnPropNameInRow="_RefDesc_" msprop:Generator_ColumnPropNameInTable="_RefDesc_Column" type="xs:string" minOccurs="0" />
              <xs:element name="RefPODesc_x002A_" msprop:Generator_UserColumnName="RefPODesc*" msprop:Generator_ColumnVarNameInTable="_columnRefPODesc_" msprop:Generator_ColumnPropNameInRow="_RefPODesc_" msprop:Generator_ColumnPropNameInTable="_RefPODesc_Column" type="xs:string" minOccurs="0" />
              <xs:element name="Rate_x002A_" msprop:Generator_UserColumnName="Rate*" msprop:Generator_ColumnVarNameInTable="_columnRate_" msprop:Generator_ColumnPropNameInRow="_Rate_" msprop:Generator_ColumnPropNameInTable="_Rate_Column" type="xs:string" minOccurs="0" />
              <xs:element name="Discount_x002A_" msprop:Generator_UserColumnName="Discount*" msprop:Generator_ColumnVarNameInTable="_columnDiscount_" msprop:Generator_ColumnPropNameInRow="_Discount_" msprop:Generator_ColumnPropNameInTable="_Discount_Column" type="xs:string" minOccurs="0" />
              <xs:element name="PackagingValue_x002A_" msprop:Generator_UserColumnName="PackagingValue*" msprop:Generator_ColumnVarNameInTable="_columnPackagingValue_" msprop:Generator_ColumnPropNameInRow="_PackagingValue_" msprop:Generator_ColumnPropNameInTable="_PackagingValue_Column" type="xs:string" minOccurs="0" />
              <xs:element name="ExciseValue_x002A_" msprop:Generator_UserColumnName="ExciseValue*" msprop:Generator_ColumnVarNameInTable="_columnExciseValue_" msprop:Generator_ColumnPropNameInRow="_ExciseValue_" msprop:Generator_ColumnPropNameInTable="_ExciseValue_Column" type="xs:string" minOccurs="0" />
              <xs:element name="VatValue_x002A_" msprop:Generator_UserColumnName="VatValue*" msprop:Generator_ColumnVarNameInTable="_columnVatValue_" msprop:Generator_ColumnPropNameInRow="_VatValue_" msprop:Generator_ColumnPropNameInTable="_VatValue_Column" type="xs:string" minOccurs="0" />
              <xs:element name="DelDate_x002A_" msprop:Generator_UserColumnName="DelDate*" msprop:Generator_ColumnVarNameInTable="_columnDelDate_" msprop:Generator_ColumnPropNameInRow="_DelDate_" msprop:Generator_ColumnPropNameInTable="_DelDate_Column" type="xs:string" minOccurs="0" />
              <xs:element name="PaymentTerm_x002A_" msprop:Generator_UserColumnName="PaymentTerm*" msprop:Generator_ColumnVarNameInTable="_columnPaymentTerm_" msprop:Generator_ColumnPropNameInRow="_PaymentTerm_" msprop:Generator_ColumnPropNameInTable="_PaymentTerm_Column" type="xs:string" minOccurs="0" />
              <xs:element name="ModeOfDispatch_x002A_" msprop:Generator_UserColumnName="ModeOfDispatch*" msprop:Generator_ColumnVarNameInTable="_columnModeOfDispatch_" msprop:Generator_ColumnPropNameInRow="_ModeOfDispatch_" msprop:Generator_ColumnPropNameInTable="_ModeOfDispatch_Column" type="xs:string" minOccurs="0" />
              <xs:element name="Inspection_x002A_" msprop:Generator_UserColumnName="Inspection*" msprop:Generator_ColumnVarNameInTable="_columnInspection_" msprop:Generator_ColumnPropNameInRow="_Inspection_" msprop:Generator_ColumnPropNameInTable="_Inspection_Column" type="xs:string" minOccurs="0" />
              <xs:element name="OctriValue_x002A_" msprop:Generator_UserColumnName="OctriValue*" msprop:Generator_ColumnVarNameInTable="_columnOctriValue_" msprop:Generator_ColumnPropNameInRow="_OctriValue_" msprop:Generator_ColumnPropNameInTable="_OctriValue_Column" type="xs:string" minOccurs="0" />
              <xs:element name="Warranty_x002A_" msprop:Generator_UserColumnName="Warranty*" msprop:Generator_ColumnVarNameInTable="_columnWarranty_" msprop:Generator_ColumnPropNameInRow="_Warranty_" msprop:Generator_ColumnPropNameInTable="_Warranty_Column" type="xs:string" minOccurs="0" />
              <xs:element name="Fright_x002A_" msprop:Generator_UserColumnName="Fright*" msprop:Generator_ColumnVarNameInTable="_columnFright_" msprop:Generator_ColumnPropNameInRow="_Fright_" msprop:Generator_ColumnPropNameInTable="_Fright_Column" type="xs:string" minOccurs="0" />
              <xs:element name="Insurance_x002A_" msprop:Generator_UserColumnName="Insurance*" msprop:Generator_ColumnVarNameInTable="_columnInsurance_" msprop:Generator_ColumnPropNameInRow="_Insurance_" msprop:Generator_ColumnPropNameInTable="_Insurance_Column" type="xs:string" minOccurs="0" />
              <xs:element name="ShipTo_x002A_" msprop:Generator_UserColumnName="ShipTo*" msprop:Generator_ColumnVarNameInTable="_columnShipTo_" msprop:Generator_ColumnPropNameInRow="_ShipTo_" msprop:Generator_ColumnPropNameInTable="_ShipTo_Column" type="xs:string" minOccurs="0" />
              <xs:element name="Remarks_x002A_" msprop:Generator_UserColumnName="Remarks*" msprop:Generator_ColumnVarNameInTable="_columnRemarks_" msprop:Generator_ColumnPropNameInRow="_Remarks_" msprop:Generator_ColumnPropNameInTable="_Remarks_Column" type="xs:string" minOccurs="0" />
              <xs:element name="BudgetCode_x002A_" msprop:Generator_UserColumnName="BudgetCode*" msprop:Generator_ColumnVarNameInTable="_columnBudgetCode_" msprop:Generator_ColumnPropNameInRow="_BudgetCode_" msprop:Generator_ColumnPropNameInTable="_BudgetCode_Column" type="xs:string" minOccurs="0" />
              <xs:element name="AddDesc" msprop:Generator_UserColumnName="AddDesc" msprop:Generator_ColumnPropNameInRow="AddDesc" msprop:Generator_ColumnVarNameInTable="columnAddDesc" msprop:Generator_ColumnPropNameInTable="AddDescColumn" type="xs:string" minOccurs="0" />
              <xs:element name="AddDesc_x002A_" msprop:Generator_UserColumnName="AddDesc*" msprop:Generator_ColumnPropNameInRow="_AddDesc_" msprop:Generator_ColumnVarNameInTable="_columnAddDesc_" msprop:Generator_ColumnPropNameInTable="_AddDesc_Column" type="xs:string" minOccurs="0" />
              <xs:element name="POQty_x002A_" msprop:Generator_UserColumnName="POQty*" msprop:Generator_ColumnVarNameInTable="_columnPOQty_" msprop:Generator_ColumnPropNameInRow="_POQty_" msprop:Generator_ColumnPropNameInTable="_POQty_Column" type="xs:string" minOccurs="0" />
              <xs:element name="TC_x002A_" msprop:Generator_UserColumnName="TC*" msprop:Generator_ColumnPropNameInRow="_TC_" msprop:Generator_ColumnVarNameInTable="_columnTC_" msprop:Generator_ColumnPropNameInTable="_TC_Column" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>