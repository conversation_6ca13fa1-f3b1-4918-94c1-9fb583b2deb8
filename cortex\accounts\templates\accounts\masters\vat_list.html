<!-- accounts/templates/accounts/masters/vat_list.html -->
<!-- VAT Rate Management List Template -->
<!-- Task Group 4: Taxation Management - VAT List -->

{% extends 'core/base.html' %}
{% load accounts_filters %}

{% block title %}VAT Rates - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-orange-600 to-sap-orange-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="percent" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">VAT Rates Management</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Configure Value Added Tax rates for different product categories</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Accounts
                </a>
                <a href="{% url 'accounts:vat_create' %}" 
                   class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    Add VAT Rate
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6" x-data="vatRatesManagement()">
    
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Total VAT Rates</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ vat_rates.count }}</p>
                </div>
                <div class="w-12 h-12 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="percent" class="w-6 h-6 text-sap-orange-600"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Active Rates</p>
                    <p class="text-2xl font-bold text-sap-green-600">{{ active_count|default:0 }}</p>
                </div>
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="check-circle" class="w-6 h-6 text-sap-green-600"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Average Rate</p>
                    <p class="text-2xl font-bold text-sap-blue-600">{{ average_rate|floatformat:1|default:"0.0" }}%</p>
                </div>
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="trending-up" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Highest Rate</p>
                    <p class="text-2xl font-bold text-sap-red-600">{{ highest_rate|floatformat:1|default:"0.0" }}%</p>
                </div>
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="arrow-up" class="w-6 h-6 text-red-600"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Search and Filters -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm mb-6">
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-sap-gray-700 mb-2">Search</label>
                    <input type="text" x-model="searchTerm" @input="filterRates" id="search"
                           placeholder="Search by terms, rate..."
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
                </div>
                <div>
                    <label for="rate_range" class="block text-sm font-medium text-sap-gray-700 mb-2">Rate Range</label>
                    <select x-model="rateRange" @change="filterRates" id="rate_range"
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
                        <option value="">All Rates</option>
                        <option value="0-5">0% - 5%</option>
                        <option value="5-10">5% - 10%</option>
                        <option value="10-15">10% - 15%</option>
                        <option value="15-20">15% - 20%</option>
                        <option value="20+">20%+</option>
                    </select>
                </div>
                <div>
                    <label for="category" class="block text-sm font-medium text-sap-gray-700 mb-2">Category</label>
                    <select x-model="categoryFilter" @change="filterRates" id="category"
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
                        <option value="">All Categories</option>
                        <option value="Essential">Essential Goods</option>
                        <option value="Standard">Standard Rate</option>
                        <option value="Luxury">Luxury Items</option>
                        <option value="Export">Export Goods</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button @click="resetFilters" 
                            class="w-full bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="refresh-cw" class="w-4 h-4 inline mr-2"></i>
                        Reset
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- VAT Rates Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">VAT Rates</h3>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-sap-gray-600" x-text="`${filteredRates.length} of ${allRates.length} rates`"></span>
                    <div class="flex items-center space-x-1">
                        <button @click="exportRates" 
                                class="text-sap-gray-600 hover:text-sap-gray-800 p-1">
                            <i data-lucide="download" class="w-4 h-4"></i>
                        </button>
                        <button @click="refreshData" 
                                class="text-sap-gray-600 hover:text-sap-gray-800 p-1">
                            <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        {% if vat_rates %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            <button @click="sortBy('terms')" class="flex items-center space-x-1">
                                <span>Terms/Description</span>
                                <i data-lucide="chevron-up-down" class="w-3 h-3"></i>
                            </button>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            <button @click="sortBy('rate')" class="flex items-center space-x-1">
                                <span>VAT Rate</span>
                                <i data-lucide="chevron-up-down" class="w-3 h-3"></i>
                            </button>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Category</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Sample Tax</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for vat in vat_rates %}
                    <tr class="hover:bg-sap-gray-50" x-show="isVisible({{ vat.id }})">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div>
                                <div class="text-sm font-medium text-sap-gray-900">{{ vat.terms|default:"Standard VAT" }}</div>
                                {% if vat.description %}
                                <div class="text-sm text-sap-gray-500">{{ vat.description|truncatechars:50 }}</div>
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <span class="text-2xl font-bold text-sap-orange-600">{{ vat.vat_percentage|floatformat:2 }}%</span>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if vat.vat_percentage <= 5 %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-green-100 text-sap-green-800">
                                Essential
                            </span>
                            {% elif vat.vat_percentage <= 15 %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-blue-100 text-sap-blue-800">
                                Standard
                            </span>
                            {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-red-100 text-sap-red-800">
                                Luxury
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if vat.is_active|default:True %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-green-100 text-sap-green-800">
                                <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                Active
                            </span>
                            {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-gray-100 text-sap-gray-800">
                                <i data-lucide="pause-circle" class="w-3 h-3 mr-1"></i>
                                Inactive
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-600">
                            <div>
                                <div>₹10,000 → <span class="font-medium text-sap-orange-600">₹{{ vat.vat_percentage|mul:100|floatformat:0 }}</span></div>
                                <div class="text-xs text-sap-gray-500">Tax amount</div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <button @click="calculateVAT({{ vat.vat_percentage }}, '{{ vat.terms|default:"VAT" }}')" 
                                        class="text-sap-green-600 hover:text-sap-green-700 p-1" title="Calculate">
                                    <i data-lucide="calculator" class="w-4 h-4"></i>
                                </button>
                                <a href="{% url 'accounts:vat_edit' vat.id %}" 
                                   class="text-sap-blue-600 hover:text-sap-blue-700 p-1" title="Edit">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </a>
                                <a href="{% url 'accounts:vat_delete' vat.id %}" 
                                   class="text-red-600 hover:text-red-700 p-1" title="Delete"
                                   onclick="return confirm('Are you sure you want to delete this VAT rate?')">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="w-24 h-24 bg-sap-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i data-lucide="percent" class="w-12 h-12 text-sap-orange-600"></i>
            </div>
            <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No VAT Rates Configured</h3>
            <p class="text-sap-gray-600 mb-6">Get started by adding your first VAT rate configuration.</p>
            <a href="{% url 'accounts:vat_create' %}" 
               class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                Add First VAT Rate
            </a>
        </div>
        {% endif %}
    </div>
    
    <!-- Quick Calculator Modal -->
    <div x-show="showCalculator" x-transition 
         class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
         @click.self="showCalculator = false">
        <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-sap-gray-800" x-text="calculatorTitle"></h3>
                <button @click="showCalculator = false" class="text-sap-gray-400 hover:text-sap-gray-600">
                    <i data-lucide="x" class="w-5 h-5"></i>
                </button>
            </div>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-sap-gray-700 mb-2">Amount (₹)</label>
                    <input type="number" x-model="calculatorAmount" step="0.01" min="0"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:ring-sap-orange-500 focus:border-sap-orange-500"
                           placeholder="10000">
                </div>
                
                <div x-show="calculatorResult" class="bg-sap-orange-50 rounded-lg p-4">
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-sap-gray-600">Base Amount:</span>
                            <div class="font-medium" x-text="'₹' + parseFloat(calculatorAmount || 0).toFixed(2)"></div>
                        </div>
                        <div>
                            <span class="text-sap-gray-600">VAT Amount:</span>
                            <div class="font-medium text-sap-orange-600" x-text="'₹' + vatAmount"></div>
                        </div>
                        <div>
                            <span class="text-sap-gray-600">Total Amount:</span>
                            <div class="font-bold text-sap-gray-800" x-text="'₹' + totalAmount"></div>
                        </div>
                        <div>
                            <span class="text-sap-gray-600">VAT Rate:</span>
                            <div class="font-medium text-sap-orange-600" x-text="calculatorRate + '%'"></div>
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center justify-end space-x-3">
                    <button @click="showCalculator = false" 
                            class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                        Close
                    </button>
                    <button @click="performCalculation" 
                            class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        Calculate
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function vatRatesManagement() {
    return {
        searchTerm: '',
        rateRange: '',
        categoryFilter: '',
        sortField: '',
        sortDirection: 'asc',
        allRates: {{ vat_rates|length }},
        filteredRates: {{ vat_rates|length }},
        showCalculator: false,
        calculatorTitle: '',
        calculatorRate: 0,
        calculatorAmount: 10000,
        calculatorResult: false,
        vatAmount: 0,
        totalAmount: 0,
        
        init() {
            lucide.createIcons();
            this.allRates = document.querySelectorAll('tbody tr').length;
            this.filteredRates = this.allRates;
        },
        
        filterRates() {
            const rows = document.querySelectorAll('tbody tr');
            let visibleCount = 0;
            
            rows.forEach(row => {
                const terms = row.querySelector('td:first-child').textContent.toLowerCase();
                const rate = parseFloat(row.querySelector('td:nth-child(2)').textContent);
                
                let visible = true;
                
                // Search filter
                if (this.searchTerm && !terms.includes(this.searchTerm.toLowerCase())) {
                    visible = false;
                }
                
                // Rate range filter
                if (this.rateRange) {
                    const [min, max] = this.rateRange.split('-').map(v => v === '+' ? 100 : parseFloat(v));
                    if (this.rateRange.includes('+')) {
                        visible = visible && rate >= min;
                    } else {
                        visible = visible && rate >= min && rate <= max;
                    }
                }
                
                // Category filter (based on rate ranges)
                if (this.categoryFilter) {
                    if (this.categoryFilter === 'Essential' && rate > 5) visible = false;
                    if (this.categoryFilter === 'Standard' && (rate <= 5 || rate > 15)) visible = false;
                    if (this.categoryFilter === 'Luxury' && rate <= 15) visible = false;
                }
                
                row.style.display = visible ? '' : 'none';
                if (visible) visibleCount++;
            });
            
            this.filteredRates = visibleCount;
        },
        
        resetFilters() {
            this.searchTerm = '';
            this.rateRange = '';
            this.categoryFilter = '';
            this.filterRates();
        },
        
        sortBy(field) {
            // Simple sorting implementation
            console.log('Sorting by:', field);
        },
        
        isVisible(id) {
            return true; // Used with Alpine.js filtering
        },
        
        calculateVAT(rate, terms) {
            this.calculatorRate = rate;
            this.calculatorTitle = `VAT Calculator - ${terms} (${rate}%)`;
            this.calculatorAmount = 10000;
            this.calculatorResult = false;
            this.showCalculator = true;
        },
        
        performCalculation() {
            const amount = parseFloat(this.calculatorAmount) || 0;
            this.vatAmount = (amount * this.calculatorRate / 100).toFixed(2);
            this.totalAmount = (amount + parseFloat(this.vatAmount)).toFixed(2);
            this.calculatorResult = true;
        },
        
        exportRates() {
            // Export functionality
            console.log('Exporting VAT rates...');
        },
        
        refreshData() {
            window.location.reload();
        }
    }
}

// Auto-calculate when amount changes
document.addEventListener('DOMContentLoaded', function() {
    // Initialize lucide icons
    lucide.createIcons();
});
</script>
{% endblock %}