{% if enriched_pos %}
<div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-sap-gray-200">
        <thead class="bg-sap-gray-50">
            <tr>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sap-gray-700 uppercase tracking-wider">
                    SN
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">
                    Fin Yrs
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">
                    Customer Name
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">
                    Code
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">
                    PO No
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">
                    Enquiry No
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">
                    Gen Date
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">
                    Gen By
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-sap-gray-200">
            {% for po_data in enriched_pos %}
            <tr class="hover:bg-sap-gray-50 transition-colors duration-200 animate-fade-in">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900 text-right">
                    {{ forloop.counter }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">
                    {{ po_data.fin_year|default:"-" }}
                </td>
                <td class="px-6 py-4 text-sm text-sap-gray-900">
                    {{ po_data.customer_name|default:"-" }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">
                    {{ po_data.customer_id|default:"-" }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm">
                    <a href="{% url 'sales_distribution:customer_po_update' po_data.po.poid %}?CustomerId={{ po_data.customer_id }}&PONo={{ po_data.po.pono }}&EnqId={{ po_data.po.enqid_id }}&POId={{ po_data.po.poid }}&ModId=2&SubModId=11" 
                       class="text-sap-blue-600 hover:text-sap-blue-800 font-medium transition-colors duration-200">
                        {{ po_data.po.pono|default:"-" }}
                    </a>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">
                    {{ po_data.po.enqid_id|default:"-" }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">
                    {{ po_data.formatted_date|default:"-" }}
                </td>
                <td class="px-6 py-4 text-sm text-sap-gray-900">
                    {{ po_data.employee_name|default:"-" }}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- Pagination for HTMX -->
{% if is_paginated %}
<div class="bg-white px-4 py-3 flex items-center justify-between border-t border-sap-gray-200 sm:px-6">
    <div class="flex-1 flex justify-between sm:hidden">
        {% if page_obj.has_previous %}
            <button hx-get="{% url 'sales_distribution:customer_po_list' %}?{% if search_type %}search_type={{ search_type }}&{% endif %}{% if search_value %}search_value={{ search_value }}&{% endif %}{% if enq_id %}enq_id={{ enq_id }}&{% endif %}page={{ page_obj.previous_page_number }}" 
                    hx-target="#po-table-container"
                    class="relative inline-flex items-center px-4 py-2 border border-sap-gray-300 text-sm font-medium rounded-md text-sap-gray-700 bg-white hover:bg-sap-gray-50 transition-colors duration-200">
                Previous
            </button>
        {% endif %}
        {% if page_obj.has_next %}
            <button hx-get="{% url 'sales_distribution:customer_po_list' %}?{% if search_type %}search_type={{ search_type }}&{% endif %}{% if search_value %}search_value={{ search_value }}&{% endif %}{% if enq_id %}enq_id={{ enq_id }}&{% endif %}page={{ page_obj.next_page_number }}" 
                    hx-target="#po-table-container"
                    class="ml-3 relative inline-flex items-center px-4 py-2 border border-sap-gray-300 text-sm font-medium rounded-md text-sap-gray-700 bg-white hover:bg-sap-gray-50 transition-colors duration-200">
                Next
            </button>
        {% endif %}
    </div>
    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
            <p class="text-sm text-sap-gray-700">
                Showing
                <span class="font-medium">{{ page_obj.start_index }}</span>
                to
                <span class="font-medium">{{ page_obj.end_index }}</span>
                of
                <span class="font-medium">{{ paginator.count }}</span>
                results
            </p>
        </div>
        <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                {% if page_obj.has_previous %}
                    <button hx-get="{% url 'sales_distribution:customer_po_list' %}?{% if search_type %}search_type={{ search_type }}&{% endif %}{% if search_value %}search_value={{ search_value }}&{% endif %}{% if enq_id %}enq_id={{ enq_id }}&{% endif %}page={{ page_obj.previous_page_number }}" 
                            hx-target="#po-table-container"
                            class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-500 hover:bg-sap-gray-50 transition-colors duration-200">
                        <span class="sr-only">Previous</span>
                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                    </button>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-sap-gray-300 bg-sap-blue-50 text-sm font-medium text-sap-blue-600">
                            {{ num }}
                        </span>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <button hx-get="{% url 'sales_distribution:customer_po_list' %}?{% if search_type %}search_type={{ search_type }}&{% endif %}{% if search_value %}search_value={{ search_value }}&{% endif %}{% if enq_id %}enq_id={{ enq_id }}&{% endif %}page={{ num }}" 
                                hx-target="#po-table-container"
                                class="relative inline-flex items-center px-4 py-2 border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-700 hover:bg-sap-gray-50 transition-colors duration-200">
                            {{ num }}
                        </button>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <button hx-get="{% url 'sales_distribution:customer_po_list' %}?{% if search_type %}search_type={{ search_type }}&{% endif %}{% if search_value %}search_value={{ search_value }}&{% endif %}{% if enq_id %}enq_id={{ enq_id }}&{% endif %}page={{ page_obj.next_page_number }}" 
                            hx-target="#po-table-container"
                            class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-500 hover:bg-sap-gray-50 transition-colors duration-200">
                        <span class="sr-only">Next</span>
                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </button>
                {% endif %}
            </nav>
        </div>
    </div>
</div>
{% endif %}

{% else %}
<!-- Empty State -->
<div class="text-center py-12 animate-fade-in">
    <div class="mx-auto h-12 w-12 text-sap-gray-400">
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
    </div>
    <h3 class="mt-2 text-lg font-medium text-sap-gray-700">No data to display !</h3>
    <p class="mt-1 text-sm text-sap-gray-500">Try adjusting your search criteria or create a new purchase order.</p>
</div>
{% endif %}