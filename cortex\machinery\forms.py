from django import forms
from django.core.exceptions import ValidationError
from .models import (
    Machine, PreventiveMaintenance, JobSchedule
)
from design.models import Item, Category, Subcategory


class MachineForm(forms.ModelForm):
    """Form for creating and editing machines"""
    
    class Meta:
        model = Machine
        fields = [
            'itemid', 'make', 'model', 'capacity', 'purchasedate',
            'suppliername', 'cost', 'warrantyexpirydate', 'lifedate',
            'receiveddate', 'insurance', 'insuranceexpirydate',
            'puttouse', 'incharge', 'location', 'pmdays'
        ]
        widgets = {
            'make': forms.TextInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'placeholder': 'Machine Make'
            }),
            'model': forms.TextInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'placeholder': 'Machine Model'
            }),
            'capacity': forms.TextInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'placeholder': 'Machine Capacity'
            }),
            'purchasedate': forms.DateInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'type': 'date'
            }),
            'suppliername': forms.TextInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'placeholder': 'Supplier Name'
            }),
            'cost': forms.NumberInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'step': '0.01',
                'placeholder': 'Cost'
            }),
            'warrantyexpirydate': forms.DateInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'type': 'date'
            }),
            'lifedate': forms.DateInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'type': 'date'
            }),
            'receiveddate': forms.DateInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'type': 'date'
            }),
            'insurance': forms.Select(choices=[(0, 'No'), (1, 'Yes')], attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'hx-trigger': 'change',
                'hx-post': '#',
                'hx-target': '#insurance-fields'
            }),
            'insuranceexpirydate': forms.DateInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'type': 'date'
            }),
            'puttouse': forms.DateInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'type': 'date'
            }),
            'incharge': forms.TextInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'placeholder': 'Person In-charge'
            }),
            'location': forms.TextInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'placeholder': 'Machine Location'
            }),
            'pmdays': forms.NumberInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'placeholder': 'Preventive Maintenance Days'
            })
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)
        
        # Make insurance expiry date conditional
        if self.data.get('insurance') != '1':
            self.fields['insuranceexpirydate'].required = False

    def clean(self):
        cleaned_data = super().clean()
        insurance = cleaned_data.get('insurance')
        insuranceexpirydate = cleaned_data.get('insuranceexpirydate')
        
        if insurance == 1 and not insuranceexpirydate:
            raise ValidationError("Insurance expiry date is required when insurance is selected.")
        
        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        
        if commit:
            instance.save()
        return instance


class MachineSearchForm(forms.Form):
    """Form for searching machines"""
    category = forms.ModelChoiceField(
        queryset=Category.objects.none(),
        required=False,
        empty_label="Select Category",
        widget=forms.Select(attrs={
            'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
            'hx-trigger': 'change',
            'hx-post': '#',
            'hx-target': '#subcategory-field'
        })
    )
    
    subcategory = forms.ModelChoiceField(
        queryset=Subcategory.objects.none(),
        required=False,
        empty_label="Select SubCategory",
        widget=forms.Select(attrs={
            'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500'
        })
    )
    
    SEARCH_CHOICES = [
        ('', 'Select'),
        ('itemcode', 'Machine Code'),
        ('description', 'Description'),
        ('location', 'Location'),
    ]
    
    search_field = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
            'hx-trigger': 'change',
            'hx-post': '#',
            'hx-target': '#search-input'
        })
    )
    
    search_value = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
            'placeholder': 'Search...',
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-post': '#',
            'hx-target': '#search-results'
        })
    )

    def __init__(self, *args, **kwargs):
        self.company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)
        
        if self.company:
            self.fields['category'].queryset = Category.objects.filter(compid=self.company)


class PreventiveMaintenanceForm(forms.ModelForm):
    """Form for preventive maintenance records"""
    
    class Meta:
        model = PreventiveMaintenance
        fields = [
            'machineid', 'pmbm', 'fromdate', 'todate', 'fromtime', 'totime',
            'nameofagency', 'nameofengineer', 'nextpmdueon', 'nextbmdueon', 'remarks'
        ]
        widgets = {
            'machineid': forms.Select(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500'
            }),
            'pmbm': forms.Select(choices=[(0, 'Preventive Maintenance'), (1, 'Breakdown Maintenance')], attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500'
            }),
            'fromdate': forms.DateInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'type': 'date'
            }),
            'todate': forms.DateInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'type': 'date'
            }),
            'fromtime': forms.TimeInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'type': 'time'
            }),
            'totime': forms.TimeInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'type': 'time'
            }),
            'nameofagency': forms.TextInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'placeholder': 'Agency Name'
            }),
            'nameofengineer': forms.TextInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'placeholder': 'Engineer Name'
            }),
            'nextpmdueon': forms.DateInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'type': 'date'
            }),
            'nextbmdueon': forms.DateInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'type': 'date'
            }),
            'remarks': forms.Textarea(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'rows': 3,
                'placeholder': 'Maintenance remarks...'
            })
        }

    def __init__(self, *args, **kwargs):
        self.company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)
        
        if self.company:
            # Filter machines by company
            self.fields['machineid'].queryset = Machine.objects.filter(company=self.company)


class JobScheduleForm(forms.ModelForm):
    """Form for job scheduling"""
    
    class Meta:
        model = JobSchedule
        fields = ['jobno', 'wono', 'itemid']
        widgets = {
            'jobno': forms.TextInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'placeholder': 'Job Number'
            }),
            'wono': forms.TextInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                'placeholder': 'Work Order Number',
                'hx-trigger': 'keyup changed delay:500ms',
                'hx-post': '#',
                'hx-target': '#work-order-details'
            }),
            'itemid': forms.Select(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500'
            })
        }

    def __init__(self, *args, **kwargs):
        self.company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)
        
        if self.company:
            # Filter items by company
            self.fields['itemid'].queryset = Item.objects.filter(compid=self.company)


class WorkOrderSearchForm(forms.Form):
    """Form for searching work orders"""
    SEARCH_CHOICES = [
        ('', 'Select'),
        ('customer', 'Customer'),
        ('work_order', 'Work Order'),
    ]
    
    search_type = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
            'hx-trigger': 'change',
            'hx-post': '#',
            'hx-target': '#search-field'
        })
    )
    
    search_value = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
            'placeholder': 'Search...',
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-post': '#',
            'hx-target': '#work-order-results'
        })
    )


class MachineItemSelectionForm(forms.Form):
    """Form for selecting items to create machines from"""
    category = forms.ModelChoiceField(
        queryset=Category.objects.none(),
        required=False,
        empty_label="Select Category",
        widget=forms.Select(attrs={
            'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
            'hx-trigger': 'change',
            'hx-get': '/machinery/ajax/subcategories/',
            'hx-target': '#subcategory-field'
        })
    )
    
    subcategory = forms.ModelChoiceField(
        queryset=Subcategory.objects.none(),
        required=False,
        empty_label="Select SubCategory",
        widget=forms.Select(attrs={
            'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
            'hx-trigger': 'change',
            'hx-get': '/machinery/ajax/items/',
            'hx-target': '#items-grid'
        })
    )
    
    SEARCH_CHOICES = [
        ('', 'Select'),
        ('itemcode', 'Machine Code'),
        ('description', 'Description'), 
        ('location', 'Location'),
    ]
    
    search_field = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500'
        })
    )
    
    search_value = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
            'placeholder': 'Search...',
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-get': '/machinery/ajax/items/',
            'hx-target': '#items-grid'
        })
    )

    def __init__(self, *args, **kwargs):
        self.company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)
        
        if self.company:
            self.fields['category'].queryset = Category.objects.filter(compid=self.company)


class MachineSpareSelectionForm(forms.Form):
    """Form for selecting spare parts for machines"""
    spare_items = forms.ModelMultipleChoiceField(
        queryset=Item.objects.none(),
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'mt-1'
        })
    )
    
    def __init__(self, *args, **kwargs):
        self.company = kwargs.pop('company', None)
        self.exclude_item = kwargs.pop('exclude_item', None)
        super().__init__(*args, **kwargs)
        
        if self.company:
            queryset = Item.objects.filter(compid=self.company, absolute__ne='1')
            if self.exclude_item:
                queryset = queryset.exclude(id=self.exclude_item)
            self.fields['spare_items'].queryset = queryset


class MachineProcessSelectionForm(forms.Form):
    """Form for selecting processes for machines"""
    processes = forms.ModelMultipleChoiceField(
        queryset=None,  # Will be set in __init__
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'mt-1'
        })
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Import here to avoid circular imports
        try:
            from material_planning.models import ProcessMaster
            self.fields['processes'].queryset = ProcessMaster.objects.filter(
                symbol__ne='0'
            ).order_by('symbol')
        except ImportError:
            # Fallback if ProcessMaster doesn't exist yet
            self.fields['processes'].queryset = Item.objects.none()


class SpareQuantityForm(forms.Form):
    """Form for specifying spare part quantities"""
    item_id = forms.IntegerField(widget=forms.HiddenInput())
    quantity = forms.FloatField(
        min_value=0.01,
        widget=forms.NumberInput(attrs={
            'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
            'step': '0.01',
            'placeholder': 'Quantity'
        })
    )