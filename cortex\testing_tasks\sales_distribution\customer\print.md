# Customer Print - Detailed Flow Diagram

## Exact File Paths Involved

### Primary Files:
1. **`/Users/<USER>/workspace/cortex/aaspnet/Module/SalesDistribution/Masters/CustomerMaster_Print.aspx`** (Search & List Interface)
2. **`/Users/<USER>/workspace/cortex/aaspnet/Module/SalesDistribution/Masters/CustomerMaster_Print.aspx.cs`** (Search Logic)
3. **`/Users/<USER>/workspace/cortex/aaspnet/Module/SalesDistribution/Masters/CustomerMaster_Print_Details.aspx`** (Print Viewer)
4. **`/Users/<USER>/workspace/cortex/aaspnet/Module/SalesDistribution/Masters/CustomerMaster_Print_Details.aspx.cs`** (Crystal Reports Logic)

### Supporting Files:
5. **`~/MasterPage.master`** (Layout template)
6. **`../../../Javascript/loadingNotifier.js`** (Loading indicators)
7. **`../../../Css/StyleSheet.css`** (Styling)
8. **`~/Module/SalesDistribution/Masters/Reports/CustPrint.rpt`** (Crystal Reports Template)
9. **`~/Module/SalesDistribution/Masters/Customer_Details_Print_All.aspx`** (Print All functionality)

---

## Visual Architecture Diagram

```
================================================================================
                          CUSTOMER PRINT MODULE                               
                        Two-Stage Process                                         
================================================================================
                                    |
                                    |
+===============================================================================+
|                       STAGE 1: SEARCH INTERFACE                              |
|                File: CustomerMaster_Print.aspx                               |
+===============================================================================+
                                    |
                                    | user selects customer
                                    |
+===============================================================================+
|                      STAGE 2: PRINT INTERFACE                                |
|             File: CustomerMaster_Print_Details.aspx                          |
+===============================================================================+

+===============================================================================+
|                        STAGE 1: SEARCH PAGE                                  |
|                                                                               |
|  +-------------------------------------------------------------------------+ |
|  |                        SEARCH SECTION                                   | |
|  |  CustomerName: [________________________] [Search Button] [Print All]  | |
|  |                   +-- AutoComplete AJAX                                 | |
|  +-------------------------------------------------------------------------+ |
|                                                                               |
|  +-------------------------------------------------------------------------+ |
|  |                      RESULTS GRIDVIEW                                   | |
|  |                                                                          | |
|  | +--+--------+------------------+-------+------------------+-----------+ | |
|  | |SN|Fin Yrs |  Customer Name   | Code  |    Address       | Gen. By   | | |
|  | +--+--------+------------------+-------+------------------+-----------+ | |
|  | |1 |2023-24 | ABC Company      |ABC001 | 123 Main St...  | Admin     | | |
|  | |2 |2023-24 | XYZ Industries   |XYZ002 | 456 Oak Ave...  | Manager   | | |
|  | |3 |2023-24 | DEF Corporation  |DEF003 | 789 Pine Rd...  | Admin     | | |
|  | +--+--------+------------------+-------+------------------+-----------+ | |
|  |                      |                                                  | |
|  |                      +-- Clickable links to Print Details               | |
|  +-------------------------------------------------------------------------+ |
+===============================================================================+

+===============================================================================+
|                        STAGE 2: PRINT PAGE                                   |
|                                                                               |
|  +-------------------------------------------------------------------------+ |
|  |                    CRYSTAL REPORTS VIEWER                               | |
|  |  +-------------------------------------------------------------------+  | |
|  |  |                  CUSTOMER DETAILS REPORT                         |  | |
|  |  |                                                                   |  | |
|  |  | Customer Name: ABC Company Ltd.                                   |  | |
|  |  | Customer Code: ABC001                                             |  | |
|  |  |                                                                   |  | |
|  |  | +-- REGISTERED OFFICE --+ +-- WORKS/FACTORY --+ +-- MATERIAL DELIVERY --+ |
|  |  | | Address: 123 Main St  | | Address: 456 Oak  | | Address: 789 Pine    | |
|  |  | | City: New York        | | City: Chicago     | | City: Los Angeles    | |
|  |  | | State: NY             | | State: IL         | | State: CA            | |
|  |  | | Country: USA          | | Country: USA      | | Country: USA         | |
|  |  | | PIN: 10001            | | PIN: 60601        | | PIN: 90210           | |
|  |  | | Contact: +1-555-123   | | Contact: +1-555-  | | Contact: +1-555-789  | |
|  |  | | Fax: +1-555-124       | | Fax: +1-555-457   | | Fax: +1-555-790      | |
|  |  | +-----------------------+ +-------------------+ +----------------------+ |
|  |  |                                                                   |  | |
|  |  | Business Information:                                             |  | |
|  |  | Contact Person: John Doe        E-mail: <EMAIL>              |  | |
|  |  | Jurisdiction: NYC123            ECC No: ECC456                    |  | |
|  |  | Commission: 5%                  Division: North                   |  | |
|  |  | TIN/VAT: VAT789                 TIN/CST: CST012                   |  | |
|  |  | PAN: **********                 TDS Code: TDS345                  |  | |
|  |  | Range: Central                  Contact: +1-555-999               |  | |
|  |  |                                                                   |  | |
|  |  | Remarks: [Customer specific notes and comments]                  |  | |
|  |  |                                                                   |  | |
|  |  | Registration Date: 15-Mar-2024                                    |  | |
|  |  +-------------------------------------------------------------------+  | |
|  |                                                                          | |
|  |  [Report Toolbar: Print, Export, Zoom, Navigation]                      | |
|  +-------------------------------------------------------------------------+ |
|                                                                               |
|  +-------------------------------------------------------------------------+ |
|  |                          [Cancel Button]                                | |
|  +-------------------------------------------------------------------------+ |
+===============================================================================+
```

---

## Process Flow Diagram

```
+-------------------+
|       START       |
|   User Access     |
|  Customer Print   |
+-------------------+
          |
          |
+-------------------+      +------------------------------------------+
|    Page_Load      | ---> | File: CustomerMaster_Print.aspx.cs      |
|   (Search Page)   |      | Method: Page_Load()                      |
+-------------------+      |                                          |
          |                | 1. sId = Session["username"]            |
          |                | 2. CompId = Session["compid"]            |
+-------------------+      | 3. FinYearId = Session["finyear"]        |
|   Initialize      | ---> | 4. BindDataCust(CId) - Empty search     |
|   Search Page     |      +------------------------------------------+
+-------------------+
          |
          |
+-------------------+      +------------------------------------------+
|  User Types in    |      | AJAX AutoComplete Integration:           |
|  Search Box       | ---> | " ServiceMethod="sql"                    |
+-------------------+      | " CompletionInterval="100"               |
          |                | " MinimumPrefixLength="1"                |
          |                | " Shows Customer Name + [Code]           |
+-------------------+      +------------------------------------------+
|   User Clicks     |
|   Search          |      +------------------------------------------+
+-------------------+      | Search_Click Event:                     |
          |                |                                          |
          |                | if (TxtSearchValue.Text != "")           |
+-------------------+      |     BindDataCust(TxtSearchValue.Text)    |
|   BindDataCust    | ---> | else                                     |
|   Method          |      |     BindDataCust(CId)                    |
+-------------------+      +------------------------------------------+
          |
          |
+-------------------+      +------------------------------------------+
|   Database        |      | SELECT CustomerName, Address Fields,    |
|   Query           | ---> |        FinYear, Employee Details         |
+-------------------+      | FROM SD_Cust_master                      |
          |                | WHERE CompId = @CompId                   |
          |                | AND CustomerName LIKE @SearchTerm        |
+-------------------+      | PLUS Key Generation:                     |
|   GridView        |      | dr[6] = fun.GetRandomAlphaNumeric()      |
|   Population      |      +------------------------------------------+
+-------------------+
          |
          |
+-------------------+      +------------------------------------------+
|   User Clicks     |      | GridView Columns:                        |
|  Customer Name    | ---> | " SN (Serial Number)                     |
+-------------------+      | " FinYear                                |
          |                | " Customer Name (HyperLink)              |
          |                | " Code (Customer ID)                     |
+-------------------+      | " Address                                |
|   Navigate to     |      | " Gen. Date, Gen. By                     |
|   Print Details   |      | " Key (Hidden - for session mgmt)       |
+-------------------+      +------------------------------------------+
          |
          |
+-------------------+      +------------------------------------------+
|   Print Page      |      | HyperLink NavigateUrl:                   |
|   Page_Init       | ---> | CustomerMaster_Print_Details.aspx?       |
+-------------------+      | CustomerId={0}&Key={1}&ModId=2&SubModId=7|
          |                +------------------------------------------+
          |
+-------------------+      +------------------------------------------+
|   Load Customer   |      | File: CustomerMaster_Print_Details      |
|   Data            | ---> |       .aspx.cs                          |
+-------------------+      | Method: Page_Init()                      |
          |                |                                          |
          |                | 1. Get Customer ID from QueryString     |
+-------------------+      | 2. Query SD_Cust_master table           |
|   Crystal         |      | 3. Get all address details              |
|   Reports         | ---> | 4. Resolve Country/State/City names     |
|   Processing      |      | 5. Set Report Parameters                |
+-------------------+      +------------------------------------------+
          |
          |
+-------------------+      +------------------------------------------+
|   Address         |      | COMPLEX ADDRESS RESOLUTION:              |
|   Resolution      |      |                                          |
|   Logic           | ---> | For each address (Regd/Work/Del):        |
+-------------------+      | 1. Get CityName from tblCity             |
          |                | 2. Get StateName from tblState          |
          |                | 3. Get CountryName from tblCountry       |
+-------------------+      | 4. Set Crystal Report Parameters        |
|   Set Report      |      |                                          |
|   Parameters      | ---> | Parameters Set:                          |
+-------------------+      | " RegCity, RegState, RegCountry         |
          |                | " WrkCity, WrkState, WrkCountry         |
          |                | " DelCity, DelState, DelCountry         |
+-------------------+      | " RegDate, Company, Address             |
|   Crystal         |      +------------------------------------------+
|   Report          |
|   Loading         |      +------------------------------------------+
+-------------------+      | string reportPath = Server.MapPath(     |
          |                |   "~/Module/SalesDistribution/          |
          |                |    Masters/Reports/CustPrint.rpt");     |
+-------------------+      | report.Load(reportPath);                 |
|   Report          | ---> | report.SetDataSource(ds);               |
|   Display         |      | report.SetParameterValue(...)           |
+-------------------+      | CrystalReportViewer1.ReportSource       |
          |                |   = report;                              |
          |                | Session[Key] = report;                   |
+-------------------+      +------------------------------------------+
|   Print Ready     |
|   User Actions    |      +------------------------------------------+
+-------------------+      | Available Actions:                       |
          |                | " Print Document                         |
          |                | " Export (PDF, Excel, Word)             |
          +--- Print -----> | " Zoom In/Out                           |
          +--- Export ----> | " Navigate Pages                        |
          +--- Cancel ----> | " Cancel (Return to Search)             |
          |                +------------------------------------------+
+-------------------+
|      END          |
|  Back to Search   |
|  or Document      |
|    Printed        |
+-------------------+

                    ALTERNATIVE FLOW: PRINT ALL
+-------------------+      +------------------------------------------+
|  User Clicks      |      | btnPrintAll_Click Event:                 |
|   Print All       | ---> |                                          |
+-------------------+      | string getRandomKey =                    |
          |                |   fun.GetRandomAlphaNumeric();           |
          |                | Response.Redirect(                       |
+-------------------+      |   "Customer_Details_Print_All.aspx?      |
|   Redirect to     | ---> |    ModId=2&SubModId=7&Key=" +            |
|   Print All       |      |    getRandomKey);                        |
+-------------------+      +------------------------------------------+
```

---

## Crystal Reports Integration Details

```
================================================================================
                           CRYSTAL REPORTS INTEGRATION                              
                        File: CustPrint.rpt                                         
================================================================================

REPORT DATA SOURCE:
+------------------+----------------------------------------------------------+
| Table            | SD_Cust_master                                           |
| Query            | SELECT * FROM SD_Cust_master                             |
|                  | WHERE CustomerId=@CustomerId AND CompId=@CompId         |
+------------------+----------------------------------------------------------+

REPORT PARAMETERS:
+------------------+----------------------------------------------------------+
| Parameter Name   | Source Value                                             |
+------------------+----------------------------------------------------------+
| RegCity          | City name from tblCity (Registered Office)              |
| RegState         | State name from tblState (Registered Office)            |
| RegCountry       | Country name from tblCountry (Registered Office)        |
| WrkCity          | City name from tblCity (Work Address)                   |
| WrkState         | State name from tblState (Work Address)                 |
| WrkCountry       | Country name from tblCountry (Work Address)             |
| DelCity          | City name from tblCity (Material Delivery)              |
| DelState         | State name from tblState (Material Delivery)            |
| DelCountry       | Country name from tblCountry (Material Delivery)        |
| RegDate          | Registration date formatted as DD-MMM-YYYY              |
| Company          | Company name from fun.getCompany(CompId)                |
| Address          | Company address from fun.CompAdd(CompId)                |
+------------------+----------------------------------------------------------+

CRYSTAL REPORTS VIEWER CONFIGURATION:
+-----------------------------+---------------------------------------------+
| Property                    | Value                                       |
+-----------------------------+---------------------------------------------+
| HasCrystalLogo              | False                                       |
| AutoDataBind                | True                                        |
| DisplayGroupTree            | False                                       |
| EnableDatabaseLogonPrompt   | False                                       |
| EnableParameterPrompt       | False                                       |
| Width                       | 400px                                       |
| ScrollBars                  | Both (via Panel)                            |
| Height                      | 460px (via Panel)                           |
+-----------------------------+---------------------------------------------+
```

---

## Search AutoComplete Implementation

```
================================================================================
                              AJAX AUTOCOMPLETE                                     
                        (CustomerMaster_Print.aspx)                                 
================================================================================

AUTOCOMPLETE CONFIGURATION:
+===============================================================================+
| <cc1:AutoCompleteExtender ID="TxtSearchValue_AutoCompleteExtender"           |
|     runat="server" CompletionInterval="100" CompletionSetCount="1"           |
|     DelimiterCharacters="" Enabled="True" FirstRowSelected="True"            |
|     MinimumPrefixLength="1" ServiceMethod="sql" ServicePath=""               |
|     ShowOnlyCurrentWordInCompletionListItem="True"                           |
|     TargetControlID="TxtSearchValue" UseContextKey="True"                    |
|     CompletionListItemCssClass="bg"                                          |
|     CompletionListHighlightedItemCssClass="bgtext"                           |
|     CompletionListCssClass="almt">                                           |
+===============================================================================+

WEB METHOD IMPLEMENTATION:
+===============================================================================+
| [WebMethod]                                                                   |
| public static string[] sql(string prefixText, int count, string contextKey)  |
| {                                                                             |
|     // Query: SELECT CustomerId, CustomerName FROM SD_Cust_master            |
|     //        WHERE CompId = @CompId                                          |
|     // Filter: CustomerName.StartsWith(prefixText)                           |
|     // Return: CustomerName + " [" + CustomerId + "]"                        |
|     // Example: "ABC Company Ltd [ABC001]"                                   |
| }                                                                             |
+===============================================================================+
```

---

## Session Management & Memory Optimization

```
================================================================================
                              SESSION MANAGEMENT                                    
================================================================================

KEY GENERATION & SESSION STORAGE:
+===============================================================================+
| // Generate unique key for each print session                                |
| string Key = fun.GetRandomAlphaNumeric();                                    |
|                                                                               |
| // Store report in session with unique key                                   |
| Session[Key] = report;                                                        |
|                                                                               |
| // Pass key in URL for state management                                      |
| NavigateUrl = "CustomerMaster_Print_Details.aspx?                            |
|               CustomerId={0}&Key={1}&ModId=2&SubModId=7"                     |
+===============================================================================+

MEMORY CLEANUP (Page_UnLoad):
+===============================================================================+
| protected void Page_UnLoad(object sender, EventArgs e)                       |
| {                                                                             |
|     this.CrystalReportViewer1.Dispose();                                     |
|     this.CrystalReportViewer1 = null;                                        |
|     report.Close();                                                           |
|     report.Dispose();                                                         |
|     GC.Collect();                                                             |
| }                                                                             |
+===============================================================================+

POSTBACK HANDLING:
+===============================================================================+
| if (!IsPostBack)                                                              |
| {                                                                             |
|     // Initial load: Create new report, set data, store in session           |
| }                                                                             |
| else                                                                          |
| {                                                                             |
|     // PostBack: Retrieve report from session using key                      |
|     Key = Request.QueryString["Key"].ToString();                             |
|     ReportDocument doc = (ReportDocument)Session[Key];                       |
|     CrystalReportViewer1.ReportSource = doc;                                 |
| }                                                                             |
+===============================================================================+
```

This diagram shows the complete Customer Print functionality with Crystal Reports integration, session management, and comprehensive address resolution logic.