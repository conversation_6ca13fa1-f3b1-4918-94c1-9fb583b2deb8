<!-- accounts/partials/payment_mode_row.html -->
<!-- HTMX partial for single PaymentMode row - SAP S/4HANA inspired -->

{% load static %}

<tr class="hover:bg-sap-gray-50 transition-colors duration-150" id="payment-mode-row-{{ payment_mode.id }}">
    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-sap-gray-900">
        {{ payment_mode.id }}
    </td>
    <td class="px-6 py-4">
        <div class="flex items-center">
            <div class="w-8 h-8 bg-sap-green-100 rounded-lg flex items-center justify-center mr-3">
                <i data-lucide="credit-card" class="w-4 h-4 text-sap-green-600"></i>
            </div>
            <div class="text-sm text-sap-gray-900">{{ payment_mode.terms|default:"N/A" }}</div>
        </div>
    </td>
    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div class="flex justify-end space-x-2">
            <!-- Edit Button -->
            <button type="button"
                    hx-get="{% url 'accounts:payment_mode_edit' payment_mode.id %}"
                    hx-target="#payment-mode-row-{{ payment_mode.id }}"
                    hx-swap="outerHTML"
                    class="inline-flex items-center px-3 py-1.5 border border-sap-green-300 rounded text-xs font-medium text-sap-green-700 bg-sap-green-50 hover:bg-sap-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-green-500 transition-colors duration-200">
                <i data-lucide="edit" class="w-3 h-3 mr-1"></i>
                Edit
            </button>
            <!-- Delete Button -->
            <button type="button"
                    hx-delete="{% url 'accounts:payment_mode_delete' payment_mode.id %}"
                    hx-target="#payment-mode-row-{{ payment_mode.id }}"
                    hx-swap="outerHTML"
                    hx-confirm="Are you sure you want to delete this payment mode? This action cannot be undone."
                    class="inline-flex items-center px-3 py-1.5 border border-sap-red-300 rounded text-xs font-medium text-sap-red-700 bg-sap-red-50 hover:bg-sap-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-red-500 transition-colors duration-200">
                <i data-lucide="trash-2" class="w-3 h-3 mr-1"></i>
                Delete
            </button>
        </div>
    </td>
</tr>