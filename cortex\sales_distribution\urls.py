# sales_distribution/urls.py
# URL configuration for Sales Distribution module
# Replaces ASP.NET CategoryEdit.aspx and other SD URLs

from django.urls import path
from . import views
from .views import work_order_views, customer_views

app_name = "sales_distribution"

urlpatterns = [
    # Dashboard
    path("", views.SalesDistributionDashboardView.as_view(), name="dashboard"),
    # Work Order Category URLs (replaces CategoryEdit.aspx and CategoryNew.aspx)
    path("categories/", views.WorkOrderCategoryListView.as_view(), name="category_list"),
    path("categories/new/", views.CategoryNewView.as_view(), name="category_new"),
    path("categories/create/", views.WorkOrderCategoryCreateView.as_view(), name="category_create"),
    path("categories/<int:cid>/edit/", views.WorkOrderCategoryUpdateView.as_view(), name="category_edit"),
    path("categories/<int:cid>/delete/", views.WorkOrderCategoryDeleteView.as_view(), name="category_delete"),
    path("categories/<int:cid>/edit-row/", views.WorkOrderCategoryEditRowView.as_view(), name="category_edit_row"),
    path(
        "categories/<int:cid>/cancel-edit/",
        views.WorkOrderCategoryCancelEditView.as_view(),
        name="category_cancel_edit",
    ),
    # Work Order Sub-Category URLs
    path("subcategories/", views.WorkOrderSubcategoryListView.as_view(), name="subcategory_list"),
    path("subcategories/create/", views.WorkOrderSubcategoryCreateView.as_view(), name="subcategory_create"),
    path("subcategories/<int:sub_cid>/edit/", views.WorkOrderSubcategoryUpdateView.as_view(), name="subcategory_edit"),
    path(
        "subcategories/<int:sub_cid>/delete/", views.WorkOrderSubcategoryDeleteView.as_view(), name="subcategory_delete"
    ),
    path(
        "subcategories/<int:sub_cid>/edit-row/",
        views.WorkOrderSubcategoryEditRowView.as_view(),
        name="subcategory_edit_row",
    ),
    path(
        "subcategories/<int:sub_cid>/cancel-edit/",
        views.WorkOrderSubcategoryCancelEditView.as_view(),
        name="subcategory_cancel_edit",
    ),
    # Work Order Type URLs
    path("wotypes/", views.WOTypeListView.as_view(), name="wotype_list"),
    path("wotypes/create/", views.WOTypeCreateView.as_view(), name="wotype_create"),
    path("wotypes/<int:id>/edit/", views.WOTypeUpdateView.as_view(), name="wotype_edit"),
    path("wotypes/<int:id>/delete/", views.WOTypeDeleteView.as_view(), name="wotype_delete"),
    path("wotypes/<int:id>/edit-row/", views.WOTypeEditRowView.as_view(), name="wotype_edit_row"),
    path("wotypes/<int:id>/cancel-edit/", views.WOTypeCancelEditView.as_view(), name="wotype_cancel_edit"),
    # Customer PO URLs
    path("customer-pos/", views.CustomerPOListView.as_view(), name="customer_po_list"),
    path("customer-pos/create/", views.CustomerPOCreateView.as_view(), name="customer_po_create"),
    path("customer-pos/<int:poid>/update/", views.CustomerPOUpdateView.as_view(), name="customer_po_update"),
    path("customer-pos/<int:poid>/", views.CustomerPODetailView.as_view(), name="customer_po_detail"),
    # Product URLs (replaces Product.aspx)
    path("products/", views.ProductListView.as_view(), name="product_list"),
    path("products/create/", views.ProductCreateView.as_view(), name="product_create"),
    path("products/<int:id>/edit/", views.ProductUpdateView.as_view(), name="product_edit"),
    path("products/<int:id>/delete/", views.ProductDeleteView.as_view(), name="product_delete"),
    path("products/<int:id>/edit-row/", views.ProductEditRowView.as_view(), name="product_edit_row"),
    path("products/<int:id>/cancel-edit/", views.ProductCancelEditView.as_view(), name="product_cancel_edit"),
    # Customer URLs (replaces CustomerMaster_Edit.aspx and CustomerMaster_New.aspx)
    path("customers/", customer_views.CustomerListView.as_view(), name="customer_list"),
    path("customers/new/", customer_views.CustomerCreateView.as_view(), name="customer_new"),
    path("customers/create/", customer_views.CustomerCreateView.as_view(), name="customer_create"),
    path("customers/<int:pk>/edit/", customer_views.CustomerUpdateView.as_view(), name="customer_edit"),
    path("customers/<int:pk>/", customer_views.CustomerDetailView.as_view(), name="customer_detail"),
    # HTMX Ajax endpoints for cascading dropdowns
    path("ajax/states/", customer_views.ajax_states, name="ajax_states"),
    path("ajax/cities/", customer_views.ajax_cities, name="ajax_cities"),
    path("ajax/customer-search/", customer_views.customer_search_ajax, name="customer_search_ajax"),
    # Work Order customer autocomplete
    path("ajax/work-order-customer-autocomplete/", views.CustomerAutocompleteView.as_view(), name="work_order_customer_autocomplete"),
    # Enhanced Enquiry URLs (replaces CustEnquiry_New.aspx with SAP Fiori-inspired UI)
    path("enquiries/", views.enquiry_views.EnquiryListView.as_view(), name="enquiry_list"),
    path("enquiries/create/", views.enquiry_views.EnquiryCreateView.as_view(), name="enquiry_create"),
    # Simple Customer-like Enquiry Form
    path("enquiries/new-simple/", views.enquiry_views.SimpleEnquiryCreateView.as_view(), name="enquiry_new_simple"),
    path("enquiries/<int:enqid>/", views.enquiry_views.EnquiryDetailView.as_view(), name="enquiry_detail"),
    path("enquiries/<int:enqid>/edit/", views.enquiry_views.EnquiryUpdateView.as_view(), name="enquiry_edit"),
    # HTMX Ajax endpoints for enhanced enquiry functionality
    path("ajax/customer-autocomplete/", views.enquiry_views.CustomerAutocompleteView.as_view(), name="customer_autocomplete"),
    path("ajax/get-customer-details/", views.enquiry_views.GetCustomerDetailsView.as_view(), name="get_customer_details"),
    path("ajax/enquiry-stats/", views.enquiry_views.EnquiryStatsView.as_view(), name="enquiry_stats"),
    path("ajax/attachment/<int:attachment_id>/delete/", views.enquiry_views.AttachmentDeleteView.as_view(), name="attachment_delete"),
    # Quotation URLs (replaces Quotation_New.aspx and Quotation_Edit.aspx)
    path("quotations/", views.QuotationListView.as_view(), name="quotation_list"),
    path("quotations/select/", views.QuotationSelectionView.as_view(), name="quotation_selection"),
    path("quotations/create/", views.QuotationCreateFromEnquiryView.as_view(), name="quotation_create_from_enquiry"),
    path("quotations/create/<int:enqid>/", views.QuotationCreateView.as_view(), name="quotation_create"),
    path("quotations/<int:quotation_id>/", views.QuotationDetailView.as_view(), name="quotation_detail"),
    path("quotations/<int:quotation_id>/edit/", views.QuotationUpdateView.as_view(), name="quotation_edit"),
    # HTMX endpoints for quotation line items
    path("ajax/quotation-item-add/", views.QuotationItemAddView.as_view(), name="quotation_item_add"),
    path(
        "ajax/quotation-item-delete/<int:detail_id>/",
        views.QuotationItemDeleteView.as_view(),
        name="quotation_item_delete",
    ),
    # Quotation workflow URLs - List views for workflow management
    path("quotations/check/", views.QuotationCheckListView.as_view(), name="quotation_check_list"),
    path("quotations/approve/", views.QuotationApproveListView.as_view(), name="quotation_approve_list"),
    path("quotations/authorize/", views.QuotationAuthorizeListView.as_view(), name="quotation_authorize_list"),
    # Individual quotation workflow actions
    path("quotations/<int:quotation_id>/check/", views.QuotationCheckView.as_view(), name="quotation_check"),
    path("quotations/<int:quotation_id>/approve/", views.QuotationApproveView.as_view(), name="quotation_approve"),
    path(
        "quotations/<int:quotation_id>/authorize/", views.QuotationAuthorizeView.as_view(), name="quotation_authorize"
    ),
    # Work Order URLs - List and detail views
    path("work-orders/", views.WorkOrderListView.as_view(), name="work_order_list"),
    path("work-orders/<int:pk>/", views.WorkOrderDetailView.as_view(), name="work_order_detail"),
    path("work-orders/<int:pk>/edit/", views.WorkOrderUpdateView.as_view(), name="work_order_edit"),
    
    # Work Order Edit URLs - Comprehensive edit system matching ASP.NET
    path("work-orders/edit/", work_order_views.WorkOrderEditListView.as_view(), name="work_order_edit_list"),
    path("work-orders/<int:pk>/edit-details/", work_order_views.WorkOrderEditDetailsView.as_view(), name="work_order_edit_details"),
    
    # Work Order URLs - Dual mode creation (PO-based or Verbal approval)
    path("work-orders/create/", work_order_views.WorkOrderCreationModeView.as_view(), name="work_order_create"),
    path("work-orders/po-selection/", work_order_views.WorkOrderPOSelectionView.as_view(), name="work_order_po_selection"),
    path("work-orders/create/po/<str:po_no>/<str:customer_id>/<int:enq_id>/<int:po_id>/", 
         work_order_views.WorkOrderCreateMultiStepView.as_view(), name="work_order_create_from_po"),
    path("work-orders/create/verbal/", work_order_views.WorkOrderCreateVerbalView.as_view(), name="work_order_create_verbal"),
    
    # Work Order URLs - Legacy compatibility
    path("work-orders/create-simple/", work_order_views.WorkOrderCreateRedirectView.as_view(), name="work_order_create_simple"),
    
    # Work Order URLs - Legacy simple implementation 
    path("work-orders/simple-create/", views.WorkOrderCreateView.as_view(), name="work_order_simple_create"),
    
    # AJAX endpoints for work order creation
    path("ajax/work-order-customer-autocomplete/", work_order_views.WorkOrderCustomerAutocompleteView.as_view(), name="work_order_customer_autocomplete"),
    path("ajax/work-order-subcategory/", work_order_views.WorkOrderSubcategoryAjaxView.as_view(), name="work_order_subcategory_ajax"),
    path("ajax/work-order-product-delete/<int:product_id>/", work_order_views.WorkOrderProductDeleteView.as_view(), name="work_order_product_delete"),
    
    # AJAX endpoints for work order edit
    path("ajax/work-order-customer-autocomplete-edit/", work_order_views.WorkOrderCustomerAutocompleteEditView.as_view(), name="work_order_customer_autocomplete_edit"),
    path("ajax/work-order-product-edit/<int:work_order_id>/<int:product_id>/", work_order_views.WorkOrderProductEditView.as_view(), name="work_order_product_edit"),
    # Work Order Release URLs
    path("work-orders/release/", views.WorkOrderReleaseListView.as_view(), name="work_order_release_list"),
    path("work-orders/<int:pk>/release/", views.WorkOrderReleaseView.as_view(), name="work_order_release"),
    # Work Order Dispatch URLs
    path("work-orders/dispatch/", views.WorkOrderDispatchListView.as_view(), name="work_order_dispatch_list"),
    path("work-orders/<int:pk>/dispatch/", views.WorkOrderDispatchView.as_view(), name="work_order_dispatch"),
    # Dispatch GunRail URLs
    path("dispatch-gunrail/", views.DispatchGunRailListView.as_view(), name="dispatch_gunrail_list"),
    path("dispatch-gunrail/create/", views.DispatchGunRailCreateView.as_view(), name="dispatch_gunrail_create"),
    # WO Open/Close URLs
    path("work-orders/open-close/", views.WorkOrderOpenCloseListView.as_view(), name="work_order_open_close_list"),
    path("work-orders/<int:pk>/open-close/", views.WorkOrderOpenCloseView.as_view(), name="work_order_open_close"),
    # WO Release & Dispatch Authority URLs
    path("wo-release-dispatch-authority/", views.WOReleaseDispatchAuthorityListView.as_view(), name="wo_release_dispatch_authority_list"),
    path("wo-release-dispatch-authority/create/", views.WOReleaseDispatchAuthorityCreateView.as_view(), name="wo_release_dispatch_authority_create"),
]
