{% extends "core/base.html" %}
{% load static %}

{% block title %}Material Return Note [MRN] - Edit{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Material Return Note [MRN] - Edit</h1>
            </div>
            <div class="flex space-x-2">
                <a href="{% url 'inventory:mrn_create' %}" 
                   class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm">
                    New
                </a>
                <a href="{% url 'inventory:mrn_list' %}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm">
                    List
                </a>
            </div>
        </div>
    </div>


    <!-- Search Section -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="p-4">
            <form method="get" class="flex items-center space-x-4">
                <div class="flex-1">
                    <select name="search_field" 
                            class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                        <option value="employee_name" {% if request.GET.search_field == 'employee_name' %}selected{% endif %}>Employee Name</option>
                        <option value="mrn_no" {% if request.GET.search_field == 'mrn_no' %}selected{% endif %}>MRN No</option>
                    </select>
                </div>
                <div class="flex-1">
                    {% if request.GET.search_field == 'employee_name' %}
                        <select name="search_value" 
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                                hx-get="{% url 'inventory:mrn_list' %}"
                                hx-trigger="change"
                                hx-target="#mrn-results"
                                hx-include="form">
                            <option value="">Select Employee</option>
                            {% for employee in employee_names %}
                                <option value="{{ employee }}" {% if request.GET.search_value == employee %}selected{% endif %}>
                                    {{ employee }}
                                </option>
                            {% endfor %}
                        </select>
                    {% else %}
                        <input type="text" name="search_value" value="{{ request.GET.search_value }}"
                               placeholder="Enter MRN Number"
                               class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                               hx-get="{% url 'inventory:mrn_list' %}"
                               hx-trigger="keyup changed delay:300ms"
                               hx-target="#mrn-results"
                               hx-include="form">
                    {% endif %}
                </div>
                <div>
                    <button type="submit" 
                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded text-sm font-medium">
                        Search
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- MRN Table -->
    <div class="bg-white shadow rounded-lg">
        <div id="mrn-results">
            {% include 'inventory/transactions/partials/mrn_results.html' %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/htmx.org@1.9.6"></script>
{% endblock %}