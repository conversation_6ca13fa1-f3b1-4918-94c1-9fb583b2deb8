# Quality Control Module Implementation Tasks

This document outlines the complete implementation plan for converting the legacy ASP.NET Quality Control module to Django. The module contains 32 files organized into 5 functional groups that need to be systematically converted.

## Overview
- **Total Files**: 32 ASP.NET files (.aspx/.aspx.cs pairs + Web.config files)
- **Django App**: `quality_control/`
- **Implementation Strategy**: Convert each functional group as a cohesive unit
- **Priority**: Start with core quality inspection, then rejections, then reporting

## Module Characteristics
- **Focused Scope**: Smaller, specialized module compared to Accounts/Inventory
- **Integration Heavy**: Integrates closely with Inventory and Accounts modules
- **Document-Centric**: Manages 3 main document types (GQN, MRQN, MCN)
- **Workflow-Oriented**: Strong approval and authorization processes

---

## Task Group 1: Quality Inspection & Testing
**Priority**: HIGH - Core quality control function
**Files**: 8 files (4 .aspx + 4 .aspx.cs)

### Files to Convert:
**Transactions:**
- `Module/QualityControl/Transactions/GoodsQualityNote_GQN_New.aspx` + `.aspx.cs`
- `Module/QualityControl/Transactions/GoodsQualityNote_GQN_New_Details.aspx` + `.aspx.cs`
- `Module/QualityControl/Transactions/GoodsQualityNote_GQN_Edit.aspx` + `.aspx.cs`
- `Module/QualityControl/Transactions/GoodsQualityNote_GQN_Edit_Details.aspx` + `.aspx.cs`

### Implementation Tasks:
- [ ] **Task 1.1**: Create GQN models in `quality_control/models.py`
  - `GoodsQualityNote` (header model)
  - `GQNDetails` (line items with inspection results)
  - `QualityParameter` (inspection criteria)
  - `QualityResult` (pass/fail/conditional results)
- [ ] **Task 1.2**: Create GQN forms with validation
  - `GoodsQualityNoteForm`
  - `GQNDetailsFormSet` for multiple line items
  - Quality parameter validation rules
- [ ] **Task 1.3**: Implement GQN CRUD views
  - `GQNListView` - list all quality notes
  - `GQNCreateView` - create new quality inspection
  - `GQNUpdateView` - edit existing quality note
  - `GQNDetailView` - view quality note details
- [ ] **Task 1.4**: Add quality inspection workflow
  - Draft → Under Inspection → Completed → Approved states
  - Inspector assignment and notifications
  - Quality check escalation rules
- [ ] **Task 1.5**: Integrate with Inventory GRR
  - Link GQN to Goods Received Receipt
  - Auto-populate inspection items from GRR
  - Update GRR status based on quality results
- [ ] **Task 1.6**: Create inspection result management
  - Pass/Fail/Conditional acceptance logic
  - Quality scoring and rating system
  - Defect categorization and tracking
- [ ] **Task 1.7**: Add quality parameter templates
  - Predefined quality checklists by item category
  - Customizable inspection criteria
  - Standard operating procedures integration
- [ ] **Task 1.8**: Create GQN templates with HTMX
  - Quality inspection forms with dynamic criteria
  - Real-time quality scoring
  - Image/document upload for quality evidence
- [ ] **Task 1.9**: Add GQN URL patterns
- [ ] **Task 1.10**: Write comprehensive GQN tests

---

## Task Group 2: Material Return Quality Control
**Priority**: HIGH - Return processing quality assurance
**Files**: 8 files (4 .aspx + 4 .aspx.cs)

### Files to Convert:
**Transactions:**
- `Module/QualityControl/Transactions/MaterialReturnQualityNote_MRQN_New.aspx` + `.aspx.cs`
- `Module/QualityControl/Transactions/MaterialReturnQualityNote_MRQN_New_Details.aspx` + `.aspx.cs`
- `Module/QualityControl/Transactions/MaterialReturnQualityNote_MRQN_Edit.aspx` + `.aspx.cs`
- `Module/QualityControl/Transactions/MaterialReturnQualityNote_MRQN_Edit_Details.aspx` + `.aspx.cs`

### Implementation Tasks:
- [ ] **Task 2.1**: Create MRQN models
  - `MaterialReturnQualityNote` (header model)
  - `MRQNDetails` (return item quality assessment)
  - `ReturnQualityReason` (defect classification)
  - `SupplierQualityRating` (supplier performance tracking)
- [ ] **Task 2.2**: Create MRQN forms with validation
  - `MaterialReturnQualityNoteForm`
  - `MRQNDetailsFormSet` for multiple return items
  - Return reason validation and categorization
- [ ] **Task 2.3**: Implement MRQN CRUD views
  - `MRQNListView` - list all return quality notes
  - `MRQNCreateView` - create quality assessment for returns
  - `MRQNUpdateView` - edit return quality assessment
  - `MRQNDetailView` - view return assessment details
- [ ] **Task 2.4**: Add return quality workflow
  - Return Requested → Quality Assessment → Approved/Rejected → Processed
  - Quality inspector assignment for returns
  - Supplier notification integration
- [ ] **Task 2.5**: Integrate with Inventory MRN
  - Link MRQN to Material Return Note
  - Auto-populate return items for quality assessment
  - Update return status based on quality evaluation
- [ ] **Task 2.6**: Create return quality analysis
  - Root cause analysis for quality issues
  - Defect trend tracking by supplier/item
  - Return cost impact calculation
- [ ] **Task 2.7**: Add supplier quality rating
  - Automatic supplier scoring based on returns
  - Quality performance dashboards
  - Supplier corrective action tracking
- [ ] **Task 2.8**: Create MRQN templates
  - Return quality assessment forms
  - Defect documentation with images
  - Supplier communication templates
- [ ] **Task 2.9**: Add MRQN URL patterns
- [ ] **Task 2.10**: Write MRQN functionality tests

---

## Task Group 3: Goods Rejection Management
**Priority**: HIGH - Critical quality control action
**Files**: 4 files (2 .aspx + 2 .aspx.cs)

### Files to Convert:
**Transactions:**
- `Module/QualityControl/Transactions/GoodsRejection_GRN.aspx` + `.aspx.cs`

**Reports:**
- `Module/QualityControl/Reports/GoodsRejection_GRN.aspx` + `.aspx.cs`

### Implementation Tasks:
- [ ] **Task 3.1**: Create Goods Rejection models
  - `GoodsRejectionNote` (rejection documentation)
  - `RejectionDetails` (rejected item details)
  - `RejectionReason` (standardized rejection codes)
  - `RejectedGoodsInventory` (rejected goods tracking)
- [ ] **Task 3.2**: Create rejection forms
  - `GoodsRejectionForm` with rejection criteria
  - `RejectionDetailsFormSet` for multiple rejected items
  - Supplier notification forms
- [ ] **Task 3.3**: Implement rejection CRUD views
  - `RejectionListView` - list all rejections
  - `RejectionCreateView` - create rejection notice
  - `RejectionUpdateView` - edit rejection details
  - `RejectionDetailView` - view rejection information
- [ ] **Task 3.4**: Add rejection workflow
  - Quality Fail → Rejection Notice → Supplier Notification → Resolution
  - Rejected goods quarantine management
  - Disposal/return-to-supplier tracking
- [ ] **Task 3.5**: Integrate with GRN and GQN
  - Link rejections to failed quality inspections
  - Auto-populate rejection from GQN failures
  - Update GRN status for rejected goods
- [ ] **Task 3.6**: Create rejected goods management
  - Quarantine area tracking
  - Rejection cost calculation
  - Disposal authorization workflow
- [ ] **Task 3.7**: Add supplier impact tracking
  - Rejection rate by supplier
  - Quality issue escalation to procurement
  - Supplier corrective action requests
- [ ] **Task 3.8**: Create rejection report generation
  - Rejection summary reports
  - Trend analysis by item/supplier
  - Cost impact reporting
- [ ] **Task 3.9**: Create rejection templates
  - Rejection notice forms
  - Supplier communication templates
  - Rejection tracking dashboards
- [ ] **Task 3.10**: Add rejection URL patterns
- [ ] **Task 3.11**: Write rejection management tests

---

## Task Group 4: Quality Authorization & Approval
**Priority**: MEDIUM - Workflow control
**Files**: 4 files (2 .aspx + 2 .aspx.cs)

### Files to Convert:
**Transactions:**
- `Module/QualityControl/Transactions/AuthorizedMCN.aspx` + `.aspx.cs`
- `Module/QualityControl/Transactions/AuthorizedMCN_Details.aspx` + `.aspx.cs`

### Implementation Tasks:
- [ ] **Task 4.1**: Create Authorization models
  - `QualityAuthorization` (authorization records)
  - `MCNAuthorization` (Material Credit Note approvals)
  - `QualityApprover` (authorized personnel)
  - `AuthorizationWorkflow` (approval process rules)
- [ ] **Task 4.2**: Create authorization forms
  - `MCNAuthorizationForm` for credit note approvals
  - `QualityApprovalForm` for general quality approvals
  - Digital signature integration
- [ ] **Task 4.3**: Implement authorization views
  - `AuthorizationListView` - pending approvals
  - `AuthorizeView` - approve/reject actions
  - `AuthorizationHistoryView` - approval audit trail
- [ ] **Task 4.4**: Add approval workflow engine
  - Multi-level approval based on amount/impact
  - Automatic routing to appropriate approvers
  - Escalation for delayed approvals
- [ ] **Task 4.5**: Integrate with Accounts MCN
  - Link to Material Credit Note from accounts module
  - Quality-driven credit note creation
  - Financial impact authorization
- [ ] **Task 4.6**: Create authorization dashboard
  - Pending approvals by approver
  - Approval performance metrics
  - Authorization bottleneck identification
- [ ] **Task 4.7**: Add approval notifications
  - Email/SMS notifications for pending approvals
  - Escalation notifications for delays
  - Approval completion confirmations
- [ ] **Task 4.8**: Create authorization templates
  - Approval forms with digital signatures
  - Authorization history views
  - Approval delegation interfaces
- [ ] **Task 4.9**: Add authorization URL patterns
- [ ] **Task 4.10**: Write authorization tests

---

## Task Group 5: Scrap Material Management & Reporting
**Priority**: MEDIUM - Asset recovery and compliance
**Files**: 4 files (2 .aspx + 2 .aspx.cs)

### Files to Convert:
**Reports:**
- `Module/QualityControl/Reports/ScrapMaterial_Report.aspx` + `.aspx.cs`
- `Module/QualityControl/Reports/ScrapMaterial_Report_Details.aspx` + `.aspx.cs`

### Implementation Tasks:
- [ ] **Task 5.1**: Create Scrap Material models
  - `ScrapMaterial` (scrap inventory tracking)
  - `ScrapCategory` (scrap classification)
  - `ScrapDisposal` (disposal tracking)
  - `ScrapValue` (recovery value tracking)
- [ ] **Task 5.2**: Create scrap management forms
  - `ScrapMaterialForm` for scrap designation
  - `ScrapDisposalForm` for disposal authorization
  - Scrap value assessment forms
- [ ] **Task 5.3**: Implement scrap CRUD views
  - `ScrapListView` - all scrap materials
  - `ScrapCreateView` - designate materials as scrap
  - `ScrapUpdateView` - edit scrap records
  - `ScrapDisposalView` - manage disposal process
- [ ] **Task 5.4**: Add scrap workflow
  - Quality Failure → Scrap Designation → Disposal Authorization → Recovery
  - Scrap material segregation tracking
  - Environmental compliance documentation
- [ ] **Task 5.5**: Create scrap reporting engine
  - Scrap quantity and value reports
  - Scrap trend analysis by category
  - Recovery rate tracking
- [ ] **Task 5.6**: Add scrap value management
  - Scrap material valuation
  - Recovery cost tracking
  - Net scrap impact calculation
- [ ] **Task 5.7**: Integrate with quality failures
  - Auto-designation of severely failed materials
  - Link to quality inspection failures
  - Root cause analysis for scrap generation
- [ ] **Task 5.8**: Create scrap analytics
  - Scrap rate by supplier/item
  - Environmental impact tracking
  - Cost of quality calculations
- [ ] **Task 5.9**: Create scrap templates
  - Scrap designation forms
  - Disposal authorization documents
  - Scrap analytics dashboards
- [ ] **Task 5.10**: Add scrap URL patterns
- [ ] **Task 5.11**: Write scrap management tests

---

## Implementation Guidelines

### Technical Requirements:
1. **Models**: Use existing database with `managed = False`
2. **Views**: Only class-based views (ListView, CreateView, UpdateView, DeleteView)
3. **Frontend**: Django templates + HTMX + Alpine.js + Tailwind CSS
4. **Forms**: Include CSRF tokens, proper validation
5. **Authentication**: All views require `LoginRequiredMixin`
6. **Testing**: Both unit tests and Playwright end-to-end tests

### Implementation Order:
1. **Phase 1**: Task Groups 1, 2, 3 (Core Quality Control Functions)
2. **Phase 2**: Task Group 4 (Authorization & Approval)
3. **Phase 3**: Task Group 5 (Scrap Management & Reporting)

### Key Models to Create:
```python
# Core Quality Control Models
class GoodsQualityNote(models.Model):
    # Primary quality inspection document
    
class MaterialReturnQualityNote(models.Model):
    # Return quality assessment
    
class GoodsRejectionNote(models.Model):
    # Rejection documentation
    
class QualityAuthorization(models.Model):
    # Approval workflow management
    
class ScrapMaterial(models.Model):
    # Scrap material tracking
    
# Supporting Models
class QualityParameter(models.Model):
    # Inspection criteria templates
    
class SupplierQualityRating(models.Model):
    # Supplier performance tracking
```

### Integration Points:
```
Quality Control Integration Map:
├── Inventory Module
│   ├── GRR → GQN (Quality Inspection)
│   ├── MRN → MRQN (Return Quality)
│   └── Stock → Scrap Material
├── Accounts Module
│   └── MCN ← Quality Authorization
└── Procurement Module
    └── Supplier Rating ← Quality Performance
```

### File Structure:
```
quality_control/
├── models.py
├── forms.py
├── views.py
├── urls.py
├── admin.py
├── templates/quality_control/
│   ├── inspections/
│   ├── returns/
│   ├── rejections/
│   ├── authorizations/
│   ├── scrap/
│   └── partials/
└── tests.py
```

### Quality Control Workflow:
```
Incoming Goods → GRR → GQN (Quality Inspection)
                        ↓
                   Pass → Accept to Stock
                        ↓
                   Fail → Goods Rejection → Return to Supplier
                   
Stock Issues → Quality Problem → MRQN → Return Assessment
                                  ↓
                             Approve Return → MCN Authorization
                                  ↓
                             Severe Defect → Scrap Material
```

### Success Criteria:
- [ ] All 32 ASP.NET files successfully converted
- [ ] Complete quality traceability implemented
- [ ] Automated quality workflow processing
- [ ] Integration with Inventory and Accounts modules
- [ ] Comprehensive quality reporting
- [ ] Mobile-responsive quality inspection interface
- [ ] Performance optimization completed
- [ ] Security best practices implemented
- [ ] Documentation completed

### Business Benefits:
- Real-time quality status visibility
- Automated supplier quality rating
- Comprehensive rejection tracking
- Streamlined approval workflows
- Integrated scrap material management
- Quality cost analysis and reporting
- Mobile quality inspection capabilities
- Regulatory compliance documentation

**Total Estimated Tasks**: 51 individual implementation tasks across 5 functional groups

### Key Performance Indicators:
- Quality inspection turnaround time
- Supplier quality ratings and trends
- Rejection rates by supplier/item
- Authorization workflow efficiency
- Scrap material recovery rates
- Cost of quality measurements
- Quality issue resolution time