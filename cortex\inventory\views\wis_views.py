from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.urls import reverse_lazy, reverse
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.http import JsonResponse
from django.db.models import Q, Count, Sum, Avg
from django.utils import timezone
from django.db import transaction
import json

from ..models import (
    WISMaster, WISDryRun, WISTransactionLog
)
from ..forms.wis_forms import (
    WISDryRunForm
)
# WISMasterForm commented out due to simplified model field mismatches


# =============================================================================
# WIS Master Views
# =============================================================================

# Commented out due to simplified model - complex views not needed
# class WISMasterListView(LoginRequiredMixin, ListView):
#     """List view for WIS Master records with search and filtering"""
#     model = WISMaster
#     template_name = 'inventory/wis/wis_master_list.html'
#     context_object_name = 'wis_records'
#     paginate_by = 20
#
#     def get_queryset(self):
#         queryset = WISMaster.objects.filter(
#             company=self.request.session.get('company')
#         ).order_by('-sys_date', '-id')
#         
#         # Apply basic search filters
#         search = self.request.GET.get('search')
#         if search:
#             queryset = queryset.filter(
#                 Q(wis_number__icontains=search) |
#                 Q(work_order_number__icontains=search)
#             )
#         
#         return queryset
#
#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         
#         # Basic statistics
#         company = self.request.session.get('company')
#         context['stats'] = {
#             'total': WISMaster.objects.filter(company=company).count(),
#         }
#         return context
#
#     def render_to_response(self, context, **response_kwargs):
#         if self.request.headers.get('HX-Request'):
#             return render(self.request, 'inventory/wis/partials/wis_master_results.html', context)
#         return super().render_to_response(context, **response_kwargs)


# Complex WIS views commented out due to simplified model
# class WISMasterDetailView(LoginRequiredMixin, DetailView):
# class WISMasterCreateView(LoginRequiredMixin, CreateView):
# class WISMasterUpdateView(LoginRequiredMixin, UpdateView):
# class WISMasterDeleteView(LoginRequiredMixin, DeleteView):


# =============================================================================
# WIS Dry Run Views
# =============================================================================

class WISDryRunListView(LoginRequiredMixin, ListView):
    """List view for WIS Dry/Actual Runs showing available work orders"""
    model = WISDryRun
    template_name = 'inventory/wis/wis_dry_run_list.html'
    context_object_name = 'work_orders'
    paginate_by = 17  # Match the original ASP.NET pagination

    def get_queryset(self):
        # Import here to avoid circular imports
        from ..models import WorkOrderMaster
        
        # Get work orders available for WIS processing
        queryset = WorkOrderMaster.objects.filter(
            company=self.request.session.get('company')
        ).order_by('-sys_date', 'wo_no')
        
        # Apply filters
        wo_category = self.request.GET.get('wo_category')
        wo_number = self.request.GET.get('wo_number')
        
        if wo_category:
            queryset = queryset.filter(category=wo_category)
        
        if wo_number:
            queryset = queryset.filter(
                Q(wo_no__icontains=wo_number)
            )
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Add category choices for filter
        context['category_choices'] = [
            ('STANDARD', 'Standard Product'),
            ('NON_STANDARD', 'Non-Standard Product'),
            ('ERECTION', 'Erection & Commissioning'),
            ('SPARES', 'Spares'),
            ('JOB_WORK', 'Job Work'),
            ('AUTOMATION', 'Automation & SPM'),
            ('AGRICULTURE', 'Agriculture Products'),
        ]
        
        return context

    def render_to_response(self, context, **response_kwargs):
        if self.request.headers.get('HX-Request'):
            return render(self.request, 'inventory/wis/partials/wis_dry_run_results.html', context)
        return super().render_to_response(context, **response_kwargs)


class WISDryRunDetailView(LoginRequiredMixin, DetailView):
    """Detail view for WIS Dry Run"""
    model = WISDryRun
    template_name = 'inventory/wis_dry_run_detail.html'
    context_object_name = 'dry_run'

    def get_queryset(self):
        return WISDryRun.objects.filter(
            company=self.request.session.get('company')
        ).select_related('created_by', 'approved_by')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['can_edit'] = self.object.simulation_status in ['DRAFT']
        context['can_run'] = self.object.simulation_status == 'DRAFT'
        context['can_approve'] = (
            self.object.simulation_status == 'COMPLETED' and 
            not self.object.approved_for_execution
        )
        context['can_convert_to_actual'] = (
            self.object.approved_for_execution and 
            self.object.simulation_status == 'COMPLETED'
        )
        return context


class WISDryRunCreateView(LoginRequiredMixin, CreateView):
    """Create view for WIS Dry Run"""
    model = WISDryRun
    form_class = WISDryRunForm
    template_name = 'inventory/wis_dry_run_form.html'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        kwargs['company'] = self.request.session.get('company')
        kwargs['financial_year'] = self.request.session.get('financial_year')
        return kwargs

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f'Dry run {self.object.dry_run_number} created successfully.')
        return response

    def get_success_url(self):
        return reverse('inventory:wis_dry_run_detail', kwargs={'pk': self.object.pk})


# =============================================================================
# Workflow Action Views - Commented out due to simplified model
# =============================================================================

# Complex workflow views commented out due to simplified WISMaster model
# These views reference fields that don't exist in the simplified model

# @login_required
# def wis_start_processing_view(request, pk):
# @login_required
# def wis_complete_processing_view(request, pk):
# @login_required
# def wis_cancel_processing_view(request, pk):


@login_required
def dry_run_execute_view(request, pk):
    """Execute dry run simulation"""
    dry_run = get_object_or_404(
        WISDryRun,
        pk=pk,
        company=request.session.get('company'),
        simulation_status='DRAFT'
    )
    
    if request.method == 'POST':
        with transaction.atomic():
            dry_run.simulation_status = 'RUNNING'
            dry_run.save()
            
            # Simulate processing (in real implementation, this would call actual simulation logic)
            import random
            
            # Simulate some processing time
            # In real implementation, this would be an asynchronous task
            
            # Mock simulation results
            simulation_results = {
                'material_availability': random.choice([True, False]),
                'capacity_constraints': random.randint(0, 3),
                'estimated_duration': random.uniform(4.0, 48.0),
                'estimated_cost': random.uniform(10000, 100000),
                'feasibility_score': random.uniform(60, 95)
            }
            
            dry_run.simulation_status = 'COMPLETED'
            dry_run.feasibility_score = simulation_results['feasibility_score']
            dry_run.estimated_duration_hours = simulation_results['estimated_duration']
            dry_run.estimated_cost = simulation_results['estimated_cost']
            dry_run.simulation_results = json.dumps(simulation_results)
            
            if simulation_results['feasibility_score'] > 80:
                dry_run.recommendations = "High feasibility. Recommend proceeding with actual run."
            elif simulation_results['feasibility_score'] > 60:
                dry_run.recommendations = "Moderate feasibility. Review constraints before proceeding."
            else:
                dry_run.recommendations = "Low feasibility. Address identified issues before proceeding."
            
            dry_run.save()
        
        messages.success(request, f'Dry run {dry_run.dry_run_number} executed successfully.')
        return redirect('inventory:wis_dry_run_detail', pk=dry_run.pk)
    
    return render(request, 'inventory/wis_dry_run_execute_confirm.html', {
        'dry_run': dry_run
    })


@login_required
def dry_run_approve_view(request, pk):
    """Approve dry run for execution"""
    dry_run = get_object_or_404(
        WISDryRun,
        pk=pk,
        company=request.session.get('company'),
        simulation_status='COMPLETED',
        approved_for_execution=False
    )
    
    if request.method == 'POST':
        dry_run.approved_for_execution = True
        dry_run.approved_by = request.user
        dry_run.approved_date = timezone.now()
        dry_run.save()
        
        messages.success(request, f'Dry run {dry_run.dry_run_number} approved for execution.')
        return redirect('inventory:wis_dry_run_detail', pk=dry_run.pk)
    
    return render(request, 'inventory/wis_dry_run_approve_confirm.html', {
        'dry_run': dry_run
    })


# Complex dry run conversion commented out due to simplified model
# @login_required
# def dry_run_convert_to_actual_view(request, pk):
#     """Convert approved dry run to actual WIS - requires complex WISMaster fields"""
#     pass


# =============================================================================
# WIS Release Views
# =============================================================================

class WISReleaseListView(LoginRequiredMixin, ListView):
    """List view for WIS Release - Release WO for WIS"""
    template_name = 'inventory/wis/wis_release_list.html'
    context_object_name = 'work_orders'
    paginate_by = 20

    def get_queryset(self):
        # Import here to avoid circular imports
        from ..models import WorkOrderMaster
        
        # Get work orders available for WIS release
        queryset = WorkOrderMaster.objects.filter(
            company=self.request.session.get('company')
        ).order_by('-sys_date', 'wo_no')
        
        # Apply filters
        wo_category = self.request.GET.get('wo_category')
        wo_number = self.request.GET.get('wo_number')
        
        if wo_category:
            queryset = queryset.filter(category=wo_category)
        
        if wo_number:
            queryset = queryset.filter(
                Q(wo_no__icontains=wo_number)
            )
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Add category choices for filter
        context['category_choices'] = [
            ('STANDARD', 'Standard Product'),
            ('NON_STANDARD', 'Non-Standard Product'),
            ('ERECTION', 'Erection & Commissioning'),
            ('SPARES', 'Spares'),
            ('JOB_WORK', 'Job Work'),
            ('AUTOMATION', 'Automation & SPM'),
            ('AGRICULTURE', 'Agriculture Products'),
        ]
        
        return context

    def render_to_response(self, context, **response_kwargs):
        if self.request.headers.get('HX-Request'):
            return render(self.request, 'inventory/wis/partials/wis_release_results.html', context)
        return super().render_to_response(context, **response_kwargs)


# =============================================================================
# Dashboard and Analytics Views
# =============================================================================

# Dashboard view commented out due to simplified model
# @login_required
# def wis_dashboard_view(request):
#     """WIS Dashboard with analytics - requires complex WIS fields"""
#     pass


# =============================================================================
# API Views
# =============================================================================

@login_required
def wis_assembly_bom_view(request, work_order):
    """WIS Assembly BOM detail view"""
    # Import design models to get BOM data
    try:
        from design.models import BillOfMaterials
        
        # Get BOM items for the work order
        bom_items = BillOfMaterials.objects.filter(
            company=request.session.get('company'),
            work_order_number=work_order
        ).select_related('item_master', 'unit_master').order_by('level', 'sequence')
        
        # Process BOM items for display
        processed_items = []
        for bom in bom_items:
            # Calculate stock quantities and requirements
            item_data = {
                'item_code': bom.item_code or 'N/A',
                'description': bom.description or 'N/A',
                'uom': bom.unit_of_measure or 'NOS',
                'unit_qty': bom.unit_quantity or 1,
                'bom_qty': bom.quantity_required or 1,
                'stock_qty': 0,  # Get from inventory
                'total_wis_qty': 0,  # Calculate from WIS runs
                'dry_run_qty': 0,  # From dry run simulation
                'balance_bom_qty': bom.quantity_required or 1,
                'after_stock_qty': 0,  # Stock - Required
                'level': bom.level or 0,
                'has_children': bom.has_children if hasattr(bom, 'has_children') else False,
            }
            processed_items.append(item_data)
        
    except ImportError:
        # If design models not available, use sample data
        processed_items = [
            {
                'item_code': '00861-00-00',
                'description': 'ASD-12, 3PHASE UNIT',
                'uom': 'NOS',
                'unit_qty': 1,
                'bom_qty': 1,
                'stock_qty': 0,
                'total_wis_qty': 0,
                'dry_run_qty': 0,
                'balance_bom_qty': 1,
                'after_stock_qty': 0,
                'level': 0,
                'has_children': True,
            },
            {
                'item_code': '00862-00-00',
                'description': 'TP-03, 3PHASE PANEL',
                'uom': 'NOS',
                'unit_qty': 1,
                'bom_qty': 1,
                'stock_qty': 0,
                'total_wis_qty': 0,
                'dry_run_qty': 0,
                'balance_bom_qty': 1,
                'after_stock_qty': 0,
                'level': 0,
                'has_children': True,
            },
        ]
    
    context = {
        'work_order': work_order,
        'bom_items': processed_items,
    }
    
    return render(request, 'inventory/wis/wis_assembly_bom.html', context)


# Statistics API commented out due to simplified model
# @login_required
# def wis_statistics_api(request):
#     """API endpoint for WIS statistics - requires complex WIS fields"""
#     pass