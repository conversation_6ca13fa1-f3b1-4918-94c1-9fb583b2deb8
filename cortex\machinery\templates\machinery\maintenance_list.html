{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Maintenance Management{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
            <h1 class="text-2xl font-semibold text-gray-900">Maintenance Management</h1>
            <p class="mt-2 text-sm text-gray-700">Track preventive and breakdown maintenance for your machinery.</p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <a href="{% url 'machinery:maintenance_create' %}" 
               class="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto">
                Add Maintenance Record
            </a>
        </div>
    </div>

    <!-- Alert Cards -->
    {% if overdue_machines or due_soon_machines %}
    <div class="mt-8 grid grid-cols-1 gap-4 sm:grid-cols-2">
        <!-- Overdue Machines -->
        {% if overdue_machines %}
        <div class="bg-red-50 border border-red-200 rounded-lg p-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L5.082 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="ml-3 flex-1">
                    <h3 class="text-sm font-medium text-red-800">Overdue Maintenance</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <p>{{ overdue_machines|length }} machine{{ overdue_machines|length|pluralize }} overdue for maintenance:</p>
                        <ul class="mt-2 space-y-1">
                            {% for item in overdue_machines %}
                            <li class="flex justify-between">
                                <span>Machine {{ item.machine.itemid }}</span>
                                <span class="font-medium">{{ item.days_overdue }} day{{ item.days_overdue|pluralize }} overdue</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Due Soon Machines -->
        {% if due_soon_machines %}
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-3 flex-1">
                    <h3 class="text-sm font-medium text-yellow-800">Maintenance Due Soon</h3>
                    <div class="mt-2 text-sm text-yellow-700">
                        <p>{{ due_soon_machines|length }} machine{{ due_soon_machines|length|pluralize }} due for maintenance within 7 days:</p>
                        <ul class="mt-2 space-y-1">
                            {% for item in due_soon_machines %}
                            <li class="flex justify-between">
                                <span>Machine {{ item.machine.itemid }}</span>
                                <span class="font-medium">Due in {{ item.days_remaining }} day{{ item.days_remaining|pluralize }}</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    {% endif %}

    <!-- Maintenance Records Table -->
    <div class="mt-8 flex flex-col">
        <div class="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
                <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                    <table class="min-w-full divide-y divide-gray-300">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Machine</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Agency/Engineer</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Next PM Due</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="relative px-6 py-3"><span class="sr-only">Actions</span></th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for record in maintenance_records %}
                            <tr class="hover:bg-gray-50 {% if record.is_overdue %}bg-red-50{% elif record.is_due_soon %}bg-yellow-50{% endif %}">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {% if record.machine_info %}
                                        <a href="{% url 'machinery:machine_detail' record.machine_info.pk %}" 
                                           class="text-indigo-600 hover:text-indigo-900">
                                            Machine {{ record.machine_info.itemid }}
                                        </a>
                                        <div class="text-xs text-gray-500">{{ record.machine_info.make }} {{ record.machine_info.model }}</div>
                                    {% else %}
                                        Machine ID: {{ record.machineid }}
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {% if record.pmbm == 0 %}
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                            Preventive
                                        </span>
                                    {% else %}
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                            Breakdown
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <div>{{ record.fromdate|default:"Not set" }}</div>
                                    {% if record.todate and record.todate != record.fromdate %}
                                        <div class="text-xs text-gray-400">to {{ record.todate }}</div>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <div>{{ record.nameofagency|default:"Not specified" }}</div>
                                    {% if record.nameofengineer %}
                                        <div class="text-xs text-gray-400">{{ record.nameofengineer }}</div>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ record.nextpmdueon|default:"Not scheduled" }}
                                    {% if record.days_remaining %}
                                        <div class="text-xs {% if record.is_overdue %}text-red-600{% elif record.is_due_soon %}text-yellow-600{% else %}text-gray-400{% endif %}">
                                            {% if record.is_overdue %}
                                                {{ record.days_remaining|add:"0"|abs }} days overdue
                                            {% elif record.is_due_soon %}
                                                Due in {{ record.days_remaining }} days
                                            {% else %}
                                                {{ record.days_remaining }} days remaining
                                            {% endif %}
                                        </div>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if record.is_overdue %}
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                            Overdue
                                        </span>
                                    {% elif record.is_due_soon %}
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            Due Soon
                                        </span>
                                    {% else %}
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                            On Track
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="{% url 'machinery:maintenance_edit' record.pk %}" 
                                       class="text-indigo-600 hover:text-indigo-900">Edit</a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="7" class="px-6 py-4 text-center text-sm text-gray-500">
                                    No maintenance records found. <a href="{% url 'machinery:maintenance_create' %}" class="text-indigo-600 hover:text-indigo-900">Add the first record</a>.
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="mt-6">
        <nav class="flex items-center justify-between">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}" 
                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Previous
                    </a>
                {% endif %}
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}" 
                       class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Next
                    </a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing <span class="font-medium">{{ page_obj.start_index }}</span> to <span class="font-medium">{{ page_obj.end_index }}</span> of 
                        <span class="font-medium">{{ page_obj.paginator.count }}</span> results
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}" 
                               class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                Previous
                            </a>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-indigo-50 text-sm font-medium text-indigo-600">
                                    {{ num }}
                                </span>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <a href="?page={{ num }}" 
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    {{ num }}
                                </a>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}" 
                               class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                Next
                            </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </nav>
    </div>
    {% endif %}
</div>
{% endblock %}