# Package 1: Master Data Templates Implementation

## Overview
**Priority**: HIGH  
**Effort**: 2-3 days  
**Impact**: Makes existing master data functionality immediately usable  
**Type**: Template creation only (Views already exist)  

## Verification Method
Before starting any template, verify the view exists:
```bash
# Check view exists in views.py
grep -n "class.*View" accounts/views.py | grep -i [ViewName]

# Check URL pattern exists  
grep -n "path.*/" accounts/urls.py | grep -i [url_name]

# Verify no template already exists
find accounts/templates -name "*[template_name]*" -type f
```

## Task List (9 Templates)

### 1. TDS Code Management
**ASP.NET File**: `Module/Accounts/Masters/TDS_Code.aspx`  
**View**: `TDSCodeListView` (VERIFIED EXISTS)  
**URL**: `tds-codes/` (VERIFIED EXISTS)  
**Template**: `accounts/templates/accounts/masters/tds_code_list.html`  
**Form**: `TDSCodeForm` (VERIFIED EXISTS)  

**Features Required**:
- List all TDS codes with rates
- Search/filter by code or description
- Add/Edit/Delete functionality
- Tax rate validation
- Compliance information display

### 2. Interest Type Management  
**ASP.NET File**: `Module/Accounts/Masters/IntrestType.aspx`  
**View**: `InterestTypeListView` (VERIFIED EXISTS)  
**URL**: `interest-types/` (VERIFIED EXISTS)  
**Template**: `accounts/templates/accounts/masters/interest_type_list.html`  
**Form**: `InterestTypeForm` (VERIFIED EXISTS)  

**Features Required**:
- List interest types with rates
- Simple interest vs compound interest
- Rate validation
- Calculation method display

### 3. Loan Type Management
**ASP.NET File**: `Module/Accounts/Masters/LoanType.aspx`  
**View**: `LoanTypeListView` (VERIFIED EXISTS)  
**URL**: `loan-types/` (VERIFIED EXISTS)  
**Template**: `accounts/templates/accounts/masters/loan_type_list.html`  
**Form**: `LoanTypeForm` (VERIFIED EXISTS)  

**Features Required**:
- List loan types with terms
- Interest rate ranges
- Eligibility criteria
- Term validation

### 4. Invoice Against Management
**ASP.NET File**: `Module/Accounts/Masters/InvoiceAgainst.aspx`  
**View**: `InvoiceAgainstListView` (VERIFIED EXISTS)  
**URL**: `invoice-against/` (VERIFIED EXISTS)  
**Template**: `accounts/templates/accounts/masters/invoice_against_list.html`  
**Form**: `InvoiceAgainstForm` (VERIFIED EXISTS)  

**Features Required**:
- List invoice categories
- Invoice type classifications
- Category descriptions
- Usage tracking

### 5. IOU Reasons Management
**ASP.NET File**: `Module/Accounts/Masters/IOU_Reasons.aspx`  
**View**: `IOUReasonsListView` (VERIFIED EXISTS)  
**URL**: `iou-reasons/` (VERIFIED EXISTS)  
**Template**: `accounts/templates/accounts/masters/iou_reasons_list.html`  
**Form**: `IOUReasonsForm` (VERIFIED EXISTS)  

**Features Required**:
- List IOU reason codes
- Approval level requirements
- Documentation requirements
- Limit configurations

### 6. Cheque Series Management
**ASP.NET File**: `Module/Accounts/Masters/Cheque_series.aspx`  
**View**: `ChequeSeriesListView` (VERIFIED EXISTS)  
**URL**: `cheque-series/` (VERIFIED EXISTS)  
**Template**: `accounts/templates/accounts/masters/cheque_series_list.html`  
**Form**: `ChequeSeriesForm` (VERIFIED EXISTS)  

**Features Required**:
- List cheque series by bank
- Range management (start/end numbers)
- Used/unused tracking
- Series validation

### 7. Freight Management
**ASP.NET File**: `Module/Accounts/Masters/Freight.aspx`  
**View**: `FreightListView` (VERIFIED EXISTS)  
**URL**: `freight/` (VERIFIED EXISTS)  
**Template**: `accounts/templates/accounts/masters/freight_list.html`  
**Form**: `FreightForm` (VERIFIED EXISTS)  

**Features Required**:
- List freight rates by destination
- Weight-based calculations
- Distance-based rates
- Carrier information

### 8. Packing & Forwarding Management
**ASP.NET File**: `Module/Accounts/Masters/Packin_Forwarding.aspx`  
**View**: `PackingForwardingListView` (VERIFIED EXISTS)  
**URL**: `packing-forwarding/` (VERIFIED EXISTS)  
**Template**: `accounts/templates/accounts/masters/packing_forwarding_list.html`  
**Form**: `PackingForwardingForm` (VERIFIED EXISTS)  

**Features Required**:
- List packing charges
- Service type definitions
- Rate calculations
- Service provider details

### 9. Warranty Terms Management
**ASP.NET File**: `Module/Accounts/Masters/WarrentyTerms.aspx`  
**View**: `WarrantyTermsListView` (VERIFIED EXISTS)  
**URL**: `warranty-terms/` (VERIFIED EXISTS)  
**Template**: `accounts/templates/accounts/masters/warranty_terms_list.html`  
**Form**: `WarrantyTermsForm` (VERIFIED EXISTS)  

**Features Required**:
- List warranty terms
- Duration specifications
- Coverage details
- Terms & conditions

## Template Requirements

### Standard Features for All Templates:
1. **SAP-inspired UI** with consistent styling
2. **HTMX integration** for dynamic operations
3. **Search and filtering** functionality
4. **Pagination** for large datasets
5. **Add/Edit/Delete** operations
6. **Form validation** with error handling
7. **Responsive design** for all screen sizes

### Template Structure:
```
accounts/templates/accounts/masters/
├── tds_code_list.html
├── interest_type_list.html  
├── loan_type_list.html
├── invoice_against_list.html
├── iou_reasons_list.html
├── cheque_series_list.html
├── freight_list.html
├── packing_forwarding_list.html
└── warranty_terms_list.html
```

## Quality Assurance Checklist

### Before Starting:
- [ ] Verify view class exists in accounts/views.py
- [ ] Verify URL pattern exists in accounts/urls.py  
- [ ] Verify form class exists in accounts/forms.py
- [ ] Confirm no existing template for this functionality

### During Development:
- [ ] Follow SAP-inspired design patterns
- [ ] Include HTMX attributes for dynamic operations
- [ ] Add proper form validation and error handling
- [ ] Ensure responsive design
- [ ] Test all CRUD operations

### After Completion:
- [ ] Template renders without errors
- [ ] All form operations work correctly
- [ ] Search/filter functionality works
- [ ] Pagination works for large datasets
- [ ] Mobile responsive design verified

## Success Criteria
- All 9 templates functional and integrated
- Master data management immediately usable
- Consistent UI/UX across all templates
- No duplication of existing functionality
- Ready for production use

## Dependencies
- Existing view classes (already verified)
- Existing form classes (already verified)  
- Existing URL patterns (already verified)
- SAP-inspired CSS framework (already available)
- HTMX library (already integrated)