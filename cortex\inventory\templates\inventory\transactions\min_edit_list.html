{% extends 'core/base.html' %}
{% load static %}

{% block title %}Material Issue Note [MIN] - Edit{% endblock %}

{% block content %}
<div class="sap-card">
    <!-- SAP Fiori Header -->
    <div class="px-6 py-5 border-b border-sap-gray-200">
        <div class="flex justify-between items-center">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-sap-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <h1 class="text-xl font-semibold text-sap-gray-900">Material Issue Note [MIN] - Edit</h1>
                    <p class="mt-1 text-sm text-sap-gray-600">
                        Search and manage existing Material Issue Notes
                    </p>
                </div>
            </div>
            <a href="{% url 'inventory:min_list' %}" 
               class="sap-button-secondary inline-flex items-center px-4 py-2 text-sm font-medium">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to List
            </a>
        </div>
    </div>

    <!-- SAP Fiori Search Form -->
    <div class="px-6 py-5 bg-white border-b border-sap-gray-200">
        <form method="get" class="flex items-end space-x-4">
            <!-- Search Type Dropdown -->
            <div class="flex-1">
                <label for="search_type" class="block text-sm font-medium text-sap-gray-700 mb-2">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    Search by
                </label>
                <select name="search_type" id="search_type" class="sap-input block w-full">
                    {% for value, label in search_options %}
                        <option value="{{ value }}" {% if search_type == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>

            <!-- Search Value Input -->
            <div class="flex-2">
                <label for="search_value" class="block text-sm font-medium text-sap-gray-700 mb-2">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Search value
                </label>
                <input type="text" name="search_value" id="search_value" value="{{ search_value }}"
                       class="sap-input block w-full"
                       placeholder="Enter search value">
            </div>

            <!-- Search Button -->
            <div>
                <button type="submit" class="sap-button-primary px-6 py-2 text-sm font-medium">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Search
                </button>
            </div>
        </form>
    </div>

    <!-- SAP Fiori MIN Results Table -->
    <div class="sap-card overflow-hidden">
        {% if min_list %}
        <div class="overflow-x-auto">
            <table class="w-full table-auto">
                <thead class="bg-sap-gray-50 border-b border-sap-gray-200">
                    <tr>
                        <th scope="col" class="w-16 px-4 py-3 text-left text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">
                            SN
                        </th>
                        <th scope="col" class="w-24 px-4 py-3 text-left text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">
                            FinYear
                        </th>
                        <th scope="col" class="w-20 px-4 py-3 text-left text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">
                            MIN No
                        </th>
                        <th scope="col" class="w-28 px-4 py-3 text-left text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">
                            Date
                        </th>
                        <th scope="col" class="w-20 px-4 py-3 text-left text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">
                            MRS No
                        </th>
                        <th scope="col" class="w-32 px-4 py-3 text-left text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">
                            Gen. By
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-100">
                    {% for min in min_list %}
                    <tr class="hover:bg-sap-blue-25 transition-colors duration-150 cursor-pointer" 
                        onclick="window.location.href='{% url 'inventory:min_detail' min.pk %}'">
                        <td class="px-4 py-3 text-sm font-medium text-sap-gray-900">
                            {{ forloop.counter0|add:page_obj.start_index|default:forloop.counter }}
                        </td>
                        <td class="px-4 py-3 text-sm text-sap-gray-600">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-sap-blue-100 text-sap-blue-800">
                                {{ min.financial_year.finyear|default:"2022-2023" }}
                            </span>
                        </td>
                        <td class="px-4 py-3 text-sm">
                            <a href="{% url 'inventory:min_detail' min.pk %}" 
                               class="font-medium text-sap-blue-600 hover:text-sap-blue-800 transition-colors duration-150">
                                {{ min.min_no|default:"N/A" }}
                            </a>
                        </td>
                        <td class="px-4 py-3 text-sm text-sap-gray-600">
                            <div class="flex items-center">
                                <svg class="w-4 h-4 mr-2 text-sap-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                {{ min.sysdate|default:"N/A" }}
                            </div>
                        </td>
                        <td class="px-4 py-3 text-sm">
                            <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-sap-gray-100 text-sap-gray-700 border border-sap-gray-200">
                                {{ min.mrs_no|default:"N/A" }}
                            </span>
                        </td>
                        <td class="px-4 py-3 text-sm text-sap-gray-600">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 w-8 h-8 bg-sap-blue-100 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-xs font-medium text-sap-blue-600">
                                        {{ min.sessionid|default:"N"|slice:":2"|upper }}
                                    </span>
                                </div>
                                <span class="truncate">{{ min.sessionid|default:"N/A" }}</span>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- SAP Fiori Pagination -->
        {% if is_paginated %}
        <div class="bg-white px-6 py-4 flex items-center justify-between border-t border-sap-gray-200">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}{% if search_type %}&search_type={{ search_type }}{% endif %}{% if search_value %}&search_value={{ search_value }}{% endif %}" 
                       class="sap-button-secondary inline-flex items-center px-4 py-2 text-sm font-medium">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                        Previous
                    </a>
                {% endif %}
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}{% if search_type %}&search_type={{ search_type }}{% endif %}{% if search_value %}&search_value={{ search_value }}{% endif %}" 
                       class="sap-button-secondary inline-flex items-center px-4 py-2 text-sm font-medium ml-3">
                        Next
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div class="flex items-center text-sm text-sap-gray-600">
                    <svg class="w-4 h-4 mr-2 text-sap-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <span class="font-medium">{{ page_obj.start_index }}-{{ page_obj.end_index }}</span>
                    <span class="mx-1">of</span>
                    <span class="font-medium">{{ page_obj.paginator.count }}</span>
                    <span class="ml-1">items</span>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-lg shadow-sm -space-x-px" aria-label="Pagination">
                        {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}{% if search_type %}&search_type={{ search_type }}{% endif %}{% if search_value %}&search_value={{ search_value }}{% endif %}" 
                               class="relative inline-flex items-center px-2 py-2 rounded-l-lg border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-500 hover:bg-sap-gray-50 transition-colors duration-150">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                            </a>
                        {% endif %}
                        
                        {% for page_num in page_obj.paginator.page_range %}
                            {% if page_num == page_obj.number %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-sap-blue-300 bg-sap-blue-50 text-sap-blue-600 text-sm font-medium">
                                    {{ page_num }}
                                </span>
                            {% else %}
                                <a href="?page={{ page_num }}{% if search_type %}&search_type={{ search_type }}{% endif %}{% if search_value %}&search_value={{ search_value }}{% endif %}" 
                                   class="relative inline-flex items-center px-4 py-2 border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-700 hover:bg-sap-gray-50 transition-colors duration-150">
                                    {{ page_num }}
                                </a>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}{% if search_type %}&search_type={{ search_type }}{% endif %}{% if search_value %}&search_value={{ search_value }}{% endif %}" 
                               class="relative inline-flex items-center px-2 py-2 rounded-r-lg border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-500 hover:bg-sap-gray-50 transition-colors duration-150">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
        {% endif %}

        {% else %}
        <div class="px-6 py-12 text-center">
            <div class="flex flex-col items-center">
                <svg class="w-12 h-12 text-sap-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                <h3 class="text-sm font-medium text-sap-gray-900 mb-1">No MIN records found</h3>
                <p class="text-sm text-sap-gray-500 mb-4">
                    {% if search_value %}
                        Try adjusting your search criteria or browse all records.
                    {% else %}
                        Use the search form above to find specific MIN records.
                    {% endif %}
                </p>
                <a href="{% url 'inventory:min_new_search' %}" 
                   class="sap-button-primary inline-flex items-center px-4 py-2 text-sm font-medium">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Create New MIN
                </a>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}