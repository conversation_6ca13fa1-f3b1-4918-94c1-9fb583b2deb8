{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }} - Sales Distribution{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800">{{ page_title }}</h1>
            <p class="text-gray-600 mt-2">Create or edit work order details</p>
        </div>

        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="{{ form.customerid.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Customer ID
                    </label>
                    {{ form.customerid }}
                    {% if form.customerid.errors %}
                        <div class="text-red-500 text-sm mt-1">{{ form.customerid.errors }}</div>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.wono.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Work Order No
                    </label>
                    {{ form.wono }}
                    {% if form.wono.errors %}
                        <div class="text-red-500 text-sm mt-1">{{ form.wono.errors }}</div>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.taskworkorderdate.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Work Order Date
                    </label>
                    {{ form.taskworkorderdate }}
                    {% if form.taskworkorderdate.errors %}
                        <div class="text-red-500 text-sm mt-1">{{ form.taskworkorderdate.errors }}</div>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.cid.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Category
                    </label>
                    {{ form.cid }}
                    {% if form.cid.errors %}
                        <div class="text-red-500 text-sm mt-1">{{ form.cid.errors }}</div>
                    {% endif %}
                </div>
            </div>

            <div>
                <label for="{{ form.taskprojecttitle.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Project Title
                </label>
                {{ form.taskprojecttitle }}
                {% if form.taskprojecttitle.errors %}
                    <div class="text-red-500 text-sm mt-1">{{ form.taskprojecttitle.errors }}</div>
                {% endif %}
            </div>

            <div class="flex justify-end space-x-4">
                <a href="{% url 'sales_distribution:work_order_list' %}" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                    Cancel
                </a>
                <button type="submit" 
                        class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                    Save Work Order
                </button>
            </div>
        </form>
    </div>
</div>

<style>
/* Style form inputs */
input[type="text"], input[type="email"], input[type="tel"], input[type="date"], select, textarea {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}
</style>
{% endblock %}