from .location_views import (
    ItemLocationListView,
    ItemLocationDetailView,
    ItemLocationCreateView,
    ItemLocationUpdateView,
    ItemLocationDeleteView,
    WISTimeConfigurationListView,
    WISTimeConfigurationDetailView,
    WISTimeConfigurationCreateView,
    WISTimeConfigurationUpdateView,
    WISTimeConfigurationDeleteView
)

from .mrs_views import (
    MRSListView,
    MRSTableListView,
    MRSDetailView,
    MRSCreateView,
    MRSUpdateView,
    MRSDeleteView,
    MRSLineItemCreateView,
    MRSApprovalView,
    mrs_submit_view,
    mrs_print_view,
    mrs_statistics_api
)

from .min_mrn_views import (
    MINListView,
    MINDetailView,
    MINCreateView,
    MINUpdateView,
    MINDeleteView,
    MINNewSearchView,
    MINNewDetailsView,
    MINEditListView,
    MRNListView,
    MRNDetailView,
    MRNCreateView,
    MRNUpdateView,
    MRNDeleteView,
    min_issue_view,
    mrn_process_view,
    min_print_view,
    mrn_print_view,
    min_mrn_statistics_api
)

from .gin_grr_views import (
    GINListView,
    GINDetailView,
    GINCreateView,
    GINUpdateView,
    GINDeleteView,
    GINNewSearchView,
    GINNewPODetailsView,
    GINEditListView,
    GINEditDetailView,
    GRRListView,
    GRRDetailView,
    GRRCreateView,
    GRRUpdateView,
    GRRDeleteView,
    GRREditListView,
    GRREditDetailView,
    gin_receive_view,
    grr_quality_check_view,
    grr_approve_view,
    gin_print_view,
    grr_print_view,
    gin_grr_statistics_api,
    inward_dashboard_view
)

from .challan_views import (
    CustomerChallanListView,
    CustomerChallanDetailView,
    CustomerChallanCreateView,
    CustomerChallanUpdateView,
    CustomerChallanDeleteView,
    SupplierChallanListView,
    SupplierChallanDetailView,
    SupplierChallanCreateView,
    SupplierChallanUpdateView,
    SupplierChallanDeleteView,
    ChallanClearanceListView,
    ChallanClearanceDetailView,
    ChallanClearanceCreateView,
    customer_challan_dispatch_view,
    supplier_challan_receive_view,
    customer_challan_print_view,
    supplier_challan_print_view,
    challan_statistics_api
)

from .wis_views import (
    WISDryRunListView,
    WISDryRunCreateView,
    WISDryRunDetailView,
    WISReleaseListView,
    wis_assembly_bom_view
)

from .movement_tracking_views import (
    InventoryMovementMasterListView,
    InventoryMovementMasterDetailView,
    InventoryMovementMasterCreateView,
    InventoryMovementMasterUpdateView,
    InventoryMovementMasterDeleteView,
    StockLedgerListView,
    StockLedgerDetailView,
    StockLedgerCreateView,
    InventorySnapshotListView,
    InventorySnapshotDetailView,
    InventorySnapshotCreateView,
    movement_authorize_view,
    movement_process_view,
    movement_complete_view,
    movement_cancel_view,
    movement_tracking_dashboard_view,
    movement_tracking_statistics_api,
    generate_inventory_snapshot_api
)

from .mcn_views import (
    MCNListView,
    MCNDetailView,
    MCNCreateView,
    MCNUpdateView,
    MCNDeleteView,
    MCNApprovalView,
    mcn_submit_view,
    mcn_print_view,
    mcn_statistics_api,
    mcn_line_item_search_api
)

from .service_note_views import (
    ServiceNoteListView,
    ServiceNoteDetailView,
    ServiceNoteCreateView,
    ServiceNoteUpdateView,
    ServiceNoteDeleteView,
    service_note_submit_view,
    service_note_approve_view,
    service_note_start_work_view,
    service_note_complete_view,
    service_note_print_view,
    service_note_statistics_api,
    service_item_search_api
)

from .gsn_views import (
    GSNNewListView,
    GSNNewDetailsView,
    GSNEditListView,
    GSNEditDetailsView,
    GSNDeleteListView,
    GSNDeleteDetailsView,
    GSNPrintListView,
    gsn_delete_detail_item,
    gsn_print_view
)

from .reporting_views import (
    InventoryReportsDashboardView,
    ABCAnalysisView,
    MovingNonMovingAnalysisView,
    StockStatementView,
    export_report_view,
    inventory_analytics_api
)

from .search_views import (
    UniversalSearchView,
    search_suggestions_api,
    quick_search_api,
    save_search_api
)

from .api_views import (
    item_search_api,
    item_details_api,
    categories_api,
    locations_api,
    stock_availability_api,
    suppliers_api,
    supplier_details_api,
    purchase_orders_api,
    validate_mrs_submission,
    gin_search_api,
    eligible_pos_api,
    ItemAutocompleteAPI
)

from .mcn_authorization_views import (
    MCNAuthorizationListView,
    MCNAuthorizationDetailView,
    AuthorizedMCNCreateView,
    MCNAuthorizationStatsView,
    MCNAuthorizationAjaxView
)

__all__ = [
    # Location views
    'ItemLocationListView',
    'ItemLocationDetailView', 
    'ItemLocationCreateView',
    'ItemLocationUpdateView',
    'ItemLocationDeleteView',
    'WISTimeConfigurationListView',
    'WISTimeConfigurationDetailView',
    'WISTimeConfigurationCreateView', 
    'WISTimeConfigurationUpdateView',
    'WISTimeConfigurationDeleteView',
    
    # MRS views
    'MRSListView',
    'MRSTableListView',
    'MRSDetailView',
    'MRSCreateView', 
    'MRSUpdateView',
    'MRSDeleteView',
    'MRSLineItemCreateView',
    'MRSApprovalView',
    'mrs_submit_view',
    'mrs_print_view',
    'mrs_statistics_api',
    
    # MIN/MRN views
    'MINListView',
    'MINDetailView',
    'MINCreateView',
    'MINUpdateView',
    'MINDeleteView',
    'MINNewSearchView',
    'MINNewDetailsView',
    'MINEditListView',
    'MRNListView',
    'MRNDetailView',
    'MRNCreateView',
    'MRNUpdateView',
    'MRNDeleteView',
    'min_issue_view',
    'mrn_process_view',
    'min_print_view',
    'mrn_print_view',
    'min_mrn_statistics_api',
    
    # GIN/GRR views
    'GINListView',
    'GINDetailView',
    'GINCreateView',
    'GINUpdateView',
    'GINDeleteView',
    'GINNewSearchView',
    'GINNewPODetailsView',
    'GINEditListView',
    'GINEditDetailView',
    'GRRListView',
    'GRRDetailView',
    'GRRCreateView',
    'GRRUpdateView',
    'GRRDeleteView',
    'GRREditListView',
    'GRREditDetailView',
    'gin_receive_view',
    'grr_quality_check_view',
    'grr_approve_view',
    'gin_print_view',
    'grr_print_view',
    'gin_grr_statistics_api',
    'inward_dashboard_view',
    
    # Challan views
    'CustomerChallanListView',
    'CustomerChallanDetailView',
    'CustomerChallanCreateView',
    'CustomerChallanUpdateView',
    'CustomerChallanDeleteView',
    'SupplierChallanListView',
    'SupplierChallanDetailView',
    'SupplierChallanCreateView',
    'SupplierChallanUpdateView',
    'SupplierChallanDeleteView',
    'ChallanClearanceListView',
    'ChallanClearanceDetailView',
    'ChallanClearanceCreateView',
    'customer_challan_dispatch_view',
    'supplier_challan_receive_view',
    'customer_challan_print_view',
    'supplier_challan_print_view',
    'challan_statistics_api',
    
    # WIS views (partial)
    'WISDryRunListView',
    'WISDryRunCreateView',
    'WISDryRunDetailView',
    'WISReleaseListView',
    'wis_assembly_bom_view',
    
    # Movement Tracking views
    'InventoryMovementMasterListView',
    'InventoryMovementMasterDetailView',
    'InventoryMovementMasterCreateView',
    'InventoryMovementMasterUpdateView',
    'InventoryMovementMasterDeleteView',
    'StockLedgerListView',
    'StockLedgerDetailView',
    'StockLedgerCreateView',
    'InventorySnapshotListView',
    'InventorySnapshotDetailView',
    'InventorySnapshotCreateView',
    'movement_authorize_view',
    'movement_process_view',
    'movement_complete_view',
    'movement_cancel_view',
    'movement_tracking_dashboard_view',
    'movement_tracking_statistics_api',
    'generate_inventory_snapshot_api',
    
    # MCN views
    'MCNListView',
    'MCNDetailView',
    'MCNCreateView',
    'MCNUpdateView',
    'MCNDeleteView',
    'MCNApprovalView',
    'mcn_submit_view',
    'mcn_print_view',
    'mcn_statistics_api',
    'mcn_line_item_search_api',
    
    # Service Note views
    'ServiceNoteListView',
    'ServiceNoteDetailView',
    'ServiceNoteCreateView',
    'ServiceNoteUpdateView',
    'ServiceNoteDeleteView',
    'service_note_submit_view',
    'service_note_approve_view',
    'service_note_start_work_view',
    'service_note_complete_view',
    'service_note_print_view',
    'service_note_statistics_api',
    'service_item_search_api',
    
    # GSN views
    'GSNNewListView',
    'GSNNewDetailsView',
    'GSNEditListView',
    'GSNEditDetailsView',
    'GSNDeleteListView',
    'GSNDeleteDetailsView',
    'GSNPrintListView',
    'gsn_delete_detail_item',
    'gsn_print_view',
    
    # Reporting views
    'InventoryReportsDashboardView',
    'ABCAnalysisView',
    'MovingNonMovingAnalysisView',
    'StockStatementView',
    'export_report_view',
    'inventory_analytics_api',
    
    # Search views
    'UniversalSearchView',
    'search_suggestions_api',
    'quick_search_api',
    'save_search_api',
    
    # API views
    'item_search_api',
    'item_details_api',
    'categories_api',
    'locations_api',
    'stock_availability_api',
    'suppliers_api',
    'supplier_details_api',
    'purchase_orders_api',
    'validate_mrs_submission',
    'gin_search_api',
    'eligible_pos_api',
    'ItemAutocompleteAPI',
    
    # MCN Authorization views
    'MCNAuthorizationListView',
    'MCNAuthorizationDetailView',
    'AuthorizedMCNCreateView',
    'MCNAuthorizationStatsView',
    'MCNAuthorizationAjaxView'
]