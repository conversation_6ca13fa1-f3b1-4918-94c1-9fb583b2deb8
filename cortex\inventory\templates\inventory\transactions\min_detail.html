{% extends 'core/base.html' %}
{% load static %}

{% block title %}MIN {{ min.min_no|default:min.id }} - Material Issue Note{% endblock %}

{% block content %}
<div class="bg-white shadow-sm">
    <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
        <div class="flex justify-between items-center">
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                    Material Issue Note - {{ min.min_no|default:min.id }}
                </h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                    View material issue details and line items
                </p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'inventory:min_update' min.pk %}" 
                   class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit
                </a>
                <a href="{% url 'inventory:min_print' min.pk %}" 
                   class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                   target="_blank">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                    </svg>
                    Print
                </a>
                <a href="{% url 'inventory:min_list' %}" 
                   class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to List
                </a>
            </div>
        </div>
    </div>

    <!-- MIN Header Information -->
    <div class="px-4 py-5 sm:px-6">
        <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div>
                <dt class="text-sm font-medium text-gray-500">MIN Number</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ min.min_no|default:"N/A" }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">MRS Number</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ min.mrs_no|default:"N/A" }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">System Date</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ min.sysdate|default:"N/A" }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">System Time</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ min.systime|default:"N/A" }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Session ID</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ min.sessionid|default:"N/A" }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Company</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ min.company.name|default:"N/A" }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Financial Year</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ min.financial_year.finyear|default:"N/A" }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">MRS ID</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ min.mrs_id|default:"N/A" }}</dd>
            </div>
        </dl>
    </div>

    <!-- MIN Line Items -->
    {% if min.details.exists %}
    <div class="border-t border-gray-200">
        <div class="px-4 py-5 sm:px-6">
            <h4 class="text-lg leading-6 font-medium text-gray-900 mb-4">Material Issue Details</h4>
            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                <table class="w-full table-auto divide-y divide-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide sm:pl-6">
                                ID
                            </th>
                            <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                                MIN Number
                            </th>
                            <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                                MRS ID
                            </th>
                            <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                                Issue Quantity
                            </th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 bg-white">
                        {% for detail in min.details.all %}
                        <tr class="hover:bg-gray-50">
                            <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                                {{ detail.id }}
                            </td>
                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                {{ detail.min_no|default:"N/A" }}
                            </td>
                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                {{ detail.mrs_id|default:"N/A" }}
                            </td>
                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                {{ detail.issue_qty|default:"0.00" }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <div class="text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No line items</h3>
            <p class="mt-1 text-sm text-gray-500">This MIN has no associated line items.</p>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}