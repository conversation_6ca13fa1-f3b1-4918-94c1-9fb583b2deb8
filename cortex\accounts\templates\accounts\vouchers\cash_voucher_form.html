<!-- accounts/templates/accounts/vouchers/cash_voucher_form.html -->
<!-- Cash Voucher Create/Edit Form Template -->
<!-- Task Group 2: Banking & Cash Management - Cash Voucher Form (Task 2.4) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{% if object %}Edit Cash Voucher{% else %}New Cash Voucher{% endif %} - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-green-600 to-sap-green-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="banknote" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">
                        {% if object %}Edit Cash Voucher{% else %}New Cash Voucher{% endif %}
                    </h1>
                    <p class="text-sm text-sap-gray-600 mt-1">
                        {% if object %}Modify cash transaction details{% else %}Record a new cash payment or receipt{% endif %}
                    </p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:cash_voucher_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Cash Voucher Form -->
    <div class="max-w-6xl mx-auto">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="edit" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                    Cash Voucher Information
                </h3>
                <p class="text-sm text-sap-gray-600 mt-1">Complete all required fields to record the cash transaction</p>
            </div>
            
            <form method="post" id="cash-voucher-form" class="p-6" x-data="cashVoucherForm()">
                {% csrf_token %}
                
                <!-- Voucher Header Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="info" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                        Voucher Header
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <!-- Voucher Number -->
                        <div>
                            <label for="{{ form.voucher_number.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Voucher Number
                            </label>
                            {{ form.voucher_number|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.voucher_number.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.voucher_number.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Auto-generated if left blank</p>
                        </div>
                        
                        <!-- Voucher Date -->
                        <div>
                            <label for="{{ form.voucher_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Voucher Date *
                            </label>
                            {{ form.voucher_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.voucher_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.voucher_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Transaction Type -->
                        <div>
                            <label for="{{ form.transaction_type.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Transaction Type *
                            </label>
                            {{ form.transaction_type|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.transaction_type.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.transaction_type.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Cash Account -->
                        <div>
                            <label for="{{ form.cash_account.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Cash Account *
                            </label>
                            {{ form.cash_account|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.cash_account.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.cash_account.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Transaction Details Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="dollar-sign" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Transaction Details
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Account Head -->
                        <div>
                            <label for="{{ form.account_head.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Account Head *
                            </label>
                            {{ form.account_head|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.account_head.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.account_head.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Amount -->
                        <div>
                            <label for="{{ form.amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Amount (₹) *
                            </label>
                            {{ form.amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Description -->
                        <div class="md:col-span-2">
                            <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Description *
                            </label>
                            {{ form.description|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.description.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.description.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Detailed description of the cash transaction</p>
                        </div>
                    </div>
                </div>
                
                <!-- Party/Vendor Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="user" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                        Party/Vendor Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Party Name -->
                        <div>
                            <label for="{{ form.party_name.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Party Name
                            </label>
                            {{ form.party_name|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.party_name.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.party_name.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Party Type -->
                        <div>
                            <label for="{{ form.party_type.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Party Type
                            </label>
                            {{ form.party_type|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.party_type.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.party_type.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Bill/Invoice Number -->
                        <div>
                            <label for="{{ form.bill_number.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Bill/Invoice Number
                            </label>
                            {{ form.bill_number|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.bill_number.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.bill_number.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Payment Details Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="credit-card" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                        Payment Details
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Payment Mode -->
                        <div>
                            <label for="{{ form.payment_mode.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Payment Mode
                            </label>
                            {{ form.payment_mode|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.payment_mode.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.payment_mode.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Reference Number -->
                        <div>
                            <label for="{{ form.reference_number.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Reference Number
                            </label>
                            {{ form.reference_number|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.reference_number.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.reference_number.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Authorization Code -->
                        <div>
                            <label for="{{ form.authorization_code.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Authorization Code
                            </label>
                            {{ form.authorization_code|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.authorization_code.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.authorization_code.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Additional Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="file-text" class="w-5 h-5 mr-2 text-sap-gray-600"></i>
                        Additional Information
                    </h4>
                    <div class="grid grid-cols-1 gap-6">
                        <!-- Remarks -->
                        <div>
                            <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Remarks
                            </label>
                            {{ form.remarks|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.remarks.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.remarks.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Additional notes for this cash voucher</p>
                        </div>
                    </div>
                </div>
                
                <!-- Amount Summary Display -->
                <div class="mb-8 bg-sap-green-50 border border-sap-green-200 rounded-lg p-6" x-show="amount > 0">
                    <h4 class="text-lg font-medium text-sap-green-800 mb-4 flex items-center">
                        <i data-lucide="calculator" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                        Transaction Summary
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="text-center">
                            <p class="text-sm text-sap-green-600">Transaction Type</p>
                            <p class="text-xl font-bold text-sap-green-800" x-text="transactionType"></p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-green-600">Amount</p>
                            <p class="text-xl font-bold text-sap-green-800" x-text="'₹' + amount.toFixed(2)"></p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-green-600">Cash Account</p>
                            <p class="text-xl font-bold text-sap-green-800" x-text="cashAccount"></p>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="resetForm()" 
                                class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="rotate-ccw" class="w-4 h-4 inline mr-2"></i>
                            Reset
                        </button>
                        <button type="button" onclick="previewVoucher()" 
                                class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="eye" class="w-4 h-4 inline mr-2"></i>
                            Preview
                        </button>
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="{% url 'accounts:cash_voucher_list' %}" 
                           class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                            {% if object %}Update Voucher{% else %}Save Voucher{% endif %}
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Cash Voucher Guidelines -->
        <div class="mt-6 bg-sap-blue-50 border border-sap-blue-200 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i data-lucide="info" class="w-5 h-5 text-sap-blue-600"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-sap-blue-800">Cash Voucher Guidelines</h3>
                    <div class="mt-2 text-sm text-sap-blue-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Voucher number will be auto-generated if not provided</li>
                            <li>Ensure proper documentation for all cash transactions</li>
                            <li>Party name is required for transactions above ₹20,000</li>
                            <li>Include relevant bill/invoice numbers for reference</li>
                            <li>Get proper authorization for high-value transactions</li>
                            <li>Maintain supporting documents for audit purposes</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function cashVoucherForm() {
    return {
        amount: 0,
        transactionType: '',
        cashAccount: '',
        
        init() {
            this.updateValues();
        },
        
        updateValues() {
            const amountField = document.getElementById('{{ form.amount.id_for_label }}');
            const typeField = document.getElementById('{{ form.transaction_type.id_for_label }}');
            const accountField = document.getElementById('{{ form.cash_account.id_for_label }}');
            
            if (amountField) this.amount = parseFloat(amountField.value) || 0;
            if (typeField) this.transactionType = typeField.options[typeField.selectedIndex]?.text || '';
            if (accountField) this.cashAccount = accountField.options[accountField.selectedIndex]?.text || '';
        }
    }
}

function resetForm() {
    if (confirm('Are you sure you want to reset the form? All unsaved changes will be lost.')) {
        document.getElementById('cash-voucher-form').reset();
    }
}

function previewVoucher() {
    alert('Voucher preview functionality would be implemented here.');
}

// Auto-generate voucher number if creating new voucher
document.addEventListener('DOMContentLoaded', function() {
    const voucherNumberInput = document.getElementById('{{ form.voucher_number.id_for_label }}');
    if (voucherNumberInput && !voucherNumberInput.value) {
        // Generate voucher number based on current timestamp
        const today = new Date();
        const year = today.getFullYear().toString().substr(-2);
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        const sequence = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        
        voucherNumberInput.value = `CV-${year}${month}${day}${sequence}`;
    }
    
    // Auto-update amount display
    const amountField = document.getElementById('{{ form.amount.id_for_label }}');
    const typeField = document.getElementById('{{ form.transaction_type.id_for_label }}');
    const accountField = document.getElementById('{{ form.cash_account.id_for_label }}');
    
    [amountField, typeField, accountField].forEach(field => {
        if (field) {
            field.addEventListener('change', function() {
                // Trigger Alpine.js update
                const alpineData = Alpine.$data(document.querySelector('[x-data="cashVoucherForm()"]'));
                if (alpineData) {
                    alpineData.updateValues();
                }
            });
        }
    });
    
    lucide.createIcons();
});
</script>
{% endblock %}