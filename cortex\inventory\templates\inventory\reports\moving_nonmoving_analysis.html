{% extends 'base.html' %}
{% load static %}

{% block title %}Moving/Non-Moving Analysis - {{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .movement-fast { background-color: #16a34a; color: white; }
    .movement-medium { background-color: #ea580c; color: white; }
    .movement-slow { background-color: #dc2626; color: white; }
    .movement-dead { background-color: #374151; color: white; }
    .analysis-result {
        transition: all 0.2s ease;
    }
    .analysis-result:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    .chart-container {
        height: 400px;
    }
    .trend-indicator {
        display: inline-flex;
        align-items: center;
    }
    .trend-up { color: #16a34a; }
    .trend-down { color: #dc2626; }
    .trend-stable { color: #6b7280; }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Moving/Non-Moving Analysis</h1>
                <p class="mt-2 text-sm text-gray-600">
                    Identify fast-moving, slow-moving, and dead stock items based on transaction patterns
                </p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'inventory:reports_dashboard' %}" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    Back to Reports
                </a>
                {% if analysis_results %}
                <button type="button" onclick="exportAnalysis()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    Export Results
                </button>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Analysis Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <form method="post" id="movement-analysis-form" class="space-y-6">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Analysis Period (Months)
                    </label>
                    {{ form.analysis_period_months }}
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Fast Moving Threshold (Transactions)
                    </label>
                    {{ form.fast_moving_threshold }}
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Dead Stock Period (Days)
                    </label>
                    {{ form.dead_stock_days }}
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Category Filter (Optional)
                    </label>
                    {{ form.category_filter }}
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Location Filter (Optional)
                    </label>
                    {{ form.location_filter }}
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Minimum Stock Value
                    </label>
                    {{ form.min_stock_value }}
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Maximum Stock Value
                    </label>
                    {{ form.max_stock_value }}
                </div>
            </div>

            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    {{ form.include_zero_stock }}
                    <label class="ml-2 text-sm text-gray-700">{{ form.include_zero_stock.help_text }}</label>
                </div>
                <div class="flex items-center">
                    {{ form.group_by_category }}
                    <label class="ml-2 text-sm text-gray-700">{{ form.group_by_category.help_text }}</label>
                </div>
            </div>

            <div class="flex items-center justify-between">
                <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <span class="inline-flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        Generate Movement Analysis
                    </span>
                </button>
                
                <div class="text-sm text-gray-500">
                    <span class="font-medium">Tip:</span> Fast-moving items typically have 10+ transactions per month
                </div>
            </div>
        </form>
    </div>

    {% if analysis_results %}
    <!-- Analysis Results -->
    <div class="space-y-6">
        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <!-- Fast Moving -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Fast Moving</h3>
                    <span class="movement-fast px-3 py-1 rounded-full text-sm font-medium">ACTIVE</span>
                </div>
                <div class="space-y-2">
                    <div class="text-3xl font-bold text-gray-900">{{ analysis_results.fast_moving.count }}</div>
                    <div class="text-sm text-gray-600">
                        ₹{{ analysis_results.fast_moving.value|floatformat:2 }} total value
                    </div>
                    <div class="text-sm text-gray-600">
                        {{ analysis_results.fast_moving.percentage|floatformat:1 }}% of total items
                    </div>
                    <div class="flex items-center text-sm">
                        <span class="trend-indicator trend-up">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                            {{ analysis_results.fast_moving.avg_transactions|floatformat:1 }} avg txns/month
                        </span>
                    </div>
                </div>
            </div>

            <!-- Medium Moving -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Medium Moving</h3>
                    <span class="movement-medium px-3 py-1 rounded-full text-sm font-medium">MODERATE</span>
                </div>
                <div class="space-y-2">
                    <div class="text-3xl font-bold text-gray-900">{{ analysis_results.medium_moving.count }}</div>
                    <div class="text-sm text-gray-600">
                        ₹{{ analysis_results.medium_moving.value|floatformat:2 }} total value
                    </div>
                    <div class="text-sm text-gray-600">
                        {{ analysis_results.medium_moving.percentage|floatformat:1 }}% of total items
                    </div>
                    <div class="flex items-center text-sm">
                        <span class="trend-indicator trend-stable">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14"></path>
                            </svg>
                            {{ analysis_results.medium_moving.avg_transactions|floatformat:1 }} avg txns/month
                        </span>
                    </div>
                </div>
            </div>

            <!-- Slow Moving -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Slow Moving</h3>
                    <span class="movement-slow px-3 py-1 rounded-full text-sm font-medium">SLOW</span>
                </div>
                <div class="space-y-2">
                    <div class="text-3xl font-bold text-gray-900">{{ analysis_results.slow_moving.count }}</div>
                    <div class="text-sm text-gray-600">
                        ₹{{ analysis_results.slow_moving.value|floatformat:2 }} total value
                    </div>
                    <div class="text-sm text-gray-600">
                        {{ analysis_results.slow_moving.percentage|floatformat:1 }}% of total items
                    </div>
                    <div class="flex items-center text-sm">
                        <span class="trend-indicator trend-down">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                            </svg>
                            {{ analysis_results.slow_moving.avg_transactions|floatformat:1 }} avg txns/month
                        </span>
                    </div>
                </div>
            </div>

            <!-- Dead Stock -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Dead Stock</h3>
                    <span class="movement-dead px-3 py-1 rounded-full text-sm font-medium">DEAD</span>
                </div>
                <div class="space-y-2">
                    <div class="text-3xl font-bold text-gray-900">{{ analysis_results.dead_stock.count }}</div>
                    <div class="text-sm text-gray-600">
                        ₹{{ analysis_results.dead_stock.value|floatformat:2 }} total value
                    </div>
                    <div class="text-sm text-gray-600">
                        {{ analysis_results.dead_stock.percentage|floatformat:1 }}% of total items
                    </div>
                    <div class="flex items-center text-sm">
                        <span class="trend-indicator trend-down">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            {{ analysis_results.dead_stock.days_since_last_transaction|floatformat:0 }} avg days idle
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts and Insights -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Movement Distribution Chart -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Movement Distribution</h3>
                <div class="chart-container">
                    <canvas id="movementChart"></canvas>
                </div>
            </div>

            <!-- Value Distribution Chart -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Value Distribution by Movement</h3>
                <div class="chart-container">
                    <canvas id="valueChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Insights and Recommendations -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Analysis Insights & Recommendations</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-medium text-gray-900 mb-3">Key Insights</h4>
                    <ul class="space-y-2 text-sm text-gray-600">
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                            </svg>
                            {{ analysis_results.insights.active_inventory_percentage|floatformat:1 }}% of inventory is actively moving
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-yellow-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            ₹{{ analysis_results.insights.dead_stock_value|floatformat:2 }} tied up in dead stock
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            Top {{ analysis_results.insights.top_movers_count }} items drive {{ analysis_results.insights.top_movers_value_percentage|floatformat:1 }}% of movement value
                        </li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="font-medium text-gray-900 mb-3">Recommendations</h4>
                    <ul class="space-y-2 text-sm text-gray-600">
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                            </svg>
                            Increase stock levels for fast-moving items to prevent stockouts
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-orange-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            Implement promotional strategies for slow-moving items
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-red-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            Consider liquidation or return to supplier for dead stock
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            Review ordering patterns for medium-moving items
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Detailed Results Table -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">Detailed Movement Analysis</h3>
                    <div class="flex space-x-2">
                        <select id="movement-filter" class="form-select rounded-md border-gray-300 text-sm">
                            <option value="all">All Categories</option>
                            <option value="fast">Fast Moving</option>
                            <option value="medium">Medium Moving</option>
                            <option value="slow">Slow Moving</option>
                            <option value="dead">Dead Stock</option>
                        </select>
                        <input type="text" id="search-items" placeholder="Search items..." class="form-input rounded-md border-gray-300 text-sm">
                    </div>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full table-auto divide-y divide-gray-200" id="results-table">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Item Code
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Description
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Movement Category
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Transactions ({{ analysis_results.parameters.period_months }}M)
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Current Stock
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Stock Value
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Last Transaction
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Days Idle
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for item in analysis_results.items %}
                        <tr class="analysis-result" data-movement="{{ item.movement_category }}">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {{ item.item_code }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ item.item_description|truncatechars:40 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="movement-{{ item.movement_category }} px-2 py-1 rounded-full text-xs font-medium">
                                    {{ item.movement_category|title }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                {{ item.transaction_count }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                {{ item.current_stock|floatformat:2 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                ₹{{ item.stock_value|floatformat:2 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                {{ item.last_transaction_date|date:"M d, Y"|default:"Never" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-right">
                                <span class="{% if item.days_since_last_transaction > 90 %}text-red-600{% elif item.days_since_last_transaction > 30 %}text-yellow-600{% else %}text-green-600{% endif %}">
                                    {{ item.days_since_last_transaction|default:"N/A" }}
                                </span>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="px-6 py-4 text-center text-sm text-gray-500">
                                No items found for the selected criteria.
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    {% if analysis_results %}
    // Initialize Movement Distribution Chart
    const movementCtx = document.getElementById('movementChart').getContext('2d');
    
    const movementData = {
        labels: ['Fast Moving', 'Medium Moving', 'Slow Moving', 'Dead Stock'],
        datasets: [{
            data: [
                {{ analysis_results.fast_moving.count }},
                {{ analysis_results.medium_moving.count }},
                {{ analysis_results.slow_moving.count }},
                {{ analysis_results.dead_stock.count }}
            ],
            backgroundColor: [
                '#16a34a',
                '#ea580c',
                '#dc2626',
                '#374151'
            ],
            borderWidth: 0
        }]
    };
    
    new Chart(movementCtx, {
        type: 'doughnut',
        data: movementData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Initialize Value Distribution Chart
    const valueCtx = document.getElementById('valueChart').getContext('2d');
    
    const valueData = {
        labels: ['Fast Moving', 'Medium Moving', 'Slow Moving', 'Dead Stock'],
        datasets: [{
            label: 'Value (₹)',
            data: [
                {{ analysis_results.fast_moving.value }},
                {{ analysis_results.medium_moving.value }},
                {{ analysis_results.slow_moving.value }},
                {{ analysis_results.dead_stock.value }}
            ],
            backgroundColor: [
                '#16a34a',
                '#ea580c',
                '#dc2626',
                '#374151'
            ],
            borderWidth: 0
        }]
    };
    
    new Chart(valueCtx, {
        type: 'bar',
        data: valueData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '₹' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Table filtering functionality
    const movementFilter = document.getElementById('movement-filter');
    const searchInput = document.getElementById('search-items');
    const tableRows = document.querySelectorAll('#results-table tbody tr[data-movement]');

    function filterTable() {
        const movementValue = movementFilter.value;
        const searchValue = searchInput.value.toLowerCase();

        tableRows.forEach(row => {
            const movement = row.getAttribute('data-movement');
            const itemCode = row.querySelector('td:first-child').textContent.toLowerCase();
            const description = row.querySelector('td:nth-child(2)').textContent.toLowerCase();

            const movementMatch = movementValue === 'all' || movement === movementValue;
            const searchMatch = itemCode.includes(searchValue) || description.includes(searchValue);

            if (movementMatch && searchMatch) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    movementFilter.addEventListener('change', filterTable);
    searchInput.addEventListener('input', filterTable);
    {% endif %}
});

function exportAnalysis() {
    const form = document.createElement('form');
    form.method = 'get';
    form.action = '{% url "inventory:export_report" %}';
    
    const typeInput = document.createElement('input');
    typeInput.type = 'hidden';
    typeInput.name = 'type';
    typeInput.value = 'moving_nonmoving_analysis';
    
    const formatInput = document.createElement('input');
    formatInput.type = 'hidden';
    formatInput.name = 'format';
    formatInput.value = 'excel';
    
    form.appendChild(typeInput);
    form.appendChild(formatInput);
    
    // Add current form parameters
    const currentForm = document.getElementById('movement-analysis-form');
    const formData = new FormData(currentForm);
    for (let [key, value] of formData.entries()) {
        if (key !== 'csrfmiddlewaretoken') {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = key;
            input.value = value;
            form.appendChild(input);
        }
    }
    
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}
</script>
{% endblock %}