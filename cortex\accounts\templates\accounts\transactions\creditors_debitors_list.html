<!-- accounts/templates/accounts/transactions/creditors_debitors_list.html -->
<!-- Creditors Debitors List View Template -->
<!-- Task Package 4: Customer & Creditor Management Templates - Creditors Debitors Consolidated View -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Creditors & Debitors - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-purple-600 to-sap-purple-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="users" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Creditors & Debitors</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Consolidated view of all creditor and debitor accounts</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to Dashboard
                </a>
                <button type="button" onclick="reconcileAccounts()" 
                        class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="check-square" class="w-4 h-4 inline mr-2"></i>
                    Reconcile
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Search and Filter Section -->
    <div class="mb-6 bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4">
            <form method="get" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4" 
                  hx-get="{% url 'accounts:creditors_debitors_list' %}" 
                  hx-target="#creditors-debitors-table-container" 
                  hx-trigger="keyup changed delay:300ms from:input, change from:select"
                  hx-push-url="true">
                
                <!-- Search -->
                <div class="flex-1">
                    <label for="search" class="block text-sm font-medium text-sap-gray-700 mb-1">Search</label>
                    <input type="text" name="search" value="{{ request.GET.search }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500"
                           placeholder="Search by entity name, reference...">
                </div>
                
                <!-- Entity Type Filter -->
                <div>
                    <label for="entity_type" class="block text-sm font-medium text-sap-gray-700 mb-1">Entity Type</label>
                    <select name="entity_type" 
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500">
                        <option value="">All Types</option>
                        <option value="Customer" {% if request.GET.entity_type == 'Customer' %}selected{% endif %}>Customers</option>
                        <option value="Supplier" {% if request.GET.entity_type == 'Supplier' %}selected{% endif %}>Suppliers</option>
                    </select>
                </div>
                
                <!-- Balance Type Filter -->
                <div>
                    <label for="balance_type" class="block text-sm font-medium text-sap-gray-700 mb-1">Balance Type</label>
                    <select name="balance_type" 
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500">
                        <option value="">All Balances</option>
                        <option value="Debit" {% if request.GET.balance_type == 'Debit' %}selected{% endif %}>Debit Balance</option>
                        <option value="Credit" {% if request.GET.balance_type == 'Credit' %}selected{% endif %}>Credit Balance</option>
                    </select>
                </div>
                
                <!-- Date Range -->
                <div>
                    <label for="date_from" class="block text-sm font-medium text-sap-gray-700 mb-1">From Date</label>
                    <input type="date" name="date_from" value="{{ request.GET.date_from }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500">
                </div>
                
                <div>
                    <label for="date_to" class="block text-sm font-medium text-sap-gray-700 mb-1">To Date</label>
                    <input type="date" name="date_to" value="{{ request.GET.date_to }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500">
                </div>
                
                <!-- Clear Button -->
                {% if request.GET %}
                <div>
                    <a href="{% url 'accounts:creditors_debitors_list' %}" 
                       class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                        Clear
                    </a>
                </div>
                {% endif %}
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="users" class="w-6 h-6 text-sap-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Records</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ total_records|default:0 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="user-check" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Customers</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ total_customers|default:0 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="truck" class="w-6 h-6 text-sap-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Suppliers</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ total_suppliers|default:0 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-red-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="trending-down" class="w-6 h-6 text-sap-red-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Receivables</p>
                    <p class="text-2xl font-bold text-sap-red-600">₹{{ total_debit|floatformat:2|default:"0.00" }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="trending-up" class="w-6 h-6 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Payables</p>
                    <p class="text-2xl font-bold text-sap-green-600">₹{{ total_credit|floatformat:2|default:"0.00" }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Aging Analysis Chart -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm mb-6">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <h3 class="text-lg font-medium text-sap-gray-800">Aging Analysis</h3>
        </div>
        <div class="px-6 py-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="text-center p-4 bg-sap-green-50 rounded-lg">
                    <p class="text-2xl font-bold text-sap-green-600">₹0.00</p>
                    <p class="text-sm text-sap-gray-600">0-30 Days</p>
                </div>
                <div class="text-center p-4 bg-sap-yellow-50 rounded-lg">
                    <p class="text-2xl font-bold text-sap-yellow-600">₹0.00</p>
                    <p class="text-sm text-sap-gray-600">31-60 Days</p>
                </div>
                <div class="text-center p-4 bg-sap-orange-50 rounded-lg">
                    <p class="text-2xl font-bold text-sap-orange-600">₹0.00</p>
                    <p class="text-sm text-sap-gray-600">61-90 Days</p>
                </div>
                <div class="text-center p-4 bg-sap-red-50 rounded-lg">
                    <p class="text-2xl font-bold text-sap-red-600">₹0.00</p>
                    <p class="text-sm text-sap-gray-600">90+ Days</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Creditors Debitors Table Container -->
    <div id="creditors-debitors-table-container">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-sap-gray-800">Creditors & Debitors</h3>
                    <div class="flex items-center space-x-2">
                        <button type="button" onclick="exportData()" 
                                class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                            Export
                        </button>
                        <button type="button" onclick="bulkPayment()" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="credit-card" class="w-4 h-4 inline mr-2"></i>
                            Bulk Payment
                        </button>
                        <button type="button" onclick="agingReport()" 
                                class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="pie-chart" class="w-4 h-4 inline mr-2"></i>
                            Aging Report
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-sap-gray-200">
                    <thead class="bg-sap-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                <input type="checkbox" id="select-all" class="rounded border-sap-gray-300 text-sap-purple-600 focus:ring-sap-purple-500">
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Entity Details
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Opening Balance
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Current Balance
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Credit Limit
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Last Transaction
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-sap-gray-200">
                        {% for item in creditors_debitors %}
                        <tr class="hover:bg-sap-gray-50 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" name="selected_entities" value="{{ item.id }}" 
                                       class="rounded border-sap-gray-300 text-sap-purple-600 focus:ring-sap-purple-500">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 rounded-lg flex items-center justify-center text-white font-semibold text-sm
                                        {% if item.entity_type == 'Customer' %}bg-gradient-to-br from-sap-blue-400 to-sap-blue-600
                                        {% else %}bg-gradient-to-br from-sap-orange-400 to-sap-orange-600{% endif %}">
                                        {% if item.entity_type == 'Customer' %}
                                            <i data-lucide="user" class="w-5 h-5"></i>
                                        {% else %}
                                            <i data-lucide="truck" class="w-5 h-5"></i>
                                        {% endif %}
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-sap-gray-900">
                                            <a href="{% url 'accounts:creditors_debitors_detail' item.id %}" 
                                               class="hover:text-sap-purple-600 transition-colors">
                                                {{ item.entity_name|default:"Unknown Entity" }}
                                            </a>
                                        </div>
                                        <div class="text-sm text-sap-gray-500">
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium
                                                {% if item.entity_type == 'Customer' %}bg-sap-blue-100 text-sap-blue-800
                                                {% else %}bg-sap-orange-100 text-sap-orange-800{% endif %}">
                                                {{ item.entity_type|default:"N/A" }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm">
                                    <span class="font-medium {% if item.opening_balance_type == 'Debit' %}text-sap-red-600{% else %}text-sap-green-600{% endif %}">
                                        ₹{{ item.opening_balance|floatformat:2|default:"0.00" }}
                                    </span>
                                    <div class="text-xs text-sap-gray-500">{{ item.opening_balance_type|default:"N/A" }}</div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm">
                                    <span class="font-medium {% if item.current_balance_type == 'Debit' %}text-sap-red-600{% else %}text-sap-green-600{% endif %}">
                                        ₹{{ item.current_balance|floatformat:2|default:"0.00" }}
                                    </span>
                                    <div class="text-xs text-sap-gray-500">{{ item.current_balance_type|default:"N/A" }}</div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-sap-gray-900">
                                    {% if item.credit_limit %}
                                        <span class="font-medium">₹{{ item.credit_limit|floatformat:2 }}</span>
                                        {% if item.current_balance and item.credit_limit %}
                                            {% widthratio item.current_balance item.credit_limit 100 as utilization %}
                                            <div class="w-full bg-sap-gray-200 rounded-full h-1.5 mt-1">
                                                <div class="{% if utilization > 80 %}bg-sap-red-600{% elif utilization > 60 %}bg-sap-orange-500{% else %}bg-sap-green-600{% endif %} h-1.5 rounded-full" 
                                                     style="width: {{ utilization|default:0 }}%"></div>
                                            </div>
                                            <div class="text-xs text-sap-gray-500 mt-1">{{ utilization|default:0 }}% used</div>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-sap-gray-400">No limit</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-sap-gray-900">
                                    {% if item.last_transaction_date %}
                                        {{ item.last_transaction_date|date:"d M Y" }}
                                        {% now "Y-m-d" as today %}
                                        {% if item.last_transaction_date|timesince|slice:":2" > "30" %}
                                            <div class="text-xs text-sap-red-500">{{ item.last_transaction_date|timesince }} ago</div>
                                        {% else %}
                                            <div class="text-xs text-sap-green-500">{{ item.last_transaction_date|timesince }} ago</div>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-sap-gray-400">No transactions</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <!-- Quick Actions Dropdown -->
                                    <div class="relative" x-data="{ open: false }">
                                        <button @click="open = !open" 
                                                class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-3 py-1 rounded-lg text-xs font-medium transition-colors">
                                            <i data-lucide="more-horizontal" class="w-4 h-4 inline mr-1"></i>
                                            Actions
                                        </button>
                                        <div x-show="open" @click.away="open = false" 
                                             class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-sap-gray-200 z-10"
                                             x-transition:enter="transition ease-out duration-200"
                                             x-transition:enter-start="opacity-0 scale-95"
                                             x-transition:enter-end="opacity-100 scale-100"
                                             x-transition:leave="transition ease-in duration-75"
                                             x-transition:leave-start="opacity-100 scale-100"
                                             x-transition:leave-end="opacity-0 scale-95">
                                            <div class="py-1">
                                                <a href="{% url 'accounts:creditors_debitors_detail' item.id %}" 
                                                   class="flex items-center px-4 py-2 text-sm text-sap-gray-700 hover:bg-sap-gray-100">
                                                    <i data-lucide="eye" class="w-4 h-4 mr-2"></i>
                                                    View Details
                                                </a>
                                                <a href="{% url 'accounts:creditors_debitors_transactions' item.id %}" 
                                                   class="flex items-center px-4 py-2 text-sm text-sap-gray-700 hover:bg-sap-gray-100">
                                                    <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                                                    View Transactions
                                                </a>
                                                <button type="button" onclick="makePayment({{ item.id }})" 
                                                        class="flex items-center w-full px-4 py-2 text-sm text-sap-gray-700 hover:bg-sap-gray-100">
                                                    <i data-lucide="credit-card" class="w-4 h-4 mr-2"></i>
                                                    Make Payment
                                                </button>
                                                <button type="button" onclick="sendStatement({{ item.id }})" 
                                                        class="flex items-center w-full px-4 py-2 text-sm text-sap-gray-700 hover:bg-sap-gray-100">
                                                    <i data-lucide="file-text" class="w-4 h-4 mr-2"></i>
                                                    Send Statement
                                                </button>
                                                <div class="border-t border-sap-gray-100"></div>
                                                <button type="button" onclick="reconcileAccount({{ item.id }})" 
                                                        class="flex items-center w-full px-4 py-2 text-sm text-sap-blue-600 hover:bg-sap-blue-50">
                                                    <i data-lucide="check-square" class="w-4 h-4 mr-2"></i>
                                                    Reconcile
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center justify-center">
                                    <i data-lucide="users" class="w-12 h-12 text-sap-gray-400 mb-4"></i>
                                    <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No records found</h3>
                                    <p class="text-sap-gray-500 mb-4">No creditors or debitors match your current filters.</p>
                                    <a href="{% url 'accounts:creditors_debitors_list' %}" 
                                       class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                        <i data-lucide="refresh-cw" class="w-4 h-4 inline mr-2"></i>
                                        Reset Filters
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
            <div class="px-6 py-4 border-t border-sap-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <p class="text-sm text-sap-gray-700">
                            Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} records
                        </p>
                    </div>
                    <div class="flex items-center space-x-2">
                        {% if page_obj.has_previous %}
                            <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}"
                               class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-3 py-2 rounded-lg text-sm font-medium transition-colors">
                                Previous
                            </a>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <span class="bg-sap-purple-600 text-white px-3 py-2 rounded-lg text-sm font-medium">{{ num }}</span>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}"
                                   class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-3 py-2 rounded-lg text-sm font-medium transition-colors">{{ num }}</a>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}"
                               class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-3 py-2 rounded-lg text-sm font-medium transition-colors">
                                Next
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- JavaScript for Interactive Features -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select All Functionality
    const selectAllCheckbox = document.getElementById('select-all');
    const entityCheckboxes = document.querySelectorAll('input[name="selected_entities"]');
    
    selectAllCheckbox?.addEventListener('change', function() {
        entityCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
    
    // Export functionality
    window.exportData = function() {
        const selectedEntities = Array.from(document.querySelectorAll('input[name="selected_entities"]:checked'))
                                       .map(cb => cb.value);
        
        if (selectedEntities.length === 0) {
            alert('Please select entities to export');
            return;
        }
        
        // Create export form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{% url "accounts:creditors_debitors_export" %}';
        
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value;
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrfmiddlewaretoken';
            csrfInput.value = csrfToken;
            form.appendChild(csrfInput);
        }
        
        selectedEntities.forEach(id => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'entity_ids';
            input.value = id;
            form.appendChild(input);
        });
        
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    };
    
    // Bulk payment functionality
    window.bulkPayment = function() {
        const selectedEntities = Array.from(document.querySelectorAll('input[name="selected_entities"]:checked'))
                                       .map(cb => cb.value);
        
        if (selectedEntities.length === 0) {
            alert('Please select entities for bulk payment processing');
            return;
        }
        
        window.location.href = `/accounts/payments/bulk/?entity_ids=${selectedEntities.join(',')}`;
    };
    
    // Aging report functionality
    window.agingReport = function() {
        window.open('/accounts/reports/aging-analysis/', '_blank');
    };
    
    // Reconcile accounts functionality
    window.reconcileAccounts = function() {
        window.location.href = '/accounts/reconciliation/accounts/';
    };
    
    // Individual action functions
    window.makePayment = function(entityId) {
        window.location.href = `/accounts/payments/create/?entity_id=${entityId}`;
    };
    
    window.sendStatement = function(entityId) {
        window.open(`/accounts/reports/entity-statement/${entityId}/`, '_blank');
    };
    
    window.reconcileAccount = function(entityId) {
        window.location.href = `/accounts/reconciliation/entity/${entityId}/`;
    };
});
</script>
{% endblock %}