{% extends 'core/base.html' %}
{% load static %}

{% block title %}Upload Document - MR Office{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                        <svg class="w-8 h-8 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                        </svg>
                        Upload New Document
                    </h1>
                    <p class="text-gray-600 mt-1">Add a new document to the MR Office system</p>
                </div>
                <a href="{% url 'mr_office:document_list' %}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200 flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Documents
                </a>
            </div>
        </div>
    </div>

    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white rounded-lg shadow-sm border p-8">
            <form method="post" enctype="multipart/form-data" hx-post="{% url 'mr_office:document_create' %}" hx-swap="outerHTML">
                {% csrf_token %}
                
                <div class="space-y-6">
                    <!-- Module Selection -->
                    <div>
                        <label for="{{ form.formodule.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Module <span class="text-red-500">*</span>
                        </label>
                        {{ form.formodule }}
                        {% if form.formodule.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.formodule.errors|first }}
                            </div>
                        {% endif %}
                        <p class="mt-1 text-sm text-gray-500">Select the module this document belongs to</p>
                    </div>

                    <!-- Format/Document Name -->
                    <div>
                        <label for="{{ form.format.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Format/Document Name <span class="text-red-500">*</span>
                        </label>
                        {{ form.format }}
                        {% if form.format.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.format.errors|first }}
                            </div>
                        {% endif %}
                        <p class="mt-1 text-sm text-gray-500">Enter a descriptive name for the document or format</p>
                    </div>

                    <!-- File Upload -->
                    <div>
                        <label for="{{ form.attachment.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Attachment <span class="text-red-500">*</span>
                        </label>
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-gray-400 transition-colors duration-200">
                            <div class="space-y-1 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <div class="flex text-sm text-gray-600">
                                    <label for="{{ form.attachment.id_for_label }}" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                        <span>Upload a file</span>
                                        {{ form.attachment }}
                                    </label>
                                    <p class="pl-1">or drag and drop</p>
                                </div>
                                <p class="text-xs text-gray-500">
                                    {{ form.attachment.help_text }}
                                </p>
                            </div>
                        </div>
                        {% if form.attachment.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.attachment.errors|first }}
                            </div>
                        {% endif %}
                    </div>

                    <!-- File Preview -->
                    <div id="file-preview" class="hidden">
                        <div class="rounded-lg border border-gray-200 p-4">
                            <div class="flex items-center">
                                <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900" id="file-name"></p>
                                    <p class="text-sm text-gray-500" id="file-size"></p>
                                </div>
                                <button type="button" class="ml-auto text-gray-400 hover:text-gray-500" onclick="clearFile()">
                                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="mt-8 flex items-center justify-end space-x-4">
                    <a href="{% url 'mr_office:document_list' %}" 
                       class="bg-white border border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors duration-200">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                        </svg>
                        Upload Document
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('{{ form.attachment.id_for_label }}');
    const filePreview = document.getElementById('file-preview');
    const fileName = document.getElementById('file-name');
    const fileSize = document.getElementById('file-size');

    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            filePreview.classList.remove('hidden');
        } else {
            filePreview.classList.add('hidden');
        }
    });

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    window.clearFile = function() {
        fileInput.value = '';
        filePreview.classList.add('hidden');
    };
});
</script>
{% endblock %}