<!-- accounts/templates/accounts/cheque_series_form.html -->
<!-- Cheque Series Form Template -->
<!-- Simple cheque series form matching actual database structure -->

{% extends 'core/base.html' %}

{% block title %}
    {% if object %}Edit{% else %}Add{% endif %} Cheque Series - Accounts
{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-blue-600 to-sap-blue-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">
                        {% if object %}Edit{% else %}Add{% endif %} Cheque Series
                    </h1>
                    <p class="text-sm text-sap-gray-600 mt-1">
                        {% if object %}
                            Update cheque series information
                        {% else %}
                            Create new cheque series
                        {% endif %}
                    </p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:cheque_series_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800">
                    {% if object %}Edit{% else %}Create{% endif %} Cheque Series
                </h3>
            </div>
            
            <div class="p-6">
                <form method="post" class="space-y-6">
                    {% csrf_token %}
                    
                    <!-- Display form errors if any -->
                    {% if form.non_field_errors %}
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <div class="text-sm text-red-700">
                                {{ form.non_field_errors }}
                            </div>
                        </div>
                    {% endif %}
                    
                    <!-- Bank ID Field -->
                    <div>
                        <label for="{{ form.bank_id.id_for_label }}" 
                               class="block text-sm font-medium text-sap-gray-700 mb-2">
                            {{ form.bank_id.label }} *
                        </label>
                        {{ form.bank_id }}
                        {% if form.bank_id.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.bank_id.errors.0 }}
                            </div>
                        {% endif %}
                        {% if form.bank_id.help_text %}
                            <p class="mt-1 text-sm text-sap-gray-500">
                                {{ form.bank_id.help_text }}
                            </p>
                        {% endif %}
                    </div>
                    
                    <!-- Start Number Field -->
                    <div>
                        <label for="{{ form.start_no.id_for_label }}" 
                               class="block text-sm font-medium text-sap-gray-700 mb-2">
                            {{ form.start_no.label }} *
                        </label>
                        {{ form.start_no }}
                        {% if form.start_no.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.start_no.errors.0 }}
                            </div>
                        {% endif %}
                        {% if form.start_no.help_text %}
                            <p class="mt-1 text-sm text-sap-gray-500">
                                {{ form.start_no.help_text }}
                            </p>
                        {% endif %}
                    </div>
                    
                    <!-- End Number Field -->
                    <div>
                        <label for="{{ form.end_no.id_for_label }}" 
                               class="block text-sm font-medium text-sap-gray-700 mb-2">
                            {{ form.end_no.label }} *
                        </label>
                        {{ form.end_no }}
                        {% if form.end_no.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.end_no.errors.0 }}
                            </div>
                        {% endif %}
                        {% if form.end_no.help_text %}
                            <p class="mt-1 text-sm text-sap-gray-500">
                                {{ form.end_no.help_text }}
                            </p>
                        {% endif %}
                    </div>
                    
                    <!-- Form Actions -->
                    <div class="flex items-center justify-end space-x-3 pt-6 border-t border-sap-gray-100">
                        <a href="{% url 'accounts:cheque_series_list' %}" 
                           class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-6 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="save" class="w-4 h-4 inline mr-2"></i>
                            {% if object %}Update{% else %}Create{% endif %} Cheque Series
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}