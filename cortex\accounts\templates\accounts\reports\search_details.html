<!-- accounts/templates/accounts/reports/search_details.html -->
<!-- Search Results Display - SAP S/4HANA Inspired Design -->
<!-- Replaces ASP.NET Search_Details.aspx functionality -->

{% extends 'core/base.html' %}
{% load static %}

{% block title %}Search Results - {{ page_title }} - Cortex{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-sap-green-500 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="list" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Search Results</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">
                        Found {{ results.paginator.count|default:"0" }} matching record{{ results.paginator.count|pluralize }} 
                        {% if search_query %}for "{{ search_query|truncatechars:50 }}"{% endif %}
                    </p>
                </div>
            </div>
            <div class="flex space-x-3">
                <button 
                    hx-get="{% url 'accounts:search_results' %}?{{ request.GET.urlencode }}&export=pdf" 
                    hx-indicator="#export-indicator"
                    class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:ring-offset-2 transition-colors duration-200">
                    <i data-lucide="file-text" class="w-4 h-4 mr-2"></i>
                    Export PDF
                </button>
                <button 
                    hx-get="{% url 'accounts:search_results' %}?{{ request.GET.urlencode }}&export=excel" 
                    hx-indicator="#export-indicator"
                    class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:ring-offset-2 transition-colors duration-200">
                    <i data-lucide="sheet" class="w-4 h-4 mr-2"></i>
                    Export Excel
                </button>
                <button 
                    onclick="window.print()" 
                    class="inline-flex items-center px-4 py-2 bg-sap-green-500 text-white rounded-lg hover:bg-sap-green-600 focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:ring-offset-2 transition-colors duration-200">
                    <i data-lucide="printer" class="w-4 h-4 mr-2"></i>
                    Print
                </button>
                <a 
                    href="{% url 'accounts:advanced_search' %}"
                    class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-sap-gray-500 focus:ring-offset-2 transition-colors duration-200">
                    <i data-lucide="search" class="w-4 h-4 mr-2"></i>
                    New Search
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-2 py-6 space-y-6" id="search-results-content">
    
    <!-- Export Indicator -->
    <div id="export-indicator" class="htmx-indicator">
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 flex items-center space-x-4">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-sap-green-500"></div>
                <span class="text-sap-gray-700">Generating export...</span>
            </div>
        </div>
    </div>

    <!-- Search Summary & Quick Filters -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-sap-gray-800 flex items-center">
                <i data-lucide="filter" class="w-5 h-5 mr-2 text-sap-blue-500"></i>
                Results Summary & Quick Filters
            </h3>
            <button 
                type="button"
                id="toggle-filters"
                class="inline-flex items-center px-3 py-1 border border-sap-gray-300 rounded text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                <i data-lucide="settings" class="w-4 h-4 mr-1"></i>
                Filters
            </button>
        </div>
        
        <!-- Search Criteria Display -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            {% if search_criteria.date_from or search_criteria.date_to %}
            <div class="flex items-center text-sm text-sap-gray-600">
                <i data-lucide="calendar" class="w-4 h-4 mr-2"></i>
                Date: {{ search_criteria.date_from|default:"Any" }} - {{ search_criteria.date_to|default:"Any" }}
            </div>
            {% endif %}
            {% if search_criteria.transaction_type %}
            <div class="flex items-center text-sm text-sap-gray-600">
                <i data-lucide="tag" class="w-4 h-4 mr-2"></i>
                Type: {{ search_criteria.transaction_type|title }}
            </div>
            {% endif %}
            {% if search_criteria.amount_from or search_criteria.amount_to %}
            <div class="flex items-center text-sm text-sap-gray-600">
                <i data-lucide="calculator" class="w-4 h-4 mr-2"></i>
                Amount: ₹{{ search_criteria.amount_from|default:"0" }} - ₹{{ search_criteria.amount_to|default:"∞" }}
            </div>
            {% endif %}
        </div>
        
        <!-- Quick Filters -->
        <div id="quick-filters" class="hidden grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 pt-4 border-t border-sap-gray-200">
            <div>
                <label for="sort_by" class="block text-sm font-medium text-sap-gray-700 mb-1">Sort By</label>
                <select 
                    name="sort_by" 
                    id="sort_by"
                    hx-get="{% url 'accounts:search_results' %}"
                    hx-target="#results-table"
                    hx-include="[name='page']"
                    class="w-full px-3 py-2 text-sm border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                    <option value="date_desc" {% if request.GET.sort_by == "date_desc" %}selected{% endif %}>Date (Newest)</option>
                    <option value="date_asc" {% if request.GET.sort_by == "date_asc" %}selected{% endif %}>Date (Oldest)</option>
                    <option value="amount_desc" {% if request.GET.sort_by == "amount_desc" %}selected{% endif %}>Amount (High-Low)</option>
                    <option value="amount_asc" {% if request.GET.sort_by == "amount_asc" %}selected{% endif %}>Amount (Low-High)</option>
                    <option value="reference" {% if request.GET.sort_by == "reference" %}selected{% endif %}>Reference No.</option>
                </select>
            </div>
            
            <div>
                <label for="filter_type" class="block text-sm font-medium text-sap-gray-700 mb-1">Filter Type</label>
                <select 
                    name="filter_type" 
                    id="filter_type"
                    hx-get="{% url 'accounts:search_results' %}"
                    hx-target="#results-table"
                    hx-include="[name='page']"
                    class="w-full px-3 py-2 text-sm border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                    <option value="">All Types</option>
                    <option value="receipt" {% if request.GET.filter_type == "receipt" %}selected{% endif %}>Receipts</option>
                    <option value="payment" {% if request.GET.filter_type == "payment" %}selected{% endif %}>Payments</option>
                    <option value="transfer" {% if request.GET.filter_type == "transfer" %}selected{% endif %}>Transfers</option>
                </select>
            </div>
            
            <div>
                <label for="filter_amount" class="block text-sm font-medium text-sap-gray-700 mb-1">Amount Range</label>
                <select 
                    name="filter_amount" 
                    id="filter_amount"
                    hx-get="{% url 'accounts:search_results' %}"
                    hx-target="#results-table"
                    hx-include="[name='page']"
                    class="w-full px-3 py-2 text-sm border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                    <option value="">All Amounts</option>
                    <option value="0-1000" {% if request.GET.filter_amount == "0-1000" %}selected{% endif %}>₹0 - ₹1,000</option>
                    <option value="1000-10000" {% if request.GET.filter_amount == "1000-10000" %}selected{% endif %}>₹1,000 - ₹10,000</option>
                    <option value="10000-100000" {% if request.GET.filter_amount == "10000-100000" %}selected{% endif %}>₹10,000 - ₹1,00,000</option>
                    <option value="100000+" {% if request.GET.filter_amount == "100000+" %}selected{% endif %}>₹1,00,000+</option>
                </select>
            </div>
            
            <div>
                <label for="results_per_page" class="block text-sm font-medium text-sap-gray-700 mb-1">Per Page</label>
                <select 
                    name="results_per_page" 
                    id="results_per_page"
                    hx-get="{% url 'accounts:search_results' %}"
                    hx-target="#results-table"
                    class="w-full px-3 py-2 text-sm border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                    <option value="25" {% if request.GET.results_per_page == "25" %}selected{% endif %}>25</option>
                    <option value="50" {% if request.GET.results_per_page == "50" %}selected{% endif %}>50</option>
                    <option value="100" {% if request.GET.results_per_page == "100" %}selected{% endif %}>100</option>
                    <option value="200" {% if request.GET.results_per_page == "200" %}selected{% endif %}>200</option>
                </select>
            </div>
            
            <div class="flex items-end">
                <button 
                    type="button"
                    onclick="clearQuickFilters()"
                    class="w-full px-3 py-2 text-sm border border-sap-gray-300 rounded-lg text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                    Clear Filters
                </button>
            </div>
            
            <div class="flex items-end">
                <button 
                    type="button"
                    onclick="saveResultsAsReport()"
                    class="w-full px-3 py-2 text-sm bg-sap-blue-500 text-white rounded-lg hover:bg-sap-blue-600">
                    Save as Report
                </button>
            </div>
        </div>
    </div>

    <!-- Results Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="file-text" class="w-5 h-5 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-semibold text-sap-gray-900">{{ results.paginator.count|default:"0" }}</p>
                    <p class="text-sm font-medium text-sap-gray-600">Total Records</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="trending-up" class="w-5 h-5 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-semibold text-sap-gray-900">₹ {{ total_amount|floatformat:2|default:"0.00" }}</p>
                    <p class="text-sm font-medium text-sap-gray-600">Total Amount</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="calendar" class="w-5 h-5 text-sap-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-semibold text-sap-gray-900">{{ date_range_days|default:"0" }}</p>
                    <p class="text-sm font-medium text-sap-gray-600">Days Covered</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-red-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="layers" class="w-5 h-5 text-sap-red-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-semibold text-sap-gray-900">{{ unique_accounts|default:"0" }}</p>
                    <p class="text-sm font-medium text-sap-gray-600">Account Heads</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Table -->
    <div id="results-table">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-200 bg-sap-gray-50">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-sap-gray-800 flex items-center">
                        <i data-lucide="table" class="w-5 h-5 mr-2 text-sap-green-500"></i>
                        Search Results
                    </h3>
                    <div class="flex items-center space-x-4">
                        <!-- Bulk Actions -->
                        <div class="flex items-center space-x-2">
                            <input 
                                type="checkbox" 
                                id="select-all"
                                class="rounded border-sap-gray-300 text-sap-green-600 focus:ring-sap-green-500">
                            <label for="select-all" class="text-sm text-sap-gray-600">Select All</label>
                        </div>
                        <button 
                            type="button"
                            id="bulk-export"
                            disabled
                            class="inline-flex items-center px-3 py-1 border border-sap-gray-300 rounded text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 disabled:opacity-50">
                            <i data-lucide="download" class="w-4 h-4 mr-1"></i>
                            Export Selected
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Table Container -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-sap-gray-200">
                    <thead class="bg-sap-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left">
                                <input type="checkbox" class="rounded border-sap-gray-300 text-sap-green-600 focus:ring-sap-green-500">
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider cursor-pointer hover:bg-sap-gray-100"
                                onclick="sortResults('date')">
                                Date
                                <i data-lucide="chevron-up-down" class="w-4 h-4 inline ml-1"></i>
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Type
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Reference
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Particulars
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Account Head
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider cursor-pointer hover:bg-sap-gray-100"
                                onclick="sortResults('amount')">
                                Amount
                                <i data-lucide="chevron-up-down" class="w-4 h-4 inline ml-1"></i>
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-sap-gray-200">
                        {% for result in results %}
                        <tr class="hover:bg-sap-gray-50 transition-colors duration-150">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input 
                                    type="checkbox" 
                                    name="selected_records" 
                                    value="{{ result.id }}"
                                    class="result-checkbox rounded border-sap-gray-300 text-sap-green-600 focus:ring-sap-green-500">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">
                                {{ result.transaction_date|date:"d/m/Y"|default:"-" }}
                                <div class="text-2xs text-sap-gray-500">{{ result.transaction_date|date:"H:i"|default:"-" }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    {% if result.transaction_type == 'receipt' %}bg-sap-green-100 text-sap-green-800
                                    {% elif result.transaction_type == 'payment' %}bg-sap-red-100 text-sap-red-800
                                    {% else %}bg-sap-blue-100 text-sap-blue-800{% endif %}">
                                    {{ result.transaction_type|title|default:"Unknown" }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-sap-blue-600">
                                <a href="{{ result.detail_url }}" class="hover:text-sap-blue-800">
                                    {{ result.reference_number|default:"-" }}
                                </a>
                            </td>
                            <td class="px-6 py-4 text-sm text-sap-gray-900">
                                <div class="max-w-xs truncate" title="{{ result.particulars }}">
                                    {{ result.particulars|default:"-" }}
                                </div>
                                {% if result.party_name %}
                                <div class="text-2xs text-sap-gray-500">{{ result.party_name }}</div>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">
                                {{ result.account_head.account_head_name|default:"-" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-right font-mono">
                                <span class="{% if result.amount %}{% if result.transaction_type == 'receipt' %}text-sap-green-600{% else %}text-sap-red-600{% endif %}{% else %}text-sap-gray-400{% endif %}">
                                    ₹ {{ result.amount|floatformat:2|default:"0.00" }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                <div class="flex items-center justify-center space-x-2">
                                    <a 
                                        href="{{ result.detail_url }}"
                                        class="text-sap-blue-600 hover:text-sap-blue-800 transition-colors duration-150"
                                        title="View Details">
                                        <i data-lucide="eye" class="w-4 h-4"></i>
                                    </a>
                                    {% if result.edit_url %}
                                    <a 
                                        href="{{ result.edit_url }}"
                                        class="text-sap-green-600 hover:text-sap-green-800 transition-colors duration-150"
                                        title="Edit Record">
                                        <i data-lucide="edit-2" class="w-4 h-4"></i>
                                    </a>
                                    {% endif %}
                                    <button 
                                        type="button"
                                        onclick="downloadRecord('{{ result.id }}')"
                                        class="text-sap-orange-600 hover:text-sap-orange-800 transition-colors duration-150"
                                        title="Download">
                                        <i data-lucide="download" class="w-4 h-4"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="px-6 py-8 text-center">
                                <div class="flex flex-col items-center justify-center">
                                    <i data-lucide="search-x" class="w-12 h-12 text-sap-gray-400 mb-4"></i>
                                    <p class="text-lg font-medium text-sap-gray-900 mb-2">No results found</p>
                                    <p class="text-sm text-sap-gray-500">Try adjusting your search criteria or filters</p>
                                    <a 
                                        href="{% url 'accounts:advanced_search' %}"
                                        class="mt-4 inline-flex items-center px-4 py-2 bg-sap-blue-500 text-white rounded-lg hover:bg-sap-blue-600 transition-colors duration-200">
                                        <i data-lucide="search" class="w-4 h-4 mr-2"></i>
                                        Modify Search
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if results.has_other_pages %}
            <div class="px-6 py-4 border-t border-sap-gray-200 bg-sap-gray-50">
                <div class="flex items-center justify-between">
                    <div class="flex items-center text-sm text-sap-gray-700">
                        Showing {{ results.start_index }} to {{ results.end_index }} of {{ results.paginator.count }} results
                    </div>
                    <div class="flex space-x-2">
                        {% if results.has_previous %}
                        <button 
                            hx-get="?page={{ results.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}"
                            hx-target="#results-table"
                            class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Previous
                        </button>
                        {% endif %}
                        
                        <!-- Page Numbers -->
                        {% for num in results.paginator.page_range %}
                            {% if num == results.number %}
                                <span class="px-3 py-2 bg-sap-green-500 text-white rounded-lg text-sm font-medium">{{ num }}</span>
                            {% elif num > results.number|add:'-3' and num < results.number|add:'3' %}
                                <button 
                                    hx-get="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}"
                                    hx-target="#results-table"
                                    class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                                    {{ num }}
                                </button>
                            {% endif %}
                        {% endfor %}
                        
                        {% if results.has_next %}
                        <button 
                            hx-get="?page={{ results.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}"
                            hx-target="#results-table"
                            class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Next
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
    
    // Toggle quick filters
    document.getElementById('toggle-filters').addEventListener('click', function() {
        const filters = document.getElementById('quick-filters');
        filters.classList.toggle('hidden');
        
        const icon = this.querySelector('i');
        if (filters.classList.contains('hidden')) {
            icon.setAttribute('data-lucide', 'settings');
        } else {
            icon.setAttribute('data-lucide', 'x');
        }
        lucide.createIcons();
    });
    
    // Select all functionality
    document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.result-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActions();
    });
    
    // Individual checkbox change
    document.querySelectorAll('.result-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });
    
    function updateBulkActions() {
        const selectedCount = document.querySelectorAll('.result-checkbox:checked').length;
        const bulkExportBtn = document.getElementById('bulk-export');
        
        if (selectedCount > 0) {
            bulkExportBtn.disabled = false;
            bulkExportBtn.textContent = `Export Selected (${selectedCount})`;
        } else {
            bulkExportBtn.disabled = true;
            bulkExportBtn.textContent = 'Export Selected';
        }
    }
    
    // Bulk export functionality
    document.getElementById('bulk-export').addEventListener('click', function() {
        const selected = Array.from(document.querySelectorAll('.result-checkbox:checked'))
                             .map(cb => cb.value);
        
        if (selected.length === 0) {
            alert('Please select records to export');
            return;
        }
        
        // Create form to submit selected IDs
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{% url "accounts:search_results" %}?export=bulk';
        
        // Add CSRF token
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
        if (csrfToken) {
            form.appendChild(csrfToken.cloneNode(true));
        }
        
        // Add selected IDs
        selected.forEach(id => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'selected_ids';
            input.value = id;
            form.appendChild(input);
        });
        
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    });
});

function sortResults(column) {
    const currentSort = new URLSearchParams(window.location.search).get('sort_by');
    let newSort = column + '_asc';
    
    if (currentSort === column + '_asc') {
        newSort = column + '_desc';
    }
    
    const url = new URL(window.location);
    url.searchParams.set('sort_by', newSort);
    url.searchParams.delete('page'); // Reset to first page
    
    htmx.ajax('GET', url.toString(), {target: '#results-table'});
}

function clearQuickFilters() {
    document.getElementById('sort_by').value = 'date_desc';
    document.getElementById('filter_type').value = '';
    document.getElementById('filter_amount').value = '';
    document.getElementById('results_per_page').value = '25';
    
    // Trigger reload
    htmx.ajax('GET', '{% url "accounts:search_results" %}', {target: '#results-table'});
}

function saveResultsAsReport() {
    const reportName = prompt('Enter a name for this report:');
    if (reportName) {
        // Implement save as report functionality
        alert(`Report "${reportName}" saved successfully!`);
    }
}

function downloadRecord(recordId) {
    // Implement individual record download
    window.open(`{% url "accounts:search_results" %}?download=${recordId}`, '_blank');
}

// Print Styles
const printStyles = `
    @media print {
        .no-print { display: none !important; }
        body { print-color-adjust: exact; }
        .bg-white { background: white !important; }
        .text-white { color: black !important; }
        .border { border: 1px solid #000 !important; }
        table { border-collapse: collapse; }
        th, td { border: 1px solid #000 !important; }
        .px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
        .py-6 { padding-top: 1rem; padding-bottom: 1rem; }
    }
`;
const styleSheet = document.createElement("style");
styleSheet.innerText = printStyles;
document.head.appendChild(styleSheet);
</script>
{% endblock %}