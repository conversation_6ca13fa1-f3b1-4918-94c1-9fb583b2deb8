<!-- accounts/templates/accounts/invoices/bill_booking_edit.html -->
<!-- Bill Booking Edit Form Template -->
<!-- ASP.NET Source: Module/Accounts/Transactions/BillBooking_Edit.aspx -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Edit Bill Booking - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-yellow-600 to-sap-yellow-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="edit-3" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">
                        Edit Bill Booking
                    </h1>
                    <p class="text-sm text-sap-gray-600 mt-1">
                        Modify bill details and manage payment scheduling
                    </p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <!-- Approval Status Badge -->
                <span class="px-3 py-1 rounded-full text-xs font-medium
                    {% if object.approval_status == 'pending' %}bg-sap-yellow-100 text-sap-yellow-800{% endif %}
                    {% if object.approval_status == 'approved' %}bg-sap-emerald-100 text-sap-emerald-800{% endif %}
                    {% if object.approval_status == 'rejected' %}bg-sap-red-100 text-sap-red-800{% endif %}
                    {% if object.approval_status == 'on_hold' %}bg-sap-gray-100 text-sap-gray-800{% endif %}">
                    {{ object.get_approval_status_display|default:"Pending" }}
                </span>
                <a href="{% url 'accounts:bill_booking_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Edit Bill Booking Form -->
    <div class="max-w-7xl mx-auto">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                            <i data-lucide="edit-3" class="w-5 h-5 mr-2 text-sap-yellow-600"></i>
                            Bill Booking Modification
                        </h3>
                        <p class="text-sm text-sap-gray-600 mt-1">Update bill details and manage aging analysis</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="showAgingAnalysis()" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="trending-up" class="w-4 h-4 inline mr-2"></i>
                            Aging Analysis
                        </button>
                        <button type="button" onclick="schedulePayment()" 
                                class="bg-sap-emerald-600 hover:bg-sap-emerald-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="calendar" class="w-4 h-4 inline mr-2"></i>
                            Schedule Payment
                        </button>
                    </div>
                </div>
            </div>
            
            <form method="post" id="bill-booking-edit-form" class="p-6" x-data="billBookingEditForm()" hx-post="{% url 'accounts:bill_booking_edit' object.id %}" hx-target="#form-messages">
                {% csrf_token %}
                
                <!-- Form Messages -->
                <div id="form-messages" class="mb-4"></div>
                
                <!-- Bill Status Overview -->
                <div class="mb-6 bg-sap-yellow-50 border border-sap-yellow-200 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-sap-yellow-800 mb-3 flex items-center">
                        <i data-lucide="info" class="w-4 h-4 mr-2"></i>
                        Bill Status Overview
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-6 gap-4 text-sm">
                        <div>
                            <span class="text-sap-yellow-600">Bill Number:</span>
                            <span class="font-medium text-sap-yellow-800">{{ object.bill_number|default:"BILL-001" }}</span>
                        </div>
                        <div>
                            <span class="text-sap-yellow-600">Bill Date:</span>
                            <span class="font-medium text-sap-yellow-800">{{ object.bill_date|date:"M d, Y" }}</span>
                        </div>
                        <div>
                            <span class="text-sap-yellow-600">Due Date:</span>
                            <span class="font-medium text-sap-yellow-800">{{ object.due_date|date:"M d, Y" }}</span>
                        </div>
                        <div>
                            <span class="text-sap-yellow-600">Total Amount:</span>
                            <span class="font-medium text-sap-yellow-800">₹{{ object.total_amount|default:"0.00" }}</span>
                        </div>
                        <div>
                            <span class="text-sap-yellow-600">Amount Paid:</span>
                            <span class="font-medium text-sap-yellow-800">₹{{ object.amount_paid|default:"0.00" }}</span>
                        </div>
                        <div>
                            <span class="text-sap-yellow-600">Days Overdue:</span>
                            <span class="font-medium text-sap-yellow-800">{{ object.days_overdue|default:"0" }}</span>
                        </div>
                    </div>
                </div>
                
                <!-- Vendor Information -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="building-2" class="w-5 h-5 mr-2 text-sap-yellow-600"></i>
                        Vendor Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Vendor Name -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Vendor Name *
                            </label>
                            <input type="text" name="vendor_name" required value="{{ object.vendor_name|default:'' }}"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-yellow-500 focus:border-sap-yellow-500"
                                   placeholder="Enter vendor name" x-model="vendorName">
                        </div>
                        
                        <!-- Vendor Code -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Vendor Code
                            </label>
                            <input type="text" name="vendor_code" value="{{ object.vendor_code|default:'' }}"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-yellow-500 focus:border-sap-yellow-500"
                                   placeholder="Vendor code" x-model="vendorCode">
                        </div>
                        
                        <!-- Vendor GST Number -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Vendor GST Number
                            </label>
                            <input type="text" name="vendor_gst_number" value="{{ object.vendor_gst_number|default:'' }}"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-yellow-500 focus:border-sap-yellow-500"
                                   placeholder="GST number" x-model="vendorGstNumber">
                        </div>
                        
                        <!-- Vendor Address -->
                        <div class="md:col-span-3">
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Vendor Address
                            </label>
                            <textarea rows="2" name="vendor_address"
                                      class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-yellow-500 focus:border-sap-yellow-500 resize-none"
                                      placeholder="Enter vendor address" x-model="vendorAddress">{{ object.vendor_address|default:'' }}</textarea>
                        </div>
                    </div>
                </div>
                
                <!-- Bill Details -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="file-text" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Bill Details
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <!-- Bill Number -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Bill Number *
                            </label>
                            <input type="text" name="bill_number" required value="{{ object.bill_number|default:'' }}"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"
                                   placeholder="Enter bill number">
                        </div>
                        
                        <!-- Bill Date -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Bill Date *
                            </label>
                            <input type="date" name="bill_date" required value="{{ object.bill_date|date:'Y-m-d' }}"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        </div>
                        
                        <!-- Due Date -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Due Date *
                            </label>
                            <input type="date" name="due_date" required value="{{ object.due_date|date:'Y-m-d' }}"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        </div>
                        
                        <!-- Bill Type -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Bill Type *
                            </label>
                            <select name="bill_type" required
                                    class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                                <option value="">Select bill type</option>
                                <option value="purchase" {% if object.bill_type == 'purchase' %}selected{% endif %}>Purchase Bill</option>
                                <option value="service" {% if object.bill_type == 'service' %}selected{% endif %}>Service Bill</option>
                                <option value="expense" {% if object.bill_type == 'expense' %}selected{% endif %}>Expense Bill</option>
                                <option value="utility" {% if object.bill_type == 'utility' %}selected{% endif %}>Utility Bill</option>
                                <option value="rent" {% if object.bill_type == 'rent' %}selected{% endif %}>Rent Bill</option>
                                <option value="other" {% if object.bill_type == 'other' %}selected{% endif %}>Other</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- Account Allocation Changes -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="layers" class="w-5 h-5 mr-2 text-sap-emerald-600"></i>
                        Account Allocation Changes
                        <button type="button" @click="recalculateAccounts()" 
                                class="ml-auto bg-sap-emerald-100 hover:bg-sap-emerald-200 text-sap-emerald-700 px-3 py-1 rounded text-sm font-medium transition-colors">
                            <i data-lucide="calculator" class="w-3 h-3 inline mr-1"></i>
                            Recalculate
                        </button>
                    </h4>
                    
                    <!-- Account Allocations Table -->
                    <div class="border border-sap-gray-300 rounded-lg overflow-hidden">
                        <table class="min-w-full divide-y divide-sap-gray-200" id="accounts-table">
                            <thead class="bg-sap-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Account Head</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Description</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Original Amount</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">New Amount</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Tax Rate</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Tax Amount</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Total</th>
                                    <th class="px-4 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Action</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-sap-gray-200" x-ref="accountsTableBody">
                                <template x-for="(account, index) in accounts" :key="index">
                                    <tr>
                                        <td class="px-4 py-3">
                                            <select x-model="account.account_head" 
                                                    class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-emerald-500">
                                                <option value="">Select account</option>
                                                <option value="raw_materials">Raw Materials</option>
                                                <option value="office_supplies">Office Supplies</option>
                                                <option value="utilities">Utilities</option>
                                                <option value="rent">Rent</option>
                                                <option value="maintenance">Maintenance</option>
                                                <option value="professional_fees">Professional Fees</option>
                                                <option value="transportation">Transportation</option>
                                                <option value="other_expenses">Other Expenses</option>
                                            </select>
                                        </td>
                                        <td class="px-4 py-3">
                                            <input type="text" x-model="account.description" 
                                                   class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-emerald-500" 
                                                   placeholder="Description">
                                        </td>
                                        <td class="px-4 py-3">
                                            <span class="text-sm text-sap-gray-600" x-text="'₹' + (account.original_amount || 0).toFixed(2)"></span>
                                        </td>
                                        <td class="px-4 py-3">
                                            <input type="number" x-model="account.amount" @input="calculateAccountTax(index)" 
                                                   class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-emerald-500" 
                                                   placeholder="Amount" min="0" step="0.01">
                                        </td>
                                        <td class="px-4 py-3">
                                            <select x-model="account.tax_rate" @change="calculateAccountTax(index)" 
                                                    class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-emerald-500">
                                                <option value="0">0%</option>
                                                <option value="5">5%</option>
                                                <option value="12">12%</option>
                                                <option value="18">18%</option>
                                                <option value="28">28%</option>
                                            </select>
                                        </td>
                                        <td class="px-4 py-3">
                                            <span class="text-sm font-medium text-sap-gray-900" x-text="'₹' + (account.tax_amount || 0).toFixed(2)"></span>
                                        </td>
                                        <td class="px-4 py-3">
                                            <span class="text-sm font-medium text-sap-gray-900" x-text="'₹' + (account.total_amount || 0).toFixed(2)"></span>
                                        </td>
                                        <td class="px-4 py-3 text-center">
                                            <button type="button" @click="removeAccount(index)" 
                                                    class="text-sap-red-600 hover:text-sap-red-900">
                                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                        
                        <!-- Add Account Button -->
                        <div class="px-4 py-3 bg-sap-gray-50 border-t border-sap-gray-200">
                            <button type="button" @click="addAccount()" 
                                    class="bg-sap-emerald-600 hover:bg-sap-emerald-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                                Add Account Allocation
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Approval Status Updates -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="shield-check" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Approval Status Updates
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Approval Status -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Approval Status
                            </label>
                            <select name="approval_status"
                                    class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                                <option value="pending" {% if object.approval_status == 'pending' %}selected{% endif %}>Pending</option>
                                <option value="approved" {% if object.approval_status == 'approved' %}selected{% endif %}>Approved</option>
                                <option value="rejected" {% if object.approval_status == 'rejected' %}selected{% endif %}>Rejected</option>
                                <option value="on_hold" {% if object.approval_status == 'on_hold' %}selected{% endif %}>On Hold</option>
                            </select>
                        </div>
                        
                        <!-- Payment Priority -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Payment Priority
                            </label>
                            <select name="payment_priority"
                                    class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                                <option value="low" {% if object.payment_priority == 'low' %}selected{% endif %}>Low</option>
                                <option value="medium" {% if object.payment_priority == 'medium' %}selected{% endif %}>Medium</option>
                                <option value="high" {% if object.payment_priority == 'high' %}selected{% endif %}>High</option>
                                <option value="urgent" {% if object.payment_priority == 'urgent' %}selected{% endif %}>Urgent</option>
                            </select>
                        </div>
                        
                        <!-- Payment Method -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Preferred Payment Method
                            </label>
                            <select name="payment_method"
                                    class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                                <option value="">Select payment method</option>
                                <option value="bank_transfer" {% if object.payment_method == 'bank_transfer' %}selected{% endif %}>Bank Transfer</option>
                                <option value="cheque" {% if object.payment_method == 'cheque' %}selected{% endif %}>Cheque</option>
                                <option value="cash" {% if object.payment_method == 'cash' %}selected{% endif %}>Cash</option>
                                <option value="online" {% if object.payment_method == 'online' %}selected{% endif %}>Online Payment</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- Payment Scheduling -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="calendar-clock" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                        Payment Scheduling
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <!-- Scheduled Payment Date -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Scheduled Payment Date
                            </label>
                            <input type="date" name="scheduled_payment_date" value="{{ object.scheduled_payment_date|date:'Y-m-d' }}"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500">
                        </div>
                        
                        <!-- Payment Terms -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Payment Terms
                            </label>
                            <select name="payment_terms"
                                    class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500">
                                <option value="immediate" {% if object.payment_terms == 'immediate' %}selected{% endif %}>Immediate</option>
                                <option value="net_15" {% if object.payment_terms == 'net_15' %}selected{% endif %}>Net 15 Days</option>
                                <option value="net_30" {% if object.payment_terms == 'net_30' %}selected{% endif %}>Net 30 Days</option>
                                <option value="net_45" {% if object.payment_terms == 'net_45' %}selected{% endif %}>Net 45 Days</option>
                                <option value="net_60" {% if object.payment_terms == 'net_60' %}selected{% endif %}>Net 60 Days</option>
                            </select>
                        </div>
                        
                        <!-- Amount Paid -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Amount Paid
                            </label>
                            <input type="number" name="amount_paid" step="0.01" min="0" value="{{ object.amount_paid|default:'0.00' }}"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500"
                                   x-model="amountPaid" @input="calculateBalanceDue()">
                        </div>
                        
                        <!-- Balance Due -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Balance Due
                            </label>
                            <input type="number" name="balance_due" step="0.01" min="0" readonly
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm bg-sap-gray-50"
                                   x-model="balanceDue">
                        </div>
                    </div>
                </div>
                
                <!-- Updated Bill Summary -->
                <div class="mb-8 bg-sap-yellow-50 border border-sap-yellow-200 rounded-lg p-6">
                    <h4 class="text-lg font-medium text-sap-yellow-800 mb-4 flex items-center">
                        <i data-lucide="receipt" class="w-5 h-5 mr-2 text-sap-yellow-600"></i>
                        Updated Bill Summary
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4 text-center">
                        <div>
                            <p class="text-sm text-sap-yellow-600">Original Amount</p>
                            <p class="text-xl font-bold text-sap-yellow-800">₹{{ object.original_amount|default:"0.00" }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-sap-yellow-600">Updated Amount</p>
                            <p class="text-xl font-bold text-sap-yellow-800" x-text="'₹' + grossAmount.toFixed(2)"></p>
                        </div>
                        <div>
                            <p class="text-sm text-sap-yellow-600">Total Tax</p>
                            <p class="text-xl font-bold text-sap-yellow-800" x-text="'₹' + totalTax.toFixed(2)"></p>
                        </div>
                        <div>
                            <p class="text-sm text-sap-yellow-600">Net Amount</p>
                            <p class="text-2xl font-bold text-sap-yellow-800" x-text="'₹' + netPayable.toFixed(2)"></p>
                        </div>
                        <div>
                            <p class="text-sm text-sap-yellow-600">Balance Due</p>
                            <p class="text-2xl font-bold text-sap-yellow-800" x-text="'₹' + balanceDue.toFixed(2)"></p>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="resetForm()" 
                                class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="rotate-ccw" class="w-4 h-4 inline mr-2"></i>
                            Reset Changes
                        </button>
                        <button type="button" @click="validateBillChanges()" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check-circle" class="w-4 h-4 inline mr-2"></i>
                            Validate Changes
                        </button>
                        <button type="button" @click="previewBill()" 
                                class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="eye" class="w-4 h-4 inline mr-2"></i>
                            Preview
                        </button>
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="{% url 'accounts:bill_booking_list' %}" 
                           class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-sap-yellow-600 hover:bg-sap-yellow-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                            Update Bill Booking
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Bill Booking Edit Guidelines -->
        <div class="mt-6 bg-sap-yellow-50 border border-sap-yellow-200 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i data-lucide="info" class="w-5 h-5 text-sap-yellow-600"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-sap-yellow-800">Bill Booking Edit Guidelines</h3>
                    <div class="mt-2 text-sm text-sap-yellow-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Account allocation changes require recalculation and approval workflow restart</li>
                            <li>Payment scheduling integrates with cash flow management systems</li>
                            <li>Approval status updates trigger notifications to relevant approvers</li>
                            <li>Aging analysis helps prioritize payment processing</li>
                            <li>All changes are tracked with audit trail for compliance</li>
                            <li>Balance due calculations include all adjustments and partial payments</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function billBookingEditForm() {
    return {
        vendorName: '{{ object.vendor_name|default:"" }}',
        vendorCode: '{{ object.vendor_code|default:"" }}',
        vendorGstNumber: '{{ object.vendor_gst_number|default:"" }}',
        vendorAddress: '{{ object.vendor_address|default:"" }}',
        accounts: [
            {
                account_head: '{{ object.account_head|default:"" }}',
                description: '{{ object.description|default:"" }}',
                original_amount: {{ object.original_amount|default:0 }},
                amount: {{ object.amount|default:0 }},
                tax_rate: {{ object.tax_rate|default:18 }},
                tax_amount: 0,
                total_amount: 0
            }
        ],
        grossAmount: 0,
        totalTax: 0,
        netPayable: 0,
        amountPaid: {{ object.amount_paid|default:0 }},
        balanceDue: 0,
        
        init() {
            this.calculateTotals();
            this.accounts.forEach((account, index) => {
                this.calculateAccountTax(index);
            });
            this.calculateBalanceDue();
        },
        
        addAccount() {
            this.accounts.push({
                account_head: '',
                description: '',
                original_amount: 0,
                amount: 0,
                tax_rate: 18,
                tax_amount: 0,
                total_amount: 0
            });
        },
        
        removeAccount(index) {
            if (this.accounts.length > 1) {
                this.accounts.splice(index, 1);
                this.calculateTotals();
            }
        },
        
        calculateAccountTax(index) {
            const account = this.accounts[index];
            const amount = parseFloat(account.amount) || 0;
            const taxRate = parseFloat(account.tax_rate) || 0;
            
            account.tax_amount = amount * (taxRate / 100);
            account.total_amount = amount + account.tax_amount;
            
            this.calculateTotals();
        },
        
        calculateTotals() {
            this.grossAmount = 0;
            this.totalTax = 0;
            
            this.accounts.forEach(account => {
                const amount = parseFloat(account.amount) || 0;
                const taxAmount = parseFloat(account.tax_amount) || 0;
                
                this.grossAmount += amount;
                this.totalTax += taxAmount;
            });
            
            this.netPayable = this.grossAmount + this.totalTax;
            this.calculateBalanceDue();
        },
        
        calculateBalanceDue() {
            this.balanceDue = this.netPayable - this.amountPaid;
        },
        
        recalculateAccounts() {
            this.accounts.forEach((account, index) => {
                this.calculateAccountTax(index);
            });
            alert('All account allocations have been recalculated with current tax rates.');
        },
        
        validateBillChanges() {
            const vendorName = this.vendorName.trim();
            
            if (!vendorName) {
                alert('Please enter vendor name.');
                return;
            }
            
            if (this.accounts.length === 0 || !this.accounts[0].account_head) {
                alert('Please add at least one account allocation.');
                return;
            }
            
            if (this.netPayable <= 0) {
                alert('Please enter valid amounts for account allocations.');
                return;
            }
            
            const originalTotal = {{ object.original_amount|default:0 }};
            if (Math.abs(this.netPayable - originalTotal) > originalTotal * 0.1) {
                if (!confirm(`Bill amount has changed by more than 10% (from ₹${originalTotal.toFixed(2)} to ₹${this.netPayable.toFixed(2)}). Continue?`)) {
                    return;
                }
            }
            
            alert('Bill changes validation passed. All modifications are within acceptable limits.');
        },
        
        previewBill() {
            if (this.netPayable <= 0) {
                alert('Please enter valid amounts to preview bill.');
                return;
            }
            
            alert('Bill preview functionality would show updated bill details with change tracking.');
        }
    }
}

function showAgingAnalysis() {
    const billDate = new Date('{{ object.bill_date|date:"Y-m-d" }}');
    const today = new Date();
    const daysOverdue = Math.max(0, Math.floor((today - billDate) / (1000 * 60 * 60 * 24)) - 30);
    
    let agingCategory = 'Current';
    if (daysOverdue > 90) agingCategory = '90+ Days';
    else if (daysOverdue > 60) agingCategory = '61-90 Days';
    else if (daysOverdue > 30) agingCategory = '31-60 Days';
    else if (daysOverdue > 0) agingCategory = '1-30 Days';
    
    alert(`Aging Analysis:\n\nBill Date: {{ object.bill_date|date:"M d, Y" }}\nDays Overdue: ${daysOverdue}\nAging Category: ${agingCategory}\nPriority: ${daysOverdue > 60 ? 'High' : daysOverdue > 30 ? 'Medium' : 'Low'}`);
}

function schedulePayment() {
    const balanceDue = document.querySelector('input[name="balance_due"]').value;
    if (parseFloat(balanceDue) <= 0) {
        alert('No balance due for this bill. Payment scheduling not required.');
        return;
    }
    
    alert('Payment scheduling functionality would integrate with cash flow management and create payment reminders.');
}

function resetForm() {
    if (confirm('Are you sure you want to reset all changes? This will reload the original bill data.')) {
        location.reload();
    }
}

// Auto-calculation and payment tracking
document.addEventListener('DOMContentLoaded', function() {
    // Add change listener for amount paid
    const amountPaidField = document.querySelector('input[name="amount_paid"]');
    if (amountPaidField) {
        amountPaidField.addEventListener('input', function() {
            // Update balance due calculation
            const event = new CustomEvent('alpine:recalculate');
            document.dispatchEvent(event);
        });
    }
    
    lucide.createIcons();
});
</script>
{% endblock %}