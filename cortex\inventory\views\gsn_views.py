from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.views.generic import ListView, DetailView, CreateView, UpdateView, TemplateView
from django.db import transaction
from django.utils import timezone
from datetime import date

from ..models import (
    MaterialServiceNote, MaterialServiceNoteDetail
)
from sys_admin.models import Company, FinancialYear
from ..forms import (
    MaterialServiceNoteForm
)


class GSNNewListView(LoginRequiredMixin, ListView):
    """List GIN records available for GSN creation"""
    template_name = 'inventory/gsn/gsn_new_list.html'
    context_object_name = 'gin_records'
    paginate_by = 25
    
    def get_queryset(self):
        """Get GIN records that have service items and pending quantities"""
        from django.db import connection
        
        # Get supplier filter
        supplier_filter = self.request.GET.get('supplier', '')
        supplier_condition = ""
        if supplier_filter:
            supplier_condition = f"AND s.SupplierName LIKE '%{supplier_filter}%'"
        
        # Custom SQL to match ASP.NET logic
        sql = f"""
        SELECT DISTINCT 
            i.Id, i.GINNo, i.SysDate as GINDate, i.ChallanNo, i.ChallanDate,
            p.PONo, s.SupplierName, s.SupplierId, f.FinYear, i.FinYearId
        FROM tblInv_Inward_Master i
        INNER JOIN tblMM_PO_Master p ON i.POMId = p.Id
        INNER JOIN tblMM_Supplier_master s ON p.SupplierId = s.SupplierId
        INNER JOIN tblFinancial_master f ON i.FinYearId = f.FinYearId
        WHERE i.CompId = %s
            AND i.FinYearId <= %s
            {supplier_condition}
        ORDER BY i.Id DESC
        """
        
        # Get company and financial year from session or default
        company_id = 1  # Default company for now
        financial_year_id = 1  # Default financial year for now
        
        with connection.cursor() as cursor:
            cursor.execute(sql, [company_id, financial_year_id])
            results = cursor.fetchall()
            
            # Convert to list of dicts for easy template access
            gin_data = []
            for row in results:
                gin_data.append({
                    'id': row[0],
                    'gin_no': row[1],
                    'gin_date': row[2],
                    'challan_no': row[3],
                    'challan_date': row[4],
                    'po_no': row[5],
                    'supplier_name': row[6],
                    'supplier_id': row[7],
                    'fin_year': row[8],
                    'fin_year_id': row[9]
                })
        
        return gin_data
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Goods Service Note - New'
        context['supplier_filter'] = self.request.GET.get('supplier', '')
        return context


class GSNNewDetailsView(LoginRequiredMixin, CreateView):
    """Create new GSN for specific GIN record"""
    model = MaterialServiceNote
    form_class = MaterialServiceNoteForm
    template_name = 'inventory/gsn/gsn_new_details.html'
    
    def dispatch(self, request, *args, **kwargs):
        self.gin_id = kwargs.get('gin_id')
        self.supplier_id = kwargs.get('supplier_id')
        self.gin_no = kwargs.get('gin_no')
        self.po_no = kwargs.get('po_no')
        self.fy_id = kwargs.get('fy_id')
        return super().dispatch(request, *args, **kwargs)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = f'GSN New Details - GIN: {self.gin_no}'
        context['gin_id'] = self.gin_id
        context['gin_no'] = self.gin_no
        context['po_no'] = self.po_no
        context['supplier_id'] = self.supplier_id
        
        # Get GIN details for service items
        context['gin_items'] = self.get_gin_service_items()
        
        if self.request.POST:
            context['detail_formset'] = ServiceNoteDetailInlineFormSet(self.request.POST)
        else:
            context['detail_formset'] = ServiceNoteDetailInlineFormSet()
            
        return context
    
    def get_gin_service_items(self):
        """Get service items from GIN that can be processed"""
        from django.db import connection
        
        sql = """
        SELECT DISTINCT 
            gd.Id as GINDetailId, gd.POId, gd.ReceivedQty as GINQty,
            im.ItemCode, im.ManfDesc as Description, u.Symbol as UOM,
            po.Qty as POQty, im.StockQty,
            COALESCE(gsn_sum.TotalGSNQty, 0) as TotalGSNQty
        FROM tblInv_Inward_Details gd
        INNER JOIN tblMM_PO_Details po ON gd.POId = po.Id
        INNER JOIN tblMM_PR_Details pr ON po.PRId = pr.Id
        INNER JOIN AccHead ah ON pr.AHId = ah.Id
        INNER JOIN tblDG_Item_Master im ON pr.ItemId = im.Id
        INNER JOIN Unit_Master u ON im.UOMBasic = u.Id
        LEFT JOIN (
            SELECT gsnd.POId, SUM(gsnd.ReceivedQty) as TotalGSNQty
            FROM tblinv_MaterialServiceNote_Details gsnd
            INNER JOIN tblinv_MaterialServiceNote_Master gsnm ON gsnd.MId = gsnm.Id
            WHERE gsnm.GINId = %s
            GROUP BY gsnd.POId
        ) gsn_sum ON gd.POId = gsn_sum.POId
        WHERE gd.GINId = %s
            AND ah.Category IN ('Labour', 'Expenses', 'Service Provider')
            AND (gd.ReceivedQty - COALESCE(gsn_sum.TotalGSNQty, 0)) > 0
        ORDER BY gd.Id
        """
        
        with connection.cursor() as cursor:
            cursor.execute(sql, [self.gin_id, self.gin_id])
            results = cursor.fetchall()
            
            items = []
            for row in results:
                items.append({
                    'gin_detail_id': row[0],
                    'po_id': row[1],
                    'gin_qty': row[2],
                    'item_code': row[3],
                    'description': row[4],
                    'uom': row[5],
                    'po_qty': row[6],
                    'stock_qty': row[7],
                    'total_gsn_qty': row[8],
                    'balance_qty': row[2] - row[8]
                })
        
        return items
    
    def form_valid(self, form):
        context = self.get_context_data()
        detail_formset = context['detail_formset']
        
        with transaction.atomic():
            # Set additional fields
            form.instance.created_by = self.request.user
            
            # Get company and financial year
            try:
                company = Company.objects.first()
                financial_year = FinancialYear.objects.filter(is_active=True).first()
                form.instance.company = company
                form.instance.financial_year = financial_year
            except (Company.DoesNotExist, FinancialYear.DoesNotExist):
                messages.error(self.request, "Company or Financial Year not found.")
                return self.form_invalid(form)
            
            # Generate GSN number
            form.instance.gsnno = self.generate_gsn_number()
            # Note: MaterialServiceNote model doesn't have status or service_type fields
            
            # Process the received quantities from the form
            received_quantities = self.request.POST.getlist('received_qty[]')
            po_ids = self.request.POST.getlist('po_id[]')
            
            if not received_quantities or not any(float(qty or 0) > 0 for qty in received_quantities):
                messages.error(self.request, "Please enter at least one received quantity.")
                return self.form_invalid(form)
            
            # Save the service note
            self.object = form.save()
            
            # Create service note details for each item with received quantity
            gin_items = self.get_gin_service_items()
            line_number = 1
            
            for i, item in enumerate(gin_items):
                if i < len(received_quantities) and float(received_quantities[i] or 0) > 0:
                    MaterialServiceNoteDetail.objects.create(
                        service_note=self.object,
                        line_number=line_number,
                        item_code=item['item_code'],
                        item_description=item['description'],
                        unit_of_measure=item['uom'],
                        service_quantity=float(received_quantities[i]),
                        unit_rate=0.00
                    )
                    line_number += 1
            
            # Update totals
            self.update_gsn_totals(self.object)
            
            messages.success(self.request, f'GSN {self.object.gsnno} created successfully.')
            return redirect('inventory:gsn_edit_details', pk=self.object.pk)
    
    def generate_gsn_number(self):
        """Generate unique GSN number"""
        today = date.today()
        prefix = f"GSN/{today.year}/{today.month:02d}/"
        
        # Get last GSN for current month
        last_gsn = MaterialServiceNote.objects.filter(
            gsnno__startswith=prefix
        ).order_by('-gsnno').first()
        
        if last_gsn:
            try:
                last_num = int(last_gsn.gsnno.split('/')[-1])
                new_num = last_num + 1
            except (ValueError, IndexError):
                new_num = 1
        else:
            new_num = 1
            
        return f"{prefix}{new_num:04d}"
    
    def update_gsn_totals(self, gsn):
        """Update GSN totals based on details"""
        details = gsn.details.all()
        gsn.total_items = details.count()
        gsn.total_amount = sum(
            detail.service_quantity * (detail.unit_rate or 0) 
            for detail in details
        )
        gsn.save(update_fields=['total_items', 'total_amount'])


class GSNEditListView(LoginRequiredMixin, TemplateView):
    """List existing GSN records for editing"""
    template_name = 'inventory/gsn/gsn_edit_list.html'
    
    def get_gsn_records(self):
        """Get GSN records with related data using raw SQL for performance"""
        from django.db import connection
        
        # Get supplier filter
        supplier_filter = self.request.GET.get('supplier', '')
        supplier_condition = ""
        params = [1]  # Default company ID
        
        if supplier_filter:
            supplier_condition = "AND s.SupplierName LIKE %s"
            params.append(f'%{supplier_filter}%')
        
        # Custom SQL to get GSN records with related data
        sql = f"""
        SELECT DISTINCT 
            gsn.Id, gsn.gsnno, gsn.ginno, gsn.taxinvoicedate as sn_date,
            i.ChallanNo as challan_number, i.ChallanDate as challan_date,
            p.PONo as po_number, s.SupplierName as supplier_name, 
            f.FinYear as fin_year, gsn.taxinvoiceno
        FROM tblinv_MaterialServiceNote_Master gsn
        LEFT JOIN tblInv_Inward_Master i ON gsn.ginid = i.Id
        LEFT JOIN tblMM_PO_Master p ON i.POMId = p.Id
        LEFT JOIN tblMM_Supplier_master s ON p.SupplierId = s.SupplierId
        LEFT JOIN tblFinancial_master f ON gsn.FinYearId = f.FinYearId
        WHERE gsn.CompId = %s
            {supplier_condition}
        ORDER BY gsn.Id DESC
        """
        
        with connection.cursor() as cursor:
            cursor.execute(sql, params)
            results = cursor.fetchall()
            
            # Convert to list of objects for easy template access
            gsn_data = []
            for row in results:
                gsn_data.append({
                    'id': row[0],
                    'gsnno': row[1],
                    'ginno': row[2],
                    'sn_date': row[3],
                    'challan_number': row[4],
                    'challan_date': row[5],
                    'po_number': row[6],
                    'supplier_name': row[7],
                    'fin_year': row[8],
                    'taxinvoiceno': row[9],
                    'financial_year': {'fin_year': row[8]} if row[8] else {'fin_year': '2022-2023'}
                })
        
        return gsn_data
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Goods Service Note [GSN] - Edit'
        context['supplier_filter'] = self.request.GET.get('supplier', '')
        context['gsn_records'] = self.get_gsn_records()
        return context


class GSNEditDetailsView(LoginRequiredMixin, UpdateView):
    """Edit existing GSN details"""
    model = MaterialServiceNote
    form_class = MaterialServiceNoteForm
    template_name = 'inventory/gsn/gsn_edit_details.html'
    
    def get_object(self, queryset=None):
        obj = super().get_object(queryset)
        # GSN records from tblinv_MaterialServiceNote_Master are inherently service notes
        # No status check needed as all records can be edited
        return obj
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = f'Edit GSN - {self.object.gsnno}'
        
        # For now, just provide the GSN object for editing
        # Details can be handled separately if needed
        return context
    
    def form_valid(self, form):
        with transaction.atomic():
            # Simple save for now - no details formset handling
            self.object = form.save()
            
            messages.success(self.request, f'GSN {self.object.gsnno} updated successfully.')
            return redirect('inventory:gsn_edit_details', pk=self.object.pk)
    
    def update_gsn_totals(self, gsn):
        """Update GSN totals based on details"""
        details = gsn.details.all()
        gsn.total_items = details.count()
        gsn.total_amount = sum(
            detail.service_quantity * (detail.unit_rate or 0) 
            for detail in details
        )
        gsn.save(update_fields=['total_items', 'total_amount'])


class GSNDeleteListView(LoginRequiredMixin, ListView):
    """List GSN records for deletion"""
    model = MaterialServiceNote
    template_name = 'inventory/gsn/gsn_delete_list.html'
    context_object_name = 'gsn_records'
    paginate_by = 25
    
    def get_queryset(self):
        queryset = MaterialServiceNote.objects.all().select_related('company', 'financial_year')
        
        return queryset.order_by('-taxinvoicedate', '-gsnno')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Goods Service Note - Delete'
        return context


class GSNDeleteDetailsView(LoginRequiredMixin, DetailView):
    """Show GSN details for deletion"""
    model = MaterialServiceNote
    template_name = 'inventory/gsn/gsn_delete_details.html'
    context_object_name = 'gsn'
    
    def get_object(self, queryset=None):
        obj = super().get_object(queryset)
        # All GSN records can be viewed for deletion
        return obj
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = f'Delete GSN - {self.object.gsnno}'
        return context


@login_required
def gsn_delete_detail_item(request, gsn_id, detail_id):
    """Delete individual GSN detail item"""
    gsn = get_object_or_404(MaterialServiceNote, id=gsn_id)
    detail = get_object_or_404(MaterialServiceNoteDetail, id=detail_id)
    
    # Allow deletion of GSN items
    
    with transaction.atomic():
        # Delete the detail item
        detail.delete()
        
        # Check if any details remain
        if not gsn.details.exists():
            # Delete the entire GSN if no details remain
            gsn_number = gsn.gsnno
            gsn.delete()
            messages.success(request, f'GSN {gsn_number} deleted completely (no items remaining).')
            return redirect('inventory:gsn_delete_list')
        else:
            # Update totals
            details = gsn.details.all()
            gsn.total_items = details.count()
            gsn.total_amount = sum(
                d.service_quantity * (d.unit_rate or 0) 
                for d in details
            )
            gsn.save(update_fields=['total_items', 'total_amount'])
            
            messages.success(request, 'GSN detail item deleted successfully.')
            return redirect('inventory:gsn_delete_details', pk=gsn_id)


class GSNPrintListView(LoginRequiredMixin, ListView):
    """List GSN records for printing"""
    model = MaterialServiceNote
    template_name = 'inventory/gsn/gsn_print_list.html'
    context_object_name = 'gsn_records'
    paginate_by = 25
    
    def get_queryset(self):
        queryset = MaterialServiceNote.objects.all().select_related('company', 'financial_year')
        
        return queryset.order_by('-taxinvoicedate', '-gsnno')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Goods Service Note - Print'
        return context


@login_required
def gsn_print_view(request, pk):
    """Print GSN"""
    gsn = get_object_or_404(MaterialServiceNote, pk=pk)
    
    context = {
        'gsn': gsn,
        'company': gsn.company,
        'print_date': timezone.now(),
        'page_title': f'Print GSN - {gsn.gsnno}'
    }
    
    return render(request, 'inventory/gsn/gsn_print.html', context)