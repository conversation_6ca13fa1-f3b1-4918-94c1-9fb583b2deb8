<!-- sales_distribution/templates/sales_distribution/partials/category_row.html -->
<!-- Category row partial - normal view mode -->

<tr id="category-row-{{ category.cid }}" class="hover:bg-sap-gray-50">
    <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900 text-right">
        {{ forloop.counter }}
    </td>
    <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
        {% if not category.is_used %}
        <div class="flex space-x-1">
            <button hx-get="{% url 'sales_distribution:category_edit_row' category.cid %}" 
                    hx-target="#category-row-{{ category.cid }}" 
                    hx-swap="outerHTML"
                    onclick="return confirmUpdate();"
                    class="inline-flex items-center p-1 border border-transparent rounded text-sap-blue-600 hover:text-sap-blue-900 hover:bg-sap-blue-50"
                    title="Edit Category">
                <i data-lucide="edit-2" class="w-4 h-4"></i>
            </button>
            <button hx-delete="{% url 'sales_distribution:category_delete' category.cid %}" 
                    hx-target="#category-row-{{ category.cid }}" 
                    hx-swap="outerHTML"
                    onclick="return confirmDelete();"
                    class="inline-flex items-center p-1 border border-transparent rounded text-red-600 hover:text-red-900 hover:bg-red-50"
                    title="Delete Category">
                <i data-lucide="trash-2" class="w-4 h-4"></i>
            </button>
        </div>
        {% else %}
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800" title="Category is in use and cannot be edited">
            <i data-lucide="lock" class="w-3 h-3 mr-1"></i>
            In Use
        </span>
        {% endif %}
    </td>
    <td class="px-6 py-4 whitespace-nowrap">
        <div class="text-sm font-medium text-sap-gray-900">{{ category.cname }}</div>
    </td>
    <td class="px-6 py-4 whitespace-nowrap text-center">
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-gray-100 text-sap-gray-800">
            {{ category.symbol|default:"-" }}
        </span>
    </td>
    <td class="px-6 py-4 whitespace-nowrap text-center">
        {% if category.hassubcat == "1" %}
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <i data-lucide="check" class="w-3 h-3 mr-1"></i>
            Yes
        </span>
        {% else %}
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            <i data-lucide="x" class="w-3 h-3 mr-1"></i>
            No
        </span>
        {% endif %}
    </td>
    <td class="px-6 py-4 whitespace-nowrap text-center">
        {% if category.is_used %}
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <i data-lucide="activity" class="w-3 h-3 mr-1"></i>
            Active
        </span>
        {% else %}
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            <i data-lucide="circle" class="w-3 h-3 mr-1"></i>
            Unused
        </span>
        {% endif %}
    </td>
</tr>