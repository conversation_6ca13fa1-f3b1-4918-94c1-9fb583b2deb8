"""
API Views for Inventory Module
Provides JSON endpoints for HTMX and Ajax interactions
"""

from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.db.models import Q
from django.utils.decorators import method_decorator
from django.views.generic import View
import json
import logging

# Import models - Note: These would need to be created in models.py
# For now, using placeholder data structure
logger = logging.getLogger(__name__)


@login_required
@require_http_methods(["GET"])
def item_search_api(request):
    """
    API endpoint for searching items with stock information
    Used by MRS line item form and other item selection interfaces
    """
    try:
        query = request.GET.get('q', '').strip()
        category_id = request.GET.get('category', '')
        location_id = request.GET.get('location', '')
        limit = int(request.GET.get('limit', 20))
        
        if len(query) < 2:
            return JsonResponse([])
        
        # Placeholder for actual item search logic
        # This would query Item_Master table with stock calculations
        
        items = []
        
        # Mock data for demonstration - replace with actual database queries
        mock_items = [
            {
                'id': 1,
                'item_code': 'ITM001',
                'description': 'Steel Rod 12mm',
                'unit_of_measure': 'PCS',
                'category_name': 'Raw Materials',
                'available_stock': 150,
                'last_price': 25.50,
                'location': 'WH-A-01'
            },
            {
                'id': 2,
                'item_code': 'ITM002', 
                'description': 'Bearing SKF 6205',
                'unit_of_measure': 'NOS',
                'category_name': 'Components',
                'available_stock': 45,
                'last_price': 125.75,
                'location': 'WH-B-15'
            },
            {
                'id': 3,
                'item_code': 'ITM003',
                'description': 'Hydraulic Oil 68',
                'unit_of_measure': 'LTR',
                'category_name': 'Consumables',
                'available_stock': 0,
                'last_price': 8.25,
                'location': 'WH-C-03'
            }
        ]
        
        # Filter mock data based on query
        filtered_items = []
        for item in mock_items:
            if (query.lower() in item['item_code'].lower() or 
                query.lower() in item['description'].lower()):
                filtered_items.append(item)
        
        # Apply category filter if provided
        if category_id:
            # Would filter by category in real implementation
            pass
            
        return JsonResponse(filtered_items[:limit], safe=False)
        
    except Exception as e:
        logger.error(f"Error in item search API: {str(e)}")
        return JsonResponse({'error': 'Search failed'}, status=500)


@login_required
@require_http_methods(["GET"])
def item_details_api(request):
    """
    API endpoint for getting detailed item information
    Used when item code is selected to auto-fill description, UOM, etc.
    """
    try:
        item_code = request.GET.get('item_code', '').strip()
        
        if not item_code:
            return JsonResponse({'error': 'Item code is required'}, status=400)
        
        # Placeholder for actual item lookup
        # This would query Item_Master and calculate current stock
        
        # Mock item details
        item_details = {
            'item_code': item_code,
            'description': 'Mock Item Description',
            'unit_of_measure': 'PCS',
            'category': 'Mock Category',
            'available_stock': 100,
            'reserved_stock': 5,
            'last_purchase_price': 25.50,
            'average_price': 24.75,
            'locations': [
                {'location_code': 'WH-A-01', 'stock': 75},
                {'location_code': 'WH-B-02', 'stock': 25}
            ],
            'specifications': 'Mock technical specifications',
            'preferred_supplier': 'Mock Supplier Ltd'
        }
        
        return JsonResponse(item_details)
        
    except Exception as e:
        logger.error(f"Error in item details API: {str(e)}")
        return JsonResponse({'error': 'Failed to fetch item details'}, status=500)


@login_required
@require_http_methods(["GET"])
def categories_api(request):
    """
    API endpoint for getting item categories
    Used by category dropdown filters
    """
    try:
        # Placeholder for actual category lookup
        # This would query Category_Master table
        
        categories = [
            {'id': 1, 'name': 'Raw Materials', 'code': 'RM'},
            {'id': 2, 'name': 'Components', 'code': 'COMP'},
            {'id': 3, 'name': 'Consumables', 'code': 'CONS'},
            {'id': 4, 'name': 'Finished Goods', 'code': 'FG'},
            {'id': 5, 'name': 'Tools & Equipment', 'code': 'TOOL'},
            {'id': 6, 'name': 'Spare Parts', 'code': 'SPARE'}
        ]
        
        return JsonResponse(categories, safe=False)
        
    except Exception as e:
        logger.error(f"Error in categories API: {str(e)}")
        return JsonResponse({'error': 'Failed to fetch categories'}, status=500)


@login_required
@require_http_methods(["GET"])
def locations_api(request):
    """
    API endpoint for getting storage locations
    Used by location dropdown filters
    """
    try:
        # Query actual ItemLocation model
        from ..models import ItemLocation
        
        locations = []
        location_objects = ItemLocation.objects.filter(
            company=request.user.company,
            financial_year=request.user.financial_year
        ).order_by('location_no')
        
        for location in location_objects:
            locations.append({
                'id': location.id,
                'location_code': location.location_no,
                'location_label': location.location_label,
                'description': location.description,
                'display_name': f"{location.location_no} - {location.description}"
            })
        
        return JsonResponse(locations, safe=False)
        
    except Exception as e:
        logger.error(f"Error in locations API: {str(e)}")
        return JsonResponse({'error': 'Failed to fetch locations'}, status=500)


@login_required
@require_http_methods(["GET"])
def stock_availability_api(request):
    """
    API endpoint for checking stock availability
    Used for real-time stock validation during requisition
    """
    try:
        item_code = request.GET.get('item_code', '').strip()
        location_code = request.GET.get('location', '')
        
        if not item_code:
            return JsonResponse({'error': 'Item code is required'}, status=400)
        
        # Placeholder for actual stock calculation
        # This would calculate current stock from all transactions
        
        stock_info = {
            'item_code': item_code,
            'total_available_stock': 100,
            'reserved_stock': 10,
            'free_stock': 90,
            'locations': [
                {
                    'location_code': 'WH-A-01',
                    'available_stock': 60,
                    'reserved_stock': 5,
                    'free_stock': 55
                },
                {
                    'location_code': 'WH-B-02', 
                    'available_stock': 40,
                    'reserved_stock': 5,
                    'free_stock': 35
                }
            ],
            'last_updated': '2024-12-06T10:30:00Z'
        }
        
        # Filter by location if specified
        if location_code:
            location_stock = next(
                (loc for loc in stock_info['locations'] if loc['location_code'] == location_code),
                None
            )
            if location_stock:
                stock_info.update(location_stock)
        
        return JsonResponse(stock_info)
        
    except Exception as e:
        logger.error(f"Error in stock availability API: {str(e)}")
        return JsonResponse({'error': 'Failed to fetch stock availability'}, status=500)


@login_required 
@require_http_methods(["GET"])
def suppliers_api(request):
    """
    API endpoint for getting supplier information
    Used by supplier dropdown filters and PO integration
    """
    try:
        from material_management.models import Supplier
        
        query = request.GET.get('q', '').strip()
        company_id = request.session.get('compid', 1)
        limit = int(request.GET.get('limit', 20))
        
        # Query actual Supplier table
        suppliers_queryset = Supplier.objects.filter(company_id=company_id)
        
        if query and len(query) >= 2:
            suppliers_queryset = suppliers_queryset.filter(
                Q(suppliername__icontains=query) |
                Q(supplierid__icontains=query)
            )
        
        suppliers_queryset = suppliers_queryset.order_by('suppliername')[:limit]
        
        suppliers = []
        for supplier in suppliers_queryset:
            # Format display text as: "SUPPLIER NAME [SUPPLIER_ID]" to match screenshots
            display_name = supplier.suppliername or 'Unknown'
            supplier_code = supplier.supplierid or ''
            
            display_text = f"{display_name} [{supplier_code}]" if supplier_code else display_name
            
            suppliers.append({
                'id': supplier.supplierid,  # Use SupplierId for matching with PO table
                'supplier_id': supplier_code,
                'supplier_name': display_name,
                'contact_person': supplier.contactperson or '',
                'phone': supplier.contactno or '',
                'email': supplier.email or '',
                'display_text': display_text
            })
        
        return JsonResponse(suppliers, safe=False)
        
    except Exception as e:
        logger.error(f"Error in suppliers API: {str(e)}")
        return JsonResponse({'error': 'Failed to fetch suppliers'}, status=500)


@login_required
@require_http_methods(["GET"])
def supplier_details_api(request, supplier_id):
    """
    API endpoint for getting detailed supplier information
    Used when supplier is selected to show additional details
    """
    try:
        from material_management.models import Supplier
        
        company_id = request.session.get('compid', 1)
        
        supplier = Supplier.objects.get(
            supplierid=supplier_id,
            company_id=company_id
        )
        
        supplier_details = {
            'id': supplier.supid,
            'supplier_id': supplier.supplierid,
            'supplier_name': supplier.suppliername,
            'scope_of_supply': supplier.scopeofsupply,
            'contact_person': supplier.contactperson,
            'phone': supplier.contactno,
            'email': supplier.email,
            'address': supplier.regdaddress,
            'tin_vat_no': supplier.tinvatno,
            'pan_no': supplier.panno,
            'display_text': f"{supplier.suppliername} [{supplier.supplierid}]"
        }
        
        return JsonResponse(supplier_details)
        
    except Supplier.DoesNotExist:
        return JsonResponse({'error': 'Supplier not found'}, status=404)
    except Exception as e:
        logger.error(f"Error in supplier details API: {str(e)}")
        return JsonResponse({'error': 'Failed to fetch supplier details'}, status=500)


@login_required
@require_http_methods(["GET"])
def purchase_orders_api(request):
    """
    API endpoint for getting purchase order information
    Used by GIN/GRR creation for PO line item selection
    """
    try:
        supplier_id = request.GET.get('supplier_id', '')
        status = request.GET.get('status', 'open')  # open, closed, all
        
        # Placeholder for actual PO lookup
        # This would query PO_Master and PO_Details tables
        
        purchase_orders = [
            {
                'id': 1,
                'po_number': 'PO-2024-001',
                'po_date': '2024-11-15',
                'supplier_name': 'ABC Engineering Ltd',
                'total_amount': 25000.50,
                'status': 'open',
                'line_items': [
                    {
                        'line_no': 1,
                        'item_code': 'ITM001',
                        'description': 'Steel Rod 12mm',
                        'ordered_qty': 100,
                        'received_qty': 60,
                        'pending_qty': 40,
                        'unit_price': 25.50
                    }
                ]
            }
        ]
        
        # Filter by supplier if provided
        if supplier_id:
            # Would filter by supplier in real implementation
            pass
            
        return JsonResponse(purchase_orders, safe=False)
        
    except Exception as e:
        logger.error(f"Error in purchase orders API: {str(e)}")
        return JsonResponse({'error': 'Failed to fetch purchase orders'}, status=500)


@login_required
@require_http_methods(["POST"])
def validate_mrs_submission(request):
    """
    API endpoint for validating MRS before submission
    Checks stock availability, business rules, etc.
    """
    try:
        data = json.loads(request.body)
        mrs_id = data.get('mrs_id')
        
        # Placeholder for actual MRS validation logic
        # This would check all line items against current stock
        
        validation_result = {
            'valid': True,
            'warnings': [],
            'errors': [],
            'line_item_validation': []
        }
        
        # Mock validation results
        validation_result['warnings'].append(
            'Item ITM003 has zero stock. Approval may be required.'
        )
        
        return JsonResponse(validation_result)
        
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        logger.error(f"Error in MRS validation API: {str(e)}")
        return JsonResponse({'error': 'Validation failed'}, status=500)


@login_required
@require_http_methods(["GET"])
def gin_search_api(request):
    """
    API endpoint for searching GIN records with supplier filtering
    Used by the GIN edit interface to show supplier-specific records
    """
    try:
        from django.db import connection
        
        search_type = request.GET.get('search_type', '0')
        search_query = request.GET.get('search_query', '').strip()
        supplier_id = request.GET.get('supplier_id', '').strip()
        
        # Get current company and financial year from session
        company_id = request.session.get('compid', 1)
        fin_year_id = request.session.get('finyear', 9)
        
        # Debug: Log the session values
        logger.info(f"GIN Search Debug - company_id: {company_id}, fin_year_id: {fin_year_id}, supplier_id: {supplier_id}")
        
        # Build the complex SQL query to join GIN + PO + Supplier data
        # Fixed JOIN: Use POMId to join with PO Master table's Id field
        sql_query = """
            SELECT DISTINCT
                gin.Id as id,
                gin.GINNo as gin_number,
                gin.PONo as po_number,
                gin.GDate as gin_date,
                gin.ChallanNo as challan_number,
                gin.ChallanDate as challan_date,
                fy.FinYear as fin_year,
                COALESCE(s.SupplierName, 'UNKNOWN') as supplier_name,
                s.SupplierId as supplier_code,
                gin.CompId,
                gin.FinYearId
            FROM tblInv_Inward_Master gin
            LEFT JOIN tblFinancial_master fy ON gin.FinYearId = fy.FinYearId
            LEFT JOIN tblMM_PO_Master po ON gin.POMId = po.Id
            LEFT JOIN tblMM_Supplier_master s ON po.SupplierId = s.SupplierId AND s.CompId = gin.CompId
            WHERE gin.CompId = ?
        """
        
        params = [company_id]
        
        # Add supplier filter if provided
        if supplier_id:
            sql_query += " AND po.SupplierId = ?"
            params.append(supplier_id)
        
        # Add search filter based on search type
        if search_query:
            if search_type == '0':  # Supplier Name
                sql_query += " AND s.SupplierName LIKE ?"
                params.append(f"%{search_query}%")
            elif search_type == '1':  # PO No
                sql_query += " AND gin.PONo LIKE ?"
                params.append(f"%{search_query}%")
            elif search_type == '2':  # GIN No
                sql_query += " AND gin.GINNo LIKE ?"
                params.append(f"%{search_query}%")
        
        sql_query += " ORDER BY gin.GDate DESC, gin.Id DESC"
        
        # Temporarily disable DEBUG to avoid SQL formatting issues with ? placeholders
        from django.conf import settings
        original_debug = settings.DEBUG
        settings.DEBUG = False
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(sql_query, params)
                columns = [col[0] for col in cursor.description]
                rows = cursor.fetchall()
                
                gin_records = []
                for row in rows:
                    gin_dict = dict(zip(columns, row))
                    
                    # Format dates for display
                    gin_date = gin_dict.get('gin_date', '')
                    challan_date = gin_dict.get('challan_date', '')
                    
                    # Handle date formatting
                    if gin_date:
                        try:
                            if isinstance(gin_date, str) and len(gin_date) >= 10:
                                # Convert from database format to display format
                                gin_date = gin_date[:10]  # Take only date part if datetime
                        except:
                            pass
                    
                    if challan_date:
                        try:
                            if isinstance(challan_date, str) and len(challan_date) >= 10:
                                # Convert from database format to display format  
                                challan_date = challan_date[:10]  # Take only date part if datetime
                        except:
                            pass
                    
                    gin_records.append({
                        'id': gin_dict['id'],
                        'gin_number': gin_dict['gin_number'] or '',
                        'po_number': gin_dict['po_number'] or '',
                        'gin_date': gin_date,
                        'challan_number': gin_dict['challan_number'] or '',
                        'challan_date': challan_date,
                        'fin_year': gin_dict['fin_year'] or '',
                        'supplier_name': gin_dict['supplier_name'] or 'UNKNOWN',
                        'supplier_code': gin_dict['supplier_code'] or ''
                    })
        finally:
            settings.DEBUG = original_debug
        
        return JsonResponse(gin_records, safe=False)
        
    except Exception as e:
        logger.error(f"Error in GIN search API: {str(e)}")
        return JsonResponse({'error': 'Failed to search GIN records'}, status=500)


@login_required
@require_http_methods(["GET"])
def eligible_pos_api(request):
    """
    API endpoint for searching eligible Purchase Orders for GIN creation
    Returns POs that can be used for creating new GINs
    """
    try:
        from django.db import connection
        
        search_type = request.GET.get('search_type', '0')
        po_search = request.GET.get('po_search', '').strip()
        supplier_id = request.GET.get('supplier_id', '').strip()
        
        # Get current company and financial year from session
        company_id = request.session.get('compid', 1)
        
        # Build SQL query to find eligible POs (not yet fully received)
        sql_query = """
            SELECT DISTINCT
                po.Id as id,
                po.PONo as po_number,
                po.PODate as po_date,
                fy.FinYear as fin_year,
                s.SupplierName as supplier_name,
                s.SupplierId as supplier_code
            FROM tblMM_PO_Master po
            LEFT JOIN tblFinancial_master fy ON po.FinYearId = fy.FinYearId
            LEFT JOIN tblMM_Supplier_master s ON po.SupplierId = s.SupplierId AND s.CompId = po.CompId
            WHERE po.CompId = ?
            AND po.POStatus IN ('APPROVED', 'PARTIALLY_RECEIVED', 'OPEN')
        """
        
        params = [company_id]
        
        # Add filters based on search type
        if search_type == '0' and supplier_id:  # Supplier Name search
            sql_query += " AND po.SupplierId = ?"
            params.append(supplier_id)
        elif search_type == '1' and po_search:  # PO No search
            sql_query += " AND po.PONo LIKE ?"
            params.append(f"%{po_search}%")
        
        sql_query += " ORDER BY po.PODate DESC, po.Id DESC"
        
        # Temporarily disable DEBUG for query execution
        from django.conf import settings
        original_debug = settings.DEBUG
        settings.DEBUG = False
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(sql_query, params)
                columns = [col[0] for col in cursor.description]
                rows = cursor.fetchall()
                
                po_records = []
                for row in rows:
                    po_dict = dict(zip(columns, row))
                    
                    # Format PO date for display
                    po_date = po_dict.get('po_date', '')
                    if po_date:
                        try:
                            if isinstance(po_date, str) and len(po_date) >= 10:
                                po_date = po_date[:10]  # Take only date part
                        except:
                            pass
                    
                    po_records.append({
                        'id': po_dict['id'],
                        'po_number': po_dict['po_number'] or '',
                        'po_date': po_date,
                        'fin_year': po_dict['fin_year'] or '',
                        'supplier_name': po_dict['supplier_name'] or 'UNKNOWN',
                        'supplier_code': po_dict['supplier_code'] or ''
                    })
        finally:
            settings.DEBUG = original_debug
        
        return JsonResponse(po_records, safe=False)
        
    except Exception as e:
        logger.error(f"Error in eligible POs API: {str(e)}")
        return JsonResponse({'error': 'Failed to search eligible POs'}, status=500)


class ItemAutocompleteAPI(View):
    """
    Class-based view for item autocomplete functionality
    Provides advanced search with filters and pagination
    """
    
    @method_decorator(login_required)
    def get(self, request):
        try:
            query = request.GET.get('q', '').strip()
            category = request.GET.get('category', '')
            location = request.GET.get('location', '')
            page = int(request.GET.get('page', 1))
            per_page = int(request.GET.get('per_page', 20))
            
            # Placeholder for actual search logic with pagination
            # This would use Django's Paginator with database queries
            
            results = {
                'items': [
                    {
                        'id': 1,
                        'item_code': 'ITM001',
                        'description': 'Steel Rod 12mm',
                        'unit_of_measure': 'PCS',
                        'category': 'Raw Materials',
                        'available_stock': 150
                    }
                ],
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total_items': 1,
                    'total_pages': 1,
                    'has_next': False,
                    'has_previous': False
                }
            }
            
            return JsonResponse(results)
            
        except Exception as e:
            logger.error(f"Error in item autocomplete API: {str(e)}")
            return JsonResponse({'error': 'Search failed'}, status=500)