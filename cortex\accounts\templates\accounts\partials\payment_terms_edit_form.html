<!-- accounts/partials/payment_terms_edit_form.html -->
<!-- HTMX partial for PaymentTerms edit form - SAP S/4HANA inspired -->

{% load static %}

<div class="bg-sap-purple-50 border border-sap-purple-200 rounded-lg p-4 mb-4" id="payment-terms-edit-form-{{ payment_terms.id }}">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                <i data-lucide="edit" class="w-4 h-4 text-sap-purple-600"></i>
            </div>
            <div>
                <h4 class="text-lg font-medium text-sap-gray-800">Edit Payment Terms</h4>
                <p class="text-sm text-sap-gray-600">Update payment terms information (ID: {{ payment_terms.id }})</p>
            </div>
        </div>
        <button type="button" 
                hx-get="{% url 'accounts:payment_terms_list' %}"
                hx-target="#payment-terms-table"
                hx-swap="outerHTML"
                class="inline-flex items-center px-3 py-1.5 border border-sap-gray-300 rounded text-xs font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
            <i data-lucide="x" class="w-3 h-3 mr-1"></i>
            Cancel
        </button>
    </div>
    
    <form hx-put="{% url 'accounts:payment_terms_edit' payment_terms.id %}" 
          hx-target="#payment-terms-edit-form-{{ payment_terms.id }}" 
          hx-swap="outerHTML"
          hx-trigger="submit"
          class="space-y-4">
        {% csrf_token %}
        
        <div>
            <label for="{{ form.terms.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                Payment Terms <span class="text-red-500">*</span>
            </label>
            {{ form.terms }}
            {% if form.terms.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.terms.errors.0 }}</p>
            {% endif %}
        </div>
        
        <div class="flex justify-end space-x-3 pt-4 border-t border-sap-purple-200">
            <button type="button" 
                    hx-get="{% url 'accounts:payment_terms_list' %}"
                    hx-target="#payment-terms-table"
                    hx-swap="outerHTML"
                    class="px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                Cancel
            </button>
            <button type="submit" 
                    class="px-4 py-2 bg-sap-purple-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-sap-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-purple-500">
                <i data-lucide="save" class="w-4 h-4 mr-2 inline"></i>
                Update Payment Terms
            </button>
        </div>
    </form>
</div>