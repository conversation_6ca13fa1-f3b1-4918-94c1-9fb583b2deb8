{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="bg-indigo-600 text-white px-4 py-2 rounded-t-lg">
        <h1 class="text-lg font-semibold">{{ title }}</h1>
    </div>
    
    <!-- Item Information -->
    <div class="bg-white border border-gray-300 p-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
                <span class="font-semibold">Item Code:</span> 
                <span class="font-bold text-blue-600">{{ item.itemcode }}</span>
            </div>
            <div>
                <span class="font-semibold">UOM:</span> 
                <span class="font-bold">{{ item.uombasic }}</span>
                <span class="ml-4 font-semibold">BOM Qty:</span> 
                <span class="font-bold">{{ bom_qty }}</span>
            </div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <span class="font-semibold">Description:</span> 
                <span class="font-bold">{{ item.description }}</span>
            </div>
            <div>
                <span class="font-semibold">WO No:</span> 
                <span class="font-bold text-green-600">{{ wono }}</span>
            </div>
        </div>
    </div>

    <!-- Schedule Input Form -->
    <div class="bg-white border-l border-r border-b border-gray-300 p-4">
        <form hx-post="{% url 'machinery:add_schedule_detail' %}" 
              hx-target="#schedule-details-list" 
              hx-swap="innerHTML"
              class="space-y-4">
            {% csrf_token %}
            <input type="hidden" name="item_id" value="{{ item.id }}">
            <input type="hidden" name="wono" value="{{ wono }}">
            
            <!-- Input Grid -->
            <div class="overflow-x-auto">
                <table class="min-w-full border border-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">SN</th>
                            <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">Machine Name</th>
                            <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">Process</th>
                            <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">Type</th>
                            <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">Shift</th>
                            <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">Batch No</th>
                            <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">Batch Qty</th>
                            <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">From Date</th>
                            <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">To Date</th>
                            <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">From Time</th>
                            <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">To Time</th>
                            <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">Incharge</th>
                            <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">Operator</th>
                            <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="border-b">
                            <td class="px-2 py-2 text-center">1</td>
                            <td class="px-2 py-2">
                                <select name="machine_id" 
                                        hx-get="{% url 'machinery:get_machine_processes' %}"
                                        hx-target="#process-dropdown"
                                        hx-include="[name='item_id']"
                                        class="w-full border rounded px-2 py-1 text-sm" required>
                                    <option value="">Select</option>
                                    {% for machine_data in available_machines %}
                                        <option value="{{ machine_data.item_id }}">{{ machine_data.machine.make }} {{ machine_data.machine.model }}</option>
                                    {% endfor %}
                                </select>
                            </td>
                            <td class="px-2 py-2">
                                <select name="process_id" id="process-dropdown" class="w-full border rounded px-2 py-1 text-sm" required>
                                    <option value="">Select Machine First</option>
                                </select>
                            </td>
                            <td class="px-2 py-2">
                                <select name="type" class="w-full border rounded px-2 py-1 text-sm" required>
                                    <option value="">Select</option>
                                    <option value="0">Fresh</option>
                                    <option value="1">Rework</option>
                                </select>
                            </td>
                            <td class="px-2 py-2">
                                <select name="shift" class="w-full border rounded px-2 py-1 text-sm" required>
                                    <option value="">Select</option>
                                    <option value="0">Day</option>
                                    <option value="1">Night</option>
                                </select>
                            </td>
                            <td class="px-2 py-2">
                                <select name="batch_no" class="w-full border rounded px-2 py-1 text-sm" required>
                                    {% for batch in batches %}
                                        <option value="{{ batch }}">{{ batch }}</option>
                                    {% endfor %}
                                </select>
                            </td>
                            <td class="px-2 py-2">
                                <input type="number" name="batch_qty" step="0.01" class="w-full border rounded px-2 py-1 text-sm" required>
                            </td>
                            <td class="px-2 py-2">
                                <input type="date" name="from_date" class="w-full border rounded px-2 py-1 text-sm" required>
                            </td>
                            <td class="px-2 py-2">
                                <input type="date" name="to_date" class="w-full border rounded px-2 py-1 text-sm" required>
                            </td>
                            <td class="px-2 py-2">
                                <input type="time" name="from_time" class="w-full border rounded px-2 py-1 text-sm" required>
                            </td>
                            <td class="px-2 py-2">
                                <input type="time" name="to_time" class="w-full border rounded px-2 py-1 text-sm" required>
                            </td>
                            <td class="px-2 py-2">
                                <input type="text" name="incharge" 
                                       list="employees-list"
                                       class="w-full border rounded px-2 py-1 text-sm" 
                                       placeholder="Type to search..." required>
                                <datalist id="employees-list">
                                    {% for emp in employees %}
                                        <option value="{{ emp.employeename }} [{{ emp.empid }}]">
                                    {% endfor %}
                                </datalist>
                            </td>
                            <td class="px-2 py-2">
                                <input type="text" name="operator" 
                                       list="employees-list"
                                       class="w-full border rounded px-2 py-1 text-sm" 
                                       placeholder="Type to search..." required>
                            </td>
                            <td class="px-2 py-2 text-center">
                                <button type="submit" class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700">
                                    Add
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </form>
    </div>

    <!-- Schedule Details List -->
    <div class="bg-white border-l border-r border-b border-gray-300 p-4">
        <div id="schedule-details-list">
            {% include 'machinery/partials/schedule_details_list.html' %}
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="bg-white border-l border-r border-b border-gray-300 p-4 text-center">
        <button hx-post="{% url 'machinery:submit_schedule_items' %}"
                hx-vals='{"item_id": "{{ item.id }}", "wono": "{{ wono }}"}'
                hx-target="#main-content"
                class="bg-red-600 text-white px-6 py-2 rounded mr-2 hover:bg-red-700">
            Submit
        </button>
        <a href="{% url 'machinery:schedule_create_detail' wono=wono %}" 
           class="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700">
            Cancel
        </a>
    </div>
</div>

<script>
function confirmationAdd() {
    return confirm('Are you sure you want to proceed?');
}
</script>
{% endblock %}