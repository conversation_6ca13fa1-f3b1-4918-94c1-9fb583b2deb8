<!-- accounts/templates/accounts/vouchers/contra_entry_list.html -->
<!-- Contra Entry List Template -->
<!-- Task Group 2: Banking & Cash Management - Contra Entry List (Task 2.4) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Contra Entries - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-purple-600 to-sap-purple-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="repeat" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Contra Entries</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage fund transfers between accounts</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:contra_entry_create' %}" 
                   class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    New Contra Entry
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Search and Filter Section -->
    <div class="mb-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-sap-gray-800">Search & Filter</h3>
                <button type="button" id="toggle-filters" class="text-sap-blue-600 hover:text-sap-blue-700">
                    <i data-lucide="filter" class="w-5 h-5"></i>
                </button>
            </div>
            
            <form method="get" class="space-y-4" id="filter-form">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <!-- Search Term -->
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-1">Search</label>
                        <input type="text" name="search" value="{{ request.GET.search }}" 
                               placeholder="Entry no, description..."
                               class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                    </div>
                    
                    <!-- Transfer Type Filter -->
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-1">Transfer Type</label>
                        <select name="transfer_type" class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                            <option value="">All Types</option>
                            <option value="BANK_TO_CASH" {% if request.GET.transfer_type == "BANK_TO_CASH" %}selected{% endif %}>Bank to Cash</option>
                            <option value="CASH_TO_BANK" {% if request.GET.transfer_type == "CASH_TO_BANK" %}selected{% endif %}>Cash to Bank</option>
                            <option value="BANK_TO_BANK" {% if request.GET.transfer_type == "BANK_TO_BANK" %}selected{% endif %}>Bank to Bank</option>
                        </select>
                    </div>
                    
                    <!-- Date Range -->
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-1">From Date</label>
                        <input type="date" name="from_date" value="{{ request.GET.from_date }}"
                               class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-1">To Date</label>
                        <input type="date" name="to_date" value="{{ request.GET.to_date }}"
                               class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                    </div>
                </div>
                
                <div class="flex items-center space-x-3">
                    <button type="submit" class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="search" class="w-4 h-4 inline mr-2"></i>
                        Search
                    </button>
                    <a href="{% url 'accounts:contra_entry_list' %}" 
                       class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="x" class="w-4 h-4 inline mr-2"></i>
                        Clear
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="repeat" class="w-5 h-5 text-sap-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-sap-gray-600">Total Transfers</p>
                    <p class="text-xl font-semibold text-sap-gray-800">{{ total_transfers|floatformat:2 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="university" class="w-5 h-5 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-sap-gray-600">Bank Transfers</p>
                    <p class="text-xl font-semibold text-sap-gray-800">{{ bank_transfers|floatformat:2 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="banknote" class="w-5 h-5 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-sap-gray-600">Cash Transfers</p>
                    <p class="text-xl font-semibold text-sap-gray-800">{{ cash_transfers|floatformat:2 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="file-text" class="w-5 h-5 text-sap-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-sap-gray-600">Total Entries</p>
                    <p class="text-xl font-semibold text-sap-gray-800">{{ entries.count }}</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Contra Entries Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Contra Entries</h3>
                <div class="flex items-center space-x-2">
                    <button type="button" onclick="exportEntries()" 
                            class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-3 py-1.5 rounded text-sm font-medium transition-colors">
                        <i data-lucide="download" class="w-4 h-4 inline mr-1"></i>
                        Export
                    </button>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Entry Details
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Transfer Type
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            From Account
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            To Account
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Amount
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for entry in entries %}
                    <tr class="hover:bg-sap-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div>
                                <div class="text-sm font-medium text-sap-gray-900">{{ entry.entry_number }}</div>
                                <div class="text-sm text-sap-gray-500">{{ entry.entry_date|date:"M d, Y" }}</div>
                                {% if entry.reference_number %}
                                <div class="text-xs text-sap-gray-400">Ref: {{ entry.reference_number }}</div>
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if entry.transfer_type == 'BANK_TO_CASH' %}bg-sap-blue-100 text-sap-blue-800
                                {% elif entry.transfer_type == 'CASH_TO_BANK' %}bg-sap-green-100 text-sap-green-800
                                {% else %}bg-sap-purple-100 text-sap-purple-800{% endif %}">
                                {% if entry.transfer_type == 'BANK_TO_CASH' %}
                                    <i data-lucide="arrow-right" class="w-3 h-3 mr-1"></i>
                                    Bank → Cash
                                {% elif entry.transfer_type == 'CASH_TO_BANK' %}
                                    <i data-lucide="arrow-left" class="w-3 h-3 mr-1"></i>
                                    Cash → Bank
                                {% else %}
                                    <i data-lucide="repeat" class="w-3 h-3 mr-1"></i>
                                    Bank → Bank
                                {% endif %}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div>
                                <div class="text-sm font-medium text-sap-gray-900">{{ entry.from_account.description|default:"N/A" }}</div>
                                {% if entry.from_bank %}
                                <div class="text-xs text-sap-gray-500">{{ entry.from_bank.name }}</div>
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div>
                                <div class="text-sm font-medium text-sap-gray-900">{{ entry.to_account.description|default:"N/A" }}</div>
                                {% if entry.to_bank %}
                                <div class="text-xs text-sap-gray-500">{{ entry.to_bank.name }}</div>
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-sap-gray-900">₹{{ entry.amount|floatformat:2 }}</div>
                            {% if entry.charges %}
                            <div class="text-xs text-sap-gray-500">Charges: ₹{{ entry.charges|floatformat:2 }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if entry.is_authorized %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-green-100 text-sap-green-800">
                                <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                Authorized
                            </span>
                            {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-yellow-100 text-sap-yellow-800">
                                <i data-lucide="clock" class="w-3 h-3 mr-1"></i>
                                Pending
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex items-center space-x-2">
                                <button type="button" onclick="viewEntry({{ entry.pk }})" 
                                        class="text-sap-blue-600 hover:text-sap-blue-900">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                </button>
                                <button type="button" onclick="printEntry({{ entry.pk }})" 
                                        class="text-sap-green-600 hover:text-sap-green-900">
                                    <i data-lucide="printer" class="w-4 h-4"></i>
                                </button>
                                {% if not entry.is_authorized %}
                                <button type="button" onclick="deleteEntry({{ entry.pk }})" 
                                        class="text-sap-red-600 hover:text-sap-red-900">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="px-6 py-12 text-center">
                            <div class="text-sap-gray-400">
                                <i data-lucide="repeat" class="w-12 h-12 mx-auto mb-4"></i>
                                <p class="text-lg font-medium">No contra entries found</p>
                                <p class="text-sm">Create your first contra entry to get started</p>
                                <a href="{% url 'accounts:contra_entry_create' %}" 
                                   class="inline-flex items-center mt-4 bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                    Create Contra Entry
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="bg-white px-4 py-3 border-t border-sap-gray-200 sm:px-6">
            <div class="flex items-center justify-between">
                <div class="flex-1 flex justify-between sm:hidden">
                    {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}" 
                       class="relative inline-flex items-center px-4 py-2 border border-sap-gray-300 text-sm font-medium rounded-md text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                        Previous
                    </a>
                    {% endif %}
                    {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}" 
                       class="ml-3 relative inline-flex items-center px-4 py-2 border border-sap-gray-300 text-sm font-medium rounded-md text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                        Next
                    </a>
                    {% endif %}
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-sap-gray-700">
                            Showing <span class="font-medium">{{ page_obj.start_index }}</span> to 
                            <span class="font-medium">{{ page_obj.end_index }}</span> of 
                            <span class="font-medium">{{ page_obj.paginator.count }}</span> results
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                            {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}" 
                               class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-500 hover:bg-sap-gray-50">
                                <i data-lucide="chevron-left" class="w-5 h-5"></i>
                            </a>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                            <span class="relative inline-flex items-center px-4 py-2 border border-sap-green-500 bg-sap-green-50 text-sm font-medium text-sap-green-600">
                                {{ num }}
                            </span>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <a href="?page={{ num }}" 
                               class="relative inline-flex items-center px-4 py-2 border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-700 hover:bg-sap-gray-50">
                                {{ num }}
                            </a>
                            {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}" 
                               class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-500 hover:bg-sap-gray-50">
                                <i data-lucide="chevron-right" class="w-5 h-5"></i>
                            </a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function exportEntries() {
    // Get current search parameters
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    
    window.location.href = '{% url "accounts:contra_entry_list" %}?' + params.toString();
}

function viewEntry(entryId) {
    window.open(`/accounts/transactions/contra-entries/${entryId}/view/`, '_blank');
}

function printEntry(entryId) {
    window.open(`/accounts/transactions/contra-entries/${entryId}/print/`, '_blank');
}

function deleteEntry(entryId) {
    if (confirm('Are you sure you want to delete this contra entry? This action cannot be undone.')) {
        fetch(`/accounts/transactions/contra-entries/${entryId}/delete/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Error deleting entry. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting entry. Please try again.');
        });
    }
}

// Auto-submit search form on input
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('input[name="search"]');
    const transferTypeSelect = document.querySelector('select[name="transfer_type"]');
    const fromDateInput = document.querySelector('input[name="from_date"]');
    const toDateInput = document.querySelector('input[name="to_date"]');
    
    let searchTimeout;
    
    function handleSearch() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            document.getElementById('filter-form').submit();
        }, 500);
    }
    
    if (searchInput) {
        searchInput.addEventListener('input', handleSearch);
    }
    
    [transferTypeSelect, fromDateInput, toDateInput].forEach(element => {
        if (element) {
            element.addEventListener('change', () => {
                document.getElementById('filter-form').submit();
            });
        }
    });
    
    lucide.createIcons();
});
</script>
{% endblock %}