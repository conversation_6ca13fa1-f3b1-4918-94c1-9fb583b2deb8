﻿#pragma warning disable 1591
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:2.0.50727.3053
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.Linq;
using System.Data.Linq.Mapping;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;



[System.Data.Linq.Mapping.DatabaseAttribute(Name="D:\\INETPUB\\WWWROOT\\NEWERP\\APP_DATA\\ERP_DB.MDF")]
public partial class TimeBudget_DeptDataContext : System.Data.Linq.DataContext
{
	
	private static System.Data.Linq.Mapping.MappingSource mappingSource = new AttributeMappingSource();
	
  #region Extensibility Method Definitions
  partial void OnCreated();
  partial void InserttblACC_Budget_Dept_Time(tblACC_Budget_Dept_Time instance);
  partial void UpdatetblACC_Budget_Dept_Time(tblACC_Budget_Dept_Time instance);
  partial void DeletetblACC_Budget_Dept_Time(tblACC_Budget_Dept_Time instance);
  partial void InserttblACC_Budget_WO_Time(tblACC_Budget_WO_Time instance);
  partial void UpdatetblACC_Budget_WO_Time(tblACC_Budget_WO_Time instance);
  partial void DeletetblACC_Budget_WO_Time(tblACC_Budget_WO_Time instance);
  #endregion
	
	public TimeBudget_DeptDataContext() : 
			base(global::System.Configuration.ConfigurationManager.ConnectionStrings["LocalSqlServer"].ConnectionString, mappingSource)
	{
		OnCreated();
	}
	
	public TimeBudget_DeptDataContext(string connection) : 
			base(connection, mappingSource)
	{
		OnCreated();
	}
	
	public TimeBudget_DeptDataContext(System.Data.IDbConnection connection) : 
			base(connection, mappingSource)
	{
		OnCreated();
	}
	
	public TimeBudget_DeptDataContext(string connection, System.Data.Linq.Mapping.MappingSource mappingSource) : 
			base(connection, mappingSource)
	{
		OnCreated();
	}
	
	public TimeBudget_DeptDataContext(System.Data.IDbConnection connection, System.Data.Linq.Mapping.MappingSource mappingSource) : 
			base(connection, mappingSource)
	{
		OnCreated();
	}
	
	public System.Data.Linq.Table<tblACC_Budget_Dept_Time> tblACC_Budget_Dept_Times
	{
		get
		{
			return this.GetTable<tblACC_Budget_Dept_Time>();
		}
	}
	
	public System.Data.Linq.Table<tblACC_Budget_WO_Time> tblACC_Budget_WO_Times
	{
		get
		{
			return this.GetTable<tblACC_Budget_WO_Time>();
		}
	}
}

[Table(Name="dbo.tblACC_Budget_Dept_Time")]
public partial class tblACC_Budget_Dept_Time : INotifyPropertyChanging, INotifyPropertyChanged
{
	
	private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(String.Empty);
	
	private int _Id;
	
	private string _SysDate;
	
	private string _SysTime;
	
	private System.Nullable<int> _CompId;
	
	private System.Nullable<int> _FinYearId;
	
	private string _SessionId;
	
	private System.Nullable<int> _BGGroup;
	
	private System.Nullable<int> _BudgetCodeId;
	
	private System.Nullable<double> _Hour;
	
    #region Extensibility Method Definitions
    partial void OnLoaded();
    partial void OnValidate(System.Data.Linq.ChangeAction action);
    partial void OnCreated();
    partial void OnIdChanging(int value);
    partial void OnIdChanged();
    partial void OnSysDateChanging(string value);
    partial void OnSysDateChanged();
    partial void OnSysTimeChanging(string value);
    partial void OnSysTimeChanged();
    partial void OnCompIdChanging(System.Nullable<int> value);
    partial void OnCompIdChanged();
    partial void OnFinYearIdChanging(System.Nullable<int> value);
    partial void OnFinYearIdChanged();
    partial void OnSessionIdChanging(string value);
    partial void OnSessionIdChanged();
    partial void OnBGGroupChanging(System.Nullable<int> value);
    partial void OnBGGroupChanged();
    partial void OnBudgetCodeIdChanging(System.Nullable<int> value);
    partial void OnBudgetCodeIdChanged();
    partial void OnHourChanging(System.Nullable<double> value);
    partial void OnHourChanged();
    #endregion
	
	public tblACC_Budget_Dept_Time()
	{
		OnCreated();
	}
	
	[Column(Storage="_Id", AutoSync=AutoSync.OnInsert, DbType="Int NOT NULL IDENTITY", IsPrimaryKey=true, IsDbGenerated=true)]
	public int Id
	{
		get
		{
			return this._Id;
		}
		set
		{
			if ((this._Id != value))
			{
				this.OnIdChanging(value);
				this.SendPropertyChanging();
				this._Id = value;
				this.SendPropertyChanged("Id");
				this.OnIdChanged();
			}
		}
	}
	
	[Column(Storage="_SysDate", DbType="VarChar(50)")]
	public string SysDate
	{
		get
		{
			return this._SysDate;
		}
		set
		{
			if ((this._SysDate != value))
			{
				this.OnSysDateChanging(value);
				this.SendPropertyChanging();
				this._SysDate = value;
				this.SendPropertyChanged("SysDate");
				this.OnSysDateChanged();
			}
		}
	}
	
	[Column(Storage="_SysTime", DbType="VarChar(50)")]
	public string SysTime
	{
		get
		{
			return this._SysTime;
		}
		set
		{
			if ((this._SysTime != value))
			{
				this.OnSysTimeChanging(value);
				this.SendPropertyChanging();
				this._SysTime = value;
				this.SendPropertyChanged("SysTime");
				this.OnSysTimeChanged();
			}
		}
	}
	
	[Column(Storage="_CompId", DbType="Int")]
	public System.Nullable<int> CompId
	{
		get
		{
			return this._CompId;
		}
		set
		{
			if ((this._CompId != value))
			{
				this.OnCompIdChanging(value);
				this.SendPropertyChanging();
				this._CompId = value;
				this.SendPropertyChanged("CompId");
				this.OnCompIdChanged();
			}
		}
	}
	
	[Column(Storage="_FinYearId", DbType="Int")]
	public System.Nullable<int> FinYearId
	{
		get
		{
			return this._FinYearId;
		}
		set
		{
			if ((this._FinYearId != value))
			{
				this.OnFinYearIdChanging(value);
				this.SendPropertyChanging();
				this._FinYearId = value;
				this.SendPropertyChanged("FinYearId");
				this.OnFinYearIdChanged();
			}
		}
	}
	
	[Column(Storage="_SessionId", DbType="VarChar(MAX)")]
	public string SessionId
	{
		get
		{
			return this._SessionId;
		}
		set
		{
			if ((this._SessionId != value))
			{
				this.OnSessionIdChanging(value);
				this.SendPropertyChanging();
				this._SessionId = value;
				this.SendPropertyChanged("SessionId");
				this.OnSessionIdChanged();
			}
		}
	}
	
	[Column(Storage="_BGGroup", DbType="Int")]
	public System.Nullable<int> BGGroup
	{
		get
		{
			return this._BGGroup;
		}
		set
		{
			if ((this._BGGroup != value))
			{
				this.OnBGGroupChanging(value);
				this.SendPropertyChanging();
				this._BGGroup = value;
				this.SendPropertyChanged("BGGroup");
				this.OnBGGroupChanged();
			}
		}
	}
	
	[Column(Storage="_BudgetCodeId", DbType="Int")]
	public System.Nullable<int> BudgetCodeId
	{
		get
		{
			return this._BudgetCodeId;
		}
		set
		{
			if ((this._BudgetCodeId != value))
			{
				this.OnBudgetCodeIdChanging(value);
				this.SendPropertyChanging();
				this._BudgetCodeId = value;
				this.SendPropertyChanged("BudgetCodeId");
				this.OnBudgetCodeIdChanged();
			}
		}
	}
	
	[Column(Storage="_Hour", DbType="Float")]
	public System.Nullable<double> Hour
	{
		get
		{
			return this._Hour;
		}
		set
		{
			if ((this._Hour != value))
			{
				this.OnHourChanging(value);
				this.SendPropertyChanging();
				this._Hour = value;
				this.SendPropertyChanged("Hour");
				this.OnHourChanged();
			}
		}
	}
	
	public event PropertyChangingEventHandler PropertyChanging;
	
	public event PropertyChangedEventHandler PropertyChanged;
	
	protected virtual void SendPropertyChanging()
	{
		if ((this.PropertyChanging != null))
		{
			this.PropertyChanging(this, emptyChangingEventArgs);
		}
	}
	
	protected virtual void SendPropertyChanged(String propertyName)
	{
		if ((this.PropertyChanged != null))
		{
			this.PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
		}
	}
}

[Table(Name="dbo.tblACC_Budget_WO_Time")]
public partial class tblACC_Budget_WO_Time : INotifyPropertyChanging, INotifyPropertyChanged
{
	
	private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(String.Empty);
	
	private int _Id;
	
	private string _SysDate;
	
	private string _SysTime;
	
	private System.Nullable<int> _CompId;
	
	private System.Nullable<int> _FinYearId;
	
	private string _SessionId;
	
	private string _WONo;
	
	private System.Nullable<double> _Hour;
	
	private System.Nullable<int> _BudgetCodeId;
	
    #region Extensibility Method Definitions
    partial void OnLoaded();
    partial void OnValidate(System.Data.Linq.ChangeAction action);
    partial void OnCreated();
    partial void OnIdChanging(int value);
    partial void OnIdChanged();
    partial void OnSysDateChanging(string value);
    partial void OnSysDateChanged();
    partial void OnSysTimeChanging(string value);
    partial void OnSysTimeChanged();
    partial void OnCompIdChanging(System.Nullable<int> value);
    partial void OnCompIdChanged();
    partial void OnFinYearIdChanging(System.Nullable<int> value);
    partial void OnFinYearIdChanged();
    partial void OnSessionIdChanging(string value);
    partial void OnSessionIdChanged();
    partial void OnWONoChanging(string value);
    partial void OnWONoChanged();
    partial void OnHourChanging(System.Nullable<double> value);
    partial void OnHourChanged();
    partial void OnBudgetCodeIdChanging(System.Nullable<int> value);
    partial void OnBudgetCodeIdChanged();
    #endregion
	
	public tblACC_Budget_WO_Time()
	{
		OnCreated();
	}
	
	[Column(Storage="_Id", AutoSync=AutoSync.OnInsert, DbType="Int NOT NULL IDENTITY", IsPrimaryKey=true, IsDbGenerated=true)]
	public int Id
	{
		get
		{
			return this._Id;
		}
		set
		{
			if ((this._Id != value))
			{
				this.OnIdChanging(value);
				this.SendPropertyChanging();
				this._Id = value;
				this.SendPropertyChanged("Id");
				this.OnIdChanged();
			}
		}
	}
	
	[Column(Storage="_SysDate", DbType="VarChar(50)")]
	public string SysDate
	{
		get
		{
			return this._SysDate;
		}
		set
		{
			if ((this._SysDate != value))
			{
				this.OnSysDateChanging(value);
				this.SendPropertyChanging();
				this._SysDate = value;
				this.SendPropertyChanged("SysDate");
				this.OnSysDateChanged();
			}
		}
	}
	
	[Column(Storage="_SysTime", DbType="VarChar(50)")]
	public string SysTime
	{
		get
		{
			return this._SysTime;
		}
		set
		{
			if ((this._SysTime != value))
			{
				this.OnSysTimeChanging(value);
				this.SendPropertyChanging();
				this._SysTime = value;
				this.SendPropertyChanged("SysTime");
				this.OnSysTimeChanged();
			}
		}
	}
	
	[Column(Storage="_CompId", DbType="Int")]
	public System.Nullable<int> CompId
	{
		get
		{
			return this._CompId;
		}
		set
		{
			if ((this._CompId != value))
			{
				this.OnCompIdChanging(value);
				this.SendPropertyChanging();
				this._CompId = value;
				this.SendPropertyChanged("CompId");
				this.OnCompIdChanged();
			}
		}
	}
	
	[Column(Storage="_FinYearId", DbType="Int")]
	public System.Nullable<int> FinYearId
	{
		get
		{
			return this._FinYearId;
		}
		set
		{
			if ((this._FinYearId != value))
			{
				this.OnFinYearIdChanging(value);
				this.SendPropertyChanging();
				this._FinYearId = value;
				this.SendPropertyChanged("FinYearId");
				this.OnFinYearIdChanged();
			}
		}
	}
	
	[Column(Storage="_SessionId", DbType="VarChar(MAX)")]
	public string SessionId
	{
		get
		{
			return this._SessionId;
		}
		set
		{
			if ((this._SessionId != value))
			{
				this.OnSessionIdChanging(value);
				this.SendPropertyChanging();
				this._SessionId = value;
				this.SendPropertyChanged("SessionId");
				this.OnSessionIdChanged();
			}
		}
	}
	
	[Column(Storage="_WONo", DbType="VarChar(MAX)")]
	public string WONo
	{
		get
		{
			return this._WONo;
		}
		set
		{
			if ((this._WONo != value))
			{
				this.OnWONoChanging(value);
				this.SendPropertyChanging();
				this._WONo = value;
				this.SendPropertyChanged("WONo");
				this.OnWONoChanged();
			}
		}
	}
	
	[Column(Storage="_Hour", DbType="Float")]
	public System.Nullable<double> Hour
	{
		get
		{
			return this._Hour;
		}
		set
		{
			if ((this._Hour != value))
			{
				this.OnHourChanging(value);
				this.SendPropertyChanging();
				this._Hour = value;
				this.SendPropertyChanged("Hour");
				this.OnHourChanged();
			}
		}
	}
	
	[Column(Storage="_BudgetCodeId", DbType="Int")]
	public System.Nullable<int> BudgetCodeId
	{
		get
		{
			return this._BudgetCodeId;
		}
		set
		{
			if ((this._BudgetCodeId != value))
			{
				this.OnBudgetCodeIdChanging(value);
				this.SendPropertyChanging();
				this._BudgetCodeId = value;
				this.SendPropertyChanged("BudgetCodeId");
				this.OnBudgetCodeIdChanged();
			}
		}
	}
	
	public event PropertyChangingEventHandler PropertyChanging;
	
	public event PropertyChangedEventHandler PropertyChanged;
	
	protected virtual void SendPropertyChanging()
	{
		if ((this.PropertyChanging != null))
		{
			this.PropertyChanging(this, emptyChangingEventArgs);
		}
	}
	
	protected virtual void SendPropertyChanged(String propertyName)
	{
		if ((this.PropertyChanged != null))
		{
			this.PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
		}
	}
}
#pragma warning restore 1591
