from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from ..models import (
    CustomerChallanMaster, CustomerChallanDetails,
    SupplierChallanMaster,
    SupplierChallan, ChallanClearance
)


class CustomerChallanForm(forms.ModelForm):
    """Form for creating and editing Customer Challans"""
    
    class Meta:
        model = CustomerChallanMaster
        fields = ['customer_id', 'work_order_number']
        widgets = {
            'customer_id': forms.TextInput(attrs={
                'class': 'sap-input',
                'placeholder': 'Customer ID'
            }),
            'work_order_number': forms.TextInput(attrs={
                'class': 'sap-input',
                'placeholder': 'Work Order Number'
            })
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.company = kwargs.pop('company', None)
        self.financial_year = kwargs.pop('financial_year', None)
        super().__init__(*args, **kwargs)

    def save(self, commit=True):
        instance = super().save(commit=False)
        
        if not instance.pk:
            # Set system fields for new instances
            from django.utils import timezone
            instance.sys_date = timezone.now().strftime('%d-%m-%Y')
            instance.sys_time = timezone.now().strftime('%H:%M:%S')
            instance.company = self.company
            instance.financial_year = self.financial_year
            
            # Auto-generate challan number
            if not instance.challan_number:
                last_challan = CustomerChallanMaster.objects.filter(
                    company=self.company
                ).order_by('-id').first()
                
                if last_challan and last_challan.challan_number:
                    try:
                        last_num = int(last_challan.challan_number)
                        instance.challan_number = f"{last_num + 1:04d}"
                    except (ValueError, TypeError):
                        instance.challan_number = "0001"
                else:
                    instance.challan_number = "0001"
            
        if commit:
            instance.save()
        
        return instance


class CustomerChallanLineItemForm(forms.ModelForm):
    """Form for Customer Challan line items"""
    
    class Meta:
        model = CustomerChallanDetails
        fields = ['challan_quantity', 'item']
        widgets = {
            'challan_quantity': forms.NumberInput(attrs={
                'class': 'sap-input',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'item': forms.Select(attrs={
                'class': 'sap-select'
            })
        }

    def clean_challan_quantity(self):
        challan_quantity = self.cleaned_data.get('challan_quantity')
        if challan_quantity is not None and challan_quantity <= 0:
            raise ValidationError("Challan quantity must be greater than zero.")
        return challan_quantity


class SupplierChallanForm(forms.ModelForm):
    """Form for creating and editing Supplier Challans"""
    
    class Meta:
        model = SupplierChallanMaster
        fields = [
            'challan_date', 'supplier_name', 'supplier_address', 'po_number',
            'po_date', 'invoice_number', 'invoice_date', 'vehicle_number',
            'driver_name', 'driver_mobile', 'transporter_name', 'gate_entry_number',
            'gate_entry_time', 'expected_delivery_date', 'remarks'
        ]
        widgets = {
            'challan_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md'
            }),
            'supplier_name': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Supplier Name',
                'list': 'supplier-list'
            }),
            'supplier_address': forms.Textarea(attrs={
                'rows': 3,
                'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md',
                'placeholder': 'Supplier Address'
            }),
            'po_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Purchase Order Number'
            }),
            'po_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md'
            }),
            'invoice_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Invoice Number'
            }),
            'invoice_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md'
            }),
            'vehicle_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Vehicle Number'
            }),
            'driver_name': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Driver Name'
            }),
            'driver_mobile': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Driver Mobile Number'
            }),
            'transporter_name': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Transporter Name'
            }),
            'gate_entry_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Gate Entry Number'
            }),
            'gate_entry_time': forms.DateTimeInput(attrs={
                'type': 'datetime-local',
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md'
            }),
            'expected_delivery_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md'
            }),
            'remarks': forms.Textarea(attrs={
                'rows': 3,
                'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md',
                'placeholder': 'Enter any additional remarks or instructions'
            })
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.company = kwargs.pop('company', None)
        self.financial_year = kwargs.pop('financial_year', None)
        super().__init__(*args, **kwargs)
        
        # Set default dates
        if not self.instance.pk:
            self.fields['challan_date'].initial = timezone.now().date()

    def clean_challan_date(self):
        challan_date = self.cleaned_data.get('challan_date')
        if challan_date and challan_date > timezone.now().date():
            raise ValidationError("Challan date cannot be in the future.")
        return challan_date

    def clean_supplier_name(self):
        supplier_name = self.cleaned_data.get('supplier_name')
        if not supplier_name:
            raise ValidationError("Supplier name is required.")
        return supplier_name.strip()

    def save(self, commit=True):
        instance = super().save(commit=False)
        
        if not instance.pk:
            # Set system fields for new instances
            instance.company = self.company
            instance.financial_year = self.financial_year
            instance.created_by = self.user
            
        if commit:
            instance.save()
            # Generate challan number if not set
            if not instance.challan_number:
                instance.generate_challan_number()
                instance.save()
        
        return instance


# TODO: Fix SupplierChallanLineItemForm after model is corrected


class ChallanClearanceForm(forms.ModelForm):
    """Form for creating challan clearances"""
    
    class Meta:
        model = ChallanClearance
        fields = [
            'clearance_date', 'clearance_type', 'supplier_challan', 'customer_challan',
            'total_cleared_quantity', 'total_cleared_value', 'remarks'
        ]
        widgets = {
            'clearance_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md'
            }),
            'clearance_type': forms.Select(attrs={
                'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'supplier_challan': forms.Select(attrs={
                'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'customer_challan': forms.Select(attrs={
                'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'total_cleared_quantity': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'total_cleared_value': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'remarks': forms.Textarea(attrs={
                'rows': 3,
                'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md',
                'placeholder': 'Clearance remarks'
            })
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.company = kwargs.pop('company', None)
        self.financial_year = kwargs.pop('financial_year', None)
        super().__init__(*args, **kwargs)
        
        # Set default date
        if not self.instance.pk:
            self.fields['clearance_date'].initial = timezone.now().date()
        
        # Filter challans based on company and clearable status
        if self.company:
            self.fields['supplier_challan'].queryset = SupplierChallanMaster.objects.filter(
                company=self.company,
                status__in=['RECEIVED', 'UNDER_INSPECTION', 'PARTIALLY_CLEARED']
            ).order_by('-challan_date')
            
            self.fields['customer_challan'].queryset = CustomerChallanMaster.objects.filter(
                company=self.company,
                status__in=['DISPATCHED', 'IN_TRANSIT']
            ).order_by('-challan_date')

    def clean(self):
        cleaned_data = super().clean()
        clearance_type = cleaned_data.get('clearance_type')
        supplier_challan = cleaned_data.get('supplier_challan')
        customer_challan = cleaned_data.get('customer_challan')
        
        if clearance_type == 'SUPPLIER' and not supplier_challan:
            raise ValidationError("Supplier challan is required for supplier clearance.")
        
        if clearance_type == 'CUSTOMER' and not customer_challan:
            raise ValidationError("Customer challan is required for customer clearance.")
        
        if clearance_type == 'SUPPLIER' and customer_challan:
            raise ValidationError("Customer challan should not be selected for supplier clearance.")
        
        if clearance_type == 'CUSTOMER' and supplier_challan:
            raise ValidationError("Supplier challan should not be selected for customer clearance.")
        
        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        
        if not instance.pk:
            # Set system fields for new instances
            instance.company = self.company
            instance.financial_year = self.financial_year
            instance.cleared_by = self.user
            instance.created_by = self.user
            
        if commit:
            instance.save()
            # Generate clearance number if not set
            if not instance.clearance_number:
                instance.generate_clearance_number()
                instance.save()
        
        return instance


class CustomerChallanSearchForm(forms.Form):
    """Search form for Customer Challans"""
    
    search = forms.CharField(
        required=False,
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md',
            'placeholder': 'Search by challan number, customer name, or work order...',
            'hx-get': '/inventory/customer-challans/',
            'hx-trigger': 'keyup changed delay:300ms',
            'hx-target': '#challan-results',
            'hx-swap': 'innerHTML'
        })
    )
    


class SupplierChallanSearchForm(forms.Form):
    """Search form for Supplier Challans"""
    
    search = forms.CharField(
        required=False,
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md',
            'placeholder': 'Search by challan number, supplier name, or PO number...',
            'hx-get': '/inventory/supplier-challans/',
            'hx-trigger': 'keyup changed delay:300ms',
            'hx-target': '#challan-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    status = forms.ChoiceField(
        required=False,
        choices=[('', 'All Status')] + SupplierChallanMaster._meta.get_field('status').choices,
        widget=forms.Select(attrs={
            'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/inventory/supplier-challans/',
            'hx-trigger': 'change',
            'hx-target': '#challan-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    pending_clearance = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
            'hx-get': '/inventory/supplier-challans/',
            'hx-trigger': 'change',
            'hx-target': '#challan-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
            'hx-get': '/inventory/supplier-challans/',
            'hx-trigger': 'change',
            'hx-target': '#challan-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
            'hx-get': '/inventory/supplier-challans/',
            'hx-trigger': 'change',
            'hx-target': '#challan-results',
            'hx-swap': 'innerHTML'
        })
    )


class SupplierChallanLegacyForm(forms.ModelForm):
    """Form for creating and editing Supplier Challans using legacy ASP.NET structure"""
    
    class Meta:
        model = SupplierChallan
        fields = ['scno', 'supplierid', 'vehicleno', 'transpoter', 'remarks']
        widgets = {
            'scno': forms.TextInput(attrs={
                'class': 'sap-input',
                'placeholder': 'Auto-generated',
                'readonly': True
            }),
            'supplierid': forms.TextInput(attrs={
                'class': 'sap-input',
                'placeholder': 'Supplier ID'
            }),
            'vehicleno': forms.TextInput(attrs={
                'class': 'sap-input',
                'placeholder': 'Enter vehicle number'
            }),
            'transpoter': forms.TextInput(attrs={
                'class': 'sap-input',
                'placeholder': 'Enter transporter name'
            }),
            'remarks': forms.Textarea(attrs={
                'class': 'sap-input',
                'rows': 3,
                'placeholder': 'Enter any remarks or notes...'
            })
        }
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.company = kwargs.pop('company', None)
        self.financial_year = kwargs.pop('financial_year', None)
        super().__init__(*args, **kwargs)
        
        # Make SCNo readonly for new records
        if not self.instance.pk:
            self.fields['scno'].widget.attrs['readonly'] = True
            self.fields['scno'].widget.attrs['placeholder'] = 'Auto-generated'
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        
        if not instance.pk:
            # Set system fields for new records
            from django.utils import timezone
            import datetime
            
            instance.sysdate = timezone.now().strftime('%d-%m-%Y')
            instance.systime = timezone.now().strftime('%H:%M:%S')
            instance.user = self.user
            instance.company_id = self.company
            instance.financial_year_id = self.financial_year
            
            # Auto-generate SCNo if not provided
            if not instance.scno:
                # Get next sequence number for this company
                last_challan = SupplierChallan.objects.filter(
                    company_id=self.company
                ).order_by('-id').first()
                
                if last_challan and last_challan.scno:
                    try:
                        last_num = int(last_challan.scno)
                        instance.scno = f"{last_num + 1:04d}"
                    except (ValueError, TypeError):
                        instance.scno = "0001"
                else:
                    instance.scno = "0001"
        
        if commit:
            instance.save()
        return instance