<!-- accounts/templates/accounts/masters/currency_list.html -->
<!-- Professional Currency Management - SAP S/4HANA Inspired Design -->
<!-- Replaces ASP.NET Currency.aspx functionality -->

{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }} - Cortex{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-sap-orange-500 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="coins" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Currency Management</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage currencies and their symbols by country</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <div class="text-right mr-4 px-4 py-2 bg-sap-gray-50 rounded-lg border border-sap-gray-200">
                    <p class="text-2xs font-medium text-sap-gray-500 uppercase tracking-wide">Total Currencies</p>
                    <p class="text-lg font-semibold text-sap-orange-600" id="currency-count">{{ currencies|length }}</p>
                </div>
                <a href="{% url 'accounts:dashboard' %}" 
                   class="inline-flex items-center px-4 py-2.5 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 hover:border-sap-blue-300 transition-all duration-200">
                    <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-2 py-4 space-y-2">
    
    <!-- Add New Currency Card -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-3 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="plus" class="w-4 h-4 text-sap-orange-600"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-sap-gray-800">Add New Currency</h3>
                        <p class="text-sm text-sap-gray-600">Create a new currency entry</p>
                    </div>
                </div>
                <button type="button" id="toggle-form-btn"
                        class="inline-flex items-center px-4 py-2 border border-sap-orange-300 rounded-lg text-sm font-medium text-sap-orange-700 bg-sap-orange-50 hover:bg-sap-orange-100 transition-colors duration-200">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    <span id="toggle-form-text">Show Form</span>
                </button>
            </div>
        </div>
        
        <!-- Add Currency Form (Hidden by default) -->
        <div id="add-currency-form" class="px-6 py-4 border-b border-sap-gray-100 hidden">
            <form hx-post="{% url 'accounts:currency_create' %}" 
                  hx-target="#currency-table" 
                  hx-swap="outerHTML"
                  hx-trigger="submit"
                  class="space-y-4">
                {% csrf_token %}
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="{{ currency_form.country.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Country <span class="text-red-500">*</span>
                        </label>
                        {{ currency_form.country }}
                        {% if currency_form.country.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ currency_form.country.errors.0 }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ currency_form.name.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Currency Name <span class="text-red-500">*</span>
                        </label>
                        {{ currency_form.name }}
                        {% if currency_form.name.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ currency_form.name.errors.0 }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ currency_form.symbol.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Symbol <span class="text-red-500">*</span>
                        </label>
                        {{ currency_form.symbol }}
                        {% if currency_form.symbol.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ currency_form.symbol.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="hideForm()" 
                            class="px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-sap-orange-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-sap-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-orange-500">
                        Insert Record
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Real-time Search Card -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4">
            <div class="flex items-center space-x-4">
                <div class="flex-1">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i data-lucide="search" class="w-4 h-4 text-sap-gray-400"></i>
                        </div>
                        <input type="text" 
                               id="real-time-search"
                               name="search" 
                               placeholder="Search by currency name, symbol, or country..." 
                               value="{{ request.GET.search|default:'' }}"
                               hx-get="{% url 'accounts:currency_list' %}"
                               hx-target="#currency-table"
                               hx-swap="outerHTML"
                               hx-trigger="keyup changed delay:300ms, search"
                               class="block w-full pl-10 pr-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm placeholder-sap-gray-500 focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
                    </div>
                </div>
                <div class="flex space-x-2">
                    <button type="button" 
                            onclick="clearSearch()"
                            class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                        <i data-lucide="x" class="w-4 h-4 mr-2"></i>
                        Clear
                    </button>
                </div>
            </div>
            <div class="mt-2 text-xs text-sap-gray-500">
                <i data-lucide="info" class="w-3 h-3 inline mr-1"></i>
                Search results update automatically as you type
            </div>
        </div>
    </div>

    <!-- Currencies Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Currencies</h3>
                <div class="text-sm text-sap-gray-600">
                    Showing {{ currencies|length }} currencies
                </div>
            </div>
        </div>
        
        <div id="currency-table">
            {% include 'accounts/partials/currency_table.html' %}
        </div>
    </div>

</div>

<script>
function showForm() {
    document.getElementById('add-currency-form').classList.remove('hidden');
    document.getElementById('toggle-form-text').textContent = 'Hide Form';
    document.getElementById('toggle-form-btn').onclick = hideForm;
}

function hideForm() {
    document.getElementById('add-currency-form').classList.add('hidden');
    document.getElementById('toggle-form-text').textContent = 'Show Form';
    document.getElementById('toggle-form-btn').onclick = showForm;
    // Clear form
    document.querySelector('#add-currency-form form').reset();
}

// Clear search function
function clearSearch() {
    document.getElementById('real-time-search').value = '';
    // Trigger HTMX to reload with cleared filters
    htmx.trigger('#real-time-search', 'keyup');
}

// Toggle form visibility
document.getElementById('toggle-form-btn').onclick = showForm;

// Confirmation dialogs
function confirmDelete() {
    return confirm('Are you sure you want to delete this currency? This action cannot be undone.');
}
</script>
{% endblock %}