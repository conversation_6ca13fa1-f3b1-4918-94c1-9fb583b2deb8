{% extends 'core/base.html' %}
{% load static %}

{% block title %}Daily Reporting System - Dashboard{% endblock %}

{% block extra_css %}
<style>
    .stat-card {
        transition: transform 0.2s ease-in-out;
    }
    .stat-card:hover {
        transform: translateY(-5px);
    }
    .progress-bar-custom {
        height: 8px;
        border-radius: 10px;
    }
    .activity-item {
        border-left: 4px solid #007bff;
        padding-left: 1rem;
        margin-bottom: 1rem;
    }
    .chart-container {
        position: relative;
        height: 300px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Daily Reporting System</h1>
            <p class="text-muted">Real-time tracking and project management dashboard</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{% url 'daily_reporting:tracker_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>New Report
            </a>
            <a href="{% url 'daily_reporting:export_reports' %}" class="btn btn-outline-secondary">
                <i class="fas fa-download me-1"></i>Export
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2 stat-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Reports Today
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_reports_today }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2 stat-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Reports This Week
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_reports_week }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-week fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2 stat-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Active Work Orders
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ active_work_orders }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2 stat-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Total Plans
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ total_design_plans|add:total_manufacturing_plans|add:total_vendor_plans }}
                            </div>
                            <div class="text-xs text-muted">
                                Design: {{ total_design_plans }} | Mfg: {{ total_manufacturing_plans }} | Vendor: {{ total_vendor_plans }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-project-diagram fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Dashboard Content -->
    <div class="row">
        <!-- Recent Reports Table -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Daily Reports</h6>
                    <a href="{% url 'daily_reporting:tracker_list' %}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    {% if recent_reports %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Employee</th>
                                        <th>Department</th>
                                        <th>WO Number</th>
                                        <th>Date</th>
                                        <th>Progress</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for report in recent_reports %}
                                    <tr>
                                        <td>
                                            <div class="fw-bold">{{ report.employee_name }}</div>
                                            <small class="text-muted">{{ report.designation }}</small>
                                        </td>
                                        <td>{{ report.department }}</td>
                                        <td>
                                            <span class="badge bg-primary">{{ report.wo_number }}</span>
                                        </td>
                                        <td>{{ report.date_of_reporting|date:"M d, Y" }}</td>
                                        <td>
                                            <div class="progress progress-bar-custom">
                                                <div class="progress-bar" role="progressbar" 
                                                     style="width: {{ report.percentage_completed }}%"
                                                     aria-valuenow="{{ report.percentage_completed }}" 
                                                     aria-valuemin="0" aria-valuemax="100">
                                                </div>
                                            </div>
                                            <small>{{ report.percentage_completed }}% Complete</small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{% url 'daily_reporting:tracker_detail' report.pk %}" 
                                                   class="btn btn-outline-info btn-sm" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{% url 'daily_reporting:tracker_edit' report.pk %}" 
                                                   class="btn btn-outline-warning btn-sm" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No reports found. <a href="{% url 'daily_reporting:tracker_create' %}">Create your first report</a></p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Quick Actions & Recent Activities -->
        <div class="col-lg-4 mb-4">
            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'daily_reporting:tracker_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>New Daily Report
                        </a>
                        <a href="{% url 'daily_reporting:design_plan_create' %}" class="btn btn-outline-primary">
                            <i class="fas fa-drafting-compass me-2"></i>Design Plan
                        </a>
                        <a href="{% url 'daily_reporting:manufacturing_plan_create' %}" class="btn btn-outline-primary">
                            <i class="fas fa-industry me-2"></i>Manufacturing Plan
                        </a>
                        <a href="{% url 'daily_reporting:vendor_plan_create' %}" class="btn btn-outline-primary">
                            <i class="fas fa-handshake me-2"></i>Vendor Plan
                        </a>
                        <a href="{% url 'daily_reporting:project_reports' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-chart-bar me-2"></i>View Reports
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent Plan Activities -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Plan Activities</h6>
                </div>
                <div class="card-body">
                    {% if recent_design_plans %}
                        <h6 class="text-success">Design Plans</h6>
                        {% for plan in recent_design_plans %}
                        <div class="activity-item">
                            <div class="fw-bold">{{ plan.wo_number }}</div>
                            <small class="text-muted">{{ plan.fixture_number }} - {{ plan.sys_date|timesince }} ago</small>
                        </div>
                        {% endfor %}
                    {% endif %}

                    {% if recent_manufacturing_plans %}
                        <h6 class="text-info mt-3">Manufacturing Plans</h6>
                        {% for plan in recent_manufacturing_plans %}
                        <div class="activity-item">
                            <div class="fw-bold">{{ plan.wo_number }}</div>
                            <small class="text-muted">{{ plan.item_number }} - {{ plan.sys_date|timesince }} ago</small>
                        </div>
                        {% endfor %}
                    {% endif %}

                    {% if recent_vendor_plans %}
                        <h6 class="text-warning mt-3">Vendor Plans</h6>
                        {% for plan in recent_vendor_plans %}
                        <div class="activity-item">
                            <div class="fw-bold">{{ plan.wo_number }}</div>
                            <small class="text-muted">{{ plan.fixture_number }} - {{ plan.sys_date|timesince }} ago</small>
                        </div>
                        {% endfor %}
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Search</h6>
                </div>
                <div class="card-body">
                    <form method="get" class="row g-3" hx-get="{% url 'daily_reporting:search_reports' %}" hx-target="#search-results">
                        <div class="col-md-3">
                            {{ filter_form.search_type }}
                        </div>
                        <div class="col-md-6">
                            {{ filter_form.search_value }}
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-1"></i>Search
                            </button>
                        </div>
                    </form>
                    <div id="search-results" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/htmx.org@1.9.10"></script>
<script>
    // Auto-refresh dashboard stats every 30 seconds
    setInterval(function() {
        fetch('{% url "daily_reporting:dashboard_stats_api" %}')
            .then(response => response.json())
            .then(data => {
                // Update dashboard statistics
                console.log('Dashboard stats updated', data);
            });
    }, 30000);

    // Initialize tooltips
    document.addEventListener('DOMContentLoaded', function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}