# accounts/reconciliation_forms.py
# Django forms for Bank Reconciliation functionality
# Task Group 2: Banking & Cash Management - Reconciliation Forms

from django import forms
from django.core.exceptions import ValidationError
from datetime import date

from .models import (
    BankReconciliationMaster, BankReconciliationDetails, Bank
)


class BankReconciliationMasterForm(forms.ModelForm):
    """
    Form for Bank Reconciliation Master
    Replaces ASP.NET BankReconciliation_New.aspx functionality
    """
    
    from_date = forms.DateField(
        widget=forms.DateInput(attrs={
            "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
            "type": "date"
        }),
        label="From Date",
        required=True
    )
    
    to_date = forms.DateField(
        widget=forms.DateInput(attrs={
            "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
            "type": "date"
        }),
        label="To Date",
        required=True
    )
    
    class Meta:
        model = BankReconciliationMaster
        fields = [
            "bank", "from_date", "to_date", "bank_statement_balance", 
            "book_balance", "remarks", "status"
        ]
        widgets = {
            "bank": forms.Select(attrs={
                "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                "required": True,
            }),
            "bank_statement_balance": forms.NumberInput(attrs={
                "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                "step": "0.01", "placeholder": "0.00", "required": True,
            }),
            "book_balance": forms.NumberInput(attrs={
                "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                "step": "0.01", "placeholder": "0.00", "required": True,
            }),
            "remarks": forms.Textarea(attrs={
                "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                "placeholder": "Enter remarks", "rows": 3
            }),
            "status": forms.Select(choices=[
                ('in_progress', 'In Progress'),
                ('completed', 'Completed'),
                ('pending', 'Pending'),
            ], attrs={
                "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            }),
        }
        labels = {
            "bank": "Bank",
            "bank_statement_balance": "Bank Statement Balance",
            "book_balance": "Book Balance",
            "remarks": "Remarks",
            "status": "Status",
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["bank"].queryset = Bank.objects.all()
        
        # Set default date range to current month
        today = date.today()
        first_day = today.replace(day=1)
        self.fields["from_date"].initial = first_day
        self.fields["to_date"].initial = today

    def clean(self):
        """Calculate difference amounts and validate date range"""
        cleaned_data = super().clean()
        
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')
        
        # Validate date range
        if from_date and to_date:
            if from_date > to_date:
                raise ValidationError("From date must be before to date")
            
            # Check if date range is reasonable (not more than 1 year)
            if (to_date - from_date).days > 365:
                raise ValidationError("Date range cannot exceed 365 days")
        
        # Calculate difference amounts
        bank_statement_balance = cleaned_data.get('bank_statement_balance') or 0
        book_balance = cleaned_data.get('book_balance') or 0
        
        difference_amount = abs(bank_statement_balance - book_balance)
        
        cleaned_data['difference_amount'] = difference_amount
        cleaned_data['reconciled_amount'] = 0  # Initial value
        cleaned_data['outstanding_amount'] = difference_amount  # Initial value
        
        return cleaned_data


class BankReconciliationDetailForm(forms.ModelForm):
    """
    Form for individual Bank Reconciliation Detail items
    """
    
    is_reconciled = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            "class": "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        }),
        label="Reconciled"
    )
    
    class Meta:
        model = BankReconciliationDetails
        fields = [
            "transaction_type", "transaction_no", "transaction_date", 
            "cheque_no", "cheque_date", "book_amount", "bank_amount", 
            "is_reconciled", "reconciled_date", "remarks"
        ]
        widgets = {
            "transaction_type": forms.Select(choices=[
                ('cash_voucher', 'Cash Voucher'),
                ('bank_voucher', 'Bank Voucher'),
                ('contra_entry', 'Contra Entry'),
                ('other', 'Other'),
            ], attrs={
                "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            }),
            "transaction_no": forms.TextInput(attrs={
                "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                "placeholder": "Transaction number"
            }),
            "transaction_date": forms.DateInput(attrs={
                "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                "type": "date"
            }),
            "cheque_no": forms.TextInput(attrs={
                "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                "placeholder": "Cheque number"
            }),
            "cheque_date": forms.DateInput(attrs={
                "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                "type": "date"
            }),
            "book_amount": forms.NumberInput(attrs={
                "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                "step": "0.01", "placeholder": "0.00"
            }),
            "bank_amount": forms.NumberInput(attrs={
                "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                "step": "0.01", "placeholder": "0.00"
            }),
            "reconciled_date": forms.DateInput(attrs={
                "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                "type": "date"
            }),
            "remarks": forms.Textarea(attrs={
                "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                "placeholder": "Enter remarks", "rows": 2
            }),
        }

    def clean(self):
        """Calculate difference amount and set reconciled date"""
        cleaned_data = super().clean()
        
        book_amount = cleaned_data.get('book_amount') or 0
        bank_amount = cleaned_data.get('bank_amount') or 0
        is_reconciled = cleaned_data.get('is_reconciled', False)
        
        # Calculate difference amount
        difference_amount = abs(book_amount - bank_amount)
        cleaned_data['difference_amount'] = difference_amount
        
        # Set reconciled date if marked as reconciled
        if is_reconciled and not cleaned_data.get('reconciled_date'):
            cleaned_data['reconciled_date'] = date.today()
        elif not is_reconciled:
            cleaned_data['reconciled_date'] = None
        
        return cleaned_data


class BankReconciliationSearchForm(forms.Form):
    """
    Form for searching unreconciled transactions
    """
    
    bank = forms.ModelChoiceField(
        queryset=Bank.objects.all(),
        required=True,
        widget=forms.Select(attrs={
            "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        }),
        label="Bank"
    )
    
    from_date = forms.DateField(
        widget=forms.DateInput(attrs={
            "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
            "type": "date"
        }),
        label="From Date",
        required=True
    )
    
    to_date = forms.DateField(
        widget=forms.DateInput(attrs={
            "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
            "type": "date"
        }),
        label="To Date",
        required=True
    )
    
    voucher_type = forms.ChoiceField(
        choices=[
            ('all', 'All Vouchers'),
            ('payment', 'Payment Vouchers'),
            ('receipt', 'Receipt Vouchers'),
        ],
        required=False,
        widget=forms.Select(attrs={
            "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        }),
        label="Voucher Type"
    )
    
    show_all = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            "class": "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        }),
        label="Show All (including reconciled)"
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set default date range to current month
        today = date.today()
        first_day = today.replace(day=1)
        self.fields["from_date"].initial = first_day
        self.fields["to_date"].initial = today
        self.fields["voucher_type"].initial = 'all'

    def clean(self):
        """Validate date range"""
        cleaned_data = super().clean()
        
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')
        
        if from_date and to_date:
            if from_date > to_date:
                raise ValidationError("From date must be before to date")
            
            # Check if date range is reasonable (not more than 1 year)
            if (to_date - from_date).days > 365:
                raise ValidationError("Date range cannot exceed 365 days")
        
        return cleaned_data


class BankReconciliationBulkUpdateForm(forms.Form):
    """
    Form for bulk updating reconciliation status
    """
    
    selected_transactions = forms.CharField(
        widget=forms.HiddenInput(),
        required=True
    )
    
    bank_date = forms.DateField(
        widget=forms.DateInput(attrs={
            "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
            "type": "date"
        }),
        label="Bank Date",
        required=True
    )
    
    additional_charges = forms.DecimalField(
        max_digits=15,
        decimal_places=2,
        initial=0,
        widget=forms.NumberInput(attrs={
            "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
            "step": "0.01", "placeholder": "0.00"
        }),
        label="Additional Charges",
        required=False
    )
    
    remarks = forms.CharField(
        widget=forms.Textarea(attrs={
            "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
            "placeholder": "Enter bulk reconciliation remarks", "rows": 3
        }),
        label="Remarks",
        required=False
    )
    
    action = forms.ChoiceField(
        choices=[
            ('reconcile', 'Mark as Reconciled'),
            ('unreconcile', 'Mark as Unreconciled'),
        ],
        widget=forms.Select(attrs={
            "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        }),
        label="Action",
        required=True
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["bank_date"].initial = date.today()

    def clean_selected_transactions(self):
        """Validate selected transactions"""
        selected = self.cleaned_data.get('selected_transactions', '')
        if not selected:
            raise ValidationError("No transactions selected")
        
        try:
            # Expect comma-separated IDs
            transaction_ids = [int(x.strip()) for x in selected.split(',') if x.strip()]
            if not transaction_ids:
                raise ValidationError("No valid transaction IDs provided")
            return transaction_ids
        except ValueError:
            raise ValidationError("Invalid transaction IDs format")

    def clean_additional_charges(self):
        """Validate additional charges"""
        charges = self.cleaned_data.get('additional_charges')
        if charges is not None and charges < 0:
            raise ValidationError("Additional charges cannot be negative")
        return charges or 0


class BankStatementUploadForm(forms.Form):
    """
    Form for uploading bank statement files for automated reconciliation
    """
    
    bank = forms.ModelChoiceField(
        queryset=Bank.objects.all(),
        required=True,
        widget=forms.Select(attrs={
            "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        }),
        label="Bank"
    )
    
    statement_file = forms.FileField(
        widget=forms.FileInput(attrs={
            "class": "block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100",
            "accept": ".csv,.xlsx,.xls"
        }),
        label="Bank Statement File",
        help_text="Upload CSV or Excel file with bank statement data",
        required=True
    )
    
    statement_date = forms.DateField(
        widget=forms.DateInput(attrs={
            "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
            "type": "date"
        }),
        label="Statement Date",
        required=True
    )
    
    auto_reconcile = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            "class": "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        }),
        label="Auto-reconcile matching transactions"
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["statement_date"].initial = date.today()

    def clean_statement_file(self):
        """Validate uploaded file"""
        file = self.cleaned_data.get('statement_file')
        if file:
            # Check file extension
            allowed_extensions = ['.csv', '.xlsx', '.xls']
            file_extension = file.name.lower()
            if not any(file_extension.endswith(ext) for ext in allowed_extensions):
                raise ValidationError("Only CSV and Excel files are allowed")
            
            # Check file size (max 10MB)
            if file.size > 10 * 1024 * 1024:
                raise ValidationError("File size cannot exceed 10MB")
        
        return file