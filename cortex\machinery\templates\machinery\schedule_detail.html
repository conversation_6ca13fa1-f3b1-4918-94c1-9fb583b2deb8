{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Schedule Details{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="sm:flex sm:items-center sm:justify-between mb-8">
        <div class="sm:flex-auto">
            <h1 class="text-2xl font-semibold text-gray-900">Schedule Details</h1>
            <p class="mt-2 text-sm text-gray-700">Complete information about this job schedule.</p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none space-x-3">
            <a href="{% url 'machinery:schedule_edit' schedule.pk %}" 
               class="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                Edit Schedule
            </a>
            <a href="{% url 'machinery:schedule_list' %}" 
               class="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                Back to List
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Basic Information</h3>
                    
                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Job Number</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ schedule.jobno|default:"Not assigned" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Work Order Number</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ schedule.wono|default:"Not specified" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Item ID</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ schedule.itemid|default:"Not specified" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Created Date</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ schedule.sysdate|default:"Not available" }}</dd>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Schedule Details -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Schedule Details</h3>
                    
                    {% if schedule_details %}
                    <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Machine</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Shift</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Schedule</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Process</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Personnel</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for detail in schedule_details %}
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        Machine {{ detail.machineid }}
                                        {% if detail.type %}
                                            <div class="text-xs text-gray-500">Type: {{ detail.type }}</div>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {% if detail.shift %}
                                            Shift {{ detail.shift }}
                                        {% else %}
                                            Not set
                                        {% endif %}
                                        {% if detail.batchno %}
                                            <div class="text-xs text-gray-400">Batch: {{ detail.batchno }}</div>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <div class="space-y-1">
                                            {% if detail.fromdate %}
                                                <div class="flex items-center">
                                                    <svg class="h-4 w-4 text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                    </svg>
                                                    <span>{{ detail.fromdate }}</span>
                                                    {% if detail.todate and detail.todate != detail.fromdate %}
                                                        <span class="mx-1">→</span>
                                                        <span>{{ detail.todate }}</span>
                                                    {% endif %}
                                                </div>
                                            {% endif %}
                                            {% if detail.fromtime %}
                                                <div class="flex items-center">
                                                    <svg class="h-4 w-4 text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                    <span>{{ detail.fromtime }}</span>
                                                    {% if detail.totime %}
                                                        <span class="mx-1">→</span>
                                                        <span>{{ detail.totime }}</span>
                                                    {% endif %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {% if detail.process %}
                                            Process {{ detail.process }}
                                        {% else %}
                                            Not specified
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ detail.qty|default:"Not set" }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <div>
                                            <strong>In-charge:</strong> {{ detail.incharge|default:"Not assigned" }}
                                        </div>
                                        {% if detail.operator %}
                                            <div class="text-xs text-gray-400">
                                                <strong>Operator:</strong> {{ detail.operator }}
                                            </div>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        {% if detail.released == 1 %}
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                                Released
                                            </span>
                                            {% if detail.releaseddate %}
                                                <div class="text-xs text-gray-500 mt-1">
                                                    {{ detail.releaseddate }} {{ detail.releasedtime }}
                                                </div>
                                                {% if detail.releasedby %}
                                                    <div class="text-xs text-gray-400">
                                                        By: {{ detail.releasedby }}
                                                    </div>
                                                {% endif %}
                                            {% endif %}
                                        {% else %}
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                Pending
                                            </span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No schedule details</h3>
                        <p class="mt-1 text-sm text-gray-500">No detailed schedule items have been added yet.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Actions -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
                    
                    <div class="space-y-3">
                        <button type="button" 
                                class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            Release Schedule
                        </button>
                        
                        <button type="button" 
                                class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Add Schedule Detail
                        </button>
                        
                        <button type="button" 
                                class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Print Schedule
                        </button>
                    </div>
                </div>
            </div>

            <!-- Schedule Statistics -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Schedule Statistics</h3>
                    
                    <div class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Total Details</dt>
                            <dd class="mt-1 text-lg font-semibold text-gray-900">{{ schedule_details.count }}</dd>
                        </div>
                        
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Released Items</dt>
                            <dd class="mt-1 text-lg font-semibold text-green-600">
                                {% if schedule_details %}
                                    {{ schedule_details|length|add:"0" }}
                                {% else %}
                                    0
                                {% endif %}
                            </dd>
                        </div>
                        
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Pending Items</dt>
                            <dd class="mt-1 text-lg font-semibold text-yellow-600">
                                {% if schedule_details %}
                                    {{ schedule_details|length|add:"0" }}
                                {% else %}
                                    0
                                {% endif %}
                            </dd>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Schedule Info -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Schedule Information</h3>
                    
                    <div class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Created By</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ schedule.user.username|default:"System" }}</dd>
                        </div>
                        
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Company</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ schedule.company.companyname|default:"Not specified" }}</dd>
                        </div>
                        
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Financial Year</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ schedule.financial_year.finyear|default:"Not specified" }}</dd>
                        </div>
                        
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Created Time</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ schedule.systime|default:"Not available" }}</dd>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Related Information -->
    {% if schedule.wono %}
    <div class="mt-8">
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Work Order Information</h3>
                
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-sm font-medium text-blue-800">Work Order: {{ schedule.wono }}</h4>
                            <div class="mt-2 text-sm text-blue-700">
                                <p>This schedule is associated with work order {{ schedule.wono }}. View the complete work order details for full context.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}