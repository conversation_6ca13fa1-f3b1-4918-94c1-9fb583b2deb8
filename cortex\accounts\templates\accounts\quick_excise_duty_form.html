<!-- accounts/templates/accounts/quick_excise_duty_form.html -->
<!-- Quick Excise Duty Creation Form Template -->
<!-- Task Group 4: Taxation Management - Quick Excise Duty Form -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Quick Create Excise Duty - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-green-600 to-sap-green-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="zap" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Quick Create Excise Duty</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Create excise duty configurations using common presets</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:excise_duty_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
                <a href="{% url 'accounts:excise_duty_create' %}" 
                   class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="settings" class="w-4 h-4 inline mr-2"></i>
                    Advanced Form
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6" x-data="quickExciseDutyForm()">
    
    <div class="max-w-3xl mx-auto">
        <!-- Quick Info -->
        <div class="bg-sap-green-50 rounded-lg p-4 mb-6">
            <div class="flex">
                <i data-lucide="info" class="w-5 h-5 text-sap-green-400 mr-3"></i>
                <div class="text-sm text-sap-green-800">
                    <p class="font-medium mb-1">Quick Create Mode</p>
                    <p>Choose from common excise duty presets to quickly create configurations. For custom rates, use the advanced form.</p>
                </div>
            </div>
        </div>
        
        <!-- Main Form -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800">Configuration Details</h3>
                <p class="text-sm text-sap-gray-600 mt-1">Enter a description and select a preset configuration</p>
            </div>
            
            <form method="post" class="p-6">
                {% csrf_token %}
                
                <!-- Error Messages -->
                {% if form.non_field_errors %}
                <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex">
                        <i data-lucide="alert-circle" class="w-5 h-5 text-red-400 mr-3"></i>
                        <div>
                            <h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                            <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                                {% for error in form.non_field_errors %}
                                <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <!-- Description Field -->
                <div class="mb-6">
                    <label for="{{ form.terms.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                        {{ form.terms.label }}
                        <span class="text-red-500">*</span>
                    </label>
                    {{ form.terms|add_class:"block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                    {% if form.terms.help_text %}
                    <p class="text-xs text-sap-gray-500 mt-1">{{ form.terms.help_text }}</p>
                    {% endif %}
                    {% if form.terms.errors %}
                    <p class="text-sm text-red-600 mt-1">{{ form.terms.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <!-- Preset Selection -->
                <div class="mb-8">
                    <label for="{{ form.preset.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-4">
                        {{ form.preset.label }}
                        <span class="text-red-500">*</span>
                    </label>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Custom Configuration -->
                        <label class="relative flex cursor-pointer">
                            <input type="radio" name="preset" value="custom" 
                                   class="sr-only peer" @change="updatePreview">
                            <div class="w-full p-4 border border-sap-gray-300 rounded-lg peer-checked:border-sap-green-500 peer-checked:bg-sap-green-50 hover:bg-sap-gray-50 transition-all duration-200">
                                <div class="flex items-start">
                                    <div class="w-8 h-8 bg-sap-gray-100 peer-checked:bg-sap-green-100 rounded-lg flex items-center justify-center mr-3">
                                        <i data-lucide="settings" class="w-4 h-4 text-sap-gray-600 peer-checked:text-sap-green-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-medium text-sap-gray-900">Custom Configuration</h4>
                                        <p class="text-xs text-sap-gray-600 mt-1">Define your own rates manually</p>
                                        <div class="text-xs text-sap-gray-500 mt-2">
                                            Use advanced form for custom rates
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </label>
                        
                        <!-- Standard Excise 12% + 2% + 1% -->
                        <label class="relative flex cursor-pointer">
                            <input type="radio" name="preset" value="excise_12_2_1" 
                                   class="sr-only peer" @change="updatePreview">
                            <div class="w-full p-4 border border-sap-gray-300 rounded-lg peer-checked:border-sap-blue-500 peer-checked:bg-sap-blue-50 hover:bg-sap-gray-50 transition-all duration-200">
                                <div class="flex items-start">
                                    <div class="w-8 h-8 bg-sap-blue-100 rounded-lg flex items-center justify-center mr-3">
                                        <i data-lucide="industry" class="w-4 h-4 text-sap-blue-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-medium text-sap-gray-900">Standard Excise</h4>
                                        <p class="text-xs text-sap-gray-600 mt-1">Most common manufacturing rate</p>
                                        <div class="text-xs text-sap-blue-600 mt-2 font-medium">
                                            12% + 2% EDU + 1% SHE = 15%
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </label>
                        
                        <!-- Reduced Excise 8% + 2% + 1% -->
                        <label class="relative flex cursor-pointer">
                            <input type="radio" name="preset" value="excise_8_2_1" 
                                   class="sr-only peer" @change="updatePreview">
                            <div class="w-full p-4 border border-sap-gray-300 rounded-lg peer-checked:border-sap-orange-500 peer-checked:bg-sap-orange-50 hover:bg-sap-gray-50 transition-all duration-200">
                                <div class="flex items-start">
                                    <div class="w-8 h-8 bg-sap-orange-100 rounded-lg flex items-center justify-center mr-3">
                                        <i data-lucide="trending-down" class="w-4 h-4 text-sap-orange-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-medium text-sap-gray-900">Reduced Excise</h4>
                                        <p class="text-xs text-sap-gray-600 mt-1">Lower rate for specific goods</p>
                                        <div class="text-xs text-sap-orange-600 mt-2 font-medium">
                                            8% + 2% EDU + 1% SHE = 11%
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </label>
                        
                        <!-- Service Tax 12% + 2% + 1% -->
                        <label class="relative flex cursor-pointer">
                            <input type="radio" name="preset" value="service_tax_12_2_1" 
                                   class="sr-only peer" @change="updatePreview">
                            <div class="w-full p-4 border border-sap-gray-300 rounded-lg peer-checked:border-sap-purple-500 peer-checked:bg-sap-purple-50 hover:bg-sap-gray-50 transition-all duration-200">
                                <div class="flex items-start">
                                    <div class="w-8 h-8 bg-sap-purple-100 rounded-lg flex items-center justify-center mr-3">
                                        <i data-lucide="briefcase" class="w-4 h-4 text-sap-purple-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-medium text-sap-gray-900">Service Tax (Legacy)</h4>
                                        <p class="text-xs text-sap-gray-600 mt-1">Traditional service tax rate</p>
                                        <div class="text-xs text-sap-purple-600 mt-2 font-medium">
                                            12% + 2% EDU + 1% SHE = 15%
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </label>
                        
                        <!-- Service Tax 15% -->
                        <label class="relative flex cursor-pointer">
                            <input type="radio" name="preset" value="service_tax_15" 
                                   class="sr-only peer" @change="updatePreview">
                            <div class="w-full p-4 border border-sap-gray-300 rounded-lg peer-checked:border-sap-green-500 peer-checked:bg-sap-green-50 hover:bg-sap-gray-50 transition-all duration-200">
                                <div class="flex items-start">
                                    <div class="w-8 h-8 bg-sap-green-100 rounded-lg flex items-center justify-center mr-3">
                                        <i data-lucide="zap" class="w-4 h-4 text-sap-green-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-medium text-sap-gray-900">Service Tax (Modern)</h4>
                                        <p class="text-xs text-sap-gray-600 mt-1">Simplified service tax rate</p>
                                        <div class="text-xs text-sap-green-600 mt-2 font-medium">
                                            15% (No cess)
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </label>
                        
                        <!-- Zero Rated -->
                        <label class="relative flex cursor-pointer">
                            <input type="radio" name="preset" value="zero_rated" 
                                   class="sr-only peer" @change="updatePreview">
                            <div class="w-full p-4 border border-sap-gray-300 rounded-lg peer-checked:border-sap-gray-500 peer-checked:bg-sap-gray-50 hover:bg-sap-gray-50 transition-all duration-200">
                                <div class="flex items-start">
                                    <div class="w-8 h-8 bg-sap-gray-100 rounded-lg flex items-center justify-center mr-3">
                                        <i data-lucide="minus-circle" class="w-4 h-4 text-sap-gray-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-medium text-sap-gray-900">Zero Rated</h4>
                                        <p class="text-xs text-sap-gray-600 mt-1">No excise duty applicable</p>
                                        <div class="text-xs text-sap-gray-600 mt-2 font-medium">
                                            0% (Exempt goods/services)
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </label>
                    </div>
                    
                    {% if form.preset.errors %}
                    <p class="text-sm text-red-600 mt-2">{{ form.preset.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <!-- Preview Section -->
                <div x-show="showPreview" x-transition class="mb-8 p-6 bg-sap-gray-50 rounded-lg">
                    <h4 class="text-base font-medium text-sap-gray-800 mb-4">Configuration Preview</h4>
                    
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                        <div class="text-center">
                            <div class="text-sm text-sap-gray-600">Basic Rate</div>
                            <div class="text-lg font-bold text-sap-blue-600" x-text="preview.basicRate + '%'"></div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-sap-gray-600">Education Cess</div>
                            <div class="text-lg font-bold text-sap-green-600" x-text="preview.eduCess + '%'"></div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-sap-gray-600">SHE Cess</div>
                            <div class="text-lg font-bold text-sap-orange-600" x-text="preview.sheCess + '%'"></div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-sap-gray-600">Total Rate</div>
                            <div class="text-xl font-bold text-sap-gray-800" x-text="preview.totalRate + '%'"></div>
                        </div>
                    </div>
                    
                    <!-- Sample Calculation -->
                    <div class="border-t border-sap-gray-200 pt-4">
                        <div class="text-sm font-medium text-sap-gray-800 mb-2">Sample Calculation (₹10,000 base amount):</div>
                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
                            <div>
                                <span class="text-sap-gray-600">Base Amount:</span>
                                <span class="font-medium ml-2">₹10,000</span>
                            </div>
                            <div>
                                <span class="text-sap-gray-600">Tax Amount:</span>
                                <span class="font-medium ml-2 text-sap-blue-600" x-text="'₹' + preview.sampleTax"></span>
                            </div>
                            <div>
                                <span class="text-sap-gray-600">Total Amount:</span>
                                <span class="font-medium ml-2 text-sap-gray-800" x-text="'₹' + preview.sampleTotal"></span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Form Actions -->
                <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'accounts:excise_duty_create' %}" 
                           class="text-sap-blue-600 hover:text-sap-blue-700 text-sm font-medium">
                            <i data-lucide="settings" class="w-4 h-4 inline mr-1"></i>
                            Need custom rates? Use advanced form
                        </a>
                    </div>
                    
                    <div class="flex items-center space-x-3">
                        <a href="{% url 'accounts:excise_duty_list' %}" 
                           class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-6 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="zap" class="w-4 h-4 inline mr-2"></i>
                            Quick Create
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Preset Information -->
        <div class="mt-6 bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-base font-medium text-sap-gray-800">Preset Information</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-medium text-sap-gray-800 mb-3">Excise Duty Presets</h4>
                        <ul class="text-sm text-sap-gray-600 space-y-2">
                            <li class="flex items-center">
                                <i data-lucide="check" class="w-4 h-4 text-sap-green-500 mr-2"></i>
                                Standard Excise: Common manufacturing rate (12% + cess)
                            </li>
                            <li class="flex items-center">
                                <i data-lucide="check" class="w-4 h-4 text-sap-green-500 mr-2"></i>
                                Reduced Excise: Special category goods (8% + cess)
                            </li>
                            <li class="flex items-center">
                                <i data-lucide="check" class="w-4 h-4 text-sap-green-500 mr-2"></i>
                                Zero Rated: Exempt goods and exports
                            </li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-sap-gray-800 mb-3">Service Tax Presets</h4>
                        <ul class="text-sm text-sap-gray-600 space-y-2">
                            <li class="flex items-center">
                                <i data-lucide="check" class="w-4 h-4 text-sap-green-500 mr-2"></i>
                                Legacy Service Tax: Traditional rate with cess (12% + cess)
                            </li>
                            <li class="flex items-center">
                                <i data-lucide="check" class="w-4 h-4 text-sap-green-500 mr-2"></i>
                                Modern Service Tax: Simplified rate (15% flat)
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function quickExciseDutyForm() {
    return {
        showPreview: false,
        preview: {
            basicRate: 0,
            eduCess: 0,
            sheCess: 0,
            totalRate: 0,
            sampleTax: 0,
            sampleTotal: 0
        },
        
        init() {
            lucide.createIcons();
        },
        
        updatePreview() {
            const selectedPreset = document.querySelector('input[name="preset"]:checked');
            if (!selectedPreset) {
                this.showPreview = false;
                return;
            }
            
            const presetConfigs = {
                'custom': { basic: 0, edu: 0, she: 0 },
                'excise_12_2_1': { basic: 12, edu: 2, she: 1 },
                'excise_8_2_1': { basic: 8, edu: 2, she: 1 },
                'service_tax_12_2_1': { basic: 12, edu: 2, she: 1 },
                'service_tax_15': { basic: 15, edu: 0, she: 0 },
                'zero_rated': { basic: 0, edu: 0, she: 0 }
            };
            
            const config = presetConfigs[selectedPreset.value];
            if (!config) return;
            
            this.preview.basicRate = config.basic;
            this.preview.eduCess = config.edu;
            this.preview.sheCess = config.she;
            this.preview.totalRate = config.basic + config.edu + config.she;
            
            // Calculate sample on ₹10,000
            const sampleAmount = 10000;
            const basicTax = sampleAmount * (config.basic / 100);
            const eduCessAmount = basicTax * (config.edu / 100);
            const sheCessAmount = basicTax * (config.she / 100);
            this.preview.sampleTax = (basicTax + eduCessAmount + sheCessAmount).toFixed(2);
            this.preview.sampleTotal = (sampleAmount + parseFloat(this.preview.sampleTax)).toFixed(2);
            
            this.showPreview = selectedPreset.value !== 'custom';
        }
    }
}

// Auto-select first option and show preview
document.addEventListener('DOMContentLoaded', function() {
    const firstRadio = document.querySelector('input[name="preset"][value="excise_12_2_1"]');
    if (firstRadio) {
        firstRadio.checked = true;
        // Trigger preview update
        setTimeout(() => {
            document.querySelector('[x-data="quickExciseDutyForm()"]').__x.$data.updatePreview();
        }, 100);
    }
});
</script>
{% endblock %}