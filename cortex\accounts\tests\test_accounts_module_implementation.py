# accounts/tests/test_accounts_module_implementation.py
# Comprehensive unit tests for Accounts Module Implementation
# Tests for all implemented task groups to verify completion

import datetime
from decimal import Decimal
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from sys_admin.models import Company, FinancialYear, City, Country, State
from accounts.models import (
    AccountHead, Bank, Currency, PaymentMode, PaymentTerms, PaidType,
    VAT, Excise, ExcisableCommodity, TDSCode, Octori,
    InvoiceAgainst, SalesInvoiceMaster, BalanceSheetConfig, AdvicePaymentMaster, SearchConfiguration,
    ExciseDuty
)


class AccountsModuleImplementationTestCase(TestCase):
    """
    Test suite for verifying complete accounts module implementation
    according to MODULE_TASK_PACKAGES/ACCOUNTS/ACCOUNTS_MODULE_IMPLEMENTATION_TASKS.md
    """
    
    def setUp(self):
        """Set up test data for all tests"""
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            email='<EMAIL>'
        )
        
        # Create location data
        self.country = Country.objects.create(name='India', code='IN')
        self.state = State.objects.create(name='Maharashtra', code='MH', country=self.country)
        self.city = City.objects.create(name='Mumbai', state=self.state)
        
        # Create test company
        self.company = Company.objects.create(
            name='Test Company Ltd',
            registered_address='Test Address',
            city=self.city
        )
        
        # Create financial year
        self.financial_year = FinancialYear.objects.create(
            year_name='2024-25',
            start_date=datetime.date(2024, 4, 1),
            end_date=datetime.date(2025, 3, 31),
            company=self.company
        )
        
        # Create client for URL testing
        self.client = Client()
        self.client.login(username='testuser', password='testpass123')


class TaskGroup1ChartOfAccountsTest(AccountsModuleImplementationTestCase):
    """Test Task Group 1: Chart of Accounts & General Setup"""
    
    def test_account_head_model_creation(self):
        """Test AccountHead model creation and basic functionality"""
        account_head = AccountHead.objects.create(
            category='Assets',
            description='Cash in Hand',
            symbol='CASH',
            abbreviation='CH'
        )
        
        self.assertEqual(str(account_head), 'Cash in Hand (CASH)')
        self.assertEqual(account_head.category, 'Assets')
        
    def test_account_head_list_view_access(self):
        """Test AccountHead list view is accessible"""
        url = reverse('accounts:account_head_list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        
    def test_account_head_create_view_access(self):
        """Test AccountHead create view is accessible"""
        url = reverse('accounts:account_head_create')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class TaskGroup2BankingCashManagementTest(AccountsModuleImplementationTestCase):
    """Test Task Group 2: Banking & Cash Management"""
    
    def test_bank_model_creation(self):
        """Test Bank model creation"""
        bank = Bank.objects.create(
            name='HDFC Bank',
            address='Mumbai Branch',
            country=self.country,
            state=self.state,
            city=self.city,
            pin_no='400001',
            contact_no='022-********',
            ifsc='HDFC0000123'
        )
        
        self.assertEqual(str(bank), 'HDFC Bank - HDFC0000123')
        
    def test_currency_model_creation(self):
        """Test Currency model creation"""
        currency = Currency.objects.create(
            country=self.country,
            name='Indian Rupee',
            symbol='₹'
        )
        
        self.assertEqual(str(currency), 'Indian Rupee (₹)')
        
    def test_bank_list_view_access(self):
        """Test Bank list view is accessible"""
        url = reverse('accounts:bank_list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        
    def test_currency_list_view_access(self):
        """Test Currency list view is accessible"""
        url = reverse('accounts:currency_list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class TaskGroup3PaymentManagementTest(AccountsModuleImplementationTestCase):
    """Test Task Group 3: Payment Management"""
    
    def test_payment_mode_model_creation(self):
        """Test PaymentMode model creation"""
        payment_mode = PaymentMode.objects.create(
            terms='Credit Card'
        )
        
        self.assertEqual(str(payment_mode), 'Credit Card')
        
    def test_payment_terms_model_creation(self):
        """Test PaymentTerms model creation"""
        payment_terms = PaymentTerms.objects.create(
            terms='Net 30 Days'
        )
        
        self.assertEqual(str(payment_terms), 'Net 30 Days')
        
    def test_paid_type_model_creation(self):
        """Test PaidType model creation"""
        paid_type = PaidType.objects.create(
            particulars='Advance Payment'
        )
        
        self.assertEqual(str(paid_type), 'Advance Payment')
        
    def test_payment_mode_list_view_access(self):
        """Test PaymentMode list view is accessible"""
        url = reverse('accounts:payment_mode_list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class TaskGroup4TaxationManagementTest(AccountsModuleImplementationTestCase):
    """Test Task Group 4: Taxation Management"""
    
    def test_vat_model_creation(self):
        """Test VAT model creation"""
        vat = VAT.objects.create(
            vat_percentage=18.0,
            description='Standard VAT Rate'
        )
        
        self.assertEqual(str(vat), '18.0% - Standard VAT Rate')
        
    def test_excise_model_creation(self):
        """Test Excise model creation"""
        excise = Excise.objects.create(
            excise_percentage=12.0,
            description='Standard Excise Duty'
        )
        
        self.assertEqual(str(excise), '12.0% - Standard Excise Duty')
        
    def test_tds_code_model_creation(self):
        """Test TDSCode model creation"""
        tds_code = TDSCode.objects.create(
            tds_code='194A',
            tds_percentage=10.0,
            description='Interest on Securities'
        )
        
        self.assertEqual(str(tds_code), '194A - 10.0%')
        
    def test_excisable_commodity_model_creation(self):
        """Test ExcisableCommodity model creation"""
        commodity = ExcisableCommodity.objects.create(
            commodity_name='Steel Products',
            commodity_code='SP001',
            description='Steel manufacturing products'
        )
        
        self.assertEqual(str(commodity), 'Steel Products (SP001)')
        
    def test_octori_model_creation(self):
        """Test Octori model creation"""
        octori = Octori.objects.create(
            octori_percentage=2.0,
            description='Local Entry Tax'
        )
        
        self.assertEqual(str(octori), '2.0% - Local Entry Tax')
        
    def test_vat_list_view_access(self):
        """Test VAT list view is accessible"""
        url = reverse('accounts:vat_list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        
    def test_tds_code_list_view_access(self):
        """Test TDS Code list view is accessible"""
        url = reverse('accounts:tds_code_list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class TaskGroup5InvoicingBillingTest(AccountsModuleImplementationTestCase):
    """Test Task Group 5: Invoicing & Billing"""
    
    def test_invoice_against_model_creation(self):
        """Test InvoiceAgainst model creation"""
        invoice_against = InvoiceAgainst.objects.create(
            description='Sales Order'
        )
        
        self.assertEqual(str(invoice_against), 'Sales Order')
        
    def test_sales_invoice_master_model_creation(self):
        """Test SalesInvoiceMaster model creation"""
        # First create required dependencies
        invoice_against = InvoiceAgainst.objects.create(description='Sales Order')
        
        sales_invoice = SalesInvoiceMaster.objects.create(
            invoice_number='SI-2024-001',
            invoice_date=datetime.date.today(),
            invoice_against=invoice_against,
            customer_name='Test Customer',
            subtotal_amount=Decimal('10000.00'),
            tax_amount=Decimal('1800.00'),
            total_amount=Decimal('11800.00'),
            company=self.company,
            financial_year=self.financial_year,
            created_by=self.user
        )
        
        self.assertEqual(str(sales_invoice), 'Invoice SI-2024-001 - Test Customer')
        self.assertEqual(sales_invoice.total_amount, Decimal('11800.00'))
        
    def test_invoicing_dashboard_view_access(self):
        """Test Invoicing dashboard view is accessible"""
        url = reverse('accounts:invoicing_dashboard')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        
    def test_sales_invoice_list_view_access(self):
        """Test Sales Invoice list view is accessible"""
        url = reverse('accounts:sales_invoice_list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class TaskGroup11FinancialReportingTest(AccountsModuleImplementationTestCase):
    """Test Task Group 11: Financial Reporting & Analysis"""
    
    def test_balance_sheet_config_model_creation(self):
        """Test BalanceSheetConfig model creation"""
        config = BalanceSheetConfig.objects.create(
            financial_year=self.financial_year,
            company=self.company
        )
        
        self.assertEqual(str(config), f'Balance Sheet Config - {self.financial_year} - {self.company}')
        
    def test_advice_payment_master_model_creation(self):
        """Test AdvicePaymentMaster model creation"""
        advice_payment = AdvicePaymentMaster.objects.create(
            ad_no='ADV-001',
            pay_to_type='Customer',
            pay_to_id=1,
            cheque_no='CHQ123',
            cheque_date=datetime.date.today(),
            drawn_on='HDFC Bank',
            payment_type='Advance',
            total_amount=Decimal('50000.00'),
            company=self.company,
            financial_year=self.financial_year,
            created_by=self.user
        )
        
        self.assertEqual(str(advice_payment), 'Advice ADV-001 - Customer')
        
    def test_search_configuration_model_creation(self):
        """Test SearchConfiguration model creation"""
        search_config = SearchConfiguration.objects.create(
            user=self.user,
            config_name='Default Sales Search',
            search_type='SALES',
            selected_columns=['invoice_number', 'customer_name', 'total_amount'],
            filter_parameters={'status': 'ACTIVE'},
            is_default=True
        )
        
        self.assertEqual(str(search_config), f'{self.user.username} - Default Sales Search')
        
    def test_balance_sheet_view_access(self):
        """Test Balance Sheet view is accessible"""
        url = reverse('accounts:balance_sheet')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        
    def test_reporting_dashboard_view_access(self):
        """Test Reporting dashboard view is accessible"""
        url = reverse('accounts:reporting_dashboard')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class VoucherProcessingTest(AccountsModuleImplementationTestCase):
    """Test Voucher Processing functionality (Task Group 2 enhancement)"""
    
    def test_cash_voucher_creation(self):
        """Test cash voucher functionality"""
        url = reverse('accounts:cash_voucher_list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        
    def test_bank_voucher_creation(self):
        """Test bank voucher functionality"""
        url = reverse('accounts:bank_voucher_list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        
    def test_contra_entry_creation(self):
        """Test contra entry functionality"""
        url = reverse('accounts:contra_entry_list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class ExciseDutyManagementTest(AccountsModuleImplementationTestCase):
    """Test Enhanced Excise Duty Management"""
    
    def test_excise_duty_calculation(self):
        """Test excise duty calculation functionality"""
        excise_duty = ExciseDuty.objects.create(
            terms='Standard Excise',
            value=Decimal('12.50'),
            accessible_value=Decimal('100.00'),
            edu_cess=Decimal('3.00'),
            she_cess=Decimal('1.00'),
            is_default_excise=True
        )
        
        # Test calculation
        calculation = excise_duty.calculate_excise_amount(Decimal('100000.00'))
        
        self.assertEqual(calculation['accessible_amount'], 100000.0)
        self.assertEqual(calculation['basic_excise'], 12500.0)  # 12.5% of 100000
        self.assertEqual(calculation['edu_cess'], 375.0)  # 3% of 12500
        self.assertEqual(calculation['she_cess'], 125.0)  # 1% of 12500
        self.assertEqual(calculation['total_excise'], 13000.0)  # Sum of all
        
    def test_excise_duty_list_view_access(self):
        """Test Excise Duty list view is accessible"""
        url = reverse('accounts:excise_duty_list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class ModuleIntegrationTest(AccountsModuleImplementationTestCase):
    """Integration tests to verify module-wide functionality"""
    
    def test_accounts_dashboard_access(self):
        """Test main accounts dashboard is accessible"""
        url = reverse('accounts:dashboard')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        
    def test_all_master_data_views_accessible(self):
        """Test all master data views are accessible"""
        master_urls = [
            'accounts:account_head_list',
            'accounts:bank_list',
            'accounts:currency_list',
            'accounts:payment_mode_list',
            'accounts:payment_terms_list',
            'accounts:paid_type_list',
            'accounts:vat_list',
            'accounts:tds_code_list',
            'accounts:invoice_against_list',
        ]
        
        for url_name in master_urls:
            with self.subTest(url=url_name):
                url = reverse(url_name)
                response = self.client.get(url)
                self.assertEqual(response.status_code, 200)
                
    def test_all_transaction_views_accessible(self):
        """Test all transaction views are accessible"""
        transaction_urls = [
            'accounts:sales_invoice_list',
            'accounts:proforma_invoice_list',
            'accounts:service_tax_invoice_list',
            'accounts:bill_booking_list',
            'accounts:cash_voucher_list',
            'accounts:bank_voucher_list',
            'accounts:contra_entry_list',
        ]
        
        for url_name in transaction_urls:
            with self.subTest(url=url_name):
                url = reverse(url_name)
                response = self.client.get(url)
                self.assertEqual(response.status_code, 200)
                
    def test_all_report_views_accessible(self):
        """Test all report views are accessible"""
        report_urls = [
            'accounts:reporting_dashboard',
            'accounts:balance_sheet',
            'accounts:advanced_search',
            'accounts:cash_bank_register',
            'accounts:vat_register_dashboard',
            'accounts:sales_vat_register',
            'accounts:purchase_vat_register',
        ]
        
        for url_name in report_urls:
            with self.subTest(url=url_name):
                url = reverse(url_name)
                response = self.client.get(url)
                self.assertEqual(response.status_code, 200)


class ImplementationComplianceTest(AccountsModuleImplementationTestCase):
    """Test compliance with implementation requirements"""
    
    def test_all_high_priority_tasks_implemented(self):
        """Verify all high-priority task groups are implemented"""
        
        # Task Group 1: Chart of Accounts & General Setup
        self.assertTrue(hasattr(AccountHead, 'description'))
        
        # Task Group 2: Banking & Cash Management  
        self.assertTrue(hasattr(Bank, 'ifsc'))
        self.assertTrue(hasattr(Currency, 'symbol'))
        
        # Task Group 3: Payment Management
        self.assertTrue(hasattr(PaymentMode, 'terms'))
        self.assertTrue(hasattr(PaymentTerms, 'terms'))
        
        # Task Group 4: Taxation Management
        self.assertTrue(hasattr(VAT, 'vat_percentage'))
        self.assertTrue(hasattr(TDSCode, 'tds_percentage'))
        
        # Task Group 5: Invoicing & Billing
        self.assertTrue(hasattr(SalesInvoiceMaster, 'total_amount'))
        
        # Task Group 11: Financial Reporting
        self.assertTrue(hasattr(BalanceSheetConfig, 'financial_year'))
        
    def test_managed_false_models(self):
        """Verify models have managed=False for existing database compatibility"""
        managed_false_models = [
            AccountHead, Bank, Currency, PaymentMode, PaymentTerms, PaidType,
            VAT, Excise, ExcisableCommodity, TDSCode, Octori, InvoiceAgainst,
            SalesInvoiceMaster, AdvicePaymentMaster
        ]
        
        for model in managed_false_models:
            with self.subTest(model=model.__name__):
                self.assertFalse(model._meta.managed)
                
    def test_string_representations(self):
        """Test all models have proper string representations"""
        # Create test instances and verify __str__ methods work
        account_head = AccountHead(description='Test Account', symbol='TEST')
        self.assertIn('Test Account', str(account_head))
        
        bank = Bank(name='Test Bank', ifsc='TEST123')
        self.assertIn('Test Bank', str(bank))
        
        vat = VAT(vat_percentage=18.0, description='Test VAT')
        self.assertIn('18.0%', str(vat))


# Additional test runner for specific implementation verification
def run_implementation_tests():
    """
    Run specific tests to verify accounts module implementation completion.
    This function can be called to validate the implementation status.
    """
    import unittest
    from django.test.utils import get_runner
    from django.conf import settings
    
    # Get the Django test runner
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    
    # Run the tests
    suite = unittest.TestLoader().loadTestsFromModule(__name__)
    result = test_runner.run_tests(['accounts.tests.test_accounts_module_implementation'])
    
    return result


if __name__ == '__main__':
    # Run tests if this file is executed directly
    run_implementation_tests()