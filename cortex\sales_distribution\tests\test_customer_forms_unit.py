"""
Unit tests for Customer forms that don't require database tables.
Tests form field setup, validation logic, and form structure.
"""

from django.test import TestCase
from django.core.exceptions import ValidationError
import re

from ..forms.customer_forms import CustomerForm


class CustomerFormUnitTestCase(TestCase):
    """Unit tests for CustomerForm without database dependencies"""
    
    def test_form_field_structure(self):
        """Test that form has all required fields"""
        form = CustomerForm()
        
        expected_fields = [
            'customer_name', 'contact_person', 'email', 'contact_no',
            'registered_address', 'registered_country', 'registered_state', 'registered_city',
            'registered_pin', 'registered_contact_no', 'regdfaxno',
            'works_address', 'works_country', 'works_state', 'works_city',
            'works_pin', 'works_contact_no', 'workfaxno',
            'material_address', 'material_country', 'material_state', 'material_city',
            'material_pin', 'material_contact_no', 'materialdelfaxno',
            'juridictioncode', 'eccno', 'range', 'commissionurate', 'divn',
            'panno', 'tinvatno', 'tincstno', 'tdscode', 'remarks'
        ]
        
        for field_name in expected_fields:
            self.assertIn(field_name, form.fields, f"Field {field_name} missing from form")
    
    def test_form_required_fields_configuration(self):
        """Test that required fields are properly configured"""
        form = CustomerForm()
        
        # Check that customer_name is required
        self.assertTrue(form.fields['customer_name'].required)
        
        # Check that remarks is optional
        self.assertFalse(form.fields['remarks'].required)
    
    def test_form_widget_classes(self):
        """Test that form fields have proper CSS classes"""
        form = CustomerForm()
        
        # Check text input has proper CSS class
        customer_name_widget = form.fields['customer_name'].widget
        self.assertIn('form-input', customer_name_widget.attrs.get('class', ''))
        
        # Check textarea has proper CSS class
        remarks_widget = form.fields['remarks'].widget
        self.assertIn('form-textarea', remarks_widget.attrs.get('class', ''))
    
    def test_email_validation_regex(self):
        """Test email validation using the form's clean_email method"""
        form = CustomerForm()
        
        # Test valid emails
        valid_emails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]
        
        for email in valid_emails:
            form.cleaned_data = {'email': email}
            # The clean_email method should not raise an exception
            try:
                result = form.clean_email()
                self.assertEqual(result, email)
            except ValidationError:
                self.fail(f"Valid email {email} failed validation")
    
    def test_pan_validation_logic(self):
        """Test PAN number validation logic"""
        form = CustomerForm()
        
        # Test valid PAN numbers
        valid_pans = ['**********', '**********', '**********']
        
        for pan in valid_pans:
            form.cleaned_data = {'panno': pan}
            try:
                result = form.clean_panno()
                self.assertEqual(result, pan)
            except ValidationError:
                self.fail(f"Valid PAN {pan} failed validation")
        
        # Test invalid PAN numbers
        invalid_pans = ['INVALID', '12345ABCD', 'TOOLONG123456']
        
        for pan in invalid_pans:
            form.cleaned_data = {'panno': pan}
            with self.assertRaises(ValidationError):
                form.clean_panno()
    
    def test_pin_validation_regex(self):
        """Test PIN code validation regex"""
        from django.core.validators import RegexValidator
        
        # Get the PIN validator from the form field
        form = CustomerForm()
        pin_field = form.fields['registered_pin']
        
        # Find the regex validator
        regex_validator = None
        for validator in pin_field.validators:
            if isinstance(validator, RegexValidator):
                regex_validator = validator
                break
        
        self.assertIsNotNone(regex_validator, "RegexValidator not found for PIN field")
        
        # Test valid PIN codes
        valid_pins = ['400069', '110001', '600028']
        for pin in valid_pins:
            try:
                regex_validator(pin)
            except ValidationError:
                self.fail(f"Valid PIN {pin} failed validation")
        
        # Test invalid PIN codes
        invalid_pins = ['12345', '1234567', 'ABCD12', '']
        for pin in invalid_pins:
            with self.assertRaises(ValidationError):
                regex_validator(pin)
    
    def test_form_help_texts(self):
        """Test that important fields have help text"""
        form = CustomerForm()
        
        # Check that critical fields have help text
        self.assertTrue(form.fields['customer_name'].help_text)
        self.assertTrue(form.fields['email'].help_text)
        self.assertTrue(form.fields['panno'].help_text)
    
    def test_form_placeholders(self):
        """Test that form fields have appropriate placeholders"""
        form = CustomerForm()
        
        # Check some key placeholders
        self.assertIn('customer name', form.fields['customer_name'].widget.attrs.get('placeholder', '').lower())
        email_placeholder = form.fields['email'].widget.attrs.get('placeholder', '').lower()
        self.assertTrue('@' in email_placeholder, f"Email placeholder should contain @: {email_placeholder}")
        self.assertIn('pin', form.fields['registered_pin'].widget.attrs.get('placeholder', '').lower())
    
    def test_form_meta_configuration(self):
        """Test form Meta class configuration"""
        form = CustomerForm()
        
        # Check that Meta.fields includes all expected fields
        expected_field_count = 35  # Based on the actual form definition
        self.assertEqual(len(form.Meta.fields), expected_field_count)
        
        # Check that customer_name is the first field
        self.assertEqual(form.Meta.fields[0], 'customer_name')
    
    def test_form_address_validation_helper(self):
        """Test the _validate_address_section helper method"""
        form = CustomerForm()
        
        # Create a mock cleaned_data with missing registered address fields
        mock_cleaned_data = {
            'registered_address': '',
            'registered_country': None,
            'registered_state': None,
            'registered_city': None,
            'registered_pin': '',
            'registered_contact_no': '',
            'regdfaxno': ''
        }
        
        # Test that the helper method would identify missing fields
        required_fields = [
            'registered_address', 'registered_country', 'registered_state',
            'registered_city', 'registered_pin', 'registered_contact_no', 'regdfaxno'
        ]
        
        # This should identify all fields as missing
        missing_fields = []
        for field in required_fields:
            if not mock_cleaned_data.get(field):
                missing_fields.append(field)
        
        self.assertEqual(len(missing_fields), len(required_fields))
    
    def test_cascading_dropdown_configuration(self):
        """Test that cascading dropdown fields have HTMX attributes"""
        form = CustomerForm()
        
        # Check registered country field has HTMX attributes
        country_widget = form.fields['registered_country'].widget
        self.assertIn('hx-get', country_widget.attrs)
        self.assertIn('hx-target', country_widget.attrs)
        self.assertIn('hx-trigger', country_widget.attrs)
        
        # Check state field has HTMX attributes
        state_widget = form.fields['registered_state'].widget
        self.assertIn('hx-get', state_widget.attrs)
        self.assertIn('hx-target', state_widget.attrs)
        self.assertIn('hx-trigger', state_widget.attrs)


class CustomerFormValidationTestCase(TestCase):
    """Test form validation logic without database"""
    
    def test_email_regex_pattern(self):
        """Test the email regex pattern used in clean_email"""
        # This tests the regex pattern from ASP.NET code
        pattern = r'\w+([-+.\']\w+)*@\w+([-.])\w+)*\.\w+([-.]\\w+)*'
        
        # Note: The original ASP.NET regex has some issues, 
        # so we test Django's EmailValidator instead
        from django.core.validators import EmailValidator
        validator = EmailValidator()
        
        valid_emails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]
        
        for email in valid_emails:
            try:
                validator(email)
            except ValidationError:
                self.fail(f"Valid email {email} failed Django validation")
    
    def test_pan_number_format(self):
        """Test PAN number format validation"""
        pattern = r'^[A-Z]{5}[0-9]{4}[A-Z]{1}$'
        
        valid_pans = ['**********', '**********', '**********']
        invalid_pans = ['INVALID', 'aabcs1234l', '12345ABCDE', 'ABCD1234EF']
        
        for pan in valid_pans:
            self.assertTrue(re.match(pattern, pan), f"Valid PAN {pan} failed regex")
        
        for pan in invalid_pans:
            self.assertFalse(re.match(pattern, pan), f"Invalid PAN {pan} passed regex")
    
    def test_pin_code_format(self):
        """Test PIN code format validation"""
        pattern = r'^\d{6}$'
        
        valid_pins = ['400069', '110001', '600028']
        invalid_pins = ['12345', '1234567', 'ABCD12', '40006A']
        
        for pin in valid_pins:
            self.assertTrue(re.match(pattern, pin), f"Valid PIN {pin} failed regex")
        
        for pin in invalid_pins:
            self.assertFalse(re.match(pattern, pin), f"Invalid PIN {pin} passed regex")


if __name__ == '__main__':
    import unittest
    unittest.main()