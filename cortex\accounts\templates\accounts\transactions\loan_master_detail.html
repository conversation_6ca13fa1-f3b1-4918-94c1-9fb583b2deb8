<!-- accounts/templates/accounts/transactions/loan_master_detail.html -->
<!-- Loan Master Detail View Template -->
<!-- Task Group 7: Capital & Loans Management - Loan Master Detail (Task 7.4) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{{ loan.loan_no }} - Loan <PERSON>ails{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-blue-600 to-sap-blue-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="credit-card" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">{{ loan.loan_no }}</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">{{ loan.lender_name }} | {{ loan.loan_type.loan_type_description|default:"<PERSON>an Details" }}</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:loan_master_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
                <a href="{% url 'accounts:loan_master_edit' loan.id %}" 
                   class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="edit" class="w-4 h-4 inline mr-2"></i>
                    Edit Loan
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Loan Status Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <!-- Loan Amount -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="dollar-sign" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Loan Amount</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ loan.loan_amount|floatformat:2 }}</p>
                    {% if loan.interest_rate %}
                    <p class="text-xs text-sap-blue-600 mt-1">{{ loan.interest_rate }}% p.a.</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Outstanding Principal -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-red-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="trending-down" class="w-6 h-6 text-sap-red-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Outstanding Principal</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ loan.outstanding_principal|default:loan.loan_amount|floatformat:2 }}</p>
                    <p class="text-xs text-sap-red-600 mt-1">Remaining balance</p>
                </div>
            </div>
        </div>
        
        <!-- EMI Amount -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="calendar" class="w-6 h-6 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Monthly EMI</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ loan.emi_amount|default:0|floatformat:2 }}</p>
                    {% if loan.next_emi_date %}
                    <p class="text-xs text-sap-green-600 mt-1">Next: {{ loan.next_emi_date|date:"d M Y" }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Remaining Tenure -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="clock" class="w-6 h-6 text-sap-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Remaining Tenure</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ remaining_months|default:loan.loan_tenure_months }}</p>
                    <p class="text-xs text-sap-purple-600 mt-1">months left</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Loan Information Sections -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        
        <!-- Basic Loan Information -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="info" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                    Loan Information
                </h3>
            </div>
            <div class="p-6 space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Loan Number:</span>
                    <span class="text-sm text-sap-gray-900">{{ loan.loan_no }}</span>
                </div>
                {% if loan.loan_account_number %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Account Number:</span>
                    <span class="text-sm text-sap-gray-900">{{ loan.loan_account_number }}</span>
                </div>
                {% endif %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Loan Type:</span>
                    <span class="text-sm text-sap-gray-900">{{ loan.loan_type.loan_type_description|default:"-" }}</span>
                </div>
                {% if loan.interest_type %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Interest Type:</span>
                    <span class="text-sm text-sap-gray-900">{{ loan.interest_type.interest_type_description }}</span>
                </div>
                {% endif %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Status:</span>
                    <span class="text-sm">
                        {% if loan.status == 'active' %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-green-100 text-sap-green-800">
                            Active
                        </span>
                        {% elif loan.status == 'pending' %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-yellow-100 text-sap-yellow-800">
                            Pending
                        </span>
                        {% elif loan.status == 'closed' %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-gray-100 text-sap-gray-800">
                            Closed
                        </span>
                        {% elif loan.status == 'overdue' %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-red-100 text-sap-red-800">
                            Overdue
                        </span>
                        {% else %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-blue-100 text-sap-blue-800">
                            {{ loan.get_status_display|default:"Unknown" }}
                        </span>
                        {% endif %}
                    </span>
                </div>
            </div>
        </div>

        <!-- Lender Information -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="building" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                    Lender Details
                </h3>
            </div>
            <div class="p-6 space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Lender Name:</span>
                    <span class="text-sm text-sap-gray-900">{{ loan.lender_name|default:"-" }}</span>
                </div>
                {% if loan.lender_contact %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Contact:</span>
                    <span class="text-sm text-sap-gray-900">{{ loan.lender_contact }}</span>
                </div>
                {% endif %}
                {% if loan.lender_branch %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Branch:</span>
                    <span class="text-sm text-sap-gray-900">{{ loan.lender_branch }}</span>
                </div>
                {% endif %}
                {% if loan.lender_address %}
                <div class="space-y-2">
                    <span class="text-sm font-medium text-sap-gray-600">Address:</span>
                    <p class="text-sm text-sap-gray-900">{{ loan.lender_address }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Payment Schedule -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="calendar" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                    Payment Schedule
                </h3>
            </div>
            <div class="p-6 space-y-4">
                {% if loan.loan_tenure_months %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Total Tenure:</span>
                    <span class="text-sm text-sap-gray-900">{{ loan.loan_tenure_months }} months</span>
                </div>
                {% endif %}
                {% if loan.installment_frequency %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Frequency:</span>
                    <span class="text-sm text-sap-gray-900">{{ loan.get_installment_frequency_display }}</span>
                </div>
                {% endif %}
                {% if loan.emi_day %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">EMI Day:</span>
                    <span class="text-sm text-sap-gray-900">{{ loan.emi_day }} of each month</span>
                </div>
                {% endif %}
                {% if loan.paid_emis %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Paid EMIs:</span>
                    <span class="text-sm text-sap-gray-900">{{ loan.paid_emis|default:0 }} / {{ loan.loan_tenure_months|default:0 }}</span>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Important Dates and Financial Summary -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        
        <!-- Important Dates -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="clock" class="w-5 h-5 mr-2 text-sap-indigo-600"></i>
                    Important Dates
                </h3>
            </div>
            <div class="p-6 space-y-4">
                {% if loan.loan_start_date %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Loan Start Date:</span>
                    <span class="text-sm text-sap-gray-900">{{ loan.loan_start_date|date:"d M Y" }}</span>
                </div>
                {% endif %}
                {% if loan.loan_end_date %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Loan End Date:</span>
                    <span class="text-sm text-sap-gray-900">{{ loan.loan_end_date|date:"d M Y" }}</span>
                </div>
                {% endif %}
                {% if loan.first_emi_date %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">First EMI Date:</span>
                    <span class="text-sm text-sap-gray-900">{{ loan.first_emi_date|date:"d M Y" }}</span>
                </div>
                {% endif %}
                {% if loan.next_emi_date %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Next EMI Date:</span>
                    <span class="text-sm font-medium text-sap-blue-600">{{ loan.next_emi_date|date:"d M Y" }}</span>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Financial Summary -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="bar-chart" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                    Financial Summary
                </h3>
            </div>
            <div class="p-6 space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Principal Amount:</span>
                    <span class="text-sm font-medium text-sap-gray-900">₹{{ loan.loan_amount|floatformat:2 }}</span>
                </div>
                {% if loan.outstanding_principal %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Outstanding Principal:</span>
                    <span class="text-sm font-medium text-sap-red-600">₹{{ loan.outstanding_principal|floatformat:2 }}</span>
                </div>
                {% endif %}
                {% if loan.outstanding_interest %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Outstanding Interest:</span>
                    <span class="text-sm font-medium text-sap-orange-600">₹{{ loan.outstanding_interest|floatformat:2 }}</span>
                </div>
                {% endif %}
                {% if total_interest_payable %}
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-sap-gray-600">Total Interest Payable:</span>
                    <span class="text-sm text-sap-gray-900">₹{{ total_interest_payable|floatformat:2 }}</span>
                </div>
                {% endif %}
                {% if total_amount_payable %}
                <div class="flex items-center justify-between pt-2 border-t border-sap-gray-200">
                    <span class="text-sm font-bold text-sap-gray-800">Total Amount Payable:</span>
                    <span class="text-sm font-bold text-sap-gray-900">₹{{ total_amount_payable|floatformat:2 }}</span>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Additional Information -->
    {% if loan.loan_purpose or loan.remarks %}
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {% if loan.loan_purpose %}
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="target" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                    Loan Purpose
                </h3>
            </div>
            <div class="p-6">
                <p class="text-sm text-sap-gray-700">{{ loan.loan_purpose }}</p>
            </div>
        </div>
        {% endif %}

        {% if loan.remarks %}
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="sticky-note" class="w-5 h-5 mr-2 text-sap-yellow-600"></i>
                    Remarks
                </h3>
            </div>
            <div class="p-6">
                <p class="text-sm text-sap-gray-700">{{ loan.remarks }}</p>
            </div>
        </div>
        {% endif %}
    </div>
    {% endif %}

    <!-- EMI Schedule and Payment History -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        
        <!-- EMI Schedule Preview -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                        <i data-lucide="calendar" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                        EMI Schedule (Preview)
                    </h3>
                    <button type="button" onclick="downloadEMISchedule()" 
                            class="text-sap-purple-600 hover:text-sap-purple-900 text-sm font-medium">
                        <i data-lucide="download" class="w-4 h-4 inline mr-1"></i>
                        Full Schedule
                    </button>
                </div>
            </div>
            <div class="p-6">
                {% if emi_schedule %}
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="border-b border-sap-gray-200">
                                <th class="text-left text-xs font-medium text-sap-gray-500 uppercase py-2">EMI #</th>
                                <th class="text-left text-xs font-medium text-sap-gray-500 uppercase py-2">Date</th>
                                <th class="text-right text-xs font-medium text-sap-gray-500 uppercase py-2">Amount</th>
                                <th class="text-center text-xs font-medium text-sap-gray-500 uppercase py-2">Status</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-sap-gray-100">
                            {% for emi in emi_schedule|slice:":5" %}
                            <tr>
                                <td class="py-2 text-sm text-sap-gray-900">{{ emi.emi_number }}</td>
                                <td class="py-2 text-sm text-sap-gray-900">{{ emi.due_date|date:"d M Y" }}</td>
                                <td class="py-2 text-sm text-sap-gray-900 text-right">₹{{ emi.emi_amount|floatformat:2 }}</td>
                                <td class="py-2 text-center">
                                    {% if emi.is_paid %}
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-green-100 text-sap-green-800">
                                        Paid
                                    </span>
                                    {% elif emi.is_overdue %}
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-red-100 text-sap-red-800">
                                        Overdue
                                    </span>
                                    {% else %}
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-yellow-100 text-sap-yellow-800">
                                        Pending
                                    </span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i data-lucide="calendar" class="w-8 h-8 text-sap-gray-400 mx-auto mb-2"></i>
                    <p class="text-sm text-sap-gray-500">EMI schedule not yet generated</p>
                    <button type="button" onclick="generateEMISchedule()" 
                            class="mt-2 text-sap-blue-600 hover:text-sap-blue-900 text-sm font-medium">
                        Generate Schedule
                    </button>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Recent Payments -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                        <i data-lucide="activity" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                        Recent Payments
                    </h3>
                    <a href="#" class="text-sap-green-600 hover:text-sap-green-900 text-sm font-medium">
                        View All
                    </a>
                </div>
            </div>
            <div class="p-6">
                {% if recent_payments %}
                <div class="space-y-4">
                    {% for payment in recent_payments %}
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="text-sm font-medium text-sap-gray-900">EMI #{{ payment.emi_number }}</div>
                            <div class="text-xs text-sap-gray-500">{{ payment.payment_date|date:"d M Y" }}</div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-sap-gray-900">₹{{ payment.amount_paid|floatformat:2 }}</div>
                            <div class="text-xs text-sap-green-600">{{ payment.payment_method|default:"Bank" }}</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i data-lucide="activity" class="w-8 h-8 text-sap-gray-400 mx-auto mb-2"></i>
                    <p class="text-sm text-sap-gray-500">No recent payments</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="mt-8 flex items-center justify-center space-x-4">
        <button type="button" onclick="recordPayment({{ loan.id }})" 
                class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
            <i data-lucide="credit-card" class="w-4 h-4 inline mr-2"></i>
            Record Payment
        </button>
        <button type="button" onclick="generateEMISchedule()" 
                class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
            <i data-lucide="calendar" class="w-4 h-4 inline mr-2"></i>
            Generate EMI Schedule
        </button>
        <button type="button" onclick="printLoanDetails()" 
                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
            <i data-lucide="printer" class="w-4 h-4 inline mr-2"></i>
            Print Details
        </button>
        <button type="button" onclick="exportLoanData()" 
                class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
            <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
            Export Data
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function recordPayment(loanId) {
    // This would open a modal or redirect to payment recording page
    window.location.href = `/accounts/transactions/loans/${loanId}/record-payment/`;
}

function generateEMISchedule() {
    window.open(`/accounts/transactions/loans/{{ loan.id }}/emi-schedule/`, '_blank');
}

function downloadEMISchedule() {
    window.open(`/accounts/transactions/loans/{{ loan.id }}/emi-schedule/?download=true`, '_blank');
}

function printLoanDetails() {
    window.print();
}

function exportLoanData() {
    window.open(`/accounts/transactions/loans/{{ loan.id }}/export/`, '_blank');
}

// Initialize lucide icons on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}