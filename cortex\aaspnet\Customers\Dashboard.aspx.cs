﻿using System;
using System.Collections;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Xml.Linq;
using System.Data.SqlClient;
using Telerik.Web.UI;

public partial class Module_Customers_Dashboard : System.Web.UI.Page
{
    clsFunctions fun = new clsFunctions();   
    SqlConnection con;
    string connStr = "";
    int CompId = 0;
    int FyId = 0;
    string SId = "";
    string wono = "";
    string wnup = "";
    string CustId = "";
    string Id = "";
    protected void Page_Load(object sender, EventArgs e)
    {        
        try
        {
            Label lbl = this.Master.FindControl("lblEmpName") as Label;
            lbl.Visible = false;

            LoginStatus log = this.Master.FindControl("LoginStatus1") as LoginStatus;
            log.LoginText = "Logout";

            RadPane Menu2 = this.Master.FindControl("Radpane1") as RadPane;
            Menu2.Visible = false;

            RadSplitBar Menu3 = this.Master.FindControl("Radsplitbar3") as RadSplitBar;
            Menu3.Visible = false;

            RadSplitBar Menu4 = this.Master.FindControl("Radsplitbar2") as RadSplitBar;
            Menu4.Visible = false;
           
            connStr = fun.Connection();
            con = new SqlConnection(connStr);

            CompId = Convert.ToInt32(Session["compid"]);
            FyId = Convert.ToInt32(Session["finyear"]);
            SId = Session["username"].ToString();
            CustId = Request.QueryString["CustId"];
            Id = Request.QueryString["Id"];

            if (!IsPostBack)
            {              
               this.FillDataUpLoad(Id);
            }
            
            string sql = fun.select("*", "SD_Cust_master", "CustomerId='" + CustId + "' AND CompId='" + CompId + "'");
            SqlCommand cmd = new SqlCommand(sql, con);
            SqlDataAdapter da = new SqlDataAdapter(cmd);
            DataSet DS = new DataSet();
            da.Fill(DS);

            Label2.Text = DS.Tables[0].Rows[0]["CustomerName"].ToString() + " [" + DS.Tables[0].Rows[0]["CustomerId"].ToString() + "]";

            string cmdStr2 = fun.select("Title", "tblPM_ForCustomer_Master", "Id='" + Id + "' ");
            SqlCommand cmd2 = new SqlCommand(cmdStr2, con);
            SqlDataAdapter DA2 = new SqlDataAdapter(cmd2);
            DataSet DS2 = new DataSet();
            DA2.Fill(DS2);

            lblTitle.Text = DS2.Tables[0].Rows[0]["Title"].ToString();
        }
        catch (Exception ex)
        {

        }
    }      
    public void FillDataUpLoad(string Id)
    {
        try
        {

            con.Open();
            DataTable dt = new DataTable();
            dt.Columns.Add(new System.Data.DataColumn("Id", typeof(string)));
            dt.Columns.Add(new System.Data.DataColumn("PLNDate", typeof(string)));
            dt.Columns.Add(new System.Data.DataColumn("FileName", typeof(string)));
            dt.Columns.Add(new System.Data.DataColumn("Remarks", typeof(string)));
            dt.Columns.Add(new System.Data.DataColumn("Time", typeof(string)));
            dt.Columns.Add(new System.Data.DataColumn("MailTo", typeof(string)));
            dt.Columns.Add(new System.Data.DataColumn("msg", typeof(string)));
            DataRow dr;
            string sql = fun.select("tblPM_ForCustomer_Master.Id,tblPM_ForCustomer_Details.MailTo,tblPM_ForCustomer_Details.Message,tblPM_ForCustomer_Details.Id as DId,tblPM_ForCustomer_Master.SysDate,tblPM_ForCustomer_Master.SysTime,tblPM_ForCustomer_Details.FileName,tblPM_ForCustomer_Details.Remarks,tblPM_ForCustomer_Master.EmpId,tblPM_ForCustomer_Master.Title", "tblPM_ForCustomer_Master,tblPM_ForCustomer_Details", "tblPM_ForCustomer_Master.Id=tblPM_ForCustomer_Details.MId AND tblPM_ForCustomer_Master.FinYearId<='" + FyId + "' AND tblPM_ForCustomer_Master.CompId='" + CompId + "' AND tblPM_ForCustomer_Master.CustId='" + CustId + "'AND tblPM_ForCustomer_Master.EmpId='" + SId + "' order by Id desc");
            SqlCommand cmd = new SqlCommand(sql, con);
            SqlDataAdapter da = new SqlDataAdapter(cmd);
            DataSet DS = new DataSet();
            da.Fill(DS);           
            for (int q = 0; q < DS.Tables[0].Rows.Count; q++)
            {
                dr = dt.NewRow();
                dr[0] = DS.Tables[0].Rows[q]["DId"].ToString();
                dr[1] = fun.FromDateDMY(DS.Tables[0].Rows[q]["SysDate"].ToString());
                if (DS.Tables[0].Rows[q]["FileName"].ToString() != "" && DS.Tables[0].Rows[q]["FileName"] != DBNull.Value)
                {
                    dr[2] = DS.Tables[0].Rows[q]["FileName"].ToString();
                }
                dr[3] = DS.Tables[0].Rows[q]["Remarks"].ToString();
                dr[4] = DS.Tables[0].Rows[q]["SysTime"].ToString();
                dr[5] = DS.Tables[0].Rows[q]["MailTo"].ToString();
                dr[6] = DS.Tables[0].Rows[q]["Message"].ToString(); 
                dt.Rows.Add(dr);
                dt.AcceptChanges();
            }
            GridView3.DataSource = dt;
            GridView3.DataBind();
            con.Close();            
           
        }
        catch (Exception ex) { }

    }  
    protected void GridView3_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        try
        {
            string CDate = fun.getCurrDate();
            string CTime = fun.getCurrTime();
           
            if (e.CommandName == "downloadImg")
            {
                GridViewRow row = (GridViewRow)(((LinkButton)e.CommandSource).NamingContainer);
                int id = Convert.ToInt32(((Label)row.FindControl("lblId0")).Text);

                Response.Redirect("~/Controls/DownloadFile.aspx?Id=" + id + "&tbl=tblPM_ForCustomer_Details&qfd=FileData&qfn=FileName&qct=ContentType");

            }
        }
        catch (Exception ex) { }
    }


}
