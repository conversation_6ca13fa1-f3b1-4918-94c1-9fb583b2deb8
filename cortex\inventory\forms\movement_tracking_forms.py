from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from ..models import (
    InventoryMovementMaster, InventoryMovementDetails,
    StockLedger, InventorySnapshot
)


class InventoryMovementMasterForm(forms.ModelForm):
    """Form for creating and editing inventory movement master records"""
    
    class Meta:
        model = InventoryMovementMaster
        fields = [
            'movement_date', 'movement_type', 'movement_category', 'source_document_type',
            'source_document_number', 'from_location_code', 'to_location_code',
            'from_warehouse', 'to_warehouse', 'work_order_number', 'project_code',
            'cost_center_code', 'priority', 'requires_approval', 'remarks'
        ]
        widgets = {
            'movement_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md'
            }),
            'movement_type': forms.Select(attrs={
                'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'movement_category': forms.Select(attrs={
                'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'source_document_type': forms.Select(attrs={
                'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'source_document_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Source Document Number'
            }),
            'from_location_code': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'From Location Code',
                'list': 'location-list'
            }),
            'to_location_code': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'To Location Code',
                'list': 'location-list'
            }),
            'from_warehouse': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'From Warehouse',
                'list': 'warehouse-list'
            }),
            'to_warehouse': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'To Warehouse',
                'list': 'warehouse-list'
            }),
            'work_order_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Work Order Number',
                'list': 'work-order-list'
            }),
            'project_code': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Project Code',
                'list': 'project-list'
            }),
            'cost_center_code': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Cost Center Code',
                'list': 'cost-center-list'
            }),
            'priority': forms.Select(attrs={
                'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'requires_approval': forms.CheckboxInput(attrs={
                'class': 'rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500'
            }),
            'remarks': forms.Textarea(attrs={
                'rows': 3,
                'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md',
                'placeholder': 'Movement remarks and special instructions'
            })
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.company = kwargs.pop('company', None)
        self.financial_year = kwargs.pop('financial_year', None)
        super().__init__(*args, **kwargs)
        
        # Set default values
        if not self.instance.pk:
            self.fields['movement_date'].initial = timezone.now().date()

    def clean_movement_date(self):
        movement_date = self.cleaned_data.get('movement_date')
        if movement_date and movement_date > timezone.now().date():
            raise ValidationError("Movement date cannot be in the future.")
        return movement_date

    def clean(self):
        cleaned_data = super().clean()
        movement_type = cleaned_data.get('movement_type')
        from_location_code = cleaned_data.get('from_location_code')
        to_location_code = cleaned_data.get('to_location_code')
        
        # Validate location requirements based on movement type
        if movement_type in ['INTERNAL', 'TRANSFER']:
            if not from_location_code:
                raise ValidationError("From location is required for internal transfers.")
            if not to_location_code:
                raise ValidationError("To location is required for internal transfers.")
            if from_location_code == to_location_code:
                raise ValidationError("From and to locations cannot be the same.")
        
        elif movement_type == 'OUTWARD':
            if not from_location_code:
                raise ValidationError("From location is required for outward movements.")
        
        elif movement_type == 'INWARD':
            if not to_location_code:
                raise ValidationError("To location is required for inward movements.")
        
        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        
        if not instance.pk:
            # Set system fields for new instances
            instance.company = self.company
            instance.financial_year = self.financial_year
            instance.created_by = self.user
            
        if commit:
            instance.save()
            # Generate movement number if not set
            if not instance.movement_number:
                instance.generate_movement_number()
                instance.save()
        
        return instance


class InventoryMovementDetailsForm(forms.ModelForm):
    """Form for inventory movement line items"""
    
    class Meta:
        model = InventoryMovementDetails
        fields = [
            'line_number', 'item_code', 'item_description', 'item_category',
            'unit_of_measure', 'from_location_code', 'to_location_code',
            'from_batch_number', 'to_batch_number', 'from_lot_number', 'to_lot_number',
            'planned_quantity', 'actual_quantity', 'unit_rate', 'movement_reason',
            'quality_status', 'expiry_date', 'manufacturing_date', 'serial_numbers',
            'barcode_scanned', 'rfid_tag', 'weight_measured', 'dimension_length',
            'dimension_width', 'dimension_height', 'special_handling_required',
            'special_handling_instructions', 'remarks'
        ]
        widgets = {
            'line_number': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'min': '1'
            }),
            'item_code': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Item Code',
                'list': 'item-code-list'
            }),
            'item_description': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Item Description'
            }),
            'item_category': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Item Category',
                'list': 'category-list'
            }),
            'unit_of_measure': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'UOM'
            }),
            'from_location_code': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'From Location',
                'list': 'location-list'
            }),
            'to_location_code': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'To Location',
                'list': 'location-list'
            }),
            'from_batch_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'From Batch Number'
            }),
            'to_batch_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'To Batch Number'
            }),
            'from_lot_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'From Lot Number'
            }),
            'to_lot_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'To Lot Number'
            }),
            'planned_quantity': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'actual_quantity': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'unit_rate': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'movement_reason': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Movement Reason'
            }),
            'quality_status': forms.Select(attrs={
                'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'expiry_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md'
            }),
            'manufacturing_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md'
            }),
            'serial_numbers': forms.Textarea(attrs={
                'rows': 2,
                'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md',
                'placeholder': 'Serial numbers (one per line or comma-separated)'
            }),
            'barcode_scanned': forms.CheckboxInput(attrs={
                'class': 'rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500'
            }),
            'rfid_tag': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'RFID Tag'
            }),
            'weight_measured': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Weight (kg)',
                'step': '0.01',
                'min': '0'
            }),
            'dimension_length': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Length (cm)',
                'step': '0.1',
                'min': '0'
            }),
            'dimension_width': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Width (cm)',
                'step': '0.1',
                'min': '0'
            }),
            'dimension_height': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Height (cm)',
                'step': '0.1',
                'min': '0'
            }),
            'special_handling_required': forms.CheckboxInput(attrs={
                'class': 'rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500'
            }),
            'special_handling_instructions': forms.Textarea(attrs={
                'rows': 2,
                'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md',
                'placeholder': 'Special handling instructions'
            }),
            'remarks': forms.Textarea(attrs={
                'rows': 2,
                'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md',
                'placeholder': 'Line item remarks'
            })
        }

    def clean_planned_quantity(self):
        planned_quantity = self.cleaned_data.get('planned_quantity')
        if planned_quantity is not None and planned_quantity <= 0:
            raise ValidationError("Planned quantity must be greater than zero.")
        return planned_quantity

    def clean_item_code(self):
        item_code = self.cleaned_data.get('item_code')
        if not item_code:
            raise ValidationError("Item code is required.")
        return item_code.upper().strip()

    def clean(self):
        cleaned_data = super().clean()
        manufacturing_date = cleaned_data.get('manufacturing_date')
        expiry_date = cleaned_data.get('expiry_date')
        
        # Validate dates
        if manufacturing_date and expiry_date:
            if expiry_date <= manufacturing_date:
                raise ValidationError("Expiry date must be after manufacturing date.")
        
        # Auto-calculate variance
        planned_quantity = cleaned_data.get('planned_quantity', 0)
        actual_quantity = cleaned_data.get('actual_quantity', 0)
        
        if actual_quantity and planned_quantity:
            variance_quantity = actual_quantity - planned_quantity
            cleaned_data['variance_quantity'] = variance_quantity
        
        return cleaned_data


class StockLedgerForm(forms.ModelForm):
    """Form for manual stock ledger entries"""
    
    class Meta:
        model = StockLedger
        fields = [
            'transaction_date', 'item_code', 'item_description', 'location_code',
            'warehouse_code', 'batch_number', 'lot_number', 'transaction_type',
            'reference_document_type', 'reference_document_number', 'work_order_number',
            'cost_center_code', 'supplier_code', 'customer_code', 'quantity_in',
            'quantity_out', 'unit_rate', 'unit_of_measure', 'valuation_method',
            'quality_status', 'expiry_date', 'manufacturing_date', 'remarks'
        ]
        widgets = {
            'transaction_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md'
            }),
            'item_code': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Item Code',
                'list': 'item-code-list'
            }),
            'item_description': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Item Description'
            }),
            'location_code': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Location Code',
                'list': 'location-list'
            }),
            'warehouse_code': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Warehouse Code',
                'list': 'warehouse-list'
            }),
            'batch_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Batch Number'
            }),
            'lot_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Lot Number'
            }),
            'transaction_type': forms.Select(attrs={
                'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'reference_document_type': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Reference Document Type'
            }),
            'reference_document_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Reference Document Number'
            }),
            'work_order_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Work Order Number'
            }),
            'cost_center_code': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Cost Center Code'
            }),
            'supplier_code': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Supplier Code'
            }),
            'customer_code': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Customer Code'
            }),
            'quantity_in': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'quantity_out': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'unit_rate': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'unit_of_measure': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'UOM'
            }),
            'valuation_method': forms.Select(attrs={
                'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'quality_status': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Quality Status'
            }),
            'expiry_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md'
            }),
            'manufacturing_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md'
            }),
            'remarks': forms.Textarea(attrs={
                'rows': 3,
                'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md',
                'placeholder': 'Transaction remarks'
            })
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.company = kwargs.pop('company', None)
        self.financial_year = kwargs.pop('financial_year', None)
        super().__init__(*args, **kwargs)
        
        # Set default values
        if not self.instance.pk:
            now = timezone.now()
            self.fields['transaction_date'].initial = now.date()

    def clean(self):
        cleaned_data = super().clean()
        quantity_in = cleaned_data.get('quantity_in', 0)
        quantity_out = cleaned_data.get('quantity_out', 0)
        
        # Ensure only one of quantity_in or quantity_out is non-zero
        if quantity_in > 0 and quantity_out > 0:
            raise ValidationError("Only one of quantity in or quantity out should be greater than zero.")
        
        if quantity_in == 0 and quantity_out == 0:
            raise ValidationError("Either quantity in or quantity out must be greater than zero.")
        
        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        
        if not instance.pk:
            # Set system fields for new instances
            instance.company = self.company
            instance.financial_year = self.financial_year
            instance.processed_by = self.user
            instance.transaction_time = timezone.now().time()
            instance.auto_generated = False
            
        if commit:
            instance.save()
            # Generate transaction ID if not set
            if not instance.transaction_id:
                instance.generate_transaction_id()
                instance.save()
        
        return instance


class InventorySnapshotForm(forms.ModelForm):
    """Form for creating inventory snapshots"""
    
    class Meta:
        model = InventorySnapshot
        fields = [
            'snapshot_date', 'snapshot_type', 'item_code', 'item_description',
            'location_code', 'warehouse_code', 'current_stock', 'blocked_stock',
            'reserved_stock', 'in_transit_stock', 'quality_hold_stock',
            'damaged_stock', 'obsolete_stock', 'unit_of_measure', 'weighted_avg_rate',
            'safety_stock_level', 'reorder_level', 'maximum_level', 'minimum_level',
            'abc_category', 'xyz_category', 'movement_frequency', 'stock_status',
            'remarks'
        ]
        widgets = {
            'snapshot_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md'
            }),
            'snapshot_type': forms.Select(attrs={
                'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'item_code': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Item Code',
                'list': 'item-code-list'
            }),
            'item_description': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Item Description'
            }),
            'location_code': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Location Code',
                'list': 'location-list'
            }),
            'warehouse_code': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Warehouse Code',
                'list': 'warehouse-list'
            }),
            'current_stock': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'blocked_stock': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'reserved_stock': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'in_transit_stock': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'quality_hold_stock': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'damaged_stock': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'obsolete_stock': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'unit_of_measure': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'UOM'
            }),
            'weighted_avg_rate': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'safety_stock_level': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'reorder_level': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'maximum_level': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'minimum_level': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'abc_category': forms.Select(attrs={
                'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'xyz_category': forms.Select(attrs={
                'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'movement_frequency': forms.Select(attrs={
                'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'stock_status': forms.Select(attrs={
                'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'remarks': forms.Textarea(attrs={
                'rows': 3,
                'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md',
                'placeholder': 'Snapshot remarks'
            })
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.company = kwargs.pop('company', None)
        self.financial_year = kwargs.pop('financial_year', None)
        super().__init__(*args, **kwargs)
        
        # Set default values
        if not self.instance.pk:
            now = timezone.now()
            self.fields['snapshot_date'].initial = now.date()

    def clean(self):
        cleaned_data = super().clean()
        
        # Auto-calculate available stock
        current_stock = cleaned_data.get('current_stock', 0)
        blocked_stock = cleaned_data.get('blocked_stock', 0)
        reserved_stock = cleaned_data.get('reserved_stock', 0)
        quality_hold_stock = cleaned_data.get('quality_hold_stock', 0)
        
        available_stock = current_stock - blocked_stock - reserved_stock - quality_hold_stock
        cleaned_data['available_stock'] = max(0, available_stock)
        
        # Auto-calculate total value
        weighted_avg_rate = cleaned_data.get('weighted_avg_rate', 0)
        total_value = current_stock * weighted_avg_rate
        cleaned_data['total_value'] = total_value
        
        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        
        if not instance.pk:
            # Set system fields for new instances
            instance.company = self.company
            instance.financial_year = self.financial_year
            instance.created_by = self.user
            instance.snapshot_time = timezone.now().time()
        
        if commit:
            instance.save()
        
        return instance


class MovementTrackingSearchForm(forms.Form):
    """Search form for inventory movement tracking"""
    
    search = forms.CharField(
        required=False,
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md',
            'placeholder': 'Search by movement number, item code, or document...',
            'hx-get': '/inventory/movements/',
            'hx-trigger': 'keyup changed delay:300ms',
            'hx-target': '#movement-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    movement_type = forms.ChoiceField(
        required=False,
        choices=[('', 'All Types')] + InventoryMovementMaster._meta.get_field('movement_type').choices,
        widget=forms.Select(attrs={
            'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/inventory/movements/',
            'hx-trigger': 'change',
            'hx-target': '#movement-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    movement_status = forms.ChoiceField(
        required=False,
        choices=[('', 'All Status')] + InventoryMovementMaster._meta.get_field('movement_status').choices,
        widget=forms.Select(attrs={
            'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/inventory/movements/',
            'hx-trigger': 'change',
            'hx-target': '#movement-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    location_code = forms.CharField(
        required=False,
        max_length=50,
        widget=forms.TextInput(attrs={
            'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
            'placeholder': 'Location Code',
            'list': 'location-list',
            'hx-get': '/inventory/movements/',
            'hx-trigger': 'change',
            'hx-target': '#movement-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
            'hx-get': '/inventory/movements/',
            'hx-trigger': 'change',
            'hx-target': '#movement-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
            'hx-get': '/inventory/movements/',
            'hx-trigger': 'change',
            'hx-target': '#movement-results',
            'hx-swap': 'innerHTML'
        })
    )


class StockLedgerSearchForm(forms.Form):
    """Search form for stock ledger"""
    
    search = forms.CharField(
        required=False,
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md',
            'placeholder': 'Search by item code, transaction ID, or reference...',
            'hx-get': '/inventory/stock-ledger/',
            'hx-trigger': 'keyup changed delay:300ms',
            'hx-target': '#stock-ledger-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    transaction_type = forms.ChoiceField(
        required=False,
        choices=[('', 'All Types')] + StockLedger._meta.get_field('transaction_type').choices,
        widget=forms.Select(attrs={
            'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/inventory/stock-ledger/',
            'hx-trigger': 'change',
            'hx-target': '#stock-ledger-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    location_code = forms.CharField(
        required=False,
        max_length=50,
        widget=forms.TextInput(attrs={
            'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
            'placeholder': 'Location Code',
            'list': 'location-list',
            'hx-get': '/inventory/stock-ledger/',
            'hx-trigger': 'change',
            'hx-target': '#stock-ledger-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
            'hx-get': '/inventory/stock-ledger/',
            'hx-trigger': 'change',
            'hx-target': '#stock-ledger-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
            'hx-get': '/inventory/stock-ledger/',
            'hx-trigger': 'change',
            'hx-target': '#stock-ledger-results',
            'hx-swap': 'innerHTML'
        })
    )