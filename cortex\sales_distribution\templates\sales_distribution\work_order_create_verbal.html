{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/umd/lucide.min.css">
<script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
{% endblock %}

{% block extra_js %}
<script>
// Tab navigation functionality
function showTab(tabIndex) {
    // Hide all tabs
    for (let i = 0; i <= 4; i++) {
        const tabContent = document.getElementById(`tab-${i}`);
        const tabButton = document.querySelector(`[data-tab="${i}"]`);
        
        if (tabContent) {
            tabContent.classList.add('hidden');
        }
        if (tabButton) {
            tabButton.classList.remove('bg-sap-blue-600', 'text-white');
            tabButton.classList.add('bg-sap-gray-100', 'text-sap-gray-600');
        }
    }
    
    // Show selected tab
    const selectedTab = document.getElementById(`tab-${tabIndex}`);
    const selectedButton = document.querySelector(`[data-tab="${tabIndex}"]`);
    
    if (selectedTab) {
        selectedTab.classList.remove('hidden');
    }
    if (selectedButton) {
        selectedButton.classList.remove('bg-sap-gray-100', 'text-sap-gray-600');
        selectedButton.classList.add('bg-sap-blue-600', 'text-white');
    }
}

// Initialize tabs on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
    
    // Get active tab from URL parameter or default to 0
    const urlParams = new URLSearchParams(window.location.search);
    const activeTab = parseInt(urlParams.get('tab')) || 0;
    showTab(activeTab);
});

// Form navigation
function goToTab(tabIndex) {
    showTab(tabIndex);
    // Update URL without refreshing page
    const url = new URL(window.location);
    url.searchParams.set('tab', tabIndex);
    window.history.pushState({}, '', url);
}

// Dynamic form submission for different actions
function submitForm(action, nextTab = null) {
    const form = document.getElementById('work-order-form');
    const actionInput = document.getElementById('action-input');
    const nextTabInput = document.getElementById('next-tab-input');
    
    actionInput.value = action;
    if (nextTab !== null) {
        nextTabInput.value = nextTab;
    }
    
    form.submit();
}
</script>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-sap-gray-50 font-sap">
    <!-- SAP Fiori Header -->
    <div class="bg-white shadow-sm border-b border-sap-gray-200">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-orange-600 rounded-lg flex items-center justify-center">
                            <i data-lucide="message-circle" class="w-5 h-5 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-xl font-semibold text-sap-gray-900">{{ page_title }}</h1>
                            <p class="text-sm text-sap-gray-600">Create work order from verbal approval</p>
                        </div>
                    </div>
                </div>
                <nav class="flex items-center space-x-2 text-sm text-sap-gray-500">
                    <a href="{% url 'sales_distribution:dashboard' %}" class="hover:text-sap-blue-600 transition-colors">Sales & Distribution</a>
                    <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    <a href="{% url 'sales_distribution:work_order_create' %}" class="hover:text-sap-blue-600 transition-colors">Work Order Creation</a>
                    <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    <span class="text-sap-gray-900 font-medium">Verbal Approval</span>
                </nav>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-6 py-8">
        <!-- Tab Navigation -->
        <div class="mb-8">
            <div class="flex flex-wrap gap-2">
                <button data-tab="0" onclick="goToTab(0)" class="px-4 py-2 text-sm font-medium rounded-lg transition-colors bg-sap-blue-600 text-white">
                    <i data-lucide="file-text" class="w-4 h-4 mr-2 inline"></i>
                    Verbal Approval
                </button>
                <button data-tab="1" onclick="goToTab(1)" class="px-4 py-2 text-sm font-medium rounded-lg transition-colors bg-sap-gray-100 text-sap-gray-600">
                    <i data-lucide="clipboard-list" class="w-4 h-4 mr-2 inline"></i>
                    Task Execution
                </button>
                <button data-tab="2" onclick="goToTab(2)" class="px-4 py-2 text-sm font-medium rounded-lg transition-colors bg-sap-gray-100 text-sap-gray-600">
                    <i data-lucide="truck" class="w-4 h-4 mr-2 inline"></i>
                    Shipping
                </button>
                <button data-tab="3" onclick="goToTab(3)" class="px-4 py-2 text-sm font-medium rounded-lg transition-colors bg-sap-gray-100 text-sap-gray-600">
                    <i data-lucide="package" class="w-4 h-4 mr-2 inline"></i>
                    Products
                </button>
                <button data-tab="4" onclick="goToTab(4)" class="px-4 py-2 text-sm font-medium rounded-lg transition-colors bg-sap-gray-100 text-sap-gray-600">
                    <i data-lucide="message-square" class="w-4 h-4 mr-2 inline"></i>
                    Instructions
                </button>
            </div>
        </div>

        <!-- Main Form -->
        <div class="sap-card">
            <form id="work-order-form" method="post">
                {% csrf_token %}
                <input type="hidden" id="action-input" name="action" value="">
                <input type="hidden" id="next-tab-input" name="next_tab" value="">

                <!-- Tab 0: Verbal Approval Details -->
                <div id="tab-0" class="tab-content">
                    <div class="mb-6">
                        <h2 class="text-lg font-semibold text-sap-gray-900 mb-2">Verbal Approval Information</h2>
                        <p class="text-sm text-sap-gray-600">Record the details of verbal approval for this work order</p>
                        
                        <!-- Temporary PO Information Notice -->
                        <div class="mt-4 p-4 bg-orange-50 border border-orange-200 rounded-lg">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <i data-lucide="info" class="w-5 h-5 text-orange-600 mt-0.5"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-orange-800">Temporary PO Generation</h3>
                                    <div class="mt-1 text-sm text-orange-700">
                                        <p>If no reference PO is provided, a temporary PO number with 'T' prefix will be automatically generated.</p>
                                        <p class="mt-1"><strong>Format:</strong> T + Year + Month + Sequential Number (e.g., T25060001)</p>
                                        <p class="mt-1">This temporary PO can be updated with the formal PO number when received from the customer.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Customer Selection -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-sap-gray-700">{{ verbal_form.customer.label }}</label>
                            {{ verbal_form.customer }}
                            {% if verbal_form.customer.help_text %}
                                <p class="text-xs text-sap-gray-500">{{ verbal_form.customer.help_text }}</p>
                            {% endif %}
                        </div>

                        <!-- Enquiry Selection -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-sap-gray-700">{{ verbal_form.enquiry.label }}</label>
                            {{ verbal_form.enquiry }}
                            {% if verbal_form.enquiry.help_text %}
                                <p class="text-xs text-sap-gray-500">{{ verbal_form.enquiry.help_text }}</p>
                            {% endif %}
                        </div>

                        <!-- Reference PO Number -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-sap-gray-700">{{ verbal_form.reference_po_number.label }}</label>
                            {{ verbal_form.reference_po_number }}
                            {% if verbal_form.reference_po_number.help_text %}
                                <p class="text-xs text-sap-gray-500">{{ verbal_form.reference_po_number.help_text }}</p>
                            {% endif %}
                        </div>

                        <!-- Approval By -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-sap-gray-700">{{ verbal_form.approval_by.label }}</label>
                            {{ verbal_form.approval_by }}
                            {% if verbal_form.approval_by.help_text %}
                                <p class="text-xs text-sap-gray-500">{{ verbal_form.approval_by.help_text }}</p>
                            {% endif %}
                        </div>

                        <!-- Approval Date -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-sap-gray-700">{{ verbal_form.approval_date.label }}</label>
                            {{ verbal_form.approval_date }}
                            {% if verbal_form.approval_date.help_text %}
                                <p class="text-xs text-sap-gray-500">{{ verbal_form.approval_date.help_text }}</p>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Approval Note -->
                    <div class="mt-6 space-y-2">
                        <label class="block text-sm font-medium text-sap-gray-700">{{ verbal_form.approval_note.label }}</label>
                        {{ verbal_form.approval_note }}
                        {% if verbal_form.approval_note.help_text %}
                            <p class="text-xs text-sap-gray-500">{{ verbal_form.approval_note.help_text }}</p>
                        {% endif %}
                    </div>

                    <div class="mt-8 flex justify-end">
                        <button type="button" onclick="goToTab(1)" class="sap-button-primary">
                            Next: Task Execution
                            <i data-lucide="chevron-right" class="w-4 h-4 ml-2"></i>
                        </button>
                    </div>
                </div>

                <!-- Tab 1: Task Execution -->
                <div id="tab-1" class="tab-content hidden">
                    <div class="mb-6">
                        <h2 class="text-lg font-semibold text-sap-gray-900 mb-2">Task Execution Details</h2>
                        <p class="text-sm text-sap-gray-600">Define project timelines and execution parameters</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Work Order Date -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-sap-gray-700">{{ task_form.work_order_date.label }}</label>
                            {{ task_form.work_order_date }}
                        </div>

                        <!-- Project Title -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-sap-gray-700">{{ task_form.project_title.label }}</label>
                            {{ task_form.project_title }}
                        </div>

                        <!-- Project Leader -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-sap-gray-700">{{ task_form.project_leader.label }}</label>
                            {{ task_form.project_leader }}
                        </div>

                        <!-- Category -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-sap-gray-700">{{ task_form.category.label }}</label>
                            {{ task_form.category }}
                        </div>

                        <!-- Subcategory -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-sap-gray-700">{{ task_form.subcategory.label }}</label>
                            {{ task_form.subcategory }}
                        </div>

                        <!-- Business Group -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-sap-gray-700">{{ task_form.business_group.label }}</label>
                            {{ task_form.business_group }}
                        </div>

                        <!-- Buyer -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-sap-gray-700">{{ task_form.buyer.label }}</label>
                            {{ task_form.buyer }}
                        </div>
                    </div>

                    <div class="mt-8 flex justify-between">
                        <button type="button" onclick="goToTab(0)" class="inline-flex items-center px-4 py-2 text-sm font-medium text-sap-gray-700 bg-white border border-sap-gray-300 rounded-lg hover:bg-sap-gray-50">
                            <i data-lucide="chevron-left" class="w-4 h-4 mr-2"></i>
                            Back: Verbal Approval
                        </button>
                        <button type="button" onclick="goToTab(2)" class="sap-button-primary">
                            Next: Shipping
                            <i data-lucide="chevron-right" class="w-4 h-4 ml-2"></i>
                        </button>
                    </div>
                </div>

                <!-- Tab 2: Shipping -->
                <div id="tab-2" class="tab-content hidden">
                    <div class="mb-6">
                        <h2 class="text-lg font-semibold text-sap-gray-900 mb-2">Shipping Information</h2>
                        <p class="text-sm text-sap-gray-600">Configure delivery and shipping details</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Shipping Address -->
                        <div class="md:col-span-2 space-y-2">
                            <label class="block text-sm font-medium text-sap-gray-700">{{ shipping_form.shipping_address.label }}</label>
                            {{ shipping_form.shipping_address }}
                        </div>

                        <!-- Country -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-sap-gray-700">{{ shipping_form.shipping_country.label }}</label>
                            {{ shipping_form.shipping_country }}
                        </div>

                        <!-- State -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-sap-gray-700">{{ shipping_form.shipping_state.label }}</label>
                            {{ shipping_form.shipping_state }}
                        </div>

                        <!-- City -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-sap-gray-700">{{ shipping_form.shipping_city.label }}</label>
                            {{ shipping_form.shipping_city }}
                        </div>

                        <!-- Contact Person 1 -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-sap-gray-700">{{ shipping_form.contact_person_1.label }}</label>
                            {{ shipping_form.contact_person_1 }}
                        </div>

                        <!-- Contact No 1 -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-sap-gray-700">{{ shipping_form.contact_no_1.label }}</label>
                            {{ shipping_form.contact_no_1 }}
                        </div>

                        <!-- Email 1 -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-sap-gray-700">{{ shipping_form.email_1.label }}</label>
                            {{ shipping_form.email_1 }}
                        </div>
                    </div>

                    <div class="mt-8 flex justify-between">
                        <button type="button" onclick="goToTab(1)" class="inline-flex items-center px-4 py-2 text-sm font-medium text-sap-gray-700 bg-white border border-sap-gray-300 rounded-lg hover:bg-sap-gray-50">
                            <i data-lucide="chevron-left" class="w-4 h-4 mr-2"></i>
                            Back: Task Execution
                        </button>
                        <button type="button" onclick="goToTab(3)" class="sap-button-primary">
                            Next: Products
                            <i data-lucide="chevron-right" class="w-4 h-4 ml-2"></i>
                        </button>
                    </div>
                </div>

                <!-- Tab 3: Products -->
                <div id="tab-3" class="tab-content hidden">
                    <div class="mb-6">
                        <h2 class="text-lg font-semibold text-sap-gray-900 mb-2">Product Management</h2>
                        <p class="text-sm text-sap-gray-600">Add products and items for this work order</p>
                    </div>

                    <!-- Product Entry Form -->
                    <div class="border border-sap-gray-200 rounded-lg p-4 mb-6">
                        <h3 class="text-md font-medium text-sap-gray-900 mb-4">Add Product</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="space-y-2">
                                <label class="block text-sm font-medium text-sap-gray-700">{{ product_form.item_code.label }}</label>
                                {{ product_form.item_code }}
                            </div>
                            <div class="space-y-2">
                                <label class="block text-sm font-medium text-sap-gray-700">{{ product_form.description.label }}</label>
                                {{ product_form.description }}
                            </div>
                            <div class="space-y-2">
                                <label class="block text-sm font-medium text-sap-gray-700">{{ product_form.quantity.label }}</label>
                                {{ product_form.quantity }}
                            </div>
                        </div>
                        <div class="mt-4">
                            <button type="button" onclick="submitForm('add_product')" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-sap-blue-600 border border-transparent rounded-lg hover:bg-sap-blue-700">
                                <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                Add Product
                            </button>
                        </div>
                    </div>

                    <!-- Products List -->
                    {% if temp_products %}
                    <div class="border border-sap-gray-200 rounded-lg overflow-hidden">
                        <table class="min-w-full divide-y divide-sap-gray-200">
                            <thead class="bg-sap-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase">Item Code</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase">Description</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase">Quantity</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase">Action</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-sap-gray-200">
                                {% for product in temp_products %}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">{{ product.item_code }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">{{ product.description }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">{{ product.quantity }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <button type="button" class="text-red-600 hover:text-red-900">Remove</button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-8 text-sap-gray-500">
                        <i data-lucide="package" class="w-12 h-12 mx-auto mb-2"></i>
                        <p>No products added yet. Add products above to continue.</p>
                    </div>
                    {% endif %}

                    <div class="mt-8 flex justify-between">
                        <button type="button" onclick="goToTab(2)" class="inline-flex items-center px-4 py-2 text-sm font-medium text-sap-gray-700 bg-white border border-sap-gray-300 rounded-lg hover:bg-sap-gray-50">
                            <i data-lucide="chevron-left" class="w-4 h-4 mr-2"></i>
                            Back: Shipping
                        </button>
                        <button type="button" onclick="goToTab(4)" class="sap-button-primary">
                            Next: Instructions
                            <i data-lucide="chevron-right" class="w-4 h-4 ml-2"></i>
                        </button>
                    </div>
                </div>

                <!-- Tab 4: Instructions -->
                <div id="tab-4" class="tab-content hidden">
                    <div class="mb-6">
                        <h2 class="text-lg font-semibold text-sap-gray-900 mb-2">Special Instructions</h2>
                        <p class="text-sm text-sap-gray-600">Configure additional requirements and instructions</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Checkbox Options -->
                        <div class="space-y-4">
                            <div class="flex items-center">
                                {{ instructions_form.primer_painting }}
                                <label for="{{ instructions_form.primer_painting.id_for_label }}" class="ml-2 text-sm text-sap-gray-700">{{ instructions_form.primer_painting.label }}</label>
                            </div>
                            <div class="flex items-center">
                                {{ instructions_form.painting }}
                                <label for="{{ instructions_form.painting.id_for_label }}" class="ml-2 text-sm text-sap-gray-700">{{ instructions_form.painting.label }}</label>
                            </div>
                            <div class="flex items-center">
                                {{ instructions_form.self_certification_report }}
                                <label for="{{ instructions_form.self_certification_report.id_for_label }}" class="ml-2 text-sm text-sap-gray-700">{{ instructions_form.self_certification_report.label }}</label>
                            </div>
                        </div>

                        <!-- Text Fields -->
                        <div class="space-y-4">
                            <div class="space-y-2">
                                <label class="block text-sm font-medium text-sap-gray-700">{{ instructions_form.other_instructions.label }}</label>
                                {{ instructions_form.other_instructions }}
                            </div>
                            <div class="space-y-2">
                                <label class="block text-sm font-medium text-sap-gray-700">{{ instructions_form.export_case_mark.label }}</label>
                                {{ instructions_form.export_case_mark }}
                            </div>
                        </div>
                    </div>

                    <div class="mt-8 flex justify-between">
                        <button type="button" onclick="goToTab(3)" class="inline-flex items-center px-4 py-2 text-sm font-medium text-sap-gray-700 bg-white border border-sap-gray-300 rounded-lg hover:bg-sap-gray-50">
                            <i data-lucide="chevron-left" class="w-4 h-4 mr-2"></i>
                            Back: Products
                        </button>
                        <button type="button" onclick="submitForm('submit_work_order')" class="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200">
                            <i data-lucide="check" class="w-4 h-4 mr-2"></i>
                            Create Work Order
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}