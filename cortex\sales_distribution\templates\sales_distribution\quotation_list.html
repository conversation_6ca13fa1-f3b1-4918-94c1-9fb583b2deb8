{% extends "core/base.html" %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-sap-blue-500 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Quotation Management</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Track and manage all customer quotations</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <div class="text-right mr-4 px-4 py-2 bg-sap-gray-50 rounded-lg border border-sap-gray-200">
                    <p class="text-2xs font-medium text-sap-gray-500 uppercase tracking-wide">Total Quotations</p>
                    <p class="text-lg font-semibold text-sap-blue-600">{{ quotations|length }}</p>
                </div>
                <a href="{% url 'sales_distribution:quotation_selection' %}" 
                   class="sap-button-primary">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    Create New Quotation
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-2 py-4 space-y-2">
    
    <!-- Search and Filter Card -->
    <div class="sap-card">
        <div class="px-6 py-4">
            <div class="flex items-center space-x-4">
                <div class="flex-1">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i data-lucide="search" class="w-4 h-4 text-sap-gray-400"></i>
                        </div>
                        <input type="text" 
                               id="real-time-search"
                               name="search" 
                               placeholder="Search quotations by customer name or quotation number..." 
                               value="{{ request.GET.search|default:'' }}"
                               class="sap-input pl-10">
                    </div>
                </div>
                <div class="w-48">
                    <select name="status" 
                            id="status-filter"
                            class="sap-input">
                        <option value="">All Status</option>
                        <option value="pending" {% if request.GET.status == "pending" %}selected{% endif %}>Pending</option>
                        <option value="checked" {% if request.GET.status == "checked" %}selected{% endif %}>Checked</option>
                        <option value="approved" {% if request.GET.status == "approved" %}selected{% endif %}>Approved</option>
                        <option value="authorized" {% if request.GET.status == "authorized" %}selected{% endif %}>Authorized</option>
                    </select>
                </div>
                <div class="flex space-x-2">
                    <button type="button" 
                            onclick="clearSearch()"
                            class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                        <i data-lucide="x" class="w-4 h-4 mr-2"></i>
                        Clear
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced SAP S/4 HANA Quotations Table -->
    <div class="sap-card">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-sap-blue-500 rounded-lg flex items-center justify-center">
                        <i data-lucide="database" class="w-4 h-4 text-white"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-sap-gray-800">Customer Quotations</h3>
                        <p class="text-sm text-sap-gray-600">Professional quotation management system</p>
                    </div>
                </div>
                <div class="text-right px-3 py-1.5 bg-sap-blue-50 rounded-lg border border-sap-blue-200">
                    <p class="text-xs font-medium text-sap-blue-600 uppercase">Active Records</p>
                    <p class="text-lg font-bold text-sap-blue-700">{{ quotations|length }}</p>
                </div>
            </div>
        </div>
        
        {% if quotations %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-gradient-to-r from-sap-gray-50 to-sap-blue-50">
                    <tr>
                        <th scope="col" class="px-4 py-4 text-center text-xs font-semibold text-sap-gray-700 uppercase tracking-wider w-16">
                            <div class="flex flex-col items-center">
                                <i data-lucide="hash" class="w-3 h-3 mb-1 text-sap-gray-500"></i>
                                <span>SN</span>
                            </div>
                        </th>
                        <th scope="col" class="px-4 py-4 text-center text-xs font-semibold text-sap-gray-700 uppercase tracking-wider w-24">
                            <div class="flex flex-col items-center">
                                <i data-lucide="calendar" class="w-3 h-3 mb-1 text-sap-gray-500"></i>
                                <span>Fin Yrs</span>
                            </div>
                        </th>
                        <th scope="col" class="px-4 py-4 text-left text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">
                            <div class="flex items-center">
                                <i data-lucide="user" class="w-3 h-3 mr-2 text-sap-gray-500"></i>
                                <span>Customer Name</span>
                            </div>
                        </th>
                        <th scope="col" class="px-4 py-4 text-center text-xs font-semibold text-sap-gray-700 uppercase tracking-wider w-20">
                            <div class="flex flex-col items-center">
                                <i data-lucide="tag" class="w-3 h-3 mb-1 text-sap-gray-500"></i>
                                <span>Code</span>
                            </div>
                        </th>
                        <th scope="col" class="px-4 py-4 text-center text-xs font-semibold text-sap-gray-700 uppercase tracking-wider w-32">
                            <div class="flex flex-col items-center">
                                <i data-lucide="file-text" class="w-3 h-3 mb-1 text-sap-gray-500"></i>
                                <span>Quotation No</span>
                            </div>
                        </th>
                        <th scope="col" class="px-4 py-4 text-center text-xs font-semibold text-sap-gray-700 uppercase tracking-wider w-24">
                            <div class="flex flex-col items-center">
                                <i data-lucide="link" class="w-3 h-3 mb-1 text-sap-gray-500"></i>
                                <span>Enquiry No</span>
                            </div>
                        </th>
                        <th scope="col" class="px-4 py-4 text-center text-xs font-semibold text-sap-gray-700 uppercase tracking-wider w-28">
                            <div class="flex flex-col items-center">
                                <i data-lucide="calendar-days" class="w-3 h-3 mb-1 text-sap-gray-500"></i>
                                <span>Gen Date</span>
                            </div>
                        </th>
                        <th scope="col" class="px-4 py-4 text-left text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">
                            <div class="flex items-center">
                                <i data-lucide="user-cog" class="w-3 h-3 mr-2 text-sap-gray-500"></i>
                                <span>Gen By</span>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for quotation in quotations %}
                    <tr class="hover:bg-sap-gray-50 transition-all duration-200 group">
                        <!-- Serial Number -->
                        <td class="px-4 py-4 whitespace-nowrap text-center">
                            <div class="w-8 h-8 bg-sap-blue-100 rounded-full flex items-center justify-center mx-auto">
                                <span class="text-sm font-semibold text-sap-blue-700">{{ forloop.counter }}</span>
                            </div>
                        </td>
                        
                        <!-- Financial Year -->
                        <td class="px-4 py-4 whitespace-nowrap text-center">
                            <div class="bg-sap-gray-100 rounded-lg px-3 py-1">
                                <span class="text-xs font-medium text-sap-gray-700">
                                    {% if quotation.finyearid %}
                                        {{ quotation.finyearid.finyear|default:"2019-20" }}
                                    {% else %}
                                        2019-20
                                    {% endif %}
                                </span>
                            </div>
                        </td>
                        
                        <!-- Customer Name -->
                        <td class="px-4 py-4 whitespace-nowrap">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-gradient-to-br from-sap-blue-400 to-sap-blue-600 rounded-lg flex items-center justify-center">
                                    <span class="text-white font-semibold text-xs">
                                        {% if quotation.enqid.customername %}
                                            {{ quotation.enqid.customername|first|default:"?" }}
                                        {% else %}
                                            C
                                        {% endif %}
                                    </span>
                                </div>
                                <div class="min-w-0 flex-1">
                                    <div class="text-sm font-medium text-sap-gray-900 truncate">
                                        {% if quotation.enqid.customername %}
                                            {{ quotation.enqid.customername }}
                                        {% else %}
                                            {{ quotation.customerid|default:"UNKNOWN CUSTOMER" }}
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </td>
                        
                        <!-- Customer Code -->
                        <td class="px-4 py-4 whitespace-nowrap text-center">
                            <span class="inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium bg-sap-gray-100 text-sap-gray-700">
                                {{ quotation.customerid|default:"N/A" }}
                            </span>
                        </td>
                        
                        <!-- Quotation Number -->
                        <td class="px-4 py-4 whitespace-nowrap text-center">
                            <div class="bg-sap-blue-50 rounded-lg px-3 py-1 border border-sap-blue-200">
                                <a href="{% url 'sales_distribution:quotation_detail' quotation.id %}" 
                                   class="text-sm font-bold text-sap-blue-700 hover:text-sap-blue-800 transition-colors duration-150">
                                    {{ quotation.quotationno }}
                                </a>
                            </div>
                        </td>
                        
                        <!-- Enquiry Number -->
                        <td class="px-4 py-4 whitespace-nowrap text-center">
                            {% if quotation.enqid %}
                                <span class="text-sm font-medium text-sap-gray-700">{{ quotation.enqid.enqid }}</span>
                            {% else %}
                                <span class="text-sm text-sap-gray-400">-</span>
                            {% endif %}
                        </td>
                        
                        <!-- Generation Date -->
                        <td class="px-4 py-4 whitespace-nowrap text-center">
                            <div class="text-sm text-sap-gray-700">
                                <i data-lucide="calendar" class="w-3 h-3 inline mr-1 text-sap-gray-500"></i>
                                {{ quotation.quotationdate|date:"d-m-Y"|default:"N/A" }}
                            </div>
                        </td>
                        
                        <!-- Generated By -->
                        <td class="px-4 py-4 whitespace-nowrap">
                            <div class="flex items-center space-x-2">
                                <div class="w-6 h-6 bg-sap-gray-200 rounded-full flex items-center justify-center">
                                    <i data-lucide="user" class="w-3 h-3 text-sap-gray-600"></i>
                                </div>
                                <span class="text-sm text-sap-gray-700 truncate">
                                    {{ quotation.sessionid|default:"Mr.Aniruddha Babarao Raut"|truncatechars:20 }}
                                </span>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        {% else %}
        <!-- Enhanced Empty State -->
        <div class="px-8 py-16 text-center">
            <div class="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-sap-blue-100 to-sap-blue-200 rounded-2xl flex items-center justify-center">
                <i data-lucide="file-x" class="w-10 h-10 text-sap-blue-500"></i>
            </div>
            <h3 class="text-xl font-semibold text-sap-gray-800 mb-3">No Quotations Found</h3>
            <p class="text-sm text-sap-gray-600 mb-8 max-w-md mx-auto">Start building your sales pipeline by creating your first quotation. Convert enquiries into professional quotations with detailed pricing.</p>
            <div class="flex items-center justify-center space-x-4">
                <a href="{% url 'sales_distribution:quotation_selection' %}" 
                   class="sap-button-primary">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    Create First Quotation
                </a>
                <a href="{% url 'sales_distribution:enquiry_list' %}" 
                   class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 transition-all duration-200">
                    <i data-lucide="search" class="w-4 h-4 mr-2"></i>
                    Browse Enquiries
                </a>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Enhanced SAP-style Pagination -->
    {% if is_paginated %}
    <div class="sap-card">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="text-sm text-sap-gray-600">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ paginator.count }} results
                </div>
                <div class="flex space-x-2">
                    {% if page_obj.has_previous %}
                        <a href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            First
                        </a>
                        <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Previous
                        </a>
                    {% endif %}
                    
                    <span class="px-3 py-2 bg-sap-blue-50 border border-sap-blue-200 rounded-lg text-sm font-medium text-sap-blue-600">
                        Page {{ page_obj.number }} of {{ paginator.num_pages }}
                    </span>
                    
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Next
                        </a>
                        <a href="?page={{ paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Last
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

</div>

<script>
// Clear search function
function clearSearch() {
    document.getElementById('real-time-search').value = '';
    document.getElementById('status-filter').value = '';
    // Reload page without parameters
    window.location.href = window.location.pathname;
}

// Enhanced search functionality
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit search on Enter key
    const searchInput = document.getElementById('real-time-search');
    const statusFilter = document.getElementById('status-filter');
    
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    }
    
    if (statusFilter) {
        statusFilter.addEventListener('change', function() {
            performSearch();
        });
    }
    
    function performSearch() {
        const search = searchInput ? searchInput.value : '';
        const status = statusFilter ? statusFilter.value : '';
        
        let url = window.location.pathname + '?';
        const params = [];
        
        if (search) params.push('search=' + encodeURIComponent(search));
        if (status) params.push('status=' + encodeURIComponent(status));
        
        url += params.join('&');
        window.location.href = url;
    }
    
    // Add enhanced row hover effects
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.classList.add('transform', 'scale-[1.005]', 'shadow-md');
        });
        
        row.addEventListener('mouseleave', function() {
            this.classList.remove('transform', 'scale-[1.005]', 'shadow-md');
        });
    });
});
</script>
{% endblock %}