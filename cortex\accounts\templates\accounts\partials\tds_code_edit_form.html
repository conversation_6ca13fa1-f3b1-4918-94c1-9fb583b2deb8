<!-- accounts/partials/tds_code_edit_form.html -->
<!-- HTMX partial for TDSCode edit form - SAP S/4HANA inspired -->

{% load static %}

<div class="bg-sap-yellow-50 border border-sap-yellow-200 rounded-lg p-4 mb-4" id="tds-code-edit-form-{{ tds_code.id }}">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-sap-yellow-100 rounded-lg flex items-center justify-center">
                <i data-lucide="edit" class="w-4 h-4 text-sap-yellow-600"></i>
            </div>
            <div>
                <h4 class="text-lg font-medium text-sap-gray-800">Edit TDS Code</h4>
                <p class="text-sm text-sap-gray-600">Update TDS code information (ID: {{ tds_code.id }})</p>
            </div>
        </div>
        <button type="button" 
                hx-get="{% url 'accounts:tds_code_list' %}"
                hx-target="#tds-code-table"
                hx-swap="outerHTML"
                class="inline-flex items-center px-3 py-1.5 border border-sap-gray-300 rounded text-xs font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
            <i data-lucide="x" class="w-3 h-3 mr-1"></i>
            Cancel
        </button>
    </div>
    
    <form hx-put="{% url 'accounts:tds_code_edit' tds_code.id %}" 
          hx-target="#tds-code-edit-form-{{ tds_code.id }}" 
          hx-swap="outerHTML"
          hx-trigger="submit"
          class="space-y-4">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="{{ form.section_code.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                    Section Code <span class="text-red-500">*</span>
                </label>
                {{ form.section_code }}
                {% if form.section_code.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.section_code.errors.0 }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.nature_of_payment.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                    Nature of Payment <span class="text-red-500">*</span>
                </label>
                {{ form.nature_of_payment }}
                {% if form.nature_of_payment.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.nature_of_payment.errors.0 }}</p>
                {% endif %}
            </div>
        </div>
        
        <div>
            <label for="{{ form.percentage.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                TDS Percentage <span class="text-red-500">*</span>
            </label>
            {{ form.percentage }}
            {% if form.percentage.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.percentage.errors.0 }}</p>
            {% endif %}
        </div>
        
        <div class="flex justify-end space-x-3 pt-4 border-t border-sap-yellow-200">
            <button type="button" 
                    hx-get="{% url 'accounts:tds_code_list' %}"
                    hx-target="#tds-code-table"
                    hx-swap="outerHTML"
                    class="px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                Cancel
            </button>
            <button type="submit" 
                    class="px-4 py-2 bg-sap-yellow-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-sap-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-yellow-500">
                <i data-lucide="save" class="w-4 h-4 mr-2 inline"></i>
                Update TDS Code
            </button>
        </div>
    </form>
</div>