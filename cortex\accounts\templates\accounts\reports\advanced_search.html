<!-- accounts/templates/accounts/reports/advanced_search.html -->
<!-- Advanced Search Interface - SAP S/4HANA Inspired Design -->
<!-- Replaces ASP.NET Search.aspx functionality -->

{% extends 'core/base.html' %}
{% load static %}

{% block title %}Advanced Search - {{ page_title }} - Cortex{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-sap-blue-500 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="search" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Advanced Search</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Multi-criteria search across all accounting records</p>
                </div>
            </div>
            <div class="flex space-x-3">
                <button 
                    type="button"
                    id="save-search-btn"
                    class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:ring-offset-2 transition-colors duration-200">
                    <i data-lucide="bookmark" class="w-4 h-4 mr-2"></i>
                    Save Search
                </button>
                <button 
                    type="button"
                    id="load-search-btn"
                    class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:ring-offset-2 transition-colors duration-200">
                    <i data-lucide="folder-open" class="w-4 h-4 mr-2"></i>
                    Load Saved
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-2 py-6 space-y-6" id="advanced-search-content">
    
    <!-- Search Form -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-200 bg-sap-gray-50">
            <h3 class="text-lg font-semibold text-sap-gray-800 flex items-center">
                <i data-lucide="filter" class="w-5 h-5 mr-2 text-sap-blue-500"></i>
                Search Criteria
            </h3>
        </div>
        
        <form 
            id="advanced-search-form"
            hx-post="{% url 'accounts:search_results' %}" 
            hx-target="#search-results" 
            hx-indicator="#search-indicator"
            class="p-6 space-y-6">
            {% csrf_token %}
            
            <!-- Basic Search -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <h4 class="text-md font-semibold text-sap-gray-800 border-b border-sap-gray-200 pb-2">
                        Basic Search Parameters
                    </h4>
                    
                    <!-- General Search -->
                    <div>
                        <label for="general_search" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            General Search
                        </label>
                        <input 
                            type="text" 
                            name="general_search" 
                            id="general_search"
                            placeholder="Search across all fields..."
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                    </div>
                    
                    <!-- Transaction Type -->
                    <div>
                        <label for="transaction_type" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Transaction Type
                        </label>
                        <select 
                            name="transaction_type" 
                            id="transaction_type"
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                            <option value="">All Types</option>
                            <option value="cash_voucher">Cash Voucher</option>
                            <option value="bank_voucher">Bank Voucher</option>
                            <option value="contra_entry">Contra Entry</option>
                            <option value="debit_note">Debit Note</option>
                            <option value="credit_note">Credit Note</option>
                            <option value="sales_invoice">Sales Invoice</option>
                            <option value="purchase_invoice">Purchase Invoice</option>
                            <option value="receipt">Receipt</option>
                            <option value="payment">Payment</option>
                        </select>
                    </div>
                    
                    <!-- Account Head -->
                    <div>
                        <label for="account_head" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Account Head
                        </label>
                        <select 
                            name="account_head" 
                            id="account_head"
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                            <option value="">Select Account Head</option>
                            {% for account in account_heads %}
                            <option value="{{ account.id }}">{{ account.account_head_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <!-- Party/Vendor -->
                    <div>
                        <label for="party_name" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Party/Vendor Name
                        </label>
                        <input 
                            type="text" 
                            name="party_name" 
                            id="party_name"
                            placeholder="Enter party or vendor name..."
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                    </div>
                </div>
                
                <div class="space-y-4">
                    <h4 class="text-md font-semibold text-sap-gray-800 border-b border-sap-gray-200 pb-2">
                        Date & Amount Filters
                    </h4>
                    
                    <!-- Date Range -->
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="date_from" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                From Date
                            </label>
                            <input 
                                type="date" 
                                name="date_from" 
                                id="date_from"
                                class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        </div>
                        <div>
                            <label for="date_to" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                To Date
                            </label>
                            <input 
                                type="date" 
                                name="date_to" 
                                id="date_to"
                                class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        </div>
                    </div>
                    
                    <!-- Amount Range -->
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="amount_from" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                Min Amount (₹)
                            </label>
                            <input 
                                type="number" 
                                name="amount_from" 
                                id="amount_from"
                                step="0.01"
                                placeholder="0.00"
                                class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        </div>
                        <div>
                            <label for="amount_to" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                Max Amount (₹)
                            </label>
                            <input 
                                type="number" 
                                name="amount_to" 
                                id="amount_to"
                                step="0.01"
                                placeholder="999999.99"
                                class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        </div>
                    </div>
                    
                    <!-- Reference Number -->
                    <div>
                        <label for="reference_number" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Reference Number
                        </label>
                        <input 
                            type="text" 
                            name="reference_number" 
                            id="reference_number"
                            placeholder="Enter reference number..."
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                    </div>
                    
                    <!-- Narration/Particulars -->
                    <div>
                        <label for="narration" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Narration/Particulars
                        </label>
                        <input 
                            type="text" 
                            name="narration" 
                            id="narration"
                            placeholder="Search in descriptions..."
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                    </div>
                </div>
            </div>
            
            <!-- Advanced Filters (Expandable) -->
            <div x-data="{ expanded: false }" class="border-t border-sap-gray-200 pt-6">
                <div class="flex items-center justify-between cursor-pointer" @click="expanded = !expanded">
                    <h4 class="text-md font-semibold text-sap-gray-800 flex items-center">
                        <i data-lucide="settings" class="w-5 h-5 mr-2 text-sap-orange-500"></i>
                        Advanced Filters
                    </h4>
                    <i data-lucide="chevron-down" class="w-5 h-5 text-sap-gray-500 transition-transform duration-200" 
                       :class="{ 'transform rotate-180': expanded }"></i>
                </div>
                
                <div x-show="expanded" x-transition class="mt-4 grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Tax Filters -->
                    <div>
                        <label for="tax_type" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Tax Type
                        </label>
                        <select 
                            name="tax_type" 
                            id="tax_type"
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                            <option value="">All Tax Types</option>
                            <option value="vat">VAT</option>
                            <option value="excise">Excise</option>
                            <option value="service_tax">Service Tax</option>
                            <option value="tds">TDS</option>
                        </select>
                    </div>
                    
                    <!-- Payment Mode -->
                    <div>
                        <label for="payment_mode" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Payment Mode
                        </label>
                        <select 
                            name="payment_mode" 
                            id="payment_mode"
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                            <option value="">All Modes</option>
                            <option value="cash">Cash</option>
                            <option value="cheque">Cheque</option>
                            <option value="neft">NEFT</option>
                            <option value="rtgs">RTGS</option>
                            <option value="dd">Demand Draft</option>
                        </select>
                    </div>
                    
                    <!-- Status -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Status
                        </label>
                        <select 
                            name="status" 
                            id="status"
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="pending">Pending</option>
                            <option value="cancelled">Cancelled</option>
                            <option value="reconciled">Reconciled</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- Search Actions -->
            <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
                <div class="flex space-x-4">
                    <button 
                        type="submit"
                        class="inline-flex items-center px-6 py-2 bg-sap-blue-500 text-white rounded-lg hover:bg-sap-blue-600 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:ring-offset-2 transition-colors duration-200">
                        <i data-lucide="search" class="w-4 h-4 mr-2"></i>
                        Search
                    </button>
                    <button 
                        type="button"
                        onclick="document.getElementById('advanced-search-form').reset()"
                        class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-sap-gray-500 focus:ring-offset-2 transition-colors duration-200">
                        <i data-lucide="x-circle" class="w-4 h-4 mr-2"></i>
                        Clear All
                    </button>
                </div>
                
                <!-- Search Options -->
                <div class="flex items-center space-x-4 text-sm">
                    <label class="flex items-center">
                        <input type="checkbox" name="exact_match" class="rounded border-sap-gray-300 text-sap-blue-600 focus:ring-sap-blue-500">
                        <span class="ml-2 text-sap-gray-700">Exact match</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="case_sensitive" class="rounded border-sap-gray-300 text-sap-blue-600 focus:ring-sap-blue-500">
                        <span class="ml-2 text-sap-gray-700">Case sensitive</span>
                    </label>
                </div>
            </div>
        </form>
        
        <!-- Search Indicator -->
        <div id="search-indicator" class="htmx-indicator p-6 border-t border-sap-gray-200">
            <div class="flex items-center justify-center py-4">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-sap-blue-500 mr-3"></div>
                <span class="text-sap-gray-600">Searching records...</span>
            </div>
        </div>
    </div>

    <!-- Saved Searches -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm" x-data="{ showSaved: false }">
        <div class="px-6 py-4 border-b border-sap-gray-200 cursor-pointer" @click="showSaved = !showSaved">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-sap-gray-800 flex items-center">
                    <i data-lucide="bookmark" class="w-5 h-5 mr-2 text-sap-green-500"></i>
                    Saved Searches
                </h3>
                <i data-lucide="chevron-down" class="w-5 h-5 text-sap-gray-500 transition-transform duration-200" 
                   :class="{ 'transform rotate-180': showSaved }"></i>
            </div>
        </div>
        <div x-show="showSaved" x-transition class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {% for saved_search in saved_searches %}
                <div class="border border-sap-gray-200 rounded-lg p-4 hover:border-sap-blue-300 transition-colors duration-150">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-medium text-sap-gray-800">{{ saved_search.name }}</h4>
                        <div class="flex space-x-2">
                            <button 
                                type="button"
                                onclick="loadSavedSearch('{{ saved_search.id }}')"
                                class="text-sap-blue-600 hover:text-sap-blue-800 transition-colors duration-150"
                                title="Load Search">
                                <i data-lucide="play" class="w-4 h-4"></i>
                            </button>
                            <button 
                                type="button"
                                onclick="deleteSavedSearch('{{ saved_search.id }}')"
                                class="text-sap-red-600 hover:text-sap-red-800 transition-colors duration-150"
                                title="Delete Search">
                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                    <p class="text-sm text-sap-gray-600 mb-2">{{ saved_search.description|default:"No description" }}</p>
                    <div class="text-2xs text-sap-gray-500">
                        Created: {{ saved_search.created_at|date:"d/m/Y" }}
                    </div>
                </div>
                {% empty %}
                <div class="col-span-full text-center py-8">
                    <i data-lucide="bookmark" class="w-12 h-12 text-sap-gray-400 mx-auto mb-4"></i>
                    <p class="text-sap-gray-500">No saved searches yet</p>
                    <p class="text-sm text-sap-gray-400 mt-1">Use the "Save Search" button to save your search criteria</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Search Results Container -->
    <div id="search-results">
        <!-- Results will be loaded here -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-8 text-center">
                <div class="flex flex-col items-center justify-center">
                    <i data-lucide="search" class="w-16 h-16 text-sap-gray-400 mb-4"></i>
                    <h3 class="text-lg font-medium text-sap-gray-900 mb-2">Ready to Search</h3>
                    <p class="text-sm text-sap-gray-500 max-w-md">
                        Enter your search criteria above and click "Search" to find matching records across all accounting modules.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Save Search Modal -->
<div id="save-search-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <h3 class="text-lg font-semibold text-sap-gray-800 mb-4">Save Search Criteria</h3>
        <form id="save-search-form">
            <div class="space-y-4">
                <div>
                    <label for="search_name" class="block text-sm font-medium text-sap-gray-700 mb-2">Search Name</label>
                    <input 
                        type="text" 
                        id="search_name" 
                        name="search_name"
                        required
                        class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                </div>
                <div>
                    <label for="search_description" class="block text-sm font-medium text-sap-gray-700 mb-2">Description (optional)</label>
                    <textarea 
                        id="search_description" 
                        name="search_description"
                        rows="3"
                        class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"></textarea>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-6">
                <button 
                    type="button" 
                    onclick="closeSaveModal()"
                    class="px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                    Cancel
                </button>
                <button 
                    type="submit"
                    class="px-4 py-2 bg-sap-blue-500 text-white rounded-lg hover:bg-sap-blue-600">
                    Save Search
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
    
    // Save search functionality
    document.getElementById('save-search-btn').addEventListener('click', function() {
        document.getElementById('save-search-modal').classList.remove('hidden');
    });
    
    // Save search form submission
    document.getElementById('save-search-form').addEventListener('submit', function(e) {
        e.preventDefault();
        // Here you would implement the actual save functionality
        // For now, just close the modal
        closeSaveModal();
        // Show success message
        alert('Search criteria saved successfully!');
    });
});

function closeSaveModal() {
    document.getElementById('save-search-modal').classList.add('hidden');
}

function loadSavedSearch(searchId) {
    // Implement loading saved search criteria
    console.log('Loading saved search:', searchId);
    // Here you would load the saved criteria and populate the form
}

function deleteSavedSearch(searchId) {
    if (confirm('Are you sure you want to delete this saved search?')) {
        // Implement delete functionality
        console.log('Deleting saved search:', searchId);
    }
}

// Auto-save search criteria to localStorage
function autoSaveSearchCriteria() {
    const form = document.getElementById('advanced-search-form');
    const formData = new FormData(form);
    const criteria = {};
    for (let [key, value] of formData.entries()) {
        if (value) criteria[key] = value;
    }
    localStorage.setItem('lastSearchCriteria', JSON.stringify(criteria));
}

// Load last search criteria from localStorage
function loadLastSearchCriteria() {
    const saved = localStorage.getItem('lastSearchCriteria');
    if (saved) {
        const criteria = JSON.parse(saved);
        const form = document.getElementById('advanced-search-form');
        Object.keys(criteria).forEach(key => {
            const field = form.querySelector(`[name="${key}"]`);
            if (field) field.value = criteria[key];
        });
    }
}

// Load last search on page load
document.addEventListener('DOMContentLoaded', loadLastSearchCriteria);

// Save search criteria when form changes
document.getElementById('advanced-search-form').addEventListener('input', autoSaveSearchCriteria);
</script>
{% endblock %}