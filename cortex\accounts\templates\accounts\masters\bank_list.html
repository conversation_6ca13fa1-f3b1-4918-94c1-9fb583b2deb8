<!-- accounts/templates/accounts/masters/bank_list.html -->
<!-- Professional Bank Management - SAP S/4HANA Inspired Design -->
<!-- Replaces ASP.NET Bank.aspx functionality -->

{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }} - Cortex{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-sap-green-500 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="landmark" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Bank Management</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage banking institutions and their details</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <div class="text-right mr-4 px-4 py-2 bg-sap-gray-50 rounded-lg border border-sap-gray-200">
                    <p class="text-2xs font-medium text-sap-gray-500 uppercase tracking-wide">Total Banks</p>
                    <p class="text-lg font-semibold text-sap-green-600" id="bank-count">{{ banks|length }}</p>
                </div>
                <a href="{% url 'accounts:dashboard' %}" 
                   class="inline-flex items-center px-4 py-2.5 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 hover:border-sap-blue-300 transition-all duration-200">
                    <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-2 py-4 space-y-2">
    
    <!-- Add New Bank Card -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-3 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-sap-green-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="plus" class="w-4 h-4 text-sap-green-600"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-sap-gray-800">Add New Bank</h3>
                        <p class="text-sm text-sap-gray-600">Create a new bank entry</p>
                    </div>
                </div>
                <button type="button" id="toggle-form-btn"
                        class="inline-flex items-center px-4 py-2 border border-sap-green-300 rounded-lg text-sm font-medium text-sap-green-700 bg-sap-green-50 hover:bg-sap-green-100 transition-colors duration-200">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    <span id="toggle-form-text">Show Form</span>
                </button>
            </div>
        </div>
        
        <!-- Add Bank Form (Hidden by default) -->
        <div id="add-bank-form" class="px-6 py-4 border-b border-sap-gray-100 hidden">
            <form hx-post="{% url 'accounts:bank_create' %}" 
                  hx-target="#bank-table" 
                  hx-swap="outerHTML"
                  hx-trigger="submit"
                  class="space-y-4">
                {% csrf_token %}
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="{{ bank_form.name.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Bank Name <span class="text-red-500">*</span>
                        </label>
                        {{ bank_form.name }}
                        {% if bank_form.name.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ bank_form.name.errors.0 }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ bank_form.address.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Address <span class="text-red-500">*</span>
                        </label>
                        {{ bank_form.address }}
                        {% if bank_form.address.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ bank_form.address.errors.0 }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ bank_form.country.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Country <span class="text-red-500">*</span>
                        </label>
                        {{ bank_form.country }}
                        {% if bank_form.country.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ bank_form.country.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="{{ bank_form.state.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            State <span class="text-red-500">*</span>
                        </label>
                        {{ bank_form.state }}
                        {% if bank_form.state.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ bank_form.state.errors.0 }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ bank_form.city.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            City <span class="text-red-500">*</span>
                        </label>
                        {{ bank_form.city }}
                        {% if bank_form.city.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ bank_form.city.errors.0 }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ bank_form.pin_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            PIN Code <span class="text-red-500">*</span>
                        </label>
                        {{ bank_form.pin_no }}
                        {% if bank_form.pin_no.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ bank_form.pin_no.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="{{ bank_form.contact_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Contact Number <span class="text-red-500">*</span>
                        </label>
                        {{ bank_form.contact_no }}
                        {% if bank_form.contact_no.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ bank_form.contact_no.errors.0 }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ bank_form.fax_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Fax Number <span class="text-red-500">*</span>
                        </label>
                        {{ bank_form.fax_no }}
                        {% if bank_form.fax_no.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ bank_form.fax_no.errors.0 }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ bank_form.ifsc.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            IFSC Code <span class="text-red-500">*</span>
                        </label>
                        {{ bank_form.ifsc }}
                        {% if bank_form.ifsc.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ bank_form.ifsc.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="hideForm()" 
                            class="px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-sap-green-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-sap-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-green-500">
                        Insert Record
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Real-time Search Card -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4">
            <div class="flex items-center space-x-4">
                <div class="flex-1">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i data-lucide="search" class="w-4 h-4 text-sap-gray-400"></i>
                        </div>
                        <input type="text" 
                               id="real-time-search"
                               name="search" 
                               placeholder="Search by bank name, IFSC, address, or contact..." 
                               value="{{ request.GET.search|default:'' }}"
                               hx-get="{% url 'accounts:bank_list' %}"
                               hx-target="#bank-table"
                               hx-swap="outerHTML"
                               hx-trigger="keyup changed delay:300ms, search"
                               class="block w-full pl-10 pr-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm placeholder-sap-gray-500 focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                    </div>
                </div>
                <div class="flex space-x-2">
                    <button type="button" 
                            onclick="clearSearch()"
                            class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                        <i data-lucide="x" class="w-4 h-4 mr-2"></i>
                        Clear
                    </button>
                </div>
            </div>
            <div class="mt-2 text-xs text-sap-gray-500">
                <i data-lucide="info" class="w-3 h-3 inline mr-1"></i>
                Search results update automatically as you type
            </div>
        </div>
    </div>

    <!-- Banks Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Banks</h3>
                <div class="text-sm text-sap-gray-600">
                    Showing {{ banks|length }} banks
                </div>
            </div>
        </div>
        
        <div id="bank-table">
            {% include 'accounts/partials/bank_table.html' %}
        </div>
    </div>

</div>

<script>
function showForm() {
    document.getElementById('add-bank-form').classList.remove('hidden');
    document.getElementById('toggle-form-text').textContent = 'Hide Form';
    document.getElementById('toggle-form-btn').onclick = hideForm;
}

function hideForm() {
    document.getElementById('add-bank-form').classList.add('hidden');
    document.getElementById('toggle-form-text').textContent = 'Show Form';
    document.getElementById('toggle-form-btn').onclick = showForm;
    // Clear form
    document.querySelector('#add-bank-form form').reset();
}

// Clear search function
function clearSearch() {
    document.getElementById('real-time-search').value = '';
    // Trigger HTMX to reload with cleared filters
    htmx.trigger('#real-time-search', 'keyup');
}

// Toggle form visibility
document.getElementById('toggle-form-btn').onclick = showForm;

// Confirmation dialogs
function confirmDelete() {
    return confirm('Are you sure you want to delete this bank? This action cannot be undone.');
}
</script>
{% endblock %}