from django import forms
from django.core.exceptions import ValidationError
from datetime import date


class UniversalSearchForm(forms.Form):
    """Universal search form for inventory"""
    
    SEARCH_TYPE_CHOICES = [
        ('all', 'All Records'),
        ('items', 'Items Only'),
        ('transactions', 'Transactions Only'),
        ('locations', 'Locations Only'),
        ('documents', 'Documents Only'),
    ]
    
    DOCUMENT_TYPE_CHOICES = [
        ('', 'All Documents'),
        ('mrs', 'Material Requisition (MRS)'),
        ('min', 'Material Issue Note (MIN)'),
        ('mrn', 'Material Return Note (MRN)'),
        ('gin', 'Goods Inward Note (GIN)'),
        ('grr', 'Goods Received Receipt (GRR)'),
        ('mcn', 'Material Credit Note (MCN)'),
        ('sn', 'Service Note (SN)'),
        ('challan', 'Challans'),
        ('wis', 'Work in Progress (WIS)'),
    ]
    
    SORT_ORDER_CHOICES = [
        ('relevance', 'Relevance'),
        ('date_desc', 'Date (Newest First)'),
        ('date_asc', 'Date (Oldest First)'),
        ('value_desc', 'Value (Highest First)'),
        ('value_asc', 'Value (Lowest First)'),
        ('alpha_asc', 'Alphabetical (A-Z)'),
        ('alpha_desc', 'Alphabetical (Z-A)'),
    ]
    
    # Main search query
    query = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 text-lg',
            'placeholder': 'Search items, transactions, documents, locations...',
            'autocomplete': 'off'
        }),
        help_text='Enter keywords, item codes, document numbers, or any text to search'
    )
    
    # Search filters
    search_type = forms.ChoiceField(
        choices=SEARCH_TYPE_CHOICES,
        initial='all',
        widget=forms.Select(attrs={
            'class': 'form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500'
        })
    )
    
    document_type = forms.ChoiceField(
        choices=DOCUMENT_TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500'
        })
    )
    
    # Date range filters
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500'
        })
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500'
        })
    )
    
    # Value range filters
    value_from = forms.DecimalField(
        required=False,
        min_value=0,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'step': '0.01',
            'placeholder': 'Minimum value'
        })
    )
    
    value_to = forms.DecimalField(
        required=False,
        min_value=0,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'step': '0.01',
            'placeholder': 'Maximum value'
        })
    )
    
    # Location filter
    location_filter = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'placeholder': 'Filter by location'
        })
    )
    
    # Category filter
    category_filter = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'placeholder': 'Filter by category'
        })
    )
    
    # Status filter
    status_filter = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'placeholder': 'Filter by status'
        })
    )
    
    # Sort order
    sort_order = forms.ChoiceField(
        choices=SORT_ORDER_CHOICES,
        initial='relevance',
        widget=forms.Select(attrs={
            'class': 'form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500'
        })
    )
    
    # Advanced options
    exact_match = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500'
        }),
        help_text='Search for exact phrase instead of individual words'
    )
    
    case_sensitive = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500'
        }),
        help_text='Case sensitive search'
    )
    
    include_archived = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500'
        }),
        help_text='Include archived/cancelled records'
    )
    
    def clean(self):
        cleaned_data = super().clean()
        query = cleaned_data.get('query')
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        value_from = cleaned_data.get('value_from')
        value_to = cleaned_data.get('value_to')
        
        # Validate that at least some search criteria is provided
        if not any([query, date_from, date_to, value_from, value_to,
                   cleaned_data.get('location_filter'), 
                   cleaned_data.get('category_filter'),
                   cleaned_data.get('status_filter')]):
            raise ValidationError("Please provide at least one search criterion.")
        
        # Validate date range
        if date_from and date_to and date_from > date_to:
            raise ValidationError("Start date cannot be after end date.")
        
        # Validate value range
        if value_from and value_to and value_from > value_to:
            raise ValidationError("Minimum value cannot be greater than maximum value.")
        
        # Validate future dates
        if date_to and date_to > date.today():
            raise ValidationError("End date cannot be in the future.")
        
        return cleaned_data


class AdvancedFilterForm(forms.Form):
    """Advanced filtering options"""
    
    QUICK_FILTER_CHOICES = [
        ('', 'No Quick Filter'),
        ('today', 'Today'),
        ('this_week', 'This Week'),
        ('this_month', 'This Month'),
        ('last_30_days', 'Last 30 Days'),
        ('last_90_days', 'Last 90 Days'),
        ('this_year', 'This Year'),
    ]
    
    quick_filter = forms.ChoiceField(
        choices=QUICK_FILTER_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500'
        })
    )
    
    # Field-specific searches
    item_code_contains = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'placeholder': 'Item code contains...'
        })
    )
    
    description_contains = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'placeholder': 'Description contains...'
        })
    )
    
    document_number_contains = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'placeholder': 'Document number contains...'
        })
    )
    
    # Quantity filters
    quantity_min = forms.DecimalField(
        required=False,
        min_value=0,
        decimal_places=3,
        widget=forms.NumberInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'step': '0.001',
            'placeholder': 'Min quantity'
        })
    )
    
    quantity_max = forms.DecimalField(
        required=False,
        min_value=0,
        decimal_places=3,
        widget=forms.NumberInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'step': '0.001',
            'placeholder': 'Max quantity'
        })
    )
    
    # User filters
    created_by_user = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'placeholder': 'Created by user'
        })
    )
    
    modified_by_user = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'placeholder': 'Modified by user'
        })
    )
    
    # Batch/Serial number search
    batch_number = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'placeholder': 'Batch number'
        })
    )
    
    serial_number = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'placeholder': 'Serial number'
        })
    )


class SavedSearchForm(forms.Form):
    """Form for saving and managing search queries"""
    
    search_name = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'placeholder': 'Enter name for this search',
            'required': True
        })
    )
    
    search_description = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-textarea rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'rows': 2,
            'placeholder': 'Optional description'
        })
    )
    
    is_public = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500'
        }),
        help_text='Make this search available to other users'
    )
    
    set_as_default = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500'
        }),
        help_text='Set as your default search'
    )