<!-- accounts/templates/accounts/vat/vat_register_dashboard.html -->
<!-- VAT Register Dashboard Template -->
<!-- Task Group 4: Taxation Management - VAT Register Dashboard (Task 4.7) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}VAT Register Dashboard - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-indigo-600 to-sap-indigo-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="calculator" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">VAT Register Dashboard</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage VAT returns, registers, and compliance reporting</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:taxation_dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to Taxation
                </a>
                <a href="{% url 'accounts:vat_return_create' %}" 
                   class="bg-sap-indigo-600 hover:bg-sap-indigo-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    New VAT Return
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- VAT Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Input VAT -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="arrow-down-circle" class="w-6 h-6 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Input VAT</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ input_vat_total|default:0|floatformat:2 }}</p>
                    <p class="text-xs text-sap-green-600 mt-1">Purchase VAT</p>
                </div>
            </div>
        </div>
        
        <!-- Output VAT -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-red-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="arrow-up-circle" class="w-6 h-6 text-sap-red-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Output VAT</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ output_vat_total|default:0|floatformat:2 }}</p>
                    <p class="text-xs text-sap-red-600 mt-1">Sales VAT</p>
                </div>
            </div>
        </div>
        
        <!-- Net VAT -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="trending-up" class="w-6 h-6 text-sap-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Net VAT</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ net_vat_total|default:0|floatformat:2 }}</p>
                    <p class="text-xs text-sap-purple-600 mt-1">Payable/Refundable</p>
                </div>
            </div>
        </div>
        
        <!-- Pending Returns -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="alert-circle" class="w-6 h-6 text-sap-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Pending Returns</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ pending_returns_count|default:0 }}</p>
                    <p class="text-xs text-sap-orange-600 mt-1">Need Filing</p>
                </div>
            </div>
        </div>
    </div>

    <!-- VAT Register Management Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        
        <!-- Purchase VAT Register -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="shopping-cart" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                    Purchase VAT Register
                </h3>
                <p class="text-sm text-sap-gray-600 mt-1">Input VAT on purchases and expenses</p>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:purchase_vat_register' %}" 
                       class="flex items-center w-full bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                        View Purchase VAT Register
                    </a>
                    <a href="{% url 'accounts:purchase_vat_report' %}" 
                       class="flex items-center w-full bg-sap-green-100 hover:bg-sap-green-200 text-sap-green-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="bar-chart" class="w-4 h-4 mr-2"></i>
                        Purchase VAT Report
                    </a>
                    <button type="button" onclick="exportPurchaseVAT()" 
                            class="flex items-center w-full bg-sap-gray-100 hover:bg-sap-gray-200 text-sap-gray-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                        Export Purchase Register
                    </button>
                </div>
                
                <!-- Purchase VAT Summary -->
                <div class="mt-6 pt-4 border-t border-sap-gray-200">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center">
                            <p class="text-sm text-sap-gray-600">Current Month</p>
                            <p class="text-lg font-bold text-sap-green-600">₹{{ current_month_purchase_vat|default:0|floatformat:2 }}</p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-gray-600">Total Entries</p>
                            <p class="text-lg font-bold text-sap-gray-900">{{ purchase_vat_entries_count|default:0 }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sales VAT Register -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="tag" class="w-5 h-5 mr-2 text-sap-red-600"></i>
                    Sales VAT Register
                </h3>
                <p class="text-sm text-sap-gray-600 mt-1">Output VAT on sales and services</p>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:sales_vat_register' %}" 
                       class="flex items-center w-full bg-sap-red-600 hover:bg-sap-red-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                        View Sales VAT Register
                    </a>
                    <a href="{% url 'accounts:sales_vat_report' %}" 
                       class="flex items-center w-full bg-sap-red-100 hover:bg-sap-red-200 text-sap-red-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="bar-chart" class="w-4 h-4 mr-2"></i>
                        Sales VAT Report
                    </a>
                    <button type="button" onclick="exportSalesVAT()" 
                            class="flex items-center w-full bg-sap-gray-100 hover:bg-sap-gray-200 text-sap-gray-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                        Export Sales Register
                    </button>
                </div>
                
                <!-- Sales VAT Summary -->
                <div class="mt-6 pt-4 border-t border-sap-gray-200">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center">
                            <p class="text-sm text-sap-gray-600">Current Month</p>
                            <p class="text-lg font-bold text-sap-red-600">₹{{ current_month_sales_vat|default:0|floatformat:2 }}</p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-gray-600">Total Entries</p>
                            <p class="text-lg font-bold text-sap-gray-900">{{ sales_vat_entries_count|default:0 }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- VAT Returns and Compliance Section -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        
        <!-- VAT Returns -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="file-text" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                    VAT Returns
                </h3>
                <p class="text-sm text-sap-gray-600 mt-1">{{ vat_returns_count|default:0 }} returns filed</p>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:vat_return_create' %}" 
                       class="flex items-center w-full bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        File VAT Return
                    </a>
                    <a href="{% url 'accounts:vat_return_list' %}" 
                       class="flex items-center w-full bg-sap-purple-100 hover:bg-sap-purple-200 text-sap-purple-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                        View All Returns
                    </a>
                </div>
            </div>
        </div>

        <!-- VAT Calculation Tools -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="calculator" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                    VAT Tools
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:vat_calculator' %}" 
                       class="flex items-center w-full bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="calculator" class="w-4 h-4 mr-2"></i>
                        VAT Calculator
                    </a>
                    <a href="{% url 'accounts:reverse_vat_calculator' %}" 
                       class="flex items-center w-full bg-sap-blue-100 hover:bg-sap-blue-200 text-sap-blue-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="rotate-ccw" class="w-4 h-4 mr-2"></i>
                        Reverse VAT Calculator
                    </a>
                </div>
            </div>
        </div>

        <!-- VAT Compliance -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="shield-check" class="w-5 h-5 mr-2 text-sap-amber-600"></i>
                    Compliance
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:vat_comparison_report' %}" 
                       class="flex items-center w-full bg-sap-amber-600 hover:bg-sap-amber-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="bar-chart-2" class="w-4 h-4 mr-2"></i>
                        VAT Comparison Report
                    </a>
                    <button type="button" onclick="generateComplianceReport()" 
                            class="flex items-center w-full bg-sap-amber-100 hover:bg-sap-amber-200 text-sap-amber-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="file-check" class="w-4 h-4 mr-2"></i>
                        Compliance Report
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent VAT Activity and Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        
        <!-- Recent VAT Transactions -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                        <i data-lucide="clock" class="w-5 h-5 mr-2 text-sap-indigo-600"></i>
                        Recent VAT Transactions
                    </h3>
                    <a href="{% url 'accounts:vat_transaction_list' %}" 
                       class="text-sap-indigo-600 hover:text-sap-indigo-900 text-sm font-medium">
                        View All
                    </a>
                </div>
            </div>
            <div class="p-6">
                {% if recent_vat_transactions %}
                <div class="space-y-4">
                    {% for transaction in recent_vat_transactions %}
                    <div class="flex items-center justify-between p-3 bg-sap-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-sap-indigo-100 rounded-lg flex items-center justify-center mr-3">
                                {% if transaction.transaction_type == 'purchase' %}
                                <i data-lucide="shopping-cart" class="w-4 h-4 text-sap-green-600"></i>
                                {% else %}
                                <i data-lucide="tag" class="w-4 h-4 text-sap-red-600"></i>
                                {% endif %}
                            </div>
                            <div>
                                <div class="text-sm font-medium text-sap-gray-900">{{ transaction.invoice_number|default:"Transaction" }}</div>
                                <div class="text-xs text-sap-gray-500">{{ transaction.party_name|default:"Party" }} - {{ transaction.date|date:"d M Y" }}</div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-sap-gray-900">₹{{ transaction.vat_amount|default:0|floatformat:2 }}</div>
                            <div class="text-xs text-sap-gray-500">{{ transaction.vat_rate|default:0 }}% VAT</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-6">
                    <i data-lucide="calculator" class="w-8 h-8 text-sap-gray-400 mx-auto mb-2"></i>
                    <p class="text-sm text-sap-gray-500">No recent VAT transactions</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- VAT Analytics -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="trending-up" class="w-5 h-5 mr-2 text-sap-amber-600"></i>
                    VAT Analytics
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-sap-green-500 rounded-full mr-3"></div>
                            <span class="text-sm text-sap-gray-700">Input VAT (5%)</span>
                        </div>
                        <span class="text-sm font-medium text-sap-gray-900">₹{{ vat_5_percent_input|default:0|floatformat:2 }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-sap-blue-500 rounded-full mr-3"></div>
                            <span class="text-sm text-sap-gray-700">Input VAT (12.5%)</span>
                        </div>
                        <span class="text-sm font-medium text-sap-gray-900">₹{{ vat_12_5_percent_input|default:0|floatformat:2 }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-sap-red-500 rounded-full mr-3"></div>
                            <span class="text-sm text-sap-gray-700">Output VAT (5%)</span>
                        </div>
                        <span class="text-sm font-medium text-sap-gray-900">₹{{ vat_5_percent_output|default:0|floatformat:2 }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-sap-purple-500 rounded-full mr-3"></div>
                            <span class="text-sm text-sap-gray-700">Output VAT (12.5%)</span>
                        </div>
                        <span class="text-sm font-medium text-sap-gray-900">₹{{ vat_12_5_percent_output|default:0|floatformat:2 }}</span>
                    </div>
                </div>
                
                <div class="mt-6 pt-4 border-t border-sap-gray-200">
                    <button type="button" onclick="generateVATAnalytics()" 
                            class="w-full bg-sap-amber-600 hover:bg-sap-amber-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="bar-chart" class="w-4 h-4 inline mr-2"></i>
                        Detailed VAT Analytics
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function exportPurchaseVAT() {
    alert('Purchase VAT register export functionality would be implemented here.');
}

function exportSalesVAT() {
    alert('Sales VAT register export functionality would be implemented here.');
}

function generateComplianceReport() {
    alert('VAT compliance report generation functionality would be implemented here.');
}

function generateVATAnalytics() {
    alert('Detailed VAT analytics functionality would be implemented here.');
}

// Initialize lucide icons on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %>