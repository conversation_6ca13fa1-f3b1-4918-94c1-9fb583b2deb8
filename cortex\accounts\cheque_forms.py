# accounts/cheque_forms.py
# Enhanced Django forms for Cheque Series management
# Task Group 2: Banking & Cash Management - Enhanced Cheque Forms

from django import forms
from django.core.exceptions import ValidationError
from django.db.models import Q
from datetime import date

from .models import ChequeSeries, Bank, BankVoucherMaster


class ChequeSeriesForm(forms.ModelForm):
    """
    Simplified form for Cheque Series management matching actual database structure
    """
    
    class Meta:
        model = ChequeSeries
        fields = ["bank_id", "start_no", "end_no"]
        widgets = {
            "bank_id": forms.NumberInput(attrs={
                "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                "placeholder": "Enter Bank ID",
                "min": "1",
            }),
            "start_no": forms.NumberInput(attrs={
                "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                "placeholder": "Enter starting cheque number",
                "min": "1",
            }),
            "end_no": forms.NumberInput(attrs={
                "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                "placeholder": "Enter ending cheque number",
                "min": "1",
            }),
        }
        labels = {
            "bank_id": "Bank ID",
            "start_no": "Starting Cheque Number",
            "end_no": "Ending Cheque Number",
        }
        help_texts = {
            "bank_id": "ID of the bank for this cheque series",
            "start_no": "First cheque number in this series",
            "end_no": "Last cheque number in this series",
        }

    def clean(self):
        """Basic validation for cheque series"""
        cleaned_data = super().clean()
        start_no = cleaned_data.get('start_no')
        end_no = cleaned_data.get('end_no')
        
        # Basic range validation
        if start_no and end_no:
            if start_no >= end_no:
                raise ValidationError("Starting cheque number must be less than ending cheque number")
        
        return cleaned_data


class ChequeUsageSearchForm(forms.Form):
    """
    Form for searching and filtering cheque usage reports
    """
    
    bank = forms.ModelChoiceField(
        queryset=Bank.objects.all(),
        required=True,
        empty_label="Select Bank",
        widget=forms.Select(attrs={
            "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        }),
        label="Bank"
    )
    
    date_from = forms.DateField(
        widget=forms.DateInput(attrs={
            "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
            "type": "date"
        }),
        label="From Date",
        required=True
    )
    
    date_to = forms.DateField(
        widget=forms.DateInput(attrs={
            "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
            "type": "date"
        }),
        label="To Date",
        required=True
    )
    
    series_filter = forms.ChoiceField(
        choices=[
            ('all', 'All Series'),
            ('active', 'Active Series Only'),
            ('exhausted', 'Exhausted Series Only'),
        ],
        required=False,
        widget=forms.Select(attrs={
            "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        }),
        label="Series Filter",
        initial='all'
    )
    
    usage_filter = forms.ChoiceField(
        choices=[
            ('all', 'All Cheques'),
            ('used', 'Used Cheques Only'),
            ('unused', 'Unused Cheques Only'),
        ],
        required=False,
        widget=forms.Select(attrs={
            "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        }),
        label="Usage Filter",
        initial='all'
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set default date range to current month
        today = date.today()
        first_day = today.replace(day=1)
        self.fields["date_from"].initial = first_day
        self.fields["date_to"].initial = today

    def clean(self):
        """Validate date range"""
        cleaned_data = super().clean()
        
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        
        if date_from and date_to:
            if date_from > date_to:
                raise ValidationError("From date must be before to date")
            
            # Check if date range is reasonable (not more than 1 year)
            if (date_to - date_from).days > 365:
                raise ValidationError("Date range cannot exceed 365 days")
        
        return cleaned_data


class ChequeBulkUpdateForm(forms.Form):
    """
    Form for bulk operations on cheque series
    """
    
    ACTION_CHOICES = [
        ('activate', 'Activate Series'),
        ('deactivate', 'Deactivate Series'),
        ('reset_current', 'Reset Current Cheque Number'),
        ('extend_series', 'Extend Series Range'),
    ]
    
    selected_series = forms.CharField(
        widget=forms.HiddenInput(),
        required=True
    )
    
    action = forms.ChoiceField(
        choices=ACTION_CHOICES,
        widget=forms.Select(attrs={
            "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        }),
        label="Action",
        required=True
    )
    
    new_current_cheque = forms.IntegerField(
        widget=forms.NumberInput(attrs={
            "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
            "placeholder": "New current cheque number",
            "min": "1"
        }),
        label="New Current Cheque Number",
        required=False,
        help_text="Required for 'Reset Current Cheque Number' action"
    )
    
    extend_to_number = forms.IntegerField(
        widget=forms.NumberInput(attrs={
            "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
            "placeholder": "Extend series to this number",
            "min": "1"
        }),
        label="Extend To Number",
        required=False,
        help_text="Required for 'Extend Series Range' action"
    )
    
    confirmation = forms.BooleanField(
        widget=forms.CheckboxInput(attrs={
            "class": "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        }),
        label="I confirm this bulk operation",
        required=True
    )

    def clean_selected_series(self):
        """Validate selected series"""
        selected = self.cleaned_data.get('selected_series', '')
        if not selected:
            raise ValidationError("No series selected")
        
        try:
            # Expect comma-separated IDs
            series_ids = [int(x.strip()) for x in selected.split(',') if x.strip()]
            if not series_ids:
                raise ValidationError("No valid series IDs provided")
            return series_ids
        except ValueError:
            raise ValidationError("Invalid series IDs format")

    def clean(self):
        """Validate form based on selected action"""
        cleaned_data = super().clean()
        action = cleaned_data.get('action')
        new_current_cheque = cleaned_data.get('new_current_cheque')
        extend_to_number = cleaned_data.get('extend_to_number')
        
        if action == 'reset_current' and not new_current_cheque:
            raise ValidationError("New current cheque number is required for reset action")
        
        if action == 'extend_series' and not extend_to_number:
            raise ValidationError("Extend to number is required for extend series action")
        
        return cleaned_data


class ChequeSeriesFilterForm(forms.Form):
    """
    Form for filtering cheque series list view
    """
    
    bank = forms.ModelChoiceField(
        queryset=Bank.objects.all(),
        required=False,
        empty_label="All Banks",
        widget=forms.Select(attrs={
            "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        }),
        label="Bank"
    )
    
    status = forms.ChoiceField(
        choices=[
            ('', 'All Statuses'),
            ('active', 'Active'),
            ('exhausted', 'Exhausted'),
            ('inactive', 'Inactive'),
        ],
        required=False,
        widget=forms.Select(attrs={
            "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        }),
        label="Status"
    )
    
    search = forms.CharField(
        widget=forms.TextInput(attrs={
            "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
            "placeholder": "Search by bank name, cheque numbers..."
        }),
        label="Search",
        required=False
    )


class QuickChequeSeriesForm(forms.ModelForm):
    """
    Simplified form for quick cheque series creation
    """
    
    series_size = forms.ChoiceField(
        choices=[
            ('25', '25 Cheques'),
            ('50', '50 Cheques'),
            ('100', '100 Cheques'),
            ('250', '250 Cheques'),
            ('500', '500 Cheques'),
        ],
        widget=forms.Select(attrs={
            "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"
        }),
        label="Series Size",
        initial='100'
    )
    
    class Meta:
        model = ChequeSeries
        fields = ["bank_id", "start_no"]
        widgets = {
            "bank_id": forms.NumberInput(attrs={
                "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                "placeholder": "Enter Bank ID",
                "min": "1",
            }),
            "start_no": forms.NumberInput(attrs={
                "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                "placeholder": "Starting cheque number",
                "min": "1",
            }),
        }
        labels = {
            "bank_id": "Bank ID",
            "start_no": "Starting Cheque Number",
        }

    def clean(self):
        """Calculate end_no based on series_size"""
        cleaned_data = super().clean()
        start_no = cleaned_data.get('start_no')
        series_size = cleaned_data.get('series_size')
        
        if start_no and series_size:
            size = int(series_size)
            end_no = start_no + size - 1
            cleaned_data['end_no'] = end_no
        
        return cleaned_data