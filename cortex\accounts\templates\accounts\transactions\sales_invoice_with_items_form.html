<!-- accounts/templates/accounts/transactions/sales_invoice_with_items_form.html -->
<!-- Enhanced Sales Invoice Form with Dynamic Item Grid and HTMX -->
<!-- Task Group 5: Sales & Service Tax Invoicing - Invoice Templates with HTMX (Task 5.14) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{{ page_title }} - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-blue-600 to-sap-blue-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">{{ page_title }}</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Comprehensive sales invoice management with dynamic item grids</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:sales_invoice_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
                {% if form_action == "Update" and sales_invoice %}
                <a href="{% url 'accounts:sales_invoice_detail' sales_invoice.pk %}" 
                   class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="eye" class="w-4 h-4 inline mr-2"></i>
                    View Invoice
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6" x-data="salesInvoiceManager()">
    
    <!-- Progress Indicator -->
    <div class="mb-8">
        <div class="flex items-center justify-between text-sm">
            <div class="flex items-center space-x-8">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-sap-blue-600 text-white rounded-full flex items-center justify-center mr-2">1</div>
                    <span class="font-medium text-sap-gray-800">Invoice Details</span>
                </div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-sap-blue-600 text-white rounded-full flex items-center justify-center mr-2">2</div>
                    <span class="font-medium text-sap-gray-800">Invoice Items</span>
                </div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-sap-gray-300 text-sap-gray-600 rounded-full flex items-center justify-center mr-2">3</div>
                    <span class="text-sap-gray-600">Review & Submit</span>
                </div>
            </div>
        </div>
        <div class="mt-4 w-full bg-sap-gray-200 rounded-full h-2">
            <div class="bg-sap-blue-600 h-2 rounded-full" style="width: 66%"></div>
        </div>
    </div>

    <form method="post" class="space-y-8" @submit="submitInvoice">
        {% csrf_token %}
        
        <!-- Error Messages -->
        {% if form.non_field_errors %}
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <i data-lucide="alert-circle" class="w-5 h-5 text-red-400 mr-3"></i>
                <div>
                    <h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                    <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                        {% for error in form.non_field_errors %}
                        <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
            
            <!-- Main Invoice Form -->
            <div class="xl:col-span-2 space-y-6">
                
                <!-- Invoice Header Information -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                            <i data-lucide="info" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                            Invoice Information
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Customer Name -->
                            <div>
                                <label for="{{ form.customer_name.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.customer_name.label }}
                                    <span class="text-red-500">*</span>
                                </label>
                                {{ form.customer_name|add_class:"block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                                {% if form.customer_name.errors %}
                                <p class="text-sm text-red-600 mt-1">{{ form.customer_name.errors.0 }}</p>
                                {% endif %}
                            </div>

                            <!-- Invoice Date -->
                            <div>
                                <label for="{{ form.invoice_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.invoice_date.label }}
                                    <span class="text-red-500">*</span>
                                </label>
                                {{ form.invoice_date|add_class:"block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                                {% if form.invoice_date.errors %}
                                <p class="text-sm text-red-600 mt-1">{{ form.invoice_date.errors.0 }}</p>
                                {% endif %}
                            </div>

                            <!-- PO Number -->
                            <div>
                                <label for="{{ form.po_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.po_no.label }}
                                </label>
                                {{ form.po_no|add_class:"block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            </div>

                            <!-- Work Order Number -->
                            <div>
                                <label for="{{ form.work_order_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.work_order_no.label }}
                                </label>
                                {{ form.work_order_no|add_class:"block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dynamic Invoice Items Grid -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                                <i data-lucide="package" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                                Invoice Items
                            </h3>
                            <button type="button" @click="addNewItem" 
                                    class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                                Add Item
                            </button>
                        </div>
                    </div>
                    
                    <div class="p-6">
                        <!-- Items Grid Header -->
                        <div class="overflow-x-auto">
                            <table class="min-w-full">
                                <thead>
                                    <tr class="bg-sap-gray-50">
                                        <th class="px-3 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Item Code</th>
                                        <th class="px-3 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Description</th>
                                        <th class="px-3 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">UOM</th>
                                        <th class="px-3 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Qty</th>
                                        <th class="px-3 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Rate</th>
                                        <th class="px-3 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Tax%</th>
                                        <th class="px-3 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Amount</th>
                                        <th class="px-3 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Line Total</th>
                                        <th class="px-3 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-sap-gray-200">
                                    <!-- Dynamic Item Rows -->
                                    <template x-for="(item, index) in invoiceItems" :key="index">
                                        <tr class="hover:bg-sap-gray-50">
                                            <td class="px-3 py-4">
                                                <input type="text" 
                                                       x-model="item.item_code"
                                                       @blur="validateItem(index)"
                                                       class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-blue-500"
                                                       placeholder="Item Code">
                                            </td>
                                            <td class="px-3 py-4">
                                                <input type="text" 
                                                       x-model="item.item_description"
                                                       class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-blue-500"
                                                       placeholder="Description">
                                            </td>
                                            <td class="px-3 py-4">
                                                <input type="text" 
                                                       x-model="item.uom"
                                                       class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-blue-500"
                                                       placeholder="UOM">
                                            </td>
                                            <td class="px-3 py-4">
                                                <input type="number" 
                                                       x-model.number="item.quantity"
                                                       @input="calculateLineTotal(index)"
                                                       step="0.001" min="0"
                                                       class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-blue-500"
                                                       placeholder="0.000">
                                            </td>
                                            <td class="px-3 py-4">
                                                <input type="number" 
                                                       x-model.number="item.rate"
                                                       @input="calculateLineTotal(index)"
                                                       step="0.01" min="0"
                                                       class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-blue-500"
                                                       placeholder="0.00">
                                            </td>
                                            <td class="px-3 py-4">
                                                <input type="number" 
                                                       x-model.number="item.tax_percentage"
                                                       @input="calculateLineTotal(index)"
                                                       step="0.01" min="0" max="100"
                                                       class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-blue-500"
                                                       placeholder="0.00">
                                            </td>
                                            <td class="px-3 py-4">
                                                <span class="text-sm font-medium text-sap-gray-900" x-text="formatCurrency(item.amount)"></span>
                                            </td>
                                            <td class="px-3 py-4">
                                                <span class="text-sm font-bold text-sap-blue-600" x-text="formatCurrency(item.line_total)"></span>
                                            </td>
                                            <td class="px-3 py-4 text-right">
                                                <button type="button" @click="removeItem(index)"
                                                        class="text-red-600 hover:text-red-700 p-1" title="Remove Item">
                                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </template>
                                    
                                    <!-- Empty State -->
                                    <tr x-show="invoiceItems.length === 0">
                                        <td colspan="9" class="px-6 py-8 text-center">
                                            <div class="text-center">
                                                <i data-lucide="package" class="w-12 h-12 text-sap-gray-400 mx-auto mb-4"></i>
                                                <h3 class="text-sm font-medium text-sap-gray-900 mb-2">No invoice items added</h3>
                                                <p class="text-sm text-sap-gray-600 mb-4">Get started by adding your first invoice item.</p>
                                                <button type="button" @click="addNewItem"
                                                        class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                                                    Add First Item
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Items Summary -->
                        <div x-show="invoiceItems.length > 0" class="mt-6 bg-sap-gray-50 rounded-lg p-4">
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                <div>
                                    <span class="text-sap-gray-600">Total Items:</span>
                                    <div class="font-medium" x-text="invoiceItems.length"></div>
                                </div>
                                <div>
                                    <span class="text-sap-gray-600">Basic Amount:</span>
                                    <div class="font-medium" x-text="formatCurrency(totals.basic_amount)"></div>
                                </div>
                                <div>
                                    <span class="text-sap-gray-600">Total Tax:</span>
                                    <div class="font-medium" x-text="formatCurrency(totals.total_tax)"></div>
                                </div>
                                <div>
                                    <span class="text-sap-gray-600">Subtotal:</span>
                                    <div class="font-bold text-sap-blue-600" x-text="formatCurrency(totals.subtotal)"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Invoice Totals and Additional Charges -->
            <div class="space-y-6">
                
                <!-- Invoice Summary -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                            <i data-lucide="calculator" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                            Invoice Summary
                        </h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <!-- Basic Amount -->
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-sap-gray-600">Basic Amount:</span>
                            <span class="font-medium" x-text="formatCurrency(totals.basic_amount)"></span>
                        </div>
                        
                        <!-- Total Tax -->
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-sap-gray-600">Total Tax:</span>
                            <span class="font-medium" x-text="formatCurrency(totals.total_tax)"></span>
                        </div>
                        
                        <!-- Subtotal -->
                        <div class="flex justify-between items-center border-t border-sap-gray-200 pt-4">
                            <span class="text-sm font-medium text-sap-gray-800">Subtotal:</span>
                            <span class="font-medium" x-text="formatCurrency(totals.subtotal)"></span>
                        </div>
                        
                        <!-- Additional Charges -->
                        <div class="space-y-3 border-t border-sap-gray-200 pt-4">
                            <div>
                                <label class="block text-xs font-medium text-sap-gray-700 mb-1">Freight</label>
                                <input type="number" x-model.number="additionalCharges.freight" @input="calculateGrandTotal"
                                       step="0.01" min="0" placeholder="0.00"
                                       class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-sap-gray-700 mb-1">Packing & Forwarding</label>
                                <input type="number" x-model.number="additionalCharges.packing" @input="calculateGrandTotal"
                                       step="0.01" min="0" placeholder="0.00"
                                       class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-sap-gray-700 mb-1">Other Charges</label>
                                <input type="number" x-model.number="additionalCharges.other" @input="calculateGrandTotal"
                                       step="0.01" placeholder="0.00"
                                       class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-sap-gray-700 mb-1">Round Off</label>
                                <input type="number" x-model.number="additionalCharges.roundOff" @input="calculateGrandTotal"
                                       step="0.01" placeholder="0.00"
                                       class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                            </div>
                        </div>
                        
                        <!-- Grand Total -->
                        <div class="border-t border-sap-gray-200 pt-4">
                            <div class="flex justify-between items-center bg-sap-green-50 rounded-lg p-3">
                                <span class="text-lg font-bold text-sap-gray-800">Grand Total:</span>
                                <span class="text-xl font-bold text-sap-green-600" x-text="formatCurrency(totals.grand_total)"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <h3 class="text-lg font-medium text-sap-gray-800">Quick Actions</h3>
                    </div>
                    <div class="p-6 space-y-3">
                        <button type="button" @click="loadTemplateItems"
                                class="w-full bg-sap-gray-100 hover:bg-sap-gray-200 text-sap-gray-800 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="template" class="w-4 h-4 inline mr-2"></i>
                            Load Template Items
                        </button>
                        <button type="button" @click="clearAllItems"
                                onclick="return confirm('Are you sure you want to clear all items?')"
                                class="w-full bg-red-100 hover:bg-red-200 text-red-800 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="trash-2" class="w-4 h-4 inline mr-2"></i>
                            Clear All Items
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
            <div class="flex items-center space-x-4">
                <a href="{% url 'accounts:sales_invoice_list' %}" 
                   class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-6 py-2 rounded-lg font-medium transition-colors">
                    Cancel
                </a>
                <button type="button" @click="saveDraft"
                        class="bg-sap-yellow-600 hover:bg-sap-yellow-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="save" class="w-4 h-4 inline mr-2"></i>
                    Save as Draft
                </button>
            </div>
            
            <div class="flex items-center space-x-3">
                <button type="button" @click="previewInvoice"
                        class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="eye" class="w-4 h-4 inline mr-2"></i>
                    Preview
                </button>
                <button type="submit" 
                        class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                        :disabled="invoiceItems.length === 0">
                    <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                    {{ form_action }} Invoice
                </button>
            </div>
        </div>

        <!-- Hidden Fields for Items Data -->
        <input type="hidden" name="items_data" :value="JSON.stringify(invoiceItems)">
        <input type="hidden" name="additional_charges" :value="JSON.stringify(additionalCharges)">
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
function salesInvoiceManager() {
    return {
        invoiceItems: [
            {% if existing_items %}
                {% for item in existing_items %}
                {
                    item_code: '{{ item.item_code|default:"" }}',
                    item_description: '{{ item.item_description|default:"" }}',
                    uom: '{{ item.uom|default:"" }}',
                    quantity: {{ item.quantity|default:0 }},
                    rate: {{ item.rate|default:0 }},
                    tax_percentage: {{ item.vat_percentage|default:0 }},
                    amount: {{ item.amount|default:0 }},
                    tax_amount: {{ item.vat_amount|default:0 }},
                    line_total: {{ item.line_total|default:0 }}
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
            {% endif %}
        ],
        
        additionalCharges: {
            freight: 0,
            packing: 0,
            other: 0,
            roundOff: 0
        },
        
        totals: {
            basic_amount: 0,
            total_tax: 0,
            subtotal: 0,
            grand_total: 0
        },
        
        init() {
            lucide.createIcons();
            this.calculateAllTotals();
            
            // Auto-save draft every 2 minutes
            setInterval(() => {
                this.autoSaveDraft();
            }, 120000);
        },
        
        addNewItem() {
            this.invoiceItems.push({
                item_code: '',
                item_description: '',
                uom: 'Nos',
                quantity: 1,
                rate: 0,
                tax_percentage: 0,
                amount: 0,
                tax_amount: 0,
                line_total: 0
            });
        },
        
        removeItem(index) {
            this.invoiceItems.splice(index, 1);
            this.calculateAllTotals();
        },
        
        calculateLineTotal(index) {
            const item = this.invoiceItems[index];
            
            // Calculate basic amount
            item.amount = (item.quantity || 0) * (item.rate || 0);
            
            // Calculate tax amount
            item.tax_amount = (item.amount * (item.tax_percentage || 0)) / 100;
            
            // Calculate line total
            item.line_total = item.amount + item.tax_amount;
            
            this.calculateAllTotals();
        },
        
        calculateAllTotals() {
            this.totals.basic_amount = this.invoiceItems.reduce((sum, item) => sum + (item.amount || 0), 0);
            this.totals.total_tax = this.invoiceItems.reduce((sum, item) => sum + (item.tax_amount || 0), 0);
            this.totals.subtotal = this.totals.basic_amount + this.totals.total_tax;
            this.calculateGrandTotal();
        },
        
        calculateGrandTotal() {
            this.totals.grand_total = this.totals.subtotal + 
                                    (this.additionalCharges.freight || 0) + 
                                    (this.additionalCharges.packing || 0) + 
                                    (this.additionalCharges.other || 0) + 
                                    (this.additionalCharges.roundOff || 0);
        },
        
        validateItem(index) {
            const item = this.invoiceItems[index];
            // Add validation logic here
            if (item.item_code && !item.item_description) {
                // Auto-populate description based on item code
                this.lookupItemDescription(index);
            }
        },
        
        lookupItemDescription(index) {
            // Implementation would make HTMX call to lookup item details
            console.log('Looking up item description for index:', index);
        },
        
        formatCurrency(amount) {
            return '₹' + (amount || 0).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        },
        
        loadTemplateItems() {
            // Implementation would load predefined item templates
            console.log('Loading template items...');
        },
        
        clearAllItems() {
            this.invoiceItems = [];
            this.calculateAllTotals();
        },
        
        saveDraft() {
            // Implementation would save as draft
            console.log('Saving draft...');
        },
        
        autoSaveDraft() {
            if (this.invoiceItems.length > 0) {
                console.log('Auto-saving draft...');
                // Implementation would auto-save
            }
        },
        
        previewInvoice() {
            // Implementation would show preview modal
            console.log('Previewing invoice...');
        },
        
        submitInvoice(event) {
            if (this.invoiceItems.length === 0) {
                event.preventDefault();
                alert('Please add at least one invoice item.');
                return false;
            }
            
            // Additional validation can go here
            return true;
        }
    }
}

// Initialize lucide icons on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}