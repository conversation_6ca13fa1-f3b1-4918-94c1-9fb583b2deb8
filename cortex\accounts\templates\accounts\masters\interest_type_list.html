<!-- accounts/templates/accounts/masters/interest_type_list.html -->
<!-- Interest Type Management List Template -->
<!-- Task Package 1: Master Data Templates - Interest Type Management -->

{% extends 'core/base.html' %}
{% load accounts_filters %}

{% block title %}Interest Types - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-blue-600 to-sap-blue-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="trending-up" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Interest Type Management</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Configure interest types, rates and calculation methods</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Accounts
                </a>
                <a href="{% url 'accounts:interest_type_create' %}" 
                   class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    Add Interest Type
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6" x-data="interestTypeManagement()">
    
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Total Interest Types</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ interest_types.count }}</p>
                </div>
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="trending-up" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Simple Interest</p>
                    <p class="text-2xl font-bold text-sap-green-600">{{ simple_count|default:0 }}</p>
                </div>
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="minus" class="w-6 h-6 text-sap-green-600"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Compound Interest</p>
                    <p class="text-2xl font-bold text-sap-orange-600">{{ compound_count|default:0 }}</p>
                </div>
                <div class="w-12 h-12 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="layers" class="w-6 h-6 text-sap-orange-600"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Average Rate</p>
                    <p class="text-2xl font-bold text-sap-blue-600">{{ average_rate|floatformat:2|default:"0.00" }}%</p>
                </div>
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="percent" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Search and Filters -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm mb-6">
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-sap-gray-700 mb-2">Search</label>
                    <input type="text" x-model="searchTerm" @input="filterTypes" id="search"
                           placeholder="Search by type, description..."
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                </div>
                <div>
                    <label for="rate_range" class="block text-sm font-medium text-sap-gray-700 mb-2">Rate Range</label>
                    <select x-model="rateRange" @change="filterTypes" id="rate_range"
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        <option value="">All Rates</option>
                        <option value="0-5">0% - 5%</option>
                        <option value="5-10">5% - 10%</option>
                        <option value="10-15">10% - 15%</option>
                        <option value="15-20">15% - 20%</option>
                        <option value="20+">20%+</option>
                    </select>
                </div>
                <div>
                    <label for="calculation_type" class="block text-sm font-medium text-sap-gray-700 mb-2">Calculation Method</label>
                    <select x-model="calculationFilter" @change="filterTypes" id="calculation_type"
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        <option value="">All Methods</option>
                        <option value="Simple">Simple Interest</option>
                        <option value="Compound">Compound Interest</option>
                        <option value="Floating">Floating Rate</option>
                        <option value="Fixed">Fixed Rate</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button @click="resetFilters" 
                            class="w-full bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="refresh-cw" class="w-4 h-4 inline mr-2"></i>
                        Reset
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Interest Types Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Interest Types</h3>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-sap-gray-600" x-text="`${filteredTypes} of ${allTypes} types`"></span>
                    <div class="flex items-center space-x-1">
                        <button @click="exportTypes" 
                                class="text-sap-gray-600 hover:text-sap-gray-800 p-1" title="Export Interest Types">
                            <i data-lucide="download" class="w-4 h-4"></i>
                        </button>
                        <button @click="refreshData" 
                                class="text-sap-gray-600 hover:text-sap-gray-800 p-1" title="Refresh Data">
                            <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        {% if interest_types %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            <button @click="sortBy('type')" class="flex items-center space-x-1">
                                <span>Interest Type</span>
                                <i data-lucide="chevron-up-down" class="w-3 h-3"></i>
                            </button>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            <button @click="sortBy('rate')" class="flex items-center space-x-1">
                                <span>Interest Rate</span>
                                <i data-lucide="chevron-up-down" class="w-3 h-3"></i>
                            </button>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Calculation Method</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Rate Validation</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Sample Interest</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for interest in interest_types %}
                    <tr class="hover:bg-sap-gray-50" x-show="isVisible({{ interest.id }})">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-sap-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i data-lucide="percent" class="w-5 h-5 text-sap-blue-600"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-sap-gray-900">{{ interest.interest_type|default:"Standard Interest" }}</div>
                                    <div class="text-xs text-sap-gray-500">{{ interest.description|truncatechars:30|default:"Interest calculation" }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <span class="text-2xl font-bold text-sap-blue-600">{{ interest.interest_percentage|floatformat:2|default:"10.00" }}%</span>
                                <span class="text-xs text-sap-gray-500 ml-2">per annum</span>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if interest.calculation_method == 'compound' or 'compound' in interest.interest_type|lower %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-orange-100 text-sap-orange-800">
                                <i data-lucide="layers" class="w-3 h-3 mr-1"></i>
                                Compound
                            </span>
                            {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-green-100 text-sap-green-800">
                                <i data-lucide="minus" class="w-3 h-3 mr-1"></i>
                                Simple
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if interest.interest_percentage <= 15 %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-green-100 text-sap-green-800">
                                <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                Valid
                            </span>
                            {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-yellow-100 text-sap-yellow-800">
                                <i data-lucide="alert-circle" class="w-3 h-3 mr-1"></i>
                                Review
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-600">
                            <div>
                                <div>₹1,00,000 → <span class="font-medium text-sap-blue-600">₹{{ interest.interest_percentage|mul:1000|floatformat:0|default:"10000" }}</span></div>
                                <div class="text-xs text-sap-gray-500">Annual interest</div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <button @click="calculateInterest({{ interest.interest_percentage|default:10 }}, '{{ interest.interest_type|default:"Interest" }}')" 
                                        class="text-sap-green-600 hover:text-sap-green-700 p-1" title="Calculate Interest">
                                    <i data-lucide="calculator" class="w-4 h-4"></i>
                                </button>
                                <button @click="viewFormula('{{ interest.interest_type|default:"Interest" }}', '{{ interest.calculation_method|default:"simple" }}')" 
                                        class="text-sap-purple-600 hover:text-sap-purple-700 p-1" title="View Formula">
                                    <i data-lucide="formula" class="w-4 h-4"></i>
                                </button>
                                <a href="{% url 'accounts:interest_type_edit' interest.id %}" 
                                   class="text-sap-blue-600 hover:text-sap-blue-700 p-1" title="Edit">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </a>
                                <a href="{% url 'accounts:interest_type_delete' interest.id %}" 
                                   class="text-red-600 hover:text-red-700 p-1" title="Delete"
                                   onclick="return confirm('Are you sure you want to delete this interest type?')">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="w-24 h-24 bg-sap-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i data-lucide="trending-up" class="w-12 h-12 text-sap-blue-600"></i>
            </div>
            <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No Interest Types Configured</h3>
            <p class="text-sap-gray-600 mb-6">Get started by adding your first interest type for financial calculations.</p>
            <a href="{% url 'accounts:interest_type_create' %}" 
               class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                Add First Interest Type
            </a>
        </div>
        {% endif %}
    </div>
    
    <!-- Interest Calculator Modal -->
    <div x-show="showCalculator" x-transition 
         class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
         @click.self="showCalculator = false">
        <div class="bg-white rounded-lg p-6 w-full max-w-lg mx-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-sap-gray-800" x-text="calculatorTitle"></h3>
                <button @click="showCalculator = false" class="text-sap-gray-400 hover:text-sap-gray-600">
                    <i data-lucide="x" class="w-5 h-5"></i>
                </button>
            </div>
            
            <div class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-2">Principal Amount (₹)</label>
                        <input type="number" x-model="calculatorPrincipal" step="0.01" min="0"
                               class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:ring-sap-blue-500 focus:border-sap-blue-500"
                               placeholder="100000">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-2">Time Period (years)</label>
                        <input type="number" x-model="calculatorYears" step="0.1" min="0"
                               class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:ring-sap-blue-500 focus:border-sap-blue-500"
                               placeholder="1">
                    </div>
                </div>
                
                <div x-show="calculatorResult" class="bg-sap-blue-50 rounded-lg p-4">
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-sap-gray-600">Principal:</span>
                            <div class="font-medium" x-text="'₹' + parseFloat(calculatorPrincipal || 0).toFixed(2)"></div>
                        </div>
                        <div>
                            <span class="text-sap-gray-600">Interest:</span>
                            <div class="font-medium text-sap-blue-600" x-text="'₹' + interestAmount"></div>
                        </div>
                        <div>
                            <span class="text-sap-gray-600">Total Amount:</span>
                            <div class="font-bold text-sap-gray-800" x-text="'₹' + totalAmount"></div>
                        </div>
                        <div>
                            <span class="text-sap-gray-600">Interest Rate:</span>
                            <div class="font-medium text-sap-blue-600" x-text="calculatorRate + '% p.a.'"></div>
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center justify-end space-x-3">
                    <button @click="showCalculator = false" 
                            class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                        Close
                    </button>
                    <button @click="performCalculation" 
                            class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        Calculate
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Formula Info Modal -->
    <div x-show="showFormula" x-transition 
         class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
         @click.self="showFormula = false">
        <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-sap-gray-800" x-text="formulaTitle"></h3>
                <button @click="showFormula = false" class="text-sap-gray-400 hover:text-sap-gray-600">
                    <i data-lucide="x" class="w-5 h-5"></i>
                </button>
            </div>
            
            <div class="space-y-4">
                <div class="bg-sap-blue-50 rounded-lg p-4">
                    <div class="text-center">
                        <div class="text-lg font-mono text-sap-blue-800 mb-2" x-text="formulaText"></div>
                        <div class="text-sm text-sap-gray-600">
                            <div>P = Principal Amount</div>
                            <div>R = Rate of Interest</div>
                            <div>T = Time Period</div>
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center justify-end">
                    <button @click="showFormula = false" 
                            class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function interestTypeManagement() {
    return {
        searchTerm: '',
        rateRange: '',
        calculationFilter: '',
        sortField: '',
        sortDirection: 'asc',
        allTypes: 0,
        filteredTypes: 0,
        showCalculator: false,
        showFormula: false,
        calculatorTitle: '',
        formulaTitle: '',
        formulaText: '',
        calculatorRate: 0,
        calculatorPrincipal: 100000,
        calculatorYears: 1,
        calculatorResult: false,
        interestAmount: 0,
        totalAmount: 0,
        
        init() {
            lucide.createIcons();
            this.allTypes = document.querySelectorAll('tbody tr').length;
            this.filteredTypes = this.allTypes;
        },
        
        filterTypes() {
            const rows = document.querySelectorAll('tbody tr');
            let visibleCount = 0;
            
            rows.forEach(row => {
                const type = row.querySelector('td:first-child').textContent.toLowerCase();
                const rate = parseFloat(row.querySelector('td:nth-child(2)').textContent);
                const method = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
                
                let visible = true;
                
                // Search filter
                if (this.searchTerm && !type.includes(this.searchTerm.toLowerCase())) {
                    visible = false;
                }
                
                // Rate range filter
                if (this.rateRange) {
                    const [min, max] = this.rateRange.split('-').map(v => v === '+' ? 100 : parseFloat(v));
                    if (this.rateRange.includes('+')) {
                        visible = visible && rate >= min;
                    } else {
                        visible = visible && rate >= min && rate <= max;
                    }
                }
                
                // Calculation method filter
                if (this.calculationFilter && !method.includes(this.calculationFilter.toLowerCase())) {
                    visible = false;
                }
                
                row.style.display = visible ? '' : 'none';
                if (visible) visibleCount++;
            });
            
            this.filteredTypes = visibleCount;
        },
        
        resetFilters() {
            this.searchTerm = '';
            this.rateRange = '';
            this.calculationFilter = '';
            this.filterTypes();
        },
        
        sortBy(field) {
            console.log('Sorting by:', field);
        },
        
        isVisible(id) {
            return true;
        },
        
        calculateInterest(rate, type) {
            this.calculatorRate = rate;
            this.calculatorTitle = `Interest Calculator - ${type} (${rate}%)`;
            this.calculatorPrincipal = 100000;
            this.calculatorYears = 1;
            this.calculatorResult = false;
            this.showCalculator = true;
        },
        
        performCalculation() {
            const principal = parseFloat(this.calculatorPrincipal) || 0;
            const years = parseFloat(this.calculatorYears) || 0;
            const interest = (principal * this.calculatorRate * years / 100);
            
            this.interestAmount = interest.toFixed(2);
            this.totalAmount = (principal + interest).toFixed(2);
            this.calculatorResult = true;
        },
        
        viewFormula(type, method) {
            this.formulaTitle = `${type} - ${method.charAt(0).toUpperCase() + method.slice(1)} Interest Formula`;
            if (method.toLowerCase().includes('compound')) {
                this.formulaText = 'A = P(1 + R/100)^T';
            } else {
                this.formulaText = 'SI = (P × R × T) / 100';
            }
            this.showFormula = true;
        },
        
        exportTypes() {
            console.log('Exporting interest types...');
        },
        
        refreshData() {
            window.location.reload();
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}