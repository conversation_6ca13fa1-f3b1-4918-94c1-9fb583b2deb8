{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }} - Sales Distribution{% endblock %}

{% block extra_css %}
<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/umd/lucide.min.css">
<script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-sap-gray-50 font-sap">
    <!-- SAP Fiori Header -->
    <div class="bg-white shadow-sm border-b border-sap-gray-200">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-orange-600 rounded-lg flex items-center justify-center">
                            <i data-lucide="toggle-left" class="w-5 h-5 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-xl font-semibold text-sap-gray-900">{{ page_title }}</h1>
                            <p class="text-sm text-sap-gray-600">Change work order status between open and closed</p>
                        </div>
                    </div>
                </div>
                <nav class="flex items-center space-x-2 text-sm text-sap-gray-500">
                    <a href="{% url 'sales_distribution:dashboard' %}" class="hover:text-sap-blue-600 transition-colors">Sales & Distribution</a>
                    <span class="text-sap-gray-400">/</span>
                    <a href="{% url 'sales_distribution:work_order_open_close_list' %}" class="hover:text-sap-blue-600 transition-colors">Work Order Open/Close</a>
                    <span class="text-sap-gray-400">/</span>
                    <span class="text-sap-gray-900">{{ work_order.wono|default:"Work Order" }}</span>
                </nav>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-6 py-8">
        <!-- Work Order Information Card -->
        <div class="bg-white rounded-xl shadow-sm border border-sap-gray-200 mb-8">
            <div class="px-6 py-4 border-b border-sap-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-lg font-semibold text-sap-gray-900">Work Order Information</h2>
                    <div class="flex items-center space-x-2">
                        {% if work_order.closeopen == 0 %}
                        <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i data-lucide="unlock" class="w-3 h-3 mr-1"></i>
                            Open
                        </span>
                        {% else %}
                        <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            <i data-lucide="lock" class="w-3 h-3 mr-1"></i>
                            Closed
                        </span>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Basic Information -->
                    <div class="space-y-4">
                        <h3 class="text-sm font-semibold text-sap-gray-900 uppercase tracking-wide">Basic Information</h3>
                        
                        <div>
                            <label class="block text-xs font-medium text-sap-gray-600 uppercase tracking-wide">Work Order No</label>
                            <div class="mt-1 text-sm text-sap-gray-900 font-medium">{{ work_order.wono|default:"-" }}</div>
                        </div>
                        
                        <div>
                            <label class="block text-xs font-medium text-sap-gray-600 uppercase tracking-wide">Customer ID</label>
                            <div class="mt-1 text-sm text-sap-gray-900">{{ work_order.customerid|default:"-" }}</div>
                        </div>
                        
                        <div>
                            <label class="block text-xs font-medium text-sap-gray-600 uppercase tracking-wide">Work Order Date</label>
                            <div class="mt-1 text-sm text-sap-gray-900">{{ work_order.taskworkorderdate|default:"-" }}</div>
                        </div>
                    </div>
                    
                    <!-- Project Information -->
                    <div class="space-y-4">
                        <h3 class="text-sm font-semibold text-sap-gray-900 uppercase tracking-wide">Project Information</h3>
                        
                        <div>
                            <label class="block text-xs font-medium text-sap-gray-600 uppercase tracking-wide">Project Title</label>
                            <div class="mt-1 text-sm text-sap-gray-900">{{ work_order.taskprojecttitle|default:"-" }}</div>
                        </div>
                        
                        <div>
                            <label class="block text-xs font-medium text-sap-gray-600 uppercase tracking-wide">Project Leader</label>
                            <div class="mt-1 text-sm text-sap-gray-900">{{ work_order.taskprojectleader|default:"-" }}</div>
                        </div>
                        
                        <div>
                            <label class="block text-xs font-medium text-sap-gray-600 uppercase tracking-wide">Category</label>
                            <div class="mt-1 text-sm text-sap-gray-900">{{ work_order.cid.cname|default:"-" }}</div>
                        </div>
                    </div>
                    
                    <!-- Status Information -->
                    <div class="space-y-4">
                        <h3 class="text-sm font-semibold text-sap-gray-900 uppercase tracking-wide">Status Information</h3>
                        
                        <div>
                            <label class="block text-xs font-medium text-sap-gray-600 uppercase tracking-wide">Current Status</label>
                            <div class="mt-1">
                                {% if work_order.closeopen == 0 %}
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i data-lucide="unlock" class="w-3 h-3 mr-1"></i>
                                    Open - Work in Progress
                                </span>
                                {% else %}
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i data-lucide="lock" class="w-3 h-3 mr-1"></i>
                                    Closed - Completed
                                </span>
                                {% endif %}
                            </div>
                        </div>
                        
                        {% if work_order.enqid %}
                        <div>
                            <label class="block text-xs font-medium text-sap-gray-600 uppercase tracking-wide">Enquiry ID</label>
                            <div class="mt-1 text-sm text-sap-gray-900">{{ work_order.enqid.enqid|default:"-" }}</div>
                        </div>
                        {% endif %}
                        
                        {% if work_order.poid %}
                        <div>
                            <label class="block text-xs font-medium text-sap-gray-600 uppercase tracking-wide">PO No</label>
                            <div class="mt-1 text-sm text-sap-gray-900">{{ work_order.pono|default:"-" }}</div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Card -->
        <div class="bg-white rounded-xl shadow-sm border border-sap-gray-200" x-data="{ showConfirm: false, action: '' }">
            <div class="px-6 py-4 border-b border-sap-gray-200">
                <h2 class="text-lg font-semibold text-sap-gray-900">Change Work Order Status</h2>
                <p class="text-sm text-sap-gray-600 mt-1">Select an action to change the status of this work order</p>
            </div>
            
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Open Work Order -->
                    <div class="relative">
                        {% if work_order.closeopen == 0 %}
                        <div class="opacity-50 pointer-events-none">
                        {% endif %}
                            <div class="border-2 border-green-200 rounded-xl p-6 bg-green-50 hover:bg-green-100 transition-colors cursor-pointer"
                                 @click="action = 'open'; showConfirm = true">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-green-600 rounded-xl flex items-center justify-center">
                                        <i data-lucide="unlock" class="w-6 h-6 text-white"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-green-900">Open Work Order</h3>
                                        <p class="text-sm text-green-700">Mark work order as active/in progress</p>
                                    </div>
                                </div>
                            </div>
                        {% if work_order.closeopen == 0 %}
                            <div class="absolute inset-0 flex items-center justify-center">
                                <span class="bg-white px-3 py-1 rounded-full text-sm font-medium text-gray-600 shadow-md">
                                    Already Open
                                </span>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- Close Work Order -->
                    <div class="relative">
                        {% if work_order.closeopen != 0 %}
                        <div class="opacity-50 pointer-events-none">
                        {% endif %}
                            <div class="border-2 border-red-200 rounded-xl p-6 bg-red-50 hover:bg-red-100 transition-colors cursor-pointer"
                                 @click="action = 'close'; showConfirm = true">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-red-600 rounded-xl flex items-center justify-center">
                                        <i data-lucide="lock" class="w-6 h-6 text-white"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-red-900">Close Work Order</h3>
                                        <p class="text-sm text-red-700">Mark work order as completed/closed</p>
                                    </div>
                                </div>
                            </div>
                        {% if work_order.closeopen != 0 %}
                            <div class="absolute inset-0 flex items-center justify-center">
                                <span class="bg-white px-3 py-1 rounded-full text-sm font-medium text-gray-600 shadow-md">
                                    Already Closed
                                </span>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Confirmation Modal -->
                <div x-show="showConfirm" x-transition class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
                    <div class="bg-white rounded-xl shadow-xl max-w-md w-full mx-4">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900" x-text="action === 'open' ? 'Open Work Order' : 'Close Work Order'"></h3>
                        </div>
                        <div class="px-6 py-4">
                            <p class="text-gray-600" x-text="action === 'open' ? 'Are you sure you want to open this work order? This will mark it as active and in progress.' : 'Are you sure you want to close this work order? This will mark it as completed.'"></p>
                        </div>
                        <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                            <button @click="showConfirm = false" 
                                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                                Cancel
                            </button>
                            <form method="post" class="inline">
                                {% csrf_token %}
                                <input type="hidden" name="action" :value="action">
                                <button type="submit" 
                                        class="px-4 py-2 text-sm font-medium text-white rounded-lg transition-colors"
                                        :class="action === 'open' ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'"
                                        x-text="action === 'open' ? 'Open Work Order' : 'Close Work Order'">
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Back Button -->
        <div class="mt-8 flex justify-end">
            <a href="{% url 'sales_distribution:work_order_open_close_list' %}" 
               class="inline-flex items-center px-4 py-2 border border-sap-gray-300 text-sm font-medium rounded-lg text-sap-gray-700 bg-white hover:bg-sap-gray-50 transition-colors">
                <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                Back to Work Order List
            </a>
        </div>
    </div>
</div>

<script>
    // Initialize Lucide icons
    document.addEventListener('DOMContentLoaded', function() {
        lucide.createIcons();
    });
</script>
{% endblock %}