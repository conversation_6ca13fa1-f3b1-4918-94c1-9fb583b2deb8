﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="Stock_Ledger" targetNamespace="http://tempuri.org/Stock_Ledger.xsd" xmlns:mstns="http://tempuri.org/Stock_Ledger.xsd" xmlns="http://tempuri.org/Stock_Ledger.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="Stock_Ledger" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="Stock_Ledger" msprop:Generator_DataSetName="Stock_Ledger">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="SysDate" msprop:Generator_UserColumnName="SysDate" msprop:Generator_ColumnVarNameInTable="columnSysDate" msprop:Generator_ColumnPropNameInRow="SysDate" msprop:Generator_ColumnPropNameInTable="SysDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="SysTime" msprop:Generator_UserColumnName="SysTime" msprop:Generator_ColumnVarNameInTable="columnSysTime" msprop:Generator_ColumnPropNameInRow="SysTime" msprop:Generator_ColumnPropNameInTable="SysTimeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="EmpName" msprop:Generator_UserColumnName="EmpName" msprop:Generator_ColumnVarNameInTable="columnEmpName" msprop:Generator_ColumnPropNameInRow="EmpName" msprop:Generator_ColumnPropNameInTable="EmpNameColumn" type="xs:string" minOccurs="0" />
              <xs:element name="AcceptedQty" msprop:Generator_UserColumnName="AcceptedQty" msprop:Generator_ColumnVarNameInTable="columnAcceptedQty" msprop:Generator_ColumnPropNameInRow="AcceptedQty" msprop:Generator_ColumnPropNameInTable="AcceptedQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Dept" msprop:Generator_UserColumnName="Dept" msprop:Generator_ColumnVarNameInTable="columnDept" msprop:Generator_ColumnPropNameInRow="Dept" msprop:Generator_ColumnPropNameInTable="DeptColumn" type="xs:string" minOccurs="0" />
              <xs:element name="WONo" msprop:Generator_UserColumnName="WONo" msprop:Generator_ColumnVarNameInTable="columnWONo" msprop:Generator_ColumnPropNameInRow="WONo" msprop:Generator_ColumnPropNameInTable="WONoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="IssueQty" msprop:Generator_UserColumnName="IssueQty" msprop:Generator_ColumnVarNameInTable="columnIssueQty" msprop:Generator_ColumnPropNameInRow="IssueQty" msprop:Generator_ColumnPropNameInTable="IssueQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="for" msprop:Generator_UserColumnName="for" msprop:Generator_ColumnVarNameInTable="columnfor" msprop:Generator_ColumnPropNameInRow="_for" msprop:Generator_ColumnPropNameInTable="forColumn" type="xs:string" minOccurs="0" />
              <xs:element name="to" msprop:Generator_UserColumnName="to" msprop:Generator_ColumnVarNameInTable="columnto" msprop:Generator_ColumnPropNameInRow="to" msprop:Generator_ColumnPropNameInTable="toColumn" type="xs:string" minOccurs="0" />
              <xs:element name="EName" msprop:Generator_UserColumnName="EName" msprop:Generator_ColumnVarNameInTable="columnEName" msprop:Generator_ColumnPropNameInRow="EName" msprop:Generator_ColumnPropNameInTable="ENameColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Id" msprop:Generator_UserColumnName="Id" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnPropNameInTable="IdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="seconds" msprop:Generator_UserColumnName="seconds" msprop:Generator_ColumnPropNameInRow="seconds" msprop:Generator_ColumnVarNameInTable="columnseconds" msprop:Generator_ColumnPropNameInTable="secondsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="SortDateTime" msprop:Generator_UserColumnName="SortDateTime" msprop:Generator_ColumnVarNameInTable="columnSortDateTime" msprop:Generator_ColumnPropNameInRow="SortDateTime" msprop:Generator_ColumnPropNameInTable="SortDateTimeColumn" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>