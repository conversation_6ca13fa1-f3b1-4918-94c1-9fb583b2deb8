from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from ..models import MaterialRequisitionSlip, MRSLineItem, MRSApprovalHistory
from design.models import Item, Category


class MaterialRequisitionSlipForm(forms.ModelForm):
    """Form for creating and editing Material Requisition Slips"""
    
    mrs_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md'
        })
    )
    
    required_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md'
        })
    )
    
    requisition_type = forms.ChoiceField(
        choices=[
            ('', 'Select Type'),
            ('PRODUCTION', 'Production'),
            ('MAINTENANCE', 'Maintenance'),
            ('PROJECT', 'Project'),
            ('OFFICE', 'Office')
        ],
        widget=forms.Select(attrs={
            'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )
    
    priority = forms.ChoiceField(
        choices=[
            ('', 'Select Priority'),
            ('LOW', 'Low'),
            ('NORMAL', 'Normal'),
            ('HIGH', 'High'),
            ('URGENT', 'Urgent')
        ],
        widget=forms.Select(attrs={
            'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )
    
    department_id = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
            'placeholder': 'Department ID'
        })
    )
    
    work_order_number = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
            'placeholder': 'Work Order Number'
        })
    )
    
    project_code = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
            'placeholder': 'Project Code'
        })
    )
    
    remarks = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'rows': 3,
            'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md',
            'placeholder': 'Additional remarks or notes'
        })
    )
    
    # Hidden field for line items JSON data (for table-based form)
    line_items_data = forms.CharField(
        required=False,
        widget=forms.HiddenInput()
    )
    
    class Meta:
        model = MaterialRequisitionSlip
        fields = [
            'sys_date', 'sys_time', 'company', 'financial_year', 
            'session_id', 'mrs_number'
        ]
        widgets = {
            'sys_date': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'YYYY-MM-DD'
            }),
            'sys_time': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'HH:MM:SS AM/PM'
            }),
            'session_id': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Employee Session ID (e.g., Sapl0001)'
            }),
            'mrs_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'MRS Number (e.g., 0001)'
            })
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.company = kwargs.pop('company', None)
        self.financial_year = kwargs.pop('financial_year', None)
        super().__init__(*args, **kwargs)
        
        # Set default values
        if not self.instance.pk:
            now = timezone.now()
            self.fields['sys_date'].initial = now.strftime('%Y-%m-%d')
            self.fields['sys_time'].initial = now.strftime('%I:%M:%S %p')
            if self.company:
                self.fields['company'].initial = self.company
            if self.financial_year:
                self.fields['financial_year'].initial = self.financial_year

    def clean_sys_date(self):
        sys_date = self.cleaned_data.get('sys_date')
        if sys_date:
            try:
                # Validate date format
                timezone.datetime.strptime(sys_date, '%Y-%m-%d')
            except ValueError:
                raise ValidationError("Date must be in YYYY-MM-DD format.")
        return sys_date

    def clean_session_id(self):
        session_id = self.cleaned_data.get('session_id')
        if session_id and not session_id.strip():
            raise ValidationError("Session ID is required.")
        return session_id

    def save(self, commit=True):
        instance = super().save(commit=False)
        
        if not instance.pk:
            # Set system fields for new instances
            if self.company:
                instance.company = self.company
            if self.financial_year:
                instance.financial_year = self.financial_year
            
        if commit:
            instance.save()
        
        return instance


class MRSLineItemForm(forms.ModelForm):
    """Form for MRS line items with advanced item selection"""
    
    # Item selection fields
    category_filter = forms.ModelChoiceField(
        queryset=Category.objects.all(),
        required=False,
        empty_label="All Categories",
        widget=forms.Select(attrs={
            'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/inventory/api/items/search/',
            'hx-trigger': 'change',
            'hx-target': '#item-search-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    item_search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
            'placeholder': 'Search by item code or description (min 2 chars)',
            'hx-get': '/inventory/api/items/search/',
            'hx-trigger': 'keyup changed delay:300ms',
            'hx-target': '#item-search-results',
            'hx-swap': 'innerHTML',
            'hx-include': '[name="category_filter"]',
            'autocomplete': 'off'
        })
    )
    
    # Selected item display (hidden fields for form submission)
    selected_item_id = forms.CharField(
        widget=forms.HiddenInput()
    )
    
    selected_item_code = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md bg-gray-50',
            'readonly': True,
            'placeholder': 'Selected item code will appear here'
        })
    )
    
    selected_item_description = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md bg-gray-50',
            'readonly': True,
            'placeholder': 'Selected item description will appear here'
        })
    )
    
    available_stock = forms.DecimalField(
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md bg-gray-50',
            'readonly': True,
            'placeholder': 'Available stock'
        })
    )
    
    class Meta:
        model = MRSLineItem
        fields = [
            'mrs', 'mrs_number', 'item_id', 
            'department_id', 'work_order_number', 'requested_quantity', 'remarks'
        ]
        widgets = {
            'mrs': forms.HiddenInput(),  # This will be set programmatically
            'item_id': forms.HiddenInput(),  # This will be populated by selected_item_id
            'mrs_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'MRS Number'
            }),
            'department_id': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Department ID'
            }),
            'work_order_number': forms.TextInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': 'Work Order Number'
            }),
            'requested_quantity': forms.NumberInput(attrs={
                'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0.01'
            }),
            'remarks': forms.Textarea(attrs={
                'rows': 2,
                'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md',
                'placeholder': 'Remarks or additional information'
            })
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set category choices
        self.fields['category_filter'].queryset = Category.objects.filter(
            compid__isnull=False
        ).order_by('cname')
        
        # If editing existing line item, populate selected item fields
        if self.instance.pk and self.instance.item_id:
            try:
                item = Item.objects.get(id=self.instance.item_id)
                self.fields['selected_item_id'].initial = item.id
                self.fields['selected_item_code'].initial = item.itemcode
                self.fields['selected_item_description'].initial = item.manfdesc
                self.fields['available_stock'].initial = item.stockqty
            except Item.DoesNotExist:
                pass

    def clean_requested_quantity(self):
        quantity = self.cleaned_data.get('requested_quantity')
        selected_item_id = self.cleaned_data.get('selected_item_id')
        
        if quantity is not None and quantity <= 0:
            raise ValidationError("Requested quantity must be greater than zero.")
        
        # Check stock availability
        if quantity and selected_item_id:
            try:
                item = Item.objects.get(id=selected_item_id)
                if quantity > item.stockqty:
                    raise ValidationError(
                        f"Requested quantity ({quantity}) exceeds available stock ({item.stockqty}). "
                        f"Available stock for {item.itemcode}: {item.stockqty}"
                    )
            except Item.DoesNotExist:
                raise ValidationError("Selected item not found. Please select a valid item.")
        
        return quantity

    def clean_selected_item_id(self):
        selected_item_id = self.cleaned_data.get('selected_item_id')
        if not selected_item_id:
            raise ValidationError("Please select an item from the search results.")
        
        try:
            item = Item.objects.get(id=selected_item_id)
            return selected_item_id
        except Item.DoesNotExist:
            raise ValidationError("Selected item not found. Please select a valid item.")
    
    def clean(self):
        cleaned_data = super().clean()
        selected_item_id = cleaned_data.get('selected_item_id')
        
        # Set item_id from selected_item_id
        if selected_item_id:
            cleaned_data['item_id'] = selected_item_id
        
        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        
        # Ensure item_id is set from selected_item_id
        if self.cleaned_data.get('selected_item_id'):
            instance.item_id = self.cleaned_data['selected_item_id']
        
        if commit:
            instance.save()
        
        return instance


class MRSApprovalForm(forms.ModelForm):
    """Form for MRS approval workflow"""
    
    action = forms.ChoiceField(
        choices=[('APPROVED', 'Approve'), ('REJECTED', 'Reject')],
        widget=forms.RadioSelect(attrs={
            'class': 'focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300'
        })
    )
    
    class Meta:
        model = MRSApprovalHistory
        fields = ['action', 'comments']
        widgets = {
            'comments': forms.Textarea(attrs={
                'rows': 3,
                'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md',
                'placeholder': 'Enter approval comments or reasons for rejection'
            })
        }

    def __init__(self, *args, **kwargs):
        self.mrs = kwargs.pop('mrs', None)
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

    def clean(self):
        cleaned_data = super().clean()
        action = cleaned_data.get('action')
        comments = cleaned_data.get('comments')
        
        if action == 'REJECTED' and not comments:
            raise ValidationError("Comments are required when rejecting an MRS.")
        
        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        
        if not instance.pk:
            instance.mrs = self.mrs
            instance.approver = self.user
            
        if commit:
            instance.save()
            
            # Update MRS status using model methods
            if instance.action == 'APPROVED':
                self.mrs.approve(self.user, instance.comments)
            elif instance.action == 'REJECTED':
                self.mrs.reject(self.user, instance.comments or 'Rejected during approval process')
        
        return instance


class MRSSearchForm(forms.Form):
    """Search form for Material Requisition Slips"""
    
    search = forms.CharField(
        required=False,
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md',
            'placeholder': 'Search by MRS number, work order, or project code...',
            'hx-get': '/inventory/mrs/',
            'hx-trigger': 'keyup changed delay:300ms',
            'hx-target': '#mrs-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    status = forms.ChoiceField(
        required=False,
        choices=[('', 'All Status'), ('ACTIVE', 'Active'), ('INACTIVE', 'Inactive')],
        widget=forms.Select(attrs={
            'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/inventory/mrs/',
            'hx-trigger': 'change',
            'hx-target': '#mrs-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    requisition_type = forms.ChoiceField(
        required=False,
        choices=[('', 'All Types'), ('PRODUCTION', 'Production'), ('MAINTENANCE', 'Maintenance'), ('OFFICE', 'Office')],
        widget=forms.Select(attrs={
            'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/inventory/mrs/',
            'hx-trigger': 'change',
            'hx-target': '#mrs-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    priority = forms.ChoiceField(
        required=False,
        choices=[('', 'All Priorities'), ('LOW', 'Low'), ('NORMAL', 'Normal'), ('HIGH', 'High'), ('URGENT', 'Urgent')],
        widget=forms.Select(attrs={
            'class': 'mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/inventory/mrs/',
            'hx-trigger': 'change',
            'hx-target': '#mrs-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
            'hx-get': '/inventory/mrs/',
            'hx-trigger': 'change',
            'hx-target': '#mrs-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md',
            'hx-get': '/inventory/mrs/',
            'hx-trigger': 'change',
            'hx-target': '#mrs-results',
            'hx-swap': 'innerHTML'
        })
    )