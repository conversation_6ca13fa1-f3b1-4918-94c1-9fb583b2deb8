<!-- accounts/templates/accounts/vouchers/bank_voucher_form.html -->
<!-- Bank Voucher Create/Edit Form Template -->
<!-- Task Group 2: Banking & Cash Management - Bank Voucher Form (Task 2.4) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{% if object %}Edit Bank Voucher{% else %}New Bank Voucher{% endif %} - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-blue-600 to-sap-blue-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="building-2" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">
                        {% if object %}Edit Bank Voucher{% else %}New Bank Voucher{% endif %}
                    </h1>
                    <p class="text-sm text-sap-gray-600 mt-1">
                        {% if object %}Modify bank transaction details{% else %}Record a new bank payment or receipt{% endif %}
                    </p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:bank_voucher_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Bank Voucher Form -->
    <div class="max-w-6xl mx-auto">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="edit" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                    Bank Voucher Information
                </h3>
                <p class="text-sm text-sap-gray-600 mt-1">Complete all required fields to record the bank transaction</p>
            </div>
            
            <form method="post" id="bank-voucher-form" class="p-6" x-data="bankVoucherForm()">
                {% csrf_token %}
                
                <!-- Voucher Header Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="info" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Voucher Header
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <!-- Voucher Number -->
                        <div>
                            <label for="{{ form.voucher_number.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Voucher Number
                            </label>
                            {{ form.voucher_number|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.voucher_number.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.voucher_number.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Auto-generated if left blank</p>
                        </div>
                        
                        <!-- Voucher Date -->
                        <div>
                            <label for="{{ form.voucher_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Voucher Date *
                            </label>
                            {{ form.voucher_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.voucher_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.voucher_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Transaction Type -->
                        <div>
                            <label for="{{ form.transaction_type.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Transaction Type *
                            </label>
                            {{ form.transaction_type|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.transaction_type.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.transaction_type.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Bank Account -->
                        <div>
                            <label for="{{ form.bank_account.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Bank Account *
                            </label>
                            {{ form.bank_account|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.bank_account.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.bank_account.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Transaction Details Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="dollar-sign" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                        Transaction Details
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Account Head -->
                        <div>
                            <label for="{{ form.account_head.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Account Head *
                            </label>
                            {{ form.account_head|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.account_head.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.account_head.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Amount -->
                        <div>
                            <label for="{{ form.amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Amount (₹) *
                            </label>
                            {{ form.amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Description -->
                        <div class="md:col-span-2">
                            <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Description *
                            </label>
                            {{ form.description|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.description.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.description.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Detailed description of the bank transaction</p>
                        </div>
                    </div>
                </div>
                
                <!-- Cheque/Instrument Details Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="credit-card" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                        Cheque/Instrument Details
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Cheque Number -->
                        <div>
                            <label for="{{ form.cheque_number.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Cheque Number
                            </label>
                            {{ form.cheque_number|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.cheque_number.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.cheque_number.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Cheque Date -->
                        <div>
                            <label for="{{ form.cheque_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Cheque Date
                            </label>
                            {{ form.cheque_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.cheque_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.cheque_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Instrument Type -->
                        <div>
                            <label for="{{ form.instrument_type.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Instrument Type
                            </label>
                            {{ form.instrument_type|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.instrument_type.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.instrument_type.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Payee Name -->
                        <div>
                            <label for="{{ form.payee_name.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Payee Name
                            </label>
                            {{ form.payee_name|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.payee_name.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.payee_name.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Drawn On Bank -->
                        <div>
                            <label for="{{ form.drawn_on_bank.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Drawn On Bank
                            </label>
                            {{ form.drawn_on_bank|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.drawn_on_bank.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.drawn_on_bank.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Clearing Status -->
                        <div>
                            <label for="{{ form.clearing_status.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Clearing Status
                            </label>
                            {{ form.clearing_status|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.clearing_status.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.clearing_status.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Party/Vendor Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="user" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                        Party/Vendor Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Party Name -->
                        <div>
                            <label for="{{ form.party_name.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Party Name
                            </label>
                            {{ form.party_name|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.party_name.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.party_name.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Party Type -->
                        <div>
                            <label for="{{ form.party_type.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Party Type
                            </label>
                            {{ form.party_type|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.party_type.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.party_type.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Bill/Invoice Number -->
                        <div>
                            <label for="{{ form.bill_number.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Bill/Invoice Number
                            </label>
                            {{ form.bill_number|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.bill_number.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.bill_number.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Additional Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="file-text" class="w-5 h-5 mr-2 text-sap-gray-600"></i>
                        Additional Information
                    </h4>
                    <div class="grid grid-cols-1 gap-6">
                        <!-- Remarks -->
                        <div>
                            <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Remarks
                            </label>
                            {{ form.remarks|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.remarks.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.remarks.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Additional notes for this bank voucher</p>
                        </div>
                    </div>
                </div>
                
                <!-- Transaction Summary Display -->
                <div class="mb-8 bg-sap-blue-50 border border-sap-blue-200 rounded-lg p-6" x-show="amount > 0">
                    <h4 class="text-lg font-medium text-sap-blue-800 mb-4 flex items-center">
                        <i data-lucide="calculator" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Transaction Summary
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="text-center">
                            <p class="text-sm text-sap-blue-600">Transaction Type</p>
                            <p class="text-xl font-bold text-sap-blue-800" x-text="transactionType"></p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-blue-600">Amount</p>
                            <p class="text-xl font-bold text-sap-blue-800" x-text="'₹' + amount.toFixed(2)"></p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-blue-600">Bank Account</p>
                            <p class="text-xl font-bold text-sap-blue-800" x-text="bankAccount"></p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-blue-600">Cheque No.</p>
                            <p class="text-xl font-bold text-sap-blue-800" x-text="chequeNumber || 'N/A'"></p>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="resetForm()" 
                                class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="rotate-ccw" class="w-4 h-4 inline mr-2"></i>
                            Reset
                        </button>
                        <button type="button" onclick="previewVoucher()" 
                                class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="eye" class="w-4 h-4 inline mr-2"></i>
                            Preview
                        </button>
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="{% url 'accounts:bank_voucher_list' %}" 
                           class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                            {% if object %}Update Voucher{% else %}Save Voucher{% endif %}
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Bank Voucher Guidelines -->
        <div class="mt-6 bg-sap-amber-50 border border-sap-amber-200 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i data-lucide="info" class="w-5 h-5 text-sap-amber-600"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-sap-amber-800">Bank Voucher Guidelines</h3>
                    <div class="mt-2 text-sm text-sap-amber-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Voucher number will be auto-generated if not provided</li>
                            <li>Ensure all cheque details are accurate for payment vouchers</li>
                            <li>Bank account must be properly configured in the system</li>
                            <li>Include relevant party and bill information for tracking</li>
                            <li>Monitor clearing status for cheque transactions</li>
                            <li>Maintain proper documentation for bank reconciliation</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function bankVoucherForm() {
    return {
        amount: 0,
        transactionType: '',
        bankAccount: '',
        chequeNumber: '',
        
        init() {
            this.updateValues();
        },
        
        updateValues() {
            const amountField = document.getElementById('{{ form.amount.id_for_label }}');
            const typeField = document.getElementById('{{ form.transaction_type.id_for_label }}');
            const accountField = document.getElementById('{{ form.bank_account.id_for_label }}');
            const chequeField = document.getElementById('{{ form.cheque_number.id_for_label }}');
            
            if (amountField) this.amount = parseFloat(amountField.value) || 0;
            if (typeField) this.transactionType = typeField.options[typeField.selectedIndex]?.text || '';
            if (accountField) this.bankAccount = accountField.options[accountField.selectedIndex]?.text || '';
            if (chequeField) this.chequeNumber = chequeField.value || '';
        }
    }
}

function resetForm() {
    if (confirm('Are you sure you want to reset the form? All unsaved changes will be lost.')) {
        document.getElementById('bank-voucher-form').reset();
    }
}

function previewVoucher() {
    alert('Voucher preview functionality would be implemented here.');
}

// Auto-generate voucher number if creating new voucher
document.addEventListener('DOMContentLoaded', function() {
    const voucherNumberInput = document.getElementById('{{ form.voucher_number.id_for_label }}');
    if (voucherNumberInput && !voucherNumberInput.value) {
        // Generate voucher number based on current timestamp
        const today = new Date();
        const year = today.getFullYear().toString().substr(-2);
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        const sequence = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        
        voucherNumberInput.value = `BV-${year}${month}${day}${sequence}`;
    }
    
    // Auto-set cheque date to voucher date if not provided
    const voucherDateField = document.getElementById('{{ form.voucher_date.id_for_label }}');
    const chequeDateField = document.getElementById('{{ form.cheque_date.id_for_label }}');
    
    if (voucherDateField && chequeDateField) {
        voucherDateField.addEventListener('change', function() {
            if (!chequeDateField.value) {
                chequeDateField.value = this.value;
            }
        });
    }
    
    // Auto-update summary display
    const amountField = document.getElementById('{{ form.amount.id_for_label }}');
    const typeField = document.getElementById('{{ form.transaction_type.id_for_label }}');
    const accountField = document.getElementById('{{ form.bank_account.id_for_label }}');
    const chequeField = document.getElementById('{{ form.cheque_number.id_for_label }}');
    
    [amountField, typeField, accountField, chequeField].forEach(field => {
        if (field) {
            field.addEventListener('change', function() {
                // Trigger Alpine.js update
                const alpineData = Alpine.$data(document.querySelector('[x-data="bankVoucherForm()"]'));
                if (alpineData) {
                    alpineData.updateValues();
                }
            });
        }
    });
    
    lucide.createIcons();
});
</script>
{% endblock %}