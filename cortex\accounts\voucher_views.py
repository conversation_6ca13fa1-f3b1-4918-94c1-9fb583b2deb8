# accounts/voucher_views.py
# Django views for Voucher processing in Accounts module
# Task Group 2: Banking & Cash Management - Voucher Views

from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.views.generic import ListView, CreateView, UpdateView
from django.urls import reverse_lazy
from django.http import JsonResponse
from django.db.models import Q, F
from datetime import date

from .models import (
    ChequeSeries, CashVoucherMaster, BankVoucherMaster, ContraEntryMaster, 
    BankReconciliationMaster, Bank, AccountHead, TDSCode
)
from .voucher_forms import (
    CashVoucherForm, BankVoucherForm, ContraEntryForm, BankReconciliationForm
)
from .cheque_forms import ChequeSeriesForm


# Cheque Series Management Views

class ChequeSeriesListView(LoginRequiredMixin, ListView):
    """
    List view for Cheque Series
    Replaces ASP.NET Cheque_series.aspx functionality
    """
    model = ChequeSeries
    template_name = 'accounts/cheque_series_list.html'
    context_object_name = 'cheque_series'
    paginate_by = 20

    def get_queryset(self):
        queryset = ChequeSeries.objects.select_related('bank').order_by('-created_date')
        
        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(bank__name__icontains=search) |
                Q(cheque_from__icontains=search) |
                Q(cheque_to__icontains=search)
            )
        
        return queryset


class ChequeSeriesCreateView(LoginRequiredMixin, CreateView):
    """
    Create view for Cheque Series
    """
    model = ChequeSeries
    form_class = ChequeSeriesForm
    template_name = 'accounts/cheque_series_form.html'
    success_url = reverse_lazy('accounts:cheque_series_list')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        messages.success(self.request, 'Cheque series created successfully.')
        return super().form_valid(form)


class ChequeSeriesUpdateView(LoginRequiredMixin, UpdateView):
    """
    Update view for Cheque Series
    """
    model = ChequeSeries
    form_class = ChequeSeriesForm
    template_name = 'accounts/cheque_series_form.html'
    success_url = reverse_lazy('accounts:cheque_series_list')

    def form_valid(self, form):
        messages.success(self.request, 'Cheque series updated successfully.')
        return super().form_valid(form)


# Cash Voucher Management Views

class CashVoucherListView(LoginRequiredMixin, ListView):
    """
    List view for Cash Vouchers
    Replaces ASP.NET CashVoucher_New.aspx functionality
    """
    model = CashVoucherMaster
    template_name = 'accounts/cash_voucher_list.html'
    context_object_name = 'cash_vouchers'
    paginate_by = 20

    def get_queryset(self):
        queryset = CashVoucherMaster.objects.select_related(
            'account_head', 'tds_code', 'company', 'financial_year'
        ).order_by('-created_date')
        
        # Filter by voucher type
        voucher_type = self.request.GET.get('voucher_type')
        if voucher_type:
            queryset = queryset.filter(voucher_type=voucher_type)
        
        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        
        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(voucher_no__icontains=search) |
                Q(pay_to_name__icontains=search) |
                Q(particulars__icontains=search)
            )
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['voucher_types'] = [
            ('payment', 'Payment'),
            ('receipt', 'Receipt'),
        ]
        context['statuses'] = [
            ('draft', 'Draft'),
            ('submitted', 'Submitted'),
            ('approved', 'Approved'),
            ('cancelled', 'Cancelled'),
        ]
        return context


class CashVoucherCreateView(LoginRequiredMixin, CreateView):
    """
    Create view for Cash Vouchers
    """
    model = CashVoucherMaster
    form_class = CashVoucherForm
    template_name = 'accounts/cash_voucher_form.html'
    success_url = reverse_lazy('accounts:cash_voucher_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = 'Create Cash Voucher'
        return context

    def form_valid(self, form):
        # Auto-generate voucher number
        last_voucher = CashVoucherMaster.objects.filter(
            voucher_type=form.cleaned_data['voucher_type']
        ).order_by('-id').first()
        
        if last_voucher and last_voucher.voucher_no:
            try:
                last_num = int(last_voucher.voucher_no.split('-')[-1])
                new_num = last_num + 1
            except (ValueError, IndexError):
                new_num = 1
        else:
            new_num = 1
        
        voucher_prefix = 'CV' if form.cleaned_data['voucher_type'] == 'payment' else 'CR'
        form.instance.voucher_no = f"{voucher_prefix}-{new_num:04d}"
        form.instance.voucher_date = date.today()
        form.instance.created_by = self.request.user
        form.instance.company_id = self.request.session.get('company_id', 1)
        form.instance.financial_year_id = self.request.session.get('financial_year_id', 1)
        
        # Set calculated amounts from form
        form.instance.tds_amount = form.cleaned_data.get('tds_amount', 0)
        form.instance.net_amount = form.cleaned_data.get('net_amount', 0)
        
        messages.success(self.request, f'Cash voucher {form.instance.voucher_no} created successfully.')
        return super().form_valid(form)


class CashVoucherUpdateView(LoginRequiredMixin, UpdateView):
    """
    Update view for Cash Vouchers
    """
    model = CashVoucherMaster
    form_class = CashVoucherForm
    template_name = 'accounts/cash_voucher_form.html'
    success_url = reverse_lazy('accounts:cash_voucher_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = f'Edit Cash Voucher - {self.object.voucher_no}'
        return context

    def form_valid(self, form):
        form.instance.updated_by = self.request.user
        
        # Set calculated amounts from form
        form.instance.tds_amount = form.cleaned_data.get('tds_amount', 0)
        form.instance.net_amount = form.cleaned_data.get('net_amount', 0)
        
        messages.success(self.request, f'Cash voucher {form.instance.voucher_no} updated successfully.')
        return super().form_valid(form)


# Bank Voucher Management Views

class BankVoucherListView(LoginRequiredMixin, ListView):
    """
    List view for Bank Vouchers
    Replaces ASP.NET BankVoucher.aspx functionality
    """
    model = BankVoucherMaster
    template_name = 'accounts/bank_voucher_list.html'
    context_object_name = 'bank_vouchers'
    paginate_by = 20

    def get_queryset(self):
        queryset = BankVoucherMaster.objects.select_related(
            'bank', 'account_head', 'tds_code', 'company', 'financial_year'
        ).order_by('-created_date')
        
        # Filter by voucher type
        voucher_type = self.request.GET.get('voucher_type')
        if voucher_type:
            queryset = queryset.filter(voucher_type=voucher_type)
        
        # Filter by bank
        bank_id = self.request.GET.get('bank')
        if bank_id:
            queryset = queryset.filter(bank_id=bank_id)
        
        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        
        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(voucher_no__icontains=search) |
                Q(pay_to_name__icontains=search) |
                Q(cheque_no__icontains=search) |
                Q(particulars__icontains=search)
            )
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['banks'] = Bank.objects.all()
        context['voucher_types'] = [
            ('payment', 'Payment'),
            ('receipt', 'Receipt'),
        ]
        context['statuses'] = [
            ('draft', 'Draft'),
            ('submitted', 'Submitted'),
            ('approved', 'Approved'),
            ('cancelled', 'Cancelled'),
        ]
        return context


class BankVoucherCreateView(LoginRequiredMixin, CreateView):
    """
    Create view for Bank Vouchers
    """
    model = BankVoucherMaster
    form_class = BankVoucherForm
    template_name = 'accounts/bank_voucher_form.html'
    success_url = reverse_lazy('accounts:bank_voucher_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = 'Create Bank Voucher'
        return context

    def form_valid(self, form):
        # Auto-generate voucher number
        last_voucher = BankVoucherMaster.objects.filter(
            voucher_type=form.cleaned_data['voucher_type']
        ).order_by('-id').first()
        
        if last_voucher and last_voucher.voucher_no:
            try:
                last_num = int(last_voucher.voucher_no.split('-')[-1])
                new_num = last_num + 1
            except (ValueError, IndexError):
                new_num = 1
        else:
            new_num = 1
        
        voucher_prefix = 'BV' if form.cleaned_data['voucher_type'] == 'payment' else 'BR'
        form.instance.voucher_no = f"{voucher_prefix}-{new_num:04d}"
        form.instance.voucher_date = date.today()
        form.instance.created_by = self.request.user
        form.instance.company_id = self.request.session.get('company_id', 1)
        form.instance.financial_year_id = self.request.session.get('financial_year_id', 1)
        
        # Set calculated amounts from form
        form.instance.tds_amount = form.cleaned_data.get('tds_amount', 0)
        form.instance.net_amount = form.cleaned_data.get('net_amount', 0)
        
        messages.success(self.request, f'Bank voucher {form.instance.voucher_no} created successfully.')
        return super().form_valid(form)


class BankVoucherUpdateView(LoginRequiredMixin, UpdateView):
    """
    Update view for Bank Vouchers
    """
    model = BankVoucherMaster
    form_class = BankVoucherForm
    template_name = 'accounts/bank_voucher_form.html'
    success_url = reverse_lazy('accounts:bank_voucher_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = f'Edit Bank Voucher - {self.object.voucher_no}'
        return context

    def form_valid(self, form):
        form.instance.updated_by = self.request.user
        
        # Set calculated amounts from form
        form.instance.tds_amount = form.cleaned_data.get('tds_amount', 0)
        form.instance.net_amount = form.cleaned_data.get('net_amount', 0)
        
        messages.success(self.request, f'Bank voucher {form.instance.voucher_no} updated successfully.')
        return super().form_valid(form)


# Contra Entry Management Views

class ContraEntryListView(LoginRequiredMixin, ListView):
    """
    List view for Contra Entries
    Replaces ASP.NET ContraEntry.aspx functionality
    """
    model = ContraEntryMaster
    template_name = 'accounts/contra_entry_list.html'
    context_object_name = 'contra_entries'
    paginate_by = 20

    def get_queryset(self):
        queryset = ContraEntryMaster.objects.select_related(
            'from_bank', 'to_bank', 'company', 'financial_year'
        ).order_by('-created_date')
        
        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        
        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(contra_no__icontains=search) |
                Q(from_bank__name__icontains=search) |
                Q(to_bank__name__icontains=search) |
                Q(cheque_no__icontains=search)
            )
        
        return queryset


class ContraEntryCreateView(LoginRequiredMixin, CreateView):
    """
    Create view for Contra Entries
    """
    model = ContraEntryMaster
    form_class = ContraEntryForm
    template_name = 'accounts/contra_entry_form.html'
    success_url = reverse_lazy('accounts:contra_entry_list')

    def form_valid(self, form):
        # Auto-generate contra number
        last_contra = ContraEntryMaster.objects.order_by('-id').first()
        
        if last_contra and last_contra.contra_no:
            try:
                last_num = int(last_contra.contra_no.split('-')[-1])
                new_num = last_num + 1
            except (ValueError, IndexError):
                new_num = 1
        else:
            new_num = 1
        
        form.instance.contra_no = f"CE-{new_num:04d}"
        form.instance.contra_date = date.today()
        form.instance.created_by = self.request.user
        form.instance.company_id = self.request.session.get('company_id', 1)
        form.instance.financial_year_id = self.request.session.get('financial_year_id', 1)
        
        messages.success(self.request, f'Contra entry {form.instance.contra_no} created successfully.')
        return super().form_valid(form)


# Bank Reconciliation Management Views

class BankReconciliationListView(LoginRequiredMixin, ListView):
    """
    List view for Bank Reconciliations
    Replaces ASP.NET BankReconciliation_New.aspx functionality
    """
    model = BankReconciliationMaster
    template_name = 'accounts/bank_reconciliation_list.html'
    context_object_name = 'reconciliations'
    paginate_by = 20

    def get_queryset(self):
        queryset = BankReconciliationMaster.objects.select_related(
            'bank', 'company', 'financial_year'
        ).order_by('-created_date')
        
        # Filter by bank
        bank_id = self.request.GET.get('bank')
        if bank_id:
            queryset = queryset.filter(bank_id=bank_id)
        
        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['banks'] = Bank.objects.all()
        context['statuses'] = [
            ('in_progress', 'In Progress'),
            ('completed', 'Completed'),
            ('pending', 'Pending'),
        ]
        return context


class BankReconciliationCreateView(LoginRequiredMixin, CreateView):
    """
    Create view for Bank Reconciliations
    """
    model = BankReconciliationMaster
    form_class = BankReconciliationForm
    template_name = 'accounts/bank_reconciliation_form.html'
    success_url = reverse_lazy('accounts:bank_reconciliation_list')

    def form_valid(self, form):
        # Auto-generate reconciliation number
        last_reconciliation = BankReconciliationMaster.objects.order_by('-id').first()
        
        if last_reconciliation and last_reconciliation.reconciliation_no:
            try:
                last_num = int(last_reconciliation.reconciliation_no.split('-')[-1])
                new_num = last_num + 1
            except (ValueError, IndexError):
                new_num = 1
        else:
            new_num = 1
        
        form.instance.reconciliation_no = f"BR-{new_num:04d}"
        form.instance.reconciliation_date = date.today()
        form.instance.created_by = self.request.user
        form.instance.company_id = self.request.session.get('company_id', 1)
        form.instance.financial_year_id = self.request.session.get('financial_year_id', 1)
        
        # Set calculated amounts from form
        form.instance.difference_amount = form.cleaned_data.get('difference_amount', 0)
        form.instance.reconciled_amount = form.cleaned_data.get('reconciled_amount', 0)
        form.instance.outstanding_amount = form.cleaned_data.get('outstanding_amount', 0)
        
        messages.success(self.request, f'Bank reconciliation {form.instance.reconciliation_no} created successfully.')
        return super().form_valid(form)


# AJAX Views for Dynamic Loading

def get_next_cheque_number(request):
    """
    AJAX view to get next available cheque number for a bank
    """
    bank_id = request.GET.get('bank_id')
    if not bank_id:
        return JsonResponse({'error': 'Bank ID required'}, status=400)
    
    try:
        cheque_series = ChequeSeries.objects.filter(
            bank_id=bank_id, 
            is_active=True,
            current_cheque_no__lt=F('cheque_to')
        ).first()
        
        if cheque_series:
            next_cheque = cheque_series.current_cheque_no + 1 if cheque_series.current_cheque_no else cheque_series.cheque_from
            return JsonResponse({'next_cheque': next_cheque})
        else:
            return JsonResponse({'error': 'No active cheque series found for this bank'}, status=404)
    
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


def load_account_heads(request):
    """
    AJAX view to load account heads by category
    """
    category = request.GET.get('category')
    account_heads = AccountHead.objects.filter(category=category).values('id', 'description', 'symbol')
    return JsonResponse({'account_heads': list(account_heads)})


def load_tds_codes(request):
    """
    AJAX view to load TDS codes
    """
    tds_codes = TDSCode.objects.values('id', 'tds_code', 'tds_percentage', 'description')
    return JsonResponse({'tds_codes': list(tds_codes)})