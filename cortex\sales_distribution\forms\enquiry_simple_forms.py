# sales_distribution/forms/enquiry_simple_forms.py
# Simple Customer-Like Enquiry Form - matches customer form layout exactly
# Only differences: enqid and enquiry_for fields

from django import forms
from ..models import Enquiry
from sys_admin.models import Country, State, City


class SimpleEnquiryForm(forms.ModelForm):
    """
    Simple Customer-like Enquiry Form
    Exactly matches CustomerForm layout with minimal differences
    Only enqid and enquiry_for are different from customer form
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Initialize Country choices for all three address sections
        country_choices = [("", "Select Country")] + [
            (c.cid, c.countryname) for c in Country.objects.all().order_by("countryname")
        ]

        self.fields["regdcountry"].choices = country_choices
        self.fields["workcountry"].choices = country_choices
        self.fields["materialdelcountry"].choices = country_choices

        # If editing an existing enquiry, populate state and city choices based on selected countries
        if self.instance and self.instance.pk:
            # Registered address states and cities
            if self.instance.regdcountry:
                reg_states = State.objects.filter(cid=self.instance.regdcountry).order_by("statename")
                self.fields["regdstate"].choices = [("", "Select State")] + [
                    (s.sid, s.statename) for s in reg_states
                ]

                if self.instance.regdstate:
                    reg_cities = City.objects.filter(sid=self.instance.regdstate).order_by("cityname")
                    self.fields["regdcity"].choices = [("", "Select City")] + [
                        (c.cityid, c.cityname) for c in reg_cities
                    ]
                else:
                    self.fields["regdcity"].choices = [("", "Select City")]
            else:
                self.fields["regdstate"].choices = [("", "Select State")]
                self.fields["regdcity"].choices = [("", "Select City")]

            # Works address states and cities
            if self.instance.workcountry:
                work_states = State.objects.filter(cid=self.instance.workcountry).order_by("statename")
                self.fields["workstate"].choices = [("", "Select State")] + [
                    (s.sid, s.statename) for s in work_states
                ]

                if self.instance.workstate:
                    work_cities = City.objects.filter(sid=self.instance.workstate).order_by("cityname")
                    self.fields["workcity"].choices = [("", "Select City")] + [
                        (c.cityid, c.cityname) for c in work_cities
                    ]
                else:
                    self.fields["workcity"].choices = [("", "Select City")]
            else:
                self.fields["workstate"].choices = [("", "Select State")]
                self.fields["workcity"].choices = [("", "Select City")]

            # Material address states and cities
            if self.instance.materialdelcountry:
                mat_states = State.objects.filter(cid=self.instance.materialdelcountry).order_by("statename")
                self.fields["materialdelstate"].choices = [("", "Select State")] + [
                    (s.sid, s.statename) for s in mat_states
                ]

                if self.instance.materialdelstate:
                    mat_cities = City.objects.filter(sid=self.instance.materialdelstate).order_by("cityname")
                    self.fields["materialdelcity"].choices = [("", "Select City")] + [
                        (c.cityid, c.cityname) for c in mat_cities
                    ]
                else:
                    self.fields["materialdelcity"].choices = [("", "Select City")]
            else:
                self.fields["materialdelstate"].choices = [("", "Select State")]
                self.fields["materialdelcity"].choices = [("", "Select City")]
        else:
            # For new enquiries, initialize empty choices (will be populated via HTMX)
            empty_choices = [("", "Select State")]
            self.fields["regdstate"].choices = empty_choices
            self.fields["workstate"].choices = empty_choices
            self.fields["materialdelstate"].choices = empty_choices

            empty_city_choices = [("", "Select City")]
            self.fields["regdcity"].choices = empty_city_choices
            self.fields["workcity"].choices = empty_city_choices
            self.fields["materialdelcity"].choices = empty_city_choices

    class Meta:
        model = Enquiry
        fields = [
            "customername",
            "regdaddress",
            "regdcountry",
            "regdstate",
            "regdcity",
            "regdpinno",
            "regdcontactno",
            "workaddress",
            "workcountry",
            "workstate",
            "workcity",
            "workpinno",
            "workcontactno",
            "materialdeladdress",
            "materialdelcountry",
            "materialdelstate",
            "materialdelcity",
            "materialdelpinno",
            "materialdelcontactno",
            "contactperson",
            "email",
            "contactno",
            "enquiryfor",  # This replaces TIN/CST field in customer form
            "remark",
            # Hidden fields for data preservation
            "customerid",
            "regdfaxno",
            "workfaxno",
            "materialdelfaxno",
            "juridictioncode",
            "commissionurate",
            "tinvatno",
            "eccno",
            "divn",
            "tincstno",
            "range",
            "panno",
            "tdscode",
        ]
        widgets = {
            # Customer Name (exactly like customer form)
            "customername": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "placeholder": "Customer's Name",
                    "required": True,
                }
            ),
            # Registered Office Section (exactly like customer form)
            "regdaddress": forms.Textarea(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "rows": 3,
                    "placeholder": "Registered Office Address",
                    "required": True,
                }
            ),
            "regdcountry": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "hx-get": "/sales-distribution/ajax/get-states/",
                    "hx-target": "#id_regdstate",
                    "hx-trigger": "change",
                    "hx-include": '[name="regdcountry"]',
                }
            ),
            "regdstate": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "hx-get": "/sales-distribution/ajax/get-cities/",
                    "hx-target": "#id_regdcity",
                    "hx-trigger": "change",
                    "hx-include": '[name="regdstate"]',
                }
            ),
            "regdcity": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"
                }
            ),
            "regdpinno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "placeholder": "PIN Code",
                }
            ),
            "regdcontactno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "placeholder": "Contact Number",
                }
            ),
            # Works/Factory Section (exactly like customer form)
            "workaddress": forms.Textarea(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "rows": 3,
                    "placeholder": "Works/Factory Address",
                }
            ),
            "workcountry": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "hx-get": "/sales-distribution/ajax/get-states/",
                    "hx-target": "#id_workstate",
                    "hx-trigger": "change",
                    "hx-include": '[name="workcountry"]',
                }
            ),
            "workstate": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "hx-get": "/sales-distribution/ajax/get-cities/",
                    "hx-target": "#id_workcity",
                    "hx-trigger": "change",
                    "hx-include": '[name="workstate"]',
                }
            ),
            "workcity": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"
                }
            ),
            "workpinno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "placeholder": "PIN Code",
                }
            ),
            "workcontactno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "placeholder": "Contact Number",
                }
            ),
            # Material Delivery Section (exactly like customer form)
            "materialdeladdress": forms.Textarea(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "rows": 3,
                    "placeholder": "Material Delivery Address",
                }
            ),
            "materialdelcountry": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "hx-get": "/sales-distribution/ajax/get-states/",
                    "hx-target": "#id_materialdelstate",
                    "hx-trigger": "change",
                    "hx-include": '[name="materialdelcountry"]',
                }
            ),
            "materialdelstate": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "hx-get": "/sales-distribution/ajax/get-cities/",
                    "hx-target": "#id_materialdelcity",
                    "hx-trigger": "change",
                    "hx-include": '[name="materialdelstate"]',
                }
            ),
            "materialdelcity": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"
                }
            ),
            "materialdelpinno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "placeholder": "PIN Code",
                }
            ),
            "materialdelcontactno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "placeholder": "Contact Number",
                }
            ),
            # Additional Details Section (exactly like customer form)
            "contactperson": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "placeholder": "Contact Person",
                }
            ),
            "email": forms.EmailInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "placeholder": "Email Address",
                }
            ),
            "contactno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "placeholder": "Contact Number",
                }
            ),
            # This is the KEY DIFFERENCE: enquiry_for instead of TIN/CST
            "enquiryfor": forms.Textarea(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "placeholder": "Enquiry Description",
                    "rows": 2,
                    "required": True,
                }
            ),
            "remark": forms.Textarea(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "rows": 2,
                    "placeholder": "Remarks",
                }
            ),
            # Hidden fields - minimal styling needed since they're hidden
            "customerid": forms.HiddenInput(),
            "regdfaxno": forms.HiddenInput(),
            "workfaxno": forms.HiddenInput(),
            "materialdelfaxno": forms.HiddenInput(),
            "juridictioncode": forms.HiddenInput(),
            "commissionurate": forms.HiddenInput(),
            "tinvatno": forms.HiddenInput(),
            "eccno": forms.HiddenInput(),
            "divn": forms.HiddenInput(),
            "tincstno": forms.HiddenInput(),
            "range": forms.HiddenInput(),
            "panno": forms.HiddenInput(),
            "tdscode": forms.HiddenInput(),
        }

    def clean_email(self):
        """Email validation matching customer form"""
        email = self.cleaned_data.get("email")
        if email:
            import re
            pattern = r"\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*"
            if not re.match(pattern, email):
                raise forms.ValidationError("Please enter a valid email address.")
        return email

    def clean_customername(self):
        """Customer name is required"""
        name = self.cleaned_data.get("customername")
        if not name or not name.strip():
            raise forms.ValidationError("Customer name is required.")
        return name.upper()  # Convert to uppercase like customer form

    def clean_enquiryfor(self):
        """Enquiry description is required"""
        enquiry = self.cleaned_data.get("enquiryfor")
        if not enquiry or not enquiry.strip():
            raise forms.ValidationError("Enquiry description is required.")
        return enquiry.strip()
