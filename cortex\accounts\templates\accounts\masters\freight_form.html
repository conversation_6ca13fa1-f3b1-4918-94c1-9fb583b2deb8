<!-- accounts/templates/accounts/masters/freight_form.html -->
<!-- Freight Terms Form Template -->
<!-- Simple freight terms form matching actual database structure -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{{ page_title }} - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-blue-600 to-sap-blue-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="truck" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">{{ page_title }}</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">{% if form_action == 'Create' %}Add a new freight term{% else %}Update freight term details{% endif %}</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:freight_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    <div class="max-w-2xl mx-auto">
        <!-- Form Card -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800">Freight Term Details</h3>
                <p class="text-sm text-sap-gray-600 mt-1">Enter the freight terms and conditions for shipments</p>
            </div>
            
            <form method="post" class="p-6">
                {% csrf_token %}
                
                {% if form.non_field_errors %}
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i data-lucide="alert-circle" class="h-5 w-5 text-red-400"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                                <div class="mt-2 text-sm text-red-700">
                                    {{ form.non_field_errors }}
                                </div>
                            </div>
                        </div>
                    </div>
                {% endif %}

                <div class="space-y-6">
                    <!-- Freight Terms Field -->
                    <div>
                        <label for="{{ form.terms.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Freight Terms <span class="text-red-500">*</span>
                        </label>
                        {% render_field form.terms class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" %}
                        {% if form.terms.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.terms.errors.0 }}
                            </div>
                        {% endif %}
                        <p class="mt-1 text-xs text-sap-gray-500">
                            Enter freight terms and conditions (e.g., "F.O.B.", "Ex Works", "At Actual")
                        </p>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="mt-8 flex items-center justify-end space-x-3 pt-6 border-t border-sap-gray-100">
                    <a href="{% url 'accounts:freight_list' %}" 
                       class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-6 py-2 rounded-lg font-medium transition-colors">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="save" class="w-4 h-4 inline mr-2"></i>
                        {{ form_action }} Freight Term
                    </button>
                </div>
            </form>
        </div>

        <!-- Help Card -->
        <div class="bg-sap-blue-50 rounded-lg border border-sap-blue-200 p-6 mt-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i data-lucide="info" class="h-5 w-5 text-sap-blue-600"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-sap-blue-800">Common Freight Terms</h3>
                    <div class="mt-2 text-sm text-sap-blue-700">
                        <ul class="list-disc list-inside space-y-1">
                            <li><strong>F.O.B.</strong> - Free On Board</li>
                            <li><strong>Ex Works</strong> - Seller delivers goods at their premises</li>
                            <li><strong>At Actual</strong> - Freight charged at actual cost</li>
                            <li><strong>Inclusive</strong> - Freight included in price</li>
                            <li><strong>To Pay Basis</strong> - Freight to be paid by recipient</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
    
    // Auto-focus on the terms field
    const termsField = document.getElementById('{{ form.terms.id_for_label }}');
    if (termsField) {
        termsField.focus();
    }
});
</script>
{% endblock %}