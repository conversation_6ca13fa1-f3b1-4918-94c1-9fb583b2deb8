# mis/urls.py
# URL patterns for MIS Budget Management module
# Task Group 1: Budget Management & Allocation

from django.urls import path
from . import views

app_name = 'mis'

urlpatterns = [
    # Dashboard
    path('', views.BudgetDashboardView.as_view(), name='dashboard'),
    
    # Budget Code URLs
    path('budget-codes/', views.BudgetCodeListView.as_view(), name='budget_code_list'),
    path('budget-codes/create/', views.BudgetCodeCreateView.as_view(), name='budget_code_create'),
    path('budget-codes/<int:pk>/update/', views.BudgetCodeUpdateView.as_view(), name='budget_code_update'),
    path('budget-codes/<int:pk>/delete/', views.BudgetCodeDeleteView.as_view(), name='budget_code_delete'),
    
    # Budget Period URLs
    path('budget-periods/', views.BudgetPeriodListView.as_view(), name='budget_period_list'),
    path('budget-periods/create/', views.BudgetPeriodCreateView.as_view(), name='budget_period_create'),
    
    # Budget Allocation URLs
    path('budget-allocations/', views.BudgetAllocationListView.as_view(), name='budget_allocation_list'),
    path('budget-allocations/create/', views.BudgetAllocationCreateView.as_view(), name='budget_allocation_create'),
    path('budget-allocations/<int:pk>/', views.BudgetAllocationDetailView.as_view(), name='budget_allocation_detail'),
    path('budget-allocations/<int:pk>/update/', views.BudgetAllocationUpdateView.as_view(), name='budget_allocation_update'),
    path('budget-allocations/<int:pk>/approve/', views.BudgetAllocationApprovalView.as_view(), name='budget_allocation_approve'),
    
    # Budget Distribution URLs
    path('budget-allocations/<int:allocation_pk>/distributions/create/', 
         views.BudgetDistributionCreateView.as_view(), 
         name='budget_distribution_create'),
    
    # HTMX and API URLs
    path('budget-allocations/search/', views.BudgetSearchView.as_view(), name='budget_search'),
]