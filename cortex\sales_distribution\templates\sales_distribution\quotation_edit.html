{% extends "core/base.html" %}
{% load static %}

{% block title %}{{ page_title }} - {{ global_company.company_name }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-slate-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- Breadcrumb Navigation -->
        <nav class="flex items-center space-x-2 text-sm mb-8" aria-label="Breadcrumb">
            <div class="flex items-center space-x-2 bg-white/80 backdrop-blur-sm rounded-lg px-4 py-2 border border-slate-200/60 shadow-sm">
                <a href="{% url 'sales_distribution:dashboard' %}" 
                   class="text-blue-600 hover:text-blue-700 transition-colors duration-200 font-medium flex items-center space-x-1">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                    </svg>
                    <span>Sales Distribution</span>
                </a>
                <svg class="w-4 h-4 text-slate-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
                <a href="{% url 'sales_distribution:quotation_list' %}" 
                   class="text-blue-600 hover:text-blue-700 transition-colors duration-200 font-medium flex items-center space-x-1">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    <span>Quotations</span>
                </a>
                <svg class="w-4 h-4 text-slate-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
                <a href="{% url 'sales_distribution:quotation_detail' quotation.id %}" 
                   class="text-blue-600 hover:text-blue-700 transition-colors duration-200 font-medium">
                    {{ quotation.quotationno }}
                </a>
                <svg class="w-4 h-4 text-slate-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
                <span class="text-slate-700 font-semibold">Edit</span>
            </div>
        </nav>

        <!-- Main Header Card -->
        <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200/60 mb-8 overflow-hidden">
            <div class="bg-gradient-to-r from-orange-500 via-orange-600 to-red-600 px-8 py-10">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="mb-6 lg:mb-0">
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                </svg>
                            </div>
                            <h1 class="text-4xl font-bold text-white tracking-tight">
                                Edit {{ quotation.quotationno }}
                            </h1>
                        </div>
                        <p class="text-orange-100 text-lg leading-relaxed max-w-2xl">
                            Customer: {{ quotation.customerid }}
                            {% if quotation.enqid %}
                                • Enquiry: ENQ-{{ quotation.enqid.enqid }}
                            {% endif %}
                        </p>
                    </div>
                    <div class="flex flex-col lg:items-end space-y-4">
                        <div class="flex items-center space-x-4">
                            <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-orange-100 text-orange-800">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                </svg>
                                Edit Mode
                            </span>
                        </div>
                        <div class="flex space-x-3">
                            <a href="{% url 'sales_distribution:quotation_detail' quotation.id %}" 
                               class="inline-flex items-center px-4 py-2 bg-white/20 text-white border border-white/30 rounded-lg font-medium hover:bg-white/30 focus:outline-none focus:ring-2 focus:ring-white/50 transition-all duration-200 backdrop-blur-sm">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                                </svg>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Form -->
        <form method="post" class="space-y-8">
            {% csrf_token %}
            {{ form.customerid }}
            {{ form.enqid }}
            
            <!-- Basic Information Section -->
            <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 overflow-hidden">
                <div class="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-slate-200/60">
                    <h2 class="text-xl font-bold text-slate-900 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        Basic Information
                    </h2>
                </div>
                <div class="px-6 py-8">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- Quotation Number -->
                        <div class="form-group">
                            <label for="{{ form.quotationno.id_for_label }}" class="block text-sm font-semibold text-slate-700 mb-2">
                                Quotation Number
                            </label>
                            {{ form.quotationno }}
                            {% if form.quotationno.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.quotationno.errors.0 }}</p>
                            {% endif %}
                        </div>

                        <!-- Due Date -->
                        <div class="form-group">
                            <label for="{{ form.duedate.id_for_label }}" class="block text-sm font-semibold text-slate-700 mb-2">
                                Due Date
                            </label>
                            {{ form.duedate }}
                            {% if form.duedate.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.duedate.errors.0 }}</p>
                            {% endif %}
                        </div>

                        <!-- Validity -->
                        <div class="form-group">
                            <label for="{{ form.validity.id_for_label }}" class="block text-sm font-semibold text-slate-700 mb-2">
                                Validity <span class="text-red-500">*</span>
                            </label>
                            {{ form.validity }}
                            {% if form.validity.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.validity.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quotation Items Section -->
            <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 overflow-hidden">
                <div class="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-slate-200/60">
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-bold text-slate-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                            </svg>
                            Quotation Items
                        </h2>
                        <button type="button" 
                                onclick="addQuotationItem()" 
                                class="inline-flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                            </svg>
                            Add Item
                        </button>
                    </div>
                </div>
                <div class="px-6 py-6">
                    <div id="quotation-items" class="space-y-4">
                        {% for detail in quotation_details %}
                        <div class="quotation-item bg-slate-50 rounded-lg p-4 border border-slate-200">
                            <div class="grid grid-cols-1 md:grid-cols-6 gap-4 items-end">
                                <!-- Item Description -->
                                <div class="md:col-span-2">
                                    <label class="block text-sm font-medium text-slate-700 mb-1">Item Description</label>
                                    <textarea name="item_desc[]" 
                                              rows="2" 
                                              class="block w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                                              placeholder="Item description">{{ detail.itemdesc }}</textarea>
                                </div>
                                
                                <!-- Quantity -->
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-1">Quantity</label>
                                    <input type="number" 
                                           name="quantity[]" 
                                           step="0.01" 
                                           min="0" 
                                           value="{{ detail.totalqty }}"
                                           class="block w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                                           placeholder="Qty">
                                </div>
                                
                                <!-- Unit -->
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-1">Unit</label>
                                    <select name="unit[]" 
                                            class="block w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <option value="">Select Unit</option>
                                        {% for unit in units %}
                                        <option value="{{ unit.id }}" {% if detail.unit.id == unit.id %}selected{% endif %}>
                                            {{ unit.unitname }} ({{ unit.symbol }})
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                
                                <!-- Rate -->
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-1">Rate</label>
                                    <input type="number" 
                                           name="rate[]" 
                                           step="0.01" 
                                           min="0" 
                                           value="{{ detail.rate }}"
                                           class="block w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                                           placeholder="Rate">
                                </div>
                                
                                <!-- Discount -->
                                <div class="flex items-end space-x-2">
                                    <div class="flex-1">
                                        <label class="block text-sm font-medium text-slate-700 mb-1">Discount</label>
                                        <input type="number" 
                                               name="discount[]" 
                                               step="0.01" 
                                               min="0" 
                                               value="{{ detail.discount|default:0 }}"
                                               class="block w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                                               placeholder="Discount">
                                    </div>
                                    <button type="button" 
                                            onclick="removeQuotationItem(this)" 
                                            class="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                                            title="Remove Item">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <!-- Empty state template -->
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-slate-900">No items yet</h3>
                            <p class="mt-1 text-sm text-slate-500">Add your first quotation item to get started.</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Terms & Conditions Section -->
            <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 overflow-hidden">
                <div class="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-slate-200/60">
                    <h2 class="text-xl font-bold text-slate-900 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        Terms & Conditions
                    </h2>
                </div>
                <div class="px-6 py-8">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Left Column -->
                        <div class="space-y-6">
                            <!-- Payment Terms -->
                            <div class="form-group">
                                <label for="{{ form.paymentterms.id_for_label }}" class="block text-sm font-semibold text-slate-700 mb-2">
                                    Payment Terms <span class="text-red-500">*</span>
                                </label>
                                {{ form.paymentterms }}
                                {% if form.paymentterms.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.paymentterms.errors.0 }}</p>
                                {% endif %}
                            </div>

                            <!-- Delivery Terms -->
                            <div class="form-group">
                                <label for="{{ form.deliveryterms.id_for_label }}" class="block text-sm font-semibold text-slate-700 mb-2">
                                    Delivery Terms <span class="text-red-500">*</span>
                                </label>
                                {{ form.deliveryterms }}
                                {% if form.deliveryterms.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.deliveryterms.errors.0 }}</p>
                                {% endif %}
                            </div>

                            <!-- Warranty -->
                            <div class="form-group">
                                <label for="{{ form.warrenty.id_for_label }}" class="block text-sm font-semibold text-slate-700 mb-2">
                                    Warranty <span class="text-red-500">*</span>
                                </label>
                                {{ form.warrenty }}
                                {% if form.warrenty.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.warrenty.errors.0 }}</p>
                                {% endif %}
                            </div>

                            <!-- Transport -->
                            <div class="form-group">
                                <label for="{{ form.transport.id_for_label }}" class="block text-sm font-semibold text-slate-700 mb-2">
                                    Transport <span class="text-red-500">*</span>
                                </label>
                                {{ form.transport }}
                                {% if form.transport.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.transport.errors.0 }}</p>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="space-y-6">
                            <!-- PF -->
                            <div class="form-group">
                                <label class="block text-sm font-semibold text-slate-700 mb-2">
                                    Packing & Forwarding <span class="text-red-500">*</span>
                                </label>
                                <div class="grid grid-cols-2 gap-2">
                                    {{ form.pftype }}
                                    {{ form.pf }}
                                </div>
                                {% if form.pf.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.pf.errors.0 }}</p>
                                {% endif %}
                            </div>

                            <!-- VAT/CST -->
                            <div class="form-group">
                                <label for="{{ form.vatcst.id_for_label }}" class="block text-sm font-semibold text-slate-700 mb-2">
                                    VAT/CST (%)
                                </label>
                                {{ form.vatcst }}
                                {% if form.vatcst.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.vatcst.errors.0 }}</p>
                                {% endif %}
                            </div>

                            <!-- Excise -->
                            <div class="form-group">
                                <label for="{{ form.excise.id_for_label }}" class="block text-sm font-semibold text-slate-700 mb-2">
                                    Excise (%)
                                </label>
                                {{ form.excise }}
                                {% if form.excise.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.excise.errors.0 }}</p>
                                {% endif %}
                            </div>

                            <!-- Freight -->
                            <div class="form-group">
                                <label class="block text-sm font-semibold text-slate-700 mb-2">
                                    Freight <span class="text-red-500">*</span>
                                </label>
                                <div class="grid grid-cols-2 gap-2">
                                    {{ form.freighttype }}
                                    {{ form.freight }}
                                </div>
                                {% if form.freight.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.freight.errors.0 }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Additional Fields Row -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8 pt-8 border-t border-slate-200">
                        <!-- Insurance -->
                        <div class="form-group">
                            <label for="{{ form.insurance.id_for_label }}" class="block text-sm font-semibold text-slate-700 mb-2">
                                Insurance <span class="text-red-500">*</span>
                            </label>
                            {{ form.insurance }}
                            {% if form.insurance.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.insurance.errors.0 }}</p>
                            {% endif %}
                        </div>

                        <!-- Note Number -->
                        <div class="form-group">
                            <label for="{{ form.noteno.id_for_label }}" class="block text-sm font-semibold text-slate-700 mb-2">
                                Note Number <span class="text-red-500">*</span>
                            </label>
                            {{ form.noteno }}
                            {% if form.noteno.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.noteno.errors.0 }}</p>
                            {% endif %}
                        </div>

                        <!-- Registration Number -->
                        <div class="form-group">
                            <label for="{{ form.registrationno.id_for_label }}" class="block text-sm font-semibold text-slate-700 mb-2">
                                Registration Number
                            </label>
                            {{ form.registrationno }}
                            {% if form.registrationno.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.registrationno.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Remarks -->
                    <div class="form-group mt-6">
                        <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-semibold text-slate-700 mb-2">
                            Remarks
                        </label>
                        {{ form.remarks }}
                        {% if form.remarks.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.remarks.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-between bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 px-6 py-4">
                <div class="flex items-center space-x-4">
                    <a href="{% url 'sales_distribution:quotation_detail' quotation.id %}" 
                       class="inline-flex items-center px-6 py-3 border border-slate-300 rounded-lg text-sm font-medium text-slate-700 bg-white hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                        </svg>
                        Cancel
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <button type="submit" 
                            class="inline-flex items-center px-8 py-3 bg-blue-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        Update Quotation
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- JavaScript for Dynamic Item Management -->
<script>
let unitOptions = '';
{% for unit in units %}
unitOptions += '<option value="{{ unit.id }}">{{ unit.unitname }} ({{ unit.symbol }})</option>';
{% endfor %}

function addQuotationItem() {
    const itemsContainer = document.getElementById('quotation-items');
    
    // Remove empty state if present
    const emptyState = itemsContainer.querySelector('.text-center');
    if (emptyState) {
        emptyState.remove();
    }
    
    const newItem = document.createElement('div');
    newItem.className = 'quotation-item bg-slate-50 rounded-lg p-4 border border-slate-200';
    newItem.innerHTML = `
        <div class="grid grid-cols-1 md:grid-cols-6 gap-4 items-end">
            <!-- Item Description -->
            <div class="md:col-span-2">
                <label class="block text-sm font-medium text-slate-700 mb-1">Item Description</label>
                <textarea name="item_desc[]" 
                          rows="2" 
                          class="block w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                          placeholder="Item description"></textarea>
            </div>
            
            <!-- Quantity -->
            <div>
                <label class="block text-sm font-medium text-slate-700 mb-1">Quantity</label>
                <input type="number" 
                       name="quantity[]" 
                       step="0.01" 
                       min="0" 
                       class="block w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                       placeholder="Qty">
            </div>
            
            <!-- Unit -->
            <div>
                <label class="block text-sm font-medium text-slate-700 mb-1">Unit</label>
                <select name="unit[]" 
                        class="block w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Select Unit</option>
                    ${unitOptions}
                </select>
            </div>
            
            <!-- Rate -->
            <div>
                <label class="block text-sm font-medium text-slate-700 mb-1">Rate</label>
                <input type="number" 
                       name="rate[]" 
                       step="0.01" 
                       min="0" 
                       class="block w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                       placeholder="Rate">
            </div>
            
            <!-- Discount -->
            <div class="flex items-end space-x-2">
                <div class="flex-1">
                    <label class="block text-sm font-medium text-slate-700 mb-1">Discount</label>
                    <input type="number" 
                           name="discount[]" 
                           step="0.01" 
                           min="0" 
                           value="0"
                           class="block w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                           placeholder="Discount">
                </div>
                <button type="button" 
                        onclick="removeQuotationItem(this)" 
                        class="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        title="Remove Item">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                    </svg>
                </button>
            </div>
        </div>
    `;
    
    itemsContainer.appendChild(newItem);
}

function removeQuotationItem(button) {
    const item = button.closest('.quotation-item');
    const itemsContainer = document.getElementById('quotation-items');
    
    // Confirm removal
    if (confirm('Are you sure you want to remove this item?')) {
        item.remove();
        
        // Show empty state if no items left
        const remainingItems = itemsContainer.querySelectorAll('.quotation-item');
        if (remainingItems.length === 0) {
            itemsContainer.innerHTML = `
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-slate-900">No items yet</h3>
                    <p class="mt-1 text-sm text-slate-500">Add your first quotation item to get started.</p>
                </div>
            `;
        }
    }
}

// Initialize with at least one item if none exist
document.addEventListener('DOMContentLoaded', function() {
    const itemsContainer = document.getElementById('quotation-items');
    const existingItems = itemsContainer.querySelectorAll('.quotation-item');
    
    if (existingItems.length === 0) {
        // Check if there's only empty state
        const emptyState = itemsContainer.querySelector('.text-center');
        if (emptyState) {
            addQuotationItem();
        }
    }
});
</script>
{% endblock %}
