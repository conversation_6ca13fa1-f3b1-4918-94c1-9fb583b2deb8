from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.urls import reverse_lazy, reverse
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.http import JsonResponse
from django.db.models import Q, Count, Sum, Min, Max
from django.utils import timezone
from datetime import datetime

from ..models import ClosingStock
from ..forms.closing_stock_forms import ClosingStockForm, ClosingStockSearchForm


class ClosingStockListView(LoginRequiredMixin, ListView):
    """List view for Closing Stock records with search and filtering"""
    model = ClosingStock
    template_name = 'inventory/closing_stock/closing_stock_list.html'
    context_object_name = 'closing_stocks'
    paginate_by = 20

    def get_queryset(self):
        # Order by ID descending (latest first) to match ASP.NET behavior
        queryset = ClosingStock.objects.all().order_by('-id')
        
        # Apply search and filters
        form = ClosingStockSearchForm(self.request.GET)
        if form.is_valid():
            search = form.cleaned_data.get('search')
            from_date = form.cleaned_data.get('from_date')
            to_date = form.cleaned_data.get('to_date')
            min_value = form.cleaned_data.get('min_value')
            max_value = form.cleaned_data.get('max_value')
            
            if search:
                # Search in date fields and stock value
                queryset = queryset.filter(
                    Q(fromdt__icontains=search) |
                    Q(todt__icontains=search) |
                    Q(clstock__icontains=search)
                )
            
            # Date range filtering (convert form dates to string format for DB comparison)
            if from_date:
                from_date_str = from_date.strftime('%d-%m-%Y')
                # Note: This is a simple string comparison, might need improvement for complex date filtering
                queryset = queryset.filter(fromdt__gte=from_date_str)
            
            if to_date:
                to_date_str = to_date.strftime('%d-%m-%Y')
                queryset = queryset.filter(todt__lte=to_date_str)
            
            # Value range filtering
            if min_value is not None:
                queryset = queryset.filter(clstock__gte=float(min_value))
            
            if max_value is not None:
                queryset = queryset.filter(clstock__lte=float(max_value))
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = ClosingStockSearchForm(self.request.GET)
        
        # Statistics for dashboard
        all_stocks = ClosingStock.objects.all()
        context['stats'] = {
            'total_records': all_stocks.count(),
            'total_value': all_stocks.aggregate(total=Sum('clstock'))['total'] or 0,
            'min_value': all_stocks.aggregate(min_val=Min('clstock'))['min_val'] or 0,
            'max_value': all_stocks.aggregate(max_val=Max('clstock'))['max_val'] or 0,
        }
        
        return context

    def render_to_response(self, context, **response_kwargs):
        if self.request.headers.get('HX-Request'):
            return render(self.request, 'inventory/closing_stock/partials/closing_stock_list_partial.html', context)
        return super().render_to_response(context, **response_kwargs)


class ClosingStockDetailView(LoginRequiredMixin, DetailView):
    """Detail view for Closing Stock records"""
    model = ClosingStock
    template_name = 'inventory/closing_stock/closing_stock_detail.html'
    context_object_name = 'closing_stock'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Convert string dates to date objects for better display
        if self.object.fromdt:
            try:
                context['from_date_formatted'] = datetime.strptime(self.object.fromdt, '%d-%m-%Y').strftime('%B %d, %Y')
            except (ValueError, TypeError):
                context['from_date_formatted'] = self.object.fromdt
        
        if self.object.todt:
            try:
                context['to_date_formatted'] = datetime.strptime(self.object.todt, '%d-%m-%Y').strftime('%B %d, %Y')
            except (ValueError, TypeError):
                context['to_date_formatted'] = self.object.todt
        
        # Calculate period duration
        if self.object.fromdt and self.object.todt:
            try:
                from_date = datetime.strptime(self.object.fromdt, '%d-%m-%Y')
                to_date = datetime.strptime(self.object.todt, '%d-%m-%Y')
                context['period_days'] = (to_date - from_date).days + 1  # +1 to include both dates
            except (ValueError, TypeError):
                context['period_days'] = None
        
        return context


class ClosingStockCreateView(LoginRequiredMixin, CreateView):
    """Create view for Closing Stock records"""
    model = ClosingStock
    form_class = ClosingStockForm
    template_name = 'inventory/closing_stock/closing_stock_form.html'

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(
            self.request, 
            f'Closing stock record for period {form.cleaned_data["from_date"].strftime("%b %d, %Y")} to {form.cleaned_data["to_date"].strftime("%b %d, %Y")} created successfully.'
        )
        return response

    def get_success_url(self):
        return reverse('inventory:closing_stock_detail', kwargs={'pk': self.object.pk})


class ClosingStockUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for Closing Stock records"""
    model = ClosingStock
    form_class = ClosingStockForm
    template_name = 'inventory/closing_stock/closing_stock_form.html'

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(
            self.request, 
            f'Closing stock record for period {form.cleaned_data["from_date"].strftime("%b %d, %Y")} to {form.cleaned_data["to_date"].strftime("%b %d, %Y")} updated successfully.'
        )
        return response

    def get_success_url(self):
        return reverse('inventory:closing_stock_detail', kwargs={'pk': self.object.pk})


class ClosingStockDeleteView(LoginRequiredMixin, DeleteView):
    """Delete view for Closing Stock records"""
    model = ClosingStock
    template_name = 'inventory/closing_stock/closing_stock_confirm_delete.html'
    success_url = reverse_lazy('inventory:closing_stock_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Add formatted dates for confirmation message
        if self.object.fromdt:
            try:
                context['from_date_formatted'] = datetime.strptime(self.object.fromdt, '%d-%m-%Y').strftime('%B %d, %Y')
            except (ValueError, TypeError):
                context['from_date_formatted'] = self.object.fromdt
        
        if self.object.todt:
            try:
                context['to_date_formatted'] = datetime.strptime(self.object.todt, '%d-%m-%Y').strftime('%B %d, %Y')
            except (ValueError, TypeError):
                context['to_date_formatted'] = self.object.todt
        
        return context

    def delete(self, request, *args, **kwargs):
        closing_stock = self.get_object()
        
        # Get period info for success message
        try:
            from_date = datetime.strptime(closing_stock.fromdt, '%d-%m-%Y').strftime('%b %d, %Y')
            to_date = datetime.strptime(closing_stock.todt, '%d-%m-%Y').strftime('%b %d, %Y')
            period_info = f"period {from_date} to {to_date}"
        except (ValueError, TypeError):
            period_info = f"record ID {closing_stock.id}"
        
        response = super().delete(request, *args, **kwargs)
        messages.success(request, f'Closing stock {period_info} deleted successfully.')
        return response


# =============================================================================
# API Views for AJAX functionality
# =============================================================================

@login_required
def closing_stock_statistics_api(request):
    """API endpoint for closing stock statistics"""
    
    # Get aggregated statistics
    stats = ClosingStock.objects.aggregate(
        total_records=Count('id'),
        total_value=Sum('clstock'),
        min_value=Min('clstock'),
        max_value=Max('clstock'),
        avg_value=Sum('clstock')  # Will calculate average manually
    )
    
    # Calculate average manually to handle None values
    if stats['total_records'] and stats['total_value']:
        stats['avg_value'] = stats['total_value'] / stats['total_records']
    else:
        stats['avg_value'] = 0
    
    # Handle None values
    for key, value in stats.items():
        if value is None:
            stats[key] = 0
    
    # Get recent records
    recent_records = []
    for record in ClosingStock.objects.order_by('-id')[:5]:
        try:
            from_date = datetime.strptime(record.fromdt, '%d-%m-%Y').strftime('%b %d, %Y') if record.fromdt else 'N/A'
            to_date = datetime.strptime(record.todt, '%d-%m-%Y').strftime('%b %d, %Y') if record.todt else 'N/A'
        except (ValueError, TypeError):
            from_date = record.fromdt or 'N/A'
            to_date = record.todt or 'N/A'
        
        recent_records.append({
            'id': record.id,
            'from_date': from_date,
            'to_date': to_date,
            'value': record.clstock or 0,
        })
    
    return JsonResponse({
        'statistics': stats,
        'recent_records': recent_records
    })


@login_required
def closing_stock_quick_add_api(request):
    """Quick add API for closing stock via AJAX"""
    if request.method == 'POST':
        form = ClosingStockForm(request.POST)
        if form.is_valid():
            instance = form.save()
            return JsonResponse({
                'success': True,
                'message': f'Closing stock record created successfully.',
                'record': {
                    'id': instance.id,
                    'from_date': form.cleaned_data['from_date'].strftime('%b %d, %Y'),
                    'to_date': form.cleaned_data['to_date'].strftime('%b %d, %Y'),
                    'value': float(form.cleaned_data['closing_stock_value']),
                }
            })
        else:
            return JsonResponse({
                'success': False,
                'errors': form.errors
            })
    
    return JsonResponse({'success': False, 'message': 'Invalid request method'})