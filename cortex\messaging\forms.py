from django import forms
from django.core.exceptions import ValidationError
from django.contrib.auth.models import User
from .models import Chat<PERSON><PERSON>, ChatMessage, ChatUser


class ChatRoomForm(forms.ModelForm):
    """Form for creating/editing chat rooms."""
    
    class Meta:
        model = ChatRoom
        fields = ['name', 'description', 'max_users']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Enter room name'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Enter room description',
                'rows': 3
            }),
            'max_users': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'min': '2',
                'max': '100',
                'value': '50'
            })
        }

    def clean_name(self):
        """Validate room name."""
        name = self.cleaned_data.get('name')
        if name:
            name = name.strip()
            if len(name) < 3:
                raise ValidationError("Room name must be at least 3 characters long.")
            
            # Check for existing room name (case-insensitive)
            existing_room = ChatRoom.objects.filter(name__iexact=name)
            if self.instance.pk:
                existing_room = existing_room.exclude(pk=self.instance.pk)
            
            if existing_room.exists():
                raise ValidationError("A room with this name already exists.")
        
        return name

    def clean_max_users(self):
        """Validate max users."""
        max_users = self.cleaned_data.get('max_users')
        if max_users is not None:
            if max_users < 2:
                raise ValidationError("Room must allow at least 2 users.")
            if max_users > 100:
                raise ValidationError("Room cannot exceed 100 users.")
        return max_users


class ChatMessageForm(forms.ModelForm):
    """Form for sending chat messages."""
    
    class Meta:
        model = ChatMessage
        fields = ['text']
        widgets = {
            'text': forms.TextInput(attrs={
                'class': 'flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Type your message...',
                'maxlength': '1000',
                'autocomplete': 'off'
            })
        }

    def clean_text(self):
        """Validate message text."""
        text = self.cleaned_data.get('text')
        if text:
            text = text.strip()
            if len(text) == 0:
                raise ValidationError("Message cannot be empty.")
            if len(text) > 1000:
                raise ValidationError("Message is too long. Maximum 1000 characters allowed.")
            
            # Remove potentially harmful HTML tags
            text = text.replace('<script', '&lt;script')
            text = text.replace('</script', '&lt;/script')
            text = text.replace('<iframe', '&lt;iframe')
            text = text.replace('</iframe', '&lt;/iframe')
        
        return text


class PrivateMessageForm(forms.Form):
    """Form for starting private messages."""
    
    recipient = forms.ModelChoiceField(
        queryset=User.objects.filter(is_active=True),
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
        }),
        empty_label="Select recipient",
        required=True
    )
    
    message = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent',
            'placeholder': 'Type your private message...',
            'rows': 4,
            'maxlength': '1000'
        }),
        required=True
    )

    def __init__(self, current_user=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if current_user:
            # Exclude current user from recipient list
            self.fields['recipient'].queryset = User.objects.filter(
                is_active=True
            ).exclude(pk=current_user.pk)

    def clean_message(self):
        """Validate private message text."""
        message = self.cleaned_data.get('message')
        if message:
            message = message.strip()
            if len(message) == 0:
                raise ValidationError("Message cannot be empty.")
            if len(message) > 1000:
                raise ValidationError("Message is too long. Maximum 1000 characters allowed.")
            
            # Remove potentially harmful HTML tags
            message = message.replace('<script', '&lt;script')
            message = message.replace('</script', '&lt;/script')
            message = message.replace('<iframe', '&lt;iframe')
            message = message.replace('</iframe', '&lt;/iframe')
        
        return message


class ChatUserForm(forms.ModelForm):
    """Form for updating chat user profile."""
    
    class Meta:
        model = ChatUser
        fields = ['employee_name', 'emp_id', 'gender']
        widgets = {
            'employee_name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Enter your display name'
            }),
            'emp_id': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Enter your employee ID'
            }),
            'gender': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
            })
        }

    def clean_employee_name(self):
        """Validate employee name."""
        employee_name = self.cleaned_data.get('employee_name')
        if employee_name:
            employee_name = employee_name.strip()
            if len(employee_name) < 2:
                raise ValidationError("Name must be at least 2 characters long.")
        return employee_name

    def clean_emp_id(self):
        """Validate employee ID."""
        emp_id = self.cleaned_data.get('emp_id')
        if emp_id:
            emp_id = emp_id.strip()
            if len(emp_id) < 3:
                raise ValidationError("Employee ID must be at least 3 characters long.")
            
            # Check for existing employee ID
            existing_user = ChatUser.objects.filter(emp_id__iexact=emp_id)
            if self.instance.pk:
                existing_user = existing_user.exclude(pk=self.instance.pk)
            
            if existing_user.exists():
                raise ValidationError("This employee ID is already taken.")
        
        return emp_id


class RoomJoinForm(forms.Form):
    """Form for joining a chat room."""
    
    room = forms.ModelChoiceField(
        queryset=ChatRoom.objects.filter(is_active=True),
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
        }),
        empty_label="Select a room to join",
        required=True
    )

    def __init__(self, current_user=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if current_user:
            # Only show rooms user is not already in
            from .models import RoomMembership
            joined_room_ids = RoomMembership.objects.filter(
                user=current_user, is_active=True
            ).values_list('room_id', flat=True)
            
            self.fields['room'].queryset = ChatRoom.objects.filter(
                is_active=True
            ).exclude(id__in=joined_room_ids)


class MessageSearchForm(forms.Form):
    """Form for searching messages."""
    
    search_text = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent',
            'placeholder': 'Search messages...',
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-get': '',
            'hx-target': '#messages-list'
        }),
        required=False
    )
    
    date_from = forms.DateField(
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent',
            'hx-trigger': 'change',
            'hx-get': '',
            'hx-target': '#messages-list'
        }),
        required=False
    )
    
    date_to = forms.DateField(
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent',
            'hx-trigger': 'change',
            'hx-get': '',
            'hx-target': '#messages-list'
        }),
        required=False
    )
    
    sender = forms.ModelChoiceField(
        queryset=User.objects.filter(is_active=True),
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent',
            'hx-trigger': 'change',
            'hx-get': '',
            'hx-target': '#messages-list'
        }),
        empty_label="All Senders",
        required=False
    )

    def clean(self):
        """Validate date range."""
        cleaned_data = super().clean()
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        
        if date_from and date_to:
            if date_from > date_to:
                raise ValidationError("From date must be before to date.")
        
        return cleaned_data