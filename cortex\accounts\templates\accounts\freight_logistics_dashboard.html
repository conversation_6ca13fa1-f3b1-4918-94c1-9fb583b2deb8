<!-- accounts/templates/accounts/freight_logistics_dashboard.html -->
<!-- Freight & Logistics Management Dashboard -->
<!-- Task Group 10: Freight & Logistics Management Dashboard (Task 10.1) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Freight & Logistics Management - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-teal-600 to-sap-teal-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="truck" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Freight & Logistics Management</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage freight rates, packing costs, and warranty terms</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to Accounts
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Freight Rates Summary -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="truck" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Freight Rates</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ freight_rates_count|default:0 }}</p>
                    <p class="text-xs text-sap-blue-600 mt-1">Active rates</p>
                </div>
            </div>
        </div>

        <!-- Packing Rates Summary -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="package" class="w-6 h-6 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Packing & Forwarding</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ packing_rates_count|default:0 }}</p>
                    <p class="text-xs text-sap-green-600 mt-1">Service rates</p>
                </div>
            </div>
        </div>

        <!-- Warranty Terms Summary -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="shield-check" class="w-6 h-6 text-sap-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Warranty Terms</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ warranty_terms_count|default:0 }}</p>
                    <p class="text-xs text-sap-purple-600 mt-1">Active terms</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Section -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        
        <!-- Freight Management -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="truck" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                    Freight Management
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:freight_create' %}" 
                       class="flex items-center w-full bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Add Freight Rate
                    </a>
                    <a href="{% url 'accounts:freight_list' %}" 
                       class="flex items-center w-full bg-sap-blue-100 hover:bg-sap-blue-200 text-sap-blue-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                        View All Freight Rates
                    </a>
                    <button type="button" onclick="calculateFreightCost()" 
                            class="flex items-center w-full bg-sap-gray-100 hover:bg-sap-gray-200 text-sap-gray-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="calculator" class="w-4 h-4 mr-2"></i>
                        Freight Calculator
                    </button>
                </div>
            </div>
        </div>

        <!-- Packing & Forwarding -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="package" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                    Packing & Forwarding
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:packing_forwarding_create' %}" 
                       class="flex items-center w-full bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Add Packing Rate
                    </a>
                    <a href="{% url 'accounts:packing_forwarding_list' %}" 
                       class="flex items-center w-full bg-sap-green-100 hover:bg-sap-green-200 text-sap-green-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                        View All Packing Rates
                    </a>
                    <button type="button" onclick="generatePackingQuote()" 
                            class="flex items-center w-full bg-sap-gray-100 hover:bg-sap-gray-200 text-sap-gray-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="file-text" class="w-4 h-4 mr-2"></i>
                        Generate Quote
                    </button>
                </div>
            </div>
        </div>

        <!-- Warranty Terms -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="shield-check" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                    Warranty Terms
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:warranty_terms_create' %}" 
                       class="flex items-center w-full bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Add Warranty Terms
                    </a>
                    <a href="{% url 'accounts:warranty_terms_list' %}" 
                       class="flex items-center w-full bg-sap-purple-100 hover:bg-sap-purple-200 text-sap-purple-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                        View All Warranty Terms
                    </a>
                    <button type="button" onclick="checkWarrantyStatus()" 
                            class="flex items-center w-full bg-sap-gray-100 hover:bg-sap-gray-200 text-sap-gray-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="search" class="w-4 h-4 mr-2"></i>
                        Check Warranty Status
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        
        <!-- Recent Freight Rates -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-sap-gray-800">Recent Freight Rates</h3>
                    <a href="{% url 'accounts:freight_list' %}" class="text-sap-blue-600 hover:text-sap-blue-700 text-sm font-medium">
                        View All
                    </a>
                </div>
            </div>
            <div class="p-6">
                {% if recent_freight_rates %}
                <div class="space-y-4">
                    {% for rate in recent_freight_rates %}
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="text-sm font-medium text-sap-gray-900">{{ rate.freight_type }}</div>
                            <div class="text-xs text-sap-gray-500">{{ rate.destination }}</div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-sap-gray-900">₹{{ rate.rate_per_km|floatformat:2 }}/km</div>
                            <div class="text-xs text-sap-gray-500">{{ rate.vehicle_type }}</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i data-lucide="truck" class="w-8 h-8 text-sap-gray-400 mx-auto mb-2"></i>
                    <p class="text-sm text-sap-gray-500">No freight rates defined</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Recent Packing Rates -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-sap-gray-800">Recent Packing Rates</h3>
                    <a href="{% url 'accounts:packing_forwarding_list' %}" class="text-sap-green-600 hover:text-sap-green-700 text-sm font-medium">
                        View All
                    </a>
                </div>
            </div>
            <div class="p-6">
                {% if recent_packing_rates %}
                <div class="space-y-4">
                    {% for rate in recent_packing_rates %}
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="text-sm font-medium text-sap-gray-900">{{ rate.service_type }}</div>
                            <div class="text-xs text-sap-gray-500">{{ rate.material_type }}</div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-sap-gray-900">₹{{ rate.rate_per_unit|floatformat:2 }}</div>
                            <div class="text-xs text-sap-gray-500">per {{ rate.unit_of_measurement }}</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i data-lucide="package" class="w-8 h-8 text-sap-gray-400 mx-auto mb-2"></i>
                    <p class="text-sm text-sap-gray-500">No packing rates defined</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Recent Warranty Terms -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-sap-gray-800">Recent Warranty Terms</h3>
                    <a href="{% url 'accounts:warranty_terms_list' %}" class="text-sap-purple-600 hover:text-sap-purple-700 text-sm font-medium">
                        View All
                    </a>
                </div>
            </div>
            <div class="p-6">
                {% if recent_warranty_terms %}
                <div class="space-y-4">
                    {% for term in recent_warranty_terms %}
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="text-sm font-medium text-sap-gray-900">{{ term.warranty_type }}</div>
                            <div class="text-xs text-sap-gray-500">{{ term.product_category }}</div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-sap-gray-900">{{ term.warranty_period_months }} months</div>
                            <div class="text-xs text-sap-gray-500">warranty</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i data-lucide="shield-check" class="w-8 h-8 text-sap-gray-400 mx-auto mb-2"></i>
                    <p class="text-sm text-sap-gray-500">No warranty terms defined</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function calculateFreightCost() {
    alert('Freight cost calculator functionality would be implemented here.');
}

function generatePackingQuote() {
    alert('Packing quote generation functionality would be implemented here.');
}

function checkWarrantyStatus() {
    alert('Warranty status check functionality would be implemented here.');
}

// Initialize lucide icons on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}