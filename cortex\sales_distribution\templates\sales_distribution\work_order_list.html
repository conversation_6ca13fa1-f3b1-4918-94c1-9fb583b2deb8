{% extends 'core/base.html' %}
{% load static %}

{% block title %}Work Order - Edit - Sales Distribution{% endblock %}

{% block content %}
<div class="flex-1 p-6">
    <div class="sap-card">
        <!-- Header matching ASP.NET original -->
        <div class="bg-gradient-to-r from-sap-blue-600 to-sap-blue-700 text-white px-4 py-3 rounded-lg mb-6">
            <h1 class="text-lg font-semibold">Work Order - Edit</h1>
        </div>

        <!-- Search Controls matching ASP.NET original -->
        <div class="bg-sap-gray-50 p-4 rounded-lg mb-6">
            <form method="get" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 items-end" id="searchForm">
                <!-- Search Type Dropdown -->
                <div class="lg:col-span-1">
                    <label class="block text-sm font-medium text-sap-gray-700 mb-1">Search By</label>
                    <select name="search_field" class="sap-input" onchange="toggleSearchFields(); this.form.submit()">
                        <option value="0" {% if search_field == "0" %}selected{% endif %}>Customer Name</option>
                        <option value="1" {% if search_field == "1" %}selected{% endif %}>Enquiry No</option>
                        <option value="2" {% if search_field == "2" %}selected{% endif %}>PO No</option>
                        <option value="3" {% if search_field == "3" %}selected{% endif %}>WO No</option>
                    </select>
                </div>

                <!-- Text input for Enquiry/PO/WO number -->
                <div class="lg:col-span-1" id="enqIdContainer" style="{% if search_field == '0' %}display: none;{% endif %}">
                    <label class="block text-sm font-medium text-sap-gray-700 mb-1">Value</label>
                    <input type="text" 
                           name="enq_id" 
                           value="{{ enq_id }}" 
                           placeholder="Enter Value" 
                           class="sap-input"
                           id="enqIdInput">
                </div>

                <!-- Customer Name Search with autocomplete -->
                <div class="lg:col-span-2" id="searchValueContainer" style="{% if search_field != '0' %}display: none;{% endif %}">
                    <label class="block text-sm font-medium text-sap-gray-700 mb-1">Customer Name</label>
                    <input type="text" 
                           name="search_value" 
                           value="{{ search_value }}" 
                           placeholder="Search Customer Name" 
                           class="sap-input"
                           id="searchValueInput"
                           list="customerList">
                    <datalist id="customerList"></datalist>
                </div>

                <!-- WO Category Dropdown -->
                <div class="lg:col-span-1">
                    <label class="block text-sm font-medium text-sap-gray-700 mb-1">Category</label>
                    <select name="wo_category" class="sap-input" onchange="this.form.submit()">
                        <option value="">All Categories</option>
                        {% for category in wo_categories %}
                        <option value="{{ category.cid }}" {% if wo_category == category.cid|stringformat:"s" %}selected{% endif %}>
                            {{ category.symbol }} - {{ category.cname }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Action Buttons -->
                <div class="lg:col-span-1 flex gap-2">
                    <button type="submit" class="sap-button-primary flex-1">
                        <i data-lucide="search" class="w-4 h-4 mr-2"></i>Search
                    </button>
                    <a href="{% url 'sales_distribution:work_order_po_selection' %}" 
                       class="sap-button-primary bg-green-600 hover:bg-green-700 text-center py-3 px-4 rounded-lg"
                       title="Create New Work Order">
                        <i data-lucide="plus" class="w-4 h-4"></i>
                    </a>
                </div>
            </form>
        </div>

        <!-- Work Orders Grid - Responsive SAP Design with Optimized Columns -->
        {% if work_orders %}
        <div class="bg-white rounded-lg border border-sap-gray-200 overflow-hidden">
            <div class="overflow-x-auto">
                <table class="w-full table-fixed">
                    <colgroup>
                        <col class="w-[4%]">   <!-- SN -->
                        <col class="w-[8%]">   <!-- Fin Yrs -->
                        <col class="w-[25%]">  <!-- Customer Name -->
                        <col class="w-[6%]">   <!-- Code -->
                        <col class="w-[10%]">  <!-- Enquiry No -->
                        <col class="w-[15%]">  <!-- PO No -->
                        <col class="w-[8%]">   <!-- WO No -->
                        <col class="w-[10%]">  <!-- Gen. Date -->
                        <col class="w-[14%]">  <!-- Gen. By -->
                    </colgroup>
                    <thead class="bg-sap-gray-50 border-b border-sap-gray-200">
                        <tr>
                            <th class="px-2 py-3 text-center text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">SN</th>
                            <th class="px-2 py-3 text-center text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">Fin Yrs</th>
                            <th class="px-3 py-3 text-left text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">Customer Name</th>
                            <th class="px-2 py-3 text-center text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">Code</th>
                            <th class="px-2 py-3 text-center text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">Enquiry No</th>
                            <th class="px-2 py-3 text-center text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">PO No</th>
                            <th class="px-2 py-3 text-center text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">WO No</th>
                            <th class="px-2 py-3 text-center text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">Gen. Date</th>
                            <th class="px-2 py-3 text-center text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">Gen. By</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-sap-gray-100">
                        {% for work_order in work_orders %}
                        <tr class="hover:bg-sap-gray-50 transition-colors duration-200">
                            <td class="px-2 py-3 text-sm text-sap-gray-900 text-center font-medium">
                                {% if page_obj %}
                                    {{ forloop.counter|add:page_obj.start_index|add:"-1" }}
                                {% else %}
                                    {{ forloop.counter }}
                                {% endif %}
                            </td>
                            <td class="px-1 py-3 text-center">
                                <span class="inline-flex items-center px-1.5 py-0.5 text-xs font-medium bg-sap-blue-50 text-sap-blue-700 rounded">
                                    {{ work_order.financial_year|default:"-" }}
                                </span>
                            </td>
                            <td class="px-3 py-3 text-sm text-sap-gray-900">
                                <div class="font-medium text-sap-gray-900 truncate leading-tight">{{ work_order.customer_name|default:"-" }}</div>
                                {% if work_order.customer_code and work_order.customer_code != work_order.customer_name %}
                                <div class="text-xs text-sap-gray-500 truncate">[{{ work_order.customer_code }}]</div>
                                {% endif %}
                            </td>
                            <td class="px-2 py-3 text-center">
                                <span class="text-xs font-mono text-sap-gray-700">{{ work_order.customer_code|default:work_order.customerid|default:"-" }}</span>
                            </td>
                            <td class="px-2 py-3 text-center">
                                <span class="text-xs font-mono text-sap-gray-700">{{ work_order.enqid|default:"-" }}</span>
                            </td>
                            <td class="px-2 py-3 text-sm text-sap-gray-900">
                                <span class="text-xs font-mono truncate block">{{ work_order.pono|default:"-" }}</span>
                            </td>
                            <td class="px-2 py-3 text-center">
                                <a href="{% url 'sales_distribution:work_order_detail' work_order.pk %}" 
                                   class="text-sap-blue-600 hover:text-sap-blue-800 font-medium text-xs transition-colors duration-200 underline">
                                    {{ work_order.wono|default:"-" }}
                                </a>
                            </td>
                            <td class="px-2 py-3 text-center">
                                <span class="text-xs text-sap-gray-700">{{ work_order.formatted_date|default:work_order.sysdate|default:"-" }}</span>
                            </td>
                            <td class="px-2 py-3 text-sm text-sap-gray-900">
                                <span class="text-xs truncate block">{{ work_order.employee_name|default:"-" }}</span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% else %}
        <div class="bg-white rounded-lg border border-sap-gray-200 p-12 text-center">
            <div class="text-lg font-semibold text-red-600">No data to display !</div>
            <p class="text-sap-gray-500 mt-2">Try adjusting your search criteria or create a new work order.</p>
        </div>
        {% endif %}

        <!-- Pagination -->
        {% if is_paginated %}
        <div class="mt-6 flex justify-between items-center">
            <div class="text-sm text-sap-gray-500">
                Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} entries
            </div>
            <nav class="flex items-center space-x-1">
                {% if page_obj.has_previous %}
                    <a href="?page=1{% if request.GET.search_field %}&search_field={{ request.GET.search_field }}{% endif %}{% if request.GET.search_value %}&search_value={{ request.GET.search_value }}{% endif %}{% if request.GET.enq_id %}&enq_id={{ request.GET.enq_id }}{% endif %}{% if request.GET.wo_category %}&wo_category={{ request.GET.wo_category }}{% endif %}" 
                       class="px-3 py-2 text-sap-gray-500 hover:text-sap-blue-600 hover:bg-sap-gray-50 rounded-lg transition-colors duration-200">
                        <i data-lucide="chevrons-left" class="w-4 h-4"></i>
                    </a>
                    <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search_field %}&search_field={{ request.GET.search_field }}{% endif %}{% if request.GET.search_value %}&search_value={{ request.GET.search_value }}{% endif %}{% if request.GET.enq_id %}&enq_id={{ request.GET.enq_id }}{% endif %}{% if request.GET.wo_category %}&wo_category={{ request.GET.wo_category }}{% endif %}" 
                       class="px-3 py-2 text-sap-gray-500 hover:text-sap-blue-600 hover:bg-sap-gray-50 rounded-lg transition-colors duration-200">
                        <i data-lucide="chevron-left" class="w-4 h-4"></i>
                    </a>
                {% endif %}
                
                <span class="px-4 py-2 bg-sap-blue-50 text-sap-blue-600 rounded-lg font-medium">
                    {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                </span>
                
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search_field %}&search_field={{ request.GET.search_field }}{% endif %}{% if request.GET.search_value %}&search_value={{ request.GET.search_value }}{% endif %}{% if request.GET.enq_id %}&enq_id={{ request.GET.enq_id }}{% endif %}{% if request.GET.wo_category %}&wo_category={{ request.GET.wo_category }}{% endif %}" 
                       class="px-3 py-2 text-sap-gray-500 hover:text-sap-blue-600 hover:bg-sap-gray-50 rounded-lg transition-colors duration-200">
                        <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    </a>
                    <a href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search_field %}&search_field={{ request.GET.search_field }}{% endif %}{% if request.GET.search_value %}&search_value={{ request.GET.search_value }}{% endif %}{% if request.GET.enq_id %}&enq_id={{ request.GET.enq_id }}{% endif %}{% if request.GET.wo_category %}&wo_category={{ request.GET.wo_category }}{% endif %}" 
                       class="px-3 py-2 text-sap-gray-500 hover:text-sap-blue-600 hover:bg-sap-gray-50 rounded-lg transition-colors duration-200">
                        <i data-lucide="chevrons-right" class="w-4 h-4"></i>
                    </a>
                {% endif %}
            </nav>
        </div>
        {% endif %}
    </div>
</div>

<script>
function toggleSearchFields() {
    var searchField = document.querySelector('select[name="search_field"]').value;
    var enqIdContainer = document.getElementById('enqIdContainer');
    var searchValueContainer = document.getElementById('searchValueContainer');
    var enqIdInput = document.getElementById('enqIdInput');
    var searchValueInput = document.getElementById('searchValueInput');
    
    if (searchField === '0') {
        // Customer Name selected
        enqIdContainer.style.display = 'none';
        searchValueContainer.style.display = 'block';
        enqIdInput.value = '';
    } else {
        // Enquiry No, PO No, or WO No selected
        enqIdContainer.style.display = 'block';
        searchValueContainer.style.display = 'none';
        searchValueInput.value = '';
        
        // Update placeholder based on selection
        if (searchField === '1') {
            enqIdInput.placeholder = 'Enter Enquiry No';
        } else if (searchField === '2') {
            enqIdInput.placeholder = 'Enter PO No';
        } else if (searchField === '3') {
            enqIdInput.placeholder = 'Enter WO No';
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
    
    // Set initial state of form fields
    toggleSearchFields();
    
    // Customer autocomplete functionality matching ASP.NET
    var customerInput = document.getElementById('searchValueInput');
    var customerList = document.getElementById('customerList');
    
    if (customerInput && customerList) {
        customerInput.addEventListener('input', function() {
            var query = this.value.trim();
            
            if (query.length >= 1) {
                // Make AJAX call to get customer suggestions
                fetch('/sales-distribution/ajax/work-order-customer-autocomplete/?q=' + encodeURIComponent(query))
                    .then(response => response.json())
                    .then(data => {
                        // Clear existing options
                        customerList.innerHTML = '';
                        
                        // Add new suggestions (using the existing customer autocomplete format)
                        if (data.customers) {
                            data.customers.forEach(function(customer) {
                                var option = document.createElement('option');
                                option.value = customer.text;
                                customerList.appendChild(option);
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching customer suggestions:', error);
                    });
            } else {
                customerList.innerHTML = '';
            }
        });
    }
});
</script>
{% endblock %}