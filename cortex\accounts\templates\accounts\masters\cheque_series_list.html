<!-- accounts/templates/accounts/masters/cheque_series_list.html -->
<!-- Cheque Series Management List Template -->
<!-- Task Package 1: Master Data Templates - Cheque Series Management -->

{% extends 'core/base.html' %}

{% block title %}Cheque Series - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-green-600 to-sap-green-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="receipt" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Cheque Series Management</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage cheque series by bank with range tracking and validation</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Accounts
                </a>
                <a href="{% url 'accounts:cheque_series_create' %}" 
                   class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    Add Cheque Series
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6" x-data="chequeSeriesManagement()">
    
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Total Series</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ cheque_series.count }}</p>
                </div>
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="receipt" class="w-6 h-6 text-sap-green-600"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Active Series</p>
                    <p class="text-2xl font-bold text-sap-green-600">{{ active_series_count|default:0 }}</p>
                </div>
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="check-circle" class="w-6 h-6 text-sap-green-600"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Used Cheques</p>
                    <p class="text-2xl font-bold text-sap-orange-600">{{ used_cheques_count|default:0 }}</p>
                </div>
                <div class="w-12 h-12 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="file-check" class="w-6 h-6 text-sap-orange-600"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Available</p>
                    <p class="text-2xl font-bold text-sap-blue-600">{{ available_cheques_count|default:0 }}</p>
                </div>
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="file-plus" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Search and Filters -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm mb-6">
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-sap-gray-700 mb-2">Search</label>
                    <input type="text" x-model="searchTerm" @input="filterSeries" id="search"
                           placeholder="Search by bank, series..."
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                </div>
                <div>
                    <label for="bank_filter" class="block text-sm font-medium text-sap-gray-700 mb-2">Bank</label>
                    <select x-model="bankFilter" @change="filterSeries" id="bank_filter"
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                        <option value="">All Banks</option>
                        <option value="SBI">State Bank of India</option>
                        <option value="HDFC">HDFC Bank</option>
                        <option value="ICICI">ICICI Bank</option>
                        <option value="Axis">Axis Bank</option>
                        <option value="Kotak">Kotak Bank</option>
                    </select>
                </div>
                <div>
                    <label for="status_filter" class="block text-sm font-medium text-sap-gray-700 mb-2">Status</label>
                    <select x-model="statusFilter" @change="filterSeries" id="status_filter"
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                        <option value="">All Status</option>
                        <option value="Active">Active</option>
                        <option value="Exhausted">Exhausted</option>
                        <option value="Blocked">Blocked</option>
                        <option value="Cancelled">Cancelled</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button @click="resetFilters" 
                            class="w-full bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="refresh-cw" class="w-4 h-4 inline mr-2"></i>
                        Reset
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Cheque Series Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Cheque Series</h3>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-sap-gray-600" x-text="`${filteredSeries} of ${allSeries} series`"></span>
                    <div class="flex items-center space-x-1">
                        <button @click="exportSeries" 
                                class="text-sap-gray-600 hover:text-sap-gray-800 p-1" title="Export Series">
                            <i data-lucide="download" class="w-4 h-4"></i>
                        </button>
                        <button @click="refreshData" 
                                class="text-sap-gray-600 hover:text-sap-gray-800 p-1" title="Refresh Data">
                            <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        {% if cheque_series %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            <button @click="sortBy('bank')" class="flex items-center space-x-1">
                                <span>Bank & Series</span>
                                <i data-lucide="chevron-up-down" class="w-3 h-3"></i>
                            </button>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Range Management</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Usage Tracking</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Series Validation</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for series in cheque_series %}
                    <tr class="hover:bg-sap-gray-50" x-show="isVisible({{ series.id }})">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-sap-green-100 rounded-lg flex items-center justify-center mr-3">
                                    <i data-lucide="building-2" class="w-5 h-5 text-sap-green-600"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-sap-gray-900">{{ series.bank_name|default:"HDFC Bank" }}</div>
                                    <div class="text-xs text-sap-gray-500">Series: {{ series.series_code|default:"CHQ2024001" }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-sap-gray-900">
                                <div class="font-medium">{{ series.start_number|default:"100001" }} - {{ series.end_number|default:"100100" }}</div>
                                <div class="text-xs text-sap-gray-500">{{ series.total_cheques|default:"100" }} cheques</div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-sap-gray-900">
                                <div class="flex items-center space-x-2">
                                    <div class="flex-1 bg-sap-gray-200 rounded-full h-2">
                                        <div class="bg-sap-green-600 h-2 rounded-full" style="width: {{ series.usage_percentage|default:25 }}%"></div>
                                    </div>
                                    <span class="text-xs font-medium">{{ series.usage_percentage|default:25 }}%</span>
                                </div>
                                <div class="text-xs text-sap-gray-500 mt-1">{{ series.used_count|default:25 }} used / {{ series.available_count|default:75 }} available</div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if series.status == 'active' or not series.status %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-green-100 text-sap-green-800">
                                <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                Active
                            </span>
                            {% elif series.status == 'exhausted' %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-orange-100 text-sap-orange-800">
                                <i data-lucide="alert-circle" class="w-3 h-3 mr-1"></i>
                                Exhausted
                            </span>
                            {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-red-100 text-sap-red-800">
                                <i data-lucide="x-circle" class="w-3 h-3 mr-1"></i>
                                Blocked
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if series.is_valid|default:True %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-green-100 text-sap-green-800">
                                <i data-lucide="shield-check" class="w-3 h-3 mr-1"></i>
                                Valid
                            </span>
                            {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-red-100 text-sap-red-800">
                                <i data-lucide="alert-triangle" class="w-3 h-3 mr-1"></i>
                                Invalid
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <button @click="viewUsage('{{ series.series_code|default:"CHQ2024001" }}')" 
                                        class="text-sap-blue-600 hover:text-sap-blue-700 p-1" title="View Usage">
                                    <i data-lucide="bar-chart" class="w-4 h-4"></i>
                                </button>
                                <button @click="getNextCheque('{{ series.series_code|default:"CHQ2024001" }}')" 
                                        class="text-sap-green-600 hover:text-sap-green-700 p-1" title="Get Next Cheque">
                                    <i data-lucide="file-plus" class="w-4 h-4"></i>
                                </button>
                                <a href="{% url 'accounts:cheque_series_edit' series.id %}" 
                                   class="text-sap-orange-600 hover:text-sap-orange-700 p-1" title="Edit">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </a>
                                <a href="{% url 'accounts:cheque_series_delete' series.id %}" 
                                   class="text-red-600 hover:text-red-700 p-1" title="Delete"
                                   onclick="return confirm('Are you sure you want to delete this cheque series?')">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="w-24 h-24 bg-sap-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i data-lucide="receipt" class="w-12 h-12 text-sap-green-600"></i>
            </div>
            <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No Cheque Series Configured</h3>
            <p class="text-sap-gray-600 mb-6">Get started by adding your first cheque series.</p>
            <a href="{% url 'accounts:cheque_series_create' %}" 
               class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                Add First Series
            </a>
        </div>
        {% endif %}
    </div>
    
    <!-- Next Cheque Modal -->
    <div x-show="showNextCheque" x-transition 
         class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
         @click.self="showNextCheque = false">
        <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-sap-gray-800" x-text="nextChequeTitle"></h3>
                <button @click="showNextCheque = false" class="text-sap-gray-400 hover:text-sap-gray-600">
                    <i data-lucide="x" class="w-5 h-5"></i>
                </button>
            </div>
            
            <div class="space-y-4">
                <div class="bg-sap-green-50 rounded-lg p-4 text-center">
                    <div class="text-3xl font-bold text-sap-green-600 mb-2" x-text="nextChequeNumber"></div>
                    <div class="text-sm text-sap-gray-600">Next Available Cheque Number</div>
                </div>
                
                <div class="text-sm text-sap-gray-600">
                    <div class="flex justify-between py-1">
                        <span>Series:</span>
                        <span x-text="currentSeries"></span>
                    </div>
                    <div class="flex justify-between py-1">
                        <span>Bank:</span>
                        <span>HDFC Bank</span>
                    </div>
                    <div class="flex justify-between py-1">
                        <span>Remaining:</span>
                        <span>75 cheques</span>
                    </div>
                </div>
                
                <div class="flex items-center justify-end space-x-3">
                    <button @click="showNextCheque = false" 
                            class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                        Close
                    </button>
                    <button @click="allocateCheque" 
                            class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        Allocate Cheque
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function chequeSeriesManagement() {
    return {
        searchTerm: '',
        bankFilter: '',
        statusFilter: '',
        sortField: '',
        sortDirection: 'asc',
        allSeries: 0,
        filteredSeries: 0,
        showNextCheque: false,
        nextChequeTitle: '',
        nextChequeNumber: '',
        currentSeries: '',
        
        init() {
            lucide.createIcons();
            this.allSeries = document.querySelectorAll('tbody tr').length;
            this.filteredSeries = this.allSeries;
        },
        
        filterSeries() {
            const rows = document.querySelectorAll('tbody tr');
            let visibleCount = 0;
            
            rows.forEach(row => {
                const bank = row.querySelector('td:first-child').textContent.toLowerCase();
                const status = row.querySelector('td:nth-child(4)').textContent.toLowerCase();
                
                let visible = true;
                
                // Search filter
                if (this.searchTerm && !bank.includes(this.searchTerm.toLowerCase())) {
                    visible = false;
                }
                
                // Bank filter
                if (this.bankFilter && !bank.includes(this.bankFilter.toLowerCase())) {
                    visible = false;
                }
                
                // Status filter
                if (this.statusFilter && !status.includes(this.statusFilter.toLowerCase())) {
                    visible = false;
                }
                
                row.style.display = visible ? '' : 'none';
                if (visible) visibleCount++;
            });
            
            this.filteredSeries = visibleCount;
        },
        
        resetFilters() {
            this.searchTerm = '';
            this.bankFilter = '';
            this.statusFilter = '';
            this.filterSeries();
        },
        
        sortBy(field) {
            console.log('Sorting by:', field);
        },
        
        isVisible(id) {
            return true;
        },
        
        viewUsage(series) {
            console.log('Viewing usage for series:', series);
        },
        
        getNextCheque(series) {
            this.currentSeries = series;
            this.nextChequeTitle = `Next Cheque - ${series}`;
            this.nextChequeNumber = '100026';
            this.showNextCheque = true;
        },
        
        allocateCheque() {
            console.log('Allocating cheque:', this.nextChequeNumber);
            this.showNextCheque = false;
        },
        
        exportSeries() {
            console.log('Exporting cheque series...');
        },
        
        refreshData() {
            window.location.reload();
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %>