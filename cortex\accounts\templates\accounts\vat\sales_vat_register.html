<!-- accounts/templates/accounts/vat/sales_vat_register.html -->
<!-- Sales VAT Register Template -->
<!-- Task Group 4: Taxation Management - Sales VAT Register (Task 4.7) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Sales VAT Register - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-red-600 to-sap-red-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="tag" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Sales VAT Register</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Output VAT collected on sales and services</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:vat_register_dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to VAT Dashboard
                </a>
                <button type="button" onclick="exportSalesVAT()" 
                        class="bg-sap-red-600 hover:bg-sap-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                    Export Register
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Filter and Search Section -->
    <div class="mb-6 bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4">
            <form method="get" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4">
                <!-- Date Range Filter -->
                <div>
                    <label for="date_from" class="block text-sm font-medium text-sap-gray-700 mb-1">From Date</label>
                    <input type="date" name="date_from" value="{{ request.GET.date_from }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-red-500 focus:border-sap-red-500">
                </div>
                
                <div>
                    <label for="date_to" class="block text-sm font-medium text-sap-gray-700 mb-1">To Date</label>
                    <input type="date" name="date_to" value="{{ request.GET.date_to }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-red-500 focus:border-sap-red-500">
                </div>
                
                <!-- VAT Rate Filter -->
                <div>
                    <label for="vat_rate" class="block text-sm font-medium text-sap-gray-700 mb-1">VAT Rate</label>
                    <select name="vat_rate" 
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-red-500 focus:border-sap-red-500">
                        <option value="">All Rates</option>
                        {% for rate in vat_rates %}
                        <option value="{{ rate }}" {% if request.GET.vat_rate == rate|stringformat:"s" %}selected{% endif %}>{{ rate }}%</option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Customer Filter -->
                <div class="flex-1">
                    <label for="customer" class="block text-sm font-medium text-sap-gray-700 mb-1">Customer</label>
                    <input type="text" name="customer" value="{{ request.GET.customer }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-red-500 focus:border-sap-red-500"
                           placeholder="Search by customer name...">
                </div>
                
                <!-- Search Button -->
                <div>
                    <button type="submit" 
                            class="bg-sap-red-600 hover:bg-sap-red-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="search" class="w-4 h-4 inline mr-2"></i>
                        Filter
                    </button>
                </div>
                
                <!-- Clear Button -->
                {% if request.GET %}
                <div>
                    <a href="{% url 'accounts:sales_vat_register' %}" 
                       class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                        Clear
                    </a>
                </div>
                {% endif %}
            </form>
        </div>
    </div>

    <!-- Sales VAT Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-red-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="tag" class="w-6 h-6 text-sap-red-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Sales</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ total_sales|default:0|floatformat:2 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="percent" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total VAT</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ total_vat|default:0|floatformat:2 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="list" class="w-6 h-6 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Entries</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ sales_entries.count|default:0 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="calculator" class="w-6 h-6 text-sap-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Avg VAT Rate</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ average_vat_rate|default:0|floatformat:1 }}%</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Sales VAT Register Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Sales VAT Entries</h3>
                <div class="flex items-center space-x-2">
                    <button type="button" onclick="printRegister()" 
                            class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="printer" class="w-4 h-4 inline mr-2"></i>
                        Print
                    </button>
                    <button type="button" onclick="generateVATReport()" 
                            class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="bar-chart" class="w-4 h-4 inline mr-2"></i>
                        VAT Report
                    </button>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Invoice Details
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Customer Information
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Sales Value
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            VAT Details
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Total Amount
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for entry in sales_entries %}
                    <tr class="hover:bg-sap-gray-50">
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-sap-red-100 rounded-lg flex items-center justify-center mr-3">
                                    <i data-lucide="receipt" class="w-5 h-5 text-sap-red-600"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-sap-gray-900">
                                        <a href="{% url 'accounts:sales_invoice_detail' entry.id %}" class="text-sap-red-600 hover:text-sap-red-900">
                                            {{ entry.invoice_number|default:"Sales Invoice" }}
                                        </a>
                                    </div>
                                    <div class="text-sm text-sap-gray-500">{{ entry.invoice_date|date:"d M Y"|default:"-" }}</div>
                                    {% if entry.reference_number %}
                                    <div class="text-xs text-sap-gray-400">Ref: {{ entry.reference_number }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-sap-gray-900">{{ entry.customer_name|default:"-" }}</div>
                            {% if entry.customer_vat_number %}
                            <div class="text-sm text-sap-gray-500">VAT: {{ entry.customer_vat_number }}</div>
                            {% endif %}
                            {% if entry.customer_address %}
                            <div class="text-xs text-sap-gray-400">{{ entry.customer_address|truncatechars:30 }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-sap-gray-900">₹{{ entry.taxable_value|default:0|floatformat:2 }}</div>
                            {% if entry.discount_amount %}
                            <div class="text-sm text-sap-green-600">-₹{{ entry.discount_amount|floatformat:2 }} Discount</div>
                            {% endif %}
                            {% if entry.product_description %}
                            <div class="text-xs text-sap-gray-400">{{ entry.product_description|truncatechars:25 }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-sap-blue-600">{{ entry.vat_rate|default:0 }}% VAT</div>
                            <div class="text-sm text-sap-gray-900">₹{{ entry.vat_amount|default:0|floatformat:2 }}</div>
                            {% if entry.vat_type %}
                            <div class="text-xs text-sap-gray-400">{{ entry.get_vat_type_display }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-sap-gray-900">₹{{ entry.total_amount|default:0|floatformat:2 }}</div>
                            {% if entry.payment_status %}
                            <div class="text-xs">
                                {% if entry.payment_status == 'paid' %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-green-100 text-sap-green-800">Paid</span>
                                {% elif entry.payment_status == 'pending' %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-yellow-100 text-sap-yellow-800">Pending</span>
                                {% else %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-red-100 text-sap-red-800">Overdue</span>
                                {% endif %}
                            </div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <a href="{% url 'accounts:sales_invoice_detail' entry.id %}" 
                                   class="text-sap-red-600 hover:text-sap-red-900" title="View Invoice">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                </a>
                                <a href="{% url 'accounts:sales_invoice_edit' entry.id %}" 
                                   class="text-sap-blue-600 hover:text-sap-blue-900" title="Edit">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </a>
                                <button type="button" onclick="viewVATDetails({{ entry.id }})" 
                                        class="text-sap-purple-600 hover:text-sap-purple-900" title="VAT Details">
                                    <i data-lucide="percent" class="w-4 h-4"></i>
                                </button>
                                <button type="button" onclick="printInvoice({{ entry.id }})" 
                                        class="text-sap-gray-600 hover:text-sap-gray-900" title="Print">
                                    <i data-lucide="printer" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="px-6 py-8 text-center">
                            <div class="text-center">
                                <i data-lucide="tag" class="w-12 h-12 text-sap-gray-400 mx-auto mb-4"></i>
                                <h3 class="text-sm font-medium text-sap-gray-900 mb-2">No sales VAT entries found</h3>
                                <p class="text-sm text-sap-gray-600 mb-4">Sales invoices with VAT will appear here.</p>
                                <a href="{% url 'accounts:sales_invoice_create' %}" 
                                   class="bg-sap-red-600 hover:bg-sap-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                                    Create Sales Invoice
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Register Summary Footer -->
        {% if sales_entries %}
        <div class="px-6 py-4 border-t border-sap-gray-200 bg-sap-gray-50">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <p class="text-sm text-sap-gray-600">Total Taxable Value</p>
                    <p class="text-lg font-bold text-sap-gray-900">₹{{ total_taxable_value|default:0|floatformat:2 }}</p>
                </div>
                <div class="text-center">
                    <p class="text-sm text-sap-gray-600">Total VAT Amount</p>
                    <p class="text-lg font-bold text-sap-red-600">₹{{ total_vat_amount|default:0|floatformat:2 }}</p>
                </div>
                <div class="text-center">
                    <p class="text-sm text-sap-gray-600">Total Invoice Value</p>
                    <p class="text-lg font-bold text-sap-gray-900">₹{{ total_invoice_value|default:0|floatformat:2 }}</p>
                </div>
                <div class="text-center">
                    <p class="text-sm text-sap-gray-600">Number of Entries</p>
                    <p class="text-lg font-bold text-sap-blue-600">{{ sales_entries.count }}</p>
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="px-6 py-4 border-t border-sap-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-sap-gray-700">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} entries
                </div>
                <div class="flex space-x-1">
                    {% if page_obj.has_previous %}
                    <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ page_obj.previous_page_number }}" 
                       class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-l-lg hover:bg-sap-gray-50">
                        Previous
                    </a>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <span class="px-3 py-2 text-sm font-medium text-sap-red-600 bg-sap-red-50 border border-sap-red-300">
                            {{ num }}
                        </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ num }}" 
                           class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 hover:bg-sap-gray-50">
                            {{ num }}
                        </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ page_obj.next_page_number }}" 
                       class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-r-lg hover:bg-sap-gray-50">
                        Next
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function exportSalesVAT() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = '?' + params.toString();
}

function printRegister() {
    window.print();
}

function generateVATReport() {
    alert('Sales VAT report generation functionality would be implemented here.');
}

function viewVATDetails(entryId) {
    window.open(`/accounts/vat/sales/${entryId}/details/`, '_blank');
}

function printInvoice(entryId) {
    window.open(`/accounts/invoices/sales/${entryId}/print/`, '_blank');
}

// Initialize lucide icons on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %>