<!-- sales_distribution/templates/sales_distribution/partials/wotypes_edit_form.html -->
<!-- WO Types Inline Edit Form - Used by HTMX for inline editing -->

<tr class="bg-sap-blue-50 border-l-4 border-sap-blue-500" id="row-{{ category.cid }}">
    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-sap-gray-900">
        {{ category.cid }}
    </td>
    <td class="px-6 py-4 whitespace-nowrap">
        <form hx-put="{% url 'sales_distribution:wo_types_update' category.cid %}"
              hx-target="#row-{{ category.cid }}"
              hx-swap="outerHTML"
              class="flex items-center space-x-2">
            {% csrf_token %}
            <input type="text" 
                   name="cname" 
                   value="{{ category.cname }}"
                   class="flex-1 px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"
                   required>
        </form>
    </td>
    <td class="px-6 py-4 whitespace-nowrap">
        {% if category.is_used %}
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                In Use
            </span>
        {% else %}
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                <i data-lucide="circle" class="w-3 h-3 mr-1"></i>
                Available
            </span>
        {% endif %}
    </td>
    <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
        <div class="flex items-center justify-center space-x-2">
            <!-- Save Button -->
            <button type="submit" 
                    onclick="this.closest('form').dispatchEvent(new Event('submit', {bubbles: true}));"
                    class="inline-flex items-center px-2 py-1 text-xs font-medium text-green-600 hover:text-green-800 transition-colors duration-200"
                    title="Save Changes">
                <i data-lucide="check" class="w-4 h-4"></i>
            </button>
            
            <!-- Cancel Button -->
            <button type="button" 
                    hx-get="{% url 'sales_distribution:wo_types_list' %}"
                    hx-target="#row-{{ category.cid }}"
                    hx-swap="outerHTML"
                    class="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-600 hover:text-gray-800 transition-colors duration-200"
                    title="Cancel Edit">
                <i data-lucide="x" class="w-4 h-4"></i>
            </button>
        </div>
    </td>
</tr>