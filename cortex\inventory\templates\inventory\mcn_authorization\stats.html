{% extends 'core/base.html' %}
{% load custom_filters %}

{% block title %}MCN Authorization Statistics{% endblock %}

{% block extra_css %}
<style>
    .stats-dashboard {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .stats-card {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    
    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .stats-value {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
        line-height: 1;
    }
    
    .stats-label {
        font-size: 0.875rem;
        color: #6c757d;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .stats-change {
        font-size: 0.75rem;
        margin-top: 0.5rem;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        display: inline-block;
    }
    
    .stats-change.positive {
        background: #d4edda;
        color: #155724;
    }
    
    .stats-change.neutral {
        background: #e2e3e5;
        color: #6c757d;
    }
    
    .primary-stat .stats-icon {
        background: linear-gradient(135deg, #0070f3 0%, #0051cc 100%);
        color: white;
    }
    
    .primary-stat .stats-value {
        color: #0070f3;
    }
    
    .success-stat .stats-icon {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        color: white;
    }
    
    .success-stat .stats-value {
        color: #28a745;
    }
    
    .warning-stat .stats-icon {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        color: white;
    }
    
    .warning-stat .stats-value {
        color: #ffc107;
    }
    
    .info-stat .stats-icon {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        color: white;
    }
    
    .info-stat .stats-value {
        color: #17a2b8;
    }
    
    .chart-section {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .progress-item {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .progress-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
    }
    
    .progress-bar-custom {
        height: 10px;
        border-radius: 5px;
        background: #e9ecef;
        overflow: hidden;
        flex: 1;
        margin: 0 1rem;
    }
    
    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #0070f3 0%, #00d4ff 100%);
        border-radius: 5px;
        transition: width 0.8s ease;
    }
    
    .quick-actions {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
    }
    
    .action-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 1rem;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        background: #f8f9fa;
        color: #495057;
        text-decoration: none;
        transition: all 0.3s ease;
        margin-bottom: 0.5rem;
    }
    
    .action-btn:hover {
        background: #0070f3;
        color: white;
        border-color: #0070f3;
        text-decoration: none;
        transform: translateX(5px);
    }
    
    @media (max-width: 768px) {
        .stats-dashboard {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .stats-value {
            font-size: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">MCN Authorization Statistics</h1>
            <p class="text-muted mb-0">Overview of Material Credit Note authorization activities</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{% url 'inventory:mcn_authorization_list' %}" class="btn btn-outline-primary">
                <i class="bi bi-arrow-left"></i> Back to Authorization
            </a>
            <button type="button" class="btn btn-primary" onclick="refreshStats()">
                <i class="bi bi-arrow-clockwise"></i> Refresh
            </button>
        </div>
    </div>

    <!-- Statistics Dashboard -->
    <div class="stats-dashboard">
        <!-- Total Work Orders -->
        <div class="stats-card primary-stat">
            <div class="stats-icon">
                <i class="bi bi-briefcase"></i>
            </div>
            <div class="stats-value">{{ total_work_orders }}</div>
            <div class="stats-label">Total Work Orders</div>
            <div class="stats-change neutral">
                <i class="bi bi-info-circle"></i> All work orders in system
            </div>
        </div>

        <!-- Work Orders with MCN -->
        <div class="stats-card info-stat">
            <div class="stats-icon">
                <i class="bi bi-file-text"></i>
            </div>
            <div class="stats-value">{{ work_orders_with_mcn }}</div>
            <div class="stats-label">Work Orders with MCN</div>
            <div class="stats-change positive">
                <i class="bi bi-arrow-up"></i> {{ authorization_rate }}% coverage
            </div>
        </div>

        <!-- Total MCN Items -->
        <div class="stats-card success-stat">
            <div class="stats-icon">
                <i class="bi bi-box"></i>
            </div>
            <div class="stats-value">{{ total_mcn_items }}</div>
            <div class="stats-label">Total MCN Items</div>
            <div class="stats-change positive">
                <i class="bi bi-check-circle"></i> Available for authorization
            </div>
        </div>

        <!-- Total MCN Quantity -->
        <div class="stats-card warning-stat">
            <div class="stats-icon">
                <i class="bi bi-stack"></i>
            </div>
            <div class="stats-value">{{ total_mcn_quantity|floatformat:0 }}</div>
            <div class="stats-label">Total MCN Quantity</div>
            <div class="stats-change neutral">
                <i class="bi bi-calculator"></i> Units pending authorization
            </div>
        </div>

        <!-- Today's MCNs -->
        <div class="stats-card primary-stat">
            <div class="stats-icon">
                <i class="bi bi-calendar-today"></i>
            </div>
            <div class="stats-value">{{ today_mcns }}</div>
            <div class="stats-label">Today's MCNs</div>
            <div class="stats-change positive">
                <i class="bi bi-calendar-check"></i> Created today
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="stats-card info-stat">
            <div class="stats-icon">
                <i class="bi bi-clock-history"></i>
            </div>
            <div class="stats-value">{{ recent_mcns }}</div>
            <div class="stats-label">Recent Activity</div>
            <div class="stats-change positive">
                <i class="bi bi-graph-up"></i> Last 7 days
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Authorization Progress -->
        <div class="col-md-8">
            <div class="chart-section">
                <h5 class="mb-4">Authorization Progress</h5>
                
                <div class="progress-item">
                    <div>
                        <div class="progress-label">Work Order Coverage</div>
                        <small class="text-muted">{{ work_orders_with_mcn }} of {{ total_work_orders }} work orders have MCN records</small>
                    </div>
                    <div class="progress-bar-custom">
                        <div class="progress-fill" style="width: {{ authorization_rate }}%"></div>
                    </div>
                    <strong>{{ authorization_rate }}%</strong>
                </div>

                <div class="progress-item">
                    <div>
                        <div class="progress-label">Daily Activity</div>
                        <small class="text-muted">{{ today_mcns }} MCNs created today vs {{ recent_mcns }} in last week</small>
                    </div>
                    <div class="progress-bar-custom">
                        <div class="progress-fill" style="width: {% widthratio today_mcns recent_mcns 100 %}%"></div>
                    </div>
                    <strong>{% widthratio today_mcns recent_mcns 100 %}%</strong>
                </div>

                <div class="progress-item">
                    <div>
                        <div class="progress-label">MCN Items per Work Order</div>
                        <small class="text-muted">Average items per work order with MCN</small>
                    </div>
                    <div class="progress-bar-custom">
                        <div class="progress-fill" style="width: {% if work_orders_with_mcn > 0 %}{% widthratio total_mcn_items work_orders_with_mcn 10 %}{% else %}0{% endif %}%"></div>
                    </div>
                    <strong>{% if work_orders_with_mcn > 0 %}{{ total_mcn_items|div:work_orders_with_mcn|floatformat:1 }}{% else %}0{% endif %}</strong>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-md-4">
            <div class="quick-actions">
                <h5 class="mb-4">Quick Actions</h5>
                
                <a href="{% url 'inventory:mcn_authorization_list' %}" class="action-btn">
                    <i class="bi bi-list-check"></i>
                    <span>View Work Orders</span>
                </a>
                
                <a href="{% url 'project_management:material_credit_note_list' %}" class="action-btn">
                    <i class="bi bi-file-plus"></i>
                    <span>Manage MCNs</span>
                </a>
                
                <a href="#" class="action-btn" onclick="generateReport()">
                    <i class="bi bi-file-earmark-text"></i>
                    <span>Generate Report</span>
                </a>
                
                <a href="#" class="action-btn" onclick="exportData()">
                    <i class="bi bi-download"></i>
                    <span>Export Data</span>
                </a>
                
                <a href="{% url 'project_management:material_credit_note_stats' %}" class="action-btn">
                    <i class="bi bi-graph-up-arrow"></i>
                    <span>MCN Statistics</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Recent Activity Summary -->
    <div class="chart-section">
        <h5 class="mb-4">System Status Summary</h5>
        <div class="row">
            <div class="col-md-4">
                <div class="text-center p-4 border rounded">
                    <h2 class="text-success mb-2">{{ authorization_rate }}%</h2>
                    <p class="text-muted mb-0">Authorization Coverage</p>
                    <small class="text-muted">Work orders with MCN records</small>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-center p-4 border rounded">
                    <h2 class="text-primary mb-2">{% if work_orders_with_mcn > 0 %}{{ total_mcn_items|div:work_orders_with_mcn|floatformat:1 }}{% else %}0{% endif %}</h2>
                    <p class="text-muted mb-0">Avg Items/WO</p>
                    <small class="text-muted">MCN items per work order</small>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-center p-4 border rounded">
                    <h2 class="text-info mb-2">{% if total_mcn_items > 0 %}{{ total_mcn_quantity|div:total_mcn_items|floatformat:1 }}{% else %}0{% endif %}</h2>
                    <p class="text-muted mb-0">Avg Quantity/Item</p>
                    <small class="text-muted">Average MCN quantity per item</small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshStats() {
    // Show loading indicator
    const refreshBtn = document.querySelector('button[onclick="refreshStats()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Refreshing...';
    refreshBtn.disabled = true;
    
    // Reload the page after a short delay
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

function generateReport() {
    alert('Report generation feature will be implemented in the next phase.');
}

function exportData() {
    alert('Data export feature will be implemented in the next phase.');
}

// Add spinning animation for refresh button
document.addEventListener('DOMContentLoaded', function() {
    const style = document.createElement('style');
    style.textContent = `
        .spin {
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(style);
    
    // Animate progress bars on load
    setTimeout(() => {
        document.querySelectorAll('.progress-fill').forEach(fill => {
            const width = fill.style.width;
            fill.style.width = '0%';
            setTimeout(() => {
                fill.style.width = width;
            }, 100);
        });
    }, 500);
});
</script>
{% endblock %}