from django.shortcuts import render, get_object_or_404
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.views.generic import ListView, CreateView, DetailView, DeleteView
from django.urls import reverse_lazy
from django.http import HttpResponse, Http404, JsonResponse
from django.core.paginator import Paginator
from core.context_processors.company_context import company_context
from .models import MROffice, ModuleMaster
from .forms import MROfficeForm, MROfficeSearchForm
import datetime


class MROfficeListView(LoginRequiredMixin, ListView):
    """List view for MR Office documents."""
    model = MROffice
    template_name = 'mr_office/document_list.html'
    context_object_name = 'documents'
    paginate_by = 20

    def get_queryset(self):
        """Filter documents by company and search criteria."""
        company_context = company_context(self.request)
        company_id = company_context.get('company_id', 1)
        
        queryset = MROffice.objects.filter(company_id=company_id).order_by('-id')
        
        # Handle search
        module_filter = self.request.GET.get('module')
        format_search = self.request.GET.get('format_search')
        
        if module_filter:
            queryset = queryset.filter(formodule=module_filter)
        
        if format_search:
            queryset = queryset.filter(format__icontains=format_search)
        
        return queryset

    def get_context_data(self, **kwargs):
        """Add search form and modules to context."""
        context = super().get_context_data(**kwargs)
        context['search_form'] = MROfficeSearchForm(self.request.GET)
        context['modules'] = ModuleMaster.objects.all()
        
        # Add module names to documents
        for document in context['documents']:
            document.module_name = document.get_module_name()
        
        return context


class MROfficeCreateView(LoginRequiredMixin, CreateView):
    """Create view for MR Office documents."""
    model = MROffice
    form_class = MROfficeForm
    template_name = 'mr_office/document_form.html'
    success_url = reverse_lazy('mr_office:document_list')

    def form_valid(self, form):
        """Save document with file data."""
        # Get company context
        company_context = company_context(self.request)
        
        # Set system fields
        now = datetime.datetime.now()
        form.instance.sysdate = now.strftime('%d-%m-%Y')
        form.instance.systime = now.strftime('%H:%M:%S')
        form.instance.company_id = company_context.get('company_id', 1)
        form.instance.finyear_id = company_context.get('financial_year_id', 1)
        form.instance.session = self.request.user
        
        # Handle file upload
        attachment = form.cleaned_data.get('attachment')
        if attachment:
            form.instance.filename = attachment.name
            form.instance.size = str(attachment.size)
            form.instance.contenttype = attachment.content_type
            form.instance.data = attachment.read()
        
        # Save formodule as integer
        form.instance.formodule = form.cleaned_data['formodule'].modid
        
        response = super().form_valid(form)
        messages.success(self.request, 'Document uploaded successfully!')
        return response

    def get_context_data(self, **kwargs):
        """Add modules to context."""
        context = super().get_context_data(**kwargs)
        context['modules'] = ModuleMaster.objects.all()
        return context


class MROfficeDetailView(LoginRequiredMixin, DetailView):
    """Detail view for MR Office documents."""
    model = MROffice
    template_name = 'mr_office/document_detail.html'
    context_object_name = 'document'

    def get_queryset(self):
        """Filter by company."""
        company_context = company_context(self.request)
        company_id = company_context.get('company_id', 1)
        return MROffice.objects.filter(company_id=company_id)

    def get_context_data(self, **kwargs):
        """Add module name to context."""
        context = super().get_context_data(**kwargs)
        context['module_name'] = self.object.get_module_name()
        return context


class MROfficeDeleteView(LoginRequiredMixin, DeleteView):
    """Delete view for MR Office documents."""
    model = MROffice
    template_name = 'mr_office/document_confirm_delete.html'
    success_url = reverse_lazy('mr_office:document_list')

    def get_queryset(self):
        """Filter by company."""
        company_context = company_context(self.request)
        company_id = company_context.get('company_id', 1)
        return MROffice.objects.filter(company_id=company_id)

    def delete(self, request, *args, **kwargs):
        """Delete with success message."""
        response = super().delete(request, *args, **kwargs)
        messages.success(request, 'Document deleted successfully!')
        return response


def download_document(request, pk):
    """Download document file."""
    company_context = company_context(request)
    company_id = company_context.get('company_id', 1)
    
    document = get_object_or_404(MROffice, pk=pk, company_id=company_id)
    
    if not document.data or not document.filename:
        raise Http404("File not found")
    
    response = HttpResponse(document.data, content_type=document.contenttype)
    response['Content-Disposition'] = f'attachment; filename="{document.filename}"'
    
    return response


def search_documents(request):
    """HTMX endpoint for document search."""
    company_context = company_context(request)
    company_id = company_context.get('company_id', 1)
    
    queryset = MROffice.objects.filter(company_id=company_id).order_by('-id')
    
    # Handle search
    module_filter = request.GET.get('module')
    format_search = request.GET.get('format_search')
    
    if module_filter:
        queryset = queryset.filter(formodule=module_filter)
    
    if format_search:
        queryset = queryset.filter(format__icontains=format_search)
    
    # Paginate results
    paginator = Paginator(queryset, 20)
    page_number = request.GET.get('page', 1)
    page_obj = paginator.get_page(page_number)
    
    # Add module names
    for document in page_obj:
        document.module_name = document.get_module_name()
    
    return render(request, 'mr_office/partials/document_table.html', {
        'documents': page_obj,
        'page_obj': page_obj
    })


def document_stats(request):
    """HTMX endpoint for document statistics."""
    company_context = company_context(request)
    company_id = company_context.get('company_id', 1)
    
    total_documents = MROffice.objects.filter(company_id=company_id).count()
    modules_count = MROffice.objects.filter(company_id=company_id).values('formodule').distinct().count()
    
    # Calculate total size
    total_size = 0
    for doc in MROffice.objects.filter(company_id=company_id):
        if doc.size and doc.size.isdigit():
            total_size += int(doc.size)
    
    # Convert to MB
    total_size_mb = round(total_size / (1024 * 1024), 2)
    
    return JsonResponse({
        'total_documents': total_documents,
        'modules_count': modules_count,
        'total_size_mb': total_size_mb
    })