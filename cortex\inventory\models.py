from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from sys_admin.models import Company, FinancialYear


# Task Group 1: Location & Setup Management Models

class ItemLocation(models.Model):
    """Item location management for warehouse, zone, and bin organization"""
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.CharField(db_column="SysDate", max_length=50, blank=True, null=True)
    systime = models.CharField(db_column="SysTime", max_length=50, blank=True, null=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId", blank=True, null=True)
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId", blank=True, null=True)
    sessionid = models.CharField(db_column="SessionId", max_length=50, blank=True, null=True)
    location_label = models.Cha<PERSON><PERSON><PERSON>(db_column="LocationLabel", max_length=50, blank=True, null=True)
    location_no = models.CharField(db_column="LocationNo", max_length=50, blank=True, null=True)
    description = models.TextField(db_column="Description", blank=True, null=True)
    
    class Meta:
        managed = False
        db_table = "tblDG_Location_Master"
        ordering = ['location_no']
        
    def __str__(self):
        return f"{self.location_no} - {self.description}"
        
    @property
    def full_location_path(self):
        """Get full hierarchical location path"""
        return f"{self.location_label}-{self.location_no}"


class WISTimeConfiguration(models.Model):
    """WIS (Warehouse Issue System) time configuration for automated processing"""
    id = models.AutoField(db_column="Id", primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId", blank=True, null=True)
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    auto_issue_time = models.CharField(db_column="TimeAuto", max_length=10, blank=True, null=True, help_text="Time for automatic issue processing")
    time_to_order = models.CharField(db_column="TimeToOrder", max_length=10, blank=True, null=True, help_text="Time to order processing")
    
    class Meta:
        managed = False
        db_table = "tblinv_AutoWIS_TimeSchedule"
        ordering = ['id']
        
    def __str__(self):
        return f"WIS Timer - {self.auto_issue_time}"
    
    @property
    def config_name(self):
        """Return a display name for backward compatibility"""
        return f"Auto WIS Schedule {self.id}"


# Task Group 2: Material Requisition Management Models

# Actual Database Models (matching real schema)
class MaterialRequisitionSlip(models.Model):
    """Material Requisition Slip (MRS) - matches actual tblInv_MaterialRequisition_Master"""
    id = models.AutoField(db_column="Id", primary_key=True)
    sys_date = models.CharField(db_column="SysDate", max_length=50)  # Stored as text: "2013-05-14"
    sys_time = models.CharField(db_column="SysTime", max_length=50)  # Stored as text: "3:45:21 PM"
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    session_id = models.CharField(db_column="SessionId", max_length=50)  # Employee ID like "Sapl0068"
    mrs_number = models.CharField(db_column="MRSNo", max_length=50, blank=True, null=True)  # "0001", "0002"

    class Meta:
        managed = False
        db_table = "tblInv_MaterialRequisition_Master"
        ordering = ['-id']

    def __str__(self):
        return f"{self.mrs_number} - {self.sys_date}"

    @property
    def display_date(self):
        """Get formatted display date"""
        return self.sys_date if self.sys_date else ''
        
    @property 
    def display_time(self):
        """Get formatted display time"""
        return self.sys_time if self.sys_time else ''
    
    @property
    def total_estimated_value(self):
        """Calculate total estimated value of all line items"""
        from django.db.models import Sum, F
        return self.details.aggregate(
            total=Sum(F('requested_quantity') * F('estimated_rate'))
        )['total'] or 0
    
    @property
    def total_approved_value(self):
        """Calculate total approved value"""
        from django.db.models import Sum, F
        return self.details.aggregate(
            total=Sum(F('approved_quantity') * F('estimated_rate'))
        )['total'] or 0
    
    def can_be_submitted(self):
        """Check if MRS can be submitted for approval"""
        return (
            self.status == 'DRAFT' and 
            self.details.exists() and
            all(item.requested_quantity > 0 for item in self.details.all())
        )
    
    def can_be_cancelled(self):
        """Check if MRS can be cancelled"""
        return self.status not in ['ISSUED', 'CANCELLED']
    
    def validate_line_items(self):
        """Validate all line items have required data"""
        errors = []
        if not self.details.exists():
            errors.append("MRS must have at least one line item")
        
        for item in self.details.all():
            if not item.item_code:
                errors.append(f"Line {item.id}: Item code is required")
            if not item.requested_quantity or item.requested_quantity <= 0:
                errors.append(f"Line {item.id}: Requested quantity must be greater than zero")
        
        return errors
    
    def check_stock_availability(self):
        """Check stock availability for all requested items"""
        availability_info = []
        for item in self.details.all():
            # This would integrate with actual stock calculation
            # For now, returning placeholder data
            availability_info.append({
                'item_code': item.item_code,
                'requested_qty': item.requested_quantity,
                'available_stock': 100,  # Placeholder
                'stock_sufficient': True  # Placeholder
            })
        return availability_info


class MRSLineItem(models.Model):
    """Material Requisition Slip line items - matches actual tblInv_MaterialRequisition_Details"""
    id = models.AutoField(db_column="Id", primary_key=True)
    mrs = models.ForeignKey(MaterialRequisitionSlip, models.CASCADE, db_column="MId", related_name='details')
    mrs_number = models.CharField(db_column="MRSNo", max_length=50, blank=True, null=True)  # Redundant with master
    item_id = models.CharField(db_column="ItemId", max_length=50, blank=True, null=True)  # FK to tblDG_Item_Master.Id (stored as text)
    department_id = models.IntegerField(db_column="DeptId", blank=True, null=True)  # FK to tblHR_Departments.Id
    work_order_number = models.CharField(db_column="WONo", max_length=50, blank=True, null=True)
    requested_quantity = models.FloatField(db_column="ReqQty", blank=True, null=True)
    remarks = models.TextField(db_column="Remarks", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_MaterialRequisition_Details"
        ordering = ['id']

    def __str__(self):
        return f"{self.mrs.mrs_number} - {self.item_id}"

    @property
    def remaining_quantity(self):
        """Calculate remaining quantity to be issued"""
        return max(0, self.approved_quantity - self.issued_quantity)

    @property
    def issue_percentage(self):
        """Calculate percentage of quantity issued"""
        if self.approved_quantity > 0:
            return (self.issued_quantity / self.approved_quantity) * 100
        return 0

    def update_pending_quantity(self):
        """Update pending quantity based on approved and issued quantities"""
        self.pending_quantity = max(0, self.approved_quantity - self.issued_quantity)
        self.save(update_fields=['pending_quantity'])

    def update_line_status(self):
        """Update line status based on issued quantity"""
        if self.approved_quantity <= 0:
            self.line_status = 'CANCELLED'
        elif self.issued_quantity >= self.approved_quantity:
            self.line_status = 'ISSUED'
        elif self.issued_quantity > 0:
            self.line_status = 'PARTIALLY_ISSUED'
        else:
            self.line_status = 'APPROVED' if self.approved_quantity > 0 else 'PENDING'
        
        self.save(update_fields=['line_status'])


class MRSApprovalHistory(models.Model):
    """MRS approval workflow history"""
    id = models.AutoField(db_column="Id", primary_key=True)
    mrs = models.ForeignKey(MaterialRequisitionSlip, models.CASCADE, db_column="MRSId", related_name='approval_history')
    approver = models.ForeignKey(User, models.DO_NOTHING, db_column="ApproverId")
    approval_level = models.IntegerField(db_column="ApprovalLevel", default=1)
    action = models.CharField(db_column="Action", max_length=20,
                            choices=[('SUBMITTED', 'Submitted'), ('APPROVED', 'Approved'), 
                                   ('REJECTED', 'Rejected'), ('CANCELLED', 'Cancelled')])
    comments = models.TextField(db_column="Comments", blank=True, null=True)
    action_date = models.DateTimeField(db_column="ActionDate", auto_now_add=True)

    class Meta:
        managed = False
        db_table = "tblInv_MRSApprovalHistory"
        ordering = ['-action_date']

    def __str__(self):
        return f"{self.mrs.mrs_number} - {self.action} by {self.approver.username}"


# Task Group 3: Material Issue & Return Management Models

class MaterialIssueNote(models.Model):
    """Material Issue Note (MIN) for issuing materials against MRS"""
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.CharField(db_column="SysDate", max_length=50, blank=True, null=True)
    systime = models.CharField(db_column="SysTime", max_length=50, blank=True, null=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    sessionid = models.CharField(db_column="SessionId", max_length=50, blank=True, null=True)
    min_no = models.CharField(db_column="MINNo", max_length=50, blank=True, null=True)
    mrs_no = models.CharField(db_column="MRSNo", max_length=50, blank=True, null=True)
    mrs_id = models.IntegerField(db_column="MRSId", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_MaterialIssue_Master"
        ordering = ['-id']

    def __str__(self):
        return f"{self.min_no} - {self.sysdate}"


class MINLineItem(models.Model):
    """Material Issue Note line items"""
    id = models.AutoField(db_column="Id", primary_key=True)
    min = models.ForeignKey(MaterialIssueNote, models.CASCADE, db_column="MId", related_name='details')
    min_no = models.CharField(db_column="MINNo", max_length=50, blank=True, null=True)
    mrs_id = models.IntegerField(db_column="MRSId", blank=True, null=True)
    issue_qty = models.FloatField(db_column="IssueQty", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_MaterialIssue_Details"
        ordering = ['id']

    def __str__(self):
        return f"{self.min_no} - Item {self.id}"


class MaterialReturnNote(models.Model):
    """Material Return Note (MRN) for returning materials back to inventory"""
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.CharField(db_column="SysDate", max_length=50)
    systime = models.CharField(db_column="SysTime", max_length=50)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    sessionid = models.CharField(db_column="SessionId", max_length=50)
    mrn_no = models.CharField(db_column="MRNNo", max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_MaterialReturn_Master"
        ordering = ['-id']

    def __str__(self):
        return f"{self.mrn_no} - {self.sysdate}"

    @property
    def total_items(self):
        """Get total number of line items"""
        return self.details.count()

    @property
    def total_returned_quantity(self):
        """Get total returned quantity across all line items"""
        from django.db.models import Sum
        return self.details.aggregate(total=Sum('returned_quantity'))['total'] or 0

    @property
    def total_return_value(self):
        """Get total return value across all line items"""
        from django.db.models import Sum
        return self.details.aggregate(total=Sum('return_value'))['total'] or 0

    @property
    def can_be_edited(self):
        """Check if MRN can be edited"""
        return self.status == 'DRAFT'

    def generate_mrn_number(self):
        """Generate unique MRN number"""
        if not self.mrn_number:
            from django.utils import timezone
            current_year = timezone.now().year
            prefix = f"MRN/{current_year}/{self.company.id:03d}/"
            
            last_mrn = MaterialReturnNote.objects.filter(
                company=self.company,
                mrn_number__startswith=prefix
            ).order_by('-id').first()
            
            if last_mrn:
                try:
                    last_num = int(last_mrn.mrn_number.split('/')[-1])
                    new_num = last_num + 1
                except (ValueError, IndexError):
                    new_num = 1
            else:
                new_num = 1
            
            self.mrn_number = f"{prefix}{new_num:05d}"


class MRNLineItem(models.Model):
    """Material Return Note line items"""
    id = models.AutoField(db_column="Id", primary_key=True)
    mrn = models.ForeignKey(MaterialReturnNote, models.CASCADE, db_column="MRNId", related_name='details')
    min_line_item = models.ForeignKey(MINLineItem, models.CASCADE, db_column="MINLineItemId", related_name='return_details', blank=True, null=True)
    item_code = models.CharField(db_column="ItemCode", max_length=50)
    item_description = models.CharField(db_column="ItemDescription", max_length=500)
    unit_of_measure = models.CharField(db_column="UnitOfMeasure", max_length=20)
    issued_quantity = models.FloatField(db_column="IssuedQuantity", default=0.0)
    returned_quantity = models.FloatField(db_column="ReturnedQuantity")
    return_rate = models.FloatField(db_column="ReturnRate", blank=True, null=True)
    return_value = models.FloatField(db_column="ReturnValue", blank=True, null=True)
    location_code = models.CharField(db_column="LocationCode", max_length=50, blank=True, null=True)
    batch_number = models.CharField(db_column="BatchNumber", max_length=50, blank=True, null=True)
    serial_number = models.CharField(db_column="SerialNumber", max_length=100, blank=True, null=True)
    condition_on_return = models.CharField(db_column="ConditionOnReturn", max_length=20,
                                         choices=[('GOOD', 'Good'), ('FAIR', 'Fair'), ('POOR', 'Poor'), ('DAMAGED', 'Damaged')],
                                         default='GOOD')
    return_reason = models.CharField(db_column="ReturnReason", max_length=20,
                                   choices=[('UNUSED', 'Unused'), ('EXCESS', 'Excess'), ('DEFECTIVE', 'Defective'), 
                                          ('EXPIRED', 'Expired'), ('COMPLETED', 'Work Completed'), ('OTHER', 'Other')],
                                   default='UNUSED')
    condition_notes = models.TextField(db_column="ConditionNotes", blank=True, null=True)
    action_required = models.CharField(db_column="ActionRequired", max_length=20,
                                     choices=[('RESTOCK', 'Restock'), ('REPAIR', 'Send for Repair'), 
                                            ('SCRAP', 'Scrap'), ('REWORK', 'Rework Required')],
                                     default='RESTOCK')
    created_date = models.DateTimeField(db_column="CreatedDate", auto_now_add=True)

    class Meta:
        managed = False
        db_table = "tblInv_MRNLineItem"
        ordering = ['id']

    def __str__(self):
        return f"{self.mrn.mrn_number} - {self.item_code}"

    @property
    def return_percentage_of_issued(self):
        """Calculate percentage of issued quantity being returned"""
        if self.issued_quantity > 0:
            return (self.returned_quantity / self.issued_quantity) * 100
        return 0

    def calculate_return_value(self):
        """Calculate return value based on quantity and rate"""
        if self.return_rate:
            self.return_value = self.returned_quantity * self.return_rate
            self.save(update_fields=['return_value'])


# Task Group 5: Goods Inward Processing Models

class GoodsInwardNote(models.Model):
    """Goods Inward Note (GIN) for recording incoming materials from suppliers"""
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.CharField(db_column="SysDate", max_length=50)
    systime = models.CharField(db_column="SysTime", max_length=50)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    sessionid = models.CharField(db_column="SessionId", max_length=50)
    gin_number = models.CharField(db_column="GINNo", max_length=50, blank=True, null=True)
    po_number = models.CharField(db_column="PONo", max_length=50, blank=True, null=True)
    pom_id = models.IntegerField(db_column="POMId", blank=True, null=True)
    challan_number = models.CharField(db_column="ChallanNo", max_length=50, blank=True, null=True)
    challan_date = models.CharField(db_column="ChallanDate", max_length=50, blank=True, null=True)
    gate_entry_number = models.CharField(db_column="GateEntryNo", max_length=50, blank=True, null=True)
    gin_date = models.CharField(db_column="GDate", max_length=50, blank=True, null=True)
    gin_time = models.CharField(db_column="GTime", max_length=50, blank=True, null=True)
    mode_of_transport = models.CharField(db_column="ModeofTransport", max_length=100, blank=True, null=True)
    vehicle_number = models.CharField(db_column="VehicleNo", max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_Inward_Master"
        ordering = ['-id']

    def __str__(self):
        return f"{self.gin_number} - {self.gin_date}"

    @property
    def can_be_edited(self):
        """Check if GIN can be edited - status field not available"""
        return True  # Status field not available in database

    @property
    def can_be_processed(self):
        """Check if GIN can be processed to next stage - status field not available"""
        return True  # Status and details fields not available in database

    def generate_gin_number(self):
        """Generate unique GIN number"""
        if not self.gin_number:
            from django.utils import timezone
            current_year = timezone.now().year
            prefix = f"GIN/{current_year}/{self.company.id:03d}/"
            
            last_gin = GoodsInwardNote.objects.filter(
                company=self.company,
                gin_number__startswith=prefix
            ).order_by('-id').first()
            
            if last_gin:
                try:
                    last_num = int(last_gin.gin_number.split('/')[-1])
                    new_num = last_num + 1
                except (ValueError, IndexError):
                    new_num = 1
            else:
                new_num = 1
            
            self.gin_number = f"{prefix}{new_num:05d}"


class GINLineItem(models.Model):
    """Goods Inward Note line items"""
    id = models.AutoField(db_column="Id", primary_key=True)
    gin_number = models.CharField(db_column="GINNo", max_length=50, blank=True, null=True)
    gin_id = models.IntegerField(db_column="GINId", blank=True, null=True)
    po_id = models.IntegerField(db_column="POId", blank=True, null=True)
    quantity = models.FloatField(db_column="Qty", blank=True, null=True)
    received_quantity = models.FloatField(db_column="ReceivedQty", blank=True, null=True)
    acategory_id = models.IntegerField(db_column="ACategoyId", blank=True, null=True)
    asubcategory_id = models.IntegerField(db_column="ASubCategoyId", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_Inward_Details"
        ordering = ['id']

    def __str__(self):
        return f"GIN Detail {self.id} - {self.gin_number}"


class GoodsReceivedReceipt(models.Model):
    """Goods Received Receipt (GRR) for confirming receipt and quality check"""
    id = models.AutoField(db_column="Id", primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    grr_number = models.CharField(db_column="GRRNumber", max_length=50, unique=True)
    grr_date = models.DateField(db_column="GRRDate")
    gin = models.ForeignKey(GoodsInwardNote, models.CASCADE, db_column="GINId", related_name='receipts')
    tax_invoice_number = models.CharField(db_column="TaxInvoiceNumber", max_length=50, blank=True, null=True)
    tax_invoice_date = models.DateField(db_column="TaxInvoiceDate", blank=True, null=True)
    quality_checked_by = models.ForeignKey(User, models.DO_NOTHING, db_column="QualityCheckedBy", related_name='grr_quality_checked', blank=True, null=True)
    quality_check_date = models.DateTimeField(db_column="QualityCheckDate", blank=True, null=True)
    approved_by = models.ForeignKey(User, models.DO_NOTHING, db_column="ApprovedBy", related_name='grr_approved', blank=True, null=True)
    approved_date = models.DateTimeField(db_column="ApprovedDate", blank=True, null=True)
    status = models.CharField(db_column="Status", max_length=20,
                            choices=[('DRAFT', 'Draft'), ('QUALITY_PENDING', 'Quality Pending'), 
                                   ('QUALITY_COMPLETED', 'Quality Completed'), ('APPROVED', 'Approved'), 
                                   ('REJECTED', 'Rejected'), ('CANCELLED', 'Cancelled')],
                            default='DRAFT')
    total_accepted_quantity = models.FloatField(db_column="TotalAcceptedQuantity", default=0.0)
    total_rejected_quantity = models.FloatField(db_column="TotalRejectedQuantity", default=0.0)
    total_accepted_value = models.FloatField(db_column="TotalAcceptedValue", default=0.0)
    vat_applicable = models.BooleanField(db_column="VATApplicable", default=False)
    vat_amount = models.FloatField(db_column="VATAmount", default=0.0)
    remarks = models.TextField(db_column="Remarks", blank=True, null=True)
    created_date = models.DateTimeField(db_column="CreatedDate", auto_now_add=True)
    created_by = models.ForeignKey(User, models.DO_NOTHING, db_column="CreatedBy", related_name='grr_created')
    modified_date = models.DateTimeField(db_column="ModifiedDate", auto_now=True)
    modified_by = models.ForeignKey(User, models.DO_NOTHING, db_column="ModifiedBy", related_name='grr_modified', blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_GoodsReceivedReceipt"
        ordering = ['-grr_date', '-id']

    def __str__(self):
        return f"{self.grr_number} - {self.grr_date}"

    @property
    def total_received_quantity(self):
        """Get total received quantity from GIN"""
        return self.gin.total_quantity

    @property
    def acceptance_rate(self):
        """Calculate overall acceptance rate"""
        if self.total_received_quantity > 0:
            return (self.total_accepted_quantity / self.total_received_quantity) * 100
        return 0

    @property
    def can_be_approved(self):
        """Check if GRR can be approved (status field not available)"""
        # return self.status == 'QUALITY_COMPLETED' and self.details.exists()
        return self.details.exists()  # Status field not available, so check details only

    def generate_grr_number(self):
        """Generate unique GRR number"""
        if not self.grr_number:
            from django.utils import timezone
            current_year = timezone.now().year
            prefix = f"GRR/{current_year}/{self.company.id:03d}/"
            
            last_grr = GoodsReceivedReceipt.objects.filter(
                company=self.company,
                grr_number__startswith=prefix
            ).order_by('-id').first()
            
            if last_grr:
                try:
                    last_num = int(last_grr.grr_number.split('/')[-1])
                    new_num = last_num + 1
                except (ValueError, IndexError):
                    new_num = 1
            else:
                new_num = 1
            
            self.grr_number = f"{prefix}{new_num:05d}"


class GRRLineItem(models.Model):
    """Goods Received Receipt line items"""
    id = models.AutoField(db_column="Id", primary_key=True)
    grr = models.ForeignKey(GoodsReceivedReceipt, models.CASCADE, db_column="GRRId", related_name='details')
    gin_line_item = models.ForeignKey(GINLineItem, models.CASCADE, db_column="GINLineItemId", related_name='receipt_details', blank=True, null=True)
    received_quantity = models.DecimalField(db_column="ReceivedQuantity", max_digits=15, decimal_places=4)
    accepted_quantity = models.DecimalField(db_column="AcceptedQuantity", max_digits=15, decimal_places=4)
    rejected_quantity = models.DecimalField(db_column="RejectedQuantity", max_digits=15, decimal_places=4, default=0.0)
    rate = models.DecimalField(db_column="UnitRate", max_digits=15, decimal_places=2, blank=True, null=True)
    accepted_value = models.DecimalField(db_column="AcceptedValue", max_digits=15, decimal_places=2, blank=True, null=True)
    quality_remarks = models.TextField(db_column="QualityRemarks", blank=True, null=True)
    status = models.CharField(db_column="Status", max_length=20, default='PENDING')
    created_date = models.DateTimeField(db_column="CreatedDate", auto_now_add=True)
    modified_date = models.DateTimeField(db_column="ModifiedDate", auto_now=True)

    class Meta:
        managed = False
        db_table = "tblInv_GoodsReceivedReceipt_Details"
        ordering = ['id']

    def __str__(self):
        return f"{self.grr.grr_number} - Line {self.id}"

    @property
    def acceptance_percentage(self):
        """Calculate acceptance percentage for this line"""
        if self.received_quantity > 0:
            return (self.accepted_quantity / self.received_quantity) * 100
        return 0

    def calculate_accepted_value(self):
        """Calculate accepted value based on quantity and rate"""
        if self.rate:
            self.accepted_value = self.accepted_quantity * self.rate
            self.save(update_fields=['accepted_value'])


# Task Group 7: Challan Management Models

class CustomerChallanMaster(models.Model):
    """Customer Challan Master based on existing database schema"""
    id = models.AutoField(db_column="Id", primary_key=True)
    sys_date = models.CharField(db_column="SysDate", max_length=20, blank=True, null=True)
    sys_time = models.CharField(db_column="SysTime", max_length=20, blank=True, null=True)
    session_id = models.CharField(db_column="SessionId", max_length=50, blank=True, null=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId", blank=True, null=True)
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId", blank=True, null=True)
    challan_number = models.CharField(db_column="CCNo", max_length=50, blank=True, null=True)
    customer_id = models.CharField(db_column="CustomerId", max_length=50, blank=True, null=True)
    work_order_number = models.CharField(db_column="WONo", max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_Customer_Challan_Master"
        ordering = ['-id']

    def __str__(self):
        return f"{self.challan_number or 'N/A'} - Customer {self.customer_id or 'N/A'}"
    
    @property
    def status(self):
        """Computed status property for compatibility"""
        return 'ACTIVE' if self.challan_number else 'DRAFT'
    
    @property
    def challan_date(self):
        """Parse sys_date to a date object if possible"""
        if self.sys_date:
            try:
                from datetime import datetime
                return datetime.strptime(self.sys_date, '%d-%m-%Y').date()
            except:
                return None
        return None

    @property
    def line_items(self):
        """Get related line items"""
        try:
            return CustomerChallanDetails.objects.filter(challan_master_id=self.id)
        except:
            return CustomerChallanDetails.objects.none()
    
    @property
    def customer_name(self):
        """Get customer name - placeholder for backward compatibility"""
        return f"Customer {self.customer_id}" if self.customer_id else "Unknown Customer"


class CustomerChallanDetails(models.Model):
    """Customer Challan Details based on actual database schema"""
    id = models.AutoField(db_column="Id", primary_key=True)
    challan_master = models.ForeignKey(CustomerChallanMaster, models.DO_NOTHING, db_column="MId")
    challan_quantity = models.FloatField(db_column="ChallanQty", blank=True, null=True)
    item = models.ForeignKey('design.Item', models.DO_NOTHING, db_column="ItemId", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_Customer_Challan_Details"
        ordering = ['id']

    def __str__(self):
        return f"Challan {self.challan_master_id} - Item {self.item_id} - Qty {self.challan_quantity}"
    
    @property
    def item_code(self):
        """Get item code from related item"""
        return self.item.item_code if self.item else "N/A"
    
    @property
    def item_description(self):
        """Get item description from related item"""
        return self.item.item_description if self.item else "N/A"



class SupplierChallanMaster(models.Model):
    """Modern Supplier Challan for incoming material tracking"""
    id = models.AutoField(db_column="Id", primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    challan_number = models.CharField(db_column="ChallanNumber", max_length=50, unique=True)
    challan_date = models.DateField(db_column="ChallanDate")
    supplier_id = models.IntegerField(db_column="SupplierId", blank=True, null=True)
    supplier_name = models.CharField(db_column="SupplierName", max_length=200)
    supplier_address = models.TextField(db_column="SupplierAddress", blank=True, null=True)
    po_number = models.CharField(db_column="PONumber", max_length=50, blank=True, null=True)
    po_date = models.DateField(db_column="PODate", blank=True, null=True)
    invoice_number = models.CharField(db_column="InvoiceNumber", max_length=50, blank=True, null=True)
    invoice_date = models.DateField(db_column="InvoiceDate", blank=True, null=True)
    vehicle_number = models.CharField(db_column="VehicleNumber", max_length=50, blank=True, null=True)
    driver_name = models.CharField(db_column="DriverName", max_length=100, blank=True, null=True)
    driver_mobile = models.CharField(db_column="DriverMobile", max_length=15, blank=True, null=True)
    transporter_name = models.CharField(db_column="TransporterName", max_length=200, blank=True, null=True)
    gate_entry_number = models.CharField(db_column="GateEntryNumber", max_length=50, blank=True, null=True)
    gate_entry_time = models.DateTimeField(db_column="GateEntryTime", blank=True, null=True)
    expected_delivery_date = models.DateField(db_column="ExpectedDeliveryDate", blank=True, null=True)
    status = models.CharField(db_column="Status", max_length=20,
                            choices=[('RECEIVED', 'Received'), ('UNDER_INSPECTION', 'Under Inspection'), 
                                   ('CLEARED', 'Cleared'), ('REJECTED', 'Rejected'), ('PARTIALLY_CLEARED', 'Partially Cleared')],
                            default='RECEIVED')
    total_items = models.IntegerField(db_column="TotalItems", default=0)
    total_quantity = models.FloatField(db_column="TotalQuantity", default=0.0)
    total_weight = models.FloatField(db_column="TotalWeight", default=0.0, blank=True, null=True)
    total_value = models.FloatField(db_column="TotalValue", default=0.0)
    received_date = models.DateTimeField(db_column="ReceivedDate", auto_now_add=True)
    clearance_date = models.DateTimeField(db_column="ClearanceDate", blank=True, null=True)
    cleared_by = models.ForeignKey(User, models.DO_NOTHING, db_column="ClearedBy", related_name='supplier_challans_cleared', blank=True, null=True)
    remarks = models.TextField(db_column="Remarks", blank=True, null=True)
    created_date = models.DateTimeField(db_column="CreatedDate", auto_now_add=True)
    created_by = models.ForeignKey(User, models.DO_NOTHING, db_column="CreatedBy", related_name='supplier_challans_created')
    modified_date = models.DateTimeField(db_column="ModifiedDate", auto_now=True)
    modified_by = models.ForeignKey(User, models.DO_NOTHING, db_column="ModifiedBy", related_name='supplier_challans_modified', blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_SupplierChallan"
        ordering = ['-challan_date', '-id']

    def __str__(self):
        return f"{self.challan_number} - {self.supplier_name}"

    @property
    def can_be_cleared(self):
        """Check if supplier challan can be cleared"""
        return self.status in ['RECEIVED', 'UNDER_INSPECTION', 'PARTIALLY_CLEARED']

    @property
    def clearance_percentage(self):
        """Calculate percentage cleared"""
        if self.total_quantity > 0:
            cleared_qty = sum(detail.cleared_quantity for detail in self.details.all())
            return (cleared_qty / self.total_quantity) * 100
        return 0

    @property
    def days_pending_clearance(self):
        """Calculate days pending clearance"""
        if self.status in ['RECEIVED', 'UNDER_INSPECTION']:
            from django.utils import timezone
            return (timezone.now().date() - self.challan_date).days
        return 0

    def generate_challan_number(self):
        """Generate unique supplier challan number"""
        if not self.challan_number:
            from django.utils import timezone
            current_year = timezone.now().year
            prefix = f"SC/{current_year}/{self.company.id:03d}/"
            
            last_challan = SupplierChallanMaster.objects.filter(
                company=self.company,
                challan_number__startswith=prefix
            ).order_by('-id').first()
            
            if last_challan:
                try:
                    last_num = int(last_challan.challan_number.split('/')[-1])
                    new_num = last_num + 1
                except (ValueError, IndexError):
                    new_num = 1
            else:
                new_num = 1
            
            self.challan_number = f"{prefix}{new_num:05d}"


class SupplierChallanLineItem(models.Model):
    """Supplier Challan line items"""
    id = models.AutoField(db_column="Id", primary_key=True)
    challan = models.ForeignKey(SupplierChallanMaster, models.CASCADE, db_column="ChallanId", related_name='details')
    po_line_item_id = models.IntegerField(db_column="POLineItemId", blank=True, null=True)
    item_code = models.CharField(db_column="ItemCode", max_length=50)
    item_description = models.CharField(db_column="ItemDescription", max_length=500)
    unit_of_measure = models.CharField(db_column="UnitOfMeasure", max_length=20)
    challan_quantity = models.FloatField(db_column="ChallanQuantity")
    cleared_quantity = models.FloatField(db_column="ClearedQuantity", default=0.0)
    rejected_quantity = models.FloatField(db_column="RejectedQuantity", default=0.0)
    pending_quantity = models.FloatField(db_column="PendingQuantity", default=0.0)
    rate = models.FloatField(db_column="Rate", blank=True, null=True)
    amount = models.FloatField(db_column="Amount", blank=True, null=True)
    weight_per_unit = models.FloatField(db_column="WeightPerUnit", blank=True, null=True)
    total_weight = models.FloatField(db_column="TotalWeight", blank=True, null=True)
    location_code = models.CharField(db_column="LocationCode", max_length=50, blank=True, null=True)
    batch_number = models.CharField(db_column="BatchNumber", max_length=50, blank=True, null=True)
    serial_number = models.CharField(db_column="SerialNumber", max_length=100, blank=True, null=True)
    manufacturing_date = models.DateField(db_column="ManufacturingDate", blank=True, null=True)
    expiry_date = models.DateField(db_column="ExpiryDate", blank=True, null=True)
    inspection_required = models.BooleanField(db_column="InspectionRequired", default=True)
    inspection_status = models.CharField(db_column="InspectionStatus", max_length=20,
                                       choices=[('PENDING', 'Pending'), ('PASSED', 'Passed'), 
                                              ('FAILED', 'Failed'), ('NOT_REQUIRED', 'Not Required')],
                                       default='PENDING')
    rejection_reason = models.TextField(db_column="RejectionReason", blank=True, null=True)
    line_status = models.CharField(db_column="LineStatus", max_length=20,
                                 choices=[('RECEIVED', 'Received'), ('UNDER_INSPECTION', 'Under Inspection'),
                                        ('CLEARED', 'Cleared'), ('PARTIALLY_CLEARED', 'Partially Cleared'),
                                        ('REJECTED', 'Rejected')],
                                 default='RECEIVED')
    created_date = models.DateTimeField(db_column="CreatedDate", auto_now_add=True)

    class Meta:
        managed = False
        db_table = "tblInv_SupplierChallanLineItem"
        ordering = ['id']

    def __str__(self):
        return f"{self.challan.challan_number} - {self.item_code}"

    @property
    def clearance_percentage(self):
        """Calculate percentage cleared"""
        if self.challan_quantity > 0:
            return (self.cleared_quantity / self.challan_quantity) * 100
        return 0

    def update_pending_quantity(self):
        """Update pending quantity based on challan and cleared quantities"""
        self.pending_quantity = max(0, self.challan_quantity - self.cleared_quantity - self.rejected_quantity)
        self.save(update_fields=['pending_quantity'])

    def update_line_status(self):
        """Update line status based on clearance progress"""
        total_processed = self.cleared_quantity + self.rejected_quantity
        
        if self.rejected_quantity > 0 and self.cleared_quantity == 0:
            self.line_status = 'REJECTED'
        elif total_processed >= self.challan_quantity:
            self.line_status = 'CLEARED' if self.rejected_quantity == 0 else 'PARTIALLY_CLEARED'
        elif total_processed > 0:
            self.line_status = 'PARTIALLY_CLEARED'
        elif self.inspection_required and self.inspection_status == 'PENDING':
            self.line_status = 'UNDER_INSPECTION'
        else:
            self.line_status = 'RECEIVED'
        
        self.save(update_fields=['line_status'])


class ChallanClearance(models.Model):
    """Challan clearance tracking for both customer and supplier challans"""
    id = models.AutoField(db_column="Id", primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    clearance_number = models.CharField(db_column="ClearanceNumber", max_length=50, unique=True)
    clearance_date = models.DateField(db_column="ClearanceDate")
    clearance_type = models.CharField(db_column="ClearanceType", max_length=20,
                                    choices=[('SUPPLIER', 'Supplier Challan'), ('CUSTOMER', 'Customer Challan')],
                                    default='SUPPLIER')
    # Polymorphic fields for either supplier or customer challan
    supplier_challan = models.ForeignKey(SupplierChallanMaster, models.CASCADE, db_column="SupplierChallanId", 
                                       related_name='clearances', blank=True, null=True)
    customer_challan = models.ForeignKey(CustomerChallanMaster, models.CASCADE, db_column="CustomerChallanId", 
                                       related_name='clearances', blank=True, null=True)
    cleared_by = models.ForeignKey(User, models.DO_NOTHING, db_column="ClearedBy", related_name='challan_clearances')
    total_cleared_quantity = models.FloatField(db_column="TotalClearedQuantity", default=0.0)
    total_cleared_value = models.FloatField(db_column="TotalClearedValue", default=0.0)
    remarks = models.TextField(db_column="Remarks", blank=True, null=True)
    created_date = models.DateTimeField(db_column="CreatedDate", auto_now_add=True)
    created_by = models.ForeignKey(User, models.DO_NOTHING, db_column="CreatedBy", related_name='clearances_created')

    class Meta:
        managed = False
        db_table = "tblInv_ChallanClearance"
        ordering = ['-clearance_date', '-id']

    def __str__(self):
        return f"{self.clearance_number} - {self.clearance_date}"

    def generate_clearance_number(self):
        """Generate unique clearance number"""
        if not self.clearance_number:
            from django.utils import timezone
            current_year = timezone.now().year
            prefix = f"CLR/{current_year}/{self.company.id:03d}/"
            
            last_clearance = ChallanClearance.objects.filter(
                company=self.company,
                clearance_number__startswith=prefix
            ).order_by('-id').first()
            
            if last_clearance:
                try:
                    last_num = int(last_clearance.clearance_number.split('/')[-1])
                    new_num = last_num + 1
                except (ValueError, IndexError):
                    new_num = 1
            else:
                new_num = 1
            
            self.clearance_number = f"{prefix}{new_num:05d}"


# Legacy models mapping for existing database tables
# Note: These map to the existing MaterialRequisition models above but with enhanced structure


class StockAdjustmentLog(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate")
    systime = models.TextField(db_column="SysTime")
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    user = models.ForeignKey(User, models.DO_NOTHING, db_column="SessionId")
    logno = models.TextField(db_column="LogNo", blank=True, null=True)
    transtype = models.IntegerField(db_column="TransType", blank=True, null=True)
    transno = models.TextField(db_column="TransNo", blank=True, null=True)
    itemid = models.IntegerField(db_column="ItemId", blank=True, null=True)
    qty = models.FloatField(db_column="Qty", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInvQc_StockAdjLog"


class ClosingStock(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    fromdt = models.TextField(db_column="FromDt", blank=True, null=True)
    todt = models.TextField(db_column="ToDt", blank=True, null=True)
    clstock = models.FloatField(db_column="ClStock", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_ClosingStck"


class CustomerChallanClearance(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate", blank=True, null=True)
    systime = models.TextField(db_column="SysTime", blank=True, null=True)
    user = models.ForeignKey(User, models.DO_NOTHING, db_column="SessionId", blank=True, null=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId", blank=True, null=True)
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId", blank=True, null=True)
    did = models.IntegerField(db_column="DId", blank=True, null=True)
    clearqty = models.FloatField(db_column="ClearQty", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_Customer_Challan_Clear"


class CustomerChallanDetail(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    customer_challan = models.ForeignKey("CustomerChallan", models.DO_NOTHING, db_column="MId", blank=True, null=True)
    challanqty = models.FloatField(db_column="ChallanQty", blank=True, null=True)
    itemid = models.IntegerField(db_column="ItemId", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_Customer_Challan_Details"


class CustomerChallan(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate", blank=True, null=True)
    systime = models.TextField(db_column="SysTime", blank=True, null=True)
    user = models.ForeignKey(User, models.DO_NOTHING, db_column="SessionId", blank=True, null=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId", blank=True, null=True)
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId", blank=True, null=True)
    ccno = models.TextField(db_column="CCNo", blank=True, null=True)
    customerid = models.TextField(db_column="CustomerId", blank=True, null=True)
    wono = models.TextField(db_column="WONo", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_Customer_Challan_Master"


class InwardDetail(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    ginno = models.TextField(db_column="GINNo", blank=True, null=True)
    ginid = models.IntegerField(db_column="GINId", blank=True, null=True)
    poid = models.IntegerField(db_column="POId", blank=True, null=True)
    qty = models.FloatField(db_column="Qty", blank=True, null=True)
    receivedqty = models.FloatField(db_column="ReceivedQty", blank=True, null=True)
    acategoyid = models.IntegerField(db_column="ACategoyId", blank=True, null=True)
    asubcategoyid = models.IntegerField(db_column="ASubCategoyId", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_Inward_Details"


class Inward(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate")
    systime = models.TextField(db_column="SysTime")
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    user = models.ForeignKey(User, models.DO_NOTHING, db_column="SessionId")
    ginno = models.TextField(db_column="GINNo", blank=True, null=True)
    pono = models.TextField(db_column="PONo", blank=True, null=True)
    pomid = models.IntegerField(db_column="POMId", blank=True, null=True)
    challanno = models.TextField(db_column="ChallanNo", blank=True, null=True)
    challandate = models.TextField(db_column="ChallanDate", blank=True, null=True)
    gateentryno = models.TextField(db_column="GateEntryNo", blank=True, null=True)
    gdate = models.TextField(db_column="GDate", blank=True, null=True)
    gtime = models.TextField(db_column="GTime", blank=True, null=True)
    modeoftransport = models.TextField(db_column="ModeofTransport", blank=True, null=True)
    vehicleno = models.TextField(db_column="VehicleNo", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_Inward_Master"


class MaterialIssueDetail(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    mid = models.IntegerField(db_column="MId", blank=True, null=True)
    minno = models.TextField(db_column="MINNo", blank=True, null=True)
    mrsid = models.IntegerField(db_column="MRSId", blank=True, null=True)
    issueqty = models.FloatField(db_column="IssueQty", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_MaterialIssue_Details"


class MaterialIssue(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate", blank=True, null=True)
    systime = models.TextField(db_column="SysTime", blank=True, null=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId", blank=True, null=True)
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId", blank=True, null=True)
    user = models.ForeignKey(User, models.DO_NOTHING, db_column="SessionId", blank=True, null=True)
    minno = models.TextField(db_column="MINNo", blank=True, null=True)
    mrsno = models.TextField(db_column="MRSNo", blank=True, null=True)
    mrsid = models.IntegerField(db_column="MRSId", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_MaterialIssue_Master"


class MaterialRequisitionDetail(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    mid = models.IntegerField(db_column="MId", blank=True, null=True)
    mrsno = models.TextField(db_column="MRSNo", blank=True, null=True)
    itemid = models.TextField(db_column="ItemId", blank=True, null=True)
    deptid = models.IntegerField(db_column="DeptId", blank=True, null=True)
    wono = models.TextField(db_column="WONo", blank=True, null=True)
    reqqty = models.FloatField(db_column="ReqQty", blank=True, null=True)
    remarks = models.TextField(db_column="Remarks", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_MaterialRequisition_Details"


class MaterialRequisition(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate")
    systime = models.TextField(db_column="SysTime")
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    user = models.ForeignKey(User, models.DO_NOTHING, db_column="SessionId")
    mrsno = models.TextField(db_column="MRSNo", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_MaterialRequisition_Master"


class MaterialReturnDetail(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    mid = models.IntegerField(db_column="MId", blank=True, null=True)
    mrnno = models.TextField(db_column="MRNNo", blank=True, null=True)
    itemid = models.TextField(db_column="ItemId", blank=True, null=True)
    deptid = models.IntegerField(db_column="DeptId", blank=True, null=True)
    wono = models.TextField(db_column="WONo", blank=True, null=True)
    retqty = models.FloatField(db_column="RetQty", blank=True, null=True)
    remarks = models.TextField(db_column="Remarks", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_MaterialReturn_Details"


class MaterialReturn(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate")
    systime = models.TextField(db_column="SysTime")
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    user = models.ForeignKey(User, models.DO_NOTHING, db_column="SessionId")
    mrnno = models.TextField(db_column="MRNNo", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_MaterialReturn_Master"


class SupplierChallanClearance(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate", blank=True, null=True)
    systime = models.TextField(db_column="SysTime", blank=True, null=True)
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId", blank=True, null=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId", blank=True, null=True)
    user = models.ForeignKey(User, models.DO_NOTHING, db_column="SessionId", blank=True, null=True)
    did = models.ForeignKey("SupplierChallanDetail", models.DO_NOTHING, db_column="DId", blank=True, null=True)
    clearedqty = models.FloatField(db_column="ClearedQty", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_Supplier_Challan_Clear"


class SupplierChallanDetail(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    supplier_challan = models.ForeignKey("SupplierChallan", models.DO_NOTHING, db_column="MId", blank=True, null=True)
    prdid = models.IntegerField(db_column="PRDId", blank=True, null=True)
    challanqty = models.FloatField(db_column="ChallanQty", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_Supplier_Challan_Details"


class SupplierChallan(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate", blank=True, null=True)
    systime = models.TextField(db_column="SysTime", blank=True, null=True)
    user = models.ForeignKey(User, models.DO_NOTHING, db_column="SessionId", blank=True, null=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId", blank=True, null=True)
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId", blank=True, null=True)
    scno = models.TextField(db_column="SCNo", blank=True, null=True)
    supplierid = models.TextField(db_column="SupplierId", blank=True, null=True)
    remarks = models.TextField(db_column="Remarks", blank=True, null=True)
    vehicleno = models.TextField(db_column="VehicleNo", blank=True, null=True)
    transpoter = models.TextField(db_column="Transpoter", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_Supplier_Challan_Master"
        ordering = ['-id']
        
    def __str__(self):
        return f"{self.challan_number} - {self.supplier_name}"
    
    # Property mappings for view compatibility
    @property
    def challan_number(self):
        """Map scno to challan_number"""
        return self.scno or ""
    
    @property
    def supplier_name(self):
        """Get supplier name from supplier ID"""
        if self.supplierid:
            return f"Supplier {self.supplierid}"
        return "Unknown Supplier"
    
    @property
    def challan_date(self):
        """Convert sysdate to date"""
        if self.sysdate:
            try:
                from datetime import datetime
                return datetime.strptime(self.sysdate, '%d-%m-%Y').date()
            except (ValueError, TypeError):
                pass
        from django.utils import timezone
        return timezone.now().date()
    
    @property
    def status(self):
        """Default status - can be enhanced based on business logic"""
        return 'RECEIVED'  # Default status for existing challans
        
    @property
    def po_number(self):
        """Default empty PO number"""
        return ""
        
    @property  
    def invoice_number(self):
        """Default empty invoice number"""
        return ""


class WarehouseIssueDetail(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    warehouse_issue = models.ForeignKey("WarehouseIssue", models.DO_NOTHING, db_column="MId")
    wisno = models.TextField(db_column="WISNo", blank=True, null=True)
    pid = models.IntegerField(db_column="PId", blank=True, null=True)
    cid = models.IntegerField(db_column="CId", blank=True, null=True)
    itemid = models.IntegerField(db_column="ItemId", blank=True, null=True)
    issuedqty = models.FloatField(db_column="IssuedQty", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_WIS_Details"


class WarehouseIssue(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate")
    systime = models.TextField(db_column="SysTime")
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    user = models.ForeignKey(User, models.DO_NOTHING, db_column="SessionId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    wisno = models.TextField(db_column="WISNo", blank=True, null=True)
    wono = models.TextField(db_column="WONo", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_WIS_Master"


# Work Order Master (from SD module but used in inventory)
class WorkOrderMaster(models.Model):
    """Work Order Master from Sales Distribution - SD_Cust_WorkOrder_Master"""
    id = models.AutoField(db_column="Id", primary_key=True)
    sys_date = models.CharField(db_column="SysDate", max_length=50, blank=True, null=True)
    wo_no = models.CharField(db_column="WONo", max_length=50, blank=True, null=True)
    task_project_title = models.TextField(db_column="TaskProjectTitle", blank=True, null=True)
    release_wis = models.CharField(db_column="ReleaseWIS", max_length=20, blank=True, null=True)  # Release/Stop
    close_open = models.CharField(db_column="CloseOpen", max_length=20, blank=True, null=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId", blank=True, null=True)
    category_id = models.IntegerField(db_column="CId", blank=True, null=True)  # Links to WO Category
    
    class Meta:
        managed = False
        db_table = "SD_Cust_WorkOrder_Master"
        ordering = ['-id']
    
    def __str__(self):
        return f"{self.wo_no} - {self.task_project_title}"
        
    @property
    def is_released(self):
        """Check if work order is released for WIS"""
        return self.release_wis == 'Release'


class WorkOrderCategory(models.Model):
    """Work Order Categories - tblSD_WO_Category"""
    id = models.AutoField(db_column="CId", primary_key=True)
    symbol = models.CharField(db_column="Symbol", max_length=10, blank=True, null=True)
    category_name = models.CharField(db_column="CName", max_length=100, blank=True, null=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId", blank=True, null=True, related_name='inventory_workorder_categories')
    
    class Meta:
        managed = False
        db_table = "tblSD_WO_Category"
        ordering = ['symbol']
    
    def __str__(self):
        return f"{self.symbol} - {self.category_name}"


class WorkOrderRelease(models.Model):
    """Work Order Release Tracking - tblInv_WORelease_WIS"""
    id = models.AutoField(db_column="Id", primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId", related_name="inv_workorder_releases")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId", related_name="inv_workorder_releases")
    wo_no = models.CharField(db_column="WONo", max_length=50, blank=True, null=True)
    release_sys_date = models.CharField(db_column="ReleaseSysDate", max_length=50, blank=True, null=True)
    release_sys_time = models.CharField(db_column="ReleaseSysTime", max_length=50, blank=True, null=True)
    release_by = models.CharField(db_column="ReleaseBy", max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblInv_WORelease_WIS"
        ordering = ['-id']
    
    def __str__(self):
        return f"Release {self.wo_no} by {self.release_by}"


class AutoWarehouseIssueSchedule(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    timeauto = models.TextField(db_column="TimeAuto", blank=True, null=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId", blank=True, null=True)
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    timetoorder = models.TextField(db_column="TimeToOrder", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblinv_AutoWIS_TimeSchedule"


class MaterialReceivedDetail(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    mid = models.IntegerField(db_column="MId", blank=True, null=True)
    grrno = models.TextField(db_column="GRRNo", blank=True, null=True)
    poid = models.IntegerField(db_column="POId", blank=True, null=True)
    receivedqty = models.FloatField(db_column="ReceivedQty", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblinv_MaterialReceived_Details"


class MaterialReceived(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate")
    systime = models.TextField(db_column="SysTime")
    user = models.ForeignKey(User, models.DO_NOTHING, db_column="SessionId")
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    grrno = models.TextField(db_column="GRRNo", blank=True, null=True)
    ginno = models.TextField(db_column="GINNo", blank=True, null=True)
    ginid = models.IntegerField(db_column="GINId", blank=True, null=True)
    taxinvoiceno = models.TextField(db_column="TaxInvoiceNo", blank=True, null=True)
    taxinvoicedate = models.TextField(db_column="TaxInvoiceDate", blank=True, null=True)
    modvatapp = models.IntegerField(db_column="ModVatApp", blank=True, null=True)
    modvatinv = models.IntegerField(db_column="ModVatInv", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblinv_MaterialReceived_Master"


class MaterialRequisitionTemp(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId", blank=True, null=True)
    user = models.ForeignKey(User, models.DO_NOTHING, db_column="SessionId", blank=True, null=True)
    itemid = models.IntegerField(db_column="ItemId", blank=True, null=True)
    deptid = models.IntegerField(db_column="DeptId", blank=True, null=True)
    wono = models.TextField(db_column="WONo", blank=True, null=True)
    reqqty = models.FloatField(db_column="ReqQty", blank=True, null=True)
    remarks = models.TextField(db_column="Remarks", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblinv_MaterialRequisition_Temp"


class MaterialReturnTemp(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    user = models.ForeignKey(User, models.DO_NOTHING, db_column="SessionId")
    itemid = models.IntegerField(db_column="ItemId", blank=True, null=True)
    deptid = models.IntegerField(db_column="DeptId", blank=True, null=True)
    wono = models.TextField(db_column="WONo", blank=True, null=True)
    retqty = models.FloatField(db_column="RetQty", blank=True, null=True)
    remarks = models.TextField(db_column="Remarks", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblinv_MaterialReturn_Temp"


class MaterialServiceNoteDetail(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    mid = models.IntegerField(db_column="MId", blank=True, null=True)
    gsnno = models.TextField(db_column="GSNNo", blank=True, null=True)
    poid = models.IntegerField(db_column="POId", blank=True, null=True)
    receivedqty = models.FloatField(db_column="ReceivedQty", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblinv_MaterialServiceNote_Details"


class MaterialServiceNote(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate")
    systime = models.TextField(db_column="SysTime")
    user = models.ForeignKey(User, models.DO_NOTHING, db_column="SessionId")
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    gsnno = models.TextField(db_column="GSNNo", blank=True, null=True)
    ginid = models.IntegerField(db_column="GINId", blank=True, null=True)
    ginno = models.TextField(db_column="GINNo", blank=True, null=True)
    taxinvoiceno = models.TextField(db_column="TaxInvoiceNo", blank=True, null=True)
    taxinvoicedate = models.TextField(db_column="TaxInvoiceDate", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblinv_MaterialServiceNote_Master"


class QualityDetail(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    gqnno = models.TextField(db_column="GQNNo", blank=True, null=True)
    grrid = models.IntegerField(db_column="GRRId", blank=True, null=True)
    acceptedqty = models.FloatField(db_column="AcceptedQty", blank=True, null=True)
    stockqty = models.FloatField(db_column="StockQty", blank=True, null=True)
    rejectedqty = models.FloatField(db_column="RejectedQty", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblinv_Quality_Details"


class Quality(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate")
    systime = models.TextField(db_column="SysTime")
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    user = models.ForeignKey(User, models.DO_NOTHING, db_column="SessionId")
    gqnno = models.TextField(db_column="GQNNo", blank=True, null=True)
    grrno = models.TextField(db_column="GRRNo", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblinv_Quality_Master"


class ReceivedDetail(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    grrno = models.TextField(db_column="GRRNo", blank=True, null=True)
    ginid = models.IntegerField(db_column="GINId", blank=True, null=True)
    inwardqty = models.FloatField(db_column="InwardQty", blank=True, null=True)
    receivedqty = models.FloatField(db_column="ReceivedQty", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblinv_Received_Details"


class Received(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate", blank=True, null=True)
    systime = models.TextField(db_column="SysTime", blank=True, null=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId", blank=True, null=True)
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId", blank=True, null=True)
    user = models.ForeignKey(User, models.DO_NOTHING, db_column="SessionId", blank=True, null=True)
    grrno = models.TextField(db_column="GRRNo", blank=True, null=True)
    ginno = models.TextField(db_column="GINNo", blank=True, null=True)
    taxinvoiceno = models.TextField(db_column="TaxInvoiceNo", blank=True, null=True)
    taxinvoicedate = models.TextField(db_column="TaxInvoiceDate", blank=True, null=True)
    modvatapplicable = models.IntegerField(db_column="ModVatApplicable", blank=True, null=True)
    modvatinvoice = models.IntegerField(db_column="ModVatInvoice", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblinv_Received_Master"


class Rejection(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate")
    systime = models.TextField(db_column="SysTime")
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    user = models.ForeignKey(User, models.DO_NOTHING, db_column="SessionId")
    grnno = models.TextField(db_column="GRNNo", blank=True, null=True)
    gqnno = models.TextField(db_column="GQNNo", blank=True, null=True)
    gqnid = models.IntegerField(db_column="GQNId", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblinv_Rejection_Master"


class StockMonth(models.Model):
    id = models.IntegerField(db_column="Id", primary_key=True)
    srno = models.IntegerField(db_column="SrNO")
    item_code = models.TextField(db_column="Item_code")
    group = models.TextField(db_column="Group")
    description = models.TextField(db_column="Description")
    unit = models.TextField(db_column="Unit")
    qty_month = models.TextField(db_column="Qty_month")
    reorder = models.TextField(db_column="ReOrder")
    min_order = models.TextField(db_column="Min_order")
    day = models.TextField(db_column="Day")

    class Meta:
        managed = False
        db_table = "tbl_Stock_Month"


class VehicleDetail(models.Model):
    id = models.IntegerField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate", blank=True, null=True)
    systime = models.TextField(db_column="SysTime", blank=True, null=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId", blank=True, null=True)
    user = models.ForeignKey(User, models.DO_NOTHING, db_column="SessionId", blank=True, null=True)
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId", blank=True, null=True)
    vehno = models.TextField(db_column="VehNo", blank=True, null=True)
    date = models.TextField(db_column="Date", blank=True, null=True)
    vehical_name = models.TextField(db_column="Vehical_Name", blank=True, null=True)
    contact = models.TextField(db_column="Contact", blank=True, null=True)
    destination = models.TextField(db_column="Destination", blank=True, null=True)
    address = models.TextField(db_column="Address", blank=True, null=True)
    fromkm = models.IntegerField(db_column="FromKM", blank=True, null=True)
    fromto = models.IntegerField(db_column="FromTo", blank=True, null=True)
    avg = models.IntegerField(db_column="Avg", blank=True, null=True)
    fluel_date = models.TextField(db_column="Fluel_Date", blank=True, null=True)
    fluel_rs = models.TextField(db_column="Fluel_Rs", blank=True, null=True)
    material = models.TextField(db_column="Material", blank=True, null=True)
    emp = models.TextField(db_column="Emp", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblVeh_Master_Details"


class Vehicle(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    vehicalname = models.TextField(db_column="VehicalName", blank=True, null=True)
    vehicalno = models.TextField(db_column="VehicalNo", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblVeh_Process_Master"


class Gatepass(models.Model):
    id = models.IntegerField(db_column="Id", primary_key=True)
    srno = models.IntegerField(db_column="SrNo")
    chalanno = models.IntegerField(db_column="ChalanNo")
    date = models.TextField(db_column="Date")
    wono = models.TextField(db_column="WoNo")
    des_name = models.TextField(db_column="Des_Name")
    codeno = models.TextField(db_column="CodeNo")
    description = models.TextField(db_column="Description")
    unit = models.TextField(db_column="Unit")
    qty = models.TextField(db_column="Qty")
    total_qty = models.TextField(db_column="Total_qty")
    issueto = models.TextField(db_column="IssueTo")
    athoriseby = models.TextField(db_column="AthoriseBy")
    rec_date = models.TextField(db_column="Rec_Date")
    qty_recd = models.TextField(db_column="Qty_Recd")
    qty_pend = models.TextField(db_column="Qty_pend")
    recdby = models.TextField(db_column="RecdBy")
    remark = models.TextField(db_column="Remark")

    class Meta:
        managed = False
        db_table = "tbl_Gatepass"


# =============================================================================
# Task Group 8: Work-in-Progress (WIS) Management Models
# =============================================================================

class WISMaster(models.Model):
    """WIS Master matching actual database schema"""
    id = models.AutoField(db_column="Id", primary_key=True)
    sys_date = models.CharField(db_column="SysDate", max_length=50)  # Stored as text: "2013-05-06"
    sys_time = models.CharField(db_column="SysTime", max_length=50)  # Stored as text: "10:51:25 AM"
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    session_id = models.CharField(db_column="SessionId", max_length=50)  # Employee ID like "Sapl0052"
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    wis_number = models.CharField(db_column="WISNo", max_length=50)  # "0001", "0002"
    work_order_number = models.CharField(db_column="WONo", max_length=50)  # "T0003", "N0002"
    
    class Meta:
        managed = False
        db_table = "tblInv_WIS_Master"
        ordering = ['-sys_date', '-id']
        
    def __str__(self):
        return f"{self.wis_number} - {self.work_order_number}"
        
    @property
    def display_date(self):
        """Get formatted display date"""
        return self.sys_date if self.sys_date else ''
        
    @property 
    def display_time(self):
        """Get formatted display time"""
        return self.sys_time if self.sys_time else ''


class WISActualRunMaterial(models.Model):
    """Actual run material processing for WIS"""
    id = models.AutoField(db_column="Id", primary_key=True)
    wis_master = models.ForeignKey(WISMaster, models.CASCADE, db_column="WISMasterId", related_name='material_runs')
    sequence_number = models.IntegerField(db_column="SequenceNumber", default=1)
    item_id = models.IntegerField(db_column="ItemId")
    item_code = models.CharField(db_column="ItemCode", max_length=50)
    item_description = models.CharField(db_column="ItemDescription", max_length=200)
    unit_of_measure = models.CharField(db_column="UnitOfMeasure", max_length=20)
    location_code = models.CharField(db_column="LocationCode", max_length=50, blank=True, null=True)
    planned_quantity = models.FloatField(db_column="PlannedQuantity", default=0.0)
    actual_quantity = models.FloatField(db_column="ActualQuantity", default=0.0)
    consumed_quantity = models.FloatField(db_column="ConsumedQuantity", default=0.0)
    wastage_quantity = models.FloatField(db_column="WastageQuantity", default=0.0)
    rate_per_unit = models.FloatField(db_column="RatePerUnit", default=0.0)
    total_value = models.FloatField(db_column="TotalValue", default=0.0)
    batch_number = models.CharField(db_column="BatchNumber", max_length=50, blank=True, null=True)
    lot_number = models.CharField(db_column="LotNumber", max_length=50, blank=True, null=True)
    stock_before = models.FloatField(db_column="StockBefore", default=0.0)
    stock_after = models.FloatField(db_column="StockAfter", default=0.0)
    processing_status = models.CharField(db_column="ProcessingStatus", max_length=20,
                                       choices=[('PENDING', 'Pending'), ('IN_PROGRESS', 'In Progress'), 
                                              ('COMPLETED', 'Completed'), ('FAILED', 'Failed'), ('SKIPPED', 'Skipped')],
                                       default='PENDING')
    processing_timestamp = models.DateTimeField(db_column="ProcessingTimestamp", blank=True, null=True)
    error_message = models.TextField(db_column="ErrorMessage", blank=True, null=True)
    variance_quantity = models.FloatField(db_column="VarianceQuantity", default=0.0)
    variance_percentage = models.FloatField(db_column="VariancePercentage", default=0.0)
    quality_check_required = models.BooleanField(db_column="QualityCheckRequired", default=False)
    quality_check_status = models.CharField(db_column="QualityCheckStatus", max_length=20, blank=True, null=True)
    remarks = models.TextField(db_column="Remarks", blank=True, null=True)
    created_date = models.DateTimeField(db_column="CreatedDate", auto_now_add=True)
    processed_by = models.ForeignKey(User, models.DO_NOTHING, db_column="ProcessedBy", related_name='material_runs_processed', blank=True, null=True)
    
    class Meta:
        managed = False
        db_table = "tblInv_WIS_ActualRun_Material"
        ordering = ['sequence_number', 'item_code']
        
    def __str__(self):
        return f"WIS {self.wis_master.wis_number} - {self.item_code}"
    
    @property
    def variance_status(self):
        """Get variance status"""
        if abs(self.variance_percentage) <= 2:
            return 'WITHIN_TOLERANCE'
        elif abs(self.variance_percentage) <= 5:
            return 'MINOR_VARIANCE'
        else:
            return 'MAJOR_VARIANCE'
    
    def calculate_variance(self):
        """Calculate variance between planned and actual"""
        self.variance_quantity = self.actual_quantity - self.planned_quantity
        if self.planned_quantity > 0:
            self.variance_percentage = (self.variance_quantity / self.planned_quantity) * 100
        else:
            self.variance_percentage = 0
    
    def update_stock_levels(self):
        """Update stock levels after processing"""
        # This would integrate with stock management system
        pass


class WISActualRunAssembly(models.Model):
    """Actual run assembly processing for WIS"""
    id = models.AutoField(db_column="Id", primary_key=True)
    wis_master = models.ForeignKey(WISMaster, models.CASCADE, db_column="WISMasterId", related_name='assembly_runs')
    sequence_number = models.IntegerField(db_column="SequenceNumber", default=1)
    assembly_id = models.IntegerField(db_column="AssemblyId")
    assembly_code = models.CharField(db_column="AssemblyCode", max_length=50)
    assembly_description = models.CharField(db_column="AssemblyDescription", max_length=200)
    parent_assembly_id = models.IntegerField(db_column="ParentAssemblyId", blank=True, null=True)
    level_number = models.IntegerField(db_column="LevelNumber", default=1)
    planned_quantity = models.FloatField(db_column="PlannedQuantity", default=0.0)
    actual_quantity = models.FloatField(db_column="ActualQuantity", default=0.0)
    completed_quantity = models.FloatField(db_column="CompletedQuantity", default=0.0)
    rejected_quantity = models.FloatField(db_column="RejectedQuantity", default=0.0)
    work_center_code = models.CharField(db_column="WorkCenterCode", max_length=50, blank=True, null=True)
    operation_sequence = models.IntegerField(db_column="OperationSequence", default=1)
    setup_time_planned = models.FloatField(db_column="SetupTimePlanned", default=0.0)
    setup_time_actual = models.FloatField(db_column="SetupTimeActual", default=0.0)
    processing_time_planned = models.FloatField(db_column="ProcessingTimePlanned", default=0.0)
    processing_time_actual = models.FloatField(db_column="ProcessingTimeActual", default=0.0)
    operator_id = models.IntegerField(db_column="OperatorId", blank=True, null=True)
    machine_id = models.IntegerField(db_column="MachineId", blank=True, null=True)
    processing_status = models.CharField(db_column="ProcessingStatus", max_length=20,
                                       choices=[('PENDING', 'Pending'), ('SETUP', 'Setup'), ('IN_PROGRESS', 'In Progress'), 
                                              ('COMPLETED', 'Completed'), ('FAILED', 'Failed'), ('ON_HOLD', 'On Hold')],
                                       default='PENDING')
    processing_start_time = models.DateTimeField(db_column="ProcessingStartTime", blank=True, null=True)
    processing_end_time = models.DateTimeField(db_column="ProcessingEndTime", blank=True, null=True)
    quality_check_required = models.BooleanField(db_column="QualityCheckRequired", default=True)
    quality_check_status = models.CharField(db_column="QualityCheckStatus", max_length=20,
                                          choices=[('PENDING', 'Pending'), ('PASSED', 'Passed'), ('FAILED', 'Failed'), ('WAIVED', 'Waived')],
                                          default='PENDING')
    rework_required = models.BooleanField(db_column="ReworkRequired", default=False)
    rework_reason = models.TextField(db_column="ReworkReason", blank=True, null=True)
    efficiency_percentage = models.FloatField(db_column="EfficiencyPercentage", default=0.0)
    remarks = models.TextField(db_column="Remarks", blank=True, null=True)
    error_message = models.TextField(db_column="ErrorMessage", blank=True, null=True)
    created_date = models.DateTimeField(db_column="CreatedDate", auto_now_add=True)
    processed_by = models.ForeignKey(User, models.DO_NOTHING, db_column="ProcessedBy", related_name='assembly_runs_processed', blank=True, null=True)
    
    class Meta:
        managed = False
        db_table = "tblInv_WIS_ActualRun_Assembly"
        ordering = ['sequence_number', 'level_number', 'assembly_code']
        
    def __str__(self):
        return f"WIS {self.wis_master.wis_number} - {self.assembly_code}"
    
    @property
    def completion_percentage(self):
        """Calculate completion percentage"""
        if self.planned_quantity > 0:
            return (self.completed_quantity / self.planned_quantity) * 100
        return 0
    
    @property
    def cycle_time_actual(self):
        """Calculate actual cycle time in minutes"""
        if self.processing_start_time and self.processing_end_time:
            return (self.processing_end_time - self.processing_start_time).total_seconds() / 60
        return 0
    
    def calculate_efficiency(self):
        """Calculate processing efficiency"""
        if self.processing_time_planned > 0 and self.processing_time_actual > 0:
            self.efficiency_percentage = (self.processing_time_planned / self.processing_time_actual) * 100
        else:
            self.efficiency_percentage = 0


class WISDryRun(models.Model):
    """WIS dry run for simulation and planning"""
    id = models.AutoField(db_column="Id", primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    dry_run_number = models.CharField(db_column="DryRunNumber", max_length=50, unique=True)
    dry_run_date = models.DateField(db_column="DryRunDate")
    work_order_number = models.CharField(db_column="WorkOrderNumber", max_length=50)
    simulation_type = models.CharField(db_column="SimulationType", max_length=20,
                                     choices=[('MATERIAL_REQUIREMENT', 'Material Requirement'), 
                                            ('CAPACITY_PLANNING', 'Capacity Planning'),
                                            ('COST_ESTIMATION', 'Cost Estimation'),
                                            ('TIMELINE_ANALYSIS', 'Timeline Analysis')],
                                     default='MATERIAL_REQUIREMENT')
    scenario_name = models.CharField(db_column="ScenarioName", max_length=100, blank=True, null=True)
    planned_start_date = models.DateField(db_column="PlannedStartDate")
    planned_end_date = models.DateField(db_column="PlannedEndDate")
    planned_quantity = models.FloatField(db_column="PlannedQuantity", default=0.0)
    simulation_status = models.CharField(db_column="SimulationStatus", max_length=20,
                                       choices=[('DRAFT', 'Draft'), ('RUNNING', 'Running'), ('COMPLETED', 'Completed'), 
                                              ('FAILED', 'Failed'), ('CANCELLED', 'Cancelled')],
                                       default='DRAFT')
    material_availability_check = models.BooleanField(db_column="MaterialAvailabilityCheck", default=True)
    capacity_availability_check = models.BooleanField(db_column="CapacityAvailabilityCheck", default=True)
    cost_calculation_enabled = models.BooleanField(db_column="CostCalculationEnabled", default=True)
    total_materials_required = models.IntegerField(db_column="TotalMaterialsRequired", default=0)
    total_assemblies_required = models.IntegerField(db_column="TotalAssembliesRequired", default=0)
    estimated_duration_hours = models.FloatField(db_column="EstimatedDurationHours", default=0.0)
    estimated_cost = models.FloatField(db_column="EstimatedCost", default=0.0)
    feasibility_score = models.FloatField(db_column="FeasibilityScore", default=0.0)
    constraints_identified = models.TextField(db_column="ConstraintsIdentified", blank=True, null=True)
    recommendations = models.TextField(db_column="Recommendations", blank=True, null=True)
    simulation_results = models.TextField(db_column="SimulationResults", blank=True, null=True)
    approved_for_execution = models.BooleanField(db_column="ApprovedForExecution", default=False)
    approved_by = models.ForeignKey(User, models.DO_NOTHING, db_column="ApprovedBy", related_name='dry_runs_approved', blank=True, null=True)
    approved_date = models.DateTimeField(db_column="ApprovedDate", blank=True, null=True)
    remarks = models.TextField(db_column="Remarks", blank=True, null=True)
    created_date = models.DateTimeField(db_column="CreatedDate", auto_now_add=True)
    created_by = models.ForeignKey(User, models.DO_NOTHING, db_column="CreatedBy", related_name='dry_runs_created')
    
    class Meta:
        managed = False
        db_table = "tblInv_WIS_DryRun"
        ordering = ['-dry_run_date', '-id']
        
    def __str__(self):
        return f"{self.dry_run_number} - {self.work_order_number}"
    
    def generate_dry_run_number(self):
        """Generate unique dry run number"""
        if not self.dry_run_number:
            today = timezone.now().date()
            prefix = f"DRY/{today.strftime('%Y%m%d')}"
            
            # Get last dry run number for today
            last_dry_run = WISDryRun.objects.filter(
                company=self.company,
                dry_run_number__startswith=prefix
            ).order_by('-id').first()
            
            if last_dry_run:
                try:
                    last_seq = int(last_dry_run.dry_run_number.split('/')[-1])
                    new_seq = last_seq + 1
                except (ValueError, IndexError):
                    new_seq = 1
            else:
                new_seq = 1
            
            self.dry_run_number = f"{prefix}/{new_seq:04d}"


class WISTransactionLog(models.Model):
    """Detailed transaction log for WIS operations"""
    id = models.AutoField(db_column="Id", primary_key=True)
    wis_master = models.ForeignKey(WISMaster, models.CASCADE, db_column="WISMasterId", related_name='transaction_logs')
    transaction_id = models.CharField(db_column="TransactionId", max_length=100)
    transaction_type = models.CharField(db_column="TransactionType", max_length=50)
    entity_type = models.CharField(db_column="EntityType", max_length=20,
                                 choices=[('MATERIAL', 'Material'), ('ASSEMBLY', 'Assembly'), ('OPERATION', 'Operation')],
                                 default='MATERIAL')
    entity_id = models.IntegerField(db_column="EntityId")
    entity_code = models.CharField(db_column="EntityCode", max_length=50)
    operation_type = models.CharField(db_column="OperationType", max_length=30,
                                    choices=[('ISSUE', 'Issue'), ('CONSUME', 'Consume'), ('PRODUCE', 'Produce'), 
                                           ('COMPLETE', 'Complete'), ('SCRAP', 'Scrap'), ('REWORK', 'Rework')],
                                    default='ISSUE')
    quantity_before = models.FloatField(db_column="QuantityBefore", default=0.0)
    quantity_after = models.FloatField(db_column="QuantityAfter", default=0.0)
    quantity_change = models.FloatField(db_column="QuantityChange", default=0.0)
    location_from = models.CharField(db_column="LocationFrom", max_length=50, blank=True, null=True)
    location_to = models.CharField(db_column="LocationTo", max_length=50, blank=True, null=True)
    reference_document_type = models.CharField(db_column="ReferenceDocumentType", max_length=30, blank=True, null=True)
    reference_document_number = models.CharField(db_column="ReferenceDocumentNumber", max_length=50, blank=True, null=True)
    transaction_timestamp = models.DateTimeField(db_column="TransactionTimestamp", auto_now_add=True)
    processed_by = models.ForeignKey(User, models.DO_NOTHING, db_column="ProcessedBy", related_name='wis_transactions_processed')
    success = models.BooleanField(db_column="Success", default=True)
    error_code = models.CharField(db_column="ErrorCode", max_length=20, blank=True, null=True)
    error_message = models.TextField(db_column="ErrorMessage", blank=True, null=True)
    retry_count = models.IntegerField(db_column="RetryCount", default=0)
    batch_number = models.CharField(db_column="BatchNumber", max_length=50, blank=True, null=True)
    cost_impact = models.FloatField(db_column="CostImpact", default=0.0)
    remarks = models.TextField(db_column="Remarks", blank=True, null=True)
    
    class Meta:
        managed = False
        db_table = "tblInv_WIS_TransactionLog"
        ordering = ['-transaction_timestamp']
        
    def __str__(self):
        return f"{self.transaction_id} - {self.operation_type} {self.entity_code}"


class WISReleaseDetails(models.Model):
    """Enhanced WIS release details for work order management"""
    id = models.AutoField(db_column="Id", primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    wis_master = models.ForeignKey(WISMaster, models.CASCADE, db_column="WISMasterId", related_name='release_details')
    release_number = models.CharField(db_column="ReleaseNumber", max_length=50)
    release_date = models.DateField(db_column="ReleaseDate")
    release_time = models.TimeField(db_column="ReleaseTime")
    work_order_number = models.CharField(db_column="WorkOrderNumber", max_length=50)
    work_order_id = models.IntegerField(db_column="WorkOrderId", blank=True, null=True)
    release_type = models.CharField(db_column="ReleaseType", max_length=20,
                                  choices=[('MANUAL', 'Manual'), ('AUTOMATIC', 'Automatic'), ('SCHEDULED', 'Scheduled')],
                                  default='MANUAL')
    release_phase = models.CharField(db_column="ReleasePhase", max_length=20,
                                   choices=[('MATERIAL_ISSUE', 'Material Issue'), ('PRODUCTION_START', 'Production Start'), 
                                          ('OPERATION_COMPLETE', 'Operation Complete'), ('FINAL_COMPLETION', 'Final Completion')],
                                   default='MATERIAL_ISSUE')
    quantity_released = models.FloatField(db_column="QuantityReleased", default=0.0)
    items_released = models.IntegerField(db_column="ItemsReleased", default=0)
    release_status = models.CharField(db_column="ReleaseStatus", max_length=20,
                                    choices=[('PENDING', 'Pending'), ('IN_PROGRESS', 'In Progress'), 
                                           ('COMPLETED', 'Completed'), ('FAILED', 'Failed'), ('CANCELLED', 'Cancelled')],
                                    default='PENDING')
    parent_release_id = models.ForeignKey('self', models.CASCADE, db_column="ParentReleaseId", blank=True, null=True)
    dependency_check_passed = models.BooleanField(db_column="DependencyCheckPassed", default=False)
    material_availability_confirmed = models.BooleanField(db_column="MaterialAvailabilityConfirmed", default=False)
    capacity_availability_confirmed = models.BooleanField(db_column="CapacityAvailabilityConfirmed", default=False)
    quality_hold_released = models.BooleanField(db_column="QualityHoldReleased", default=True)
    priority_override = models.BooleanField(db_column="PriorityOverride", default=False)
    override_reason = models.TextField(db_column="OverrideReason", blank=True, null=True)
    estimated_completion_time = models.DateTimeField(db_column="EstimatedCompletionTime", blank=True, null=True)
    actual_completion_time = models.DateTimeField(db_column="ActualCompletionTime", blank=True, null=True)
    released_by = models.ForeignKey(User, models.DO_NOTHING, db_column="ReleasedBy", related_name='wis_releases_created')
    approved_by = models.ForeignKey(User, models.DO_NOTHING, db_column="ApprovedBy", related_name='wis_releases_approved', blank=True, null=True)
    remarks = models.TextField(db_column="Remarks", blank=True, null=True)
    created_date = models.DateTimeField(db_column="CreatedDate", auto_now_add=True)
    
    class Meta:
        managed = False
        db_table = "tblInv_WIS_ReleaseDetails"
        ordering = ['-release_date', '-release_time']
        
    def __str__(self):
        return f"{self.release_number} - {self.work_order_number}"
    
    @property
    def is_overdue(self):
        """Check if release is overdue"""
        if self.estimated_completion_time and self.release_status in ['PENDING', 'IN_PROGRESS']:
            return timezone.now() > self.estimated_completion_time
        return False


# =============================================================================
# Task Group 10: Inventory Movement Tracking Models
# =============================================================================

class InventoryMovementMaster(models.Model):
    """Master record for tracking all inventory movements"""
    id = models.AutoField(db_column="Id", primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    movement_number = models.CharField(db_column="MovementNumber", max_length=50, unique=True)
    movement_date = models.DateField(db_column="MovementDate")
    movement_type = models.CharField(db_column="MovementType", max_length=30,
                                   choices=[('INWARD', 'Inward Movement'), ('OUTWARD', 'Outward Movement'), 
                                          ('INTERNAL', 'Internal Transfer'), ('ADJUSTMENT', 'Stock Adjustment'),
                                          ('PRODUCTION', 'Production Movement'), ('SCRAP', 'Scrap Movement')],
                                   default='INTERNAL')
    movement_category = models.CharField(db_column="MovementCategory", max_length=30,
                                       choices=[('PHYSICAL', 'Physical Movement'), ('LOGICAL', 'Logical Movement'), 
                                              ('SYSTEM', 'System Generated'), ('MANUAL', 'Manual Entry')],
                                       default='PHYSICAL')
    source_document_type = models.CharField(db_column="SourceDocumentType", max_length=30,
                                          choices=[('MRS', 'Material Requisition Slip'), ('MIN', 'Material Issue Note'),
                                                 ('MRN', 'Material Return Note'), ('GIN', 'Goods Inward Note'),
                                                 ('GRR', 'Goods Received Receipt'), ('WIS', 'Work In Progress'),
                                                 ('CHALLAN', 'Challan'), ('MANUAL', 'Manual Entry')],
                                          blank=True, null=True)
    source_document_number = models.CharField(db_column="SourceDocumentNumber", max_length=50, blank=True, null=True)
    source_document_id = models.IntegerField(db_column="SourceDocumentId", blank=True, null=True)
    from_location_code = models.CharField(db_column="FromLocationCode", max_length=50, blank=True, null=True)
    to_location_code = models.CharField(db_column="ToLocationCode", max_length=50, blank=True, null=True)
    from_warehouse = models.CharField(db_column="FromWarehouse", max_length=50, blank=True, null=True)
    to_warehouse = models.CharField(db_column="ToWarehouse", max_length=50, blank=True, null=True)
    work_order_number = models.CharField(db_column="WorkOrderNumber", max_length=50, blank=True, null=True)
    project_code = models.CharField(db_column="ProjectCode", max_length=50, blank=True, null=True)
    cost_center_code = models.CharField(db_column="CostCenterCode", max_length=50, blank=True, null=True)
    movement_status = models.CharField(db_column="MovementStatus", max_length=20,
                                     choices=[('PENDING', 'Pending'), ('IN_PROGRESS', 'In Progress'), 
                                            ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled'),
                                            ('PARTIALLY_COMPLETED', 'Partially Completed')],
                                     default='PENDING')
    total_items = models.IntegerField(db_column="TotalItems", default=0)
    total_quantity = models.FloatField(db_column="TotalQuantity", default=0.0)
    total_value = models.FloatField(db_column="TotalValue", default=0.0)
    variance_quantity = models.FloatField(db_column="VarianceQuantity", default=0.0)
    variance_value = models.FloatField(db_column="VarianceValue", default=0.0)
    authorized_by = models.ForeignKey(User, models.DO_NOTHING, db_column="AuthorizedBy", related_name='movements_authorized', blank=True, null=True)
    authorized_date = models.DateTimeField(db_column="AuthorizedDate", blank=True, null=True)
    processed_by = models.ForeignKey(User, models.DO_NOTHING, db_column="ProcessedBy", related_name='movements_processed', blank=True, null=True)
    processed_date = models.DateTimeField(db_column="ProcessedDate", blank=True, null=True)
    priority = models.CharField(db_column="Priority", max_length=20,
                              choices=[('LOW', 'Low'), ('NORMAL', 'Normal'), ('HIGH', 'High'), ('URGENT', 'Urgent')],
                              default='NORMAL')
    requires_approval = models.BooleanField(db_column="RequiresApproval", default=False)
    auto_generated = models.BooleanField(db_column="AutoGenerated", default=False)
    system_generated = models.BooleanField(db_column="SystemGenerated", default=False)
    remarks = models.TextField(db_column="Remarks", blank=True, null=True)
    error_log = models.TextField(db_column="ErrorLog", blank=True, null=True)
    created_date = models.DateTimeField(db_column="CreatedDate", auto_now_add=True)
    created_by = models.ForeignKey(User, models.DO_NOTHING, db_column="CreatedBy", related_name='movements_created')
    modified_date = models.DateTimeField(db_column="ModifiedDate", auto_now=True)
    modified_by = models.ForeignKey(User, models.DO_NOTHING, db_column="ModifiedBy", related_name='movements_modified', blank=True, null=True)
    
    class Meta:
        managed = False
        db_table = "tblInv_MovementMaster"
        ordering = ['-movement_date', '-id']
        
    def __str__(self):
        return f"{self.movement_number} - {self.movement_type} ({self.movement_date})"
    
    @property
    def completion_percentage(self):
        """Calculate completion percentage"""
        if self.total_items > 0:
            completed_items = self.movement_details.filter(line_status='COMPLETED').count()
            return (completed_items / self.total_items) * 100
        return 0
    
    @property
    def variance_percentage(self):
        """Calculate variance percentage"""
        if self.total_quantity > 0:
            return (self.variance_quantity / self.total_quantity) * 100
        return 0
    
    def generate_movement_number(self):
        """Generate unique movement number"""
        if not self.movement_number:
            today = timezone.now().date()
            prefix = f"MOV/{self.movement_type[:3]}/{today.strftime('%Y%m%d')}"
            
            # Get last movement number for today
            last_movement = InventoryMovementMaster.objects.filter(
                company=self.company,
                movement_number__startswith=prefix
            ).order_by('-id').first()
            
            if last_movement:
                try:
                    last_seq = int(last_movement.movement_number.split('/')[-1])
                    new_seq = last_seq + 1
                except (ValueError, IndexError):
                    new_seq = 1
            else:
                new_seq = 1
            
            self.movement_number = f"{prefix}/{new_seq:04d}"


class InventoryMovementDetails(models.Model):
    """Detailed line items for inventory movements"""
    id = models.AutoField(db_column="Id", primary_key=True)
    movement_master = models.ForeignKey(InventoryMovementMaster, models.CASCADE, db_column="MovementMasterId", related_name='movement_details')
    line_number = models.IntegerField(db_column="LineNumber", default=1)
    item_id = models.IntegerField(db_column="ItemId")
    item_code = models.CharField(db_column="ItemCode", max_length=50)
    item_description = models.CharField(db_column="ItemDescription", max_length=200)
    item_category = models.CharField(db_column="ItemCategory", max_length=50, blank=True, null=True)
    unit_of_measure = models.CharField(db_column="UnitOfMeasure", max_length=20)
    from_location_code = models.CharField(db_column="FromLocationCode", max_length=50, blank=True, null=True)
    to_location_code = models.CharField(db_column="ToLocationCode", max_length=50, blank=True, null=True)
    from_batch_number = models.CharField(db_column="FromBatchNumber", max_length=50, blank=True, null=True)
    to_batch_number = models.CharField(db_column="ToBatchNumber", max_length=50, blank=True, null=True)
    from_lot_number = models.CharField(db_column="FromLotNumber", max_length=50, blank=True, null=True)
    to_lot_number = models.CharField(db_column="ToLotNumber", max_length=50, blank=True, null=True)
    planned_quantity = models.FloatField(db_column="PlannedQuantity", default=0.0)
    actual_quantity = models.FloatField(db_column="ActualQuantity", default=0.0)
    variance_quantity = models.FloatField(db_column="VarianceQuantity", default=0.0)
    unit_rate = models.FloatField(db_column="UnitRate", default=0.0)
    total_value = models.FloatField(db_column="TotalValue", default=0.0)
    stock_before_movement = models.FloatField(db_column="StockBeforeMovement", default=0.0)
    stock_after_movement = models.FloatField(db_column="StockAfterMovement", default=0.0)
    movement_reason = models.CharField(db_column="MovementReason", max_length=50, blank=True, null=True)
    quality_status = models.CharField(db_column="QualityStatus", max_length=20,
                                    choices=[('PENDING', 'Pending'), ('PASSED', 'Passed'), ('FAILED', 'Failed'), ('WAIVED', 'Waived')],
                                    default='PENDING')
    expiry_date = models.DateField(db_column="ExpiryDate", blank=True, null=True)
    manufacturing_date = models.DateField(db_column="ManufacturingDate", blank=True, null=True)
    serial_numbers = models.TextField(db_column="SerialNumbers", blank=True, null=True)
    line_status = models.CharField(db_column="LineStatus", max_length=20,
                                 choices=[('PENDING', 'Pending'), ('IN_PROGRESS', 'In Progress'), 
                                        ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled'),
                                        ('ON_HOLD', 'On Hold')],
                                 default='PENDING')
    movement_timestamp = models.DateTimeField(db_column="MovementTimestamp", blank=True, null=True)
    processed_by = models.ForeignKey(User, models.DO_NOTHING, db_column="ProcessedBy", related_name='movement_details_processed', blank=True, null=True)
    barcode_scanned = models.BooleanField(db_column="BarcodeScanned", default=False)
    rfid_tag = models.CharField(db_column="RFIDTag", max_length=50, blank=True, null=True)
    weight_measured = models.FloatField(db_column="WeightMeasured", default=0.0, blank=True, null=True)
    dimension_length = models.FloatField(db_column="DimensionLength", default=0.0, blank=True, null=True)
    dimension_width = models.FloatField(db_column="DimensionWidth", default=0.0, blank=True, null=True)
    dimension_height = models.FloatField(db_column="DimensionHeight", default=0.0, blank=True, null=True)
    special_handling_required = models.BooleanField(db_column="SpecialHandlingRequired", default=False)
    special_handling_instructions = models.TextField(db_column="SpecialHandlingInstructions", blank=True, null=True)
    error_message = models.TextField(db_column="ErrorMessage", blank=True, null=True)
    remarks = models.TextField(db_column="Remarks", blank=True, null=True)
    created_date = models.DateTimeField(db_column="CreatedDate", auto_now_add=True)
    
    class Meta:
        managed = False
        db_table = "tblInv_MovementDetails"
        ordering = ['line_number']
        
    def __str__(self):
        return f"{self.movement_master.movement_number} - Line {self.line_number}: {self.item_code}"
    
    @property
    def variance_percentage(self):
        """Calculate variance percentage"""
        if self.planned_quantity > 0:
            return (self.variance_quantity / self.planned_quantity) * 100
        return 0
    
    def calculate_variance(self):
        """Calculate variance between planned and actual"""
        self.variance_quantity = self.actual_quantity - self.planned_quantity
        self.total_value = self.actual_quantity * self.unit_rate
    
    def update_stock_levels(self):
        """Update stock levels after movement"""
        # This would integrate with stock management system
        pass


class StockLedger(models.Model):
    """Comprehensive stock ledger for all inventory transactions"""
    id = models.AutoField(db_column="Id", primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    transaction_date = models.DateField(db_column="TransactionDate")
    transaction_time = models.TimeField(db_column="TransactionTime")
    transaction_id = models.CharField(db_column="TransactionId", max_length=100, unique=True)
    item_id = models.IntegerField(db_column="ItemId")
    item_code = models.CharField(db_column="ItemCode", max_length=50)
    item_description = models.CharField(db_column="ItemDescription", max_length=200)
    location_code = models.CharField(db_column="LocationCode", max_length=50)
    warehouse_code = models.CharField(db_column="WarehouseCode", max_length=50, blank=True, null=True)
    batch_number = models.CharField(db_column="BatchNumber", max_length=50, blank=True, null=True)
    lot_number = models.CharField(db_column="LotNumber", max_length=50, blank=True, null=True)
    transaction_type = models.CharField(db_column="TransactionType", max_length=30,
                                      choices=[('RECEIPT', 'Receipt'), ('ISSUE', 'Issue'), ('TRANSFER', 'Transfer'),
                                             ('ADJUSTMENT', 'Adjustment'), ('PRODUCTION', 'Production'), 
                                             ('CONSUMPTION', 'Consumption'), ('SCRAP', 'Scrap'), ('RETURN', 'Return')],
                                      default='RECEIPT')
    reference_document_type = models.CharField(db_column="ReferenceDocumentType", max_length=30, blank=True, null=True)
    reference_document_number = models.CharField(db_column="ReferenceDocumentNumber", max_length=50, blank=True, null=True)
    reference_line_number = models.IntegerField(db_column="ReferenceLineNumber", blank=True, null=True)
    work_order_number = models.CharField(db_column="WorkOrderNumber", max_length=50, blank=True, null=True)
    cost_center_code = models.CharField(db_column="CostCenterCode", max_length=50, blank=True, null=True)
    supplier_code = models.CharField(db_column="SupplierCode", max_length=50, blank=True, null=True)
    customer_code = models.CharField(db_column="CustomerCode", max_length=50, blank=True, null=True)
    quantity_in = models.FloatField(db_column="QuantityIn", default=0.0)
    quantity_out = models.FloatField(db_column="QuantityOut", default=0.0)
    quantity_balance = models.FloatField(db_column="QuantityBalance", default=0.0)
    unit_rate = models.FloatField(db_column="UnitRate", default=0.0)
    value_in = models.FloatField(db_column="ValueIn", default=0.0)
    value_out = models.FloatField(db_column="ValueOut", default=0.0)
    value_balance = models.FloatField(db_column="ValueBalance", default=0.0)
    unit_of_measure = models.CharField(db_column="UnitOfMeasure", max_length=20)
    valuation_method = models.CharField(db_column="ValuationMethod", max_length=20,
                                      choices=[('FIFO', 'First In First Out'), ('LIFO', 'Last In First Out'),
                                             ('WEIGHTED_AVG', 'Weighted Average'), ('STANDARD', 'Standard Cost')],
                                      default='WEIGHTED_AVG')
    movement_master_id = models.IntegerField(db_column="MovementMasterId", blank=True, null=True)
    movement_detail_id = models.IntegerField(db_column="MovementDetailId", blank=True, null=True)
    quality_status = models.CharField(db_column="QualityStatus", max_length=20, blank=True, null=True)
    expiry_date = models.DateField(db_column="ExpiryDate", blank=True, null=True)
    manufacturing_date = models.DateField(db_column="ManufacturingDate", blank=True, null=True)
    aging_days = models.IntegerField(db_column="AgingDays", default=0)
    is_slow_moving = models.BooleanField(db_column="IsSlowMoving", default=False)
    is_fast_moving = models.BooleanField(db_column="IsFastMoving", default=False)
    is_dead_stock = models.BooleanField(db_column="IsDeadStock", default=False)
    last_movement_date = models.DateField(db_column="LastMovementDate", blank=True, null=True)
    abc_category = models.CharField(db_column="ABCCategory", max_length=10,
                                  choices=[('A', 'Category A'), ('B', 'Category B'), ('C', 'Category C')],
                                  blank=True, null=True)
    xyz_category = models.CharField(db_column="XYZCategory", max_length=10,
                                  choices=[('X', 'Category X'), ('Y', 'Category Y'), ('Z', 'Category Z')],
                                  blank=True, null=True)
    auto_generated = models.BooleanField(db_column="AutoGenerated", default=True)
    processed_by = models.ForeignKey(User, models.DO_NOTHING, db_column="ProcessedBy", related_name='stock_transactions_processed')
    remarks = models.TextField(db_column="Remarks", blank=True, null=True)
    created_date = models.DateTimeField(db_column="CreatedDate", auto_now_add=True)
    
    class Meta:
        managed = False
        db_table = "tblInv_StockLedger"
        ordering = ['-transaction_date', '-transaction_time', '-id']
        
    def __str__(self):
        return f"{self.transaction_id} - {self.item_code} ({self.transaction_type})"
    
    @property
    def transaction_age_days(self):
        """Calculate age of transaction in days"""
        return (timezone.now().date() - self.transaction_date).days
    
    def generate_transaction_id(self):
        """Generate unique transaction ID"""
        if not self.transaction_id:
            timestamp = timezone.now().strftime('%Y%m%d%H%M%S')
            self.transaction_id = f"TXN/{self.transaction_type}/{timestamp}/{self.id or 'NEW'}"


class InventorySnapshot(models.Model):
    """Periodic inventory snapshots for reporting and analysis"""
    id = models.AutoField(db_column="Id", primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    snapshot_date = models.DateField(db_column="SnapshotDate")
    snapshot_time = models.TimeField(db_column="SnapshotTime")
    snapshot_type = models.CharField(db_column="SnapshotType", max_length=20,
                                   choices=[('DAILY', 'Daily'), ('WEEKLY', 'Weekly'), ('MONTHLY', 'Monthly'),
                                          ('QUARTERLY', 'Quarterly'), ('YEARLY', 'Yearly'), ('ON_DEMAND', 'On Demand')],
                                   default='DAILY')
    item_id = models.IntegerField(db_column="ItemId")
    item_code = models.CharField(db_column="ItemCode", max_length=50)
    item_description = models.CharField(db_column="ItemDescription", max_length=200)
    location_code = models.CharField(db_column="LocationCode", max_length=50)
    warehouse_code = models.CharField(db_column="WarehouseCode", max_length=50, blank=True, null=True)
    current_stock = models.FloatField(db_column="CurrentStock", default=0.0)
    blocked_stock = models.FloatField(db_column="BlockedStock", default=0.0)
    reserved_stock = models.FloatField(db_column="ReservedStock", default=0.0)
    available_stock = models.FloatField(db_column="AvailableStock", default=0.0)
    in_transit_stock = models.FloatField(db_column="InTransitStock", default=0.0)
    quality_hold_stock = models.FloatField(db_column="QualityHoldStock", default=0.0)
    damaged_stock = models.FloatField(db_column="DamagedStock", default=0.0)
    obsolete_stock = models.FloatField(db_column="ObsoleteStock", default=0.0)
    unit_of_measure = models.CharField(db_column="UnitOfMeasure", max_length=20)
    weighted_avg_rate = models.FloatField(db_column="WeightedAvgRate", default=0.0)
    total_value = models.FloatField(db_column="TotalValue", default=0.0)
    last_receipt_date = models.DateField(db_column="LastReceiptDate", blank=True, null=True)
    last_issue_date = models.DateField(db_column="LastIssueDate", blank=True, null=True)
    last_movement_date = models.DateField(db_column="LastMovementDate", blank=True, null=True)
    aging_days = models.IntegerField(db_column="AgingDays", default=0)
    turnover_ratio = models.FloatField(db_column="TurnoverRatio", default=0.0)
    safety_stock_level = models.FloatField(db_column="SafetyStockLevel", default=0.0)
    reorder_level = models.FloatField(db_column="ReorderLevel", default=0.0)
    maximum_level = models.FloatField(db_column="MaximumLevel", default=0.0)
    minimum_level = models.FloatField(db_column="MinimumLevel", default=0.0)
    abc_category = models.CharField(db_column="ABCCategory", max_length=10, blank=True, null=True)
    xyz_category = models.CharField(db_column="XYZCategory", max_length=10, blank=True, null=True)
    movement_frequency = models.CharField(db_column="MovementFrequency", max_length=20,
                                        choices=[('FAST', 'Fast Moving'), ('NORMAL', 'Normal Moving'),
                                               ('SLOW', 'Slow Moving'), ('DEAD', 'Dead Stock')],
                                        default='NORMAL')
    stock_status = models.CharField(db_column="StockStatus", max_length=20,
                                  choices=[('ADEQUATE', 'Adequate'), ('BELOW_MIN', 'Below Minimum'),
                                         ('ABOVE_MAX', 'Above Maximum'), ('CRITICAL', 'Critical'),
                                         ('EXCESS', 'Excess'), ('SHORTAGE', 'Shortage')],
                                  default='ADEQUATE')
    remarks = models.TextField(db_column="Remarks", blank=True, null=True)
    created_date = models.DateTimeField(db_column="CreatedDate", auto_now_add=True)
    created_by = models.ForeignKey(User, models.DO_NOTHING, db_column="CreatedBy", related_name='snapshots_created')
    
    class Meta:
        managed = False
        db_table = "tblInv_InventorySnapshot"
        ordering = ['-snapshot_date', '-snapshot_time', 'item_code']
        
    def __str__(self):
        return f"{self.item_code} - {self.snapshot_date} ({self.snapshot_type})"
    
    @property
    def stock_health_score(self):
        """Calculate stock health score (0-100)"""
        score = 100
        
        # Deduct points for various issues
        if self.aging_days > 365:
            score -= 30  # Very old stock
        elif self.aging_days > 180:
            score -= 15  # Old stock
        
        if self.current_stock < self.minimum_level:
            score -= 25  # Below minimum
        elif self.current_stock > self.maximum_level:
            score -= 15  # Above maximum
        
        if self.turnover_ratio < 1:
            score -= 20  # Low turnover
        
        if self.damaged_stock > 0:
            score -= 10  # Damaged stock present
        
        return max(0, score)
    
    @property
    def is_reorder_required(self):
        """Check if reorder is required"""
        return self.available_stock <= self.reorder_level


class MovementTrackingDashboard(models.Model):
    """Dashboard metrics for movement tracking"""
    id = models.AutoField(db_column="Id", primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    dashboard_date = models.DateField(db_column="DashboardDate")
    total_movements = models.IntegerField(db_column="TotalMovements", default=0)
    inward_movements = models.IntegerField(db_column="InwardMovements", default=0)
    outward_movements = models.IntegerField(db_column="OutwardMovements", default=0)
    internal_transfers = models.IntegerField(db_column="InternalTransfers", default=0)
    pending_movements = models.IntegerField(db_column="PendingMovements", default=0)
    completed_movements = models.IntegerField(db_column="CompletedMovements", default=0)
    total_value_moved = models.FloatField(db_column="TotalValueMoved", default=0.0)
    average_movement_time = models.FloatField(db_column="AverageMovementTime", default=0.0)
    exception_count = models.IntegerField(db_column="ExceptionCount", default=0)
    variance_count = models.IntegerField(db_column="VarianceCount", default=0)
    total_items_moved = models.IntegerField(db_column="TotalItemsMoved", default=0)
    unique_items_moved = models.IntegerField(db_column="UniqueItemsMoved", default=0)
    fast_moving_items = models.IntegerField(db_column="FastMovingItems", default=0)
    slow_moving_items = models.IntegerField(db_column="SlowMovingItems", default=0)
    dead_stock_items = models.IntegerField(db_column="DeadStockItems", default=0)
    stock_accuracy_percentage = models.FloatField(db_column="StockAccuracyPercentage", default=100.0)
    movement_efficiency = models.FloatField(db_column="MovementEfficiency", default=100.0)
    created_date = models.DateTimeField(db_column="CreatedDate", auto_now_add=True)
    
    class Meta:
        managed = False
        db_table = "tblInv_MovementTrackingDashboard"
        ordering = ['-dashboard_date']
        
    def __str__(self):
        return f"Movement Dashboard - {self.dashboard_date}"


class MaterialCreditNote(models.Model):
    """Material Credit Note Master for stock adjustments and corrections"""
    ADJUSTMENT_TYPE_CHOICES = [
        ('INCREASE', 'Stock Increase'),
        ('DECREASE', 'Stock Decrease'),
        ('WRITE_OFF', 'Write Off'),
        ('RETURN', 'Return to Supplier'),
        ('DAMAGED', 'Damaged Stock'),
        ('EXPIRED', 'Expired Stock'),
        ('QUALITY_REJECT', 'Quality Rejection'),
        ('TRANSFER', 'Transfer Adjustment'),
        ('INVENTORY_CORRECTION', 'Inventory Correction'),
    ]
    
    STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('PENDING_APPROVAL', 'Pending Approval'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
        ('PROCESSED', 'Processed'),
        ('CANCELLED', 'Cancelled'),
    ]
    
    PRIORITY_CHOICES = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('URGENT', 'Urgent'),
    ]
    
    id = models.AutoField(db_column="Id", primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId", related_name="inventory_material_credit_notes")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinancialYearId", related_name="inventory_material_credit_notes")
    mcn_number = models.CharField(db_column="MCNNumber", max_length=50, unique=True)
    mcn_date = models.DateField(db_column="MCNDate")
    adjustment_type = models.CharField(db_column="AdjustmentType", max_length=20, choices=ADJUSTMENT_TYPE_CHOICES)
    reference_number = models.CharField(db_column="ReferenceNumber", max_length=100, blank=True, null=True)
    reference_date = models.DateField(db_column="ReferenceDate", blank=True, null=True)
    status = models.CharField(db_column="Status", max_length=20, choices=STATUS_CHOICES, default='DRAFT')
    priority = models.CharField(db_column="Priority", max_length=10, choices=PRIORITY_CHOICES, default='MEDIUM')
    department_id = models.IntegerField(db_column="DepartmentId", blank=True, null=True)
    reason_code = models.CharField(db_column="ReasonCode", max_length=50, blank=True, null=True)
    reason_description = models.TextField(db_column="ReasonDescription", blank=True, null=True)
    total_items = models.IntegerField(db_column="TotalItems", default=0)
    total_adjustment_value = models.DecimalField(db_column="TotalAdjustmentValue", max_digits=15, decimal_places=2, default=0.00)
    requested_by = models.ForeignKey(User, models.DO_NOTHING, db_column="RequestedBy", related_name='mcn_requested')
    requested_date = models.DateTimeField(db_column="RequestedDate", auto_now_add=True)
    approved_by = models.ForeignKey(User, models.DO_NOTHING, db_column="ApprovedBy", blank=True, null=True, related_name='mcn_approved')
    approved_date = models.DateTimeField(db_column="ApprovedDate", blank=True, null=True)
    processed_by = models.ForeignKey(User, models.DO_NOTHING, db_column="ProcessedBy", blank=True, null=True, related_name='mcn_processed')
    processed_date = models.DateTimeField(db_column="ProcessedDate", blank=True, null=True)
    remarks = models.TextField(db_column="Remarks", blank=True, null=True)
    is_emergency = models.BooleanField(db_column="IsEmergency", default=False)
    created_by = models.ForeignKey(User, models.DO_NOTHING, db_column="CreatedBy", related_name='mcn_created')
    created_date = models.DateTimeField(db_column="CreatedDate", auto_now_add=True)
    modified_by = models.ForeignKey(User, models.DO_NOTHING, db_column="ModifiedBy", blank=True, null=True, related_name='mcn_modified')
    modified_date = models.DateTimeField(db_column="ModifiedDate", auto_now=True)
    
    class Meta:
        managed = False
        db_table = "tblInv_MaterialCreditNote_Master"
        ordering = ['-mcn_date', '-mcn_number']
        
    def __str__(self):
        return f"MCN-{self.mcn_number} ({self.mcn_date})"
    
    @property
    def can_be_edited(self):
        """Check if MCN can be edited"""
        return self.status in ['DRAFT', 'REJECTED']
    
    @property
    def can_be_approved(self):
        """Check if MCN can be approved"""
        return self.status == 'PENDING_APPROVAL'
    
    @property
    def can_be_processed(self):
        """Check if MCN can be processed"""
        return self.status == 'APPROVED'
    
    @property
    def is_completed(self):
        """Check if MCN is completed"""
        return self.status in ['PROCESSED', 'CANCELLED']


class MCNLineItem(models.Model):
    """Material Credit Note Line Items for detailed adjustments"""
    ADJUSTMENT_ACTION_CHOICES = [
        ('ADD', 'Add to Stock'),
        ('SUBTRACT', 'Subtract from Stock'),
        ('WRITE_OFF', 'Write Off'),
        ('TRANSFER_IN', 'Transfer In'),
        ('TRANSFER_OUT', 'Transfer Out'),
    ]
    
    LINE_STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
        ('PROCESSED', 'Processed'),
        ('CANCELLED', 'Cancelled'),
    ]
    
    id = models.AutoField(db_column="Id", primary_key=True)
    mcn = models.ForeignKey(MaterialCreditNote, models.CASCADE, db_column="MCNId", related_name='line_items')
    line_number = models.IntegerField(db_column="LineNumber")
    item_code = models.CharField(db_column="ItemCode", max_length=100)
    item_description = models.TextField(db_column="ItemDescription")
    unit_of_measure = models.CharField(db_column="UnitOfMeasure", max_length=20)
    current_stock = models.DecimalField(db_column="CurrentStock", max_digits=15, decimal_places=3, default=0.000)
    adjustment_quantity = models.DecimalField(db_column="AdjustmentQuantity", max_digits=15, decimal_places=3)
    adjustment_action = models.CharField(db_column="AdjustmentAction", max_length=15, choices=ADJUSTMENT_ACTION_CHOICES)
    unit_rate = models.DecimalField(db_column="UnitRate", max_digits=15, decimal_places=2, default=0.00)
    total_amount = models.DecimalField(db_column="TotalAmount", max_digits=15, decimal_places=2, default=0.00)
    location_code = models.CharField(db_column="LocationCode", max_length=50, blank=True, null=True)
    batch_number = models.CharField(db_column="BatchNumber", max_length=50, blank=True, null=True)
    serial_number = models.CharField(db_column="SerialNumber", max_length=100, blank=True, null=True)
    expiry_date = models.DateField(db_column="ExpiryDate", blank=True, null=True)
    reason_code = models.CharField(db_column="ReasonCode", max_length=50, blank=True, null=True)
    line_remarks = models.TextField(db_column="LineRemarks", blank=True, null=True)
    line_status = models.CharField(db_column="LineStatus", max_length=15, choices=LINE_STATUS_CHOICES, default='PENDING')
    processed_date = models.DateTimeField(db_column="ProcessedDate", blank=True, null=True)
    processed_by = models.ForeignKey(User, models.DO_NOTHING, db_column="ProcessedBy", blank=True, null=True)
    created_date = models.DateTimeField(db_column="CreatedDate", auto_now_add=True)
    
    class Meta:
        managed = False
        db_table = "tblInv_MaterialCreditNote_Details"
        ordering = ['line_number']
        unique_together = ['mcn', 'line_number']
        
    def __str__(self):
        return f"MCN Line {self.line_number}: {self.item_code}"
    
    @property
    def adjusted_stock(self):
        """Calculate adjusted stock after this line item"""
        if self.adjustment_action == 'ADD':
            return self.current_stock + self.adjustment_quantity
        elif self.adjustment_action == 'SUBTRACT':
            return self.current_stock - self.adjustment_quantity
        elif self.adjustment_action == 'WRITE_OFF':
            return 0
        else:
            return self.current_stock
    
    @property
    def stock_variance(self):
        """Calculate stock variance"""
        return self.adjusted_stock - self.current_stock
    
    def save(self, *args, **kwargs):
        # Calculate total amount
        self.total_amount = self.adjustment_quantity * self.unit_rate
        super().save(*args, **kwargs)


class MCNApprovalHistory(models.Model):
    """Track MCN approval workflow history"""
    ACTION_CHOICES = [
        ('SUBMITTED', 'Submitted for Approval'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
        ('RETURNED', 'Returned for Revision'),
        ('CANCELLED', 'Cancelled'),
        ('PROCESSED', 'Processed'),
    ]
    
    id = models.AutoField(db_column="Id", primary_key=True)
    mcn = models.ForeignKey(MaterialCreditNote, models.CASCADE, db_column="MCNId", related_name='approval_history')
    approver = models.ForeignKey(User, models.DO_NOTHING, db_column="ApproverId")
    action = models.CharField(db_column="Action", max_length=15, choices=ACTION_CHOICES)
    approval_level = models.IntegerField(db_column="ApprovalLevel", default=1)
    comments = models.TextField(db_column="Comments", blank=True, null=True)
    action_date = models.DateTimeField(db_column="ActionDate", auto_now_add=True)
    
    class Meta:
        managed = False
        db_table = "tblInv_MCN_ApprovalHistory"
        ordering = ['-action_date']
        
    def __str__(self):
        return f"MCN {self.mcn.mcn_number} - {self.action} by {self.approver.username}"


# Enhanced Service Note Models for Task Group 6
class EnhancedMaterialServiceNote(models.Model):
    """Enhanced Material Service Note for comprehensive service management"""
    STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('PENDING_APPROVAL', 'Pending Approval'),
        ('APPROVED', 'Approved'),
        ('IN_PROGRESS', 'In Progress'), 
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled'),
    ]
    
    PRIORITY_CHOICES = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('URGENT', 'Urgent'),
    ]
    
    id = models.AutoField(db_column="Id", primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinancialYearId")
    sn_number = models.CharField(db_column="SNNumber", max_length=50, unique=True)
    sn_date = models.DateField(db_column="SNDate")
    service_type = models.CharField(db_column="ServiceType", max_length=100)
    status = models.CharField(db_column="Status", max_length=20, choices=STATUS_CHOICES, default='DRAFT')
    priority = models.CharField(db_column="Priority", max_length=10, choices=PRIORITY_CHOICES, default='MEDIUM')
    supplier_id = models.IntegerField(db_column="SupplierId", blank=True, null=True)
    reference_number = models.CharField(db_column="ReferenceNumber", max_length=100, blank=True, null=True)
    reference_date = models.DateField(db_column="ReferenceDate", blank=True, null=True)
    department_id = models.IntegerField(db_column="DepartmentId", blank=True, null=True)
    location_code = models.CharField(db_column="LocationCode", max_length=50, blank=True, null=True)
    service_description = models.TextField(db_column="ServiceDescription")
    remarks = models.TextField(db_column="Remarks", blank=True, null=True)
    total_items = models.IntegerField(db_column="TotalItems", default=0)
    total_amount = models.DecimalField(db_column="TotalAmount", max_digits=15, decimal_places=2, default=0.00)
    submitted_by = models.ForeignKey(User, models.DO_NOTHING, db_column="SubmittedBy", blank=True, null=True, related_name='sn_submitted')
    submitted_date = models.DateTimeField(db_column="SubmittedDate", blank=True, null=True)
    approved_by = models.ForeignKey(User, models.DO_NOTHING, db_column="ApprovedBy", blank=True, null=True, related_name='sn_approved')
    approved_date = models.DateTimeField(db_column="ApprovedDate", blank=True, null=True)
    work_started_by = models.ForeignKey(User, models.DO_NOTHING, db_column="WorkStartedBy", blank=True, null=True, related_name='sn_work_started')
    work_started_date = models.DateTimeField(db_column="WorkStartedDate", blank=True, null=True)
    completed_by = models.ForeignKey(User, models.DO_NOTHING, db_column="CompletedBy", blank=True, null=True, related_name='sn_completed')
    completed_date = models.DateTimeField(db_column="CompletedDate", blank=True, null=True)
    created_by = models.ForeignKey(User, models.DO_NOTHING, db_column="CreatedBy", related_name='sn_created')
    created_date = models.DateTimeField(db_column="CreatedDate", auto_now_add=True)
    modified_by = models.ForeignKey(User, models.DO_NOTHING, db_column="ModifiedBy", blank=True, null=True, related_name='sn_modified')
    modified_date = models.DateTimeField(db_column="ModifiedDate", auto_now=True)
    
    class Meta:
        abstract = True  # Make this model abstract to avoid database conflicts
        
    def __str__(self):
        return f"SN-{self.sn_number} ({self.service_type})"
    
    @property
    def can_be_edited(self):
        """Check if Service Note can be edited"""
        return self.status in ['DRAFT', 'REJECTED']
    
    @property
    def can_be_submitted(self):
        """Check if Service Note can be submitted"""
        return self.status == 'DRAFT'
    
    @property
    def can_be_approved(self):
        """Check if Service Note can be approved"""
        return self.status == 'PENDING_APPROVAL'
    
    @property
    def can_start_work(self):
        """Check if work can be started"""
        return self.status == 'APPROVED'
    
    @property
    def can_be_completed(self):
        """Check if Service Note can be completed"""
        return self.status == 'IN_PROGRESS'
    
    @property
    def is_active(self):
        """Check if Service Note is active"""
        return self.status not in ['COMPLETED', 'CANCELLED']


class EnhancedMaterialServiceNoteDetail(models.Model):
    """Enhanced Service Note Detail Items"""
    
    id = models.AutoField(db_column="Id", primary_key=True)
    service_note = models.ForeignKey(EnhancedMaterialServiceNote, models.CASCADE, db_column="ServiceNoteId", related_name='details')
    line_number = models.IntegerField(db_column="LineNumber")
    item_code = models.CharField(db_column="ItemCode", max_length=100)
    item_description = models.TextField(db_column="ItemDescription")
    unit_of_measure = models.CharField(db_column="UnitOfMeasure", max_length=20)
    service_quantity = models.DecimalField(db_column="ServiceQuantity", max_digits=15, decimal_places=3)
    unit_rate = models.DecimalField(db_column="UnitRate", max_digits=15, decimal_places=2, default=0.00)
    service_specification = models.TextField(db_column="ServiceSpecification", blank=True, null=True)
    expected_completion_date = models.DateField(db_column="ExpectedCompletionDate", blank=True, null=True)
    line_remarks = models.TextField(db_column="LineRemarks", blank=True, null=True)
    created_date = models.DateTimeField(db_column="CreatedDate", auto_now_add=True)
    
    class Meta:
        abstract = True  # Make this model abstract to avoid database conflicts
        
    def __str__(self):
        return f"SN Line {self.line_number}: {self.item_code}"
    
    @property
    def total_amount(self):
        """Calculate total amount for this line"""
        return self.service_quantity * self.unit_rate
