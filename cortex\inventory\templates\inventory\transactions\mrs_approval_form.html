{% extends 'core/base.html' %}
{% load static %}

{% block title %}Approve MRS - {{ object.mrs_number }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <!-- Breadcrumb -->
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-4">
                    <li>
                        <div>
                            <a href="{% url 'core:dashboard' %}" class="text-gray-400 hover:text-gray-500">
                                <svg class="flex-shrink-0 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                                </svg>
                                <span class="sr-only">Home</span>
                            </a>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                            </svg>
                            <a href="{% url 'inventory:mrs_list' %}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">MRS</a>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                            </svg>
                            <a href="{% url 'inventory:mrs_detail' object.pk %}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">{{ object.mrs_number }}</a>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                            </svg>
                            <span class="ml-4 text-sm font-medium text-gray-500">Approval</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- Page Header -->
            <div class="mt-4">
                <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                    MRS Approval
                </h1>
                <p class="mt-1 text-sm text-gray-500">
                    Review and approve Material Requisition Slip {{ object.mrs_number }}
                </p>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            <div class="space-y-6">

                <!-- MRS Summary Card -->
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">Requisition Summary</h3>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                {{ object.get_status_display }}
                            </span>
                        </div>
                        
                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">MRS Number</dt>
                                <dd class="mt-1 text-sm text-gray-900 font-semibold">{{ object.mrs_number }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Requested By</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ object.requested_by.get_full_name|default:object.requested_by.username }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Required Date</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ object.required_date|date:"M d, Y" }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Priority</dt>
                                <dd class="mt-1">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if object.priority == 'URGENT' %}bg-red-100 text-red-800
                                        {% elif object.priority == 'HIGH' %}bg-orange-100 text-orange-800
                                        {% elif object.priority == 'NORMAL' %}bg-blue-100 text-blue-800
                                        {% else %}bg-gray-100 text-gray-800
                                        {% endif %}">
                                        {{ object.get_priority_display }}
                                    </span>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Requisition Type</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ object.get_requisition_type_display }}</dd>
                            </div>
                            {% if object.department_id %}
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Department</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ object.department_id }}</dd>
                            </div>
                            {% endif %}
                            {% if object.work_order_number %}
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Work Order</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ object.work_order_number }}</dd>
                            </div>
                            {% endif %}
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Total Items</dt>
                                <dd class="mt-1 text-sm text-gray-900 font-semibold">{{ object.total_items }}</dd>
                            </div>
                        </div>

                        {% if object.remarks %}
                        <div class="mt-6">
                            <dt class="text-sm font-medium text-gray-500">Remarks</dt>
                            <dd class="mt-1 text-sm text-gray-900 bg-gray-50 p-3 rounded-md">{{ object.remarks }}</dd>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Line Items Review -->
                <div class="bg-white shadow overflow-hidden sm:rounded-md" x-data="approvalForm()">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">
                            Requested Items Review ({{ object.line_items.count }})
                        </h3>
                        <p class="mt-1 text-sm text-gray-500">
                            Review all requested items and their stock availability before approval
                        </p>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="w-full table-auto divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <input type="checkbox" @change="toggleAllItems" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requested</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Available</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Approved Qty</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Comments</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for line_item in object.line_items.all %}
                                <tr class="hover:bg-gray-50" x-data="{ 
                                    approved: true, 
                                    approvedQty: {{ line_item.requested_quantity }}, 
                                    itemComments: '',
                                    availableStock: {{ line_item.available_stock|default:0 }}
                                }">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" 
                                               x-model="approved"
                                               @change="updateItemApproval({{ line_item.id }}, approved, approvedQty, itemComments)"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {{ line_item.item_code }}
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-900">
                                        {{ line_item.item_description }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ line_item.unit_of_measure }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ line_item.requested_quantity }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <span :class="availableStock >= {{ line_item.requested_quantity }} ? 'text-green-600' : 'text-red-600'">
                                            {{ line_item.available_stock|default:"0" }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="number" 
                                               x-model="approvedQty"
                                               @input="updateItemApproval({{ line_item.id }}, approved, approvedQty, itemComments)"
                                               min="0" 
                                               max="{{ line_item.requested_quantity }}"
                                               step="0.01"
                                               :disabled="!approved"
                                               class="block w-20 border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span x-show="approved && approvedQty > 0" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Approved
                                        </span>
                                        <span x-show="approved && approvedQty == 0" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            Zero Qty
                                        </span>
                                        <span x-show="!approved" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            Rejected
                                        </span>
                                    </td>
                                    <td class="px-6 py-4">
                                        <input type="text" 
                                               x-model="itemComments"
                                               @input="updateItemApproval({{ line_item.id }}, approved, approvedQty, itemComments)"
                                               placeholder="Optional comments"
                                               class="block w-32 border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Approval Form -->
                <div class="bg-white shadow rounded-lg">
                    <form method="post" x-data="approvalForm()" @submit.prevent="submitApproval">
                        {% csrf_token %}
                        
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Approval Decision</h3>
                            
                            <div class="space-y-6">
                                <!-- Approval Decision -->
                                <div>
                                    <label class="text-base font-medium text-gray-900">Approval Decision</label>
                                    <div class="mt-4 space-y-4">
                                        <div class="flex items-center">
                                            <input id="approve" 
                                                   name="approval_decision" 
                                                   type="radio" 
                                                   value="approve"
                                                   x-model="approvalDecision"
                                                   class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300">
                                            <label for="approve" class="ml-3 block text-sm font-medium text-gray-700">
                                                Approve Requisition
                                            </label>
                                        </div>
                                        <div class="flex items-center">
                                            <input id="partial_approve" 
                                                   name="approval_decision" 
                                                   type="radio" 
                                                   value="partial"
                                                   x-model="approvalDecision"
                                                   class="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300">
                                            <label for="partial_approve" class="ml-3 block text-sm font-medium text-gray-700">
                                                Partially Approve (with modifications)
                                            </label>
                                        </div>
                                        <div class="flex items-center">
                                            <input id="reject" 
                                                   name="approval_decision" 
                                                   type="radio" 
                                                   value="reject"
                                                   x-model="approvalDecision"
                                                   class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300">
                                            <label for="reject" class="ml-3 block text-sm font-medium text-gray-700">
                                                Reject Requisition
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Approval Comments -->
                                <div>
                                    <label for="approval_comments" class="block text-sm font-medium text-gray-700">
                                        Approval Comments
                                        <span x-show="approvalDecision === 'reject'" class="text-red-500">*</span>
                                    </label>
                                    <textarea id="approval_comments" 
                                              name="approval_comments"
                                              x-model="approvalComments"
                                              rows="4"
                                              class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                              :placeholder="approvalDecision === 'reject' ? 'Please provide reason for rejection' : 'Optional comments for approval'"></textarea>
                                </div>

                                <!-- Conditional Fields -->
                                <div x-show="approvalDecision === 'partial'" class="border-l-4 border-yellow-400 bg-yellow-50 p-4">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-yellow-800">Partial Approval Notice</h3>
                                            <div class="mt-2 text-sm text-yellow-700">
                                                <p>Please review and modify individual item quantities in the table above. Items with zero approved quantity will be excluded.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div x-show="approvalDecision === 'reject'" class="border-l-4 border-red-400 bg-red-50 p-4">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-red-800">Rejection Notice</h3>
                                            <div class="mt-2 text-sm text-red-700">
                                                <p>This requisition will be rejected and returned to the requester. Please provide clear reasons for rejection.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="px-4 py-3 bg-gray-50 text-right sm:px-6 space-x-3">
                            <a href="{% url 'inventory:mrs_detail' object.pk %}" 
                               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Cancel
                            </a>
                            <button type="submit" 
                                    :disabled="!canSubmit"
                                    :class="canSubmit ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-400 cursor-not-allowed'"
                                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                Submit Approval
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function approvalForm() {
    return {
        approvalDecision: 'approve',
        approvalComments: '',
        lineItemApprovals: {},
        
        get canSubmit() {
            if (this.approvalDecision === 'reject') {
                return this.approvalComments.trim().length > 0;
            }
            return this.approvalDecision !== '';
        },
        
        updateItemApproval(itemId, approved, quantity, comments) {
            this.lineItemApprovals[itemId] = {
                approved: approved,
                approved_quantity: approved ? quantity : 0,
                comments: comments
            };
        },
        
        toggleAllItems(event) {
            const isChecked = event.target.checked;
            // This would need to be implemented to work with the line item x-data
            console.log('Toggle all items:', isChecked);
        },
        
        async submitApproval() {
            if (!this.canSubmit) return;
            
            const formData = {
                approval_decision: this.approvalDecision,
                approval_comments: this.approvalComments,
                line_item_approvals: this.lineItemApprovals
            };
            
            try {
                const response = await fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(formData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        window.location.href = result.redirect_url || "{% url 'inventory:mrs_detail' object.pk %}";
                    } else {
                        alert(result.error || 'Failed to submit approval');
                    }
                } else {
                    alert('Failed to submit approval');
                }
            } catch (error) {
                console.error('Error submitting approval:', error);
                alert('An error occurred while submitting approval');
            }
        }
    }
}
</script>
{% endblock %}