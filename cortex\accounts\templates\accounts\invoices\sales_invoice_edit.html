<!-- accounts/templates/accounts/invoices/sales_invoice_edit.html -->
<!-- Sales Invoice Edit Form Template -->
<!-- ASP.NET Source: Module/Accounts/Transactions/SalesInvoice_Edit.aspx -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Edit Sales Invoice - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-emerald-600 to-sap-emerald-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="edit" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">
                        Edit Sales Invoice
                    </h1>
                    <p class="text-sm text-sap-gray-600 mt-1">
                        Modify sales invoice details and track payment status
                    </p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <!-- Payment Status Badge -->
                <span class="px-3 py-1 rounded-full text-xs font-medium
                    {% if object.payment_status == 'pending' %}bg-sap-yellow-100 text-sap-yellow-800{% endif %}
                    {% if object.payment_status == 'partial' %}bg-sap-orange-100 text-sap-orange-800{% endif %}
                    {% if object.payment_status == 'paid' %}bg-sap-emerald-100 text-sap-emerald-800{% endif %}
                    {% if object.payment_status == 'overdue' %}bg-sap-red-100 text-sap-red-800{% endif %}">
                    {{ object.get_payment_status_display|default:"Pending" }}
                </span>
                <a href="{% url 'accounts:sales_invoice_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Edit Sales Invoice Form -->
    <div class="max-w-7xl mx-auto">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                            <i data-lucide="edit" class="w-5 h-5 mr-2 text-sap-emerald-600"></i>
                            Sales Invoice Information
                        </h3>
                        <p class="text-sm text-sap-gray-600 mt-1">Update invoice details and manage payment tracking</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="generateCreditNote()" 
                                class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="file-minus" class="w-4 h-4 inline mr-2"></i>
                            Generate Credit Note
                        </button>
                        <button type="button" onclick="recordPayment()" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="credit-card" class="w-4 h-4 inline mr-2"></i>
                            Record Payment
                        </button>
                    </div>
                </div>
            </div>
            
            <form method="post" id="sales-invoice-edit-form" class="p-6" x-data="salesInvoiceEditForm()" hx-post="{% url 'accounts:sales_invoice_edit' object.id %}" hx-target="#form-messages">
                {% csrf_token %}
                
                <!-- Form Messages -->
                <div id="form-messages" class="mb-4"></div>
                
                <!-- Invoice Status Overview -->
                <div class="mb-6 bg-sap-emerald-50 border border-sap-emerald-200 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-sap-emerald-800 mb-3 flex items-center">
                        <i data-lucide="info" class="w-4 h-4 mr-2"></i>
                        Invoice Status Overview
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4 text-sm">
                        <div>
                            <span class="text-sap-emerald-600">Invoice Number:</span>
                            <span class="font-medium text-sap-emerald-800">{{ object.invoice_number|default:"INV-001" }}</span>
                        </div>
                        <div>
                            <span class="text-sap-emerald-600">Invoice Date:</span>
                            <span class="font-medium text-sap-emerald-800">{{ object.invoice_date|date:"M d, Y" }}</span>
                        </div>
                        <div>
                            <span class="text-sap-emerald-600">Due Date:</span>
                            <span class="font-medium text-sap-emerald-800">{{ object.due_date|date:"M d, Y" }}</span>
                        </div>
                        <div>
                            <span class="text-sap-emerald-600">Total Amount:</span>
                            <span class="font-medium text-sap-emerald-800">₹{{ object.total_amount|default:"0.00" }}</span>
                        </div>
                        <div>
                            <span class="text-sap-emerald-600">Balance Due:</span>
                            <span class="font-medium text-sap-emerald-800">₹{{ object.balance_due|default:"0.00" }}</span>
                        </div>
                    </div>
                </div>
                
                <!-- Invoice Header Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="info" class="w-5 h-5 mr-2 text-sap-emerald-600"></i>
                        Invoice Header
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <!-- Invoice Number -->
                        <div>
                            <label for="{{ form.invoice_number.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Invoice Number
                            </label>
                            {{ form.invoice_number|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500 bg-sap-gray-50" }}
                            {% if form.invoice_number.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.invoice_number.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">System generated - cannot be modified</p>
                        </div>
                        
                        <!-- Invoice Date -->
                        <div>
                            <label for="{{ form.invoice_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Invoice Date *
                            </label>
                            {{ form.invoice_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.invoice_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.invoice_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Due Date -->
                        <div>
                            <label for="{{ form.due_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Due Date *
                            </label>
                            {{ form.due_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.due_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.due_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Invoice Type -->
                        <div>
                            <label for="{{ form.invoice_type.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Invoice Type *
                            </label>
                            {{ form.invoice_type|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.invoice_type.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.invoice_type.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Customer Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="user" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Customer Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Customer Name -->
                        <div>
                            <label for="{{ form.customer_name.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Customer Name *
                            </label>
                            {{ form.customer_name|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.customer_name.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.customer_name.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Customer Code -->
                        <div>
                            <label for="{{ form.customer_code.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Customer Code
                            </label>
                            {{ form.customer_code|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.customer_code.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.customer_code.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Customer Address -->
                        <div class="md:col-span-2">
                            <label for="{{ form.customer_address.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Customer Address
                            </label>
                            {{ form.customer_address|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.customer_address.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.customer_address.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Customer GST/VAT Number -->
                        <div>
                            <label for="{{ form.customer_gst_number.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                GST/VAT Number
                            </label>
                            {{ form.customer_gst_number|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.customer_gst_number.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.customer_gst_number.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Customer PAN -->
                        <div>
                            <label for="{{ form.customer_pan.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                PAN Number
                            </label>
                            {{ form.customer_pan|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.customer_pan.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.customer_pan.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Invoice Items Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="package" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                        Invoice Items
                        <button type="button" @click="recalculateItems()" 
                                class="ml-auto bg-sap-purple-100 hover:bg-sap-purple-200 text-sap-purple-700 px-3 py-1 rounded text-sm font-medium transition-colors">
                            <i data-lucide="calculator" class="w-3 h-3 inline mr-1"></i>
                            Recalculate
                        </button>
                    </h4>
                    
                    <!-- Items Table -->
                    <div class="border border-sap-gray-300 rounded-lg overflow-hidden">
                        <table class="min-w-full divide-y divide-sap-gray-200" id="items-table">
                            <thead class="bg-sap-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Item/Service</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Quantity</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Unit Price</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Discount</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Tax Rate</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Amount</th>
                                    <th class="px-4 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Action</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-sap-gray-200" x-ref="itemsTableBody">
                                <template x-for="(item, index) in items" :key="index">
                                    <tr>
                                        <td class="px-4 py-3">
                                            <input type="text" x-model="item.description" 
                                                   class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-emerald-500" 
                                                   placeholder="Item description">
                                        </td>
                                        <td class="px-4 py-3">
                                            <input type="number" x-model="item.quantity" @input="calculateItemAmount(index)" 
                                                   class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-emerald-500" 
                                                   placeholder="Qty" min="0" step="0.01">
                                        </td>
                                        <td class="px-4 py-3">
                                            <input type="number" x-model="item.unit_price" @input="calculateItemAmount(index)" 
                                                   class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-emerald-500" 
                                                   placeholder="Price" min="0" step="0.01">
                                        </td>
                                        <td class="px-4 py-3">
                                            <input type="number" x-model="item.discount_percent" @input="calculateItemAmount(index)" 
                                                   class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-emerald-500" 
                                                   placeholder="%" min="0" max="100" step="0.01">
                                        </td>
                                        <td class="px-4 py-3">
                                            <select x-model="item.tax_rate" @change="calculateItemAmount(index)" 
                                                    class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-emerald-500">
                                                <option value="0">0%</option>
                                                <option value="5">5%</option>
                                                <option value="12">12%</option>
                                                <option value="18">18%</option>
                                                <option value="28">28%</option>
                                            </select>
                                        </td>
                                        <td class="px-4 py-3">
                                            <span class="text-sm font-medium text-sap-gray-900" x-text="'₹' + (item.total_amount || 0).toFixed(2)"></span>
                                        </td>
                                        <td class="px-4 py-3 text-center">
                                            <button type="button" @click="removeItem(index)" 
                                                    class="text-sap-red-600 hover:text-sap-red-900">
                                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                        
                        <!-- Add Item Button -->
                        <div class="px-4 py-3 bg-sap-gray-50 border-t border-sap-gray-200">
                            <button type="button" @click="addItem()" 
                                    class="bg-sap-emerald-600 hover:bg-sap-emerald-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                                Add Item
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Payment Status Tracking -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="credit-card" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Payment Status Tracking
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <!-- Payment Status -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Payment Status
                            </label>
                            <select name="payment_status"
                                    class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                                <option value="pending" {% if object.payment_status == 'pending' %}selected{% endif %}>Pending</option>
                                <option value="partial" {% if object.payment_status == 'partial' %}selected{% endif %}>Partially Paid</option>
                                <option value="paid" {% if object.payment_status == 'paid' %}selected{% endif %}>Paid</option>
                                <option value="overdue" {% if object.payment_status == 'overdue' %}selected{% endif %}>Overdue</option>
                            </select>
                        </div>
                        
                        <!-- Amount Paid -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Amount Paid
                            </label>
                            <input type="number" name="amount_paid" step="0.01" min="0" value="{{ object.amount_paid|default:'0.00' }}"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"
                                   placeholder="Amount paid">
                        </div>
                        
                        <!-- Payment Date -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Last Payment Date
                            </label>
                            <input type="date" name="payment_date" value="{{ object.payment_date|date:'Y-m-d' }}"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        </div>
                        
                        <!-- Payment Method -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Payment Method
                            </label>
                            <select name="payment_method"
                                    class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                                <option value="">Select payment method</option>
                                <option value="cash" {% if object.payment_method == 'cash' %}selected{% endif %}>Cash</option>
                                <option value="cheque" {% if object.payment_method == 'cheque' %}selected{% endif %}>Cheque</option>
                                <option value="bank_transfer" {% if object.payment_method == 'bank_transfer' %}selected{% endif %}>Bank Transfer</option>
                                <option value="credit_card" {% if object.payment_method == 'credit_card' %}selected{% endif %}>Credit Card</option>
                                <option value="online" {% if object.payment_method == 'online' %}selected{% endif %}>Online Payment</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- Updated Invoice Totals Display -->
                <div class="mb-8 bg-sap-emerald-50 border border-sap-emerald-200 rounded-lg p-6">
                    <h4 class="text-lg font-medium text-sap-emerald-800 mb-4 flex items-center">
                        <i data-lucide="calculator" class="w-5 h-5 mr-2 text-sap-emerald-600"></i>
                        Updated Invoice Summary
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div class="text-center">
                            <p class="text-sm text-sap-emerald-600">Subtotal</p>
                            <p class="text-xl font-bold text-sap-emerald-800" x-text="'₹' + subtotal.toFixed(2)"></p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-emerald-600">Discount</p>
                            <p class="text-xl font-bold text-sap-emerald-800" x-text="'₹' + totalDiscount.toFixed(2)"></p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-emerald-600">Tax Amount</p>
                            <p class="text-xl font-bold text-sap-emerald-800" x-text="'₹' + totalTax.toFixed(2)"></p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-emerald-600">Total Amount</p>
                            <p class="text-2xl font-bold text-sap-emerald-800" x-text="'₹' + grandTotal.toFixed(2)"></p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-emerald-600">Balance Due</p>
                            <p class="text-2xl font-bold text-sap-emerald-800" x-text="'₹' + balanceDue.toFixed(2)"></p>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="resetForm()" 
                                class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="rotate-ccw" class="w-4 h-4 inline mr-2"></i>
                            Reset Changes
                        </button>
                        <button type="button" @click="previewInvoice()" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="eye" class="w-4 h-4 inline mr-2"></i>
                            Preview
                        </button>
                        <button type="button" @click="emailInvoice()" 
                                class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="mail" class="w-4 h-4 inline mr-2"></i>
                            Email Invoice
                        </button>
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="{% url 'accounts:sales_invoice_list' %}" 
                           class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-sap-emerald-600 hover:bg-sap-emerald-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                            Update Invoice
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Sales Invoice Edit Guidelines -->
        <div class="mt-6 bg-sap-emerald-50 border border-sap-emerald-200 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i data-lucide="info" class="w-5 h-5 text-sap-emerald-600"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-sap-emerald-800">Sales Invoice Edit Guidelines</h3>
                    <div class="mt-2 text-sm text-sap-emerald-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Invoice number cannot be modified once generated for audit trail integrity</li>
                            <li>Payment status updates automatically trigger customer notifications</li>
                            <li>Item modifications require tax recalculation and compliance verification</li>
                            <li>Credit note generation links to original invoice for proper accounting</li>
                            <li>All changes are tracked with timestamp and user information</li>
                            <li>Updated invoices are automatically included in VAT registers</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function salesInvoiceEditForm() {
    return {
        items: [
            {
                description: '{{ object.item_description|default:"" }}',
                quantity: {{ object.quantity|default:1 }},
                unit_price: {{ object.unit_price|default:0 }},
                discount_percent: {{ object.discount_percent|default:0 }},
                tax_rate: {{ object.tax_rate|default:18 }},
                total_amount: 0
            }
        ],
        subtotal: 0,
        totalDiscount: 0,
        totalTax: 0,
        grandTotal: 0,
        balanceDue: 0,
        amountPaid: {{ object.amount_paid|default:0 }},
        
        init() {
            this.calculateTotals();
            this.items.forEach((item, index) => {
                this.calculateItemAmount(index);
            });
        },
        
        addItem() {
            this.items.push({
                description: '',
                quantity: 0,
                unit_price: 0,
                discount_percent: 0,
                tax_rate: 18,
                total_amount: 0
            });
        },
        
        removeItem(index) {
            if (this.items.length > 1) {
                this.items.splice(index, 1);
                this.calculateTotals();
            }
        },
        
        calculateItemAmount(index) {
            const item = this.items[index];
            const quantity = parseFloat(item.quantity) || 0;
            const unitPrice = parseFloat(item.unit_price) || 0;
            const discountPercent = parseFloat(item.discount_percent) || 0;
            const taxRate = parseFloat(item.tax_rate) || 0;
            
            const lineTotal = quantity * unitPrice;
            const discountAmount = lineTotal * (discountPercent / 100);
            const taxableAmount = lineTotal - discountAmount;
            const taxAmount = taxableAmount * (taxRate / 100);
            
            item.total_amount = taxableAmount + taxAmount;
            
            this.calculateTotals();
        },
        
        calculateTotals() {
            this.subtotal = 0;
            this.totalDiscount = 0;
            this.totalTax = 0;
            
            this.items.forEach(item => {
                const quantity = parseFloat(item.quantity) || 0;
                const unitPrice = parseFloat(item.unit_price) || 0;
                const discountPercent = parseFloat(item.discount_percent) || 0;
                const taxRate = parseFloat(item.tax_rate) || 0;
                
                const lineTotal = quantity * unitPrice;
                const discountAmount = lineTotal * (discountPercent / 100);
                const taxableAmount = lineTotal - discountAmount;
                const taxAmount = taxableAmount * (taxRate / 100);
                
                this.subtotal += lineTotal;
                this.totalDiscount += discountAmount;
                this.totalTax += taxAmount;
            });
            
            this.grandTotal = this.subtotal - this.totalDiscount + this.totalTax;
            this.balanceDue = this.grandTotal - this.amountPaid;
        },
        
        recalculateItems() {
            this.items.forEach((item, index) => {
                this.calculateItemAmount(index);
            });
            alert('All item amounts have been recalculated based on current tax rates.');
        },
        
        previewInvoice() {
            if (this.items.length === 0 || !this.items[0].description) {
                alert('Please add at least one item to preview the invoice.');
                return;
            }
            
            alert('Invoice preview functionality would open in a new window.');
        },
        
        emailInvoice() {
            if (this.grandTotal <= 0) {
                alert('Please ensure invoice has valid amounts before emailing.');
                return;
            }
            
            const customerName = document.getElementById('{{ form.customer_name.id_for_label }}').value;
            if (!customerName.trim()) {
                alert('Please enter customer name before emailing invoice.');
                return;
            }
            
            alert('Email invoice functionality would open email composer with invoice PDF attached.');
        }
    }
}

function generateCreditNote() {
    if (confirm('Generate a credit note for this sales invoice? This will create a new credit note entry.')) {
        alert('Credit note generation functionality would be implemented here.');
    }
}

function recordPayment() {
    const modal = prompt('Enter payment amount to record:', '0.00');
    if (modal && parseFloat(modal) > 0) {
        alert(`Payment of ₹${parseFloat(modal).toFixed(2)} would be recorded against this invoice.`);
    }
}

function resetForm() {
    if (confirm('Are you sure you want to reset all changes? This will reload the original invoice data.')) {
        location.reload();
    }
}

// Auto-calculation and payment tracking
document.addEventListener('DOMContentLoaded', function() {
    // Add change listener for amount paid
    const amountPaidField = document.querySelector('input[name="amount_paid"]');
    if (amountPaidField) {
        amountPaidField.addEventListener('input', function() {
            // Update balance due calculation
            const event = new CustomEvent('alpine:recalculate');
            document.dispatchEvent(event);
        });
    }
    
    // Auto-update payment status based on amount paid
    const paymentStatusField = document.querySelector('select[name="payment_status"]');
    if (amountPaidField && paymentStatusField) {
        amountPaidField.addEventListener('change', function() {
            const amountPaid = parseFloat(this.value) || 0;
            const totalAmount = {{ object.total_amount|default:0 }};
            
            if (amountPaid === 0) {
                paymentStatusField.value = 'pending';
            } else if (amountPaid >= totalAmount) {
                paymentStatusField.value = 'paid';
            } else {
                paymentStatusField.value = 'partial';
            }
        });
    }
    
    lucide.createIcons();
});
</script>
{% endblock %}