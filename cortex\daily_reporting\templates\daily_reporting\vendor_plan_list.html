<!-- daily_reporting/templates/daily_reporting/vendor_plan_list.html -->
<!-- Vendor Plan List Template -->

{% extends 'core/base.html' %}

{% block title %}Vendor Plan - Daily Reporting{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-blue-600 to-sap-blue-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="truck" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Vendor Plan Management</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Track vendor planning and processing activities</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'daily_reporting:dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Dashboard
                </a>
                <a href="{% url 'daily_reporting:vendor_plan_create' %}" 
                   class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    Add Vendor Plan
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Total Plans</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ vendor_plans.count }}</p>
                </div>
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="truck" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Active WOs</p>
                    <p class="text-2xl font-bold text-sap-blue-600">{{ active_work_orders|default:0 }}</p>
                </div>
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="file-text" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">This Week</p>
                    <p class="text-2xl font-bold text-sap-green-600">{{ plans_this_week|default:0 }}</p>
                </div>
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="calendar" class="w-6 h-6 text-sap-green-600"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Processing</p>
                    <p class="text-2xl font-bold text-sap-orange-600">{{ processing_count|default:0 }}</p>
                </div>
                <div class="w-12 h-12 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="settings" class="w-6 h-6 text-sap-orange-600"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Search and Filters -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm mb-6">
        <div class="p-6">
            <form method="get" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-sap-gray-700 mb-2">Search</label>
                    <input type="text" name="search" id="search" value="{{ request.GET.search }}"
                           placeholder="Search by WO number, fixture..."
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                </div>
                <div>
                    <label for="status" class="block text-sm font-medium text-sap-gray-700 mb-2">Status</label>
                    <select name="status" id="status" 
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        <option value="">All Status</option>
                        <option value="planning">Planning</option>
                        <option value="processing">Processing</option>
                        <option value="completed">Completed</option>
                    </select>
                </div>
                <div>
                    <label for="date_from" class="block text-sm font-medium text-sap-gray-700 mb-2">Date From</label>
                    <input type="date" name="date_from" id="date_from" value="{{ request.GET.date_from }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                </div>
                <div class="flex items-end">
                    <button type="submit" 
                            class="w-full bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="search" class="w-4 h-4 inline mr-2"></i>
                        Search
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Vendor Plans Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Vendor Plans</h3>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-sap-gray-600">{{ vendor_plans.count }} plans</span>
                    <div class="flex items-center space-x-1">
                        <button class="text-sap-gray-600 hover:text-sap-gray-800 p-1" title="Export Data">
                            <i data-lucide="download" class="w-4 h-4"></i>
                        </button>
                        <button class="text-sap-gray-600 hover:text-sap-gray-800 p-1" title="Refresh Data">
                            <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        {% if vendor_plans %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Work Order
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Fixture Details
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Parts Info
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Processing Status
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Date
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for plan in vendor_plans %}
                    <tr class="hover:bg-sap-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-sap-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i data-lucide="clipboard" class="w-4 h-4 text-sap-blue-600"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-sap-gray-900">{{ plan.wo_number }}</div>
                                    <div class="text-sm text-sap-gray-500">SN: {{ plan.serial_number }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-sap-gray-900">{{ plan.fixture_number }}</div>
                            <div class="text-sm text-sap-gray-500">{{ plan.planning|truncatechars:30 }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-sap-gray-900">
                                <div>Mfg: {{ plan.number_parts_manufacturing }}</div>
                                <div class="text-sap-gray-500">Received: {{ plan.number_parts_received }}</div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-sap-gray-900">
                                <div class="flex items-center space-x-2">
                                    {% if plan.weldment_fabrication %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-blue-100 text-sap-blue-800">
                                        Processing
                                    </span>
                                    {% else %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-yellow-100 text-sap-yellow-800">
                                        Planning
                                    </span>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-500">
                            {{ plan.sys_date|date:"M d, Y" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <a href="{% url 'daily_reporting:vendor_plan_detail' plan.id %}" 
                                   class="text-sap-blue-600 hover:text-sap-blue-700 p-1" title="View Details">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                </a>
                                <a href="{% url 'daily_reporting:vendor_plan_edit' plan.id %}" 
                                   class="text-sap-orange-600 hover:text-sap-orange-700 p-1" title="Edit">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </a>
                                <button class="text-red-600 hover:text-red-700 p-1" title="Delete"
                                        onclick="return confirm('Are you sure you want to delete this vendor plan?')">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-sap-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-sap-gray-300 text-sm font-medium rounded-md text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                        Previous
                    </a>
                {% endif %}
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-sap-gray-300 text-sm font-medium rounded-md text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                        Next
                    </a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-sap-gray-700">
                        Showing
                        <span class="font-medium">{{ page_obj.start_index }}</span>
                        to
                        <span class="font-medium">{{ page_obj.end_index }}</span>
                        of
                        <span class="font-medium">{{ paginator.count }}</span>
                        results
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-500 hover:bg-sap-gray-50">
                                <i data-lucide="chevron-left" class="h-5 w-5"></i>
                            </a>
                        {% endif %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-700">
                            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                        </span>
                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-500 hover:bg-sap-gray-50">
                                <i data-lucide="chevron-right" class="h-5 w-5"></i>
                            </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
        {% endif %}
        
        {% else %}
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="w-24 h-24 bg-sap-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i data-lucide="truck" class="w-12 h-12 text-sap-blue-600"></i>
            </div>
            <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No Vendor Plans Found</h3>
            <p class="text-sap-gray-600 mb-6">Get started by creating your first vendor plan.</p>
            <a href="{% url 'daily_reporting:vendor_plan_create' %}" 
               class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                Create First Plan
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize lucide icons
    lucide.createIcons();
});
</script>
{% endblock %}