{% extends 'core/base.html' %}
{% load static %}

{% block title %}Delete Document - MR Office{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="bg-white rounded-lg shadow-sm border p-8">
            <div class="flex items-center mb-6">
                <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                    <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
            </div>
            
            <div class="text-center">
                <h3 class="text-lg font-medium text-gray-900 mb-2">Delete Document</h3>
                <p class="text-sm text-gray-500 mb-6">
                    Are you sure you want to delete this document? This action cannot be undone.
                </p>
            </div>

            <!-- Document Info -->
            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                <dl class="space-y-2">
                    <div class="flex justify-between">
                        <dt class="text-sm font-medium text-gray-500">Format/Document:</dt>
                        <dd class="text-sm text-gray-900">{{ object.format }}</dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-sm font-medium text-gray-500">Module:</dt>
                        <dd class="text-sm text-gray-900">{{ object.get_module_name }}</dd>
                    </div>
                    {% if object.filename %}
                    <div class="flex justify-between">
                        <dt class="text-sm font-medium text-gray-500">File:</dt>
                        <dd class="text-sm text-gray-900">{{ object.filename }}</dd>
                    </div>
                    {% endif %}
                </dl>
            </div>

            <form method="post">
                {% csrf_token %}
                <div class="flex items-center justify-end space-x-4">
                    <a href="{% url 'mr_office:document_detail' object.pk %}" 
                       class="bg-white border border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors duration-200">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        Delete Document
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}