{% extends "core/base.html" %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-sap-blue-500 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Customer Enquiries</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage and track customer enquiries</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <div class="text-right mr-4 px-4 py-2 bg-sap-gray-50 rounded-lg border border-sap-gray-200">
                    <p class="text-2xs font-medium text-sap-gray-500 uppercase tracking-wide">Total Enquiries</p>
                    <p class="text-lg font-semibold text-sap-blue-600">{{ enquiries|length }}</p>
                </div>
                <a href="{% url 'sales_distribution:enquiry_create' %}" 
                   class="inline-flex items-center px-4 py-2.5 border border-transparent rounded-lg text-sm font-medium text-white bg-sap-blue-600 hover:bg-sap-blue-700 transition-all duration-200">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    Create New Enquiry
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-2 py-4 space-y-2">
    
    <!-- Search and Filter Card -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4">
            <div class="flex items-center space-x-4">
                <div class="flex-1">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i data-lucide="search" class="w-4 h-4 text-sap-gray-400"></i>
                        </div>
                        <input type="text" 
                               id="real-time-search"
                               name="search" 
                               placeholder="Search enquiries by customer name or enquiry number..." 
                               value="{{ request.GET.search|default:'' }}"
                               class="block w-full pl-10 pr-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm placeholder-sap-gray-500 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                    </div>
                </div>
                <div class="w-48">
                    <select name="financial_year" 
                            id="financial-year-filter"
                            class="block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        <option value="">All Financial Years</option>
                        <option value="2022-2023" {% if request.GET.financial_year == "2022-2023" %}selected{% endif %}>2022-2023</option>
                        <option value="2023-2024" {% if request.GET.financial_year == "2023-2024" %}selected{% endif %}>2023-2024</option>
                    </select>
                </div>
                <div class="flex space-x-2">
                    <button type="button" 
                            onclick="clearSearch()"
                            class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                        <i data-lucide="x" class="w-4 h-4 mr-2"></i>
                        Clear
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Enquiries Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Customer Enquiries</h3>
                <div class="text-sm text-sap-gray-600">
                    Showing {{ enquiries|length }} enquir{{ enquiries|length|pluralize:"y,ies" }}
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            {% if enquiries %}
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider w-16">
                            SN
                        </th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider w-24">
                            Fin Yrs
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Customer
                        </th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider w-20">
                            Code
                        </th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider w-24">
                            Enquiry No.
                        </th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider w-28">
                            Gen. Date
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Gen. By
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for enquiry in enquiries %}
                    <tr class="hover:bg-sap-gray-50 transition-colors duration-150">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900 text-center">
                            {{ forloop.counter }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900 text-center">
                            {% if enquiry.finyearid %}
                                {{ enquiry.finyearid.finyear|default:"2022-2023" }}
                            {% else %}
                                2022-2023
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-sap-blue-600">
                                <a href="{% url 'sales_distribution:enquiry_detail' enquiry.enqid %}" class="hover:text-sap-blue-800">
                                    {{ enquiry.customername|default:"UNKNOWN CUSTOMER" }}
                                </a>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900 text-center">
                            {{ enquiry.customerid|default:"N/A" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900 text-center">
                            {{ enquiry.enqid }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900 text-center">
                            {{ enquiry.sysdate|default:"N/A" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">
                            {{ enquiry.sessionid|default:"Mr.Vinodkumar Sharmrao kadepure" }}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <div class="px-6 py-12 text-center">
                <div class="w-16 h-16 mx-auto mb-4 bg-sap-gray-100 rounded-full flex items-center justify-center">
                    <i data-lucide="file-text" class="w-8 h-8 text-sap-gray-400"></i>
                </div>
                <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No Enquiries Found</h3>
                <p class="text-sm text-sap-gray-600 mb-6">Create your first customer enquiry to get started.</p>
                <a href="{% url 'sales_distribution:enquiry_create' %}" 
                   class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-sap-blue-600 hover:bg-sap-blue-700">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    Create First Enquiry
                </a>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="text-sm text-sap-gray-600">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ paginator.count }} results
                </div>
                <div class="flex space-x-2">
                    {% if page_obj.has_previous %}
                        <a href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.financial_year %}&financial_year={{ request.GET.financial_year }}{% endif %}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            First
                        </a>
                        <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.financial_year %}&financial_year={{ request.GET.financial_year }}{% endif %}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Previous
                        </a>
                    {% endif %}
                    
                    <span class="px-3 py-2 bg-sap-blue-50 border border-sap-blue-200 rounded-lg text-sm font-medium text-sap-blue-600">
                        Page {{ page_obj.number }} of {{ paginator.num_pages }}
                    </span>
                    
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.financial_year %}&financial_year={{ request.GET.financial_year }}{% endif %}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Next
                        </a>
                        <a href="?page={{ paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.financial_year %}&financial_year={{ request.GET.financial_year }}{% endif %}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Last
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

</div>

<script>
// Clear search function
function clearSearch() {
    document.getElementById('real-time-search').value = '';
    document.getElementById('financial-year-filter').value = '';
    // Reload page without parameters
    window.location.href = window.location.pathname;
}

// Enhanced search functionality can be added here
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit search on Enter key
    const searchInput = document.getElementById('real-time-search');
    const yearFilter = document.getElementById('financial-year-filter');
    
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    }
    
    if (yearFilter) {
        yearFilter.addEventListener('change', function() {
            performSearch();
        });
    }
    
    function performSearch() {
        const search = searchInput ? searchInput.value : '';
        const year = yearFilter ? yearFilter.value : '';
        
        let url = window.location.pathname + '?';
        const params = [];
        
        if (search) params.push('search=' + encodeURIComponent(search));
        if (year) params.push('financial_year=' + encodeURIComponent(year));
        
        url += params.join('&');
        window.location.href = url;
    }
});
</script>
{% endblock %}
