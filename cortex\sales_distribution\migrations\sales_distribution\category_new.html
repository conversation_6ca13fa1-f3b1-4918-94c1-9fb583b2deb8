<!-- sales_distribution/templates/sales_distribution/category_new.html -->
<!-- Professional Category New - SAP S/4HANA Inspired Design -->
<!-- Replaces ASP.NET CategoryNew.aspx functionality -->

{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }} - Cortex{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-sap-green-500 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="plus-circle" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Category Of Work Order - New</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Add new work order categories and view existing ones</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <div class="text-right mr-4 px-4 py-2 bg-sap-gray-50 rounded-lg border border-sap-gray-200">
                    <p class="text-2xs font-medium text-sap-gray-500 uppercase tracking-wide">Total Categories</p>
                    <p class="text-lg font-semibold text-sap-green-600" id="category-count">{{ categories|length }}</p>
                </div>
                <a href="{% url 'sales_distribution:category_list' %}" 
                   class="inline-flex items-center px-4 py-2.5 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 hover:border-sap-blue-300 transition-all duration-200">
                    <i data-lucide="edit" class="w-4 h-4 mr-2"></i>
                    Manage Categories
                </a>
                <a href="{% url 'sales_distribution:dashboard' %}" 
                   class="inline-flex items-center px-4 py-2.5 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 hover:border-sap-blue-300 transition-all duration-200">
                    <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-2 py-4 space-y-4">
    
    <!-- Add New Category Form Card -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="plus" class="w-4 h-4 text-sap-green-600"></i>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-sap-gray-800">Add New Category</h3>
                    <p class="text-sm text-sap-gray-600">Create a new work order category</p>
                </div>
            </div>
        </div>
        
        <div class="px-6 py-6">
            {% if messages %}
                {% for message in messages %}
                    <div class="mb-4 {% if message.tags == 'error' %}bg-red-50 border border-red-200{% else %}bg-green-50 border border-green-200{% endif %} rounded-lg p-4">
                        <div class="flex">
                            {% if message.tags == 'error' %}
                                <i data-lucide="alert-circle" class="w-5 h-5 text-red-400 mr-2 mt-0.5"></i>
                                <div class="text-sm text-red-600">{{ message }}</div>
                            {% else %}
                                <i data-lucide="check-circle" class="w-5 h-5 text-green-400 mr-2 mt-0.5"></i>
                                <div class="text-sm text-green-600">{{ message }}</div>
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
            
            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="{{ category_form.cname.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Category Name <span class="text-red-500">*</span>
                        </label>
                        {{ category_form.cname }}
                        {% if category_form.cname.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ category_form.cname.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ category_form.symbol.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Symbol <span class="text-red-500">*</span>
                            <span class="text-xs text-sap-gray-500">(1 character)</span>
                        </label>
                        {{ category_form.symbol }}
                        {% if category_form.symbol.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ category_form.symbol.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div class="flex items-center space-x-3 pt-6">
                        <div class="flex items-center">
                            {{ category_form.hassubcat }}
                            <label for="{{ category_form.hassubcat.id_for_label }}" class="ml-2 text-sm font-medium text-sap-gray-700">
                                Has Sub-Categories
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-start">
                    <button type="submit" 
                            onclick="return confirmationAdd();"
                            class="inline-flex items-center px-6 py-3 bg-sap-green-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-sap-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-green-500 transition-colors duration-200">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Insert Category
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Real-time Search for Existing Categories -->
    {% if categories %}
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Search Existing Categories</h3>
                <div class="text-sm text-sap-gray-600" data-search-status>
                    Real-time search through {{ categories|length }} categories
                </div>
            </div>
        </div>
        <div class="px-6 py-4">
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i data-lucide="search" class="w-4 h-4 text-sap-gray-400"></i>
                </div>
                <input type="text" 
                       id="real-time-search"
                       placeholder="Search categories in real-time..." 
                       oninput="filterCategories(this.value)"
                       class="block w-full pl-10 pr-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm placeholder-sap-gray-500 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
            </div>
            <div class="mt-2 text-xs text-sap-gray-500">
                <i data-lucide="info" class="w-3 h-3 inline mr-1"></i>
                Search results update automatically as you type
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Existing Categories Table -->
    {% if categories %}
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <h3 class="text-lg font-medium text-sap-gray-800">Existing Categories</h3>
            <p class="text-sm text-sap-gray-600 mt-1">View all work order categories</p>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider w-16">
                            SN
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Category Name
                        </th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider w-24">
                            Symbol
                        </th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider w-32">
                            Has Sub-Category
                        </th>
                    </tr>
                </thead>
                <tbody id="categories-tbody" class="bg-white divide-y divide-sap-gray-200">
                    {% for category in categories %}
                    <tr class="category-row hover:bg-sap-gray-50" data-category-name="{{ category.cname|lower }}" data-symbol="{{ category.symbol|lower }}" data-hassubcat="{{ category.hassubcat }}">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900 text-right">
                            {{ forloop.counter }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-sap-gray-900">{{ category.cname }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-gray-100 text-sap-gray-800">
                                {{ category.symbol|default:"-" }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            {% if category.hassubcat == "1" %}
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i data-lucide="check" class="w-3 h-3 mr-1"></i>
                                Yes
                            </span>
                            {% else %}
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                <i data-lucide="x" class="w-3 h-3 mr-1"></i>
                                No
                            </span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="px-6 py-4 border-t border-sap-gray-100">
            <div class="flex items-center justify-between">
                <div class="text-sm text-sap-gray-600">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ paginator.count }} results
                </div>
                <div class="flex space-x-2">
                    {% if page_obj.has_previous %}
                        <a href="?page=1" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            First
                        </a>
                        <a href="?page={{ page_obj.previous_page_number }}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Previous
                        </a>
                    {% endif %}
                    
                    <span class="px-3 py-2 bg-sap-green-50 border border-sap-green-200 rounded-lg text-sm font-medium text-sap-green-600">
                        Page {{ page_obj.number }} of {{ paginator.num_pages }}
                    </span>
                    
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Next
                        </a>
                        <a href="?page={{ paginator.num_pages }}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Last
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    {% else %}
    <!-- Empty State -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-12 text-center">
            <div class="flex flex-col items-center">
                <i data-lucide="inbox" class="w-16 h-16 text-sap-gray-400 mb-4"></i>
                <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No categories found</h3>
                <p class="text-sm text-sap-gray-600 mb-6">You haven't created any work order categories yet. Use the form above to add your first category.</p>
            </div>
        </div>
    </div>
    {% endif %}

</div>

<script>
// Confirmation dialog - matches ASP.NET OnClientClick="return confirmationAdd()"
function confirmationAdd() {
    return confirm('Are you sure you want to add this category?');
}

// Real-time search filtering function
function filterCategories(searchTerm) {
    const searchQuery = searchTerm.toLowerCase().trim();
    const rows = document.querySelectorAll('.category-row');
    let visibleCount = 0;
    
    rows.forEach(function(row) {
        const categoryName = row.getAttribute('data-category-name');
        const symbol = row.getAttribute('data-symbol');
        
        const isVisible = categoryName.includes(searchQuery) || symbol.includes(searchQuery);
        
        if (isVisible) {
            row.style.display = '';
            visibleCount++;
            // Update row number
            const serialCell = row.querySelector('td:first-child');
            if (serialCell) {
                serialCell.textContent = visibleCount;
            }
        } else {
            row.style.display = 'none';
        }
    });
    
    // Update the search status message
    updateSearchStatus(visibleCount, rows.length, searchQuery);
}

// Update search status
function updateSearchStatus(visibleCount, totalCount, searchQuery) {
    const statusElement = document.querySelector('[data-search-status]');
    if (statusElement) {
        if (searchQuery) {
            statusElement.textContent = `Showing ${visibleCount} of ${totalCount} categories matching "${searchQuery}"`;
        } else {
            statusElement.textContent = `Real-time search through ${totalCount} categories`;
        }
    }
}

// Auto-focus on category name field when page loads
document.addEventListener('DOMContentLoaded', function() {
    const categoryNameField = document.getElementById('{{ category_form.cname.id_for_label }}');
    if (categoryNameField) {
        categoryNameField.focus();
    }
});
</script>
{% endblock %}