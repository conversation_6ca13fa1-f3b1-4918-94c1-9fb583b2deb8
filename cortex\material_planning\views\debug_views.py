"""
Debug Views for Material Planning
Simple views for testing and debugging
"""

from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
import json

from ..models import Process


class DebugProcessView(LoginRequiredMixin, TemplateView):
    """Simple debug view for testing process display"""
    template_name = 'material_planning/processes/simple_debug.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get all processes
        processes = Process.objects.all()
        
        # Convert to simple data structure
        processes_data = []
        for process in processes:
            processes_data.append({
                'id': process.id,
                'processname': process.processname or 'Unnamed Process',
                'symbol': process.symbol or '-'
            })
        
        # Convert to JSON string for template
        context['processes'] = json.dumps(processes_data)
        
        # Also provide raw data for debugging
        context['processes_raw'] = processes_data
        context['processes_count'] = len(processes_data)
        
        return context