from django import forms
from django.core.exceptions import ValidationError
from datetime import date


class ABCAnalysisForm(forms.Form):
    """Form for ABC Analysis parameters"""
    
    ANALYSIS_PERIOD_CHOICES = [
        ('3_months', 'Last 3 Months'),
        ('6_months', 'Last 6 Months'),
        ('12_months', 'Last 12 Months'),
        ('custom', 'Custom Date Range'),
    ]
    
    CLASSIFICATION_CRITERIA_CHOICES = [
        ('value', 'By Value'),
        ('quantity', 'By Quantity'),
        ('frequency', 'By Transaction Frequency'),
    ]
    
    analysis_period = forms.ChoiceField(
        choices=ANALYSIS_PERIOD_CHOICES,
        initial='12_months',
        widget=forms.Select(attrs={
            'class': 'form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'required': True
        })
    )
    
    classification_criteria = forms.ChoiceField(
        choices=CLASSIFICATION_CRITERIA_CHOICES,
        initial='value',
        widget=forms.Select(attrs={
            'class': 'form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'required': True
        })
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500'
        }),
        help_text='Required if Custom Date Range is selected'
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500'
        }),
        help_text='Required if Custom Date Range is selected'
    )
    
    a_percentage = forms.DecimalField(
        initial=20,
        min_value=1,
        max_value=99,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'step': '0.01'
        }),
        help_text='Percentage of items to classify as A category (default: 20%)'
    )
    
    b_percentage = forms.DecimalField(
        initial=30,
        min_value=1,
        max_value=99,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'step': '0.01'
        }),
        help_text='Percentage of items to classify as B category (default: 30%)'
    )
    
    include_zero_movement = forms.BooleanField(
        required=False,
        initial=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500'
        }),
        help_text='Include items with zero movement in the analysis'
    )
    
    category_filter = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'placeholder': 'Filter by item category (optional)'
        })
    )
    
    def clean(self):
        cleaned_data = super().clean()
        analysis_period = cleaned_data.get('analysis_period')
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        a_percentage = cleaned_data.get('a_percentage')
        b_percentage = cleaned_data.get('b_percentage')
        
        # Validate custom date range
        if analysis_period == 'custom':
            if not date_from or not date_to:
                raise ValidationError("Start and end dates are required for custom date range.")
            if date_from > date_to:
                raise ValidationError("Start date cannot be after end date.")
            if date_to > date.today():
                raise ValidationError("End date cannot be in the future.")
        
        # Validate ABC percentages
        if a_percentage and b_percentage:
            total_ab = a_percentage + b_percentage
            if total_ab >= 100:
                raise ValidationError("A and B percentages combined must be less than 100%.")
        
        return cleaned_data


class MovingNonMovingAnalysisForm(forms.Form):
    """Form for Moving/Non-moving Items Analysis"""
    
    MOVEMENT_PERIOD_CHOICES = [
        ('30_days', 'Last 30 Days'),
        ('60_days', 'Last 60 Days'),
        ('90_days', 'Last 90 Days'),
        ('180_days', 'Last 180 Days'),
        ('365_days', 'Last 365 Days'),
        ('custom', 'Custom Date Range'),
    ]
    
    ANALYSIS_TYPE_CHOICES = [
        ('all', 'All Items'),
        ('moving', 'Moving Items Only'),
        ('non_moving', 'Non-moving Items Only'),
        ('slow_moving', 'Slow Moving Items'),
    ]
    
    movement_period = forms.ChoiceField(
        choices=MOVEMENT_PERIOD_CHOICES,
        initial='90_days',
        widget=forms.Select(attrs={
            'class': 'form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'required': True
        })
    )
    
    analysis_type = forms.ChoiceField(
        choices=ANALYSIS_TYPE_CHOICES,
        initial='all',
        widget=forms.Select(attrs={
            'class': 'form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'required': True
        })
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500'
        }),
        help_text='Required if Custom Date Range is selected'
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500'
        }),
        help_text='Required if Custom Date Range is selected'
    )
    
    minimum_stock_value = forms.DecimalField(
        required=False,
        min_value=0,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'step': '0.01',
            'placeholder': 'Minimum stock value to include'
        }),
        help_text='Filter items by minimum stock value (optional)'
    )
    
    slow_moving_threshold = forms.IntegerField(
        initial=5,
        min_value=1,
        widget=forms.NumberInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'min': '1'
        }),
        help_text='Number of transactions to classify as slow moving (default: 5 or less)'
    )
    
    category_filter = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'placeholder': 'Filter by item category (optional)'
        })
    )
    
    location_filter = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'placeholder': 'Filter by location (optional)'
        })
    )
    
    include_zero_stock = forms.BooleanField(
        required=False,
        initial=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500'
        }),
        help_text='Include items with zero current stock'
    )
    
    def clean(self):
        cleaned_data = super().clean()
        movement_period = cleaned_data.get('movement_period')
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        
        # Validate custom date range
        if movement_period == 'custom':
            if not date_from or not date_to:
                raise ValidationError("Start and end dates are required for custom date range.")
            if date_from > date_to:
                raise ValidationError("Start date cannot be after end date.")
            if date_to > date.today():
                raise ValidationError("End date cannot be in the future.")
        
        return cleaned_data


class StockStatementForm(forms.Form):
    """Form for Stock Statement generation"""
    
    STATEMENT_TYPE_CHOICES = [
        ('summary', 'Summary Statement'),
        ('detailed', 'Detailed Statement'),
        ('category_wise', 'Category-wise Statement'),
        ('location_wise', 'Location-wise Statement'),
        ('valuation', 'Stock Valuation Statement'),
    ]
    
    VALUATION_METHOD_CHOICES = [
        ('fifo', 'FIFO (First In, First Out)'),
        ('lifo', 'LIFO (Last In, First Out)'),
        ('avg_cost', 'Average Cost'),
        ('standard_cost', 'Standard Cost'),
    ]
    
    statement_type = forms.ChoiceField(
        choices=STATEMENT_TYPE_CHOICES,
        initial='summary',
        widget=forms.Select(attrs={
            'class': 'form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'required': True
        })
    )
    
    as_on_date = forms.DateField(
        initial=date.today,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'required': True
        }),
        help_text='Stock statement as on this date'
    )
    
    valuation_method = forms.ChoiceField(
        choices=VALUATION_METHOD_CHOICES,
        initial='avg_cost',
        widget=forms.Select(attrs={
            'class': 'form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500'
        }),
        help_text='Valuation method for stock value calculation'
    )
    
    category_filter = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'placeholder': 'Filter by category'
        })
    )
    
    location_filter = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'placeholder': 'Filter by location'
        })
    )
    
    include_zero_stock = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500'
        }),
        help_text='Include items with zero stock'
    )
    
    include_negative_stock = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500'
        }),
        help_text='Include items with negative stock'
    )
    
    show_item_details = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500'
        }),
        help_text='Show detailed item information'
    )
    
    def clean_as_on_date(self):
        as_on_date = self.cleaned_data.get('as_on_date')
        if as_on_date and as_on_date > date.today():
            raise ValidationError("Statement date cannot be in the future.")
        return as_on_date


class ClosingStockForm(forms.Form):
    """Form for Closing Stock processing"""
    
    closing_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'required': True
        }),
        help_text='Date for closing stock calculation'
    )
    
    include_wip = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500'
        }),
        label='Include Work-in-Progress',
        help_text='Include WIP stock in closing calculation'
    )
    
    adjust_negative_stock = forms.BooleanField(
        required=False,
        initial=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500'
        }),
        help_text='Automatically adjust negative stock to zero'
    )
    
    create_adjustment_entries = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500'
        }),
        help_text='Create stock adjustment entries for discrepancies'
    )
    
    remarks = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-textarea rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'rows': 3,
            'placeholder': 'Remarks for closing stock process'
        })
    )
    
    def clean_closing_date(self):
        closing_date = self.cleaned_data.get('closing_date')
        if closing_date and closing_date > date.today():
            raise ValidationError("Closing date cannot be in the future.")
        return closing_date


class ReportExportForm(forms.Form):
    """Form for report export options"""
    
    EXPORT_FORMAT_CHOICES = [
        ('pdf', 'PDF'),
        ('excel', 'Excel (XLSX)'),
        ('csv', 'CSV'),
    ]
    
    PAGE_SIZE_CHOICES = [
        ('A4', 'A4'),
        ('A3', 'A3'),
        ('Letter', 'Letter'),
        ('Legal', 'Legal'),
    ]
    
    ORIENTATION_CHOICES = [
        ('portrait', 'Portrait'),
        ('landscape', 'Landscape'),
    ]
    
    export_format = forms.ChoiceField(
        choices=EXPORT_FORMAT_CHOICES,
        initial='pdf',
        widget=forms.Select(attrs={
            'class': 'form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'required': True
        })
    )
    
    page_size = forms.ChoiceField(
        choices=PAGE_SIZE_CHOICES,
        initial='A4',
        widget=forms.Select(attrs={
            'class': 'form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500'
        })
    )
    
    orientation = forms.ChoiceField(
        choices=ORIENTATION_CHOICES,
        initial='portrait',
        widget=forms.Select(attrs={
            'class': 'form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500'
        })
    )
    
    include_charts = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500'
        }),
        help_text='Include charts and graphs in the export'
    )
    
    include_summary = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500'
        }),
        help_text='Include summary statistics'
    )
    
    company_header = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500'
        }),
        help_text='Include company header and logo'
    )