from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from sys_admin.models import Company, FinancialYear
from .models import ItemLocation, WISTimeConfiguration


class ItemLocationModelTest(TestCase):
    """Test cases for ItemLocation model"""
    
    def setUp(self):
        self.user = User.objects.create_user(username='testuser', password='testpass123')
        self.company = Company.objects.create(company_name='Test Company')
        self.financial_year = FinancialYear.objects.create(
            company=self.company,
            finyear='2023-24',
            fromdate='2023-04-01',
            todate='2024-03-31'
        )
    
    def test_create_warehouse_location(self):
        """Test creating a warehouse location"""
        location = ItemLocation.objects.create(
            company=self.company,
            financial_year=self.financial_year,
            location_code='WH001',
            location_name='Main Warehouse',
            location_type='WAREHOUSE',
            warehouse_code='WH001',
            capacity=1000.0,
            created_by=self.user
        )
        
        self.assertEqual(location.location_code, 'WH001')
        self.assertEqual(location.location_type, 'WAREHOUSE')
        self.assertEqual(location.capacity, 1000.0)
        self.assertEqual(location.current_stock, 0.0)
        self.assertTrue(location.is_active)
    
    def test_location_hierarchy(self):
        """Test location hierarchy (warehouse -> zone -> bin)"""
        # Create warehouse
        warehouse = ItemLocation.objects.create(
            company=self.company,
            financial_year=self.financial_year,
            location_code='WH001',
            location_name='Main Warehouse',
            location_type='WAREHOUSE',
            warehouse_code='WH001',
            created_by=self.user
        )
        
        # Create zone under warehouse
        zone = ItemLocation.objects.create(
            company=self.company,
            financial_year=self.financial_year,
            location_code='ZN001',
            location_name='Zone A',
            location_type='ZONE',
            zone_code='ZN001',
            parent_location=warehouse,
            created_by=self.user
        )
        
        # Create bin under zone
        bin_location = ItemLocation.objects.create(
            company=self.company,
            financial_year=self.financial_year,
            location_code='BN001',
            location_name='Bin A1',
            location_type='BIN',
            bin_code='BN001',
            parent_location=zone,
            created_by=self.user
        )
        
        self.assertEqual(zone.parent_location, warehouse)
        self.assertEqual(bin_location.parent_location, zone)
        self.assertIn('WH001 > ZN001 > BN001', bin_location.full_location_path)
    
    def test_utilization_percentage(self):
        """Test utilization percentage calculation"""
        location = ItemLocation.objects.create(
            company=self.company,
            financial_year=self.financial_year,
            location_code='BN001',
            location_name='Test Bin',
            location_type='BIN',
            bin_code='BN001',
            capacity=100.0,
            current_stock=25.0,
            created_by=self.user
        )
        
        self.assertEqual(location.utilization_percentage, 25.0)


class ItemLocationViewTest(TestCase):
    """Test cases for ItemLocation views"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(username='testuser', password='testpass123')
        self.company = Company.objects.create(company_name='Test Company')
        self.financial_year = FinancialYear.objects.create(
            company=self.company,
            finyear='2023-24',
            fromdate='2023-04-01',
            todate='2024-03-31'
        )
        
        # Create a test location
        self.location = ItemLocation.objects.create(
            company=self.company,
            financial_year=self.financial_year,
            location_code='WH001',
            location_name='Test Warehouse',
            location_type='WAREHOUSE',
            warehouse_code='WH001',
            created_by=self.user
        )
    
    def test_location_list_view_requires_login(self):
        """Test that location list view requires authentication"""
        response = self.client.get(reverse('inventory:location_list'))
        self.assertEqual(response.status_code, 302)  # Redirect to login
    
    def test_location_list_view_with_login(self):
        """Test location list view with authenticated user"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('inventory:location_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Item Locations')
    
    def test_location_detail_view(self):
        """Test location detail view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('inventory:location_detail', kwargs={'pk': self.location.pk}))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.location.location_code)
        self.assertContains(response, self.location.location_name)


class WISTimeConfigurationTest(TestCase):
    """Test cases for WISTimeConfiguration model"""
    
    def setUp(self):
        self.user = User.objects.create_user(username='testuser', password='testpass123')
        self.company = Company.objects.create(company_name='Test Company')
        self.financial_year = FinancialYear.objects.create(
            company=self.company,
            finyear='2023-24',
            fromdate='2023-04-01',
            todate='2024-03-31'
        )
    
    def test_create_wis_configuration(self):
        """Test creating a WIS time configuration"""
        config = WISTimeConfiguration.objects.create(
            company=self.company,
            financial_year=self.financial_year,
            config_name='Default WIS Config',
            auto_issue_time='09:00:00',
            auto_return_time='17:00:00',
            batch_size=50,
            retry_interval_minutes=15,
            max_retry_attempts=3,
            is_enabled=True,
            created_by=self.user
        )
        
        self.assertEqual(config.config_name, 'Default WIS Config')
        self.assertEqual(config.batch_size, 50)
        self.assertTrue(config.is_enabled)
        self.assertEqual(config.last_run_status, 'SUCCESS')
