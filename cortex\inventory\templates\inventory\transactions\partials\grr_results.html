<table class="w-full table-auto divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                GRR Details
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                GIN Reference
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Invoice Details
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Quality Check
            </th>
            <th scope="col" class="relative px-6 py-3">
                <span class="sr-only">Actions</span>
            </th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for grr in grr_list %}
        <tr class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                        <div class="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                            <svg class="h-6 w-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">
                            <a href="{% url 'inventory:grr_detail' grr.Id %}" class="hover:underline">
                                {{ grr.grr_number }}
                            </a>
                        </div>
                        <div class="text-sm text-gray-500">{{ grr.grr_date }}</div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">
                    {% if grr.gin_number %}
                        <a href="{% url 'inventory:gin_detail' grr.GINId %}" class="text-indigo-600 hover:underline">
                            {{ grr.gin_number }}
                        </a>
                    {% else %}
                        -
                    {% endif %}
                </div>
                <div class="text-sm text-gray-500">{{ grr.challan_date }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">
                    {% if grr.tax_invoice_number %}
                        {{ grr.tax_invoice_number }}
                    {% else %}
                        -
                    {% endif %}
                </div>
                {% if grr.tax_invoice_date %}
                <div class="text-sm text-gray-500">{{ grr.tax_invoice_date }}</div>
                {% endif %}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                {% if grr.status == 'DRAFT' %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Draft
                    </span>
                {% elif grr.status == 'QUALITY_PENDING' %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        Quality Pending
                    </span>
                {% elif grr.status == 'QUALITY_COMPLETED' %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Quality Completed
                    </span>
                {% elif grr.status == 'APPROVED' %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Approved
                    </span>
                {% else %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {{ grr.status|default:"Unknown" }}
                    </span>
                {% endif %}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {% if grr.quality_checked_by_id %}
                    <div class="text-sm text-gray-900">User {{ grr.quality_checked_by_id }}</div>
                    {% if grr.quality_check_date %}
                    <div class="text-sm text-gray-500">{{ grr.quality_check_date|date:"M d, Y" }}</div>
                    {% endif %}
                {% else %}
                    <span class="text-gray-400">Not checked</span>
                {% endif %}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-2">
                    <a href="{% url 'inventory:grr_detail' grr.Id %}" 
                       class="text-indigo-600 hover:text-indigo-900">
                        View
                    </a>
                    
                    {% if grr.status == 'DRAFT' or grr.status == 'QUALITY_PENDING' %}
                    <a href="{% url 'inventory:grr_update' grr.Id %}" 
                       class="text-blue-600 hover:text-blue-900">
                        Edit
                    </a>
                    {% endif %}
                    
                    {% if grr.status == 'QUALITY_PENDING' %}
                    <button type="button" 
                            hx-post="{% url 'inventory:grr_quality_check' grr.Id %}"
                            hx-confirm="Complete quality check for {{ grr.grr_number }}?"
                            class="text-green-600 hover:text-green-900">
                        Complete QC
                    </button>
                    {% endif %}
                    
                    {% if grr.status == 'QUALITY_COMPLETED' %}
                    <button type="button" 
                            hx-post="{% url 'inventory:grr_approve' grr.Id %}"
                            hx-confirm="Approve {{ grr.grr_number }}?"
                            class="text-green-600 hover:text-green-900">
                        Approve
                    </button>
                    {% endif %}
                    
                    <a href="{% url 'inventory:grr_print' grr.Id %}" 
                       class="text-gray-600 hover:text-gray-900">
                        Print
                    </a>
                </div>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                <div class="flex flex-col items-center py-8">
                    <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="text-sm font-medium text-gray-900 mb-1">No GRRs found</h3>
                    <p class="text-sm text-gray-500 mb-4">Get started by creating a new Goods Received Receipt.</p>
                    <a href="{% url 'inventory:grr_create' %}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                        </svg>
                        Create GRR
                    </a>
                </div>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

{% if is_paginated %}
<div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
    <div class="flex-1 flex justify-between sm:hidden">
        {% if page_obj.has_previous %}
        <a href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}page={{ page_obj.previous_page_number }}" 
           class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            Previous
        </a>
        {% endif %}
        {% if page_obj.has_next %}
        <a href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}page={{ page_obj.next_page_number }}" 
           class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            Next
        </a>
        {% endif %}
    </div>
    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
            <p class="text-sm text-gray-700">
                Showing
                <span class="font-medium">{{ page_obj.start_index }}</span>
                to
                <span class="font-medium">{{ page_obj.end_index }}</span>
                of
                <span class="font-medium">{{ paginator.count }}</span>
                results
            </p>
        </div>
        <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                {% if page_obj.has_previous %}
                <a href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}page={{ page_obj.previous_page_number }}" 
                   class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <span class="sr-only">Previous</span>
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                </a>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <span class="relative inline-flex items-center px-4 py-2 border border-indigo-500 bg-indigo-50 text-sm font-medium text-indigo-600">
                        {{ num }}
                    </span>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <a href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}page={{ num }}" 
                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                        {{ num }}
                    </a>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                <a href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}page={{ page_obj.next_page_number }}" 
                   class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <span class="sr-only">Next</span>
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                </a>
                {% endif %}
            </nav>
        </div>
    </div>
</div>
{% endif %}