from django.shortcuts import render, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView, CreateView, UpdateView, DetailView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from datetime import datetime, timedelta

from .models import (
    Machine, PreventiveMaintenance, JobSchedule, JobScheduleDetail, JobScheduleDetailTemp,
    MachineProcess, MachineSpare, PreventiveMaintenanceDetail,
    MachineSpareTemp, MachineProcessTemp, JobCompletion
)
from .forms import (
    MachineForm, MachineSearchForm, PreventiveMaintenanceForm,
    JobScheduleForm, WorkOrderSearchForm, MachineItemSelectionForm
)
from design.models import Item, Subcategory


class MachineItemSelectionView(LoginRequiredMixin, ListView):
    """View for selecting items to create machines from (replicates ASP.NET Machinery_New.aspx)"""
    model = Item
    template_name = 'machinery/machine_item_selection.html'
    context_object_name = 'items'
    paginate_by = 20

    def get_queryset(self):
        """Get available items that can be made into machines"""
        # Clear temp tables on page load
        self.clear_temp_tables()
        
        queryset = Item.objects.filter(
            compid=self.request.user.company,
            absolute__ne='1'
        ).exclude(
            # Exclude items already registered as machines
            id__in=Machine.objects.filter(
                company=self.request.user.company,
                financial_year__lte=self.request.user.financial_year
            ).values_list('itemid', flat=True)
        )

        # Apply search filters
        category = self.request.GET.get('category')
        subcategory = self.request.GET.get('subcategory')
        search_field = self.request.GET.get('search_field')
        search_value = self.request.GET.get('search_value')

        if category:
            queryset = queryset.filter(category_id=category)

        if subcategory:
            queryset = queryset.filter(subcategory_id=subcategory)

        if search_field and search_value:
            if search_field == 'itemcode':
                queryset = queryset.filter(itemcode__icontains=search_value)
            elif search_field == 'description':
                queryset = queryset.filter(description__icontains=search_value)
            elif search_field == 'location':
                queryset = queryset.filter(location__icontains=search_value)

        return queryset.order_by('-id')

    def clear_temp_tables(self):
        """Clear temporary tables for current user"""
        MachineSpareTemp.objects.filter(
            company=self.request.user.company,
            sessionid=str(self.request.user.id)
        ).delete()
        
        MachineProcessTemp.objects.filter(
            company=self.request.user.company,
            sessionid=str(self.request.user.id)
        ).delete()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = MachineItemSelectionForm(
            data=self.request.GET or None,
            company=self.request.user.company
        )
        return context


class MachineListView(LoginRequiredMixin, ListView):
    """List view for machines with search functionality"""
    model = Machine
    template_name = 'machinery/machine_list.html'
    context_object_name = 'machines'
    paginate_by = 20

    def get_queryset(self):
        queryset = Machine.objects.filter(
            company=self.request.user.company,
            financial_year__lte=self.request.user.financial_year
        ).select_related('company', 'financial_year', 'user')

        # Apply search filters
        category = self.request.GET.get('category')
        subcategory = self.request.GET.get('subcategory')
        search_field = self.request.GET.get('search_field')
        search_value = self.request.GET.get('search_value')

        if category:
            # Filter by category through item master
            queryset = queryset.filter(itemid__in=
                Item.objects.filter(category_id=category).values_list('id', flat=True)
            )

        if subcategory:
            # Filter by subcategory through item master
            queryset = queryset.filter(itemid__in=
                Item.objects.filter(subcategory_id=subcategory).values_list('id', flat=True)
            )

        if search_field and search_value:
            if search_field == 'itemcode':
                queryset = queryset.filter(
                    itemid__in=Item.objects.filter(
                        itemcode__icontains=search_value
                    ).values_list('id', flat=True)
                )
            elif search_field == 'description':
                queryset = queryset.filter(
                    itemid__in=Item.objects.filter(
                        description__icontains=search_value
                    ).values_list('id', flat=True)
                )
            elif search_field == 'location':
                queryset = queryset.filter(location__icontains=search_value)

        return queryset.order_by('-id')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = MachineSearchForm(
            data=self.request.GET or None,
            company=self.request.user.company
        )
        return context


class MachineCreateDetailView(LoginRequiredMixin, CreateView):
    """Detail view for creating machines with spare parts and processes (replicates ASP.NET Machinery_New_Details.aspx)"""
    model = Machine
    form_class = MachineForm
    template_name = 'machinery/machine_create_detail.html'
    
    def dispatch(self, request, *args, **kwargs):
        """Get the item being converted to machine"""
        self.item_id = kwargs.get('item_id')
        try:
            self.item = Item.objects.get(
                id=self.item_id,
                compid=request.user.company
            )
        except Item.DoesNotExist:
            messages.error(request, 'Item not found.')
            return redirect('machinery:machine_item_selection')
        return super().dispatch(request, *args, **kwargs)
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        kwargs['company'] = self.request.user.company
        return kwargs

    def form_valid(self, form):
        """Save machine with spare parts and processes"""
        # Check if spare parts and processes are selected
        spare_parts = MachineSpareTemp.objects.filter(
            company=self.request.user.company,
            sessionid=str(self.request.user.id)
        )
        
        processes = MachineProcessTemp.objects.filter(
            company=self.request.user.company,
            sessionid=str(self.request.user.id)
        )
        
        if not spare_parts.exists() or not processes.exists():
            messages.error(self.request, 'Functionality and spare details are required.')
            return self.form_invalid(form)
        
        # Save the machine
        machine = form.save(commit=False)
        machine.user = self.request.user
        machine.company = self.request.user.company
        machine.financial_year = self.request.user.financial_year
        machine.itemid = self.item.id
        machine.sysdate = timezone.now().strftime('%Y-%m-%d')
        machine.systime = timezone.now().strftime('%H:%M:%S')
        machine.save()
        
        # Save spare parts
        for spare_temp in spare_parts:
            MachineSpare.objects.create(
                machine=machine,
                itemid=spare_temp.itemid,
                qty=spare_temp.qty
            )
        
        # Save processes
        for process_temp in processes:
            MachineProcess.objects.create(
                machine=machine,
                pid=process_temp.pid
            )
        
        # Clear temp tables
        spare_parts.delete()
        processes.delete()
        
        messages.success(self.request, 'Machine created successfully.')
        return redirect('machinery:machine_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['item'] = self.item
        context['title'] = f'Create Machine from {self.item.itemcode}'
        
        # Get spare parts and processes from temp tables
        context['spare_parts'] = MachineSpareTemp.objects.filter(
            company=self.request.user.company,
            sessionid=str(self.request.user.id)
        )
        
        context['processes'] = MachineProcessTemp.objects.filter(
            company=self.request.user.company,
            sessionid=str(self.request.user.id)
        )
        
        # Get available spare items and processes for selection
        context['available_spare_items'] = Item.objects.filter(
            compid=self.request.user.company,
            absolute__ne='1'
        ).exclude(id=self.item.id)
        
        try:
            from material_planning.models import ProcessMaster
            context['available_processes'] = ProcessMaster.objects.filter(
                symbol__ne='0'
            ).order_by('symbol')
        except ImportError:
            context['available_processes'] = []
        
        return context


class MachineCreateView(LoginRequiredMixin, CreateView):
    """Create view for new machines"""
    model = Machine
    form_class = MachineForm
    template_name = 'machinery/machine_form.html'
    success_url = reverse_lazy('machinery:machine_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        kwargs['company'] = self.request.user.company
        return kwargs

    def form_valid(self, form):
        machine = form.save(commit=False)
        machine.user = self.request.user
        machine.company = self.request.user.company
        machine.financial_year = self.request.user.financial_year
        machine.sysdate = timezone.now().date()
        machine.systime = timezone.now().time()
        machine.save()
        
        messages.success(self.request, 'Machine created successfully.')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Add New Machine'
        context['available_items'] = Item.objects.filter(
            compid=self.request.user.company
        ).exclude(
            id__in=Machine.objects.filter(
                company=self.request.user.company
            ).values_list('itemid', flat=True)
        )
        return context


class MachineUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for existing machines"""
    model = Machine
    form_class = MachineForm
    template_name = 'machinery/machine_form.html'
    success_url = reverse_lazy('machinery:machine_list')

    def get_queryset(self):
        return Machine.objects.filter(company=self.request.user.company)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        kwargs['company'] = self.request.user.company
        return kwargs

    def form_valid(self, form):
        machine = form.save(commit=False)
        machine.sysdate = timezone.now().date()
        machine.systime = timezone.now().time()
        machine.save()
        
        messages.success(self.request, 'Machine updated successfully.')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Edit Machine'
        context['machine_spares'] = MachineSpare.objects.filter(machine=self.object)
        context['machine_processes'] = MachineProcess.objects.filter(machine=self.object)
        return context


class MachineDetailView(LoginRequiredMixin, DetailView):
    """Detail view for machines"""
    model = Machine
    template_name = 'machinery/machine_detail.html'
    context_object_name = 'machine'

    def get_queryset(self):
        return Machine.objects.filter(company=self.request.user.company)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['machine_spares'] = MachineSpare.objects.filter(machine=self.object)
        context['machine_processes'] = MachineProcess.objects.filter(machine=self.object)
        context['maintenance_history'] = PreventiveMaintenance.objects.filter(
            machineid=self.object.id
        ).order_by('-sysdate')
        return context


class PMBMListView(LoginRequiredMixin, ListView):
    """List view for PMBM machines (replicates ASP.NET PMBM_New.aspx)"""
    model = Machine
    template_name = 'machinery/pmbm_list.html'
    context_object_name = 'machines'
    paginate_by = 20

    def get_queryset(self):
        """Get machines registered for maintenance with PM calculations"""
        # Clear temp tables on page load
        self.clear_temp_tables()
        
        queryset = Machine.objects.filter(
            company=self.request.user.company
        ).select_related('company', 'financial_year', 'user')

        # Apply search filters similar to ASP.NET version
        category = self.request.GET.get('category')
        subcategory = self.request.GET.get('subcategory')
        search_field = self.request.GET.get('search_field')
        search_value = self.request.GET.get('search_value')

        if category:
            queryset = queryset.filter(
                itemid__in=Item.objects.filter(category_id=category).values_list('id', flat=True)
            )

        if subcategory:
            queryset = queryset.filter(
                itemid__in=Item.objects.filter(subcategory_id=subcategory).values_list('id', flat=True)
            )

        if search_field and search_value:
            if search_field == 'itemcode':
                queryset = queryset.filter(
                    itemid__in=Item.objects.filter(
                        itemcode__icontains=search_value
                    ).values_list('id', flat=True)
                )
            elif search_field == 'description':
                queryset = queryset.filter(
                    itemid__in=Item.objects.filter(
                        description__icontains=search_value
                    ).values_list('id', flat=True)
                )

        # Add maintenance calculations to each machine
        machines_with_pm_data = []
        for machine in queryset:
            # Get item details
            try:
                item = Item.objects.get(id=machine.itemid)
                machine.item_details = item
            except Item.DoesNotExist:
                continue
            
            # Calculate PM status
            machine.pm_status = self.calculate_pm_status(machine)
            machines_with_pm_data.append(machine)

        return machines_with_pm_data

    def calculate_pm_status(self, machine):
        """Calculate PM status similar to ASP.NET logic"""
        try:
            # Get last maintenance
            last_maintenance = PreventiveMaintenance.objects.filter(
                machineid=machine.id
            ).order_by('-sysdate').first()
            
            if last_maintenance:
                last_date = datetime.strptime(last_maintenance.sysdate, '%Y-%m-%d').date()
            else:
                last_date = datetime.strptime(machine.sysdate, '%Y-%m-%d').date()
            
            current_date = timezone.now().date()
            days_since_last = (current_date - last_date).days
            
            if machine.pmdays:
                pm_days = int(machine.pmdays)
                remaining_days = pm_days - days_since_last
                
                return {
                    'last_pm_date': last_date,
                    'days_remaining': remaining_days,
                    'is_overdue': remaining_days <= 0,
                    'pm_days': pm_days
                }
        except (ValueError, TypeError):
            pass
        
        return {
            'last_pm_date': None,
            'days_remaining': 0,
            'is_overdue': False,
            'pm_days': 0
        }

    def clear_temp_tables(self):
        """Clear temporary tables for current user"""
        MachineSpareTemp.objects.filter(
            company=self.request.user.company,
            sessionid=str(self.request.user.id)
        ).delete()
        
        MachineProcessTemp.objects.filter(
            company=self.request.user.company,
            sessionid=str(self.request.user.id)
        ).delete()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = MachineSearchForm(
            data=self.request.GET or None,
            company=self.request.user.company
        )
        return context


class PreventiveMaintenanceListView(LoginRequiredMixin, ListView):
    """List view for preventive maintenance records"""
    model = PreventiveMaintenance
    template_name = 'machinery/maintenance_list.html'
    context_object_name = 'maintenance_records'
    paginate_by = 20

    def get_queryset(self):
        queryset = PreventiveMaintenance.objects.filter(
            company=self.request.user.company
        ).select_related('company', 'financial_year', 'user')

        # Add machine information and calculate due dates
        for record in queryset:
            try:
                machine = Machine.objects.get(id=record.machineid)
                record.machine_info = machine
                
                # Calculate days remaining for next PM
                if record.nextpmdueon:
                    days_remaining = (
                        datetime.strptime(record.nextpmdueon, '%Y-%m-%d').date() - 
                        timezone.now().date()
                    ).days
                    record.days_remaining = days_remaining
                    record.is_overdue = days_remaining < 0
                    record.is_due_soon = 0 <= days_remaining <= 7
            except Machine.DoesNotExist:
                record.machine_info = None

        return queryset.order_by('-sysdate')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get machines that need maintenance
        overdue_machines = []
        due_soon_machines = []
        
        machines = Machine.objects.filter(company=self.request.user.company)
        for machine in machines:
            # Get last maintenance date
            last_maintenance = PreventiveMaintenance.objects.filter(
                machineid=machine.id
            ).order_by('-sysdate').first()
            
            if last_maintenance:
                last_date = datetime.strptime(last_maintenance.sysdate, '%Y-%m-%d').date()
            else:
                last_date = datetime.strptime(machine.sysdate, '%Y-%m-%d').date()
            
            if machine.pmdays:
                next_due = last_date + timedelta(days=int(machine.pmdays))
                days_remaining = (next_due - timezone.now().date()).days
                
                if days_remaining < 0:
                    overdue_machines.append({
                        'machine': machine,
                        'days_overdue': abs(days_remaining),
                        'last_maintenance': last_date
                    })
                elif 0 <= days_remaining <= 7:
                    due_soon_machines.append({
                        'machine': machine,
                        'days_remaining': days_remaining,
                        'last_maintenance': last_date
                    })
        
        context['overdue_machines'] = overdue_machines
        context['due_soon_machines'] = due_soon_machines
        return context


class PreventiveMaintenanceCreateView(LoginRequiredMixin, CreateView):
    """Create view for preventive maintenance records"""
    model = PreventiveMaintenance
    form_class = PreventiveMaintenanceForm
    template_name = 'machinery/maintenance_form.html'
    success_url = reverse_lazy('machinery:maintenance_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['company'] = self.request.user.company
        return kwargs

    def form_valid(self, form):
        maintenance = form.save(commit=False)
        maintenance.user = self.request.user
        maintenance.company = self.request.user.company
        maintenance.financial_year = self.request.user.financial_year
        maintenance.sysdate = timezone.now().strftime('%Y-%m-%d')
        maintenance.systime = timezone.now().strftime('%H:%M:%S')
        maintenance.save()
        
        messages.success(self.request, 'Maintenance record created successfully.')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Add Maintenance Record'
        return context


class PMBMCreateDetailView(LoginRequiredMixin, CreateView):
    """Detail view for creating PMBM records (replicates ASP.NET PMBM_New_Details.aspx)"""
    model = PreventiveMaintenance
    form_class = PreventiveMaintenanceForm
    template_name = 'machinery/pmbm_create_detail.html'
    success_url = reverse_lazy('machinery:pmbm_list')

    def dispatch(self, request, *args, **kwargs):
        """Get the machine and item being maintained"""
        self.machine_id = kwargs.get('machine_id')
        self.item_id = kwargs.get('item_id')
        
        try:
            self.machine = Machine.objects.get(
                id=self.machine_id,
                company=request.user.company
            )
            
            # Get item details
            self.item = Item.objects.get(id=self.item_id)
            
        except (Machine.DoesNotExist, Item.DoesNotExist):
            messages.error(request, 'Machine or item not found.')
            return redirect('machinery:pmbm_list')
        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['company'] = self.request.user.company
        return kwargs

    def get_initial(self):
        initial = super().get_initial()
        initial['machineid'] = self.machine.id
        return initial

    def form_valid(self, form):
        maintenance = form.save(commit=False)
        maintenance.user = self.request.user
        maintenance.company = self.request.user.company
        maintenance.financial_year = self.request.user.financial_year
        maintenance.machineid = self.machine.id
        maintenance.sysdate = timezone.now().strftime('%Y-%m-%d')
        maintenance.systime = timezone.now().strftime('%H:%M:%S')
        
        # Calculate next PM due date if PM days specified
        if maintenance.pmbm == 0 and self.machine.pmdays:  # Preventive maintenance
            try:
                from_date = datetime.strptime(maintenance.fromdate, '%Y-%m-%d')
                pm_days = int(self.machine.pmdays)
                next_pm_date = from_date + timedelta(days=pm_days)
                maintenance.nextpmdueon = next_pm_date.strftime('%Y-%m-%d')
            except (ValueError, TypeError):
                pass
        
        maintenance.save()
        
        messages.success(self.request, 'PMBM record created successfully.')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['machine'] = self.machine
        context['item'] = self.item
        context['title'] = f'PMBM for {self.item.itemcode} - {self.machine.make} {self.machine.model}'
        
        # Get machine spare parts for maintenance
        context['machine_spares'] = MachineSpare.objects.filter(machine=self.machine)
        
        # Get maintenance history
        context['maintenance_history'] = PreventiveMaintenance.objects.filter(
            machineid=self.machine.id
        ).order_by('-sysdate')[:5]
        
        return context


class PreventiveMaintenanceUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for preventive maintenance records"""
    model = PreventiveMaintenance
    form_class = PreventiveMaintenanceForm
    template_name = 'machinery/maintenance_form.html'
    success_url = reverse_lazy('machinery:maintenance_list')

    def get_queryset(self):
        return PreventiveMaintenance.objects.filter(company=self.request.user.company)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['company'] = self.request.user.company
        return kwargs

    def form_valid(self, form):
        maintenance = form.save(commit=False)
        maintenance.sysdate = timezone.now().date()
        maintenance.systime = timezone.now().time()
        maintenance.save()
        
        messages.success(self.request, 'Maintenance record updated successfully.')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Edit Maintenance Record'
        context['maintenance_details'] = PreventiveMaintenanceDetail.objects.filter(
            preventive_maintenance=self.object
        )
        return context


class JobScheduleListView(LoginRequiredMixin, ListView):
    """List view for job schedules"""
    model = JobSchedule
    template_name = 'machinery/schedule_list.html'
    context_object_name = 'schedules'
    paginate_by = 20

    def get_queryset(self):
        return JobSchedule.objects.filter(
            company=self.request.user.company,
            financial_year__lte=self.request.user.financial_year
        ).select_related('company', 'financial_year', 'user').order_by('-id')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = WorkOrderSearchForm(data=self.request.GET or None)
        return context


class JobScheduleCreateView(LoginRequiredMixin, ListView):
    """Create view for job schedules - Work order selection (replicates ASP.NET Schedule_New.aspx)"""
    template_name = 'machinery/schedule_create.html'
    context_object_name = 'work_orders'
    paginate_by = 18

    def get_queryset(self):
        """Get work orders for scheduling"""
        try:
            from sales_distribution.models import WorkOrder, Customer
            
            search_type = self.request.GET.get('search_type', '')
            search_value = self.request.GET.get('search_value', '')
            
            queryset = WorkOrder.objects.filter(
                compid=self.request.user.company,
                finyearid__lte=self.request.user.financial_year
            ).select_related('compid', 'finyearid').order_by('-id')
            
            if search_type and search_value:
                if search_type == 'customer':
                    # Search by customer name
                    customers = Customer.objects.filter(
                        compid=self.request.user.company,
                        customer_name__icontains=search_value
                    ).values_list('customerid', flat=True)
                    queryset = queryset.filter(customerid__in=customers)
                    
                elif search_type == 'work_order':
                    # Search by work order number
                    queryset = queryset.filter(wono__icontains=search_value)
            
            # Enhance with customer information
            work_orders = []
            for wo in queryset:
                try:
                    customer = Customer.objects.filter(
                        compid=self.request.user.company,
                        customerid=wo.customerid
                    ).first()
                    
                    wo.customer_name = customer.customer_name if customer else 'N/A'
                    wo.customer_code = wo.customerid
                    work_orders.append(wo)
                except Exception:
                    continue
            
            return work_orders
            
        except ImportError:
            return []

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = WorkOrderSearchForm(data=self.request.GET or None)
        context['title'] = 'Job Scheduling Input - New'
        return context


class JobScheduleCreateDetailView(LoginRequiredMixin, CreateView):
    """Detail view for creating job schedules with work order (replicates ASP.NET Schedule_New_Details.aspx)"""
    model = JobSchedule
    form_class = JobScheduleForm
    template_name = 'machinery/schedule_create_detail.html'
    success_url = reverse_lazy('machinery:schedule_list')

    def dispatch(self, request, *args, **kwargs):
        """Get the work order being scheduled"""
        self.wono = kwargs.get('wono')
        try:
            from sales_distribution.models import WorkOrder, Customer
            
            self.work_order = WorkOrder.objects.get(
                wono=self.wono,
                compid=request.user.company
            )
            
            # Get customer information
            try:
                customer = Customer.objects.get(
                    compid=request.user.company,
                    customerid=self.work_order.customerid
                )
                self.work_order.customer_name = customer.customer_name
            except Customer.DoesNotExist:
                self.work_order.customer_name = 'N/A'
                
        except (WorkOrder.DoesNotExist, ImportError):
            messages.error(request, 'Work order not found.')
            return redirect('machinery:schedule_create')
        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['company'] = self.request.user.company
        return kwargs

    def form_valid(self, form):
        schedule = form.save(commit=False)
        schedule.user = self.request.user
        schedule.company = self.request.user.company
        schedule.financial_year = self.request.user.financial_year
        schedule.wono = self.wono
        schedule.sysdate = timezone.now().strftime('%Y-%m-%d')
        schedule.systime = timezone.now().strftime('%H:%M:%S')
        schedule.save()
        
        messages.success(self.request, 'Job schedule created successfully.')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['work_order'] = self.work_order
        context['title'] = 'Job Scheduling Input - New'
        
        # Get work order items for scheduling
        try:
            from sales_distribution.models import WorkorderProductsDetails
            context['work_order_items'] = WorkorderProductsDetails.objects.filter(
                mid=self.work_order,
                compid=self.request.user.company
            )
        except ImportError:
            context['work_order_items'] = []
            
        return context


class JobScheduleItemsView(LoginRequiredMixin, ListView):
    """Item scheduling view (replicates ASP.NET Schedule_New_Items.aspx)"""
    model = JobScheduleDetailTemp
    template_name = 'machinery/schedule_items.html'
    context_object_name = 'schedule_details'
    
    def dispatch(self, request, *args, **kwargs):
        """Get the item and work order information"""
        self.item_id = kwargs.get('item_id')
        self.wono = kwargs.get('wono')
        
        try:
            self.item = Item.objects.get(
                id=self.item_id,
                compid=request.user.company
            )
            
            # Clear temp tables for this user on page load
            JobScheduleDetailTemp.objects.filter(
                company=request.user.company,
                user=request.user
            ).delete()
            
        except Item.DoesNotExist:
            messages.error(request, 'Item not found.')
            return redirect('machinery:schedule_create')
        return super().dispatch(request, *args, **kwargs)
    
    def get_queryset(self):
        """Get schedule details from temp table"""
        return JobScheduleDetailTemp.objects.filter(
            company=self.request.user.company,
            user=self.request.user,
            itemid=self.item_id
        ).order_by('id')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['item'] = self.item
        context['wono'] = self.wono
        context['title'] = 'Job Scheduling Input - New'
        
        # Get BOM quantity and details
        try:
            from design.models import BOMDetail
            bom_detail = BOMDetail.objects.filter(
                wono=self.wono,
                itemid=self.item_id,
                compid=self.request.user.company
            ).first()
            
            if bom_detail:
                context['bom_qty'] = bom_detail.qty
            else:
                context['bom_qty'] = 1
        except ImportError:
            context['bom_qty'] = 1
        
        # Get available machines for this item
        available_machines = Machine.objects.filter(
            company=self.request.user.company,
            itemid__in=Item.objects.filter(
                compid=self.request.user.company
            ).values_list('id', flat=True)
        )
        
        # Filter machines that have available processes
        machines_with_processes = []
        for machine in available_machines:
            # Get machine processes not already scheduled
            machine_processes = MachineProcess.objects.filter(
                machine=machine
            ).exclude(
                pid__in=JobScheduleDetailTemp.objects.filter(
                    company=self.request.user.company,
                    user=self.request.user,
                    itemid=self.item_id,
                    machineid=machine.itemid
                ).values_list('process', flat=True)
            )
            
            if machine_processes.exists():
                machines_with_processes.append({
                    'machine': machine,
                    'item_id': machine.itemid,
                    'processes': machine_processes
                })
        
        context['available_machines'] = machines_with_processes
        
        # Get work order batch information
        try:
            from sales_distribution.models import WorkOrder
            work_order = WorkOrder.objects.get(
                wono=self.wono,
                compid=self.request.user.company
            )
            
            batches = getattr(work_order, 'batches', 1) or 1
            context['batches'] = list(range(1, int(batches) + 1))
        except (ImportError, Exception):
            context['batches'] = [1]
        
        # Get employees for autocomplete
        try:
            from human_resource.models import Employee
            context['employees'] = Employee.objects.filter(
                compid=self.request.user.company
            ).values('empid', 'employeename')
        except ImportError:
            context['employees'] = []
        
        return context


class JobScheduleSplitItemsView(LoginRequiredMixin, ListView):
    """Split scheduling view (replicates ASP.NET Schedule_New_Items_BySplit.aspx)"""
    template_name = 'machinery/schedule_split_items.html'
    context_object_name = 'bom_items'
    
    def dispatch(self, request, *args, **kwargs):
        """Get the work order and BOM item information"""
        self.wono = kwargs.get('wono')
        self.bom_id = kwargs.get('bom_id')
        
        # Clear temp tables for this user
        JobScheduleDetailTemp.objects.filter(
            company=request.user.company,
            user=request.user
        ).delete()
        
        return super().dispatch(request, *args, **kwargs)
    
    def get_queryset(self):
        """Get BOM components for split scheduling"""
        try:
            from design.models import BOMDetail
            
            # Get the parent BOM item
            parent_bom = BOMDetail.objects.filter(
                id=self.bom_id,
                wono=self.wono,
                compid=self.request.user.company
            ).first()
            
            if not parent_bom:
                return []
            
            # Get child components of this BOM item
            child_components = BOMDetail.objects.filter(
                wono=self.wono,
                compid=self.request.user.company,
                pid=parent_bom.cid  # Parent ID matches child ID
            ).exclude(
                # Exclude items that are parents themselves
                cid__in=BOMDetail.objects.filter(
                    wono=self.wono,
                    compid=self.request.user.company
                ).values_list('pid', flat=True)
            ).select_related('itemid')
            
            return child_components
            
        except ImportError:
            return []
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['wono'] = self.wono
        context['bom_id'] = self.bom_id
        context['title'] = 'Job Scheduling Input - Split View'
        
        # Calculate BOM quantities for each item
        enhanced_items = []
        for bom_item in self.get_queryset():
            # Calculate total quantity considering BOM tree
            total_qty = self.calculate_bom_tree_quantity(bom_item)
            
            enhanced_items.append({
                'bom_item': bom_item,
                'total_qty': total_qty,
                'item': bom_item.itemid if hasattr(bom_item, 'itemid') else None
            })
        
        context['enhanced_items'] = enhanced_items
        return context
    
    def calculate_bom_tree_quantity(self, bom_item):
        """Calculate total quantity considering BOM hierarchy"""
        # This is a simplified calculation - in practice you'd need to
        # traverse the entire BOM tree and multiply quantities
        return float(bom_item.qty) if bom_item.qty else 1.0


class JobScheduleOutputView(LoginRequiredMixin, ListView):
    """Job output recording view (replicates ASP.NET Schedule_Output_New.aspx)"""
    template_name = 'machinery/schedule_output.html'
    context_object_name = 'work_order_items'
    
    def get_queryset(self):
        """Get work order items for output recording"""
        wono = self.request.GET.get('wono')
        if not wono:
            return []
        
        try:
            from design.models import BOMDetail
            from sales_distribution.models import WorkOrder, Customer
            
            # Get BOM details for the work order
            return BOMDetail.objects.filter(
                wono=wono,
                compid=self.request.user.company
            ).select_related('itemid')
            
        except ImportError:
            return []
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        wono = self.request.GET.get('wono')
        
        # Get available work orders
        try:
            from sales_distribution.models import WorkOrder, Customer
            
            work_orders = WorkOrder.objects.filter(
                compid=self.request.user.company
            ).values('wono', 'customerid')
            
            # Enhance with customer information
            enhanced_work_orders = []
            for wo in work_orders:
                try:
                    customer = Customer.objects.filter(
                        compid=self.request.user.company,
                        customerid=wo['customerid']
                    ).first()
                    
                    wo_data = {
                        'wono': wo['wono'],
                        'customer_name': customer.customer_name if customer else 'N/A'
                    }
                    enhanced_work_orders.append(wo_data)
                except Exception:
                    continue
            
            context['work_orders'] = enhanced_work_orders
            
            # If work order is selected, get customer name
            if wono:
                selected_wo = WorkOrder.objects.filter(
                    wono=wono,
                    compid=self.request.user.company
                ).first()
                
                if selected_wo:
                    try:
                        customer = Customer.objects.filter(
                            compid=self.request.user.company,
                            customerid=selected_wo.customerid
                        ).first()
                        context['customer_name'] = customer.customer_name if customer else 'N/A'
                    except Exception:
                        context['customer_name'] = 'N/A'
                else:
                    context['customer_name'] = 'N/A'
            
        except ImportError:
            context['work_orders'] = []
            context['customer_name'] = 'N/A'
        
        context['selected_wono'] = wono
        context['title'] = 'Job Scheduling Output - New'
        return context


class JobScheduleOutputDetailView(LoginRequiredMixin, ListView):
    """Job output detail recording view (replicates ASP.NET Schedule_Output_New_Details.aspx)"""
    model = JobScheduleDetail
    template_name = 'machinery/schedule_output_detail.html'
    context_object_name = 'schedule_details'
    
    def dispatch(self, request, *args, **kwargs):
        self.item_id = kwargs.get('item_id')
        self.wono = kwargs.get('wono')
        
        try:
            self.item = Item.objects.get(
                id=self.item_id,
                compid=request.user.company
            )
        except Item.DoesNotExist:
            messages.error(request, 'Item not found.')
            return redirect('machinery:schedule_output')
        return super().dispatch(request, *args, **kwargs)
    
    def get_queryset(self):
        """Get scheduled jobs for output recording"""
        return JobScheduleDetail.objects.filter(
            job_schedule__company=self.request.user.company,
            job_schedule__wono=self.wono,
            job_schedule__itemid=self.item_id,
            released=0  # Only unreleased jobs
        ).select_related('job_schedule')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['item'] = self.item
        context['wono'] = self.wono
        context['title'] = 'Job Output Recording'
        
        # Get existing job completions
        context['job_completions'] = JobCompletion.objects.filter(
            job_schedule_detail__in=self.get_queryset()
        )
        
        return context


class JobScheduleUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for job schedules"""
    model = JobSchedule
    form_class = JobScheduleForm
    template_name = 'machinery/schedule_form.html'
    success_url = reverse_lazy('machinery:schedule_list')

    def get_queryset(self):
        return JobSchedule.objects.filter(company=self.request.user.company)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['company'] = self.request.user.company
        return kwargs

    def form_valid(self, form):
        schedule = form.save(commit=False)
        schedule.sysdate = timezone.now().date()
        schedule.systime = timezone.now().time()
        schedule.save()
        
        messages.success(self.request, 'Job schedule updated successfully.')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Edit Job Schedule'
        context['schedule_details'] = JobScheduleDetail.objects.filter(
            job_schedule=self.object
        )
        return context


class JobScheduleDetailView(LoginRequiredMixin, DetailView):
    """Detail view for job schedules"""
    model = JobSchedule
    template_name = 'machinery/schedule_detail.html'
    context_object_name = 'schedule'

    def get_queryset(self):
        return JobSchedule.objects.filter(company=self.request.user.company)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['schedule_details'] = JobScheduleDetail.objects.filter(
            job_schedule=self.object
        ).order_by('fromdate', 'fromtime')
        return context


# HTMX Views for dynamic content

def get_subcategories(request):
    """HTMX view to get subcategories based on category selection"""
    category_id = request.GET.get('category')
    subcategories = []
    
    if category_id:
        subcategories = Subcategory.objects.filter(
            category_id=category_id
        ).values('id', 'name')
    
    return JsonResponse({'subcategories': list(subcategories)})


def search_machines(request):
    """HTMX view for real-time machine search"""
    if request.method == 'POST':
        form = MachineSearchForm(request.POST, company=request.user.company)
        
        if form.is_valid():
            # Build queryset based on form data
            machines = Machine.objects.filter(
                company=request.user.company
            )
            
            # Apply filters...
            # (Implementation similar to MachineListView.get_queryset())
            
            return render(request, 'machinery/partials/machine_search_results.html', {
                'machines': machines[:20]  # Limit results
            })
    
    return HttpResponse('')


def get_machine_details(request, machine_id):
    """HTMX view to get machine details"""
    try:
        machine = Machine.objects.get(
            id=machine_id, 
            company=request.user.company
        )
        
        # Get related item details
        try:
            item = Item.objects.get(id=machine.itemid)
            machine.item_details = item
        except Item.DoesNotExist:
            machine.item_details = None
        
        return render(request, 'machinery/partials/machine_details.html', {
            'machine': machine
        })
    except Machine.DoesNotExist:
        return HttpResponse('Machine not found')


def check_insurance_fields(request):
    """HTMX view to show/hide insurance fields based on selection"""
    insurance_selected = request.POST.get('insurance') == '1'
    
    return render(request, 'machinery/partials/insurance_fields.html', {
        'show_insurance_fields': insurance_selected
    })


def search_work_orders(request):
    """HTMX view for work order search"""
    search_type = request.GET.get('search_type')
    search_value = request.GET.get('search_value', '')
    
    work_orders = []
    
    if search_type and search_value:
        try:
            # Import WorkOrder and Customer from sales_distribution
            from sales_distribution.models import WorkOrder, Customer
            
            if search_type == 'customer':
                # Search by customer name
                customers = Customer.objects.filter(
                    compid=request.user.company,
                    customer_name__icontains=search_value
                ).values_list('customerid', flat=True)
                
                work_orders = WorkOrder.objects.filter(
                    compid=request.user.company,
                    finyearid__lte=request.user.financial_year,
                    customerid__in=customers
                ).select_related('compid', 'finyearid').order_by('-id')[:20]
                
            elif search_type == 'work_order':
                # Search by work order number
                work_orders = WorkOrder.objects.filter(
                    compid=request.user.company,
                    finyearid__lte=request.user.financial_year,
                    wono__icontains=search_value
                ).select_related('compid', 'finyearid').order_by('-id')[:20]
            
            # Enhance work orders with customer information
            enhanced_work_orders = []
            for wo in work_orders:
                try:
                    customer = Customer.objects.filter(
                        compid=request.user.company,
                        customerid=wo.customerid
                    ).first()
                    
                    wo.customer_name = customer.customer_name if customer else 'N/A'
                    wo.customer_code = wo.customerid
                    enhanced_work_orders.append(wo)
                except Exception:
                    continue
            
            work_orders = enhanced_work_orders
            
        except ImportError:
            # Sales distribution module not available
            pass
    
    return render(request, 'machinery/partials/work_order_results.html', {
        'work_orders': work_orders
    })


def add_spare_parts(request):
    """HTMX view for adding spare parts to machine"""
    if request.method == 'POST':
        selected_items = request.POST.getlist('spare_items')
        quantities = request.POST.getlist('quantities')
        
        success_count = 0
        for i, item_id in enumerate(selected_items):
            if i < len(quantities) and quantities[i]:
                try:
                    qty = float(quantities[i])
                    if qty > 0:
                        MachineSpareTemp.objects.create(
                            company=request.user.company,
                            sessionid=str(request.user.id),
                            itemid=int(item_id),
                            qty=qty
                        )
                        success_count += 1
                except (ValueError, TypeError):
                    pass
        
        if success_count > 0:
            messages.success(request, f'{success_count} spare parts added.')
        
        # Return updated spare parts list
        spare_parts = MachineSpareTemp.objects.filter(
            company=request.user.company,
            sessionid=str(request.user.id)
        )
        
        return render(request, 'machinery/partials/spare_parts_list.html', {
            'spare_parts': spare_parts
        })
    
    return HttpResponse('')


def remove_spare_part(request, spare_id):
    """HTMX view for removing spare part"""
    try:
        spare = MachineSpareTemp.objects.get(
            id=spare_id,
            company=request.user.company,
            sessionid=str(request.user.id)
        )
        spare.delete()
        messages.success(request, 'Spare part removed.')
    except MachineSpareTemp.DoesNotExist:
        messages.error(request, 'Spare part not found.')
    
    # Return updated spare parts list
    spare_parts = MachineSpareTemp.objects.filter(
        company=request.user.company,
        sessionid=str(request.user.id)
    )
    
    return render(request, 'machinery/partials/spare_parts_list.html', {
        'spare_parts': spare_parts
    })


def add_processes(request):
    """HTMX view for adding processes to machine"""
    if request.method == 'POST':
        selected_processes = request.POST.getlist('processes')
        
        success_count = 0
        for process_id in selected_processes:
            try:
                MachineProcessTemp.objects.create(
                    company=request.user.company,
                    sessionid=str(request.user.id),
                    pid=int(process_id)
                )
                success_count += 1
            except (ValueError, TypeError):
                pass
        
        if success_count > 0:
            messages.success(request, f'{success_count} processes added.')
        
        # Return updated processes list
        processes = MachineProcessTemp.objects.filter(
            company=request.user.company,
            sessionid=str(request.user.id)
        )
        
        return render(request, 'machinery/partials/processes_list.html', {
            'processes': processes
        })
    
    return HttpResponse('')


def remove_process(request, process_id):
    """HTMX view for removing process"""
    try:
        process = MachineProcessTemp.objects.get(
            id=process_id,
            company=request.user.company,
            sessionid=str(request.user.id)
        )
        process.delete()
        messages.success(request, 'Process removed.')
    except MachineProcessTemp.DoesNotExist:
        messages.error(request, 'Process not found.')
    
    # Return updated processes list
    processes = MachineProcessTemp.objects.filter(
        company=request.user.company,
        sessionid=str(request.user.id)
    )
    
    return render(request, 'machinery/partials/processes_list.html', {
        'processes': processes
    })


def get_available_items(request):
    """HTMX view to get available items for machine creation"""
    category = request.GET.get('category')
    subcategory = request.GET.get('subcategory')
    search_field = request.GET.get('search_field')
    search_value = request.GET.get('search_value', '')
    
    items = Item.objects.filter(
        compid=request.user.company,
        absolute__ne='1'
    ).exclude(
        id__in=Machine.objects.filter(
            company=request.user.company
        ).values_list('itemid', flat=True)
    )
    
    if category:
        items = items.filter(category_id=category)
    
    if subcategory:
        items = items.filter(subcategory_id=subcategory)
    
    if search_field and search_value:
        if search_field == 'itemcode':
            items = items.filter(itemcode__icontains=search_value)
        elif search_field == 'description':
            items = items.filter(description__icontains=search_value)
        elif search_field == 'location':
            items = items.filter(location__icontains=search_value)
    
    items = items.order_by('-id')[:20]  # Limit results
    
    return render(request, 'machinery/partials/items_grid.html', {
        'items': items
    })


def download_machine_file(request, machine_id):
    """Download machine attachment file"""
    try:
        machine = Machine.objects.get(
            id=machine_id,
            company=request.user.company
        )
        
        # Check if file exists and has data
        if machine.filedata and machine.filename:
            # Determine content type based on file extension if not available
            content_type = machine.contenttype
            if not content_type:
                import mimetypes
                content_type, _ = mimetypes.guess_type(machine.filename)
                if not content_type:
                    content_type = 'application/octet-stream'
            
            response = HttpResponse(
                machine.filedata,
                content_type=content_type
            )
            
            # Sanitize filename for security
            import os
            safe_filename = os.path.basename(machine.filename)
            response['Content-Disposition'] = f'attachment; filename="{safe_filename}"'
            response['Content-Length'] = len(machine.filedata)
            
            # Add some security headers
            response['X-Content-Type-Options'] = 'nosniff'
            response['X-Frame-Options'] = 'DENY'
            
            return response
        else:
            messages.error(request, 'No file attached to this machine.')
            return redirect('machinery:machine_detail', pk=machine_id)
            
    except Machine.DoesNotExist:
        messages.error(request, 'Machine not found.')
        return redirect('machinery:machine_list')
    except Exception as e:
        messages.error(request, f'Error downloading file: {str(e)}')
        return redirect('machinery:machine_detail', pk=machine_id)


def add_schedule_detail(request):
    """HTMX view for adding schedule details to temp table"""
    if request.method == 'POST':
        machine_id = request.POST.get('machine_id')
        process_id = request.POST.get('process_id')
        shift = request.POST.get('shift')
        type_val = request.POST.get('type')
        batch_no = request.POST.get('batch_no')
        batch_qty = request.POST.get('batch_qty')
        from_date = request.POST.get('from_date')
        to_date = request.POST.get('to_date')
        from_time = request.POST.get('from_time')
        to_time = request.POST.get('to_time')
        incharge = request.POST.get('incharge')
        operator = request.POST.get('operator')
        item_id = request.POST.get('item_id')
        
        try:
            # Validate machine availability
            machine = Machine.objects.get(
                itemid=machine_id,
                company=request.user.company
            )
            
            # Check if machine is busy during the specified time
            existing_schedules = JobScheduleDetail.objects.filter(
                machineid=machine_id,
                fromdate__lte=to_date,
                todate__gte=from_date,
                released__ne=1
            )
            
            if existing_schedules.exists():
                last_schedule = existing_schedules.order_by('-todate', '-totime').first()
                messages.error(request, f'Machine is busy until {last_schedule.todate} {last_schedule.totime}')
            else:
                # Add to temp table
                JobScheduleDetailTemp.objects.create(
                    company=request.user.company,
                    user=request.user,
                    machineid=machine_id,
                    shift=shift,
                    type=type_val,
                    batchno=batch_no,
                    fromdate=from_date,
                    todate=to_date,
                    fromtime=from_time,
                    totime=to_time,
                    process=process_id,
                    qty=batch_qty,
                    incharge=incharge,
                    operator=operator,
                    itemid=item_id
                )
                messages.success(request, 'Schedule detail added successfully.')
        
        except Machine.DoesNotExist:
            messages.error(request, 'Machine not found.')
        except Exception as e:
            messages.error(request, f'Error adding schedule detail: {str(e)}')
        
        # Return updated schedule details list
        schedule_details = JobScheduleDetailTemp.objects.filter(
            company=request.user.company,
            user=request.user,
            itemid=item_id
        ).order_by('id')
        
        return render(request, 'machinery/partials/schedule_details_list.html', {
            'schedule_details': schedule_details
        })
    
    return HttpResponse('')


def remove_schedule_detail(request, detail_id):
    """HTMX view for removing schedule detail from temp table"""
    try:
        detail = JobScheduleDetailTemp.objects.get(
            id=detail_id,
            company=request.user.company,
            user=request.user
        )
        item_id = detail.itemid
        detail.delete()
        messages.success(request, 'Schedule detail removed.')
        
        # Return updated schedule details list
        schedule_details = JobScheduleDetailTemp.objects.filter(
            company=request.user.company,
            user=request.user,
            itemid=item_id
        ).order_by('id')
        
        return render(request, 'machinery/partials/schedule_details_list.html', {
            'schedule_details': schedule_details
        })
    except JobScheduleDetailTemp.DoesNotExist:
        messages.error(request, 'Schedule detail not found.')
        return HttpResponse('')


def submit_schedule_items(request):
    """HTMX view for submitting schedule items and creating job schedule"""
    if request.method == 'POST':
        item_id = request.POST.get('item_id')
        wono = request.POST.get('wono')
        
        try:
            # Check if there are schedule details in temp table
            schedule_details = JobScheduleDetailTemp.objects.filter(
                company=request.user.company,
                user=request.user,
                itemid=item_id
            )
            
            if not schedule_details.exists():
                messages.error(request, 'No schedule details found to submit.')
                return HttpResponse('')
            
            # Generate job number
            last_job = JobSchedule.objects.filter(
                company=request.user.company,
                financial_year=request.user.financial_year
            ).order_by('-jobno').first()
            
            if last_job and last_job.jobno:
                job_no = str(int(last_job.jobno) + 1).zfill(4)
            else:
                job_no = '0001'
            
            # Create job schedule master
            job_schedule = JobSchedule.objects.create(
                sysdate=timezone.now().strftime('%Y-%m-%d'),
                systime=timezone.now().strftime('%H:%M:%S'),
                user=request.user,
                company=request.user.company,
                financial_year=request.user.financial_year,
                jobno=job_no,
                wono=wono,
                itemid=item_id
            )
            
            # Create job schedule details from temp table
            for detail in schedule_details:
                JobScheduleDetail.objects.create(
                    job_schedule=job_schedule,
                    shift=detail.shift,
                    machineid=detail.machineid,
                    type=detail.type,
                    batchno=detail.batchno,
                    fromdate=detail.fromdate,
                    todate=detail.todate,
                    fromtime=detail.fromtime,
                    totime=detail.totime,
                    process=detail.process,
                    qty=detail.qty,
                    incharge=detail.incharge,
                    operator=detail.operator
                )
            
            # Clear temp table
            schedule_details.delete()
            
            messages.success(request, f'Job schedule {job_no} created successfully.')
            
            # Redirect to schedule list
            return render(request, 'machinery/partials/schedule_success.html', {
                'job_no': job_no,
                'redirect_url': reverse_lazy('machinery:schedule_list')
            })
            
        except Exception as e:
            messages.error(request, f'Error creating job schedule: {str(e)}')
            return HttpResponse('')
    
    return HttpResponse('')


def record_job_output(request):
    """HTMX view for recording job output"""
    if request.method == 'POST':
        schedule_detail_id = request.POST.get('schedule_detail_id')
        output_qty = request.POST.get('output_qty')
        uom = request.POST.get('uom')
        
        try:
            schedule_detail = JobScheduleDetail.objects.get(
                id=schedule_detail_id,
                job_schedule__company=request.user.company
            )
            
            # Create job completion record
            JobCompletion.objects.create(
                job_schedule=schedule_detail.job_schedule,
                job_schedule_detail=schedule_detail,
                outputqty=output_qty,
                uom=uom
            )
            
            messages.success(request, 'Job output recorded successfully.')
            
            # Return updated completion list
            completions = JobCompletion.objects.filter(
                job_schedule_detail=schedule_detail
            )
            
            return render(request, 'machinery/partials/job_completions.html', {
                'completions': completions,
                'schedule_detail': schedule_detail
            })
            
        except JobScheduleDetail.DoesNotExist:
            messages.error(request, 'Schedule detail not found.')
        except Exception as e:
            messages.error(request, f'Error recording output: {str(e)}')
    
    return HttpResponse('')


def get_machine_processes(request):
    """HTMX view to get processes for selected machine"""
    machine_id = request.GET.get('machine_id')
    item_id = request.GET.get('item_id')
    
    try:
        machine = Machine.objects.get(
            itemid=machine_id,
            company=request.user.company
        )
        
        # Get machine processes not already scheduled
        machine_processes = MachineProcess.objects.filter(
            machine=machine
        ).exclude(
            pid__in=JobScheduleDetailTemp.objects.filter(
                company=request.user.company,
                user=request.user,
                itemid=item_id,
                machineid=machine_id
            ).values_list('process', flat=True)
        )
        
        # Get process details
        processes = []
        for mp in machine_processes:
            try:
                from material_planning.models import ProcessMaster
                process = ProcessMaster.objects.filter(id=mp.pid).first()
                if process:
                    processes.append({
                        'id': process.id,
                        'name': process.processname
                    })
            except ImportError:
                pass
        
        return render(request, 'machinery/partials/machine_processes.html', {
            'processes': processes
        })
        
    except Machine.DoesNotExist:
        return HttpResponse('')


def get_last_machine_schedule(request):
    """HTMX view to get last schedule time for machine"""
    machine_id = request.GET.get('machine_id')
    
    try:
        # Check temp table first
        last_temp = JobScheduleDetailTemp.objects.filter(
            company=request.user.company,
            user=request.user,
            machineid=machine_id
        ).order_by('-id').first()
        
        if last_temp:
            last_time = last_temp.totime
        else:
            # Check actual schedule table
            last_schedule = JobScheduleDetail.objects.filter(
                machineid=machine_id,
                released__ne=1
            ).order_by('-todate', '-totime').first()
            
            last_time = last_schedule.totime if last_schedule else '08:00:00'
        
        return JsonResponse({'last_time': last_time})
        
    except Exception:
        return JsonResponse({'last_time': '08:00:00'})


# Machinery Reports and Analytics Views

class MachineryDashboardView(LoginRequiredMixin, ListView):
    """Main machinery dashboard with KPIs and overview"""
    model = Machine
    template_name = 'machinery/dashboard.html'
    context_object_name = 'machines'

    def get_queryset(self):
        return Machine.objects.filter(
            company=self.request.user.company
        ).select_related('company', 'financial_year', 'user')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        company = self.request.user.company
        current_date = timezone.now().date()
        
        # Basic machine statistics
        total_machines = Machine.objects.filter(company=company).count()
        context['total_machines'] = total_machines
        
        # Maintenance statistics
        total_maintenance = PreventiveMaintenance.objects.filter(company=company).count()
        context['total_maintenance'] = total_maintenance
        
        # Machines needing maintenance (overdue and due soon)
        overdue_machines = []
        due_soon_machines = []
        
        for machine in self.get_queryset():
            if machine.pmdays:
                try:
                    # Get last maintenance date
                    last_maintenance = PreventiveMaintenance.objects.filter(
                        machineid=machine.id
                    ).order_by('-sysdate').first()
                    
                    if last_maintenance:
                        last_date = datetime.strptime(last_maintenance.sysdate, '%Y-%m-%d').date()
                    else:
                        last_date = datetime.strptime(machine.sysdate, '%Y-%m-%d').date()
                    
                    pm_days = int(machine.pmdays)
                    next_due = last_date + timedelta(days=pm_days)
                    days_remaining = (next_due - current_date).days
                    
                    if days_remaining < 0:
                        overdue_machines.append({
                            'machine': machine,
                            'days_overdue': abs(days_remaining),
                            'last_maintenance': last_date
                        })
                    elif 0 <= days_remaining <= 7:
                        due_soon_machines.append({
                            'machine': machine,
                            'days_remaining': days_remaining,
                            'last_maintenance': last_date
                        })
                except (ValueError, TypeError):
                    continue
        
        context['overdue_machines'] = overdue_machines[:5]  # Latest 5
        context['due_soon_machines'] = due_soon_machines[:5]  # Latest 5
        context['overdue_count'] = len(overdue_machines)
        context['due_soon_count'] = len(due_soon_machines)
        
        # Insurance expiry tracking
        insurance_expiring = []
        for machine in self.get_queryset():
            if machine.insurance == 1 and machine.insuranceexpirydate:
                try:
                    expiry_date = datetime.strptime(machine.insuranceexpirydate, '%Y-%m-%d').date()
                    days_to_expiry = (expiry_date - current_date).days
                    
                    if days_to_expiry <= 30:  # Expiring within 30 days
                        insurance_expiring.append({
                            'machine': machine,
                            'days_to_expiry': days_to_expiry,
                            'expiry_date': expiry_date
                        })
                except (ValueError, TypeError):
                    continue
        
        context['insurance_expiring'] = insurance_expiring[:5]
        context['insurance_expiring_count'] = len(insurance_expiring)
        
        # Recent activities
        context['recent_maintenance'] = PreventiveMaintenance.objects.filter(
            company=company
        ).order_by('-sysdate')[:5]
        
        # Machine status breakdown
        insured_machines = Machine.objects.filter(company=company, insurance=1).count()
        context['insured_machines'] = insured_machines
        context['uninsured_machines'] = total_machines - insured_machines
        
        return context


class MachineryReportsView(LoginRequiredMixin, ListView):
    """Machinery reports listing view"""
    model = Machine
    template_name = 'machinery/reports_list.html'
    context_object_name = 'machines'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Machinery Reports'
        return context


class MachineUtilizationReportView(LoginRequiredMixin, ListView):
    """Machine utilization report"""
    model = Machine
    template_name = 'machinery/reports/utilization_report.html'
    context_object_name = 'machines'
    
    def get_queryset(self):
        return Machine.objects.filter(
            company=self.request.user.company
        ).select_related('company', 'financial_year', 'user')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Calculate utilization metrics for each machine
        machines_with_utilization = []
        for machine in self.get_queryset():
            # Get maintenance records to calculate downtime
            maintenance_records = PreventiveMaintenance.objects.filter(
                machineid=machine.id
            ).order_by('-sysdate')
            
            total_maintenance_days = 0
            for record in maintenance_records:
                if record.fromdate and record.todate:
                    try:
                        from_date = datetime.strptime(record.fromdate, '%Y-%m-%d').date()
                        to_date = datetime.strptime(record.todate, '%Y-%m-%d').date()
                        maintenance_days = (to_date - from_date).days + 1
                        total_maintenance_days += maintenance_days
                    except (ValueError, TypeError):
                        continue
            
            # Calculate utilization percentage (simplified calculation)
            days_in_service = 365  # Assume 1 year for calculation
            uptime_days = days_in_service - total_maintenance_days
            utilization_percent = (uptime_days / days_in_service) * 100 if days_in_service > 0 else 0
            
            # Get item details
            try:
                item = Item.objects.get(id=machine.itemid)
                machine.item_details = item
            except Item.DoesNotExist:
                machine.item_details = None
            
            machine.maintenance_days = total_maintenance_days
            machine.uptime_days = uptime_days
            machine.utilization_percent = max(0, min(100, utilization_percent))
            machine.maintenance_count = maintenance_records.count()
            
            machines_with_utilization.append(machine)
        
        context['machines'] = machines_with_utilization
        context['title'] = 'Machine Utilization Report'
        return context


class MaintenanceScheduleReportView(LoginRequiredMixin, ListView):
    """Maintenance schedule report"""
    model = PreventiveMaintenance
    template_name = 'machinery/reports/maintenance_schedule_report.html'
    context_object_name = 'maintenance_records'
    
    def get_queryset(self):
        return PreventiveMaintenance.objects.filter(
            company=self.request.user.company
        ).select_related('company', 'financial_year', 'user').order_by('-sysdate')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Enhance maintenance records with machine details
        enhanced_records = []
        for record in self.get_queryset():
            try:
                machine = Machine.objects.get(id=record.machineid)
                record.machine_info = machine
                
                # Get item details
                try:
                    item = Item.objects.get(id=machine.itemid)
                    record.item_info = item
                except Item.DoesNotExist:
                    record.item_info = None
                
            except Machine.DoesNotExist:
                record.machine_info = None
                record.item_info = None
            
            enhanced_records.append(record)
        
        context['maintenance_records'] = enhanced_records
        context['title'] = 'Maintenance Schedule Report'
        
        # Summary statistics
        total_maintenance = len(enhanced_records)
        preventive_count = len([r for r in enhanced_records if r.pmbm == 0])
        breakdown_count = len([r for r in enhanced_records if r.pmbm == 1])
        
        context['total_maintenance'] = total_maintenance
        context['preventive_count'] = preventive_count
        context['breakdown_count'] = breakdown_count
        
        return context


class MachineCostAnalysisReportView(LoginRequiredMixin, ListView):
    """Machine cost analysis report"""
    model = Machine
    template_name = 'machinery/reports/cost_analysis_report.html'
    context_object_name = 'machines'
    
    def get_queryset(self):
        return Machine.objects.filter(
            company=self.request.user.company
        ).select_related('company', 'financial_year', 'user')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Calculate cost metrics
        total_cost = 0
        machines_with_costs = []
        
        for machine in self.get_queryset():
            # Get item details
            try:
                item = Item.objects.get(id=machine.itemid)
                machine.item_details = item
            except Item.DoesNotExist:
                machine.item_details = None
            
            # Calculate total cost including maintenance
            purchase_cost = float(machine.cost) if machine.cost else 0.0
            
            # Estimate maintenance cost (simplified calculation)
            maintenance_records = PreventiveMaintenance.objects.filter(machineid=machine.id)
            estimated_maintenance_cost = maintenance_records.count() * 5000  # Assume ₹5000 per maintenance
            
            machine.purchase_cost = purchase_cost
            machine.maintenance_cost = estimated_maintenance_cost
            machine.total_cost = purchase_cost + estimated_maintenance_cost
            
            total_cost += machine.total_cost
            machines_with_costs.append(machine)
        
        # Sort by total cost descending
        machines_with_costs.sort(key=lambda x: x.total_cost, reverse=True)
        
        context['machines'] = machines_with_costs
        context['total_fleet_cost'] = total_cost
        context['average_machine_cost'] = total_cost / len(machines_with_costs) if machines_with_costs else 0
        context['title'] = 'Machine Cost Analysis Report'
        
        return context


class InsuranceExpiryTrackingView(LoginRequiredMixin, ListView):
    """Insurance expiry tracking view"""
    model = Machine
    template_name = 'machinery/insurance_tracking.html'
    context_object_name = 'machines'
    
    def get_queryset(self):
        return Machine.objects.filter(
            company=self.request.user.company,
            insurance=1  # Only insured machines
        ).select_related('company', 'financial_year', 'user')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        current_date = timezone.now().date()
        
        # Categorize machines by insurance status
        expired_insurance = []
        expiring_soon = []  # Within 30 days
        expiring_later = []  # Within 90 days
        active_insurance = []
        
        for machine in self.get_queryset():
            if machine.insuranceexpirydate:
                try:
                    expiry_date = datetime.strptime(machine.insuranceexpirydate, '%Y-%m-%d').date()
                    days_to_expiry = (expiry_date - current_date).days
                    
                    # Get item details
                    try:
                        item = Item.objects.get(id=machine.itemid)
                        machine.item_details = item
                    except Item.DoesNotExist:
                        machine.item_details = None
                    
                    machine.days_to_expiry = days_to_expiry
                    machine.expiry_date_obj = expiry_date
                    
                    if days_to_expiry < 0:
                        expired_insurance.append(machine)
                    elif days_to_expiry <= 30:
                        expiring_soon.append(machine)
                    elif days_to_expiry <= 90:
                        expiring_later.append(machine)
                    else:
                        active_insurance.append(machine)
                        
                except (ValueError, TypeError):
                    # Invalid date format
                    machine.days_to_expiry = None
                    machine.expiry_date_obj = None
                    active_insurance.append(machine)
            else:
                # No expiry date set
                machine.days_to_expiry = None
                machine.expiry_date_obj = None
                active_insurance.append(machine)
        
        # Sort by days to expiry
        expired_insurance.sort(key=lambda x: abs(x.days_to_expiry), reverse=True)
        expiring_soon.sort(key=lambda x: x.days_to_expiry)
        expiring_later.sort(key=lambda x: x.days_to_expiry)
        
        context['expired_insurance'] = expired_insurance
        context['expiring_soon'] = expiring_soon
        context['expiring_later'] = expiring_later
        context['active_insurance'] = active_insurance
        
        # Summary counts
        context['expired_count'] = len(expired_insurance)
        context['expiring_soon_count'] = len(expiring_soon)
        context['expiring_later_count'] = len(expiring_later)
        context['active_count'] = len(active_insurance)
        context['total_insured'] = self.get_queryset().count()
        
        # Calculate insurance costs
        total_insurance_value = 0
        for machine in self.get_queryset():
            if machine.cost:
                # Assume insurance is 2% of machine cost annually
                insurance_value = float(machine.cost) * 0.02
                total_insurance_value += insurance_value
        
        context['total_insurance_value'] = total_insurance_value
        context['title'] = 'Insurance Expiry Tracking'
        
        return context
