{% extends "core/base.html" %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{{ page_title }}</h1>
                        <p class="text-gray-600 mt-1">Search and select enquiries to create quotations</p>
                    </div>
                    <div class="flex space-x-3">
                        <a href="{% url 'sales_distribution:quotation_list' %}" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                            View All Quotations
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search Form -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-t-lg">
                <h3 class="text-lg font-medium">Search Enquiries</h3>
            </div>
            <div class="px-6 py-6">
                <form method="get" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label for="{{ form.enquiry_id.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ form.enquiry_id.label }}
                            </label>
                            {{ form.enquiry_id }}
                            {% if form.enquiry_id.errors %}
                                <div class="text-red-600 text-sm mt-1">
                                    {% for error in form.enquiry_id.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="{{ form.customer_id.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ form.customer_id.label }}
                            </label>
                            {{ form.customer_id }}
                            {% if form.customer_id.errors %}
                                <div class="text-red-600 text-sm mt-1">
                                    {% for error in form.customer_id.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    {% if form.non_field_errors %}
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <div class="text-red-600 text-sm">
                                {% for error in form.non_field_errors %}{{ error }}{% endfor %}
                            </div>
                        </div>
                    {% endif %}

                    <div class="flex justify-end space-x-3">
                        <a href="{% url 'sales_distribution:quotation_selection' %}" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            Clear
                        </a>
                        
                        <button type="submit" 
                                class="inline-flex items-center px-6 py-2 bg-blue-600 border border-transparent rounded-lg font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Search Enquiries
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Results -->
        {% if enquiries %}
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Available Enquiries</h3>
                    <p class="text-sm text-gray-600 mt-1">{{ enquiries|length }} enquir{{ enquiries|length|pluralize:"y,ies" }} found</p>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Enquiry Details
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Customer Information
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Enquiry Description
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Date Created
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Quotations
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for enquiry in enquiries %}
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">
                                            Enquiry #{{ enquiry.enqid }}
                                        </div>
                                        {% if enquiry.customerid %}
                                            <div class="text-xs text-gray-500">
                                                Customer ID: {{ enquiry.customerid }}
                                            </div>
                                        {% endif %}
                                    </td>
                                    
                                    <td class="px-6 py-4">
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ enquiry.customername|default:"—" }}
                                        </div>
                                        {% if enquiry.contactperson %}
                                            <div class="text-xs text-gray-500">
                                                Contact: {{ enquiry.contactperson }}
                                            </div>
                                        {% endif %}
                                        {% if enquiry.email %}
                                            <div class="text-xs text-gray-500">
                                                {{ enquiry.email }}
                                            </div>
                                        {% endif %}
                                        {% if enquiry.regdcity %}
                                            <div class="text-xs text-gray-500">
                                                {{ enquiry.regdcity.cityname }}{% if enquiry.regdstate %}, {{ enquiry.regdstate.statename }}{% endif %}
                                            </div>
                                        {% endif %}
                                    </td>
                                    
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-gray-900">
                                            {{ enquiry.enquiryfor|truncatechars:100|default:"—" }}
                                        </div>
                                        {% if enquiry.remark %}
                                            <div class="text-xs text-gray-500 mt-1">
                                                Note: {{ enquiry.remark|truncatechars:80 }}
                                            </div>
                                        {% endif %}
                                    </td>
                                    
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            {{ enquiry.sysdate|default:"—" }}
                                        </div>
                                        {% if enquiry.systime %}
                                            <div class="text-xs text-gray-500">
                                                {{ enquiry.systime }}
                                            </div>
                                        {% endif %}
                                    </td>
                                    
                                    <td class="px-6 py-4 whitespace-nowrap text-center">
                                        {% if enquiry.quotation_count > 0 %}
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                {{ enquiry.quotation_count }} quotation{{ enquiry.quotation_count|pluralize }}
                                            </span>
                                        {% else %}
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                No quotations
                                            </span>
                                        {% endif %}
                                    </td>
                                    
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex items-center justify-end space-x-2">
                                            <a href="{% url 'sales_distribution:enquiry_detail' enquiry.enqid %}" 
                                               class="text-gray-600 hover:text-gray-700 tooltip" 
                                               title="View Enquiry">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                </svg>
                                            </a>
                                            
                                            <a href="{% url 'sales_distribution:quotation_create' enquiry.enqid %}" 
                                               class="inline-flex items-center px-3 py-1.5 bg-green-600 border border-transparent rounded text-xs font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                                </svg>
                                                Create Quotation
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        {% elif form.is_bound %}
            <!-- No Results Found -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No enquiries found</h3>
                    <p class="mt-1 text-sm text-gray-500">
                        No enquiries match your search criteria. Try different search parameters.
                    </p>
                    <div class="mt-6">
                        <a href="{% url 'sales_distribution:enquiry_new' %}" 
                           class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                            Create New Enquiry
                        </a>
                    </div>
                </div>
            </div>
        {% else %}
            <!-- Initial State -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Search for Enquiries</h3>
                    <p class="mt-1 text-sm text-gray-500">
                        Enter an Enquiry ID or Customer ID above to find enquiries available for quotation creation.
                    </p>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<style>
    .form-group input,
    .form-group select {
        width: 100%;
        padding: 0.625rem 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    
    .form-group input:focus,
    .form-group select:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    }
    
    .tooltip {
        position: relative;
    }
    
    .tooltip:hover::after {
        content: attr(title);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: #1f2937;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        z-index: 1000;
        margin-bottom: 4px;
    }
    
    .tooltip:hover::before {
        content: "";
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 4px solid transparent;
        border-top-color: #1f2937;
        z-index: 1000;
    }
</style>
{% endblock %}