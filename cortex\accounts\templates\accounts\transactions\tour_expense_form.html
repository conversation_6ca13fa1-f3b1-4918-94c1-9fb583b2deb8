<!-- accounts/templates/accounts/transactions/tour_expense_form.html -->
<!-- Tour Expense Create/Edit Form Template -->
<!-- Task Group 9: Tour/Expense Management - Tour Expense Form (Task 9.5) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{% if object %}Edit Tour Expense{% else %}New Tour Expense{% endif %} - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-orange-600 to-sap-orange-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="receipt" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">
                        {% if object %}Edit Tour Expense{% else %}New Tour Expense{% endif %}
                    </h1>
                    <p class="text-sm text-sap-gray-600 mt-1">
                        {% if object %}Modify expense details and receipt information{% else %}Record a new tour-related expense{% endif %}
                    </p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:tour_expense_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Tour Expense Form -->
    <div class="max-w-6xl mx-auto">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="edit" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                    Expense Information
                </h3>
                <p class="text-sm text-sap-gray-600 mt-1">Complete all required fields to record the expense</p>
            </div>
            
            <form method="post" enctype="multipart/form-data" id="expense-form" class="p-6" x-data="expenseForm()">
                {% csrf_token %}
                
                <!-- Basic Expense Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="info" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                        Basic Expense Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Expense Number -->
                        <div>
                            <label for="{{ form.expense_number.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Expense Number
                            </label>
                            {{ form.expense_number|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.expense_number.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.expense_number.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Auto-generated if left blank</p>
                        </div>
                        
                        <!-- Tour Voucher -->
                        <div>
                            <label for="{{ form.tour_voucher.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Tour Voucher
                            </label>
                            {{ form.tour_voucher|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.tour_voucher.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.tour_voucher.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Expense Date -->
                        <div>
                            <label for="{{ form.expense_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Expense Date *
                            </label>
                            {{ form.expense_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.expense_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.expense_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Expense Type -->
                        <div>
                            <label for="{{ form.expense_type.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Expense Type *
                            </label>
                            {{ form.expense_type|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.expense_type.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.expense_type.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Category -->
                        <div>
                            <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Category
                            </label>
                            {{ form.category|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.category.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.category.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Status -->
                        <div>
                            <label for="{{ form.status.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Status
                            </label>
                            {{ form.status|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.status.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.status.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Employee Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="user" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Employee Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Employee Name -->
                        <div>
                            <label for="{{ form.employee_name.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Employee Name *
                            </label>
                            {{ form.employee_name|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.employee_name.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.employee_name.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Employee Code -->
                        <div>
                            <label for="{{ form.employee_code.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Employee Code
                            </label>
                            {{ form.employee_code|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.employee_code.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.employee_code.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Department -->
                        <div>
                            <label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Department
                            </label>
                            {{ form.department|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.department.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.department.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Expense Details Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="dollar-sign" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                        Expense Details
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Expense Amount -->
                        <div>
                            <label for="{{ form.expense_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Expense Amount (₹) *
                            </label>
                            {{ form.expense_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.expense_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.expense_amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Tax Amount -->
                        <div>
                            <label for="{{ form.tax_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Tax Amount (₹)
                            </label>
                            {{ form.tax_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.tax_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.tax_amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Total Amount -->
                        <div>
                            <label for="{{ form.total_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Total Amount (₹)
                            </label>
                            {{ form.total_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.total_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.total_amount.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Auto-calculated from expense + tax</p>
                        </div>
                        
                        <!-- Currency -->
                        <div>
                            <label for="{{ form.currency.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Currency
                            </label>
                            {{ form.currency|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.currency.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.currency.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Expense Description -->
                    <div class="mt-6">
                        <label for="{{ form.expense_description.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                            Expense Description *
                        </label>
                        {{ form.expense_description|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                        {% if form.expense_description.errors %}
                        <p class="text-red-600 text-xs mt-1">{{ form.expense_description.errors.0 }}</p>
                        {% endif %}
                        <p class="text-xs text-sap-gray-500 mt-1">Detailed description of the expense</p>
                    </div>
                </div>
                
                <!-- Vendor/Receipt Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="file-text" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                        Vendor & Receipt Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Vendor Name -->
                        <div>
                            <label for="{{ form.vendor_name.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Vendor Name
                            </label>
                            {{ form.vendor_name|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.vendor_name.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.vendor_name.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Receipt Number -->
                        <div>
                            <label for="{{ form.receipt_number.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Receipt Number
                            </label>
                            {{ form.receipt_number|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.receipt_number.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.receipt_number.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Receipt Date -->
                        <div>
                            <label for="{{ form.receipt_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Receipt Date
                            </label>
                            {{ form.receipt_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.receipt_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.receipt_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Payment Method -->
                        <div>
                            <label for="{{ form.payment_method.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Payment Method
                            </label>
                            {{ form.payment_method|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.payment_method.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.payment_method.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Receipt Upload -->
                    <div class="mt-6">
                        <label for="{{ form.receipt_file.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                            Receipt File
                        </label>
                        {{ form.receipt_file|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                        {% if form.receipt_file.errors %}
                        <p class="text-red-600 text-xs mt-1">{{ form.receipt_file.errors.0 }}</p>
                        {% endif %}
                        <p class="text-xs text-sap-gray-500 mt-1">Upload scanned copy of receipt (PDF, JPG, PNG)</p>
                    </div>
                </div>
                
                <!-- Location Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="map-pin" class="w-5 h-5 mr-2 text-sap-indigo-600"></i>
                        Location Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Destination -->
                        <div>
                            <label for="{{ form.destination.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Destination
                            </label>
                            {{ form.destination|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.destination.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.destination.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- From Location -->
                        <div>
                            <label for="{{ form.from_location.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                From Location
                            </label>
                            {{ form.from_location|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.from_location.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.from_location.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Additional Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="sticky-note" class="w-5 h-5 mr-2 text-sap-gray-600"></i>
                        Additional Information
                    </h4>
                    <div class="grid grid-cols-1 gap-6">
                        <!-- Remarks -->
                        <div>
                            <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Remarks
                            </label>
                            {{ form.remarks|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.remarks.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.remarks.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Additional notes or justification for the expense</p>
                        </div>
                    </div>
                </div>
                
                <!-- Amount Calculation Display -->
                <div class="mb-8 bg-sap-orange-50 border border-sap-orange-200 rounded-lg p-6" x-show="calculatedTotal > 0">
                    <h4 class="text-lg font-medium text-sap-orange-800 mb-4 flex items-center">
                        <i data-lucide="calculator" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                        Amount Calculation
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="text-center">
                            <p class="text-sm text-sap-orange-600">Expense Amount</p>
                            <p class="text-xl font-bold text-sap-orange-800" x-text="'₹' + expenseAmount.toFixed(2)"></p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-orange-600">Tax Amount</p>
                            <p class="text-xl font-bold text-sap-orange-800" x-text="'₹' + taxAmount.toFixed(2)"></p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-orange-600">Total Amount</p>
                            <p class="text-xl font-bold text-sap-orange-800" x-text="'₹' + calculatedTotal.toFixed(2)"></p>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="resetForm()" 
                                class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="rotate-ccw" class="w-4 h-4 inline mr-2"></i>
                            Reset
                        </button>
                        <button type="button" @click="calculateTotal()" 
                                class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="calculator" class="w-4 h-4 inline mr-2"></i>
                            Calculate Total
                        </button>
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="{% url 'accounts:tour_expense_list' %}" 
                           class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                            {% if object %}Update Expense{% else %}Save Expense{% endif %}
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Expense Guidelines -->
        <div class="mt-6 bg-sap-blue-50 border border-sap-blue-200 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i data-lucide="info" class="w-5 h-5 text-sap-blue-600"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-sap-blue-800">Expense Submission Guidelines</h3>
                    <div class="mt-2 text-sm text-sap-blue-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Ensure all expense details are accurate and complete</li>
                            <li>Upload clear and legible receipts for all expenses</li>
                            <li>Submit expenses within 30 days of incurrence</li>
                            <li>Provide detailed descriptions for business justification</li>
                            <li>Follow company expense policy for approval limits</li>
                            <li>Keep original receipts for audit purposes</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function expenseForm() {
    return {
        calculatedTotal: 0,
        expenseAmount: 0,
        taxAmount: 0,
        
        calculateTotal() {
            const expense = parseFloat(document.getElementById('{{ form.expense_amount.id_for_label }}').value) || 0;
            const tax = parseFloat(document.getElementById('{{ form.tax_amount.id_for_label }}').value) || 0;
            
            this.expenseAmount = expense;
            this.taxAmount = tax;
            this.calculatedTotal = expense + tax;
            
            // Update total amount field
            const totalField = document.getElementById('{{ form.total_amount.id_for_label }}');
            if (totalField) {
                totalField.value = this.calculatedTotal.toFixed(2);
            }
        }
    }
}

function resetForm() {
    if (confirm('Are you sure you want to reset the form? All unsaved changes will be lost.')) {
        document.getElementById('expense-form').reset();
    }
}

// Auto-generate expense number if creating new expense
document.addEventListener('DOMContentLoaded', function() {
    const expenseNumberInput = document.getElementById('{{ form.expense_number.id_for_label }}');
    if (expenseNumberInput && !expenseNumberInput.value) {
        // Generate expense number based on current timestamp
        const today = new Date();
        const year = today.getFullYear().toString().substr(-2);
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        const sequence = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        
        expenseNumberInput.value = `EXP-${year}${month}${day}${sequence}`;
    }
    
    // Auto-calculate total when expense amount or tax amount changes
    const expenseAmountField = document.getElementById('{{ form.expense_amount.id_for_label }}');
    const taxAmountField = document.getElementById('{{ form.tax_amount.id_for_label }}');
    const totalAmountField = document.getElementById('{{ form.total_amount.id_for_label }}');
    
    [expenseAmountField, taxAmountField].forEach(field => {
        if (field) {
            field.addEventListener('input', function() {
                const expense = parseFloat(expenseAmountField.value) || 0;
                const tax = parseFloat(taxAmountField.value) || 0;
                const total = expense + tax;
                
                if (totalAmountField) {
                    totalAmountField.value = total.toFixed(2);
                }
            });
        }
    });
    
    // Auto-set receipt date to expense date if not provided
    const expenseDateField = document.getElementById('{{ form.expense_date.id_for_label }}');
    const receiptDateField = document.getElementById('{{ form.receipt_date.id_for_label }}');
    
    if (expenseDateField && receiptDateField) {
        expenseDateField.addEventListener('change', function() {
            if (!receiptDateField.value) {
                receiptDateField.value = this.value;
            }
        });
    }
    
    // Auto-populate employee info from tour voucher
    const tourVoucherField = document.getElementById('{{ form.tour_voucher.id_for_label }}');
    if (tourVoucherField) {
        tourVoucherField.addEventListener('change', function() {
            // This would typically make an AJAX call to get employee info
            console.log('Tour voucher changed:', this.value);
        });
    }
    
    lucide.createIcons();
});
</script>
{% endblock %>