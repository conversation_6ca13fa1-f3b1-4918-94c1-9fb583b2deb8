{% extends "core/base.html" %}
{% load static %}

{% block title %}{{ action }} Budget Code - MIS{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        background: white;
        border-radius: 8px;
        padding: 2rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    }
    .form-section {
        margin-bottom: 2rem;
    }
    .form-section:last-child {
        margin-bottom: 0;
    }
    .field-help {
        font-size: 0.875rem;
        color: #6b7280;
        margin-top: 0.25rem;
    }
    .error-message {
        color: #dc2626;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }
</style>
{% endblock %}

{% block page_header %}
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">{{ action }} Budget Code</h1>
            <p class="text-gray-600">Budget classification management</p>
        </div>
        <div class="flex space-x-3">
            <a href="{% url 'mis:budget_code_list' %}" 
               class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                <i class="fas fa-arrow-left mr-2"></i>Back to List
            </a>
        </div>
    </div>
{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="form-container">
        <form method="post" hx-post="{% if action == 'Create' %}{% url 'mis:budget_code_create' %}{% else %}{% url 'mis:budget_code_update' object.pk %}{% endif %}"
              hx-target="#form-messages"
              hx-swap="innerHTML">
            {% csrf_token %}
            
            <!-- Form Messages -->
            <div id="form-messages" class="mb-6">
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} p-4 mb-4 rounded-lg {% if message.tags == 'success' %}bg-green-50 text-green-800 border border-green-200{% elif message.tags == 'error' %}bg-red-50 text-red-800 border border-red-200{% elif message.tags == 'warning' %}bg-yellow-50 text-yellow-800 border border-yellow-200{% else %}bg-blue-50 text-blue-800 border border-blue-200{% endif %}">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    {% if message.tags == 'success' %}
                                        <i class="fas fa-check-circle"></i>
                                    {% elif message.tags == 'error' %}
                                        <i class="fas fa-exclamation-circle"></i>
                                    {% elif message.tags == 'warning' %}
                                        <i class="fas fa-exclamation-triangle"></i>
                                    {% else %}
                                        <i class="fas fa-info-circle"></i>
                                    {% endif %}
                                </div>
                                <div class="ml-3">
                                    {{ message }}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% endif %}
            </div>

            <!-- Budget Code Information -->
            <div class="form-section">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Budget Code Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Symbol Field -->
                    <div>
                        <label for="{{ form.symbol.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ form.symbol.label }}
                            <span class="text-red-500">*</span>
                        </label>
                        {{ form.symbol }}
                        <div class="field-help">
                            Unique 2-character budget symbol (e.g., "PD" for Production)
                        </div>
                        {% if form.symbol.errors %}
                            {% for error in form.symbol.errors %}
                                <div class="error-message">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                    </div>

                    <!-- Description Field (spans full width) -->
                    <div class="md:col-span-2">
                        <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ form.description.label }}
                            <span class="text-red-500">*</span>
                        </label>
                        {{ form.description }}
                        <div class="field-help">
                            Descriptive name for the budget code (e.g., "Production Department Budget")
                        </div>
                        {% if form.description.errors %}
                            {% for error in form.description.errors %}
                                <div class="error-message">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-section">
                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    <a href="{% url 'mis:budget_code_list' %}" 
                       class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg text-sm font-medium">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg text-sm font-medium">
                        <i class="fas fa-save mr-2"></i>{{ action }} Budget Code
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Additional Information Panel -->
    {% if action == 'Update' and object %}
    <div class="form-container mt-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Usage Information</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-blue-50 p-4 rounded-lg">
                <div class="text-2xl font-bold text-blue-600">{{ object.allocations.count }}</div>
                <div class="text-sm text-blue-600">Total Allocations</div>
            </div>
            
            <div class="bg-green-50 p-4 rounded-lg">
                <div class="text-2xl font-bold text-green-600">
                    {{ object.allocations.filter|status:'approved'|length }}
                </div>
                <div class="text-sm text-green-600">Approved Allocations</div>
            </div>
            
            <div class="bg-yellow-50 p-4 rounded-lg">
                <div class="text-2xl font-bold text-yellow-600">
                    {{ object.allocations.filter|status:'submitted'|length }}
                </div>
                <div class="text-sm text-yellow-600">Pending Approvals</div>
            </div>
        </div>

        {% if object.allocations.exists %}
        <div class="mt-6">
            <h4 class="text-md font-semibold text-gray-900 mb-3">Recent Allocations</h4>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Period</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for allocation in object.allocations.all|slice:":5" %}
                        <tr>
                            <td class="px-4 py-2 text-sm text-gray-900">{{ allocation.budget_period.name }}</td>
                            <td class="px-4 py-2 text-sm text-gray-900">{{ allocation.get_allocation_type_display }}</td>
                            <td class="px-4 py-2 text-sm font-medium text-gray-900">₹{{ allocation.allocated_amount|floatformat:2 }}</td>
                            <td class="px-4 py-2">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                           {% if allocation.status == 'approved' %}bg-green-100 text-green-800
                                           {% elif allocation.status == 'submitted' %}bg-yellow-100 text-yellow-800
                                           {% elif allocation.status == 'draft' %}bg-gray-100 text-gray-800
                                           {% else %}bg-red-100 text-red-800{% endif %}">
                                    {{ allocation.get_status_display }}
                                </span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            {% if object.allocations.count > 5 %}
            <div class="mt-3 text-center">
                <a href="{% url 'mis:budget_allocation_list' %}?budget_code={{ object.pk }}" 
                   class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    View all {{ object.allocations.count }} allocations →
                </a>
            </div>
            {% endif %}
        </div>
        {% endif %}
    </div>
    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on first input
    const firstInput = document.querySelector('input[type="text"]');
    if (firstInput) {
        firstInput.focus();
    }

    // Real-time validation feedback
    const symbolField = document.getElementById('{{ form.symbol.id_for_label }}');
    const descriptionField = document.getElementById('{{ form.description.id_for_label }}');
    
    if (symbolField) {
        symbolField.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
            if (this.value.length > 2) {
                this.value = this.value.substring(0, 2);
            }
        });
    }
    
    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        // Clear previous error messages
        document.querySelectorAll('.error-message').forEach(el => el.remove());
        
        // Validate symbol
        if (symbolField && !symbolField.value.trim()) {
            showFieldError(symbolField, 'Budget symbol is required.');
            isValid = false;
        }
        
        // Validate description
        if (descriptionField && !descriptionField.value.trim()) {
            showFieldError(descriptionField, 'Description is required.');
            isValid = false;
        }
        
        if (!isValid) {
            e.preventDefault();
        }
    });
    
    function showFieldError(field, message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        field.parentNode.appendChild(errorDiv);
        field.classList.add('border-red-500');
    }
});
</script>
{% endblock %}