<!-- sales_distribution/templates/sales_distribution/partials/customer_table.html -->
<!-- Customer Table Partial for HTMX Updates -->

<div class="overflow-x-auto">
    {% if customers %}
    <table class="min-w-full divide-y divide-sap-gray-200">
        <thead class="bg-sap-gray-50">
            <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider w-16">
                    SN
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider w-24">
                    Fin Yrs
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    Customer Name
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider w-24">
                    Code
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    Address
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider w-24">
                    Gen. Date
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider w-32">
                    Gen. By
                </th>
                <th class="px-6 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider w-24">
                    Actions
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-sap-gray-200">
            {% for customer in customers %}
            <tr class="hover:bg-sap-gray-50 transition-colors duration-150">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900 text-right">
                    {{ forloop.counter }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-600">
                    {% if customer.finyearid %}
                        {{ customer.finyearid.finyear }}
                    {% else %}
                        -
                    {% endif %}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    {% if customer.customer_name %}
                        <a href="{% url 'sales_distribution:customer_edit' customer.salesid %}" 
                           class="text-sm font-medium text-sap-blue-600 hover:text-sap-blue-700 hover:underline"
                           title="Edit Customer: {{ customer.customer_name }}">
                            {{ customer.customer_name }}
                        </a>
                    {% else %}
                        <span class="text-sm text-sap-gray-400">-</span>
                    {% endif %}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-600">
                    {% if customer.customerid %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-gray-100 text-sap-gray-800">
                            {{ customer.customerid }}
                        </span>
                    {% else %}
                        -
                    {% endif %}
                </td>
                <td class="px-6 py-4 text-sm text-sap-gray-600">
                    <div class="max-w-xs truncate" title="{{ customer.full_address }}">
                        {% if customer.full_address %}
                            {{ customer.full_address }}
                        {% else %}
                            <span class="text-sap-gray-400">No address provided</span>
                        {% endif %}
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-600">
                    {% if customer.sysdate %}
                        {{ customer.sysdate }}
                    {% else %}
                        -
                    {% endif %}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-600">
                    {% if customer.sessionid %}
                        <div class="max-w-24 truncate" title="{{ customer.sessionid }}">
                            {{ customer.sessionid }}
                        </div>
                    {% else %}
                        -
                    {% endif %}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-center">
                    <div class="flex items-center justify-center space-x-2">
                        <a href="{% url 'sales_distribution:customer_edit' customer.salesid %}"
                           class="inline-flex items-center p-1.5 border border-sap-blue-300 rounded-lg text-sap-blue-600 bg-sap-blue-50 hover:bg-sap-blue-100 transition-colors duration-200"
                           title="Edit Customer">
                            <i data-lucide="edit-2" class="w-3 h-3"></i>
                        </a>
                        <button type="button"
                                class="inline-flex items-center p-1.5 border border-sap-green-300 rounded-lg text-sap-green-600 bg-sap-green-50 hover:bg-sap-green-100 transition-colors duration-200"
                                title="View Details">
                            <i data-lucide="eye" class="w-3 h-3"></i>
                        </button>
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <!-- Empty State -->
    <div class="px-6 py-12 text-center">
        <div class="flex flex-col items-center">
            <i data-lucide="users" class="w-16 h-16 text-sap-gray-400 mb-4"></i>
            <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No customers found</h3>
            {% if request.GET.search %}
                <p class="text-sm text-sap-gray-600 mb-4">
                    No customers match your search criteria "{{ request.GET.search }}".
                </p>
                <button onclick="clearSearch()" 
                        class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                    <i data-lucide="x" class="w-4 h-4 mr-2"></i>
                    Clear Search
                </button>
            {% else %}
                <p class="text-sm text-sap-gray-600">No customers have been created yet.</p>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>