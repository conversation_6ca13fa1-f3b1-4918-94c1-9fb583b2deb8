{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
.sap-input {
    @apply w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.sap-button {
    @apply px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500;
}

.sap-checkbox {
    @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
}

.tab-container {
    @apply bg-white rounded-lg shadow-md overflow-hidden;
}

.tab-nav {
    @apply flex bg-gray-100 border-b border-gray-200;
}

.tab-button {
    @apply flex-1 px-4 py-3 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-200 focus:outline-none focus:text-gray-900 focus:bg-gray-200 border-b-2 border-transparent;
}

.tab-button.active {
    @apply text-blue-600 bg-white border-blue-600;
}

.tab-content {
    @apply p-6;
}

.tab-pane {
    @apply hidden;
}

.tab-pane.active {
    @apply block;
}

.form-section {
    @apply mb-6;
}

.form-section h3 {
    @apply text-lg font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2;
}

.form-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4;
}

.form-group {
    @apply mb-4;
}

.form-group label {
    @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-group .required:after {
    content: " *";
    @apply text-red-500;
}

.po-info-card {
    @apply bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6;
}

.products-table {
    @apply w-full border-collapse border border-gray-300 mt-4;
}

.products-table th,
.products-table td {
    @apply border border-gray-300 p-2 text-left;
}

.products-table th {
    @apply bg-gray-100 font-semibold;
}

.alert {
    @apply p-4 mb-4 rounded-md;
}

.alert-success {
    @apply bg-green-100 border border-green-400 text-green-700;
}

.alert-error {
    @apply bg-red-100 border border-red-400 text-red-700;
}

.date-range-group {
    @apply grid grid-cols-2 gap-2;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let currentTab = {{ active_tab|default:0 }};

function showTab(tabIndex) {
    // Hide all tab panes
    const tabPanes = document.querySelectorAll('.tab-pane');
    tabPanes.forEach(pane => pane.classList.remove('active'));
    
    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => button.classList.remove('active'));
    
    // Show selected tab pane
    document.getElementById(`tab-${tabIndex}`).classList.add('active');
    
    // Add active class to selected tab button
    document.getElementById(`tab-button-${tabIndex}`).classList.add('active');
    
    currentTab = tabIndex;
    
    // Update URL with current tab
    const url = new URL(window.location);
    url.searchParams.set('tab', tabIndex);
    window.history.pushState({}, '', url);
}

function nextTab() {
    if (currentTab < 3) {
        showTab(currentTab + 1);
    }
}

function previousTab() {
    if (currentTab > 0) {
        showTab(currentTab - 1);
    }
}

function loadSubcategories(categoryId) {
    const subcategorySelect = document.getElementById('id_subcategory');
    
    if (!categoryId) {
        subcategorySelect.innerHTML = '<option value="">Select Subcategory</option>';
        return;
    }
    
    fetch(`{% url 'sales_distribution:work_order_subcategory_ajax' %}?category_id=${categoryId}`)
        .then(response => response.json())
        .then(data => {
            subcategorySelect.innerHTML = '<option value="">Select Subcategory</option>';
            data.subcategories.forEach(subcat => {
                const option = document.createElement('option');
                option.value = subcat.sub_cid;
                option.textContent = subcat.sub_c_name;
                subcategorySelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error loading subcategories:', error);
        });
}

function loadStates(countryId) {
    const stateSelect = document.getElementById('id_shipping_state');
    const citySelect = document.getElementById('id_shipping_city');
    
    // Clear existing options
    stateSelect.innerHTML = '<option value="">Select State</option>';
    citySelect.innerHTML = '<option value="">Select City</option>';
    
    if (!countryId) return;
    
    // This would need to be implemented as an AJAX endpoint
    // For now, we'll keep the existing functionality
}

function loadCities(stateId) {
    const citySelect = document.getElementById('id_shipping_city');
    
    citySelect.innerHTML = '<option value="">Select City</option>';
    
    if (!stateId) return;
    
    // This would need to be implemented as an AJAX endpoint
    // For now, we'll keep the existing functionality
}

function addProduct() {
    const form = document.getElementById('product-form');
    const formData = new FormData(form);
    formData.append('action', 'add_product');
    
    fetch(window.location.href, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => {
        if (response.ok) {
            location.reload();
        } else {
            alert('Error adding product');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error adding product');
    });
}

function removeProduct(productId) {
    if (confirm('Are you sure you want to remove this product?')) {
        fetch(`{% url 'sales_distribution:work_order_product_delete' product_id=0 %}`.replace('0', productId), {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Error removing product');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error removing product');
        });
    }
}

function submitWorkOrder() {
    if (currentTab !== 3) {
        alert('Please complete all tabs before submitting the work order.');
        return false;
    }
    
    if (confirm('Are you sure you want to submit this work order? This action cannot be undone.')) {
        const form = document.getElementById('main-form');
        const submitInput = document.createElement('input');
        submitInput.type = 'hidden';
        submitInput.name = 'action';
        submitInput.value = 'submit_work_order';
        form.appendChild(submitInput);
        form.submit();
    }
    
    return false;
}

document.addEventListener('DOMContentLoaded', function() {
    showTab(currentTab);
});
</script>
{% endblock %}

{% block content %}
<div class="container mx-auto p-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">{{ page_title }}</h1>
        <nav class="text-sm">
            <a href="{% url 'sales_distribution:dashboard' %}" class="text-blue-600 hover:text-blue-800">Sales & Distribution</a>
            <span class="text-gray-500 mx-2">></span>
            <a href="{% url 'sales_distribution:work_order_po_selection' %}" class="text-blue-600 hover:text-blue-800">Work Order - New</a>
            <span class="text-gray-500 mx-2">></span>
            <span class="text-gray-700">Create Work Order</span>
        </nav>
    </div>

    <!-- Purchase Order Information Header (Matching Legacy Layout) -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div class="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4 border-l-4 border-blue-500">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div class="space-y-2">
                    <div><span class="font-medium text-gray-700">Customer Name:</span> <span class="text-gray-900 font-semibold">{{ customer_name }}</span></div>
                    <div><span class="font-medium text-gray-700">Enquiry No:</span> <span class="text-gray-900">{{ enquiry_no|default:"N/A" }}</span></div>
                </div>
                <div class="space-y-2">
                    <div><span class="font-medium text-gray-700">PO No.:</span> <span class="text-blue-600 font-bold text-lg">{{ po_no }}</span></div>
                    <div><span class="font-medium text-gray-700">PO Date:</span> <span class="text-gray-900">{{ po.formatted_po_date|default:"N/A" }}</span></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert {% if message.tags == 'success' %}alert-success{% else %}alert-error{% endif %}">
                {{ message }}
            </div>
        {% endfor %}
    {% endif %}

    <!-- Main Form -->
    <form id="main-form" method="post" class="tab-container">
        {% csrf_token %}
        
        <!-- Tab Navigation -->
        <div class="tab-nav">
            <button type="button" id="tab-button-0" class="tab-button" onclick="showTab(0)">
                1. Task Execution
            </button>
            <button type="button" id="tab-button-1" class="tab-button" onclick="showTab(1)">
                2. Shipping
            </button>
            <button type="button" id="tab-button-2" class="tab-button" onclick="showTab(2)">
                3. Products
            </button>
            <button type="button" id="tab-button-3" class="tab-button" onclick="showTab(3)">
                4. Instructions
            </button>
        </div>

        <!-- Tab Content -->
        <div class="tab-content">
            <!-- Tab 1: Task Execution -->
            <div id="tab-0" class="tab-pane">
                <!-- Top Row: Category, Date, Project Title (Matching Legacy Layout) -->
                <div class="border-b border-gray-200 pb-4 mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                        <div class="form-group">
                            <label for="{{ task_form.category.id_for_label }}" class="required text-sm font-medium text-gray-700">Category:</label>
                            {{ task_form.category }}
                        </div>
                        <div class="form-group">
                            <label for="{{ task_form.subcategory.id_for_label }}" class="text-sm font-medium text-gray-700">Subcategory:</label>
                            {{ task_form.subcategory }}
                        </div>
                        <div class="form-group">
                            <label for="{{ task_form.work_order_date.id_for_label }}" class="required text-sm font-medium text-gray-700">Date of WO:</label>
                            {{ task_form.work_order_date }}
                        </div>
                        <div></div>
                    </div>
                    
                    <!-- Project Title (Full Width) -->
                    <div class="mt-4">
                        <label for="{{ task_form.project_title.id_for_label }}" class="required text-sm font-medium text-gray-700">Project Title:</label>
                        {{ task_form.project_title }}
                    </div>
                    
                    <!-- Project Leader and Business Group -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div class="md:col-span-1">
                            <label for="{{ task_form.project_leader.id_for_label }}" class="required text-sm font-medium text-gray-700">Project Leader:</label>
                            {{ task_form.project_leader }}
                        </div>
                        <div class="md:col-span-1">
                            <label for="{{ task_form.business_group.id_for_label }}" class="required text-sm font-medium text-gray-700">Business Group:</label>
                            {{ task_form.business_group }}
                        </div>
                    </div>
                </div>

                <!-- Target Dates Table Structure (Matching Legacy Layout) -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="space-y-3">
                        <!-- Target DAP Date -->
                        <div class="flex items-center">
                            <div class="w-1/4 text-sm font-medium text-gray-700">Target DAP Date</div>
                            <div class="flex-1 flex items-center space-x-2">
                                <span class="text-sm text-gray-600">From</span>
                                {{ task_form.target_dap_from_date }}
                                <span class="text-sm text-gray-600">To</span>
                                {{ task_form.target_dap_to_date }}
                            </div>
                        </div>
                        
                        <!-- Design Finalization Date -->
                        <div class="flex items-center">
                            <div class="w-1/4 text-sm font-medium text-gray-700">Design Finalization Date</div>
                            <div class="flex-1 flex items-center space-x-2">
                                <span class="text-sm text-gray-600">From</span>
                                {{ task_form.design_finalization_from_date }}
                                <span class="text-sm text-gray-600">To</span>
                                {{ task_form.design_finalization_to_date }}
                            </div>
                        </div>
                        
                        <!-- Target Manufacturing Date -->
                        <div class="flex items-center">
                            <div class="w-1/4 text-sm font-medium text-gray-700">Target Manufg. Date</div>
                            <div class="flex-1 flex items-center space-x-2">
                                <span class="text-sm text-gray-600">From</span>
                                {{ task_form.target_manufacturing_from_date }}
                                <span class="text-sm text-gray-600">To</span>
                                {{ task_form.target_manufacturing_to_date }}
                            </div>
                        </div>
                        
                        <!-- Target Try-out Date -->
                        <div class="flex items-center">
                            <div class="w-1/4 text-sm font-medium text-gray-700">Target Try-out Date</div>
                            <div class="flex-1 flex items-center space-x-2">
                                <span class="text-sm text-gray-600">From</span>
                                {{ task_form.target_tryout_from_date }}
                                <span class="text-sm text-gray-600">To</span>
                                {{ task_form.target_tryout_to_date }}
                            </div>
                        </div>
                        
                        <!-- Target Despatch Date -->
                        <div class="flex items-center">
                            <div class="w-1/4 text-sm font-medium text-gray-700">Target Despatch Date</div>
                            <div class="flex-1 flex items-center space-x-2">
                                <span class="text-sm text-gray-600">From</span>
                                {{ task_form.target_despatch_from_date }}
                                <span class="text-sm text-gray-600">To</span>
                                {{ task_form.target_despatch_to_date }}
                            </div>
                        </div>
                        
                        <!-- Target Assembly Date -->
                        <div class="flex items-center">
                            <div class="w-1/4 text-sm font-medium text-gray-700">Target Assembly Date</div>
                            <div class="flex-1 flex items-center space-x-2">
                                <span class="text-sm text-gray-600">From</span>
                                {{ task_form.target_assembly_from_date }}
                                <span class="text-sm text-gray-600">To</span>
                                {{ task_form.target_assembly_to_date }}
                            </div>
                        </div>
                        
                        <!-- Target Installation Date -->
                        <div class="flex items-center">
                            <div class="w-1/4 text-sm font-medium text-gray-700">Target Installation Date</div>
                            <div class="flex-1 flex items-center space-x-2">
                                <span class="text-sm text-gray-600">From</span>
                                {{ task_form.target_installation_from_date }}
                                <span class="text-sm text-gray-600">To</span>
                                {{ task_form.target_installation_to_date }}
                            </div>
                        </div>
                        
                        <!-- Customer Inspection Date -->
                        <div class="flex items-center">
                            <div class="w-1/4 text-sm font-medium text-gray-700">Cust. Inspection Date</div>
                            <div class="flex-1 flex items-center space-x-2">
                                <span class="text-sm text-gray-600">From</span>
                                {{ task_form.customer_inspection_from_date }}
                                <span class="text-sm text-gray-600">To</span>
                                {{ task_form.customer_inspection_to_date }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Material Procurement Section -->
                <div class="mt-6 border-t border-gray-200 pt-4">
                    <h4 class="text-md font-semibold text-gray-800 mb-4">Material Procurement</h4>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <div class="w-1/4 text-sm font-medium text-gray-700">Manufacturing Material</div>
                            <div class="flex-1 flex items-center space-x-4">
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm text-gray-600">Date</span>
                                    {{ task_form.manufacturing_material_date }}
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm text-gray-600">Buyer</span>
                                    {{ task_form.buyer }}
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-1/4 text-sm font-medium text-gray-700">Boughtout Material</div>
                            <div class="flex-1 flex items-center space-x-2">
                                <span class="text-sm text-gray-600">Date</span>
                                {{ task_form.boughtout_material_date }}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="nextTab()" class="sap-button">Next: Shipping</button>
                </div>
            </div>

            <!-- Tab 2: Shipping -->
            <div id="tab-1" class="tab-pane">
                <!-- Main Layout: Address on left, Location details on right (Matching Legacy) -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                    <!-- Address Section (Left) -->
                    <div class="lg:col-span-1">
                        <label for="{{ shipping_form.shipping_address.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                        {{ shipping_form.shipping_address }}
                    </div>
                    
                    <!-- Location Details (Right) -->
                    <div class="lg:col-span-2 space-y-4">
                        <div class="flex items-center">
                            <div class="w-20 text-sm font-medium text-gray-700">Country</div>
                            <div class="flex-1 flex items-center">
                                <span class="mr-2">:</span>
                                {{ shipping_form.shipping_country }}
                            </div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-20 text-sm font-medium text-gray-700">State</div>
                            <div class="flex-1 flex items-center">
                                <span class="mr-2">:</span>
                                {{ shipping_form.shipping_state }}
                            </div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-20 text-sm font-medium text-gray-700">City</div>
                            <div class="flex-1 flex items-center">
                                <span class="mr-2">:</span>
                                {{ shipping_form.shipping_city }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information (Matching Legacy Table Layout) -->
                <div class="bg-gray-50 rounded-lg p-4 space-y-3">
                    <!-- Contact Person 1 Row -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                        <div class="text-sm font-medium text-gray-700">Contact Person 1</div>
                        <div class="flex items-center">
                            <span class="mr-2">:</span>
                            {{ shipping_form.contact_person_1 }}
                        </div>
                        <div class="text-sm font-medium text-gray-700">Contact No</div>
                        <div class="flex items-center">
                            <span class="mr-2">:</span>
                            {{ shipping_form.contact_no_1 }}
                        </div>
                    </div>
                    
                    <!-- Email 1 Row -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                        <div class="text-sm font-medium text-gray-700">Email</div>
                        <div class="flex items-center">
                            <span class="mr-2">:</span>
                            {{ shipping_form.email_1 }}
                        </div>
                        <div></div>
                        <div></div>
                    </div>
                    
                    <!-- Contact Person 2 Row -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                        <div class="text-sm font-medium text-gray-700">Contact Person 2</div>
                        <div class="flex items-center">
                            <span class="mr-2">:</span>
                            {{ shipping_form.contact_person_2 }}
                        </div>
                        <div class="text-sm font-medium text-gray-700">Contact No</div>
                        <div class="flex items-center">
                            <span class="mr-2">:</span>
                            {{ shipping_form.contact_no_2 }}
                        </div>
                    </div>
                    
                    <!-- Email 2 Row -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                        <div class="text-sm font-medium text-gray-700">Email</div>
                        <div class="flex items-center">
                            <span class="mr-2">:</span>
                            {{ shipping_form.email_2 }}
                        </div>
                        <div></div>
                        <div></div>
                    </div>
                    
                    <!-- Fax and ECC Row -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                        <div class="text-sm font-medium text-gray-700">Fax No</div>
                        <div class="flex items-center">
                            <span class="mr-2">:</span>
                            {{ shipping_form.fax_no }}
                        </div>
                        <div class="text-sm font-medium text-gray-700">ECC No</div>
                        <div class="flex items-center">
                            <span class="mr-2">:</span>
                            {{ shipping_form.ecc_no }}
                        </div>
                    </div>
                    
                    <!-- TIN CST and TIN VAT Row -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                        <div class="text-sm font-medium text-gray-700">TIN/CST No.</div>
                        <div class="flex items-center">
                            <span class="mr-2">:</span>
                            {{ shipping_form.tin_cst_no }}
                        </div>
                        <div class="text-sm font-medium text-gray-700">TIN/VAT No.</div>
                        <div class="flex items-center">
                            <span class="mr-2">:</span>
                            {{ shipping_form.tin_vat_no }}
                        </div>
                    </div>
                </div>

                <div class="flex justify-between pt-4">
                    <button type="button" onclick="previousTab()" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">Previous: Task Execution</button>
                    <button type="button" onclick="nextTab()" class="sap-button">Next: Products</button>
                </div>
            </div>

            <!-- Tab 3: Products -->
            <div id="tab-2" class="tab-pane">
                <!-- Legacy Layout: Left side form, Right side table -->
                <div class="grid grid-cols-1 lg:grid-cols-5 gap-6">
                    <!-- Left Side: Product Entry Form (30% width) -->
                    <div class="lg:col-span-2">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="space-y-4">
                                <div>
                                    <label for="{{ product_form.item_code.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Item Code</label>
                                    {{ product_form.item_code }}
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Desc.of Item</label>
                                    {{ product_form.description }}
                                </div>
                                
                                <div>
                                    <label for="{{ product_form.quantity.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Qty</label>
                                    {{ product_form.quantity }}
                                </div>
                                
                                <div class="flex space-x-2">
                                    <button type="button" onclick="addProduct()" class="sap-button text-sm">Submit</button>
                                    <button type="button" onclick="nextTab()" class="sap-button text-sm">Next</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Right Side: Products Grid (70% width) -->
                    <div class="lg:col-span-3">
                        {% if temp_products %}
                            <div class="overflow-x-auto">
                                <table class="min-w-full bg-white border border-gray-300">
                                    <thead class="bg-gray-100">
                                        <tr>
                                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase border border-gray-300">SN</th>
                                            <th class="px-3 py-2 text-center text-xs font-medium text-gray-700 uppercase border border-gray-300">Edit</th>
                                            <th class="px-3 py-2 text-center text-xs font-medium text-gray-700 uppercase border border-gray-300">Delete</th>
                                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase border border-gray-300">Item Code</th>
                                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase border border-gray-300">Description</th>
                                            <th class="px-3 py-2 text-right text-xs font-medium text-gray-700 uppercase border border-gray-300">Quantity</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for product in temp_products %}
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-3 py-2 text-sm text-gray-900 border border-gray-300">{{ forloop.counter }}</td>
                                            <td class="px-3 py-2 text-center border border-gray-300">
                                                <button type="button" class="text-blue-600 hover:text-blue-800 text-sm">Edit</button>
                                            </td>
                                            <td class="px-3 py-2 text-center border border-gray-300">
                                                <button type="button" onclick="removeProduct({{ product.id }})" class="text-red-600 hover:text-red-800 text-sm">Delete</button>
                                            </td>
                                            <td class="px-3 py-2 text-sm text-gray-900 border border-gray-300">{{ product.item_code }}</td>
                                            <td class="px-3 py-2 text-sm text-gray-900 border border-gray-300">{{ product.description }}</td>
                                            <td class="px-3 py-2 text-sm text-gray-900 text-right border border-gray-300">{{ product.quantity }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="bg-white border border-gray-300 rounded-lg p-8 text-center">
                                <div class="text-gray-500">
                                    <span class="text-lg font-medium text-red-600">No data to display !</span>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div class="flex justify-between pt-4">
                    <button type="button" onclick="previousTab()" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">Previous: Shipping</button>
                    <button type="button" onclick="nextTab()" class="sap-button">Next: Instructions</button>
                </div>
            </div>

            <!-- Tab 4: Instructions -->
            <div id="tab-3" class="tab-pane">
                <!-- Instructions Table Layout (Matching Legacy) -->
                <div class="bg-gray-50 rounded-lg p-4 space-y-4">
                    <!-- Checkbox Instructions -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="text-sm font-medium text-gray-700">Primer Painting to be done.</div>
                        <div class="flex items-center">
                            {{ instructions_form.primer_painting }}
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="text-sm font-medium text-gray-700">Painting to be done.</div>
                        <div class="flex items-center">
                            {{ instructions_form.painting }}
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="text-sm font-medium text-gray-700">Self Certification Report to be submitted.</div>
                        <div class="flex items-center">
                            {{ instructions_form.self_certification_report }}
                        </div>
                    </div>
                    
                    <!-- Text Input Instructions -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-start">
                        <div class="text-sm font-medium text-gray-700">others</div>
                        <div>
                            {{ instructions_form.other_instructions }}
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-start">
                        <div class="text-sm font-medium text-gray-700">Export Case Mark</div>
                        <div>
                            {{ instructions_form.export_case_mark }}
                        </div>
                    </div>
                    
                    <!-- Attach Annexure Section -->
                    <div class="border-t border-gray-300 pt-4">
                        <div class="text-sm font-medium text-gray-700 mb-2">Attach Annexure</div>
                        <div class="min-h-[100px] bg-white border border-gray-300 rounded-md p-2">
                            <!-- This would be for file upload in the actual implementation -->
                            {{ instructions_form.attach_annexure }}
                        </div>
                    </div>
                    
                    <!-- Packing Instructions Note -->
                    <div class="border-t border-gray-300 pt-4">
                        <div class="text-sm text-gray-600">
                            *Packing Instructions : Export Seaworthy / Wooden / Corrugated 7 day before desp.
                        </div>
                    </div>
                </div>

                <div class="flex justify-between pt-4">
                    <button type="button" onclick="previousTab()" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">Previous: Products</button>
                    <button type="button" onclick="submitWorkOrder()" class="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 font-semibold">Submit Work Order</button>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}