<!-- accounts/partials/bank_edit_form.html -->
<!-- HTMX partial for Bank edit form - SAP S/4HANA inspired -->
<!-- Inline/Modal edit form for bank records -->

{% load static %}

<div class="bg-sap-blue-50 border border-sap-blue-200 rounded-lg p-4 mb-4" id="bank-edit-form-{{ bank.id }}">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                <i data-lucide="edit" class="w-4 h-4 text-sap-blue-600"></i>
            </div>
            <div>
                <h4 class="text-lg font-medium text-sap-gray-800">Edit Bank</h4>
                <p class="text-sm text-sap-gray-600">Update bank information (ID: {{ bank.id }})</p>
            </div>
        </div>
        <button type="button" 
                hx-get="{% url 'accounts:bank_list' %}"
                hx-target="#bank-table"
                hx-swap="outerHTML"
                class="inline-flex items-center px-3 py-1.5 border border-sap-gray-300 rounded text-xs font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
            <i data-lucide="x" class="w-3 h-3 mr-1"></i>
            Cancel
        </button>
    </div>
    
    <form hx-put="{% url 'accounts:bank_edit' bank.id %}" 
          hx-target="#bank-edit-form-{{ bank.id }}" 
          hx-swap="outerHTML"
          hx-trigger="submit"
          class="space-y-4">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                    Bank Name <span class="text-red-500">*</span>
                </label>
                {{ form.name }}
                {% if form.name.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.name.errors.0 }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.address.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                    Address <span class="text-red-500">*</span>
                </label>
                {{ form.address }}
                {% if form.address.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.address.errors.0 }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.country.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                    Country <span class="text-red-500">*</span>
                </label>
                {{ form.country }}
                {% if form.country.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.country.errors.0 }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label for="{{ form.state.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                    State <span class="text-red-500">*</span>
                </label>
                {{ form.state }}
                {% if form.state.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.state.errors.0 }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.city.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                    City <span class="text-red-500">*</span>
                </label>
                {{ form.city }}
                {% if form.city.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.city.errors.0 }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.pin_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                    PIN Code <span class="text-red-500">*</span>
                </label>
                {{ form.pin_no }}
                {% if form.pin_no.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.pin_no.errors.0 }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label for="{{ form.contact_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                    Contact Number <span class="text-red-500">*</span>
                </label>
                {{ form.contact_no }}
                {% if form.contact_no.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.contact_no.errors.0 }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.fax_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                    Fax Number
                </label>
                {{ form.fax_no }}
                {% if form.fax_no.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.fax_no.errors.0 }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.ifsc.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                    IFSC Code <span class="text-red-500">*</span>
                </label>
                {{ form.ifsc }}
                {% if form.ifsc.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.ifsc.errors.0 }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="flex justify-end space-x-3 pt-4 border-t border-sap-blue-200">
            <button type="button" 
                    hx-get="{% url 'accounts:bank_list' %}"
                    hx-target="#bank-table"
                    hx-swap="outerHTML"
                    class="px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                Cancel
            </button>
            <button type="submit" 
                    class="px-4 py-2 bg-sap-blue-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-sap-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-blue-500">
                <i data-lucide="save" class="w-4 h-4 mr-2 inline"></i>
                Update Bank
            </button>
        </div>
    </form>
</div>