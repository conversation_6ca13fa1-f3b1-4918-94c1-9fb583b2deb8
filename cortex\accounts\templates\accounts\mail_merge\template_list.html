<!-- accounts/templates/accounts/mail_merge/template_list.html -->
<!-- Mail Merge Template Management List -->
<!-- Task Group 11: Financial Reporting & Analysis - Mail Merge Template Management (Task 11.4) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Mail Merge Templates - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-indigo-600 to-sap-indigo-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="template" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Mail Merge Templates</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage templates for automated document generation and communication</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:mail_merge_create' %}" 
                   class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="mail-merge" class="w-4 h-4 inline mr-2"></i>
                    Start Mail Merge
                </a>
                <button type="button" onclick="openTemplateModal()" 
                        class="bg-sap-indigo-600 hover:bg-sap-indigo-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    New Template
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Template Categories -->
    <div class="mb-6">
        <div class="border-b border-sap-gray-200">
            <nav class="-mb-px flex space-x-8">
                <button class="whitespace-nowrap py-2 px-1 border-b-2 border-sap-indigo-500 text-sap-indigo-600 font-medium text-sm" 
                        onclick="filterTemplates('all')">
                    All Templates
                    <span class="bg-sap-indigo-100 text-sap-indigo-600 py-0.5 px-2.5 rounded-full text-xs font-medium ml-2">12</span>
                </button>
                <button class="whitespace-nowrap py-2 px-1 border-b-2 border-transparent text-sap-gray-500 hover:text-sap-gray-700 hover:border-sap-gray-300 font-medium text-sm" 
                        onclick="filterTemplates('invoice')">
                    Invoicing
                    <span class="bg-sap-gray-100 text-sap-gray-600 py-0.5 px-2.5 rounded-full text-xs font-medium ml-2">4</span>
                </button>
                <button class="whitespace-nowrap py-2 px-1 border-b-2 border-transparent text-sap-gray-500 hover:text-sap-gray-700 hover:border-sap-gray-300 font-medium text-sm" 
                        onclick="filterTemplates('payment')">
                    Payments
                    <span class="bg-sap-gray-100 text-sap-gray-600 py-0.5 px-2.5 rounded-full text-xs font-medium ml-2">3</span>
                </button>
                <button class="whitespace-nowrap py-2 px-1 border-b-2 border-transparent text-sap-gray-500 hover:text-sap-gray-700 hover:border-sap-gray-300 font-medium text-sm" 
                        onclick="filterTemplates('communication')">
                    Communications
                    <span class="bg-sap-gray-100 text-sap-gray-600 py-0.5 px-2.5 rounded-full text-xs font-medium ml-2">5</span>
                </button>
            </nav>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        
        <!-- Invoice Templates -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                            <i data-lucide="file-text" class="w-5 h-5 text-sap-blue-600"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-sap-gray-800">Sales Invoice</h3>
                            <p class="text-sm text-sap-gray-600">Standard sales invoice format</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-1">
                        <button class="text-sap-blue-600 hover:text-sap-blue-700 p-1" title="Edit Template">
                            <i data-lucide="edit" class="w-4 h-4"></i>
                        </button>
                        <button class="text-sap-green-600 hover:text-sap-green-700 p-1" title="Use Template">
                            <i data-lucide="play" class="w-4 h-4"></i>
                        </button>
                        <button class="text-sap-red-600 hover:text-sap-red-700 p-1" title="Delete Template">
                            <i data-lucide="trash-2" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <div class="flex justify-between text-sm">
                        <span class="text-sap-gray-600">Variables:</span>
                        <span class="font-medium">8 fields</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-sap-gray-600">Last Used:</span>
                        <span class="font-medium">2 days ago</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-sap-gray-600">Usage Count:</span>
                        <span class="font-medium">245 times</span>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="bg-sap-gray-50 rounded-lg p-3">
                        <div class="text-xs font-medium text-sap-gray-700 mb-1">Preview</div>
                        <div class="text-xs text-sap-gray-600 line-clamp-3">
                            Dear {customer_name}, Thank you for your business. Please find your invoice #{invoice_no} dated {invoice_date} for amount {total_amount}...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Reminder Template -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                            <i data-lucide="alert-circle" class="w-5 h-5 text-sap-orange-600"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-sap-gray-800">Payment Reminder</h3>
                            <p class="text-sm text-sap-gray-600">Overdue payment notification</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-1">
                        <button class="text-sap-blue-600 hover:text-sap-blue-700 p-1" title="Edit Template">
                            <i data-lucide="edit" class="w-4 h-4"></i>
                        </button>
                        <button class="text-sap-green-600 hover:text-sap-green-700 p-1" title="Use Template">
                            <i data-lucide="play" class="w-4 h-4"></i>
                        </button>
                        <button class="text-sap-red-600 hover:text-sap-red-700 p-1" title="Delete Template">
                            <i data-lucide="trash-2" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <div class="flex justify-between text-sm">
                        <span class="text-sap-gray-600">Variables:</span>
                        <span class="font-medium">6 fields</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-sap-gray-600">Last Used:</span>
                        <span class="font-medium">1 week ago</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-sap-gray-600">Usage Count:</span>
                        <span class="font-medium">89 times</span>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="bg-sap-gray-50 rounded-lg p-3">
                        <div class="text-xs font-medium text-sap-gray-700 mb-1">Preview</div>
                        <div class="text-xs text-sap-gray-600 line-clamp-3">
                            Dear {customer_name}, This is a friendly reminder that your payment for invoice #{invoice_no} is now {days_overdue} days overdue...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Statement Template -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-sap-green-100 rounded-lg flex items-center justify-center">
                            <i data-lucide="file-bar-chart" class="w-5 h-5 text-sap-green-600"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-sap-gray-800">Customer Statement</h3>
                            <p class="text-sm text-sap-gray-600">Monthly account statement</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-1">
                        <button class="text-sap-blue-600 hover:text-sap-blue-700 p-1" title="Edit Template">
                            <i data-lucide="edit" class="w-4 h-4"></i>
                        </button>
                        <button class="text-sap-green-600 hover:text-sap-green-700 p-1" title="Use Template">
                            <i data-lucide="play" class="w-4 h-4"></i>
                        </button>
                        <button class="text-sap-red-600 hover:text-sap-red-700 p-1" title="Delete Template">
                            <i data-lucide="trash-2" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <div class="flex justify-between text-sm">
                        <span class="text-sap-gray-600">Variables:</span>
                        <span class="font-medium">12 fields</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-sap-gray-600">Last Used:</span>
                        <span class="font-medium">3 days ago</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-sap-gray-600">Usage Count:</span>
                        <span class="font-medium">156 times</span>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="bg-sap-gray-50 rounded-lg p-3">
                        <div class="text-xs font-medium text-sap-gray-700 mb-1">Preview</div>
                        <div class="text-xs text-sap-gray-600 line-clamp-3">
                            Dear {customer_name}, Please find attached your account statement for {statement_period}. Your current balance is {balance_amount}...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Credit Note Template -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-sap-indigo-100 rounded-lg flex items-center justify-center">
                            <i data-lucide="minus-circle" class="w-5 h-5 text-sap-indigo-600"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-sap-gray-800">Credit Note</h3>
                            <p class="text-sm text-sap-gray-600">Credit note notification</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-1">
                        <button class="text-sap-blue-600 hover:text-sap-blue-700 p-1" title="Edit Template">
                            <i data-lucide="edit" class="w-4 h-4"></i>
                        </button>
                        <button class="text-sap-green-600 hover:text-sap-green-700 p-1" title="Use Template">
                            <i data-lucide="play" class="w-4 h-4"></i>
                        </button>
                        <button class="text-sap-red-600 hover:text-sap-red-700 p-1" title="Delete Template">
                            <i data-lucide="trash-2" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <div class="flex justify-between text-sm">
                        <span class="text-sap-gray-600">Variables:</span>
                        <span class="font-medium">7 fields</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-sap-gray-600">Last Used:</span>
                        <span class="font-medium">5 days ago</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-sap-gray-600">Usage Count:</span>
                        <span class="font-medium">34 times</span>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="bg-sap-gray-50 rounded-lg p-3">
                        <div class="text-xs font-medium text-sap-gray-700 mb-1">Preview</div>
                        <div class="text-xs text-sap-gray-600 line-clamp-3">
                            Dear {customer_name}, We have issued credit note #{credit_note_no} for amount {credit_amount} due to {reason}...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Welcome Letter Template -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                            <i data-lucide="heart-handshake" class="w-5 h-5 text-sap-purple-600"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-sap-gray-800">Welcome Letter</h3>
                            <p class="text-sm text-sap-gray-600">New customer welcome</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-1">
                        <button class="text-sap-blue-600 hover:text-sap-blue-700 p-1" title="Edit Template">
                            <i data-lucide="edit" class="w-4 h-4"></i>
                        </button>
                        <button class="text-sap-green-600 hover:text-sap-green-700 p-1" title="Use Template">
                            <i data-lucide="play" class="w-4 h-4"></i>
                        </button>
                        <button class="text-sap-red-600 hover:text-sap-red-700 p-1" title="Delete Template">
                            <i data-lucide="trash-2" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <div class="flex justify-between text-sm">
                        <span class="text-sap-gray-600">Variables:</span>
                        <span class="font-medium">5 fields</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-sap-gray-600">Last Used:</span>
                        <span class="font-medium">1 day ago</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-sap-gray-600">Usage Count:</span>
                        <span class="font-medium">67 times</span>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="bg-sap-gray-50 rounded-lg p-3">
                        <div class="text-xs font-medium text-sap-gray-700 mb-1">Preview</div>
                        <div class="text-xs text-sap-gray-600 line-clamp-3">
                            Dear {customer_name}, Welcome to our family! We are delighted to have you as our valued customer. Your account has been created...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Account Summary Template -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-sap-teal-100 rounded-lg flex items-center justify-center">
                            <i data-lucide="pie-chart" class="w-5 h-5 text-sap-teal-600"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-sap-gray-800">Account Summary</h3>
                            <p class="text-sm text-sap-gray-600">Quarterly account summary</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-1">
                        <button class="text-sap-blue-600 hover:text-sap-blue-700 p-1" title="Edit Template">
                            <i data-lucide="edit" class="w-4 h-4"></i>
                        </button>
                        <button class="text-sap-green-600 hover:text-sap-green-700 p-1" title="Use Template">
                            <i data-lucide="play" class="w-4 h-4"></i>
                        </button>
                        <button class="text-sap-red-600 hover:text-sap-red-700 p-1" title="Delete Template">
                            <i data-lucide="trash-2" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <div class="flex justify-between text-sm">
                        <span class="text-sap-gray-600">Variables:</span>
                        <span class="font-medium">15 fields</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-sap-gray-600">Last Used:</span>
                        <span class="font-medium">1 month ago</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-sap-gray-600">Usage Count:</span>
                        <span class="font-medium">23 times</span>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="bg-sap-gray-50 rounded-lg p-3">
                        <div class="text-xs font-medium text-sap-gray-700 mb-1">Preview</div>
                        <div class="text-xs text-sap-gray-600 line-clamp-3">
                            Dear {customer_name}, Please find your quarterly account summary for {quarter} {year}. Total transactions: {transaction_count}...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Empty State for filtered results -->
    <div id="empty-state" class="hidden text-center py-12">
        <i data-lucide="template" class="w-16 h-16 text-sap-gray-400 mx-auto mb-4"></i>
        <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No templates found</h3>
        <p class="text-sm text-sap-gray-600 mb-6">Try adjusting your filter or create a new template.</p>
        <button type="button" onclick="openTemplateModal()" 
                class="bg-sap-indigo-600 hover:bg-sap-indigo-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
            <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
            Create First Template
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function filterTemplates(category) {
    // Implementation for filtering templates by category
    console.log('Filtering templates by:', category);
    
    // Update active tab
    document.querySelectorAll('nav button').forEach(btn => {
        btn.className = btn.className.replace('border-sap-indigo-500 text-sap-indigo-600', 'border-transparent text-sap-gray-500 hover:text-sap-gray-700 hover:border-sap-gray-300');
    });
    
    event.target.className = event.target.className.replace('border-transparent text-sap-gray-500 hover:text-sap-gray-700 hover:border-sap-gray-300', 'border-sap-indigo-500 text-sap-indigo-600');
}

function openTemplateModal() {
    // Implementation for opening new template modal
    console.log('Opening template creation modal...');
    alert('Template creation modal would open here.');
}

// Initialize lucide icons on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}