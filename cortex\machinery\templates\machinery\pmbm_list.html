{% extends "core/base.html" %}
{% load static %}

{% block title %}Preventive/Breakdown Maintenance - New{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-gradient-to-r from-green-600 to-green-800 rounded-lg shadow-lg mb-6">
            <div class="px-6 py-4">
                <h1 class="text-2xl font-bold text-white">Preventive/Breakdown Maintenance - New</h1>
                <p class="text-green-100 mt-1">Select a machine for maintenance</p>
            </div>
        </div>

        <!-- Search Form -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <form method="get" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                {% csrf_token %}
                
                <!-- Category -->
                <div>
                    <label for="{{ search_form.category.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Category
                    </label>
                    {{ search_form.category }}
                </div>

                <!-- Subcategory -->
                <div>
                    <label for="{{ search_form.subcategory.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Sub Category
                    </label>
                    {{ search_form.subcategory }}
                </div>

                <!-- Search Field -->
                <div>
                    <label for="{{ search_form.search_field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Search By
                    </label>
                    {{ search_form.search_field }}
                </div>

                <!-- Search Value -->
                <div class="md:col-span-3">
                    <label for="{{ search_form.search_value.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Search Value
                    </label>
                    <div class="flex space-x-2">
                        {{ search_form.search_value }}
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                            Search
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Machines Grid -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                SN
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Machine Code
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Date
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Description
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                UOM
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Stock Qty
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Location
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                PM In Days
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Last PM Date
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Remain Days
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for machine in machines %}
                        <tr class="hover:bg-gray-50 {% if machine.pm_status.is_overdue %}bg-pink-50{% endif %}">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ forloop.counter }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <a href="{% url 'machinery:pmbm_create' machine.id machine.itemid %}" 
                                   class="text-blue-600 hover:text-blue-900 font-medium">
                                    {{ machine.item_details.itemcode }}
                                </a>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                {{ machine.sysdate|date:"d/m/Y" }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                                {{ machine.item_details.description|truncatechars:60 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                {{ machine.item_details.unit.symbol|default:"-" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                                {{ machine.item_details.stockqty|default:"0" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ machine.location|default:"N/A" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                {{ machine.pm_status.pm_days|default:"0" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                {% if machine.pm_status.last_pm_date %}
                                    {{ machine.pm_status.last_pm_date|date:"d/m/Y" }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                                {% if machine.pm_status.is_overdue %}
                                    <span class="text-red-600 font-bold">
                                        {{ machine.pm_status.days_remaining }}
                                    </span>
                                {% else %}
                                    <span class="text-gray-500">
                                        {{ machine.pm_status.days_remaining }}
                                    </span>
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="10" class="px-6 py-12 text-center text-gray-500">
                                <div class="flex flex-col items-center">
                                    <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                                    </svg>
                                    <p class="text-lg font-medium text-gray-900 mb-1">No machines to display!</p>
                                    <p class="text-gray-500">Try adjusting your search criteria</p>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
        <div class="mt-6 flex justify-center">
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                {% if page_obj.has_previous %}
                    <a href="?page=1" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        First
                    </a>
                    <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        Previous
                    </a>
                {% endif %}

                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                </span>

                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        Next
                    </a>
                    <a href="?page={{ page_obj.paginator.num_pages }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        Last
                    </a>
                {% endif %}
            </nav>
        </div>
        {% endif %}

        <!-- Color Legend -->
        <div class="mt-6 bg-white rounded-lg shadow-lg p-4">
            <h3 class="text-lg font-medium text-gray-900 mb-2">Status Legend</h3>
            <div class="flex items-center space-x-6">
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-pink-100 border border-pink-200 rounded mr-2"></div>
                    <span class="text-sm text-gray-600">Overdue for Maintenance</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-white border border-gray-200 rounded mr-2"></div>
                    <span class="text-sm text-gray-600">Normal</span>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="{% static 'js/htmx.min.js' %}"></script>
{% endblock %}