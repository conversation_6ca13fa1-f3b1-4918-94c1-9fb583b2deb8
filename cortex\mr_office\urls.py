from django.urls import path
from . import views

app_name = 'mr_office'

urlpatterns = [
    # Main document management URLs
    path('', views.MROfficeListView.as_view(), name='document_list'),
    path('documents/', views.MROfficeListView.as_view(), name='document_list'),
    path('documents/new/', views.MROfficeCreateView.as_view(), name='document_create'),
    path('documents/<int:pk>/', views.MROfficeDetailView.as_view(), name='document_detail'),
    path('documents/<int:pk>/delete/', views.MROfficeDeleteView.as_view(), name='document_delete'),
    
    # File operations
    path('documents/<int:pk>/download/', views.download_document, name='document_download'),
    
    # HTMX endpoints
    path('api/search-documents/', views.search_documents, name='search_documents'),
    path('api/document-stats/', views.document_stats, name='document_stats'),
]