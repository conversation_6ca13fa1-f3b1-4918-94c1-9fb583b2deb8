﻿using System;
using System.Collections;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Xml.Linq;
using System.Data.SqlClient;
using CrystalDecisions.CrystalReports.Engine;
using CrystalDecisions.ReportAppServer.ClientDoc;

public partial class Admin_Access_AcessReport : System.Web.UI.Page
{
    clsFunctions fun = new clsFunctions();
    string CompId = "";
    string FyId = "";
    ReportDocument crystalRpt = new ReportDocument();
  
    string Key = string.Empty;

    protected void Page_Init(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {

            string connStr = fun.Connection();
            SqlConnection con = new SqlConnection(connStr);
            DataSet Access = new DataSet();
            DataTable dt = new DataTable();
            try
            {
                con.Open();
                CompId = Request.QueryString["cid"].ToString();
                FyId = Request.QueryString["fyid"].ToString();
                Key = Request.QueryString["Key"].ToString();             

                string sql = fun.select("Id,SysDate,SysTime ,SessionId ,CompId , FinYearId ,EmpId , ModId , SubModId , AccessType , Access ", "tblAccess_Master", "CompId='" + CompId + "' AND FinYearId<='" + FyId + "' AND EmpId!='Sapl0001'");


                SqlCommand Cmditem = new SqlCommand(sql, con);
                SqlDataAdapter DAitem = new SqlDataAdapter(Cmditem);
                DataSet DSitem = new DataSet();
                DAitem.Fill(DSitem);
               
                dt.Columns.Add(new System.Data.DataColumn("Id", typeof(int)));
                dt.Columns.Add(new System.Data.DataColumn("CompId", typeof(int)));
                dt.Columns.Add(new System.Data.DataColumn("FinYear", typeof(string)));
                dt.Columns.Add(new System.Data.DataColumn("EmployeeName", typeof(string)));
                dt.Columns.Add(new System.Data.DataColumn("Department", typeof(string)));
                dt.Columns.Add(new System.Data.DataColumn("Desination", typeof(string)));
                dt.Columns.Add(new System.Data.DataColumn("ContactNo", typeof(string)));
                dt.Columns.Add(new System.Data.DataColumn("CompanyEmail", typeof(string)));
                dt.Columns.Add(new System.Data.DataColumn("ExtNo", typeof(string)));
                dt.Columns.Add(new System.Data.DataColumn("ModName", typeof(string)));
                dt.Columns.Add(new System.Data.DataColumn("SubModName", typeof(string)));
                dt.Columns.Add(new System.Data.DataColumn("AccessType", typeof(string)));
                dt.Columns.Add(new System.Data.DataColumn("Access", typeof(string)));
                dt.Columns.Add(new System.Data.DataColumn("Type", typeof(string)));
                DataRow dr;
                dr = dt.NewRow();

                for (int i = 0; i < DSitem.Tables[0].Rows.Count; i++)
                {
                    dr = dt.NewRow();
                   
                    string sqlEmp = fun.select("EmployeeName,Department,Designation,ContactNo,CompanyEmail,ExtensionNo", "tblHR_OfficeStaff", "EmpId='" + DSitem.Tables[0].Rows[i]["EmpId"].ToString() + "' and ResignationDate=''");

                    SqlCommand cmdemp = new SqlCommand(sqlEmp, con);
                    SqlDataAdapter daemp = new SqlDataAdapter(cmdemp);
                    DataSet dsemp = new DataSet();
                    daemp.Fill(dsemp);
                    if (dsemp.Tables[0].Rows.Count > 0)
                    {

                        SqlCommand cmddept = new SqlCommand(fun.select("Description As Department ", "tblHR_Departments", "Id='" + dsemp.Tables[0].Rows[0]["Department"].ToString() + "'"), con);
                        SqlDataAdapter dadept = new SqlDataAdapter(cmddept);
                        DataSet dsdept = new DataSet();
                        dadept.Fill(dsdept);
                        if (dsdept.Tables[0].Rows.Count > 0)
                        {
                            dr[4] = dsdept.Tables[0].Rows[0]["Department"].ToString();
                        }

                        SqlCommand cmddesn = new SqlCommand(fun.select("Type As Desination", "tblHR_Designation", "Id='" + dsemp.Tables[0].Rows[0]["Designation"].ToString() + "'"), con);
                        SqlDataAdapter dadesn = new SqlDataAdapter(cmddesn);
                        DataSet dsdesn = new DataSet();
                        dadesn.Fill(dsdesn);

                        if (dsdesn.Tables[0].Rows.Count > 0)
                        {
                            dr[5] = dsdesn.Tables[0].Rows[0]["Desination"].ToString();
                        }

                        SqlCommand cmdext = new SqlCommand(fun.select("ExtNo", "tblHR_IntercomExt", "Id='" + dsemp.Tables[0].Rows[0]["ExtensionNo"].ToString() + "'"), con);
                        SqlDataAdapter daext = new SqlDataAdapter(cmdext);
                        DataSet dsext = new DataSet();
                        daext.Fill(dsext);

                        if (dsext.Tables[0].Rows.Count > 0)
                        {
                            dr[8] = dsext.Tables[0].Rows[0]["ExtNo"].ToString();
                        }

                        SqlCommand cmdMod = new SqlCommand(fun.select("ModName", "tblModule_Master", "ModId='" + DSitem.Tables[0].Rows[i]["ModId"].ToString() + "'"), con);
                        SqlDataAdapter daMod = new SqlDataAdapter(cmdMod);
                        DataSet dsMod = new DataSet();
                        daMod.Fill(dsMod);

                        if (dsMod.Tables[0].Rows.Count > 0)
                        {
                            dr[9] = dsMod.Tables[0].Rows[0]["ModName"].ToString();
                        }

                        SqlCommand cmdSMod = new SqlCommand(fun.select("SubModName,MTR as Type", "tblSubModule_Master", "SubModId='" + DSitem.Tables[0].Rows[i]["SubModId"].ToString() + "'"), con);
                        SqlDataAdapter daSMod = new SqlDataAdapter(cmdSMod);
                        DataSet dsSMod = new DataSet();
                        daSMod.Fill(dsSMod);

                        if (dsSMod.Tables[0].Rows.Count > 0)
                        {
                            dr[10] = dsSMod.Tables[0].Rows[0]["SubModName"].ToString();
                        }

                        SqlCommand sqlYear = new SqlCommand(fun.select("FinYear", "tblFinancial_master", "FinYearId='" + DSitem.Tables[0].Rows[i]["FinYearId"].ToString() + "'"), con);

                        SqlDataAdapter daYear = new SqlDataAdapter(sqlYear);
                        DataSet DsYear = new DataSet();
                        daYear.Fill(DsYear);

                        dr[0] = Convert.ToInt32(DSitem.Tables[0].Rows[i]["Id"]);
                        dr[1] = Convert.ToInt32(DSitem.Tables[0].Rows[i]["CompId"]);
                        dr[2] = DsYear.Tables[0].Rows[0]["FinYear"].ToString();
                        dr[3] = dsemp.Tables[0].Rows[0]["EmployeeName"].ToString();
                        dr[6] = dsemp.Tables[0].Rows[0]["ContactNo"].ToString();
                        dr[7] = dsemp.Tables[0].Rows[0]["CompanyEmail"].ToString();

                        if (DSitem.Tables[0].Rows.Count > 0)
                        {
                            string tp1 = DSitem.Tables[0].Rows[i]["AccessType"].ToString();
                            string type1 = "";

                            if (tp1 == "1")
                            {
                                type1 = "New";
                            }
                            else if (tp1 == "2")
                            {
                                type1 = "Edit";
                            }
                            else if (tp1 == "3")
                            {
                                type1 = "Delete";
                            }
                            else if (tp1 == "4")
                            {
                                type1 = "Print";
                            }

                            dr[11] = type1;
                        }

                        if (DSitem.Tables[0].Rows.Count > 0)
                        {
                            string tp2 = DSitem.Tables[0].Rows[i]["Access"].ToString();
                            string type2 = "";

                            if (tp2 == "1")
                            {
                                type2 = "Yes";
                            }

                            else if (tp2 == "2")
                            {
                                type2 = "Yes";
                            }
                            else if (tp2 == "3")
                            {
                                type2 = "Yes";
                            }
                            else if (tp2 == "4")
                            {
                                type2 = "Yes";
                            }
                            else if (tp2 == "0")
                            {
                                type2 = "No";
                            }
                            dr[12] = type2;
                        }

                        if (dsSMod.Tables[0].Rows.Count > 0)
                        {
                            string tp = dsSMod.Tables[0].Rows[0]["Type"].ToString();
                            string type = "";

                            if (tp == "1")
                            {
                                type = "Master";

                            }
                            else if (tp == "2")
                            {
                                type = "Transaction";

                            }
                            else if (tp == "3")
                            {
                                type = "Report";
                            }

                            dr[13] = type;
                        }
                        dt.Rows.Add(dr);
                        dt.AcceptChanges();
                    }
                }
                DataView dataview = dt.DefaultView;
                dataview.Sort = "EmployeeName ASC";
                DataTable td = dataview.ToTable();
                Access.Tables.Add(td);
                Access.AcceptChanges();

                DataSet xsd = new AdminAccess();
                xsd.Tables[0].Merge(Access.Tables[0]);
                xsd.AcceptChanges();
                string reportPath = Server.MapPath("~/Admin/Access/AccessReports.rpt");
                crystalRpt.Load(reportPath);
                crystalRpt.SetDataSource(xsd);
                string Add = fun.CompAdd(Convert.ToInt32(CompId));
                crystalRpt.SetParameterValue("CompAdd", Add);
                CrystalReportViewer1.ReportSource = crystalRpt;
                Session[Key] = crystalRpt;
            }
            catch (Exception ex) { }
            finally
            {
                Access.Clear();
                Access.Dispose();
                dt.Clear();
                dt.Dispose();
                con.Close();
                con.Dispose();

            }


        }
        else
        {
            Key = Request.QueryString["Key"].ToString();
            ReportDocument doc = (ReportDocument)Session[Key];
            CrystalReportViewer1.ReportSource = doc;
        }
    }
       

    protected void Page_Load(object sender, EventArgs e)
    {
        crystalRpt = new ReportDocument();
    }
    protected void btnCancel_Click(object sender, EventArgs e)
    {
        Response.Redirect("AccessModule.aspx?");
    }


    protected void Page_UnLoad(object sender, EventArgs e)
    {
        this.CrystalReportViewer1.Dispose();
        this.CrystalReportViewer1 = null;
        crystalRpt.Close();
        crystalRpt.Dispose();
        GC.Collect();
    }


}