# accounts/tax_calculation_engine.py
# Comprehensive Tax Calculation Engine for Accounts module
# Task Group 4: Taxation Management - Tax Calculation Engines

from decimal import Decimal, ROUND_HALF_UP
from typing import Dict, List, Optional, Union
from dataclasses import dataclass
from enum import Enum

from .models import ExciseDuty


class TaxType(Enum):
    """Enumeration for different tax types"""
    EXCISE = "excise"
    SERVICE_TAX = "service_tax"
    VAT = "vat"
    CST = "cst"
    TDS = "tds"
    OCTORI = "octori"
    CESS = "cess"


class CalculationMethod(Enum):
    """Enumeration for calculation methods"""
    INCLUSIVE = "inclusive"  # Tax included in amount
    EXCLUSIVE = "exclusive"  # Tax added to amount
    REVERSE = "reverse"      # Calculate base from total


@dataclass
class TaxComponent:
    """Represents a single tax component"""
    tax_type: TaxType
    name: str
    rate: Decimal
    amount: Decimal
    base_amount: Decimal
    is_compound: bool = False  # True if calculated on other taxes
    calculation_base: Optional[str] = None  # What this tax is calculated on


@dataclass
class TaxCalculationResult:
    """Result of tax calculation"""
    base_amount: Decimal
    tax_components: List[TaxComponent]
    total_tax_amount: Decimal
    total_amount: Decimal
    effective_tax_rate: Decimal
    calculation_method: CalculationMethod
    calculation_details: Dict


class TaxCalculationEngine:
    """
    Comprehensive tax calculation engine for all tax types
    Supports Indian tax system including Excise, Service Tax, VAT, CST, TDS, etc.
    """
    
    def __init__(self):
        self.precision = Decimal('0.01')  # Round to 2 decimal places
        self.tax_precision = Decimal('0.001')  # Tax rates to 3 decimal places
    
    def calculate_excise_duty(
        self, 
        base_amount: Union[Decimal, float], 
        excise_duty: ExciseDuty,
        calculation_method: CalculationMethod = CalculationMethod.EXCLUSIVE
    ) -> TaxCalculationResult:
        """Calculate excise duty and related cess"""
        
        base_amount = Decimal(str(base_amount))
        tax_components = []
        
        if calculation_method == CalculationMethod.INCLUSIVE:
            # Reverse calculation - extract base from inclusive amount
            total_rate = self._get_excise_total_rate(excise_duty)
            actual_base = (base_amount * 100) / (100 + total_rate)
            actual_base = actual_base.quantize(self.precision, rounding=ROUND_HALF_UP)
        else:
            actual_base = base_amount
        
        # Calculate accessible amount
        accessible_amount = actual_base
        if excise_duty.accessible_value and excise_duty.accessible_value > 0:
            accessible_amount = (actual_base * excise_duty.accessible_value) / 100
            accessible_amount = accessible_amount.quantize(self.precision, rounding=ROUND_HALF_UP)
        
        # Basic excise duty
        basic_excise_amount = Decimal('0')
        if excise_duty.value and excise_duty.value > 0:
            basic_excise_amount = (accessible_amount * excise_duty.value) / 100
            basic_excise_amount = basic_excise_amount.quantize(self.precision, rounding=ROUND_HALF_UP)
            
            tax_components.append(TaxComponent(
                tax_type=TaxType.EXCISE,
                name=f"Basic Excise ({excise_duty.value}%)",
                rate=excise_duty.value,
                amount=basic_excise_amount,
                base_amount=accessible_amount
            ))
        
        # Education Cess (calculated on basic excise)
        edu_cess_amount = Decimal('0')
        if excise_duty.edu_cess and excise_duty.edu_cess > 0 and basic_excise_amount > 0:
            edu_cess_amount = (basic_excise_amount * excise_duty.edu_cess) / 100
            edu_cess_amount = edu_cess_amount.quantize(self.precision, rounding=ROUND_HALF_UP)
            
            tax_components.append(TaxComponent(
                tax_type=TaxType.CESS,
                name=f"Education Cess ({excise_duty.edu_cess}%)",
                rate=excise_duty.edu_cess,
                amount=edu_cess_amount,
                base_amount=basic_excise_amount,
                is_compound=True,
                calculation_base="Basic Excise"
            ))
        
        # SHE Cess (calculated on basic excise)
        she_cess_amount = Decimal('0')
        if excise_duty.she_cess and excise_duty.she_cess > 0 and basic_excise_amount > 0:
            she_cess_amount = (basic_excise_amount * excise_duty.she_cess) / 100
            she_cess_amount = she_cess_amount.quantize(self.precision, rounding=ROUND_HALF_UP)
            
            tax_components.append(TaxComponent(
                tax_type=TaxType.CESS,
                name=f"SHE Cess ({excise_duty.she_cess}%)",
                rate=excise_duty.she_cess,
                amount=she_cess_amount,
                base_amount=basic_excise_amount,
                is_compound=True,
                calculation_base="Basic Excise"
            ))
        
        total_tax_amount = basic_excise_amount + edu_cess_amount + she_cess_amount
        total_amount = actual_base + total_tax_amount
        
        effective_rate = Decimal('0')
        if actual_base > 0:
            effective_rate = (total_tax_amount / actual_base) * 100
            effective_rate = effective_rate.quantize(self.tax_precision, rounding=ROUND_HALF_UP)
        
        calculation_details = {
            'original_amount': float(base_amount),
            'accessible_amount': float(accessible_amount),
            'accessible_percentage': float(excise_duty.accessible_value or 100),
            'basic_excise_rate': float(excise_duty.value or 0),
            'edu_cess_rate': float(excise_duty.edu_cess or 0),
            'she_cess_rate': float(excise_duty.she_cess or 0),
            'calculation_method': calculation_method.value
        }
        
        return TaxCalculationResult(
            base_amount=actual_base,
            tax_components=tax_components,
            total_tax_amount=total_tax_amount,
            total_amount=total_amount,
            effective_tax_rate=effective_rate,
            calculation_method=calculation_method,
            calculation_details=calculation_details
        )
    
    def calculate_vat(
        self, 
        base_amount: Union[Decimal, float], 
        vat_rate: Union[Decimal, float],
        calculation_method: CalculationMethod = CalculationMethod.EXCLUSIVE
    ) -> TaxCalculationResult:
        """Calculate VAT"""
        
        base_amount = Decimal(str(base_amount))
        vat_rate = Decimal(str(vat_rate))
        tax_components = []
        
        if calculation_method == CalculationMethod.INCLUSIVE:
            # Reverse calculation
            actual_base = (base_amount * 100) / (100 + vat_rate)
            actual_base = actual_base.quantize(self.precision, rounding=ROUND_HALF_UP)
        else:
            actual_base = base_amount
        
        vat_amount = Decimal('0')
        if vat_rate > 0:
            vat_amount = (actual_base * vat_rate) / 100
            vat_amount = vat_amount.quantize(self.precision, rounding=ROUND_HALF_UP)
            
            tax_components.append(TaxComponent(
                tax_type=TaxType.VAT,
                name=f"VAT ({vat_rate}%)",
                rate=vat_rate,
                amount=vat_amount,
                base_amount=actual_base
            ))
        
        total_amount = actual_base + vat_amount
        effective_rate = (vat_amount / actual_base) * 100 if actual_base > 0 else Decimal('0')
        
        calculation_details = {
            'original_amount': float(base_amount),
            'vat_rate': float(vat_rate),
            'calculation_method': calculation_method.value
        }
        
        return TaxCalculationResult(
            base_amount=actual_base,
            tax_components=tax_components,
            total_tax_amount=vat_amount,
            total_amount=total_amount,
            effective_tax_rate=effective_rate,
            calculation_method=calculation_method,
            calculation_details=calculation_details
        )
    
    def calculate_tds(
        self, 
        base_amount: Union[Decimal, float], 
        tds_rate: Union[Decimal, float],
        tds_threshold: Optional[Union[Decimal, float]] = None
    ) -> TaxCalculationResult:
        """Calculate TDS (Tax Deducted at Source)"""
        
        base_amount = Decimal(str(base_amount))
        tds_rate = Decimal(str(tds_rate))
        tax_components = []
        
        # Check threshold if provided
        tds_amount = Decimal('0')
        if tds_threshold is None or base_amount >= Decimal(str(tds_threshold)):
            if tds_rate > 0:
                tds_amount = (base_amount * tds_rate) / 100
                tds_amount = tds_amount.quantize(self.precision, rounding=ROUND_HALF_UP)
                
                tax_components.append(TaxComponent(
                    tax_type=TaxType.TDS,
                    name=f"TDS ({tds_rate}%)",
                    rate=tds_rate,
                    amount=tds_amount,
                    base_amount=base_amount
                ))
        
        net_amount = base_amount - tds_amount  # TDS is deducted
        effective_rate = (tds_amount / base_amount) * 100 if base_amount > 0 else Decimal('0')
        
        calculation_details = {
            'gross_amount': float(base_amount),
            'tds_rate': float(tds_rate),
            'tds_threshold': float(tds_threshold) if tds_threshold else None,
            'threshold_applicable': tds_threshold is not None and base_amount >= Decimal(str(tds_threshold))
        }
        
        return TaxCalculationResult(
            base_amount=base_amount,
            tax_components=tax_components,
            total_tax_amount=tds_amount,
            total_amount=net_amount,  # Net amount after TDS deduction
            effective_tax_rate=effective_rate,
            calculation_method=CalculationMethod.EXCLUSIVE,
            calculation_details=calculation_details
        )
    
    def calculate_composite_tax(
        self,
        base_amount: Union[Decimal, float],
        tax_config: Dict,
        calculation_method: CalculationMethod = CalculationMethod.EXCLUSIVE
    ) -> TaxCalculationResult:
        """
        Calculate composite tax (multiple tax types)
        
        tax_config format:
        {
            'excise': {'rate': 12.0, 'edu_cess': 2.0, 'she_cess': 1.0},
            'vat': {'rate': 5.0},
            'tds': {'rate': 10.0, 'threshold': 5000}
        }
        """
        
        base_amount = Decimal(str(base_amount))
        all_tax_components = []
        running_amount = base_amount
        total_tax_amount = Decimal('0')
        
        calculation_details = {
            'original_amount': float(base_amount),
            'calculation_method': calculation_method.value,
            'tax_sequence': []
        }
        
        # Process taxes in sequence: Excise -> VAT -> TDS
        
        # 1. Excise Duty (if specified)
        if 'excise' in tax_config:
            excise_config = tax_config['excise']
            
            # Create temporary ExciseDuty object
            excise_duty = type('ExciseDuty', (), {
                'value': Decimal(str(excise_config.get('rate', 0))),
                'accessible_value': Decimal(str(excise_config.get('accessible_value', 100))),
                'edu_cess': Decimal(str(excise_config.get('edu_cess', 0))),
                'she_cess': Decimal(str(excise_config.get('she_cess', 0)))
            })()
            
            excise_result = self.calculate_excise_duty(running_amount, excise_duty, calculation_method)
            all_tax_components.extend(excise_result.tax_components)
            
            if calculation_method == CalculationMethod.INCLUSIVE:
                running_amount = excise_result.base_amount
            else:
                running_amount = excise_result.total_amount
            
            calculation_details['tax_sequence'].append({
                'tax_type': 'excise',
                'base_amount': float(excise_result.base_amount),
                'tax_amount': float(excise_result.total_tax_amount),
                'running_total': float(running_amount)
            })
        
        # 2. VAT (calculated on amount including excise)
        if 'vat' in tax_config:
            vat_config = tax_config['vat']
            vat_rate = Decimal(str(vat_config.get('rate', 0)))
            
            # VAT is calculated on the amount including excise
            vat_base = running_amount if calculation_method != CalculationMethod.INCLUSIVE else base_amount
            vat_result = self.calculate_vat(vat_base, vat_rate, CalculationMethod.EXCLUSIVE)
            
            all_tax_components.extend(vat_result.tax_components)
            running_amount += vat_result.total_tax_amount
            
            calculation_details['tax_sequence'].append({
                'tax_type': 'vat',
                'base_amount': float(vat_base),
                'tax_amount': float(vat_result.total_tax_amount),
                'running_total': float(running_amount)
            })
        
        # 3. TDS (calculated on gross amount before other taxes)
        if 'tds' in tax_config:
            tds_config = tax_config['tds']
            tds_rate = Decimal(str(tds_config.get('rate', 0)))
            tds_threshold = tds_config.get('threshold')
            
            tds_result = self.calculate_tds(base_amount, tds_rate, tds_threshold)
            all_tax_components.extend(tds_result.tax_components)
            
            calculation_details['tax_sequence'].append({
                'tax_type': 'tds',
                'base_amount': float(base_amount),
                'tax_amount': float(tds_result.total_tax_amount),
                'net_amount': float(tds_result.total_amount)
            })
        
        # Calculate totals
        total_tax_amount = sum(component.amount for component in all_tax_components)
        
        # For TDS, the final amount is net amount (after deduction)
        tds_amount = sum(comp.amount for comp in all_tax_components if comp.tax_type == TaxType.TDS)
        final_amount = running_amount - tds_amount
        
        effective_rate = Decimal('0')
        if base_amount > 0:
            effective_rate = (total_tax_amount / base_amount) * 100
            effective_rate = effective_rate.quantize(self.tax_precision, rounding=ROUND_HALF_UP)
        
        return TaxCalculationResult(
            base_amount=base_amount,
            tax_components=all_tax_components,
            total_tax_amount=total_tax_amount,
            total_amount=final_amount,
            effective_tax_rate=effective_rate,
            calculation_method=calculation_method,
            calculation_details=calculation_details
        )
    
    def _get_excise_total_rate(self, excise_duty: ExciseDuty) -> Decimal:
        """Calculate total excise rate including all cess"""
        base_rate = excise_duty.value or Decimal('0')
        edu_rate = excise_duty.edu_cess or Decimal('0')
        she_rate = excise_duty.she_cess or Decimal('0')
        
        # Cess is calculated on basic excise, so total rate is not simply additive
        # For inclusive calculations, we need to solve: base + base*rate + (base*rate)*cess_rate = total
        total_cess_rate = edu_rate + she_rate
        effective_rate = base_rate * (1 + total_cess_rate / 100)
        
        return effective_rate
    
    def reverse_calculate_base_amount(
        self,
        total_amount: Union[Decimal, float],
        tax_config: Dict
    ) -> TaxCalculationResult:
        """
        Reverse calculate base amount from total amount including all taxes
        """
        total_amount = Decimal(str(total_amount))
        
        # Use iterative method to find base amount
        # Start with estimate and refine
        estimated_base = total_amount * Decimal('0.8')  # Initial estimate
        
        for iteration in range(10):  # Maximum 10 iterations
            result = self.calculate_composite_tax(estimated_base, tax_config, CalculationMethod.EXCLUSIVE)
            
            difference = total_amount - result.total_amount
            if abs(difference) < Decimal('0.01'):  # Within 1 cent
                break
            
            # Adjust estimate
            adjustment_factor = total_amount / result.total_amount
            estimated_base = estimated_base * adjustment_factor
        
        # Recalculate with final base amount
        final_result = self.calculate_composite_tax(estimated_base, tax_config, CalculationMethod.EXCLUSIVE)
        final_result.calculation_details['reverse_calculation'] = True
        final_result.calculation_details['iterations'] = iteration + 1
        final_result.calculation_details['target_amount'] = float(total_amount)
        
        return final_result
    
    def get_tax_breakdown_summary(self, result: TaxCalculationResult) -> Dict:
        """Get a summary breakdown of tax calculation"""
        
        summary = {
            'base_amount': float(result.base_amount),
            'total_tax_amount': float(result.total_tax_amount),
            'total_amount': float(result.total_amount),
            'effective_tax_rate': float(result.effective_tax_rate),
            'calculation_method': result.calculation_method.value,
            'tax_components': []
        }
        
        # Group by tax type
        tax_groups = {}
        for component in result.tax_components:
            tax_type = component.tax_type.value
            if tax_type not in tax_groups:
                tax_groups[tax_type] = {
                    'type': tax_type,
                    'components': [],
                    'total_amount': Decimal('0'),
                    'total_rate': Decimal('0')
                }
            
            tax_groups[tax_type]['components'].append({
                'name': component.name,
                'rate': float(component.rate),
                'amount': float(component.amount),
                'base_amount': float(component.base_amount),
                'is_compound': component.is_compound,
                'calculation_base': component.calculation_base
            })
            tax_groups[tax_type]['total_amount'] += component.amount
        
        # Convert to list and add totals
        for group in tax_groups.values():
            group['total_amount'] = float(group['total_amount'])
            summary['tax_components'].append(group)
        
        return summary
    
    def validate_tax_rates(self, tax_config: Dict) -> List[str]:
        """Validate tax configuration and return list of errors"""
        errors = []
        
        if 'excise' in tax_config:
            excise = tax_config['excise']
            rate = excise.get('rate', 0)
            if not 0 <= rate <= 100:
                errors.append(f"Excise rate {rate}% must be between 0% and 100%")
            
            edu_cess = excise.get('edu_cess', 0)
            if not 0 <= edu_cess <= 100:
                errors.append(f"Education cess {edu_cess}% must be between 0% and 100%")
            
            she_cess = excise.get('she_cess', 0)
            if not 0 <= she_cess <= 100:
                errors.append(f"SHE cess {she_cess}% must be between 0% and 100%")
        
        if 'vat' in tax_config:
            vat_rate = tax_config['vat'].get('rate', 0)
            if not 0 <= vat_rate <= 100:
                errors.append(f"VAT rate {vat_rate}% must be between 0% and 100%")
        
        if 'tds' in tax_config:
            tds_rate = tax_config['tds'].get('rate', 0)
            if not 0 <= tds_rate <= 100:
                errors.append(f"TDS rate {tds_rate}% must be between 0% and 100%")
            
            threshold = tax_config['tds'].get('threshold')
            if threshold is not None and threshold < 0:
                errors.append(f"TDS threshold {threshold} must be non-negative")
        
        return errors


# Convenience functions for common tax calculations

def calculate_invoice_taxes(
    base_amount: Union[Decimal, float],
    excise_duty_id: Optional[int] = None,
    vat_rate: Optional[Union[Decimal, float]] = None,
    tds_rate: Optional[Union[Decimal, float]] = None,
    calculation_method: CalculationMethod = CalculationMethod.EXCLUSIVE
) -> TaxCalculationResult:
    """
    Calculate taxes for invoice with common tax types
    """
    engine = TaxCalculationEngine()
    tax_config = {}
    
    # Add excise if specified
    if excise_duty_id:
        try:
            excise_duty = ExciseDuty.objects.get(id=excise_duty_id)
            tax_config['excise'] = {
                'rate': float(excise_duty.value or 0),
                'accessible_value': float(excise_duty.accessible_value or 100),
                'edu_cess': float(excise_duty.edu_cess or 0),
                'she_cess': float(excise_duty.she_cess or 0)
            }
        except ExciseDuty.DoesNotExist:
            pass
    
    # Add VAT if specified
    if vat_rate is not None:
        tax_config['vat'] = {'rate': float(vat_rate)}
    
    # Add TDS if specified
    if tds_rate is not None:
        tax_config['tds'] = {'rate': float(tds_rate)}
    
    return engine.calculate_composite_tax(base_amount, tax_config, calculation_method)


def get_default_tax_rates() -> Dict:
    """Get default tax rates from database"""
    defaults = {}
    
    # Get default excise duty
    default_excise = ExciseDuty.objects.filter(is_default_excise=True).first()
    if default_excise:
        defaults['default_excise'] = {
            'id': default_excise.id,
            'rate': float(default_excise.value or 0),
            'accessible_value': float(default_excise.accessible_value or 100),
            'edu_cess': float(default_excise.edu_cess or 0),
            'she_cess': float(default_excise.she_cess or 0)
        }
    
    # Get default service tax
    default_service_tax = ExciseDuty.objects.filter(is_default_service_tax=True).first()
    if default_service_tax:
        defaults['default_service_tax'] = {
            'id': default_service_tax.id,
            'rate': float(default_service_tax.value or 0),
            'accessible_value': float(default_service_tax.accessible_value or 100),
            'edu_cess': float(default_service_tax.edu_cess or 0),
            'she_cess': float(default_service_tax.she_cess or 0)
        }
    
    return defaults