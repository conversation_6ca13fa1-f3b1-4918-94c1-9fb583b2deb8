<!-- accounts/templates/accounts/expenses/dashboard.html -->
<!-- Tour/Expense Management Dashboard -->
<!-- Task Group 9: Tour/Expense Management Dashboard (Task 9.1) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Tour & Expense Management - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-amber-600 to-sap-amber-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="map" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Tour & Expense Management</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage employee tours, travel expenses, and reimbursements</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to Accounts
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Tour & Expense Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Active Tours -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="plane" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Active Tours</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ active_tours_count|default:0 }}</p>
                    <p class="text-xs text-sap-blue-600 mt-1">Currently ongoing</p>
                </div>
            </div>
        </div>
        
        <!-- Pending Expenses -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="receipt" class="w-6 h-6 text-sap-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Pending Expenses</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ pending_expenses_count|default:0 }}</p>
                    <p class="text-xs text-sap-orange-600 mt-1">Awaiting approval</p>
                </div>
            </div>
        </div>
        
        <!-- Monthly Spend -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="dollar-sign" class="w-6 h-6 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Monthly Spend</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ monthly_spend|default:0|floatformat:2 }}</p>
                    <p class="text-xs text-sap-green-600 mt-1">Current month</p>
                </div>
            </div>
        </div>
        
        <!-- Overdue Settlements -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-red-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="alert-triangle" class="w-6 h-6 text-sap-red-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Overdue Settlements</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ overdue_settlements_count|default:0 }}</p>
                    <p class="text-xs text-sap-red-600 mt-1">Requires attention</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tour Management Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        
        <!-- Tour Voucher Management -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="plane" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                    Tour Voucher Management
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:tour_voucher_create' %}" 
                       class="flex items-center w-full bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Create Tour Voucher
                    </a>
                    <a href="{% url 'accounts:tour_voucher_list' %}" 
                       class="flex items-center w-full bg-sap-blue-100 hover:bg-sap-blue-200 text-sap-blue-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                        View All Tours
                    </a>
                    <button type="button" onclick="generateTourReport()" 
                            class="flex items-center w-full bg-sap-gray-100 hover:bg-sap-gray-200 text-sap-gray-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="file-text" class="w-4 h-4 mr-2"></i>
                        Tour Summary Report
                    </button>
                </div>
            </div>
        </div>

        <!-- Expense Management -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="receipt" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                    Expense Management
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:tour_expense_create' %}" 
                       class="flex items-center w-full bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Add Tour Expense
                    </a>
                    <a href="{% url 'accounts:tour_expense_list' %}" 
                       class="flex items-center w-full bg-sap-orange-100 hover:bg-sap-orange-200 text-sap-orange-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                        View All Expenses
                    </a>
                    <button type="button" onclick="generateExpenseReport()" 
                            class="flex items-center w-full bg-sap-gray-100 hover:bg-sap-gray-200 text-sap-gray-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="bar-chart" class="w-4 h-4 mr-2"></i>
                        Expense Analysis
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Master Configuration Section -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        
        <!-- Tour Expense Types -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="tag" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                    Expense Types
                </h3>
                <p class="text-sm text-sap-gray-600 mt-1">{{ expense_types_count|default:0 }} configured</p>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:tour_expense_type_create' %}" 
                       class="flex items-center w-full bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Add Expense Type
                    </a>
                    <a href="{% url 'accounts:tour_expense_type_list' %}" 
                       class="flex items-center w-full bg-sap-purple-100 hover:bg-sap-purple-200 text-sap-purple-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                        Manage Types
                    </a>
                </div>
            </div>
        </div>

        <!-- Tour Categories -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="folder" class="w-5 h-5 mr-2 text-sap-teal-600"></i>
                    Tour Categories
                </h3>
                <p class="text-sm text-sap-gray-600 mt-1">{{ tour_categories_count|default:0 }} configured</p>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:tour_category_create' %}" 
                       class="flex items-center w-full bg-sap-teal-600 hover:bg-sap-teal-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Add Category
                    </a>
                    <a href="{% url 'accounts:tour_category_list' %}" 
                       class="flex items-center w-full bg-sap-teal-100 hover:bg-sap-teal-200 text-sap-teal-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                        Manage Categories
                    </a>
                </div>
            </div>
        </div>

        <!-- Settlement Management -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="check-square" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                    Settlements
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:tour_settlement_create' %}" 
                       class="flex items-center w-full bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Process Settlement
                    </a>
                    <a href="{% url 'accounts:tour_settlement_list' %}" 
                       class="flex items-center w-full bg-sap-green-100 hover:bg-sap-green-200 text-sap-green-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                        View Settlements
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity and Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        
        <!-- Recent Tours -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                        <i data-lucide="clock" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Recent Tours
                    </h3>
                    <a href="{% url 'accounts:tour_voucher_list' %}" 
                       class="text-sap-blue-600 hover:text-sap-blue-900 text-sm font-medium">
                        View All
                    </a>
                </div>
            </div>
            <div class="p-6">
                {% if recent_tours %}
                <div class="space-y-4">
                    {% for tour in recent_tours %}
                    <div class="flex items-center justify-between p-3 bg-sap-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-sap-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <i data-lucide="plane" class="w-4 h-4 text-sap-blue-600"></i>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-sap-gray-900">{{ tour.tour_purpose|default:"Tour" }}</div>
                                <div class="text-xs text-sap-gray-500">{{ tour.employee_name|default:"Employee" }} - {{ tour.start_date|date:"d M Y" }}</div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-sap-gray-900">₹{{ tour.total_amount|default:0|floatformat:2 }}</div>
                            <div class="text-xs text-sap-gray-500">{{ tour.get_status_display|default:"Active" }}</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-6">
                    <i data-lucide="plane" class="w-8 h-8 text-sap-gray-400 mx-auto mb-2"></i>
                    <p class="text-sm text-sap-gray-500">No recent tours</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Expense Analytics -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="trending-up" class="w-5 h-5 mr-2 text-sap-amber-600"></i>
                    Expense Analytics
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-sap-blue-500 rounded-full mr-3"></div>
                            <span class="text-sm text-sap-gray-700">Travel Expenses</span>
                        </div>
                        <span class="text-sm font-medium text-sap-gray-900">₹{{ travel_expenses|default:0|floatformat:2 }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-sap-green-500 rounded-full mr-3"></div>
                            <span class="text-sm text-sap-gray-700">Accommodation</span>
                        </div>
                        <span class="text-sm font-medium text-sap-gray-900">₹{{ accommodation_expenses|default:0|floatformat:2 }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-sap-orange-500 rounded-full mr-3"></div>
                            <span class="text-sm text-sap-gray-700">Food & Meals</span>
                        </div>
                        <span class="text-sm font-medium text-sap-gray-900">₹{{ food_expenses|default:0|floatformat:2 }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-sap-purple-500 rounded-full mr-3"></div>
                            <span class="text-sm text-sap-gray-700">Other Expenses</span>
                        </div>
                        <span class="text-sm font-medium text-sap-gray-900">₹{{ other_expenses|default:0|floatformat:2 }}</span>
                    </div>
                </div>
                
                <div class="mt-6 pt-4 border-t border-sap-gray-200">
                    <button type="button" onclick="generateDetailedAnalytics()" 
                            class="w-full bg-sap-amber-600 hover:bg-sap-amber-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="bar-chart" class="w-4 h-4 inline mr-2"></i>
                        View Detailed Analytics
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function generateTourReport() {
    alert('Tour summary report generation functionality would be implemented here.');
}

function generateExpenseReport() {
    alert('Expense analysis report generation functionality would be implemented here.');
}

function generateDetailedAnalytics() {
    alert('Detailed expense analytics functionality would be implemented here.');
}

// Initialize lucide icons on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %>