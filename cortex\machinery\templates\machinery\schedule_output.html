{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="bg-indigo-600 text-white px-4 py-2 rounded-t-lg">
        <h1 class="text-lg font-semibold">{{ title }}</h1>
    </div>
    
    <!-- Work Order Selection -->
    <div class="bg-white border border-gray-300 p-4">
        <form method="get" class="grid grid-cols-1 md:grid-cols-2 gap-4 items-end">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">WO No:</label>
                <select name="wono" 
                        onchange="this.form.submit()"
                        class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                    <option value="">Select Work Order</option>
                    {% for wo in work_orders %}
                        <option value="{{ wo.wono }}" {% if wo.wono == selected_wono %}selected{% endif %}>
                            {{ wo.wono }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Customer Name:</label>
                <span class="block w-full p-2 bg-gray-50 border border-gray-300 rounded-md">
                    {{ customer_name|default:"Select a work order" }}
                </span>
            </div>
        </form>
    </div>

    <!-- Work Order Items -->
    <div class="bg-white border-l border-r border-b border-gray-300 p-4">
        {% if work_order_items %}
        <div class="overflow-x-auto">
            <table class="min-w-full border border-gray-300">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-2 text-xs font-medium text-gray-500 uppercase border-b">SN</th>
                        <th class="px-4 py-2 text-xs font-medium text-gray-500 uppercase border-b">Item Code</th>
                        <th class="px-4 py-2 text-xs font-medium text-gray-500 uppercase border-b">Description</th>
                        <th class="px-4 py-2 text-xs font-medium text-gray-500 uppercase border-b">UOM</th>
                        <th class="px-4 py-2 text-xs font-medium text-gray-500 uppercase border-b">BOM Qty</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in work_order_items %}
                    <tr class="border-b hover:bg-gray-50">
                        <td class="px-4 py-2 text-center">{{ forloop.counter }}</td>
                        <td class="px-4 py-2 text-center">
                            <a href="{% url 'machinery:schedule_output_detail' wono=selected_wono item_id=item.itemid.id %}"
                               class="text-blue-600 hover:text-blue-800 font-medium">
                                {{ item.itemid.itemcode }}
                            </a>
                        </td>
                        <td class="px-4 py-2">{{ item.itemid.description }}</td>
                        <td class="px-4 py-2 text-center">{{ item.itemid.uombasic }}</td>
                        <td class="px-4 py-2 text-center">{{ item.qty }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% elif selected_wono %}
        <div class="text-center py-8">
            <p class="text-gray-500 text-lg">No items found for this work order.</p>
            <p class="text-gray-400 text-sm">Please check if the work order has BOM details.</p>
        </div>
        {% else %}
        <div class="text-center py-8">
            <p class="text-gray-500 text-lg">Please select a work order to view items.</p>
            <p class="text-gray-400 text-sm">Use the dropdown above to select a work order.</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}