{% extends 'core/base.html' %}
{% load static %}

{% block title %}Goods Inward Note [GIN] - Edit{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <div class="bg-blue-600 text-white px-4 py-2">
        <h1 class="text-lg font-bold">Goods Inward Note [GIN] - Edit Details</h1>
        <p class="text-sm opacity-90">Supplier: {{ gin.supplier_name|default:"Unknown Supplier" }}</p>
    </div>
    
    <div class="p-4">
        <!-- GIN Header Information -->
        <div class="bg-white rounded-lg shadow-sm border mb-4">
            <div class="px-4 py-3 border-b border-gray-200">
                <div class="grid grid-cols-6 gap-4 text-sm">
                    <div>
                        <span class="font-medium">GIN No :</span> {{ gin.gin_number|default:"0778" }}
                    </div>
                    <div>
                        <span class="font-medium">Challan Date :</span> {{ gin.challan_date|default:"21-11-2022" }}
                    </div>
                    <div>
                        <span class="font-medium">Challan No :</span> {{ gin.challan_number|default:"2304" }}
                    </div>
                    <div class="col-span-3 text-right">
                        <span class="font-medium">Business Group :</span> {{ gin.business_group|default:"FABRICATION-SAPL Plant 2" }}
                    </div>
                </div>
            </div>
            
            <!-- Form Fields -->
            <div class="p-4">
                <form method="post">
                    {% csrf_token %}
                    <div class="grid grid-cols-4 gap-4 items-center mb-4">
                        <div class="flex items-center gap-2">
                            <label class="text-sm font-medium">Gate Entry No:</label>
                            <input type="text" name="gate_entry_no" value="{{ gin.gate_entry_number|default:'SAPL/22/11/68' }}" 
                                   class="border border-gray-300 rounded px-2 py-1 text-sm flex-1">
                        </div>
                        
                        <div class="flex items-center gap-2">
                            <label class="text-sm font-medium">Date :</label>
                            <input type="text" name="gin_date" value="{{ gin.gin_date|default:'21-11-2022' }}" 
                                   class="border border-gray-300 rounded px-2 py-1 text-sm w-24">
                        </div>
                        
                        <div class="flex items-center gap-2">
                            <label class="text-sm font-medium">Time :</label>
                            <select name="time_hour" class="border border-gray-300 rounded px-2 py-1 text-sm w-12">
                                <option value="00">00</option>
                                <option value="01">01</option>
                                <option value="02">02</option>
                                <option value="03">03</option>
                                <option value="04">04</option>
                                <option value="05">05</option>
                                <option value="06">06</option>
                                <option value="07">07</option>
                                <option value="08">08</option>
                                <option value="09" selected>09</option>
                                <option value="10">10</option>
                                <option value="11">11</option>
                                <option value="12">12</option>
                            </select>
                            <span>:</span>
                            <select name="time_minute" class="border border-gray-300 rounded px-2 py-1 text-sm w-12">
                                <option value="00">00</option>
                                <option value="05">05</option>
                                <option value="10">10</option>
                                <option value="15">15</option>
                                <option value="20">20</option>
                                <option value="25">25</option>
                                <option value="30">30</option>
                                <option value="35">35</option>
                                <option value="40">40</option>
                                <option value="45">45</option>
                                <option value="50">50</option>
                                <option value="55">55</option>
                                <option value="56" selected>56</option>
                            </select>
                            <span>:</span>
                            <select name="time_second" class="border border-gray-300 rounded px-2 py-1 text-sm w-12">
                                <option value="00" selected>00</option>
                                <option value="15">15</option>
                                <option value="30">30</option>
                                <option value="45">45</option>
                            </select>
                            <select name="time_period" class="border border-gray-300 rounded px-2 py-1 text-sm w-12">
                                <option value="AM" selected>AM</option>
                                <option value="PM">PM</option>
                            </select>
                        </div>
                        
                        <div class="flex items-center gap-2">
                            <label class="text-sm font-medium">Mode of Transport :</label>
                            <span>-</span>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 items-center mb-4">
                        <div class="flex items-center gap-2">
                            <label class="text-sm font-medium">Mode of Transport :</label>
                            <input type="text" name="mode_of_transport" value="{{ gin.mode_of_transport|default:'' }}" 
                                   class="border border-gray-300 rounded px-2 py-1 text-sm flex-1" required>
                            <span class="text-red-500">*</span>
                        </div>
                        
                        <div class="flex items-center gap-2">
                            <label class="text-sm font-medium">Vehicle No :</label>
                            <input type="text" name="vehicle_no" value="{{ gin.vehicle_number|default:'' }}" 
                                   class="border border-gray-300 rounded px-2 py-1 text-sm flex-1" required>
                            <span class="text-red-500">*</span>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Supplier-wise Line Items Table -->
        <div class="bg-white rounded-lg shadow-sm border">
            <div class="px-4 py-3 border-b border-gray-200 bg-gray-50">
                <h2 class="text-lg font-semibold text-gray-900">Supplier-wise Line Items Details</h2>
                <p class="text-sm text-gray-600 mt-1">Material details for supplier: <strong>{{ gin.supplier_name|default:"Unknown Supplier" }}</strong></p>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full table-auto divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-8">SN</th>
                            <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">Edit</th>
                            <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">Image</th>
                            <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">Spec. Sheet</th>
                            <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">Item Code</th>
                            <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                            <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">UOM</th>
                            <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">Category</th>
                            <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">Sub-Cate</th>
                            <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">PO Qty</th>
                            <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">Tot Recd Qty</th>
                            <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">Challan Qty</th>
                            <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">Recd Qty</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for line_item in gin_line_items %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-2 py-3 text-sm text-gray-900 text-right">{{ line_item.sn }}</td>
                            <td class="px-2 py-3 text-sm">
                                <a href="#" class="text-blue-600 hover:text-blue-800 underline">Edit</a>
                            </td>
                            <td class="px-2 py-3 text-sm text-center">
                                {% if line_item.has_image %}
                                    <a href="#" class="text-blue-600 hover:text-blue-800 underline">Image</a>
                                {% endif %}
                            </td>
                            <td class="px-2 py-3 text-sm text-center">
                                {% if line_item.has_spec %}
                                    <a href="#" class="text-blue-600 hover:text-blue-800 underline">Spec</a>
                                {% endif %}
                            </td>
                            <td class="px-2 py-3 text-sm text-gray-900 text-center">{{ line_item.item_code }}</td>
                            <td class="px-2 py-3 text-sm text-gray-900">{{ line_item.description }}</td>
                            <td class="px-2 py-3 text-sm text-gray-900 text-center">{{ line_item.uom }}</td>
                            <td class="px-2 py-3 text-sm text-gray-900 text-center">{{ line_item.category }}</td>
                            <td class="px-2 py-3 text-sm text-gray-900 text-center">{{ line_item.sub_category }}</td>
                            <td class="px-2 py-3 text-sm text-gray-900 text-right">{{ line_item.po_qty }}</td>
                            <td class="px-2 py-3 text-sm text-gray-900 text-right">{{ line_item.total_received_qty }}</td>
                            <td class="px-2 py-3 text-sm text-gray-900 text-right">{{ line_item.challan_qty }}</td>
                            <td class="px-2 py-3 text-sm text-gray-900 text-right">{{ line_item.received_qty }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="13" class="px-4 py-8 text-center">
                                <div class="text-gray-500">
                                    <p class="text-lg font-medium text-red-900">No line items found!</p>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Navigation Buttons -->
        <div class="mt-4 flex justify-center gap-4">
            <a href="{% url 'inventory:gin_edit_list' %}" 
               class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded font-medium transition-colors">
                Back to GIN List
            </a>
            <button type="button" 
                    onclick="window.history.back()" 
                    class="bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded font-medium transition-colors">
                Cancel
            </button>
        </div>
    </div>
</div>
{% endblock %}