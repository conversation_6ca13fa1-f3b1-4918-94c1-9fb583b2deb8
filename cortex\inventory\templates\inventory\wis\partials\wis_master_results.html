{% load static %}

<div class="overflow-x-auto">
    {% if wis_records %}
        <table class="w-full table-auto divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        WIS Number
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Work Order
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Session ID
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Financial Year
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for wis in wis_records %}
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        <a href="{% url 'inventory:wis_master_detail' wis.pk %}" class="text-indigo-600 hover:text-indigo-900">
                            {{ wis.wis_number }}
                        </a>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {{ wis.sys_date|default:"N/A" }}
                        {% if wis.sys_time %}
                            <span class="block text-xs text-gray-400">{{ wis.sys_time }}</span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {{ wis.work_order_number|default:"-" }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {{ wis.session_id|default:"-" }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {{ wis.financial_year|default:"-" }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex justify-end space-x-2">
                            <a href="{% url 'inventory:wis_master_detail' wis.pk %}" 
                               class="text-indigo-600 hover:text-indigo-900 text-sm">
                                View
                            </a>
                            <a href="{% url 'inventory:wis_master_update' wis.pk %}" 
                               class="text-green-600 hover:text-green-900 text-sm">
                                Edit
                            </a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Pagination -->
        {% if is_paginated %}
        <div class="px-6 py-3 border-t border-gray-200">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-700">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} WIS records
                </div>
                <div class="flex space-x-1">
                    {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}" 
                           class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 border border-gray-300 rounded-md">
                            Previous
                        </a>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <span class="px-3 py-2 text-sm bg-indigo-600 text-white border border-indigo-600 rounded-md">
                                {{ num }}
                            </span>
                        {% else %}
                            <a href="?page={{ num }}" 
                               class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 border border-gray-300 rounded-md">
                                {{ num }}
                            </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}" 
                           class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 border border-gray-300 rounded-md">
                            Next
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    {% else %}
        <div class="text-center py-12">
            <div class="mx-auto h-12 w-12 text-gray-400">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
            </div>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No WIS records found</h3>
            <p class="mt-1 text-sm text-gray-500">Get started by creating a new WIS configuration.</p>
            <div class="mt-6">
                <a href="{% url 'inventory:wis_master_create' %}" 
                   class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    Create WIS
                </a>
            </div>
        </div>
    {% endif %}
</div>