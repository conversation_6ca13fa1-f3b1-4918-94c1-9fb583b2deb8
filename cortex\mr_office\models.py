from django.db import models
from django.contrib.auth.models import User
from sys_admin.models import Company, FinancialYear


class ModuleMaster(models.Model):
    """Module master for dropdown selection."""
    modid = models.AutoField(db_column="ModId", primary_key=True)
    modname = models.TextField(db_column="ModName", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblModule_Master"

    def __str__(self):
        return self.modname or f"Module {self.modid}"


class MROffice(models.Model):
    """MR Office document management."""
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate", blank=True, null=True)
    systime = models.TextField(db_column="SysTime", blank=True, null=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId", blank=True, null=True)
    session = models.ForeignKey(User, models.DO_NOTHING, db_column="SessionId", blank=True, null=True)
    finyear = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId", blank=True, null=True)
    formodule = models.IntegerField(db_column="ForModule", blank=True, null=True)
    format = models.TextField(db_column="Format", blank=True, null=True)
    filename = models.TextField(db_column="FileName", blank=True, null=True)
    size = models.TextField(db_column="Size", blank=True, null=True)
    contenttype = models.TextField(db_column="ContentType", blank=True, null=True)
    data = models.BinaryField(db_column="Data", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblMROffice"

    def __str__(self):
        return f"{self.format} - {self.filename}"

    def get_module_name(self):
        """Get module name from ModuleMaster."""
        try:
            module = ModuleMaster.objects.get(modid=self.formodule)
            return module.modname
        except ModuleMaster.DoesNotExist:
            return f"Module {self.formodule}"
