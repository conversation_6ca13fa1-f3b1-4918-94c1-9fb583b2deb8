{% extends "core/base.html" %}
{% load static %}

{% block title %}Delete Supplier Challan {{ object.scno }} - Inventory Management{% endblock %}

{% block content %}
<div class="sap-page">
    <!-- Page Header -->
    <div class="sap-page-header">
        <div class="sap-page-header-content">
            <div class="sap-page-title">
                <h1>Delete Supplier Challan</h1>
                <p>Confirm deletion of supplier challan {{ object.scno }}</p>
            </div>
        </div>
    </div>

    <!-- Confirmation Panel -->
    <div class="sap-panel">
        <div class="sap-panel-content">
            <div class="sap-message-strip sap-message-strip--warning mb-6">
                <svg class="w-5 h-5 text-yellow-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <div class="sap-message-strip-text">
                    <strong>Warning:</strong> This action cannot be undone. All data associated with this supplier challan will be permanently deleted.
                </div>
            </div>

            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Challan Details</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <span class="text-sm font-medium text-gray-700">Challan Number:</span>
                        <span class="text-sm text-gray-900 ml-2">{{ object.scno }}</span>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-700">Supplier:</span>
                        <span class="text-sm text-gray-900 ml-2">{{ object.supplier_name }}</span>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-700">Date:</span>
                        <span class="text-sm text-gray-900 ml-2">{{ object.challan_date|date:"M d, Y" }}</span>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-700">Status:</span>
                        <span class="text-sm text-gray-900 ml-2">{{ object.status }}</span>
                    </div>
                </div>
            </div>

            <form method="post">
                {% csrf_token %}
                <div class="sap-form-actions">
                    <button type="submit" class="sap-button sap-button--danger">
                        Yes, Delete Challan
                    </button>
                    <a href="{% url 'inventory:supplier_challan_detail' object.pk %}" class="sap-button sap-button--transparent">
                        Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}