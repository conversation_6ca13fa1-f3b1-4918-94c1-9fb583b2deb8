<!-- Partial template for adding new subcategory -->
<div class="px-6 py-4">
    <form hx-post="{% url 'sales_distribution:subcategory_create' %}" 
          hx-target="#subcategory-table" 
          hx-swap="outerHTML"
          hx-trigger="submit"
          class="space-y-4">
        {% csrf_token %}
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="{{ form.cid.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                    Parent Category <span class="text-red-500">*</span>
                </label>
                {{ form.cid }}
                {% if form.cid.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.cid.errors.0 }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.sub_c_name.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                    Sub-Category Name <span class="text-red-500">*</span>
                </label>
                {{ form.sub_c_name }}
                {% if form.sub_c_name.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.sub_c_name.errors.0 }}</p>
                {% endif %}
            </div>
        </div>
        <div class="flex justify-end space-x-3 pt-4">
            <button type="button" onclick="hideForm()" 
                    class="px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                Cancel
            </button>
            <button type="submit" 
                    class="px-4 py-2 bg-sap-purple-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-sap-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-purple-500">
                Add Sub-Category
            </button>
        </div>
    </form>
</div>