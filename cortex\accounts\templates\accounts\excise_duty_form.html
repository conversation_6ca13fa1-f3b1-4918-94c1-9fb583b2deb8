<!-- accounts/templates/accounts/excise_duty_form.html -->
<!-- Excise Duty Create/Edit Form Template -->
<!-- Task Group 4: Taxation Management - Excise Duty Form -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{{ form_title|default:"Excise Duty Management" }} - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-blue-600 to-sap-blue-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="industry" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">{{ form_title|default:"Excise Duty Management" }}</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Configure excise duty rates and education cess</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:excise_duty_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
                {% if action == 'edit' %}
                <a href="{% url 'accounts:excise_calculator' %}?duty_id={{ object.id }}" 
                   class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="calculator" class="w-4 h-4 inline mr-2"></i>
                    Calculate
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6" x-data="exciseDutyForm()">
    
    <!-- Main Form -->
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800">Excise Duty Configuration</h3>
                <p class="text-sm text-sap-gray-600 mt-1">Enter excise duty rates and cess information</p>
            </div>
            
            <form method="post" class="p-6" @submit="validateForm">
                {% csrf_token %}
                
                <!-- Error Messages -->
                {% if form.non_field_errors %}
                <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex">
                        <i data-lucide="alert-circle" class="w-5 h-5 text-red-400 mr-3"></i>
                        <div>
                            <h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                            <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                                {% for error in form.non_field_errors %}
                                <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Basic Information -->
                    <div class="space-y-6">
                        <div>
                            <h4 class="text-base font-medium text-sap-gray-800 mb-4">Basic Information</h4>
                            
                            <!-- Terms/Description -->
                            <div class="mb-6">
                                <label for="{{ form.terms.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.terms.label }}
                                    <span class="text-red-500">*</span>
                                </label>
                                {{ form.terms|add_class:"block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                                {% if form.terms.help_text %}
                                <p class="text-xs text-sap-gray-500 mt-1">{{ form.terms.help_text }}</p>
                                {% endif %}
                                {% if form.terms.errors %}
                                <p class="text-sm text-red-600 mt-1">{{ form.terms.errors.0 }}</p>
                                {% endif %}
                            </div>
                            
                            <!-- Basic Excise Rate -->
                            <div class="mb-6">
                                <label for="{{ form.value.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.value.label }}
                                    <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    {{ form.value|add_class:"block w-full px-3 py-2.5 pr-8 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                                    <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                        <span class="text-sap-gray-500 text-sm">%</span>
                                    </div>
                                </div>
                                {% if form.value.help_text %}
                                <p class="text-xs text-sap-gray-500 mt-1">{{ form.value.help_text }}</p>
                                {% endif %}
                                {% if form.value.errors %}
                                <p class="text-sm text-red-600 mt-1">{{ form.value.errors.0 }}</p>
                                {% endif %}
                            </div>
                            
                            <!-- Accessible Value -->
                            <div class="mb-6">
                                <label for="{{ form.accessible_value.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.accessible_value.label }}
                                    <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    {{ form.accessible_value|add_class:"block w-full px-3 py-2.5 pr-8 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                                    <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                        <span class="text-sap-gray-500 text-sm">%</span>
                                    </div>
                                </div>
                                {% if form.accessible_value.help_text %}
                                <p class="text-xs text-sap-gray-500 mt-1">{{ form.accessible_value.help_text }}</p>
                                {% endif %}
                                {% if form.accessible_value.errors %}
                                <p class="text-sm text-red-600 mt-1">{{ form.accessible_value.errors.0 }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Cess Information -->
                    <div class="space-y-6">
                        <div>
                            <h4 class="text-base font-medium text-sap-gray-800 mb-4">Cess Configuration</h4>
                            
                            <!-- Education Cess -->
                            <div class="mb-6">
                                <label for="{{ form.edu_cess.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.edu_cess.label }}
                                    <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    {{ form.edu_cess|add_class:"block w-full px-3 py-2.5 pr-8 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                                    <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                        <span class="text-sap-gray-500 text-sm">%</span>
                                    </div>
                                </div>
                                {% if form.edu_cess.help_text %}
                                <p class="text-xs text-sap-gray-500 mt-1">{{ form.edu_cess.help_text }}</p>
                                {% endif %}
                                {% if form.edu_cess.errors %}
                                <p class="text-sm text-red-600 mt-1">{{ form.edu_cess.errors.0 }}</p>
                                {% endif %}
                            </div>
                            
                            <!-- SHE Cess -->
                            <div class="mb-6">
                                <label for="{{ form.she_cess.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    {{ form.she_cess.label }}
                                    <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    {{ form.she_cess|add_class:"block w-full px-3 py-2.5 pr-8 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                                    <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                        <span class="text-sap-gray-500 text-sm">%</span>
                                    </div>
                                </div>
                                {% if form.she_cess.help_text %}
                                <p class="text-xs text-sap-gray-500 mt-1">{{ form.she_cess.help_text }}</p>
                                {% endif %}
                                {% if form.she_cess.errors %}
                                <p class="text-sm text-red-600 mt-1">{{ form.she_cess.errors.0 }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Default Settings and Status -->
                <div class="mt-8 pt-6 border-t border-sap-gray-200">
                    <h4 class="text-base font-medium text-sap-gray-800 mb-4">Default Settings & Status</h4>
                    
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                        <!-- Default Excise -->
                        <div class="flex items-center">
                            {{ form.is_default_excise|add_class:"h-4 w-4 text-sap-blue-600 focus:ring-sap-blue-500 border-sap-gray-300 rounded" }}
                            <label for="{{ form.is_default_excise.id_for_label }}" class="ml-2 text-sm text-sap-gray-700">
                                {{ form.is_default_excise.label }}
                            </label>
                        </div>
                        
                        <!-- Default Service Tax -->
                        <div class="flex items-center">
                            {{ form.is_default_service_tax|add_class:"h-4 w-4 text-sap-green-600 focus:ring-sap-green-500 border-sap-gray-300 rounded" }}
                            <label for="{{ form.is_default_service_tax.id_for_label }}" class="ml-2 text-sm text-sap-gray-700">
                                {{ form.is_default_service_tax.label }}
                            </label>
                        </div>
                        
                        <!-- Active Status -->
                        <div class="flex items-center">
                            {{ form.is_active|add_class:"h-4 w-4 text-sap-blue-600 focus:ring-sap-blue-500 border-sap-gray-300 rounded" }}
                            <label for="{{ form.is_active.id_for_label }}" class="ml-2 text-sm text-sap-gray-700">
                                {{ form.is_active.label }}
                            </label>
                        </div>
                    </div>
                    
                    <!-- Help text for default settings -->
                    <div class="mt-4 p-4 bg-sap-blue-50 rounded-lg">
                        <div class="flex">
                            <i data-lucide="info" class="w-5 h-5 text-sap-blue-400 mr-3 mt-0.5"></i>
                            <div class="text-sm text-sap-blue-800">
                                <p class="font-medium mb-1">Default Settings:</p>
                                <ul class="list-disc list-inside space-y-1">
                                    <li>Only one excise duty can be set as default excise at a time</li>
                                    <li>Only one excise duty can be set as default service tax at a time</li>
                                    <li>Default duties are automatically selected in new transactions</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Calculation Preview -->
                <div x-show="showPreview" x-transition class="mt-8 pt-6 border-t border-sap-gray-200">
                    <h4 class="text-base font-medium text-sap-gray-800 mb-4">Rate Calculation Preview</h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-sap-blue-50 rounded-lg p-4">
                            <div class="text-sm text-sap-blue-600 font-medium">Total Excise Rate</div>
                            <div class="text-2xl font-bold text-sap-blue-800" x-text="totalRate + '%'"></div>
                            <div class="text-xs text-sap-blue-600 mt-1">Including all cess</div>
                        </div>
                        
                        <div class="bg-sap-green-50 rounded-lg p-4">
                            <div class="text-sm text-sap-green-600 font-medium">Effective Rate</div>
                            <div class="text-2xl font-bold text-sap-green-800" x-text="effectiveRate + '%'"></div>
                            <div class="text-xs text-sap-green-600 mt-1">On accessible value</div>
                        </div>
                        
                        <div class="bg-sap-orange-50 rounded-lg p-4">
                            <div class="text-sm text-sap-orange-600 font-medium">Sample Tax (₹10,000)</div>
                            <div class="text-2xl font-bold text-sap-orange-800" x-text="'₹' + sampleTax"></div>
                            <div class="text-xs text-sap-orange-600 mt-1">Total tax amount</div>
                        </div>
                    </div>
                </div>
                
                <!-- Form Actions -->
                <div class="mt-8 pt-6 border-t border-sap-gray-200 flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button type="button" @click="calculatePreview" 
                                class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="calculator" class="w-4 h-4 inline mr-2"></i>
                            Calculate Preview
                        </button>
                        
                        {% if action == 'edit' %}
                        <a href="{% url 'accounts:excise_duty_delete' object.pk %}" 
                           class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="trash-2" class="w-4 h-4 inline mr-2"></i>
                            Delete
                        </a>
                        {% endif %}
                    </div>
                    
                    <div class="flex items-center space-x-3">
                        <a href="{% url 'accounts:excise_duty_list' %}" 
                           class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-6 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="save" class="w-4 h-4 inline mr-2"></i>
                            {{ action == 'edit' and 'Update' or 'Create' }} Excise Duty
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Calculation Examples (for edit mode) -->
        {% if action == 'edit' and calculation_examples %}
        <div class="mt-8 bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800">Calculation Examples</h3>
                <p class="text-sm text-sap-gray-600 mt-1">See how this excise duty affects different amounts</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {% for example in calculation_examples %}
                    <div class="border border-sap-gray-200 rounded-lg p-4">
                        <div class="text-lg font-bold text-sap-gray-800">₹{{ example.base_amount|floatformat:0 }}</div>
                        <div class="text-sm text-sap-gray-600 mt-1">
                            Tax: <span class="font-medium text-sap-blue-600">₹{{ example.calculation.total_excise|floatformat:2 }}</span>
                        </div>
                        <div class="text-sm text-sap-gray-600">
                            Total: <span class="font-medium text-sap-gray-800">₹{{ example.calculation.total_amount|floatformat:2 }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function exciseDutyForm() {
    return {
        showPreview: false,
        totalRate: 0,
        effectiveRate: 0,
        sampleTax: 0,
        
        init() {
            lucide.createIcons();
        },
        
        calculatePreview() {
            const value = parseFloat(document.getElementById('{{ form.value.id_for_label }}').value) || 0;
            const accessibleValue = parseFloat(document.getElementById('{{ form.accessible_value.id_for_label }}').value) || 100;
            const eduCess = parseFloat(document.getElementById('{{ form.edu_cess.id_for_label }}').value) || 0;
            const sheCess = parseFloat(document.getElementById('{{ form.she_cess.id_for_label }}').value) || 0;
            
            // Calculate rates
            this.totalRate = (value + eduCess + sheCess).toFixed(3);
            this.effectiveRate = (value * (accessibleValue / 100) + eduCess + sheCess).toFixed(3);
            
            // Calculate sample tax on ₹10,000
            const sampleAmount = 10000;
            const accessibleAmount = sampleAmount * (accessibleValue / 100);
            const basicTax = accessibleAmount * (value / 100);
            const eduCessAmount = basicTax * (eduCess / 100);
            const sheCessAmount = basicTax * (sheCess / 100);
            this.sampleTax = (basicTax + eduCessAmount + sheCessAmount).toFixed(2);
            
            this.showPreview = true;
        },
        
        validateForm(event) {
            // Client-side validation can be added here
            return true;
        }
    }
}

// Auto-calculate on input change
document.addEventListener('DOMContentLoaded', function() {
    const inputs = ['{{ form.value.id_for_label }}', '{{ form.accessible_value.id_for_label }}', '{{ form.edu_cess.id_for_label }}', '{{ form.she_cess.id_for_label }}'];
    
    inputs.forEach(id => {
        const input = document.getElementById(id);
        if (input) {
            input.addEventListener('input', function() {
                // Auto-calculate preview if all fields have values
                const allFieldsFilled = inputs.every(inputId => {
                    const field = document.getElementById(inputId);
                    return field && field.value && parseFloat(field.value) >= 0;
                });
                
                if (allFieldsFilled) {
                    // Trigger calculation with a small delay
                    setTimeout(() => {
                        document.querySelector('[x-data="exciseDutyForm()"]').__x.$data.calculatePreview();
                    }, 100);
                }
            });
        }
    });
});
</script>
{% endblock %}