<!-- Product Add Form - SAP S/4HANA Style -->
<div class="bg-white border border-gray-200 rounded-lg shadow-sm mb-6">
    <div class="border-b border-gray-200 bg-gray-50 px-6 py-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="plus-circle" class="w-3 h-3 text-blue-600"></i>
                </div>
                <div>
                    <h2 class="text-base font-semibold text-gray-800">Add New Product</h2>
                    <p class="text-gray-600 text-xs">Enter product information below</p>
                </div>
            </div>
            <button type="button" 
                    onclick="document.getElementById('product-form-container').innerHTML=''"
                    class="text-gray-400 hover:text-gray-600">
                <i data-lucide="x" class="w-5 h-5"></i>
            </button>
        </div>
    </div>
    
    <div class="p-6">
        <form hx-post="{% url 'sales_distribution:product_create' %}"
              hx-target="#product-table-container"
              hx-swap="innerHTML"
              class="space-y-4">
            {% csrf_token %}
            
            {% if error %}
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex items-center">
                    <i data-lucide="alert-circle" class="w-5 h-5 text-red-600 mr-2"></i>
                    <span class="text-sm text-red-800">{{ error }}</span>
                </div>
            </div>
            {% endif %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Product Name -->
                <div>
                    <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Product Name <span class="text-red-500">*</span>
                    </label>
                    {{ form.name }}
                    {% if form.name.errors %}
                        {% for error in form.name.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ error }}</p>
                        {% endfor %}
                    {% endif %}
                </div>
                
                <!-- Submit Button -->
                <div class="flex items-end">
                    <button type="submit" 
                            class="w-full inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Add Product
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>