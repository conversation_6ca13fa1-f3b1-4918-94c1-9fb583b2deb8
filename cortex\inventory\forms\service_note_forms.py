from django import forms
from django.core.exceptions import ValidationError
from ..models import MaterialServiceNote, MaterialServiceNoteDetail


class MaterialServiceNoteForm(forms.ModelForm):
    """Form for creating and editing Material Service Notes"""
    
    class Meta:
        model = MaterialServiceNote
        fields = [
            'gsnno', 'ginid', 'ginno', 'taxinvoiceno', 'taxinvoicedate'
        ]
        widgets = {
            'gsnno': forms.TextInput(attrs={
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'placeholder': 'GSN Number'
            }),
            'ginid': forms.NumberInput(attrs={
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'placeholder': 'GIN ID'
            }),
            'ginno': forms.TextInput(attrs={
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'placeholder': 'GIN Number'
            }),
            'taxinvoiceno': forms.TextInput(attrs={
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'placeholder': 'Tax Invoice Number'
            }),
            'taxinvoicedate': forms.TextInput(attrs={
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'placeholder': 'Tax Invoice Date'
            })
        }
        labels = {
            'gsnno': 'GSN Number',
            'ginid': 'GIN ID',
            'ginno': 'GIN Number',
            'taxinvoiceno': 'Tax Invoice Number',
            'taxinvoicedate': 'Tax Invoice Date'
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Set required fields  
        self.fields['gsnno'].required = True


class MaterialServiceNoteDetailForm(forms.ModelForm):
    """Form for Service Note detail items"""
    
    class Meta:
        model = MaterialServiceNoteDetail
        fields = [
            'mid', 'gsnno', 'poid', 'receivedqty'
        ]
        widgets = {
            'mid': forms.NumberInput(attrs={
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'placeholder': 'Material ID'
            }),
            'gsnno': forms.TextInput(attrs={
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'placeholder': 'GSN Number'
            }),
            'poid': forms.NumberInput(attrs={
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'placeholder': 'PO ID'
            }),
            'receivedqty': forms.NumberInput(attrs={
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'step': '0.001',
                'placeholder': 'Received Quantity'
            })
        }
        labels = {
            'mid': 'Material ID',
            'gsnno': 'GSN Number',
            'poid': 'PO ID', 
            'receivedqty': 'Received Quantity'
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set required fields
        self.fields['receivedqty'].required = True


class ServiceNoteDetailFormSet(forms.BaseInlineFormSet):
    """Formset for Service Note detail items"""
    
    def clean(self):
        """Validate formset"""
        if any(self.errors):
            return
            
        if not any(form.cleaned_data for form in self.forms if form.cleaned_data):
            raise ValidationError("At least one service item is required.")


class ServiceNoteSearchForm(forms.Form):
    """Form for searching Service Notes"""
    
    gsnno = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'placeholder': 'GSN Number'
        })
    )
    
    ginno = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'placeholder': 'GIN Number'
        })
    )



# Note: MaterialServiceNoteDetail doesn't have ForeignKey to MaterialServiceNote
# Using separate forms instead of inline formset