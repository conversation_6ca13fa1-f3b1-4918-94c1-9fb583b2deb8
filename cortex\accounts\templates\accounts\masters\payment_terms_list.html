{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .sap-blue-600 { color: #0070f3; }
    .sap-blue-500 { color: #0366d6; }
    .sap-gray-300 { border-color: #d1d5db; }
    .sap-gray-500 { color: #6b7280; }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">{{ page_title }}</h1>
                    <p class="text-gray-600 mt-1">Manage payment terms and conditions</p>
                </div>
                <div class="flex space-x-3">
                    <button 
                        class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                        hx-get="{% url 'accounts:payment_terms_create' %}"
                        hx-target="#payment-terms-form-container"
                        hx-swap="innerHTML">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        Add Payment Terms
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Search Section -->
        <div class="p-6">
            <div class="max-w-md">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                    </div>
                    <input 
                        type="text"
                        name="search"
                        placeholder="Search payment terms..."
                        class="block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-lg text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        hx-get="{% url 'accounts:payment_terms_list' %}"
                        hx-target="#payment-terms-table-container"
                        hx-trigger="keyup changed delay:300ms"
                        hx-include="[name='search']"
                        autocomplete="off">
                </div>
            </div>
        </div>
    </div>

    <!-- Form Container -->
    <div id="payment-terms-form-container" class="mb-6"></div>

    <!-- Table Container -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div id="payment-terms-table-container">
            {% include 'accounts/partials/payment_terms_table.html' %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/htmx.org@1.9.6"></script>
<script src="https://unpkg.com/alpinejs@3.13.1/dist/cdn.min.js" defer></script>
{% endblock %}