{% load custom_filters %}

<div class="authorization-results">
    {% if success %}
        <div class="alert alert-success d-flex align-items-center">
            <i class="bi bi-check-circle-fill me-3" style="font-size: 1.5rem;"></i>
            <div>
                <h6 class="alert-heading mb-1">Authorization Successful!</h6>
                <p class="mb-0">
                    {{ authorized_count }} item{{ authorized_count|pluralize }} 
                    {% if authorized_count == 1 %}has{% else %}have{% endif %} been authorized successfully 
                    for Work Order <strong>{{ work_order.wono }}</strong>.
                </p>
            </div>
        </div>
        
        <div class="mt-3">
            <h6>Next Steps:</h6>
            <ul class="list-unstyled">
                <li><i class="bi bi-arrow-right text-primary"></i> Stock quantities will be updated automatically</li>
                <li><i class="bi bi-arrow-right text-primary"></i> Authorization audit trail has been recorded</li>
                <li><i class="bi bi-arrow-right text-primary"></i> Relevant departments will be notified</li>
            </ul>
        </div>
        
        <div class="mt-3 d-flex gap-2">
            <a href="{% url 'inventory:mcn_authorization_list' %}" class="btn btn-primary btn-sm">
                <i class="bi bi-list-ul"></i> Back to Work Orders
            </a>
            <button type="button" class="btn btn-outline-primary btn-sm" onclick="window.location.reload()">
                <i class="bi bi-arrow-clockwise"></i> Refresh Page
            </button>
        </div>
        
    {% else %}
        <div class="alert alert-warning d-flex align-items-center">
            <i class="bi bi-exclamation-triangle-fill me-3" style="font-size: 1.5rem;"></i>
            <div>
                <h6 class="alert-heading mb-1">Authorization Issues</h6>
                <p class="mb-0">
                    {% if authorized_count > 0 %}
                        {{ authorized_count }} item{{ authorized_count|pluralize }} 
                        {% if authorized_count == 1 %}was{% else %}were{% endif %} authorized, 
                        but some items could not be processed.
                    {% else %}
                        No items were authorized. Please check the authorization quantities and try again.
                    {% endif %}
                </p>
            </div>
        </div>
        
        <div class="mt-3">
            <h6>Please check:</h6>
            <ul class="list-unstyled">
                <li><i class="bi bi-arrow-right text-warning"></i> Authorization quantities are valid and positive</li>
                <li><i class="bi bi-arrow-right text-warning"></i> Quantities do not exceed available MCN amounts</li>
                <li><i class="bi bi-arrow-right text-warning"></i> Items are not already fully authorized</li>
                <li><i class="bi bi-arrow-right text-warning"></i> You have proper authorization permissions</li>
            </ul>
        </div>
        
        <div class="mt-3 d-flex gap-2">
            <button type="button" class="btn btn-primary btn-sm" onclick="window.location.reload()">
                <i class="bi bi-arrow-clockwise"></i> Try Again
            </button>
            <a href="{% url 'inventory:mcn_authorization_list' %}" class="btn btn-outline-secondary btn-sm">
                <i class="bi bi-arrow-left"></i> Back to List
            </a>
        </div>
    {% endif %}
</div>

<style>
    .authorization-results {
        padding: 1rem;
    }
    
    .authorization-results .alert {
        border-radius: 8px;
        border: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .authorization-results .alert-success {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        color: #155724;
    }
    
    .authorization-results .alert-warning {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        color: #856404;
    }
    
    .authorization-results .list-unstyled li {
        padding: 0.25rem 0;
        font-size: 0.875rem;
    }
    
    .authorization-results .btn {
        border-radius: 6px;
        font-weight: 500;
    }
</style>