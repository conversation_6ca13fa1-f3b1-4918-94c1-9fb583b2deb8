# accounts/reconciliation_views.py
# Django views for Bank Reconciliation functionality
# Task Group 2: Banking & Cash Management - Reconciliation Views

from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.views.generic import ListView, CreateView, UpdateView, View
from django.urls import reverse_lazy
from django.http import JsonResponse, HttpResponse
from django.db.models import Q, Sum
from django.db import transaction
from decimal import Decimal
from datetime import date, datetime
import csv

from .models import (
    BankReconciliationMaster, BankReconciliationDetails, Bank, 
    BankVoucherMaster, ContraEntryMaster
)
from .reconciliation_forms import (
    BankReconciliationMasterForm, BankReconciliationSearchForm, BankReconciliationBulkUpdateForm,
    BankStatementUploadForm
)


class BankReconciliationMasterListView(LoginRequiredMixin, ListView):
    """
    List view for Bank Reconciliation Masters
    Replaces ASP.NET BankReconciliation_New.aspx main functionality
    """
    model = BankReconciliationMaster
    template_name = 'accounts/bank_reconciliation_master_list.html'
    context_object_name = 'reconciliation_masters'
    paginate_by = 20

    def get_queryset(self):
        queryset = BankReconciliationMaster.objects.select_related(
            'bank', 'company', 'financial_year'
        ).order_by('-reconciliation_date')
        
        # Filter by bank
        bank_id = self.request.GET.get('bank')
        if bank_id:
            queryset = queryset.filter(bank_id=bank_id)
        
        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        
        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(reconciliation_no__icontains=search) |
                Q(bank__name__icontains=search) |
                Q(remarks__icontains=search)
            )
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['banks'] = Bank.objects.all()
        context['statuses'] = [
            ('in_progress', 'In Progress'),
            ('completed', 'Completed'),
            ('pending', 'Pending'),
        ]
        return context


class BankReconciliationMasterCreateView(LoginRequiredMixin, CreateView):
    """
    Create view for Bank Reconciliation Masters
    """
    model = BankReconciliationMaster
    form_class = BankReconciliationMasterForm
    template_name = 'accounts/bank_reconciliation_master_form.html'
    success_url = reverse_lazy('accounts:bank_reconciliation_master_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = 'Create Bank Reconciliation'
        return context

    def form_valid(self, form):
        # Auto-generate reconciliation number
        last_reconciliation = BankReconciliationMaster.objects.order_by('-id').first()
        
        if last_reconciliation and last_reconciliation.reconciliation_no:
            try:
                last_num = int(last_reconciliation.reconciliation_no.split('-')[-1])
                new_num = last_num + 1
            except (ValueError, IndexError):
                new_num = 1
        else:
            new_num = 1
        
        form.instance.reconciliation_no = f"BRM-{new_num:04d}"
        form.instance.reconciliation_date = date.today()
        form.instance.created_by = self.request.user
        form.instance.company_id = self.request.session.get('company_id', 1)
        form.instance.financial_year_id = self.request.session.get('financial_year_id', 1)
        
        # Set calculated amounts from form
        form.instance.difference_amount = form.cleaned_data.get('difference_amount', 0)
        form.instance.reconciled_amount = form.cleaned_data.get('reconciled_amount', 0)
        form.instance.outstanding_amount = form.cleaned_data.get('outstanding_amount', 0)
        
        messages.success(self.request, f'Bank reconciliation {form.instance.reconciliation_no} created successfully.')
        return super().form_valid(form)


class BankReconciliationMasterUpdateView(LoginRequiredMixin, UpdateView):
    """
    Update view for Bank Reconciliation Masters
    """
    model = BankReconciliationMaster
    form_class = BankReconciliationMasterForm
    template_name = 'accounts/bank_reconciliation_master_form.html'
    success_url = reverse_lazy('accounts:bank_reconciliation_master_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = f'Edit Bank Reconciliation - {self.object.reconciliation_no}'
        return context

    def form_valid(self, form):
        form.instance.updated_by = self.request.user
        
        # Set calculated amounts from form
        form.instance.difference_amount = form.cleaned_data.get('difference_amount', 0)
        form.instance.reconciled_amount = form.cleaned_data.get('reconciled_amount', 0)
        form.instance.outstanding_amount = form.cleaned_data.get('outstanding_amount', 0)
        
        messages.success(self.request, f'Bank reconciliation {form.instance.reconciliation_no} updated successfully.')
        return super().form_valid(form)


class BankReconciliationDetailView(LoginRequiredMixin, View):
    """
    Detail view for Bank Reconciliation with transactions
    Implements the main reconciliation workflow from ASP.NET
    """
    
    def get(self, request, pk):
        """Display reconciliation detail page with transactions"""
        reconciliation_master = get_object_or_404(BankReconciliationMaster, pk=pk)
        
        # Get search form
        search_form = BankReconciliationSearchForm(request.GET or None)
        
        # Get transactions for reconciliation
        transactions = self.get_transactions(reconciliation_master, search_form)
        
        # Get existing reconciliation details
        reconciliation_details = BankReconciliationDetails.objects.filter(
            reconciliation_master=reconciliation_master
        ).order_by('-transaction_date')
        
        context = {
            'reconciliation_master': reconciliation_master,
            'search_form': search_form,
            'transactions': transactions,
            'reconciliation_details': reconciliation_details,
            'bulk_form': BankReconciliationBulkUpdateForm(),
        }
        
        return render(request, 'accounts/bank_reconciliation_detail.html', context)
    
    def post(self, request, pk):
        """Handle individual transaction reconciliation"""
        reconciliation_master = get_object_or_404(BankReconciliationMaster, pk=pk)
        
        action = request.POST.get('action')
        
        if action == 'reconcile_transaction':
            return self.reconcile_single_transaction(request, reconciliation_master)
        elif action == 'bulk_reconcile':
            return self.bulk_reconcile_transactions(request, reconciliation_master)
        
        messages.error(request, 'Invalid action specified.')
        return redirect('accounts:bank_reconciliation_detail', pk=pk)
    
    def get_transactions(self, reconciliation_master, search_form):
        """Get unreconciled transactions for the bank within date range"""
        transactions = []
        
        if not search_form or not search_form.is_valid():
            return transactions
        
        bank = reconciliation_master.bank
        from_date = search_form.cleaned_data.get('from_date')
        to_date = search_form.cleaned_data.get('to_date')
        voucher_type = search_form.cleaned_data.get('voucher_type', 'all')
        show_all = search_form.cleaned_data.get('show_all', False)
        
        # Base filter for date range and bank
        base_filter = Q(bank=bank, voucher_date__range=[from_date, to_date])
        
        # Filter by voucher type if specified
        if voucher_type != 'all':
            base_filter &= Q(voucher_type=voucher_type)
        
        # Get bank vouchers
        bank_vouchers = BankVoucherMaster.objects.filter(base_filter).values(
            'id', 'voucher_no', 'voucher_date', 'voucher_type', 'particulars',
            'cheque_no', 'cheque_date', 'amount', 'pay_to_name'
        )
        
        for voucher in bank_vouchers:
            # Check if already reconciled
            is_reconciled = BankReconciliationDetails.objects.filter(
                transaction_type='bank_voucher',
                transaction_id=voucher['id'],
                reconciliation_master=reconciliation_master
            ).exists()
            
            if show_all or not is_reconciled:
                transactions.append({
                    'type': 'bank_voucher',
                    'id': voucher['id'],
                    'transaction_no': voucher['voucher_no'],
                    'transaction_date': voucher['voucher_date'],
                    'voucher_type': voucher['voucher_type'],
                    'particulars': voucher['particulars'],
                    'cheque_no': voucher['cheque_no'],
                    'cheque_date': voucher['cheque_date'],
                    'amount': voucher['amount'],
                    'pay_to_name': voucher['pay_to_name'],
                    'is_reconciled': is_reconciled,
                })
        
        # Get contra entries
        contra_entries = ContraEntryMaster.objects.filter(
            Q(from_bank=bank) | Q(to_bank=bank),
            contra_date__range=[from_date, to_date]
        ).values(
            'id', 'contra_no', 'contra_date', 'particulars',
            'cheque_no', 'cheque_date', 'amount', 'from_bank__name', 'to_bank__name'
        )
        
        for contra in contra_entries:
            is_reconciled = BankReconciliationDetails.objects.filter(
                transaction_type='contra_entry',
                transaction_id=contra['id'],
                reconciliation_master=reconciliation_master
            ).exists()
            
            if show_all or not is_reconciled:
                transactions.append({
                    'type': 'contra_entry',
                    'id': contra['id'],
                    'transaction_no': contra['contra_no'],
                    'transaction_date': contra['contra_date'],
                    'voucher_type': 'contra',
                    'particulars': contra['particulars'],
                    'cheque_no': contra['cheque_no'],
                    'cheque_date': contra['cheque_date'],
                    'amount': contra['amount'],
                    'pay_to_name': f"{contra['from_bank__name']} → {contra['to_bank__name']}",
                    'is_reconciled': is_reconciled,
                })
        
        # Sort transactions by date
        transactions.sort(key=lambda x: x['transaction_date'], reverse=True)
        
        return transactions
    
    def reconcile_single_transaction(self, request, reconciliation_master):
        """Reconcile a single transaction"""
        transaction_type = request.POST.get('transaction_type')
        transaction_id = request.POST.get('transaction_id')
        bank_date = request.POST.get('bank_date')
        bank_amount = request.POST.get('bank_amount')
        additional_charges = request.POST.get('additional_charges', 0)
        remarks = request.POST.get('remarks', '')
        
        if not all([transaction_type, transaction_id, bank_date, bank_amount]):
            messages.error(request, 'Missing required fields for reconciliation.')
            return redirect('accounts:bank_reconciliation_detail', pk=reconciliation_master.pk)
        
        try:
            with transaction.atomic():
                # Get transaction details
                book_amount = self.get_transaction_amount(transaction_type, transaction_id)
                
                # Create reconciliation detail
                reconciliation_detail = BankReconciliationDetails.objects.create(
                    reconciliation_master=reconciliation_master,
                    transaction_type=transaction_type,
                    transaction_id=int(transaction_id),
                    transaction_no=self.get_transaction_number(transaction_type, transaction_id),
                    transaction_date=self.get_transaction_date(transaction_type, transaction_id),
                    cheque_no=self.get_transaction_cheque_no(transaction_type, transaction_id),
                    cheque_date=self.get_transaction_cheque_date(transaction_type, transaction_id),
                    book_amount=book_amount,
                    bank_amount=Decimal(bank_amount),
                    bank_date=datetime.strptime(bank_date, '%Y-%m-%d').date(),
                    additional_charges=Decimal(additional_charges),
                    difference_amount=abs(book_amount - Decimal(bank_amount)),
                    is_reconciled=True,
                    reconciled_date=date.today(),
                    remarks=remarks,
                    created_by=request.user
                )
                
                # Update reconciliation master totals
                self.update_reconciliation_totals(reconciliation_master)
                
                messages.success(request, f'Transaction {reconciliation_detail.transaction_no} reconciled successfully.')
        
        except Exception as e:
            messages.error(request, f'Error reconciling transaction: {str(e)}')
        
        return redirect('accounts:bank_reconciliation_detail', pk=reconciliation_master.pk)
    
    def bulk_reconcile_transactions(self, request, reconciliation_master):
        """Bulk reconcile multiple transactions"""
        bulk_form = BankReconciliationBulkUpdateForm(request.POST)
        
        if not bulk_form.is_valid():
            for field, errors in bulk_form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')
            return redirect('accounts:bank_reconciliation_detail', pk=reconciliation_master.pk)
        
        transaction_ids = bulk_form.cleaned_data['selected_transactions']
        bank_date = bulk_form.cleaned_data['bank_date']
        additional_charges = bulk_form.cleaned_data['additional_charges']
        remarks = bulk_form.cleaned_data['remarks']
        action = bulk_form.cleaned_data['action']
        
        try:
            with transaction.atomic():
                if action == 'reconcile':
                    reconciled_count = 0
                    for transaction_data in request.POST.getlist('selected_transactions[]'):
                        transaction_type, transaction_id = transaction_data.split(':')
                        
                        # Check if already reconciled
                        if BankReconciliationDetails.objects.filter(
                            reconciliation_master=reconciliation_master,
                            transaction_type=transaction_type,
                            transaction_id=int(transaction_id)
                        ).exists():
                            continue
                        
                        # Get transaction details
                        book_amount = self.get_transaction_amount(transaction_type, transaction_id)
                        
                        # Create reconciliation detail
                        BankReconciliationDetails.objects.create(
                            reconciliation_master=reconciliation_master,
                            transaction_type=transaction_type,
                            transaction_id=int(transaction_id),
                            transaction_no=self.get_transaction_number(transaction_type, transaction_id),
                            transaction_date=self.get_transaction_date(transaction_type, transaction_id),
                            cheque_no=self.get_transaction_cheque_no(transaction_type, transaction_id),
                            cheque_date=self.get_transaction_cheque_date(transaction_type, transaction_id),
                            book_amount=book_amount,
                            bank_amount=book_amount,  # Assume exact match for bulk
                            bank_date=bank_date,
                            additional_charges=additional_charges,
                            difference_amount=0,  # Assume exact match
                            is_reconciled=True,
                            reconciled_date=date.today(),
                            remarks=remarks,
                            created_by=request.user
                        )
                        reconciled_count += 1
                    
                    # Update reconciliation master totals
                    self.update_reconciliation_totals(reconciliation_master)
                    
                    messages.success(request, f'{reconciled_count} transactions reconciled successfully.')
                
                elif action == 'unreconcile':
                    unreconciled_count = 0
                    for transaction_data in request.POST.getlist('selected_transactions[]'):
                        transaction_type, transaction_id = transaction_data.split(':')
                        
                        deleted = BankReconciliationDetails.objects.filter(
                            reconciliation_master=reconciliation_master,
                            transaction_type=transaction_type,
                            transaction_id=int(transaction_id)
                        ).delete()
                        
                        if deleted[0] > 0:
                            unreconciled_count += 1
                    
                    # Update reconciliation master totals
                    self.update_reconciliation_totals(reconciliation_master)
                    
                    messages.success(request, f'{unreconciled_count} transactions unreconciled successfully.')
        
        except Exception as e:
            messages.error(request, f'Error in bulk reconciliation: {str(e)}')
        
        return redirect('accounts:bank_reconciliation_detail', pk=reconciliation_master.pk)
    
    def get_transaction_amount(self, transaction_type, transaction_id):
        """Get the amount for a transaction"""
        if transaction_type == 'bank_voucher':
            voucher = BankVoucherMaster.objects.get(id=transaction_id)
            return voucher.amount
        elif transaction_type == 'contra_entry':
            contra = ContraEntryMaster.objects.get(id=transaction_id)
            return contra.amount
        return Decimal('0.00')
    
    def get_transaction_number(self, transaction_type, transaction_id):
        """Get the transaction number"""
        if transaction_type == 'bank_voucher':
            voucher = BankVoucherMaster.objects.get(id=transaction_id)
            return voucher.voucher_no
        elif transaction_type == 'contra_entry':
            contra = ContraEntryMaster.objects.get(id=transaction_id)
            return contra.contra_no
        return ''
    
    def get_transaction_date(self, transaction_type, transaction_id):
        """Get the transaction date"""
        if transaction_type == 'bank_voucher':
            voucher = BankVoucherMaster.objects.get(id=transaction_id)
            return voucher.voucher_date
        elif transaction_type == 'contra_entry':
            contra = ContraEntryMaster.objects.get(id=transaction_id)
            return contra.contra_date
        return date.today()
    
    def get_transaction_cheque_no(self, transaction_type, transaction_id):
        """Get the cheque number"""
        if transaction_type == 'bank_voucher':
            voucher = BankVoucherMaster.objects.get(id=transaction_id)
            return voucher.cheque_no or ''
        elif transaction_type == 'contra_entry':
            contra = ContraEntryMaster.objects.get(id=transaction_id)
            return contra.cheque_no or ''
        return ''
    
    def get_transaction_cheque_date(self, transaction_type, transaction_id):
        """Get the cheque date"""
        if transaction_type == 'bank_voucher':
            voucher = BankVoucherMaster.objects.get(id=transaction_id)
            return voucher.cheque_date
        elif transaction_type == 'contra_entry':
            contra = ContraEntryMaster.objects.get(id=transaction_id)
            return contra.cheque_date
        return None
    
    def update_reconciliation_totals(self, reconciliation_master):
        """Update reconciliation master totals"""
        details = BankReconciliationDetails.objects.filter(
            reconciliation_master=reconciliation_master
        )
        
        reconciled_amount = details.aggregate(
            total=Sum('book_amount')
        )['total'] or Decimal('0.00')
        
        outstanding_amount = reconciliation_master.difference_amount - reconciled_amount
        
        reconciliation_master.reconciled_amount = reconciled_amount
        reconciliation_master.outstanding_amount = max(outstanding_amount, Decimal('0.00'))
        
        # Update status based on reconciliation progress
        if outstanding_amount <= 0:
            reconciliation_master.status = 'completed'
        elif reconciled_amount > 0:
            reconciliation_master.status = 'in_progress'
        else:
            reconciliation_master.status = 'pending'
        
        reconciliation_master.save()


class BankStatementUploadView(LoginRequiredMixin, View):
    """
    View for uploading bank statement files for automated reconciliation
    """
    
    def get(self, request):
        """Display bank statement upload form"""
        form = BankStatementUploadForm()
        return render(request, 'accounts/bank_statement_upload.html', {'form': form})
    
    def post(self, request):
        """Process uploaded bank statement file"""
        form = BankStatementUploadForm(request.POST, request.FILES)
        
        if not form.is_valid():
            return render(request, 'accounts/bank_statement_upload.html', {'form': form})
        
        bank = form.cleaned_data['bank']
        statement_file = form.cleaned_data['statement_file']
        statement_date = form.cleaned_data['statement_date']
        auto_reconcile = form.cleaned_data['auto_reconcile']
        
        try:
            # Process the uploaded file
            results = self.process_statement_file(
                statement_file, bank, statement_date, auto_reconcile, request.user
            )
            
            messages.success(
                request, 
                f"Bank statement processed successfully. "
                f"Imported: {results['imported']}, "
                f"Auto-reconciled: {results['auto_reconciled']}"
            )
            
            return redirect('accounts:bank_reconciliation_master_list')
        
        except Exception as e:
            messages.error(request, f'Error processing bank statement: {str(e)}')
            return render(request, 'accounts/bank_statement_upload.html', {'form': form})
    
    def process_statement_file(self, statement_file, bank, statement_date, auto_reconcile, user):
        """Process the uploaded bank statement file"""
        # This is a placeholder implementation
        # In a real system, you would parse CSV/Excel files and match transactions
        
        results = {
            'imported': 0,
            'auto_reconciled': 0,
        }
        
        # For now, just return empty results
        # Implementation would depend on bank statement format
        
        return results


# AJAX Views for Dynamic Loading

def load_unreconciled_transactions(request):
    """
    AJAX view to load unreconciled transactions for a bank and date range
    """
    bank_id = request.GET.get('bank_id')
    from_date = request.GET.get('from_date')
    to_date = request.GET.get('to_date')
    voucher_type = request.GET.get('voucher_type', 'all')
    
    if not all([bank_id, from_date, to_date]):
        return JsonResponse({'error': 'Missing required parameters'}, status=400)
    
    try:
        bank = Bank.objects.get(id=bank_id)
        
        # Parse dates
        from_date = datetime.strptime(from_date, '%Y-%m-%d').date()
        to_date = datetime.strptime(to_date, '%Y-%m-%d').date()
        
        transactions = []
        
        # Get bank vouchers
        bank_vouchers = BankVoucherMaster.objects.filter(
            bank=bank,
            voucher_date__range=[from_date, to_date]
        )
        
        if voucher_type != 'all':
            bank_vouchers = bank_vouchers.filter(voucher_type=voucher_type)
        
        for voucher in bank_vouchers:
            transactions.append({
                'type': 'bank_voucher',
                'id': voucher.id,
                'transaction_no': voucher.voucher_no,
                'transaction_date': voucher.voucher_date.strftime('%Y-%m-%d'),
                'particulars': voucher.particulars,
                'amount': float(voucher.amount),
                'cheque_no': voucher.cheque_no or '',
                'pay_to_name': voucher.pay_to_name,
            })
        
        # Get contra entries
        contra_entries = ContraEntryMaster.objects.filter(
            Q(from_bank=bank) | Q(to_bank=bank),
            contra_date__range=[from_date, to_date]
        )
        
        for contra in contra_entries:
            transactions.append({
                'type': 'contra_entry',
                'id': contra.id,
                'transaction_no': contra.contra_no,
                'transaction_date': contra.contra_date.strftime('%Y-%m-%d'),
                'particulars': contra.particulars,
                'amount': float(contra.amount),
                'cheque_no': contra.cheque_no or '',
                'pay_to_name': f"{contra.from_bank.name} → {contra.to_bank.name}",
            })
        
        return JsonResponse({'transactions': transactions})
    
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


def export_reconciliation_report(request):
    """
    Export reconciliation report as CSV
    """
    reconciliation_id = request.GET.get('reconciliation_id')
    
    if not reconciliation_id:
        return JsonResponse({'error': 'Reconciliation ID required'}, status=400)
    
    try:
        reconciliation = BankReconciliationMaster.objects.get(id=reconciliation_id)
        details = BankReconciliationDetails.objects.filter(
            reconciliation_master=reconciliation
        ).order_by('transaction_date')
        
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="bank_reconciliation_{reconciliation.reconciliation_no}.csv"'
        
        writer = csv.writer(response)
        writer.writerow([
            'Transaction No', 'Transaction Date', 'Transaction Type',
            'Cheque No', 'Cheque Date', 'Book Amount', 'Bank Amount',
            'Bank Date', 'Additional Charges', 'Difference Amount',
            'Status', 'Remarks'
        ])
        
        for detail in details:
            writer.writerow([
                detail.transaction_no,
                detail.transaction_date.strftime('%Y-%m-%d'),
                detail.transaction_type.replace('_', ' ').title(),
                detail.cheque_no or '',
                detail.cheque_date.strftime('%Y-%m-%d') if detail.cheque_date else '',
                detail.book_amount,
                detail.bank_amount,
                detail.bank_date.strftime('%Y-%m-%d') if detail.bank_date else '',
                detail.additional_charges,
                detail.difference_amount,
                'Reconciled' if detail.is_reconciled else 'Not Reconciled',
                detail.remarks or ''
            ])
        
        return response
    
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)