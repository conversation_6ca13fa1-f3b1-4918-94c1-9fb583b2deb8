<!-- accounts/partials/bank_table.html -->
<!-- HTMX partial for Bank table - SAP S/4HANA inspired -->
<!-- Replaces ASP.NET GridView with modern responsive table -->

{% load static %}

<div class="overflow-hidden">
    {% if banks %}
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-sap-gray-200">
            <thead class="bg-sap-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        Bank Name
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        Address
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        Location
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        Contact Details
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        IFSC Code
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-sap-gray-200" id="bank-tbody">
                {% for bank in banks %}
                <tr class="hover:bg-sap-gray-50 transition-colors duration-150" id="bank-row-{{ bank.id }}">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-sap-green-100 rounded-lg flex items-center justify-center mr-3">
                                <i data-lucide="landmark" class="w-4 h-4 text-sap-green-600"></i>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-sap-gray-900">{{ bank.name|default:"N/A" }}</div>
                                <div class="text-xs text-sap-gray-500">ID: {{ bank.id }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4">
                        <div class="text-sm text-sap-gray-900 max-w-xs">
                            {{ bank.address|default:"N/A"|truncatechars:80 }}
                        </div>
                        {% if bank.pin_no %}
                        <div class="text-xs text-sap-gray-500">PIN: {{ bank.pin_no }}</div>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-sap-gray-900">
                            {% if bank.city %}{{ bank.city.city_name }}{% else %}N/A{% endif %}
                        </div>
                        <div class="text-xs text-sap-gray-500">
                            {% if bank.state %}{{ bank.state.state_name }}, {% endif %}
                            {% if bank.country %}{{ bank.country.country_name }}{% else %}N/A{% endif %}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-sap-gray-900">
                            {% if bank.contact_no %}
                                <div class="flex items-center">
                                    <i data-lucide="phone" class="w-3 h-3 mr-1 text-sap-gray-400"></i>
                                    {{ bank.contact_no }}
                                </div>
                            {% endif %}
                        </div>
                        {% if bank.fax_no %}
                        <div class="text-xs text-sap-gray-500 flex items-center">
                            <i data-lucide="printer" class="w-3 h-3 mr-1 text-sap-gray-400"></i>
                            {{ bank.fax_no }}
                        </div>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-mono bg-sap-blue-100 text-sap-blue-800 rounded">
                            {{ bank.ifsc|default:"N/A" }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex justify-end space-x-2">
                            <!-- Edit Button -->
                            <button type="button"
                                    hx-get="{% url 'accounts:bank_edit' bank.id %}"
                                    hx-target="#bank-row-{{ bank.id }}"
                                    hx-swap="outerHTML"
                                    class="inline-flex items-center px-3 py-1.5 border border-sap-blue-300 rounded text-xs font-medium text-sap-blue-700 bg-sap-blue-50 hover:bg-sap-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-blue-500 transition-colors duration-200">
                                <i data-lucide="edit" class="w-3 h-3 mr-1"></i>
                                Edit
                            </button>
                            <!-- Delete Button -->
                            <button type="button"
                                    hx-delete="{% url 'accounts:bank_delete' bank.id %}"
                                    hx-target="#bank-row-{{ bank.id }}"
                                    hx-swap="outerHTML"
                                    hx-confirm="Are you sure you want to delete this bank? This action cannot be undone."
                                    class="inline-flex items-center px-3 py-1.5 border border-sap-red-300 rounded text-xs font-medium text-sap-red-700 bg-sap-red-50 hover:bg-sap-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-red-500 transition-colors duration-200">
                                <i data-lucide="trash-2" class="w-3 h-3 mr-1"></i>
                                Delete
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="text-center py-12">
        <div class="w-16 h-16 bg-sap-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i data-lucide="landmark" class="w-8 h-8 text-sap-gray-400"></i>
        </div>
        <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No banks found</h3>
        <p class="text-sm text-sap-gray-600 mb-4">
            {% if request.GET.search %}
                Try adjusting your search criteria.
            {% else %}
                Get started by creating your first bank entry.
            {% endif %}
        </p>
        {% if request.GET.search %}
        <button type="button" 
                onclick="clearSearch()"
                class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
            <i data-lucide="x" class="w-4 h-4 mr-2"></i>
            Clear Search
        </button>
        {% endif %}
    </div>
    {% endif %}
</div>