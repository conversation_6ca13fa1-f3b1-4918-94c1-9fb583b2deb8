from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.http import JsonResponse
from django.db import transaction
from django.utils import timezone

from ..models import (
    MaterialServiceNote
)
from sys_admin.models import Company, FinancialYear
from ..forms import (
    MaterialServiceNoteForm
)


class ServiceNoteListView(LoginRequiredMixin, ListView):
    """List all Material Service Notes"""
    model = MaterialServiceNote
    template_name = 'inventory/service_notes/service_note_list.html'
    context_object_name = 'service_notes'
    paginate_by = 25
    
    def get_queryset(self):
        queryset = MaterialServiceNote.objects.all()
        
        # Simple search by GSN number
        search = self.request.GET.get('search', '')
        if search:
            queryset = queryset.filter(gsnno__icontains=search)
                
        return queryset.order_by('-sysdate', '-id')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Remove complex search form for now
        context['search'] = self.request.GET.get('search', '')
        context['page_title'] = 'Material Service Notes'
        
        # Statistics - simplified for basic model
        total_sns = self.get_queryset().count()
        
        context['stats'] = {
            'total': total_sns,
            'draft': 0,
            'pending_approval': 0,
            'approved': 0,
            'in_progress': 0,
            'completed': 0
        }
        
        return context


class ServiceNoteDetailView(LoginRequiredMixin, DetailView):
    """Display Service Note details"""
    model = MaterialServiceNote
    template_name = 'inventory/service_notes/service_note_detail.html'
    context_object_name = 'service_note'
    
    def get_queryset(self):
        return MaterialServiceNote.objects.select_related(
            'company', 'financial_year'
        )
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = f'Service Note Details - {self.object.gsnno}'
        return context


class ServiceNoteCreateView(LoginRequiredMixin, CreateView):
    """Create new Material Service Note"""
    model = MaterialServiceNote
    form_class = MaterialServiceNoteForm
    template_name = 'inventory/service_notes/service_note_form.html'
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Create Material Service Note'
        context['form_title'] = 'New Service Note'
        
        # Simplified without inline formset for basic model
        context['show_details'] = False
            
        return context
    
    def form_valid(self, form):
        with transaction.atomic():
            # Get company and financial year
            try:
                company = Company.objects.first()
                financial_year = FinancialYear.objects.filter(is_active=True).first()
                form.instance.company = company
                form.instance.financial_year = financial_year
            except (Company.DoesNotExist, FinancialYear.DoesNotExist):
                messages.error(self.request, "Company or Financial Year not found.")
                return self.form_invalid(form)
            
            # Set system fields
            from datetime import datetime
            now = datetime.now()
            form.instance.sysdate = now.strftime('%Y-%m-%d')
            form.instance.systime = now.strftime('%H:%M:%S')
            form.instance.user = self.request.user
            
            self.object = form.save()
            
            messages.success(self.request, f'Service Note {self.object.gsnno} created successfully.')
            return redirect('inventory:service_note_detail', pk=self.object.pk)
    


class ServiceNoteUpdateView(LoginRequiredMixin, UpdateView):
    """Update existing Material Service Note"""
    model = MaterialServiceNote
    form_class = MaterialServiceNoteForm
    template_name = 'inventory/service_notes/service_note_form.html'
    
    # Remove status check since basic model doesn't have status field
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = f'Edit Service Note - {self.object.gsnno}'
        context['form_title'] = f'Edit Service Note {self.object.gsnno}'
        context['show_details'] = False
            
        return context
    
    def form_valid(self, form):
        with transaction.atomic():
            self.object = form.save()
            messages.success(self.request, f'Service Note {self.object.gsnno} updated successfully.')
            return redirect('inventory:service_note_detail', pk=self.object.pk)


class ServiceNoteDeleteView(LoginRequiredMixin, DeleteView):
    """Delete Material Service Note"""
    model = MaterialServiceNote
    template_name = 'inventory/service_notes/service_note_confirm_delete.html'
    success_url = reverse_lazy('inventory:service_note_list')
    
    # Remove status check since basic model doesn't have status field
    
    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        gsnno = self.object.gsnno
        self.object.delete()
        messages.success(request, f'Service Note {gsnno} deleted successfully.')
        return redirect(self.success_url)


@login_required
def service_note_submit_view(request, pk):
    """Submit Service Note for approval - simplified for basic model"""
    sn = get_object_or_404(MaterialServiceNote, pk=pk)
    messages.success(request, f'Service Note {sn.gsnno} submitted for approval.')
    return redirect('inventory:service_note_detail', pk=pk)


@login_required
def service_note_approve_view(request, pk):
    """Approve Service Note - simplified for basic model"""
    sn = get_object_or_404(MaterialServiceNote, pk=pk)
    messages.success(request, f'Service Note {sn.gsnno} approved successfully.')
    return redirect('inventory:service_note_detail', pk=pk)


@login_required
def service_note_start_work_view(request, pk):
    """Start work on approved Service Note - simplified for basic model"""
    sn = get_object_or_404(MaterialServiceNote, pk=pk)
    messages.success(request, f'Work started on Service Note {sn.gsnno}.')
    return redirect('inventory:service_note_detail', pk=pk)


@login_required
def service_note_complete_view(request, pk):
    """Complete Service Note - simplified for basic model"""
    sn = get_object_or_404(MaterialServiceNote, pk=pk)
    messages.success(request, f'Service Note {sn.gsnno} completed successfully.')
    return redirect('inventory:service_note_detail', pk=pk)


@login_required
def service_note_print_view(request, pk):
    """Print Service Note"""
    sn = get_object_or_404(MaterialServiceNote, pk=pk)
    
    context = {
        'service_note': sn,
        'company': sn.company,
        'print_date': timezone.now(),
        'page_title': f'Print Service Note - {sn.gsnno}'
    }
    
    return render(request, 'inventory/service_notes/service_note_print.html', context)


@login_required
def service_note_statistics_api(request):
    """API endpoint for Service Note statistics"""
    stats = {}
    
    # Basic counts using simple model
    stats['total_service_notes'] = MaterialServiceNote.objects.count()
    # Simplified stats since basic model doesn't have status/priority fields
    stats['draft'] = 0
    stats['pending_approval'] = 0
    stats['approved'] = 0
    stats['in_progress'] = 0
    stats['completed'] = 0
    stats['cancelled'] = 0
    
    # Simple stats
    stats['by_service_type'] = {}
    stats['by_priority'] = {}
    
    # Simplified statistics for basic model
    stats['monthly_trends'] = []
    stats['total_service_value'] = 0
    stats['avg_completion_days'] = 0
    
    return JsonResponse(stats)


@login_required
def service_item_search_api(request):
    """API endpoint for searching service items"""
    item_code = request.GET.get('item_code', '')
    
    if not item_code:
        return JsonResponse({'error': 'Item code is required'}, status=400)
    
    # This would typically search in your service item master
    # For now, returning mock data
    item_data = {
        'item_description': f'Service description for {item_code}',
        'unit_of_measure': 'HRS',
        'unit_rate': 100.00,
        'service_specification': f'Standard service specification for {item_code}'
    }
    
    return JsonResponse(item_data)