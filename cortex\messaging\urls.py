from django.urls import path
from . import views

app_name = 'messaging'

urlpatterns = [
    # Dashboard
    path('', views.MessagingDashboardView.as_view(), name='dashboard'),
    
    # Chat Room Management URLs
    path('rooms/', views.ChatRoomListView.as_view(), name='room_list'),
    path('rooms/create/', views.ChatRoomCreateView.as_view(), name='room_create'),
    path('rooms/<int:pk>/', views.ChatRoomDetailView.as_view(), name='room_detail'),
    path('rooms/<int:pk>/join/', views.join_room, name='join_room'),
    path('rooms/<int:pk>/leave/', views.leave_room, name='leave_room'),
    
    # Private Messaging URLs
    path('private/', views.PrivateMessageListView.as_view(), name='private_list'),
    path('private/<int:pk>/', views.PrivateMessageDetailView.as_view(), name='private_detail'),
    path('private/start/', views.start_private_chat, name='start_private_chat'),
    
    # User Profile Management
    path('profile/', views.ChatUserUpdateView.as_view(), name='profile_update'),
    
    # HTMX API URLs for Real-time Updates
    path('api/room-messages/<int:room_id>/', views.load_room_messages, name='load_room_messages'),
    path('api/private-messages/<int:user_id>/', views.load_private_messages, name='load_private_messages'),
    path('api/online-users/', views.online_users, name='online_users'),
    path('api/search-messages/', views.search_messages, name='search_messages'),
    path('api/messaging-stats/', views.messaging_stats, name='messaging_stats'),
]