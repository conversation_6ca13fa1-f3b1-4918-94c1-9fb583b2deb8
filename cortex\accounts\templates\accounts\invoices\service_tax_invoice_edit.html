<!-- accounts/templates/accounts/invoices/service_tax_invoice_edit.html -->
<!-- Service Tax Invoice Edit Form Template -->
<!-- ASP.NET Source: Module/Accounts/Transactions/ServiceTaxInvoice_Edit.aspx -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Edit Service Tax Invoice - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-red-600 to-sap-red-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="edit-3" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">
                        Edit Service Tax Invoice
                    </h1>
                    <p class="text-sm text-sap-gray-600 mt-1">
                        Modify service tax invoice with compliance tracking
                    </p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <!-- Compliance Status Badge -->
                <span class="px-3 py-1 rounded-full text-xs font-medium
                    {% if object.compliance_status == 'pending' %}bg-sap-yellow-100 text-sap-yellow-800{% endif %}
                    {% if object.compliance_status == 'compliant' %}bg-sap-emerald-100 text-sap-emerald-800{% endif %}
                    {% if object.compliance_status == 'under_review' %}bg-sap-blue-100 text-sap-blue-800{% endif %}
                    {% if object.compliance_status == 'exempted' %}bg-sap-gray-100 text-sap-gray-800{% endif %}">
                    {{ object.get_compliance_status_display|default:"Pending Review" }}
                </span>
                <a href="{% url 'accounts:service_tax_invoice_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Edit Service Tax Invoice Form -->
    <div class="max-w-7xl mx-auto">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                            <i data-lucide="edit-3" class="w-5 h-5 mr-2 text-sap-red-600"></i>
                            Service Tax Invoice Amendment
                        </h3>
                        <p class="text-sm text-sap-gray-600 mt-1">Update service details with compliance impact assessment</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="showComplianceImpact()" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="shield-alert" class="w-4 h-4 inline mr-2"></i>
                            Compliance Impact
                        </button>
                        <button type="button" onclick="showAmendmentHistory()" 
                                class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="history" class="w-4 h-4 inline mr-2"></i>
                            Amendment History
                        </button>
                    </div>
                </div>
            </div>
            
            <form method="post" id="service-tax-edit-form" class="p-6" x-data="serviceTaxEditForm()" hx-post="{% url 'accounts:service_tax_invoice_edit' object.id %}" hx-target="#form-messages">
                {% csrf_token %}
                
                <!-- Form Messages -->
                <div id="form-messages" class="mb-4"></div>
                
                <!-- Amendment Alert -->
                <div class="mb-6 bg-sap-red-50 border border-sap-red-200 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-sap-red-800 mb-2 flex items-center">
                        <i data-lucide="alert-circle" class="w-4 h-4 mr-2"></i>
                        Service Tax Amendment Process
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                        <div>
                            <span class="text-sap-red-600">Original Date:</span>
                            <span class="font-medium text-sap-red-800">{{ object.created_at|date:"M d, Y" }}</span>
                        </div>
                        <div>
                            <span class="text-sap-red-600">Service Tax Period:</span>
                            <span class="font-medium text-sap-red-800">{{ object.service_tax_period|default:"Q1" }}</span>
                        </div>
                        <div>
                            <span class="text-sap-red-600">Financial Year:</span>
                            <span class="font-medium text-sap-red-800">{{ object.financial_year|default:"2024-25" }}</span>
                        </div>
                        <div>
                            <span class="text-sap-red-600">Amendment Count:</span>
                            <span class="font-medium text-sap-red-800">{{ object.amendment_count|default:"0" }}</span>
                        </div>
                    </div>
                    <div class="mt-3 p-3 bg-sap-red-100 rounded border">
                        <p class="text-xs text-sap-red-700">
                            <strong>Important:</strong> Service tax amendments may require regulatory notifications and impact quarterly filings. 
                            Ensure all changes comply with applicable service tax regulations.
                        </p>
                    </div>
                </div>
                
                <!-- Service Provider Information -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="building" class="w-5 h-5 mr-2 text-sap-red-600"></i>
                        Service Provider Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Service Provider Name -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Service Provider Name *
                            </label>
                            <input type="text" name="service_provider_name" required value="{{ object.service_provider_name|default:'' }}"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-red-500 focus:border-sap-red-500"
                                   placeholder="Enter service provider name">
                        </div>
                        
                        <!-- Service Category -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Service Category *
                            </label>
                            <select name="service_category" required
                                    class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-red-500 focus:border-sap-red-500">
                                <option value="">Select service category</option>
                                <option value="consulting" {% if object.service_category == 'consulting' %}selected{% endif %}>Consulting Services</option>
                                <option value="technical" {% if object.service_category == 'technical' %}selected{% endif %}>Technical Services</option>
                                <option value="maintenance" {% if object.service_category == 'maintenance' %}selected{% endif %}>Maintenance Services</option>
                                <option value="transport" {% if object.service_category == 'transport' %}selected{% endif %}>Transport Services</option>
                                <option value="construction" {% if object.service_category == 'construction' %}selected{% endif %}>Construction Services</option>
                                <option value="manpower" {% if object.service_category == 'manpower' %}selected{% endif %}>Manpower Services</option>
                                <option value="other" {% if object.service_category == 'other' %}selected{% endif %}>Other Services</option>
                            </select>
                        </div>
                        
                        <!-- Place of Service -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Place of Service *
                            </label>
                            <input type="text" name="place_of_service" required value="{{ object.place_of_service|default:'' }}"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-red-500 focus:border-sap-red-500"
                                   placeholder="Enter place where service is provided">
                        </div>
                        
                        <!-- Service Registration Number -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Service Tax Registration Number
                            </label>
                            <input type="text" name="service_tax_reg_no" value="{{ object.service_tax_reg_no|default:'' }}"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-red-500 focus:border-sap-red-500"
                                   placeholder="Service tax registration number">
                        </div>
                        
                        <!-- Service Date -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Service Date *
                            </label>
                            <input type="date" name="service_date" required value="{{ object.service_date|date:'Y-m-d' }}"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-red-500 focus:border-sap-red-500">
                        </div>
                        
                        <!-- Invoice Date -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Invoice Date *
                            </label>
                            <input type="date" name="invoice_date" required value="{{ object.invoice_date|date:'Y-m-d' }}"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-red-500 focus:border-sap-red-500">
                        </div>
                    </div>
                </div>
                
                <!-- Tax Rate Changes Impact -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="trending-up" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                        Tax Rate Changes Handling
                        <button type="button" @click="checkTaxRateUpdates()" 
                                class="ml-auto bg-sap-orange-100 hover:bg-sap-orange-200 text-sap-orange-700 px-3 py-1 rounded text-sm font-medium transition-colors">
                            <i data-lucide="refresh-cw" class="w-3 h-3 inline mr-1"></i>
                            Check Updates
                        </button>
                    </h4>
                    <div class="bg-sap-orange-50 border border-sap-orange-200 rounded-lg p-4">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-sap-orange-700 mb-1">
                                    Original Tax Rate
                                </label>
                                <input type="text" readonly value="{{ object.original_tax_rate|default:'12.36' }}%"
                                       class="block w-full px-3 py-2 border border-sap-orange-300 rounded-lg text-sm bg-sap-orange-100">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-sap-orange-700 mb-1">
                                    Current Applicable Rate
                                </label>
                                <select name="current_tax_rate" @change="calculateTaxRateImpact()"
                                        class="block w-full px-3 py-2 border border-sap-orange-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
                                    <option value="12.36">12.36%</option>
                                    <option value="14">14%</option>
                                    <option value="15">15%</option>
                                    <option value="18">18%</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-sap-orange-700 mb-1">
                                    Rate Change Impact
                                </label>
                                <span class="block w-full px-3 py-2 border border-sap-orange-300 rounded-lg text-sm bg-sap-orange-100" x-text="taxRateImpact"></span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Service Details Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="clipboard-list" class="w-5 h-5 mr-2 text-sap-emerald-600"></i>
                        Updated Service Details
                    </h4>
                    
                    <!-- Services Table -->
                    <div class="border border-sap-gray-300 rounded-lg overflow-hidden">
                        <table class="min-w-full divide-y divide-sap-gray-200" id="services-table">
                            <thead class="bg-sap-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Service Description</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Service Value</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Service Tax Rate</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Service Tax Amount</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Total Amount</th>
                                    <th class="px-4 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Action</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-sap-gray-200" x-ref="servicesTableBody">
                                <template x-for="(service, index) in services" :key="index">
                                    <tr>
                                        <td class="px-4 py-3">
                                            <input type="text" x-model="service.description" 
                                                   class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-red-500" 
                                                   placeholder="Service description">
                                        </td>
                                        <td class="px-4 py-3">
                                            <input type="number" x-model="service.value" @input="calculateServiceTax(index)" 
                                                   class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-red-500" 
                                                   placeholder="Service value" min="0" step="0.01">
                                        </td>
                                        <td class="px-4 py-3">
                                            <select x-model="service.tax_rate" @change="calculateServiceTax(index)" 
                                                    class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-red-500">
                                                <option value="12.36">12.36%</option>
                                                <option value="14">14%</option>
                                                <option value="15">15%</option>
                                                <option value="18">18%</option>
                                            </select>
                                        </td>
                                        <td class="px-4 py-3">
                                            <span class="text-sm font-medium text-sap-gray-900" x-text="'₹' + (service.tax_amount || 0).toFixed(2)"></span>
                                        </td>
                                        <td class="px-4 py-3">
                                            <span class="text-sm font-medium text-sap-gray-900" x-text="'₹' + (service.total_amount || 0).toFixed(2)"></span>
                                        </td>
                                        <td class="px-4 py-3 text-center">
                                            <button type="button" @click="removeService(index)" 
                                                    class="text-sap-red-600 hover:text-sap-red-900">
                                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                        
                        <!-- Add Service Button -->
                        <div class="px-4 py-3 bg-sap-gray-50 border-t border-sap-gray-200">
                            <button type="button" @click="addService()" 
                                    class="bg-sap-emerald-600 hover:bg-sap-emerald-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                                Add Service
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Compliance Documentation -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="file-text" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Amendment Documentation
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Amendment Reason -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Amendment Reason *
                            </label>
                            <select name="amendment_reason" required
                                    class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                                <option value="">Select amendment reason</option>
                                <option value="tax_rate_change">Tax Rate Changes</option>
                                <option value="service_value_correction">Service Value Correction</option>
                                <option value="classification_change">Service Classification Change</option>
                                <option value="compliance_requirement">Compliance Requirement</option>
                                <option value="clerical_error">Clerical Error Correction</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        
                        <!-- Compliance Impact -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Compliance Status Update
                            </label>
                            <select name="compliance_status"
                                    class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                                <option value="pending" {% if object.compliance_status == 'pending' %}selected{% endif %}>Pending Review</option>
                                <option value="compliant" {% if object.compliance_status == 'compliant' %}selected{% endif %}>Compliant</option>
                                <option value="under_review" {% if object.compliance_status == 'under_review' %}selected{% endif %}>Under Review</option>
                                <option value="exempted" {% if object.compliance_status == 'exempted' %}selected{% endif %}>Exempted</option>
                            </select>
                        </div>
                        
                        <!-- Amendment Comments -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Amendment Comments *
                            </label>
                            <textarea rows="3" name="amendment_comments" required
                                      placeholder="Provide detailed comments explaining the amendment reasons and compliance implications..."
                                      class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500 resize-none"></textarea>
                        </div>
                    </div>
                </div>
                
                <!-- Updated Total Summary -->
                <div class="mb-8 bg-sap-red-50 border border-sap-red-200 rounded-lg p-6">
                    <h4 class="text-lg font-medium text-sap-red-800 mb-4 flex items-center">
                        <i data-lucide="receipt" class="w-5 h-5 mr-2 text-sap-red-600"></i>
                        Updated Invoice Summary
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
                        <div>
                            <p class="text-sm text-sap-red-600">Total Service Value</p>
                            <p class="text-xl font-bold text-sap-red-800" x-text="'₹' + totalServiceValue.toFixed(2)"></p>
                        </div>
                        <div>
                            <p class="text-sm text-sap-red-600">Total Service Tax</p>
                            <p class="text-xl font-bold text-sap-red-800" x-text="'₹' + totalServiceTax.toFixed(2)"></p>
                        </div>
                        <div>
                            <p class="text-sm text-sap-red-600">Amendment Impact</p>
                            <p class="text-xl font-bold text-sap-red-800" x-text="'₹' + amendmentImpact.toFixed(2)"></p>
                        </div>
                        <div>
                            <p class="text-sm text-sap-red-600">Net Amount</p>
                            <p class="text-2xl font-bold text-sap-red-800" x-text="'₹' + netAmount.toFixed(2)"></p>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="resetForm()" 
                                class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="rotate-ccw" class="w-4 h-4 inline mr-2"></i>
                            Reset Changes
                        </button>
                        <button type="button" @click="validateAmendment()" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="shield-check" class="w-4 h-4 inline mr-2"></i>
                            Validate Amendment
                        </button>
                        <button type="button" @click="previewAmendment()" 
                                class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="eye" class="w-4 h-4 inline mr-2"></i>
                            Preview Changes
                        </button>
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="{% url 'accounts:service_tax_invoice_list' %}" 
                           class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-sap-red-600 hover:bg-sap-red-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                            Save Amendment
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Amendment Guidelines -->
        <div class="mt-6 bg-sap-red-50 border border-sap-red-200 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i data-lucide="info" class="w-5 h-5 text-sap-red-600"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-sap-red-800">Service Tax Amendment Guidelines</h3>
                    <div class="mt-2 text-sm text-sap-red-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Service tax amendments require regulatory compliance documentation</li>
                            <li>Tax rate changes must align with applicable regulations and notification dates</li>
                            <li>Amendment impact analysis helps assess quarterly filing implications</li>
                            <li>Compliance status updates trigger workflow notifications to relevant authorities</li>
                            <li>All amendments are tracked with full audit trail for regulatory reporting</li>
                            <li>Amendment frequency may be subject to regulatory scrutiny</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Compliance Impact Modal -->
<div id="compliance-impact-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-96 overflow-hidden">
            <div class="px-6 py-4 border-b border-sap-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-sap-gray-800">Compliance Impact Assessment</h3>
                    <button type="button" onclick="closeComplianceImpact()" class="text-sap-gray-400 hover:text-sap-gray-600">
                        <i data-lucide="x" class="w-5 h-5"></i>
                    </button>
                </div>
            </div>
            <div class="p-6 overflow-y-auto max-h-80">
                <div id="compliance-impact-content">
                    <!-- Compliance impact assessment will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function serviceTaxEditForm() {
    return {
        services: [
            {
                description: '{{ object.service_description|default:"" }}',
                value: {{ object.service_value|default:0 }},
                tax_rate: {{ object.service_tax_rate|default:12.36 }},
                tax_amount: 0,
                total_amount: 0
            }
        ],
        totalServiceValue: 0,
        totalServiceTax: 0,
        amendmentImpact: 0,
        netAmount: 0,
        taxRateImpact: 'No Change',
        originalTotalAmount: {{ object.total_amount|default:0 }},
        
        init() {
            this.calculateTotals();
            this.services.forEach((service, index) => {
                this.calculateServiceTax(index);
            });
        },
        
        addService() {
            this.services.push({
                description: '',
                value: 0,
                tax_rate: 12.36,
                tax_amount: 0,
                total_amount: 0
            });
        },
        
        removeService(index) {
            if (this.services.length > 1) {
                this.services.splice(index, 1);
                this.calculateTotals();
            }
        },
        
        calculateServiceTax(index) {
            const service = this.services[index];
            const value = parseFloat(service.value) || 0;
            const taxRate = parseFloat(service.tax_rate) || 0;
            
            service.tax_amount = value * (taxRate / 100);
            service.total_amount = value + service.tax_amount;
            
            this.calculateTotals();
        },
        
        calculateTotals() {
            this.totalServiceValue = 0;
            this.totalServiceTax = 0;
            
            this.services.forEach(service => {
                const value = parseFloat(service.value) || 0;
                const taxAmount = parseFloat(service.tax_amount) || 0;
                
                this.totalServiceValue += value;
                this.totalServiceTax += taxAmount;
            });
            
            this.netAmount = this.totalServiceValue + this.totalServiceTax;
            this.amendmentImpact = this.netAmount - this.originalTotalAmount;
        },
        
        calculateTaxRateImpact() {
            const currentRate = parseFloat(document.querySelector('select[name="current_tax_rate"]').value);
            const originalRate = {{ object.original_tax_rate|default:12.36 }};
            
            if (currentRate > originalRate) {
                this.taxRateImpact = `+${(currentRate - originalRate).toFixed(2)}% Increase`;
            } else if (currentRate < originalRate) {
                this.taxRateImpact = `${(currentRate - originalRate).toFixed(2)}% Decrease`;
            } else {
                this.taxRateImpact = 'No Change';
            }
        },
        
        checkTaxRateUpdates() {
            alert('Tax rate update check functionality would validate against current regulations.');
        },
        
        validateAmendment() {
            const amendmentReason = document.querySelector('select[name="amendment_reason"]').value;
            const amendmentComments = document.querySelector('textarea[name="amendment_comments"]').value;
            
            if (!amendmentReason) {
                alert('Please select an amendment reason to proceed.');
                return;
            }
            
            if (!amendmentComments.trim()) {
                alert('Please provide detailed amendment comments for compliance documentation.');
                return;
            }
            
            if (this.amendmentImpact !== 0) {
                const confirmMsg = `Amendment will ${this.amendmentImpact > 0 ? 'increase' : 'decrease'} invoice amount by ₹${Math.abs(this.amendmentImpact).toFixed(2)}. Continue?`;
                if (!confirm(confirmMsg)) {
                    return;
                }
            }
            
            alert('Amendment validation passed. All compliance requirements met.');
        },
        
        previewAmendment() {
            if (this.netAmount <= 0) {
                alert('Please enter valid service amounts to preview amendment.');
                return;
            }
            
            alert('Amendment preview functionality would show before/after comparison.');
        }
    }
}

function showComplianceImpact() {
    document.getElementById('compliance-impact-modal').classList.remove('hidden');
    document.getElementById('compliance-impact-content').innerHTML = `
        <div class="space-y-4">
            <div class="bg-sap-blue-50 p-4 rounded-lg">
                <h4 class="font-medium text-sap-blue-800 mb-2">Quarterly Filing Impact</h4>
                <p class="text-sm text-sap-blue-700">This amendment will affect Q{{ object.service_tax_period|slice:"1:" }} {{ object.financial_year }} service tax return filing.</p>
            </div>
            <div class="bg-sap-yellow-50 p-4 rounded-lg">
                <h4 class="font-medium text-sap-yellow-800 mb-2">Regulatory Notifications</h4>
                <p class="text-sm text-sap-yellow-700">Amendment may require notification to service tax authorities within prescribed timelines.</p>
            </div>
            <div class="bg-sap-emerald-50 p-4 rounded-lg">
                <h4 class="font-medium text-sap-emerald-800 mb-2">Documentation Requirements</h4>
                <p class="text-sm text-sap-emerald-700">Maintain proper documentation including amendment reason, supporting documents, and audit trail.</p>
            </div>
        </div>
    `;
}

function closeComplianceImpact() {
    document.getElementById('compliance-impact-modal').classList.add('hidden');
}

function showAmendmentHistory() {
    alert('Amendment history functionality would show detailed change log.');
}

function resetForm() {
    if (confirm('Are you sure you want to reset all changes? This will reload the original invoice data.')) {
        location.reload();
    }
}

document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}