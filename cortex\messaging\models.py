from django.db import models
from django.contrib.auth.models import User


class Room(models.Model):
    """Chat rooms for messaging."""
    room_id = models.AutoField(db_column="RoomID", primary_key=True)
    name = models.TextField(db_column="Name")

    class Meta:
        managed = False
        db_table = "Room"

    def __str__(self):
        return self.name


class LoggedInUser(models.Model):
    """Users currently logged into chat rooms."""
    logged_in_user_id = models.AutoField(db_column="LoggedInUserID", primary_key=True)
    user_id = models.IntegerField(db_column="UserID")
    room = models.ForeignKey("Room", models.DO_NOTHING, db_column="RoomID")

    class Meta:
        managed = False
        db_table = "LoggedInUser"


class Message(models.Model):
    """Chat messages."""
    message_id = models.AutoField(db_column="MessageID", primary_key=True)
    room = models.ForeignKey("Room", models.DO_NOTHING, db_column="RoomID", blank=True, null=True)
    user_id = models.IntegerField(db_column="UserID")
    to_user_id = models.IntegerField(db_column="ToUserID", blank=True, null=True)
    text = models.TextField(db_column="Text")
    timestamp = models.TextField(db_column="TimeStamp")
    color = models.TextField(db_column="Color", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "Message"

    def __str__(self):
        return f"Message {self.message_id} in {self.room.name if self.room else 'Private'}"


class PrivateMessage(models.Model):
    """Private message invitations."""
    private_message_id = models.AutoField(db_column="PrivateMessageID", primary_key=True)
    user_id = models.IntegerField(db_column="UserID")
    to_user_id = models.IntegerField(db_column="ToUserID")

    class Meta:
        managed = False
        db_table = "PrivateMessage"


# Extended models for better Django integration
class ChatUser(models.Model):
    """Extended user model for chat functionality."""
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    employee_name = models.CharField(max_length=200)
    emp_id = models.CharField(max_length=50, unique=True)
    gender = models.CharField(max_length=1, choices=[('M', 'Male'), ('F', 'Female')], default='M')
    is_online = models.BooleanField(default=False)
    last_seen = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.employee_name} ({self.emp_id})"

    class Meta:
        db_table = "chat_users"


class ChatRoom(models.Model):
    """Enhanced chat room model."""
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    max_users = models.IntegerField(default=50)

    def __str__(self):
        return self.name

    @property
    def online_users_count(self):
        return self.logged_users.count()

    class Meta:
        db_table = "chat_rooms"


class ChatMessage(models.Model):
    """Enhanced message model with better Django integration."""
    room = models.ForeignKey(ChatRoom, on_delete=models.CASCADE, related_name='messages', blank=True, null=True)
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_messages')
    recipient = models.ForeignKey(User, on_delete=models.CASCADE, related_name='received_messages', blank=True, null=True)
    text = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)
    is_system_message = models.BooleanField(default=False)
    is_private = models.BooleanField(default=False)
    
    # Legacy fields for compatibility
    color = models.CharField(max_length=20, blank=True, null=True)

    def __str__(self):
        if self.is_private:
            return f"Private: {self.sender.username} to {self.recipient.username if self.recipient else 'Unknown'}"
        return f"{self.sender.username} in {self.room.name if self.room else 'Unknown Room'}"

    class Meta:
        db_table = "chat_messages"
        ordering = ['-timestamp']


class RoomMembership(models.Model):
    """Track user membership in chat rooms."""
    room = models.ForeignKey(ChatRoom, on_delete=models.CASCADE, related_name='logged_users')
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    joined_at = models.DateTimeField(auto_now_add=True)
    last_activity = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = "room_memberships"
        unique_together = ['room', 'user']

    def __str__(self):
        return f"{self.user.username} in {self.room.name}"


class PrivateMessageInvitation(models.Model):
    """Private chat invitations."""
    from_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_invitations')
    to_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='received_invitations')
    created_at = models.DateTimeField(auto_now_add=True)
    is_accepted = models.BooleanField(default=False)
    accepted_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        db_table = "private_message_invitations"
        unique_together = ['from_user', 'to_user']

    def __str__(self):
        return f"Invitation from {self.from_user.username} to {self.to_user.username}"