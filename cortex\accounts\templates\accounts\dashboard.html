<!-- accounts/templates/accounts/dashboard.html -->
<!-- Accounts Dashboard - SAP S/4HANA Inspired Design -->
<!-- Replaces ASP.NET Default.aspx functionality -->

{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }} - Cortex{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-sap-blue-500 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="calculator" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Accounts</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Financial accounting and management system</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-2 py-4 space-y-6">
    
    <!-- Statistics Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="list" class="w-5 h-5 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-semibold text-sap-gray-900">{{ account_head_count }}</p>
                    <p class="text-sm font-medium text-sap-gray-600">Account Heads</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="landmark" class="w-5 h-5 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-semibold text-sap-gray-900">{{ bank_count }}</p>
                    <p class="text-sm font-medium text-sap-gray-600">Banks</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="coins" class="w-5 h-5 text-sap-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-semibold text-sap-gray-900">{{ currency_count }}</p>
                    <p class="text-sm font-medium text-sap-gray-600">Currencies</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <h3 class="text-lg font-medium text-sap-gray-800">Quick Actions</h3>
            <p class="text-sm text-sap-gray-600 mt-1">Common accounting tasks</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <a href="{% url 'accounts:account_head_list' %}" 
                   class="flex items-center p-4 border border-sap-gray-200 rounded-lg hover:bg-sap-blue-50 hover:border-sap-blue-300 transition-all duration-200">
                    <div class="w-8 h-8 bg-sap-blue-100 rounded-lg flex items-center justify-center mr-3">
                        <i data-lucide="list" class="w-4 h-4 text-sap-blue-600"></i>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-sap-gray-900">Account Heads</h4>
                        <p class="text-xs text-sap-gray-600">Manage chart of accounts</p>
                    </div>
                </a>

                <a href="{% url 'accounts:bank_list' %}" 
                   class="flex items-center p-4 border border-sap-gray-200 rounded-lg hover:bg-sap-green-50 hover:border-sap-green-300 transition-all duration-200">
                    <div class="w-8 h-8 bg-sap-green-100 rounded-lg flex items-center justify-center mr-3">
                        <i data-lucide="landmark" class="w-4 h-4 text-sap-green-600"></i>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-sap-gray-900">Banks</h4>
                        <p class="text-xs text-sap-gray-600">Manage bank details</p>
                    </div>
                </a>

                <a href="{% url 'accounts:currency_list' %}" 
                   class="flex items-center p-4 border border-sap-gray-200 rounded-lg hover:bg-sap-orange-50 hover:border-sap-orange-300 transition-all duration-200">
                    <div class="w-8 h-8 bg-sap-orange-100 rounded-lg flex items-center justify-center mr-3">
                        <i data-lucide="coins" class="w-4 h-4 text-sap-orange-600"></i>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-sap-gray-900">Currencies</h4>
                        <p class="text-xs text-sap-gray-600">Manage currency symbols</p>
                    </div>
                </a>

                <div class="flex items-center p-4 border border-sap-gray-200 rounded-lg bg-sap-gray-50 opacity-50">
                    <div class="w-8 h-8 bg-sap-gray-100 rounded-lg flex items-center justify-center mr-3">
                        <i data-lucide="credit-card" class="w-4 h-4 text-sap-gray-400"></i>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-sap-gray-500">Payments</h4>
                        <p class="text-xs text-sap-gray-400">Coming soon</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Module Information -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <h3 class="text-lg font-medium text-sap-gray-800">Module Information</h3>
            <p class="text-sm text-sap-gray-600 mt-1">Accounts module features and status</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-sm font-medium text-sap-gray-900 mb-3">Implemented Features</h4>
                    <ul class="space-y-2 text-sm text-sap-gray-600">
                        <li class="flex items-center">
                            <i data-lucide="check" class="w-4 h-4 text-sap-green-500 mr-2"></i>
                            Chart of Accounts (Account Heads)
                        </li>
                        <li class="flex items-center">
                            <i data-lucide="check" class="w-4 h-4 text-sap-green-500 mr-2"></i>
                            Bank Management
                        </li>
                        <li class="flex items-center">
                            <i data-lucide="check" class="w-4 h-4 text-sap-green-500 mr-2"></i>
                            Currency Management
                        </li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-sap-gray-900 mb-3">Planned Features</h4>
                    <ul class="space-y-2 text-sm text-sap-gray-600">
                        <li class="flex items-center">
                            <i data-lucide="clock" class="w-4 h-4 text-sap-orange-500 mr-2"></i>
                            Cash & Bank Entry Management
                        </li>
                        <li class="flex items-center">
                            <i data-lucide="clock" class="w-4 h-4 text-sap-orange-500 mr-2"></i>
                            Payment Management
                        </li>
                        <li class="flex items-center">
                            <i data-lucide="clock" class="w-4 h-4 text-sap-orange-500 mr-2"></i>
                            Taxation Management
                        </li>
                        <li class="flex items-center">
                            <i data-lucide="clock" class="w-4 h-4 text-sap-orange-500 mr-2"></i>
                            Invoicing & Billing
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

</div>
{% endblock %}