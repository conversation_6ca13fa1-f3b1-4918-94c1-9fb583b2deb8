<!-- Enquiry Table Partial for HTMX Updates -->
{% if enquiries %}
<table class="w-full">
    <thead class="bg-sap-gray-50 border-b border-sap-gray-200">
        <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">
                <button class="flex items-center gap-1 hover:text-sap-gray-900">
                    Enquiry #
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m6 9 6 6 6-6"></path>
                    </svg>
                </button>
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">Customer</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">Contact</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">Enquiry Summary</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">Status</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">Date</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-sap-gray-200">
        {% for enquiry in enquiries %}
        <tr class="hover:bg-sap-gray-50 transition-colors">
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex flex-col">
                    <div class="text-sm font-medium text-sap-blue-600">#{{ enquiry.enqid }}</div>
                    <div class="text-xs text-sap-gray-500">ID: {{ enquiry.customerid|default:"Auto" }}</div>
                </div>
            </td>
            
            <td class="px-6 py-4">
                <div class="flex flex-col">
                    <div class="text-sm font-medium text-sap-gray-900">{{ enquiry.customername }}</div>
                    {% if enquiry.regdcity %}
                        <div class="text-xs text-sap-gray-500 flex items-center gap-1">
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
                                <circle cx="12" cy="10" r="3"></circle>
                            </svg>
                            {{ enquiry.regdcity.cityname }}{% if enquiry.regdstate %}, {{ enquiry.regdstate.statename }}{% endif %}{% if enquiry.regdcountry %}, {{ enquiry.regdcountry.countryname }}{% endif %}
                        </div>
                    {% endif %}
                </div>
            </td>
            
            <td class="px-6 py-4">
                <div class="flex flex-col">
                    {% if enquiry.contactperson %}
                        <div class="text-sm text-sap-gray-900">{{ enquiry.contactperson }}</div>
                    {% endif %}
                    {% if enquiry.email %}
                        <div class="text-xs text-sap-gray-500">{{ enquiry.email }}</div>
                    {% endif %}
                    {% if enquiry.contactno %}
                        <div class="text-xs text-sap-gray-500">{{ enquiry.contactno }}</div>
                    {% endif %}
                </div>
            </td>
            
            <td class="px-6 py-4">
                <div class="text-sm text-sap-gray-900 max-w-xs truncate">
                    {{ enquiry.enquiryfor|truncatewords:15 }}
                </div>
                {% if enquiry.remark %}
                    <div class="text-xs text-sap-gray-500 mt-1 max-w-xs truncate">
                        {{ enquiry.remark|truncatewords:10 }}
                    </div>
                {% endif %}
            </td>
            
            <td class="px-6 py-4 whitespace-nowrap">
                {% if enquiry.status_display.status == 'Quoted' %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-green-500 bg-opacity-10 text-sap-green-600">
                        Quoted
                    </span>
                {% elif enquiry.status_display.status == 'New' %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-blue-500 bg-opacity-10 text-sap-blue-600">
                        New
                    </span>
                {% else %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-orange-500 bg-opacity-10 text-sap-orange-600">
                        {{ enquiry.status_display.status }}
                    </span>
                {% endif %}
            </td>
            
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-sap-gray-900">{{ enquiry.sysdate }}</div>
                <div class="text-xs text-sap-gray-500">{{ enquiry.systime }}</div>
            </td>
            
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center gap-2">
                    <a href="{% url 'sales_distribution:enquiry_detail' enquiry.enqid %}" 
                       class="text-sap-blue-600 hover:text-sap-blue-700 p-1 rounded transition-colors" title="View">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                            <circle cx="12" cy="12" r="3"></circle>
                        </svg>
                    </a>
                    
                    {% if enquiry.postatus != 2 %}
                        <a href="{% url 'sales_distribution:enquiry_edit' enquiry.enqid %}" 
                           class="text-sap-gray-600 hover:text-sap-gray-700 p-1 rounded transition-colors" title="Edit">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4Z"></path>
                            </svg>
                        </a>
                    {% endif %}
                    
                    <button class="text-sap-gray-600 hover:text-sap-gray-700 p-1 rounded transition-colors" title="More">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="1"></circle>
                            <circle cx="19" cy="12" r="1"></circle>
                            <circle cx="5" cy="12" r="1"></circle>
                        </svg>
                    </button>
                </div>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<!-- Pagination -->
{% if is_paginated %}
<div class="bg-sap-gray-50 px-6 py-3 border-t border-sap-gray-200">
    <div class="flex items-center justify-between">
        <div class="text-sm text-sap-gray-700">
            Showing <span class="font-medium">{{ page_obj.start_index }}</span> to <span class="font-medium">{{ page_obj.end_index }}</span> of <span class="font-medium">{{ page_obj.paginator.count }}</span> results
        </div>
        <div class="flex items-center gap-2">
            {% if page_obj.has_previous %}
                <a href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.country %}country={{ request.GET.country }}&{% endif %}{% if request.GET.date_from %}date_from={{ request.GET.date_from }}&{% endif %}{% if request.GET.date_to %}date_to={{ request.GET.date_to }}&{% endif %}page={{ page_obj.previous_page_number }}"
                   class="p-2 text-sap-gray-600 hover:text-sap-gray-900 hover:bg-white rounded transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m15 18-6-6 6-6"></path>
                    </svg>
                </a>
            {% else %}
                <button class="p-2 text-sap-gray-600 hover:text-sap-gray-900 hover:bg-white rounded transition-colors disabled:opacity-50" disabled>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m15 18-6-6 6-6"></path>
                    </svg>
                </button>
            {% endif %}
            
            <!-- Current Page -->
            <span class="px-3 py-2 bg-sap-blue-600 text-white rounded font-medium">{{ page_obj.number }}</span>
            
            <!-- Show a few page numbers around current page -->
            {% for num in page_obj.paginator.page_range %}
                {% if num != page_obj.number and num >= page_obj.number|add:'-2' and num <= page_obj.number|add:'2' %}
                    <a href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.country %}country={{ request.GET.country }}&{% endif %}{% if request.GET.date_from %}date_from={{ request.GET.date_from }}&{% endif %}{% if request.GET.date_to %}date_to={{ request.GET.date_to }}&{% endif %}page={{ num }}"
                       class="px-3 py-2 text-sap-gray-600 hover:text-sap-gray-900 hover:bg-white rounded transition-colors">{{ num }}</a>
                {% endif %}
            {% endfor %}
            
            {% if page_obj.has_next %}
                <a href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.country %}country={{ request.GET.country }}&{% endif %}{% if request.GET.date_from %}date_from={{ request.GET.date_from }}&{% endif %}{% if request.GET.date_to %}date_to={{ request.GET.date_to }}&{% endif %}page={{ page_obj.next_page_number }}"
                   class="p-2 text-sap-gray-600 hover:text-sap-gray-900 hover:bg-white rounded transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m9 18 6-6-6-6"></path>
                    </svg>
                </a>
            {% else %}
                <button class="p-2 text-sap-gray-600 hover:text-sap-gray-900 hover:bg-white rounded transition-colors disabled:opacity-50" disabled>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m9 18 6-6-6-6"></path>
                    </svg>
                </button>
            {% endif %}
        </div>
    </div>
</div>
{% endif %}

{% else %}
<!-- No data state -->
<div class="text-center py-16 px-6 text-gray-500">
    <span class="text-6xl block mb-4 opacity-50">🔍</span>
    <h3 class="text-lg font-semibold text-gray-700 mb-2">No enquiries found</h3>
    <p class="text-gray-500 mb-4">
        {% if request.GET.search or request.GET.status or request.GET.country %}
            Try adjusting your search criteria or filters.
        {% else %}
            Start by creating your first customer enquiry.
        {% endif %}
    </p>
    {% if not request.GET.search and not request.GET.status and not request.GET.country %}
        <a href="{% url 'sales_distribution:enquiry_create' %}" 
           class="inline-flex items-center gap-2 bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-2 rounded-lg font-medium hover:-translate-y-0.5 hover:shadow-lg transition-all duration-200">
            ➕ Create First Enquiry
        </a>
    {% endif %}
</div>
{% endif %}
