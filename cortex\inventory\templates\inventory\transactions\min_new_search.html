{% extends 'core/base.html' %}
{% load static %}

{% block title %}Material Issue Note [MIN] - New{% endblock %}

{% block content %}
<div class="bg-white shadow-sm">
    <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
        <div class="flex justify-between items-center">
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                    Material Issue Note [MIN] - New
                </h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                    Search and select MRS records to create Material Issue Notes
                </p>
            </div>
            <a href="{% url 'inventory:min_list' %}" 
               class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to List
            </a>
        </div>
    </div>

    <!-- Search Form -->
    <div class="px-4 py-4 border-b border-gray-200">
        <form method="get" class="flex items-center space-x-4">
            <!-- Employee Name Dropdown -->
            <div class="flex items-center space-x-2">
                <label for="employee_name" class="text-sm font-medium text-gray-700">Employee Name</label>
                <select name="employee_name" id="employee_name" 
                        class="mt-1 block w-48 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                    <option value="">Select Employee</option>
                    {% for employee in employee_names %}
                        <option value="{{ employee }}" {% if employee_search == employee %}selected{% endif %}>
                            {{ employee }}
                        </option>
                    {% endfor %}
                </select>
            </div>

            <!-- MRS Number Search -->
            <div class="flex items-center space-x-2">
                <label for="mrs_no" class="text-sm font-medium text-gray-700">MRS No</label>
                <input type="text" name="mrs_no" id="mrs_no" value="{{ mrs_search }}"
                       class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-32 shadow-sm sm:text-sm border-gray-300 rounded-md"
                       placeholder="Enter MRS No">
            </div>

            <!-- Search Button -->
            <button type="submit" 
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                Search
            </button>
        </form>
    </div>

    <!-- MRS Results Table -->
    <div class="px-4 py-5 sm:px-6">
        {% if mrs_list %}
        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
            <table class="w-full table-auto divide-y divide-gray-300">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide sm:pl-6">
                            SN
                        </th>
                        <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                            Fin Year
                        </th>
                        <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                            MRS No
                        </th>
                        <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                            Date
                        </th>
                        <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                            Gen. By
                        </th>
                        <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                            <span class="sr-only">Actions</span>
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                    {% for mrs in mrs_list %}
                    <tr class="hover:bg-gray-50">
                        <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                            {{ forloop.counter }}
                        </td>
                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                            {{ mrs.financial_year.finyear|default:"N/A" }}
                        </td>
                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                            {{ mrs.mrs_number|default:"N/A" }}
                        </td>
                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                            {{ mrs.sys_date|default:"N/A" }}
                        </td>
                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                            {{ mrs.session_id|default:"N/A" }}
                        </td>
                        <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                            <a href="{% url 'inventory:min_new_details' mrs.id %}" 
                               class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Select
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-4">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}{% if employee_search %}&employee_name={{ employee_search }}{% endif %}{% if mrs_search %}&mrs_no={{ mrs_search }}{% endif %}" 
                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Previous
                    </a>
                {% endif %}
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}{% if employee_search %}&employee_name={{ employee_search }}{% endif %}{% if mrs_search %}&mrs_no={{ mrs_search }}{% endif %}" 
                       class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Next
                    </a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} results
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        {% for page_num in page_obj.paginator.page_range %}
                            {% if page_num == page_obj.number %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-indigo-50 text-indigo-600 text-sm font-medium">
                                    {{ page_num }}
                                </span>
                            {% else %}
                                <a href="?page={{ page_num }}{% if employee_search %}&employee_name={{ employee_search }}{% endif %}{% if mrs_search %}&mrs_no={{ mrs_search }}{% endif %}" 
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    {{ page_num }}
                                </a>
                            {% endif %}
                        {% endfor %}
                    </nav>
                </div>
            </div>
        </div>
        {% endif %}

        {% else %}
        <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No MRS records found</h3>
            <p class="mt-1 text-sm text-gray-500">
                {% if employee_search or mrs_search %}
                    Try adjusting your search criteria.
                {% else %}
                    Use the search form above to find MRS records for MIN creation.
                {% endif %}
            </p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}