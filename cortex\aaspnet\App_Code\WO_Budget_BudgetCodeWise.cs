﻿using System;
using System.Data;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Xml.Linq;
using System.Data.SqlClient;

/// <summary>
/// Summary description for WO_Budget_BudgetCodeWise
/// </summary>
public class WO_Budget_BudgetCodeWise
{
    clsFunctions fun = new clsFunctions();
    CalBalBudgetAmt CBBA = new CalBalBudgetAmt();
    string connStr = "";
    SqlConnection con;
    public WO_Budget_BudgetCodeWise()
    {
        connStr = fun.Connection();
        con = new SqlConnection(connStr);
    }

    public DataTable FillDataTableToExport(int CompId, int FinYearId, string x, string y, string z, string l)
    {

        DataTable dt = new DataTable();
           try
        {
            dt.Columns.Add(new System.Data.DataColumn("Sr No", typeof(int)));//0
            dt.Columns.Add(new System.Data.DataColumn("FinYear", typeof(string)));//1
            dt.Columns.Add(new System.Data.DataColumn("WONO", typeof(string)));//2
            dt.Columns.Add(new System.Data.DataColumn("Project Title", typeof(string)));//3
            dt.Columns.Add(new System.Data.DataColumn("Budget", typeof(double)));//4
            dt.Columns.Add(new System.Data.DataColumn("Mech.Mfg[M]", typeof(double)));//5
            dt.Columns.Add(new System.Data.DataColumn("Mech.Bought[MB]", typeof(double)));//6
            dt.Columns.Add(new System.Data.DataColumn("Elec.Mfg[E]", typeof(double)));//7
            dt.Columns.Add(new System.Data.DataColumn("Elec.Bought[EB]", typeof(double)));//8
            dt.Columns.Add(new System.Data.DataColumn("Labour Charges[L]", typeof(double)));//9
            dt.Columns.Add(new System.Data.DataColumn("Transportation[T]", typeof(double)));//10
            dt.Columns.Add(new System.Data.DataColumn("Conveyance[C]", typeof(double)));//11
            dt.Columns.Add(new System.Data.DataColumn("Site Expences[S]", typeof(double)));//12
            dt.Columns.Add(new System.Data.DataColumn("Others[O]", typeof(double)));//13
            dt.Columns.Add(new System.Data.DataColumn("Consultancy[CO]", typeof(double)));//14
            //------------------------------------------------------------------------------
            dt.Columns.Add(new System.Data.DataColumn("PO Basic", typeof(double)));//15
            dt.Columns.Add(new System.Data.DataColumn("Tax", typeof(double)));//16
            dt.Columns.Add(new System.Data.DataColumn("PO Total", typeof(double)));//17
            dt.Columns.Add(new System.Data.DataColumn("Tot.Balance", typeof(double)));//18
            dt.Columns.Add(new System.Data.DataColumn("MKT PO", typeof(double)));//19
            dt.Columns.Add(new System.Data.DataColumn("INV Amt", typeof(double)));//20
            dt.Columns.Add(new System.Data.DataColumn("Bank Receipt", typeof(double)));//21
            dt.Columns.Add(new System.Data.DataColumn("Bal", typeof(double)));//22



            DataTable XDT = new DataTable();
            XDT.Columns.Add(new System.Data.DataColumn("WONo", typeof(string)));
            XDT.Columns.Add(new System.Data.DataColumn("POBasicAmt", typeof(double)));
            XDT.Columns.Add(new System.Data.DataColumn("POTaxAmt", typeof(double)));
            XDT.Columns.Add(new System.Data.DataColumn("POTotalAmt", typeof(double)));
            XDT.Columns.Add(new System.Data.DataColumn("TotBudgetAssined", typeof(double)));
            XDT.Columns.Add(new System.Data.DataColumn("BalBudget", typeof(double)));
            DataRow dr;
            con.Open();
            string Sql = "select Id,CustomerId,TaskProjectTitle,WONo,FinYear from SD_Cust_WorkOrder_Master inner join tblFinancial_master on SD_Cust_WorkOrder_Master.FinYearId=tblFinancial_master.FinYearId  And SD_Cust_WorkOrder_Master.FinYearId<=" + FinYearId + " And SD_Cust_WorkOrder_Master.CloseOpen=0 And SD_Cust_WorkOrder_Master.CompId=" + CompId + "" + l + "" + x + "" + y + "" + z + " Order by SD_Cust_WorkOrder_Master.WONo ASC";
            SqlCommand da = new SqlCommand(Sql, con);
            SqlDataReader dreader = da.ExecuteReader();
            int SrNo = 1;
            while (dreader.Read())
            {
                if (dreader.HasRows)
                {
                    dr = dt.NewRow();
                    dr[0] = SrNo++;
                    dr[1] = dreader["FinYear"].ToString();
                    dr[2] = dreader["WONo"].ToString();
                    dr[3] = dreader["TaskProjectTitle"].ToString();
                    string selectBudget = "SELECT Id FROM tblMIS_BudgetCode";
                    SqlCommand cmdBD = new SqlCommand(selectBudget, con);
                    SqlDataReader rdr = cmdBD.ExecuteReader();
                    int cnt = 5;
                    double FinalTot = 0;
                    while (rdr.Read())
                    {
                        double BalTotal = 0;
                        if (rdr.HasRows)
                        {
                            DataTable YDT = new DataTable();
                            YDT = CBBA.TotBudget_WONO_1(Convert.ToInt32(rdr["Id"]), CompId, FinYearId, dreader["WONo"].ToString(), 0);

                            XDT.Rows.Add(YDT.Rows[0]["WONo"], YDT.Rows[0]["POBasicAmt"], YDT.Rows[0]["POTaxAmt"], YDT.Rows[0]["POTotalAmt"], YDT.Rows[0]["TotBudgetAssined"], YDT.Rows[0]["BalBudget"]);
                            dr[cnt] = Convert.ToDouble(YDT.Rows[0]["TotBudgetAssined"]);
                        }
                        cnt++;
                    }

                    //--------------------------------------------------------------Po Amt --------------------
                    if (XDT.Rows.Count > 0)
                    {
                        var grpbyfilter = from row in XDT.AsEnumerable()
                                          group row by new
                                          {
                                              y = row.Field<string>("WONo"),
                                          } into grp
                                          select new
                                          {
                                              Total1 = grp.Sum(r => r.Field<double>("POBasicAmt")),
                                              Total2 = grp.Sum(r => r.Field<double>("POTaxAmt")),
                                              Total3 = grp.Sum(r => r.Field<double>("POTotalAmt")),
                                              Total4 = grp.Sum(r => r.Field<double>("TotBudgetAssined")),
                                              Total5 = grp.Sum(r => r.Field<double>("BalBudget")),

                                          };

                        double Value1 = 0;
                        double Value2 = 0;
                        double Value3 = 0;
                        double Value4 = 0;
                        double Value5 = 0;
                        foreach (var text in grpbyfilter.ToList())
                        {
                            Value1 = Convert.ToDouble(text.Total1);
                            Value2 = Convert.ToDouble(text.Total2);
                            Value3 = Convert.ToDouble(text.Total3);
                            Value4 = Convert.ToDouble(text.Total4);
                            Value5 = Convert.ToDouble(text.Total5);
                        }
                        dr[4] = Value4;
                        dr[15] = Value1;
                        dr[16] = Value2;
                        dr[17] = Value3;
                        dr[18] = Value5;
                        dr[19] = 0;

                        //--------------------------------------------------------------Inv Amt --------------------
                        string CustCode = "";
                        CustCode = dreader["CustomerId"].ToString();
                        string WONoId = "";
                        WONoId = dreader["Id"].ToString() + ",";
                        double InvAmt = 0;
                         InvAmt= this.TotInvAmt(CustCode, WONoId);
                        dr[20] = InvAmt;
                        //--------------------------------------------------------------Bank Receipt Amt --------------------
                        double BankReceiptAmt = 0;

          string StrBnkAmt = fun.select("Sum(Amount) AS Amount ", "tblACC_BankVoucher_Received_Masters", "WONo='" + dreader["WONo"] + "'");                        
                        SqlCommand cmdBnkAmt = new SqlCommand(StrBnkAmt, con);
                        SqlDataReader rdrBnkAmt = cmdBnkAmt.ExecuteReader();
                        rdrBnkAmt.Read();
                        {
                            if (rdrBnkAmt.HasRows && rdrBnkAmt["Amount"]!=DBNull.Value)
                            {
                                BankReceiptAmt = Math.Round(Convert.ToDouble(rdrBnkAmt["Amount"]),2);
                     
                            }
                        }
                        dr[21] = BankReceiptAmt;
                        //dr[22] = Math.Round((InvAmt - BankReceiptAmt),2);
                        dr[22] = 0;
                    }

                    dt.Rows.Add(dr);
                    dt.AcceptChanges();
                }
            }
        }
          catch (Exception ex)
        {
        }
          finally
        {
            con.Close();
        }
        return dt;

    }
    public double TotInvAmt(string CustCode, string WONo)
    {
        double fdapExp1Ins = 0;
        try
        {
            
            string strInv = "select Id from tblACC_SalesInvoice_Master where tblACC_SalesInvoice_Master.CustomerCode='" + CustCode + "' AND tblACC_SalesInvoice_Master.WONo='" + WONo + "' ";
            SqlCommand cmdInv = new SqlCommand(strInv, con);
            SqlDataReader rdrInv = cmdInv.ExecuteReader();
            while (rdrInv.Read())
            {
                
                double totQty = 0;
                double finaltot = 0;
                double deduction = 0;
                double addition = 0;
                double fda = 0;
                double pf = 0;
                double excise = 0;
                double fdap = 0;
                double fdapEx = 0;
                double fdapExp = 0;
                double p = 0;
                double p1 = 0;
                double fdapExp1 = 0;
                double Insurance = 0;
                /// Calculate Basic                  
                string strAmt = "select sum(case when Unit_Master.EffectOnInvoice=1 then (ReqQty*(AmtInPer/100)*Rate) Else (ReqQty*Rate) End) As Amt from tblACC_SalesInvoice_Details inner join tblACC_SalesInvoice_Master on tblACC_SalesInvoice_Master.Id=tblACC_SalesInvoice_Details.MId inner join  Unit_Master on tblACC_SalesInvoice_Details.Unit=Unit_Master.Id And tblACC_SalesInvoice_Master.CustomerCode='" + CustCode + "' And tblACC_SalesInvoice_Master.Id=" + rdrInv["Id"] + " ";
                SqlCommand cmdAmt = new SqlCommand(strAmt, con);
                SqlDataReader rdrAmt = cmdAmt.ExecuteReader();
                rdrAmt.Read();
                totQty += Convert.ToDouble(rdrAmt["Amt"]);

                /// Calculate Addition 
                string strdeduct1 = "select Sum(case when AddType=0 then AddAmt Else ((" + totQty + " *AddAmt)/100)End) As AddAmt from tblACC_SalesInvoice_Master where tblACC_SalesInvoice_Master.CustomerCode='" + CustCode + "'And tblACC_SalesInvoice_Master.Id='" + rdrInv["Id"] + "' ";
                SqlCommand cmdded1 = new SqlCommand(strdeduct1, con);
                SqlDataReader rdr31 = cmdded1.ExecuteReader();
                rdr31.Read();
                addition = Convert.ToDouble(rdr31["AddAmt"]);

                finaltot += totQty + addition;

                /// Calculate deduction 
                string strdeduct = "select Sum(case when DeductionType=0 then Deduction Else ((" + finaltot + " *Deduction)/100)End) As deduct from tblACC_SalesInvoice_Master where tblACC_SalesInvoice_Master.CustomerCode='" + CustCode + "'And tblACC_SalesInvoice_Master.Id='" + rdrInv["Id"] + "' ";
                SqlCommand cmdded = new SqlCommand(strdeduct, con);
                SqlDataReader rdr3 = cmdded.ExecuteReader();
                rdr3.Read();

                deduction = Convert.ToDouble(rdr3["deduct"]);
                fda += (finaltot - deduction);

                /// Calculate Packing And Forwarding (PF)
                string strpf = "select Sum(case when PFType=0 then PF Else ((" + fda + " *PF)/100)End) As pf from  tblACC_SalesInvoice_Master where tblACC_SalesInvoice_Master.CustomerCode='" + CustCode + "'And tblACC_SalesInvoice_Master.Id='" + rdrInv["Id"] + "'";
                SqlCommand cmdpf = new SqlCommand(strpf, con);
                SqlDataReader rdr4 = cmdpf.ExecuteReader();
                rdr4.Read();
                pf = Convert.ToDouble(rdr4["pf"]);
                fdap += (fda + pf);

                /// Calculate Excise (CENVAT)
                string strEx = "select Sum((" + fdap + ")*((tblExciseser_Master.AccessableValue)/100) + ((" + fdap + ")*((tblExciseser_Master.AccessableValue)/100)*tblExciseser_Master.EDUCess/100)+((" + fdap + ")*((tblExciseser_Master.AccessableValue)/100)*tblExciseser_Master.SHECess/100)) As Ex from  tblACC_SalesInvoice_Master inner join tblExciseser_Master on tblExciseser_Master.Id=tblACC_SalesInvoice_Master.CENVAT where tblACC_SalesInvoice_Master.CustomerCode='" + CustCode + "'And tblACC_SalesInvoice_Master.Id='" + rdrInv["Id"] + "'";
                SqlCommand cmdEx = new SqlCommand(strEx, con);
                SqlDataReader rdr5 = cmdEx.ExecuteReader();
                rdr5.Read();
                excise = Convert.ToDouble(rdr5["Ex"]);
                fdapEx += (fdap + excise);

                /// Calculate CSTVAT (within/out Maharashtra)               
                string strCSTVAT = "select FreightType,Freight,InvoiceMode,CST,VAT from  tblACC_SalesInvoice_Master where tblACC_SalesInvoice_Master.CustomerCode='" + CustCode + "'And tblACC_SalesInvoice_Master.Id='" + rdrInv["Id"] + "'";
                SqlCommand cmdCSTVAT = new SqlCommand(strCSTVAT, con);
                SqlDataReader rdr6 = cmdCSTVAT.ExecuteReader();
                while (rdr6.Read())
                {
                    double f = Convert.ToDouble(rdr6["Freight"].ToString());
                    double v = 0;
                    if (rdr6["InvoiceMode"].ToString() == "2")
                    {
                        if (rdr6["FreightType"].ToString() == "0")
                        {
                            p = f;
                        }
                        else
                        {
                            p = fdapEx * (f / 100);
                        }
                        string SqlCst = fun.select("Value", "tblVAT_Master", "Id='" + rdr6["VAT"].ToString() + "'");
                        SqlCommand cmdSqlCst = new SqlCommand(SqlCst, con);
                        SqlDataReader rdr7 = cmdSqlCst.ExecuteReader();
                        while (rdr7.Read())
                        {
                            v = Convert.ToDouble(rdr7["Value"]);
                        }
                        p1 = (fdapEx + p) * (v / 100);

                    }
                    else if (rdr6["InvoiceMode"].ToString() == "3")
                    {
                        string SqlCst = fun.select("Value", "tblVAT_Master", "Id='" + rdr6["CST"].ToString() + "'");
                        SqlCommand cmdSqlCst = new SqlCommand(SqlCst, con);
                        SqlDataReader rdr7 = cmdSqlCst.ExecuteReader();
                        while (rdr7.Read())
                        {
                            v = Convert.ToDouble(rdr7["Value"]);
                        }
                        p = fdapEx * (v / 100);
                        if (rdr6["FreightType"].ToString() == "0")
                        {
                            p1 = f;
                        }
                        else
                        {
                            p1 = (fdapEx + p) * (f / 100);
                        }

                    }

                }
                fdapExp += fdapEx + p;
                fdapExp1 += fdapExp + p1;


                /// Calculate Insurance (LIC)
                string strInc = "select Sum(case when InsuranceType=0 then Insurance Else ((" + fdapExp1 + " *Insurance)/100)End) As Insurance from  tblACC_SalesInvoice_Master where tblACC_SalesInvoice_Master.CustomerCode='" + CustCode + "'And tblACC_SalesInvoice_Master.Id='" + rdrInv["Id"] + "'";
                SqlCommand cmdInc = new SqlCommand(strInc, con);
                SqlDataReader rdr8 = cmdInc.ExecuteReader();
                rdr8.Read();
                Insurance = Convert.ToDouble(rdr8["Insurance"]);
                fdapExp1Ins += fdapExp1 + Insurance;                

            }
        }
        catch (Exception ex) { }

        return Math.Round(fdapExp1Ins, 2);
    }
}
