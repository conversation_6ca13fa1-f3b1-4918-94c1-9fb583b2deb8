{% extends "core/base.html" %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{{ page_title }}</h1>
                        <p class="text-gray-600 mt-1">Manage customer enquiries and track their progress</p>
                    </div>
                    <div class="flex space-x-3">
                        <a href="{% url 'sales_distribution:enquiry_create' %}" 
                           class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                            Create New Enquiry
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filter -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-t-lg">
                <h3 class="text-lg font-medium">Search & Filter Enquiries</h3>
            </div>
            <div class="px-6 py-6">
                <form method="get" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="form-group">
                            <label for="{{ filter_form.search.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                Search
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                                {{ filter_form.search }}
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="{{ filter_form.status.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                Status
                            </label>
                            {{ filter_form.status }}
                        </div>

                        <div class="form-group">
                            <label for="{{ filter_form.country.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                Country
                            </label>
                            {{ filter_form.country }}
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label for="{{ filter_form.date_from.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                From Date
                            </label>
                            {{ filter_form.date_from }}
                        </div>

                        <div class="form-group">
                            <label for="{{ filter_form.date_to.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                To Date
                            </label>
                            {{ filter_form.date_to }}
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <a href="{% url 'sales_distribution:enquiry_list' %}" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            Clear
                        </a>
                        
                        <button type="submit" 
                                class="inline-flex items-center px-6 py-2 bg-blue-600 border border-transparent rounded-lg font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Search
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Enquiries List -->
        {% if enquiries %}
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">All Enquiries</h3>
                        <p class="text-sm text-gray-600">{{ enquiries|length }} enquir{{ enquiries|length|pluralize:"y,ies" }} found</p>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Enquiry Info
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Customer Details
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Enquiry Description
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Contact Info
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Date & Status
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for enquiry in enquiries %}
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">
                                            Enquiry #{{ enquiry.enqid }}
                                        </div>
                                        {% if enquiry.customerid %}
                                            <div class="text-xs text-gray-500">
                                                Customer ID: {{ enquiry.customerid }}
                                            </div>
                                        {% else %}
                                            <div class="text-xs text-gray-500">
                                                New Customer
                                            </div>
                                        {% endif %}
                                    </td>
                                    
                                    <td class="px-6 py-4">
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ enquiry.customername|default:"—" }}
                                        </div>
                                        {% if enquiry.contactperson %}
                                            <div class="text-xs text-gray-500">
                                                Contact: {{ enquiry.contactperson }}
                                            </div>
                                        {% endif %}
                                        {% if enquiry.regdcity %}
                                            <div class="text-xs text-gray-500">
                                                {{ enquiry.regdcity.cityname }}{% if enquiry.regdstate %}, {{ enquiry.regdstate.statename }}{% endif %}
                                            </div>
                                        {% endif %}
                                    </td>
                                    
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-gray-900">
                                            {{ enquiry.enquiryfor|truncatechars:80|default:"—" }}
                                        </div>
                                        {% if enquiry.remark %}
                                            <div class="text-xs text-gray-500 mt-1">
                                                Note: {{ enquiry.remark|truncatechars:60 }}
                                            </div>
                                        {% endif %}
                                    </td>
                                    
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        {% if enquiry.email %}
                                            <div class="text-sm text-gray-900">
                                                <a href="mailto:{{ enquiry.email }}" class="text-blue-600 hover:text-blue-700">
                                                    {{ enquiry.email }}
                                                </a>
                                            </div>
                                        {% endif %}
                                        {% if enquiry.contactno %}
                                            <div class="text-xs text-gray-500">
                                                {{ enquiry.contactno }}
                                            </div>
                                        {% endif %}
                                    </td>
                                    
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            {{ enquiry.sysdate|default:"—" }}
                                        </div>
                                        {% if enquiry.systime %}
                                            <div class="text-xs text-gray-500">
                                                {{ enquiry.systime }}
                                            </div>
                                        {% endif %}
                                        
                                        <!-- Status Badge -->
                                        <div class="mt-1">
                                            {% if enquiry.postatus == 2 %}
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    Converted
                                                </span>
                                            {% elif enquiry.postatus == 1 %}
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                    Quoted
                                                </span>
                                            {% elif enquiry.flag == 1 %}
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    Existing Customer
                                                </span>
                                            {% else %}
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                    New Customer
                                                </span>
                                            {% endif %}
                                        </div>
                                    </td>
                                    
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex items-center justify-end space-x-2">
                                            <a href="{% url 'sales_distribution:enquiry_detail' enquiry.enqid %}" 
                                               class="text-gray-600 hover:text-gray-700 tooltip" 
                                               title="View Details">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                </svg>
                                            </a>
                                            
                                            {% if enquiry.postatus != 2 %}
                                                <a href="{% url 'sales_distribution:enquiry_edit' enquiry.enqid %}" 
                                                   class="text-blue-600 hover:text-blue-700 tooltip" 
                                                   title="Edit Enquiry">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                    </svg>
                                                </a>
                                            {% endif %}
                                            
                                            <!-- Convert to Quotation -->
                                            {% if enquiry.postatus != 2 %}
                                                <button type="button" 
                                                        class="text-green-600 hover:text-green-700 tooltip conversion-btn"
                                                        data-enquiry-id="{{ enquiry.enqid }}"
                                                        title="Convert to Quotation">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                                    </svg>
                                                </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                        <div class="flex-1 flex justify-between sm:hidden">
                            {% if page_obj.has_previous %}
                                <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    Previous
                                </a>
                            {% endif %}
                            {% if page_obj.has_next %}
                                <a href="?page={{ page_obj.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    Next
                                </a>
                            {% endif %}
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    Showing <span class="font-medium">{{ page_obj.start_index }}</span> to <span class="font-medium">{{ page_obj.end_index }}</span> of <span class="font-medium">{{ paginator.count }}</span> results
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                    {% if page_obj.has_previous %}
                                        <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                        </a>
                                    {% endif %}
                                    
                                    {% for num in page_obj.paginator.page_range %}
                                        {% if page_obj.number == num %}
                                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                                                {{ num }}
                                            </span>
                                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                            <a href="?page={{ num }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                                {{ num }}
                                            </a>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if page_obj.has_next %}
                                        <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                        </a>
                                    {% endif %}
                                </nav>
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
        {% else %}
            <!-- Empty State -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No enquiries found</h3>
                    <p class="mt-1 text-sm text-gray-500">
                        {% if request.GET.search or request.GET.status or request.GET.country %}
                            No enquiries match your search criteria. Try different filters.
                        {% else %}
                            Get started by creating your first customer enquiry.
                        {% endif %}
                    </p>
                    <div class="mt-6">
                        <a href="{% url 'sales_distribution:enquiry_create' %}" 
                           class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                            Create New Enquiry
                        </a>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle conversion to quotation
    const conversionButtons = document.querySelectorAll('.conversion-btn');
    
    conversionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const enquiryId = this.dataset.enquiryId;
            
            if (confirm(`Are you sure you want to convert enquiry #${enquiryId} to quotation?`)) {
                // Here you would make an AJAX call to handle the conversion
                // For now, we'll just show an alert
                alert(`Conversion to quotation functionality would be implemented for enquiry ${enquiryId}`);
                
                // In a real implementation, you would:
                // window.location.href = `/sales-distribution/quotations/create-from-enquiry/${enquiryId}/`;
            }
        });
    });
});
</script>
{% endblock %}
