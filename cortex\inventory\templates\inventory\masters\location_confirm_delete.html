{% extends "core/base.html" %}
{% load static %}

{% block title %}Delete Location - Inventory Management{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center space-x-4">
            <a href="{% url 'inventory:location_list' %}" 
               class="text-gray-500 hover:text-gray-700">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </a>
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Delete Location</h1>
                <p class="text-gray-600 mt-1">Permanently remove this item location</p>
            </div>
        </div>
    </div>

    <!-- Confirmation -->
    <div class="bg-white shadow rounded-lg">
        <div class="p-6">
            <div class="flex items-center mb-6">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-medium text-gray-900">Confirm Deletion</h3>
                    <p class="text-gray-600">Are you sure you want to delete this location? This action cannot be undone.</p>
                </div>
            </div>

            <!-- Location Details -->
            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                <h4 class="text-sm font-medium text-gray-900 mb-3">Location Details:</h4>
                <dl class="grid grid-cols-1 gap-x-4 gap-y-3 sm:grid-cols-2">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Location Code</dt>
                        <dd class="text-sm text-gray-900">{{ object.location_code }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Location Name</dt>
                        <dd class="text-sm text-gray-900">{{ object.location_name }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Type</dt>
                        <dd class="text-sm text-gray-900">{{ object.get_location_type_display }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Current Stock</dt>
                        <dd class="text-sm text-gray-900">{{ object.current_stock|floatformat:2 }}</dd>
                    </div>
                    {% if object.parent_location %}
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Parent Location</dt>
                        <dd class="text-sm text-gray-900">{{ object.parent_location.location_code }}</dd>
                    </div>
                    {% endif %}
                    {% if object.capacity %}
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Capacity</dt>
                        <dd class="text-sm text-gray-900">{{ object.capacity|floatformat:2 }}</dd>
                    </div>
                    {% endif %}
                </dl>
            </div>

            <!-- Warning -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">
                            Before deleting this location:
                        </h3>
                        <div class="mt-2 text-sm text-yellow-700">
                            <ul class="list-disc pl-5">
                                <li>Ensure all stock has been moved to other locations</li>
                                <li>Delete any child locations first</li>
                                <li>Update any references to this location in other modules</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <form method="post" class="flex justify-end space-x-3">
                {% csrf_token %}
                <a href="{% url 'inventory:location_detail' object.pk %}" 
                   class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                    Cancel
                </a>
                <button type="submit" 
                        class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    Delete Location
                </button>
            </form>
        </div>
    </div>
</div>
{% endblock %}