<!-- accounts/templates/accounts/reports/cash_bank_register.html -->
<!-- Cash & Bank Register Report - SAP S/4HANA Inspired Design -->
<!-- Replaces ASP.NET Cash_Bank_Register.aspx functionality -->

{% extends 'core/base.html' %}
{% load static %}

{% block title %}Cash & Bank Register - {{ page_title }} - Cortex{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-sap-green-500 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="landmark" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Cash & Bank Register</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Transaction listing and bank reconciliation tracking</p>
                </div>
            </div>
            <div class="flex space-x-3">
                <button 
                    hx-get="{% url 'accounts:cash_bank_register' %}?export=pdf" 
                    hx-indicator="#export-indicator"
                    class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:ring-offset-2 transition-colors duration-200">
                    <i data-lucide="file-text" class="w-4 h-4 mr-2"></i>
                    Export PDF
                </button>
                <button 
                    hx-get="{% url 'accounts:cash_bank_register' %}?export=excel" 
                    hx-indicator="#export-indicator"
                    class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:ring-offset-2 transition-colors duration-200">
                    <i data-lucide="sheet" class="w-4 h-4 mr-2"></i>
                    Export Excel
                </button>
                <button 
                    onclick="window.print()" 
                    class="inline-flex items-center px-4 py-2 bg-sap-green-500 text-white rounded-lg hover:bg-sap-green-600 focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:ring-offset-2 transition-colors duration-200">
                    <i data-lucide="printer" class="w-4 h-4 mr-2"></i>
                    Print
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-2 py-6 space-y-6" id="cash-bank-register-content">
    
    <!-- Export Indicator -->
    <div id="export-indicator" class="htmx-indicator">
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 flex items-center space-x-4">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-sap-green-500"></div>
                <span class="text-sap-gray-700">Generating export...</span>
            </div>
        </div>
    </div>

    <!-- Filter Controls -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
        <h3 class="text-lg font-semibold text-sap-gray-800 mb-4 flex items-center">
            <i data-lucide="filter" class="w-5 h-5 mr-2 text-sap-blue-500"></i>
            Filter Options
        </h3>
        <form 
            hx-get="{% url 'accounts:cash_bank_register' %}" 
            hx-target="#register-results" 
            hx-indicator="#filter-indicator"
            hx-trigger="input changed delay:500ms from:input, change from:select"
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            
            <!-- Date Range -->
            <div>
                <label for="date_from" class="block text-sm font-medium text-sap-gray-700 mb-2">From Date</label>
                <input 
                    type="date" 
                    name="date_from" 
                    id="date_from"
                    value="{{ request.GET.date_from }}"
                    class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
            </div>
            
            <div>
                <label for="date_to" class="block text-sm font-medium text-sap-gray-700 mb-2">To Date</label>
                <input 
                    type="date" 
                    name="date_to" 
                    id="date_to"
                    value="{{ request.GET.date_to }}"
                    class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
            </div>
            
            <!-- Account Selection -->
            <div>
                <label for="account_head" class="block text-sm font-medium text-sap-gray-700 mb-2">Account Head</label>
                <select 
                    name="account_head" 
                    id="account_head"
                    class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                    <option value="">All Accounts</option>
                    {% for account in account_heads %}
                    <option value="{{ account.id }}" {% if request.GET.account_head == account.id|stringformat:"s" %}selected{% endif %}>
                        {{ account.account_head_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <!-- Transaction Type -->
            <div>
                <label for="transaction_type" class="block text-sm font-medium text-sap-gray-700 mb-2">Transaction Type</label>
                <select 
                    name="transaction_type" 
                    id="transaction_type"
                    class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                    <option value="">All Types</option>
                    <option value="receipt" {% if request.GET.transaction_type == "receipt" %}selected{% endif %}>Receipt</option>
                    <option value="payment" {% if request.GET.transaction_type == "payment" %}selected{% endif %}>Payment</option>
                    <option value="transfer" {% if request.GET.transaction_type == "transfer" %}selected{% endif %}>Transfer</option>
                </select>
            </div>
            
            <!-- Search Box -->
            <div class="md:col-span-2">
                <label for="search" class="block text-sm font-medium text-sap-gray-700 mb-2">Search (Particulars/Reference)</label>
                <input 
                    type="text" 
                    name="search" 
                    id="search"
                    value="{{ request.GET.search }}"
                    placeholder="Search by particulars, reference number, or description..."
                    class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
            </div>
            
            <!-- Clear Filters -->
            <div class="flex items-end">
                <button 
                    type="button"
                    onclick="document.querySelector('form').reset(); htmx.trigger(document.querySelector('form'), 'submit')"
                    class="w-full px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-sap-gray-500 focus:ring-offset-2 transition-colors duration-200">
                    <i data-lucide="x-circle" class="w-4 h-4 mr-2 inline"></i>
                    Clear Filters
                </button>
            </div>
        </form>
        
        <!-- Filter Indicator -->
        <div id="filter-indicator" class="htmx-indicator mt-4">
            <div class="flex items-center justify-center py-2">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-sap-green-500 mr-2"></div>
                <span class="text-sm text-sap-gray-600">Loading...</span>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="arrow-down-right" class="w-5 h-5 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-semibold text-sap-gray-900">₹ {{ total_receipts|floatformat:2|default:"0.00" }}</p>
                    <p class="text-sm font-medium text-sap-gray-600">Total Receipts</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-red-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="arrow-up-right" class="w-5 h-5 text-sap-red-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-semibold text-sap-gray-900">₹ {{ total_payments|floatformat:2|default:"0.00" }}</p>
                    <p class="text-sm font-medium text-sap-gray-600">Total Payments</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="trending-up" class="w-5 h-5 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-semibold text-sap-gray-900">₹ {{ net_balance|floatformat:2|default:"0.00" }}</p>
                    <p class="text-sm font-medium text-sap-gray-600">Net Balance</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="list" class="w-5 h-5 text-sap-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-semibold text-sap-gray-900">{{ total_transactions|default:"0" }}</p>
                    <p class="text-sm font-medium text-sap-gray-600">Total Transactions</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Register Results -->
    <div id="register-results">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-200 bg-sap-gray-50">
                <h3 class="text-lg font-semibold text-sap-gray-800 flex items-center">
                    <i data-lucide="list" class="w-5 h-5 mr-2 text-sap-green-500"></i>
                    Transaction Register
                </h3>
            </div>
            
            <!-- Table Container -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-sap-gray-200">
                    <thead class="bg-sap-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Date
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Reference
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Particulars
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Account Head
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Receipt
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Payment
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Balance
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Reconciled
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-sap-gray-200">
                        {% for transaction in transactions %}
                        <tr class="hover:bg-sap-gray-50 transition-colors duration-150">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">
                                {{ transaction.transaction_date|date:"d/m/Y" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-sap-blue-600">
                                {{ transaction.reference_number|default:"-" }}
                            </td>
                            <td class="px-6 py-4 text-sm text-sap-gray-900">
                                <div class="max-w-xs truncate" title="{{ transaction.particulars }}">
                                    {{ transaction.particulars|default:"-" }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">
                                {{ transaction.account_head.account_head_name|default:"-" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-right font-mono">
                                {% if transaction.transaction_type == 'receipt' %}
                                    <span class="text-sap-green-600">₹ {{ transaction.amount|floatformat:2 }}</span>
                                {% else %}
                                    <span class="text-sap-gray-400">-</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-right font-mono">
                                {% if transaction.transaction_type == 'payment' %}
                                    <span class="text-sap-red-600">₹ {{ transaction.amount|floatformat:2 }}</span>
                                {% else %}
                                    <span class="text-sap-gray-400">-</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-right font-mono text-sap-gray-900">
                                ₹ {{ transaction.running_balance|floatformat:2|default:"0.00" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                {% if transaction.is_reconciled %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-sap-green-100 text-sap-green-800">
                                        <i data-lucide="check" class="w-3 h-3 mr-1"></i>
                                        Reconciled
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-sap-orange-100 text-sap-orange-800">
                                        <i data-lucide="clock" class="w-3 h-3 mr-1"></i>
                                        Pending
                                    </span>
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="px-6 py-8 text-center">
                                <div class="flex flex-col items-center justify-center">
                                    <i data-lucide="inbox" class="w-12 h-12 text-sap-gray-400 mb-4"></i>
                                    <p class="text-lg font-medium text-sap-gray-900 mb-2">No transactions found</p>
                                    <p class="text-sm text-sap-gray-500">Try adjusting your filters or date range</p>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if transactions.has_other_pages %}
            <div class="px-6 py-4 border-t border-sap-gray-200 bg-sap-gray-50">
                <div class="flex items-center justify-between">
                    <div class="flex items-center text-sm text-sap-gray-700">
                        Showing {{ transactions.start_index }} to {{ transactions.end_index }} of {{ transactions.paginator.count }} results
                    </div>
                    <div class="flex space-x-2">
                        {% if transactions.has_previous %}
                        <button 
                            hx-get="?page={{ transactions.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}"
                            hx-target="#register-results"
                            class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Previous
                        </button>
                        {% endif %}
                        
                        <span class="px-3 py-2 text-sm font-medium text-sap-gray-700">
                            Page {{ transactions.number }} of {{ transactions.paginator.num_pages }}
                        </span>
                        
                        {% if transactions.has_next %}
                        <button 
                            hx-get="?page={{ transactions.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}"
                            hx-target="#register-results"
                            class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Next
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Bank-wise Summary (Expandable) -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm" x-data="{ expanded: false }">
        <div class="px-6 py-4 border-b border-sap-gray-200 cursor-pointer" @click="expanded = !expanded">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-sap-gray-800 flex items-center">
                    <i data-lucide="building-2" class="w-5 h-5 mr-2 text-sap-blue-500"></i>
                    Bank-wise Summary
                </h3>
                <i data-lucide="chevron-down" class="w-5 h-5 text-sap-gray-500 transition-transform duration-200" 
                   :class="{ 'transform rotate-180': expanded }"></i>
            </div>
        </div>
        <div x-show="expanded" x-transition class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {% for bank_summary in bank_wise_summary %}
                <div class="border border-sap-gray-200 rounded-lg p-4">
                    <h4 class="font-medium text-sap-gray-800 mb-2">{{ bank_summary.bank_name }}</h4>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-sap-gray-600">Opening Balance:</span>
                            <span class="font-mono">₹ {{ bank_summary.opening_balance|floatformat:2 }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sap-gray-600">Total Receipts:</span>
                            <span class="font-mono text-sap-green-600">₹ {{ bank_summary.total_receipts|floatformat:2 }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sap-gray-600">Total Payments:</span>
                            <span class="font-mono text-sap-red-600">₹ {{ bank_summary.total_payments|floatformat:2 }}</span>
                        </div>
                        <div class="flex justify-between pt-2 border-t border-sap-gray-200">
                            <span class="font-medium text-sap-gray-800">Closing Balance:</span>
                            <span class="font-mono font-medium">₹ {{ bank_summary.closing_balance|floatformat:2 }}</span>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-span-full text-center py-8">
                    <p class="text-sap-gray-500">No bank data available</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});

// Print Styles
const printStyles = `
    @media print {
        .no-print { display: none !important; }
        body { print-color-adjust: exact; }
        .bg-white { background: white !important; }
        .text-white { color: black !important; }
        .border { border: 1px solid #000 !important; }
        table { border-collapse: collapse; }
        th, td { border: 1px solid #000 !important; }
    }
`;
const styleSheet = document.createElement("style");
styleSheet.innerText = printStyles;
document.head.appendChild(styleSheet);
</script>
{% endblock %}