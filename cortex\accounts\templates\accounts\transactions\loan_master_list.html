<!-- accounts/templates/accounts/transactions/loan_master_list.html -->
<!-- Loan Master List View Template -->
<!-- Task Group 7: Capital & Loans Management - Loan Master List (Task 7.2) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Loan Management - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-blue-600 to-sap-blue-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="credit-card" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Loan Management</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage company loans, EMI schedules, and repayments</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:capital_loans_dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to Dashboard
                </a>
                <a href="{% url 'accounts:loan_master_create' %}" 
                   class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    New Loan
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Search and Filter Section -->
    <div class="mb-6 bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4">
            <form method="get" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4">
                <!-- Search by Lender -->
                <div class="flex-1">
                    <label for="lender_name" class="block text-sm font-medium text-sap-gray-700 mb-1">Search Lender</label>
                    <input type="text" name="lender_name" value="{{ request.GET.lender_name }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"
                           placeholder="Search by lender name...">
                </div>
                
                <!-- Loan Type Filter -->
                <div>
                    <label for="loan_type" class="block text-sm font-medium text-sap-gray-700 mb-1">Loan Type</label>
                    <select name="loan_type" 
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        <option value="">All Types</option>
                        {% for loan_type in loan_types %}
                        <option value="{{ loan_type.id }}" {% if request.GET.loan_type == loan_type.id|stringformat:"s" %}selected{% endif %}>
                            {{ loan_type.loan_type_description }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-sap-gray-700 mb-1">Status</label>
                    <select name="status" 
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        <option value="">All Status</option>
                        {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if request.GET.status == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Search Button -->
                <div>
                    <button type="submit" 
                            class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="search" class="w-4 h-4 inline mr-2"></i>
                        Search
                    </button>
                </div>
                
                <!-- Clear Button -->
                {% if request.GET %}
                <div>
                    <a href="{% url 'accounts:loan_master_list' %}" 
                       class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                        Clear
                    </a>
                </div>
                {% endif %}
            </form>
        </div>
    </div>

    <!-- Loan Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="credit-card" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Loans</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ loans.count|default:0 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="check-circle" class="w-6 h-6 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Active</p>
                    <p class="text-2xl font-bold text-sap-gray-900">0</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-yellow-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="clock" class="w-6 h-6 text-sap-yellow-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Pending</p>
                    <p class="text-2xl font-bold text-sap-gray-900">0</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="dollar-sign" class="w-6 h-6 text-sap-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Amount</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹0.00</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Loans Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Loan Portfolio</h3>
                <div class="flex items-center space-x-2">
                    <button type="button" onclick="exportLoans()" 
                            class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                        Export
                    </button>
                    <button type="button" onclick="generateEMIReport()" 
                            class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="calendar" class="w-4 h-4 inline mr-2"></i>
                        EMI Report
                    </button>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Loan Details
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Lender
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Amount & Interest
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Term & EMI
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Dates
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for loan in loans %}
                    <tr class="hover:bg-sap-gray-50">
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-sap-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i data-lucide="credit-card" class="w-5 h-5 text-sap-blue-600"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-sap-gray-900">
                                        <a href="{% url 'accounts:loan_master_detail' loan.id %}" class="text-sap-blue-600 hover:text-sap-blue-900">
                                            {{ loan.loan_no }}
                                        </a>
                                    </div>
                                    <div class="text-sm text-sap-gray-500">{{ loan.loan_type.loan_type_description|default:"-" }}</div>
                                    {% if loan.loan_account_number %}
                                    <div class="text-xs text-sap-gray-400">A/c: {{ loan.loan_account_number }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-sap-gray-900">{{ loan.lender_name|default:"-" }}</div>
                            {% if loan.lender_contact %}
                            <div class="text-sm text-sap-gray-500">{{ loan.lender_contact }}</div>
                            {% endif %}
                            {% if loan.lender_branch %}
                            <div class="text-xs text-sap-gray-400">{{ loan.lender_branch }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-sap-gray-900">₹{{ loan.loan_amount|floatformat:2 }}</div>
                            {% if loan.interest_rate %}
                            <div class="text-sm text-sap-gray-500">{{ loan.interest_rate }}% p.a.</div>
                            {% endif %}
                            {% if loan.interest_type %}
                            <div class="text-xs text-sap-gray-400">{{ loan.interest_type.interest_type_description }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            {% if loan.loan_tenure_months %}
                            <div class="text-sm text-sap-gray-900">{{ loan.loan_tenure_months }} months</div>
                            {% endif %}
                            {% if loan.emi_amount %}
                            <div class="text-sm font-medium text-sap-blue-600">₹{{ loan.emi_amount|floatformat:2 }} EMI</div>
                            {% endif %}
                            {% if loan.installment_frequency %}
                            <div class="text-xs text-sap-gray-400">{{ loan.get_installment_frequency_display }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            {% if loan.loan_start_date %}
                            <div class="text-sm text-sap-gray-900">Start: {{ loan.loan_start_date|date:"d M Y" }}</div>
                            {% endif %}
                            {% if loan.loan_end_date %}
                            <div class="text-sm text-sap-gray-500">End: {{ loan.loan_end_date|date:"d M Y" }}</div>
                            {% endif %}
                            {% if loan.next_emi_date %}
                            <div class="text-xs text-sap-gray-400">Next EMI: {{ loan.next_emi_date|date:"d M Y" }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if loan.status == 'active' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-green-100 text-sap-green-800">
                                Active
                            </span>
                            {% elif loan.status == 'pending' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-yellow-100 text-sap-yellow-800">
                                Pending
                            </span>
                            {% elif loan.status == 'closed' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-gray-100 text-sap-gray-800">
                                Closed
                            </span>
                            {% elif loan.status == 'overdue' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-red-100 text-sap-red-800">
                                Overdue
                            </span>
                            {% else %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-blue-100 text-sap-blue-800">
                                {{ loan.get_status_display|default:"Unknown" }}
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <a href="{% url 'accounts:loan_master_detail' loan.id %}" 
                                   class="text-sap-blue-600 hover:text-sap-blue-900" title="View Details">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                </a>
                                <button type="button" onclick="generateEMISchedule({{ loan.id }})" 
                                        class="text-sap-green-600 hover:text-sap-green-900" title="EMI Schedule">
                                    <i data-lucide="calendar" class="w-4 h-4"></i>
                                </button>
                                <button type="button" onclick="recordPayment({{ loan.id }})" 
                                        class="text-sap-purple-600 hover:text-sap-purple-900" title="Record Payment">
                                    <i data-lucide="credit-card" class="w-4 h-4"></i>
                                </button>
                                <button type="button" onclick="printLoanDetails({{ loan.id }})" 
                                        class="text-sap-orange-600 hover:text-sap-orange-900" title="Print">
                                    <i data-lucide="printer" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="px-6 py-8 text-center">
                            <div class="text-center">
                                <i data-lucide="credit-card" class="w-12 h-12 text-sap-gray-400 mx-auto mb-4"></i>
                                <h3 class="text-sm font-medium text-sap-gray-900 mb-2">No loans found</h3>
                                <p class="text-sm text-sap-gray-600 mb-4">Get started by adding your first loan.</p>
                                <a href="{% url 'accounts:loan_master_create' %}" 
                                   class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                                    Add Loan
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="px-6 py-4 border-t border-sap-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-sap-gray-700">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} entries
                </div>
                <div class="flex space-x-1">
                    {% if page_obj.has_previous %}
                    <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ page_obj.previous_page_number }}" 
                       class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-l-lg hover:bg-sap-gray-50">
                        Previous
                    </a>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <span class="px-3 py-2 text-sm font-medium text-sap-blue-600 bg-sap-blue-50 border border-sap-blue-300">
                            {{ num }}
                        </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ num }}" 
                           class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 hover:bg-sap-gray-50">
                            {{ num }}
                        </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ page_obj.next_page_number }}" 
                       class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-r-lg hover:bg-sap-gray-50">
                        Next
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function exportLoans() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = '?' + params.toString();
}

function generateEMIReport() {
    alert('EMI report generation functionality would be implemented here.');
}

function generateEMISchedule(loanId) {
    window.open(`/accounts/transactions/loans/${loanId}/emi-schedule/`, '_blank');
}

function recordPayment(loanId) {
    alert(`Record payment functionality for loan ID ${loanId} would be implemented here.`);
}

function printLoanDetails(loanId) {
    window.open(`/accounts/transactions/loans/${loanId}/print/`, '_blank');
}

// Initialize lucide icons on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %>