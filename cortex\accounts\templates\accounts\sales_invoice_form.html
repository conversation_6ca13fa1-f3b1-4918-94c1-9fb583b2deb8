<!-- accounts/templates/accounts/sales_invoice_form.html -->
<!-- Sales Invoice Form Template with HTMX -->
<!-- Task Group 5: Invoicing & Billing - Sales Invoice Templates -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{% if object %}Edit Sales Invoice{% else %}Create Sales Invoice{% endif %}{% endblock %}

{% block extra_css %}
<style>
    .invoice-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .invoice-section {
        background: white;
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
    }
    
    .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #f3f4f6;
    }
    
    .section-header h3 {
        margin: 0;
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
    }
    
    .tax-calculation-panel {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        border-radius: 1rem;
        padding: 1.5rem;
        margin: 1rem 0;
    }
    
    .line-item-row {
        background: #f9fafb;
        border-radius: 0.5rem;
        padding: 1rem;
        margin: 0.5rem 0;
        border: 1px solid #e5e7eb;
    }
    
    .amount-display {
        font-size: 1.25rem;
        font-weight: bold;
        color: #059669;
        text-align: right;
    }
    
    .invoice-totals {
        background: #f8fafc;
        border-radius: 1rem;
        padding: 1.5rem;
        border: 2px solid #e2e8f0;
    }
    
    .htmx-indicator {
        opacity: 0;
        transition: opacity 0.3s;
    }
    .htmx-request .htmx-indicator {
        opacity: 1;
    }
    
    .calculation-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 2px solid #f3f4f6;
        border-radius: 50%;
        border-top-color: #3b82f6;
        animation: spin 1s ease-in-out infinite;
    }
    
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-6 py-8" x-data="invoiceForm()">
    <!-- Header Section -->
    <div class="invoice-header">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
                <h1 class="text-3xl font-bold">
                    {% if object %}Edit Sales Invoice: {{ object.invoice_no }}{% else %}Create New Sales Invoice{% endif %}
                </h1>
                <p class="mt-2 opacity-90">
                    {% if object %}Modify invoice details and line items{% else %}Generate professional sales invoices with automatic tax calculations{% endif %}
                </p>
            </div>
            <div class="mt-4 sm:mt-0 flex space-x-3">
                <a href="{% url 'accounts:sales_invoice_list' %}" 
                   class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Back to List
                </a>
                {% if object %}
                <a href="{% url 'accounts:sales_invoice_detail' object.id %}" 
                   class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-eye mr-2"></i>View Invoice
                </a>
                {% endif %}
            </div>
        </div>
    </div>

    <form method="post" id="sales-invoice-form" x-ref="invoiceForm">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
            <!-- Main Form Sections -->
            <div class="xl:col-span-2 space-y-6">
                
                <!-- Invoice Header Information -->
                <div class="invoice-section">
                    <div class="section-header">
                        <h3><i class="fas fa-file-invoice mr-2 text-blue-600"></i>Invoice Information</h3>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="invoice_date" class="block text-sm font-medium text-gray-700 mb-2">
                                Invoice Date <span class="text-red-500">*</span>
                            </label>
                            <input type="date" name="invoice_date" id="invoice_date" 
                                   value="{% if object %}{{ object.invoice_date|date:'Y-m-d' }}{% else %}{{ today|date:'Y-m-d' }}{% endif %}" 
                                   required
                                   class="block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <div>
                            <label for="invoice_type" class="block text-sm font-medium text-gray-700 mb-2">
                                Invoice Type
                            </label>
                            <select name="invoice_type" id="invoice_type"
                                    class="block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Select Type</option>
                                <option value="Regular" {% if object.invoice_type == 'Regular' %}selected{% endif %}>Regular Invoice</option>
                                <option value="Proforma" {% if object.invoice_type == 'Proforma' %}selected{% endif %}>Proforma Invoice</option>
                                <option value="Service" {% if object.invoice_type == 'Service' %}selected{% endif %}>Service Invoice</option>
                                <option value="Export" {% if object.invoice_type == 'Export' %}selected{% endif %}>Export Invoice</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="work_order_no" class="block text-sm font-medium text-gray-700 mb-2">
                                Work Order No.
                            </label>
                            <input type="text" name="work_order_no" id="work_order_no" 
                                   value="{{ object.work_order_no|default:'' }}" 
                                   class="block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="WO-2024-001">
                        </div>
                        
                        <div>
                            <label for="po_no" class="block text-sm font-medium text-gray-700 mb-2">
                                PO Number
                            </label>
                            <input type="text" name="po_no" id="po_no" 
                                   value="{{ object.po_no|default:'' }}" 
                                   class="block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="PO-2024-001">
                        </div>
                        
                        <div>
                            <label for="po_date" class="block text-sm font-medium text-gray-700 mb-2">
                                PO Date
                            </label>
                            <input type="date" name="po_date" id="po_date" 
                                   value="{% if object.po_date %}{{ object.po_date|date:'Y-m-d' }}{% endif %}" 
                                   class="block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                                Status
                            </label>
                            <select name="status" id="status"
                                    class="block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="draft" {% if object.status == 'draft' %}selected{% endif %}>Draft</option>
                                <option value="submitted" {% if object.status == 'submitted' %}selected{% endif %}>Submitted</option>
                                <option value="approved" {% if object.status == 'approved' %}selected{% endif %}>Approved</option>
                                <option value="cancelled" {% if object.status == 'cancelled' %}selected{% endif %}>Cancelled</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Customer Information -->
                <div class="invoice-section">
                    <div class="section-header">
                        <h3><i class="fas fa-user-tie mr-2 text-green-600"></i>Customer Information</h3>
                        <div class="ml-auto">
                            <button type="button" onclick="openCustomerLookup()" 
                                    class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                <i class="fas fa-search mr-1"></i>Lookup Customer
                            </button>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="customer_id" class="block text-sm font-medium text-gray-700 mb-2">
                                Customer ID
                            </label>
                            <input type="text" name="customer_id" id="customer_id" 
                                   value="{{ object.customer_id|default:'' }}" 
                                   hx-get="{% url 'accounts:ajax_customer_lookup' %}" 
                                   hx-trigger="blur changed" 
                                   hx-target="#customer-details"
                                   hx-include="[name='customer_id']"
                                   class="block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="Enter customer ID">
                            <div class="htmx-indicator">
                                <div class="calculation-spinner mt-2"></div>
                            </div>
                        </div>
                        
                        <div>
                            <label for="customer_name" class="block text-sm font-medium text-gray-700 mb-2">
                                Customer Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="customer_name" id="customer_name" 
                                   value="{{ object.customer_name|default:'' }}" 
                                   required
                                   class="block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="Enter customer name">
                        </div>
                    </div>
                    
                    <div id="customer-details" class="mt-4">
                        <!-- Customer details will be loaded here via HTMX -->
                    </div>
                </div>

                <!-- Invoice Line Items -->
                <div class="invoice-section">
                    <div class="section-header">
                        <h3><i class="fas fa-list mr-2 text-purple-600"></i>Invoice Items</h3>
                        <div class="ml-auto">
                            <button type="button" onclick="addLineItem()" 
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                                <i class="fas fa-plus mr-1"></i>Add Item
                            </button>
                        </div>
                    </div>
                    
                    <div id="line-items-container">
                        <!-- Line items will be rendered here -->
                        {% if object.invoice_details.all %}
                            {% for item in object.invoice_details.all %}
                                {% include 'accounts/partials/invoice_line_item.html' %}
                            {% endfor %}
                        {% else %}
                            <div class="line-item-row" id="line-item-0">
                                {% include 'accounts/partials/invoice_line_item.html' with item=None index=0 %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mt-4 text-center">
                        <button type="button" onclick="addLineItem()" 
                                class="text-blue-600 hover:text-blue-800 font-medium">
                            <i class="fas fa-plus-circle mr-1"></i>Add Another Item
                        </button>
                    </div>
                </div>

                <!-- Additional Charges -->
                <div class="invoice-section">
                    <div class="section-header">
                        <h3><i class="fas fa-calculator mr-2 text-yellow-600"></i>Additional Charges & Discounts</h3>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div>
                            <label for="freight_amount" class="block text-sm font-medium text-gray-700 mb-2">
                                Freight Amount (₹)
                            </label>
                            <input type="number" name="freight_amount" id="freight_amount" 
                                   value="{{ object.freight_amount|default:'0' }}" 
                                   step="0.01" min="0"
                                   hx-get="{% url 'accounts:ajax_calculate_invoice_totals' %}" 
                                   hx-trigger="blur changed" 
                                   hx-target="#invoice-totals"
                                   hx-include="#sales-invoice-form"
                                   class="block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <div>
                            <label for="pf_amount" class="block text-sm font-medium text-gray-700 mb-2">
                                P&F Amount (₹)
                            </label>
                            <input type="number" name="pf_amount" id="pf_amount" 
                                   value="{{ object.pf_amount|default:'0' }}" 
                                   step="0.01" min="0"
                                   hx-get="{% url 'accounts:ajax_calculate_invoice_totals' %}" 
                                   hx-trigger="blur changed" 
                                   hx-target="#invoice-totals"
                                   hx-include="#sales-invoice-form"
                                   class="block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <div>
                            <label for="discount_amount" class="block text-sm font-medium text-gray-700 mb-2">
                                Discount Amount (₹)
                            </label>
                            <input type="number" name="discount_amount" id="discount_amount" 
                                   value="{{ object.discount_amount|default:'0' }}" 
                                   step="0.01" min="0"
                                   hx-get="{% url 'accounts:ajax_calculate_invoice_totals' %}" 
                                   hx-trigger="blur changed" 
                                   hx-target="#invoice-totals"
                                   hx-include="#sales-invoice-form"
                                   class="block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <div>
                            <label for="discount_percentage" class="block text-sm font-medium text-gray-700 mb-2">
                                Discount (%)
                            </label>
                            <input type="number" name="discount_percentage" id="discount_percentage" 
                                   step="0.01" min="0" max="100"
                                   onchange="calculateDiscountAmount()"
                                   class="block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Sidebar - Invoice Totals & Actions -->
            <div class="xl:col-span-1 space-y-6">
                
                <!-- Invoice Totals -->
                <div id="invoice-totals" class="invoice-totals">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">
                        <i class="fas fa-calculator mr-2"></i>Invoice Totals
                    </h3>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Basic Amount:</span>
                            <span class="font-medium" id="basic-amount-display">₹{{ object.basic_amount|default:'0.00'|floatformat:2 }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">VAT Amount:</span>
                            <span class="font-medium" id="vat-amount-display">₹{{ object.vat_amount|default:'0.00'|floatformat:2 }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">CST Amount:</span>
                            <span class="font-medium" id="cst-amount-display">₹{{ object.cst_amount|default:'0.00'|floatformat:2 }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">Excise Amount:</span>
                            <span class="font-medium" id="excise-amount-display">₹{{ object.excise_amount|default:'0.00'|floatformat:2 }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">Service Tax:</span>
                            <span class="font-medium" id="service-tax-amount-display">₹{{ object.service_tax_amount|default:'0.00'|floatformat:2 }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">Freight:</span>
                            <span class="font-medium" id="freight-amount-display">₹{{ object.freight_amount|default:'0.00'|floatformat:2 }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">P&F:</span>
                            <span class="font-medium" id="pf-amount-display">₹{{ object.pf_amount|default:'0.00'|floatformat:2 }}</span>
                        </div>
                        
                        <div class="flex justify-between text-red-600">
                            <span>Discount:</span>
                            <span class="font-medium" id="discount-amount-display">-₹{{ object.discount_amount|default:'0.00'|floatformat:2 }}</span>
                        </div>
                        
                        <hr class="my-4">
                        
                        <div class="flex justify-between text-xl font-bold text-green-600">
                            <span>Total Amount:</span>
                            <span id="total-amount-display">₹{{ object.total_amount|default:'0.00'|floatformat:2 }}</span>
                        </div>
                    </div>
                    
                    <div class="htmx-indicator mt-4">
                        <div class="flex items-center justify-center">
                            <div class="calculation-spinner mr-2"></div>
                            <span class="text-sm text-gray-600">Calculating...</span>
                        </div>
                    </div>
                </div>

                <!-- Tax Calculator -->
                <div class="tax-calculation-panel">
                    <h4 class="text-md font-bold text-gray-900 mb-3">
                        <i class="fas fa-percentage mr-2"></i>Tax Calculator
                    </h4>
                    
                    <div class="space-y-3">
                        <button type="button" onclick="openTaxCalculator()" 
                                class="w-full bg-white hover:bg-gray-50 text-gray-900 py-2 px-4 rounded-lg font-medium border border-gray-300">
                            <i class="fas fa-calculator mr-2"></i>Open Tax Calculator
                        </button>
                        
                        <button type="button" onclick="autoApplyTaxes()" 
                                class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium">
                            <i class="fas fa-magic mr-2"></i>Auto Apply Taxes
                        </button>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-lg p-6 border border-gray-200">
                    <h4 class="text-md font-bold text-gray-900 mb-4">
                        <i class="fas fa-bolt mr-2"></i>Quick Actions
                    </h4>
                    
                    <div class="space-y-3">
                        <button type="button" onclick="duplicateLastInvoice()" 
                                class="w-full bg-gray-100 hover:bg-gray-200 text-gray-900 py-2 px-4 rounded-lg font-medium text-sm">
                            <i class="fas fa-copy mr-2"></i>Duplicate Last Invoice
                        </button>
                        
                        <button type="button" onclick="loadTemplate()" 
                                class="w-full bg-gray-100 hover:bg-gray-200 text-gray-900 py-2 px-4 rounded-lg font-medium text-sm">
                            <i class="fas fa-file-import mr-2"></i>Load Template
                        </button>
                        
                        <button type="button" onclick="saveAsTemplate()" 
                                class="w-full bg-gray-100 hover:bg-gray-200 text-gray-900 py-2 px-4 rounded-lg font-medium text-sm">
                            <i class="fas fa-save mr-2"></i>Save as Template
                        </button>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="bg-white rounded-lg p-6 border border-gray-200">
                    <div class="space-y-3">
                        <button type="submit" name="action" value="save_draft" 
                                class="w-full bg-gray-600 hover:bg-gray-700 text-white py-3 px-4 rounded-lg font-medium">
                            <i class="fas fa-save mr-2"></i>Save as Draft
                        </button>
                        
                        <button type="submit" name="action" value="submit" 
                                class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium">
                            <i class="fas fa-paper-plane mr-2"></i>Submit Invoice
                        </button>
                        
                        {% if object and object.status == 'draft' %}
                        <button type="submit" name="action" value="approve" 
                                class="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg font-medium">
                            <i class="fas fa-check mr-2"></i>Approve Invoice
                        </button>
                        {% endif %}
                        
                        <button type="button" onclick="previewInvoice()" 
                                class="w-full bg-purple-600 hover:bg-purple-700 text-white py-3 px-4 rounded-lg font-medium">
                            <i class="fas fa-eye mr-2"></i>Preview Invoice
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Tax Calculator Modal -->
<div id="tax-calculator-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-bold text-gray-900 mb-4">Invoice Tax Calculator</h3>
            <div id="tax-calculator-content">
                <!-- Tax calculator content will be loaded here -->
            </div>
            <div class="flex justify-end space-x-3 mt-6">
                <button onclick="closeTaxCalculator()" 
                        class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-2 rounded-lg font-medium">
                    Close
                </button>
                <button onclick="applyCalculatedTaxes()" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium">
                    Apply to Invoice
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Customer Lookup Modal -->
<div id="customer-lookup-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-bold text-gray-900 mb-4">Customer Lookup</h3>
            <div id="customer-lookup-content">
                <!-- Customer lookup content will be loaded here -->
            </div>
            <div class="flex justify-end space-x-3 mt-6">
                <button onclick="closeCustomerLookup()" 
                        class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-2 rounded-lg font-medium">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function invoiceForm() {
    return {
        lineItemCount: 1,
        
        init() {
            this.updateTotals();
        }
    }
}

let lineItemIndex = {% if object.invoice_details.all %}{{ object.invoice_details.all|length }}{% else %}1{% endif %};

// Add new line item
function addLineItem() {
    const container = document.getElementById('line-items-container');
    
    fetch('{% url "accounts:ajax_get_line_item_template" %}?index=' + lineItemIndex)
        .then(response => response.text())
        .then(html => {
            const newItem = document.createElement('div');
            newItem.className = 'line-item-row';
            newItem.id = 'line-item-' + lineItemIndex;
            newItem.innerHTML = html;
            container.appendChild(newItem);
            lineItemIndex++;
        });
}

// Remove line item
function removeLineItem(index) {
    const item = document.getElementById('line-item-' + index);
    if (item) {
        item.remove();
        updateInvoiceTotals();
    }
}

// Calculate line total
function calculateLineTotal(index) {
    const qty = parseFloat(document.getElementById('quantity-' + index).value) || 0;
    const rate = parseFloat(document.getElementById('rate-' + index).value) || 0;
    const vatRate = parseFloat(document.getElementById('vat-rate-' + index).value) || 0;
    
    const amount = qty * rate;
    const vatAmount = (amount * vatRate) / 100;
    const lineTotal = amount + vatAmount;
    
    document.getElementById('amount-' + index).value = amount.toFixed(2);
    document.getElementById('vat-amount-' + index).value = vatAmount.toFixed(2);
    document.getElementById('line-total-' + index).value = lineTotal.toFixed(2);
    
    // Update line total display
    document.getElementById('line-total-display-' + index).textContent = '₹' + lineTotal.toLocaleString('en-IN', {minimumFractionDigits: 2});
    
    // Trigger invoice totals update
    updateInvoiceTotals();
}

// Update invoice totals
function updateInvoiceTotals() {
    // This will trigger HTMX to update totals
    htmx.trigger('#sales-invoice-form', 'change');
}

// Calculate discount amount from percentage
function calculateDiscountAmount() {
    const discountPercentage = parseFloat(document.getElementById('discount_percentage').value) || 0;
    const basicAmount = parseFloat(document.getElementById('basic-amount-display').textContent.replace(/[₹,]/g, '')) || 0;
    
    const discountAmount = (basicAmount * discountPercentage) / 100;
    document.getElementById('discount_amount').value = discountAmount.toFixed(2);
    
    updateInvoiceTotals();
}

// Tax Calculator Modal
function openTaxCalculator() {
    const modal = document.getElementById('tax-calculator-modal');
    
    // Load tax calculator content
    fetch('{% url "accounts:ajax_tax_calculator_widget" %}')
        .then(response => response.text())
        .then(html => {
            document.getElementById('tax-calculator-content').innerHTML = html;
            modal.classList.remove('hidden');
        });
}

function closeTaxCalculator() {
    document.getElementById('tax-calculator-modal').classList.add('hidden');
}

function applyCalculatedTaxes() {
    // Get calculated tax values and apply to invoice
    // Implementation depends on tax calculator structure
    closeTaxCalculator();
}

// Auto apply taxes based on defaults
function autoApplyTaxes() {
    fetch('{% url "accounts:ajax_get_default_taxes" %}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Apply default tax rates to all line items
                applyDefaultTaxesToLineItems(data.defaults);
                updateInvoiceTotals();
            }
        });
}

function applyDefaultTaxesToLineItems(defaults) {
    const lineItems = document.querySelectorAll('[id^="line-item-"]');
    lineItems.forEach((item, index) => {
        if (defaults.vat_rate) {
            const vatField = item.querySelector('[id^="vat-rate-"]');
            if (vatField) vatField.value = defaults.vat_rate;
        }
        
        calculateLineTotal(index);
    });
}

// Customer Lookup Modal
function openCustomerLookup() {
    const modal = document.getElementById('customer-lookup-modal');
    
    fetch('{% url "accounts:ajax_customer_lookup_widget" %}')
        .then(response => response.text())
        .then(html => {
            document.getElementById('customer-lookup-content').innerHTML = html;
            modal.classList.remove('hidden');
        });
}

function closeCustomerLookup() {
    document.getElementById('customer-lookup-modal').classList.add('hidden');
}

function selectCustomer(customerId, customerName, customerData) {
    document.getElementById('customer_id').value = customerId;
    document.getElementById('customer_name').value = customerName;
    
    // Populate additional customer details if available
    if (customerData) {
        // Implementation depends on customer data structure
    }
    
    closeCustomerLookup();
}

// Quick Actions
function duplicateLastInvoice() {
    if (confirm('This will replace current data with the last invoice. Continue?')) {
        fetch('{% url "accounts:ajax_get_last_invoice" %}')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    populateInvoiceData(data.invoice);
                }
            });
    }
}

function loadTemplate() {
    // Implementation for loading invoice templates
    alert('Template loading feature coming soon!');
}

function saveAsTemplate() {
    const templateName = prompt('Enter template name:');
    if (templateName) {
        const formData = new FormData(document.getElementById('sales-invoice-form'));
        formData.append('template_name', templateName);
        
        fetch('{% url "accounts:ajax_save_invoice_template" %}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Template saved successfully!');
            } else {
                alert('Error saving template: ' + data.error);
            }
        });
    }
}

function previewInvoice() {
    // Open invoice preview in new window
    const form = document.getElementById('sales-invoice-form');
    const formData = new FormData(form);
    
    // Create a temporary form for preview
    const previewForm = document.createElement('form');
    previewForm.method = 'POST';
    previewForm.action = '{% url "accounts:ajax_preview_invoice" %}';
    previewForm.target = '_blank';
    
    // Add CSRF token
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrfmiddlewaretoken';
    csrfInput.value = document.querySelector('[name=csrfmiddlewaretoken]').value;
    previewForm.appendChild(csrfInput);
    
    // Add form data
    for (let [key, value] of formData.entries()) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value;
        previewForm.appendChild(input);
    }
    
    document.body.appendChild(previewForm);
    previewForm.submit();
    document.body.removeChild(previewForm);
}

function populateInvoiceData(invoiceData) {
    // Implementation to populate form with invoice data
    Object.keys(invoiceData).forEach(key => {
        const field = document.querySelector(`[name="${key}"]`);
        if (field) {
            field.value = invoiceData[key];
        }
    });
    
    updateInvoiceTotals();
}

// Initialize HTMX event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Listen for HTMX events
    document.body.addEventListener('htmx:beforeRequest', function(event) {
        // Show loading indicator
        const indicator = event.target.closest('form')?.querySelector('.htmx-indicator');
        if (indicator) {
            indicator.style.opacity = '1';
        }
    });
    
    document.body.addEventListener('htmx:afterRequest', function(event) {
        // Hide loading indicator
        const indicator = event.target.closest('form')?.querySelector('.htmx-indicator');
        if (indicator) {
            indicator.style.opacity = '0';
        }
    });
});

// Auto-save functionality
let autoSaveTimer;
function scheduleAutoSave() {
    clearTimeout(autoSaveTimer);
    autoSaveTimer = setTimeout(() => {
        if (document.getElementById('sales-invoice-form').checkValidity()) {
            autoSaveInvoice();
        }
    }, 30000); // Auto-save every 30 seconds
}

function autoSaveInvoice() {
    const formData = new FormData(document.getElementById('sales-invoice-form'));
    formData.append('auto_save', 'true');
    
    fetch(window.location.href, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show auto-save indicator
            const indicator = document.createElement('div');
            indicator.className = 'fixed top-4 right-4 bg-green-100 text-green-800 px-3 py-2 rounded-lg text-sm';
            indicator.textContent = 'Auto-saved';
            document.body.appendChild(indicator);
            
            setTimeout(() => {
                document.body.removeChild(indicator);
            }, 2000);
        }
    })
    .catch(error => console.error('Auto-save failed:', error));
}

// Start auto-save scheduling
document.addEventListener('input', scheduleAutoSave);
document.addEventListener('change', scheduleAutoSave);
</script>
{% endblock %}