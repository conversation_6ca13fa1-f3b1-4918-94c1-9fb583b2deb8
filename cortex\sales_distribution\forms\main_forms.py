# sales_distribution/forms.py
# Django forms for Sales Distribution module
# Based on ASP.NET CategoryEdit.aspx functionality

from django import forms
from django.core.exceptions import ValidationError
from ..models import WorkOrderCategory, WorkorderSubcategory, Customer, Product, Enquiry, Quotation, QuotationDetails, Unit, WOType, PurchaseOrder, PurchaseOrderDetails


class WorkOrderCategoryForm(forms.ModelForm):
    """
    Form for WorkOrder Category management
    Replaces ASP.NET CategoryEdit.aspx form functionality
    """

    class Meta:
        model = WorkOrderCategory
        fields = ["cname", "symbol", "hassubcat"]
        widgets = {
            "cname": forms.TextInput(
                attrs={
                    "class": "w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-transparent",
                    "placeholder": "Enter category name",
                    "maxlength": "100",
                }
            ),
            "symbol": forms.TextInput(
                attrs={
                    "class": "w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-transparent",
                    "placeholder": "Enter symbol (1 char)",
                    "maxlength": "1",
                }
            ),
            "hassubcat": forms.CheckboxInput(
                attrs={
                    "class": "w-4 h-4 text-sap-blue-600 border-sap-gray-300 rounded focus:ring-sap-blue-500 focus:ring-2"
                }
            ),
        }
        labels = {"cname": "Category Name", "symbol": "Symbol", "hassubcat": "Has Sub-Category"}

    def clean_cname(self):
        """Validate category name"""
        cname = self.cleaned_data.get("cname")
        if not cname or not cname.strip():
            raise ValidationError("Category name is required")
        return cname.strip()

    def clean_symbol(self):
        """Validate symbol - matches ASP.NET CategoryNew.aspx logic"""
        symbol = self.cleaned_data.get("symbol")
        if not symbol or not symbol.strip():
            raise ValidationError("Symbol is required")

        symbol = symbol.strip().upper()  # Convert to uppercase like ASP.NET

        # Limit to 1 character like ASP.NET MaxLength="1"
        if len(symbol) > 1:
            raise ValidationError("Symbol must be only 1 character")

        # Check for duplicate symbols (excluding current instance during edit)
        # Matches ASP.NET validation: "Category symbol is already used."
        qs = WorkOrderCategory.objects.filter(symbol=symbol)
        if self.instance.pk:
            qs = qs.exclude(pk=self.instance.pk)
        if qs.exists():
            raise ValidationError("Category symbol is already used.")

        return symbol


class WorkOrderCategoryEditForm(forms.ModelForm):
    """
    Inline edit form for WorkOrder Category
    Used for HTMX-based inline editing similar to ASP.NET GridView edit functionality
    """

    class Meta:
        model = WorkOrderCategory
        fields = ["cname", "hassubcat"]
        widgets = {
            "cname": forms.TextInput(
                attrs={
                    "class": "w-full px-2 py-1 text-sm border border-sap-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-sap-blue-500",
                    "placeholder": "Category name",
                }
            ),
            "hassubcat": forms.CheckboxInput(
                attrs={"class": "w-4 h-4 text-sap-blue-600 border-sap-gray-300 rounded focus:ring-sap-blue-500"}
            ),
        }

    def clean_cname(self):
        """Validate category name"""
        cname = self.cleaned_data.get("cname")
        if not cname or not cname.strip():
            raise ValidationError("Category name is required")
        return cname.strip()


class WorkOrderSubcategoryForm(forms.ModelForm):
    """
    Form for WorkOrder Sub-Category management
    """

    class Meta:
        model = WorkorderSubcategory
        fields = ["cid", "sub_c_name"]
        widgets = {
            "cid": forms.Select(
                attrs={
                    "class": "w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-transparent"
                }
            ),
            "sub_c_name": forms.TextInput(
                attrs={
                    "class": "w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-transparent",
                    "placeholder": "Enter sub-category name",
                }
            ),
        }
        labels = {"cid": "Category", "sub_c_name": "Sub-Category Name"}

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Filter categories that have subcategories enabled
        self.fields["cid"].queryset = WorkOrderCategory.objects.filter(hassubcat="1")
        self.fields['cid'].label_from_instance = lambda obj: f"{obj.cname}"


class WorkOrderSubcategoryEditForm(forms.ModelForm):
    """
    Inline edit form for WorkOrder Sub-Category
    """
    class Meta:
        model = WorkorderSubcategory
        fields = ['sub_c_name', 'cid']
        widgets = {
            'sub_c_name': forms.TextInput(attrs={
                'class': 'w-full px-2 py-1 text-sm border border-sap-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-sap-blue-500',
                'placeholder': 'Sub-category name',
            }),
            'cid': forms.Select(attrs={
                'class': 'w-full px-2 py-1 text-sm border border-sap-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-sap-blue-500',
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['cid'].queryset = WorkOrderCategory.objects.filter(hassubcat='1')
        self.fields['cid'].label_from_instance = lambda obj: f"{obj.cname}"

class SubCategoryFilterForm(forms.Form):
    """
    Filter form for SubCategory search
    """
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(
            attrs={
                "class": "block w-full pl-10 pr-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm placeholder-sap-gray-500 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                "placeholder": "Search sub-categories...",
                "autocomplete": "off",
            }
        ),
    )


class WOTypeForm(forms.ModelForm):
    """
    Form for Work Order Type management
    """
    class Meta:
        model = WOType
        fields = ['wo_type']
        widgets = {
            'wo_type': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-transparent',
                'placeholder': 'Enter work order type',
            }),
        }
        labels = {
            'wo_type': 'Work Order Type',
        }

class WOTypeEditForm(forms.ModelForm):
    """
    Inline edit form for Work Order Type
    """
    class Meta:
        model = WOType
        fields = ['wo_type']
        widgets = {
            'wo_type': forms.TextInput(attrs={
                'class': 'w-full px-2 py-1 text-sm border border-sap-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-sap-blue-500',
                'placeholder': 'Work order type',
            }),
        }

class WOTypeFilterForm(forms.Form):
    """
    Filter form for WOType search
    """
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(
            attrs={
                "class": "block w-full pl-10 pr-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm placeholder-sap-gray-500 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                "placeholder": "Search work order types...",
                "autocomplete": "off",
            }
        ),
    )
    category = forms.ModelChoiceField(
        queryset=WorkOrderCategory.objects.filter(hassubcat='1'),
        required=False,
        widget=forms.Select(
            attrs={
                "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"
            }
        ),
        label="Category"
    )


class CustomerPOForm(forms.ModelForm):
    """
    Comprehensive Customer PO Form - replicates ASP.NET CustPO_New_Details functionality
    Tab-based form with Customer Details, Goods Details, and Terms & Conditions
    """
    
    # Customer Details Tab (Tab 1)
    quotationno = forms.ModelChoiceField(
        queryset=Quotation.objects.none(),
        empty_label="Select Quotation",
        required=True,
        widget=forms.Select(attrs={'class': 'sap-input'})
    )
    
    # Goods Details Tab (Tab 2) - handled by formset
    
    # Terms & Conditions Tab (Tab 3)
    attachment = forms.FileField(
        required=False,
        widget=forms.FileInput(attrs={'class': 'sap-input', 'accept': '.pdf,.doc,.docx,.xls,.xlsx,.jpg,.png'})
    )
    
    class Meta:
        model = PurchaseOrder
        fields = [
            # Tab 1: Customer Details
            'quotationno', 'pono', 'podate', 'poreceiveddate', 'vendorcode',
            
            # Tab 3: Terms & Conditions - Legacy Tax Fields
            'paymentterms', 'pf', 'excise', 'vat', 'octroi', 'cst',
            'warrenty', 'insurance', 'transport', 'noteno', 'registrationno', 
            'freight', 'validity', 'othercharges', 'remarks'
        ]
        
        widgets = {
            # Tab 1: Customer Details
            'pono': forms.TextInput(attrs={'class': 'sap-input', 'required': True}),
            'podate': forms.DateInput(attrs={'type': 'date', 'class': 'sap-input', 'required': True}),
            'poreceiveddate': forms.DateInput(attrs={'type': 'date', 'class': 'sap-input', 'required': True}),
            'vendorcode': forms.TextInput(attrs={'class': 'sap-input', 'required': True}),
            
            # Tab 3: Terms & Conditions - Legacy Tax Fields
            'paymentterms': forms.TextInput(attrs={
                'class': 'sap-input', 
                'required': True,
                'placeholder': 'Enter payment terms'
            }),
            'pf': forms.TextInput(attrs={
                'class': 'sap-input', 
                'required': True,
                'placeholder': 'Packing & forwarding charges'
            }),
            'excise': forms.HiddenInput(attrs={
                'value': 'N/A'
            }),
            'vat': forms.HiddenInput(attrs={
                'value': 'N/A'
            }),
            'octroi': forms.HiddenInput(attrs={
                'value': 'N/A'
            }),
            'cst': forms.TextInput(attrs={
                'class': 'sap-input', 
                'required': True,
                'placeholder': 'GST percentage/amount'
            }),
            'warrenty': forms.TextInput(attrs={
                'class': 'sap-input', 
                'required': True,
                'placeholder': 'Warranty terms'
            }),
            'insurance': forms.TextInput(attrs={
                'class': 'sap-input', 
                'required': True,
                'placeholder': 'Insurance details'
            }),
            'transport': forms.TextInput(attrs={
                'class': 'sap-input', 
                'required': True,
                'placeholder': 'Mode of transport'
            }),
            'noteno': forms.TextInput(attrs={
                'class': 'sap-input', 
                'required': True,
                'placeholder': 'R.R./G.C. note number'
            }),
            'registrationno': forms.TextInput(attrs={
                'class': 'sap-input',
                'placeholder': 'Vehicle registration number'
            }),
            'freight': forms.TextInput(attrs={
                'class': 'sap-input', 
                'required': True,
                'placeholder': 'Freight charges'
            }),
            'validity': forms.TextInput(attrs={
                'class': 'sap-input', 
                'required': True,
                'placeholder': 'Validity period'
            }),
            'othercharges': forms.TextInput(attrs={
                'class': 'sap-input', 
                'required': True,
                'placeholder': 'Other charges'
            }),
            'remarks': forms.Textarea(attrs={
                'class': 'sap-input', 
                'rows': 3,
                'placeholder': 'Additional remarks'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        enq_id = kwargs.pop('enq_id', None)
        company_id = kwargs.pop('company_id', None)
        fin_year_id = kwargs.pop('fin_year_id', None)
        
        super().__init__(*args, **kwargs)
        
        # Filter quotations based on enquiry, company, and financial year
        if enq_id and company_id and fin_year_id:
            self.fields['quotationno'].queryset = Quotation.objects.filter(
                enqid_id=enq_id,
                compid_id=company_id,
                finyearid_id=fin_year_id
            ).order_by('-id')
    
    def clean_pono(self):
        pono = self.cleaned_data.get('pono')
        if not pono or not pono.strip():
            raise forms.ValidationError('PO Number is required.')
        return pono.strip()
    
    
    def clean_vendorcode(self):
        vendorcode = self.cleaned_data.get('vendorcode')
        if not vendorcode or not vendorcode.strip():
            raise forms.ValidationError('Vendor Code is required.')
        return vendorcode.strip()


class CustomerPODetailForm(forms.ModelForm):
    """
    Customer PO Details Form - replicates Tab 2 (Goods Details) functionality
    Used with formset for multiple line items
    """
    
    class Meta:
        model = PurchaseOrderDetails
        fields = ['itemdesc', 'totalqty', 'unit', 'rate', 'discount']
        
        widgets = {
            'itemdesc': forms.Textarea(attrs={
                'class': 'sap-input', 
                'rows': 3, 
                'required': True,
                'placeholder': 'Description & Specification'
            }),
            'totalqty': forms.NumberInput(attrs={
                'class': 'sap-input', 
                'step': '0.001', 
                'min': '0',
                'required': True,
                'placeholder': '0.000'
            }),
            'unit': forms.Select(attrs={'class': 'sap-input', 'required': True}),
            'rate': forms.NumberInput(attrs={
                'class': 'sap-input', 
                'step': '0.001', 
                'min': '0',
                'required': True,
                'placeholder': '0.000'
            }),
            'discount': forms.NumberInput(attrs={
                'class': 'sap-input', 
                'step': '0.001', 
                'min': '0', 
                'max': '100',
                'required': True,
                'placeholder': '0.000'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Load unit choices from database
        from ..models import Unit
        self.fields['unit'].queryset = Unit.objects.all().order_by('unitname')
    
    def clean_totalqty(self):
        qty = self.cleaned_data.get('totalqty')
        if qty is None or qty <= 0:
            raise forms.ValidationError('Total Quantity must be greater than 0.')
        if qty > 999999999.999:
            raise forms.ValidationError('Total Quantity is too large.')
        return qty
    
    def clean_rate(self):
        rate = self.cleaned_data.get('rate')
        if rate is None or rate <= 0:
            raise forms.ValidationError('Rate must be greater than 0.')
        if rate > 999999999.999:
            raise forms.ValidationError('Rate is too large.')
        return rate
    
    def clean_discount(self):
        discount = self.cleaned_data.get('discount')
        if discount is None:
            return 0.0
        if discount < 0 or discount > 100:
            raise forms.ValidationError('Discount must be between 0 and 100 percent.')
        return discount


# Formset for handling multiple PO details
CustomerPODetailFormSet = forms.inlineformset_factory(
    PurchaseOrder,
    PurchaseOrderDetails,
    form=CustomerPODetailForm,
    extra=1,
    min_num=1,
    validate_min=True,
    can_delete=True
)


class CustomerForm(forms.ModelForm):
    """
    Form for Customer management - Django replacement for ASP.NET CustomerMaster_New.aspx
    Comprehensive form with all three address sections: Registered, Works, Material Delivery
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Initialize Country choices for all three address sections
        from sys_admin.models import Country, State, City

        country_choices = [("", "Select Country")] + [
            (c.cid, c.countryname) for c in Country.objects.all().order_by("countryname")
        ]

        self.fields["registered_country"].choices = country_choices
        self.fields["works_country"].choices = country_choices
        self.fields["material_country"].choices = country_choices

        # If editing an existing customer, populate state and city choices based on selected countries
        if self.instance and self.instance.pk:
            # Registered address states and cities
            if self.instance.registered_country:
                reg_states = State.objects.filter(cid=self.instance.registered_country).order_by("statename")
                self.fields["registered_state"].choices = [("", "Select State")] + [
                    (s.sid, s.statename) for s in reg_states
                ]

                if self.instance.registered_state:
                    reg_cities = City.objects.filter(sid=self.instance.registered_state).order_by("cityname")
                    self.fields["registered_city"].choices = [("", "Select City")] + [
                        (c.cityid, c.cityname) for c in reg_cities
                    ]
                else:
                    self.fields["registered_city"].choices = [("", "Select City")]
            else:
                self.fields["registered_state"].choices = [("", "Select State")]
                self.fields["registered_city"].choices = [("", "Select City")]

            # Works address states and cities
            if self.instance.works_country:
                work_states = State.objects.filter(cid=self.instance.works_country).order_by("statename")
                self.fields["works_state"].choices = [("", "Select State")] + [
                    (s.sid, s.statename) for s in work_states
                ]

                if self.instance.works_state:
                    work_cities = City.objects.filter(sid=self.instance.works_state).order_by("cityname")
                    self.fields["works_city"].choices = [("", "Select City")] + [
                        (c.cityid, c.cityname) for c in work_cities
                    ]
                else:
                    self.fields["works_city"].choices = [("", "Select City")]
            else:
                self.fields["works_state"].choices = [("", "Select State")]
                self.fields["works_city"].choices = [("", "Select City")]

            # Material address states and cities
            if self.instance.material_country:
                mat_states = State.objects.filter(cid=self.instance.material_country).order_by("statename")
                self.fields["material_state"].choices = [("", "Select State")] + [
                    (s.sid, s.statename) for s in mat_states
                ]

                if self.instance.material_state:
                    mat_cities = City.objects.filter(sid=self.instance.material_state).order_by("cityname")
                    self.fields["material_city"].choices = [("", "Select City")] + [
                        (c.cityid, c.cityname) for c in mat_cities
                    ]
                else:
                    self.fields["material_city"].choices = [("", "Select City")]
            else:
                self.fields["material_state"].choices = [("", "Select State")]
                self.fields["material_city"].choices = [("", "Select City")]
        else:
            # For new customers, initialize empty choices (will be populated via HTMX)
            empty_choices = [("", "Select State")]
            self.fields["registered_state"].choices = empty_choices
            self.fields["works_state"].choices = empty_choices
            self.fields["material_state"].choices = empty_choices

            empty_city_choices = [("", "Select City")]
            self.fields["registered_city"].choices = empty_city_choices
            self.fields["works_city"].choices = empty_city_choices
            self.fields["material_city"].choices = empty_city_choices

    class Meta:
        model = Customer
        fields = [
            "customer_name",
            "registered_address",
            "registered_country",
            "registered_state",
            "registered_city",
            "registered_pin",
            "registered_contact_no",
            "works_address",
            "works_country",
            "works_state",
            "works_city",
            "works_pin",
            "works_contact_no",
            "material_address",
            "material_country",
            "material_state",
            "material_city",
            "material_pin",
            "material_contact_no",
            "contact_person",
            "email",
            "contact_no",
            "tincstno",
            "remarks",
        ]
        widgets = {
            # Customer Name
            "customer_name": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "placeholder": "Customer's Name",
                    "required": True,
                }
            ),
            # Registered Office Section
            "registered_address": forms.Textarea(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "rows": 3,
                    "placeholder": "Registered Office Address",
                    "required": True,
                }
            ),
            "registered_country": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "hx-get": "/sales-distribution/ajax/get-states/",
                    "hx-target": "#id_registered_state",
                    "hx-trigger": "change",
                    "hx-include": '[name="registered_country"]',
                }
            ),
            "registered_state": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "hx-get": "/sales-distribution/ajax/get-cities/",
                    "hx-target": "#id_registered_city",
                    "hx-trigger": "change",
                    "hx-include": '[name="registered_state"]',
                }
            ),
            "registered_city": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"
                }
            ),
            "registered_pin": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "placeholder": "PIN Code",
                }
            ),
            "registered_contact_no": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "placeholder": "Contact Number",
                }
            ),
            # Works/Factory Section
            "works_address": forms.Textarea(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "rows": 3,
                    "placeholder": "Works/Factory Address",
                }
            ),
            "works_country": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "hx-get": "/sales-distribution/ajax/get-states/",
                    "hx-target": "#id_works_state",
                    "hx-trigger": "change",
                    "hx-include": '[name="works_country"]',
                }
            ),
            "works_state": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "hx-get": "/sales-distribution/ajax/get-cities/",
                    "hx-target": "#id_works_city",
                    "hx-trigger": "change",
                    "hx-include": '[name="works_state"]',
                }
            ),
            "works_city": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"
                }
            ),
            "works_pin": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "placeholder": "PIN Code",
                }
            ),
            "works_contact_no": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "placeholder": "Contact Number",
                }
            ),
            # Material Delivery Section
            "material_address": forms.Textarea(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "rows": 3,
                    "placeholder": "Material Delivery Address",
                }
            ),
            "material_country": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "hx-get": "/sales-distribution/ajax/get-states/",
                    "hx-target": "#id_material_state",
                    "hx-trigger": "change",
                    "hx-include": '[name="material_country"]',
                }
            ),
            "material_state": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "hx-get": "/sales-distribution/ajax/get-cities/",
                    "hx-target": "#id_material_city",
                    "hx-trigger": "change",
                    "hx-include": '[name="material_state"]',
                }
            ),
            "material_city": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"
                }
            ),
            "material_pin": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "placeholder": "PIN Code",
                }
            ),
            "material_contact_no": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "placeholder": "Contact Number",
                }
            ),
            # Additional Details Section
            "contact_person": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "placeholder": "Contact Person",
                }
            ),
            "email": forms.EmailInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "placeholder": "Email Address",
                }
            ),
            "contact_no": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "placeholder": "Contact Number",
                }
            ),
            "tincstno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "placeholder": "TIN/CST Number",
                }
            ),
            "remarks": forms.Textarea(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                    "rows": 2,
                    "placeholder": "Remarks",
                }
            ),
        }

    def clean_email(self):
        """Email validation matching ASP.NET regex"""
        email = self.cleaned_data.get("email")
        if email:
            # ASP.NET regex: \w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*
            import re

            pattern = r"\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*"
            if not re.match(pattern, email):
                raise forms.ValidationError("Please enter a valid email address.")
        return email

    def clean_customer_name(self):
        """Customer name is required"""
        name = self.cleaned_data.get("customer_name")
        if not name or not name.strip():
            raise forms.ValidationError("Customer name is required.")
        return name.upper()  # Convert to uppercase like ASP.NET


class CustomerFilterForm(forms.Form):
    """
    Filter form for Customer search - replaces ASP.NET TxtSearchValue and AutoComplete functionality
    """

    search = forms.CharField(
        required=False,
        widget=forms.TextInput(
            attrs={
                "class": "block w-full pl-10 pr-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm placeholder-sap-gray-500 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500",
                "placeholder": "Search customers by name, ID, or address...",
                "autocomplete": "off",
            }
        ),
    )


class WorkOrderCategoryFilterForm(forms.Form):
    """
    Filter form for WorkOrder Category list
    """

    search = forms.CharField(
        required=False,
        widget=forms.TextInput(
            attrs={
                "class": "w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-transparent",
                "placeholder": "Search categories...",
            }
        ),
    )

    has_subcategory = forms.ChoiceField(
        choices=[("", "All"), ("1", "Yes"), ("0", "No")],
        required=False,
        widget=forms.Select(
            attrs={
                "class": "px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-transparent"
            }
        ),
    )


class ProductForm(forms.ModelForm):
    """
    Form for Product Master - Replaces ASP.NET Product.aspx functionality
    Simple category-based product management
    """

    class Meta:
        model = Product
        fields = ["name"]
        widgets = {
            "name": forms.TextInput(
                attrs={
                    "class": "w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "Enter product name",
                    "maxlength": "100",
                }
            ),
        }
        labels = {"name": "Product Name"}

    def clean_name(self):
        """Validate product name"""
        name = self.cleaned_data.get("name")
        if not name or not name.strip():
            raise ValidationError("Product name is required")
        return name.strip()


class ProductFilterForm(forms.Form):
    """
    Filter form for Product search
    """

    search = forms.CharField(
        required=False,
        widget=forms.TextInput(
            attrs={
                "class": "w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                "placeholder": "Search products...",
            }
        ),
    )


class EnquiryForm(forms.ModelForm):
    """
    Form for Customer Enquiry - Replaces ASP.NET CustEnquiry_New.aspx functionality
    Comprehensive enquiry form with customer details and multiple address sections
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Initialize Country choices for all three address sections
        from sys_admin.models import Country

        country_choices = [("", "Select Country")] + [
            (c.cid, c.countryname) for c in Country.objects.all().order_by("countryname")
        ]

        self.fields["regdcountry"].choices = country_choices
        self.fields["workcountry"].choices = country_choices
        self.fields["materialdelcountry"].choices = country_choices

        # Initialize empty state and city choices (will be populated via HTMX)
        empty_choices = [("", "Select State")]
        self.fields["regdstate"].choices = empty_choices
        self.fields["workstate"].choices = empty_choices
        self.fields["materialdelstate"].choices = empty_choices

        empty_city_choices = [("", "Select City")]
        self.fields["regdcity"].choices = empty_city_choices
        self.fields["workcity"].choices = empty_city_choices
        self.fields["materialdelcity"].choices = empty_city_choices

    class Meta:
        model = Enquiry
        fields = [
            # Customer Information
            "customerid",
            "customername",
            "contactperson",
            "email",
            "contactno",
            # Registered Address
            "regdaddress",
            "regdcountry",
            "regdstate",
            "regdcity",
            "regdpinno",
            "regdcontactno",
            "regdfaxno",
            # Work Address
            "workaddress",
            "workcountry",
            "workstate",
            "workcity",
            "workpinno",
            "workcontactno",
            "workfaxno",
            # Material Delivery Address
            "materialdeladdress",
            "materialdelcountry",
            "materialdelstate",
            "materialdelcity",
            "materialdelpinno",
            "materialdelcontactno",
            "materialdelfaxno",
            # Business Information
            "juridictioncode",
            "commissionurate",
            "tinvatno",
            "eccno",
            "divn",
            "tincstno",
            "range",
            "panno",
            "tdscode",
            # Enquiry Details
            "enquiryfor",
            "remark",
        ]
        
        widgets = {
            # Customer Information
            "customerid": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "Customer ID",
                }
            ),
            "customername": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "Customer Name",
                    "required": True,
                }
            ),
            "contactperson": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "Contact Person",
                }
            ),
            "email": forms.EmailInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "Email Address",
                }
            ),
            "contactno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "Contact Number",
                }
            ),
            
            # Registered Address
            "regdaddress": forms.Textarea(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "rows": 3,
                    "placeholder": "Registered Office Address",
                    "required": True,
                }
            ),
            "regdcountry": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                }
            ),
            "regdstate": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                }
            ),
            "regdcity": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                }
            ),
            "regdpinno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "PIN Code",
                }
            ),
            "regdcontactno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "Contact Number",
                }
            ),
            "regdfaxno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "Fax Number",
                }
            ),
            
            # Work Address
            "workaddress": forms.Textarea(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "rows": 3,
                    "placeholder": "Works/Factory Address",
                }
            ),
            "workcountry": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                }
            ),
            "workstate": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                }
            ),
            "workcity": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                }
            ),
            "workpinno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "PIN Code",
                }
            ),
            "workcontactno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "Contact Number",
                }
            ),
            "workfaxno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "Fax Number",
                }
            ),
            
            # Material Delivery Address
            "materialdeladdress": forms.Textarea(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "rows": 3,
                    "placeholder": "Material Delivery Address",
                }
            ),
            "materialdelcountry": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                }
            ),
            "materialdelstate": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                }
            ),
            "materialdelcity": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                }
            ),
            "materialdelpinno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "PIN Code",
                }
            ),
            "materialdelcontactno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "Contact Number",
                }
            ),
            "materialdelfaxno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "Fax Number",
                }
            ),
            
            # Business Information
            "juridictioncode": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "Jurisdiction Code",
                }
            ),
            "commissionurate": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "Commission Rate",
                }
            ),
            "tinvatno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "TIN/VAT Number",
                }
            ),
            "eccno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "ECC Number",
                }
            ),
            "divn": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "Division",
                }
            ),
            "tincstno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "TIN/CST Number",
                }
            ),
            "range": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "Range",
                }
            ),
            "panno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "PAN Number",
                }
            ),
            "tdscode": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "TDS Code",
                }
            ),
            
            # Enquiry Details
            "enquiryfor": forms.Textarea(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "rows": 4,
                    "placeholder": "Describe the enquiry in detail...",
                    "required": True,
                }
            ),
            "remark": forms.Textarea(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "rows": 3,
                    "placeholder": "Additional remarks",
                }
            ),
        }

    def clean_customername(self):
        """Customer name is required"""
        name = self.cleaned_data.get("customername")
        if not name or not name.strip():
            raise forms.ValidationError("Customer name is required.")
        return name.upper()  # Convert to uppercase like ASP.NET

    def clean_enquiryfor(self):
        """Enquiry description is required"""
        enquiry = self.cleaned_data.get("enquiryfor")
        if not enquiry or not enquiry.strip():
            raise forms.ValidationError("Enquiry description is required.")
        return enquiry.strip()


class QuotationForm(forms.ModelForm):
    """
    Form for Quotation management - replaces ASP.NET Quotation_New.aspx functionality
    Comprehensive quotation form with customer, financial, and terms sections
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Initialize Unit choices for quotation details
        unit_choices = [("", "Select Unit")] + [
            (u.id, f"{u.unitname} ({u.symbol})") for u in Unit.objects.all().order_by("unitname")
        ]
        # This will be used in the template for dynamic unit selection

    class Meta:
        model = Quotation
        fields = [
            # Customer and basic info
            "customerid",
            "enqid",
            "quotationno",
            
            # Financial terms
            "paymentterms",
            "pftype",
            "pf",
            "vatcst",
            "excise",
            "octroitype",
            "octroi",
            "warrenty",
            "insurance",
            "validity",
            "otherchargestype",
            "othercharges",
            "transport",
            "noteno",
            "registrationno",
            "freighttype",
            "freight",
            "remarks",
            "deliveryterms",
            "duedate",
        ]
        
        widgets = {
            # Basic Information
            "customerid": forms.HiddenInput(),
            "enqid": forms.HiddenInput(),
            "quotationno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "readonly": True,
                    "placeholder": "Auto-generated",
                }
            ),
            
            # Terms and Conditions
            "paymentterms": forms.Textarea(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "rows": 3,
                    "placeholder": "Payment terms and conditions",
                    "required": True,
                }
            ),
            
            # Financial Fields - PF
            "pftype": forms.Select(
                choices=[(0, "Fixed"), (1, "Percentage")],
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                }
            ),
            "pf": forms.NumberInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "step": "0.01",
                    "min": "0",
                    "placeholder": "PF amount/percentage",
                    "required": True,
                }
            ),
            
            # VAT/CST
            "vatcst": forms.NumberInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "step": "0.01",
                    "min": "0",
                    "placeholder": "VAT/CST percentage",
                }
            ),
            
            # Excise
            "excise": forms.NumberInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "step": "0.01",
                    "min": "0",
                    "placeholder": "Excise percentage",
                }
            ),
            
            # Octroi
            "octroitype": forms.Select(
                choices=[(0, "Fixed"), (1, "Percentage")],
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                }
            ),
            "octroi": forms.NumberInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "step": "0.01",
                    "min": "0",
                    "placeholder": "Octroi amount/percentage",
                    "required": True,
                }
            ),
            
            # Warranty
            "warrenty": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "Warranty terms",
                    "required": True,
                }
            ),
            
            # Insurance
            "insurance": forms.NumberInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "step": "0.01",
                    "min": "0",
                    "placeholder": "Insurance amount",
                    "required": True,
                }
            ),
            
            # Validity
            "validity": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "Quotation validity period",
                    "required": True,
                }
            ),
            
            # Other Charges
            "otherchargestype": forms.Select(
                choices=[(0, "Fixed"), (1, "Percentage")],
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                }
            ),
            "othercharges": forms.NumberInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "step": "0.01",
                    "min": "0",
                    "placeholder": "Other charges amount/percentage",
                    "required": True,
                }
            ),
            
            # Transport
            "transport": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "Transport details",
                    "required": True,
                }
            ),
            
            # Note Number
            "noteno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "Note number",
                    "required": True,
                }
            ),
            
            # Registration Number
            "registrationno": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "placeholder": "Registration number",
                }
            ),
            
            # Freight
            "freighttype": forms.Select(
                choices=[(0, "Fixed"), (1, "Percentage")],
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                }
            ),
            "freight": forms.NumberInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "step": "0.01",
                    "min": "0",
                    "placeholder": "Freight amount/percentage",
                    "required": True,
                }
            ),
            
            # Delivery Terms
            "deliveryterms": forms.Textarea(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "rows": 3,
                    "placeholder": "Delivery terms and conditions",
                    "required": True,
                }
            ),
            
            # Due Date
            "duedate": forms.DateInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "type": "date",
                }
            ),
            
            # Remarks
            "remarks": forms.Textarea(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "rows": 3,
                    "placeholder": "Additional remarks",
                }
            ),
        }

    def clean_paymentterms(self):
        """Payment terms is required"""
        terms = self.cleaned_data.get("paymentterms")
        if not terms or not terms.strip():
            raise forms.ValidationError("Payment terms are required.")
        return terms.strip()

    def clean_pf(self):
        """PF is required and must be numeric"""
        pf = self.cleaned_data.get("pf")
        if pf is None:
            raise forms.ValidationError("PF is required.")
        if pf < 0:
            raise forms.ValidationError("PF cannot be negative.")
        return pf

    def clean_octroi(self):
        """Octroi is required and must be numeric"""
        octroi = self.cleaned_data.get("octroi")
        if octroi is None:
            raise forms.ValidationError("Octroi is required.")
        if octroi < 0:
            raise forms.ValidationError("Octroi cannot be negative.")
        return octroi

    def clean_warrenty(self):
        """Warranty is required"""
        warranty = self.cleaned_data.get("warrenty")
        if not warranty or not warranty.strip():
            raise forms.ValidationError("Warranty terms are required.")
        return warranty.strip()

    def clean_insurance(self):
        """Insurance is required and must be numeric"""
        insurance = self.cleaned_data.get("insurance")
        if insurance is None:
            raise forms.ValidationError("Insurance is required.")
        if insurance < 0:
            raise forms.ValidationError("Insurance cannot be negative.")
        return insurance

    def clean_transport(self):
        """Transport is required"""
        transport = self.cleaned_data.get("transport")
        if not transport or not transport.strip():
            raise forms.ValidationError("Transport details are required.")
        return transport.strip()

    def clean_noteno(self):
        """Note number is required"""
        noteno = self.cleaned_data.get("noteno")
        if not noteno or not noteno.strip():
            raise forms.ValidationError("Note number is required.")
        return noteno.strip()

    def clean_freight(self):
        """Freight is required and must be numeric"""
        freight = self.cleaned_data.get("freight")
        if freight is None:
            raise forms.ValidationError("Freight is required.")
        if freight < 0:
            raise forms.ValidationError("Freight cannot be negative.")
        return freight

    def clean_validity(self):
        """Validity is required"""
        validity = self.cleaned_data.get("validity")
        if not validity or not validity.strip():
            raise forms.ValidationError("Validity period is required.")
        return validity.strip()

    def clean_othercharges(self):
        """Other charges is required and must be numeric"""
        othercharges = self.cleaned_data.get("othercharges")
        if othercharges is None:
            raise forms.ValidationError("Other charges is required.")
        if othercharges < 0:
            raise forms.ValidationError("Other charges cannot be negative.")
        return othercharges

    def clean_deliveryterms(self):
        """Delivery terms is required"""
        deliveryterms = self.cleaned_data.get("deliveryterms")
        if not deliveryterms or not deliveryterms.strip():
            raise forms.ValidationError("Delivery terms are required.")
        return deliveryterms.strip()

    def clean_duedate(self):
        """Due date validation - matches ASP.NET DateValidation requirement"""
        due_date = self.cleaned_data.get("duedate")
        if not due_date:
            raise forms.ValidationError("Due date is required.")
        return due_date


class QuotationDetailsForm(forms.ModelForm):
    """
    Form for Quotation line items - replaces ASP.NET quotation details grid functionality
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Populate unit choices
        self.fields["unit"].queryset = Unit.objects.all().order_by("unitname")

    class Meta:
        model = QuotationDetails
        fields = ["itemdesc", "totalqty", "unit", "rate", "discount"]
        
        widgets = {
            "itemdesc": forms.Textarea(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "rows": 2,
                    "placeholder": "Item description",
                    "required": True,
                }
            ),
            "totalqty": forms.NumberInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "step": "0.01",
                    "min": "0.01",
                    "placeholder": "Quantity",
                    "required": True,
                }
            ),
            "unit": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "required": True,
                }
            ),
            "rate": forms.NumberInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "step": "0.01",
                    "min": "0.01",
                    "placeholder": "Rate per unit",
                    "required": True,
                }
            ),
            "discount": forms.NumberInput(
                attrs={
                    "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    "step": "0.01",
                    "min": "0",
                    "placeholder": "Discount amount",
                }
            ),
        }

    def clean_itemdesc(self):
        """Item description is required"""
        desc = self.cleaned_data.get("itemdesc")
        if not desc or not desc.strip():
            raise forms.ValidationError("Item description is required.")
        return desc.strip()

    def clean_totalqty(self):
        """Quantity must be positive"""
        qty = self.cleaned_data.get("totalqty")
        if qty is None or qty <= 0:
            raise forms.ValidationError("Quantity must be greater than 0.")
        return qty

    def clean_rate(self):
        """Rate must be positive"""
        rate = self.cleaned_data.get("rate")
        if rate is None or rate <= 0:
            raise forms.ValidationError("Rate must be greater than 0.")
        return rate

    def clean_discount(self):
        """Discount cannot be negative"""
        discount = self.cleaned_data.get("discount")
        if discount is not None and discount < 0:
            raise forms.ValidationError("Discount cannot be negative.")
        return discount or 0


class EnquirySelectionForm(forms.Form):
    """
    Form for selecting enquiry for quotation creation - replaces ASP.NET Quotation_New.aspx enquiry selection
    """
    
    enquiry_id = forms.CharField(
        required=False,
        widget=forms.TextInput(
            attrs={
                "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                "placeholder": "Enter Enquiry ID",
            }
        ),
        label="Enquiry ID"
    )
    
    customer_id = forms.CharField(
        required=False,
        widget=forms.TextInput(
            attrs={
                "class": "block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                "placeholder": "Enter Customer ID",
            }
        ),
        label="Customer ID"
    )

    def clean(self):
        cleaned_data = super().clean()
        enquiry_id = cleaned_data.get("enquiry_id")
        customer_id = cleaned_data.get("customer_id")
        
        # At least one search criteria should be provided
        if not enquiry_id and not customer_id:
            raise forms.ValidationError("Please provide either Enquiry ID or Customer ID.")
        
        return cleaned_data


class QuotationFilterForm(forms.Form):
    """
    Filter form for Quotation list and search functionality
    """

    search = forms.CharField(
        required=False,
        widget=forms.TextInput(
            attrs={
                "class": "block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-lg text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                "placeholder": "Search quotations by customer, number, or enquiry...",
                "autocomplete": "off",
            }
        ),
    )
    
    status = forms.ChoiceField(
        choices=[
            ("", "All Status"),
            ("draft", "Draft"),
            ("checked", "Checked"),
            ("approved", "Approved"),
            ("authorized", "Authorized"),
        ],
        required=False,
        widget=forms.Select(
            attrs={
                "class": "px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            }
        ),
    )
