from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.http import JsonResponse
from django.db import transaction
from django.core.exceptions import PermissionDenied
from django.utils import timezone
from datetime import datetime, date

from ..models import (
    MaterialCreditNote, MCNApprovalHistory,
    Company, FinancialYear
)
from ..forms import (
    MaterialCreditNoteForm, MCNApprovalForm, 
    MCNSearchForm, MCNLineItemInlineFormSet
)


class MCNListView(LoginRequiredMixin, ListView):
    """List all Material Credit Notes"""
    model = MaterialCreditNote
    template_name = 'inventory/credit_notes/mcn_list.html'
    context_object_name = 'mcns'
    paginate_by = 25
    
    def get_queryset(self):
        queryset = MaterialCreditNote.objects.select_related(
            'company', 'financial_year', 'requested_by', 'approved_by'
        ).prefetch_related('line_items')
        
        # Apply search filters
        form = MCNSearchForm(self.request.GET)
        if form.is_valid():
            if form.cleaned_data.get('mcn_number'):
                queryset = queryset.filter(
                    mcn_number__icontains=form.cleaned_data['mcn_number']
                )
            if form.cleaned_data.get('status'):
                queryset = queryset.filter(status=form.cleaned_data['status'])
            if form.cleaned_data.get('adjustment_type'):
                queryset = queryset.filter(adjustment_type=form.cleaned_data['adjustment_type'])
            if form.cleaned_data.get('priority'):
                queryset = queryset.filter(priority=form.cleaned_data['priority'])
            if form.cleaned_data.get('date_from'):
                queryset = queryset.filter(mcn_date__gte=form.cleaned_data['date_from'])
            if form.cleaned_data.get('date_to'):
                queryset = queryset.filter(mcn_date__lte=form.cleaned_data['date_to'])
            if form.cleaned_data.get('requested_by'):
                queryset = queryset.filter(requested_by=form.cleaned_data['requested_by'])
                
        return queryset.order_by('-mcn_date', '-mcn_number')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = MCNSearchForm(self.request.GET)
        context['page_title'] = 'Material Credit Notes'
        
        # Statistics
        total_mcns = self.get_queryset().count()
        pending_approval = self.get_queryset().filter(status='PENDING_APPROVAL').count()
        approved = self.get_queryset().filter(status='APPROVED').count()
        processed = self.get_queryset().filter(status='PROCESSED').count()
        
        context['stats'] = {
            'total': total_mcns,
            'pending_approval': pending_approval,
            'approved': approved,
            'processed': processed
        }
        
        return context


class MCNDetailView(LoginRequiredMixin, DetailView):
    """Display MCN details"""
    model = MaterialCreditNote
    template_name = 'inventory/credit_notes/mcn_detail.html'
    context_object_name = 'mcn'
    
    def get_queryset(self):
        return MaterialCreditNote.objects.select_related(
            'company', 'financial_year', 'requested_by', 'approved_by'
        ).prefetch_related(
            'line_items',
            'approval_history__approver'
        )
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = f'MCN Details - {self.object.mcn_number}'
        context['can_edit'] = self.object.can_be_edited
        context['can_approve'] = self.object.can_be_approved
        context['can_process'] = self.object.can_be_processed
        return context


class MCNCreateView(LoginRequiredMixin, CreateView):
    """Create new Material Credit Note"""
    model = MaterialCreditNote
    form_class = MaterialCreditNoteForm
    template_name = 'inventory/credit_notes/mcn_form.html'
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Create Material Credit Note'
        context['form_title'] = 'New MCN'
        
        if self.request.POST:
            context['line_item_formset'] = MCNLineItemInlineFormSet(self.request.POST)
        else:
            context['line_item_formset'] = MCNLineItemInlineFormSet()
            
        return context
    
    def form_valid(self, form):
        context = self.get_context_data()
        line_item_formset = context['line_item_formset']
        
        with transaction.atomic():
            # Set additional fields
            form.instance.requested_by = self.request.user
            form.instance.created_by = self.request.user
            
            # Get company and financial year (you may need to adjust this logic)
            try:
                company = Company.objects.first()  # Adjust as needed
                financial_year = FinancialYear.objects.filter(is_active=True).first()
                form.instance.company = company
                form.instance.financial_year = financial_year
            except (Company.DoesNotExist, FinancialYear.DoesNotExist):
                messages.error(self.request, "Company or Financial Year not found.")
                return self.form_invalid(form)
            
            # Generate MCN number
            form.instance.mcn_number = self.generate_mcn_number()
            
            if line_item_formset.is_valid():
                self.object = form.save()
                line_item_formset.instance = self.object
                line_item_formset.save()
                
                # Calculate totals
                self.update_mcn_totals(self.object)
                
                messages.success(self.request, f'MCN {self.object.mcn_number} created successfully.')
                return redirect('inventory:mcn_detail', pk=self.object.pk)
            else:
                return self.form_invalid(form)
    
    def generate_mcn_number(self):
        """Generate unique MCN number"""
        today = date.today()
        prefix = f"MCN/{today.year}/{today.month:02d}/"
        
        # Get last MCN for current month
        last_mcn = MaterialCreditNote.objects.filter(
            mcn_number__startswith=prefix
        ).order_by('-mcn_number').first()
        
        if last_mcn:
            try:
                last_num = int(last_mcn.mcn_number.split('/')[-1])
                new_num = last_num + 1
            except (ValueError, IndexError):
                new_num = 1
        else:
            new_num = 1
            
        return f"{prefix}{new_num:04d}"
    
    def update_mcn_totals(self, mcn):
        """Update MCN totals based on line items"""
        line_items = mcn.line_items.all()
        mcn.total_items = line_items.count()
        mcn.total_adjustment_value = sum(item.total_amount for item in line_items)
        mcn.save(update_fields=['total_items', 'total_adjustment_value'])


class MCNUpdateView(LoginRequiredMixin, UpdateView):
    """Update existing Material Credit Note"""
    model = MaterialCreditNote
    form_class = MaterialCreditNoteForm
    template_name = 'inventory/credit_notes/mcn_form.html'
    
    def get_object(self, queryset=None):
        obj = super().get_object(queryset)
        if not obj.can_be_edited:
            raise PermissionDenied("This MCN cannot be edited in its current status.")
        return obj
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = f'Edit MCN - {self.object.mcn_number}'
        context['form_title'] = f'Edit MCN {self.object.mcn_number}'
        
        if self.request.POST:
            context['line_item_formset'] = MCNLineItemInlineFormSet(
                self.request.POST, instance=self.object
            )
        else:
            context['line_item_formset'] = MCNLineItemInlineFormSet(instance=self.object)
            
        return context
    
    def form_valid(self, form):
        context = self.get_context_data()
        line_item_formset = context['line_item_formset']
        
        with transaction.atomic():
            form.instance.modified_by = self.request.user
            
            if line_item_formset.is_valid():
                self.object = form.save()
                line_item_formset.save()
                
                # Update totals
                self.update_mcn_totals(self.object)
                
                messages.success(self.request, f'MCN {self.object.mcn_number} updated successfully.')
                return redirect('inventory:mcn_detail', pk=self.object.pk)
            else:
                return self.form_invalid(form)
    
    def update_mcn_totals(self, mcn):
        """Update MCN totals based on line items"""
        line_items = mcn.line_items.all()
        mcn.total_items = line_items.count()
        mcn.total_adjustment_value = sum(item.total_amount for item in line_items)
        mcn.save(update_fields=['total_items', 'total_adjustment_value'])


class MCNDeleteView(LoginRequiredMixin, DeleteView):
    """Delete Material Credit Note"""
    model = MaterialCreditNote
    template_name = 'inventory/credit_notes/mcn_confirm_delete.html'
    success_url = reverse_lazy('inventory:mcn_list')
    
    def get_object(self, queryset=None):
        obj = super().get_object(queryset)
        if not obj.can_be_edited:
            raise PermissionDenied("This MCN cannot be deleted in its current status.")
        return obj
    
    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        mcn_number = self.object.mcn_number
        self.object.delete()
        messages.success(request, f'MCN {mcn_number} deleted successfully.')
        return redirect(self.success_url)


class MCNApprovalView(LoginRequiredMixin, UpdateView):
    """Handle MCN approval workflow"""
    model = MaterialCreditNote
    template_name = 'inventory/credit_notes/mcn_approval.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = f'MCN Approval - {self.object.mcn_number}'
        context['approval_form'] = MCNApprovalForm(mcn=self.object)
        return context
    
    def post(self, request, *args, **kwargs):
        self.object = self.get_object()
        approval_form = MCNApprovalForm(request.POST, mcn=self.object)
        
        if approval_form.is_valid():
            with transaction.atomic():
                # Create approval history record
                approval_history = approval_form.save(commit=False)
                approval_history.mcn = self.object
                approval_history.approver = request.user
                approval_history.save()
                
                # Update MCN status based on action
                action = approval_form.cleaned_data['action']
                if action == 'SUBMITTED':
                    self.object.status = 'PENDING_APPROVAL'
                elif action == 'APPROVED':
                    self.object.status = 'APPROVED'
                    self.object.approved_by = request.user
                    self.object.approved_date = timezone.now()
                elif action == 'REJECTED':
                    self.object.status = 'REJECTED'
                elif action == 'RETURNED':
                    self.object.status = 'DRAFT'
                elif action == 'PROCESSED':
                    self.object.status = 'PROCESSED'
                    self.object.processed_by = request.user
                    self.object.processed_date = timezone.now()
                    # Here you would implement stock adjustment logic
                    self.process_stock_adjustments(self.object)
                elif action == 'CANCELLED':
                    self.object.status = 'CANCELLED'
                
                self.object.save()
                
                messages.success(request, f'MCN {self.object.mcn_number} {action.lower()} successfully.')
                return redirect('inventory:mcn_detail', pk=self.object.pk)
        
        context = self.get_context_data()
        context['approval_form'] = approval_form
        return render(request, self.template_name, context)
    
    def process_stock_adjustments(self, mcn):
        """Process stock adjustments for approved MCN"""
        # This is where you would implement the actual stock adjustment logic
        # For now, we'll just mark line items as processed
        for line_item in mcn.line_items.all():
            line_item.line_status = 'PROCESSED'
            line_item.processed_date = timezone.now()
            line_item.processed_by = self.request.user
            line_item.save()
            
            # TODO: Implement actual stock updates in stock ledger
            # This would involve creating entries in the stock ledger
            # based on the adjustment_action and adjustment_quantity


@login_required
def mcn_submit_view(request, pk):
    """Submit MCN for approval"""
    mcn = get_object_or_404(MaterialCreditNote, pk=pk)
    
    if mcn.status != 'DRAFT':
        messages.error(request, "Only draft MCNs can be submitted for approval.")
        return redirect('inventory:mcn_detail', pk=pk)
    
    if not mcn.line_items.exists():
        messages.error(request, "MCN must have at least one line item to submit.")
        return redirect('inventory:mcn_detail', pk=pk)
    
    with transaction.atomic():
        mcn.status = 'PENDING_APPROVAL'
        mcn.save()
        
        # Create approval history
        MCNApprovalHistory.objects.create(
            mcn=mcn,
            approver=request.user,
            action='SUBMITTED',
            comments='Submitted for approval'
        )
    
    messages.success(request, f'MCN {mcn.mcn_number} submitted for approval.')
    return redirect('inventory:mcn_detail', pk=pk)


@login_required
def mcn_print_view(request, pk):
    """Print MCN"""
    mcn = get_object_or_404(MaterialCreditNote, pk=pk)
    
    context = {
        'mcn': mcn,
        'company': mcn.company,
        'print_date': timezone.now(),
        'page_title': f'Print MCN - {mcn.mcn_number}'
    }
    
    return render(request, 'inventory/credit_notes/mcn_print.html', context)


@login_required
def mcn_statistics_api(request):
    """API endpoint for MCN statistics"""
    stats = {}
    
    # Basic counts
    stats['total_mcns'] = MaterialCreditNote.objects.count()
    stats['draft'] = MaterialCreditNote.objects.filter(status='DRAFT').count()
    stats['pending_approval'] = MaterialCreditNote.objects.filter(status='PENDING_APPROVAL').count()
    stats['approved'] = MaterialCreditNote.objects.filter(status='APPROVED').count()
    stats['processed'] = MaterialCreditNote.objects.filter(status='PROCESSED').count()
    stats['rejected'] = MaterialCreditNote.objects.filter(status='REJECTED').count()
    stats['cancelled'] = MaterialCreditNote.objects.filter(status='CANCELLED').count()
    
    # By adjustment type
    stats['by_type'] = {}
    for choice in MaterialCreditNote.ADJUSTMENT_TYPE_CHOICES:
        stats['by_type'][choice[0]] = MaterialCreditNote.objects.filter(
            adjustment_type=choice[0]
        ).count()
    
    # By priority
    stats['by_priority'] = {}
    for choice in MaterialCreditNote.PRIORITY_CHOICES:
        stats['by_priority'][choice[0]] = MaterialCreditNote.objects.filter(
            priority=choice[0]
        ).count()
    
    # Monthly trends (last 12 months)
    from django.db.models import Count
    from datetime import timedelta
    
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365)
    
    monthly_data = MaterialCreditNote.objects.filter(
        mcn_date__range=[start_date.date(), end_date.date()]
    ).extra(
        select={'month': "strftime('%%Y-%%m', mcn_date)"}
    ).values('month').annotate(
        count=Count('id')
    ).order_by('month')
    
    stats['monthly_trends'] = list(monthly_data)
    
    # Emergency MCNs
    stats['emergency_mcns'] = MaterialCreditNote.objects.filter(is_emergency=True).count()
    
    # Total adjustment value
    from django.db.models import Sum
    total_value = MaterialCreditNote.objects.filter(
        status='PROCESSED'
    ).aggregate(
        total=Sum('total_adjustment_value')
    )['total'] or 0
    
    stats['total_adjustment_value'] = float(total_value)
    
    return JsonResponse(stats)


@login_required
def mcn_line_item_search_api(request):
    """API endpoint for searching line items"""
    item_code = request.GET.get('item_code', '')
    
    if not item_code:
        return JsonResponse({'error': 'Item code is required'}, status=400)
    
    # This would typically search in your item master
    # For now, returning mock data
    item_data = {
        'item_description': f'Description for {item_code}',
        'unit_of_measure': 'PCS',
        'current_stock': 100.000,
        'unit_rate': 50.00,
        'location_code': 'WH-01'
    }
    
    return JsonResponse(item_data)