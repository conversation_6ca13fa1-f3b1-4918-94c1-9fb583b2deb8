{% if results %}
    <div class="list-group">
        {% for result in results %}
        <div class="list-group-item list-group-item-action">
            <div class="d-flex w-100 justify-content-between">
                <div>
                    <h6 class="mb-1">{{ result.employee_name }}</h6>
                    <p class="mb-1">{{ result.activity }}</p>
                    <small class="text-muted">WO: {{ result.wo_number }} | {{ result.department }}</small>
                </div>
                <div class="text-end">
                    <small class="text-muted">{{ result.date_of_reporting }}</small>
                    <div class="progress mt-1" style="width: 100px; height: 6px;">
                        <div class="progress-bar" style="width: {{ result.percentage_completed }}%"></div>
                    </div>
                    <small>{{ result.percentage_completed }}%</small>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
{% else %}
    <div class="text-center text-muted py-3">
        <i class="fas fa-search fa-2x mb-2"></i>
        <p>No results found for "{{ search_term }}"</p>
    </div>
{% endif %}