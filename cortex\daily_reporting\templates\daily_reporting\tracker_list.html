{% extends 'core/base.html' %}
{% load static %}

{% block title %}Daily Reports - List{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Daily Reports</h1>
            <p class="text-muted">Manage and track daily reporting activities</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{% url 'daily_reporting:tracker_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>New Report
            </a>
            <a href="{% url 'daily_reporting:export_reports' %}" class="btn btn-outline-secondary">
                <i class="fas fa-download me-1"></i>Export
            </a>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-8">
                    <div class="input-group">
                        <input type="text" name="search" class="form-control" 
                               placeholder="Search by employee, work order, department, or activity..."
                               value="{{ search }}"
                               hx-get="{% url 'daily_reporting:tracker_list' %}"
                               hx-target="#reports-table"
                               hx-trigger="keyup changed delay:300ms">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-4">
                    <a href="{% url 'daily_reporting:tracker_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-undo me-1"></i>Clear Filters
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Reports Table -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Daily Reports ({{ reports|length }} of {{ page_obj.paginator.count }})</h6>
        </div>
        <div class="card-body" id="reports-table">
            {% if reports %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Employee</th>
                                <th>Department</th>
                                <th>WO Number</th>
                                <th>Reporting Date</th>
                                <th>Activity</th>
                                <th>Progress</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for report in reports %}
                            <tr>
                                <td>
                                    <div class="fw-bold">{{ report.employee_name }}</div>
                                    <small class="text-muted">{{ report.designation }}</small>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ report.department }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ report.wo_number }}</span>
                                </td>
                                <td>{{ report.date_of_reporting|date:"M d, Y" }}</td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" 
                                         title="{{ report.activity }}">
                                        {{ report.activity|truncatechars:50 }}
                                    </div>
                                </td>
                                <td>
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar 
                                            {% if report.percentage_completed < 25 %}bg-danger
                                            {% elif report.percentage_completed < 50 %}bg-warning
                                            {% elif report.percentage_completed < 75 %}bg-info
                                            {% else %}bg-success{% endif %}" 
                                             role="progressbar" 
                                             style="width: {{ report.percentage_completed }}%"
                                             aria-valuenow="{{ report.percentage_completed }}" 
                                             aria-valuemin="0" aria-valuemax="100">
                                        </div>
                                    </div>
                                    <small>{{ report.percentage_completed }}%</small>
                                </td>
                                <td>
                                    {% if report.percentage_completed == 100 %}
                                        <span class="badge bg-success">Completed</span>
                                    {% elif report.percentage_completed > 0 %}
                                        <span class="badge bg-warning">In Progress</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Not Started</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'daily_reporting:tracker_detail' report.pk %}" 
                                           class="btn btn-outline-info" 
                                           data-bs-toggle="tooltip" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'daily_reporting:tracker_edit' report.pk %}" 
                                           class="btn btn-outline-warning"
                                           data-bs-toggle="tooltip" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'daily_reporting:tracker_delete' report.pk %}" 
                                           class="btn btn-outline-danger"
                                           data-bs-toggle="tooltip" title="Delete"
                                           onclick="return confirm('Are you sure you want to delete this report?')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search %}&search={{ search }}{% endif %}">First</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}">Previous</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}">Next</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search %}&search={{ search }}{% endif %}">Last</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Reports Found</h5>
                    {% if search %}
                        <p class="text-muted">No reports match your search criteria: "{{ search }}"</p>
                        <a href="{% url 'daily_reporting:tracker_list' %}" class="btn btn-outline-secondary">Clear Search</a>
                    {% else %}
                        <p class="text-muted">Start by creating your first daily report</p>
                        <a href="{% url 'daily_reporting:tracker_create' %}" class="btn btn-primary">Create Report</a>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/htmx.org@1.9.10"></script>
<script>
    // Initialize tooltips
    document.addEventListener('DOMContentLoaded', function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}