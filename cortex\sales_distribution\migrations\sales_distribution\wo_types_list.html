<!-- sales_distribution/templates/sales_distribution/wo_types_list.html -->
<!-- Professional WO Types Management - SAP S/4HANA Inspired Design -->
<!-- Replaces ASP.NET WOTypes.aspx functionality -->

{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }} - Cortex{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-sap-blue-500 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="clipboard-list" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">WO Types Management</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Configure and manage work order types for sales distribution</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <div class="text-right mr-4 px-4 py-2 bg-sap-gray-50 rounded-lg border border-sap-gray-200">
                    <p class="text-2xs font-medium text-sap-gray-500 uppercase tracking-wide">Total Types</p>
                    <p class="text-lg font-semibold text-sap-blue-600" id="types-count">{{ categories|length }}</p>
                </div>
                <a href="{% url 'sales_distribution:category_list' %}" 
                   class="inline-flex items-center px-4 py-2.5 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 hover:border-sap-blue-300 transition-all duration-200">
                    <i data-lucide="layers" class="w-4 h-4 mr-2"></i>
                    Manage Categories
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-2 py-4 space-y-2">
    
    <!-- Add New WO Type Card -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-3 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="plus" class="w-5 h-5 mr-2 text-sap-blue-500"></i>
                    Add New WO Type
                </h2>
                <button type="button" id="toggle-add-form" 
                        class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-sap-blue-600 hover:text-sap-blue-800 transition-colors duration-200">
                    <i data-lucide="chevron-down" class="w-4 h-4 ml-1 transform transition-transform duration-200" id="toggle-icon"></i>
                </button>
            </div>
        </div>
        <div class="px-6 py-4 border-b border-sap-gray-100" id="add-form-container" style="display: none;">
            <form hx-post="{% url 'sales_distribution:wo_types_create' %}" 
                  hx-target="#wo-types-table" 
                  hx-trigger="submit"
                  class="grid grid-cols-1 gap-4 md:grid-cols-2">
                {% csrf_token %}
                
                <!-- Category Name -->
                <div class="md:col-span-1">
                    <label for="{{ wotypes_form.cname.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                        WO Type Name <span class="text-red-500">*</span>
                    </label>
                    {{ wotypes_form.cname }}
                    {% if wotypes_form.cname.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ wotypes_form.cname.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <!-- Submit Button -->
                <div class="md:col-span-2 pt-4 border-t border-sap-gray-100">
                    <div class="flex items-center justify-end space-x-3">
                        <button type="button" id="cancel-add" 
                                class="px-4 py-2 text-sm font-medium text-sap-gray-700 bg-white border border-sap-gray-300 rounded-lg hover:bg-sap-gray-50 transition-colors duration-200">
                            Cancel
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 text-sm font-medium text-white bg-sap-blue-600 border border-transparent rounded-lg hover:bg-sap-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-blue-500 transition-colors duration-200">
                            <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                            Add WO Type
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Search and Filter Card -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="search" class="w-5 h-5 mr-2 text-sap-blue-500"></i>
                    Search & Filter
                </h2>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Real-time Search -->
                <div>
                    <label for="search-input" class="block text-sm font-medium text-sap-gray-700 mb-2">
                        Search WO Types
                    </label>
                    <div class="relative">
                        <input type="text" 
                               id="search-input"
                               name="search"
                               value="{{ request.GET.search }}"
                               placeholder="Search by type name..."
                               hx-get="{% url 'sales_distribution:wo_types_list' %}"
                               hx-trigger="keyup changed delay:300ms"
                               hx-target="#wo-types-table"
                               hx-include="[name='search']"
                               class="w-full pl-10 pr-3 py-2 border border-sap-gray-300 rounded-lg text-sm placeholder-sap-gray-500 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i data-lucide="search" class="w-4 h-4 text-sap-gray-400"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- WO Types List Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-sap-gray-800">WO Types List</h2>
                <div class="text-sm text-sap-gray-500">
                    Showing {{ categories|length }} {% if categories|length != 1 %}types{% else %}type{% endif %}
                </div>
            </div>
        </div>
        
        <div id="wo-types-table">
            {% include 'sales_distribution/partials/wotypes_table.html' %}
        </div>
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm px-6 py-4">
        <div class="flex items-center justify-between">
            <div class="text-sm text-sap-gray-600">
                Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} results
            </div>
            <div class="flex items-center space-x-2">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" 
                       class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-lg hover:bg-sap-gray-50 hover:text-sap-gray-700 transition-colors duration-200">
                        Previous
                    </a>
                {% endif %}
                
                <span class="px-3 py-2 text-sm font-medium text-sap-blue-600 bg-sap-blue-50 border border-sap-blue-200 rounded-lg">
                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                </span>
                
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" 
                       class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-lg hover:bg-sap-gray-50 hover:text-sap-gray-700 transition-colors duration-200">
                        Next
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- JavaScript for form toggle -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const toggleButton = document.getElementById('toggle-add-form');
    const formContainer = document.getElementById('add-form-container');
    const toggleIcon = document.getElementById('toggle-icon');
    const cancelButton = document.getElementById('cancel-add');
    
    function toggleForm() {
        const isHidden = formContainer.style.display === 'none';
        formContainer.style.display = isHidden ? 'block' : 'none';
        toggleIcon.style.transform = isHidden ? 'rotate(180deg)' : 'rotate(0deg)';
    }
    
    toggleButton.addEventListener('click', toggleForm);
    cancelButton.addEventListener('click', function() {
        formContainer.style.display = 'none';
        toggleIcon.style.transform = 'rotate(0deg)';
        // Reset form
        formContainer.querySelector('form').reset();
    });
    
    // Auto-hide form after successful submission
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'wo-types-table') {
            formContainer.style.display = 'none';
            toggleIcon.style.transform = 'rotate(0deg)';
            // Reset form
            formContainer.querySelector('form').reset();
            // Update count
            const newCount = document.querySelectorAll('#wo-types-table tbody tr').length;
            document.getElementById('types-count').textContent = newCount;
        }
    });
});
</script>
{% endblock %}