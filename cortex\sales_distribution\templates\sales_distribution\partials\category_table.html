<!-- sales_distribution/templates/sales_distribution/partials/category_table.html -->
<!-- Category table partial - replaces ASP.NET GridView functionality -->

<div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-sap-gray-200">
        <thead class="bg-sap-gray-50">
            <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider w-16">
                    SN
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider w-20">
                    Actions
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    Category Name
                </th>
                <th class="px-6 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider w-24">
                    Symbol
                </th>
                <th class="px-6 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider w-32">
                    Has Sub-Category
                </th>
                <th class="px-6 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider w-20">
                    Status
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-sap-gray-200">
            {% for category in categories %}
                {% include 'sales_distribution/partials/category_row.html' with category=category forloop=forloop %}
            {% empty %}
                <tr>
                    <td colspan="6" class="px-6 py-12 text-center">
                        <div class="flex flex-col items-center">
                            <i data-lucide="inbox" class="w-12 h-12 text-sap-gray-400 mb-4"></i>
                            <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No categories found</h3>
                            <p class="text-sm text-sap-gray-600 mb-4">Get started by creating your first work order category.</p>
                            <button onclick="showForm()" 
                                    class="inline-flex items-center px-4 py-2 bg-sap-blue-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-sap-blue-700">
                                <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                Add Category
                            </button>
                        </div>
                    </td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
</div>