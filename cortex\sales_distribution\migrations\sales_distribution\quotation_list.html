{% extends "core/base.html" %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{{ page_title }}</h1>
                        <p class="text-gray-600 mt-1">Manage all quotations and their approval status</p>
                    </div>
                    <div class="flex space-x-3">
                        <a href="{% url 'sales_distribution:quotation_selection' %}" 
                           class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                            Create New Quotation
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filter -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-t-lg">
                <h3 class="text-lg font-medium">Search & Filter Quotations</h3>
            </div>
            <div class="px-6 py-6">
                <form method="get" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label for="{{ filter_form.search.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                Search
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                                {{ filter_form.search }}
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="{{ filter_form.status.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                Status
                            </label>
                            {{ filter_form.status }}
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <a href="{% url 'sales_distribution:quotation_list' %}" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            Clear
                        </a>
                        
                        <button type="submit" 
                                class="inline-flex items-center px-6 py-2 bg-blue-600 border border-transparent rounded-lg font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Search
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Quotations List -->
        {% if quotations %}
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">All Quotations</h3>
                        <p class="text-sm text-gray-600">{{ quotations|length }} quotation{{ quotations|length|pluralize }} found</p>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Quotation Info
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Customer Details
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Enquiry Reference
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Date & Terms
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Status
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for quotation in quotations %}
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">
                                            Quotation #{{ quotation.quotationno }}
                                        </div>
                                        <div class="text-xs text-gray-500">
                                            ID: {{ quotation.id }}
                                        </div>
                                        {% if quotation.validity %}
                                            <div class="text-xs text-gray-500">
                                                Valid: {{ quotation.validity }}
                                            </div>
                                        {% endif %}
                                    </td>
                                    
                                    <td class="px-6 py-4">
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ quotation.enqid.customername|default:"—" }}
                                        </div>
                                        <div class="text-xs text-gray-500">
                                            Customer ID: {{ quotation.customerid|default:"—" }}
                                        </div>
                                        {% if quotation.enqid.contactperson %}
                                            <div class="text-xs text-gray-500">
                                                Contact: {{ quotation.enqid.contactperson }}
                                            </div>
                                        {% endif %}
                                    </td>
                                    
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-gray-900">
                                            Enquiry #{{ quotation.enqid.enqid }}
                                        </div>
                                        {% if quotation.enqid.enquiryfor %}
                                            <div class="text-xs text-gray-500 mt-1">
                                                {{ quotation.enqid.enquiryfor|truncatechars:60 }}
                                            </div>
                                        {% endif %}
                                    </td>
                                    
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            {{ quotation.sysdate|default:"—" }}
                                        </div>
                                        {% if quotation.systime %}
                                            <div class="text-xs text-gray-500">
                                                {{ quotation.systime }}
                                            </div>
                                        {% endif %}
                                        {% if quotation.duedate %}
                                            <div class="text-xs text-gray-500">
                                                Due: {{ quotation.duedate }}
                                            </div>
                                        {% endif %}
                                    </td>
                                    
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ quotation.status_display.class }}">
                                            {{ quotation.status_display.status }}
                                        </span>
                                        
                                        <!-- Approval Progress -->
                                        <div class="mt-1 flex space-x-1">
                                            {% if quotation.checked == 1 %}
                                                <span class="inline-block w-2 h-2 bg-yellow-400 rounded-full" title="Checked"></span>
                                            {% else %}
                                                <span class="inline-block w-2 h-2 bg-gray-300 rounded-full" title="Not Checked"></span>
                                            {% endif %}
                                            
                                            {% if quotation.approve == 1 %}
                                                <span class="inline-block w-2 h-2 bg-blue-400 rounded-full" title="Approved"></span>
                                            {% else %}
                                                <span class="inline-block w-2 h-2 bg-gray-300 rounded-full" title="Not Approved"></span>
                                            {% endif %}
                                            
                                            {% if quotation.authorize == 1 %}
                                                <span class="inline-block w-2 h-2 bg-green-400 rounded-full" title="Authorized"></span>
                                            {% else %}
                                                <span class="inline-block w-2 h-2 bg-gray-300 rounded-full" title="Not Authorized"></span>
                                            {% endif %}
                                        </div>
                                    </td>
                                    
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex items-center justify-end space-x-2">
                                            <a href="{% url 'sales_distribution:quotation_detail' quotation.id %}" 
                                               class="text-gray-600 hover:text-gray-700 tooltip" 
                                               title="View Details">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                </svg>
                                            </a>
                                            
                                            {% if quotation.authorize != 1 %}
                                                <a href="{% url 'sales_distribution:quotation_edit' quotation.id %}" 
                                                   class="text-blue-600 hover:text-blue-700 tooltip" 
                                                   title="Edit Quotation">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                    </svg>
                                                </a>
                                            {% endif %}
                                            
                                            <!-- Approval Actions -->
                                            {% if quotation.checked == 0 %}
                                                <button type="button" 
                                                        class="text-yellow-600 hover:text-yellow-700 tooltip approval-btn"
                                                        data-action="check"
                                                        data-quotation-id="{{ quotation.id }}"
                                                        title="Check Quotation">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                </button>
                                            {% elif quotation.approve == 0 and quotation.checked == 1 %}
                                                <button type="button" 
                                                        class="text-blue-600 hover:text-blue-700 tooltip approval-btn"
                                                        data-action="approve"
                                                        data-quotation-id="{{ quotation.id }}"
                                                        title="Approve Quotation">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                </button>
                                            {% elif quotation.authorize == 0 and quotation.approve == 1 %}
                                                <button type="button" 
                                                        class="text-green-600 hover:text-green-700 tooltip approval-btn"
                                                        data-action="authorize"
                                                        data-quotation-id="{{ quotation.id }}"
                                                        title="Authorize Quotation">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                        <div class="flex-1 flex justify-between sm:hidden">
                            {% if page_obj.has_previous %}
                                <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    Previous
                                </a>
                            {% endif %}
                            {% if page_obj.has_next %}
                                <a href="?page={{ page_obj.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    Next
                                </a>
                            {% endif %}
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    Showing <span class="font-medium">{{ page_obj.start_index }}</span> to <span class="font-medium">{{ page_obj.end_index }}</span> of <span class="font-medium">{{ paginator.count }}</span> results
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                    {% if page_obj.has_previous %}
                                        <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                        </a>
                                    {% endif %}
                                    
                                    {% for num in page_obj.paginator.page_range %}
                                        {% if page_obj.number == num %}
                                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                                                {{ num }}
                                            </span>
                                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                            <a href="?page={{ num }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                                {{ num }}
                                            </a>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if page_obj.has_next %}
                                        <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                        </a>
                                    {% endif %}
                                </nav>
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
        {% else %}
            <!-- Empty State -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No quotations found</h3>
                    <p class="mt-1 text-sm text-gray-500">
                        {% if request.GET.search or request.GET.status %}
                            No quotations match your search criteria. Try different filters.
                        {% else %}
                            Get started by creating your first quotation.
                        {% endif %}
                    </p>
                    <div class="mt-6">
                        <a href="{% url 'sales_distribution:quotation_selection' %}" 
                           class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                            Create New Quotation
                        </a>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<style>
    .form-group input,
    .form-group select {
        width: 100%;
        padding: 0.625rem 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    
    .form-group input:focus,
    .form-group select:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    }
    
    .tooltip {
        position: relative;
    }
    
    .tooltip:hover::after {
        content: attr(title);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: #1f2937;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        z-index: 1000;
        margin-bottom: 4px;
    }
    
    .tooltip:hover::before {
        content: "";
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 4px solid transparent;
        border-top-color: #1f2937;
        z-index: 1000;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle approval actions
    const approvalButtons = document.querySelectorAll('.approval-btn');
    
    approvalButtons.forEach(button => {
        button.addEventListener('click', function() {
            const action = this.dataset.action;
            const quotationId = this.dataset.quotationId;
            
            if (confirm(`Are you sure you want to ${action} this quotation?`)) {
                // Here you would make an AJAX call to handle the approval
                // For now, we'll just show an alert
                alert(`${action.charAt(0).toUpperCase() + action.slice(1)} action would be performed for quotation ${quotationId}`);
                
                // In a real implementation, you would:
                // fetch(`/sales-distribution/quotations/${quotationId}/${action}/`, {
                //     method: 'POST',
                //     headers: {
                //         'X-CSRFToken': getCookie('csrftoken'),
                //         'Content-Type': 'application/json',
                //     },
                // })
                // .then(response => response.json())
                // .then(data => {
                //     if (data.success) {
                //         location.reload();
                //     }
                // });
            }
        });
    });
});
</script>
{% endblock %}