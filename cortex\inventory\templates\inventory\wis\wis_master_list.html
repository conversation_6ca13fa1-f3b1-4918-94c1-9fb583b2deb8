{% extends "core/base.html" %}
{% load static %}

{% block title %}WIS Master - Inventory Management{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Warehouse Issue System (WIS)</h1>
                <p class="text-gray-600 mt-1">Manage automated material issue and return processing</p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'inventory:wis_dry_run_create' %}" 
                   class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    Dry Run
                </a>
                <a href="{% url 'inventory:wis_master_create' %}" 
                   class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create WIS
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500">Total WIS</p>
                        <p class="text-lg font-semibold text-gray-900" id="total-wis">{{ total_wis|default:0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500">Pending</p>
                        <p class="text-lg font-semibold text-gray-900" id="pending-wis">{{ pending_wis|default:0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500">Completed</p>
                        <p class="text-lg font-semibold text-gray-900" id="completed-wis">{{ completed_wis|default:0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500">Processing</p>
                        <p class="text-lg font-semibold text-gray-900" id="processing-wis">{{ processing_wis|default:0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500">Failed</p>
                        <p class="text-lg font-semibold text-gray-900" id="failed-wis">{{ failed_wis|default:0 }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="p-6">
            <form method="get" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                    <input type="text" name="search" value="{{ request.GET.search }}"
                           placeholder="WIS number, work order..."
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                           hx-get="{% url 'inventory:wis_master_list' %}"
                           hx-trigger="keyup changed delay:300ms"
                           hx-target="#wis-results"
                           hx-include="form">
                </div>
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                    <select name="status" 
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            hx-get="{% url 'inventory:wis_master_list' %}"
                            hx-trigger="change"
                            hx-target="#wis-results"
                            hx-include="form">
                        <option value="">All Status</option>
                        <option value="PENDING" {% if request.GET.status == 'PENDING' %}selected{% endif %}>Pending</option>
                        <option value="PROCESSING" {% if request.GET.status == 'PROCESSING' %}selected{% endif %}>Processing</option>
                        <option value="COMPLETED" {% if request.GET.status == 'COMPLETED' %}selected{% endif %}>Completed</option>
                        <option value="FAILED" {% if request.GET.status == 'FAILED' %}selected{% endif %}>Failed</option>
                        <option value="CANCELLED" {% if request.GET.status == 'CANCELLED' %}selected{% endif %}>Cancelled</option>
                    </select>
                </div>
                <div>
                    <label for="run_type" class="block text-sm font-medium text-gray-700">Run Type</label>
                    <select name="run_type" 
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            hx-get="{% url 'inventory:wis_master_list' %}"
                            hx-trigger="change"
                            hx-target="#wis-results"
                            hx-include="form">
                        <option value="">All Types</option>
                        <option value="ACTUAL" {% if request.GET.run_type == 'ACTUAL' %}selected{% endif %}>Actual Run</option>
                        <option value="DRY" {% if request.GET.run_type == 'DRY' %}selected{% endif %}>Dry Run</option>
                    </select>
                </div>
                <div>
                    <label for="processing_mode" class="block text-sm font-medium text-gray-700">Mode</label>
                    <select name="processing_mode" 
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            hx-get="{% url 'inventory:wis_master_list' %}"
                            hx-trigger="change"
                            hx-target="#wis-results"
                            hx-include="form">
                        <option value="">All Modes</option>
                        <option value="MANUAL" {% if request.GET.processing_mode == 'MANUAL' %}selected{% endif %}>Manual</option>
                        <option value="AUTOMATIC" {% if request.GET.processing_mode == 'AUTOMATIC' %}selected{% endif %}>Automatic</option>
                        <option value="SCHEDULED" {% if request.GET.processing_mode == 'SCHEDULED' %}selected{% endif %}>Scheduled</option>
                    </select>
                </div>
                <div>
                    <label for="date_from" class="block text-sm font-medium text-gray-700">Date From</label>
                    <input type="date" name="date_from" value="{{ request.GET.date_from }}"
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                           hx-get="{% url 'inventory:wis_master_list' %}"
                           hx-trigger="change"
                           hx-target="#wis-results"
                           hx-include="form">
                </div>
            </form>
        </div>
    </div>

    <!-- WIS Table -->
    <div class="bg-white shadow rounded-lg">
        <div id="wis-results">
            {% include 'inventory/wis/partials/wis_master_results.html' %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/htmx.org@1.9.6"></script>
{% endblock %}