from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from django.utils import timezone
from sys_admin.models import Company, FinancialYear


# Budget Management Models

class BudgetCode(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    description = models.TextField(db_column="Description", blank=True, null=True)
    symbol = models.TextField(db_column="Symbol", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblMIS_BudgetCode"


class BudgetCodeTime(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    description = models.TextField(db_column="Description", blank=True, null=True)
    symbol = models.TextField(db_column="Symbol", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblMIS_BudgetCode_Time"


class BudgetHoursFieldCategory(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    category = models.TextField(db_column="Category", blank=True, null=True)
    symbol = models.TextField(db_column="Symbol", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblMIS_BudgetHrs_Field_Category"


class BudgetHoursFieldSubcategory(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    category = models.ForeignKey(BudgetHoursFieldCategory, models.DO_NOTHING, db_column="MId")
    subcategory = models.TextField(db_column="SubCategory", blank=True, null=True)
    symbol = models.TextField(db_column="Symbol", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblMIS_BudgetHrs_Field_SubCategory"


# Budget Allocation and Management Models

class BudgetTransaction(models.Model):
    """Budget allocation transactions - maps to tblACC_Budget_Transactions"""
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate", blank=True, null=True)
    systime = models.TextField(db_column="SysTime", blank=True, null=True) 
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId", blank=True, null=True)
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId", blank=True, null=True)
    session_id = models.TextField(db_column="SessionId", blank=True, null=True)
    budget_code = models.TextField(db_column="BudgetCode", blank=True, null=True)
    amount = models.FloatField(db_column="Amount", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblACC_Budget_Transactions"
        verbose_name = "Budget Transaction"
        verbose_name_plural = "Budget Transactions"

    def __str__(self):
        return f"Budget {self.budget_code} - {self.amount}"


class DepartmentBudget(models.Model):
    """Department-specific budget allocations - maps to tblACC_Budget_Dept"""
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate", blank=True, null=True)
    systime = models.TextField(db_column="SysTime", blank=True, null=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId", blank=True, null=True)
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId", blank=True, null=True)
    session_id = models.TextField(db_column="SessionId", blank=True, null=True)
    dept_id = models.IntegerField(db_column="DeptId", blank=True, null=True)
    acc_id = models.IntegerField(db_column="AccId", blank=True, null=True) 
    amount = models.FloatField(db_column="Amount", blank=True, null=True)
    bg_id = models.IntegerField(db_column="BGId", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblACC_Budget_Dept"
        verbose_name = "Department Budget"
        verbose_name_plural = "Department Budgets"

    def __str__(self):
        return f"Dept {self.dept_id} Budget - {self.amount}"


class WorkOrderBudget(models.Model):
    """Work Order budget allocations - maps to tblACC_Budget_WO"""
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate", blank=True, null=True)
    systime = models.TextField(db_column="SysTime", blank=True, null=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId", blank=True, null=True)
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId", blank=True, null=True)
    session_id = models.TextField(db_column="SessionId", blank=True, null=True)
    wo_id = models.IntegerField(db_column="WOId", blank=True, null=True)
    acc_id = models.IntegerField(db_column="AccId", blank=True, null=True)
    amount = models.FloatField(db_column="Amount", blank=True, null=True)
    bg_id = models.IntegerField(db_column="BGId", blank=True, null=True)

    class Meta:
        managed = False 
        db_table = "tblACC_Budget_WO"
        verbose_name = "Work Order Budget"
        verbose_name_plural = "Work Order Budgets"

    def __str__(self):
        return f"WO {self.wo_id} Budget - {self.amount}"


class BudgetPeriod(models.Model):
    """Budget periods for time-based budget management"""
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100, help_text="Budget period name (e.g., Q1 2024, H1 2024)")
    start_date = models.DateField()
    end_date = models.DateField()
    financial_year = models.ForeignKey(FinancialYear, models.CASCADE, related_name='budget_periods')
    company = models.ForeignKey(Company, models.CASCADE, related_name='budget_periods')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        managed = True
        db_table = "mis_budget_period"
        verbose_name = "Budget Period"
        verbose_name_plural = "Budget Periods"
        unique_together = ['name', 'financial_year', 'company']

    def __str__(self):
        return f"{self.name} ({self.start_date} - {self.end_date})"


class BudgetAllocation(models.Model):
    """Budget allocation records with approval workflow"""
    ALLOCATION_STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('reviewed', 'Under Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('active', 'Active'),
        ('expired', 'Expired'),
    ]

    ALLOCATION_TYPE_CHOICES = [
        ('department', 'Department Budget'),
        ('project', 'Project Budget'),
        ('work_order', 'Work Order Budget'),
        ('general', 'General Budget'),
    ]

    id = models.AutoField(primary_key=True)
    budget_code = models.ForeignKey(BudgetCode, models.CASCADE, related_name='allocations')
    budget_period = models.ForeignKey(BudgetPeriod, models.CASCADE, related_name='allocations')
    allocation_type = models.CharField(max_length=20, choices=ALLOCATION_TYPE_CHOICES, default='general')
    allocated_amount = models.DecimalField(max_digits=15, decimal_places=2, validators=[MinValueValidator(0)])
    utilized_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, validators=[MinValueValidator(0)])
    status = models.CharField(max_length=20, choices=ALLOCATION_STATUS_CHOICES, default='draft')
    
    # Reference IDs for different allocation types
    department_id = models.IntegerField(blank=True, null=True, help_text="Department ID for department budgets")
    project_id = models.IntegerField(blank=True, null=True, help_text="Project ID for project budgets")
    work_order_id = models.IntegerField(blank=True, null=True, help_text="Work Order ID for WO budgets")
    
    # Approval workflow
    requested_by = models.ForeignKey(User, models.CASCADE, related_name='budget_requests')
    approved_by = models.ForeignKey(User, models.SET_NULL, blank=True, null=True, related_name='budget_approvals')
    submitted_at = models.DateTimeField(blank=True, null=True)
    approved_at = models.DateTimeField(blank=True, null=True)
    
    # Metadata
    company = models.ForeignKey(Company, models.CASCADE, related_name='budget_allocations')
    financial_year = models.ForeignKey(FinancialYear, models.CASCADE, related_name='budget_allocations')
    notes = models.TextField(blank=True, help_text="Additional notes or justification")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        managed = True
        db_table = "mis_budget_allocation"
        verbose_name = "Budget Allocation"
        verbose_name_plural = "Budget Allocations"
        indexes = [
            models.Index(fields=['budget_code', 'budget_period']),
            models.Index(fields=['status', 'allocation_type']),
            models.Index(fields=['company', 'financial_year']),
        ]

    def __str__(self):
        return f"{self.budget_code.description} - {self.allocated_amount} ({self.status})"

    @property
    def remaining_amount(self):
        """Calculate remaining budget amount"""
        return self.allocated_amount - self.utilized_amount

    @property
    def utilization_percentage(self):
        """Calculate budget utilization percentage"""
        if self.allocated_amount > 0:
            return (self.utilized_amount / self.allocated_amount) * 100
        return 0

    def can_approve(self, user):
        """Check if user can approve this allocation"""
        # Implement approval logic based on amount thresholds and user roles
        return user.is_superuser or user.groups.filter(name='Budget Managers').exists()

    def submit_for_approval(self, user):
        """Submit allocation for approval"""
        if self.status == 'draft':
            self.status = 'submitted'
            self.submitted_at = timezone.now()
            self.save()
            return True
        return False

    def approve(self, user):
        """Approve the budget allocation"""
        if self.status == 'submitted' and self.can_approve(user):
            self.status = 'approved'
            self.approved_by = user
            self.approved_at = timezone.now()
            self.save()
            return True
        return False


class BudgetDistribution(models.Model):
    """Detailed budget distribution tracking for complex allocations"""
    DISTRIBUTION_CATEGORY_CHOICES = [
        ('po', 'Purchase Orders'),
        ('cash_pay', 'Cash Payments'),
        ('cash_rec', 'Cash Receipts'),
        ('tax', 'Tax Payments'),
        ('labor', 'Labor Costs'),
        ('material', 'Material Costs'),
        ('overhead', 'Overhead Costs'),
        ('other', 'Other Expenses'),
    ]

    id = models.AutoField(primary_key=True)
    budget_allocation = models.ForeignKey(BudgetAllocation, models.CASCADE, related_name='distributions')
    category = models.CharField(max_length=20, choices=DISTRIBUTION_CATEGORY_CHOICES)
    allocated_amount = models.DecimalField(max_digits=15, decimal_places=2, validators=[MinValueValidator(0)])
    actual_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, validators=[MinValueValidator(0)])
    percentage = models.DecimalField(max_digits=5, decimal_places=2, validators=[MinValueValidator(0)], 
                                   help_text="Percentage of total budget allocation")
    
    # Additional tracking
    description = models.CharField(max_length=255, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        managed = True
        db_table = "mis_budget_distribution"
        verbose_name = "Budget Distribution"
        verbose_name_plural = "Budget Distributions"
        unique_together = ['budget_allocation', 'category']

    def __str__(self):
        return f"{self.budget_allocation.budget_code.description} - {self.category} ({self.allocated_amount})"

    @property
    def variance(self):
        """Calculate variance between allocated and actual amounts"""
        return self.actual_amount - self.allocated_amount

    @property
    def variance_percentage(self):
        """Calculate variance percentage"""
        if self.allocated_amount > 0:
            return (self.variance / self.allocated_amount) * 100
        return 0


# Task Group 2: Departmental Budget Management Models

class DepartmentBudgetMaster(models.Model):
    """Enhanced departmental budget management with workflow support"""
    BUDGET_STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('dept_review', 'Department Review'),
        ('mgmt_review', 'Management Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('active', 'Active'),
        ('locked', 'Locked'),
        ('archived', 'Archived'),
    ]

    BUDGET_TYPE_CHOICES = [
        ('operational', 'Operational Budget'),
        ('capital', 'Capital Expenditure'),
        ('project', 'Project Budget'),
        ('maintenance', 'Maintenance Budget'),
        ('training', 'Training Budget'),
        ('travel', 'Travel Budget'),
        ('other', 'Other'),
    ]

    id = models.AutoField(primary_key=True)
    budget_number = models.CharField(max_length=50, unique=True, help_text="Auto-generated budget number")
    budget_period = models.ForeignKey(BudgetPeriod, models.CASCADE, related_name='department_budgets')
    
    # Department information (integrate with HR module when available)
    department_id = models.IntegerField(help_text="Department ID from HR module")
    department_name = models.CharField(max_length=100, help_text="Department name for reference")
    
    # Budget details
    budget_type = models.CharField(max_length=20, choices=BUDGET_TYPE_CHOICES, default='operational')
    total_budget_amount = models.DecimalField(max_digits=15, decimal_places=2, validators=[MinValueValidator(0)])
    allocated_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, validators=[MinValueValidator(0)])
    utilized_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, validators=[MinValueValidator(0)])
    committed_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, validators=[MinValueValidator(0)])
    
    # Status and workflow
    status = models.CharField(max_length=20, choices=BUDGET_STATUS_CHOICES, default='draft')
    priority = models.CharField(max_length=10, choices=[
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ], default='medium')
    
    # Workflow participants
    requested_by = models.ForeignKey(User, models.CASCADE, related_name='dept_budget_requests')
    department_head = models.ForeignKey(User, models.SET_NULL, blank=True, null=True, related_name='dept_budget_head_reviews')
    finance_reviewer = models.ForeignKey(User, models.SET_NULL, blank=True, null=True, related_name='dept_budget_finance_reviews')
    approved_by = models.ForeignKey(User, models.SET_NULL, blank=True, null=True, related_name='dept_budget_approvals')
    
    # Workflow timestamps
    submitted_at = models.DateTimeField(blank=True, null=True)
    dept_reviewed_at = models.DateTimeField(blank=True, null=True)
    finance_reviewed_at = models.DateTimeField(blank=True, null=True)
    approved_at = models.DateTimeField(blank=True, null=True)
    
    # Business justification
    business_justification = models.TextField(help_text="Business justification for budget request")
    expected_outcomes = models.TextField(blank=True, help_text="Expected outcomes and benefits")
    risk_assessment = models.TextField(blank=True, help_text="Risk assessment and mitigation")
    
    # Metadata
    company = models.ForeignKey(Company, models.CASCADE, related_name='department_budgets')
    financial_year = models.ForeignKey(FinancialYear, models.CASCADE, related_name='department_budgets')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        managed = True
        db_table = "mis_department_budget_master"
        verbose_name = "Department Budget"
        verbose_name_plural = "Department Budgets"
        indexes = [
            models.Index(fields=['department_id', 'budget_period']),
            models.Index(fields=['status', 'budget_type']),
            models.Index(fields=['company', 'financial_year']),
        ]

    def __str__(self):
        return f"{self.budget_number} - {self.department_name} ({self.budget_type})"

    @property
    def available_amount(self):
        """Calculate available budget amount"""
        return self.total_budget_amount - self.allocated_amount

    @property
    def utilization_percentage(self):
        """Calculate budget utilization percentage"""
        if self.total_budget_amount > 0:
            return (self.utilized_amount / self.total_budget_amount) * 100
        return 0

    @property
    def allocation_percentage(self):
        """Calculate budget allocation percentage"""
        if self.total_budget_amount > 0:
            return (self.allocated_amount / self.total_budget_amount) * 100
        return 0

    def can_modify(self, user):
        """Check if user can modify this budget"""
        return (self.status in ['draft', 'rejected'] and 
                (user == self.requested_by or user.is_superuser))

    def submit_for_review(self, user):
        """Submit budget for department review"""
        if self.status == 'draft' and self.can_modify(user):
            self.status = 'submitted'
            self.submitted_at = timezone.now()
            self.save()
            return True
        return False

    def department_approve(self, user):
        """Department head approval"""
        if self.status == 'submitted':
            self.status = 'dept_review'
            self.department_head = user
            self.dept_reviewed_at = timezone.now()
            self.save()
            return True
        return False

    def finance_review(self, user):
        """Finance team review"""
        if self.status == 'dept_review':
            self.status = 'mgmt_review'
            self.finance_reviewer = user
            self.finance_reviewed_at = timezone.now()
            self.save()
            return True
        return False

    def final_approve(self, user):
        """Final approval by management"""
        if self.status == 'mgmt_review':
            self.status = 'approved'
            self.approved_by = user
            self.approved_at = timezone.now()
            self.save()
            return True
        return False


class DepartmentBudgetDetails(models.Model):
    """Detailed line items for department budgets"""
    EXPENSE_CATEGORY_CHOICES = [
        ('salaries', 'Salaries & Benefits'),
        ('travel', 'Travel & Transportation'),
        ('training', 'Training & Development'),
        ('equipment', 'Equipment & Tools'),
        ('supplies', 'Office Supplies'),
        ('utilities', 'Utilities'),
        ('maintenance', 'Maintenance & Repairs'),
        ('software', 'Software & Licenses'),
        ('consulting', 'Consulting Services'),
        ('marketing', 'Marketing & Promotion'),
        ('other', 'Other Expenses'),
    ]

    id = models.AutoField(primary_key=True)
    department_budget = models.ForeignKey(DepartmentBudgetMaster, models.CASCADE, related_name='details')
    
    # Line item details
    line_number = models.IntegerField(help_text="Line item sequence number")
    expense_category = models.CharField(max_length=20, choices=EXPENSE_CATEGORY_CHOICES)
    description = models.CharField(max_length=255)
    detailed_description = models.TextField(blank=True)
    
    # Budget amounts
    requested_amount = models.DecimalField(max_digits=15, decimal_places=2, validators=[MinValueValidator(0)])
    approved_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, validators=[MinValueValidator(0)])
    actual_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, validators=[MinValueValidator(0)])
    committed_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, validators=[MinValueValidator(0)])
    
    # Timing and scheduling
    planned_start_date = models.DateField(blank=True, null=True)
    planned_end_date = models.DateField(blank=True, null=True)
    actual_start_date = models.DateField(blank=True, null=True)
    actual_end_date = models.DateField(blank=True, null=True)
    
    # Justification and tracking
    business_justification = models.TextField(help_text="Justification for this line item")
    success_metrics = models.TextField(blank=True, help_text="How success will be measured")
    is_critical = models.BooleanField(default=False, help_text="Mark as critical for business operations")
    is_recurring = models.BooleanField(default=False, help_text="Recurring expense")
    
    # Approval status for individual line items
    is_approved = models.BooleanField(default=False)
    approved_by = models.ForeignKey(User, models.SET_NULL, blank=True, null=True, related_name='line_item_approvals')
    approved_at = models.DateTimeField(blank=True, null=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        managed = True
        db_table = "mis_department_budget_details"
        verbose_name = "Department Budget Detail"
        verbose_name_plural = "Department Budget Details"
        unique_together = ['department_budget', 'line_number']
        indexes = [
            models.Index(fields=['expense_category', 'is_critical']),
            models.Index(fields=['planned_start_date', 'planned_end_date']),
        ]

    def __str__(self):
        return f"{self.department_budget.budget_number} - Line {self.line_number}: {self.description}"

    @property
    def variance_amount(self):
        """Calculate variance between approved and actual amounts"""
        return self.actual_amount - self.approved_amount

    @property
    def variance_percentage(self):
        """Calculate variance percentage"""
        if self.approved_amount > 0:
            return (self.variance_amount / self.approved_amount) * 100
        return 0

    @property
    def available_amount(self):
        """Calculate available amount for this line item"""
        return self.approved_amount - self.actual_amount - self.committed_amount


class DepartmentBudgetTimeTracking(models.Model):
    """Time-based tracking for department budget utilization"""
    TRACKING_PERIOD_CHOICES = [
        ('monthly', 'Monthly'),
        ('quarterly', 'Quarterly'),
        ('weekly', 'Weekly'),
        ('daily', 'Daily'),
    ]

    id = models.AutoField(primary_key=True)
    department_budget = models.ForeignKey(DepartmentBudgetMaster, models.CASCADE, related_name='time_tracking')
    budget_detail = models.ForeignKey(DepartmentBudgetDetails, models.CASCADE, blank=True, null=True, related_name='time_tracking')
    
    # Time period
    tracking_period = models.CharField(max_length=10, choices=TRACKING_PERIOD_CHOICES, default='monthly')
    period_start = models.DateField()
    period_end = models.DateField()
    
    # Budget tracking
    planned_amount = models.DecimalField(max_digits=15, decimal_places=2, validators=[MinValueValidator(0)])
    actual_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, validators=[MinValueValidator(0)])
    committed_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, validators=[MinValueValidator(0)])
    
    # Performance metrics
    planned_milestones = models.TextField(blank=True, help_text="Planned milestones for this period")
    achieved_milestones = models.TextField(blank=True, help_text="Actually achieved milestones")
    performance_notes = models.TextField(blank=True, help_text="Performance notes and observations")
    
    # Forecasting
    forecast_amount = models.DecimalField(max_digits=15, decimal_places=2, blank=True, null=True, help_text="Forecasted amount for next period")
    forecast_notes = models.TextField(blank=True, help_text="Forecasting assumptions and notes")
    
    # Status
    is_locked = models.BooleanField(default=False, help_text="Lock period data from further changes")
    locked_by = models.ForeignKey(User, models.SET_NULL, blank=True, null=True, related_name='locked_budget_periods')
    locked_at = models.DateTimeField(blank=True, null=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        managed = True
        db_table = "mis_department_budget_time"
        verbose_name = "Department Budget Time Tracking"
        verbose_name_plural = "Department Budget Time Tracking"
        unique_together = ['department_budget', 'period_start', 'period_end']
        indexes = [
            models.Index(fields=['period_start', 'period_end']),
            models.Index(fields=['tracking_period', 'is_locked']),
        ]

    def __str__(self):
        return f"{self.department_budget.budget_number} - {self.period_start} to {self.period_end}"

    @property
    def variance_amount(self):
        """Calculate variance between planned and actual amounts"""
        return self.actual_amount - self.planned_amount

    @property
    def variance_percentage(self):
        """Calculate variance percentage"""
        if self.planned_amount > 0:
            return (self.variance_amount / self.planned_amount) * 100
        return 0

    @property
    def burn_rate(self):
        """Calculate daily burn rate for the period"""
        period_days = (self.period_end - self.period_start).days + 1
        if period_days > 0:
            return self.actual_amount / period_days
        return 0


class DepartmentBudgetHistory(models.Model):
    """Historical records and revisions for department budgets"""
    ACTION_CHOICES = [
        ('created', 'Budget Created'),
        ('modified', 'Budget Modified'),
        ('submitted', 'Submitted for Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('transferred', 'Budget Transferred'),
        ('reallocated', 'Budget Reallocated'),
        ('locked', 'Budget Locked'),
        ('archived', 'Budget Archived'),
    ]

    id = models.AutoField(primary_key=True)
    department_budget = models.ForeignKey(DepartmentBudgetMaster, models.CASCADE, related_name='history')
    
    # Change tracking
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    description = models.TextField(help_text="Description of what changed")
    old_values = models.JSONField(blank=True, null=True, help_text="Old values in JSON format")
    new_values = models.JSONField(blank=True, null=True, help_text="New values in JSON format")
    
    # Amounts tracking
    old_amount = models.DecimalField(max_digits=15, decimal_places=2, blank=True, null=True)
    new_amount = models.DecimalField(max_digits=15, decimal_places=2, blank=True, null=True)
    
    # User and timing
    performed_by = models.ForeignKey(User, models.CASCADE, related_name='budget_history_actions')
    reason = models.TextField(blank=True, help_text="Reason for the change")
    approval_reference = models.CharField(max_length=100, blank=True, help_text="Reference number for approval")
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        managed = True
        db_table = "mis_department_budget_history"
        verbose_name = "Department Budget History"
        verbose_name_plural = "Department Budget History"
        indexes = [
            models.Index(fields=['department_budget', 'action']),
            models.Index(fields=['performed_by', 'created_at']),
        ]

    def __str__(self):
        return f"{self.department_budget.budget_number} - {self.action} by {self.performed_by.username}"


class DepartmentBudgetTransfer(models.Model):
    """Track budget transfers between departments"""
    TRANSFER_STATUS_CHOICES = [
        ('pending', 'Pending Approval'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]

    id = models.AutoField(primary_key=True)
    transfer_number = models.CharField(max_length=50, unique=True, help_text="Auto-generated transfer number")
    
    # Source and destination
    from_department_budget = models.ForeignKey(DepartmentBudgetMaster, models.CASCADE, related_name='outgoing_transfers')
    to_department_budget = models.ForeignKey(DepartmentBudgetMaster, models.CASCADE, related_name='incoming_transfers')
    
    # Transfer details
    transfer_amount = models.DecimalField(max_digits=15, decimal_places=2, validators=[MinValueValidator(0)])
    transfer_reason = models.TextField(help_text="Reason for budget transfer")
    urgency = models.CharField(max_length=10, choices=[
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ], default='medium')
    
    # Approval workflow
    status = models.CharField(max_length=20, choices=TRANSFER_STATUS_CHOICES, default='pending')
    requested_by = models.ForeignKey(User, models.CASCADE, related_name='budget_transfer_requests')
    approved_by = models.ForeignKey(User, models.SET_NULL, blank=True, null=True, related_name='budget_transfer_approvals')
    
    # Timeline
    requested_at = models.DateTimeField(auto_now_add=True)
    approved_at = models.DateTimeField(blank=True, null=True)
    completed_at = models.DateTimeField(blank=True, null=True)
    
    # Business impact
    business_impact = models.TextField(help_text="Impact on business operations")
    expected_completion_date = models.DateField(help_text="When transfer should be completed")
    
    # Metadata
    company = models.ForeignKey(Company, models.CASCADE, related_name='budget_transfers')
    financial_year = models.ForeignKey(FinancialYear, models.CASCADE, related_name='budget_transfers')

    class Meta:
        managed = True
        db_table = "mis_department_budget_transfer"
        verbose_name = "Department Budget Transfer"
        verbose_name_plural = "Department Budget Transfers"
        indexes = [
            models.Index(fields=['status', 'urgency']),
            models.Index(fields=['requested_at', 'expected_completion_date']),
        ]

    def __str__(self):
        return f"{self.transfer_number} - {self.transfer_amount} ({self.status})"

    def can_approve(self, user):
        """Check if user can approve this transfer"""
        return user.is_superuser or user.groups.filter(name__in=['Budget Managers', 'Finance Team']).exists()

    def approve_transfer(self, user):
        """Approve the budget transfer"""
        if self.status == 'pending' and self.can_approve(user):
            self.status = 'approved'
            self.approved_by = user
            self.approved_at = timezone.now()
            self.save()
            return True
        return False

    def complete_transfer(self):
        """Mark transfer as completed and update budget amounts"""
        if self.status == 'approved':
            # Update source budget
            if self.from_department_budget.available_amount >= self.transfer_amount:
                self.from_department_budget.allocated_amount -= self.transfer_amount
                self.from_department_budget.save()
                
                # Update destination budget
                self.to_department_budget.total_budget_amount += self.transfer_amount
                self.to_department_budget.save()
                
                # Mark transfer as completed
                self.status = 'completed'
                self.completed_at = timezone.now()
                self.save()
                return True
        return False


# ============================================================================
# Task Group 6: Tax Computation & Compliance Models
# ============================================================================

class TaxConfiguration(models.Model):
    """
    Tax rules and rates configuration for different tax types
    Supports Excise, VAT/GST, CST, Service Tax calculations
    """
    TAX_TYPE_CHOICES = [
        ('excise', 'Excise Duty'),
        ('vat', 'Value Added Tax (VAT)'),
        ('gst', 'Goods and Services Tax (GST)'),
        ('cst', 'Central Sales Tax (CST)'),
        ('service_tax', 'Service Tax'),
        ('tds', 'Tax Deducted at Source (TDS)'),
        ('customs', 'Customs Duty'),
        ('cess', 'Cess'),
    ]

    CALCULATION_METHOD_CHOICES = [
        ('percentage', 'Percentage Based'),
        ('fixed_amount', 'Fixed Amount'),
        ('slab_based', 'Slab Based'),
        ('formula_based', 'Formula Based'),
    ]

    id = models.AutoField(primary_key=True)
    tax_type = models.CharField(max_length=20, choices=TAX_TYPE_CHOICES)
    tax_code = models.CharField(max_length=20, unique=True, help_text="Unique tax code (e.g., EXCISE_12.5)")
    tax_name = models.CharField(max_length=100, help_text="Display name for tax")
    description = models.TextField(help_text="Detailed description of tax")
    
    # Rate configuration
    calculation_method = models.CharField(max_length=20, choices=CALCULATION_METHOD_CHOICES, default='percentage')
    tax_rate = models.DecimalField(max_digits=8, decimal_places=4, default=0, help_text="Tax rate (e.g., 12.5000 for 12.5%)")
    fixed_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, help_text="Fixed tax amount")
    minimum_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, help_text="Minimum tax amount")
    maximum_amount = models.DecimalField(max_digits=15, decimal_places=2, blank=True, null=True, help_text="Maximum tax amount")
    
    # Applicability
    applicable_from = models.DateField(help_text="Tax applicable from date")
    applicable_to = models.DateField(blank=True, null=True, help_text="Tax applicable till date")
    is_active = models.BooleanField(default=True)
    
    # Additional configuration
    formula = models.TextField(blank=True, help_text="Custom calculation formula (for formula-based taxes)")
    exemption_threshold = models.DecimalField(max_digits=15, decimal_places=2, default=0, help_text="Amount below which tax is exempted")
    
    # Metadata
    company = models.ForeignKey(Company, models.CASCADE, related_name='tax_configurations')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, models.CASCADE, related_name='tax_config_created')

    class Meta:
        managed = True
        db_table = "mis_tax_configuration"
        verbose_name = "Tax Configuration"
        verbose_name_plural = "Tax Configurations"
        indexes = [
            models.Index(fields=['tax_type', 'is_active']),
            models.Index(fields=['applicable_from', 'applicable_to']),
        ]

    def __str__(self):
        return f"{self.tax_code} - {self.tax_name} ({self.tax_rate}%)"

    def is_applicable_on_date(self, check_date):
        """Check if tax is applicable on given date"""
        if not self.is_active:
            return False
        if check_date < self.applicable_from:
            return False
        if self.applicable_to and check_date > self.applicable_to:
            return False
        return True

    def calculate_tax(self, base_amount, quantity=1):
        """Calculate tax amount based on configuration"""
        if base_amount < self.exemption_threshold:
            return 0
        
        if self.calculation_method == 'percentage':
            tax_amount = (base_amount * self.tax_rate) / 100
        elif self.calculation_method == 'fixed_amount':
            tax_amount = self.fixed_amount * quantity
        elif self.calculation_method == 'slab_based':
            # TODO: Implement slab-based calculation
            tax_amount = (base_amount * self.tax_rate) / 100
        elif self.calculation_method == 'formula_based':
            # TODO: Implement formula-based calculation
            tax_amount = (base_amount * self.tax_rate) / 100
        else:
            tax_amount = 0
        
        # Apply minimum and maximum limits
        if tax_amount < self.minimum_amount:
            tax_amount = self.minimum_amount
        if self.maximum_amount and tax_amount > self.maximum_amount:
            tax_amount = self.maximum_amount
        
        return tax_amount


class TaxComputation(models.Model):
    """
    Tax calculation records for transactions
    Replaces ASP.NET Excise_VAT_CST_Compute.aspx functionality
    """
    COMPUTATION_STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('calculated', 'Calculated'),
        ('verified', 'Verified'),
        ('approved', 'Approved'),
        ('posted', 'Posted to Accounts'),
        ('cancelled', 'Cancelled'),
    ]

    TRANSACTION_TYPE_CHOICES = [
        ('sales_invoice', 'Sales Invoice'),
        ('purchase_invoice', 'Purchase Invoice'),
        ('service_invoice', 'Service Invoice'),
        ('proforma_invoice', 'Proforma Invoice'),
        ('credit_note', 'Credit Note'),
        ('debit_note', 'Debit Note'),
        ('import_invoice', 'Import Invoice'),
        ('export_invoice', 'Export Invoice'),
    ]

    id = models.AutoField(primary_key=True)
    computation_number = models.CharField(max_length=50, unique=True, help_text="Auto-generated computation number")
    
    # Transaction reference
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPE_CHOICES)
    transaction_id = models.IntegerField(help_text="Reference to source transaction")
    transaction_number = models.CharField(max_length=50, help_text="Source transaction number")
    transaction_date = models.DateField()
    
    # Party details
    party_name = models.CharField(max_length=255)
    party_address = models.TextField()
    party_gstin = models.CharField(max_length=15, blank=True, help_text="GSTIN/PAN of party")
    party_state_code = models.CharField(max_length=2, blank=True, help_text="State code for GST")
    
    # Computation details
    base_amount = models.DecimalField(max_digits=15, decimal_places=2, validators=[MinValueValidator(0)])
    discount_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    taxable_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    
    # Tax amounts
    excise_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    vat_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    cst_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    service_tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    gst_cgst_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    gst_sgst_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    gst_igst_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    gst_cess_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    tds_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    
    # Total amounts
    total_tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    grand_total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    
    # Status and workflow
    status = models.CharField(max_length=20, choices=COMPUTATION_STATUS_CHOICES, default='draft')
    computed_by = models.ForeignKey(User, models.CASCADE, related_name='tax_computations')
    verified_by = models.ForeignKey(User, models.SET_NULL, blank=True, null=True, related_name='tax_verifications')
    approved_by = models.ForeignKey(User, models.SET_NULL, blank=True, null=True, related_name='tax_approvals')
    
    # Timestamps
    computed_at = models.DateTimeField(auto_now_add=True)
    verified_at = models.DateTimeField(blank=True, null=True)
    approved_at = models.DateTimeField(blank=True, null=True)
    
    # Additional details
    remarks = models.TextField(blank=True)
    reversal_reason = models.TextField(blank=True, help_text="Reason for tax reversal/cancellation")
    
    # Metadata
    company = models.ForeignKey(Company, models.CASCADE, related_name='tax_computations')
    financial_year = models.ForeignKey(FinancialYear, models.CASCADE, related_name='tax_computations')

    class Meta:
        managed = True
        db_table = "mis_tax_computation"
        verbose_name = "Tax Computation"
        verbose_name_plural = "Tax Computations"
        indexes = [
            models.Index(fields=['transaction_type', 'transaction_date']),
            models.Index(fields=['status', 'computed_at']),
            models.Index(fields=['party_gstin', 'transaction_date']),
        ]

    def __str__(self):
        return f"{self.computation_number} - {self.party_name} (₹{self.grand_total_amount:,.2f})"

    def calculate_taxes(self):
        """Calculate all applicable taxes"""
        computation_date = self.transaction_date
        
        # Reset all tax amounts
        self.excise_amount = 0
        self.vat_amount = 0
        self.cst_amount = 0
        self.service_tax_amount = 0
        self.gst_cgst_amount = 0
        self.gst_sgst_amount = 0
        self.gst_igst_amount = 0
        self.gst_cess_amount = 0
        self.tds_amount = 0
        
        # Calculate taxable amount
        self.taxable_amount = self.base_amount - self.discount_amount
        
        # Get applicable tax configurations
        applicable_taxes = TaxConfiguration.objects.filter(
            company=self.company,
            is_active=True,
            applicable_from__lte=computation_date
        ).filter(
            models.Q(applicable_to__isnull=True) | models.Q(applicable_to__gte=computation_date)
        )
        
        # Calculate each tax type
        for tax_config in applicable_taxes:
            tax_amount = tax_config.calculate_tax(self.taxable_amount)
            
            if tax_config.tax_type == 'excise':
                self.excise_amount += tax_amount
            elif tax_config.tax_type == 'vat':
                self.vat_amount += tax_amount
            elif tax_config.tax_type == 'cst':
                self.cst_amount += tax_amount
            elif tax_config.tax_type == 'service_tax':
                self.service_tax_amount += tax_amount
            elif tax_config.tax_type == 'gst':
                # Determine CGST/SGST or IGST based on party state
                if self.is_interstate_transaction():
                    self.gst_igst_amount += tax_amount
                else:
                    # Split between CGST and SGST
                    cgst_sgst_amount = tax_amount / 2
                    self.gst_cgst_amount += cgst_sgst_amount
                    self.gst_sgst_amount += cgst_sgst_amount
            elif tax_config.tax_type == 'cess':
                self.gst_cess_amount += tax_amount
            elif tax_config.tax_type == 'tds':
                self.tds_amount += tax_amount
        
        # Calculate total tax and grand total
        self.total_tax_amount = (
            self.excise_amount + self.vat_amount + self.cst_amount + 
            self.service_tax_amount + self.gst_cgst_amount + self.gst_sgst_amount + 
            self.gst_igst_amount + self.gst_cess_amount + self.tds_amount
        )
        
        self.grand_total_amount = self.taxable_amount + self.total_tax_amount - self.tds_amount
        
        self.status = 'calculated'
        self.save()

    def is_interstate_transaction(self):
        """Check if transaction is interstate for GST calculation"""
        # TODO: Implement based on company state and party state
        return self.party_state_code != '27'  # Assuming company is in Maharashtra (27)


class TaxComputationDetails(models.Model):
    """
    Detailed tax computation for individual line items
    """
    id = models.AutoField(primary_key=True)
    tax_computation = models.ForeignKey(TaxComputation, models.CASCADE, related_name='details')
    
    # Line item details
    line_number = models.IntegerField()
    item_code = models.CharField(max_length=50, blank=True)
    item_description = models.CharField(max_length=255)
    hsn_code = models.CharField(max_length=10, blank=True, help_text="HSN/SAC Code")
    
    # Quantities and rates
    quantity = models.DecimalField(max_digits=15, decimal_places=3, default=1)
    unit_price = models.DecimalField(max_digits=15, decimal_places=2)
    line_amount = models.DecimalField(max_digits=15, decimal_places=2)
    discount_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    taxable_amount = models.DecimalField(max_digits=15, decimal_places=2)
    
    # Tax configuration references
    tax_configurations = models.ManyToManyField(TaxConfiguration, through='TaxComputationDetailTax')
    
    # Line-wise tax amounts
    line_excise_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    line_vat_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    line_cst_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    line_service_tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    line_gst_cgst_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    line_gst_sgst_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    line_gst_igst_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    line_gst_cess_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    line_tds_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    
    line_total_tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    line_grand_total = models.DecimalField(max_digits=15, decimal_places=2, default=0)

    class Meta:
        managed = True
        db_table = "mis_tax_computation_details"
        verbose_name = "Tax Computation Detail"
        verbose_name_plural = "Tax Computation Details"
        unique_together = ['tax_computation', 'line_number']

    def __str__(self):
        return f"{self.tax_computation.computation_number} - Line {self.line_number}"


class TaxComputationDetailTax(models.Model):
    """
    Many-to-many relationship between computation details and tax configurations
    """
    id = models.AutoField(primary_key=True)
    computation_detail = models.ForeignKey(TaxComputationDetails, models.CASCADE)
    tax_configuration = models.ForeignKey(TaxConfiguration, models.CASCADE)
    applicable_rate = models.DecimalField(max_digits=8, decimal_places=4, help_text="Rate applied for this computation")
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, help_text="Tax amount calculated")
    
    class Meta:
        managed = True
        db_table = "mis_tax_computation_detail_tax"
        unique_together = ['computation_detail', 'tax_configuration']


class TaxCompliance(models.Model):
    """
    Tax compliance tracking and monitoring
    """
    COMPLIANCE_TYPE_CHOICES = [
        ('monthly_return', 'Monthly Return'),
        ('quarterly_return', 'Quarterly Return'),
        ('annual_return', 'Annual Return'),
        ('audit', 'Tax Audit'),
        ('assessment', 'Tax Assessment'),
        ('refund', 'Tax Refund'),
        ('payment', 'Tax Payment'),
    ]

    COMPLIANCE_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('submitted', 'Submitted'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('overdue', 'Overdue'),
    ]

    id = models.AutoField(primary_key=True)
    compliance_number = models.CharField(max_length=50, unique=True)
    compliance_type = models.CharField(max_length=20, choices=COMPLIANCE_TYPE_CHOICES)
    tax_type = models.CharField(max_length=20, choices=TaxConfiguration.TAX_TYPE_CHOICES)
    
    # Period details
    period_from = models.DateField()
    period_to = models.DateField()
    due_date = models.DateField()
    
    # Status and tracking
    status = models.CharField(max_length=20, choices=COMPLIANCE_STATUS_CHOICES, default='pending')
    assigned_to = models.ForeignKey(User, models.CASCADE, related_name='tax_compliance_assigned')
    completed_by = models.ForeignKey(User, models.SET_NULL, blank=True, null=True, related_name='tax_compliance_completed')
    
    # Amounts
    tax_liability = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    tax_paid = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    penalty_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    interest_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    
    # Filing details
    return_file_path = models.CharField(max_length=500, blank=True, help_text="Path to return file")
    acknowledgment_number = models.CharField(max_length=50, blank=True)
    filing_date = models.DateTimeField(blank=True, null=True)
    
    # Notes and remarks
    compliance_notes = models.TextField(blank=True)
    rejection_reason = models.TextField(blank=True)
    
    # Metadata
    company = models.ForeignKey(Company, models.CASCADE, related_name='tax_compliance')
    financial_year = models.ForeignKey(FinancialYear, models.CASCADE, related_name='tax_compliance')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        managed = True
        db_table = "mis_tax_compliance"
        verbose_name = "Tax Compliance"
        verbose_name_plural = "Tax Compliance"
        indexes = [
            models.Index(fields=['compliance_type', 'due_date']),
            models.Index(fields=['status', 'assigned_to']),
            models.Index(fields=['tax_type', 'period_from', 'period_to']),
        ]

    def __str__(self):
        return f"{self.compliance_number} - {self.tax_type} ({self.period_from} to {self.period_to})"

    @property
    def is_overdue(self):
        """Check if compliance is overdue"""
        from datetime import date
        return self.due_date < date.today() and self.status not in ['completed', 'submitted', 'approved']

    def mark_overdue(self):
        """Mark compliance as overdue"""
        if self.is_overdue:
            self.status = 'overdue'
            self.save()


class TaxReport(models.Model):
    """
    Tax reports for regulatory compliance
    Replaces ASP.NET ServiceTaxReport.aspx functionality
    """
    REPORT_TYPE_CHOICES = [
        ('excise_register', 'Excise Register'),
        ('vat_register', 'VAT Register'),
        ('service_tax_register', 'Service Tax Register'),
        ('gst_register', 'GST Register'),
        ('tds_register', 'TDS Register'),
        ('tax_summary', 'Tax Summary Report'),
        ('compliance_status', 'Compliance Status Report'),
    ]

    REPORT_FORMAT_CHOICES = [
        ('excel', 'Excel Format'),
        ('pdf', 'PDF Format'),
        ('csv', 'CSV Format'),
        ('xml', 'XML Format'),
        ('json', 'JSON Format'),
    ]

    id = models.AutoField(primary_key=True)
    report_number = models.CharField(max_length=50, unique=True)
    report_type = models.CharField(max_length=30, choices=REPORT_TYPE_CHOICES)
    report_name = models.CharField(max_length=255)
    
    # Report parameters
    period_from = models.DateField()
    period_to = models.DateField()
    tax_types = models.JSONField(default=list, help_text="List of tax types included in report")
    filters = models.JSONField(default=dict, help_text="Additional filters applied")
    
    # Report generation
    report_format = models.CharField(max_length=10, choices=REPORT_FORMAT_CHOICES, default='excel')
    file_path = models.CharField(max_length=500, blank=True)
    file_size = models.BigIntegerField(default=0, help_text="File size in bytes")
    
    # Status
    generation_status = models.CharField(max_length=20, choices=[
        ('pending', 'Pending'),
        ('generating', 'Generating'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ], default='pending')
    
    # Metadata
    generated_by = models.ForeignKey(User, models.CASCADE, related_name='tax_reports')
    generated_at = models.DateTimeField(blank=True, null=True)
    download_count = models.IntegerField(default=0)
    
    company = models.ForeignKey(Company, models.CASCADE, related_name='tax_reports')
    financial_year = models.ForeignKey(FinancialYear, models.CASCADE, related_name='tax_reports')

    class Meta:
        managed = True
        db_table = "mis_tax_report"
        verbose_name = "Tax Report"
        verbose_name_plural = "Tax Reports"
        indexes = [
            models.Index(fields=['report_type', 'period_from', 'period_to']),
            models.Index(fields=['generation_status', 'generated_at']),
        ]

    def __str__(self):
        return f"{self.report_number} - {self.report_name}"
