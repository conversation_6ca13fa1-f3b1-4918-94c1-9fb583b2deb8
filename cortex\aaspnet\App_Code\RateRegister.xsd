﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="RateRegister" targetNamespace="http://tempuri.org/RateRegister.xsd" xmlns:mstns="http://tempuri.org/RateRegister.xsd" xmlns="http://tempuri.org/RateRegister.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="RateRegister" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="RateRegister" msprop:Generator_DataSetName="RateRegister">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" msprop:Generator_UserColumnName="Id" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInTable="IdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="ItemCode" msprop:Generator_UserColumnName="ItemCode" msprop:Generator_ColumnPropNameInRow="ItemCode" msprop:Generator_ColumnVarNameInTable="columnItemCode" msprop:Generator_ColumnPropNameInTable="ItemCodeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ManfDesc" msprop:Generator_UserColumnName="ManfDesc" msprop:Generator_ColumnPropNameInRow="ManfDesc" msprop:Generator_ColumnVarNameInTable="columnManfDesc" msprop:Generator_ColumnPropNameInTable="ManfDescColumn" type="xs:string" minOccurs="0" />
              <xs:element name="UOMBasic" msprop:Generator_UserColumnName="UOMBasic" msprop:Generator_ColumnPropNameInRow="UOMBasic" msprop:Generator_ColumnVarNameInTable="columnUOMBasic" msprop:Generator_ColumnPropNameInTable="UOMBasicColumn" type="xs:string" minOccurs="0" />
              <xs:element name="FinYear" msprop:Generator_UserColumnName="FinYear" msprop:Generator_ColumnPropNameInRow="FinYear" msprop:Generator_ColumnVarNameInTable="columnFinYear" msprop:Generator_ColumnPropNameInTable="FinYearColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PONo" msprop:Generator_UserColumnName="PONo" msprop:Generator_ColumnPropNameInRow="PONo" msprop:Generator_ColumnVarNameInTable="columnPONo" msprop:Generator_ColumnPropNameInTable="PONoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Rate" msprop:Generator_UserColumnName="Rate" msprop:Generator_ColumnPropNameInRow="Rate" msprop:Generator_ColumnVarNameInTable="columnRate" msprop:Generator_ColumnPropNameInTable="RateColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Discount" msprop:Generator_UserColumnName="Discount" msprop:Generator_ColumnPropNameInRow="Discount" msprop:Generator_ColumnVarNameInTable="columnDiscount" msprop:Generator_ColumnPropNameInTable="DiscountColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Excise" msprop:Generator_UserColumnName="Excise" msprop:Generator_ColumnPropNameInRow="Excise" msprop:Generator_ColumnVarNameInTable="columnExcise" msprop:Generator_ColumnPropNameInTable="ExciseColumn" type="xs:string" minOccurs="0" />
              <xs:element name="SupplierName" msprop:Generator_UserColumnName="SupplierName" msprop:Generator_ColumnPropNameInRow="SupplierName" msprop:Generator_ColumnVarNameInTable="columnSupplierName" msprop:Generator_ColumnPropNameInTable="SupplierNameColumn" type="xs:string" minOccurs="0" />
              <xs:element name="SupplierId" msprop:Generator_UserColumnName="SupplierId" msprop:Generator_ColumnPropNameInRow="SupplierId" msprop:Generator_ColumnVarNameInTable="columnSupplierId" msprop:Generator_ColumnPropNameInTable="SupplierIdColumn" type="xs:string" minOccurs="0" />
              <xs:element name="IndirectCost" msprop:Generator_UserColumnName="IndirectCost" msprop:Generator_ColumnPropNameInRow="IndirectCost" msprop:Generator_ColumnVarNameInTable="columnIndirectCost" msprop:Generator_ColumnPropNameInTable="IndirectCostColumn" type="xs:double" minOccurs="0" />
              <xs:element name="DirectCost" msprop:Generator_UserColumnName="DirectCost" msprop:Generator_ColumnPropNameInRow="DirectCost" msprop:Generator_ColumnVarNameInTable="columnDirectCost" msprop:Generator_ColumnPropNameInTable="DirectCostColumn" type="xs:double" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="VAT" msprop:Generator_UserColumnName="VAT" msprop:Generator_ColumnPropNameInRow="VAT" msprop:Generator_ColumnVarNameInTable="columnVAT" msprop:Generator_ColumnPropNameInTable="VATColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PF" msprop:Generator_UserColumnName="PF" msprop:Generator_ColumnPropNameInRow="PF" msprop:Generator_ColumnVarNameInTable="columnPF" msprop:Generator_ColumnPropNameInTable="PFColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Amount" msprop:Generator_UserColumnName="Amount" msprop:Generator_ColumnVarNameInTable="columnAmount" msprop:Generator_ColumnPropNameInRow="Amount" msprop:Generator_ColumnPropNameInTable="AmountColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Symbol" msprop:Generator_UserColumnName="Symbol" msprop:Generator_ColumnPropNameInRow="Symbol" msprop:Generator_ColumnVarNameInTable="columnSymbol" msprop:Generator_ColumnPropNameInTable="SymbolColumn" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>