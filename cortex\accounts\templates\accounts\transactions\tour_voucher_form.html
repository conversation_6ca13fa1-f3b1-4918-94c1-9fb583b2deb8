<!-- accounts/templates/accounts/transactions/tour_voucher_form.html -->
<!-- Tour Voucher Create/Edit Form Template -->
<!-- Task Group 9: Tour/Expense Management - Tour Voucher Form (Task 9.3) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{% if object %}Edit Tour Voucher{% else %}New Tour Voucher{% endif %} - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-blue-600 to-sap-blue-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="plane" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">
                        {% if object %}Edit Tour Voucher{% else %}New Tour Voucher{% endif %}
                    </h1>
                    <p class="text-sm text-sap-gray-600 mt-1">
                        {% if object %}Modify tour details and arrangements{% else %}Create a new tour voucher for employee travel{% endif %}
                    </p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:tour_voucher_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Tour Voucher Form -->
    <div class="max-w-6xl mx-auto">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="edit" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                    Tour Information
                </h3>
                <p class="text-sm text-sap-gray-600 mt-1">Complete all required fields to create the tour voucher</p>
            </div>
            
            <form method="post" id="tour-form" class="p-6" x-data="tourForm()">
                {% csrf_token %}
                
                <!-- Basic Tour Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="info" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Basic Tour Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Tour Number -->
                        <div>
                            <label for="{{ form.tour_number.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Tour Number
                            </label>
                            {{ form.tour_number|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.tour_number.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.tour_number.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Auto-generated if left blank</p>
                        </div>
                        
                        <!-- Tour Category -->
                        <div>
                            <label for="{{ form.tour_category.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Tour Category *
                            </label>
                            {{ form.tour_category|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.tour_category.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.tour_category.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Tour Type -->
                        <div>
                            <label for="{{ form.tour_type.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Tour Type *
                            </label>
                            {{ form.tour_type|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.tour_type.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.tour_type.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Tour Purpose -->
                        <div class="md:col-span-3">
                            <label for="{{ form.tour_purpose.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Tour Purpose *
                            </label>
                            {{ form.tour_purpose|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.tour_purpose.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.tour_purpose.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Brief description of the tour purpose and objectives</p>
                        </div>
                    </div>
                </div>
                
                <!-- Employee Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="user" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                        Employee Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Employee Name -->
                        <div>
                            <label for="{{ form.employee_name.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Employee Name *
                            </label>
                            {{ form.employee_name|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.employee_name.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.employee_name.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Employee Code -->
                        <div>
                            <label for="{{ form.employee_code.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Employee Code
                            </label>
                            {{ form.employee_code|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.employee_code.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.employee_code.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Department -->
                        <div>
                            <label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Department
                            </label>
                            {{ form.department|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.department.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.department.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Designation -->
                        <div>
                            <label for="{{ form.designation.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Designation
                            </label>
                            {{ form.designation|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.designation.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.designation.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Grade -->
                        <div>
                            <label for="{{ form.grade.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Grade
                            </label>
                            {{ form.grade|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.grade.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.grade.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Reporting Manager -->
                        <div>
                            <label for="{{ form.reporting_manager.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Reporting Manager
                            </label>
                            {{ form.reporting_manager|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.reporting_manager.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.reporting_manager.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Travel Details Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="map-pin" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                        Travel Details
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- From Location -->
                        <div>
                            <label for="{{ form.from_location.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                From Location *
                            </label>
                            {{ form.from_location|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.from_location.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.from_location.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Destination -->
                        <div>
                            <label for="{{ form.destination.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Destination *
                            </label>
                            {{ form.destination|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.destination.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.destination.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Mode of Transport -->
                        <div>
                            <label for="{{ form.mode_of_transport.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Mode of Transport *
                            </label>
                            {{ form.mode_of_transport|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.mode_of_transport.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.mode_of_transport.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Class of Travel -->
                        <div>
                            <label for="{{ form.class_of_travel.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Class of Travel
                            </label>
                            {{ form.class_of_travel|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.class_of_travel.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.class_of_travel.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Tour Duration Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="calendar" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                        Tour Duration
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <!-- Start Date -->
                        <div>
                            <label for="{{ form.start_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Start Date *
                            </label>
                            {{ form.start_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.start_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.start_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- End Date -->
                        <div>
                            <label for="{{ form.end_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                End Date *
                            </label>
                            {{ form.end_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.end_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.end_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Duration Days -->
                        <div>
                            <label for="{{ form.duration_days.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Duration (Days)
                            </label>
                            {{ form.duration_days|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.duration_days.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.duration_days.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Auto-calculated</p>
                        </div>
                        
                        <!-- Working Days -->
                        <div>
                            <label for="{{ form.working_days.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Working Days
                            </label>
                            {{ form.working_days|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.working_days.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.working_days.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Financial Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="dollar-sign" class="w-5 h-5 mr-2 text-sap-indigo-600"></i>
                        Financial Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Advance Amount -->
                        <div>
                            <label for="{{ form.advance_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Advance Amount (₹)
                            </label>
                            {{ form.advance_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.advance_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.advance_amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Estimated Budget -->
                        <div>
                            <label for="{{ form.estimated_budget.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Estimated Budget (₹)
                            </label>
                            {{ form.estimated_budget|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.estimated_budget.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.estimated_budget.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Per Day Allowance -->
                        <div>
                            <label for="{{ form.per_day_allowance.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Per Day Allowance (₹)
                            </label>
                            {{ form.per_day_allowance|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.per_day_allowance.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.per_day_allowance.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Additional Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="file-text" class="w-5 h-5 mr-2 text-sap-gray-600"></i>
                        Additional Information
                    </h4>
                    <div class="grid grid-cols-1 gap-6">
                        <!-- Tour Description -->
                        <div>
                            <label for="{{ form.tour_description.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Tour Description
                            </label>
                            {{ form.tour_description|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.tour_description.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.tour_description.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Detailed description of tour activities and schedule</p>
                        </div>
                        
                        <!-- Remarks -->
                        <div>
                            <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Remarks
                            </label>
                            {{ form.remarks|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.remarks.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.remarks.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Additional notes or special instructions</p>
                        </div>
                    </div>
                </div>
                
                <!-- Budget Calculation Display -->
                <div class="mb-8 bg-sap-blue-50 border border-sap-blue-200 rounded-lg p-6" x-show="calculatedBudget > 0">
                    <h4 class="text-lg font-medium text-sap-blue-800 mb-4 flex items-center">
                        <i data-lucide="calculator" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Budget Calculation
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="text-center">
                            <p class="text-sm text-sap-blue-600">Daily Allowance</p>
                            <p class="text-xl font-bold text-sap-blue-800" x-text="'₹' + dailyAllowance.toFixed(2)"></p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-blue-600">Total Days</p>
                            <p class="text-xl font-bold text-sap-blue-800" x-text="totalDays"></p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-blue-600">Total Budget</p>
                            <p class="text-xl font-bold text-sap-blue-800" x-text="'₹' + calculatedBudget.toFixed(2)"></p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-blue-600">Advance %</p>
                            <p class="text-xl font-bold text-sap-blue-800" x-text="advancePercentage + '%'"></p>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="resetForm()" 
                                class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="rotate-ccw" class="w-4 h-4 inline mr-2"></i>
                            Reset
                        </button>
                        <button type="button" @click="calculateBudget()" 
                                class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="calculator" class="w-4 h-4 inline mr-2"></i>
                            Calculate Budget
                        </button>
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="{% url 'accounts:tour_voucher_list' %}" 
                           class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                            {% if object %}Update Tour{% else %}Create Tour{% endif %}
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Tour Guidelines -->
        <div class="mt-6 bg-sap-amber-50 border border-sap-amber-200 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i data-lucide="info" class="w-5 h-5 text-sap-amber-600"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-sap-amber-800">Tour Voucher Guidelines</h3>
                    <div class="mt-2 text-sm text-sap-amber-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Tour number will be auto-generated if not provided</li>
                            <li>Ensure all employee details are accurate for approval workflow</li>
                            <li>Advance amount should not exceed 80% of estimated budget</li>
                            <li>Mode of transport should align with company travel policy</li>
                            <li>Submit tour voucher at least 7 days before travel date</li>
                            <li>Keep all receipts for expense settlement after tour completion</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function tourForm() {
    return {
        calculatedBudget: 0,
        dailyAllowance: 0,
        totalDays: 0,
        advancePercentage: 0,
        
        calculateBudget() {
            const perDayAllowance = parseFloat(document.getElementById('{{ form.per_day_allowance.id_for_label }}').value) || 0;
            const durationDays = parseFloat(document.getElementById('{{ form.duration_days.id_for_label }}').value) || 0;
            const advanceAmount = parseFloat(document.getElementById('{{ form.advance_amount.id_for_label }}').value) || 0;
            
            if (perDayAllowance && durationDays) {
                this.calculatedBudget = perDayAllowance * durationDays;
                this.dailyAllowance = perDayAllowance;
                this.totalDays = durationDays;
                
                // Update estimated budget field
                const budgetField = document.getElementById('{{ form.estimated_budget.id_for_label }}');
                if (budgetField) {
                    budgetField.value = this.calculatedBudget.toFixed(2);
                }
                
                // Calculate advance percentage
                if (advanceAmount > 0) {
                    this.advancePercentage = ((advanceAmount / this.calculatedBudget) * 100).toFixed(1);
                }
            } else {
                alert('Please enter per day allowance and duration to calculate budget.');
            }
        },
        
        calculateDuration() {
            const startDate = document.getElementById('{{ form.start_date.id_for_label }}').value;
            const endDate = document.getElementById('{{ form.end_date.id_for_label }}').value;
            
            if (startDate && endDate) {
                const start = new Date(startDate);
                const end = new Date(endDate);
                const diffTime = Math.abs(end - start);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
                
                const durationField = document.getElementById('{{ form.duration_days.id_for_label }}');
                if (durationField) {
                    durationField.value = diffDays;
                }
                
                this.totalDays = diffDays;
            }
        }
    }
}

function resetForm() {
    if (confirm('Are you sure you want to reset the form? All unsaved changes will be lost.')) {
        document.getElementById('tour-form').reset();
    }
}

// Auto-generate tour number if creating new tour
document.addEventListener('DOMContentLoaded', function() {
    const tourNumberInput = document.getElementById('{{ form.tour_number.id_for_label }}');
    if (tourNumberInput && !tourNumberInput.value) {
        // Generate tour number based on current timestamp
        const today = new Date();
        const year = today.getFullYear().toString().substr(-2);
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        const sequence = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        
        tourNumberInput.value = `TV-${year}${month}${day}${sequence}`;
    }
    
    // Auto-calculate duration when dates change
    const startDateField = document.getElementById('{{ form.start_date.id_for_label }}');
    const endDateField = document.getElementById('{{ form.end_date.id_for_label }}');
    
    [startDateField, endDateField].forEach(field => {
        if (field) {
            field.addEventListener('change', function() {
                calculateDuration();
            });
        }
    });
    
    // Auto-calculate working days (excluding weekends)
    const durationField = document.getElementById('{{ form.duration_days.id_for_label }}');
    const workingDaysField = document.getElementById('{{ form.working_days.id_for_label }}');
    
    if (durationField && workingDaysField) {
        durationField.addEventListener('change', function() {
            const totalDays = parseInt(this.value) || 0;
            // Estimate working days (assuming 5/7 ratio)
            const workingDays = Math.ceil(totalDays * 5 / 7);
            workingDaysField.value = workingDays;
        });
    }
    
    lucide.createIcons();
});

function calculateDuration() {
    const startDate = document.getElementById('{{ form.start_date.id_for_label }}').value;
    const endDate = document.getElementById('{{ form.end_date.id_for_label }}').value;
    
    if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        const diffTime = Math.abs(end - start);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
        
        const durationField = document.getElementById('{{ form.duration_days.id_for_label }}');
        if (durationField) {
            durationField.value = diffDays;
            
            // Trigger change event to calculate working days
            durationField.dispatchEvent(new Event('change'));
        }
    }
}
</script>
{% endblock %>