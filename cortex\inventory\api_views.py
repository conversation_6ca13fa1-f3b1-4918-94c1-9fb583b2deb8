from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.db.models import Q
from django.views import View
from django.utils.decorators import method_decorator
from django.shortcuts import render
from design.models import Item, Category
from .models import ItemLocation
import json


@require_http_methods(["GET"])
def item_search_api(request):
    """API endpoint for searching items with category filter, pagination, and scenario-based filtering"""
    
    # Get search parameters
    query = request.GET.get('q', '').strip()
    item_search = request.GET.get('item_search', '').strip()  # Alternative parameter name
    category_id = request.GET.get('category', '') or request.GET.get('category_filter', '')
    location = request.GET.get('location', '')
    
    # Get search type parameter
    search_type = request.GET.get('search_type', 'category').strip()
    
    try:
        page = int(request.GET.get('page', 1))
    except (ValueError, TypeError):
        page = 1
    try:
        limit = int(request.GET.get('limit', 20))
    except (ValueError, TypeError):
        limit = 20
    
    # Use either query parameter
    search_term = query or item_search
    
    # Start with all items
    items = Item.objects.select_related('compid').all()
    
    # Apply search type filtering (basic implementation)
    # This can be extended later to implement specific work order material filtering
    
    # Apply category filter if provided
    if category_id:
        try:
            category_id = int(category_id)
            items = items.filter(cid=category_id)
        except (ValueError, TypeError):
            pass
    
    # Apply location filter if provided
    if location:
        try:
            # If location is numeric, filter by location ID
            location_id = int(location)
            items = items.filter(location=location_id)
        except (ValueError, TypeError):
            # If location is text, try to find matching location by location_no or label
            location_obj = ItemLocation.objects.filter(
                Q(location_no__icontains=location) | 
                Q(location_label__icontains=location)
            ).first()
            if location_obj:
                items = items.filter(location=location_obj.id)
    
    # Apply search filter if query is provided (minimum 2 characters)
    if search_term and len(search_term) >= 2:
        items = items.filter(
            Q(itemcode__icontains=search_term) | 
            Q(manfdesc__icontains=search_term)
        )
    
    # Get total count for pagination
    total_count = items.count()
    
    # Apply pagination
    start_idx = (page - 1) * limit
    end_idx = start_idx + limit
    items = items.order_by('itemcode')[start_idx:end_idx]
    
    # Build response data
    results = []
    for item in items:
        # Get category name
        category_name = "Unknown"
        if item.cid:
            try:
                category = Category.objects.get(cid=item.cid)
                category_name = category.cname
            except Category.DoesNotExist:
                pass
        
        # Determine stock status
        stock_status = "in_stock" if item.stockqty > 0 else "out_of_stock"
        stock_warning = "" if item.stockqty > 0 else "Out of Stock"
        
        # Check minimum stock level
        if item.minstockqty and item.stockqty <= item.minstockqty:
            stock_status = "low_stock"
            stock_warning = f"Low Stock (Min: {item.minstockqty})"
        
        # Get location display name
        location_display = 'Unknown'
        if item.location:
            try:
                location_obj = ItemLocation.objects.get(id=item.location)
                location_display = f"{location_obj.location_label}-{location_obj.location_no}" if location_obj.location_label and location_obj.location_no else (location_obj.location_no or location_obj.description or f"Location {location_obj.id}")
            except ItemLocation.DoesNotExist:
                location_display = f"Location {item.location}"
        
        results.append({
            'id': item.id,
            'item_code': item.itemcode or '',
            'description': item.manfdesc or '',
            'category_name': category_name,
            'available_stock': float(item.stockqty),
            'min_stock': float(item.minstockqty) if item.minstockqty else 0,
            'stock_status': stock_status,
            'stock_warning': stock_warning,
            'uom': item.uombasic or '',
            'location': location_display
        })
    
    # Check if this is an HTMX request
    if request.headers.get('HX-Request'):
        # Return HTML template for HTMX
        context = {
            'results': results,
            'total_results': len(results),
            'query': search_term,
            'category_filter': category_id
        }
        return render(request, 'inventory/partials/item_search_results.html', context)
    else:
        # Return JSON for API calls
        return JsonResponse({
            'success': True,
            'results': results,
            'total_results': len(results),
            'total_count': total_count,
            'page': page,
            'per_page': limit,
            'total_pages': (total_count + limit - 1) // limit,
            'query': search_term,
            'category_filter': category_id,
            'location': location,
            'search_type': search_type
        })


@require_http_methods(["GET"])
def item_details_api(request):
    """API endpoint for getting detailed item information"""
    
    item_id = request.GET.get('item_id')
    if not item_id:
        return JsonResponse({'success': False, 'error': 'Item ID is required'})
    
    try:
        item = Item.objects.select_related('compid').get(id=item_id)
        
        # Get category name
        category_name = "Unknown"
        if item.cid:
            try:
                category = Category.objects.get(cid=item.cid)
                category_name = category.cname
            except Category.DoesNotExist:
                pass
        
        # Calculate stock status
        stock_status = "in_stock" if item.stockqty > 0 else "out_of_stock"
        stock_warning = ""
        
        if item.stockqty <= 0:
            stock_warning = "Out of Stock"
        elif item.minstockqty and item.stockqty <= item.minstockqty:
            stock_status = "low_stock"
            stock_warning = f"Low Stock (Min: {item.minstockqty})"
        
        data = {
            'success': True,
            'item': {
                'id': item.id,
                'item_code': item.itemcode or '',
                'description': item.manfdesc or '',
                'category_name': category_name,
                'category_id': item.cid,
                'available_stock': float(item.stockqty),
                'min_stock': float(item.minstockqty) if item.minstockqty else 0,
                'stock_status': stock_status,
                'stock_warning': stock_warning,
                'uom': item.uombasic or '',
                'location': item.location or '',
                'part_no': item.partno or '',
                'hsn_code': item.hsncode or '',
                'lead_days': item.leaddays or '',
                'min_order_qty': float(item.minorderqty) if item.minorderqty else 0
            }
        }
        
        return JsonResponse(data)
        
    except Item.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Item not found'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@require_http_methods(["GET"])
def categories_api(request):
    """API endpoint for getting all categories"""
    
    try:
        categories = Category.objects.filter(
            compid__isnull=False
        ).order_by('cname')
        
        results = []
        for category in categories:
            results.append({
                'id': category.cid,
                'name': category.cname,
                'symbol': category.symbol or ''
            })
        
        return JsonResponse({
            'success': True,
            'categories': results
        })
        
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@require_http_methods(["GET"])
def locations_api(request):
    """API endpoint for getting all item locations"""
    
    try:
        locations = ItemLocation.objects.filter(
            company__isnull=False
        ).order_by('location_label', 'location_no')
        
        results = []
        for location in locations:
            results.append({
                'id': location.id,
                'location_no': location.location_no or '',
                'location_label': location.location_label or '',
                'description': location.description or '',
                'display_name': f"{location.location_label}-{location.location_no}" if location.location_label and location.location_no else (location.location_no or location.description or f"Location {location.id}")
            })
        
        return JsonResponse({
            'success': True,
            'locations': results
        })
        
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@require_http_methods(["GET"])
def stock_availability_api(request):
    """API endpoint for checking stock availability for multiple items"""
    
    try:
        # Get item IDs and quantities from request
        items_data = request.GET.get('items')
        if not items_data:
            return JsonResponse({'success': False, 'error': 'Items data is required'})
        
        # Parse items data (expected format: "item_id:qty,item_id:qty")
        availability_results = []
        
        for item_pair in items_data.split(','):
            if ':' in item_pair:
                item_id, qty = item_pair.strip().split(':')
                try:
                    item_id = int(item_id)
                    qty = float(qty)
                    
                    item = Item.objects.get(id=item_id)
                    is_available = item.stockqty >= qty
                    
                    availability_results.append({
                        'item_id': item_id,
                        'item_code': item.itemcode,
                        'requested_qty': qty,
                        'available_stock': float(item.stockqty),
                        'is_available': is_available,
                        'shortage': max(0, qty - item.stockqty) if not is_available else 0
                    })
                    
                except (Item.DoesNotExist, ValueError, TypeError):
                    availability_results.append({
                        'item_id': item_id,
                        'error': 'Item not found or invalid data'
                    })
        
        return JsonResponse({
            'success': True,
            'availability': availability_results
        })
        
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@method_decorator(csrf_exempt, name='dispatch')
class ItemAutocompleteAPI(View):
    """Advanced item autocomplete API with pagination and filters"""
    
    def get(self, request):
        query = request.GET.get('q', '').strip()
        category_id = request.GET.get('category')
        limit = int(request.GET.get('limit', 10))
        offset = int(request.GET.get('offset', 0))
        
        # Base queryset
        items = Item.objects.select_related('compid').all()
        
        # Apply filters
        if category_id:
            try:
                items = items.filter(cid=int(category_id))
            except (ValueError, TypeError):
                pass
        
        if query and len(query) >= 2:
            items = items.filter(
                Q(itemcode__icontains=query) |
                Q(manfdesc__icontains=query)
            )
        
        # Get total count for pagination
        total_count = items.count()
        
        # Apply pagination
        items = items.order_by('itemcode')[offset:offset + limit]
        
        # Build results
        results = []
        for item in items:
            # Get category name
            category_name = "Unknown"
            if item.cid:
                try:
                    category = Category.objects.get(cid=item.cid)
                    category_name = category.cname
                except Category.DoesNotExist:
                    pass
            
            results.append({
                'id': item.id,
                'text': f"{item.itemcode} - {item.manfdesc}",
                'item_code': item.itemcode or '',
                'description': item.manfdesc or '',
                'category': category_name,
                'stock': float(item.stockqty),
                'min_stock': float(item.minstockqty) if item.minstockqty else 0,
                'uom': item.uombasic or ''
            })
        
        return JsonResponse({
            'results': results,
            'pagination': {
                'more': (offset + limit) < total_count,
                'total': total_count,
                'offset': offset,
                'limit': limit
            }
        })


# Additional utility functions for MRS form
@require_http_methods(["POST"])
@csrf_exempt
def add_mrs_temp_item(request):
    """Add item to MRS temporary storage"""
    
    if not request.user.is_authenticated:
        return JsonResponse({'success': False, 'error': 'Authentication required'}, status=401)
    
    try:
        data = json.loads(request.body)
        item_id = data.get('item_id')
        quantity = data.get('quantity', 0)
        remarks = data.get('remarks', '')
        bg_wo_type = data.get('bg_wo_type', '')
        bg_wo_value = data.get('bg_wo_value', '')
        
        # Validate required fields
        if not item_id:
            return JsonResponse({'success': False, 'error': 'Item ID is required'})
        
        if not bg_wo_type:
            return JsonResponse({'success': False, 'error': 'BG Group or Work Order type is required'})
        
        if not bg_wo_value:
            field_name = 'Department' if bg_wo_type == 'BGGroup' else 'Work Order Number'
            return JsonResponse({'success': False, 'error': f'{field_name} is required'})
        
        try:
            quantity = float(quantity)
            if quantity <= 0:
                return JsonResponse({'success': False, 'error': 'Quantity must be greater than zero'})
        except (ValueError, TypeError):
            return JsonResponse({'success': False, 'error': 'Invalid quantity'})
        
        # Check if item exists and validate stock
        try:
            item = Item.objects.get(id=item_id)
            if quantity > item.stockqty:
                return JsonResponse({
                    'success': False, 
                    'error': f'Requested quantity ({quantity}) exceeds available stock ({item.stockqty})'
                })
        except Item.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Item not found'})
        
        # Use raw SQL to insert into temp table
        from django.db import connection
        
        session_id = getattr(request.user, 'username', 'admin')
        company_id = 1  # Default company ID
        
        # Convert bg_wo_value to dept_id if it's BGGroup
        dept_id = None
        wo_no = None
        
        if bg_wo_type == 'BGGroup':
            # For now, use a simple mapping or store as text
            dept_id = bg_wo_value  # Store as text for now
        else:
            wo_no = bg_wo_value
        
        # Check if item already exists in temp for this session
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT Id FROM tblinv_MaterialRequisition_Temp 
                WHERE CompId = %s AND SessionId = %s AND ItemId = %s
            """, [company_id, session_id, item_id])
            
            existing_item = cursor.fetchone()
            
            if existing_item:
                return JsonResponse({
                    'success': False, 
                    'error': 'Item already added to requisition'
                })
            
            # Insert new temp item
            cursor.execute("""
                INSERT INTO tblinv_MaterialRequisition_Temp 
                (CompId, SessionId, ItemId, DeptId, WONo, ReqQty, Remarks)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, [company_id, session_id, item_id, dept_id, wo_no, quantity, remarks])
        
        return JsonResponse({
            'success': True,
            'message': f'Item {item.itemcode} added to requisition successfully',
            'item': {
                'id': item.id,
                'item_code': item.itemcode,
                'description': item.manfdesc,
                'requested_quantity': quantity,
                'remarks': remarks,
                'bg_wo_type': bg_wo_type,
                'bg_wo_value': bg_wo_value
            }
        })
        
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@require_http_methods(["GET"])
def get_mrs_temp_items(request):
    """Get MRS temporary items for current session"""
    
    if not request.user.is_authenticated:
        return JsonResponse({'success': False, 'error': 'Authentication required'}, status=401)
    
    try:
        from django.db import connection
        
        session_id = getattr(request.user, 'username', 'admin')
        company_id = 1  # Default company ID
        
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT 
                    t.Id, t.ItemId, t.DeptId, t.WONo, t.ReqQty, t.Remarks,
                    i.ItemCode, i.ManfDesc, i.UOMBasic, i.StockQty
                FROM tblinv_MaterialRequisition_Temp t
                INNER JOIN tblDG_Item_Master i ON t.ItemId = i.Id
                WHERE t.CompId = %s AND t.SessionId = %s
                ORDER BY t.Id DESC
            """, [company_id, session_id])
            
            results = cursor.fetchall()
            
            items = []
            for row in results:
                # Determine bg_wo_type and bg_wo_value
                dept_id = row[2]
                wo_no = row[3]
                
                if dept_id and not wo_no:
                    bg_wo_type = 'BGGroup'
                    bg_wo_value = dept_id
                elif wo_no:
                    bg_wo_type = 'WorkOrder'
                    bg_wo_value = wo_no
                else:
                    bg_wo_type = ''
                    bg_wo_value = ''
                
                items.append({
                    'temp_id': row[0],
                    'id': row[1],
                    'item_code': row[6] or '',
                    'description': row[7] or '',
                    'uom': row[8] or 'PCS',
                    'available_stock': float(row[9]) if row[9] else 0,
                    'requested_quantity': float(row[4]) if row[4] else 0,
                    'remarks': row[5] or '',
                    'bg_wo_type': bg_wo_type,
                    'bg_wo_value': bg_wo_value
                })
        
        return JsonResponse({
            'success': True,
            'items': items,
            'count': len(items)
        })
        
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@require_http_methods(["POST"])
@csrf_exempt
def remove_mrs_temp_item(request):
    """Remove item from MRS temporary storage"""
    
    if not request.user.is_authenticated:
        return JsonResponse({'success': False, 'error': 'Authentication required'}, status=401)
    
    try:
        data = json.loads(request.body)
        temp_id = data.get('temp_id')
        
        if not temp_id:
            return JsonResponse({'success': False, 'error': 'Temp ID is required'})
        
        from django.db import connection
        
        session_id = getattr(request.user, 'username', 'admin')
        company_id = 1  # Default company ID
        
        with connection.cursor() as cursor:
            # Delete the temp item (only if it belongs to current session)
            cursor.execute("""
                DELETE FROM tblinv_MaterialRequisition_Temp 
                WHERE Id = %s AND CompId = %s AND SessionId = %s
            """, [temp_id, company_id, session_id])
            
            if cursor.rowcount == 0:
                return JsonResponse({'success': False, 'error': 'Item not found or access denied'})
        
        return JsonResponse({
            'success': True,
            'message': 'Item removed from requisition successfully'
        })
        
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@require_http_methods(["POST"])
@csrf_exempt
def validate_mrs_submission(request):
    """Validate MRS form submission before saving"""
    
    try:
        data = json.loads(request.body)
        line_items = data.get('line_items', [])
        
        validation_errors = []
        stock_warnings = []
        
        for i, item_data in enumerate(line_items):
            item_id = item_data.get('item_id')
            qty = item_data.get('quantity', 0)
            
            if not item_id:
                validation_errors.append(f"Line {i+1}: Item is required")
                continue
            
            if qty <= 0:
                validation_errors.append(f"Line {i+1}: Quantity must be greater than zero")
                continue
            
            try:
                item = Item.objects.get(id=item_id)
                
                if qty > item.stockqty:
                    validation_errors.append(
                        f"Line {i+1}: Requested quantity ({qty}) exceeds available stock ({item.stockqty}) for {item.itemcode}"
                    )
                elif item.minstockqty and (item.stockqty - qty) < item.minstockqty:
                    stock_warnings.append(
                        f"Line {i+1}: Issuing {qty} units of {item.itemcode} will bring stock below minimum level"
                    )
                    
            except Item.DoesNotExist:
                validation_errors.append(f"Line {i+1}: Selected item not found")
        
        return JsonResponse({
            'success': len(validation_errors) == 0,
            'validation_errors': validation_errors,
            'stock_warnings': stock_warnings
        })
        
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})