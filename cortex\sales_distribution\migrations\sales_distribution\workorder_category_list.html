<!-- sales_distribution/templates/sales_distribution/workorder_category_list.html -->
<!-- Professional Work Order Category Management - SAP S/4HANA Inspired Design -->
<!-- Replaces ASP.NET CategoryEdit.aspx functionality -->

{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }} - Cortex{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-sap-blue-500 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="layers" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Work Order Category Management</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Configure and manage work order categories for sales distribution</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <div class="text-right mr-4 px-4 py-2 bg-sap-gray-50 rounded-lg border border-sap-gray-200">
                    <p class="text-2xs font-medium text-sap-gray-500 uppercase tracking-wide">Total Categories</p>
                    <p class="text-lg font-semibold text-sap-blue-600" id="category-count">{{ categories|length }}</p>
                </div>
                <a href="{% url 'sales_distribution:subcategory_list' %}" 
                   class="inline-flex items-center px-4 py-2.5 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 hover:border-sap-blue-300 transition-all duration-200">
                    <i data-lucide="git-branch" class="w-4 h-4 mr-2"></i>
                    Manage Sub-Categories
                </a>
                <a href="{% url 'sales_distribution:customer_list' %}" 
                   class="inline-flex items-center px-4 py-2.5 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 hover:border-sap-blue-300 transition-all duration-200">
                    <i data-lucide="users" class="w-4 h-4 mr-2"></i>
                    Manage Customers
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-2 py-4 space-y-2">
    
    <!-- Add New Category Card -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-3 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-sap-green-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="plus" class="w-4 h-4 text-sap-green-600"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-sap-gray-800">Add New Category</h3>
                        <p class="text-sm text-sap-gray-600">Create a new work order category</p>
                    </div>
                </div>
                <button type="button" id="toggle-form-btn"
                        class="inline-flex items-center px-4 py-2 border border-sap-green-300 rounded-lg text-sm font-medium text-sap-green-700 bg-sap-green-50 hover:bg-sap-green-100 transition-colors duration-200">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    <span id="toggle-form-text">Show Form</span>
                </button>
            </div>
        </div>
        
        <!-- Add Category Form (Hidden by default) -->
        <div id="add-category-form" class="px-6 py-4 border-b border-sap-gray-100 hidden">
            <form hx-post="{% url 'sales_distribution:category_create' %}" 
                  hx-target="#category-table" 
                  hx-swap="outerHTML"
                  hx-trigger="submit"
                  class="space-y-4">
                {% csrf_token %}
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="{{ category_form.cname.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Category Name <span class="text-red-500">*</span>
                        </label>
                        {{ category_form.cname }}
                        {% if category_form.cname.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ category_form.cname.errors.0 }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ category_form.symbol.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Symbol
                        </label>
                        {{ category_form.symbol }}
                        {% if category_form.symbol.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ category_form.symbol.errors.0 }}</p>
                        {% endif %}
                    </div>
                    <div class="flex items-center space-x-3 pt-6">
                        <div class="flex items-center">
                            {{ category_form.hassubcat }}
                            <label for="{{ category_form.hassubcat.id_for_label }}" class="ml-2 text-sm font-medium text-sap-gray-700">
                                Has Sub-Categories
                            </label>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="hideForm()" 
                            class="px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-sap-blue-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-sap-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-blue-500">
                        Add Category
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Real-time Search and Filter Card -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4">
            <div class="flex items-center space-x-4">
                <div class="flex-1">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i data-lucide="search" class="w-4 h-4 text-sap-gray-400"></i>
                        </div>
                        <input type="text" 
                               id="real-time-search"
                               name="search" 
                               placeholder="Search categories in real-time..." 
                               value="{{ request.GET.search|default:'' }}"
                               hx-get="{% url 'sales_distribution:category_list' %}"
                               hx-target="#category-table"
                               hx-swap="outerHTML"
                               hx-trigger="keyup changed delay:300ms, search"
                               hx-include="[name='has_subcategory']"
                               class="block w-full pl-10 pr-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm placeholder-sap-gray-500 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                    </div>
                </div>
                <div class="w-48">
                    <select name="has_subcategory" 
                            id="has-subcategory-filter"
                            hx-get="{% url 'sales_distribution:category_list' %}"
                            hx-target="#category-table"
                            hx-swap="outerHTML"
                            hx-trigger="change"
                            hx-include="[name='search']"
                            class="block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        <option value="">All Categories</option>
                        <option value="1" {% if request.GET.has_subcategory == "1" %}selected{% endif %}>Has Sub-Categories</option>
                        <option value="0" {% if request.GET.has_subcategory == "0" %}selected{% endif %}>No Sub-Categories</option>
                    </select>
                </div>
                <div class="flex space-x-2">
                    <button type="button" 
                            onclick="clearSearch()"
                            class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                        <i data-lucide="x" class="w-4 h-4 mr-2"></i>
                        Clear
                    </button>
                </div>
            </div>
            <div class="mt-2 text-xs text-sap-gray-500">
                <i data-lucide="info" class="w-3 h-3 inline mr-1"></i>
                Search results update automatically as you type
            </div>
        </div>
    </div>

    <!-- Categories Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Categories</h3>
                <div class="text-sm text-sap-gray-600">
                    Showing {{ categories|length }} of {{ categories|length }} categories
                </div>
            </div>
        </div>
        
        <div id="category-table">
            {% include 'sales_distribution/partials/category_table.html' %}
        </div>
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="text-sm text-sap-gray-600">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ paginator.count }} results
                </div>
                <div class="flex space-x-2">
                    {% if page_obj.has_previous %}
                        <a href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.has_subcategory %}&has_subcategory={{ request.GET.has_subcategory }}{% endif %}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            First
                        </a>
                        <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.has_subcategory %}&has_subcategory={{ request.GET.has_subcategory }}{% endif %}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Previous
                        </a>
                    {% endif %}
                    
                    <span class="px-3 py-2 bg-sap-blue-50 border border-sap-blue-200 rounded-lg text-sm font-medium text-sap-blue-600">
                        Page {{ page_obj.number }} of {{ paginator.num_pages }}
                    </span>
                    
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.has_subcategory %}&has_subcategory={{ request.GET.has_subcategory }}{% endif %}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Next
                        </a>
                        <a href="?page={{ paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.has_subcategory %}&has_subcategory={{ request.GET.has_subcategory }}{% endif %}" 
                           class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                            Last
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

</div>

<script>
function showForm() {
    document.getElementById('add-category-form').classList.remove('hidden');
    document.getElementById('toggle-form-text').textContent = 'Hide Form';
    document.getElementById('toggle-form-btn').onclick = hideForm;
}

function hideForm() {
    document.getElementById('add-category-form').classList.add('hidden');
    document.getElementById('toggle-form-text').textContent = 'Show Form';
    document.getElementById('toggle-form-btn').onclick = showForm;
    // Clear form
    document.querySelector('#add-category-form form').reset();
}

// Clear search function
function clearSearch() {
    document.getElementById('real-time-search').value = '';
    document.getElementById('has-subcategory-filter').value = '';
    // Trigger HTMX to reload with cleared filters
    htmx.trigger('#real-time-search', 'keyup');
}

// Toggle form visibility
document.getElementById('toggle-form-btn').onclick = showForm;

// Confirmation dialogs
function confirmUpdate() {
    return confirm('Are you sure you want to update this category?');
}

function confirmDelete() {
    return confirm('Are you sure you want to delete this category? This action cannot be undone.');
}
</script>
{% endblock %}