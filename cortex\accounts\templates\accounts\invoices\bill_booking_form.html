<!-- accounts/templates/accounts/invoices/bill_booking_form.html -->
<!-- Bill Booking Create Form Template -->
<!-- ASP.NET Source: Module/Accounts/Transactions/BillBooking_New.aspx -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}New Bill Booking - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-orange-600 to-sap-orange-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="clipboard-check" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">
                        New Bill Booking
                    </h1>
                    <p class="text-sm text-sap-gray-600 mt-1">
                        Record vendor bill with approval workflow integration
                    </p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:bill_booking_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Bill Booking Form -->
    <div class="max-w-7xl mx-auto">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="edit" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                    Bill Booking Information
                </h3>
                <p class="text-sm text-sap-gray-600 mt-1">Enter vendor bill details for approval and payment processing</p>
            </div>
            
            <form method="post" id="bill-booking-form" class="p-6" x-data="billBookingForm()" hx-post="{% url 'accounts:bill_booking_create' %}" hx-target="#form-messages">
                {% csrf_token %}
                
                <!-- Form Messages -->
                <div id="form-messages" class="mb-4"></div>
                
                <!-- Vendor Information -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="building-2" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                        Vendor Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Vendor Name -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Vendor Name *
                            </label>
                            <input type="text" name="vendor_name" required
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500"
                                   placeholder="Enter vendor name" x-model="vendorName" @input="searchVendor()">
                            <!-- Vendor search dropdown would appear here -->
                        </div>
                        
                        <!-- Vendor Code -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Vendor Code
                            </label>
                            <input type="text" name="vendor_code"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500"
                                   placeholder="Vendor code" x-model="vendorCode">
                        </div>
                        
                        <!-- Vendor GST Number -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Vendor GST Number
                            </label>
                            <input type="text" name="vendor_gst_number"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500"
                                   placeholder="GST number" x-model="vendorGstNumber">
                        </div>
                        
                        <!-- Vendor Address -->
                        <div class="md:col-span-3">
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Vendor Address
                            </label>
                            <textarea rows="2" name="vendor_address"
                                      class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500 resize-none"
                                      placeholder="Enter vendor address" x-model="vendorAddress"></textarea>
                        </div>
                    </div>
                </div>
                
                <!-- Bill Details -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="file-text" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Bill Details
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <!-- Bill Number -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Bill Number *
                            </label>
                            <input type="text" name="bill_number" required
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"
                                   placeholder="Enter bill number">
                        </div>
                        
                        <!-- Bill Date -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Bill Date *
                            </label>
                            <input type="date" name="bill_date" required
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        </div>
                        
                        <!-- Due Date -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Due Date *
                            </label>
                            <input type="date" name="due_date" required
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        </div>
                        
                        <!-- Bill Type -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Bill Type *
                            </label>
                            <select name="bill_type" required
                                    class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                                <option value="">Select bill type</option>
                                <option value="purchase">Purchase Bill</option>
                                <option value="service">Service Bill</option>
                                <option value="expense">Expense Bill</option>
                                <option value="utility">Utility Bill</option>
                                <option value="rent">Rent Bill</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- Purchase Order Reference -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="shopping-cart" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                        Purchase Order Reference
                        <button type="button" @click="searchPO()" 
                                class="ml-auto bg-sap-purple-100 hover:bg-sap-purple-200 text-sap-purple-700 px-3 py-1 rounded text-sm font-medium transition-colors">
                            <i data-lucide="search" class="w-3 h-3 inline mr-1"></i>
                            Search PO
                        </button>
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <!-- PO Number -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                PO Number
                            </label>
                            <input type="text" name="po_number"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500"
                                   placeholder="Purchase order number" x-model="poNumber">
                        </div>
                        
                        <!-- PO Date -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                PO Date
                            </label>
                            <input type="date" name="po_date"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500"
                                   x-model="poDate">
                        </div>
                        
                        <!-- PO Amount -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                PO Amount
                            </label>
                            <input type="number" name="po_amount" step="0.01" min="0"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500"
                                   placeholder="Purchase order amount" x-model="poAmount">
                        </div>
                        
                        <!-- PO Balance -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                PO Balance
                            </label>
                            <input type="number" name="po_balance" step="0.01" min="0" readonly
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm bg-sap-gray-50"
                                   placeholder="Remaining PO balance" x-model="poBalance">
                        </div>
                    </div>
                </div>
                
                <!-- Account Head Mapping -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="layers" class="w-5 h-5 mr-2 text-sap-emerald-600"></i>
                        Account Head Mapping
                    </h4>
                    
                    <!-- Account Allocations Table -->
                    <div class="border border-sap-gray-300 rounded-lg overflow-hidden">
                        <table class="min-w-full divide-y divide-sap-gray-200" id="accounts-table">
                            <thead class="bg-sap-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Account Head</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Description</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Amount</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Tax Rate</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Tax Amount</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Total</th>
                                    <th class="px-4 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Action</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-sap-gray-200" x-ref="accountsTableBody">
                                <template x-for="(account, index) in accounts" :key="index">
                                    <tr>
                                        <td class="px-4 py-3">
                                            <select x-model="account.account_head" 
                                                    class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-emerald-500">
                                                <option value="">Select account</option>
                                                <option value="raw_materials">Raw Materials</option>
                                                <option value="office_supplies">Office Supplies</option>
                                                <option value="utilities">Utilities</option>
                                                <option value="rent">Rent</option>
                                                <option value="maintenance">Maintenance</option>
                                                <option value="professional_fees">Professional Fees</option>
                                                <option value="transportation">Transportation</option>
                                                <option value="other_expenses">Other Expenses</option>
                                            </select>
                                        </td>
                                        <td class="px-4 py-3">
                                            <input type="text" x-model="account.description" 
                                                   class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-emerald-500" 
                                                   placeholder="Description">
                                        </td>
                                        <td class="px-4 py-3">
                                            <input type="number" x-model="account.amount" @input="calculateAccountTax(index)" 
                                                   class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-emerald-500" 
                                                   placeholder="Amount" min="0" step="0.01">
                                        </td>
                                        <td class="px-4 py-3">
                                            <select x-model="account.tax_rate" @change="calculateAccountTax(index)" 
                                                    class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-emerald-500">
                                                <option value="0">0%</option>
                                                <option value="5">5%</option>
                                                <option value="12">12%</option>
                                                <option value="18">18%</option>
                                                <option value="28">28%</option>
                                            </select>
                                        </td>
                                        <td class="px-4 py-3">
                                            <span class="text-sm font-medium text-sap-gray-900" x-text="'₹' + (account.tax_amount || 0).toFixed(2)"></span>
                                        </td>
                                        <td class="px-4 py-3">
                                            <span class="text-sm font-medium text-sap-gray-900" x-text="'₹' + (account.total_amount || 0).toFixed(2)"></span>
                                        </td>
                                        <td class="px-4 py-3 text-center">
                                            <button type="button" @click="removeAccount(index)" 
                                                    class="text-sap-red-600 hover:text-sap-red-900">
                                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                        
                        <!-- Add Account Button -->
                        <div class="px-4 py-3 bg-sap-gray-50 border-t border-sap-gray-200">
                            <button type="button" @click="addAccount()" 
                                    class="bg-sap-emerald-600 hover:bg-sap-emerald-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                                Add Account Allocation
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- TDS/Advance Adjustments -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="calculator" class="w-5 h-5 mr-2 text-sap-red-600"></i>
                        TDS/Advance Adjustments
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <!-- TDS Applicable -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                TDS Applicable
                            </label>
                            <select name="tds_applicable" @change="calculateTDS()"
                                    class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-red-500 focus:border-sap-red-500">
                                <option value="no">No</option>
                                <option value="yes">Yes</option>
                            </select>
                        </div>
                        
                        <!-- TDS Rate -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                TDS Rate (%)
                            </label>
                            <select name="tds_rate" @change="calculateTDS()"
                                    class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-red-500 focus:border-sap-red-500">
                                <option value="0">0%</option>
                                <option value="1">1%</option>
                                <option value="2">2%</option>
                                <option value="5">5%</option>
                                <option value="10">10%</option>
                            </select>
                        </div>
                        
                        <!-- TDS Amount -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                TDS Amount
                            </label>
                            <input type="number" name="tds_amount" step="0.01" min="0" readonly
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm bg-sap-gray-50"
                                   x-model="tdsAmount">
                        </div>
                        
                        <!-- Advance Adjustment -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Advance Adjustment
                            </label>
                            <input type="number" name="advance_adjustment" step="0.01" min="0"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-red-500 focus:border-sap-red-500"
                                   placeholder="Advance amount" x-model="advanceAdjustment" @input="calculateTotals()">
                        </div>
                    </div>
                </div>
                
                <!-- Bill Summary -->
                <div class="mb-8 bg-sap-orange-50 border border-sap-orange-200 rounded-lg p-6">
                    <h4 class="text-lg font-medium text-sap-orange-800 mb-4 flex items-center">
                        <i data-lucide="receipt" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                        Bill Booking Summary
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4 text-center">
                        <div>
                            <p class="text-sm text-sap-orange-600">Gross Amount</p>
                            <p class="text-xl font-bold text-sap-orange-800" x-text="'₹' + grossAmount.toFixed(2)"></p>
                        </div>
                        <div>
                            <p class="text-sm text-sap-orange-600">Total Tax</p>
                            <p class="text-xl font-bold text-sap-orange-800" x-text="'₹' + totalTax.toFixed(2)"></p>
                        </div>
                        <div>
                            <p class="text-sm text-sap-orange-600">TDS Deduction</p>
                            <p class="text-xl font-bold text-sap-orange-800" x-text="'₹' + tdsAmount.toFixed(2)"></p>
                        </div>
                        <div>
                            <p class="text-sm text-sap-orange-600">Advance Adjustment</p>
                            <p class="text-xl font-bold text-sap-orange-800" x-text="'₹' + advanceAdjustment.toFixed(2)"></p>
                        </div>
                        <div>
                            <p class="text-sm text-sap-orange-600">Net Payable</p>
                            <p class="text-2xl font-bold text-sap-orange-800" x-text="'₹' + netPayable.toFixed(2)"></p>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="resetForm()" 
                                class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="rotate-ccw" class="w-4 h-4 inline mr-2"></i>
                            Reset
                        </button>
                        <button type="button" @click="validateBill()" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check-circle" class="w-4 h-4 inline mr-2"></i>
                            Validate Bill
                        </button>
                        <button type="button" @click="previewBill()" 
                                class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="eye" class="w-4 h-4 inline mr-2"></i>
                            Preview
                        </button>
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="{% url 'accounts:bill_booking_list' %}" 
                           class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                            Save Bill Booking
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Bill Booking Guidelines -->
        <div class="mt-6 bg-sap-orange-50 border border-sap-orange-200 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i data-lucide="info" class="w-5 h-5 text-sap-orange-600"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-sap-orange-800">Bill Booking Guidelines</h3>
                    <div class="mt-2 text-sm text-sap-orange-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>All bills require approval workflow before payment processing</li>
                            <li>Account head mapping ensures proper expense categorization</li>
                            <li>TDS calculations are automatic based on vendor type and amount</li>
                            <li>Purchase order reference links bills to procurement process</li>
                            <li>Advance adjustments help manage vendor payments</li>
                            <li>Bill validation checks all compliance requirements</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function billBookingForm() {
    return {
        vendorName: '',
        vendorCode: '',
        vendorGstNumber: '',
        vendorAddress: '',
        poNumber: '',
        poDate: '',
        poAmount: 0,
        poBalance: 0,
        accounts: [
            {
                account_head: '',
                description: '',
                amount: 0,
                tax_rate: 18,
                tax_amount: 0,
                total_amount: 0
            }
        ],
        grossAmount: 0,
        totalTax: 0,
        tdsAmount: 0,
        advanceAdjustment: 0,
        netPayable: 0,
        
        searchVendor() {
            // Vendor search functionality would be implemented here
            if (this.vendorName.length >= 3) {
                // Simulate vendor lookup
                console.log('Searching for vendor:', this.vendorName);
            }
        },
        
        searchPO() {
            if (!this.vendorName.trim()) {
                alert('Please select a vendor first to search for purchase orders.');
                return;
            }
            alert('Purchase order search functionality would show available POs for the selected vendor.');
        },
        
        addAccount() {
            this.accounts.push({
                account_head: '',
                description: '',
                amount: 0,
                tax_rate: 18,
                tax_amount: 0,
                total_amount: 0
            });
        },
        
        removeAccount(index) {
            if (this.accounts.length > 1) {
                this.accounts.splice(index, 1);
                this.calculateTotals();
            }
        },
        
        calculateAccountTax(index) {
            const account = this.accounts[index];
            const amount = parseFloat(account.amount) || 0;
            const taxRate = parseFloat(account.tax_rate) || 0;
            
            account.tax_amount = amount * (taxRate / 100);
            account.total_amount = amount + account.tax_amount;
            
            this.calculateTotals();
        },
        
        calculateTDS() {
            const tdsApplicable = document.querySelector('select[name="tds_applicable"]').value;
            const tdsRate = parseFloat(document.querySelector('select[name="tds_rate"]').value) || 0;
            
            if (tdsApplicable === 'yes' && tdsRate > 0) {
                this.tdsAmount = this.grossAmount * (tdsRate / 100);
            } else {
                this.tdsAmount = 0;
            }
            
            this.calculateTotals();
        },
        
        calculateTotals() {
            this.grossAmount = 0;
            this.totalTax = 0;
            
            this.accounts.forEach(account => {
                const amount = parseFloat(account.amount) || 0;
                const taxAmount = parseFloat(account.tax_amount) || 0;
                
                this.grossAmount += amount;
                this.totalTax += taxAmount;
            });
            
            this.netPayable = this.grossAmount + this.totalTax - this.tdsAmount - this.advanceAdjustment;
        },
        
        validateBill() {
            const vendorName = this.vendorName.trim();
            const billNumber = document.querySelector('input[name="bill_number"]').value.trim();
            const billDate = document.querySelector('input[name="bill_date"]').value;
            
            if (!vendorName) {
                alert('Please enter vendor name.');
                return;
            }
            
            if (!billNumber) {
                alert('Please enter bill number.');
                return;
            }
            
            if (!billDate) {
                alert('Please select bill date.');
                return;
            }
            
            if (this.accounts.length === 0 || !this.accounts[0].account_head) {
                alert('Please add at least one account allocation.');
                return;
            }
            
            if (this.netPayable <= 0) {
                alert('Please enter valid amounts for account allocations.');
                return;
            }
            
            alert('Bill validation passed. All required fields are completed.');
        },
        
        previewBill() {
            if (!this.vendorName.trim()) {
                alert('Please enter vendor name to preview bill.');
                return;
            }
            
            if (this.netPayable <= 0) {
                alert('Please enter valid amounts to preview bill.');
                return;
            }
            
            alert('Bill preview functionality would show bill details for verification.');
        }
    }
}

function resetForm() {
    if (confirm('Are you sure you want to reset the form? All unsaved changes will be lost.')) {
        location.reload();
    }
}

// Auto-setup on page load
document.addEventListener('DOMContentLoaded', function() {
    // Set default bill date to today
    const billDateField = document.querySelector('input[name="bill_date"]');
    if (billDateField && !billDateField.value) {
        const today = new Date().toISOString().split('T')[0];
        billDateField.value = today;
    }
    
    // Auto-set due date to 30 days from bill date
    const dueDateField = document.querySelector('input[name="due_date"]');
    if (billDateField && dueDateField) {
        billDateField.addEventListener('change', function() {
            if (!dueDateField.value && this.value) {
                const billDate = new Date(this.value);
                const dueDate = new Date(billDate);
                dueDate.setDate(dueDate.getDate() + 30);
                
                dueDateField.value = dueDate.toISOString().split('T')[0];
            }
        });
    }
    
    lucide.createIcons();
});
</script>
{% endblock %}