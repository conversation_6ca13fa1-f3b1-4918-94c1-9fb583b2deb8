{% extends 'core/base.html' %}
{% load static %}

{% block title %}MRS Detail - {{ object.mrs_number }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <!-- Breadcrumb -->
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-4">
                    <li>
                        <div>
                            <a href="{% url 'core:dashboard' %}" class="text-gray-400 hover:text-gray-500">
                                <svg class="flex-shrink-0 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                                </svg>
                                <span class="sr-only">Home</span>
                            </a>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                            </svg>
                            <a href="{% url 'inventory:mrs_list' %}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">MRS</a>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                            </svg>
                            <span class="ml-4 text-sm font-medium text-gray-500">{{ object.mrs_number }}</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- Page Header -->
            <div class="mt-4 md:flex md:items-center md:justify-between">
                <div class="flex-1 min-w-0">
                    <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                        Material Requisition Slip
                    </h1>
                    <div class="mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:mt-0 sm:space-x-6">
                        <div class="mt-2 flex items-center text-sm text-gray-500">
                            <span class="font-medium">{{ object.mrs_number }}</span>
                        </div>
                        <div class="mt-2 flex items-center text-sm text-gray-500">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if object.status == 'DRAFT' %}bg-gray-100 text-gray-800
                                {% elif object.status == 'SUBMITTED' %}bg-yellow-100 text-yellow-800
                                {% elif object.status == 'APPROVED' %}bg-green-100 text-green-800
                                {% elif object.status == 'PARTIALLY_ISSUED' %}bg-blue-100 text-blue-800
                                {% elif object.status == 'ISSUED' %}bg-purple-100 text-purple-800
                                {% elif object.status == 'CANCELLED' %}bg-red-100 text-red-800
                                {% endif %}">
                                {{ object.get_status_display }}
                            </span>
                        </div>
                        <div class="mt-2 flex items-center text-sm text-gray-500">
                            <svg class="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                            </svg>
                            {{ object.mrs_date|date:"M d, Y" }}
                        </div>
                    </div>
                </div>
                <div class="mt-4 flex md:mt-0 md:ml-4 space-x-3">
                    {% if object.status == 'DRAFT' and user == object.created_by %}
                        <a href="{% url 'inventory:mrs_update' object.pk %}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                            Edit
                        </a>
                    {% endif %}
                    
                    {% if object.status == 'SUBMITTED' and perms.inventory.approve_mrs %}
                        <a href="{% url 'inventory:mrs_approve' object.pk %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Approve
                        </a>
                    {% endif %}

                    {% if object.status == 'APPROVED' and perms.inventory.create_min %}
                        <a href="{% url 'inventory:min_create' %}?mrs_id={{ object.id }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                            </svg>
                            Create Issue Note
                        </a>
                    {% endif %}

                    <a href="{% url 'inventory:mrs_print' object.pk %}" target="_blank" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                        </svg>
                        Print
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            <div class="space-y-6">
                
                <!-- MRS Header Information -->
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Requisition Details</h3>
                        
                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">MRS Number</dt>
                                <dd class="mt-1 text-sm text-gray-900 font-semibold">{{ object.mrs_number }}</dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">MRS Date</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ object.mrs_date|date:"M d, Y" }}</dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Required Date</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ object.required_date|date:"M d, Y" }}</dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Requisition Type</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ object.get_requisition_type_display }}</dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Priority</dt>
                                <dd class="mt-1">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if object.priority == 'URGENT' %}bg-red-100 text-red-800
                                        {% elif object.priority == 'HIGH' %}bg-orange-100 text-orange-800
                                        {% elif object.priority == 'NORMAL' %}bg-blue-100 text-blue-800
                                        {% else %}bg-gray-100 text-gray-800
                                        {% endif %}">
                                        {{ object.get_priority_display }}
                                    </span>
                                </dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Status</dt>
                                <dd class="mt-1">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if object.status == 'DRAFT' %}bg-gray-100 text-gray-800
                                        {% elif object.status == 'SUBMITTED' %}bg-yellow-100 text-yellow-800
                                        {% elif object.status == 'APPROVED' %}bg-green-100 text-green-800
                                        {% elif object.status == 'PARTIALLY_ISSUED' %}bg-blue-100 text-blue-800
                                        {% elif object.status == 'ISSUED' %}bg-purple-100 text-purple-800
                                        {% elif object.status == 'CANCELLED' %}bg-red-100 text-red-800
                                        {% endif %}">
                                        {{ object.get_status_display }}
                                    </span>
                                </dd>
                            </div>
                            
                            {% if object.department_id %}
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Department ID</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ object.department_id }}</dd>
                            </div>
                            {% endif %}
                            
                            {% if object.work_order_number %}
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Work Order Number</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ object.work_order_number }}</dd>
                            </div>
                            {% endif %}
                            
                            {% if object.project_code %}
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Project Code</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ object.project_code }}</dd>
                            </div>
                            {% endif %}
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Requested By</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ object.requested_by.get_full_name|default:object.requested_by.username }}</dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Created Date</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ object.created_date|date:"M d, Y g:i A" }}</dd>
                            </div>
                            
                            {% if object.approved_by %}
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Approved By</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ object.approved_by.get_full_name|default:object.approved_by.username }}</dd>
                            </div>
                            {% endif %}
                        </div>
                        
                        {% if object.remarks %}
                        <div class="mt-6">
                            <dt class="text-sm font-medium text-gray-500">Remarks</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ object.remarks }}</dd>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Line Items -->
                <div class="bg-white shadow overflow-hidden sm:rounded-md">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">
                                Requested Items ({{ object.line_items.count }})
                            </h3>
                            {% if object.status == 'DRAFT' and user == object.created_by %}
                                <a href="{% url 'inventory:mrs_line_item_create' object.pk %}" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                    </svg>
                                    Add Item
                                </a>
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if object.line_items.exists %}
                        <div class="overflow-x-auto">
                            <table class="w-full table-auto divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requested Qty</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Available Stock</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issued Qty</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pending Qty</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Purpose</th>
                                        {% if object.status == 'DRAFT' and user == object.created_by %}
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                        {% endif %}
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    {% for line_item in object.line_items.all %}
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {{ line_item.item_code }}
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-900">
                                            {{ line_item.item_description }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ line_item.unit_of_measure }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ line_item.requested_quantity }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <span class="{% if line_item.available_stock < line_item.requested_quantity %}text-red-600{% else %}text-green-600{% endif %}">
                                                {{ line_item.available_stock|default:"0" }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ line_item.issued_quantity|default:"0" }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ line_item.pending_quantity|default:line_item.requested_quantity }}
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-500">
                                            {{ line_item.purpose|default:"-" }}
                                        </td>
                                        {% if object.status == 'DRAFT' and user == object.created_by %}
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <a href="#" class="text-blue-600 hover:text-blue-900">Edit</a>
                                                <a href="#" class="text-red-600 hover:text-red-900">Delete</a>
                                            </div>
                                        </td>
                                        {% endif %}
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="px-4 py-12 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-6m-4 0H4" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No items requested</h3>
                            <p class="mt-1 text-sm text-gray-500">Get started by adding items to this requisition.</p>
                            {% if object.status == 'DRAFT' and user == object.created_by %}
                                <div class="mt-6">
                                    <a href="{% url 'inventory:mrs_line_item_create' object.pk %}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                        </svg>
                                        Add First Item
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>

                <!-- Approval History -->
                {% if object.status != 'DRAFT' %}
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Approval History</h3>
                        
                        <div class="flow-root">
                            <ul role="list" class="-mb-8">
                                <li>
                                    <div class="relative pb-8">
                                        <span class="absolute top-5 left-5 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                        <div class="relative flex items-start space-x-3">
                                            <div class="relative">
                                                <div class="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="min-w-0 flex-1">
                                                <div>
                                                    <div class="text-sm">
                                                        <span class="font-medium text-gray-900">{{ object.created_by.get_full_name|default:object.created_by.username }}</span>
                                                    </div>
                                                    <p class="mt-0.5 text-sm text-gray-500">Created MRS</p>
                                                </div>
                                                <div class="mt-2 text-sm text-gray-700">
                                                    <p>{{ object.created_date|date:"M d, Y g:i A" }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                
                                {% if object.status != 'DRAFT' %}
                                <li>
                                    <div class="relative pb-8">
                                        {% if object.approved_by %}
                                        <span class="absolute top-5 left-5 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                        {% endif %}
                                        <div class="relative flex items-start space-x-3">
                                            <div class="relative">
                                                <div class="h-10 w-10 rounded-full bg-yellow-500 flex items-center justify-center ring-8 ring-white">
                                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="min-w-0 flex-1">
                                                <div>
                                                    <div class="text-sm">
                                                        <span class="font-medium text-gray-900">{{ object.requested_by.get_full_name|default:object.requested_by.username }}</span>
                                                    </div>
                                                    <p class="mt-0.5 text-sm text-gray-500">Submitted for approval</p>
                                                </div>
                                                <div class="mt-2 text-sm text-gray-700">
                                                    <p>{{ object.requested_date|date:"M d, Y g:i A" }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                {% endif %}
                                
                                {% if object.approved_by %}
                                <li>
                                    <div class="relative">
                                        <div class="relative flex items-start space-x-3">
                                            <div class="relative">
                                                <div class="h-10 w-10 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="min-w-0 flex-1">
                                                <div>
                                                    <div class="text-sm">
                                                        <span class="font-medium text-gray-900">{{ object.approved_by.get_full_name|default:object.approved_by.username }}</span>
                                                    </div>
                                                    <p class="mt-0.5 text-sm text-gray-500">Approved requisition</p>
                                                </div>
                                                <div class="mt-2 text-sm text-gray-700">
                                                    <p>{{ object.approved_date|date:"M d, Y g:i A" }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Related Transactions -->
                {% if object.status != 'DRAFT' %}
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Related Transactions</h3>
                        
                        <!-- Material Issue Notes -->
                        {% if object.material_issue_notes.exists %}
                        <div class="mb-6">
                            <h4 class="text-sm font-medium text-gray-900 mb-2">Material Issue Notes</h4>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="space-y-2">
                                    {% for min in object.material_issue_notes.all %}
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <a href="{% url 'inventory:min_detail' min.pk %}" class="text-blue-600 hover:text-blue-900 font-medium">{{ min.min_number }}</a>
                                            <span class="text-sm text-gray-500 ml-2">{{ min.issue_date|date:"M d, Y" }}</span>
                                        </div>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            {% if min.status == 'ISSUED' %}bg-green-100 text-green-800
                                            {% else %}bg-yellow-100 text-yellow-800
                                            {% endif %}">
                                            {{ min.get_status_display }}
                                        </span>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        {% if not object.material_issue_notes.exists and object.status == 'APPROVED' %}
                        <div class="text-center py-4">
                            <p class="text-sm text-gray-500">No material has been issued against this requisition yet.</p>
                            {% if perms.inventory.create_min %}
                            <div class="mt-3">
                                <a href="{% url 'inventory:min_create' %}?mrs_id={{ object.id }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                    Create Issue Note
                                </a>
                            </div>
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}