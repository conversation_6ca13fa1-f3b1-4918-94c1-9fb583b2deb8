{% extends "core/base.html" %}
{% load static %}

{% block title %}Start WIS Dry Run - Inventory Management{% endblock %}

{% block content %}
<div class="sap-page">
    <!-- Page Header -->
    <div class="sap-page-header">
        <div class="sap-page-header-content">
            <div class="sap-page-title">
                <h1>Start WIS Dry Run</h1>
                <p>Initialize material issue simulation for work order processing</p>
            </div>
            <div class="sap-page-actions">
                <a href="{% url 'inventory:wis_dry_run_list' %}" class="sap-button sap-button--transparent">
                    Cancel
                </a>
            </div>
        </div>
    </div>

    <!-- Form Content -->
    <div class="sap-panel">
        <div class="sap-panel-header">
            <h3 class="sap-panel-title">Dry Run Configuration</h3>
        </div>
        <div class="sap-panel-content">
            <form method="post" class="sap-form">
                {% csrf_token %}
                
                <!-- Form Messages -->
                {% if form.errors %}
                    <div class="sap-message-strip sap-message-strip--error mb-4">
                        <div class="sap-message-strip-text">
                            Please correct the errors below.
                        </div>
                    </div>
                {% endif %}

                <div class="sap-form-row">
                    <div class="sap-form-group">
                        <label class="sap-form-label required">{{ form.work_order_number.label }}</label>
                        {{ form.work_order_number }}
                        {% if form.work_order_number.errors %}
                            <div class="sap-form-element-state sap-form-element-state--error">
                                {{ form.work_order_number.errors.0 }}
                            </div>
                        {% endif %}
                        <div class="sap-form-element-information">
                            Enter the work order number for which to run the dry simulation
                        </div>
                    </div>
                    <div class="sap-form-group">
                        <label class="sap-form-label">{{ form.dry_run_number.label }}</label>
                        {{ form.dry_run_number }}
                        {% if form.dry_run_number.errors %}
                            <div class="sap-form-element-state sap-form-element-state--error">
                                {{ form.dry_run_number.errors.0 }}
                            </div>
                        {% endif %}
                        <div class="sap-form-element-information">
                            Auto-generated if left blank
                        </div>
                    </div>
                </div>

                <div class="sap-form-row">
                    <div class="sap-form-group">
                        <label class="sap-form-label">{{ form.simulation_type.label }}</label>
                        {{ form.simulation_type }}
                        {% if form.simulation_type.errors %}
                            <div class="sap-form-element-state sap-form-element-state--error">
                                {{ form.simulation_type.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    <div class="sap-form-group">
                        <label class="sap-form-label">{{ form.simulation_status.label }}</label>
                        {{ form.simulation_status }}
                        {% if form.simulation_status.errors %}
                            <div class="sap-form-element-state sap-form-element-state--error">
                                {{ form.simulation_status.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div class="sap-form-row">
                    <div class="sap-form-group full-width">
                        <label class="sap-form-label">{{ form.remarks.label }}</label>
                        {{ form.remarks }}
                        {% if form.remarks.errors %}
                            <div class="sap-form-element-state sap-form-element-state--error">
                                {{ form.remarks.errors.0 }}
                            </div>
                        {% endif %}
                        <div class="sap-form-element-information">
                            Optional remarks about this dry run simulation
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="sap-form-actions">
                    <button type="submit" class="sap-button sap-button--emphasized">
                        Start Dry Run
                    </button>
                    <a href="{% url 'inventory:wis_dry_run_list' %}" class="sap-button sap-button--transparent">
                        Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on first form field
    const firstInput = document.querySelector('input[type="text"], select');
    if (firstInput) {
        firstInput.focus();
    }
    
    // Form validation feedback
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.textContent = 'Starting...';
                submitBtn.disabled = true;
            }
        });
    }
});
</script>
{% endblock %}