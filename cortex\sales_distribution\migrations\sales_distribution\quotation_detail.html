{% extends "core/base.html" %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{{ page_title }}</h1>
                        <p class="text-gray-600 mt-1">View quotation details and line items</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <!-- Status Badge -->
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ status_display.class }}">
                            {{ status_display.status }}
                        </span>
                        
                        <div class="flex space-x-3">
                            <a href="{% url 'sales_distribution:quotation_list' %}" 
                               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                                </svg>
                                Back to List
                            </a>
                            
                            {% if quotation.authorize != 1 %}
                                <a href="{% url 'sales_distribution:quotation_edit' quotation.id %}" 
                                   class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                    Edit Quotation
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quotation Overview -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-t-lg">
                <h3 class="text-lg font-medium">Quotation Overview</h3>
            </div>
            <div class="px-6 py-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Quotation Number</label>
                        <p class="text-lg font-semibold text-gray-900">{{ quotation.quotationno }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Customer ID</label>
                        <p class="text-gray-900">{{ quotation.customerid|default:"—" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Enquiry Reference</label>
                        <p class="text-gray-900">
                            <a href="{% url 'sales_distribution:enquiry_detail' quotation.enqid.enqid %}" 
                               class="text-blue-600 hover:text-blue-700">
                                Enquiry #{{ quotation.enqid.enqid }}
                            </a>
                        </p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Date Created</label>
                        <p class="text-gray-900">{{ quotation.sysdate|default:"—" }}</p>
                        {% if quotation.systime %}
                            <p class="text-xs text-gray-500">{{ quotation.systime }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Information -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Customer Information</h3>
            </div>
            <div class="px-6 py-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Customer Name</label>
                        <p class="text-gray-900">{{ quotation.enqid.customername|default:"—" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Contact Person</label>
                        <p class="text-gray-900">{{ quotation.enqid.contactperson|default:"—" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <p class="text-gray-900">{{ quotation.enqid.email|default:"—" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Contact Number</label>
                        <p class="text-gray-900">{{ quotation.enqid.contactno|default:"—" }}</p>
                    </div>
                </div>
                
                {% if quotation.enqid.regdaddress %}
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                        <p class="text-gray-900">{{ quotation.enqid.regdaddress }}</p>
                        {% if quotation.enqid.regdcity or quotation.enqid.regdstate or quotation.enqid.regdcountry %}
                            <p class="text-gray-600 text-sm">
                                {% if quotation.enqid.regdcity %}{{ quotation.enqid.regdcity.cityname }}{% endif %}{% if quotation.enqid.regdstate %}, {{ quotation.enqid.regdstate.statename }}{% endif %}{% if quotation.enqid.regdcountry %}, {{ quotation.enqid.regdcountry.countryname }}{% endif %}
                            </p>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Line Items -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Line Items</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Item Description
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Quantity
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Unit
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Rate
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Discount
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Amount
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for detail in quotation_details %}
                            <tr>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900">{{ detail.itemdesc }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ detail.totalqty|floatformat:2 }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ detail.unit.unitname }} ({{ detail.unit.symbol }})</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">₹{{ detail.rate|floatformat:2 }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">₹{{ detail.discount|default:0|floatformat:2 }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right">
                                    <div class="text-sm font-medium text-gray-900">₹{{ detail.line_amount|floatformat:2 }}</div>
                                </td>
                            </tr>
                        {% empty %}
                            <tr>
                                <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                                    No line items found for this quotation.
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                    {% if quotation_details %}
                        <tfoot class="bg-gray-50">
                            <tr>
                                <td colspan="5" class="px-6 py-4 text-right text-sm font-medium text-gray-900">
                                    Subtotal:
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-bold text-gray-900">
                                    ₹{{ line_total|floatformat:2 }}
                                </td>
                            </tr>
                        </tfoot>
                    {% endif %}
                </table>
            </div>
        </div>

        <!-- Terms and Financial Details -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- Terms and Conditions -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Terms & Conditions</h3>
                </div>
                <div class="px-6 py-6 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Payment Terms</label>
                        <p class="text-gray-900">{{ quotation.paymentterms|default:"—" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Delivery Terms</label>
                        <p class="text-gray-900">{{ quotation.deliveryterms|default:"—" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Validity</label>
                        <p class="text-gray-900">{{ quotation.validity|default:"—" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Warranty</label>
                        <p class="text-gray-900">{{ quotation.warrenty|default:"—" }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Transport</label>
                        <p class="text-gray-900">{{ quotation.transport|default:"—" }}</p>
                    </div>
                    {% if quotation.duedate %}
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Due Date</label>
                            <p class="text-gray-900">{{ quotation.duedate }}</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Financial Details -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Financial Details</h3>
                </div>
                <div class="px-6 py-6 space-y-3">
                    {% if quotation.pf %}
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-700">PF {% if quotation.pftype == 1 %}(%){% endif %}</span>
                            <span class="text-sm font-medium text-gray-900">{{ quotation.pf|floatformat:2 }}</span>
                        </div>
                    {% endif %}
                    
                    {% if quotation.vatcst %}
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-700">VAT/CST (%)</span>
                            <span class="text-sm font-medium text-gray-900">{{ quotation.vatcst }}%</span>
                        </div>
                    {% endif %}
                    
                    {% if quotation.excise %}
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-700">Excise (%)</span>
                            <span class="text-sm font-medium text-gray-900">{{ quotation.excise }}%</span>
                        </div>
                    {% endif %}
                    
                    {% if quotation.octroi %}
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-700">Octroi {% if quotation.octroitype == 1 %}(%){% endif %}</span>
                            <span class="text-sm font-medium text-gray-900">{{ quotation.octroi|floatformat:2 }}</span>
                        </div>
                    {% endif %}
                    
                    {% if quotation.insurance %}
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-700">Insurance</span>
                            <span class="text-sm font-medium text-gray-900">₹{{ quotation.insurance|floatformat:2 }}</span>
                        </div>
                    {% endif %}
                    
                    {% if quotation.freight %}
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-700">Freight {% if quotation.freighttype == 1 %}(%){% endif %}</span>
                            <span class="text-sm font-medium text-gray-900">{{ quotation.freight|floatformat:2 }}</span>
                        </div>
                    {% endif %}
                    
                    {% if quotation.othercharges %}
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-700">Other Charges {% if quotation.otherchargestype == 1 %}(%){% endif %}</span>
                            <span class="text-sm font-medium text-gray-900">{{ quotation.othercharges|floatformat:2 }}</span>
                        </div>
                    {% endif %}
                    
                    {% if quotation.noteno %}
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-700">Note Number</span>
                            <span class="text-sm font-medium text-gray-900">{{ quotation.noteno }}</span>
                        </div>
                    {% endif %}
                    
                    {% if quotation.registrationno %}
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-700">Registration Number</span>
                            <span class="text-sm font-medium text-gray-900">{{ quotation.registrationno }}</span>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Approval Status -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Approval Status</h3>
            </div>
            <div class="px-6 py-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Check Status -->
                    <div class="flex items-center space-x-3">
                        {% if quotation.checked == 1 %}
                            <div class="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">Checked</p>
                                {% if quotation.checkedby %}
                                    <p class="text-xs text-gray-500">By: {{ quotation.checkedby }}</p>
                                {% endif %}
                                {% if quotation.checkeddate %}
                                    <p class="text-xs text-gray-500">{{ quotation.checkeddate }}{% if quotation.checkedtime %} {{ quotation.checkedtime }}{% endif %}</p>
                                {% endif %}
                            </div>
                        {% else %}
                            <div class="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">Pending Check</p>
                            </div>
                        {% endif %}
                    </div>

                    <!-- Approval Status -->
                    <div class="flex items-center space-x-3">
                        {% if quotation.approve == 1 %}
                            <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">Approved</p>
                                {% if quotation.approvedby %}
                                    <p class="text-xs text-gray-500">By: {{ quotation.approvedby }}</p>
                                {% endif %}
                                {% if quotation.approvedate %}
                                    <p class="text-xs text-gray-500">{{ quotation.approvedate }}{% if quotation.approvetime %} {{ quotation.approvetime }}{% endif %}</p>
                                {% endif %}
                            </div>
                        {% else %}
                            <div class="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">Pending Approval</p>
                            </div>
                        {% endif %}
                    </div>

                    <!-- Authorization Status -->
                    <div class="flex items-center space-x-3">
                        {% if quotation.authorize == 1 %}
                            <div class="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">Authorized</p>
                                {% if quotation.authorizedby %}
                                    <p class="text-xs text-gray-500">By: {{ quotation.authorizedby }}</p>
                                {% endif %}
                                {% if quotation.authorizedate %}
                                    <p class="text-xs text-gray-500">{{ quotation.authorizedate }}{% if quotation.authorizetime %} {{ quotation.authorizetime }}{% endif %}</p>
                                {% endif %}
                            </div>
                        {% else %}
                            <div class="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">Pending Authorization</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Remarks -->
        {% if quotation.remarks %}
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Remarks</h3>
                </div>
                <div class="px-6 py-6">
                    <p class="text-gray-900">{{ quotation.remarks }}</p>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}