{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }} - Sales Distribution{% endblock %}

{% block extra_css %}
<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/umd/lucide.min.css">
<script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-sap-gray-50 font-sap">
    <!-- SAP Fiori Header -->
    <div class="bg-white shadow-sm border-b border-sap-gray-200">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-orange-600 rounded-lg flex items-center justify-center">
                            <i data-lucide="toggle-left" class="w-5 h-5 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-xl font-semibold text-sap-gray-900">{{ page_title }}</h1>
                            <p class="text-sm text-sap-gray-600">Manage work order status - open or close as needed</p>
                        </div>
                    </div>
                </div>
                <nav class="flex items-center space-x-2 text-sm text-sap-gray-500">
                    <a href="{% url 'sales_distribution:dashboard' %}" class="hover:text-sap-blue-600 transition-colors">Sales & Distribution</a>
                    <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    <span class="text-sap-gray-900 font-medium">Work Order Status Management</span>
                </nav>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-6 py-8">
        <!-- Work Orders Status Management Card -->
        <div class="sap-card">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-orange-100 rounded-xl flex items-center justify-center">
                        <i data-lucide="activity" class="w-5 h-5 text-orange-600"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-semibold text-sap-gray-900">Work Order Status Management</h2>
                        <p class="text-sm text-sap-gray-600">{{ work_orders|length }} work order{{ work_orders|length|pluralize }} available for status management</p>
                    </div>
                </div>
                {% if work_orders %}
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2 text-sm text-sap-gray-500">
                        <i data-lucide="layers" class="w-4 h-4"></i>
                        <span>{{ work_orders|length }} item{{ work_orders|length|pluralize }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button class="inline-flex items-center px-4 py-2 text-sm font-medium text-sap-blue-600 bg-sap-blue-50 border border-sap-blue-200 rounded-lg hover:bg-sap-blue-100 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 transition-all duration-200">
                            <i data-lucide="filter" class="w-4 h-4 mr-2"></i>
                            Filter Status
                        </button>
                        <button class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-orange-600 border border-transparent rounded-lg hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200">
                            <i data-lucide="toggle-right" class="w-4 h-4 mr-2"></i>
                            Bulk Actions
                        </button>
                    </div>
                </div>
                {% endif %}
            </div>

        {% if work_orders %}
            <div class="overflow-hidden rounded-xl border border-sap-gray-200">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-sap-gray-200">
                        <thead class="bg-sap-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                    <div class="flex items-center space-x-2">
                                        <input type="checkbox" class="rounded text-sap-blue-600 focus:ring-sap-blue-500">
                                        <span>Select</span>
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                    <div class="flex items-center space-x-2">
                                        <i data-lucide="hash" class="w-4 h-4"></i>
                                        <span>WO Number</span>
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                    <div class="flex items-center space-x-2">
                                        <i data-lucide="building-2" class="w-4 h-4"></i>
                                        <span>Customer</span>
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                    <div class="flex items-center space-x-2">
                                        <i data-lucide="briefcase" class="w-4 h-4"></i>
                                        <span>Project Title</span>
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                    <div class="flex items-center space-x-2">
                                        <i data-lucide="calendar" class="w-4 h-4"></i>
                                        <span>Target Date</span>
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                    <div class="flex items-center space-x-2">
                                        <i data-lucide="toggle-left" class="w-4 h-4"></i>
                                        <span>Status</span>
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                    <div class="flex items-center space-x-2">
                                        <i data-lucide="settings" class="w-4 h-4"></i>
                                        <span>Actions</span>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-sap-gray-200">
                            {% for work_order in work_orders %}
                            <tr class="hover:bg-sap-gray-50 transition-colors duration-150 group">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="checkbox" class="rounded text-sap-blue-600 focus:ring-sap-blue-500">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 w-8 h-8 bg-sap-blue-100 rounded-lg flex items-center justify-center mr-3">
                                            <i data-lucide="file-text" class="w-4 h-4 text-sap-blue-600"></i>
                                        </div>
                                        <div class="text-sm font-semibold text-sap-blue-600 group-hover:text-sap-blue-700">
                                            {{ work_order.wono|default:"-" }}
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 w-6 h-6 bg-sap-gray-200 rounded-full flex items-center justify-center mr-2">
                                            <i data-lucide="user" class="w-3 h-3 text-sap-gray-600"></i>
                                        </div>
                                        <div class="text-sm font-medium text-sap-gray-900">{{ work_order.customerid }}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-sap-gray-600">{{ work_order.taskprojecttitle|default:"-" }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-sap-gray-600">{{ work_order.tasktargetdespach_tdate|default:"-" }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if work_order.status == 'closed' %}
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i data-lucide="lock" class="w-3 h-3 mr-1"></i>
                                            Closed
                                        </span>
                                    {% elif work_order.status == 'inactive' %}
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            <i data-lucide="pause" class="w-3 h-3 mr-1"></i>
                                            Inactive
                                        </span>
                                    {% else %}
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i data-lucide="play" class="w-3 h-3 mr-1"></i>
                                            Active
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center space-x-2">
                                        {% if work_order.status == 'closed' or work_order.status == 'inactive' %}
                                            <a href="{% url 'sales_distribution:work_order_open_close' work_order.pk %}?action=open" 
                                               onclick="return confirm('Are you sure you want to activate this work order?')"
                                               class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 transform hover:scale-105">
                                                <i data-lucide="unlock" class="w-4 h-4 mr-1"></i>
                                                Open
                                            </a>
                                        {% else %}
                                            <a href="{% url 'sales_distribution:work_order_open_close' work_order.pk %}?action=close" 
                                               onclick="return confirm('Are you sure you want to close this work order? This action will suspend all activities.')"
                                               class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200 transform hover:scale-105">
                                                <i data-lucide="lock" class="w-4 h-4 mr-1"></i>
                                                Close
                                            </a>
                                        {% endif %}
                                        <a href="{% url 'sales_distribution:work_order_open_close' work_order.pk %}" 
                                           class="inline-flex items-center px-3 py-2 text-sm font-medium text-sap-blue-600 bg-sap-blue-50 border border-sap-blue-200 rounded-lg hover:bg-sap-blue-100 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 transition-all duration-200">
                                            <i data-lucide="settings" class="w-4 h-4 mr-1"></i>
                                            Manage
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        {% else %}
            <div class="text-center py-16">
                <div class="w-20 h-20 bg-sap-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="toggle-left" class="w-10 h-10 text-sap-gray-400"></i>
                </div>
                <h3 class="text-lg font-semibold text-sap-gray-900 mb-2">No Work Orders Available</h3>
                <p class="text-sap-gray-600 max-w-md mx-auto mb-6">
                    No work orders are currently available for status management. All work orders may already be in their desired state.
                </p>
                <a href="{% url 'sales_distribution:work_order_list' %}" 
                   class="inline-flex items-center px-6 py-3 text-sm font-medium text-sap-blue-600 bg-sap-blue-50 border border-sap-blue-200 rounded-lg hover:bg-sap-blue-100 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 transition-all duration-200">
                    <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                    View All Work Orders
                </a>
            </div>
        {% endif %}
        </div>
    </div>
</div>

<script>
// Initialize Lucide icons
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}