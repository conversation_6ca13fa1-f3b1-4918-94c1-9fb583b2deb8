﻿<?xml version="1.0" encoding="utf-8"?>
<Database Name="D:\INETPUB\WWWROOT\NEWERP\APP_DATA\ERP_DB.MDF" Class="TimeBudget_DeptDataContext" xmlns="http://schemas.microsoft.com/linqtosql/dbml/2007">
  <Connection Mode="WebSettings" ConnectionString="Data Source=PC103;Initial Catalog=D:\INETPUB\WWWROOT\NEWERP\APP_DATA\ERP_DB.MDF;Integrated Security=True" SettingsObjectName="System.Configuration.ConfigurationManager.ConnectionStrings" SettingsPropertyName="D__INETPUB_WWWROOT_NEWERP_APP_DATA_ERP_DB_MDFConnectionString" Provider="System.Data.SqlClient" />
  <Table Name="dbo.tblACC_Budget_Dept_Time" Member="tblACC_Budget_Dept_Times">
    <Type Name="tblACC_Budget_Dept_Time">
      <Column Name="Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="SysDate" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="SysTime" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="CompId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="FinYearId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SessionId" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="BGGroup" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="BudgetCodeId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Hour" Type="System.Double" DbType="Float" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.tblACC_Budget_WO_Time" Member="tblACC_Budget_WO_Times">
    <Type Name="tblACC_Budget_WO_Time">
      <Column Name="Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="SysDate" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="SysTime" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="CompId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="FinYearId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SessionId" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="WONo" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="Hour" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="BudgetCodeId" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
</Database>