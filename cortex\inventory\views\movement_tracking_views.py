from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.urls import reverse_lazy, reverse
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.http import JsonResponse
from django.db.models import Q, Count, Sum, Avg, F, Max
from django.utils import timezone
from django.db import transaction
from datetime import timedelta

from ..models import (
    InventoryMovementMaster, StockLedger, InventorySnapshot
)
from ..forms.movement_tracking_forms import (
    InventoryMovementMasterForm, StockLedgerForm, InventorySnapshotForm, MovementTrackingSearchForm,
    StockLedgerSearchForm
)


# =============================================================================
# Inventory Movement Master Views
# =============================================================================

class InventoryMovementMasterListView(LoginRequiredMixin, ListView):
    """List view for inventory movement master records"""
    model = InventoryMovementMaster
    template_name = 'inventory/movement_master_list.html'
    context_object_name = 'movements'
    paginate_by = 20

    def get_queryset(self):
        queryset = InventoryMovementMaster.objects.filter(
            company=self.request.session.get('company')
        ).select_related('created_by', 'authorized_by', 'processed_by').order_by('-movement_date', '-id')
        
        # Apply search and filters
        form = MovementTrackingSearchForm(self.request.GET)
        if form.is_valid():
            search = form.cleaned_data.get('search')
            movement_type = form.cleaned_data.get('movement_type')
            movement_status = form.cleaned_data.get('movement_status')
            location_code = form.cleaned_data.get('location_code')
            date_from = form.cleaned_data.get('date_from')
            date_to = form.cleaned_data.get('date_to')
            
            if search:
                queryset = queryset.filter(
                    Q(movement_number__icontains=search) |
                    Q(source_document_number__icontains=search) |
                    Q(work_order_number__icontains=search) |
                    Q(remarks__icontains=search)
                )
            
            if movement_type:
                queryset = queryset.filter(movement_type=movement_type)
            
            if movement_status:
                queryset = queryset.filter(movement_status=movement_status)
            
            if location_code:
                queryset = queryset.filter(
                    Q(from_location_code__icontains=location_code) |
                    Q(to_location_code__icontains=location_code)
                )
            
            if date_from:
                queryset = queryset.filter(movement_date__gte=date_from)
            
            if date_to:
                queryset = queryset.filter(movement_date__lte=date_to)
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = MovementTrackingSearchForm(self.request.GET)
        
        # Statistics
        company = self.request.session.get('company')
        context['stats'] = {
            'total': InventoryMovementMaster.objects.filter(company=company).count(),
            'pending': InventoryMovementMaster.objects.filter(company=company, movement_status='PENDING').count(),
            'in_progress': InventoryMovementMaster.objects.filter(company=company, movement_status='IN_PROGRESS').count(),
            'completed': InventoryMovementMaster.objects.filter(company=company, movement_status='COMPLETED').count(),
            'inward': InventoryMovementMaster.objects.filter(company=company, movement_type='INWARD').count(),
            'outward': InventoryMovementMaster.objects.filter(company=company, movement_type='OUTWARD').count(),
            'internal': InventoryMovementMaster.objects.filter(company=company, movement_type='INTERNAL').count(),
        }
        return context

    def render_to_response(self, context, **response_kwargs):
        if self.request.headers.get('HX-Request'):
            return render(self.request, 'inventory/partials/movement_master_list_partial.html', context)
        return super().render_to_response(context, **response_kwargs)


class InventoryMovementMasterDetailView(LoginRequiredMixin, DetailView):
    """Detail view for inventory movement master"""
    model = InventoryMovementMaster
    template_name = 'inventory/movement_master_detail.html'
    context_object_name = 'movement'

    def get_queryset(self):
        return InventoryMovementMaster.objects.filter(
            company=self.request.session.get('company')
        ).select_related('created_by', 'authorized_by', 'processed_by').prefetch_related('movement_details')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['can_edit'] = self.object.movement_status in ['PENDING']
        context['can_authorize'] = (
            self.object.movement_status == 'PENDING' and 
            self.object.requires_approval and 
            not self.object.authorized_by
        )
        context['can_process'] = (
            self.object.movement_status in ['PENDING'] and
            (not self.object.requires_approval or self.object.authorized_by)
        )
        context['can_complete'] = self.object.movement_status == 'IN_PROGRESS'
        context['can_cancel'] = self.object.movement_status in ['PENDING', 'IN_PROGRESS']
        
        # Movement details
        context['movement_details'] = self.object.movement_details.all().order_by('line_number')
        
        # Statistics
        details = context['movement_details']
        context['detail_stats'] = {
            'total_lines': details.count(),
            'completed_lines': details.filter(line_status='COMPLETED').count(),
            'pending_lines': details.filter(line_status='PENDING').count(),
            'total_planned_qty': details.aggregate(total=Sum('planned_quantity'))['total'] or 0,
            'total_actual_qty': details.aggregate(total=Sum('actual_quantity'))['total'] or 0,
            'total_variance_qty': details.aggregate(total=Sum('variance_quantity'))['total'] or 0,
        }
        
        return context


class InventoryMovementMasterCreateView(LoginRequiredMixin, CreateView):
    """Create view for inventory movement master"""
    model = InventoryMovementMaster
    form_class = InventoryMovementMasterForm
    template_name = 'inventory/movement_master_form.html'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        kwargs['company'] = self.request.session.get('company')
        kwargs['financial_year'] = self.request.session.get('financial_year')
        return kwargs

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f'Movement {self.object.movement_number} created successfully.')
        return response

    def get_success_url(self):
        return reverse('inventory:movement_master_detail', kwargs={'pk': self.object.pk})


class InventoryMovementMasterUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for inventory movement master"""
    model = InventoryMovementMaster
    form_class = InventoryMovementMasterForm
    template_name = 'inventory/movement_master_form.html'

    def get_queryset(self):
        return InventoryMovementMaster.objects.filter(
            company=self.request.session.get('company'),
            movement_status='PENDING'
        )

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        kwargs['company'] = self.request.session.get('company')
        kwargs['financial_year'] = self.request.session.get('financial_year')
        return kwargs

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f'Movement {self.object.movement_number} updated successfully.')
        return response

    def get_success_url(self):
        return reverse('inventory:movement_master_detail', kwargs={'pk': self.object.pk})


class InventoryMovementMasterDeleteView(LoginRequiredMixin, DeleteView):
    """Delete view for inventory movement master"""
    model = InventoryMovementMaster
    template_name = 'inventory/movement_master_confirm_delete.html'
    success_url = reverse_lazy('inventory:movement_master_list')

    def get_queryset(self):
        return InventoryMovementMaster.objects.filter(
            company=self.request.session.get('company'),
            movement_status='PENDING'
        )

    def delete(self, request, *args, **kwargs):
        movement = self.get_object()
        movement_number = movement.movement_number
        response = super().delete(request, *args, **kwargs)
        messages.success(request, f'Movement {movement_number} deleted successfully.')
        return response


# =============================================================================
# Stock Ledger Views
# =============================================================================

class StockLedgerListView(LoginRequiredMixin, ListView):
    """List view for stock ledger entries"""
    model = StockLedger
    template_name = 'inventory/stock_ledger_list.html'
    context_object_name = 'ledger_entries'
    paginate_by = 50

    def get_queryset(self):
        queryset = StockLedger.objects.filter(
            company=self.request.session.get('company')
        ).select_related('processed_by').order_by('-transaction_date', '-transaction_time', '-id')
        
        # Apply search and filters
        form = StockLedgerSearchForm(self.request.GET)
        if form.is_valid():
            search = form.cleaned_data.get('search')
            transaction_type = form.cleaned_data.get('transaction_type')
            location_code = form.cleaned_data.get('location_code')
            date_from = form.cleaned_data.get('date_from')
            date_to = form.cleaned_data.get('date_to')
            
            if search:
                queryset = queryset.filter(
                    Q(item_code__icontains=search) |
                    Q(transaction_id__icontains=search) |
                    Q(reference_document_number__icontains=search) |
                    Q(work_order_number__icontains=search)
                )
            
            if transaction_type:
                queryset = queryset.filter(transaction_type=transaction_type)
            
            if location_code:
                queryset = queryset.filter(location_code__icontains=location_code)
            
            if date_from:
                queryset = queryset.filter(transaction_date__gte=date_from)
            
            if date_to:
                queryset = queryset.filter(transaction_date__lte=date_to)
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = StockLedgerSearchForm(self.request.GET)
        
        # Statistics
        company = self.request.session.get('company')
        context['stats'] = {
            'total_transactions': StockLedger.objects.filter(company=company).count(),
            'receipts': StockLedger.objects.filter(company=company, transaction_type='RECEIPT').count(),
            'issues': StockLedger.objects.filter(company=company, transaction_type='ISSUE').count(),
            'transfers': StockLedger.objects.filter(company=company, transaction_type='TRANSFER').count(),
            'adjustments': StockLedger.objects.filter(company=company, transaction_type='ADJUSTMENT').count(),
        }
        return context

    def render_to_response(self, context, **response_kwargs):
        if self.request.headers.get('HX-Request'):
            return render(self.request, 'inventory/partials/stock_ledger_list_partial.html', context)
        return super().render_to_response(context, **response_kwargs)


class StockLedgerDetailView(LoginRequiredMixin, DetailView):
    """Detail view for stock ledger entry"""
    model = StockLedger
    template_name = 'inventory/stock_ledger_detail.html'
    context_object_name = 'ledger_entry'

    def get_queryset(self):
        return StockLedger.objects.filter(
            company=self.request.session.get('company')
        ).select_related('processed_by')


class StockLedgerCreateView(LoginRequiredMixin, CreateView):
    """Create view for manual stock ledger entry"""
    model = StockLedger
    form_class = StockLedgerForm
    template_name = 'inventory/stock_ledger_form.html'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        kwargs['company'] = self.request.session.get('company')
        kwargs['financial_year'] = self.request.session.get('financial_year')
        return kwargs

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f'Stock ledger entry {self.object.transaction_id} created successfully.')
        return response

    def get_success_url(self):
        return reverse('inventory:stock_ledger_detail', kwargs={'pk': self.object.pk})


# =============================================================================
# Inventory Snapshot Views
# =============================================================================

class InventorySnapshotListView(LoginRequiredMixin, ListView):
    """List view for inventory snapshots"""
    model = InventorySnapshot
    template_name = 'inventory/inventory_snapshot_list.html'
    context_object_name = 'snapshots'
    paginate_by = 20

    def get_queryset(self):
        return InventorySnapshot.objects.filter(
            company=self.request.session.get('company')
        ).select_related('created_by').order_by('-snapshot_date', '-snapshot_time', 'item_code')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Statistics
        company = self.request.session.get('company')
        latest_date = InventorySnapshot.objects.filter(company=company).aggregate(
            latest=Max('snapshot_date')
        )['latest']
        
        if latest_date:
            latest_snapshots = InventorySnapshot.objects.filter(
                company=company,
                snapshot_date=latest_date
            )
            
            context['latest_stats'] = {
                'snapshot_date': latest_date,
                'total_items': latest_snapshots.count(),
                'total_value': latest_snapshots.aggregate(total=Sum('total_value'))['total'] or 0,
                'items_below_min': latest_snapshots.filter(stock_status='BELOW_MIN').count(),
                'items_above_max': latest_snapshots.filter(stock_status='ABOVE_MAX').count(),
                'reorder_required': latest_snapshots.filter(
                    current_stock__lte=F('reorder_level')
                ).count(),
                'fast_moving': latest_snapshots.filter(movement_frequency='FAST').count(),
                'slow_moving': latest_snapshots.filter(movement_frequency='SLOW').count(),
                'dead_stock': latest_snapshots.filter(movement_frequency='DEAD').count(),
            }
        
        return context


class InventorySnapshotDetailView(LoginRequiredMixin, DetailView):
    """Detail view for inventory snapshot"""
    model = InventorySnapshot
    template_name = 'inventory/inventory_snapshot_detail.html'
    context_object_name = 'snapshot'

    def get_queryset(self):
        return InventorySnapshot.objects.filter(
            company=self.request.session.get('company')
        ).select_related('created_by')


class InventorySnapshotCreateView(LoginRequiredMixin, CreateView):
    """Create view for inventory snapshot"""
    model = InventorySnapshot
    form_class = InventorySnapshotForm
    template_name = 'inventory/inventory_snapshot_form.html'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        kwargs['company'] = self.request.session.get('company')
        kwargs['financial_year'] = self.request.session.get('financial_year')
        return kwargs

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Inventory snapshot created successfully.')
        return response

    def get_success_url(self):
        return reverse('inventory:inventory_snapshot_detail', kwargs={'pk': self.object.pk})


# =============================================================================
# Workflow Action Views
# =============================================================================

@login_required
def movement_authorize_view(request, pk):
    """Authorize movement"""
    movement = get_object_or_404(
        InventoryMovementMaster,
        pk=pk,
        company=request.session.get('company'),
        movement_status='PENDING',
        requires_approval=True,
        authorized_by__isnull=True
    )
    
    if request.method == 'POST':
        movement.authorized_by = request.user
        movement.authorized_date = timezone.now()
        movement.save()
        
        messages.success(request, f'Movement {movement.movement_number} authorized successfully.')
        
        if request.headers.get('HX-Request'):
            return render(request, 'inventory/partials/movement_status_updated.html', {
                'movement': movement,
                'message': f'Movement {movement.movement_number} authorized successfully.'
            })
        
        return redirect('inventory:movement_master_detail', pk=movement.pk)
    
    return render(request, 'inventory/movement_authorize_confirm.html', {
        'movement': movement
    })


@login_required
def movement_process_view(request, pk):
    """Start movement processing"""
    movement = get_object_or_404(
        InventoryMovementMaster,
        pk=pk,
        company=request.session.get('company'),
        movement_status='PENDING'
    )
    
    # Check if authorization is required
    if movement.requires_approval and not movement.authorized_by:
        messages.error(request, 'Movement must be authorized before processing.')
        return redirect('inventory:movement_master_detail', pk=movement.pk)
    
    if request.method == 'POST':
        with transaction.atomic():
            movement.movement_status = 'IN_PROGRESS'
            movement.processed_by = request.user
            movement.processed_date = timezone.now()
            movement.save()
            
            # Update line items to in progress
            movement.movement_details.filter(line_status='PENDING').update(
                line_status='IN_PROGRESS',
                movement_timestamp=timezone.now()
            )
        
        messages.success(request, f'Movement {movement.movement_number} processing started.')
        
        if request.headers.get('HX-Request'):
            return render(request, 'inventory/partials/movement_status_updated.html', {
                'movement': movement,
                'message': f'Movement {movement.movement_number} processing started.'
            })
        
        return redirect('inventory:movement_master_detail', pk=movement.pk)
    
    return render(request, 'inventory/movement_process_confirm.html', {
        'movement': movement
    })


@login_required
def movement_complete_view(request, pk):
    """Complete movement processing"""
    movement = get_object_or_404(
        InventoryMovementMaster,
        pk=pk,
        company=request.session.get('company'),
        movement_status='IN_PROGRESS'
    )
    
    if request.method == 'POST':
        with transaction.atomic():
            # Calculate totals
            details = movement.movement_details.all()
            total_quantity = details.aggregate(total=Sum('actual_quantity'))['total'] or 0
            total_value = details.aggregate(total=Sum('total_value'))['total'] or 0
            variance_quantity = details.aggregate(total=Sum('variance_quantity'))['total'] or 0
            
            movement.movement_status = 'COMPLETED'
            movement.total_quantity = total_quantity
            movement.total_value = total_value
            movement.variance_quantity = variance_quantity
            movement.save()
            
            # Complete all line items
            details.update(line_status='COMPLETED', movement_timestamp=timezone.now())
            
            # Create stock ledger entries for each line item
            for detail in details:
                create_stock_ledger_entry(movement, detail, request.user)
        
        messages.success(request, f'Movement {movement.movement_number} completed successfully.')
        
        if request.headers.get('HX-Request'):
            return render(request, 'inventory/partials/movement_status_updated.html', {
                'movement': movement,
                'message': f'Movement {movement.movement_number} completed successfully.'
            })
        
        return redirect('inventory:movement_master_detail', pk=movement.pk)
    
    return render(request, 'inventory/movement_complete_confirm.html', {
        'movement': movement
    })


@login_required
def movement_cancel_view(request, pk):
    """Cancel movement"""
    movement = get_object_or_404(
        InventoryMovementMaster,
        pk=pk,
        company=request.session.get('company'),
        movement_status__in=['PENDING', 'IN_PROGRESS']
    )
    
    if request.method == 'POST':
        cancel_reason = request.POST.get('cancel_reason', '')
        
        with transaction.atomic():
            movement.movement_status = 'CANCELLED'
            movement.error_log = f"Cancelled by {request.user.username}: {cancel_reason}"
            movement.save()
            
            # Cancel all line items
            movement.movement_details.update(line_status='CANCELLED')
        
        messages.success(request, f'Movement {movement.movement_number} cancelled successfully.')
        
        if request.headers.get('HX-Request'):
            return render(request, 'inventory/partials/movement_status_updated.html', {
                'movement': movement,
                'message': f'Movement {movement.movement_number} cancelled successfully.'
            })
        
        return redirect('inventory:movement_master_detail', pk=movement.pk)
    
    return render(request, 'inventory/movement_cancel_confirm.html', {
        'movement': movement
    })


def create_stock_ledger_entry(movement, detail, user):
    """Create stock ledger entry for movement detail"""
    # Determine transaction type based on movement type
    transaction_type_map = {
        'INWARD': 'RECEIPT',
        'OUTWARD': 'ISSUE',
        'INTERNAL': 'TRANSFER',
        'ADJUSTMENT': 'ADJUSTMENT',
        'PRODUCTION': 'PRODUCTION',
        'SCRAP': 'SCRAP'
    }
    
    transaction_type = transaction_type_map.get(movement.movement_type, 'TRANSFER')
    
    # Create the stock ledger entry
    stock_entry = StockLedger.objects.create(
        company=movement.company,
        financial_year=movement.financial_year,
        transaction_date=movement.movement_date,
        transaction_time=timezone.now().time(),
        item_id=detail.item_id,
        item_code=detail.item_code,
        item_description=detail.item_description,
        location_code=detail.to_location_code or detail.from_location_code,
        warehouse_code=movement.to_warehouse or movement.from_warehouse,
        batch_number=detail.to_batch_number or detail.from_batch_number,
        lot_number=detail.to_lot_number or detail.from_lot_number,
        transaction_type=transaction_type,
        reference_document_type=movement.source_document_type or 'MOVEMENT',
        reference_document_number=movement.movement_number,
        reference_line_number=detail.line_number,
        work_order_number=movement.work_order_number,
        cost_center_code=movement.cost_center_code,
        quantity_in=detail.actual_quantity if movement.movement_type == 'INWARD' else 0,
        quantity_out=detail.actual_quantity if movement.movement_type == 'OUTWARD' else 0,
        unit_rate=detail.unit_rate,
        value_in=detail.total_value if movement.movement_type == 'INWARD' else 0,
        value_out=detail.total_value if movement.movement_type == 'OUTWARD' else 0,
        unit_of_measure=detail.unit_of_measure,
        movement_master_id=movement.id,
        movement_detail_id=detail.id,
        quality_status=detail.quality_status,
        expiry_date=detail.expiry_date,
        manufacturing_date=detail.manufacturing_date,
        processed_by=user,
        auto_generated=True,
        remarks=f"Auto-generated from movement {movement.movement_number}"
    )
    
    stock_entry.generate_transaction_id()
    stock_entry.save()
    
    return stock_entry


# =============================================================================
# Dashboard and Analytics Views
# =============================================================================

@login_required
def movement_tracking_dashboard_view(request):
    """Movement tracking dashboard with analytics"""
    company = request.session.get('company')
    today = timezone.now().date()
    
    # Current period stats (last 30 days)
    last_30_days = today - timedelta(days=30)
    
    movement_stats = InventoryMovementMaster.objects.filter(
        company=company,
        movement_date__gte=last_30_days
    ).aggregate(
        total=Count('id'),
        pending=Count('id', filter=Q(movement_status='PENDING')),
        in_progress=Count('id', filter=Q(movement_status='IN_PROGRESS')),
        completed=Count('id', filter=Q(movement_status='COMPLETED')),
        cancelled=Count('id', filter=Q(movement_status='CANCELLED')),
        inward=Count('id', filter=Q(movement_type='INWARD')),
        outward=Count('id', filter=Q(movement_type='OUTWARD')),
        internal=Count('id', filter=Q(movement_type='INTERNAL')),
        total_value=Sum('total_value'),
        total_variance=Sum('variance_quantity')
    )
    
    # Stock ledger stats
    ledger_stats = StockLedger.objects.filter(
        company=company,
        transaction_date__gte=last_30_days
    ).aggregate(
        total_transactions=Count('id'),
        receipts=Count('id', filter=Q(transaction_type='RECEIPT')),
        issues=Count('id', filter=Q(transaction_type='ISSUE')),
        transfers=Count('id', filter=Q(transaction_type='TRANSFER')),
        adjustments=Count('id', filter=Q(transaction_type='ADJUSTMENT')),
        total_value_in=Sum('value_in'),
        total_value_out=Sum('value_out')
    )
    
    # Recent movements
    recent_movements = InventoryMovementMaster.objects.filter(
        company=company
    ).select_related('created_by').order_by('-created_date')[:10]
    
    # Recent stock transactions
    recent_transactions = StockLedger.objects.filter(
        company=company
    ).select_related('processed_by').order_by('-created_date')[:10]
    
    # Top moving items (by quantity)
    top_moving_items = StockLedger.objects.filter(
        company=company,
        transaction_date__gte=last_30_days
    ).values('item_code', 'item_description').annotate(
        total_quantity=Sum('quantity_out') + Sum('quantity_in'),
        total_transactions=Count('id')
    ).order_by('-total_quantity')[:10]
    
    # Movement efficiency metrics
    completed_movements = InventoryMovementMaster.objects.filter(
        company=company,
        movement_status='COMPLETED',
        processed_date__isnull=False
    )
    
    efficiency_metrics = {
        'avg_processing_time': None,
        'completion_rate': 0,
        'variance_rate': 0,
        'accuracy_percentage': 100
    }
    
    if completed_movements.exists():
        # Calculate completion rate
        total_movements = InventoryMovementMaster.objects.filter(
            company=company,
            movement_status__in=['COMPLETED', 'CANCELLED']
        ).count()
        
        if total_movements > 0:
            efficiency_metrics['completion_rate'] = (completed_movements.count() / total_movements) * 100
        
        # Calculate variance rate
        total_planned = completed_movements.aggregate(
            total=Sum('movement_details__planned_quantity')
        )['total'] or 0
        
        total_variance = completed_movements.aggregate(
            total=Sum('variance_quantity')
        )['total'] or 0
        
        if total_planned > 0:
            efficiency_metrics['variance_rate'] = abs(total_variance / total_planned) * 100
            efficiency_metrics['accuracy_percentage'] = max(0, 100 - efficiency_metrics['variance_rate'])
    
    context = {
        'movement_stats': movement_stats,
        'ledger_stats': ledger_stats,
        'recent_movements': recent_movements,
        'recent_transactions': recent_transactions,
        'top_moving_items': top_moving_items,
        'efficiency_metrics': efficiency_metrics,
        'period_days': 30,
    }
    
    return render(request, 'inventory/movement_tracking_dashboard.html', context)


# =============================================================================
# API Views
# =============================================================================

@login_required
def movement_tracking_statistics_api(request):
    """API endpoint for movement tracking statistics"""
    company = request.session.get('company')
    
    movement_stats = InventoryMovementMaster.objects.filter(company=company).aggregate(
        total=Count('id'),
        pending=Count('id', filter=Q(movement_status='PENDING')),
        in_progress=Count('id', filter=Q(movement_status='IN_PROGRESS')),
        completed=Count('id', filter=Q(movement_status='COMPLETED')),
        cancelled=Count('id', filter=Q(movement_status='CANCELLED')),
        inward=Count('id', filter=Q(movement_type='INWARD')),
        outward=Count('id', filter=Q(movement_type='OUTWARD')),
        internal=Count('id', filter=Q(movement_type='INTERNAL')),
        adjustments=Count('id', filter=Q(movement_type='ADJUSTMENT')),
        total_value=Sum('total_value'),
        avg_completion=Avg('completion_percentage')
    )
    
    ledger_stats = StockLedger.objects.filter(company=company).aggregate(
        total_transactions=Count('id'),
        receipts=Count('id', filter=Q(transaction_type='RECEIPT')),
        issues=Count('id', filter=Q(transaction_type='ISSUE')),
        transfers=Count('id', filter=Q(transaction_type='TRANSFER')),
        adjustments=Count('id', filter=Q(transaction_type='ADJUSTMENT')),
        total_value_in=Sum('value_in'),
        total_value_out=Sum('value_out'),
        unique_items=Count('item_code', distinct=True)
    )
    
    # Get latest snapshot stats
    latest_snapshot_date = InventorySnapshot.objects.filter(company=company).aggregate(
        latest=Max('snapshot_date')
    )['latest']
    
    snapshot_stats = {}
    if latest_snapshot_date:
        snapshot_stats = InventorySnapshot.objects.filter(
            company=company,
            snapshot_date=latest_snapshot_date
        ).aggregate(
            total_items=Count('id'),
            total_stock_value=Sum('total_value'),
            items_below_reorder=Count('id', filter=Q(current_stock__lte=F('reorder_level'))),
            fast_moving_items=Count('id', filter=Q(movement_frequency='FAST')),
            slow_moving_items=Count('id', filter=Q(movement_frequency='SLOW')),
            dead_stock_items=Count('id', filter=Q(movement_frequency='DEAD')),
            avg_stock_health=Avg('stock_health_score')
        )
        snapshot_stats['snapshot_date'] = latest_snapshot_date.isoformat()
    
    return JsonResponse({
        'movements': movement_stats,
        'stock_ledger': ledger_stats,
        'inventory_snapshot': snapshot_stats
    })


@login_required
def generate_inventory_snapshot_api(request):
    """API endpoint to generate inventory snapshot"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)
    
    company = request.session.get('company')
    financial_year = request.session.get('financial_year')
    
    try:
        with transaction.atomic():
            # This would typically be done as a background task
            # For now, we'll create a simple snapshot generation
            
            snapshot_date = timezone.now().date()
            snapshot_time = timezone.now().time()
            
            # Get unique items from stock ledger
            unique_items = StockLedger.objects.filter(
                company=company
            ).values('item_id', 'item_code', 'item_description', 'location_code').distinct()
            
            created_count = 0
            
            for item in unique_items:
                # Calculate current stock for this item/location
                ledger_entries = StockLedger.objects.filter(
                    company=company,
                    item_code=item['item_code'],
                    location_code=item['location_code']
                ).order_by('transaction_date', 'transaction_time')
                
                if ledger_entries.exists():
                    latest_entry = ledger_entries.last()
                    current_stock = latest_entry.quantity_balance
                    weighted_avg_rate = latest_entry.unit_rate
                    
                    # Create snapshot entry
                    InventorySnapshot.objects.create(
                        company_id=company,
                        financial_year_id=financial_year,
                        snapshot_date=snapshot_date,
                        snapshot_time=snapshot_time,
                        snapshot_type='ON_DEMAND',
                        item_id=item['item_id'],
                        item_code=item['item_code'],
                        item_description=item['item_description'],
                        location_code=item['location_code'],
                        current_stock=current_stock,
                        available_stock=max(0, current_stock),  # Simplified calculation
                        unit_of_measure=latest_entry.unit_of_measure,
                        weighted_avg_rate=weighted_avg_rate,
                        total_value=current_stock * weighted_avg_rate,
                        last_movement_date=latest_entry.transaction_date,
                        aging_days=(snapshot_date - latest_entry.transaction_date).days,
                        created_by=request.user
                    )
                    created_count += 1
        
        return JsonResponse({
            'success': True,
            'message': f'Generated {created_count} snapshot entries',
            'snapshot_date': snapshot_date.isoformat(),
            'created_count': created_count
        })
    
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)