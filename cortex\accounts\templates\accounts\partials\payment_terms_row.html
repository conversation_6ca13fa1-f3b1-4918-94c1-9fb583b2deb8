<!-- accounts/partials/payment_terms_row.html -->
<!-- HTMX partial for single PaymentTerms row - SAP S/4HANA inspired -->

{% load static %}

<tr class="hover:bg-sap-gray-50 transition-colors duration-150" id="payment-terms-row-{{ payment_terms.id }}">
    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-sap-gray-900">
        {{ payment_terms.id }}
    </td>
    <td class="px-6 py-4">
        <div class="flex items-center">
            <div class="w-8 h-8 bg-sap-purple-100 rounded-lg flex items-center justify-center mr-3">
                <i data-lucide="calendar" class="w-4 h-4 text-sap-purple-600"></i>
            </div>
            <div class="text-sm text-sap-gray-900">{{ payment_terms.terms|default:"N/A" }}</div>
        </div>
    </td>
    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div class="flex justify-end space-x-2">
            <!-- Edit Button -->
            <button type="button"
                    hx-get="{% url 'accounts:payment_terms_edit' payment_terms.id %}"
                    hx-target="#payment-terms-row-{{ payment_terms.id }}"
                    hx-swap="outerHTML"
                    class="inline-flex items-center px-3 py-1.5 border border-sap-purple-300 rounded text-xs font-medium text-sap-purple-700 bg-sap-purple-50 hover:bg-sap-purple-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-purple-500 transition-colors duration-200">
                <i data-lucide="edit" class="w-3 h-3 mr-1"></i>
                Edit
            </button>
            <!-- Delete Button -->
            <button type="button"
                    hx-delete="{% url 'accounts:payment_terms_delete' payment_terms.id %}"
                    hx-target="#payment-terms-row-{{ payment_terms.id }}"
                    hx-swap="outerHTML"
                    hx-confirm="Are you sure you want to delete these payment terms? This action cannot be undone."
                    class="inline-flex items-center px-3 py-1.5 border border-sap-red-300 rounded text-xs font-medium text-sap-red-700 bg-sap-red-50 hover:bg-sap-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-red-500 transition-colors duration-200">
                <i data-lucide="trash-2" class="w-3 h-3 mr-1"></i>
                Delete
            </button>
        </div>
    </td>
</tr>