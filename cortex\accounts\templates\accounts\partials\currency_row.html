<!-- accounts/partials/currency_row.html -->
<!-- HTMX partial for single Currency row - SAP S/4HANA inspired -->
<!-- Used for updating individual currency rows after edit/create -->

{% load static %}

<tr class="hover:bg-sap-gray-50 transition-colors duration-150" id="currency-row-{{ currency.id }}">
    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-sap-gray-900">
        {{ currency.id }}
    </td>
    <td class="px-6 py-4 whitespace-nowrap">
        <div class="flex items-center">
            <div class="w-8 h-8 bg-sap-blue-100 rounded-lg flex items-center justify-center mr-3">
                <i data-lucide="globe" class="w-4 h-4 text-sap-blue-600"></i>
            </div>
            <div class="text-sm font-medium text-sap-gray-900">
                {% if currency.country %}{{ currency.country.country_name }}{% else %}N/A{% endif %}
            </div>
        </div>
    </td>
    <td class="px-6 py-4 whitespace-nowrap">
        <div class="text-sm text-sap-gray-900">{{ currency.name|default:"N/A" }}</div>
    </td>
    <td class="px-6 py-4 whitespace-nowrap">
        <span class="inline-flex px-3 py-1 text-sm font-mono bg-sap-orange-100 text-sap-orange-800 rounded-full">
            {{ currency.symbol|default:"N/A" }}
        </span>
    </td>
    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div class="flex justify-end space-x-2">
            <!-- Edit Button -->
            <button type="button"
                    hx-get="{% url 'accounts:currency_edit' currency.id %}"
                    hx-target="#currency-row-{{ currency.id }}"
                    hx-swap="outerHTML"
                    class="inline-flex items-center px-3 py-1.5 border border-sap-orange-300 rounded text-xs font-medium text-sap-orange-700 bg-sap-orange-50 hover:bg-sap-orange-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-orange-500 transition-colors duration-200">
                <i data-lucide="edit" class="w-3 h-3 mr-1"></i>
                Edit
            </button>
            <!-- Delete Button -->
            <button type="button"
                    hx-delete="{% url 'accounts:currency_delete' currency.id %}"
                    hx-target="#currency-row-{{ currency.id }}"
                    hx-swap="outerHTML"
                    hx-confirm="Are you sure you want to delete this currency? This action cannot be undone."
                    class="inline-flex items-center px-3 py-1.5 border border-sap-red-300 rounded text-xs font-medium text-sap-red-700 bg-sap-red-50 hover:bg-sap-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-red-500 transition-colors duration-200">
                <i data-lucide="trash-2" class="w-3 h-3 mr-1"></i>
                Delete
            </button>
        </div>
    </td>
</tr>