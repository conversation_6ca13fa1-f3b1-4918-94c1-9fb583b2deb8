﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="PRPrint" targetNamespace="http://tempuri.org/PRPrint.xsd" xmlns:mstns="http://tempuri.org/PRPrint.xsd" xmlns="http://tempuri.org/PRPrint.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="PRPrint" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="PRPrint" msprop:Generator_DataSetName="PRPrint">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ItemCode" msprop:Generator_UserColumnName="ItemCode" msprop:Generator_ColumnPropNameInRow="ItemCode" msprop:Generator_ColumnVarNameInTable="columnItemCode" msprop:Generator_ColumnPropNameInTable="ItemCodeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PurchDesc" msprop:Generator_UserColumnName="PurchDesc" msprop:Generator_ColumnPropNameInRow="PurchDesc" msprop:Generator_ColumnVarNameInTable="columnPurchDesc" msprop:Generator_ColumnPropNameInTable="PurchDescColumn" type="xs:string" minOccurs="0" />
              <xs:element name="UomPurch" msprop:Generator_UserColumnName="UomPurch" msprop:Generator_ColumnPropNameInRow="UomPurch" msprop:Generator_ColumnVarNameInTable="columnUomPurch" msprop:Generator_ColumnPropNameInTable="UomPurchColumn" type="xs:string" minOccurs="0" />
              <xs:element name="DelDate" msprop:Generator_UserColumnName="DelDate" msprop:Generator_ColumnPropNameInRow="DelDate" msprop:Generator_ColumnVarNameInTable="columnDelDate" msprop:Generator_ColumnPropNameInTable="DelDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="SPRQTY" msprop:Generator_UserColumnName="SPRQTY" msprop:Generator_ColumnPropNameInRow="SPRQTY" msprop:Generator_ColumnVarNameInTable="columnSPRQTY" msprop:Generator_ColumnPropNameInTable="SPRQTYColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Rate" msprop:Generator_UserColumnName="Rate" msprop:Generator_ColumnPropNameInRow="Rate" msprop:Generator_ColumnVarNameInTable="columnRate" msprop:Generator_ColumnPropNameInTable="RateColumn" type="xs:double" minOccurs="0" />
              <xs:element name="WONo" msprop:Generator_UserColumnName="WONo" msprop:Generator_ColumnPropNameInRow="WONo" msprop:Generator_ColumnVarNameInTable="columnWONo" msprop:Generator_ColumnPropNameInTable="WONoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="SupplierName" msprop:Generator_UserColumnName="SupplierName" msprop:Generator_ColumnPropNameInRow="SupplierName" msprop:Generator_ColumnVarNameInTable="columnSupplierName" msprop:Generator_ColumnPropNameInTable="SupplierNameColumn" type="xs:string" minOccurs="0" />
              <xs:element name="AcHead" msprop:Generator_UserColumnName="AcHead" msprop:Generator_ColumnPropNameInRow="AcHead" msprop:Generator_ColumnVarNameInTable="columnAcHead" msprop:Generator_ColumnPropNameInTable="AcHeadColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Remarks" msprop:Generator_UserColumnName="Remarks" msprop:Generator_ColumnPropNameInRow="Remarks" msprop:Generator_ColumnVarNameInTable="columnRemarks" msprop:Generator_ColumnPropNameInTable="RemarksColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="Intender" msprop:Generator_UserColumnName="Intender" msprop:Generator_ColumnPropNameInRow="Intender" msprop:Generator_ColumnVarNameInTable="columnIntender" msprop:Generator_ColumnPropNameInTable="IntenderColumn" type="xs:string" minOccurs="0" />
              <xs:element name="TaskProjectTitle" msprop:Generator_UserColumnName="TaskProjectTitle" msprop:Generator_ColumnVarNameInTable="columnTaskProjectTitle" msprop:Generator_ColumnPropNameInRow="TaskProjectTitle" msprop:Generator_ColumnPropNameInTable="TaskProjectTitleColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Discount" msprop:Generator_UserColumnName="Discount" msprop:Generator_ColumnPropNameInRow="Discount" msprop:Generator_ColumnVarNameInTable="columnDiscount" msprop:Generator_ColumnPropNameInTable="DiscountColumn" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>