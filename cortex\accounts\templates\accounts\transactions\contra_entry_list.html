<!-- accounts/templates/accounts/transactions/contra_entry_list.html -->
<!-- Contra Entry List View Template -->
<!-- Task Group 2: Banking & Cash Management - Contra Entry List (Task 2.9) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Contra Entries - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-purple-600 to-sap-purple-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="repeat" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Contra Entries</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage cash-to-bank and bank-to-bank transfers</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to Dashboard
                </a>
                <a href="{% url 'accounts:contra_entry_create' %}" 
                   class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    New Contra Entry
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Search and Filter Section -->
    <div class="mb-6 bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4">
            <form method="get" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4">
                <!-- Search -->
                <div class="flex-1">
                    <label for="search" class="block text-sm font-medium text-sap-gray-700 mb-1">Search</label>
                    <input type="text" name="search" value="{{ request.GET.search }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500"
                           placeholder="Search by voucher number, description...">
                </div>
                
                <!-- Transfer Type Filter -->
                <div>
                    <label for="transfer_type" class="block text-sm font-medium text-sap-gray-700 mb-1">Transfer Type</label>
                    <select name="transfer_type" 
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500">
                        <option value="">All Types</option>
                        <option value="cash_to_bank" {% if request.GET.transfer_type == 'cash_to_bank' %}selected{% endif %}>Cash to Bank</option>
                        <option value="bank_to_cash" {% if request.GET.transfer_type == 'bank_to_cash' %}selected{% endif %}>Bank to Cash</option>
                        <option value="bank_to_bank" {% if request.GET.transfer_type == 'bank_to_bank' %}selected{% endif %}>Bank to Bank</option>
                    </select>
                </div>
                
                <!-- Date Range -->
                <div>
                    <label for="date_from" class="block text-sm font-medium text-sap-gray-700 mb-1">From Date</label>
                    <input type="date" name="date_from" value="{{ request.GET.date_from }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500">
                </div>
                
                <div>
                    <label for="date_to" class="block text-sm font-medium text-sap-gray-700 mb-1">To Date</label>
                    <input type="date" name="date_to" value="{{ request.GET.date_to }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500">
                </div>
                
                <!-- Search Button -->
                <div>
                    <button type="submit" 
                            class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="search" class="w-4 h-4 inline mr-2"></i>
                        Search
                    </button>
                </div>
                
                <!-- Clear Button -->
                {% if request.GET %}
                <div>
                    <a href="{% url 'accounts:contra_entry_list' %}" 
                       class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                        Clear
                    </a>
                </div>
                {% endif %}
            </form>
        </div>
    </div>

    <!-- Contra Entry Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="arrow-right" class="w-6 h-6 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Cash to Bank</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ cash_to_bank_count|default:0 }}</p>
                    <p class="text-xs text-sap-green-600 mt-1">₹{{ cash_to_bank_amount|default:0|floatformat:2 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="arrow-left" class="w-6 h-6 text-sap-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Bank to Cash</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ bank_to_cash_count|default:0 }}</p>
                    <p class="text-xs text-sap-orange-600 mt-1">₹{{ bank_to_cash_amount|default:0|floatformat:2 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="shuffle" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Bank to Bank</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ bank_to_bank_count|default:0 }}</p>
                    <p class="text-xs text-sap-blue-600 mt-1">₹{{ bank_to_bank_amount|default:0|floatformat:2 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="repeat" class="w-6 h-6 text-sap-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Amount</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ total_amount|default:0|floatformat:2 }}</p>
                    <p class="text-xs text-sap-purple-600 mt-1">All transfers</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Contra Entries Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Contra Entries</h3>
                <div class="flex items-center space-x-2">
                    <button type="button" onclick="exportEntries()" 
                            class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                        Export
                    </button>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Voucher #
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Date
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Transfer Type
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            From Account
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            To Account
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Amount
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Description
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for entry in contra_entries %}
                    <tr class="hover:bg-sap-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-sap-gray-900">
                                {{ entry.voucher_no }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-sap-gray-900">{{ entry.voucher_date|date:"d M Y" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if entry.transfer_type == 'cash_to_bank' %}
                            <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-sap-green-100 text-sap-green-800">
                                <i data-lucide="arrow-right" class="w-3 h-3 mr-1"></i>
                                Cash → Bank
                            </span>
                            {% elif entry.transfer_type == 'bank_to_cash' %}
                            <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-sap-orange-100 text-sap-orange-800">
                                <i data-lucide="arrow-left" class="w-3 h-3 mr-1"></i>
                                Bank → Cash
                            </span>
                            {% else %}
                            <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-sap-blue-100 text-sap-blue-800">
                                <i data-lucide="shuffle" class="w-3 h-3 mr-1"></i>
                                Bank → Bank
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-sap-gray-900">{{ entry.from_account_head.description|default:"-" }}</div>
                            {% if entry.from_bank %}
                            <div class="text-sm text-sap-gray-500">{{ entry.from_bank.name }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-sap-gray-900">{{ entry.to_account_head.description|default:"-" }}</div>
                            {% if entry.to_bank %}
                            <div class="text-sm text-sap-gray-500">{{ entry.to_bank.name }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-sap-gray-900">₹{{ entry.amount|floatformat:2 }}</div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-sap-gray-900 max-w-xs truncate" title="{{ entry.description }}">
                                {{ entry.description|default:"-" }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <a href="#" onclick="viewEntry({{ entry.id }})" 
                                   class="text-sap-blue-600 hover:text-sap-blue-900" title="View Details">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                </a>
                                <button type="button" onclick="printEntry({{ entry.id }})" 
                                        class="text-sap-green-600 hover:text-sap-green-900" title="Print">
                                    <i data-lucide="printer" class="w-4 h-4"></i>
                                </button>
                                <button type="button" onclick="deleteEntry({{ entry.id }})" 
                                        class="text-sap-red-600 hover:text-sap-red-900" title="Delete">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="px-6 py-8 text-center">
                            <div class="text-center">
                                <i data-lucide="repeat" class="w-12 h-12 text-sap-gray-400 mx-auto mb-4"></i>
                                <h3 class="text-sm font-medium text-sap-gray-900 mb-2">No contra entries found</h3>
                                <p class="text-sm text-sap-gray-600 mb-4">Get started by creating your first contra entry.</p>
                                <a href="{% url 'accounts:contra_entry_create' %}" 
                                   class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                                    Create Contra Entry
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="px-6 py-4 border-t border-sap-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-sap-gray-700">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} entries
                </div>
                <div class="flex space-x-1">
                    {% if page_obj.has_previous %}
                    <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ page_obj.previous_page_number }}" 
                       class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-l-lg hover:bg-sap-gray-50">
                        Previous
                    </a>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <span class="px-3 py-2 text-sm font-medium text-sap-purple-600 bg-sap-purple-50 border border-sap-purple-300">
                            {{ num }}
                        </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ num }}" 
                           class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 hover:bg-sap-gray-50">
                            {{ num }}
                        </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ page_obj.next_page_number }}" 
                       class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-r-lg hover:bg-sap-gray-50">
                        Next
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function exportEntries() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = '?' + params.toString();
}

function viewEntry(id) {
    // For now, just show a modal or navigate to detail view
    alert(`View contra entry details for ID: ${id}`);
}

function printEntry(id) {
    window.open(`/accounts/transactions/contra-entries/${id}/print/`, '_blank');
}

function deleteEntry(id) {
    if (confirm('Are you sure you want to delete this contra entry? This action cannot be undone.')) {
        fetch(`/accounts/transactions/contra-entries/${id}/delete/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        }).then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Error deleting contra entry');
            }
        });
    }
}

// Initialize lucide icons on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}