<!-- SAP Fiori Style MIN Results Table -->
<div class="sap-card overflow-hidden">
    <div class="overflow-x-auto">
        <table class="w-full table-auto">
            <thead class="bg-sap-gray-50 border-b border-sap-gray-200">
                <tr>
                    <th scope="col" class="px-3 py-3 text-left text-xs font-semibold text-sap-gray-700 uppercase tracking-wider w-16">
                        SN
                    </th>
                    <th scope="col" class="px-3 py-3 text-left text-xs font-semibold text-sap-gray-700 uppercase tracking-wider w-24">
                        FinYear
                    </th>
                    <th scope="col" class="px-3 py-3 text-left text-xs font-semibold text-sap-gray-700 uppercase tracking-wider w-20">
                        MIN No
                    </th>
                    <th scope="col" class="px-3 py-3 text-left text-xs font-semibold text-sap-gray-700 uppercase tracking-wider w-28">
                        Date
                    </th>
                    <th scope="col" class="px-3 py-3 text-left text-xs font-semibold text-sap-gray-700 uppercase tracking-wider w-20">
                        MRS No
                    </th>
                    <th scope="col" class="px-3 py-3 text-left text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">
                        Gen. By
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-sap-gray-100">
                {% for min in min_list %}
                <tr class="hover:bg-sap-blue-25 transition-colors duration-150 cursor-pointer" 
                    onclick="window.location.href='{% url 'inventory:min_detail' min.pk %}'">
                    <td class="px-3 py-3 text-sm font-medium text-sap-gray-900 text-center">
                        {{ forloop.counter0|add:page_obj.start_index|default:forloop.counter }}
                    </td>
                    <td class="px-3 py-3 text-sm text-sap-gray-600">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-sap-blue-100 text-sap-blue-800">
                            {{ min.financial_year.finyear|default:"2022-2023" }}
                        </span>
                    </td>
                    <td class="px-3 py-3 text-sm text-center">
                        <a href="{% url 'inventory:min_detail' min.pk %}" 
                           class="font-medium text-sap-blue-600 hover:text-sap-blue-800 transition-colors duration-150">
                            {{ min.min_no|default:"N/A" }}
                        </a>
                    </td>
                    <td class="px-3 py-3 text-sm text-sap-gray-600">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1 text-sap-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <span class="text-sm">{{ min.sysdate|default:"N/A" }}</span>
                        </div>
                    </td>
                    <td class="px-3 py-3 text-sm text-center">
                        <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-sap-gray-100 text-sap-gray-700 border border-sap-gray-200">
                            {{ min.mrs_no|default:"N/A" }}
                        </span>
                    </td>
                    <td class="px-3 py-3 text-sm text-sap-gray-600">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 w-6 h-6 bg-sap-blue-100 rounded-full flex items-center justify-center mr-2">
                                <span class="text-xs font-medium text-sap-blue-600">
                                    {{ min.sessionid|default:"N"|slice:":2"|upper }}
                                </span>
                            </div>
                            <span class="truncate text-sm">{{ min.sessionid|default:"N/A" }}</span>
                        </div>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="6" class="px-6 py-12 text-center">
                        <div class="flex flex-col items-center">
                            <svg class="w-12 h-12 text-sap-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                            <h3 class="text-sm font-medium text-sap-gray-900 mb-1">No Material Issue Notes found</h3>
                            <p class="text-sm text-sap-gray-500">Get started by creating a new MIN record.</p>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- SAP Fiori Style Pagination -->
{% if is_paginated %}
<div class="bg-white px-6 py-4 flex items-center justify-between border-t border-sap-gray-200">
    <div class="flex-1 flex justify-between sm:hidden">
        {% if page_obj.has_previous %}
            <a href="?page={{ page_obj.previous_page_number }}" 
               class="sap-button-secondary inline-flex items-center px-4 py-2 text-sm font-medium">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                Previous
            </a>
        {% endif %}
        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}" 
               class="sap-button-secondary inline-flex items-center px-4 py-2 text-sm font-medium ml-3">
                Next
                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a>
        {% endif %}
    </div>
    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div class="flex items-center text-sm text-sap-gray-600">
            <svg class="w-4 h-4 mr-2 text-sap-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <span class="font-medium">{{ page_obj.start_index }}-{{ page_obj.end_index }}</span>
            <span class="mx-1">of</span>
            <span class="font-medium">{{ page_obj.paginator.count }}</span>
            <span class="ml-1">items</span>
        </div>
        <div>
            <nav class="relative z-0 inline-flex rounded-lg shadow-sm -space-x-px" aria-label="Pagination">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}" 
                       class="relative inline-flex items-center px-2 py-2 rounded-l-lg border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-500 hover:bg-sap-gray-50 transition-colors duration-150">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                {% endif %}
                
                {% for page_num in page_obj.paginator.page_range %}
                    {% if page_num == page_obj.number %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-sap-blue-300 bg-sap-blue-50 text-sap-blue-600 text-sm font-medium">
                            {{ page_num }}
                        </span>
                    {% else %}
                        <a href="?page={{ page_num }}" 
                           class="relative inline-flex items-center px-4 py-2 border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-700 hover:bg-sap-gray-50 transition-colors duration-150">
                            {{ page_num }}
                        </a>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}" 
                       class="relative inline-flex items-center px-2 py-2 rounded-r-lg border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-500 hover:bg-sap-gray-50 transition-colors duration-150">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                {% endif %}
            </nav>
        </div>
    </div>
</div>
{% endif %}