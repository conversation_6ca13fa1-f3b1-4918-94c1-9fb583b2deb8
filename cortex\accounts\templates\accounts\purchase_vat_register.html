<!-- accounts/templates/accounts/purchase_vat_register.html -->
<!-- Purchase VAT Register Template -->
<!-- Task Group 4: Taxation Management - Purchase VAT Register with Filtering -->

{% extends 'core/base.html' %}
{% load static %}

{% block title %}Purchase VAT Register - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-green-500 to-sap-green-600 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="trending-down" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Purchase VAT Register</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Input VAT details and analysis</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:vat_register_dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Dashboard
                </a>
                <button @click="exportData('csv')" 
                        class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                    Export CSV
                </button>
                <button @click="printReport()" 
                        class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="printer" class="w-4 h-4 inline mr-2"></i>
                    Print
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6 space-y-6" x-data="purchaseVATRegister()">
    
    <!-- Filters Section -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-medium text-sap-gray-800">Filter & Search</h3>
                    <p class="text-sm text-sap-gray-600 mt-1">Customize your purchase VAT register view</p>
                </div>
                <button @click="resetFilters()" 
                        class="text-sap-gray-600 hover:text-sap-gray-800 text-sm font-medium">
                    Reset Filters
                </button>
            </div>
        </div>
        <div class="p-6">
            <form hx-get="{% url 'accounts:purchase_vat_register' %}" 
                  hx-target="#vat-register-content" 
                  hx-trigger="change delay:300ms, submit"
                  hx-indicator="#loading-indicator">
                {% csrf_token %}
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
                    <!-- Date Range -->
                    <div>
                        <label for="from_date" class="block text-sm font-medium text-sap-gray-700 mb-2">From Date</label>
                        <input type="date" name="from_date" id="from_date" 
                               value="{{ from_date }}"
                               class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                    </div>
                    <div>
                        <label for="to_date" class="block text-sm font-medium text-sap-gray-700 mb-2">To Date</label>
                        <input type="date" name="to_date" id="to_date" 
                               value="{{ to_date }}"
                               class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                    </div>
                    
                    <!-- Company Selection -->
                    <div>
                        <label for="company_id" class="block text-sm font-medium text-sap-gray-700 mb-2">Company</label>
                        <select name="company_id" id="company_id" 
                                class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                            {% for company in companies %}
                            <option value="{{ company.id }}" {% if company.id|stringformat:"s" == company_id %}selected{% endif %}>
                                {{ company.company_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <!-- Financial Year -->
                    <div>
                        <label for="financial_year_id" class="block text-sm font-medium text-sap-gray-700 mb-2">Financial Year</label>
                        <select name="financial_year_id" id="financial_year_id" 
                                class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                            {% for fy in financial_years %}
                            <option value="{{ fy.id }}" {% if fy.id|stringformat:"s" == financial_year_id %}selected{% endif %}>
                                {{ fy.year_code }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <!-- VAT Rate Filter -->
                    <div>
                        <label for="vat_rate_filter" class="block text-sm font-medium text-sap-gray-700 mb-2">VAT Rate</label>
                        <select name="vat_rate_filter" id="vat_rate_filter" 
                                class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                            <option value="">All Rates</option>
                            {% for vat in vat_rates %}
                            <option value="{{ vat.vat_percentage }}" {% if vat.vat_percentage|stringformat:"s" == vat_rate_filter %}selected{% endif %}>
                                {{ vat.vat_percentage }}%
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                
                <!-- Supplier Search -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="supplier_filter" class="block text-sm font-medium text-sap-gray-700 mb-2">Supplier Search</label>
                        <input type="text" name="supplier_filter" id="supplier_filter" 
                               value="{{ supplier_filter }}"
                               placeholder="Search by supplier name or ID..."
                               class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                    </div>
                    <div class="flex items-end">
                        <button type="submit" 
                                class="w-full bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="search" class="w-4 h-4 inline mr-2"></i>
                            Search
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    {% if summary_totals %}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-green-100 rounded-lg flex items-center justify-center mr-3">
                    <i data-lucide="hash" class="w-5 h-5 text-sap-green-600"></i>
                </div>
                <div>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ summary_totals.total_bills }}</p>
                    <p class="text-sm text-sap-gray-600">Total Bills</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <i data-lucide="coins" class="w-5 h-5 text-sap-blue-600"></i>
                </div>
                <div>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ summary_totals.total_basic_amount|floatformat:0 }}</p>
                    <p class="text-sm text-sap-gray-600">Basic Amount</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-orange-100 rounded-lg flex items-center justify-center mr-3">
                    <i data-lucide="percent" class="w-5 h-5 text-sap-orange-600"></i>
                </div>
                <div>
                    <p class="text-2xl font-bold text-sap-green-600">₹{{ summary_totals.total_vat_amount|floatformat:0 }}</p>
                    <p class="text-sm text-sap-gray-600">Input VAT</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-red-100 rounded-lg flex items-center justify-center mr-3">
                    <i data-lucide="truck" class="w-5 h-5 text-sap-red-600"></i>
                </div>
                <div>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ summary_totals.total_freight_amount|floatformat:0 }}</p>
                    <p class="text-sm text-sap-gray-600">Freight</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-gray-100 rounded-lg flex items-center justify-center mr-3">
                    <i data-lucide="receipt" class="w-5 h-5 text-sap-gray-600"></i>
                </div>
                <div>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ summary_totals.total_bill_amount|floatformat:0 }}</p>
                    <p class="text-sm text-sap-gray-600">Total Amount</p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Loading Indicator -->
    <div id="loading-indicator" class="htmx-indicator">
        <div class="flex items-center justify-center py-8">
            <div class="flex items-center text-sap-gray-600">
                <i data-lucide="loader" class="w-6 h-6 animate-spin mr-3"></i>
                Loading purchase VAT register data...
            </div>
        </div>
    </div>

    <!-- VAT Register Table -->
    <div id="vat-register-content" class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-medium text-sap-gray-800">Purchase VAT Register</h3>
                    <p class="text-sm text-sap-gray-600 mt-1">{{ from_date|date:"d/m/Y" }} to {{ to_date|date:"d/m/Y" }}</p>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="text-sm text-sap-gray-600">{{ vat_register_data|length }} records</span>
                    <button @click="toggleVATBreakdown()" 
                            class="text-sap-green-600 hover:text-sap-green-700 text-sm font-medium">
                        <span x-text="showVATBreakdown ? 'Hide' : 'Show'"></span> VAT Breakdown
                    </button>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Bill Details</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Supplier</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Basic Amount</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">VAT Amount</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Other Taxes</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Total Amount</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for record in vat_register_data %}
                    <tr class="hover:bg-sap-gray-50 transition-colors" 
                        x-data="{ expanded: false }">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div>
                                    <div class="text-sm font-medium text-sap-gray-900">
                                        {{ record.bill.bill_no }}
                                    </div>
                                    <div class="text-sm text-sap-gray-500">
                                        {{ record.bill.bill_date|date:"d/m/Y" }}
                                    </div>
                                    {% if record.bill.supplier_bill_no %}
                                    <div class="text-xs text-sap-green-600">
                                        Supplier Bill: {{ record.bill.supplier_bill_no }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-sap-gray-900">
                                {{ record.bill.supplier_name|truncatechars:25 }}
                            </div>
                            {% if record.bill.supplier_id %}
                            <div class="text-sm text-sap-gray-500">
                                ID: {{ record.bill.supplier_id }}
                            </div>
                            {% endif %}
                        </td>
                        
                        <td class="px-6 py-4 whitespace-nowrap text-right">
                            <div class="text-sm font-medium text-sap-gray-900">
                                ₹{{ record.total_basic|floatformat:2 }}
                            </div>
                        </td>
                        
                        <td class="px-6 py-4 whitespace-nowrap text-right">
                            <div class="text-sm font-medium text-sap-green-600">
                                ₹{{ record.total_vat|floatformat:2 }}
                            </div>
                            {% if record.vat_breakdown and showVATBreakdown %}
                            <div class="text-xs text-sap-gray-500 mt-1">
                                {% for rate, amounts in record.vat_breakdown.items %}
                                {{ rate }}%: ₹{{ amounts.vat_amount|floatformat:0 }}{% if not forloop.last %}, {% endif %}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </td>
                        
                        <td class="px-6 py-4 whitespace-nowrap text-right">
                            <div class="text-sm text-sap-gray-900">
                                {% with cst=record.bill.cst_amount|default:0 excise=record.bill.excise_amount|default:0 service_tax=record.bill.service_tax_amount|default:0 %}
                                ₹{{ cst|add:excise|add:service_tax|floatformat:2 }}
                                {% endwith %}
                            </div>
                            <div class="text-xs text-sap-gray-500">
                                {% if record.bill.cst_amount %}CST: ₹{{ record.bill.cst_amount|floatformat:0 }}{% endif %}
                                {% if record.bill.excise_amount %}{% if record.bill.cst_amount %}, {% endif %}Excise: ₹{{ record.bill.excise_amount|floatformat:0 }}{% endif %}
                            </div>
                        </td>
                        
                        <td class="px-6 py-4 whitespace-nowrap text-right">
                            <div class="text-sm font-bold text-sap-gray-900">
                                ₹{{ record.total_amount|floatformat:2 }}
                            </div>
                        </td>
                        
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            <div class="flex items-center justify-center space-x-2">
                                <button @click="expanded = !expanded" 
                                        class="text-sap-green-600 hover:text-sap-green-700 text-sm">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                </button>
                                <button @click="viewBill('{{ record.bill.id }}')" 
                                        class="text-sap-blue-600 hover:text-sap-blue-700 text-sm">
                                    <i data-lucide="external-link" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <!-- Expanded Details Row -->
                    <tr x-show="expanded" x-transition class="bg-sap-green-50">
                        <td colspan="7" class="px-6 py-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                <div>
                                    <h5 class="text-sm font-medium text-sap-gray-800 mb-2">Bill Information</h5>
                                    <div class="text-sm text-sap-gray-600 space-y-1">
                                        {% if record.bill.po_no %}<p><strong>PO Number:</strong> {{ record.bill.po_no }}</p>{% endif %}
                                        {% if record.bill.grr_no %}<p><strong>GRR Number:</strong> {{ record.bill.grr_no }}</p>{% endif %}
                                        <p><strong>Status:</strong> 
                                            <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full 
                                                {% if record.bill.status == 'authorized' %}bg-sap-green-100 text-sap-green-800{% else %}bg-sap-orange-100 text-sap-orange-800{% endif %}">
                                                {{ record.bill.status|title }}
                                            </span>
                                        </p>
                                    </div>
                                </div>
                                
                                {% if record.vat_breakdown %}
                                <div>
                                    <h5 class="text-sm font-medium text-sap-gray-800 mb-2">VAT Breakdown</h5>
                                    <div class="text-sm text-sap-gray-600 space-y-1">
                                        {% for rate, amounts in record.vat_breakdown.items %}
                                        <div class="flex justify-between">
                                            <span>{{ rate }}% VAT:</span>
                                            <span class="font-medium">₹{{ amounts.vat_amount|floatformat:2 }}</span>
                                        </div>
                                        <div class="text-xs text-sap-gray-500 ml-4">
                                            Basic: ₹{{ amounts.basic_amount|floatformat:2 }}
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}
                                
                                <div>
                                    <h5 class="text-sm font-medium text-sap-gray-800 mb-2">Additional Charges</h5>
                                    <div class="text-sm text-sap-gray-600 space-y-1">
                                        {% if record.bill.freight_amount %}<p>Freight: ₹{{ record.bill.freight_amount|floatformat:2 }}</p>{% endif %}
                                        {% if record.bill.discount_amount %}<p>Discount: ₹{{ record.bill.discount_amount|floatformat:2 }}</p>{% endif %}
                                        {% if record.bill.packing_forwarding_amount %}<p>P&F: ₹{{ record.bill.packing_forwarding_amount|floatformat:2 }}</p>{% endif %}
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="px-6 py-8 text-center">
                            <div class="text-sap-gray-500">
                                <i data-lucide="inbox" class="w-12 h-12 mx-auto mb-4 text-sap-gray-300"></i>
                                <p class="text-lg font-medium">No purchase VAT records found</p>
                                <p class="text-sm">Try adjusting your search criteria or date range</p>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- VAT Rate Analysis -->
    {% if summary_totals.vat_rate_breakdown %}
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <h3 class="text-lg font-medium text-sap-gray-800">VAT Rate-wise Analysis</h3>
            <p class="text-sm text-sap-gray-600 mt-1">Purchase breakdown by VAT percentage</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {% for breakdown in summary_totals.vat_rate_breakdown %}
                <div class="bg-sap-gray-50 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-sap-green-100 rounded-lg flex items-center justify-center mr-3">
                                <span class="text-xs font-bold text-sap-green-600">{{ breakdown.vat_percentage|default:0 }}%</span>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-sap-gray-900">{{ breakdown.vat_percentage|default:0 }}% VAT</p>
                            </div>
                        </div>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <span class="text-sap-gray-600">Basic Amount:</span>
                            <span class="font-medium">₹{{ breakdown.basic_amount|floatformat:2 }}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-sap-gray-600">Input VAT:</span>
                            <span class="font-medium text-sap-green-600">₹{{ breakdown.vat_amount|floatformat:2 }}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-sap-gray-600">Total Amount:</span>
                            <span class="font-bold">₹{{ breakdown.total_amount|floatformat:2 }}</span>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Input VAT Credit Analysis -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <h3 class="text-lg font-medium text-sap-gray-800">Input VAT Credit Analysis</h3>
            <p class="text-sm text-sap-gray-600 mt-1">Available input tax credit for set-off</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-sap-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i data-lucide="trending-down" class="w-8 h-8 text-sap-green-600"></i>
                    </div>
                    <div class="text-2xl font-bold text-sap-green-600 mb-2">
                        ₹{{ summary_totals.total_vat_amount|floatformat:0 }}
                    </div>
                    <div class="text-sm text-sap-gray-600">Total Input VAT Credit</div>
                    <div class="text-xs text-sap-gray-500 mt-1">Available for set-off</div>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 bg-sap-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i data-lucide="calculator" class="w-8 h-8 text-sap-blue-600"></i>
                    </div>
                    <div class="text-2xl font-bold text-sap-blue-600 mb-2">
                        {% widthratio summary_totals.total_vat_amount summary_totals.total_basic_amount 100 %}%
                    </div>
                    <div class="text-sm text-sap-gray-600">Effective Input VAT Rate</div>
                    <div class="text-xs text-sap-gray-500 mt-1">On total purchases</div>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 bg-sap-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i data-lucide="percent" class="w-8 h-8 text-sap-orange-600"></i>
                    </div>
                    <div class="text-2xl font-bold text-sap-orange-600 mb-2">
                        {{ summary_totals.total_bills }}
                    </div>
                    <div class="text-sm text-sap-gray-600">VAT Bills Processed</div>
                    <div class="text-xs text-sap-gray-500 mt-1">In selected period</div>
                </div>
            </div>
        </div>
    </div>

</div>
{% endblock %}

{% block extra_js %}
<script>
function purchaseVATRegister() {
    return {
        showVATBreakdown: false,
        
        init() {
            lucide.createIcons();
        },
        
        resetFilters() {
            // Reset all filter fields
            document.getElementById('supplier_filter').value = '';
            document.getElementById('vat_rate_filter').value = '';
            
            // Set date range to current month
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            document.getElementById('from_date').value = firstDay.toISOString().split('T')[0];
            document.getElementById('to_date').value = today.toISOString().split('T')[0];
            
            // Trigger the search
            htmx.trigger(document.querySelector('form'), 'submit');
        },
        
        toggleVATBreakdown() {
            this.showVATBreakdown = !this.showVATBreakdown;
            // Re-render the table to show/hide VAT breakdown
            htmx.trigger(document.querySelector('form'), 'submit');
        },
        
        viewBill(billId) {
            // Open bill details in new window/modal
            window.open(`/accounts/bill-booking/${billId}/view/`, '_blank');
        },
        
        exportData(format) {
            const form = document.querySelector('form');
            const formData = new FormData(form);
            
            const params = new URLSearchParams(formData);
            params.append('export_type', 'purchase');
            
            window.open(`{% url 'accounts:export_vat_register' %}?${params.toString()}`);
        },
        
        printReport() {
            window.print();
        }
    }
}

// Real-time search functionality
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
    
    // Add real-time search to supplier filter
    const supplierFilter = document.getElementById('supplier_filter');
    if (supplierFilter) {
        let debounceTimer;
        supplierFilter.addEventListener('input', function() {
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(() => {
                htmx.trigger(document.querySelector('form'), 'submit');
            }, 300);
        });
    }
});
</script>
{% endblock %}