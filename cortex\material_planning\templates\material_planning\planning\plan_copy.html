{% extends 'material_planning/base.html' %}

{% block title %}Copy Material Plan{% endblock %}

{% block content %}
<div class="space-y-6 animate-fade-in">
    <!-- Header Section -->
    <div class="sap-card">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-sap-gray-900 mb-2">Copy Material Plan</h1>
                <p class="text-sap-gray-600">Create a copy of plan: {{ original_plan.plno|default:"N/A" }}</p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'material_planning:plan_detail' original_plan.pk %}" class="sap-button-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Plan
                </a>
            </div>
        </div>
    </div>

    <!-- Copy Confirmation -->
    <div class="sap-card">
        <h2 class="text-xl font-semibold text-sap-gray-900 mb-4">Confirm Copy Operation</h2>
        
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">What will be copied?</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <ul class="list-disc list-inside space-y-1">
                            <li>Plan number (with "_COPY" suffix)</li>
                            <li>Work order number (with "_COPY" suffix)</li>
                            <li>All material details and specifications</li>
                            <li>Company and financial year information</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Original Plan Information -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <div>
                <h3 class="text-lg font-medium text-sap-gray-900 mb-3">Original Plan Details</h3>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-sap-gray-700">Plan Number:</span>
                        <span class="text-sm text-sap-gray-900">{{ original_plan.plno|default:"N/A" }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-sap-gray-700">Work Order:</span>
                        <span class="text-sm text-sap-gray-900">{{ original_plan.wono|default:"N/A" }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-sap-gray-700">Company:</span>
                        <span class="text-sm text-sap-gray-900">{{ original_plan.company.companyname|default:"N/A" }}</span>
                    </div>
                </div>
            </div>
            
            <div>
                <h3 class="text-lg font-medium text-sap-gray-900 mb-3">New Plan Details</h3>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-sap-gray-700">Plan Number:</span>
                        <span class="text-sm text-sap-gray-900">{{ original_plan.plno|default:"N/A" }}_COPY</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-sap-gray-700">Work Order:</span>
                        <span class="text-sm text-sap-gray-900">{{ original_plan.wono|default:"N/A" }}_COPY</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-sap-gray-700">Created by:</span>
                        <span class="text-sm text-sap-gray-900">{{ request.user.username }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <form method="post" class="flex justify-end space-x-3">
            {% csrf_token %}
            <a href="{% url 'material_planning:plan_detail' original_plan.pk %}" class="sap-button-secondary">
                <i class="fas fa-times mr-2"></i>
                Cancel
            </a>
            <button type="submit" class="sap-button-primary">
                <i class="fas fa-copy mr-2"></i>
                Create Copy
            </button>
        </form>
    </div>
</div>
{% endblock %}