"""
Material Planning Reporting Views - Task Group 4
Business intelligence and performance tracking views
"""

from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import JsonResponse
from django.utils import timezone
from datetime import timedelta

from ..models import Material


class PlanningDashboardView(LoginRequiredMixin, TemplateView):
    """Executive planning dashboard with key metrics and analytics"""
    template_name = 'material_planning/reports/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Calculate dashboard metrics
        current_date = timezone.now().date()
        current_month_start = current_date.replace(day=1)
        last_month_start = (current_month_start - timedelta(days=1)).replace(day=1)
        
        # Planning Overview
        plans = Material.objects.all()
        context['planning_overview'] = {
            'total_plans': plans.count(),
            'active_plans': plans.count(),
            'draft_plans': 0,
            'completed_plans': 0,
            'overdue_plans': 0,
        }
        
        # Financial Metrics (simplified for existing data)
        context['financial_metrics'] = {
            'total_budget': 0,
            'total_estimated_cost': 0,
            'total_actual_cost': 0,
            'budget_utilization': 0,
        }
        
        # Supplier Performance Summary (simplified)
        context['supplier_summary'] = {
            'total_suppliers': 0,
            'category_a_suppliers': 0,
            'category_o_suppliers': 0,
            'category_f_suppliers': 0,
            'preferred_suppliers': 0,
        }
        
        # Exception Summary (simplified)
        context['exception_summary'] = {
            'total_exceptions': 0,
            'open_exceptions': 0,
            'critical_exceptions': 0,
            'overdue_exceptions': 0,
        }
        
        # Recent Activity
        context['recent_plans'] = plans.order_by('-id')[:5]
        context['recent_exceptions'] = []
        
        # KPI Summary (simplified)
        kpis = []
        context['dashboard_kpis'] = kpis
        
        return context


# HTMX Views for dynamic functionality
def dashboard_refresh(request):
    """HTMX view for dashboard refresh"""
    return JsonResponse({'status': 'refreshed'})


def kpi_update(request, kpi_id):
    """HTMX view for KPI updates"""
    return JsonResponse({'status': 'updated'})


def exception_chart_data(request):
    """HTMX view for exception chart data"""
    return JsonResponse({'chart_data': []})


def supplier_performance_chart(request):
    """HTMX view for supplier performance chart"""
    return JsonResponse({'chart_data': []})


def planning_cost_breakdown(request, plan_id):
    """HTMX view for planning cost breakdown"""
    return JsonResponse({'cost_data': []})


def generate_report_preview(request):
    """HTMX view for report preview"""
    return JsonResponse({'preview': 'No preview available'})