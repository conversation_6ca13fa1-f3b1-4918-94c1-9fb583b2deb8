<!-- accounts/templates/accounts/excise_duty_confirm_delete.html -->
<!-- Excise Duty Delete Confirmation Template -->
<!-- Task Group 4: Taxation Management - Excise Duty Delete Confirmation -->

{% extends 'core/base.html' %}

{% block title %}Delete Excise Duty - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-red-600 to-red-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="trash-2" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Delete Excise Duty</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Confirm deletion of excise duty configuration</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:excise_duty_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
                <a href="{% url 'accounts:excise_duty_edit' object.pk %}" 
                   class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="edit" class="w-4 h-4 inline mr-2"></i>
                    Edit Instead
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    <div class="max-w-2xl mx-auto">
        
        <!-- Deletion Confirmation Card -->
        <div class="bg-white rounded-lg border border-red-200 shadow-sm">
            <div class="px-6 py-4 bg-red-50 border-b border-red-200 rounded-t-lg">
                <div class="flex items-center">
                    <i data-lucide="alert-triangle" class="w-6 h-6 text-red-600 mr-3"></i>
                    <h3 class="text-lg font-medium text-red-800">Confirm Deletion</h3>
                </div>
            </div>
            
            <div class="p-6">
                <p class="text-sap-gray-700 mb-6">
                    Are you sure you want to delete the following excise duty configuration? This action cannot be undone.
                </p>
                
                <!-- Excise Duty Details -->
                <div class="bg-sap-gray-50 rounded-lg p-4 mb-6">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-600">Terms/Description</label>
                            <p class="text-base font-semibold text-sap-gray-900">{{ object.terms }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-600">Basic Rate</label>
                            <p class="text-base font-semibold text-sap-gray-900">{{ object.value|floatformat:3 }}%</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-600">Education Cess</label>
                            <p class="text-base font-semibold text-sap-gray-900">{{ object.edu_cess|floatformat:3 }}%</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-600">SHE Cess</label>
                            <p class="text-base font-semibold text-sap-gray-900">{{ object.she_cess|floatformat:3 }}%</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-600">Total Rate</label>
                            <p class="text-lg font-bold text-sap-blue-600">{{ object.total_excise_rate|floatformat:3 }}%</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-600">Status</label>
                            <div class="flex items-center space-x-2">
                                {% if object.is_active %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-green-100 text-sap-green-800">
                                    <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                    Active
                                </span>
                                {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-gray-100 text-sap-gray-800">
                                    <i data-lucide="pause-circle" class="w-3 h-3 mr-1"></i>
                                    Inactive
                                </span>
                                {% endif %}
                                
                                {% if object.is_default_excise %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-blue-100 text-sap-blue-800">
                                    <i data-lucide="star" class="w-3 h-3 mr-1"></i>
                                    Default Excise
                                </span>
                                {% endif %}
                                
                                {% if object.is_default_service_tax %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-purple-100 text-sap-purple-800">
                                    <i data-lucide="star" class="w-3 h-3 mr-1"></i>
                                    Default Service Tax
                                </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Usage Warning -->
                {% if usage_count > 0 %}
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <i data-lucide="alert-triangle" class="w-5 h-5 text-yellow-400 mr-3"></i>
                        <div>
                            <h4 class="text-sm font-medium text-yellow-800">Usage Warning</h4>
                            <p class="text-sm text-yellow-700 mt-1">
                                This excise duty is currently being used in {{ usage_count }} transaction(s). 
                                Deleting it may affect existing invoices and calculations.
                            </p>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                {% if not can_delete %}
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <i data-lucide="x-circle" class="w-5 h-5 text-red-400 mr-3"></i>
                        <div>
                            <h4 class="text-sm font-medium text-red-800">Cannot Delete</h4>
                            <p class="text-sm text-red-700 mt-1">
                                This excise duty cannot be deleted because:
                            </p>
                            <ul class="list-disc list-inside text-sm text-red-700 mt-2">
                                {% if object.is_default_excise %}
                                <li>It is set as the default excise duty</li>
                                {% endif %}
                                {% if object.is_default_service_tax %}
                                <li>It is set as the default service tax</li>
                                {% endif %}
                                {% if usage_count > 0 %}
                                <li>It is being used in {{ usage_count }} transaction(s)</li>
                                {% endif %}
                            </ul>
                            <p class="text-sm text-red-700 mt-2">
                                Please remove the default flags or replace this excise duty in existing transactions before deleting.
                            </p>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <!-- Default Duty Warning -->
                {% if object.is_default_excise or object.is_default_service_tax %}
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <i data-lucide="info" class="w-5 h-5 text-blue-400 mr-3"></i>
                        <div>
                            <h4 class="text-sm font-medium text-blue-800">Default Duty Notice</h4>
                            <p class="text-sm text-blue-700 mt-1">
                                This excise duty is currently set as a default 
                                {% if object.is_default_excise and object.is_default_service_tax %}
                                for both excise and service tax
                                {% elif object.is_default_excise %}
                                for excise duty
                                {% else %}
                                for service tax
                                {% endif %}. 
                                Deleting it will remove the default setting and may affect new transactions.
                            </p>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <!-- Deletion Impact -->
                <div class="bg-sap-gray-50 border border-sap-gray-200 rounded-lg p-4 mb-6">
                    <h4 class="text-sm font-medium text-sap-gray-800 mb-2">Impact of Deletion:</h4>
                    <ul class="text-sm text-sap-gray-700 space-y-1">
                        <li class="flex items-center">
                            <i data-lucide="x" class="w-4 h-4 text-red-500 mr-2"></i>
                            This excise duty configuration will be permanently removed
                        </li>
                        <li class="flex items-center">
                            <i data-lucide="x" class="w-4 h-4 text-red-500 mr-2"></i>
                            It will no longer be available for new transactions
                        </li>
                        {% if object.is_default_excise or object.is_default_service_tax %}
                        <li class="flex items-center">
                            <i data-lucide="alert-triangle" class="w-4 h-4 text-yellow-500 mr-2"></i>
                            Default setting will be removed
                        </li>
                        {% endif %}
                        <li class="flex items-center">
                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                            Existing transactions will retain their calculated tax amounts
                        </li>
                    </ul>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-4">
                    <div class="flex items-center space-x-3">
                        <a href="{% url 'accounts:excise_duty_list' %}" 
                           class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="x" class="w-4 h-4 inline mr-2"></i>
                            Cancel
                        </a>
                        <a href="{% url 'accounts:excise_duty_edit' object.pk %}" 
                           class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="edit" class="w-4 h-4 inline mr-2"></i>
                            Edit Instead
                        </a>
                    </div>
                    
                    {% if can_delete %}
                    <form method="post" class="inline">
                        {% csrf_token %}
                        <button type="submit" 
                                onclick="return confirm('Are you absolutely sure? This action cannot be undone.')"
                                class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="trash-2" class="w-4 h-4 inline mr-2"></i>
                            Delete Permanently
                        </button>
                    </form>
                    {% else %}
                    <button disabled 
                            class="bg-sap-gray-300 text-sap-gray-500 px-6 py-2 rounded-lg font-medium cursor-not-allowed">
                        <i data-lucide="lock" class="w-4 h-4 inline mr-2"></i>
                        Cannot Delete
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Alternative Actions -->
        <div class="mt-6 bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-base font-medium text-sap-gray-800">Alternative Actions</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <a href="{% url 'accounts:excise_duty_edit' object.pk %}" 
                       class="flex items-center p-4 border border-sap-gray-200 rounded-lg hover:bg-sap-blue-50 hover:border-sap-blue-300 transition-all duration-200">
                        <div class="w-10 h-10 bg-sap-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <i data-lucide="edit" class="w-5 h-5 text-sap-blue-600"></i>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-sap-gray-900">Edit Configuration</h4>
                            <p class="text-xs text-sap-gray-600">Modify rates or settings instead</p>
                        </div>
                    </a>
                    
                    <a href="{% url 'accounts:excise_duty_create' %}" 
                       class="flex items-center p-4 border border-sap-gray-200 rounded-lg hover:bg-sap-green-50 hover:border-sap-green-300 transition-all duration-200">
                        <div class="w-10 h-10 bg-sap-green-100 rounded-lg flex items-center justify-center mr-3">
                            <i data-lucide="plus" class="w-5 h-5 text-sap-green-600"></i>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-sap-gray-900">Create New</h4>
                            <p class="text-xs text-sap-gray-600">Add a replacement configuration</p>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}