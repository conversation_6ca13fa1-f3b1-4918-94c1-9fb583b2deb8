# sales_distribution/forms/enquiry_forms.py
# Enhanced SAP Fiori-inspired Enquiry Forms
# Replaces ASP.NET CustEnquiry_New.aspx functionality with modern UI

from django import forms
from django.core.exceptions import ValidationError
from django.core.validators import EmailValidator
from ..models import Enquiry
from sys_admin.models import Country, State, City
import re


class EnquiryForm(forms.ModelForm):
    """
    Enhanced SAP Fiori-inspired Customer Enquiry Form
    Replaces ASP.NET CustEnquiry_New.aspx functionality
    Modern design with better UX and validation
    """
    
    # Customer type selection (New/Existing)
    customer_type = forms.ChoiceField(
        choices=[('new', 'New Customer'), ('existing', 'Existing Customer')],
        widget=forms.RadioSelect(attrs={
            'class': 'text-blue-600 border-gray-300 focus:ring-blue-500',
            'x-model': 'customerType',
            '@change': 'handleCustomerTypeChange()',
        }),
        initial='new',
        label='Customer Type'
    )
    
    # File attachments (single file for now)
    attachments = forms.FileField(
        required=False,
        widget=forms.ClearableFileInput(attrs={
            'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100',
            'accept': '.pdf,.doc,.docx,.jpg,.jpeg,.png,.xls,.xlsx',
        }),
        label='Attachments'
    )

    class Meta:
        model = Enquiry
        fields = [
            # Customer Information
            'customername',
            'contactperson', 
            'email',
            'contactno',
            
            # Registered Office Address
            'regdaddress',
            'regdcountry',
            'regdstate', 
            'regdcity',
            'regdpinno',
            'regdcontactno',
            'regdfaxno',
            
            # Works/Factory Address  
            'workaddress',
            'workcountry',
            'workstate',
            'workcity', 
            'workpinno',
            'workcontactno',
            'workfaxno',
            
            # Material Delivery Address
            'materialdeladdress',
            'materialdelcountry',
            'materialdelstate',
            'materialdelcity',
            'materialdelpinno', 
            'materialdelcontactno',
            'materialdelfaxno',
            
            # Business Information
            'juridictioncode',
            'commissionurate',
            'tinvatno',
            'eccno',
            'divn',
            'tincstno',
            'range',
            'panno',
            'tdscode',
            
            # Enquiry Details
            'enquiryfor',
            'remark',
        ]
        
        widgets = {
            # Customer Information Section
            'customername': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors',
                'placeholder': 'Enter customer name',
                'x-model': 'form.customername',
                'x-bind:disabled': 'customerType === "existing"',
                'required': True,
            }),
            
            'contactperson': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors',
                'placeholder': 'Contact person name',
                'x-model': 'form.contactperson',
            }),
            
            'email': forms.EmailInput(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors',
                'placeholder': '<EMAIL>',
                'x-model': 'form.email',
            }),
            
            'contactno': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors',
                'placeholder': 'Contact number',
                'x-model': 'form.contactno',
            }),
            
            # Registered Office Address Section
            'regdaddress': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none',
                'placeholder': 'Registered office address',
                'rows': 3,
                'x-model': 'form.regdaddress',
                'required': True,
            }),
            
            'regdcountry': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white',
                'x-model': 'form.regdcountry',
                'hx-get': '/sales-distribution/ajax/get-states/',
                'hx-target': '#id_regdstate',
                'hx-trigger': 'change',
                'hx-include': '[name="regdcountry"]',
                'x-on:change': 'clearRegdStatesAndCities()',
            }),
            
            'regdstate': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white',
                'x-model': 'form.regdstate',
                'hx-get': '/sales-distribution/ajax/get-cities/',
                'hx-target': '#id_regdcity',
                'hx-trigger': 'change',
                'hx-include': '[name="regdstate"]',
                'x-on:change': 'clearRegdCities()',
            }),
            
            'regdcity': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white',
                'x-model': 'form.regdcity',
            }),
            
            'regdpinno': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors',
                'placeholder': 'PIN Code',
                'maxlength': '6',
                'x-model': 'form.regdpinno',
                'pattern': '[0-9]{6}',
            }),
            
            'regdcontactno': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors',
                'placeholder': 'Contact number',
                'x-model': 'form.regdcontactno',
            }),
            
            'regdfaxno': forms.HiddenInput(),
            
            # Works/Factory Address Section
            'workaddress': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none',
                'placeholder': 'Works/Factory address',
                'rows': 3,
                'x-model': 'form.workaddress',
            }),
            
            'workcountry': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white',
                'x-model': 'form.workcountry',
                'hx-get': '/sales-distribution/ajax/get-states/',
                'hx-target': '#id_workstate',
                'hx-trigger': 'change',
                'hx-include': '[name="workcountry"]',
                'x-on:change': 'clearWorkStatesAndCities()',
            }),
            
            'workstate': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white',
                'x-model': 'form.workstate',
                'hx-get': '/sales-distribution/ajax/get-cities/',
                'hx-target': '#id_workcity',
                'hx-trigger': 'change',
                'hx-include': '[name="workstate"]',
                'x-on:change': 'clearWorkCities()',
            }),
            
            'workcity': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white',
                'x-model': 'form.workcity',
            }),
            
            'workpinno': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors',
                'placeholder': 'PIN Code',
                'maxlength': '6',
                'x-model': 'form.workpinno',
                'pattern': '[0-9]{6}',
            }),
            
            'workcontactno': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors',
                'placeholder': 'Contact number',
                'x-model': 'form.workcontactno',
            }),
            
            'workfaxno': forms.HiddenInput(),
            
            # Material Delivery Address Section
            'materialdeladdress': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none',
                'placeholder': 'Material delivery address',
                'rows': 3,
                'x-model': 'form.materialdeladdress',
            }),
            
            'materialdelcountry': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white',
                'x-model': 'form.materialdelcountry',
                'hx-get': '/sales-distribution/ajax/get-states/',
                'hx-target': '#id_materialdelstate',
                'hx-trigger': 'change',
                'hx-include': '[name="materialdelcountry"]',
                'x-on:change': 'clearMaterialStatesAndCities()',
            }),
            
            'materialdelstate': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white',
                'x-model': 'form.materialdelstate',
                'hx-get': '/sales-distribution/ajax/get-cities/',
                'hx-target': '#id_materialdelcity',
                'hx-trigger': 'change',
                'hx-include': '[name="materialdelstate"]',
                'x-on:change': 'clearMaterialCities()',
            }),
            
            'materialdelcity': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white',
                'x-model': 'form.materialdelcity',
            }),
            
            'materialdelpinno': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors',
                'placeholder': 'PIN Code',
                'maxlength': '6',
                'x-model': 'form.materialdelpinno',
                'pattern': '[0-9]{6}',
            }),
            
            'materialdelcontactno': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors',
                'placeholder': 'Contact number',
                'x-model': 'form.materialdelcontactno',
            }),
            
            'materialdelfaxno': forms.HiddenInput(),
            
            # Business Information Section
            'juridictioncode': forms.HiddenInput(),
            
            'commissionurate': forms.HiddenInput(),
            
            'tinvatno': forms.HiddenInput(),
            
            'eccno': forms.HiddenInput(),
            
            'divn': forms.HiddenInput(),
            
            'tincstno': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors',
                'placeholder': 'TIN/CST number',
                'x-model': 'form.tincstno',
            }),
            
            'range': forms.HiddenInput(),
            
            'panno': forms.HiddenInput(),
            
            'tdscode': forms.HiddenInput(),
            
            # Enquiry Details Section
            'enquiryfor': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none',
                'placeholder': 'Describe your enquiry in detail...',
                'rows': 4,
                'x-model': 'form.enquiryfor',
                'required': True,
            }),
            
            'remark': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none',
                'placeholder': 'Additional remarks...',
                'rows': 3,
                'x-model': 'form.remark',
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set all hidden fields to not required
        hidden_fields = [
            'regdfaxno', 'workfaxno', 'materialdelfaxno',
            'juridictioncode', 'commissionurate', 'tinvatno',
            'eccno', 'divn', 'range', 'panno', 'tdscode'
        ]
        
        for field_name in hidden_fields:
            if field_name in self.fields:
                self.fields[field_name].required = False
        
        # Initialize country choices for all address sections
        self._populate_country_choices()
        
        # Initialize empty state and city choices
        self._initialize_empty_choices()
        
        # If editing existing enquiry, populate dependent dropdowns
        if self.instance and self.instance.pk:
            self._populate_existing_data()
    
    def _populate_country_choices(self):
        """Populate country dropdown choices"""
        countries = Country.objects.all().order_by('countryname')
        country_choices = [('', '🌍 Select Country')] + [
            (c.cid, f"🏳️ {c.countryname}") for c in countries
        ]
        
        self.fields['regdcountry'].choices = country_choices
        self.fields['workcountry'].choices = country_choices
        self.fields['materialdelcountry'].choices = country_choices
    
    def _initialize_empty_choices(self):
        """Initialize empty state and city choices"""
        empty_state_choices = [('', '📍 Select State')]
        empty_city_choices = [('', '🏙️ Select City')]
        
        # State choices
        self.fields['regdstate'].choices = empty_state_choices
        self.fields['workstate'].choices = empty_state_choices
        self.fields['materialdelstate'].choices = empty_state_choices
        
        # City choices
        self.fields['regdcity'].choices = empty_city_choices
        self.fields['workcity'].choices = empty_city_choices
        self.fields['materialdelcity'].choices = empty_city_choices
    
    def _populate_existing_data(self):
        """Populate dependent dropdowns for existing enquiry"""
        # Registered address
        if self.instance.regdcountry:
            states = State.objects.filter(cid=self.instance.regdcountry).order_by('statename')
            self.fields['regdstate'].choices = [('', '📍 Select State')] + [
                (s.sid, f"📍 {s.statename}") for s in states
            ]
            
            if self.instance.regdstate:
                cities = City.objects.filter(sid=self.instance.regdstate).order_by('cityname')
                self.fields['regdcity'].choices = [('', '🏙️ Select City')] + [
                    (c.cityid, f"🏙️ {c.cityname}") for c in cities
                ]
        
        # Works address
        if self.instance.workcountry:
            states = State.objects.filter(cid=self.instance.workcountry).order_by('statename')
            self.fields['workstate'].choices = [('', '📍 Select State')] + [
                (s.sid, f"📍 {s.statename}") for s in states
            ]
            
            if self.instance.workstate:
                cities = City.objects.filter(sid=self.instance.workstate).order_by('cityname')
                self.fields['workcity'].choices = [('', '🏙️ Select City')] + [
                    (c.cityid, f"🏙️ {c.cityname}") for c in cities
                ]
        
        # Material delivery address
        if self.instance.materialdelcountry:
            states = State.objects.filter(cid=self.instance.materialdelcountry).order_by('statename')
            self.fields['materialdelstate'].choices = [('', '📍 Select State')] + [
                (s.sid, f"📍 {s.statename}") for s in states
            ]
            
            if self.instance.materialdelstate:
                cities = City.objects.filter(sid=self.instance.materialdelstate).order_by('cityname')
                self.fields['materialdelcity'].choices = [('', '🏙️ Select City')] + [
                    (c.cityid, f"🏙️ {c.cityname}") for c in cities
                ]

    def clean_customername(self):
        """Validate customer name"""
        name = self.cleaned_data.get('customername')
        if not name or not name.strip():
            raise ValidationError("Customer name is required.")
        return name.strip().upper()

    def clean_email(self):
        """Enhanced email validation"""
        email = self.cleaned_data.get('email')
        if email:
            # Use Django's built-in email validator
            validator = EmailValidator()
            try:
                validator(email)
            except ValidationError:
                raise ValidationError("Please enter a valid email address.")
        return email

    def clean_panno(self):
        """Validate PAN number format"""
        pan = self.cleaned_data.get('panno')
        if pan:
            pan = pan.upper().strip()
            # PAN format: **********
            pan_pattern = r'^[A-Z]{5}[0-9]{4}[A-Z]{1}$'
            if not re.match(pan_pattern, pan):
                raise ValidationError("PAN number format should be: **********")
        return pan

    def clean_regdpinno(self):
        """Validate PIN code format"""
        pin = self.cleaned_data.get('regdpinno')
        if pin:
            pin = pin.strip()
            if not pin.isdigit() or len(pin) != 6:
                raise ValidationError("PIN code should be 6 digits.")
        return pin

    def clean_workpinno(self):
        """Validate works PIN code format"""
        pin = self.cleaned_data.get('workpinno')
        if pin:
            pin = pin.strip()
            if not pin.isdigit() or len(pin) != 6:
                raise ValidationError("PIN code should be 6 digits.")
        return pin

    def clean_materialdelpinno(self):
        """Validate material delivery PIN code format"""
        pin = self.cleaned_data.get('materialdelpinno')
        if pin:
            pin = pin.strip()
            if not pin.isdigit() or len(pin) != 6:
                raise ValidationError("PIN code should be 6 digits.")
        return pin

    def clean_enquiryfor(self):
        """Validate enquiry description"""
        enquiry = self.cleaned_data.get('enquiryfor')
        if not enquiry or not enquiry.strip():
            raise ValidationError("Enquiry description is required.")
        return enquiry.strip()


class ExistingCustomerSelectionForm(forms.Form):
    """
    Form for selecting existing customer with enhanced autocomplete
    """
    existing_customer = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors',
            'placeholder': 'Start typing customer name...',
            'x-model': 'existingCustomerName',
            'x-on:input.debounce.300ms': 'searchCustomers($event.target.value)',
            'x-on:focus': 'showCustomerDropdown = true',
            'autocomplete': 'off',
        }),
        label='Search Existing Customer'
    )


class EnquiryFilterForm(forms.Form):
    """
    Enhanced filter form for enquiry list with modern SAP-like controls
    """
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500 transition-colors',
            'placeholder': 'Search enquiries...',
            'x-model': 'filters.search',
            'x-on:input.debounce.300ms': 'applyFilters()',
        }),
        label='Search'
    )
    
    status = forms.ChoiceField(
        choices=[
            ('', 'All Status'),
            ('new', '🆕 New'),
            ('in_progress', '⏳ In Progress'),
            ('quoted', '💰 Quoted'),
            ('converted', '✅ Converted'),
        ],
        required=False,
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500 transition-colors bg-white',
            'x-model': 'filters.status',
            'x-on:change': 'applyFilters()',
        }),
        label='Status'
    )
    
    country = forms.ModelChoiceField(
        queryset=Country.objects.all().order_by('countryname'),
        required=False,
        empty_label='🌍 All Countries',
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500 transition-colors bg-white',
            'x-model': 'filters.country',
            'x-on:change': 'applyFilters()',
        }),
        label='Country'
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500 transition-colors',
            'x-model': 'filters.dateFrom',
            'x-on:change': 'applyFilters()',
        }),
        label='From Date'
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500 transition-colors',
            'x-model': 'filters.dateTo',
            'x-on:change': 'applyFilters()',
        }),
        label='To Date'
    )
