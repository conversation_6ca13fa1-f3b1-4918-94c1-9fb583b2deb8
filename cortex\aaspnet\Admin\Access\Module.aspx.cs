﻿using System;
using System.Collections;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Xml.Linq;
using System.Data.SqlClient;

public partial class Admin_Access_Module : System.Web.UI.Page
{
    clsFunctions fun = new clsFunctions();
    
    protected void Page_Load(object sender, EventArgs e)
    {

    }
    protected void btnSave_Click(object sender, EventArgs e)
    {
        
        string connStr = fun.Connection();
    SqlConnection con = new SqlConnection(connStr);
        
        try
        {
            con.Open();
            string cmdstr = fun.insert("tblModule_Master",
                 "ModName,DashBoardPage", "'" +TxtModuleName.Text+ "','" +TxtLinkPage.Text+ "'");
            SqlCommand cmd = new SqlCommand(cmdstr, con);
            cmd.ExecuteNonQuery();
            Page.Response.Redirect(Page.Request.Url.ToString(), true);

        }
        catch( Exception ex)
        {

        }
        finally
        {
            //con.close();
        }

    }
    protected void Button1_Click(object sender, EventArgs e)
    {
        Response.Redirect("../Menu.aspx");
    }
}
