from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy, reverse
from django.db.models import Q
from django.http import JsonResponse
from django.utils import timezone
from sys_admin.models import Company, FinancialYear
from ..models import (
    MaterialIssueNote, MaterialReturnNote, MaterialRequisitionSlip
)
from ..forms.min_mrn_forms import (
    MaterialIssueNoteForm, MaterialReturnNoteForm, 
    MRNSearchForm
)


def get_current_company(request=None):
    """Get current company (first one for now)"""
    return Company.objects.first()


def get_current_financial_year(request=None):
    """Get current financial year (first one for now)"""
    return FinancialYear.objects.first()


# Material Issue Note (MIN) Views

class MINListView(LoginRequiredMixin, ListView):
    """List view for Material Issue Notes with real-time search"""
    model = MaterialIssueNote
    template_name = 'inventory/transactions/min_list.html'
    context_object_name = 'min_list'
    paginate_by = 20

    def get_queryset(self):
        queryset = MaterialIssueNote.objects.select_related(
            'company', 'financial_year'
        ).prefetch_related('details')
        
        # Apply filters from search form
        search = self.request.GET.get('search')
        
        if search:
            queryset = queryset.filter(
                Q(min_no__icontains=search) |
                Q(mrs_no__icontains=search) |
                Q(sessionid__icontains=search)
            )
        
        return queryset.order_by('-id')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Statistics for dashboard
        total_min = MaterialIssueNote.objects.count()
        
        context['stats'] = {
            'total_min': total_min,
        }
        
        return context

    def render_to_response(self, context, **response_kwargs):
        if self.request.headers.get('HX-Request'):
            return render(self.request, 'inventory/transactions/partials/min_results.html', context)
        return super().render_to_response(context, **response_kwargs)


class MINDetailView(LoginRequiredMixin, DetailView):
    """Detail view for Material Issue Note"""
    model = MaterialIssueNote
    template_name = 'inventory/transactions/min_detail.html'
    context_object_name = 'min'

    def get_queryset(self):
        return MaterialIssueNote.objects.select_related(
            'company', 'financial_year'
        ).prefetch_related('details')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        min_obj = self.get_object()
        
        # Check if user can edit this MIN
        context['can_edit'] = True  # Simplified for now
        
        return context


class MINCreateView(LoginRequiredMixin, CreateView):
    """Create view for Material Issue Note"""
    model = MaterialIssueNote
    form_class = MaterialIssueNoteForm
    template_name = 'inventory/transactions/min_form.html'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        kwargs['company'] = get_current_company(self.request)
        kwargs['financial_year'] = get_current_financial_year(self.request)
        return kwargs

    def form_valid(self, form):
        messages.success(self.request, 'Material Issue Note created successfully.')
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('inventory:min_detail', kwargs={'pk': self.object.pk})


class MINUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for Material Issue Note"""
    model = MaterialIssueNote
    form_class = MaterialIssueNoteForm
    template_name = 'inventory/transactions/min_form.html'

    def get_queryset(self):
        # Simplified - return all MIN records
        return MaterialIssueNote.objects.all()

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        kwargs['company'] = self.object.company
        kwargs['financial_year'] = self.object.financial_year
        return kwargs

    def form_valid(self, form):
        messages.success(self.request, 'Material Issue Note updated successfully.')
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('inventory:min_detail', kwargs={'pk': self.object.pk})


class MINDeleteView(LoginRequiredMixin, DeleteView):
    """Delete view for Material Issue Note"""
    model = MaterialIssueNote
    template_name = 'inventory/transactions/min_confirm_delete.html'
    success_url = reverse_lazy('inventory:min_list')

    def get_queryset(self):
        # Simplified - allow deletion for all MIN records
        return MaterialIssueNote.objects.all()

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Material Issue Note deleted successfully.')
        return super().delete(request, *args, **kwargs)


# Material Return Note (MRN) Views

class MRNListView(LoginRequiredMixin, ListView):
    """List view for Material Return Notes with real-time search"""
    model = MaterialReturnNote
    template_name = 'inventory/transactions/mrn_list.html'
    context_object_name = 'mrn_list'
    paginate_by = 20

    def get_queryset(self):
        queryset = MaterialReturnNote.objects.select_related(
            'company', 'financial_year'
        )
        
        # Apply filters from search form
        search_field = self.request.GET.get('search_field', 'employee_name')
        search_value = self.request.GET.get('search_value', '')
        
        if search_value:
            if search_field == 'employee_name':
                queryset = queryset.filter(sessionid__icontains=search_value)
            elif search_field == 'mrn_no':
                queryset = queryset.filter(mrn_no__icontains=search_value)
        
        return queryset.order_by('-id')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = MRNSearchForm(self.request.GET)
        
        # Statistics for dashboard
        total_mrn = MaterialReturnNote.objects.count()
        
        context['stats'] = {
            'total_mrn': total_mrn,
        }
        
        # Get unique employee names for dropdown (from sessionid)
        employee_names = MaterialReturnNote.objects.values_list('sessionid', flat=True).distinct()
        context['employee_names'] = [name for name in employee_names if name]
        
        return context

    def render_to_response(self, context, **response_kwargs):
        if self.request.headers.get('HX-Request'):
            return render(self.request, 'inventory/transactions/partials/mrn_results.html', context)
        return super().render_to_response(context, **response_kwargs)


class MRNDetailView(LoginRequiredMixin, DetailView):
    """Detail view for Material Return Note"""
    model = MaterialReturnNote
    template_name = 'inventory/transactions/mrn_detail.html'
    context_object_name = 'mrn'

    def get_queryset(self):
        return MaterialReturnNote.objects.select_related(
            'company', 'financial_year'
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        mrn = self.get_object()
        
        # Check if user can edit this MRN
        context['can_edit'] = True  # Simplified for now
        
        return context


class MRNCreateView(LoginRequiredMixin, CreateView):
    """Create view for Material Return Note"""
    model = MaterialReturnNote
    form_class = MaterialReturnNoteForm
    template_name = 'inventory/transactions/mrn_form.html'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        kwargs['company'] = get_current_company(self.request)
        kwargs['financial_year'] = get_current_financial_year(self.request)
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Add sample items for the item master tab
        # In a real implementation, you would load from ItemMaster model
        context['sample_items'] = [
            {
                'item_code': '00901-02-010',
                'description': 'DIABOLA ROLLER',
                'uom': 'KGS',
                'business_group': 'Production'
            },
            {
                'item_code': '00902-03-020',
                'description': 'STEEL PLATE',
                'uom': 'KGS',
                'business_group': 'Materials'
            },
            {
                'item_code': '00903-04-030',
                'description': 'BEARING ASSEMBLY',
                'uom': 'NOS',
                'business_group': 'Components'
            },
            {
                'item_code': '00904-05-040',
                'description': 'HYDRAULIC CYLINDER',
                'uom': 'NOS',
                'business_group': 'Hydraulics'
            },
            {
                'item_code': '00905-06-050',
                'description': 'MOTOR ASSEMBLY',
                'uom': 'NOS',
                'business_group': 'Electrical'
            }
        ]
        
        return context

    def form_valid(self, form):
        messages.success(self.request, 'Material Return Note created successfully.')
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('inventory:mrn_detail', kwargs={'pk': self.object.pk})


class MRNUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for Material Return Note"""
    model = MaterialReturnNote
    form_class = MaterialReturnNoteForm
    template_name = 'inventory/transactions/mrn_form.html'

    def get_queryset(self):
        # Simplified - return all MRN records
        return MaterialReturnNote.objects.all()

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        kwargs['company'] = self.object.company
        kwargs['financial_year'] = self.object.financial_year
        return kwargs

    def form_valid(self, form):
        messages.success(self.request, 'Material Return Note updated successfully.')
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('inventory:mrn_detail', kwargs={'pk': self.object.pk})


class MRNDeleteView(LoginRequiredMixin, DeleteView):
    """Delete view for Material Return Note"""
    model = MaterialReturnNote
    template_name = 'inventory/transactions/mrn_confirm_delete.html'
    success_url = reverse_lazy('inventory:mrn_list')

    def get_queryset(self):
        # Simplified - allow deletion for all MRN records
        return MaterialReturnNote.objects.all()

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Material Return Note deleted successfully.')
        return super().delete(request, *args, **kwargs)


class MRNEditDetailsView(LoginRequiredMixin, DetailView):
    """MRN Edit Details View - matching ASP.NET interface for editing MRN items"""
    model = MaterialReturnNote
    template_name = 'inventory/transactions/mrn_edit_details.html'
    context_object_name = 'mrn'

    def get_queryset(self):
        return MaterialReturnNote.objects.select_related(
            'company', 'financial_year'
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        mrn = self.get_object()
        
        # Get MRN details from MaterialReturnDetail model
        from ..models import MaterialReturnDetail
        mrn_details = MaterialReturnDetail.objects.filter(mid=mrn.id)
        
        # Prepare items data for the template
        mrn_items = []
        for detail in mrn_details:
            mrn_items.append({
                'detail': detail,
                'item_code': detail.itemid or 'N/A',
                'description': 'DIABOLA ROLLER',  # TODO: Get from item master
                'uom': 'KGS',  # TODO: Get from item master
                'bg_group': detail.deptid or '',
                'wo_no': detail.wono or '',
                'return_qty': detail.retqty or 0,
                'remarks': detail.remarks or '',
            })
        
        context['mrn_items'] = mrn_items
        return context

    def post(self, request, *args, **kwargs):
        """Handle MRN item updates"""
        mrn = self.get_object()
        
        # TODO: Process form data to update MRN items
        # This would involve:
        # 1. Validating submitted item data
        # 2. Updating MaterialReturnDetail records
        # 3. Recalculating MRN totals
        
        messages.success(request, f'MRN {mrn.mrn_no} updated successfully.')
        return redirect('inventory:mrn_list')


# API and utility views

def min_issue_view(request, pk):
    """Issue MIN - change status from draft to issued"""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)
    
    min_obj = get_object_or_404(MaterialIssueNote, pk=pk)
    
    # Check permissions (simplified)
    if not request.user.has_perm('inventory.change_materialissue'):
        return JsonResponse({'error': 'Permission denied'}, status=403)
    
    # Issue MIN (simplified)
    if not min_obj.details.exists():
        return JsonResponse({'error': 'Cannot issue MIN without line items'}, status=400)
    
    messages.success(request, f'MIN {min_obj.min_no} issued successfully.')
    
    if request.headers.get('HX-Request'):
        return JsonResponse({'success': True, 'message': 'MIN issued successfully'})
    
    return redirect('inventory:min_detail', pk=min_obj.pk)


def mrn_process_view(request, pk):
    """Process MRN - simplified version"""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)
    
    mrn = get_object_or_404(MaterialReturnNote, pk=pk)
    
    # Check permissions (simplified)
    if not request.user.has_perm('inventory.change_materialreturn'):
        return JsonResponse({'error': 'Permission denied'}, status=403)
    
    messages.success(request, f'MRN {mrn.mrn_no} processed successfully.')
    
    if request.headers.get('HX-Request'):
        return JsonResponse({'success': True, 'message': 'MRN processed successfully'})
    
    return redirect('inventory:mrn_detail', pk=mrn.pk)


def min_print_view(request, pk):
    """Print view for MIN"""
    min_obj = get_object_or_404(MaterialIssueNote, pk=pk)
    
    context = {
        'min': min_obj,
        'company': min_obj.company,
        'print_date': timezone.now()
    }
    
    return render(request, 'inventory/transactions/min_print.html', context)


def mrn_print_view(request, pk):
    """Print view for MRN"""
    mrn = get_object_or_404(MaterialReturnNote, pk=pk)
    
    context = {
        'mrn': mrn,
        'company': mrn.company,
        'print_date': timezone.now()
    }
    
    return render(request, 'inventory/transactions/mrn_print.html', context)


# MIN New Views (matching ASP.NET interface)

class MINNewSearchView(LoginRequiredMixin, ListView):
    """MIN New Search - Search MRS records to create MIN from (matching ASP.NET)"""
    model = MaterialRequisitionSlip
    template_name = 'inventory/transactions/min_new_search.html'
    context_object_name = 'mrs_list'
    paginate_by = 20

    def get_queryset(self):
        """Get approved MRS records that can be used to create MINs"""
        queryset = MaterialRequisitionSlip.objects.select_related(
            'company', 'financial_year'
        ).prefetch_related('details')
        
        # Apply search filters
        employee_search = self.request.GET.get('employee_name', '').strip()
        mrs_search = self.request.GET.get('mrs_no', '').strip()
        
        if employee_search:
            # Search by employee name (assuming session_id contains employee info)
            queryset = queryset.filter(session_id__icontains=employee_search)
        
        if mrs_search:
            queryset = queryset.filter(mrs_number__icontains=mrs_search)
        
        # Only show approved MRS records (simplified - you might need to add status field)
        return queryset.order_by('-id')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get unique employee names for dropdown (from session_id)
        employee_names = MaterialRequisitionSlip.objects.values_list('session_id', flat=True).distinct()
        context['employee_names'] = [name for name in employee_names if name]
        
        context['employee_search'] = self.request.GET.get('employee_name', '')
        context['mrs_search'] = self.request.GET.get('mrs_no', '')
        
        return context


class MINNewDetailsView(LoginRequiredMixin, DetailView):
    """MIN New Details - Show MRS items for selective issuance (matching ASP.NET)"""
    model = MaterialRequisitionSlip
    template_name = 'inventory/transactions/min_new_details.html'
    context_object_name = 'mrs'
    pk_url_kwarg = 'mrs_id'

    def get_queryset(self):
        return MaterialRequisitionSlip.objects.select_related(
            'company', 'financial_year'
        ).prefetch_related('details')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        mrs = self.get_object()
        
        # Get MRS line items with additional details
        mrs_items = []
        for detail in mrs.details.all():
            # Get stock quantity (simplified - you might need proper stock calculation)
            stock_qty = 0  # TODO: Calculate actual stock from inventory
            
            mrs_items.append({
                'detail': detail,
                'item_code': detail.item_id or 'N/A',
                'description': 'Sample Item Description',  # TODO: Get from item master
                'uom': 'KGS',  # TODO: Get from item master
                'stock_qty': stock_qty,
                'req_qty': detail.requested_quantity or 0,
                'issued_qty': 0,  # Will be filled by user
            })
        
        context['mrs_items'] = mrs_items
        return context

    def post(self, request, *args, **kwargs):
        """Handle MIN generation from selected items"""
        mrs = self.get_object()
        
        # TODO: Process selected items and create MIN
        # This would involve:
        # 1. Validating selected items and quantities
        # 2. Creating MaterialIssueNote record
        # 3. Creating MINLineItem records
        # 4. Updating stock quantities
        
        messages.success(request, f'MIN generated successfully from MRS {mrs.mrs_number}')
        return redirect('inventory:min_list')


class MINEditListView(LoginRequiredMixin, ListView):
    """MIN Edit List - Search and manage existing MINs (matching ASP.NET)"""
    model = MaterialIssueNote
    template_name = 'inventory/transactions/min_edit_list.html'
    context_object_name = 'min_list'
    paginate_by = 20

    def get_queryset(self):
        """Get MIN records with search functionality"""
        queryset = MaterialIssueNote.objects.select_related(
            'company', 'financial_year'
        ).prefetch_related('details')
        
        # Apply search filters
        search_type = self.request.GET.get('search_type', 'mrs_no')
        search_value = self.request.GET.get('search_value', '').strip()
        
        if search_value:
            if search_type == 'mrs_no':
                queryset = queryset.filter(mrs_no__icontains=search_value)
            elif search_type == 'min_no':
                queryset = queryset.filter(min_no__icontains=search_value)
            elif search_type == 'employee_name':
                queryset = queryset.filter(sessionid__icontains=search_value)
        
        return queryset.order_by('-id')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Search form data
        context['search_type'] = self.request.GET.get('search_type', 'mrs_no')
        context['search_value'] = self.request.GET.get('search_value', '')
        
        # Search type options
        context['search_options'] = [
            ('mrs_no', 'MRS No'),
            ('min_no', 'MIN No'),
            ('employee_name', 'Employee Name'),
        ]
        
        return context


def min_mrn_statistics_api(request):
    """API endpoint for MIN/MRN statistics"""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)
    
    stats = {
        'min_stats': {
            'total_min': MaterialIssueNote.objects.count(),
        },
        'mrn_stats': {
            'total_mrn': MaterialReturnNote.objects.count(),
        },
        'recent_activity': {
            'recent_mins': MaterialIssueNote.objects.order_by('-id')[:5].values(
                'min_no', 'sysdate', 'sessionid'
            ),
        }
    }
    
    return JsonResponse(stats)