<!-- accounts/templates/accounts/masters/sundry_creditor_list.html -->
<!-- Sundry Creditor List View Template -->
<!-- Task Package 4: Customer & Creditor Management Templates - Sundry Creditor List -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Sundry Creditors - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-blue-600 to-sap-blue-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="users" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Sundry Creditors</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage vendor relationships and outstanding balances</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to Dashboard
                </a>
                <a href="{% url 'accounts:sundry_creditor_create' %}" 
                   class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    New Creditor
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Search and Filter Section -->
    <div class="mb-6 bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4">
            <form method="get" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4" 
                  hx-get="{% url 'accounts:sundry_creditor_list' %}" 
                  hx-target="#creditor-table-container" 
                  hx-trigger="keyup changed delay:300ms from:input, change from:select"
                  hx-push-url="true">
                
                <!-- Search -->
                <div class="flex-1">
                    <label for="search" class="block text-sm font-medium text-sap-gray-700 mb-1">Search</label>
                    <input type="text" name="search" value="{{ request.GET.search }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"
                           placeholder="Search by creditor name, code, contact person, email...">
                </div>
                
                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-sap-gray-700 mb-1">Status</label>
                    <select name="status" 
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        <option value="">All Status</option>
                        <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>Active</option>
                        <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>Inactive</option>
                    </select>
                </div>
                
                <!-- City Filter -->
                <div>
                    <label for="city" class="block text-sm font-medium text-sap-gray-700 mb-1">City</label>
                    <input type="text" name="city" value="{{ request.GET.city }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"
                           placeholder="Filter by city...">
                </div>
                
                <!-- Clear Button -->
                {% if request.GET %}
                <div>
                    <a href="{% url 'accounts:sundry_creditor_list' %}" 
                       class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                        Clear
                    </a>
                </div>
                {% endif %}
            </form>
        </div>
    </div>

    <!-- Creditor Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="users" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Creditors</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ total_creditors|default:0 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="check-circle" class="w-6 h-6 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Active</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ active_creditors|default:0 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="credit-card" class="w-6 h-6 text-sap-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Credit Limit</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ total_credit_limit|floatformat:2|default:"0.00" }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="trending-up" class="w-6 h-6 text-sap-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Outstanding Balance</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ total_opening_balance|floatformat:2|default:"0.00" }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Creditors Table Container -->
    <div id="creditor-table-container">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-sap-gray-800">Sundry Creditors</h3>
                    <div class="flex items-center space-x-2">
                        <button type="button" onclick="exportCreditors()" 
                                class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                            Export
                        </button>
                        <button type="button" onclick="bulkActions()" 
                                class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="layers" class="w-4 h-4 inline mr-2"></i>
                            Bulk Actions
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-sap-gray-200">
                    <thead class="bg-sap-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                <input type="checkbox" id="select-all" class="rounded border-sap-gray-300 text-sap-blue-600 focus:ring-sap-blue-500">
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Creditor Details
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Contact Information
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Financial Details
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-sap-gray-200">
                        {% for creditor in sundry_creditors %}
                        <tr class="hover:bg-sap-gray-50 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" name="selected_creditors" value="{{ creditor.id }}" 
                                       class="rounded border-sap-gray-300 text-sap-blue-600 focus:ring-sap-blue-500">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-sap-blue-400 to-sap-blue-600 rounded-lg flex items-center justify-center text-white font-semibold text-sm">
                                        {{ creditor.creditor_name|first|upper|default:"C" }}
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-sap-gray-900">
                                            <a href="{% url 'accounts:sundry_creditor_detail' creditor.id %}" 
                                               class="hover:text-sap-blue-600 transition-colors">
                                                {{ creditor.creditor_name|default:"N/A" }}
                                            </a>
                                        </div>
                                        <div class="text-sm text-sap-gray-500">{{ creditor.creditor_code|default:"N/A" }}</div>
                                        <div class="text-xs text-sap-gray-400">{{ creditor.contact_person|default:"N/A" }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-sap-gray-900">
                                    {% if creditor.email %}
                                        <div class="flex items-center mb-1">
                                            <i data-lucide="mail" class="w-3 h-3 mr-1 text-sap-gray-400"></i>
                                            <a href="mailto:{{ creditor.email }}" class="hover:text-sap-blue-600">{{ creditor.email }}</a>
                                        </div>
                                    {% endif %}
                                    {% if creditor.mobile %}
                                        <div class="flex items-center mb-1">
                                            <i data-lucide="phone" class="w-3 h-3 mr-1 text-sap-gray-400"></i>
                                            <a href="tel:{{ creditor.mobile }}" class="hover:text-sap-blue-600">{{ creditor.mobile }}</a>
                                        </div>
                                    {% endif %}
                                    {% if creditor.city %}
                                        <div class="flex items-center">
                                            <i data-lucide="map-pin" class="w-3 h-3 mr-1 text-sap-gray-400"></i>
                                            <span class="text-xs">{{ creditor.city.city_name }}</span>
                                        </div>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-sap-gray-900">
                                    <div class="flex items-center justify-between mb-1">
                                        <span class="text-xs text-sap-gray-500">Credit Limit:</span>
                                        <span class="font-medium">₹{{ creditor.credit_limit|floatformat:2|default:"0.00" }}</span>
                                    </div>
                                    <div class="flex items-center justify-between mb-1">
                                        <span class="text-xs text-sap-gray-500">Current Balance:</span>
                                        <span class="font-medium {% if creditor.current_balance_type == 'Debit' %}text-sap-red-600{% else %}text-sap-green-600{% endif %}">
                                            ₹{{ creditor.current_balance|floatformat:2|default:"0.00" }}
                                        </span>
                                    </div>
                                    {% if creditor.payment_terms %}
                                        <div class="text-xs text-sap-gray-400">{{ creditor.payment_terms|truncatechars:20 }}</div>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if creditor.is_active %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-green-100 text-sap-green-800">
                                        <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                        Active
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-red-100 text-sap-red-800">
                                        <i data-lucide="x-circle" class="w-3 h-3 mr-1"></i>
                                        Inactive
                                    </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <!-- Quick Actions Dropdown -->
                                    <div class="relative" x-data="{ open: false }">
                                        <button @click="open = !open" 
                                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-3 py-1 rounded-lg text-xs font-medium transition-colors">
                                            <i data-lucide="more-horizontal" class="w-4 h-4 inline mr-1"></i>
                                            Actions
                                        </button>
                                        <div x-show="open" @click.away="open = false" 
                                             class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-sap-gray-200 z-10"
                                             x-transition:enter="transition ease-out duration-200"
                                             x-transition:enter-start="opacity-0 scale-95"
                                             x-transition:enter-end="opacity-100 scale-100"
                                             x-transition:leave="transition ease-in duration-75"
                                             x-transition:leave-start="opacity-100 scale-100"
                                             x-transition:leave-end="opacity-0 scale-95">
                                            <div class="py-1">
                                                <a href="{% url 'accounts:sundry_creditor_detail' creditor.id %}" 
                                                   class="flex items-center px-4 py-2 text-sm text-sap-gray-700 hover:bg-sap-gray-100">
                                                    <i data-lucide="eye" class="w-4 h-4 mr-2"></i>
                                                    View Details
                                                </a>
                                                <a href="{% url 'accounts:sundry_creditor_edit' creditor.id %}" 
                                                   class="flex items-center px-4 py-2 text-sm text-sap-gray-700 hover:bg-sap-gray-100">
                                                    <i data-lucide="edit" class="w-4 h-4 mr-2"></i>
                                                    Edit
                                                </a>
                                                <a href="{% url 'accounts:sundry_creditor_transactions' creditor.id %}" 
                                                   class="flex items-center px-4 py-2 text-sm text-sap-gray-700 hover:bg-sap-gray-100">
                                                    <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                                                    View Transactions
                                                </a>
                                                <button type="button" onclick="makePayment({{ creditor.id }})" 
                                                        class="flex items-center w-full px-4 py-2 text-sm text-sap-gray-700 hover:bg-sap-gray-100">
                                                    <i data-lucide="credit-card" class="w-4 h-4 mr-2"></i>
                                                    Make Payment
                                                </button>
                                                <button type="button" onclick="sendEmail({{ creditor.id }})" 
                                                        class="flex items-center w-full px-4 py-2 text-sm text-sap-gray-700 hover:bg-sap-gray-100">
                                                    <i data-lucide="mail" class="w-4 h-4 mr-2"></i>
                                                    Send Email
                                                </button>
                                                <div class="border-t border-sap-gray-100"></div>
                                                <a href="{% url 'accounts:sundry_creditor_delete' creditor.id %}" 
                                                   class="flex items-center px-4 py-2 text-sm text-sap-red-600 hover:bg-sap-red-50">
                                                    <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
                                                    Delete
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center justify-center">
                                    <i data-lucide="users" class="w-12 h-12 text-sap-gray-400 mb-4"></i>
                                    <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No creditors found</h3>
                                    <p class="text-sap-gray-500 mb-4">Get started by adding your first creditor.</p>
                                    <a href="{% url 'accounts:sundry_creditor_create' %}" 
                                       class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                        <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                                        Add First Creditor
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
            <div class="px-6 py-4 border-t border-sap-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <p class="text-sm text-sap-gray-700">
                            Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} creditors
                        </p>
                    </div>
                    <div class="flex items-center space-x-2">
                        {% if page_obj.has_previous %}
                            <a href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.city %}city={{ request.GET.city }}&{% endif %}page={{ page_obj.previous_page_number }}"
                               class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-3 py-2 rounded-lg text-sm font-medium transition-colors">
                                Previous
                            </a>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <span class="bg-sap-blue-600 text-white px-3 py-2 rounded-lg text-sm font-medium">{{ num }}</span>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <a href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.city %}city={{ request.GET.city }}&{% endif %}page={{ num }}"
                                   class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-3 py-2 rounded-lg text-sm font-medium transition-colors">{{ num }}</a>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <a href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.city %}city={{ request.GET.city }}&{% endif %}page={{ page_obj.next_page_number }}"
                               class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-3 py-2 rounded-lg text-sm font-medium transition-colors">
                                Next
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- JavaScript for Interactive Features -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select All Functionality
    const selectAllCheckbox = document.getElementById('select-all');
    const creditorCheckboxes = document.querySelectorAll('input[name="selected_creditors"]');
    
    selectAllCheckbox?.addEventListener('change', function() {
        creditorCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
    
    // Export functionality
    window.exportCreditors = function() {
        const selectedCreditors = Array.from(document.querySelectorAll('input[name="selected_creditors"]:checked'))
                                       .map(cb => cb.value);
        
        if (selectedCreditors.length === 0) {
            alert('Please select creditors to export');
            return;
        }
        
        // Create export form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{% url "accounts:sundry_creditor_export" %}';
        
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value;
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrfmiddlewaretoken';
            csrfInput.value = csrfToken;
            form.appendChild(csrfInput);
        }
        
        selectedCreditors.forEach(id => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'creditor_ids';
            input.value = id;
            form.appendChild(input);
        });
        
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    };
    
    // Bulk actions functionality
    window.bulkActions = function() {
        const selectedCreditors = Array.from(document.querySelectorAll('input[name="selected_creditors"]:checked'))
                                       .map(cb => cb.value);
        
        if (selectedCreditors.length === 0) {
            alert('Please select creditors for bulk actions');
            return;
        }
        
        // Show bulk actions modal or dropdown
        alert(`Selected ${selectedCreditors.length} creditors for bulk actions`);
    };
    
    // Make payment functionality
    window.makePayment = function(creditorId) {
        // Redirect to payment form or show payment modal
        window.location.href = `/accounts/payments/create/?creditor_id=${creditorId}`;
    };
    
    // Send email functionality
    window.sendEmail = function(creditorId) {
        // Redirect to email composition or show email modal
        window.location.href = `/accounts/communication/email/?creditor_id=${creditorId}`;
    };
});
</script>
{% endblock %}