<!-- Customer Table Partial for HTMX Updates -->
<div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-sap-gray-200">
        <thead class="bg-sap-gray-50">
            <tr>
                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    <div class="flex items-center space-x-1">
                        <i data-lucide="hash" class="w-4 h-4"></i>
                        <span>Customer ID</span>
                    </div>
                </th>
                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    <div class="flex items-center space-x-1">
                        <i data-lucide="user" class="w-4 h-4"></i>
                        <span>Customer Name</span>
                    </div>
                </th>
                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    <div class="flex items-center space-x-1">
                        <i data-lucide="map-pin" class="w-4 h-4"></i>
                        <span>Address</span>
                    </div>
                </th>
                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    <div class="flex items-center space-x-1">
                        <i data-lucide="phone" class="w-4 h-4"></i>
                        <span>Contact</span>
                    </div>
                </th>
                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    <div class="flex items-center space-x-1">
                        <i data-lucide="mail" class="w-4 h-4"></i>
                        <span>Email</span>
                    </div>
                </th>
                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    <div class="flex items-center space-x-1">
                        <i data-lucide="calendar" class="w-4 h-4"></i>
                        <span>Date Added</span>
                    </div>
                </th>
                <th scope="col" class="px-6 py-4 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    Actions
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-sap-gray-200">
            {% for customer in customers %}
            <tr class="hover:bg-sap-gray-50 transition-colors duration-200" id="customer-row-{{ customer.salesid }}">
                <!-- Customer ID -->
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10">
                            <div class="h-10 w-10 rounded-full bg-sap-blue-100 flex items-center justify-center">
                                <span class="text-sm font-medium text-sap-blue-600">
                                    {% if customer.customer_name %}
                                        {{ customer.customer_name.0|upper }}
                                    {% else %}
                                        C
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-sap-gray-900">{{ customer.customerid|default:"N/A" }}</div>
                            <div class="text-sm text-sap-gray-500">ID: {{ customer.salesid }}</div>
                        </div>
                    </div>
                </td>

                <!-- Customer Name -->
                <td class="px-6 py-4">
                    <div class="text-sm font-medium text-sap-gray-900">
                        {{ customer.customer_name|default:"Unnamed Customer" }}
                    </div>
                    {% if customer.contact_person %}
                    <div class="text-sm text-sap-gray-500">
                        <i data-lucide="user-check" class="w-3 h-3 inline mr-1"></i>
                        {{ customer.contact_person }}
                    </div>
                    {% endif %}
                </td>

                <!-- Address -->
                <td class="px-6 py-4">
                    <div class="text-sm text-sap-gray-900 max-w-xs truncate" title="{{ customer.full_address }}">
                        {% if customer.registered_address %}
                            {{ customer.registered_address|truncatechars:50 }}
                        {% else %}
                            <span class="text-sap-gray-400 italic">No address provided</span>
                        {% endif %}
                    </div>
                    {% if customer.registered_city %}
                    <div class="text-sm text-sap-gray-500">
                        <i data-lucide="map-pin" class="w-3 h-3 inline mr-1"></i>
                        {{ customer.registered_city.cityname }}{% if customer.registered_state %}, {{ customer.registered_state.statename }}{% endif %}
                    </div>
                    {% endif %}
                </td>

                <!-- Contact -->
                <td class="px-6 py-4 whitespace-nowrap">
                    {% if customer.contact_no %}
                    <div class="text-sm text-sap-gray-900">
                        <i data-lucide="phone" class="w-3 h-3 inline mr-1"></i>
                        {{ customer.contact_no }}
                    </div>
                    {% endif %}
                    {% if customer.registered_contact_no and customer.registered_contact_no != customer.contact_no %}
                    <div class="text-sm text-sap-gray-500">
                        <i data-lucide="phone" class="w-3 h-3 inline mr-1"></i>
                        {{ customer.registered_contact_no }}
                    </div>
                    {% endif %}
                    {% if not customer.contact_no and not customer.registered_contact_no %}
                    <span class="text-sap-gray-400 italic text-sm">No contact</span>
                    {% endif %}
                </td>

                <!-- Email -->
                <td class="px-6 py-4 whitespace-nowrap">
                    {% if customer.email %}
                    <div class="text-sm text-sap-gray-900">
                        <a href="mailto:{{ customer.email }}" class="text-sap-blue-600 hover:text-sap-blue-800 flex items-center">
                            <i data-lucide="mail" class="w-3 h-3 mr-1"></i>
                            {{ customer.email|truncatechars:25 }}
                        </a>
                    </div>
                    {% else %}
                    <span class="text-sap-gray-400 italic text-sm">No email</span>
                    {% endif %}
                </td>

                <!-- Date Added -->
                <td class="px-6 py-4 whitespace-nowrap">
                    {% if customer.sysdate %}
                    <div class="text-sm text-sap-gray-900">{{ customer.sysdate }}</div>
                    {% if customer.systime %}
                    <div class="text-sm text-sap-gray-500">{{ customer.systime }}</div>
                    {% endif %}
                    {% else %}
                    <span class="text-sap-gray-400 italic text-sm">Unknown</span>
                    {% endif %}
                </td>

                <!-- Actions -->
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex items-center justify-end space-x-2">
                        <!-- View Button -->
                        <button class="inline-flex items-center px-3 py-1.5 border border-sap-gray-300 rounded-md text-sm text-sap-gray-700 hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 transition-colors"
                                title="View customer details">
                            <i data-lucide="eye" class="w-4 h-4"></i>
                        </button>
                        
                        <!-- Edit Button -->
                        <a href="{% url 'sales_distribution:customer_edit' customer.salesid %}" 
                           class="inline-flex items-center px-3 py-1.5 border border-sap-blue-300 text-sap-blue-600 rounded-md text-sm hover:bg-sap-blue-50 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 transition-colors"
                           title="Edit customer">
                            <i data-lucide="edit-2" class="w-4 h-4"></i>
                        </a>
                        
                        <!-- More Actions Menu -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" 
                                    class="inline-flex items-center px-3 py-1.5 border border-sap-gray-300 rounded-md text-sm text-sap-gray-700 hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 transition-colors"
                                    title="More actions">
                                <i data-lucide="more-horizontal" class="w-4 h-4"></i>
                            </button>
                            
                            <div x-show="open" 
                                 @click.away="open = false"
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="transform opacity-0 scale-95"
                                 x-transition:enter-end="transform opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-75"
                                 x-transition:leave-start="transform opacity-100 scale-100"
                                 x-transition:leave-end="transform opacity-0 scale-95"
                                 class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
                                <div class="py-1">
                                    <a href="#" class="flex items-center px-4 py-2 text-sm text-sap-gray-700 hover:bg-sap-gray-100">
                                        <i data-lucide="file-text" class="w-4 h-4 mr-3"></i>
                                        View Orders
                                    </a>
                                    <a href="#" class="flex items-center px-4 py-2 text-sm text-sap-gray-700 hover:bg-sap-gray-100">
                                        <i data-lucide="copy" class="w-4 h-4 mr-3"></i>
                                        Duplicate
                                    </a>
                                    <a href="#" class="flex items-center px-4 py-2 text-sm text-sap-gray-700 hover:bg-sap-gray-100">
                                        <i data-lucide="archive" class="w-4 h-4 mr-3"></i>
                                        Archive
                                    </a>
                                    <div class="border-t border-sap-gray-100"></div>
                                    <a href="#" class="flex items-center px-4 py-2 text-sm text-sap-red-600 hover:bg-red-50">
                                        <i data-lucide="trash-2" class="w-4 h-4 mr-3"></i>
                                        Delete
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="px-6 py-12 text-center">
                    <div class="flex flex-col items-center space-y-3">
                        <div class="w-16 h-16 bg-sap-gray-100 rounded-full flex items-center justify-center">
                            <i data-lucide="users" class="w-8 h-8 text-sap-gray-400"></i>
                        </div>
                        <div class="text-sap-gray-500">
                            <p class="text-lg font-medium">No customers found</p>
                            <p class="text-sm">{% if request.GET.search %}Try adjusting your search criteria{% else %}Get started by adding your first customer{% endif %}</p>
                        </div>
                        {% if not request.GET.search %}
                        <a href="{% url 'sales_distribution:customer_new' %}" 
                           class="inline-flex items-center px-4 py-2 bg-sap-blue-600 text-white rounded-lg hover:bg-sap-blue-700 transition-colors">
                            <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                            Add First Customer
                        </a>
                        {% endif %}
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- Table Footer with Summary -->
{% if customers %}
<div class="bg-sap-gray-50 px-6 py-3 border-t border-sap-gray-200">
    <div class="flex items-center justify-between text-sm text-sap-gray-600">
        <div class="flex items-center space-x-4">
            <span class="flex items-center">
                <i data-lucide="users" class="w-4 h-4 mr-1"></i>
                {{ customers|length }} customer{{ customers|length|pluralize }} in this view
            </span>
            {% if request.GET.search or request.GET.filter %}
            <span class="flex items-center text-sap-blue-600">
                <i data-lucide="filter" class="w-4 h-4 mr-1"></i>
                Filtered results
                <a href="{% url 'sales_distribution:customer_list' %}" 
                   class="ml-2 text-sap-blue-600 hover:text-sap-blue-800 underline">
                    Clear filters
                </a>
            </span>
            {% endif %}
        </div>
        <div class="text-sap-gray-500">
            Last updated: {{ "now"|date:"M d, Y H:i" }}
        </div>
    </div>
</div>
{% endif %}
