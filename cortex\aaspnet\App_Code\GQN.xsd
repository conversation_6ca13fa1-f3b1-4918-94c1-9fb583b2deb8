﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="GQN" targetNamespace="http://tempuri.org/GQN.xsd" xmlns:mstns="http://tempuri.org/GQN.xsd" xmlns="http://tempuri.org/GQN.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="GQN" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="GQN" msprop:Generator_DataSetName="GQN">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" msprop:Generator_UserColumnName="Id" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnPropNameInTable="IdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="ItemCode" msprop:Generator_UserColumnName="ItemCode" msprop:Generator_ColumnVarNameInTable="columnItemCode" msprop:Generator_ColumnPropNameInRow="ItemCode" msprop:Generator_ColumnPropNameInTable="ItemCodeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Description" msprop:Generator_UserColumnName="Description" msprop:Generator_ColumnVarNameInTable="columnDescription" msprop:Generator_ColumnPropNameInRow="Description" msprop:Generator_ColumnPropNameInTable="DescriptionColumn" type="xs:string" minOccurs="0" />
              <xs:element name="UOM" msprop:Generator_UserColumnName="UOM" msprop:Generator_ColumnVarNameInTable="columnUOM" msprop:Generator_ColumnPropNameInRow="UOM" msprop:Generator_ColumnPropNameInTable="UOMColumn" type="xs:string" minOccurs="0" />
              <xs:element name="POQty" msprop:Generator_UserColumnName="POQty" msprop:Generator_ColumnVarNameInTable="columnPOQty" msprop:Generator_ColumnPropNameInRow="POQty" msprop:Generator_ColumnPropNameInTable="POQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="InvQty" msprop:Generator_UserColumnName="InvQty" msprop:Generator_ColumnVarNameInTable="columnInvQty" msprop:Generator_ColumnPropNameInRow="InvQty" msprop:Generator_ColumnPropNameInTable="InvQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="RecedQty" msprop:Generator_UserColumnName="RecedQty" msprop:Generator_ColumnVarNameInTable="columnRecedQty" msprop:Generator_ColumnPropNameInRow="RecedQty" msprop:Generator_ColumnPropNameInTable="RecedQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="AcceptedQty" msprop:Generator_UserColumnName="AcceptedQty" msprop:Generator_ColumnVarNameInTable="columnAcceptedQty" msprop:Generator_ColumnPropNameInRow="AcceptedQty" msprop:Generator_ColumnPropNameInTable="AcceptedQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="RejReason" msprop:Generator_UserColumnName="RejReason" msprop:Generator_ColumnVarNameInTable="columnRejReason" msprop:Generator_ColumnPropNameInRow="RejReason" msprop:Generator_ColumnPropNameInTable="RejReasonColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Remarks" msprop:Generator_UserColumnName="Remarks" msprop:Generator_ColumnVarNameInTable="columnRemarks" msprop:Generator_ColumnPropNameInRow="Remarks" msprop:Generator_ColumnPropNameInTable="RemarksColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="GQNDate" msprop:Generator_UserColumnName="GQNDate" msprop:Generator_ColumnVarNameInTable="columnGQNDate" msprop:Generator_ColumnPropNameInRow="GQNDate" msprop:Generator_ColumnPropNameInTable="GQNDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="GRRDate" msprop:Generator_UserColumnName="GRRDate" msprop:Generator_ColumnVarNameInTable="columnGRRDate" msprop:Generator_ColumnPropNameInRow="GRRDate" msprop:Generator_ColumnPropNameInTable="GRRDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="GINDate" msprop:Generator_UserColumnName="GINDate" msprop:Generator_ColumnVarNameInTable="columnGINDate" msprop:Generator_ColumnPropNameInRow="GINDate" msprop:Generator_ColumnPropNameInTable="GINDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PODate" msprop:Generator_UserColumnName="PODate" msprop:Generator_ColumnVarNameInTable="columnPODate" msprop:Generator_ColumnPropNameInRow="PODate" msprop:Generator_ColumnPropNameInTable="PODateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PONo" msprop:Generator_UserColumnName="PONo" msprop:Generator_ColumnVarNameInTable="columnPONo" msprop:Generator_ColumnPropNameInRow="PONo" msprop:Generator_ColumnPropNameInTable="PONoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ModVatApp" msprop:Generator_UserColumnName="ModVatApp" msprop:Generator_ColumnPropNameInRow="ModVatApp" msprop:Generator_ColumnVarNameInTable="columnModVatApp" msprop:Generator_ColumnPropNameInTable="ModVatAppColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ModVatInv" msprop:Generator_UserColumnName="ModVatInv" msprop:Generator_ColumnPropNameInRow="ModVatInv" msprop:Generator_ColumnVarNameInTable="columnModVatInv" msprop:Generator_ColumnPropNameInTable="ModVatInvColumn" type="xs:string" minOccurs="0" />
              <xs:element name="InspectedBy" msprop:Generator_UserColumnName="InspectedBy" msprop:Generator_ColumnPropNameInRow="InspectedBy" msprop:Generator_ColumnVarNameInTable="columnInspectedBy" msprop:Generator_ColumnPropNameInTable="InspectedByColumn" type="xs:string" minOccurs="0" />
              <xs:element name="SN" msprop:Generator_UserColumnName="SN" msprop:Generator_ColumnPropNameInRow="SN" msprop:Generator_ColumnVarNameInTable="columnSN" msprop:Generator_ColumnPropNameInTable="SNColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PN" msprop:Generator_UserColumnName="PN" msprop:Generator_ColumnPropNameInRow="PN" msprop:Generator_ColumnVarNameInTable="columnPN" msprop:Generator_ColumnPropNameInTable="PNColumn" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>