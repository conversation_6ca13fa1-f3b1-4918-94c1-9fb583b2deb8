# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is "Cortex" - a Django project that converts an ASP.NET enterprise application into a modern Django web application. The project follows a modular architecture with separate Django apps for different business functions.

## Architecture

### Core Structure
- **cortex/**: Main Django project configuration
- **core/**: Authentication, dashboard, and shared functionality
- **sys_admin/**: System administration (Country, State, City management)
- **Module/**: Legacy ASP.NET code being converted (reference only)

### Business Modules
Each business function is implemented as a separate Django app:
- `accounts/`: Financial accounting and transactions
- `design/`: Product design and BOM management  
- `human_resource/`: HR management, payroll, employee data
- `inventory/`: Stock management and warehouse operations
- `material_costing/`: Cost analysis and pricing
- `material_management/`: Material planning and procurement
- `material_planning/`: Production planning
- `messaging/`: Internal communication systems
- `mis/`: Management information systems
- `mr_office/`: Material requisition office
- `project_management/`: Project tracking and planning
- `quality_control/`: Quality assurance processes
- `sales_distribution/`: Sales and distribution management
- `scheduler/`: Task and resource scheduling

### Technology Stack
- **Backend**: Django 5.2 with class-based views
- **Frontend**: Django templates + HTMX + Alpine.js + Tailwind CSS
- **Database**: SQLite (existing database with `managed = False` models)
- **Testing**: Playwright for end-to-end testing

### Database Architecture
- Uses existing SQLite database (`db.sqlite3`)
- Models have `managed = False` to preserve existing schema
- Key entities: Country → State → City hierarchy, Company, FinancialYear
- Foreign key relationships maintained from original ASP.NET structure

## Development Commands

### Basic Django Operations
```bash
# Start development server
python manage.py runserver

# Apply migrations (limited due to managed=False models)
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Collect static files
python manage.py collectstatic

# Django shell
python manage.py shell
```

### Testing
```bash
# Run Django unit tests
python manage.py test

# Run Playwright tests
npx playwright test

# Install Playwright browsers
npx playwright install
```

## Critical Development Guidelines

### ASP.NET to Django Conversion Rules
This project converts legacy ASP.NET code to Django following strict rules documented in `README.md`:

1. **File Organization**: Choose consistent structure (forms.py OR forms/ directory - never both)
2. **Models**: Use existing models.py files with `managed = False` - DO NOT modify database structure
3. **Views**: Use ONLY class-based views (ListView, CreateView, UpdateView, DeleteView)
4. **Frontend**: Django + HTMX + Alpine.js + Tailwind CSS only
5. **Forms**: Always include CSRF tokens, implement proper validation
6. **UI Standards**: Create modern, professional interfaces from day one

### Code Architecture Patterns

#### View Structure
```python
# Use class-based views exclusively
class CountryListView(LoginRequiredMixin, ListView):
    model = Country
    template_name = 'sys_admin/country_list.html'
    
class CountryCreateView(LoginRequiredMixin, CreateView):
    model = Country
    form_class = CountryForm
    template_name = 'sys_admin/country_form.html'
```

#### HTMX Integration
- Use HTMX for dynamic form submissions and page updates
- Always check for `request.headers.get('HX-Request')` in views
- Return partial templates for HTMX responses
- Include proper CSRF tokens in all forms

#### Template Organization
```
app_name/templates/app_name/
├── base.html
├── model_list.html
├── model_form.html
├── model_detail.html
└── partials/
    ├── form_errors.html
    └── success_messages.html
```

### Authentication & Security
- All business views require `LoginRequiredMixin`
- Core app handles authentication with modern login/logout views
- Company context available globally via `core.context_processors.company_context`
- Always use Django's built-in CSRF protection

### URL Patterns
```python
# Main URL structure
path('', include('core.urls'))  # Authentication, dashboard
path('sys-admin/', include('sys_admin.urls'))  # System administration
# Business modules follow similar pattern
```

### Model Relationships
```python
# Existing database relationships to respect
Country (1) → (N) State → (N) City
Company → City (registered and plant addresses)
FinancialYear → Company
```

## File Organization Rules

### For Large Apps
```
large_app/
├── views/
│   ├── __init__.py
│   ├── country_views.py
│   └── user_views.py
├── forms/
│   ├── __init__.py
│   ├── country_forms.py
│   └── user_forms.py
├── templates/large_app/
└── tests/
    ├── test_country.py
    └── test_user.py
```

### For Small Apps
```
small_app/
├── views.py
├── forms.py
├── templates/small_app/
└── tests.py
```

## Testing Strategy

### Django Tests
- Write tests for every converted ASP.NET feature
- Test all CRUD operations and business logic
- Verify existing database relationships work correctly
- Use `TestCase` class for database-related tests

### Playwright Tests
- End-to-end testing for complete user workflows
- Test all form fields with proper selectors
- Verify HTMX interactions work correctly
- Test responsive design and cross-browser compatibility

## Legacy Code Reference

The `Module/` directory contains original ASP.NET code for reference:
- Use .aspx/.aspx.cs files to understand business requirements
- Convert Page_Load, Button_Click events to Django view methods
- Translate ASP.NET validation controls to Django form validation
- Preserve business logic while modernizing implementation

## Static Files & Assets

```
core/static/
├── css/
├── js/
├── images/
└── vendor/
```

Uses Tailwind CSS for styling - no custom CSS should be written inline.

## Company Context

Global company information available in all templates via context processor:
- Current company details
- Financial year information
- User permissions and roles

## Performance Considerations

- Use `select_related()` and `prefetch_related()` for foreign key queries
- Implement pagination for large datasets
- Use HTMX for dynamic updates to avoid full page reloads
- Cache static content appropriately

## Security Notes

- Never modify the existing database schema
- Always use parameterized queries (Django ORM handles this)
- Validate all user input through Django forms
- Implement proper user authentication and authorization
- Never expose sensitive configuration in templates

## Implementation Guidelines
- All search functionality must be real time search and no need to hit enter or click on search button.