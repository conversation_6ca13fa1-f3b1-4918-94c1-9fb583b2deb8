{% extends 'material_planning/base.html' %}

{% block title %}Process Categories - Inline Editor{% endblock %}

{% block extra_head %}
<!-- FontAwesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
{% endblock %}

{% block content %}
<div class="space-y-6 animate-fade-in" x-data="processManager()">
    <!-- Header Section with SAP Card Design -->
    <div class="sap-card">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-sap-gray-900 mb-2">Process Categories</h1>
                <p class="text-sap-gray-600">Manage manufacturing process categories with inline editing</p>
                <div class="flex items-center mt-3 space-x-4">
                    <span class="sap-status-info" x-text="`${processes.length} Total Processes`"></span>
                    <span class="text-sm text-sap-gray-500">Last updated: {{ "now"|date:"M d, Y H:i" }}</span>
                </div>
            </div>
            <div class="flex space-x-3">
                <button @click="toggleAddMode()" 
                        class="sap-button-primary"
                        :class="{ 'bg-red-600 hover:bg-red-700': addMode }"
                        x-text="addMode ? 'Cancel Add' : 'Add New Process'">
                    <i class="fas fa-plus mr-2" x-show="!addMode"></i>
                    <i class="fas fa-times mr-2" x-show="addMode"></i>
                </button>
                <button class="sap-button-secondary">
                    <i class="fas fa-download mr-2"></i>
                    Export
                </button>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div x-show="message" 
         x-transition
         class="sap-card"
         :class="messageType === 'success' ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'">
        <div class="flex items-center">
            <i class="fas" 
               :class="messageType === 'success' ? 'fa-check-circle text-green-600' : 'fa-exclamation-circle text-red-600'"></i>
            <span class="ml-2" x-text="message"></span>
            <button @click="clearMessage()" class="ml-auto text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <!-- Process Categories Table with Inline Editing -->
    <div class="sap-card p-0 overflow-hidden">
        <div class="px-6 py-4 border-b border-sap-gray-200 bg-sap-gray-50">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-sap-gray-900">Process Categories (GridView Style)</h3>
                <div class="text-sm text-sap-gray-600">
                    <span x-show="editingId !== null" class="text-sap-blue-600">
                        <i class="fas fa-edit mr-1"></i>
                        Editing Process ID: <span x-text="editingId"></span>
                    </span>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full">
                <thead class="bg-sap-gray-50 border-b border-sap-gray-200">
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-semibold text-sap-gray-700 uppercase tracking-wider w-16">SN</th>
                        <th class="px-4 py-3 text-center text-xs font-semibold text-sap-gray-700 uppercase tracking-wider w-20">Edit</th>
                        <th class="px-4 py-3 text-center text-xs font-semibold text-sap-gray-700 uppercase tracking-wider w-20">Delete</th>
                        <th class="px-4 py-3 text-left text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">Name of Process</th>
                        <th class="px-4 py-3 text-center text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">Symbol</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-100">
                    <!-- Add New Process Row (Footer equivalent) -->
                    <tr x-show="addMode" class="bg-sap-blue-50 border-2 border-sap-blue-200">
                        <td class="px-4 py-4 text-center">
                            <button @click="saveNewProcess()" 
                                    class="sap-button-primary text-sm py-1 px-3"
                                    :disabled="!newProcess.processname || !newProcess.symbol">
                                <i class="fas fa-save mr-1"></i>
                                Insert
                            </button>
                        </td>
                        <td class="px-4 py-4 text-center">-</td>
                        <td class="px-4 py-4 text-center">-</td>
                        <td class="px-4 py-4">
                            <input type="text" 
                                   x-model="newProcess.processname"
                                   placeholder="Enter process name"
                                   class="sap-input w-full text-sm"
                                   @keydown.enter="saveNewProcess()"
                                   @keydown.escape="toggleAddMode()">
                            <span x-show="validationErrors.processname" 
                                  class="text-red-500 text-xs mt-1" 
                                  x-text="validationErrors.processname"></span>
                        </td>
                        <td class="px-4 py-4">
                            <input type="text" 
                                   x-model="newProcess.symbol"
                                   placeholder="Enter symbol"
                                   class="sap-input w-full text-sm text-center"
                                   @keydown.enter="saveNewProcess()"
                                   @keydown.escape="toggleAddMode()">
                            <span x-show="validationErrors.symbol" 
                                  class="text-red-500 text-xs mt-1" 
                                  x-text="validationErrors.symbol"></span>
                        </td>
                    </tr>

                    <!-- Process Rows -->
                    <template x-for="(process, index) in paginatedProcesses" :key="process.id">
                        <tr class="hover:bg-sap-gray-50 transition-colors duration-150"
                            :class="{ 'bg-sap-yellow-50': editingId === process.id }">
                            <!-- Serial Number -->
                            <td class="px-4 py-4 text-center text-sm text-sap-gray-900" 
                                x-text="((currentPage - 1) * pageSize) + index + 1"></td>
                            
                            <!-- Edit Button -->
                            <td class="px-4 py-4 text-center">
                                <template x-if="editingId !== process.id">
                                    <button @click="startEdit(process)" 
                                            class="text-sap-blue-600 hover:text-sap-blue-700 text-sm">
                                        <i class="fas fa-edit"></i>
                                        Edit
                                    </button>
                                </template>
                                <template x-if="editingId === process.id">
                                    <div class="flex space-x-2">
                                        <button @click="saveEdit()" 
                                                class="text-green-600 hover:text-green-700 text-sm"
                                                :disabled="!editingProcess.processname || !editingProcess.symbol">
                                            <i class="fas fa-save"></i>
                                            Update
                                        </button>
                                        <button @click="cancelEdit()" 
                                                class="text-gray-600 hover:text-gray-700 text-sm">
                                            <i class="fas fa-times"></i>
                                            Cancel
                                        </button>
                                    </div>
                                </template>
                            </td>
                            
                            <!-- Delete Button -->
                            <td class="px-4 py-4 text-center">
                                <button @click="deleteProcess(process)" 
                                        class="text-red-600 hover:text-red-700 text-sm"
                                        :disabled="editingId === process.id">
                                    <i class="fas fa-trash"></i>
                                    Delete
                                </button>
                            </td>
                            
                            <!-- Process Name -->
                            <td class="px-4 py-4">
                                <template x-if="editingId !== process.id">
                                    <div class="text-sm font-medium text-sap-gray-900" 
                                         x-text="process.processname || 'Unnamed Process'"></div>
                                </template>
                                <template x-if="editingId === process.id">
                                    <input type="text" 
                                           x-model="editingProcess.processname"
                                           class="sap-input w-full text-sm"
                                           @keydown.enter="saveEdit()"
                                           @keydown.escape="cancelEdit()">
                                </template>
                            </td>
                            
                            <!-- Symbol -->
                            <td class="px-4 py-4 text-center">
                                <template x-if="editingId !== process.id">
                                    <div class="text-sm text-sap-gray-900" 
                                         x-text="process.symbol || '-'"></div>
                                </template>
                                <template x-if="editingId === process.id">
                                    <input type="text" 
                                           x-model="editingProcess.symbol"
                                           class="sap-input w-full text-sm text-center"
                                           @keydown.enter="saveEdit()"
                                           @keydown.escape="cancelEdit()">
                                </template>
                            </td>
                        </tr>
                    </template>

                    <!-- Empty State -->
                    <tr x-show="processes.length === 0">
                        <td colspan="5" class="px-6 py-12 text-center">
                            <div class="flex flex-col items-center">
                                <div class="w-16 h-16 bg-sap-gray-100 rounded-full flex items-center justify-center mb-4">
                                    <i class="fas fa-inbox text-sap-gray-400 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No Process Categories Found</h3>
                                <p class="text-sap-gray-500 mb-4">Get started by creating your first process category.</p>
                                <button @click="toggleAddMode()" class="sap-button-primary">
                                    <i class="fas fa-plus mr-2"></i>
                                    Create First Category
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Pagination Controls -->
        <div class="px-6 py-4 border-t border-sap-gray-200 bg-sap-gray-50" x-show="totalPages > 1">
            <div class="flex items-center justify-between">
                <div class="text-sm text-sap-gray-700">
                    Showing <span x-text="startIndex"></span> to <span x-text="endIndex"></span> of <span x-text="processes.length"></span> results
                </div>
                <div class="flex space-x-2">
                    <button @click="currentPage = Math.max(1, currentPage - 1)" 
                            :disabled="currentPage === 1"
                            class="sap-button-secondary py-2 px-3"
                            :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    
                    <span class="flex items-center px-3 py-2 text-sm text-sap-gray-700">
                        Page <span x-text="currentPage"></span> of <span x-text="totalPages"></span>
                    </span>
                    
                    <button @click="currentPage = Math.min(totalPages, currentPage + 1)" 
                            :disabled="currentPage === totalPages"
                            class="sap-button-secondary py-2 px-3"
                            :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div x-show="showDeleteModal" 
     x-transition 
     class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
     style="display: none;"
     @click.self="forceCloseModal()"
     @keydown.escape.window="forceCloseModal()">
    <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4" @click.stop>
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Confirm Deletion</h3>
            <button @click="forceCloseModal()" 
                    class="text-gray-400 hover:text-gray-600 text-xl font-bold">
                ×
            </button>
        </div>
        <p class="text-gray-600 mb-6">
            Are you sure you want to delete the process 
            "<span x-text="processToDelete?.processname || 'Unknown Process'" class="font-semibold"></span>"?
            This action cannot be undone.
        </p>
        <div class="flex justify-end space-x-3">
            <button @click="forceCloseModal()" 
                    class="sap-button-secondary">
                Cancel
            </button>
            <button @click="confirmDelete()" 
                    class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded transition-colors">
                <i class="fas fa-trash mr-2"></i>
                Delete
            </button>
        </div>
    </div>
</div>

<script>
function processManager() {
    const processesData = {{ processes|safe }};
    console.log('Loaded processes:', processesData);
    
    return {
        processes: processesData || [],
        editingId: null,
        editingProcess: {},
        addMode: false,
        newProcess: { processname: '', symbol: '' },
        showDeleteModal: false,
        processToDelete: null,
        message: '',
        messageType: 'success',
        validationErrors: {},
        currentPage: 1,
        pageSize: 15,
        
        init() {
            console.log('Process manager initialized');
            console.log('Total processes:', this.processes.length);
            console.log('Show delete modal:', this.showDeleteModal);
            
            // Force hide the modal on initialization
            this.showDeleteModal = false;
            this.processToDelete = null;
        },

        get paginatedProcesses() {
            const start = (this.currentPage - 1) * this.pageSize;
            const end = start + this.pageSize;
            return this.processes.slice(start, end);
        },

        get totalPages() {
            return Math.ceil(this.processes.length / this.pageSize);
        },

        get startIndex() {
            return (this.currentPage - 1) * this.pageSize + 1;
        },

        get endIndex() {
            return Math.min(this.currentPage * this.pageSize, this.processes.length);
        },

        toggleAddMode() {
            this.addMode = !this.addMode;
            if (this.addMode) {
                this.newProcess = { processname: '', symbol: '' };
                this.validationErrors = {};
                this.cancelEdit(); // Cancel any ongoing edits
            }
        },

        startEdit(process) {
            this.cancelEdit(); // Cancel any ongoing edits
            this.addMode = false; // Cancel add mode
            this.editingId = process.id;
            this.editingProcess = { ...process };
        },

        cancelEdit() {
            this.editingId = null;
            this.editingProcess = {};
        },

        async saveEdit() {
            if (!this.editingProcess.processname || !this.editingProcess.symbol) {
                this.showMessage('Process name and symbol are required.', 'error');
                return;
            }

            try {
                const response = await fetch(`/material-planning/processes/${this.editingId}/update/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(this.editingProcess)
                });

                const data = await response.json();
                
                if (data.success) {
                    // Update the process in the local array
                    const index = this.processes.findIndex(p => p.id === this.editingId);
                    if (index !== -1) {
                        this.processes[index] = { ...this.editingProcess };
                    }
                    this.cancelEdit();
                    this.showMessage(`Process "${this.editingProcess.processname}" updated successfully.`, 'success');
                } else {
                    this.showMessage(data.message || 'Failed to update process.', 'error');
                }
            } catch (error) {
                console.error('Error updating process:', error);
                this.showMessage('An error occurred while updating the process.', 'error');
            }
        },

        async saveNewProcess() {
            this.validationErrors = {};
            
            if (!this.newProcess.processname) {
                this.validationErrors.processname = 'Process name is required.';
            }
            if (!this.newProcess.symbol) {
                this.validationErrors.symbol = 'Symbol is required.';
            }

            if (Object.keys(this.validationErrors).length > 0) {
                return;
            }

            try {
                const response = await fetch('/material-planning/processes/create/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(this.newProcess)
                });

                const data = await response.json();
                
                if (data.success) {
                    this.processes.push(data.process);
                    this.toggleAddMode();
                    this.showMessage(`Process "${data.process.processname}" created successfully.`, 'success');
                } else {
                    this.showMessage(data.message || 'Failed to create process.', 'error');
                }
            } catch (error) {
                console.error('Error creating process:', error);
                this.showMessage('An error occurred while creating the process.', 'error');
            }
        },

        deleteProcess(process) {
            this.processToDelete = process;
            this.showDeleteModal = true;
        },

        async confirmDelete() {
            try {
                const response = await fetch(`/material-planning/processes/${this.processToDelete.id}/delete/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                const data = await response.json();
                
                if (data.success) {
                    this.processes = this.processes.filter(p => p.id !== this.processToDelete.id);
                    this.showDeleteModal = false;
                    this.showMessage(`Process "${this.processToDelete.processname}" deleted successfully.`, 'success');
                    
                    // Adjust current page if needed
                    if (this.paginatedProcesses.length === 0 && this.currentPage > 1) {
                        this.currentPage--;
                    }
                } else {
                    this.showMessage(data.message || 'Failed to delete process.', 'error');
                }
            } catch (error) {
                console.error('Error deleting process:', error);
                this.showMessage('An error occurred while deleting the process.', 'error');
            }
        },

        showMessage(text, type = 'success') {
            this.message = text;
            this.messageType = type;
            
            // Auto-hide success messages after 5 seconds
            if (type === 'success') {
                setTimeout(() => {
                    this.clearMessage();
                }, 5000);
            }
        },

        clearMessage() {
            this.message = '';
        },

        forceCloseModal() {
            this.showDeleteModal = false;
            this.processToDelete = null;
            console.log('Modal force closed');
        }
    };
}
</script>

<!-- Hidden CSRF Token -->
<input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}">

<!-- Force hide modal on page load -->
<script>
// Force hide the modal immediately when DOM loads
document.addEventListener('DOMContentLoaded', function() {
    hideModalForcibly();
});

// Also hide when Alpine.js initializes
document.addEventListener('alpine:init', function() {
    hideModalForcibly();
});

// Force hide function
function hideModalForcibly() {
    console.log('Force hiding modal...');
    
    // Find modal by class and hide it
    const modal = document.querySelector('.fixed.inset-0.bg-black.bg-opacity-50');
    if (modal) {
        modal.style.display = 'none';
        console.log('Modal hidden via display:none');
    }
    
    // Also try to set Alpine.js data if available
    setTimeout(() => {
        if (window.Alpine && window.Alpine.store) {
            try {
                // Try to access Alpine data and force modal to false
                const processManagerEl = document.querySelector('[x-data*="processManager"]');
                if (processManagerEl && processManagerEl._x_dataStack) {
                    const data = processManagerEl._x_dataStack[0];
                    if (data) {
                        data.showDeleteModal = false;
                        data.processToDelete = null;
                        console.log('Alpine data updated to hide modal');
                    }
                }
            } catch (e) {
                console.log('Could not access Alpine data:', e);
            }
        }
    }, 100);
}

// Hide modal every 500ms for the first 3 seconds as a safety measure
let hideCount = 0;
const hideInterval = setInterval(() => {
    hideCount++;
    hideModalForcibly();
    
    if (hideCount >= 6) { // Stop after 3 seconds (6 * 500ms)
        clearInterval(hideInterval);
        console.log('Stopped force hiding modal');
    }
}, 500);
</script>

{% endblock %}