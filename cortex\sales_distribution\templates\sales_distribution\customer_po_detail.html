{% extends "core/base.html" %}
{% load static %}

{% block title %}Purchase Order Details{% endblock %}

{% block content %}
<div class="min-h-screen bg-sap-gray-50 py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Header Section -->
        <div class="sap-card mb-6">
            <div class="px-6 py-4 bg-sap-blue-600 text-white rounded-t-xl flex justify-between items-center">
                <div>
                    <h1 class="text-xl font-bold">Purchase Order Details</h1>
                    <p class="text-sap-blue-100 text-sm mt-1">PO Number: {{ po.pono|default:"N/A" }}</p>
                </div>
                <a href="{% url 'sales_distribution:customer_po_list' %}" 
                   class="inline-flex items-center px-4 py-2 bg-white text-sap-blue-600 font-medium rounded-lg hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-sap-blue-600 transition-all duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to List
                </a>
            </div>
        </div>

        <!-- Purchase Order Information Section -->
        <div class="sap-card mb-6">
            <div class="px-6 py-4 border-b border-sap-gray-200">
                <h2 class="text-lg font-semibold text-sap-gray-900">Purchase Order Information</h2>
            </div>
            
            <div class="px-6 py-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Column 1 -->
                    <div class="space-y-4">
                        <div>
                            <label class="text-sm font-medium text-sap-gray-600">PO Number</label>
                            <p class="text-sm text-sap-gray-900 font-semibold">{{ po.pono|default:"-" }}</p>
                        </div>
                        
                        <div>
                            <label class="text-sm font-medium text-sap-gray-600">Supplier ID</label>
                            <p class="text-sm text-sap-gray-900">{{ po.vendorcode|default:"-" }}</p>
                        </div>
                        
                        <div>
                            <label class="text-sm font-medium text-sap-gray-600">Customer</label>
                            <p class="text-sm text-sap-gray-900">{{ customer_name|default:"Unknown Customer" }}</p>
                            {% if customer_address %}
                            <p class="text-xs text-sap-gray-600 mt-1">{{ customer_address }}</p>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Column 2 -->
                    <div class="space-y-4">
                        <div>
                            <label class="text-sm font-medium text-sap-gray-600">Date</label>
                            <p class="text-sm text-sap-gray-900">{{ po.podate|default:"-" }}</p>
                        </div>
                        
                        <div>
                            <label class="text-sm font-medium text-sap-gray-600">Amendment Number</label>
                            <p class="text-sm text-sap-gray-900">0</p>
                        </div>
                        
                        <div>
                            <label class="text-sm font-medium text-sap-gray-600">Received Date</label>
                            <p class="text-sm text-sap-gray-900">{{ po.poreceiveddate|default:"-" }}</p>
                        </div>
                    </div>
                    
                    <!-- Column 3 -->
                    <div class="space-y-4">
                        <div>
                            <label class="text-sm font-medium text-sap-gray-600">Financial Year</label>
                            <p class="text-sm text-sap-gray-900">{{ financial_year|default:"Unknown" }}</p>
                        </div>
                        
                        <div>
                            <label class="text-sm font-medium text-sap-gray-600">Status</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Checked
                            </span>
                        </div>
                        
                        <div>
                            <label class="text-sm font-medium text-sap-gray-600">Created By</label>
                            <p class="text-sm text-sap-gray-900">{{ employee_name|default:"Unknown" }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Details Section -->
        <div class="sap-card">
            <div class="px-6 py-4 border-b border-sap-gray-200">
                <h2 class="text-lg font-semibold text-sap-gray-900">Order Details</h2>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-sap-gray-200">
                    <thead class="bg-sap-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Item
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Description
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Quantity
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Unit
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Rate
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                                Amount
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-sap-gray-200">
                        {% for detail in po_details %}
                        <tr class="hover:bg-sap-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">
                                {{ forloop.counter }}
                            </td>
                            <td class="px-6 py-4 text-sm text-sap-gray-900">
                                <div class="max-w-xs">
                                    {{ detail.itemdesc|default:"-" }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">
                                {{ detail.totalqty|default:"-"|floatformat:3 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">
                                {{ detail.unit.unitname|default:"-" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">
                                {{ detail.rate|default:"-"|floatformat:2 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">
                                {% if detail.totalqty and detail.rate %}
                                    {% widthratio detail.totalqty 1 detail.rate as amount %}
                                    {{ amount|floatformat:2 }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-sm text-sap-gray-500">
                                No order details found
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Terms & Conditions Section (if available) -->
        {% if po.paymentterms or po.warrenty or po.remarks %}
        <div class="sap-card mt-6">
            <div class="px-6 py-4 border-b border-sap-gray-200">
                <h2 class="text-lg font-semibold text-sap-gray-900">Terms & Conditions</h2>
            </div>
            
            <div class="px-6 py-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {% if po.paymentterms %}
                    <div>
                        <label class="text-sm font-medium text-sap-gray-600">Payment Terms</label>
                        <p class="text-sm text-sap-gray-900 mt-1">{{ po.paymentterms }}</p>
                    </div>
                    {% endif %}
                    
                    {% if po.warrenty %}
                    <div>
                        <label class="text-sm font-medium text-sap-gray-600">Warranty</label>
                        <p class="text-sm text-sap-gray-900 mt-1">{{ po.warrenty }}</p>
                    </div>
                    {% endif %}
                    
                    {% if po.transport %}
                    <div>
                        <label class="text-sm font-medium text-sap-gray-600">Transport</label>
                        <p class="text-sm text-sap-gray-900 mt-1">{{ po.transport }}</p>
                    </div>
                    {% endif %}
                    
                    {% if po.freight %}
                    <div>
                        <label class="text-sm font-medium text-sap-gray-600">Freight</label>
                        <p class="text-sm text-sap-gray-900 mt-1">{{ po.freight }}</p>
                    </div>
                    {% endif %}
                    
                    {% if po.remarks %}
                    <div class="md:col-span-2">
                        <label class="text-sm font-medium text-sap-gray-600">Remarks</label>
                        <p class="text-sm text-sap-gray-900 mt-1">{{ po.remarks }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Action Buttons -->
        <div class="mt-6 flex justify-center space-x-4">
            <a href="{% url 'sales_distribution:customer_po_update' po.poid %}" 
               class="inline-flex items-center px-4 py-2 bg-sap-blue-600 text-white font-medium rounded-lg hover:bg-sap-blue-700 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:ring-offset-2 transition-all duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit PO
            </a>
            
            <button type="button" 
                    class="inline-flex items-center px-4 py-2 bg-sap-green-600 text-white font-medium rounded-lg hover:bg-sap-green-700 focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:ring-offset-2 transition-all duration-200"
                    onclick="window.print()">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                </svg>
                Print PO
            </button>
        </div>
    </div>
</div>
{% endblock %}