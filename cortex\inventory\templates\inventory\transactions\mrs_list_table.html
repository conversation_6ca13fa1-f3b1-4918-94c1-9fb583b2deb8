{% extends "core/base.html" %}
{% load static %}

{% block title %}Material Requisition Slip (MRS) - Edit{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">Material Requisition Slip (MRS) - Edit</h1>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <form method="get" class="flex items-center space-x-4 mb-4">
                <div class="flex items-center space-x-2">
                    <label for="employee_name" class="text-sm font-medium text-gray-700 whitespace-nowrap">Employee Name</label>
                    <select name="employee_name" id="employee_name" class="block w-full px-4 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Employees</option>
                        {% for emp in employee_options %}
                            <option value="{{ emp.name }}" {% if employee_filter == emp.name %}selected{% endif %}>
                                {{ emp.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded text-sm font-medium hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-all duration-200">
                    Search
                </button>
            </form>
            
            <!-- Data Summary -->
            {% if summary %}
            <div class="border-t pt-4">
                <h4 class="text-sm font-medium text-gray-700 mb-2">Data Summary</h4>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div class="bg-blue-50 p-3 rounded-lg">
                        <div class="text-blue-800 font-medium">{{ summary.total_records }}</div>
                        <div class="text-blue-600">Total Records</div>
                    </div>
                    <div class="bg-green-50 p-3 rounded-lg">
                        <div class="text-green-800 font-medium">{{ summary.with_employee_data }}</div>
                        <div class="text-green-600">With Employee Data</div>
                    </div>
                    <div class="bg-purple-50 p-3 rounded-lg">
                        <div class="text-purple-800 font-medium">{{ summary.unique_employees }}</div>
                        <div class="text-purple-600">Unique Employees</div>
                    </div>
                    <div class="bg-orange-50 p-3 rounded-lg">
                        <div class="text-orange-800 font-medium">{{ summary.departments }}</div>
                        <div class="text-orange-600">Departments</div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Table Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <!-- Table Header -->
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">Material Requisition Records</h3>
                    <div class="text-sm text-gray-600">
                        <span>Showing {{ pagination.start_index }}-{{ pagination.end_index }} of {{ pagination.total_count }} records</span>
                        {% if employee_filter %}
                            <span class="ml-2 text-blue-600">(filtered by: {{ employee_filter }})</span>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Table -->
            <div class="overflow-x-auto">
                <table class="w-full table-auto divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                                SN
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                                Select
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                                Fin Year
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                                MRS No
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                                Date
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Gen By
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for mrs in mrs_list %}
                        <tr class="hover:bg-gray-50 transition-colors duration-150 {% cycle 'bg-white' 'bg-gray-25' %}">
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 border-r border-gray-200">
                                {{ forloop.counter }}
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm border-r border-gray-200">
                                <a href="{% url 'inventory:mrs_detail' mrs.pk %}" 
                                   class="text-blue-600 hover:text-blue-800 font-medium hover:underline transition-colors duration-150">
                                    Select
                                </a>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 border-r border-gray-200">
                                {{ mrs.fin_year|default:"2022-2023" }}
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 border-r border-gray-200 font-medium">
                                {{ mrs.mrs_number }}
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 border-r border-gray-200">
                                {% if mrs.sys_date %}
                                    {% with mrs.sys_date|date:"d-m-Y" as formatted_date %}
                                        {{ formatted_date }}
                                    {% endwith %}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                {{ mrs.employee_name|default:"Unknown Employee" }}
                                {% if mrs.department and mrs.department != "N/A" %}
                                    <div class="text-xs text-gray-500">{{ mrs.department }}</div>
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="px-4 py-8 text-center text-sm text-gray-500">
                                <div class="flex flex-col items-center">
                                    <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    <p class="text-gray-500">No Material Requisition Slips found</p>
                                    <p class="text-xs text-gray-400 mt-1">Try adjusting your search criteria</p>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if pagination.total_pages > 1 %}
            <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center text-sm text-gray-700">
                        <span class="mr-2">Records per page:</span>
                        <select class="border border-gray-300 rounded px-2 py-1 text-sm focus:ring-blue-500 focus:border-blue-500" 
                                onchange="changePerPage(this.value)">
                            <option value="50" {% if pagination.per_page == 50 %}selected{% endif %}>50</option>
                            <option value="100" {% if pagination.per_page == 100 %}selected{% endif %}>100</option>
                            <option value="200" {% if pagination.per_page == 200 %}selected{% endif %}>200</option>
                        </select>
                    </div>
                    
                    <!-- Pagination controls -->
                    <div class="flex items-center space-x-1">
                        <span class="text-sm text-gray-700 mr-2">Page:</span>
                        
                        <!-- Previous button -->
                        {% if pagination.has_previous %}
                        <a href="?page={{ pagination.previous_page }}{% if employee_filter %}&employee_name={{ employee_filter }}{% endif %}&per_page={{ pagination.per_page }}" 
                           class="px-2 py-1 text-sm text-blue-600 hover:bg-blue-50 rounded border border-gray-300">
                            Previous
                        </a>
                        {% endif %}
                        
                        <!-- Page numbers -->
                        {% for page_num in pagination.current_page|add:"-5"|add:"1 2 3 4 5 6 7 8 9 10"|make_list %}
                            {% if page_num|add:"0" > 0 and page_num|add:"0" <= pagination.total_pages %}
                                {% if page_num|add:"0" == pagination.current_page %}
                                    <span class="px-2 py-1 text-sm bg-blue-600 text-white rounded">{{ page_num|add:"0" }}</span>
                                {% else %}
                                    <a href="?page={{ page_num|add:"0" }}{% if employee_filter %}&employee_name={{ employee_filter }}{% endif %}&per_page={{ pagination.per_page }}" 
                                       class="px-2 py-1 text-sm text-blue-600 hover:bg-blue-50 rounded border border-gray-300">
                                        {{ page_num|add:"0" }}
                                    </a>
                                {% endif %}
                            {% endif %}
                        {% endfor %}
                        
                        <!-- Next button -->
                        {% if pagination.has_next %}
                        <a href="?page={{ pagination.next_page }}{% if employee_filter %}&employee_name={{ employee_filter }}{% endif %}&per_page={{ pagination.per_page }}" 
                           class="px-2 py-1 text-sm text-blue-600 hover:bg-blue-50 rounded border border-gray-300">
                            Next
                        </a>
                        {% endif %}
                        
                        <span class="text-sm text-gray-500 ml-2">
                            ({{ pagination.total_pages }} pages total)
                        </span>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// Simple row selection behavior
document.addEventListener('DOMContentLoaded', function() {
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach(row => {
        row.addEventListener('click', function(e) {
            if (e.target.tagName !== 'A') {
                const selectLink = this.querySelector('a');
                if (selectLink) {
                    selectLink.click();
                }
            }
        });
    });
});

// Change per page function
function changePerPage(perPage) {
    const url = new URL(window.location);
    url.searchParams.set('per_page', perPage);
    url.searchParams.set('page', 1); // Reset to first page
    window.location.href = url.toString();
}
</script>
{% endblock %}