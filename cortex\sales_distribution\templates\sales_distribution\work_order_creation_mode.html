{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/umd/lucide.min.css">
<script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-sap-gray-50 font-sap">
    <!-- SAP Fiori Header -->
    <div class="bg-white shadow-sm border-b border-sap-gray-200">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-sap-blue-600 rounded-lg flex items-center justify-center">
                            <i data-lucide="plus-circle" class="w-5 h-5 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-xl font-semibold text-sap-gray-900">{{ page_title }}</h1>
                            <p class="text-sm text-sap-gray-600">Choose your preferred work order creation method</p>
                        </div>
                    </div>
                </div>
                <nav class="flex items-center space-x-2 text-sm text-sap-gray-500">
                    <a href="{% url 'sales_distribution:dashboard' %}" class="hover:text-sap-blue-600 transition-colors">Sales & Distribution</a>
                    <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    <span class="text-sap-gray-900 font-medium">Work Order Creation</span>
                </nav>
            </div>
        </div>
    </div>

    <div class="max-w-5xl mx-auto px-6 py-8">
        <!-- Creation Mode Selection -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            
            <!-- PO-Based Creation -->
            <div class="sap-card hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                <div class="text-center">
                    <div class="w-20 h-20 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i data-lucide="file-check" class="w-10 h-10 text-green-600"></i>
                    </div>
                    <h2 class="text-xl font-semibold text-sap-gray-900 mb-4">Purchase Order Based</h2>
                    <p class="text-sap-gray-600 mb-6 text-sm leading-relaxed">
                        Create work orders from existing purchase orders. This is the standard workflow where formal POs are already available and approved.
                    </p>
                    
                    <div class="space-y-3 mb-8">
                        <div class="flex items-center text-sm text-sap-gray-600">
                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-2"></i>
                            <span>Formal purchase order required</span>
                        </div>
                        <div class="flex items-center text-sm text-sap-gray-600">
                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-2"></i>
                            <span>Auto-populate customer & enquiry details</span>
                        </div>
                        <div class="flex items-center text-sm text-sap-gray-600">
                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-2"></i>
                            <span>Standard approval workflow</span>
                        </div>
                        <div class="flex items-center text-sm text-sap-gray-600">
                            <i data-lucide="check-circle" class="w-4 h-4 text-green-500 mr-2"></i>
                            <span>Complete financial documentation</span>
                        </div>
                    </div>
                    
                    <a href="{% url 'sales_distribution:work_order_po_selection' %}" 
                       class="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 w-full justify-center">
                        <i data-lucide="file-text" class="w-4 h-4 mr-2"></i>
                        Select Purchase Order
                    </a>
                </div>
            </div>

            <!-- Verbal Approval Creation -->
            <div class="sap-card hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                <div class="text-center">
                    <div class="w-20 h-20 bg-orange-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i data-lucide="message-circle" class="w-10 h-10 text-orange-600"></i>
                    </div>
                    <h2 class="text-xl font-semibold text-sap-gray-900 mb-4">Verbal Approval Based</h2>
                    <p class="text-sap-gray-600 mb-6 text-sm leading-relaxed">
                        Start work orders based on verbal approvals when formal POs are pending. This allows quick project initiation while maintaining proper documentation.
                    </p>
                    
                    <div class="space-y-3 mb-8">
                        <div class="flex items-center text-sm text-sap-gray-600">
                            <i data-lucide="check-circle" class="w-4 h-4 text-orange-500 mr-2"></i>
                            <span>No formal PO required initially</span>
                        </div>
                        <div class="flex items-center text-sm text-sap-gray-600">
                            <i data-lucide="check-circle" class="w-4 h-4 text-orange-500 mr-2"></i>
                            <span>Record verbal approval details</span>
                        </div>
                        <div class="flex items-center text-sm text-sap-gray-600">
                            <i data-lucide="check-circle" class="w-4 h-4 text-orange-500 mr-2"></i>
                            <span>Manual customer & project setup</span>
                        </div>
                        <div class="flex items-center text-sm text-sap-gray-600">
                            <i data-lucide="check-circle" class="w-4 h-4 text-orange-500 mr-2"></i>
                            <span>PO can be linked later</span>
                        </div>
                    </div>
                    
                    <a href="{% url 'sales_distribution:work_order_create_verbal' %}" 
                       class="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-orange-600 border border-transparent rounded-lg hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200 w-full justify-center">
                        <i data-lucide="mic" class="w-4 h-4 mr-2"></i>
                        Create from Verbal Approval
                    </a>
                </div>
            </div>
        </div>

        <!-- Information Card -->
        <div class="mt-8 sap-card bg-sap-blue-50 border-sap-blue-200">
            <div class="flex items-start space-x-4">
                <div class="flex-shrink-0">
                    <i data-lucide="info" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-sap-blue-900 mb-2">Important Information</h3>
                    <div class="text-sm text-sap-blue-800 space-y-2">
                        <p><strong>Purchase Order Based:</strong> Choose this option when you have a formal, approved purchase order ready for work order creation.</p>
                        <p><strong>Verbal Approval Based:</strong> Use this when work needs to start based on verbal approval while waiting for formal PO documentation. All verbal approvals must be properly documented with approval details.</p>
                        <p class="text-xs text-sap-blue-700 mt-3">
                            <i data-lucide="shield-check" class="w-3 h-3 inline mr-1"></i>
                            Both methods maintain full audit trails and can be converted between modes as needed.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize Lucide icons
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}