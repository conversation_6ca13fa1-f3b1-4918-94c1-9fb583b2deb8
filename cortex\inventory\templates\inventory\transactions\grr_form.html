{% extends 'core/base.html' %}
{% load static %}

{% block title %}
    {% if object %}Edit{% else %}Create{% endif %} Goods Received Receipt [GRR]
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <div class="bg-blue-600 text-white px-4 py-2">
        <h1 class="text-lg font-bold">
            {% if object %}Edit{% else %}Create{% endif %} Goods Received Receipt [GRR]
        </h1>
    </div>
    
    <div class="p-4">
        <div class="bg-white rounded-lg shadow-sm border max-w-4xl mx-auto">
            <div class="p-6">
                <form method="post" class="space-y-6">
                    {% csrf_token %}
                    
                    {% if form.errors %}
                        <div class="bg-red-50 border border-red-200 rounded-md p-4">
                            <div class="text-red-800 text-sm">
                                <p class="font-medium">Please correct the following errors:</p>
                                <ul class="mt-2 list-disc list-inside">
                                    {% for field, errors in form.errors.items %}
                                        {% for error in errors %}
                                            <li>{{ field|title }}: {{ error }}</li>
                                        {% endfor %}
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    {% endif %}
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- GRR Number -->
                        <div>
                            <label for="{{ form.grr_number.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                GRR Number *
                            </label>
                            {{ form.grr_number }}
                            {% if form.grr_number.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.grr_number.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- GRR Date -->
                        <div>
                            <label for="{{ form.grr_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                GRR Date *
                            </label>
                            {{ form.grr_date }}
                            {% if form.grr_date.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.grr_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- GIN -->
                        <div>
                            <label for="{{ form.gin.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                GIN Reference *
                            </label>
                            {{ form.gin }}
                            {% if form.gin.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.gin.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Tax Invoice Number -->
                        <div>
                            <label for="{{ form.tax_invoice_number.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                Tax Invoice Number
                            </label>
                            {{ form.tax_invoice_number }}
                            {% if form.tax_invoice_number.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.tax_invoice_number.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Tax Invoice Date -->
                        <div>
                            <label for="{{ form.tax_invoice_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                Tax Invoice Date
                            </label>
                            {{ form.tax_invoice_date }}
                            {% if form.tax_invoice_date.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.tax_invoice_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Quality Checked By -->
                        <div>
                            <label for="{{ form.quality_checked_by.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                Quality Checked By
                            </label>
                            {{ form.quality_checked_by }}
                            {% if form.quality_checked_by.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.quality_checked_by.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Remarks -->
                    <div>
                        <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Remarks
                        </label>
                        {{ form.remarks }}
                        {% if form.remarks.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.remarks.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="flex justify-end space-x-3 pt-6 border-t">
                        <a href="{% url 'inventory:grr_list' %}" 
                           class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded text-sm font-medium">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm font-medium">
                            {% if object %}Update{% else %}Create{% endif %} GRR
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}