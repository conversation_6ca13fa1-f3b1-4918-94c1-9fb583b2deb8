from django.contrib import admin
from .models import (
    ItemLocation, WISTimeConfiguration, MaterialRequisitionSlip, MRSLineItem, MRSApprovalHistory,
    MaterialCreditNote, MCNLineItem, MCNApprovalHistory,
)


@admin.register(ItemLocation)
class ItemLocationAdmin(admin.ModelAdmin):
    list_display = ('id', 'location_label', 'location_no', 'description', 'company')
    list_filter = ('location_label', 'company')
    search_fields = ('location_label', 'location_no', 'description')
    readonly_fields = ('sysdate', 'systime')
    fieldsets = (
        ('Basic Information', {
            'fields': ('location_label', 'location_no', 'description')
        }),
        ('System Fields', {
            'fields': ('company', 'financial_year', 'sessionid', 'sysdate', 'systime'),
            'classes': ('collapse',)
        })
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('company', 'financial_year')


@admin.register(WISTimeConfiguration)
class WISTimeConfigurationAdmin(admin.ModelAdmin):
    list_display = ('id', 'auto_issue_time', 'time_to_order', 'company')
    list_filter = ('company',)
    search_fields = ('auto_issue_time', 'time_to_order')
    fieldsets = (
        ('Timer Configuration', {
            'fields': ('auto_issue_time', 'time_to_order')
        }),
        ('System Fields', {
            'fields': ('company', 'financial_year'),
            'classes': ('collapse',)
        })
    )


class MRSLineItemInline(admin.TabularInline):
    model = MRSLineItem
    extra = 0
    fields = ('item_id', 'department_id', 'requested_quantity', 'work_order_number', 'remarks')
    readonly_fields = ('id',)


class MRSApprovalHistoryInline(admin.TabularInline):
    model = MRSApprovalHistory
    extra = 0
    readonly_fields = ('approver', 'action', 'comments', 'action_date')
    fields = ('approver', 'action', 'comments', 'action_date')
    can_delete = False


@admin.register(MaterialRequisitionSlip)
class MaterialRequisitionSlipAdmin(admin.ModelAdmin):
    list_display = ('mrs_number', 'sys_date', 'sys_time', 'session_id', 'company', 'financial_year')
    list_filter = ('company', 'financial_year', 'sys_date')
    search_fields = ('mrs_number', 'session_id')
    readonly_fields = ('id',)
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('mrs_number', 'sys_date', 'sys_time', 'session_id')
        }),
        ('System Fields', {
            'fields': ('company', 'financial_year'),
            'classes': ('collapse',)
        })
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'company', 'financial_year'
        )


@admin.register(MRSLineItem)
class MRSLineItemAdmin(admin.ModelAdmin):
    list_display = ('mrs', 'mrs_number', 'item_id', 'requested_quantity', 'department_id', 'work_order_number')
    list_filter = ('department_id', 'work_order_number')
    search_fields = ('mrs_number', 'item_id', 'work_order_number')
    readonly_fields = ('id',)
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('mrs', 'mrs_number', 'item_id', 'department_id')
        }),
        ('Quantities & Details', {
            'fields': ('requested_quantity', 'work_order_number', 'remarks')
        })
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('mrs')


@admin.register(MRSApprovalHistory)
class MRSApprovalHistoryAdmin(admin.ModelAdmin):
    list_display = ('mrs', 'action', 'approver', 'approval_level', 'action_date')
    list_filter = ('action', 'approval_level', 'action_date')
    search_fields = ('mrs__mrs_number', 'approver__username', 'comments')
    readonly_fields = ('action_date',)
    
    fieldsets = (
        ('Approval Information', {
            'fields': ('mrs', 'approver', 'approval_level', 'action')
        }),
        ('Details', {
            'fields': ('comments', 'action_date')
        })
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('mrs', 'approver')


# MCN Admin Classes
class MCNLineItemInline(admin.TabularInline):
    model = MCNLineItem
    extra = 0
    fields = ('line_number', 'item_code', 'item_description', 'unit_of_measure', 'current_stock', 'adjustment_quantity', 'adjustment_action', 'unit_rate', 'total_amount', 'line_status')
    readonly_fields = ('total_amount', 'processed_date')


class MCNApprovalHistoryInline(admin.TabularInline):
    model = MCNApprovalHistory
    extra = 0
    readonly_fields = ('approver', 'action', 'comments', 'action_date')
    fields = ('approver', 'action', 'comments', 'action_date')
    can_delete = False


@admin.register(MaterialCreditNote)
class MaterialCreditNoteAdmin(admin.ModelAdmin):
    list_display = ('mcn_number', 'mcn_date', 'adjustment_type', 'priority', 'status', 'requested_by', 'total_items', 'total_adjustment_value', 'is_emergency')
    list_filter = ('status', 'adjustment_type', 'priority', 'mcn_date', 'is_emergency', 'company')
    search_fields = ('mcn_number', 'reference_number', 'reason_code', 'reason_description', 'remarks')
    readonly_fields = ('mcn_number', 'requested_date', 'approved_date', 'processed_date', 'total_items', 'total_adjustment_value', 'created_date', 'modified_date')
    date_hierarchy = 'mcn_date'
    inlines = [MCNLineItemInline, MCNApprovalHistoryInline]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('mcn_number', 'mcn_date', 'adjustment_type', 'status', 'priority')
        }),
        ('Reference Details', {
            'fields': ('reference_number', 'reference_date', 'reason_code', 'reason_description')
        }),
        ('Approval Information', {
            'fields': ('requested_by', 'requested_date', 'approved_by', 'approved_date', 'processed_by', 'processed_date'),
            'classes': ('collapse',)
        }),
        ('Additional Information', {
            'fields': ('department_id', 'remarks', 'is_emergency')
        }),
        ('Statistics', {
            'fields': ('total_items', 'total_adjustment_value'),
            'classes': ('collapse',)
        }),
        ('System Fields', {
            'fields': ('company', 'financial_year', 'created_by', 'created_date', 'modified_by', 'modified_date'),
            'classes': ('collapse',)
        })
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'company', 'financial_year', 'requested_by', 'approved_by', 'processed_by'
        ).prefetch_related('line_items')


@admin.register(MCNLineItem)
class MCNLineItemAdmin(admin.ModelAdmin):
    list_display = ('mcn', 'line_number', 'item_code', 'item_description', 'adjustment_quantity', 'adjustment_action', 'unit_rate', 'total_amount', 'line_status')
    list_filter = ('line_status', 'adjustment_action', 'unit_of_measure', 'mcn__status')
    search_fields = ('item_code', 'item_description', 'mcn__mcn_number', 'batch_number', 'serial_number')
    readonly_fields = ('total_amount', 'processed_date', 'created_date')
    
    fieldsets = (
        ('Item Information', {
            'fields': ('mcn', 'line_number', 'item_code', 'item_description', 'unit_of_measure')
        }),
        ('Stock Information', {
            'fields': ('current_stock', 'adjustment_quantity', 'adjustment_action', 'unit_rate', 'total_amount')
        }),
        ('Location & Batch Details', {
            'fields': ('location_code', 'batch_number', 'serial_number', 'expiry_date')
        }),
        ('Additional Details', {
            'fields': ('reason_code', 'line_remarks', 'line_status')
        }),
        ('Processing Information', {
            'fields': ('processed_by', 'processed_date'),
            'classes': ('collapse',)
        }),
        ('System Fields', {
            'fields': ('created_date',),
            'classes': ('collapse',)
        })
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('mcn', 'processed_by')


@admin.register(MCNApprovalHistory)
class MCNApprovalHistoryAdmin(admin.ModelAdmin):
    list_display = ('mcn', 'action', 'approver', 'approval_level', 'action_date')
    list_filter = ('action', 'approval_level', 'action_date')
    search_fields = ('mcn__mcn_number', 'approver__username', 'comments')
    readonly_fields = ('action_date',)
    
    fieldsets = (
        ('Approval Information', {
            'fields': ('mcn', 'approver', 'approval_level', 'action')
        }),
        ('Details', {
            'fields': ('comments', 'action_date')
        })
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('mcn', 'approver')


# Service Note Admin Classes removed since Enhanced models are abstract
