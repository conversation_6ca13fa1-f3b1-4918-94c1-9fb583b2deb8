<!-- accounts/templates/accounts/masters/tds_code_list.html -->
<!-- TDS Code Management List Template -->
<!-- Task Group 4: Taxation Management - TDS Code List -->

{% extends 'core/base.html' %}
{% load accounts_filters %}

{% block title %}TDS Codes - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-purple-600 to-sap-purple-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">TDS Code Management</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Configure Tax Deducted at Source codes and rates for compliance</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Accounts
                </a>
                <a href="{% url 'accounts:tds_code_create' %}" 
                   class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    Add TDS Code
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6" x-data="tdsCodeManagement()">
    
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Total TDS Codes</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ tds_codes.count }}</p>
                </div>
                <div class="w-12 h-12 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="file-text" class="w-6 h-6 text-sap-purple-600"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Active Codes</p>
                    <p class="text-2xl font-bold text-sap-green-600">{{ active_count|default:tds_codes.count }}</p>
                </div>
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="check-circle" class="w-6 h-6 text-sap-green-600"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Average Rate</p>
                    <p class="text-2xl font-bold text-sap-blue-600">{{ average_rate|floatformat:1|default:"0.0" }}%</p>
                </div>
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="trending-up" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Highest Rate</p>
                    <p class="text-2xl font-bold text-sap-red-600">{{ highest_rate|floatformat:1|default:"0.0" }}%</p>
                </div>
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="arrow-up" class="w-6 h-6 text-red-600"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Search and Filters -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm mb-6">
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-sap-gray-700 mb-2">Search</label>
                    <input type="text" x-model="searchTerm" @input="filterCodes" id="search"
                           placeholder="Search by code, description..."
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500">
                </div>
                <div>
                    <label for="rate_range" class="block text-sm font-medium text-sap-gray-700 mb-2">Rate Range</label>
                    <select x-model="rateRange" @change="filterCodes" id="rate_range"
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500">
                        <option value="">All Rates</option>
                        <option value="0-1">0% - 1%</option>
                        <option value="1-5">1% - 5%</option>
                        <option value="5-10">5% - 10%</option>
                        <option value="10-20">10% - 20%</option>
                        <option value="20+">20%+</option>
                    </select>
                </div>
                <div>
                    <label for="category" class="block text-sm font-medium text-sap-gray-700 mb-2">Category</label>
                    <select x-model="categoryFilter" @change="filterCodes" id="category"
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500">
                        <option value="">All Categories</option>
                        <option value="Section 194A">Banking Interest</option>
                        <option value="Section 194C">Contractor Payments</option>
                        <option value="Section 194H">Commission/Brokerage</option>
                        <option value="Section 194I">Rent Payments</option>
                        <option value="Section 194J">Professional Services</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button @click="resetFilters" 
                            class="w-full bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="refresh-cw" class="w-4 h-4 inline mr-2"></i>
                        Reset
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- TDS Codes Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">TDS Codes</h3>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-sap-gray-600" x-text="`${filteredCodes} of ${allCodes} codes`"></span>
                    <div class="flex items-center space-x-1">
                        <button @click="exportCodes" 
                                class="text-sap-gray-600 hover:text-sap-gray-800 p-1">
                            <i data-lucide="download" class="w-4 h-4"></i>
                        </button>
                        <button @click="refreshData" 
                                class="text-sap-gray-600 hover:text-sap-gray-800 p-1">
                            <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        {% if tds_codes %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            <button @click="sortBy('code')" class="flex items-center space-x-1">
                                <span>TDS Code</span>
                                <i data-lucide="chevron-up-down" class="w-3 h-3"></i>
                            </button>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            <button @click="sortBy('percentage')" class="flex items-center space-x-1">
                                <span>TDS Rate</span>
                                <i data-lucide="chevron-up-down" class="w-3 h-3"></i>
                            </button>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Description</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Category</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Sample Deduction</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for tds in tds_codes %}
                    <tr class="hover:bg-sap-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-sap-purple-100 rounded-lg flex items-center justify-center mr-3">
                                    <span class="text-sm font-bold text-sap-purple-600">{{ tds.tds_code|slice:":2"|upper }}</span>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-sap-gray-900">{{ tds.tds_code|default:"N/A" }}</div>
                                    <div class="text-xs text-sap-gray-500">#{{ tds.id }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <span class="text-2xl font-bold text-sap-purple-600">{{ tds.tds_percentage|floatformat:2|default:"0.00" }}%</span>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-sap-gray-900">
                                {{ tds.description|default:"Standard TDS deduction"|truncatechars:60 }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if tds.tds_percentage <= 1 %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-green-100 text-sap-green-800">
                                Low Rate
                            </span>
                            {% elif tds.tds_percentage <= 10 %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-blue-100 text-sap-blue-800">
                                Standard
                            </span>
                            {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-red-100 text-sap-red-800">
                                High Rate
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-600">
                            <div>
                                <div>₹1,00,000 → <span class="font-medium text-sap-purple-600">₹{{ tds.tds_percentage|mul:1000|floatformat:0|default:"0" }}</span></div>
                                <div class="text-xs text-sap-gray-500">TDS amount</div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <button @click="calculateTDS({{ tds.tds_percentage|default:0 }}, '{{ tds.tds_code|default:"TDS" }}')" 
                                        class="text-sap-green-600 hover:text-sap-green-700 p-1" title="Calculate TDS">
                                    <i data-lucide="calculator" class="w-4 h-4"></i>
                                </button>
                                <a href="{% url 'accounts:tds_code_edit' tds.id %}" 
                                   class="text-sap-blue-600 hover:text-sap-blue-700 p-1" title="Edit">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </a>
                                <a href="{% url 'accounts:tds_code_delete' tds.id %}" 
                                   class="text-red-600 hover:text-red-700 p-1" title="Delete"
                                   onclick="return confirm('Are you sure you want to delete this TDS code?')">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="bg-sap-gray-50 px-6 py-3 border-t border-sap-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-sap-gray-600">
                        Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} codes
                    </span>
                </div>
                <div class="flex items-center space-x-1">
                    {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}" 
                       class="px-3 py-1 text-sm bg-white border border-sap-gray-300 rounded hover:bg-sap-gray-50">
                        Previous
                    </a>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <span class="px-3 py-1 text-sm bg-sap-purple-600 text-white rounded">{{ num }}</span>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <a href="?page={{ num }}" 
                       class="px-3 py-1 text-sm bg-white border border-sap-gray-300 rounded hover:bg-sap-gray-50">{{ num }}</a>
                    {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}" 
                       class="px-3 py-1 text-sm bg-white border border-sap-gray-300 rounded hover:bg-sap-gray-50">
                        Next
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
        
        {% else %}
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="w-24 h-24 bg-sap-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i data-lucide="file-text" class="w-12 h-12 text-sap-purple-600"></i>
            </div>
            <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No TDS Codes Configured</h3>
            <p class="text-sap-gray-600 mb-6">Get started by adding your first TDS code for tax compliance.</p>
            <a href="{% url 'accounts:tds_code_create' %}" 
               class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                Add First TDS Code
            </a>
        </div>
        {% endif %}
    </div>
    
    <!-- TDS Calculator Modal -->
    <div x-show="showCalculator" x-transition 
         class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
         @click.self="showCalculator = false">
        <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-sap-gray-800" x-text="calculatorTitle"></h3>
                <button @click="showCalculator = false" class="text-sap-gray-400 hover:text-sap-gray-600">
                    <i data-lucide="x" class="w-5 h-5"></i>
                </button>
            </div>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-sap-gray-700 mb-2">Payment Amount (₹)</label>
                    <input type="number" x-model="calculatorAmount" step="0.01" min="0"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:ring-sap-purple-500 focus:border-sap-purple-500"
                           placeholder="100000">
                </div>
                
                <div x-show="calculatorResult" class="bg-sap-purple-50 rounded-lg p-4">
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-sap-gray-600">Payment Amount:</span>
                            <div class="font-medium" x-text="'₹' + parseFloat(calculatorAmount || 0).toFixed(2)"></div>
                        </div>
                        <div>
                            <span class="text-sap-gray-600">TDS Amount:</span>
                            <div class="font-medium text-sap-purple-600" x-text="'₹' + tdsAmount"></div>
                        </div>
                        <div>
                            <span class="text-sap-gray-600">Net Payment:</span>
                            <div class="font-bold text-sap-gray-800" x-text="'₹' + netAmount"></div>
                        </div>
                        <div>
                            <span class="text-sap-gray-600">TDS Rate:</span>
                            <div class="font-medium text-sap-purple-600" x-text="calculatorRate + '%'"></div>
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center justify-end space-x-3">
                    <button @click="showCalculator = false" 
                            class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                        Close
                    </button>
                    <button @click="performCalculation" 
                            class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        Calculate
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function tdsCodeManagement() {
    return {
        searchTerm: '',
        rateRange: '',
        categoryFilter: '',
        sortField: '',
        sortDirection: 'asc',
        allCodes: {{ tds_codes.count }},
        filteredCodes: {{ tds_codes.count }},
        showCalculator: false,
        calculatorTitle: '',
        calculatorRate: 0,
        calculatorAmount: 100000,
        calculatorResult: false,
        tdsAmount: 0,
        netAmount: 0,
        
        init() {
            lucide.createIcons();
            this.allCodes = document.querySelectorAll('tbody tr').length;
            this.filteredCodes = this.allCodes;
        },
        
        filterCodes() {
            const rows = document.querySelectorAll('tbody tr');
            let visibleCount = 0;
            
            rows.forEach(row => {
                const code = row.querySelector('td:first-child').textContent.toLowerCase();
                const description = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
                const rate = parseFloat(row.querySelector('td:nth-child(2)').textContent);
                
                let visible = true;
                
                // Search filter
                if (this.searchTerm && !code.includes(this.searchTerm.toLowerCase()) && !description.includes(this.searchTerm.toLowerCase())) {
                    visible = false;
                }
                
                // Rate range filter
                if (this.rateRange) {
                    const [min, max] = this.rateRange.split('-').map(v => v === '+' ? 100 : parseFloat(v));
                    if (this.rateRange.includes('+')) {
                        visible = visible && rate >= min;
                    } else {
                        visible = visible && rate >= min && rate <= max;
                    }
                }
                
                // Category filter (based on rate ranges and common TDS sections)
                if (this.categoryFilter) {
                    if (this.categoryFilter.includes('194A') && rate !== 10) visible = false;
                    if (this.categoryFilter.includes('194C') && rate !== 1) visible = false;
                    if (this.categoryFilter.includes('194H') && rate !== 5) visible = false;
                    if (this.categoryFilter.includes('194I') && rate !== 10) visible = false;
                    if (this.categoryFilter.includes('194J') && rate !== 10) visible = false;
                }
                
                row.style.display = visible ? '' : 'none';
                if (visible) visibleCount++;
            });
            
            this.filteredCodes = visibleCount;
        },
        
        resetFilters() {
            this.searchTerm = '';
            this.rateRange = '';
            this.categoryFilter = '';
            this.filterCodes();
        },
        
        sortBy(field) {
            // Simple sorting implementation
            console.log('Sorting by:', field);
        },
        
        calculateTDS(rate, code) {
            this.calculatorRate = rate;
            this.calculatorTitle = `TDS Calculator - ${code} (${rate}%)`;
            this.calculatorAmount = 100000;
            this.calculatorResult = false;
            this.showCalculator = true;
        },
        
        performCalculation() {
            const amount = parseFloat(this.calculatorAmount) || 0;
            this.tdsAmount = (amount * this.calculatorRate / 100).toFixed(2);
            this.netAmount = (amount - parseFloat(this.tdsAmount)).toFixed(2);
            this.calculatorResult = true;
        },
        
        exportCodes() {
            // Export functionality
            console.log('Exporting TDS codes...');
        },
        
        refreshData() {
            window.location.reload();
        }
    }
}

// Auto-calculate when amount changes
document.addEventListener('DOMContentLoaded', function() {
    // Initialize lucide icons
    lucide.createIcons();
});
</script>
{% endblock %}