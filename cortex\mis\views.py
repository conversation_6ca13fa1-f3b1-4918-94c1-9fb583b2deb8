# mis/views.py
# Django class-based views for MIS Budget Management module
# Task Group 1: Budget Management & Allocation

from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView
from django.contrib import messages
from django.db.models import Q, Sum
from django.urls import reverse_lazy
from django.views import View
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_protect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.utils import timezone

from .models import (
    BudgetCode, BudgetPeriod, BudgetAllocation, BudgetDistribution
)
from .forms import (
    BudgetCodeForm, BudgetPeriodForm, BudgetAllocationForm, 
    BudgetDistributionForm, BudgetSearchForm, BudgetApprovalForm
)
from sys_admin.models import Company, FinancialYear
# Removed: import is not needed as context processor handles this automatically


# Budget Code Management Views

class BudgetCodeListView(LoginRequiredMixin, ListView):
    """
    List view for Budget Codes
    Replaces ASP.NET Budget_Code.aspx grid functionality
    """
    model = BudgetCode
    template_name = 'mis/budget_codes/budget_code_list.html'
    context_object_name = 'budget_codes'
    paginate_by = 20

    def get_queryset(self):
        """Get budget codes with search functionality"""
        queryset = BudgetCode.objects.all().order_by('-id')
        
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(description__icontains=search) |
                Q(symbol__icontains=search)
            )
        
        return queryset

    def get_context_data(self, **kwargs):
        """Add additional context data"""
        context = super().get_context_data(**kwargs)
        context['search_query'] = self.request.GET.get('search', '')
        
        # Add statistics
        context['total_codes'] = BudgetCode.objects.count()
        context['active_allocations'] = BudgetAllocation.objects.filter(
            status__in=['approved', 'active']
        ).count()
        
        return context


class BudgetCodeCreateView(LoginRequiredMixin, CreateView):
    """
    Create view for Budget Codes
    Replaces ASP.NET Budget_Code.aspx insert functionality
    """
    model = BudgetCode
    form_class = BudgetCodeForm
    template_name = 'mis/budget_codes/budget_code_form.html'
    success_url = reverse_lazy('mis:budget_code_list')

    def get_context_data(self, **kwargs):
        """Add context data"""
        context = super().get_context_data(**kwargs)
        # Company context handled by context processor
        context['action'] = 'Create'
        return context

    def form_valid(self, form):
        """Handle successful form submission"""
        messages.success(self.request, 'Budget code created successfully.')
        return super().form_valid(form)

    def form_invalid(self, form):
        """Handle form validation errors"""
        messages.error(self.request, 'Please correct the errors below.')
        return super().form_invalid(form)


class BudgetCodeUpdateView(LoginRequiredMixin, UpdateView):
    """
    Update view for Budget Codes
    Replaces ASP.NET Budget_Code.aspx edit functionality
    """
    model = BudgetCode
    form_class = BudgetCodeForm
    template_name = 'mis/budget_codes/budget_code_form.html'
    success_url = reverse_lazy('mis:budget_code_list')

    def get_context_data(self, **kwargs):
        """Add context data"""
        context = super().get_context_data(**kwargs)
        # Company context handled by context processor
        context['action'] = 'Update'
        return context

    def form_valid(self, form):
        """Handle successful form submission"""
        messages.success(self.request, 'Budget code updated successfully.')
        return super().form_valid(form)


class BudgetCodeDeleteView(LoginRequiredMixin, DeleteView):
    """
    Delete view for Budget Codes
    Replaces ASP.NET Budget_Code.aspx delete functionality
    """
    model = BudgetCode
    template_name = 'mis/budget_codes/budget_code_confirm_delete.html'
    success_url = reverse_lazy('mis:budget_code_list')

    def get_context_data(self, **kwargs):
        """Add context data"""
        context = super().get_context_data(**kwargs)
        # Company context handled by context processor
        
        # Check for existing allocations
        context['existing_allocations'] = BudgetAllocation.objects.filter(
            budget_code=self.object
        ).count()
        
        return context

    def delete(self, request, *args, **kwargs):
        """Handle deletion with validation"""
        budget_code = self.get_object()
        
        # Check for existing allocations
        if BudgetAllocation.objects.filter(budget_code=budget_code).exists():
            messages.error(
                request, 
                'Cannot delete budget code with existing allocations. '
                'Please remove all allocations first.'
            )
            return redirect('mis:budget_code_list')
        
        messages.success(request, f'Budget code "{budget_code.description}" deleted successfully.')
        return super().delete(request, *args, **kwargs)


# Budget Period Management Views

class BudgetPeriodListView(LoginRequiredMixin, ListView):
    """List view for Budget Periods"""
    model = BudgetPeriod
    template_name = 'mis/budget_periods/budget_period_list.html'
    context_object_name = 'budget_periods'
    paginate_by = 20

    def get_queryset(self):
        """Get budget periods for current company"""
        # Get company from global context
        try:
            company = Company.objects.filter(defaultcomp=1).first()
            if not company:
                company = Company.objects.first()
        except:
            company = None
        
        if company:
            return BudgetPeriod.objects.filter(company=company).order_by('-start_date')
        return BudgetPeriod.objects.none()

    def get_context_data(self, **kwargs):
        """Add context data"""
        context = super().get_context_data(**kwargs)
        # Company context handled by context processor
        return context


class BudgetPeriodCreateView(LoginRequiredMixin, CreateView):
    """Create view for Budget Periods"""
    model = BudgetPeriod
    form_class = BudgetPeriodForm
    template_name = 'mis/budget_periods/budget_period_form.html'
    success_url = reverse_lazy('mis:budget_period_list')

    def get_form_kwargs(self):
        """Pass company context to form"""
        kwargs = super().get_form_kwargs()
        # Get company from global context
        try:
            company = Company.objects.filter(defaultcomp=1).first()
            if not company:
                company = Company.objects.first()
        except:
            company = None
        kwargs['company'] = company
        return kwargs

    def form_valid(self, form):
        """Handle successful form submission"""
        # Get company and set on form instance
        try:
            company = Company.objects.filter(defaultcomp=1).first()
            if not company:
                company = Company.objects.first()
        except:
            company = None
        form.instance.company = company
        messages.success(self.request, 'Budget period created successfully.')
        return super().form_valid(form)


# Budget Allocation Management Views

class BudgetAllocationListView(LoginRequiredMixin, ListView):
    """
    List view for Budget Allocations
    Replaces ASP.NET BudgetCode_Allocation.aspx functionality
    """
    model = BudgetAllocation
    template_name = 'mis/budget_allocations/budget_allocation_list.html'
    context_object_name = 'budget_allocations'
    paginate_by = 20

    def get_queryset(self):
        """Get budget allocations with filtering"""
        # Get company from global context
        try:
            company = Company.objects.filter(defaultcomp=1).first()
            if not company:
                company = Company.objects.first()
        except:
            company = None
        
        queryset = BudgetAllocation.objects.filter(company=company).select_related(
            'budget_code', 'budget_period', 'requested_by', 'approved_by'
        ).order_by('-created_at')
        
        # Apply search filters
        form = BudgetSearchForm(self.request.GET)
        if form.is_valid():
            if form.cleaned_data.get('budget_code'):
                queryset = queryset.filter(budget_code=form.cleaned_data['budget_code'])
            if form.cleaned_data.get('allocation_type'):
                queryset = queryset.filter(allocation_type=form.cleaned_data['allocation_type'])
            if form.cleaned_data.get('status'):
                queryset = queryset.filter(status=form.cleaned_data['status'])
            if form.cleaned_data.get('budget_period'):
                queryset = queryset.filter(budget_period=form.cleaned_data['budget_period'])
            if form.cleaned_data.get('amount_min'):
                queryset = queryset.filter(allocated_amount__gte=form.cleaned_data['amount_min'])
            if form.cleaned_data.get('amount_max'):
                queryset = queryset.filter(allocated_amount__lte=form.cleaned_data['amount_max'])
            if form.cleaned_data.get('date_from'):
                queryset = queryset.filter(created_at__date__gte=form.cleaned_data['date_from'])
            if form.cleaned_data.get('date_to'):
                queryset = queryset.filter(created_at__date__lte=form.cleaned_data['date_to'])
        
        return queryset

    def get_context_data(self, **kwargs):
        """Add context data"""
        context = super().get_context_data(**kwargs)
        # Company context handled by context processor
        context['search_form'] = BudgetSearchForm(self.request.GET)
        
        # Add statistics
        company = context.get('company')
        if company:
            context['total_allocated'] = BudgetAllocation.objects.filter(
                company=company, status__in=['approved', 'active']
            ).aggregate(total=Sum('allocated_amount'))['total'] or 0
            
            context['total_utilized'] = BudgetAllocation.objects.filter(
                company=company, status__in=['approved', 'active']
            ).aggregate(total=Sum('utilized_amount'))['total'] or 0
            
            context['pending_approvals'] = BudgetAllocation.objects.filter(
                company=company, status='submitted'
            ).count()
        
        return context


class BudgetAllocationCreateView(LoginRequiredMixin, CreateView):
    """
    Create view for Budget Allocations
    Replaces ASP.NET BudgetCode_Allocation.aspx create functionality
    """
    model = BudgetAllocation
    form_class = BudgetAllocationForm
    template_name = 'mis/budget_allocations/budget_allocation_form.html'
    success_url = reverse_lazy('mis:budget_allocation_list')

    def get_form_kwargs(self):
        """Pass company and user context to form"""
        kwargs = super().get_form_kwargs()
        # Get company from global context
        try:
            company = Company.objects.filter(defaultcomp=1).first()
            if not company:
                company = Company.objects.first()
        except:
            company = None
        kwargs['company'] = company
        kwargs['user'] = self.request.user
        return kwargs

    def form_valid(self, form):
        """Handle successful form submission"""
        # Get company and set on form instance
        try:
            company = Company.objects.filter(defaultcomp=1).first()
            if not company:
                company = Company.objects.first()
        except:
            company = None
        form.instance.company = company
        # Get financial year
        try:
            financial_year = FinancialYear.objects.filter(is_current=True).first()
            if not financial_year:
                financial_year = FinancialYear.objects.order_by('-end_date').first()
        except:
            financial_year = None
        form.instance.financial_year = financial_year
        form.instance.requested_by = self.request.user
        
        messages.success(self.request, 'Budget allocation created successfully.')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        """Add context data"""
        context = super().get_context_data(**kwargs)
        # Company context handled by context processor
        context['action'] = 'Create'
        return context


class BudgetAllocationDetailView(LoginRequiredMixin, DetailView):
    """Detail view for Budget Allocations with distributions"""
    model = BudgetAllocation
    template_name = 'mis/budget_allocations/budget_allocation_detail.html'
    context_object_name = 'budget_allocation'

    def get_context_data(self, **kwargs):
        """Add context data including distributions"""
        context = super().get_context_data(**kwargs)
        # Company context handled by context processor
        
        # Get distributions
        context['distributions'] = self.object.distributions.filter(is_active=True)
        
        # Add approval form if user can approve
        if self.object.status == 'submitted' and self.object.can_approve(self.request.user):
            context['approval_form'] = BudgetApprovalForm()
        
        return context


class BudgetAllocationUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for Budget Allocations"""
    model = BudgetAllocation
    form_class = BudgetAllocationForm
    template_name = 'mis/budget_allocations/budget_allocation_form.html'

    def get_form_kwargs(self):
        """Pass company context to form"""
        kwargs = super().get_form_kwargs()
        # Get company from global context
        try:
            company = Company.objects.filter(defaultcomp=1).first()
            if not company:
                company = Company.objects.first()
        except:
            company = None
        kwargs['company'] = company
        kwargs['user'] = self.request.user
        return kwargs

    def get_context_data(self, **kwargs):
        """Add context data"""
        context = super().get_context_data(**kwargs)
        # Company context handled by context processor
        context['action'] = 'Update'
        return context

    def form_valid(self, form):
        """Handle successful form submission"""
        # Only allow updates if in draft status
        if self.object.status != 'draft':
            messages.error(self.request, 'Only draft allocations can be updated.')
            return redirect('mis:budget_allocation_detail', pk=self.object.pk)
        
        messages.success(self.request, 'Budget allocation updated successfully.')
        return super().form_valid(form)

    def get_success_url(self):
        """Return to detail view after update"""
        return reverse_lazy('mis:budget_allocation_detail', kwargs={'pk': self.object.pk})


# Budget Distribution Management Views

class BudgetDistributionCreateView(LoginRequiredMixin, CreateView):
    """
    Create view for Budget Distributions
    Replaces ASP.NET Budget_Dist.aspx functionality
    """
    model = BudgetDistribution
    form_class = BudgetDistributionForm
    template_name = 'mis/budget_distributions/budget_distribution_form.html'

    def dispatch(self, request, *args, **kwargs):
        """Get budget allocation and verify permissions"""
        self.budget_allocation = get_object_or_404(
            BudgetAllocation, 
            pk=kwargs['allocation_pk']
        )
        return super().dispatch(request, *args, **kwargs)

    def form_valid(self, form):
        """Handle successful form submission"""
        form.instance.budget_allocation = self.budget_allocation
        messages.success(self.request, 'Budget distribution created successfully.')
        return super().form_valid(form)

    def get_success_url(self):
        """Return to allocation detail view"""
        return reverse_lazy('mis:budget_allocation_detail', kwargs={'pk': self.budget_allocation.pk})

    def get_context_data(self, **kwargs):
        """Add context data"""
        context = super().get_context_data(**kwargs)
        # Company context handled by context processor
        context['budget_allocation'] = self.budget_allocation
        context['action'] = 'Create'
        return context


# HTMX and API Views

@method_decorator(csrf_protect, name='dispatch')
class BudgetSearchView(LoginRequiredMixin, View):
    """HTMX view for real-time budget search"""
    
    def get(self, request):
        """Handle HTMX search requests"""
        form = BudgetSearchForm(request.GET)
        allocations = BudgetAllocation.objects.none()
        
        if form.is_valid():
            # Get company from global context
            try:
                company = Company.objects.filter(defaultcomp=1).first()
                if not company:
                    company = Company.objects.first()
            except:
                company = None
            
            allocations = BudgetAllocation.objects.filter(company=company)
            
            # Apply filters (same logic as in ListView)
            if form.cleaned_data.get('budget_code'):
                allocations = allocations.filter(budget_code=form.cleaned_data['budget_code'])
            # ... (apply other filters)
        
        return render(request, 'mis/budget_allocations/partials/allocation_results.html', {
            'budget_allocations': allocations[:20]  # Limit results
        })


class BudgetAllocationApprovalView(LoginRequiredMixin, View):
    """Handle budget allocation approval/rejection"""
    
    def post(self, request, pk):
        """Process approval action"""
        allocation = get_object_or_404(BudgetAllocation, pk=pk)
        form = BudgetApprovalForm(request.POST)
        
        if not allocation.can_approve(request.user):
            messages.error(request, 'You do not have permission to approve this allocation.')
            return redirect('mis:budget_allocation_detail', pk=pk)
        
        if form.is_valid():
            action = form.cleaned_data['action']
            comments = form.cleaned_data['comments']
            
            if action == 'approve':
                if allocation.approve(request.user):
                    messages.success(request, 'Budget allocation approved successfully.')
                else:
                    messages.error(request, 'Failed to approve allocation.')
            
            elif action == 'reject':
                allocation.status = 'rejected'
                allocation.approved_by = request.user
                allocation.approved_at = timezone.now()
                allocation.save()
                messages.info(request, 'Budget allocation rejected.')
            
            # TODO: Add audit log for approval actions
        
        return redirect('mis:budget_allocation_detail', pk=pk)


# Dashboard and Analytics Views

class BudgetDashboardView(LoginRequiredMixin, View):
    """
    Main budget management dashboard
    Replaces ASP.NET main navigation functionality
    """
    template_name = 'mis/dashboard/budget_dashboard.html'
    
    def get(self, request):
        """Display budget dashboard with key metrics"""
        context = {}
        # Get company from global context
        try:
            company = Company.objects.filter(defaultcomp=1).first()
            if not company:
                company = Company.objects.first()
        except:
            company = None
        
        if company:
            # Key metrics
            context.update({
                'total_budget_codes': BudgetCode.objects.count(),
                'active_periods': BudgetPeriod.objects.filter(
                    company=company, is_active=True
                ).count(),
                'total_allocations': BudgetAllocation.objects.filter(company=company).count(),
                'pending_approvals': BudgetAllocation.objects.filter(
                    company=company, status='submitted'
                ).count(),
                'total_allocated_amount': BudgetAllocation.objects.filter(
                    company=company, status__in=['approved', 'active']
                ).aggregate(total=Sum('allocated_amount'))['total'] or 0,
                'total_utilized_amount': BudgetAllocation.objects.filter(
                    company=company, status__in=['approved', 'active']
                ).aggregate(total=Sum('utilized_amount'))['total'] or 0,
            })
            
            # Recent allocations
            context['recent_allocations'] = BudgetAllocation.objects.filter(
                company=company
            ).select_related('budget_code', 'budget_period').order_by('-created_at')[:10]
            
            # Budget utilization by code
            context['budget_utilization'] = BudgetAllocation.objects.filter(
                company=company, status__in=['approved', 'active']
            ).values('budget_code__description', 'budget_code__symbol').annotate(
                total_allocated=Sum('allocated_amount'),
                total_utilized=Sum('utilized_amount')
            ).order_by('-total_allocated')[:10]
        
        return render(request, self.template_name, context)
