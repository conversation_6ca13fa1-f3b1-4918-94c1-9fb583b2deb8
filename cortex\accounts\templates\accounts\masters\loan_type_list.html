<!-- accounts/templates/accounts/masters/loan_type_list.html -->
<!-- Loan Type Management List Template -->
<!-- Task Package 1: Master Data Templates - Loan Type Management -->

{% extends 'core/base.html' %}

{% block title %}Loan Types - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-green-600 to-sap-green-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="banknote" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Loan Type Management</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Configure loan types with terms, interest rates and eligibility criteria</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Accounts
                </a>
                <a href="{% url 'accounts:loan_type_create' %}" 
                   class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    Add Loan Type
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6" x-data="loanTypeManagement()">
    
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Total Loan Types</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ loan_types.count }}</p>
                </div>
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="banknote" class="w-6 h-6 text-sap-green-600"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Short Term</p>
                    <p class="text-2xl font-bold text-sap-blue-600">{{ short_term_count|default:0 }}</p>
                </div>
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="clock" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Long Term</p>
                    <p class="text-2xl font-bold text-sap-orange-600">{{ long_term_count|default:0 }}</p>
                </div>
                <div class="w-12 h-12 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="calendar" class="w-6 h-6 text-sap-orange-600"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Avg Interest Rate</p>
                    <p class="text-2xl font-bold text-sap-red-600">{{ average_rate|floatformat:2|default:"0.00" }}%</p>
                </div>
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="percent" class="w-6 h-6 text-red-600"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Search and Filters -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm mb-6">
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-sap-gray-700 mb-2">Search</label>
                    <input type="text" x-model="searchTerm" @input="filterTypes" id="search"
                           placeholder="Search by loan type, terms..."
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                </div>
                <div>
                    <label for="rate_range" class="block text-sm font-medium text-sap-gray-700 mb-2">Interest Rate Range</label>
                    <select x-model="rateRange" @change="filterTypes" id="rate_range"
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                        <option value="">All Rates</option>
                        <option value="0-5">0% - 5%</option>
                        <option value="5-10">5% - 10%</option>
                        <option value="10-15">10% - 15%</option>
                        <option value="15-20">15% - 20%</option>
                        <option value="20+">20%+</option>
                    </select>
                </div>
                <div>
                    <label for="term_category" class="block text-sm font-medium text-sap-gray-700 mb-2">Term Category</label>
                    <select x-model="termFilter" @change="filterTypes" id="term_category"
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                        <option value="">All Terms</option>
                        <option value="Short">Short Term (≤12 months)</option>
                        <option value="Medium">Medium Term (1-5 years)</option>
                        <option value="Long">Long Term (>5 years)</option>
                        <option value="Flexible">Flexible Terms</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button @click="resetFilters" 
                            class="w-full bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="refresh-cw" class="w-4 h-4 inline mr-2"></i>
                        Reset
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Loan Types Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Loan Types</h3>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-sap-gray-600" x-text="`${filteredTypes} of ${allTypes} types`"></span>
                    <div class="flex items-center space-x-1">
                        <button @click="exportTypes" 
                                class="text-sap-gray-600 hover:text-sap-gray-800 p-1" title="Export Loan Types">
                            <i data-lucide="download" class="w-4 h-4"></i>
                        </button>
                        <button @click="refreshData" 
                                class="text-sap-gray-600 hover:text-sap-gray-800 p-1" title="Refresh Data">
                            <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        {% if loan_types %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            <button @click="sortBy('type')" class="flex items-center space-x-1">
                                <span>Loan Type</span>
                                <i data-lucide="chevron-up-down" class="w-3 h-3"></i>
                            </button>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            <button @click="sortBy('rate')" class="flex items-center space-x-1">
                                <span>Interest Rates</span>
                                <i data-lucide="chevron-up-down" class="w-3 h-3"></i>
                            </button>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Terms & Duration</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Eligibility</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Term Validation</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for loan in loan_types %}
                    <tr class="hover:bg-sap-gray-50" x-show="isVisible({{ loan.id }})">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-sap-green-100 rounded-lg flex items-center justify-center mr-3">
                                    <i data-lucide="banknote" class="w-5 h-5 text-sap-green-600"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-sap-gray-900">{{ loan.loan_type|default:"Personal Loan" }}</div>
                                    <div class="text-xs text-sap-gray-500">{{ loan.description|truncatechars:30|default:"Standard loan product" }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div>
                                <div class="text-lg font-bold text-sap-green-600">{{ loan.min_rate|floatformat:2|default:"8.50" }}% - {{ loan.max_rate|floatformat:2|default:"12.50" }}%</div>
                                <div class="text-xs text-sap-gray-500">per annum</div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div>
                                <div class="text-sm font-medium text-sap-gray-900">{{ loan.min_term|default:"12" }} - {{ loan.max_term|default:"60" }} months</div>
                                <div class="text-xs text-sap-gray-500">
                                    {% if loan.max_term <= 12 %}Short Term{% elif loan.max_term <= 60 %}Medium Term{% else %}Long Term{% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-sap-gray-900">
                                {% if loan.eligibility_criteria %}
                                {{ loan.eligibility_criteria|truncatechars:40 }}
                                {% else %}
                                Standard eligibility criteria apply
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if loan.max_rate <= 15 and loan.max_term <= 84 %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-green-100 text-sap-green-800">
                                <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                Valid
                            </span>
                            {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-yellow-100 text-sap-yellow-800">
                                <i data-lucide="alert-circle" class="w-3 h-3 mr-1"></i>
                                Review
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <button @click="calculateEMI({{ loan.min_rate|default:10 }}, {{ loan.max_term|default:36 }}, '{{ loan.loan_type|default:"Loan" }}')" 
                                        class="text-sap-green-600 hover:text-sap-green-700 p-1" title="Calculate EMI">
                                    <i data-lucide="calculator" class="w-4 h-4"></i>
                                </button>
                                <button @click="viewDetails('{{ loan.loan_type|default:"Loan" }}', '{{ loan.eligibility_criteria|default:"Standard eligibility" }}')" 
                                        class="text-sap-blue-600 hover:text-sap-blue-700 p-1" title="View Details">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                </button>
                                <a href="{% url 'accounts:loan_type_edit' loan.id %}" 
                                   class="text-sap-orange-600 hover:text-sap-orange-700 p-1" title="Edit">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </a>
                                <a href="{% url 'accounts:loan_type_delete' loan.id %}" 
                                   class="text-red-600 hover:text-red-700 p-1" title="Delete"
                                   onclick="return confirm('Are you sure you want to delete this loan type?')">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="w-24 h-24 bg-sap-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i data-lucide="banknote" class="w-12 h-12 text-sap-green-600"></i>
            </div>
            <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No Loan Types Configured</h3>
            <p class="text-sap-gray-600 mb-6">Get started by adding your first loan type with terms and rates.</p>
            <a href="{% url 'accounts:loan_type_create' %}" 
               class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                Add First Loan Type
            </a>
        </div>
        {% endif %}
    </div>
    
    <!-- EMI Calculator Modal -->
    <div x-show="showCalculator" x-transition 
         class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
         @click.self="showCalculator = false">
        <div class="bg-white rounded-lg p-6 w-full max-w-lg mx-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-sap-gray-800" x-text="calculatorTitle"></h3>
                <button @click="showCalculator = false" class="text-sap-gray-400 hover:text-sap-gray-600">
                    <i data-lucide="x" class="w-5 h-5"></i>
                </button>
            </div>
            
            <div class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-2">Loan Amount (₹)</label>
                        <input type="number" x-model="calculatorAmount" step="1000" min="0"
                               class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:ring-sap-green-500 focus:border-sap-green-500"
                               placeholder="500000">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-2">Term (months)</label>
                        <input type="number" x-model="calculatorTerm" step="1" min="1"
                               class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:ring-sap-green-500 focus:border-sap-green-500"
                               placeholder="36">
                    </div>
                </div>
                
                <div x-show="calculatorResult" class="bg-sap-green-50 rounded-lg p-4">
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-sap-gray-600">Loan Amount:</span>
                            <div class="font-medium" x-text="'₹' + parseFloat(calculatorAmount || 0).toLocaleString()"></div>
                        </div>
                        <div>
                            <span class="text-sap-gray-600">Monthly EMI:</span>
                            <div class="font-bold text-sap-green-600 text-lg" x-text="'₹' + emiAmount"></div>
                        </div>
                        <div>
                            <span class="text-sap-gray-600">Total Interest:</span>
                            <div class="font-medium text-sap-orange-600" x-text="'₹' + totalInterest"></div>
                        </div>
                        <div>
                            <span class="text-sap-gray-600">Total Amount:</span>
                            <div class="font-bold text-sap-gray-800" x-text="'₹' + totalAmount"></div>
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center justify-end space-x-3">
                    <button @click="showCalculator = false" 
                            class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                        Close
                    </button>
                    <button @click="performEMICalculation" 
                            class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        Calculate EMI
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loan Details Modal -->
    <div x-show="showDetails" x-transition 
         class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
         @click.self="showDetails = false">
        <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-sap-gray-800" x-text="detailsTitle"></h3>
                <button @click="showDetails = false" class="text-sap-gray-400 hover:text-sap-gray-600">
                    <i data-lucide="x" class="w-5 h-5"></i>
                </button>
            </div>
            
            <div class="space-y-4">
                <div class="bg-sap-green-50 rounded-lg p-4">
                    <h4 class="font-medium text-sap-green-800 mb-2">Eligibility Criteria</h4>
                    <div class="text-sm text-sap-green-700" x-text="detailsContent"></div>
                </div>
                
                <div class="flex items-center justify-end">
                    <button @click="showDetails = false" 
                            class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function loanTypeManagement() {
    return {
        searchTerm: '',
        rateRange: '',
        termFilter: '',
        sortField: '',
        sortDirection: 'asc',
        allTypes: 0,
        filteredTypes: 0,
        showCalculator: false,
        showDetails: false,
        calculatorTitle: '',
        detailsTitle: '',
        detailsContent: '',
        calculatorRate: 0,
        calculatorAmount: 500000,
        calculatorTerm: 36,
        calculatorResult: false,
        emiAmount: 0,
        totalInterest: 0,
        totalAmount: 0,
        
        init() {
            lucide.createIcons();
            this.allTypes = document.querySelectorAll('tbody tr').length;
            this.filteredTypes = this.allTypes;
        },
        
        filterTypes() {
            const rows = document.querySelectorAll('tbody tr');
            let visibleCount = 0;
            
            rows.forEach(row => {
                const type = row.querySelector('td:first-child').textContent.toLowerCase();
                const rateText = row.querySelector('td:nth-child(2)').textContent;
                const maxRate = parseFloat(rateText.split(' - ')[1]);
                const termText = row.querySelector('td:nth-child(3)').textContent;
                const maxTerm = parseInt(termText.split(' - ')[1]);
                
                let visible = true;
                
                // Search filter
                if (this.searchTerm && !type.includes(this.searchTerm.toLowerCase())) {
                    visible = false;
                }
                
                // Rate range filter
                if (this.rateRange) {
                    const [min, max] = this.rateRange.split('-').map(v => v === '+' ? 100 : parseFloat(v));
                    if (this.rateRange.includes('+')) {
                        visible = visible && maxRate >= min;
                    } else {
                        visible = visible && maxRate >= min && maxRate <= max;
                    }
                }
                
                // Term filter
                if (this.termFilter) {
                    if (this.termFilter === 'Short' && maxTerm > 12) visible = false;
                    if (this.termFilter === 'Medium' && (maxTerm <= 12 || maxTerm > 60)) visible = false;
                    if (this.termFilter === 'Long' && maxTerm <= 60) visible = false;
                }
                
                row.style.display = visible ? '' : 'none';
                if (visible) visibleCount++;
            });
            
            this.filteredTypes = visibleCount;
        },
        
        resetFilters() {
            this.searchTerm = '';
            this.rateRange = '';
            this.termFilter = '';
            this.filterTypes();
        },
        
        sortBy(field) {
            console.log('Sorting by:', field);
        },
        
        isVisible(id) {
            return true;
        },
        
        calculateEMI(rate, term, type) {
            this.calculatorRate = rate;
            this.calculatorTerm = term;
            this.calculatorTitle = `EMI Calculator - ${type} (${rate}% interest)`;
            this.calculatorAmount = 500000;
            this.calculatorResult = false;
            this.showCalculator = true;
        },
        
        performEMICalculation() {
            const principal = parseFloat(this.calculatorAmount) || 0;
            const rate = this.calculatorRate / 12 / 100; // Monthly rate
            const term = parseInt(this.calculatorTerm) || 1;
            
            // EMI calculation: P * r * (1+r)^n / ((1+r)^n - 1)
            const emi = principal * rate * Math.pow(1 + rate, term) / (Math.pow(1 + rate, term) - 1);
            const totalPaid = emi * term;
            const interest = totalPaid - principal;
            
            this.emiAmount = emi.toFixed(0);
            this.totalInterest = interest.toFixed(0);
            this.totalAmount = totalPaid.toFixed(0);
            this.calculatorResult = true;
        },
        
        viewDetails(type, eligibility) {
            this.detailsTitle = `${type} - Loan Details`;
            this.detailsContent = eligibility;
            this.showDetails = true;
        },
        
        exportTypes() {
            console.log('Exporting loan types...');
        },
        
        refreshData() {
            window.location.reload();
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}