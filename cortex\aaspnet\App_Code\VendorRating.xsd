﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="VendorRating" targetNamespace="http://tempuri.org/VendorRating.xsd" xmlns:mstns="http://tempuri.org/VendorRating.xsd" xmlns="http://tempuri.org/VendorRating.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="VendorRating" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="VendorRating" msprop:Generator_DataSetName="VendorRating">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ItemCode" msprop:Generator_UserColumnName="ItemCode" msprop:Generator_ColumnPropNameInRow="ItemCode" msprop:Generator_ColumnVarNameInTable="columnItemCode" msprop:Generator_ColumnPropNameInTable="ItemCodeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ManfDesc" msprop:Generator_UserColumnName="ManfDesc" msprop:Generator_ColumnPropNameInRow="ManfDesc" msprop:Generator_ColumnVarNameInTable="columnManfDesc" msprop:Generator_ColumnPropNameInTable="ManfDescColumn" type="xs:string" minOccurs="0" />
              <xs:element name="UOMBasic" msprop:Generator_UserColumnName="UOMBasic" msprop:Generator_ColumnPropNameInRow="UOMBasic" msprop:Generator_ColumnVarNameInTable="columnUOMBasic" msprop:Generator_ColumnPropNameInTable="UOMBasicColumn" type="xs:string" minOccurs="0" />
              <xs:element name="RecedQty" msprop:Generator_UserColumnName="RecedQty" msprop:Generator_ColumnPropNameInRow="RecedQty" msprop:Generator_ColumnVarNameInTable="columnRecedQty" msprop:Generator_ColumnPropNameInTable="RecedQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="NormalAccQty" msprop:Generator_UserColumnName="NormalAccQty" msprop:Generator_ColumnPropNameInRow="NormalAccQty" msprop:Generator_ColumnVarNameInTable="columnNormalAccQty" msprop:Generator_ColumnPropNameInTable="NormalAccQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="SegregatedQty" msprop:Generator_UserColumnName="SegregatedQty" msprop:Generator_ColumnPropNameInRow="SegregatedQty" msprop:Generator_ColumnVarNameInTable="columnSegregatedQty" msprop:Generator_ColumnPropNameInTable="SegregatedQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="RejectedQty" msprop:Generator_UserColumnName="RejectedQty" msprop:Generator_ColumnPropNameInRow="RejectedQty" msprop:Generator_ColumnVarNameInTable="columnRejectedQty" msprop:Generator_ColumnPropNameInTable="RejectedQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="SupId" msprop:Generator_UserColumnName="SupId" msprop:Generator_ColumnPropNameInRow="SupId" msprop:Generator_ColumnVarNameInTable="columnSupId" msprop:Generator_ColumnPropNameInTable="SupIdColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="Delrate" msprop:Generator_UserColumnName="Delrate" msprop:Generator_ColumnPropNameInRow="Delrate" msprop:Generator_ColumnVarNameInTable="columnDelrate" msprop:Generator_ColumnPropNameInTable="DelrateColumn" type="xs:double" minOccurs="0" />
              <xs:element name="OverallRating" msprop:Generator_UserColumnName="OverallRating" msprop:Generator_ColumnPropNameInRow="OverallRating" msprop:Generator_ColumnVarNameInTable="columnOverallRating" msprop:Generator_ColumnPropNameInTable="OverallRatingColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Rating" msprop:Generator_UserColumnName="Rating" msprop:Generator_ColumnPropNameInRow="Rating" msprop:Generator_ColumnVarNameInTable="columnRating" msprop:Generator_ColumnPropNameInTable="RatingColumn" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>