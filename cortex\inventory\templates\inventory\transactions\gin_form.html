{% extends "core/base.html" %}
{% load static %}

{% block title %}
    {% if object %}Edit GIN - {{ object.gin_number }}{% else %}New Goods Inward Note{% endif %} - Inventory
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-4">
                <li>
                    <div>
                        <a href="{% url 'inventory:gin_list' %}" class="text-gray-400 hover:text-gray-500">
                            <svg class="flex-shrink-0 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                            </svg>
                            <span class="sr-only">Home</span>
                        </a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                        </svg>
                        <a href="{% url 'inventory:gin_list' %}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">GIN</a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                        </svg>
                        <span class="ml-4 text-sm font-medium text-gray-500">
                            {% if object %}Edit - {{ object.gin_number }}{% else %}New GIN{% endif %}
                        </span>
                    </div>
                </li>
            </ol>
        </nav>
        
        <div class="mt-2">
            <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                {% if object %}Edit Goods Inward Note{% else %}New Goods Inward Note{% endif %}
            </h1>
            {% if object %}
                <p class="mt-1 text-sm text-gray-500">
                    GIN Number: {{ object.gin_number }} | Date: {{ object.gin_date }}
                </p>
            {% endif %}
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white shadow rounded-lg">
        <form method="post" class="space-y-6 p-6">
            {% csrf_token %}
            
            {% if form.non_field_errors %}
                <div class="rounded-md bg-red-50 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">Form Errors</h3>
                            <div class="mt-2 text-sm text-red-700">
                                {{ form.non_field_errors }}
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <!-- PO Number -->
                <div>
                    <label for="{{ form.po_number.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Purchase Order Number
                    </label>
                    {{ form.po_number }}
                    {% if form.po_number.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.po_number.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- GIN Date -->
                <div>
                    <label for="{{ form.gin_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        GIN Date
                    </label>
                    {{ form.gin_date }}
                    {% if form.gin_date.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.gin_date.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Challan Number -->
                <div>
                    <label for="{{ form.challan_number.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Challan Number
                    </label>
                    {{ form.challan_number }}
                    {% if form.challan_number.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.challan_number.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Challan Date -->
                <div>
                    <label for="{{ form.challan_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Challan Date
                    </label>
                    {{ form.challan_date }}
                    {% if form.challan_date.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.challan_date.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Gate Entry Number -->
                <div>
                    <label for="{{ form.gate_entry_number.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Gate Entry Number
                    </label>
                    {{ form.gate_entry_number }}
                    {% if form.gate_entry_number.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.gate_entry_number.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Vehicle Number -->
                <div>
                    <label for="{{ form.vehicle_number.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Vehicle Number
                    </label>
                    {{ form.vehicle_number }}
                    {% if form.vehicle_number.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.vehicle_number.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Mode of Transport -->
                <div class="sm:col-span-2">
                    <label for="{{ form.mode_of_transport.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Mode of Transport
                    </label>
                    {{ form.mode_of_transport }}
                    {% if form.mode_of_transport.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.mode_of_transport.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{% url 'inventory:gin_list' %}" 
                   class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Cancel
                </a>
                <button type="submit" 
                        class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    {% if object %}Update GIN{% else %}Create GIN{% endif %}
                </button>
            </div>
        </form>
    </div>

    {% if object %}
    <!-- Quick Actions -->
    <div class="mt-6 bg-gray-50 rounded-lg p-4">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div class="flex space-x-4">
            <a href="{% url 'inventory:gin_edit_detail' object.pk %}" 
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                </svg>
                Advanced Edit
            </a>
            
            <a href="{% url 'inventory:gin_detail' object.pk %}" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                    <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                </svg>
                View Details
            </a>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}