from django.db import models

# Create your models here.
from django.core.exceptions import ValidationError
import datetime

# Create your models here.
from sys_admin.models import City, Country, State, FinancialYear, Company
from design.models import Item


class Product(models.Model):
    """Product/Category Master - Used for product categorization"""
    id = models.AutoField(db_column="Id", primary_key=True)
    name = models.TextField(db_column="Name", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "Category_Master"

    def __str__(self):
        return f"{self.name}" if self.name else f"Product {self.id}"


class Unit(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    unitname = models.TextField(db_column="UnitName", blank=True, null=True)
    symbol = models.TextField(db_column="Symbol", blank=True, null=True)
    effectoninvoice = models.IntegerField(db_column="EffectOnInvoice", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "Unit_Master"


class EnquiryAttachment(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    enqid = models.ForeignKey("Enquiry", models.DO_NOTHING, db_column="EnqId")
    compid = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    sessionid = models.TextField(db_column="SessionId")
    finyearid = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    filename = models.TextField(db_column="FileName", blank=True, null=True)
    filesize = models.FloatField(db_column="FileSize", blank=True, null=True)
    contenttype = models.TextField(db_column="ContentType", blank=True, null=True)
    filedata = models.BinaryField(db_column="FileData", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "SD_Cust_Enquiry_Attach_Master"


class Enquiry(models.Model):
    enqid = models.AutoField(db_column="EnqId", primary_key=True)
    sysdate = models.TextField(db_column="SysDate")
    systime = models.TextField(db_column="SysTime")
    sessionid = models.TextField(db_column="SessionId")
    compid = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    finyearid = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    customerid = models.TextField(db_column="CustomerId", blank=True, null=True)
    customername = models.TextField(db_column="CustomerName", blank=True, null=True)
    regdaddress = models.TextField(db_column="RegdAddress", blank=True, null=True)
    regdcountry = models.ForeignKey(
        Country,
        models.DO_NOTHING,
        db_column="RegdCountry",
        blank=True,
        null=True,
        related_name="enquiry_regd_country_set",
    )
    regdstate = models.ForeignKey(
        State, models.DO_NOTHING, db_column="RegdState", blank=True, null=True, related_name="enquiry_regd_state_set"
    )
    regdcity = models.ForeignKey(
        City, models.DO_NOTHING, db_column="RegdCity", blank=True, null=True, related_name="enquiry_regd_city_set"
    )
    regdpinno = models.TextField(db_column="RegdPinNo", blank=True, null=True)
    regdcontactno = models.TextField(db_column="RegdContactNo", blank=True, null=True)
    regdfaxno = models.TextField(db_column="RegdFaxNo", blank=True, null=True)
    workaddress = models.TextField(db_column="WorkAddress", blank=True, null=True)
    workcountry = models.ForeignKey(
        Country,
        models.DO_NOTHING,
        db_column="WorkCountry",
        blank=True,
        null=True,
        related_name="enquiry_work_country_set",
    )
    workstate = models.ForeignKey(
        State, models.DO_NOTHING, db_column="WorkState", blank=True, null=True, related_name="enquiry_work_state_set"
    )
    workcity = models.ForeignKey(
        City, models.DO_NOTHING, db_column="WorkCity", blank=True, null=True, related_name="enquiry_work_city_set"
    )
    workpinno = models.TextField(db_column="WorkPinNo", blank=True, null=True)
    workcontactno = models.TextField(db_column="WorkContactNo", blank=True, null=True)
    workfaxno = models.TextField(db_column="WorkFaxNo", blank=True, null=True)
    materialdeladdress = models.TextField(db_column="MaterialDelAddress", blank=True, null=True)
    materialdelcountry = models.ForeignKey(
        Country,
        models.DO_NOTHING,
        db_column="MaterialDelCountry",
        blank=True,
        null=True,
        related_name="enquiry_materialdel_country_set",
    )
    materialdelstate = models.ForeignKey(
        State,
        models.DO_NOTHING,
        db_column="MaterialDelState",
        blank=True,
        null=True,
        related_name="enquiry_materialdel_state_set",
    )
    materialdelcity = models.ForeignKey(
        City,
        models.DO_NOTHING,
        db_column="MaterialDelCity",
        blank=True,
        null=True,
        related_name="enquiry_materialdel_city_set",
    )
    materialdelpinno = models.TextField(db_column="MaterialDelPinNo", blank=True, null=True)
    materialdelcontactno = models.TextField(db_column="MaterialDelContactNo", blank=True, null=True)
    materialdelfaxno = models.TextField(db_column="MaterialDelFaxNo", blank=True, null=True)
    contactperson = models.TextField(db_column="ContactPerson", blank=True, null=True)
    juridictioncode = models.TextField(db_column="JuridictionCode", blank=True, null=True)
    commissionurate = models.TextField(db_column="Commissionurate", blank=True, null=True)
    tinvatno = models.TextField(db_column="TinVatNo", blank=True, null=True)
    email = models.TextField(db_column="Email", blank=True, null=True)
    eccno = models.TextField(db_column="EccNo", blank=True, null=True)
    divn = models.TextField(db_column="Divn", blank=True, null=True)
    tincstno = models.TextField(db_column="TinCstNo", blank=True, null=True)
    contactno = models.TextField(db_column="ContactNo", blank=True, null=True)
    range = models.TextField(db_column="Range", blank=True, null=True)
    panno = models.TextField(db_column="PanNo", blank=True, null=True)
    tdscode = models.TextField(db_column="TDSCode", blank=True, null=True)
    remark = models.TextField(db_column="Remark", blank=True, null=True)
    enquiryfor = models.TextField(db_column="EnquiryFor", blank=True, null=True)
    flag = models.IntegerField(db_column="Flag", blank=True, null=True)
    postatus = models.IntegerField(db_column="POStatus", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "SD_Cust_Enquiry_Master"

    def clean(self):
        """Validate model data before saving."""
        # Validate that required fields are present
        if not self.sysdate:
            self.sysdate = datetime.date.today().strftime("%Y-%m-%d")
        if not self.systime:
            self.systime = datetime.datetime.now().strftime("%H:%M:%S")
        if not self.sessionid:
            raise ValidationError("Session ID is required")

        # Check for company and financial year more safely
        try:
            if not hasattr(self, "compid") or not self.compid:
                raise ValidationError("Company is required")
        except AttributeError:
            if not hasattr(self, "compid_id") or not self.compid_id:
                raise ValidationError("Company is required")

        try:
            if not hasattr(self, "finyearid") or not self.finyearid:
                raise ValidationError("Financial Year is required")
        except AttributeError:
            if not hasattr(self, "finyearid_id") or not self.finyearid_id:
                raise ValidationError("Financial Year is required")

        if not self.enquiryfor or not self.enquiryfor.strip():
            raise ValidationError("Enquiry description is required")

    def save(self, *args, **kwargs):
        # Auto-populate system fields if not set
        if not self.sysdate:
            self.sysdate = datetime.date.today().strftime("%Y-%m-%d")
        if not self.systime:
            self.systime = datetime.datetime.now().strftime("%H:%M:%S")

        self.full_clean()  # Run model validation
        super().save(*args, **kwargs)

    def __str__(self):
        customer_info = None
        if self.customername and self.customername.strip():
            customer_info = self.customername
        elif self.customerid and self.customerid.strip():
            customer_info = self.customerid
        else:
            customer_info = "Unknown Customer"
        return f"Enquiry {self.enqid} - {customer_info}"


class PurchaseOrder(models.Model):
    poid = models.AutoField(db_column="POId", primary_key=True)
    sysdate = models.TextField(db_column="SysDate")
    systime = models.TextField(db_column="SysTime")
    sessionid = models.TextField(db_column="SessionId")
    compid = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    finyearid = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    customerid = models.TextField(db_column="CustomerId", blank=True, null=True)
    enqid = models.ForeignKey(Enquiry, models.DO_NOTHING, db_column="EnqId", blank=True, null=True)
    quotationno = models.ForeignKey("Quotation", models.DO_NOTHING, db_column="QuotationNo", blank=True, null=True)
    pono = models.TextField(db_column="PONo", blank=True, null=True)
    podate = models.TextField(db_column="PODate", blank=True, null=True)
    poreceiveddate = models.TextField(db_column="POReceivedDate", blank=True, null=True)
    vendorcode = models.TextField(db_column="VendorCode", blank=True, null=True)
    paymentterms = models.TextField(db_column="PaymentTerms", blank=True, null=True)
    pf = models.TextField(db_column="PF", blank=True, null=True)
    vat = models.TextField(db_column="VAT", blank=True, null=True)
    excise = models.TextField(db_column="Excise", blank=True, null=True)
    octroi = models.TextField(db_column="Octroi", blank=True, null=True)
    cst = models.TextField(db_column="CST", blank=True, null=True)
    # Other standard fields
    warrenty = models.TextField(db_column="Warrenty", blank=True, null=True)
    insurance = models.TextField(db_column="Insurance", blank=True, null=True)
    transport = models.TextField(db_column="Transport", blank=True, null=True)
    noteno = models.TextField(db_column="NoteNo", blank=True, null=True)
    registrationno = models.TextField(db_column="RegistrationNo", blank=True, null=True)
    freight = models.TextField(db_column="Freight", blank=True, null=True)
    remarks = models.TextField(db_column="Remarks", blank=True, null=True)
    validity = models.TextField(db_column="Validity", blank=True, null=True)
    othercharges = models.TextField(db_column="OtherCharges", blank=True, null=True)
    filename = models.TextField(db_column="FileName", blank=True, null=True)
    filesize = models.FloatField(db_column="FileSize", blank=True, null=True)
    contenttype = models.TextField(db_column="ContentType", blank=True, null=True)
    filedata = models.BinaryField(db_column="FileData", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "SD_Cust_PO_Master"


class PurchaseOrderDetails(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sessionid = models.TextField(db_column="SessionId")
    compid = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    finyearid = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    poid = models.ForeignKey(PurchaseOrder, models.DO_NOTHING, db_column="POId")
    itemdesc = models.TextField(db_column="ItemDesc")
    totalqty = models.FloatField(db_column="TotalQty")
    unit = models.ForeignKey("Unit", models.DO_NOTHING, db_column="Unit")
    rate = models.FloatField(db_column="Rate")
    discount = models.FloatField(db_column="Discount")

    class Meta:
        managed = False
        db_table = "SD_Cust_PO_Details"


class Quotation(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate")
    systime = models.TextField(db_column="SysTime")
    sessionid = models.TextField(db_column="SessionId")
    compid = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    finyearid = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    customerid = models.TextField(db_column="CustomerId")
    enqid = models.ForeignKey(Enquiry, models.DO_NOTHING, db_column="EnqId")
    quotationno = models.TextField(db_column="QuotationNo")
    paymentterms = models.TextField(db_column="PaymentTerms", blank=True, null=True)
    pftype = models.IntegerField(db_column="PFType", blank=True, null=True)
    pf = models.FloatField(db_column="PF", blank=True, null=True)
    vatcst = models.IntegerField(db_column="VATCST", blank=True, null=True)
    excise = models.IntegerField(db_column="Excise", blank=True, null=True)
    octroitype = models.IntegerField(db_column="OctroiType", blank=True, null=True)
    octroi = models.FloatField(db_column="Octroi", blank=True, null=True)
    warrenty = models.TextField(db_column="Warrenty", blank=True, null=True)
    insurance = models.FloatField(db_column="Insurance", blank=True, null=True)
    validity = models.TextField(db_column="Validity", blank=True, null=True)
    otherchargestype = models.IntegerField(db_column="OtherChargesType", blank=True, null=True)
    othercharges = models.FloatField(db_column="otherCharges", blank=True, null=True)
    transport = models.TextField(db_column="Transport", blank=True, null=True)
    noteno = models.TextField(db_column="NoteNo", blank=True, null=True)
    registrationno = models.TextField(db_column="RegistrationNo", blank=True, null=True)
    freighttype = models.IntegerField(db_column="FreightType", blank=True, null=True)
    freight = models.FloatField(db_column="Freight", blank=True, null=True)
    remarks = models.TextField(db_column="Remarks", blank=True, null=True)
    deliveryterms = models.TextField(db_column="DeliveryTerms", blank=True, null=True)
    duedate = models.TextField(db_column="DueDate", blank=True, null=True)
    checked = models.IntegerField(db_column="Checked", blank=True, null=True)
    checkedby = models.TextField(db_column="CheckedBy", blank=True, null=True)
    checkeddate = models.TextField(db_column="CheckedDate", blank=True, null=True)
    checkedtime = models.TextField(db_column="CheckedTime", blank=True, null=True)
    approve = models.IntegerField(db_column="Approve", blank=True, null=True)
    approvedby = models.TextField(db_column="ApprovedBy", blank=True, null=True)
    approvedate = models.TextField(db_column="ApproveDate", blank=True, null=True)
    approvetime = models.TextField(db_column="ApproveTime", blank=True, null=True)
    authorize = models.IntegerField(db_column="Authorize", blank=True, null=True)
    authorizedby = models.TextField(db_column="AuthorizedBy", blank=True, null=True)
    authorizedate = models.TextField(db_column="AuthorizeDate", blank=True, null=True)
    authorizetime = models.TextField(db_column="AuthorizeTime", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "SD_Cust_Quotation_Master"


class QuotationDetails(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sessionid = models.TextField(db_column="SessionId")
    compid = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    finyearid = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    mid = models.ForeignKey("Quotation", models.DO_NOTHING, db_column="MId")
    itemdesc = models.TextField(db_column="ItemDesc")
    totalqty = models.FloatField(db_column="TotalQty")
    unit = models.ForeignKey("Unit", models.DO_NOTHING, db_column="Unit")
    rate = models.FloatField(db_column="Rate")
    discount = models.FloatField(db_column="Discount", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "SD_Cust_Quotation_Details"


class WorkorderDispatch(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate", blank=True, null=True)
    systime = models.TextField(db_column="SysTime", blank=True, null=True)
    sessionid = models.TextField(db_column="SessionId")
    compid = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    finyearid = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    dano = models.TextField(db_column="DANo", blank=True, null=True)
    wrno = models.TextField(db_column="WRNo", blank=True, null=True)
    wrid = models.ForeignKey("WorkorderRelease", models.DO_NOTHING, db_column="WRId", blank=True, null=True)
    itemid = models.ForeignKey(Item, models.DO_NOTHING, db_column="ItemId", blank=True, null=True)
    issuedqty = models.FloatField(db_column="IssuedQty", blank=True, null=True)
    dispatchqty = models.FloatField(db_column="DispatchQty", blank=True, null=True)
    freightcharges = models.TextField(db_column="FreightCharges", blank=True, null=True)
    vehicleby = models.TextField(db_column="Vehicleby", blank=True, null=True)
    octroicharges = models.TextField(db_column="OctroiCharges", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "SD_Cust_WorkOrder_Dispatch"


class WorkOrder(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate")
    systime = models.TextField(db_column="SysTime")
    sessionid = models.TextField(db_column="SessionId")
    compid = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    finyearid = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    customerid = models.TextField(db_column="CustomerId")
    enqid = models.ForeignKey(Enquiry, models.DO_NOTHING, db_column="EnqId", blank=True, null=True)
    poid = models.ForeignKey(PurchaseOrder, models.DO_NOTHING, db_column="POId", blank=True, null=True)
    pono = models.TextField(db_column="PONo", blank=True, null=True)
    cid = models.ForeignKey("WorkOrderCategory", models.DO_NOTHING, db_column="CId", blank=True, null=True)
    scid = models.ForeignKey("WorkorderSubcategory", models.DO_NOTHING, db_column="SCId", blank=True, null=True)
    wono = models.TextField(db_column="WONo", blank=True, null=True)
    taskworkorderdate = models.TextField(db_column="TaskWorkOrderDate", blank=True, null=True)
    taskprojecttitle = models.TextField(db_column="TaskProjectTitle", blank=True, null=True)
    taskprojectleader = models.TextField(db_column="TaskProjectLeader", blank=True, null=True)
    taskbusinessgroup = models.IntegerField(db_column="TaskBusinessGroup", blank=True, null=True)
    tasktargetdap_fdate = models.TextField(db_column="TaskTargetDAP_FDate", blank=True, null=True)
    tasktargetdap_tdate = models.TextField(db_column="TaskTargetDAP_TDate", blank=True, null=True)
    taskdesignfinalization_fdate = models.TextField(db_column="TaskDesignFinalization_FDate", blank=True, null=True)
    taskdesignfinalization_tdate = models.TextField(db_column="TaskDesignFinalization_TDate", blank=True, null=True)
    tasktargetmanufg_fdate = models.TextField(db_column="TaskTargetManufg_FDate", blank=True, null=True)
    tasktargetmanufg_tdate = models.TextField(db_column="TaskTargetManufg_TDate", blank=True, null=True)
    tasktargettryout_fdate = models.TextField(db_column="TaskTargetTryOut_FDate", blank=True, null=True)
    tasktargettryout_tdate = models.TextField(db_column="TaskTargetTryOut_TDate", blank=True, null=True)
    tasktargetdespach_fdate = models.TextField(db_column="TaskTargetDespach_FDate", blank=True, null=True)
    tasktargetdespach_tdate = models.TextField(db_column="TaskTargetDespach_TDate", blank=True, null=True)
    tasktargetassembly_fdate = models.TextField(db_column="TaskTargetAssembly_FDate", blank=True, null=True)
    tasktargetassembly_tdate = models.TextField(db_column="TaskTargetAssembly_TDate", blank=True, null=True)
    tasktargetinstalation_fdate = models.TextField(db_column="TaskTargetInstalation_FDate", blank=True, null=True)
    tasktargetinstalation_tdate = models.TextField(db_column="TaskTargetInstalation_TDate", blank=True, null=True)
    taskcustinspection_fdate = models.TextField(db_column="TaskCustInspection_FDate", blank=True, null=True)
    taskcustinspection_tdate = models.TextField(db_column="TaskCustInspection_TDate", blank=True, null=True)
    shippingadd = models.TextField(db_column="ShippingAdd", blank=True, null=True)
    shippingcountry = models.ForeignKey(
        Country,
        models.DO_NOTHING,
        db_column="ShippingCountry",
        blank=True,
        null=True,
        related_name="workorder_shipping_country_set",
    )
    shippingstate = models.ForeignKey(
        State,
        models.DO_NOTHING,
        db_column="ShippingState",
        blank=True,
        null=True,
        related_name="workorder_shipping_state_set",
    )
    shippingcity = models.ForeignKey(
        City,
        models.DO_NOTHING,
        db_column="ShippingCity",
        blank=True,
        null=True,
        related_name="workorder_shipping_city_set",
    )
    shippingcontactperson1 = models.TextField(db_column="ShippingContactPerson1", blank=True, null=True)
    shippingcontactno1 = models.TextField(db_column="ShippingContactNo1", blank=True, null=True)
    shippingemail1 = models.TextField(db_column="ShippingEmail1", blank=True, null=True)
    shippingcontactperson2 = models.TextField(db_column="ShippingContactPerson2", blank=True, null=True)
    shippingcontactno2 = models.TextField(db_column="ShippingContactNo2", blank=True, null=True)
    shippingemail2 = models.TextField(db_column="ShippingEmail2", blank=True, null=True)
    shippingfaxno = models.TextField(db_column="ShippingFaxNo", blank=True, null=True)
    shippingeccno = models.TextField(db_column="ShippingEccNo", blank=True, null=True)
    shippingtincstno = models.TextField(db_column="ShippingTinCstNo", blank=True, null=True)
    shippingtinvatno = models.TextField(db_column="ShippingTinVatNo", blank=True, null=True)
    instractionprimerpainting = models.IntegerField(db_column="InstractionPrimerPainting", blank=True, null=True)
    instractionpainting = models.IntegerField(db_column="InstractionPainting", blank=True, null=True)
    instractionselfcertrept = models.IntegerField(db_column="InstractionSelfCertRept", blank=True, null=True)
    instractionother = models.TextField(db_column="InstractionOther", blank=True, null=True)
    instractionexportcasemark = models.TextField(db_column="InstractionExportCaseMark", blank=True, null=True)
    instractionattachannexure = models.TextField(db_column="InstractionAttachAnnexure", blank=True, null=True)
    releasewis = models.IntegerField(db_column="ReleaseWIS", blank=True, null=True)
    dryactualrun = models.IntegerField(db_column="DryActualRun", blank=True, null=True)
    updatewo = models.IntegerField(db_column="UpdateWO", blank=True, null=True)
    batches = models.FloatField(db_column="Batches", blank=True, null=True)
    closeopen = models.IntegerField(db_column="CloseOpen", blank=True, null=True)
    remarks = models.TextField(db_column="Remarks", blank=True, null=True)
    manufmaterialdate = models.TextField(db_column="ManufMaterialDate", blank=True, null=True)
    boughtoutmaterialdate = models.TextField(db_column="BoughtoutMaterialDate", blank=True, null=True)
    buyer = models.IntegerField(db_column="Buyer", blank=True, null=True)
    r_person = models.TextField(blank=True, null=True)
    critics = models.TextField(db_column="Critics", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "SD_Cust_WorkOrder_Master"


class WorkorderProductsDetails(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    mid = models.ForeignKey("WorkOrder", models.DO_NOTHING, db_column="MId")
    sessionid = models.TextField(db_column="SessionId")
    compid = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    finyearid = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    itemcode = models.ForeignKey(
        Item, models.DO_NOTHING, db_column="ItemCode", to_field="itemcode", blank=True, null=True
    )
    description = models.TextField(db_column="Description", blank=True, null=True)
    qty = models.FloatField(db_column="Qty", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "SD_Cust_WorkOrder_Products_Details"


class WorkorderRelease(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate")
    systime = models.TextField(db_column="SysTime")
    sessionid = models.TextField(db_column="SessionId")
    compid = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId", related_name="sd_workorder_releases")
    finyearid = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId", related_name="sd_workorder_releases")
    wrno = models.TextField(db_column="WRNo", blank=True, null=True)
    wono = models.TextField(db_column="WONo", blank=True, null=True)
    itemid = models.ForeignKey(Item, models.DO_NOTHING, db_column="ItemId", to_field="itemcode", blank=True, null=True)
    issuedqty = models.FloatField(db_column="IssuedQty", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "SD_Cust_WorkOrder_Release"


class Customer(models.Model):
    salesid = models.AutoField(db_column="SalesId", primary_key=True)
    sysdate = models.TextField(db_column="SysDate")
    systime = models.TextField(db_column="SysTime")
    sessionid = models.TextField(db_column="SessionId")
    compid = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    finyearid = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId", blank=True, null=True)
    enqid = models.IntegerField(db_column="EnqId", blank=True, null=True)
    customerid = models.TextField(db_column="CustomerId", blank=True, null=True)
    customer_name = models.TextField(db_column="CustomerName", blank=True, null=True)
    registered_address = models.TextField(db_column="RegdAddress", blank=True, null=True)
    registered_country = models.ForeignKey(Country, models.DO_NOTHING, db_column="RegdCountry", blank=True, null=True)
    registered_state = models.ForeignKey(State, models.DO_NOTHING, db_column="RegdState", blank=True, null=True)
    registered_city = models.ForeignKey(City, models.DO_NOTHING, db_column="RegdCity", blank=True, null=True)
    registered_pin = models.TextField(db_column="RegdPinNo", blank=True, null=True)
    registered_contact_no = models.TextField(db_column="RegdContactNo", blank=True, null=True)
    regdfaxno = models.TextField(db_column="RegdFaxNo", blank=True, null=True)
    works_address = models.TextField(db_column="WorkAddress", blank=True, null=True)
    works_country = models.ForeignKey(
        Country,
        models.DO_NOTHING,
        db_column="WorkCountry",
        related_name="customer_works_country_set",
        blank=True,
        null=True,
    )
    works_state = models.ForeignKey(
        State, models.DO_NOTHING, db_column="WorkState", related_name="customer_works_state_set", blank=True, null=True
    )
    works_city = models.ForeignKey(
        City, models.DO_NOTHING, db_column="WorkCity", related_name="customer_works_city_set", blank=True, null=True
    )
    works_pin = models.TextField(db_column="WorkPinNo", blank=True, null=True)
    works_contact_no = models.TextField(db_column="WorkContactNo", blank=True, null=True)
    workfaxno = models.TextField(db_column="WorkFaxNo", blank=True, null=True)
    material_address = models.TextField(db_column="MaterialDelAddress", blank=True, null=True)
    material_country = models.ForeignKey(
        Country,
        models.DO_NOTHING,
        db_column="MaterialDelCountry",
        related_name="customer_material_country_set",
        blank=True,
        null=True,
    )
    material_state = models.ForeignKey(
        State,
        models.DO_NOTHING,
        db_column="MaterialDelState",
        related_name="customer_material_state_set",
        blank=True,
        null=True,
    )
    material_city = models.ForeignKey(
        City,
        models.DO_NOTHING,
        db_column="MaterialDelCity",
        related_name="customer_material_city_set",
        blank=True,
        null=True,
    )
    material_pin = models.TextField(db_column="MaterialDelPinNo", blank=True, null=True)
    material_contact_no = models.TextField(db_column="MaterialDelContactNo", blank=True, null=True)
    materialdelfaxno = models.TextField(db_column="MaterialDelFaxNo", blank=True, null=True)
    contact_person = models.TextField(db_column="ContactPerson", blank=True, null=True)
    juridictioncode = models.TextField(db_column="JuridictionCode", blank=True, null=True)
    commissionurate = models.TextField(db_column="Commissionurate", blank=True, null=True)
    tinvatno = models.TextField(db_column="TinVatNo", blank=True, null=True)
    email = models.TextField(db_column="Email", blank=True, null=True)
    eccno = models.TextField(db_column="EccNo", blank=True, null=True)
    divn = models.TextField(db_column="Divn", blank=True, null=True)
    tincstno = models.TextField(db_column="TinCstNo", blank=True, null=True)
    contact_no = models.TextField(db_column="ContactNo", blank=True, null=True)
    range = models.TextField(db_column="Range", blank=True, null=True)
    panno = models.TextField(db_column="PanNo", blank=True, null=True)
    tdscode = models.TextField(db_column="TDSCode", blank=True, null=True)
    remarks = models.TextField(db_column="Remark", blank=True, null=True)
    # gst_no has been commented out as the column doesn't exist in the database
    # gst_no = models.TextField(db_column='GST_No', blank=True, null=True)  # Separate field for GST number

    class Meta:
        managed = False
        db_table = "SD_Cust_master"

    def __str__(self):
        """Return customer name for dropdown display"""
        if self.customer_name and self.customer_name.strip():
            return self.customer_name
        elif self.customerid and self.customerid.strip():
            return f"Customer {self.customerid}"
        else:
            return f"Customer {self.salesid}"


class WorkOrderCategory(models.Model):
    cid = models.AutoField(db_column="CId", primary_key=True)
    sysdate = models.TextField(db_column="SysDate", blank=True, null=True)
    systime = models.TextField(db_column="SysTime", blank=True, null=True)
    compid = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId", blank=True, null=True, related_name='sales_workorder_categories')
    finyearid = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId", blank=True, null=True)
    sessionid = models.TextField(db_column="SessionId", blank=True, null=True)
    cname = models.TextField(db_column="CName", blank=True, null=True)
    symbol = models.TextField(db_column="Symbol", blank=True, null=True)
    hassubcat = models.TextField(db_column="HasSubCat", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblSD_WO_Category"


class WorkorderSubcategory(models.Model):
    sub_cid = models.AutoField(db_column="SCId", primary_key=True)
    sysdate = models.TextField(db_column="SysDate", blank=True, null=True)
    systime = models.TextField(db_column="SysTime", blank=True, null=True)
    compid = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId", blank=True, null=True)
    finyearid = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId", blank=True, null=True)
    sessionid = models.TextField(db_column="SessionId", blank=True, null=True)
    cid = models.ForeignKey("WorkOrderCategory", models.DO_NOTHING, db_column="CId")
    sub_c_name = models.TextField(db_column="SCName")

    class Meta:
        managed = False
        db_table = "tblSD_WO_SubCategory"


class WOType(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    wo_type = models.TextField(db_column="WO_Type", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblSD_WO_Type"

    def __str__(self):
        return self.wo_type





