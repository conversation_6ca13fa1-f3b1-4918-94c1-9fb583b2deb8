{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }} - Sales Distribution{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-slate-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- Breadcrumb Navigation -->
        <nav class="flex items-center space-x-2 text-sm mb-8" aria-label="Breadcrumb">
            <div class="flex items-center space-x-2 bg-white/80 backdrop-blur-sm rounded-lg px-4 py-2 border border-slate-200/60 shadow-sm">
                <a href="{% url 'sales_distribution:dashboard' %}" 
                   class="text-blue-600 hover:text-blue-700 transition-colors duration-200 font-medium flex items-center space-x-1">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                    </svg>
                    <span>Sales Distribution</span>
                </a>
                <svg class="w-4 h-4 text-slate-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
                <a href="{% url 'sales_distribution:work_order_dispatch_list' %}" 
                   class="text-blue-600 hover:text-blue-700 transition-colors duration-200 font-medium flex items-center space-x-1">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                    </svg>
                    <span>Work Order Dispatch</span>
                </a>
                <svg class="w-4 h-4 text-slate-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
                <span class="text-slate-700 font-semibold">{{ work_order.wono|default:"WO Details" }}</span>
            </div>
        </nav>

        <!-- Main Header Card -->
        <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200/60 mb-8 overflow-hidden">
            <div class="bg-gradient-to-r from-orange-600 via-orange-700 to-red-700 px-8 py-10">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="mb-6 lg:mb-0">
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                                </svg>
                            </div>
                            <h1 class="text-4xl font-bold text-white tracking-tight">
                                Dispatch Work Order
                            </h1>
                        </div>
                        <p class="text-orange-100 text-lg leading-relaxed max-w-2xl">
                            Work Order: {{ work_order.wono|default:"N/A" }}
                            • Customer: {{ work_order.customerid }}
                        </p>
                    </div>
                    <div class="flex flex-col lg:items-end space-y-4">
                        <div class="flex items-center space-x-4">
                            <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-orange-100 text-orange-800">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                Ready for Dispatch
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Grid -->
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
            
            <!-- Main Content -->
            <div class="xl:col-span-2 space-y-8">
                
                <!-- Work Order Details -->
                <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 overflow-hidden">
                    <div class="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-slate-200/60">
                        <h2 class="text-xl font-bold text-slate-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                            </svg>
                            Work Order Information
                        </h2>
                    </div>
                    
                    <div class="px-6 py-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-slate-600 mb-1">Work Order No.</label>
                                    <div class="text-lg font-semibold text-slate-900">{{ work_order.wono|default:"-" }}</div>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-slate-600 mb-1">Customer ID</label>
                                    <div class="text-lg text-slate-900">{{ work_order.customerid }}</div>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-slate-600 mb-1">Project Title</label>
                                    <div class="text-lg text-slate-900">{{ work_order.taskprojecttitle|default:"-" }}</div>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-slate-600 mb-1">Project Leader</label>
                                    <div class="text-lg text-slate-900">{{ work_order.taskprojectleader|default:"-" }}</div>
                                </div>
                            </div>
                            
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-slate-600 mb-1">Work Order Date</label>
                                    <div class="text-lg text-slate-900">{{ work_order.taskworkorderdate|default:"-" }}</div>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-slate-600 mb-1">Category</label>
                                    <div class="text-lg text-slate-900">{{ work_order.cid.cname|default:"-" }}</div>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-slate-600 mb-1">Sub-Category</label>
                                    <div class="text-lg text-slate-900">{{ work_order.scid.sub_c_name|default:"-" }}</div>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-slate-600 mb-1">Target Dispatch Date</label>
                                    <div class="text-lg text-slate-900">{{ work_order.tasktargetdespach_fdate|default:"-" }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Shipping Information -->
                {% if work_order.shippingadd %}
                <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 overflow-hidden">
                    <div class="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-slate-200/60">
                        <h2 class="text-xl font-bold text-slate-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            Shipping Information
                        </h2>
                    </div>
                    
                    <div class="px-6 py-6">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-slate-600 mb-1">Shipping Address</label>
                                <div class="text-lg text-slate-900">{{ work_order.shippingadd }}</div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                {% if work_order.shippingcountry %}
                                <div>
                                    <label class="block text-sm font-medium text-slate-600 mb-1">Country</label>
                                    <div class="text-lg text-slate-900">{{ work_order.shippingcountry.countryname }}</div>
                                </div>
                                {% endif %}
                                
                                {% if work_order.shippingstate %}
                                <div>
                                    <label class="block text-sm font-medium text-slate-600 mb-1">State</label>
                                    <div class="text-lg text-slate-900">{{ work_order.shippingstate.statename }}</div>
                                </div>
                                {% endif %}
                                
                                {% if work_order.shippingcity %}
                                <div>
                                    <label class="block text-sm font-medium text-slate-600 mb-1">City</label>
                                    <div class="text-lg text-slate-900">{{ work_order.shippingcity.cityname }}</div>
                                </div>
                                {% endif %}
                            </div>
                            
                            {% if work_order.shippingcontactperson1 or work_order.shippingcontactno1 %}
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {% if work_order.shippingcontactperson1 %}
                                <div>
                                    <label class="block text-sm font-medium text-slate-600 mb-1">Contact Person</label>
                                    <div class="text-lg text-slate-900">{{ work_order.shippingcontactperson1 }}</div>
                                </div>
                                {% endif %}
                                
                                {% if work_order.shippingcontactno1 %}
                                <div>
                                    <label class="block text-sm font-medium text-slate-600 mb-1">Contact Number</label>
                                    <div class="text-lg text-slate-900">{{ work_order.shippingcontactno1 }}</div>
                                </div>
                                {% endif %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Sidebar -->
            <div class="space-y-8">
                
                <!-- Dispatch Actions -->
                <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 overflow-hidden">
                    <div class="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-slate-200/60">
                        <h2 class="text-lg font-bold text-slate-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                            Dispatch Actions
                        </h2>
                    </div>
                    <div class="px-6 py-6 space-y-3">
                        <!-- Dispatch Action -->
                        <form method="post" class="w-full">
                            {% csrf_token %}
                            <button type="submit" 
                                    class="w-full inline-flex items-center justify-center px-4 py-3 bg-orange-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                                </svg>
                                Dispatch Work Order
                            </button>
                        </form>

                        <!-- View Details -->
                        <a href="{% url 'sales_distribution:work_order_detail' work_order.pk %}" 
                           class="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                            </svg>
                            View Full Details
                        </a>

                        <!-- Back to List -->
                        <a href="{% url 'sales_distribution:work_order_dispatch_list' %}" 
                           class="w-full inline-flex items-center justify-center px-4 py-2 bg-slate-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                            </svg>
                            Back to Dispatch List
                        </a>
                    </div>
                </div>

                <!-- Work Order Summary -->
                <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 overflow-hidden">
                    <div class="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-slate-200/60">
                        <h2 class="text-lg font-bold text-slate-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                            </svg>
                            Summary
                        </h2>
                    </div>
                    <div class="px-6 py-6 space-y-4">
                        <div class="flex justify-between items-center py-2 border-b border-slate-100">
                            <span class="text-sm font-medium text-slate-600">Work Order No.</span>
                            <span class="text-sm text-slate-900 font-semibold">{{ work_order.wono|default:"-" }}</span>
                        </div>
                        
                        <div class="flex justify-between items-center py-2 border-b border-slate-100">
                            <span class="text-sm font-medium text-slate-600">Customer</span>
                            <span class="text-sm text-slate-900">{{ work_order.customerid }}</span>
                        </div>
                        
                        <div class="flex justify-between items-center py-2 border-b border-slate-100">
                            <span class="text-sm font-medium text-slate-600">Date</span>
                            <span class="text-sm text-slate-900">{{ work_order.taskworkorderdate|default:"-" }}</span>
                        </div>
                        
                        {% if work_order.batches %}
                        <div class="flex justify-between items-center py-2">
                            <span class="text-sm font-medium text-slate-600">Batches</span>
                            <span class="text-sm text-slate-900">{{ work_order.batches|floatformat:0 }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}