﻿<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="Octori.aspx.cs" Inherits="Module_Accounts_Masters_Octori" Title="ERP" Theme="Default" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" Runat="Server">
<link type="text/css" href="../../../Css/yui-datatable.css" rel="stylesheet" />

    <script src="../../../Javascript/PopUpMsg.js" type="text/javascript"></script>
    <link href="../../../Css/StyleSheet.css" rel="stylesheet" type="text/css" />

    <script src="../../../Javascript/loadingNotifier.js" type="text/javascript"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cp3" Runat="Server">
 
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="cp1" Runat="Server">
</asp:Content>
<asp:Content ID="Content4" ContentPlaceHolderID="cp2" Runat="Server">
</asp:Content>
<asp:Content ID="Content5" ContentPlaceHolderID="MainContent" Runat="Server">
</asp:Content>
<asp:Content ID="Content6" ContentPlaceHolderID="ContentPlaceHolder2" Runat="Server">
    <p><br /></p><p></p>
</asp:Content>
<asp:Content ID="Content7" ContentPlaceHolderID="ContentPlaceHolder3" Runat="Server">

   <br />
   
    <table align="center" style="width: 55%">
    
        <tr align="center">
            <td align="left" valign="middle" style="background:url(../../../images/hdbg.JPG)" height="21" class="fontcsswhite"><b>
                &nbsp; Octori </b></td>
        </tr>
    
        <tr align="center">
            <td align="center" valign="top">
               <asp:GridView ID="GridView1"  Width="100%"
                runat="server" 
                AllowPaging="True"
                ShowFooter="True"
                AutoGenerateColumns="False" 
                DataKeyNames="Id" 
                FooterStyle-Wrap="True"
                OnRowDeleted="GridView1_RowDeleted" 
                DataSourceID="SqlDataSource1"
              OnRowUpdated="GridView1_RowUpdated" 
              OnRowCommand="GridView1_RowCommand"
              CssClass="yui-datatable-theme" onrowdatabound="GridView1_RowDataBound" 
                    onrowupdating="GridView1_RowUpdating">
                    
<FooterStyle Wrap="True"></FooterStyle>
                    <Columns>
                        <asp:CommandField ButtonType="Link"  ShowEditButton="True" ValidationGroup="Shree"/>
                        <asp:CommandField   ButtonType="Link" ShowDeleteButton="True"  />
                        
                        <asp:TemplateField HeaderText="SN" SortExpression="Id" >
                        <ItemTemplate>
                       <%# Container.DataItemIndex+1 %>
                        </ItemTemplate>
                        <FooterTemplate>
                        <asp:Button ID="btnInsert" runat="server" Text="Insert"  ValidationGroup="abc"
                         OnClientClick="return confirmationAdd()" CommandName="Add" CssClass="redbox" />
                        </FooterTemplate>
                            <ItemStyle HorizontalAlign="Right" />
                        </asp:TemplateField>
                        
                        <asp:TemplateField HeaderText="Terms" SortExpression="Terms">
                        <ItemTemplate>
                        <asp:Label ID="txtTerms" runat="server" Text='<%#Eval("Terms") %>'>    </asp:Label>
                        </ItemTemplate>
                        
                        <EditItemTemplate>
                        <asp:TextBox ID="txtTerms1" Width="90%" runat="server" Text='<%#Bind("Terms") %>'>
                        </asp:TextBox>
                        <asp:RequiredFieldValidator ID="ReqTerms" ValidationGroup="Shree" ControlToValidate="txtTerms1" 
                            runat="server" ErrorMessage="*">
                            </asp:RequiredFieldValidator>
                        </EditItemTemplate>
                        
                        <FooterTemplate>
                        <asp:TextBox ID="txtTerms2" runat="server" Width="90%">
                        </asp:TextBox>
                            <asp:RequiredFieldValidator ID="ReqTerms" ValidationGroup="abc" ControlToValidate="txtTerms2" 
                            runat="server" ErrorMessage="*">
                            </asp:RequiredFieldValidator>
                        </FooterTemplate>
                            <FooterStyle HorizontalAlign="Left" />
                            <ItemStyle HorizontalAlign="Left" Width="60%" />
                        </asp:TemplateField>
                        
                        <asp:TemplateField HeaderText="Value" SortExpression="Value">
                        <ItemTemplate>
                        <asp:Label ID="txtValue" runat="server" Text='<%#Eval("Value") %>'>    </asp:Label>
                        </ItemTemplate>
                        
                        <EditItemTemplate>
                        <asp:TextBox ID="txtValue1" Width="60%" runat="server" Text='<%#Bind("Value") %>'>
                        </asp:TextBox>
                        
                         <asp:RequiredFieldValidator ID="ReqValue" ValidationGroup="Shree" ControlToValidate="txtValue1"
                          runat="server" ErrorMessage="*">
                          </asp:RequiredFieldValidator>                         

				            <asp:RegularExpressionValidator ID="RegularExpressionValidator1" runat="server" ValidationGroup="Shree"
                            ControlToValidate="txtValue1" ErrorMessage="*"  ValidationExpression="^\d{1,15}(\.\d{0,3})?$">
            				</asp:RegularExpressionValidator>

                        </EditItemTemplate>
                        
                        <FooterTemplate>
                        <asp:TextBox ID="txtValue2" runat="server" Width="60%">
                        </asp:TextBox>
                        <asp:RegularExpressionValidator ID="RegularExpressionValidator2" runat="server" ValidationGroup="abc"
                            ControlToValidate="txtValue2" ErrorMessage="*"  ValidationExpression="^\d{1,15}(\.\d{0,3})?$">
            				</asp:RegularExpressionValidator>
                         <asp:RequiredFieldValidator ID="ReqValue" ValidationGroup="abc" ControlToValidate="txtValue2"
                          runat="server" ErrorMessage="*">
                          </asp:RequiredFieldValidator>
                                                    
				            
            				
                        </FooterTemplate>
                           
                            <ItemStyle Width="15%" />
                           
                        </asp:TemplateField>
                    </Columns>
                    <EmptyDataTemplate>
                    <table>
                    <tr>
                    <td></td>
                    <td align="center">
                        <asp:Label ID="Label1" Font-Bold="true" Font-Size="Medium" Font-Names="Times New Roman" runat="server" Text="Terms"></asp:Label></td>
                        
                        <td align="center">
                         <asp:Label ID="Label2" runat="server" Font-Size="Medium" Font-Names="Times New Roman"  Font-Bold="true" Text="Value"></asp:Label></td>
               </tr>
               <tr>
                    <td>
                   <asp:Button ID="btnInsert" runat="server" Text="Insert" ValidationGroup="pqr" OnClientClick="return confirmationAdd()" CommandName="Add1" CssClass="redbox" /></td>
                <td>
                   <asp:TextBox ID="txtTerms3" runat="server" Width="230" />
                   <asp:RequiredFieldValidator ID="ReqTerm0" ValidationGroup="pqr" ControlToValidate="txtTerms3" runat="server" ErrorMessage="*"></asp:RequiredFieldValidator></td>
                   <td>
                   <asp:TextBox ID="txtValue3" runat="server" Width="50%" />
                     <asp:RequiredFieldValidator ID="Reqvalue0" ValidationGroup="pqr" ControlToValidate="txtValue3" 
                     runat="server" ErrorMessage="*">
                     </asp:RequiredFieldValidator>
                               
				            <asp:RegularExpressionValidator ID="RegularExpressionValidator3" runat="server" ValidationGroup="pqr"
                            ControlToValidate="txtValue3" ErrorMessage="*"  ValidationExpression="^\d{1,15}(\.\d{0,3})?$">
            				</asp:RegularExpressionValidator>
                    
                     </td>
                     </tr>
                     </table>
                   </EmptyDataTemplate>
              </asp:GridView>
                            
              <asp:SqlDataSource ID="SqlDataSource1" runat="server" 
                    ConnectionString="<%$ ConnectionStrings:LocalSqlServer %>" 
                    DeleteCommand="DELETE FROM [tblOctroi_Master] WHERE [Id] = @Id" 
                    InsertCommand="INSERT INTO [tblOctroi_Master] ([Terms], [Value]) VALUES (@Terms, @Value)" 
                    SelectCommand="SELECT * FROM [tblOctroi_Master] order by [Id] desc" 
                    UpdateCommand="UPDATE [tblOctroi_Master] SET [Terms] = @Terms, [Value] = @Value WHERE [Id] = @Id" 
                    ProviderName="System.Data.SqlClient">
                    <DeleteParameters>
                        <asp:Parameter Name="Id" Type="Int32" />
                    </DeleteParameters>
                    <UpdateParameters>
                        <asp:Parameter Name="Terms" Type="String" />
                        <asp:Parameter Name="Value" Type="String" />
                        <asp:Parameter Name="Id" Type="Int32" />
                    </UpdateParameters>
                    <InsertParameters>
                        <asp:Parameter Name="Terms" Type="String" />
                        <asp:Parameter Name="Value" Type="String" />
                    </InsertParameters>
                </asp:SqlDataSource>
                </td>
        </tr>
   <tr>
            <td align="center">
                
    <asp:Label ID="lblMessage" runat="server" Text="Label"></asp:Label>
            </td>
        </tr>
    </table>
<br />    
            
              

</asp:Content>
<asp:Content ID="Content8" ContentPlaceHolderID="ContentPlaceHolder4" Runat="Server">
</asp:Content>

