<!-- Product Edit Row Form -->
<tr class="bg-blue-50 border border-blue-200" id="product-row-{{ product.id }}">
    <!-- Serial Number -->
    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
        {% if page_obj %}
            {{ forloop.counter|add:page_obj.start_index|add:"-1" }}
        {% else %}
            {{ forloop.counter }}
        {% endif %}
    </td>
    
    <!-- Editable Product Name -->
    <td class="px-6 py-4 whitespace-nowrap">
        <div class="flex items-center">
            <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                <i data-lucide="package" class="w-4 h-4 text-blue-600"></i>
            </div>
            <div class="flex-1">
                <form hx-put="{% url 'sales_distribution:product_edit' product.id %}"
                      hx-target="#product-row-{{ product.id }}"
                      hx-swap="outerHTML"
                      class="flex items-center space-x-2">
                    {% csrf_token %}
                    {{ form.name }}
                    {% if form.name.errors %}
                        <div class="text-red-600 text-xs">{{ form.name.errors.0 }}</div>
                    {% endif %}
                    
                    <!-- Action Buttons -->
                    <div class="flex space-x-1">
                        <button type="submit" 
                                title="Save Changes"
                                class="inline-flex items-center p-1.5 border border-green-300 rounded text-green-600 bg-green-50 hover:bg-green-100 transition-colors duration-200">
                            <i data-lucide="check" class="w-3 h-3"></i>
                        </button>
                        <button type="button"
                                hx-get="{% url 'sales_distribution:product_cancel_edit' product.id %}"
                                hx-target="#product-row-{{ product.id }}"
                                hx-swap="outerHTML"
                                title="Cancel Edit"
                                class="inline-flex items-center p-1.5 border border-gray-300 rounded text-gray-600 bg-gray-50 hover:bg-gray-100 transition-colors duration-200">
                            <i data-lucide="x" class="w-3 h-3"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </td>
    
    <!-- Actions (Disabled during edit) -->
    <td class="px-6 py-4 whitespace-nowrap text-center">
        <div class="text-sm text-gray-400">Editing...</div>
    </td>
</tr>