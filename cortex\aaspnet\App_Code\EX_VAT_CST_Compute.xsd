﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="EX_VAT_CST_Compute" targetNamespace="http://tempuri.org/EX_VAT_CST_Compute.xsd" xmlns:mstns="http://tempuri.org/EX_VAT_CST_Compute.xsd" xmlns="http://tempuri.org/EX_VAT_CST_Compute.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="EX_VAT_CST_Compute" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="EX_VAT_CST_Compute" msprop:Generator_DataSetName="EX_VAT_CST_Compute">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="VATerms" msprop:Generator_UserColumnName="VATerms" msprop:Generator_ColumnVarNameInTable="columnVATerms" msprop:Generator_ColumnPropNameInRow="VATerms" msprop:Generator_ColumnPropNameInTable="VATermsColumn" type="xs:string" minOccurs="0" />
              <xs:element name="VATAmt" msprop:Generator_UserColumnName="VATAmt" msprop:Generator_ColumnVarNameInTable="columnVATAmt" msprop:Generator_ColumnPropNameInRow="VATAmt" msprop:Generator_ColumnPropNameInTable="VATAmtColumn" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="DataTable2" msprop:Generator_UserTableName="DataTable2" msprop:Generator_RowDeletedName="DataTable2RowDeleted" msprop:Generator_RowChangedName="DataTable2RowChanged" msprop:Generator_RowClassName="DataTable2Row" msprop:Generator_RowChangingName="DataTable2RowChanging" msprop:Generator_RowEvArgName="DataTable2RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable2RowChangeEventHandler" msprop:Generator_TableClassName="DataTable2DataTable" msprop:Generator_TableVarName="tableDataTable2" msprop:Generator_RowDeletingName="DataTable2RowDeleting" msprop:Generator_TablePropName="DataTable2">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="CSTerms" msprop:Generator_UserColumnName="CSTerms" msprop:Generator_ColumnVarNameInTable="columnCSTerms" msprop:Generator_ColumnPropNameInRow="CSTerms" msprop:Generator_ColumnPropNameInTable="CSTermsColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CSTAmt" msprop:Generator_UserColumnName="CSTAmt" msprop:Generator_ColumnVarNameInTable="columnCSTAmt" msprop:Generator_ColumnPropNameInRow="CSTAmt" msprop:Generator_ColumnPropNameInTable="CSTAmtColumn" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="DataTable3" msprop:Generator_UserTableName="DataTable3" msprop:Generator_RowDeletedName="DataTable3RowDeleted" msprop:Generator_TableClassName="DataTable3DataTable" msprop:Generator_RowChangedName="DataTable3RowChanged" msprop:Generator_RowClassName="DataTable3Row" msprop:Generator_RowChangingName="DataTable3RowChanging" msprop:Generator_RowEvArgName="DataTable3RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable3RowChangeEventHandler" msprop:Generator_TablePropName="DataTable3" msprop:Generator_TableVarName="tableDataTable3" msprop:Generator_RowDeletingName="DataTable3RowDeleting">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ExciseTerm" msprop:Generator_UserColumnName="ExciseTerm" msprop:Generator_ColumnPropNameInRow="ExciseTerm" msprop:Generator_ColumnVarNameInTable="columnExciseTerm" msprop:Generator_ColumnPropNameInTable="ExciseTermColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ExBasicAmt" msprop:Generator_UserColumnName="ExBasicAmt" msprop:Generator_ColumnPropNameInRow="ExBasicAmt" msprop:Generator_ColumnVarNameInTable="columnExBasicAmt" msprop:Generator_ColumnPropNameInTable="ExBasicAmtColumn" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="DataTable4" msprop:Generator_UserTableName="DataTable4" msprop:Generator_RowDeletedName="DataTable4RowDeleted" msprop:Generator_TableClassName="DataTable4DataTable" msprop:Generator_RowChangedName="DataTable4RowChanged" msprop:Generator_RowClassName="DataTable4Row" msprop:Generator_RowChangingName="DataTable4RowChanging" msprop:Generator_RowEvArgName="DataTable4RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable4RowChangeEventHandler" msprop:Generator_TablePropName="DataTable4" msprop:Generator_TableVarName="tableDataTable4" msprop:Generator_RowDeletingName="DataTable4RowDeleting">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="VATTerm" msprop:Generator_UserColumnName="VATTerm" msprop:Generator_ColumnPropNameInRow="VATTerm" msprop:Generator_ColumnVarNameInTable="columnVATTerm" msprop:Generator_ColumnPropNameInTable="VATTermColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Amt" msprop:Generator_UserColumnName="Amt" msprop:Generator_ColumnPropNameInRow="Amt" msprop:Generator_ColumnVarNameInTable="columnAmt" msprop:Generator_ColumnPropNameInTable="AmtColumn" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="DataTable5" msprop:Generator_UserTableName="DataTable5" msprop:Generator_RowDeletedName="DataTable5RowDeleted" msprop:Generator_TableClassName="DataTable5DataTable" msprop:Generator_RowChangedName="DataTable5RowChanged" msprop:Generator_RowClassName="DataTable5Row" msprop:Generator_RowChangingName="DataTable5RowChanging" msprop:Generator_RowEvArgName="DataTable5RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable5RowChangeEventHandler" msprop:Generator_TablePropName="DataTable5" msprop:Generator_TableVarName="tableDataTable5" msprop:Generator_RowDeletingName="DataTable5RowDeleting">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="CSTTerm" msprop:Generator_UserColumnName="CSTTerm" msprop:Generator_ColumnPropNameInRow="CSTTerm" msprop:Generator_ColumnVarNameInTable="columnCSTTerm" msprop:Generator_ColumnPropNameInTable="CSTTermColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CSTAmt" msprop:Generator_UserColumnName="CSTAmt" msprop:Generator_ColumnPropNameInRow="CSTAmt" msprop:Generator_ColumnVarNameInTable="columnCSTAmt" msprop:Generator_ColumnPropNameInTable="CSTAmtColumn" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="DataTable6" msprop:Generator_UserTableName="DataTable6" msprop:Generator_RowDeletedName="DataTable6RowDeleted" msprop:Generator_TableClassName="DataTable6DataTable" msprop:Generator_RowChangedName="DataTable6RowChanged" msprop:Generator_RowClassName="DataTable6Row" msprop:Generator_RowChangingName="DataTable6RowChanging" msprop:Generator_RowEvArgName="DataTable6RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable6RowChangeEventHandler" msprop:Generator_TablePropName="DataTable6" msprop:Generator_TableVarName="tableDataTable6" msprop:Generator_RowDeletingName="DataTable6RowDeleting">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="EXTerm" msprop:Generator_UserColumnName="EXTerm" msprop:Generator_ColumnPropNameInRow="EXTerm" msprop:Generator_ColumnVarNameInTable="columnEXTerm" msprop:Generator_ColumnPropNameInTable="EXTermColumn" type="xs:string" minOccurs="0" />
              <xs:element name="EXAmt" msprop:Generator_UserColumnName="EXAmt" msprop:Generator_ColumnPropNameInRow="EXAmt" msprop:Generator_ColumnVarNameInTable="columnEXAmt" msprop:Generator_ColumnPropNameInTable="EXAmtColumn" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>