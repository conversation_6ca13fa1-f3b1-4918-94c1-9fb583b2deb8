{% extends "core/base.html" %}
{% load static %}

{% block title %}Budget Management Dashboard - MIS{% endblock %}

{% block extra_css %}
<style>
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px;
        padding: 1.5rem;
        color: white;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s ease-in-out;
    }
    .metric-card:hover {
        transform: translateY(-2px);
    }
    .metric-value {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    .chart-container {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        margin-bottom: 1.5rem;
    }
</style>
{% endblock %}

{% block page_header %}
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Budget Management Dashboard</h1>
            <p class="text-gray-600">Management Information System - Financial Control Center</p>
        </div>
        <div class="flex space-x-3">
            <a href="{% url 'mis:budget_code_create' %}" 
               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                <i class="fas fa-plus mr-2"></i>New Budget Code
            </a>
            <a href="{% url 'mis:budget_allocation_create' %}" 
               class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                <i class="fas fa-plus mr-2"></i>New Allocation
            </a>
        </div>
    </div>
{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Key Metrics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Budget Codes -->
        <div class="metric-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="metric-value">{{ total_budget_codes }}</div>
            <div class="text-sm opacity-90">Budget Codes</div>
            <div class="text-xs opacity-75 mt-1">Active classifications</div>
        </div>

        <!-- Active Periods -->
        <div class="metric-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="metric-value">{{ active_periods }}</div>
            <div class="text-sm opacity-90">Active Periods</div>
            <div class="text-xs opacity-75 mt-1">Current budget periods</div>
        </div>

        <!-- Total Allocations -->
        <div class="metric-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            <div class="metric-value">{{ total_allocations }}</div>
            <div class="text-sm opacity-90">Total Allocations</div>
            <div class="text-xs opacity-75 mt-1">All time allocations</div>
        </div>

        <!-- Pending Approvals -->
        <div class="metric-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
            <div class="metric-value">{{ pending_approvals }}</div>
            <div class="text-sm opacity-90">Pending Approvals</div>
            <div class="text-xs opacity-75 mt-1">Requires action</div>
        </div>
    </div>

    <!-- Financial Overview -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Budget Summary -->
        <div class="chart-container">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Budget Summary</h3>
            <div class="space-y-4">
                <div class="flex justify-between items-center p-4 bg-blue-50 rounded-lg">
                    <div>
                        <div class="text-sm text-gray-600">Total Allocated</div>
                        <div class="text-2xl font-bold text-blue-600">₹{{ total_allocated_amount|floatformat:2 }}</div>
                    </div>
                    <div class="text-blue-600">
                        <i class="fas fa-arrow-up text-2xl"></i>
                    </div>
                </div>
                
                <div class="flex justify-between items-center p-4 bg-green-50 rounded-lg">
                    <div>
                        <div class="text-sm text-gray-600">Total Utilized</div>
                        <div class="text-2xl font-bold text-green-600">₹{{ total_utilized_amount|floatformat:2 }}</div>
                    </div>
                    <div class="text-green-600">
                        <i class="fas fa-chart-line text-2xl"></i>
                    </div>
                </div>
                
                {% with remaining=total_allocated_amount|add:"-"|add:total_utilized_amount %}
                <div class="flex justify-between items-center p-4 bg-yellow-50 rounded-lg">
                    <div>
                        <div class="text-sm text-gray-600">Remaining Budget</div>
                        <div class="text-2xl font-bold text-yellow-600">₹{{ remaining|floatformat:2 }}</div>
                    </div>
                    <div class="text-yellow-600">
                        <i class="fas fa-wallet text-2xl"></i>
                    </div>
                </div>
                {% endwith %}
            </div>
        </div>

        <!-- Budget Utilization Chart -->
        <div class="chart-container">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Budget Utilization by Code</h3>
            {% if budget_utilization %}
                <div class="space-y-3">
                    {% for item in budget_utilization %}
                        {% with utilization_percent=item.total_utilized|div:item.total_allocated|mul:100 %}
                        <div class="space-y-2">
                            <div class="flex justify-between text-sm">
                                <span class="font-medium">{{ item.budget_code__symbol }} - {{ item.budget_code__description|truncatechars:20 }}</span>
                                <span class="text-gray-600">{{ utilization_percent|floatformat:1 }}%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: {{ utilization_percent|floatformat:1 }}%"></div>
                            </div>
                            <div class="flex justify-between text-xs text-gray-500">
                                <span>₹{{ item.total_utilized|floatformat:0 }}</span>
                                <span>₹{{ item.total_allocated|floatformat:0 }}</span>
                            </div>
                        </div>
                        {% endwith %}
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-chart-bar text-4xl mb-4"></i>
                    <p>No budget utilization data available</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Recent Allocations -->
    <div class="chart-container">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Recent Allocations</h3>
            <a href="{% url 'mis:budget_allocation_list' %}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                View All →
            </a>
        </div>
        
        {% if recent_allocations %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Budget Code</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Period</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for allocation in recent_allocations %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="text-sm font-medium text-gray-900">{{ allocation.budget_code.symbol }}</div>
                                    <div class="text-sm text-gray-500 ml-2">{{ allocation.budget_code.description|truncatechars:30 }}</div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ allocation.budget_period.name }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    {{ allocation.get_allocation_type_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                ₹{{ allocation.allocated_amount|floatformat:2 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if allocation.status == 'approved' %}
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        Approved
                                    </span>
                                {% elif allocation.status == 'submitted' %}
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        Pending
                                    </span>
                                {% elif allocation.status == 'draft' %}
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                        Draft
                                    </span>
                                {% else %}
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                        {{ allocation.get_status_display }}
                                    </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ allocation.created_at|date:"M d, Y" }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-clipboard-list text-4xl mb-4"></i>
                <p>No budget allocations yet</p>
                <p class="text-sm">Create your first budget allocation to get started</p>
            </div>
        {% endif %}
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white rounded-lg shadow p-6 text-center">
            <div class="text-blue-600 mb-4">
                <i class="fas fa-plus-circle text-4xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Create Budget Code</h3>
            <p class="text-gray-600 text-sm mb-4">Set up new budget classifications</p>
            <a href="{% url 'mis:budget_code_create' %}" 
               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                Create Now
            </a>
        </div>

        <div class="bg-white rounded-lg shadow p-6 text-center">
            <div class="text-green-600 mb-4">
                <i class="fas fa-calendar-plus text-4xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Setup Budget Period</h3>
            <p class="text-gray-600 text-sm mb-4">Define budget time periods</p>
            <a href="{% url 'mis:budget_period_create' %}" 
               class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                Setup Period
            </a>
        </div>

        <div class="bg-white rounded-lg shadow p-6 text-center">
            <div class="text-purple-600 mb-4">
                <i class="fas fa-chart-line text-4xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">View Reports</h3>
            <p class="text-gray-600 text-sm mb-4">Analyze budget performance</p>
            <a href="{% url 'mis:budget_allocation_list' %}" 
               class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                View Reports
            </a>
        </div>
    </div>
</div>

<script>
// Add any dashboard-specific JavaScript here
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh dashboard every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);
});
</script>
{% endblock %}