﻿<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="Bank.aspx.cs" Inherits="Module_Accounts_Masters_Bank" Title="ERP" Theme="Default" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" Runat="Server">
    <link type="text/css" href="../../../Css/yui-datatable.css" rel="stylesheet" />
    <link href="../../../Css/StyleSheet.css" rel="stylesheet" type="text/css" />

    <script src="../../../Javascript/PopUpMsg.js" type="text/javascript"></script>

    <script src="../../../Javascript/loadingNotifier.js" type="text/javascript"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cp3" Runat="Server">
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="cp1" Runat="Server">
</asp:Content>
<asp:Content ID="Content4" ContentPlaceHolderID="cp2" Runat="Server">
</asp:Content>
<asp:Content ID="Content5" ContentPlaceHolderID="MainContent" Runat="Server">
</asp:Content>
<asp:Content ID="Content6" ContentPlaceHolderID="ContentPlaceHolder2" Runat="Server">
    <p><br /></p><p></p>
</asp:Content>
<asp:Content ID="Content7" ContentPlaceHolderID="ContentPlaceHolder3" Runat="Server">
        <table align="center" style="width: 100%">
        <tr>
            <td  align="left" valign="middle" style="background:url(../../../images/hdbg.JPG)" height="21" class="fontcsswhite"><b>
                &nbsp;Bank</b></td>
        </tr>
        <tr>
            <td align="center">
            <asp:GridView ID="GridView1" 
                runat="server"
                ShowFooter="True" 
                AutoGenerateColumns="False" 
                DataKeyNames="Id" 
                FooterStyle-Wrap="True"
                OnRowDeleted="GridView1_RowDeleted" 
               
              OnRowUpdated="GridView1_RowUpdated" 
              OnRowCommand="GridView1_RowCommand" CssClass="yui-datatable-theme" Width="100%" 
                    onrowdatabound="GridView1_RowDataBound" 
                    onrowupdating="GridView1_RowUpdating" onrowediting="GridView1_RowEditing" 
                    onrowcancelingedit="GridView1_RowCancelingEdit" 
                    onrowdeleting="GridView1_RowDeleting">
                    
<FooterStyle Wrap="True"></FooterStyle>
                   
                   <Columns>
                      <asp:CommandField ButtonType="Link"  ShowEditButton="True" 
                           ValidationGroup="Shree">
                          <ItemStyle HorizontalAlign="Center" Width="4%" />
                       </asp:CommandField>
                        <asp:CommandField   ButtonType="Link" ShowDeleteButton="True"  >
                         
                            <ItemStyle HorizontalAlign="Center"  Width="3%"/>
                       </asp:CommandField>
                         
                        <asp:TemplateField HeaderText="SN" >
                        <ItemTemplate>
                        <%# Container.DataItemIndex+1 %>
                        </ItemTemplate>
                        <FooterTemplate> 
                        <asp:Button ID="btnInsert" runat="server" Text="Insert" ValidationGroup="abc" 
                        OnClientClick=" return confirmationAdd()" CommandName="Add" CssClass="redbox" />
                        </FooterTemplate>
                            <ItemStyle HorizontalAlign="Right" />
                        </asp:TemplateField>
                        
                         <asp:TemplateField HeaderText="Id" Visible="false" >
                        <EditItemTemplate>
                        <asp:Label ID="lblId" runat="server" Text='<%#Eval("Id") %>'>    </asp:Label>
                        </EditItemTemplate>
                        </asp:TemplateField>
                        
                        
                        <asp:TemplateField HeaderText="Name of Bank">
                        <ItemTemplate>
                        <asp:Label ID="lblName" runat="server" Text='<%#Eval("Name") %>'>    </asp:Label>
                        </ItemTemplate>
                        <EditItemTemplate>
                        <asp:TextBox ID="txtBank" Width="90%" runat="server" CssClass="box3"  ValidationGroup="Shree" Text='<%#Bind("Name") %>'>
                        </asp:TextBox>
                         <asp:RequiredFieldValidator ID="ReqBank" ValidationGroup="Shree" ControlToValidate="txtBank" 
                            runat="server" ErrorMessage="*">
                            </asp:RequiredFieldValidator>
                        </EditItemTemplate>
                        
                        <FooterTemplate>
                        <asp:TextBox ID="txtBank1" CssClass="box3"   Width="90%" ValidationGroup="abc" runat="server">
                        </asp:TextBox>
                         <asp:RequiredFieldValidator ID="ReqBank1" runat="server" ValidationGroup="abc" ControlToValidate="txtBank1" 
                         ErrorMessage="*"></asp:RequiredFieldValidator>
                        </FooterTemplate>
                        
                            <FooterStyle HorizontalAlign="Left" />
                            <ItemStyle HorizontalAlign="Left" Width="20%" />
                        </asp:TemplateField>  
                        
                        
                        
                  <asp:TemplateField HeaderText="Address">
                        <ItemTemplate>
                        <asp:Label ID="lblAdd" runat="server" Text='<%#Eval("Address") %>'>    </asp:Label>
                        </ItemTemplate>
                        <EditItemTemplate>
                        <asp:TextBox ID="txtAddress" Width="85%" runat="server" CssClass="box3"  ValidationGroup="Shree" Text='<%#Bind("Address") %>'>
                        </asp:TextBox>
                         <asp:RequiredFieldValidator ID="ReqAdd" ValidationGroup="Shree" ControlToValidate="txtAddress" 
                            runat="server" ErrorMessage="*">
                            </asp:RequiredFieldValidator>
                        </EditItemTemplate>
                        
                        <FooterTemplate>
                        <asp:TextBox ID="txtAddress1" CssClass="box3"   Width="85%" ValidationGroup="abc" runat="server">
                        </asp:TextBox>
                         <asp:RequiredFieldValidator ID="ReqAdd1" runat="server" ValidationGroup="abc" ControlToValidate="txtAddress1" 
                         ErrorMessage="*"></asp:RequiredFieldValidator>
                        </FooterTemplate>
                        
                            <FooterStyle HorizontalAlign="Left" />
                            <ItemStyle HorizontalAlign="Left" Width="15%" />
                        </asp:TemplateField>  
                              
                        
                         <asp:TemplateField HeaderText="Country">
                        <ItemTemplate>
                       <asp:Label ID="lblCountry" runat="server" Text='<%#Eval("CountryName") %>'>    </asp:Label>
                        </ItemTemplate>
                        <EditItemTemplate>
                     
                     <asp:Label ID="lblCountryE" Visible="false" runat="server" Text='<%#Eval("Country") %>'>    </asp:Label>
                      <asp:DropDownList ID="DrpCountry" Width="100%"  AutoPostBack="True"   runat="server"  onselectedindexchanged="DrpCountry_SelectedIndexChanged"   DataSourceID="SqlDataSource2" DataValueField="CId" DataTextField="CountryName" ></asp:DropDownList> 
                     
                        </EditItemTemplate>
                        
                        <FooterTemplate>
     <asp:DropDownList ID="DrpCountry1" Width="100%" runat="server"   AutoPostBack="True"  onselectedindexchanged="DrpCountry1_SelectedIndexChanged"  DataSourceID="SqlDataSource2" DataValueField="CId" DataTextField="CountryName" ></asp:DropDownList> 
                        </FooterTemplate>
                        
                            <FooterStyle HorizontalAlign="Left" />
                            <ItemStyle HorizontalAlign="Left" Width="10%" />
                        </asp:TemplateField>  
                              
                        
                         <asp:TemplateField HeaderText="State">
                        <ItemTemplate>
                        <asp:Label ID="lblState" runat="server" Text='<%#Eval("StateName") %>'>    </asp:Label>
                        </ItemTemplate>
                        <EditItemTemplate>
                        
                           <asp:Label ID="lblState1" Visible="false"  runat="server" Text='<%#Eval("State") %>'>    </asp:Label>
                         <asp:DropDownList ID="Drpstate" runat="server"  Width="100%" 
                    AutoPostBack="True" onselectedindexchanged="Drpstate_SelectedIndexChanged" 
                    CssClass="box3" >
                </asp:DropDownList>
                        
                        </EditItemTemplate>
                        
                        <FooterTemplate>
                        <asp:DropDownList ID="Drpstate1" runat="server"  Width="100%" 
                    AutoPostBack="True" onselectedindexchanged="Drpstate1_SelectedIndexChanged" 
                    CssClass="box3" >
                </asp:DropDownList>
                       
                        </FooterTemplate>
                        
                            <FooterStyle HorizontalAlign="Left" />
                            <ItemStyle HorizontalAlign="Left" Width="10%" />
                        </asp:TemplateField> 
                         <asp:TemplateField HeaderText="City " >
                        <ItemTemplate>
                        <asp:Label ID="lblCity" runat="server" Text='<%#Eval("CityName") %>'>    
                        </asp:Label>
                        </ItemTemplate>
                        <EditItemTemplate>
                         <asp:Label ID="lblCIty1" Visible="false"  runat="server" Text='<%#Eval("City") %>'>    </asp:Label>
                        <asp:DropDownList ID="DrpCity" runat="server"  Width="100%" 
                    AutoPostBack="True" 
                    CssClass="box3" >
                </asp:DropDownList>
                       
                        </EditItemTemplate>
                        <FooterTemplate>
                         <asp:DropDownList ID="DrpCity1" runat="server"  Width="100%" 
                    AutoPostBack="True" 
                    CssClass="box3" >
                </asp:DropDownList>
                          
                        </FooterTemplate>
                            <ItemStyle Width="10%" />
                        </asp:TemplateField>
                        
                    <asp:TemplateField HeaderText="PIN No">
                        <ItemTemplate>
                        <asp:Label ID="lblPINNo" runat="server" Text='<%#Eval("PINNo") %>'>    </asp:Label>
                        </ItemTemplate>
                        <EditItemTemplate>
                        <asp:TextBox ID="txtPINNo" Width="85%" runat="server" CssClass="box3"  ValidationGroup="Shree" Text='<%#Bind("PINNo") %>'>
                        </asp:TextBox>
                         <asp:RequiredFieldValidator ID="ReqPINNo" ValidationGroup="Shree" ControlToValidate="txtPINNo" 
                            runat="server" ErrorMessage="*">
                            </asp:RequiredFieldValidator>
                        </EditItemTemplate>
                        
                        <FooterTemplate>
                        <asp:TextBox ID="txtPINNo1" CssClass="box3"   Width="85%" ValidationGroup="abc" runat="server">
                        </asp:TextBox>
                         <asp:RequiredFieldValidator ID="ReqPINNo1" runat="server" ValidationGroup="abc" ControlToValidate="txtPINNo1" 
                         ErrorMessage="*"></asp:RequiredFieldValidator>
                        </FooterTemplate>
                        
                            <FooterStyle HorizontalAlign="Left" />
                            <ItemStyle HorizontalAlign="Left" Width="8%" />
                        </asp:TemplateField>  
                              
                        
                              <asp:TemplateField HeaderText="Contact No">
                        <ItemTemplate>
                        <asp:Label ID="lblContactNo" runat="server" Text='<%#Eval("ContactNo") %>'>    </asp:Label>
                        </ItemTemplate>
                        <EditItemTemplate>
                        <asp:TextBox ID="txtContactNo" Width="85%" runat="server" CssClass="box3"  ValidationGroup="Shree" Text='<%#Bind("ContactNo") %>'>
                        </asp:TextBox>
                         <asp:RequiredFieldValidator ID="ReqContactNo" ValidationGroup="Shree" ControlToValidate="txtContactNo" 
                            runat="server" ErrorMessage="*">
                            </asp:RequiredFieldValidator>
                        </EditItemTemplate>
                        
                        <FooterTemplate>
                        <asp:TextBox ID="txtContactNo1" CssClass="box3"   Width="85%" ValidationGroup="abc" runat="server">
                        </asp:TextBox>
                         <asp:RequiredFieldValidator ID="ReqContactNo1" runat="server" ValidationGroup="abc" ControlToValidate="txtContactNo1" 
                         ErrorMessage="*"></asp:RequiredFieldValidator>
                        </FooterTemplate>
                        
                            <FooterStyle HorizontalAlign="Left" />
                            <ItemStyle HorizontalAlign="Left" Width="10%" />
                        </asp:TemplateField>  
                              
                              
                              
                         <asp:TemplateField HeaderText="Fax No">
                        <ItemTemplate>
                        <asp:Label ID="lblFaxNo" runat="server" Text='<%#Eval("FaxNo") %>'>    </asp:Label>
                        </ItemTemplate>
                        <EditItemTemplate>
                        <asp:TextBox ID="txtFaxNo" Width="85%" runat="server" CssClass="box3"  ValidationGroup="Shree" Text='<%#Bind("FaxNo") %>'>
                        </asp:TextBox>
                         <asp:RequiredFieldValidator ID="ReqFaxNo" ValidationGroup="Shree" ControlToValidate="txtFaxNo" 
                            runat="server" ErrorMessage="*">
                            </asp:RequiredFieldValidator>
                        </EditItemTemplate>
                        
                        <FooterTemplate>
                        <asp:TextBox ID="txtFaxNo1" CssClass="box3"   Width="85%" ValidationGroup="abc" runat="server">
                        </asp:TextBox>
                         <asp:RequiredFieldValidator ID="ReqFaxNo1" runat="server" ValidationGroup="abc" ControlToValidate="txtFaxNo1" 
                         ErrorMessage="*"></asp:RequiredFieldValidator>
                        </FooterTemplate>
                        
                            <FooterStyle HorizontalAlign="Left" />
                            <ItemStyle HorizontalAlign="Left" Width="10%" />
                        </asp:TemplateField>  
                                            
                     <asp:TemplateField HeaderText="IFSC Code">
                        <ItemTemplate>
                        <asp:Label ID="lblIFSC" runat="server" Text='<%#Eval("IFSC") %>'>    </asp:Label>
                        </ItemTemplate>
                        <EditItemTemplate>
                        <asp:TextBox ID="txtIFSC" Width="85%" runat="server" CssClass="box3"  ValidationGroup="Shree" Text='<%#Bind("IFSC") %>'>
                        </asp:TextBox>
                         <asp:RequiredFieldValidator ID="ReqIFSC" ValidationGroup="Shree" ControlToValidate="txtIFSC" 
                            runat="server" ErrorMessage="*">
                            </asp:RequiredFieldValidator>
                        </EditItemTemplate>                        
                        <FooterTemplate>
                        <asp:TextBox ID="txtIFSC1" CssClass="box3"   Width="85%" ValidationGroup="abc" runat="server">
                        </asp:TextBox>
                         <asp:RequiredFieldValidator ID="ReqIFSC1" runat="server" ValidationGroup="abc" ControlToValidate="txtIFSC1" 
                         ErrorMessage="*"></asp:RequiredFieldValidator>
                        </FooterTemplate>
                        
                            <FooterStyle HorizontalAlign="Left" />
                            <ItemStyle HorizontalAlign="Left" Width="10%" />
                        </asp:TemplateField>  
                              
                    </Columns> 
                    <EmptyDataTemplate>
                    <table>
                    <tr>
                    <td></td>
                    <td align="center">
                        <asp:Label ID="Label1" Font-Bold="true" Font-Size="Medium" Font-Names="Times New Roman" runat="server" Text="Name of Bank"></asp:Label></td>
               <td align="center">
                        <asp:Label ID="Label2" Font-Bold="true" Font-Size="Medium" Font-Names="Times New Roman" runat="server" Text="Address"></asp:Label></td>   
                  
                  <td align="center">
                        <asp:Label ID="Label3" Font-Bold="true" Font-Size="Medium" Font-Names="Times New Roman" runat="server" Text="Country"></asp:Label></td>
               
               <td align="center">
                        <asp:Label ID="Label4" Font-Bold="true" Font-Size="Medium" Font-Names="Times New Roman" runat="server" Text="State"></asp:Label></td>
           
           <td align="center"><asp:Label ID="Label5" Font-Bold="true" Font-Size="Medium" Font-Names="Times New Roman" runat="server" Text="City"></asp:Label></td>
   
   <td align="center"><asp:Label ID="Label6" Font-Bold="true" Font-Size="Medium" Font-Names="Times New Roman" runat="server" Text="Pin No"></asp:Label></td>
  <td align="center"><asp:Label ID="Label7" Font-Bold="true" Font-Size="Medium" Font-Names="Times New Roman" runat="server" Text="Contact No"></asp:Label></td>
   <td align="center"><asp:Label ID="Label8" Font-Bold="true" Font-Size="Medium" Font-Names="Times New Roman" runat="server" Text="Fax No"></asp:Label></td>
     <td align="center"><asp:Label ID="Label9" Font-Bold="true" Font-Size="Medium" Font-Names="Times New Roman" runat="server" Text="IFSC Code"></asp:Label></td>
           </tr>
                    <tr>
                    <td>
                   <asp:Button ID="btnInsert" runat="server" Text="Insert" ValidationGroup="pqr" 
                   OnClientClick=" return confirmationAdd()"  CommandName="Add1" CssClass="redbox" />
                   </td>
                   <td>
                   <asp:TextBox ID="txtName2" CssClass="box3"  runat="server" ValidationGroup="pqr" Width="180Px" > </asp:TextBox> 
                       <asp:RequiredFieldValidator ID="ReqName2" runat="server" ValidationGroup="pqr" ControlToValidate="txtName2" ErrorMessage="*"></asp:RequiredFieldValidator>
                        </td>
                         <td>
                   <asp:TextBox ID="TextAdddress2" CssClass="box3"  runat="server" ValidationGroup="pqr" Width="180Px" > </asp:TextBox> 
                       <asp:RequiredFieldValidator ID="ReqAdddress2" runat="server" ValidationGroup="pqr" ControlToValidate="TextAdddress2" ErrorMessage="*"></asp:RequiredFieldValidator>
                        </td>
                        <td>
                    <asp:DropDownList ID="DrpCountry2" runat="server" DataTextField="CountryName"  DataValueField="CId" Width="100px"  DataSourceID="SqlDataSource2"
                    AutoPostBack="True" onselectedindexchanged="DrpCountry2_SelectedIndexChanged" 
                    CssClass="box3" >
                </asp:DropDownList>
                        </td>
                        <td>
                    <asp:DropDownList ID="Drpstate2" runat="server"  Width="100 px" 
                    AutoPostBack="True" onselectedindexchanged="Drpstate2_SelectedIndexChanged" 
                    CssClass="box3" >
                </asp:DropDownList>
                        </td>
                        
                          <td>
                    <asp:DropDownList ID="DrpCity2" runat="server"  Width="100px" 
                    AutoPostBack="True" 
                    CssClass="box3" >
                </asp:DropDownList>
                        </td>
                        
                        
                        <td>
                   <asp:TextBox ID="TextPinNo2" CssClass="box3"  runat="server" ValidationGroup="pqr" Width="120Px" > </asp:TextBox> 
                       <asp:RequiredFieldValidator ID="ReqPinNo2" runat="server" ValidationGroup="pqr" ControlToValidate="TextPinNo2" ErrorMessage="*"></asp:RequiredFieldValidator>
                        </td>
                        
                        <td>
                   <asp:TextBox ID="TextContactNo2" CssClass="box3"  runat="server" ValidationGroup="pqr" Width="100Px" > </asp:TextBox> 
                       <asp:RequiredFieldValidator ID="ReqContactNo2" runat="server" ValidationGroup="pqr" ControlToValidate="TextContactNo2" ErrorMessage="*"></asp:RequiredFieldValidator>
                        </td>
                        
                        <td>
                   <asp:TextBox ID="TextFaxNo2" CssClass="box3"  runat="server" ValidationGroup="pqr" Width="100Px" > </asp:TextBox> 
                       <asp:RequiredFieldValidator ID="ReqFaxNo2" runat="server" ValidationGroup="pqr" ControlToValidate="TextFaxNo2" ErrorMessage="*"></asp:RequiredFieldValidator>
                        </td>
                            <td>
                   <asp:TextBox ID="TextIFSC2" CssClass="box3"  runat="server" ValidationGroup="pqr" Width="130Px" > </asp:TextBox> 
                       <asp:RequiredFieldValidator ID="ReqIFSC2" runat="server" ValidationGroup="pqr" ControlToValidate="TextIFSC2" ErrorMessage="*"></asp:RequiredFieldValidator>
                        </td> 
                        
                        
                         </tr>                      
                        </table>
                   </EmptyDataTemplate> 
                    
              </asp:GridView>
                
              
                
            
                &nbsp;</td>
        </tr>
        <tr>
            <td align="center">
                <br />
                <asp:Label ID="lblMessage" runat="server" style="color: #FF0000"></asp:Label>
                  <asp:SqlDataSource ID="SqlDataSource2" runat="server"  
                    ConnectionString="<%$ ConnectionStrings:LocalSqlServer %>" 
                    SelectCommand="SELECT * FROM [tblCountry]"   ></asp:SqlDataSource>
            </td>
        </tr>
    </table>
<br />

</asp:Content>
<asp:Content ID="Content8" ContentPlaceHolderID="ContentPlaceHolder4" Runat="Server">
</asp:Content>

