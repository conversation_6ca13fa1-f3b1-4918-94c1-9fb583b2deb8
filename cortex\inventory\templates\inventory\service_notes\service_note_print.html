{% extends 'core/base.html' %}
{% load static %}

{% block title %}Print Service Note {{ service_note.gsnno }}{% endblock %}

{% block extra_css %}
<style>
    @media print {
        .no-print { display: none !important; }
        body { background: white !important; }
        .print-container { 
            padding: 0 !important; 
            margin: 0 !important;
            box-shadow: none !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Print Controls -->
    <div class="no-print bg-gray-700 text-white px-4 py-2 flex justify-between items-center">
        <h1 class="text-lg font-bold">Print Service Note {{ service_note.gsnno }}</h1>
        <div class="flex space-x-2">
            <button onclick="window.print()" 
                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm">
                Print
            </button>
            <a href="{% url 'inventory:service_note_detail' service_note.pk %}" 
               class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded text-sm">
                Back to Details
            </a>
        </div>
    </div>
    
    <!-- Printable Content -->
    <div class="print-container bg-white p-8 max-w-4xl mx-auto">
        <!-- Company Header -->
        <div class="text-center border-b-2 border-gray-300 pb-4 mb-6">
            <h1 class="text-2xl font-bold text-gray-900">Synergytech Automation Pvt. Ltd.</h1>
            <p class="text-sm text-gray-600">Material Service Note</p>
        </div>

        <!-- Service Note Information -->
        <div class="grid grid-cols-2 gap-8 mb-6">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Service Note Information</h3>
                <div class="space-y-2">
                    <div class="flex">
                        <span class="w-32 text-sm font-medium text-gray-500">GSN Number:</span>
                        <span class="text-sm text-gray-900">{{ service_note.gsnno|default:"N/A" }}</span>
                    </div>
                    <div class="flex">
                        <span class="w-32 text-sm font-medium text-gray-500">GIN ID:</span>
                        <span class="text-sm text-gray-900">{{ service_note.ginid|default:"N/A" }}</span>
                    </div>
                    <div class="flex">
                        <span class="w-32 text-sm font-medium text-gray-500">GIN Number:</span>
                        <span class="text-sm text-gray-900">{{ service_note.ginno|default:"N/A" }}</span>
                    </div>
                    <div class="flex">
                        <span class="w-32 text-sm font-medium text-gray-500">Created Date:</span>
                        <span class="text-sm text-gray-900">{{ service_note.sysdate|default:"N/A" }}</span>
                    </div>
                </div>
            </div>
            
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Company Information</h3>
                <div class="space-y-2">
                    <div class="flex">
                        <span class="w-32 text-sm font-medium text-gray-500">Company:</span>
                        <span class="text-sm text-gray-900">{{ service_note.company.name|default:"N/A" }}</span>
                    </div>
                    <div class="flex">
                        <span class="w-32 text-sm font-medium text-gray-500">Financial Year:</span>
                        <span class="text-sm text-gray-900">{{ service_note.financial_year.finyear|default:"N/A" }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tax Invoice Information -->
        {% if service_note.taxinvoiceno or service_note.taxinvoicedate %}
        <div class="border border-gray-200 p-4 rounded mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">Tax Invoice Details</h3>
            <div class="grid grid-cols-2 gap-4">
                <div class="flex">
                    <span class="w-32 text-sm font-medium text-gray-500">Invoice No:</span>
                    <span class="text-sm text-gray-900">{{ service_note.taxinvoiceno|default:"Not provided" }}</span>
                </div>
                <div class="flex">
                    <span class="w-32 text-sm font-medium text-gray-500">Invoice Date:</span>
                    <span class="text-sm text-gray-900">{{ service_note.taxinvoicedate|default:"Not provided" }}</span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Footer -->
        <div class="mt-12 pt-6 border-t border-gray-300">
            <div class="grid grid-cols-3 gap-8 text-center">
                <div>
                    <div class="border-t border-gray-400 pt-2 mt-8">
                        <p class="text-sm font-medium">Prepared By</p>
                    </div>
                </div>
                <div>
                    <div class="border-t border-gray-400 pt-2 mt-8">
                        <p class="text-sm font-medium">Checked By</p>
                    </div>
                </div>
                <div>
                    <div class="border-t border-gray-400 pt-2 mt-8">
                        <p class="text-sm font-medium">Approved By</p>
                    </div>
                </div>
            </div>
            
            <div class="text-center text-xs text-gray-500 mt-6">
                <p>Printed on {{ print_date|date:"d M Y \\a\\\\t H:i" }}</p>
                <p>This is a system generated document</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}