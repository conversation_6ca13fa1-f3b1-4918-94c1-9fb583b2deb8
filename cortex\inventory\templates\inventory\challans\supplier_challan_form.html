{% extends "core/base.html" %}
{% load static %}

{% block title %}{% if object %}Edit{% else %}Create{% endif %} Supplier Challan - Inventory Management{% endblock %}

{% block content %}
<div class="sap-page">
    <!-- Page Header -->
    <div class="sap-page-header">
        <div class="sap-page-header-content">
            <div class="sap-page-title">
                <h1>{% if object %}Edit{% else %}Create{% endif %} Supplier Challan</h1>
                <p>{% if object %}Update supplier challan details{% else %}Create new supplier challan for material receipt{% endif %}</p>
            </div>
            <div class="sap-page-actions">
                <a href="{% url 'inventory:supplier_challan_list' %}" class="sap-button sap-button--transparent">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to List
                </a>
            </div>
        </div>
    </div>

    <!-- Main Form -->
    <div class="sap-panel">
        <div class="sap-panel-header">
            <h3 class="sap-panel-title">
                {% if object %}Edit Supplier Challan - {{ object.scno }}{% else %}New Supplier Challan{% endif %}
            </h3>
        </div>
        <div class="sap-panel-content">
            <form method="post" class="sap-form" id="supplier-challan-form">
                {% csrf_token %}
                
                <!-- Step 1: Supplier Selection -->
                <div id="supplier-selection-step" {% if object %}style="display: none;"{% endif %}>
                    <div class="sap-form-row">
                        <div class="sap-form-group">
                            <label class="sap-form-label required">Supplier Search</label>
                            <div class="relative">
                                <input type="text" 
                                       id="supplier-search" 
                                       placeholder="Search supplier by name or code..."
                                       class="sap-input"
                                       autocomplete="off">
                                <div id="supplier-dropdown" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg hidden max-h-60 overflow-y-auto">
                                    <!-- Supplier search results will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Supplier Selection Results -->
                    <div id="supplier-list" class="mt-6">
                        <div class="sap-table-container--responsive">
                            <table class="sap-table">
                                <thead>
                                    <tr>
                                        <th class="sap-table-header">Select</th>
                                        <th class="sap-table-header">Supplier Code</th>
                                        <th class="sap-table-header">Supplier Name</th>
                                        <th class="sap-table-header">Financial Year</th>
                                    </tr>
                                </thead>
                                <tbody id="supplier-results">
                                    <!-- Sample suppliers for demo -->
                                    <tr class="sap-table-row">
                                        <td class="sap-table-cell">
                                            <input type="radio" name="selected_supplier" value="S098" 
                                                   data-name="SAPL PLANT II" class="supplier-radio">
                                        </td>
                                        <td class="sap-table-cell">S098</td>
                                        <td class="sap-table-cell">SAPL PLANT II</td>
                                        <td class="sap-table-cell">2024-2025</td>
                                    </tr>
                                    <tr class="sap-table-row">
                                        <td class="sap-table-cell">
                                            <input type="radio" name="selected_supplier" value="C042" 
                                                   data-name="CONFLUENCE TECHNOLOGY" class="supplier-radio">
                                        </td>
                                        <td class="sap-table-cell">C042</td>
                                        <td class="sap-table-cell">CONFLUENCE TECHNOLOGY</td>
                                        <td class="sap-table-cell">2024-2025</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="sap-form-actions mt-4">
                            <button type="button" id="select-supplier-btn" class="sap-button sap-button--emphasized" disabled>
                                Continue with Selected Supplier
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Challan Details -->
                <div id="challan-details-step" {% if not object %}style="display: none;"{% endif %}>
                    <!-- Selected Supplier Info -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <p class="font-medium text-blue-900">Selected Supplier</p>
                                <p class="text-sm text-blue-700" id="selected-supplier-info">
                                    {% if object %}{{ object.supplierid }} - {{ object.supplier_name }}{% endif %}
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Basic Challan Information -->
                    <div class="sap-form-row">
                        <div class="sap-form-group">
                            <label class="sap-form-label">Challan Number</label>
                            <input type="text" name="scno" value="{{ object.scno|default:'' }}" 
                                   class="sap-input" 
                                   {% if not object %}placeholder="Auto-generated"{% endif %}
                                   {% if not object %}readonly{% endif %}>
                        </div>
                        <div class="sap-form-group">
                            <label class="sap-form-label">Supplier ID</label>
                            <input type="text" name="supplierid" value="{{ object.supplierid|default:'' }}" 
                                   class="sap-input" readonly id="supplier-id-field">
                        </div>
                    </div>

                    <div class="sap-form-row">
                        <div class="sap-form-group">
                            <label class="sap-form-label">Vehicle Number</label>
                            <input type="text" name="vehicleno" value="{{ object.vehicleno|default:'' }}" 
                                   class="sap-input" placeholder="Enter vehicle number">
                        </div>
                        <div class="sap-form-group">
                            <label class="sap-form-label">Transporter</label>
                            <input type="text" name="transpoter" value="{{ object.transpoter|default:'' }}" 
                                   class="sap-input" placeholder="Enter transporter name">
                        </div>
                    </div>

                    <div class="sap-form-row">
                        <div class="sap-form-group full-width">
                            <label class="sap-form-label">Remarks</label>
                            <textarea name="remarks" class="sap-input" rows="3" 
                                      placeholder="Enter any remarks or notes...">{{ object.remarks|default:'' }}</textarea>
                        </div>
                    </div>

                    <!-- Purchase Requisition Selection (for new challans) -->
                    {% if not object %}
                    <div class="mt-8">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Select Purchase Requisitions</h4>
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                            <p class="text-sm text-yellow-800">
                                <strong>Note:</strong> Select purchase requisition items that are being delivered in this challan. 
                                You can only challan quantities that are available (PR Qty - Already Challaned).
                            </p>
                        </div>
                        
                        <div id="pr-selection" class="sap-table-container--responsive">
                            <table class="sap-table">
                                <thead>
                                    <tr>
                                        <th class="sap-table-header w-16">Select</th>
                                        <th class="sap-table-header">PR No</th>
                                        <th class="sap-table-header">WO No</th>
                                        <th class="sap-table-header">Item Code</th>
                                        <th class="sap-table-header">Description</th>
                                        <th class="sap-table-header">UOM</th>
                                        <th class="sap-table-header">PR Qty</th>
                                        <th class="sap-table-header">Remaining</th>
                                        <th class="sap-table-header">Challan Qty</th>
                                    </tr>
                                </thead>
                                <tbody id="pr-items">
                                    <!-- PR items will be loaded based on selected supplier -->
                                    <tr class="sap-table-row">
                                        <td colspan="9" class="sap-table-cell text-center py-8 text-gray-500">
                                            Select a supplier to view available purchase requisitions
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Form Actions -->
                    <div class="sap-form-actions">
                        {% if not object %}
                        <button type="button" id="back-to-supplier-btn" class="sap-button sap-button--transparent">
                            Back to Supplier Selection
                        </button>
                        {% endif %}
                        <button type="submit" class="sap-button sap-button--emphasized">
                            {% if object %}Update Challan{% else %}Create Challan{% endif %}
                        </button>
                        <a href="{% url 'inventory:supplier_challan_list' %}" class="sap-button sap-button--transparent">
                            Cancel
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const supplierRadios = document.querySelectorAll('.supplier-radio');
    const selectSupplierBtn = document.getElementById('select-supplier-btn');
    const supplierSelectionStep = document.getElementById('supplier-selection-step');
    const challanDetailsStep = document.getElementById('challan-details-step');
    const backToSupplierBtn = document.getElementById('back-to-supplier-btn');
    const supplierIdField = document.getElementById('supplier-id-field');
    const selectedSupplierInfo = document.getElementById('selected-supplier-info');

    // Enable/disable continue button based on supplier selection
    supplierRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            selectSupplierBtn.disabled = false;
        });
    });

    // Handle supplier selection
    selectSupplierBtn.addEventListener('click', function() {
        const selectedRadio = document.querySelector('.supplier-radio:checked');
        if (selectedRadio) {
            const supplierId = selectedRadio.value;
            const supplierName = selectedRadio.getAttribute('data-name');
            
            // Update supplier info in step 2
            supplierIdField.value = supplierId;
            selectedSupplierInfo.textContent = `${supplierId} - ${supplierName}`;
            
            // Hide step 1, show step 2
            supplierSelectionStep.style.display = 'none';
            challanDetailsStep.style.display = 'block';
            
            // Load PR items for selected supplier (placeholder for now)
            loadPRItems(supplierId);
        }
    });

    // Handle back to supplier selection
    if (backToSupplierBtn) {
        backToSupplierBtn.addEventListener('click', function() {
            supplierSelectionStep.style.display = 'block';
            challanDetailsStep.style.display = 'none';
        });
    }

    // Supplier search functionality (placeholder)
    const supplierSearch = document.getElementById('supplier-search');
    if (supplierSearch) {
        supplierSearch.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = document.querySelectorAll('#supplier-results tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });
    }

    function loadPRItems(supplierId) {
        // Placeholder for loading PR items via AJAX
        const prItemsContainer = document.getElementById('pr-items');
        prItemsContainer.innerHTML = `
            <tr class="sap-table-row">
                <td class="sap-table-cell text-center py-8 text-gray-500" colspan="9">
                    Loading purchase requisitions for supplier ${supplierId}...
                    <br><small>This functionality will be implemented to load actual PR data.</small>
                </td>
            </tr>
        `;
    }
});
</script>
{% endblock %}