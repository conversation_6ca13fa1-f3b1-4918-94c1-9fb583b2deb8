﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="CrDrDetails" targetNamespace="http://tempuri.org/CrDrDetails.xsd" xmlns:mstns="http://tempuri.org/CrDrDetails.xsd" xmlns="http://tempuri.org/CrDrDetails.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="CrDrDetails" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="CrDrDetails" msprop:Generator_DataSetName="CrDrDetails">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="VchDate" msprop:Generator_UserColumnName="VchDate" msprop:Generator_ColumnVarNameInTable="columnVchDate" msprop:Generator_ColumnPropNameInRow="VchDate" msprop:Generator_ColumnPropNameInTable="VchDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="VchNo" msprop:Generator_UserColumnName="VchNo" msprop:Generator_ColumnVarNameInTable="columnVchNo" msprop:Generator_ColumnPropNameInRow="VchNo" msprop:Generator_ColumnPropNameInTable="VchNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="VchType" msprop:Generator_UserColumnName="VchType" msprop:Generator_ColumnVarNameInTable="columnVchType" msprop:Generator_ColumnPropNameInRow="VchType" msprop:Generator_ColumnPropNameInTable="VchTypeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Particulars" msprop:Generator_UserColumnName="Particulars" msprop:Generator_ColumnVarNameInTable="columnParticulars" msprop:Generator_ColumnPropNameInRow="Particulars" msprop:Generator_ColumnPropNameInTable="ParticularsColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Credit" msprop:Generator_UserColumnName="Credit" msprop:Generator_ColumnVarNameInTable="columnCredit" msprop:Generator_ColumnPropNameInRow="Credit" msprop:Generator_ColumnPropNameInTable="CreditColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Debit" msprop:Generator_UserColumnName="Debit" msprop:Generator_ColumnVarNameInTable="columnDebit" msprop:Generator_ColumnPropNameInRow="Debit" msprop:Generator_ColumnPropNameInTable="DebitColumn" type="xs:double" minOccurs="0" />
              <xs:element name="OtherCharges" msprop:Generator_UserColumnName="OtherCharges" msprop:Generator_ColumnPropNameInRow="OtherCharges" msprop:Generator_ColumnVarNameInTable="columnOtherCharges" msprop:Generator_ColumnPropNameInTable="OtherChargesColumn" type="xs:double" minOccurs="0" />
              <xs:element name="VchLinkData" msprop:Generator_UserColumnName="VchLinkData" msprop:Generator_ColumnPropNameInRow="VchLinkData" msprop:Generator_ColumnVarNameInTable="columnVchLinkData" msprop:Generator_ColumnPropNameInTable="VchLinkDataColumn" type="xs:string" minOccurs="0" />
              <xs:element name="DTSort" msprop:Generator_UserColumnName="DTSort" msprop:Generator_ColumnVarNameInTable="columnDTSort" msprop:Generator_ColumnPropNameInRow="DTSort" msprop:Generator_ColumnPropNameInTable="DTSortColumn" type="xs:dateTime" minOccurs="0" />
              <xs:element name="BillNo" msprop:Generator_UserColumnName="BillNo" msprop:Generator_ColumnPropNameInRow="BillNo" msprop:Generator_ColumnVarNameInTable="columnBillNo" msprop:Generator_ColumnPropNameInTable="BillNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="BillDate" msprop:Generator_UserColumnName="BillDate" msprop:Generator_ColumnPropNameInRow="BillDate" msprop:Generator_ColumnVarNameInTable="columnBillDate" msprop:Generator_ColumnPropNameInTable="BillDateColumn" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>