{% extends 'core/base.html' %}
{% load custom_filters %}

{% block title %}MCN Authorization - {{ work_order.wono }}{% endblock %}

{% block extra_css %}
<style>
    .authorization-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .wo-info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }
    
    .wo-info-item {
        text-align: center;
    }
    
    .wo-info-value {
        font-size: 1.25rem;
        font-weight: bold;
        color: #0070f3;
        display: block;
        margin-bottom: 0.25rem;
    }
    
    .wo-info-label {
        font-size: 0.875rem;
        color: #6c757d;
        font-weight: 500;
    }
    
    .mcn-items-section {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        overflow: hidden;
    }
    
    .section-header {
        background: #f8f9fa;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        justify-content: between;
        align-items: center;
    }
    
    .mcn-item-card {
        border-bottom: 1px solid #e9ecef;
        padding: 1.5rem;
        transition: background-color 0.3s ease;
    }
    
    .mcn-item-card:hover {
        background-color: #f8f9fa;
    }
    
    .mcn-item-card:last-child {
        border-bottom: none;
    }
    
    .mcn-item-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .mcn-number {
        font-weight: bold;
        color: #0070f3;
        font-size: 1.1rem;
    }
    
    .mcn-status {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
    }
    
    .status-pending {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }
    
    .status-partial {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }
    
    .status-completed {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .mcn-item-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }
    
    .detail-item {
        text-align: center;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e9ecef;
    }
    
    .detail-value {
        font-size: 1.1rem;
        font-weight: bold;
        color: #495057;
        display: block;
        margin-bottom: 0.25rem;
    }
    
    .detail-label {
        font-size: 0.75rem;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .authorization-form {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 6px;
        border: 1px solid #e9ecef;
        margin-top: 1rem;
    }
    
    .auth-input-group {
        display: flex;
        gap: 1rem;
        align-items: end;
        margin-bottom: 1rem;
    }
    
    .auth-input {
        flex: 1;
    }
    
    .bom-info {
        background: #e3f2fd;
        padding: 1rem;
        border-radius: 6px;
        border-left: 4px solid #2196f3;
        margin: 1rem 0;
    }
    
    .bom-title {
        font-weight: bold;
        color: #1976d2;
        margin-bottom: 0.5rem;
    }
    
    .bom-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 0.5rem;
        font-size: 0.875rem;
    }
    
    .totals-summary {
        background: #e8f5e8;
        border: 1px solid #c3e6cb;
        border-radius: 8px;
        padding: 1.5rem;
        margin-top: 2rem;
    }
    
    .totals-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }
    
    .total-item {
        text-align: center;
        padding: 1rem;
        background: white;
        border-radius: 6px;
    }
    
    .total-value {
        font-size: 1.5rem;
        font-weight: bold;
        color: #155724;
        display: block;
    }
    
    .total-label {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    
    .bulk-actions {
        position: sticky;
        bottom: 0;
        background: white;
        border-top: 1px solid #dee2e6;
        padding: 1rem;
        box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    }
    
    .selected-count {
        background: #0070f3;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    @media (max-width: 768px) {
        .wo-info-grid {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .mcn-item-details {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .auth-input-group {
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .totals-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">MCN Authorization</h1>
            <p class="text-muted mb-0">Work Order: {{ work_order.wono }}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{% url 'inventory:mcn_authorization_list' %}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to List
            </a>
            <button type="button" class="btn btn-primary" onclick="authorizeSelected()">
                <i class="bi bi-check-circle"></i> Authorize Selected
            </button>
        </div>
    </div>

    <!-- Work Order Information -->
    <div class="authorization-header">
        <h5 class="mb-3">Work Order Information</h5>
        <div class="wo-info-grid">
            <div class="wo-info-item">
                <span class="wo-info-value">{{ work_order.wono }}</span>
                <div class="wo-info-label">Work Order No</div>
            </div>
            <div class="wo-info-item">
                <span class="wo-info-value">{{ customer_name }}</span>
                <div class="wo-info-label">Customer</div>
            </div>
            <div class="wo-info-item">
                <span class="wo-info-value">{{ customer_code }}</span>
                <div class="wo-info-label">Customer Code</div>
            </div>
            <div class="wo-info-item">
                <span class="wo-info-value">{{ work_order.sysdate|default:'-' }}</span>
                <div class="wo-info-label">Date</div>
            </div>
        </div>
        {% if work_order.taskprojecttitle %}
        <div class="mt-3">
            <h6 class="mb-1">Project Title</h6>
            <p class="mb-0 text-muted">{{ work_order.taskprojecttitle }}</p>
        </div>
        {% endif %}
    </div>

    <!-- MCN Items Section -->
    <div class="mcn-items-section">
        <div class="section-header">
            <h5 class="mb-0">MCN Items for Authorization</h5>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAll()">
                    <i class="bi bi-check-all"></i> Select All
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearSelection()">
                    <i class="bi bi-x-circle"></i> Clear
                </button>
            </div>
        </div>

        {% if mcn_items %}
            <form id="authorizationForm" method="post">
                {% csrf_token %}
                
                {% for item in mcn_items %}
                <div class="mcn-item-card" data-item-id="{{ item.detail_id }}">
                    <div class="mcn-item-header">
                        <div class="d-flex align-items-center gap-3">
                            <input type="checkbox" 
                                   class="form-check-input item-checkbox" 
                                   name="selected_items" 
                                   value='{"detail_id": {{ item.detail_id }}, "mcn_id": {{ item.mcn_id }}, "project_id": {{ item.project_id }}, "customer_id": {{ item.customer_id }}}'
                                   onchange="updateSelection()">
                            <span class="mcn-number">MCN: {{ item.mcn_no }}</span>
                        </div>
                        <span class="mcn-status {% if item.pending_quantity > 0 %}status-pending{% elif item.authorized_quantity > 0 %}status-partial{% else %}status-completed{% endif %}">
                            {% if item.pending_quantity > 0 %}
                                Pending
                            {% elif item.authorized_quantity > 0 %}
                                Partial
                            {% else %}
                                Completed
                            {% endif %}
                        </span>
                    </div>

                    <div class="mcn-item-details">
                        <div class="detail-item">
                            <span class="detail-value">{{ item.mcn_quantity|floatformat:2 }}</span>
                            <div class="detail-label">MCN Quantity</div>
                        </div>
                        <div class="detail-item">
                            <span class="detail-value">{{ item.authorized_quantity|floatformat:2 }}</span>
                            <div class="detail-label">Authorized</div>
                        </div>
                        <div class="detail-item">
                            <span class="detail-value">{{ item.pending_quantity|floatformat:2 }}</span>
                            <div class="detail-label">Pending</div>
                        </div>
                        <div class="detail-item">
                            <span class="detail-value">Project {{ item.project_id }}</span>
                            <div class="detail-label">Project ID</div>
                        </div>
                    </div>

                    {% if item.bom_info %}
                    <div class="bom-info">
                        <div class="bom-title">
                            <i class="bi bi-diagram-3"></i> BOM Information
                        </div>
                        <div class="bom-details">
                            <div><strong>Item:</strong> {{ item.bom_info.item_code }}</div>
                            <div><strong>Description:</strong> {{ item.bom_info.description }}</div>
                            <div><strong>UOM:</strong> {{ item.bom_info.uom }}</div>
                            <div><strong>BOM Qty:</strong> {{ item.bom_info.bom_qty|floatformat:2 }}</div>
                            <div><strong>Issued:</strong> {{ item.bom_info.issued_qty|floatformat:2 }}</div>
                            <div><strong>Balance:</strong> {{ item.bom_info.balance_qty|floatformat:2 }}</div>
                        </div>
                    </div>
                    {% endif %}

                    {% if item.can_authorize %}
                    <div class="authorization-form" style="display: none;" id="authForm_{{ item.detail_id }}">
                        <h6 class="mb-3">Authorize Quantity</h6>
                        <div class="auth-input-group">
                            <div class="auth-input">
                                <label for="auth_quantity_{{ item.detail_id }}" class="form-label">
                                    Authorization Quantity (Max: {{ item.pending_quantity|floatformat:2 }})
                                </label>
                                <input type="number" 
                                       class="form-control sap-input auth-quantity-input" 
                                       id="auth_quantity_{{ item.detail_id }}"
                                       name="auth_quantity_{{ item.detail_id }}"
                                       min="0" 
                                       max="{{ item.pending_quantity }}"
                                       step="0.01"
                                       placeholder="0.00"
                                       data-detail-id="{{ item.detail_id }}"
                                       onchange="validateAuthQuantity(this)">
                            </div>
                            <div class="auth-input">
                                <label for="remarks_{{ item.detail_id }}" class="form-label">Remarks</label>
                                <input type="text" 
                                       class="form-control sap-input" 
                                       id="remarks_{{ item.detail_id }}"
                                       name="remarks_{{ item.detail_id }}"
                                       placeholder="Authorization remarks (optional)">
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-sm btn-success" onclick="authorizeItem({{ item.detail_id }})">
                                <i class="bi bi-check"></i> Authorize This Item
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleAuthForm({{ item.detail_id }})">
                                <i class="bi bi-x"></i> Cancel
                            </button>
                        </div>
                    </div>
                    {% else %}
                    <div class="alert alert-info mb-0">
                        <i class="bi bi-info-circle"></i> This item has been fully authorized.
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </form>

        {% else %}
            <!-- No MCN Items -->
            <div class="text-center py-5">
                <div class="mb-4">
                    <i class="bi bi-inbox" style="font-size: 4rem; color: #dee2e6;"></i>
                </div>
                <h5 class="text-muted mb-3">No MCN Items Found</h5>
                <p class="text-muted mb-4">
                    No MCN records are available for authorization in this work order.
                </p>
                <a href="{% url 'project_management:material_credit_note_list' %}" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> Create MCN
                </a>
            </div>
        {% endif %}
    </div>

    <!-- Totals Summary -->
    {% if totals %}
    <div class="totals-summary">
        <h5 class="mb-3">Authorization Summary</h5>
        <div class="totals-grid">
            <div class="total-item">
                <span class="total-value">{{ totals.total_items }}</span>
                <div class="total-label">Total Items</div>
            </div>
            <div class="total-item">
                <span class="total-value">{{ totals.total_mcn_qty|floatformat:2 }}</span>
                <div class="total-label">Total MCN Qty</div>
            </div>
            <div class="total-item">
                <span class="total-value">{{ totals.total_authorized_qty|floatformat:2 }}</span>
                <div class="total-label">Total Authorized</div>
            </div>
            <div class="total-item">
                <span class="total-value">{{ totals.total_pending_qty|floatformat:2 }}</span>
                <div class="total-label">Pending Authorization</div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Bulk Actions (Sticky Bottom) -->
    <div class="bulk-actions" id="bulkActions" style="display: none;">
        <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center gap-3">
                <span class="selected-count" id="selectedCount">0 items selected</span>
                <input type="number" 
                       class="form-control sap-input" 
                       id="bulkAuthQuantity"
                       placeholder="Authorization quantity"
                       step="0.01"
                       min="0"
                       style="width: 200px;">
            </div>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-success" onclick="bulkAuthorize()">
                    <i class="bi bi-check-circle"></i> Authorize Selected
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="clearSelection()">
                    <i class="bi bi-x-circle"></i> Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Authorization Results Modal -->
<div class="modal fade" id="authorizationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Authorization Result</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="authorizationResult">
                <!-- Results will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">OK</button>
            </div>
        </div>
    </div>
</div>

<script>
let selectedItems = new Set();

function updateSelection() {
    const checkboxes = document.querySelectorAll('.item-checkbox:checked');
    selectedItems.clear();
    
    checkboxes.forEach(checkbox => {
        selectedItems.add(checkbox.value);
        // Show authorization form for selected items
        const itemData = JSON.parse(checkbox.value);
        showAuthForm(itemData.detail_id);
    });
    
    // Hide authorization forms for unselected items
    document.querySelectorAll('.item-checkbox:not(:checked)').forEach(checkbox => {
        const itemData = JSON.parse(checkbox.value);
        hideAuthForm(itemData.detail_id);
    });
    
    updateBulkActions();
}

function showAuthForm(detailId) {
    const authForm = document.getElementById(`authForm_${detailId}`);
    if (authForm) {
        authForm.style.display = 'block';
    }
}

function hideAuthForm(detailId) {
    const authForm = document.getElementById(`authForm_${detailId}`);
    if (authForm) {
        authForm.style.display = 'none';
    }
}

function updateBulkActions() {
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');
    
    if (selectedItems.size > 0) {
        bulkActions.style.display = 'block';
        selectedCount.textContent = `${selectedItems.size} item${selectedItems.size !== 1 ? 's' : ''} selected`;
    } else {
        bulkActions.style.display = 'none';
    }
}

function selectAll() {
    document.querySelectorAll('.item-checkbox').forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelection();
}

function clearSelection() {
    document.querySelectorAll('.item-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelection();
}

function toggleAuthForm(detailId) {
    const authForm = document.getElementById(`authForm_${detailId}`);
    const checkbox = document.querySelector(`input[name="selected_items"][value*='"detail_id": ${detailId}']`);
    
    if (authForm.style.display === 'none' || !authForm.style.display) {
        authForm.style.display = 'block';
        checkbox.checked = true;
    } else {
        authForm.style.display = 'none';
        checkbox.checked = false;
    }
    updateSelection();
}

function validateAuthQuantity(input) {
    const quantity = parseFloat(input.value);
    const maxQuantity = parseFloat(input.getAttribute('max'));
    
    if (quantity > maxQuantity) {
        alert(`Authorization quantity cannot exceed ${maxQuantity}`);
        input.value = maxQuantity;
    }
    
    if (quantity < 0) {
        input.value = 0;
    }
}

function authorizeSelected() {
    if (selectedItems.size === 0) {
        alert('Please select at least one item for authorization.');
        return;
    }
    
    // Validate that all selected items have authorization quantities
    let hasErrors = false;
    selectedItems.forEach(itemValue => {
        const itemData = JSON.parse(itemValue);
        const quantityInput = document.getElementById(`auth_quantity_${itemData.detail_id}`);
        
        if (!quantityInput || !quantityInput.value || parseFloat(quantityInput.value) <= 0) {
            hasErrors = true;
            alert(`Please enter a valid authorization quantity for item ${itemData.detail_id}`);
        }
    });
    
    if (hasErrors) return;
    
    // Submit the form via HTMX
    const form = document.getElementById('authorizationForm');
    const formData = new FormData(form);
    
    // Add selected items to form data
    selectedItems.forEach(itemValue => {
        formData.append('selected_items', itemValue);
    });
    
    fetch(window.location.href, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'HX-Request': 'true'
        }
    })
    .then(response => response.text())
    .then(html => {
        document.getElementById('authorizationResult').innerHTML = html;
        const modal = new bootstrap.Modal(document.getElementById('authorizationModal'));
        modal.show();
        
        // Reload page after successful authorization
        setTimeout(() => {
            window.location.reload();
        }, 2000);
    })
    .catch(error => {
        console.error('Authorization failed:', error);
        alert('Authorization failed. Please try again.');
    });
}

function authorizeItem(detailId) {
    const checkbox = document.querySelector(`input[name="selected_items"][value*='"detail_id": ${detailId}']`);
    if (!checkbox.checked) {
        checkbox.checked = true;
        updateSelection();
    }
    
    authorizeSelected();
}

function bulkAuthorize() {
    const bulkQuantity = document.getElementById('bulkAuthQuantity').value;
    
    if (!bulkQuantity || parseFloat(bulkQuantity) <= 0) {
        alert('Please enter a valid bulk authorization quantity.');
        return;
    }
    
    // Set the bulk quantity for all selected items
    selectedItems.forEach(itemValue => {
        const itemData = JSON.parse(itemValue);
        const quantityInput = document.getElementById(`auth_quantity_${itemData.detail_id}`);
        if (quantityInput) {
            quantityInput.value = bulkQuantity;
            validateAuthQuantity(quantityInput);
        }
    });
    
    authorizeSelected();
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    updateSelection();
    
    // Auto-save feature (optional)
    document.querySelectorAll('.auth-quantity-input').forEach(input => {
        input.addEventListener('change', function() {
            // Could implement auto-save here
        });
    });
});
</script>
{% endblock %}