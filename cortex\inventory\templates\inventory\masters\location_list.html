{% extends "core/base.html" %}
{% load static %}

{% block title %}Item Locations - Inventory Management{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Item Locations</h1>
                <p class="text-gray-600 mt-1">Manage warehouse, zone, and bin locations</p>
            </div>
            <a href="{% url 'inventory:location_create' %}" 
               class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Create Location
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500">Total Locations</p>
                        <p class="text-lg font-semibold text-gray-900">{{ total_locations }}</p>
                    </div>
                </div>
            </div>
        </div>

        {% for label, count in label_stats.items %}
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center">
                            <span class="text-white font-bold">{{ label }}</span>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500">Label {{ label }}</p>
                        <p class="text-lg font-semibold text-gray-900">{{ count }}</p>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Search and Filter -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="p-6">
            <form method="get" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    {{ search_form.search.label_tag }}
                    {{ search_form.search }}
                </div>
                <div>
                    {{ search_form.location_label.label_tag }}
                    {{ search_form.location_label }}
                </div>
                <div>
                    <!-- Extra filter space for future use -->
                </div>
            </form>
        </div>
    </div>

    <!-- Locations Table -->
    <div class="bg-white shadow rounded-lg">
        <div id="location-results">
            {% include 'inventory/masters/partials/location_results.html' %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/htmx.org@1.9.6"></script>
<script>
    // Setup HTMX for search form
    document.addEventListener('DOMContentLoaded', function() {
        const searchForm = document.querySelector('form');
        const inputs = searchForm.querySelectorAll('input, select');
        
        inputs.forEach(input => {
            input.setAttribute('hx-get', window.location.pathname);
            input.setAttribute('hx-trigger', input.type === 'text' ? 'keyup changed delay:300ms' : 'change');
            input.setAttribute('hx-target', '#location-results');
            input.setAttribute('hx-include', 'form');
        });
    });
</script>
{% endblock %}