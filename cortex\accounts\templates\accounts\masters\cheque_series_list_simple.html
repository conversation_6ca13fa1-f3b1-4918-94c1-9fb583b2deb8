{% extends 'core/base.html' %}

{% block title %}Cheque Series - Accounts{% endblock %}

{% block content %}
<div class="px-8 py-6">
    <div class="flex items-center justify-between mb-6">
        <h1 class="text-2xl font-semibold text-sap-gray-800">Cheque Series</h1>
        <a href="{% url 'accounts:cheque_series_create' %}" 
           class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
            <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
            Add New Series
        </a>
    </div>

    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Bank ID</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Start No</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">End No</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Total Cheques</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for series in cheque_series %}
                    <tr class="hover:bg-sap-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">{{ series.bank_id }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">{{ series.start_no }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">{{ series.end_no }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">{{ series.total_cheques }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="{% url 'accounts:cheque_series_edit' series.pk %}" 
                               class="text-sap-blue-600 hover:text-sap-blue-900 mr-3">Edit</a>
                            <a href="{% url 'accounts:cheque_series_delete' series.pk %}" 
                               class="text-red-600 hover:text-red-900">Delete</a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center text-sm text-sap-gray-500">
                            No cheque series found. <a href="{% url 'accounts:cheque_series_create' %}" class="text-sap-blue-600 hover:text-sap-blue-900">Create the first one</a>.
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}