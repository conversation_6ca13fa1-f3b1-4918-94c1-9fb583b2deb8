﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="OfferLetter" targetNamespace="http://tempuri.org/OfferLetter.xsd" xmlns:mstns="http://tempuri.org/OfferLetter.xsd" xmlns="http://tempuri.org/OfferLetter.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="OfferLetter" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="OfferLetter" msprop:Generator_DataSetName="OfferLetter">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="OfferId" msprop:Generator_UserColumnName="OfferId" msprop:Generator_ColumnPropNameInRow="OfferId" msprop:Generator_ColumnVarNameInTable="columnOfferId" msprop:Generator_ColumnPropNameInTable="OfferIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="EmployeeName" msprop:Generator_UserColumnName="EmployeeName" msprop:Generator_ColumnVarNameInTable="columnEmployeeName" msprop:Generator_ColumnPropNameInRow="EmployeeName" msprop:Generator_ColumnPropNameInTable="EmployeeNameColumn" type="xs:string" minOccurs="0" />
              <xs:element name="StaffType" msprop:Generator_UserColumnName="StaffType" msprop:Generator_ColumnVarNameInTable="columnStaffType" msprop:Generator_ColumnPropNameInRow="StaffType" msprop:Generator_ColumnPropNameInTable="StaffTypeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="TypeOf" msprop:Generator_UserColumnName="TypeOf" msprop:Generator_ColumnVarNameInTable="columnTypeOf" msprop:Generator_ColumnPropNameInRow="TypeOf" msprop:Generator_ColumnPropNameInTable="TypeOfColumn" type="xs:int" minOccurs="0" />
              <xs:element name="salary" msprop:Generator_UserColumnName="salary" msprop:Generator_ColumnVarNameInTable="columnsalary" msprop:Generator_ColumnPropNameInRow="salary" msprop:Generator_ColumnPropNameInTable="salaryColumn" type="xs:double" minOccurs="0" />
              <xs:element name="DutyHrs" msprop:Generator_UserColumnName="DutyHrs" msprop:Generator_ColumnVarNameInTable="columnDutyHrs" msprop:Generator_ColumnPropNameInRow="DutyHrs" msprop:Generator_ColumnPropNameInTable="DutyHrsColumn" type="xs:string" minOccurs="0" />
              <xs:element name="OTHrs" msprop:Generator_UserColumnName="OTHrs" msprop:Generator_ColumnVarNameInTable="columnOTHrs" msprop:Generator_ColumnPropNameInRow="OTHrs" msprop:Generator_ColumnPropNameInTable="OTHrsColumn" type="xs:string" minOccurs="0" />
              <xs:element name="OverTime" msprop:Generator_UserColumnName="OverTime" msprop:Generator_ColumnVarNameInTable="columnOverTime" msprop:Generator_ColumnPropNameInRow="OverTime" msprop:Generator_ColumnPropNameInTable="OverTimeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="InterviewedBy" msprop:Generator_UserColumnName="InterviewedBy" msprop:Generator_ColumnVarNameInTable="columnInterviewedBy" msprop:Generator_ColumnPropNameInRow="InterviewedBy" msprop:Generator_ColumnPropNameInTable="InterviewedByColumn" type="xs:string" minOccurs="0" />
              <xs:element name="AuthorizedBy" msprop:Generator_UserColumnName="AuthorizedBy" msprop:Generator_ColumnVarNameInTable="columnAuthorizedBy" msprop:Generator_ColumnPropNameInRow="AuthorizedBy" msprop:Generator_ColumnPropNameInTable="AuthorizedByColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ReferenceBy" msprop:Generator_UserColumnName="ReferenceBy" msprop:Generator_ColumnVarNameInTable="columnReferenceBy" msprop:Generator_ColumnPropNameInRow="ReferenceBy" msprop:Generator_ColumnPropNameInTable="ReferenceByColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Designation" msprop:Generator_UserColumnName="Designation" msprop:Generator_ColumnVarNameInTable="columnDesignation" msprop:Generator_ColumnPropNameInRow="Designation" msprop:Generator_ColumnPropNameInTable="DesignationColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ExGratia" msprop:Generator_UserColumnName="ExGratia" msprop:Generator_ColumnVarNameInTable="columnExGratia" msprop:Generator_ColumnPropNameInRow="ExGratia" msprop:Generator_ColumnPropNameInTable="ExGratiaColumn" type="xs:double" minOccurs="0" />
              <xs:element name="VehicleAllowance" msprop:Generator_UserColumnName="VehicleAllowance" msprop:Generator_ColumnVarNameInTable="columnVehicleAllowance" msprop:Generator_ColumnPropNameInRow="VehicleAllowance" msprop:Generator_ColumnPropNameInTable="VehicleAllowanceColumn" type="xs:double" minOccurs="0" />
              <xs:element name="LTA" msprop:Generator_UserColumnName="LTA" msprop:Generator_ColumnVarNameInTable="columnLTA" msprop:Generator_ColumnPropNameInRow="LTA" msprop:Generator_ColumnPropNameInTable="LTAColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Loyalty" msprop:Generator_UserColumnName="Loyalty" msprop:Generator_ColumnVarNameInTable="columnLoyalty" msprop:Generator_ColumnPropNameInRow="Loyalty" msprop:Generator_ColumnPropNameInTable="LoyaltyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PaidLeaves" msprop:Generator_UserColumnName="PaidLeaves" msprop:Generator_ColumnVarNameInTable="columnPaidLeaves" msprop:Generator_ColumnPropNameInRow="PaidLeaves" msprop:Generator_ColumnPropNameInTable="PaidLeavesColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Remarks" msprop:Generator_UserColumnName="Remarks" msprop:Generator_ColumnVarNameInTable="columnRemarks" msprop:Generator_ColumnPropNameInRow="Remarks" msprop:Generator_ColumnPropNameInTable="RemarksColumn" type="xs:string" minOccurs="0" />
              <xs:element name="HeaderText" msprop:Generator_UserColumnName="HeaderText" msprop:Generator_ColumnPropNameInRow="HeaderText" msprop:Generator_ColumnVarNameInTable="columnHeaderText" msprop:Generator_ColumnPropNameInTable="HeaderTextColumn" type="xs:string" minOccurs="0" />
              <xs:element name="FooterText" msprop:Generator_UserColumnName="FooterText" msprop:Generator_ColumnPropNameInRow="FooterText" msprop:Generator_ColumnVarNameInTable="columnFooterText" msprop:Generator_ColumnPropNameInTable="FooterTextColumn" type="xs:string" minOccurs="0" />
              <xs:element name="SysDate" msprop:Generator_UserColumnName="SysDate" msprop:Generator_ColumnVarNameInTable="columnSysDate" msprop:Generator_ColumnPropNameInRow="SysDate" msprop:Generator_ColumnPropNameInTable="SysDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PerMonth" msprop:Generator_UserColumnName="PerMonth" msprop:Generator_ColumnPropNameInRow="PerMonth" msprop:Generator_ColumnVarNameInTable="columnPerMonth" msprop:Generator_ColumnPropNameInTable="PerMonthColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PerMonthA" msprop:Generator_UserColumnName="PerMonthA" msprop:Generator_ColumnPropNameInRow="PerMonthA" msprop:Generator_ColumnVarNameInTable="columnPerMonthA" msprop:Generator_ColumnPropNameInTable="PerMonthAColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Basic" msprop:Generator_UserColumnName="Basic" msprop:Generator_ColumnPropNameInRow="Basic" msprop:Generator_ColumnVarNameInTable="columnBasic" msprop:Generator_ColumnPropNameInTable="BasicColumn" type="xs:double" minOccurs="0" />
              <xs:element name="BasicA" msprop:Generator_UserColumnName="BasicA" msprop:Generator_ColumnPropNameInRow="BasicA" msprop:Generator_ColumnVarNameInTable="columnBasicA" msprop:Generator_ColumnPropNameInTable="BasicAColumn" type="xs:double" minOccurs="0" />
              <xs:element name="DA" msprop:Generator_UserColumnName="DA" msprop:Generator_ColumnPropNameInRow="DA" msprop:Generator_ColumnVarNameInTable="columnDA" msprop:Generator_ColumnPropNameInTable="DAColumn" type="xs:double" minOccurs="0" />
              <xs:element name="DAA" msprop:Generator_UserColumnName="DAA" msprop:Generator_ColumnPropNameInRow="DAA" msprop:Generator_ColumnVarNameInTable="columnDAA" msprop:Generator_ColumnPropNameInTable="DAAColumn" type="xs:double" minOccurs="0" />
              <xs:element name="HRA" msprop:Generator_UserColumnName="HRA" msprop:Generator_ColumnPropNameInRow="HRA" msprop:Generator_ColumnVarNameInTable="columnHRA" msprop:Generator_ColumnPropNameInTable="HRAColumn" type="xs:double" minOccurs="0" />
              <xs:element name="HRAA" msprop:Generator_UserColumnName="HRAA" msprop:Generator_ColumnPropNameInRow="HRAA" msprop:Generator_ColumnVarNameInTable="columnHRAA" msprop:Generator_ColumnPropNameInTable="HRAAColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Conveyance" msprop:Generator_UserColumnName="Conveyance" msprop:Generator_ColumnPropNameInRow="Conveyance" msprop:Generator_ColumnVarNameInTable="columnConveyance" msprop:Generator_ColumnPropNameInTable="ConveyanceColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ConveyanceA" msprop:Generator_UserColumnName="ConveyanceA" msprop:Generator_ColumnPropNameInRow="ConveyanceA" msprop:Generator_ColumnVarNameInTable="columnConveyanceA" msprop:Generator_ColumnPropNameInTable="ConveyanceAColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Education" msprop:Generator_UserColumnName="Education" msprop:Generator_ColumnPropNameInRow="Education" msprop:Generator_ColumnVarNameInTable="columnEducation" msprop:Generator_ColumnPropNameInTable="EducationColumn" type="xs:double" minOccurs="0" />
              <xs:element name="EducationA" msprop:Generator_UserColumnName="EducationA" msprop:Generator_ColumnPropNameInRow="EducationA" msprop:Generator_ColumnVarNameInTable="columnEducationA" msprop:Generator_ColumnPropNameInTable="EducationAColumn" type="xs:double" minOccurs="0" />
              <xs:element name="MedicalAllowance" msprop:Generator_UserColumnName="MedicalAllowance" msprop:Generator_ColumnPropNameInRow="MedicalAllowance" msprop:Generator_ColumnVarNameInTable="columnMedicalAllowance" msprop:Generator_ColumnPropNameInTable="MedicalAllowanceColumn" type="xs:double" minOccurs="0" />
              <xs:element name="MedicalAllowanceA" msprop:Generator_UserColumnName="MedicalAllowanceA" msprop:Generator_ColumnPropNameInRow="MedicalAllowanceA" msprop:Generator_ColumnVarNameInTable="columnMedicalAllowanceA" msprop:Generator_ColumnPropNameInTable="MedicalAllowanceAColumn" type="xs:double" minOccurs="0" />
              <xs:element name="AttendanceBonus" msprop:Generator_UserColumnName="AttendanceBonus" msprop:Generator_ColumnPropNameInRow="AttendanceBonus" msprop:Generator_ColumnVarNameInTable="columnAttendanceBonus" msprop:Generator_ColumnPropNameInTable="AttendanceBonusColumn" type="xs:double" minOccurs="0" />
              <xs:element name="AttendanceBonusA" msprop:Generator_UserColumnName="AttendanceBonusA" msprop:Generator_ColumnPropNameInRow="AttendanceBonusA" msprop:Generator_ColumnVarNameInTable="columnAttendanceBonusA" msprop:Generator_ColumnPropNameInTable="AttendanceBonusAColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ExGratiaA" msprop:Generator_UserColumnName="ExGratiaA" msprop:Generator_ColumnPropNameInRow="ExGratiaA" msprop:Generator_ColumnVarNameInTable="columnExGratiaA" msprop:Generator_ColumnPropNameInTable="ExGratiaAColumn" type="xs:double" minOccurs="0" />
              <xs:element name="TakeHomeINR" msprop:Generator_UserColumnName="TakeHomeINR" msprop:Generator_ColumnPropNameInRow="TakeHomeINR" msprop:Generator_ColumnVarNameInTable="columnTakeHomeINR" msprop:Generator_ColumnPropNameInTable="TakeHomeINRColumn" type="xs:double" minOccurs="0" />
              <xs:element name="TakeHomeWithAttend1" msprop:Generator_UserColumnName="TakeHomeWithAttend1" msprop:Generator_ColumnPropNameInRow="TakeHomeWithAttend1" msprop:Generator_ColumnVarNameInTable="columnTakeHomeWithAttend1" msprop:Generator_ColumnPropNameInTable="TakeHomeWithAttend1Column" type="xs:double" minOccurs="0" />
              <xs:element name="TakeHomeWithAttend2" msprop:Generator_UserColumnName="TakeHomeWithAttend2" msprop:Generator_ColumnPropNameInRow="TakeHomeWithAttend2" msprop:Generator_ColumnVarNameInTable="columnTakeHomeWithAttend2" msprop:Generator_ColumnPropNameInTable="TakeHomeWithAttend2Column" type="xs:double" minOccurs="0" />
              <xs:element name="LoyaltyBenefitA" msprop:Generator_UserColumnName="LoyaltyBenefitA" msprop:Generator_ColumnPropNameInRow="LoyaltyBenefitA" msprop:Generator_ColumnVarNameInTable="columnLoyaltyBenefitA" msprop:Generator_ColumnPropNameInTable="LoyaltyBenefitAColumn" type="xs:double" minOccurs="0" />
              <xs:element name="LTAA" msprop:Generator_UserColumnName="LTAA" msprop:Generator_ColumnPropNameInRow="LTAA" msprop:Generator_ColumnVarNameInTable="columnLTAA" msprop:Generator_ColumnPropNameInTable="LTAAColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PFE" msprop:Generator_UserColumnName="PFE" msprop:Generator_ColumnPropNameInRow="PFE" msprop:Generator_ColumnVarNameInTable="columnPFE" msprop:Generator_ColumnPropNameInTable="PFEColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PFEA" msprop:Generator_UserColumnName="PFEA" msprop:Generator_ColumnPropNameInRow="PFEA" msprop:Generator_ColumnVarNameInTable="columnPFEA" msprop:Generator_ColumnPropNameInTable="PFEAColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PFC" msprop:Generator_UserColumnName="PFC" msprop:Generator_ColumnPropNameInRow="PFC" msprop:Generator_ColumnVarNameInTable="columnPFC" msprop:Generator_ColumnPropNameInTable="PFCColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PFCA" msprop:Generator_UserColumnName="PFCA" msprop:Generator_ColumnPropNameInRow="PFCA" msprop:Generator_ColumnVarNameInTable="columnPFCA" msprop:Generator_ColumnPropNameInTable="PFCAColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Bonus" msprop:Generator_UserColumnName="Bonus" msprop:Generator_ColumnPropNameInRow="Bonus" msprop:Generator_ColumnVarNameInTable="columnBonus" msprop:Generator_ColumnPropNameInTable="BonusColumn" type="xs:double" minOccurs="0" />
              <xs:element name="BonusA" msprop:Generator_UserColumnName="BonusA" msprop:Generator_ColumnPropNameInRow="BonusA" msprop:Generator_ColumnVarNameInTable="columnBonusA" msprop:Generator_ColumnPropNameInTable="BonusAColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Gratuity" msprop:Generator_UserColumnName="Gratuity" msprop:Generator_ColumnPropNameInRow="Gratuity" msprop:Generator_ColumnVarNameInTable="columnGratuity" msprop:Generator_ColumnPropNameInTable="GratuityColumn" type="xs:double" minOccurs="0" />
              <xs:element name="GratuityA" msprop:Generator_UserColumnName="GratuityA" msprop:Generator_ColumnPropNameInRow="GratuityA" msprop:Generator_ColumnVarNameInTable="columnGratuityA" msprop:Generator_ColumnPropNameInTable="GratuityAColumn" type="xs:double" minOccurs="0" />
              <xs:element name="VehicleAllowanceA" msprop:Generator_UserColumnName="VehicleAllowanceA" msprop:Generator_ColumnPropNameInRow="VehicleAllowanceA" msprop:Generator_ColumnVarNameInTable="columnVehicleAllowanceA" msprop:Generator_ColumnPropNameInTable="VehicleAllowanceAColumn" type="xs:double" minOccurs="0" />
              <xs:element name="CTCinINR" msprop:Generator_UserColumnName="CTCinINR" msprop:Generator_ColumnPropNameInRow="CTCinINR" msprop:Generator_ColumnVarNameInTable="columnCTCinINR" msprop:Generator_ColumnPropNameInTable="CTCinINRColumn" type="xs:double" minOccurs="0" />
              <xs:element name="CTCinINRA" msprop:Generator_UserColumnName="CTCinINRA" msprop:Generator_ColumnPropNameInRow="CTCinINRA" msprop:Generator_ColumnVarNameInTable="columnCTCinINRA" msprop:Generator_ColumnPropNameInTable="CTCinINRAColumn" type="xs:double" minOccurs="0" />
              <xs:element name="CTCinINRwithAttendBonus1" msprop:Generator_UserColumnName="CTCinINRwithAttendBonus1" msprop:Generator_ColumnPropNameInRow="CTCinINRwithAttendBonus1" msprop:Generator_ColumnVarNameInTable="columnCTCinINRwithAttendBonus1" msprop:Generator_ColumnPropNameInTable="CTCinINRwithAttendBonus1Column" type="xs:double" minOccurs="0" />
              <xs:element name="CTCinINRwithAttendBonus1A" msprop:Generator_UserColumnName="CTCinINRwithAttendBonus1A" msprop:Generator_ColumnPropNameInRow="CTCinINRwithAttendBonus1A" msprop:Generator_ColumnVarNameInTable="columnCTCinINRwithAttendBonus1A" msprop:Generator_ColumnPropNameInTable="CTCinINRwithAttendBonus1AColumn" type="xs:double" minOccurs="0" />
              <xs:element name="CTCinINRwithAttendBonus2" msprop:Generator_UserColumnName="CTCinINRwithAttendBonus2" msprop:Generator_ColumnPropNameInRow="CTCinINRwithAttendBonus2" msprop:Generator_ColumnVarNameInTable="columnCTCinINRwithAttendBonus2" msprop:Generator_ColumnPropNameInTable="CTCinINRwithAttendBonus2Column" type="xs:double" minOccurs="0" />
              <xs:element name="CTCinINRwithAttendBonus2A" msprop:Generator_UserColumnName="CTCinINRwithAttendBonus2A" msprop:Generator_ColumnPropNameInRow="CTCinINRwithAttendBonus2A" msprop:Generator_ColumnVarNameInTable="columnCTCinINRwithAttendBonus2A" msprop:Generator_ColumnPropNameInTable="CTCinINRwithAttendBonus2AColumn" type="xs:double" minOccurs="0" />
              <xs:element name="AttBonPer" msprop:Generator_UserColumnName="AttBonPer" msprop:Generator_ColumnPropNameInRow="AttBonPer" msprop:Generator_ColumnVarNameInTable="columnAttBonPer" msprop:Generator_ColumnPropNameInTable="AttBonPerColumn" type="xs:string" minOccurs="0" />
              <xs:element name="AttBonPer2" msprop:Generator_UserColumnName="AttBonPer2" msprop:Generator_ColumnVarNameInTable="columnAttBonPer2" msprop:Generator_ColumnPropNameInRow="AttBonPer2" msprop:Generator_ColumnPropNameInTable="AttBonPer2Column" type="xs:string" minOccurs="0" />
              <xs:element name="AttendanceBonus2" msprop:Generator_UserColumnName="AttendanceBonus2" msprop:Generator_ColumnVarNameInTable="columnAttendanceBonus2" msprop:Generator_ColumnPropNameInRow="AttendanceBonus2" msprop:Generator_ColumnPropNameInTable="AttendanceBonus2Column" type="xs:double" minOccurs="0" />
              <xs:element name="AttendanceBonusB" msprop:Generator_UserColumnName="AttendanceBonusB" msprop:Generator_ColumnVarNameInTable="columnAttendanceBonusB" msprop:Generator_ColumnPropNameInRow="AttendanceBonusB" msprop:Generator_ColumnPropNameInTable="AttendanceBonusBColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PFEmployee" msprop:Generator_UserColumnName="PFEmployee" msprop:Generator_ColumnPropNameInRow="PFEmployee" msprop:Generator_ColumnVarNameInTable="columnPFEmployee" msprop:Generator_ColumnPropNameInTable="PFEmployeeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PFCompany" msprop:Generator_UserColumnName="PFCompany" msprop:Generator_ColumnPropNameInRow="PFCompany" msprop:Generator_ColumnVarNameInTable="columnPFCompany" msprop:Generator_ColumnPropNameInTable="PFCompanyColumn" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="DataTable2" msprop:Generator_UserTableName="DataTable2" msprop:Generator_RowDeletedName="DataTable2RowDeleted" msprop:Generator_TableClassName="DataTable2DataTable" msprop:Generator_RowChangedName="DataTable2RowChanged" msprop:Generator_RowClassName="DataTable2Row" msprop:Generator_RowChangingName="DataTable2RowChanging" msprop:Generator_RowEvArgName="DataTable2RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable2RowChangeEventHandler" msprop:Generator_TablePropName="DataTable2" msprop:Generator_TableVarName="tableDataTable2" msprop:Generator_RowDeletingName="DataTable2RowDeleting">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Access_Perticulars" msprop:Generator_UserColumnName="Access_Perticulars" msprop:Generator_ColumnPropNameInRow="Access_Perticulars" msprop:Generator_ColumnVarNameInTable="columnAccess_Perticulars" msprop:Generator_ColumnPropNameInTable="Access_PerticularsColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Access_Amount" msprop:Generator_UserColumnName="Access_Amount" msprop:Generator_ColumnPropNameInRow="Access_Amount" msprop:Generator_ColumnVarNameInTable="columnAccess_Amount" msprop:Generator_ColumnPropNameInTable="Access_AmountColumn" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>