# accounts/tests/test_taxation.py
# Comprehensive unit tests for the taxation module in accounts app
# Tests cover VAT Register Views, Excise Duty Management, Tax Calculation Engine, Forms, and Models

import json
from decimal import Decimal
from datetime import date

from django.test import TestCase, RequestFactory, Client
from django.contrib.auth.models import User
from django.urls import reverse

from accounts.models import (
    VAT, ExciseDuty, TDSCode, Octori, SalesInvoiceMaster, BillBookingMaster, Company, FinancialYear
)
from accounts.tax_calculation_engine import (
    TaxCalculationEngine, TaxType, CalculationMethod, TaxCalculationResult, calculate_invoice_taxes, get_default_tax_rates
)
from accounts.forms import VATForm, ExciseDutyForm, TDSCodeForm, OctoriForm, ExciseCalculatorForm
try:
    from accounts.excise_forms import (
        ExciseDutySearchForm, ExciseBulkUpdateForm, QuickExciseDutyForm
    )
except ImportError:
    # Some forms may not exist yet
    ExciseDutySearchForm = None
    ExciseBulkUpdateForm = None 
    QuickExciseDutyForm = None
from sys_admin.models import Company, FinancialYear


class TaxationModelsTestCase(TestCase):
    """Test cases for taxation-related models"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test company and financial year
        self.company = Company.objects.create(
            company_name='Test Company',
            company_short_name='TEST',
            registration_no='REG123'
        )
        
        self.financial_year = FinancialYear.objects.create(
            financial_year='2024-25',
            start_date=date(2024, 4, 1),
            end_date=date(2025, 3, 31),
            is_current=True
        )
    
    def test_vat_model_creation(self):
        """Test VAT model creation and validation"""
        vat = VAT.objects.create(
            vat_percentage=5.50,
            description='Standard VAT Rate'
        )
        
        self.assertEqual(vat.vat_percentage, 5.50)
        self.assertEqual(vat.description, 'Standard VAT Rate')
    
    def test_vat_model_str_representation(self):
        """Test VAT model string representation"""
        vat = VAT.objects.create(
            vat_percentage=12.50,
            description='High VAT Rate'
        )
        
        expected_str = "12.5% - High VAT Rate"
        self.assertEqual(str(vat), expected_str)
    
    def test_excise_duty_model_creation(self):
        """Test ExciseDuty model creation and validation"""
        excise = ExciseDuty.objects.create(
            terms='Standard Excise',
            value=Decimal('12.00'),
            accessible_value=Decimal('100.00'),
            edu_cess=Decimal('2.00'),
            she_cess=Decimal('1.00'),
            is_default_excise=True,
            is_active=True,
            created_by=self.user
        )
        
        self.assertEqual(excise.terms, 'Standard Excise')
        self.assertEqual(excise.value, Decimal('12.00'))
        self.assertEqual(excise.accessible_value, Decimal('100.00'))
        self.assertEqual(excise.edu_cess, Decimal('2.00'))
        self.assertEqual(excise.she_cess, Decimal('1.00'))
        self.assertTrue(excise.is_default_excise)
        self.assertTrue(excise.is_active)
        self.assertEqual(excise.created_by, self.user)
    
    def test_excise_duty_calculation_methods(self):
        """Test ExciseDuty model calculation methods"""
        excise = ExciseDuty.objects.create(
            terms='Test Excise',
            value=Decimal('10.00'),
            accessible_value=Decimal('100.00'),
            edu_cess=Decimal('2.00'),
            she_cess=Decimal('1.00'),
            is_active=True
        )
        
        # Test total excise rate calculation
        # Basic: 10%, Edu Cess: 2% of basic, SHE Cess: 1% of basic
        # Total should be 10% + (10% * 2%) + (10% * 1%) = 10.3%
        expected_total_rate = Decimal('10.30')
        self.assertEqual(excise.total_excise_rate, expected_total_rate)
        
        # Test effective rate (for inclusive calculations)
        # Should be basic rate + cess on basic rate
        expected_effective_rate = Decimal('10.30')
        self.assertEqual(excise.effective_rate, expected_effective_rate)
        
        # Test excise amount calculation
        base_amount = Decimal('1000.00')
        calculation = excise.calculate_excise_amount(base_amount)
        
        self.assertEqual(calculation['accessible_amount'], 1000.00)
        self.assertEqual(calculation['basic_excise'], 100.00)
        self.assertEqual(calculation['edu_cess'], 2.00)
        self.assertEqual(calculation['she_cess'], 1.00)
        self.assertEqual(calculation['total_excise'], 103.00)
    
    def test_excise_duty_with_accessible_value(self):
        """Test ExciseDuty calculation with different accessible value"""
        excise = ExciseDuty.objects.create(
            terms='Test Excise',
            value=Decimal('12.00'),
            accessible_value=Decimal('80.00'),  # Only 80% of value is accessible
            edu_cess=Decimal('2.00'),
            she_cess=Decimal('1.00'),
            is_active=True
        )
        
        base_amount = Decimal('1000.00')
        calculation = excise.calculate_excise_amount(base_amount)
        
        # Accessible amount should be 80% of base
        self.assertEqual(calculation['accessible_amount'], 800.00)
        # Basic excise on accessible amount
        self.assertEqual(calculation['basic_excise'], 96.00)
        # Cess on basic excise
        self.assertEqual(calculation['edu_cess'], 1.92)
        self.assertEqual(calculation['she_cess'], 0.96)
        self.assertEqual(calculation['total_excise'], 98.88)
    
    def test_tds_code_model_creation(self):
        """Test TDSCode model creation"""
        tds = TDSCode.objects.create(
            tds_code='194A',
            description='Professional Services',
            tds_percentage=10.00
        )
        
        self.assertEqual(tds.tds_code, '194A')
        self.assertEqual(tds.description, 'Professional Services')
        self.assertEqual(tds.tds_percentage, 10.00)
    
    def test_octori_model_creation(self):
        """Test Octori model creation"""
        octori = Octori.objects.create(
            octori_percentage=1.50,
            description='Local tax'
        )
        
        self.assertEqual(octori.octori_percentage, 1.50)
        self.assertEqual(octori.description, 'Local tax')


class TaxCalculationEngineTestCase(TestCase):
    """Test cases for Tax Calculation Engine"""
    
    def setUp(self):
        """Set up test data"""
        self.engine = TaxCalculationEngine()
        
        # Create test excise duty
        self.excise_duty = ExciseDuty.objects.create(
            terms='Test Excise',
            value=Decimal('12.00'),
            accessible_value=Decimal('100.00'),
            edu_cess=Decimal('2.00'),
            she_cess=Decimal('1.00'),
            is_active=True
        )
    
    def test_engine_initialization(self):
        """Test TaxCalculationEngine initialization"""
        self.assertEqual(self.engine.precision, Decimal('0.01'))
        self.assertEqual(self.engine.tax_precision, Decimal('0.001'))
    
    def test_excise_duty_calculation_exclusive(self):
        """Test excise duty calculation with exclusive method"""
        base_amount = Decimal('10000.00')
        
        result = self.engine.calculate_excise_duty(
            base_amount, 
            self.excise_duty, 
            CalculationMethod.EXCLUSIVE
        )
        
        self.assertEqual(result.base_amount, base_amount)
        self.assertEqual(len(result.tax_components), 3)  # Basic + Edu Cess + SHE Cess
        
        # Check tax components
        basic_component = result.tax_components[0]
        self.assertEqual(basic_component.tax_type, TaxType.EXCISE)
        self.assertEqual(basic_component.amount, Decimal('1200.00'))  # 12% of 10000
        
        edu_cess_component = result.tax_components[1]
        self.assertEqual(edu_cess_component.tax_type, TaxType.CESS)
        self.assertEqual(edu_cess_component.amount, Decimal('24.00'))  # 2% of 1200
        self.assertTrue(edu_cess_component.is_compound)
        
        she_cess_component = result.tax_components[2]
        self.assertEqual(she_cess_component.tax_type, TaxType.CESS)
        self.assertEqual(she_cess_component.amount, Decimal('12.00'))  # 1% of 1200
        self.assertTrue(she_cess_component.is_compound)
        
        # Check totals
        expected_total_tax = Decimal('1236.00')  # 1200 + 24 + 12
        self.assertEqual(result.total_tax_amount, expected_total_tax)
        self.assertEqual(result.total_amount, base_amount + expected_total_tax)
    
    def test_excise_duty_calculation_inclusive(self):
        """Test excise duty calculation with inclusive method"""
        total_amount = Decimal('11236.00')  # Amount including all taxes
        
        result = self.engine.calculate_excise_duty(
            total_amount,
            self.excise_duty,
            CalculationMethod.INCLUSIVE
        )
        
        # Base amount should be extracted from total
        # With 12.36% effective rate, base = 11236 * 100 / 112.36
        expected_base = Decimal('10000.00')
        self.assertAlmostEqual(float(result.base_amount), float(expected_base), places=1)
        
        # Tax amount should be total - base
        expected_tax = total_amount - result.base_amount
        self.assertAlmostEqual(float(result.total_tax_amount), float(expected_tax), places=1)
    
    def test_vat_calculation_exclusive(self):
        """Test VAT calculation with exclusive method"""
        base_amount = Decimal('10000.00')
        vat_rate = Decimal('5.00')
        
        result = self.engine.calculate_vat(base_amount, vat_rate, CalculationMethod.EXCLUSIVE)
        
        self.assertEqual(result.base_amount, base_amount)
        self.assertEqual(len(result.tax_components), 1)
        
        vat_component = result.tax_components[0]
        self.assertEqual(vat_component.tax_type, TaxType.VAT)
        self.assertEqual(vat_component.rate, vat_rate)
        self.assertEqual(vat_component.amount, Decimal('500.00'))  # 5% of 10000
        
        self.assertEqual(result.total_tax_amount, Decimal('500.00'))
        self.assertEqual(result.total_amount, Decimal('10500.00'))
        self.assertEqual(result.effective_tax_rate, Decimal('5.00'))
    
    def test_vat_calculation_inclusive(self):
        """Test VAT calculation with inclusive method"""
        total_amount = Decimal('10500.00')
        vat_rate = Decimal('5.00')
        
        result = self.engine.calculate_vat(total_amount, vat_rate, CalculationMethod.INCLUSIVE)
        
        # Base should be 10500 * 100 / 105 = 10000
        expected_base = Decimal('10000.00')
        self.assertEqual(result.base_amount, expected_base)
        
        expected_vat = Decimal('500.00')
        self.assertEqual(result.total_tax_amount, expected_vat)
        self.assertEqual(result.total_amount, total_amount)
    
    def test_tds_calculation_with_threshold(self):
        """Test TDS calculation with threshold"""
        base_amount = Decimal('50000.00')
        tds_rate = Decimal('10.00')
        threshold = Decimal('30000.00')
        
        result = self.engine.calculate_tds(base_amount, tds_rate, threshold)
        
        # Should calculate TDS as amount exceeds threshold
        self.assertEqual(result.base_amount, base_amount)
        self.assertEqual(len(result.tax_components), 1)
        
        tds_component = result.tax_components[0]
        self.assertEqual(tds_component.tax_type, TaxType.TDS)
        self.assertEqual(tds_component.amount, Decimal('5000.00'))  # 10% of 50000
        
        # TDS is deducted, so total amount is less than base
        self.assertEqual(result.total_amount, Decimal('45000.00'))  # 50000 - 5000
    
    def test_tds_calculation_below_threshold(self):
        """Test TDS calculation below threshold"""
        base_amount = Decimal('20000.00')
        tds_rate = Decimal('10.00')
        threshold = Decimal('30000.00')
        
        result = self.engine.calculate_tds(base_amount, tds_rate, threshold)
        
        # Should not calculate TDS as amount is below threshold
        self.assertEqual(result.base_amount, base_amount)
        self.assertEqual(len(result.tax_components), 0)
        self.assertEqual(result.total_tax_amount, Decimal('0.00'))
        self.assertEqual(result.total_amount, base_amount)  # No TDS deduction
    
    def test_composite_tax_calculation(self):
        """Test composite tax calculation with multiple tax types"""
        base_amount = Decimal('10000.00')
        
        tax_config = {
            'excise': {
                'rate': 12.0,
                'accessible_value': 100.0,
                'edu_cess': 2.0,
                'she_cess': 1.0
            },
            'vat': {
                'rate': 5.0
            },
            'tds': {
                'rate': 10.0,
                'threshold': 5000.0
            }
        }
        
        result = self.engine.calculate_composite_tax(base_amount, tax_config)
        
        # Should have components for excise (3), vat (1), tds (1) = 5 total
        self.assertEqual(len(result.tax_components), 5)
        
        # Check that all tax types are present
        tax_types = [component.tax_type for component in result.tax_components]
        self.assertIn(TaxType.EXCISE, tax_types)
        self.assertIn(TaxType.CESS, tax_types)
        self.assertIn(TaxType.VAT, tax_types)
        self.assertIn(TaxType.TDS, tax_types)
        
        # TDS should be deducted from final amount
        excise_total = Decimal('1236.00')  # From previous test
        vat_on_excise_inclusive = (base_amount + excise_total) * Decimal('0.05')  # 5% VAT
        tds_amount = base_amount * Decimal('0.10')  # 10% TDS on base
        
        expected_final = base_amount + excise_total + vat_on_excise_inclusive - tds_amount
        self.assertAlmostEqual(float(result.total_amount), float(expected_final), places=1)
    
    def test_reverse_calculation(self):
        """Test reverse calculation from total amount"""
        total_amount = Decimal('11500.00')
        
        tax_config = {
            'excise': {'rate': 10.0},
            'vat': {'rate': 5.0}
        }
        
        result = self.engine.reverse_calculate_base_amount(total_amount, tax_config)
        
        # Verify that forward calculation gives back the target amount
        forward_result = self.engine.calculate_composite_tax(
            result.base_amount, 
            tax_config, 
            CalculationMethod.EXCLUSIVE
        )
        
        self.assertAlmostEqual(
            float(forward_result.total_amount), 
            float(total_amount), 
            places=1
        )
    
    def test_tax_rate_validation(self):
        """Test tax rate validation"""
        # Valid configuration
        valid_config = {
            'excise': {'rate': 12.0, 'edu_cess': 2.0, 'she_cess': 1.0},
            'vat': {'rate': 5.0},
            'tds': {'rate': 10.0, 'threshold': 5000.0}
        }
        
        errors = self.engine.validate_tax_rates(valid_config)
        self.assertEqual(len(errors), 0)
        
        # Invalid configuration - rates too high
        invalid_config = {
            'excise': {'rate': 150.0},  # Rate > 100%
            'vat': {'rate': -5.0},      # Negative rate
            'tds': {'rate': 10.0, 'threshold': -1000.0}  # Negative threshold
        }
        
        errors = self.engine.validate_tax_rates(invalid_config)
        self.assertGreater(len(errors), 0)
        self.assertIn('150.0% must be between 0% and 100%', ' '.join(errors))
        self.assertIn('-5.0% must be between 0% and 100%', ' '.join(errors))
        self.assertIn('-1000.0 must be non-negative', ' '.join(errors))
    
    def test_tax_breakdown_summary(self):
        """Test tax breakdown summary generation"""
        base_amount = Decimal('10000.00')
        
        tax_config = {
            'excise': {'rate': 12.0, 'edu_cess': 2.0},
            'vat': {'rate': 5.0}
        }
        
        result = self.engine.calculate_composite_tax(base_amount, tax_config)
        summary = self.engine.get_tax_breakdown_summary(result)
        
        self.assertIn('base_amount', summary)
        self.assertIn('total_tax_amount', summary)
        self.assertIn('total_amount', summary)
        self.assertIn('effective_tax_rate', summary)
        self.assertIn('tax_components', summary)
        
        # Check grouped components
        tax_groups = {group['type']: group for group in summary['tax_components']}
        self.assertIn('excise', tax_groups)
        self.assertIn('cess', tax_groups)
        self.assertIn('vat', tax_groups)


class VATRegisterViewsTestCase(TestCase):
    """Test cases for VAT Register Views"""
    
    def setUp(self):
        """Set up test data"""
        self.client = Client()
        self.factory = RequestFactory()
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test company and financial year
        self.company = Company.objects.create(
            company_name='Test Company',
            company_short_name='TEST'
        )
        
        self.financial_year = FinancialYear.objects.create(
            financial_year='2024-25',
            start_date=date(2024, 4, 1),
            end_date=date(2025, 3, 31),
            is_current=True
        )
        
        # Create test VAT rates
        self.vat_5 = VAT.objects.create(
            vat_percentage=Decimal('5.00'),
            description='Standard Rate'
        )
        
        self.vat_12 = VAT.objects.create(
            vat_percentage=Decimal('12.00'),
            description='Higher Rate'
        )
        
        # Create test invoice data
        self.sales_invoice = SalesInvoiceMaster.objects.create(
            invoice_no='SI001',
            invoice_date=date.today(),
            customer_name='Test Customer',
            basic_amount=Decimal('10000.00'),
            vat_amount=Decimal('500.00'),
            total_amount=Decimal('10500.00'),
            status='approved',
            company=self.company,
            financial_year=self.financial_year
        )
        
        self.purchase_bill = BillBookingMaster.objects.create(
            bill_no='PB001',
            bill_date=date.today(),
            supplier_name='Test Supplier',
            basic_amount=Decimal('8000.00'),
            vat_amount=Decimal('400.00'),
            total_amount=Decimal('8400.00'),
            status='authorized',
            company=self.company,
            financial_year=self.financial_year
        )
    
    def test_vat_register_dashboard_view_get(self):
        """Test VAT Register Dashboard view GET request"""
        self.client.login(username='testuser', password='testpass123')
        
        # Setup session data
        session = self.client.session
        session['company_id'] = self.company.id
        session['financial_year_id'] = self.financial_year.id
        session.save()
        
        url = reverse('accounts:vat_register_dashboard')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'VAT Register Dashboard')
        self.assertIn('current_company', response.context)
        self.assertIn('current_fy', response.context)
        self.assertIn('current_month_summary', response.context)
        self.assertIn('quarterly_summary', response.context)
        self.assertIn('vat_rates', response.context)
    
    def test_vat_register_dashboard_view_requires_login(self):
        """Test VAT Register Dashboard view requires authentication"""
        url = reverse('accounts:vat_register_dashboard')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 302)  # Redirect to login
        self.assertIn('/login/', response.url)
    
    def test_sales_vat_register_view_get(self):
        """Test Sales VAT Register view GET request"""
        self.client.login(username='testuser', password='testpass123')
        
        session = self.client.session
        session['company_id'] = self.company.id
        session['financial_year_id'] = self.financial_year.id
        session.save()
        
        url = reverse('accounts:sales_vat_register')
        response = self.client.get(url, {
            'from_date': '2024-01-01',
            'to_date': '2024-12-31'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('vat_register_data', response.context)
        self.assertIn('summary_totals', response.context)
        self.assertIn('companies', response.context)
        self.assertIn('financial_years', response.context)
        self.assertIn('vat_rates', response.context)
    
    def test_sales_vat_register_view_with_filters(self):
        """Test Sales VAT Register view with filters"""
        self.client.login(username='testuser', password='testpass123')
        
        session = self.client.session
        session['company_id'] = self.company.id
        session['financial_year_id'] = self.financial_year.id
        session.save()
        
        url = reverse('accounts:sales_vat_register')
        response = self.client.get(url, {
            'from_date': '2024-01-01',
            'to_date': '2024-12-31',
            'customer_filter': 'Test Customer',
            'vat_rate_filter': '5.00',
            'invoice_type_filter': 'standard'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['customer_filter'], 'Test Customer')
        self.assertEqual(response.context['vat_rate_filter'], '5.00')
        self.assertEqual(response.context['invoice_type_filter'], 'standard')
    
    def test_purchase_vat_register_view_get(self):
        """Test Purchase VAT Register view GET request"""
        self.client.login(username='testuser', password='testpass123')
        
        session = self.client.session
        session['company_id'] = self.company.id
        session['financial_year_id'] = self.financial_year.id
        session.save()
        
        url = reverse('accounts:purchase_vat_register')
        response = self.client.get(url, {
            'from_date': '2024-01-01',
            'to_date': '2024-12-31'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('vat_register_data', response.context)
        self.assertIn('summary_totals', response.context)
    
    def test_vat_return_form_view_monthly(self):
        """Test VAT Return Form view for monthly return"""
        self.client.login(username='testuser', password='testpass123')
        
        session = self.client.session
        session['company_id'] = self.company.id
        session['financial_year_id'] = self.financial_year.id
        session.save()
        
        url = reverse('accounts:vat_return_form')
        response = self.client.get(url, {
            'return_period': 'monthly',
            'period_year': '2024',
            'period_month': '6'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('vat_return_data', response.context)
        self.assertEqual(response.context['return_period'], 'monthly')
        self.assertEqual(response.context['period_month'], '6')
    
    def test_vat_return_form_view_quarterly(self):
        """Test VAT Return Form view for quarterly return"""
        self.client.login(username='testuser', password='testpass123')
        
        session = self.client.session
        session['company_id'] = self.company.id
        session['financial_year_id'] = self.financial_year.id
        session.save()
        
        url = reverse('accounts:vat_return_form')
        response = self.client.get(url, {
            'return_period': 'quarterly',
            'period_year': '2024',
            'period_quarter': '2'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('vat_return_data', response.context)
        self.assertEqual(response.context['return_period'], 'quarterly')
        self.assertEqual(response.context['period_quarter'], '2')
    
    def test_export_vat_register_view_sales(self):
        """Test Export VAT Register view for sales"""
        self.client.login(username='testuser', password='testpass123')
        
        session = self.client.session
        session['company_id'] = self.company.id
        session['financial_year_id'] = self.financial_year.id
        session.save()
        
        url = reverse('accounts:export_vat_register')
        response = self.client.get(url, {
            'export_type': 'sales',
            'from_date': '2024-01-01',
            'to_date': '2024-12-31'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        self.assertIn('attachment', response['Content-Disposition'])
    
    def test_export_vat_register_view_purchase(self):
        """Test Export VAT Register view for purchase"""
        self.client.login(username='testuser', password='testpass123')
        
        session = self.client.session
        session['company_id'] = self.company.id
        session['financial_year_id'] = self.financial_year.id
        session.save()
        
        url = reverse('accounts:export_vat_register')
        response = self.client.get(url, {
            'export_type': 'purchase',
            'from_date': '2024-01-01',
            'to_date': '2024-12-31'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        self.assertIn('attachment', response['Content-Disposition'])
    
    def test_ajax_get_vat_summary(self):
        """Test AJAX VAT summary endpoint"""
        self.client.login(username='testuser', password='testpass123')
        
        session = self.client.session
        session['company_id'] = self.company.id
        session['financial_year_id'] = self.financial_year.id
        session.save()
        
        url = reverse('accounts:ajax_get_vat_summary')
        response = self.client.get(url, {
            'from_date': '2024-01-01',
            'to_date': '2024-12-31'
        })
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        
        self.assertTrue(data['success'])
        self.assertIn('sales_vat', data)
        self.assertIn('purchase_vat', data)
        self.assertIn('net_vat_liability', data)
        self.assertIn('period', data)
    
    def test_ajax_get_vat_summary_missing_dates(self):
        """Test AJAX VAT summary endpoint with missing dates"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:ajax_get_vat_summary')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.content)
        self.assertIn('error', data)
        self.assertEqual(data['error'], 'Date range required')
    
    def test_ajax_get_vat_rate_breakdown_sales(self):
        """Test AJAX VAT rate breakdown endpoint for sales"""
        self.client.login(username='testuser', password='testpass123')
        
        session = self.client.session
        session['company_id'] = self.company.id
        session['financial_year_id'] = self.financial_year.id
        session.save()
        
        url = reverse('accounts:ajax_get_vat_rate_breakdown')
        response = self.client.get(url, {
            'type': 'sales',
            'from_date': '2024-01-01',
            'to_date': '2024-12-31'
        })
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        
        self.assertTrue(data['success'])
        self.assertIn('breakdown', data)
        self.assertIn('transaction_type', data)
        self.assertEqual(data['transaction_type'], 'sales')
    
    def test_ajax_get_vat_rate_breakdown_purchase(self):
        """Test AJAX VAT rate breakdown endpoint for purchase"""
        self.client.login(username='testuser', password='testpass123')
        
        session = self.client.session
        session['company_id'] = self.company.id
        session['financial_year_id'] = self.financial_year.id
        session.save()
        
        url = reverse('accounts:ajax_get_vat_rate_breakdown')
        response = self.client.get(url, {
            'type': 'purchase',
            'from_date': '2024-01-01',
            'to_date': '2024-12-31'
        })
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        
        self.assertTrue(data['success'])
        self.assertIn('breakdown', data)
        self.assertEqual(data['transaction_type'], 'purchase')


class ExciseDutyViewsTestCase(TestCase):
    """Test cases for Excise Duty Views"""
    
    def setUp(self):
        """Set up test data"""
        self.client = Client()
        self.factory = RequestFactory()
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test excise duties
        self.excise_duty_1 = ExciseDuty.objects.create(
            terms='Standard Excise',
            value=Decimal('12.00'),
            accessible_value=Decimal('100.00'),
            edu_cess=Decimal('2.00'),
            she_cess=Decimal('1.00'),
            is_default_excise=True,
            is_active=True,
            created_by=self.user
        )
        
        self.excise_duty_2 = ExciseDuty.objects.create(
            terms='Service Tax',
            value=Decimal('14.00'),
            accessible_value=Decimal('100.00'),
            edu_cess=Decimal('2.00'),
            she_cess=Decimal('1.00'),
            is_default_service_tax=True,
            is_active=True,
            created_by=self.user
        )
    
    def test_excise_duty_list_view_get(self):
        """Test Excise Duty List view GET request"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:excise_duty_list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Standard Excise')
        self.assertContains(response, 'Service Tax')
        self.assertIn('excise_duties', response.context)
        self.assertIn('summary', response.context)
    
    def test_excise_duty_list_view_with_search(self):
        """Test Excise Duty List view with search"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:excise_duty_list')
        response = self.client.get(url, {'search': 'Standard'})
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Standard Excise')
        self.assertNotContains(response, 'Service Tax')
    
    def test_excise_duty_list_view_with_filters(self):
        """Test Excise Duty List view with filters"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:excise_duty_list')
        response = self.client.get(url, {
            'duty_type': 'excise',
            'rate_range': '10-15',
            'status': 'active'
        })
        
        self.assertEqual(response.status_code, 200)
        # Should show both duties as they're in 10-15% range and active
        self.assertContains(response, 'Standard Excise')
    
    def test_excise_duty_create_view_get(self):
        """Test Excise Duty Create view GET request"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:excise_duty_create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Create Excise/Service Tax')
        self.assertIn('form', response.context)
    
    def test_excise_duty_create_view_post_valid(self):
        """Test Excise Duty Create view POST with valid data"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:excise_duty_create')
        data = {
            'terms': 'New Excise Duty',
            'value': '10.00',
            'accessible_value': '100.00',
            'edu_cess': '2.00',
            'she_cess': '1.00',
            'is_active': True
        }
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, 302)  # Redirect after success
        
        # Check that object was created
        new_excise = ExciseDuty.objects.filter(terms='New Excise Duty').first()
        self.assertIsNotNone(new_excise)
        self.assertEqual(new_excise.value, Decimal('10.00'))
        self.assertEqual(new_excise.created_by, self.user)
    
    def test_excise_duty_create_view_post_invalid(self):
        """Test Excise Duty Create view POST with invalid data"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:excise_duty_create')
        data = {
            'terms': '',  # Required field missing
            'value': 'invalid',  # Invalid decimal
        }
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, 200)  # Stay on form page
        self.assertFormError(response, 'form', 'terms', 'This field is required.')
    
    def test_excise_duty_update_view_get(self):
        """Test Excise Duty Update view GET request"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:excise_duty_update', kwargs={'pk': self.excise_duty_1.pk})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Edit Excise/Service Tax')
        self.assertContains(response, 'Standard Excise')
        self.assertIn('calculation_examples', response.context)
    
    def test_excise_duty_update_view_post_valid(self):
        """Test Excise Duty Update view POST with valid data"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:excise_duty_update', kwargs={'pk': self.excise_duty_1.pk})
        data = {
            'terms': 'Updated Excise Duty',
            'value': '15.00',
            'accessible_value': '100.00',
            'edu_cess': '3.00',
            'she_cess': '1.50',
            'is_active': True
        }
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, 302)  # Redirect after success
        
        # Check that object was updated
        updated_excise = ExciseDuty.objects.get(pk=self.excise_duty_1.pk)
        self.assertEqual(updated_excise.terms, 'Updated Excise Duty')
        self.assertEqual(updated_excise.value, Decimal('15.00'))
        self.assertEqual(updated_excise.updated_by, self.user)
    
    def test_excise_duty_delete_view_get(self):
        """Test Excise Duty Delete view GET request"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:excise_duty_delete', kwargs={'pk': self.excise_duty_1.pk})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Standard Excise')
        self.assertIn('usage_count', response.context)
        self.assertIn('can_delete', response.context)
    
    def test_excise_duty_delete_view_post_default_duty(self):
        """Test Excise Duty Delete view POST for default duty (should fail)"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:excise_duty_delete', kwargs={'pk': self.excise_duty_1.pk})
        response = self.client.post(url)
        
        # Should redirect with error message (default duty cannot be deleted)
        self.assertEqual(response.status_code, 302)
        
        # Object should still exist
        self.assertTrue(ExciseDuty.objects.filter(pk=self.excise_duty_1.pk).exists())
    
    def test_excise_duty_delete_view_post_non_default(self):
        """Test Excise Duty Delete view POST for non-default duty"""
        # Create a non-default duty
        non_default_duty = ExciseDuty.objects.create(
            terms='Non-default Duty',
            value=Decimal('8.00'),
            is_active=True,
            created_by=self.user
        )
        
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:excise_duty_delete', kwargs={'pk': non_default_duty.pk})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, 302)  # Redirect after success
        
        # Object should be deleted
        self.assertFalse(ExciseDuty.objects.filter(pk=non_default_duty.pk).exists())
    
    def test_excise_calculator_view_get(self):
        """Test Excise Calculator view GET request"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:excise_calculator')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('calculator_form', response.context)
        self.assertIn('active_duties', response.context)
    
    def test_excise_calculator_view_post_valid(self):
        """Test Excise Calculator view POST with valid data"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:excise_calculator')
        data = {
            'excise_duty': self.excise_duty_1.pk,
            'base_amount': '10000.00',
            'calculation_type': 'exclusive'
        }
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('calculation_result', response.context)
        
        result = response.context['calculation_result']
        self.assertIn('total_excise', result)
        self.assertIn('excise_duty', result)
    
    def test_excise_calculator_view_post_inclusive(self):
        """Test Excise Calculator view POST with inclusive calculation"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:excise_calculator')
        data = {
            'excise_duty': self.excise_duty_1.pk,
            'base_amount': '11236.00',
            'calculation_type': 'inclusive'
        }
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('calculation_result', response.context)
        
        result = response.context['calculation_result']
        self.assertIn('assessable_amount', result)
        self.assertEqual(result['calculation_type'], 'inclusive')
    
    def test_excise_duty_report_view(self):
        """Test Excise Duty Report view"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:excise_duty_report')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('rate_ranges', response.context)
        self.assertIn('usage_stats', response.context)
        self.assertIn('summary_stats', response.context)
    
    def test_get_excise_duty_details_ajax(self):
        """Test AJAX get excise duty details endpoint"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:get_excise_duty_details')
        response = self.client.get(url, {'duty_id': self.excise_duty_1.pk})
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        
        self.assertEqual(data['id'], self.excise_duty_1.pk)
        self.assertEqual(data['terms'], 'Standard Excise')
        self.assertEqual(data['value'], 12.0)
        self.assertTrue(data['is_default_excise'])
    
    def test_get_excise_duty_details_ajax_not_found(self):
        """Test AJAX get excise duty details endpoint with invalid ID"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:get_excise_duty_details')
        response = self.client.get(url, {'duty_id': 9999})
        
        self.assertEqual(response.status_code, 404)
        data = json.loads(response.content)
        self.assertIn('error', data)
    
    def test_calculate_excise_amount_ajax(self):
        """Test AJAX calculate excise amount endpoint"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:calculate_excise_amount')
        response = self.client.get(url, {
            'duty_id': self.excise_duty_1.pk,
            'amount': '10000.00',
            'type': 'exclusive'
        })
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        
        self.assertIn('calculation', data)
        self.assertIn('duty_info', data)
        self.assertEqual(data['calculation']['original_amount'], 10000.0)
    
    def test_calculate_excise_amount_ajax_inclusive(self):
        """Test AJAX calculate excise amount endpoint with inclusive calculation"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:calculate_excise_amount')
        response = self.client.get(url, {
            'duty_id': self.excise_duty_1.pk,
            'amount': '11236.00',
            'type': 'inclusive'
        })
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        
        self.assertIn('calculation', data)
        self.assertIn('assessable_amount', data['calculation'])
    
    def test_calculate_excise_amount_ajax_invalid_amount(self):
        """Test AJAX calculate excise amount endpoint with invalid amount"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:calculate_excise_amount')
        response = self.client.get(url, {
            'duty_id': self.excise_duty_1.pk,
            'amount': 'invalid',
            'type': 'exclusive'
        })
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.content)
        self.assertIn('error', data)


class TaxCalculatorViewsTestCase(TestCase):
    """Test cases for Tax Calculator Views"""
    
    def setUp(self):
        """Set up test data"""
        self.client = Client()
        self.factory = RequestFactory()
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test tax data
        self.excise_duty = ExciseDuty.objects.create(
            terms='Test Excise',
            value=Decimal('12.00'),
            accessible_value=Decimal('100.00'),
            edu_cess=Decimal('2.00'),
            she_cess=Decimal('1.00'),
            is_default_excise=True,
            is_active=True,
            created_by=self.user
        )
        
        self.vat = VAT.objects.create(
            vat_percentage=Decimal('5.00'),
            description='Standard VAT'
        )
        
        self.tds_code = TDSCode.objects.create(
            tds_code='194A',
            description='Professional Services',
            tds_percentage=Decimal('10.00'),
            threshold_amount=Decimal('30000.00')
        )
    
    def test_tax_calculator_dashboard_view(self):
        """Test Tax Calculator Dashboard view"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:tax_calculator_dashboard')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('available_taxes', response.context)
        self.assertIn('default_rates', response.context)
        self.assertIn('recent_calculations', response.context)
    
    def test_composite_invoice_tax_calculator_view_get(self):
        """Test Composite Invoice Tax Calculator view GET request"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:composite_tax_calculator')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('excise_duties', response.context)
        self.assertIn('vat_rates', response.context)
        self.assertIn('tds_codes', response.context)
        self.assertIn('default_rates', response.context)
    
    def test_composite_invoice_tax_calculator_view_post_valid(self):
        """Test Composite Invoice Tax Calculator view POST with valid data"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:composite_tax_calculator')
        data = {
            'base_amount': '10000.00',
            'calculation_method': 'exclusive',
            'excise_duty_id': str(self.excise_duty.pk),
            'vat_rate': '5.00',
            'tds_rate': '10.00',
            'tds_threshold': '5000.00'
        }
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('calculation_result', response.context)
        self.assertIn('calculation_summary', response.context)
        
        result = response.context['calculation_result']
        self.assertIsNotNone(result.base_amount)
        self.assertIsNotNone(result.total_tax_amount)
        self.assertIsNotNone(result.total_amount)
    
    def test_composite_invoice_tax_calculator_view_post_inclusive(self):
        """Test Composite Invoice Tax Calculator view POST with inclusive calculation"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:composite_tax_calculator')
        data = {
            'base_amount': '12000.00',
            'calculation_method': 'inclusive',
            'excise_duty_id': str(self.excise_duty.pk),
            'vat_rate': '5.00'
        }
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('calculation_result', response.context)
        
        result = response.context['calculation_result']
        self.assertEqual(result.calculation_method, CalculationMethod.INCLUSIVE)
    
    def test_composite_invoice_tax_calculator_view_post_invalid_amount(self):
        """Test Composite Invoice Tax Calculator view POST with invalid amount"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:composite_tax_calculator')
        data = {
            'base_amount': 'invalid',
            'calculation_method': 'exclusive'
        }
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('calculation_errors', response.context)
    
    def test_reverse_calculator_view_get(self):
        """Test Reverse Calculator view GET request"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:reverse_tax_calculator')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('excise_duties', response.context)
        self.assertIn('vat_rates', response.context)
        self.assertIn('tds_codes', response.context)
    
    def test_reverse_calculator_view_post_valid(self):
        """Test Reverse Calculator view POST with valid data"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:reverse_tax_calculator')
        data = {
            'total_amount': '11500.00',
            'excise_duty_id': str(self.excise_duty.pk),
            'vat_rate': '5.00'
        }
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('calculation_result', response.context)
        self.assertIn('calculation_summary', response.context)
        
        result = response.context['calculation_result']
        # Should calculate base amount from total
        self.assertLess(result.base_amount, Decimal('11500.00'))
    
    def test_tax_comparison_view_get(self):
        """Test Tax Comparison view GET request"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:tax_comparison')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('excise_duties', response.context)
        self.assertIn('vat_rates', response.context)
    
    def test_tax_comparison_view_post_valid(self):
        """Test Tax Comparison view POST with valid data"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:tax_comparison')
        data = {
            'base_amount': '10000.00',
            'scenario1_name': 'Current Tax',
            'scenario1_excise_duty_id': str(self.excise_duty.pk),
            'scenario1_vat_rate': '5.00',
            'scenario2_name': 'Alternative Tax',
            'scenario2_excise_duty_id': str(self.excise_duty.pk),
            'scenario2_vat_rate': '12.00'
        }
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('scenarios', response.context)
        
        scenarios = response.context['scenarios']
        self.assertGreaterEqual(len(scenarios), 2)
        
        # Should have difference calculation
        if len(scenarios) >= 3:
            self.assertIn('diff', scenarios[2])
    
    def test_tax_rate_analysis_view(self):
        """Test Tax Rate Analysis view"""
        self.client.login(username='testuser', password='testpass123')
        
        url = reverse('accounts:tax_rate_analysis')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('excise_stats', response.context)
        self.assertIn('vat_stats', response.context)
        self.assertIn('tds_stats', response.context)
        self.assertIn('sample_calculations', response.context)


class TaxationFormsTestCase(TestCase):
    """Test cases for taxation-related forms"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_vat_form_valid_data(self):
        """Test VAT form with valid data"""
        form_data = {
            'vat_percentage': '5.50',
            'description': 'Standard VAT Rate',
            'is_active': True
        }
        form = VATForm(data=form_data)
        
        self.assertTrue(form.is_valid())
        vat = form.save()
        self.assertEqual(vat.vat_percentage, Decimal('5.50'))
        self.assertEqual(vat.description, 'Standard VAT Rate')
        self.assertTrue(vat.is_active)
    
    def test_vat_form_invalid_data(self):
        """Test VAT form with invalid data"""
        # Test negative percentage
        form_data = {
            'vat_percentage': '-5.00',
            'description': 'Invalid VAT Rate'
        }
        form = VATForm(data=form_data)
        
        self.assertFalse(form.is_valid())
        self.assertIn('vat_percentage', form.errors)
        
        # Test percentage too high
        form_data['vat_percentage'] = '150.00'
        form = VATForm(data=form_data)
        
        self.assertFalse(form.is_valid())
        self.assertIn('vat_percentage', form.errors)
    
    def test_excise_duty_form_valid_data(self):
        """Test ExciseDuty form with valid data"""
        form_data = {
            'terms': 'Test Excise Duty',
            'value': '12.00',
            'accessible_value': '100.00',
            'edu_cess': '2.00',
            'she_cess': '1.00',
            'is_default_excise': False,
            'is_default_service_tax': False,
            'is_active': True
        }
        form = ExciseDutyForm(data=form_data)
        
        self.assertTrue(form.is_valid())
        excise = form.save(commit=False)
        excise.created_by = self.user
        excise.save()
        
        self.assertEqual(excise.terms, 'Test Excise Duty')
        self.assertEqual(excise.value, Decimal('12.00'))
        self.assertTrue(excise.is_active)
    
    def test_excise_duty_form_invalid_data(self):
        """Test ExciseDuty form with invalid data"""
        # Test missing required field
        form_data = {
            'value': '12.00'
            # Missing 'terms' field
        }
        form = ExciseDutyForm(data=form_data)
        
        self.assertFalse(form.is_valid())
        self.assertIn('terms', form.errors)
        
        # Test invalid percentage values
        form_data = {
            'terms': 'Test',
            'value': '-10.00',  # Negative value
            'edu_cess': '150.00'  # Too high
        }
        form = ExciseDutyForm(data=form_data)
        
        self.assertFalse(form.is_valid())
        self.assertIn('value', form.errors)
        self.assertIn('edu_cess', form.errors)
    
    def test_tds_code_form_valid_data(self):
        """Test TDSCode form with valid data"""
        form_data = {
            'tds_code': '194A',
            'description': 'Professional Services',
            'tds_percentage': '10.00',
            'threshold_amount': '30000.00',
            'is_active': True
        }
        form = TDSCodeForm(data=form_data)
        
        self.assertTrue(form.is_valid())
        tds = form.save()
        
        self.assertEqual(tds.tds_code, '194A')
        self.assertEqual(tds.description, 'Professional Services')
        self.assertEqual(tds.tds_percentage, Decimal('10.00'))
        self.assertEqual(tds.threshold_amount, Decimal('30000.00'))
    
    def test_tds_code_form_invalid_data(self):
        """Test TDSCode form with invalid data"""
        # Test duplicate TDS code
        TDSCode.objects.create(
            tds_code='194A',
            description='Existing Code',
            tds_percentage=Decimal('10.00')
        )
        
        form_data = {
            'tds_code': '194A',  # Duplicate
            'description': 'Another Code',
            'tds_percentage': '5.00'
        }
        form = TDSCodeForm(data=form_data)
        
        self.assertFalse(form.is_valid())
        self.assertIn('tds_code', form.errors)
    
    def test_octori_form_valid_data(self):
        """Test Octori form with valid data"""
        form_data = {
            'octori_percentage': '1.50',
            'description': 'Local Tax',
            'is_active': True
        }
        form = OctoriForm(data=form_data)
        
        self.assertTrue(form.is_valid())
        octori = form.save()
        
        self.assertEqual(octori.octori_percentage, Decimal('1.50'))
        self.assertEqual(octori.description, 'Local Tax')
        self.assertTrue(octori.is_active)
    
    def test_excise_calculator_form_valid_data(self):
        """Test ExciseCalculator form with valid data"""
        excise_duty = ExciseDuty.objects.create(
            terms='Test Excise',
            value=Decimal('12.00'),
            is_active=True
        )
        
        form_data = {
            'excise_duty': excise_duty.pk,
            'base_amount': '10000.00',
            'calculation_type': 'exclusive'
        }
        form = ExciseCalculatorForm(data=form_data)
        
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['excise_duty'], excise_duty)
        self.assertEqual(form.cleaned_data['base_amount'], Decimal('10000.00'))
        self.assertEqual(form.cleaned_data['calculation_type'], 'exclusive')
    
    def test_excise_calculator_form_invalid_data(self):
        """Test ExciseCalculator form with invalid data"""
        form_data = {
            'excise_duty': '',  # Required field missing
            'base_amount': '-1000.00',  # Negative amount
            'calculation_type': 'invalid'  # Invalid choice
        }
        form = ExciseCalculatorForm(data=form_data)
        
        self.assertFalse(form.is_valid())
        self.assertIn('excise_duty', form.errors)
        self.assertIn('base_amount', form.errors)
        self.assertIn('calculation_type', form.errors)


class TaxationUtilityFunctionsTestCase(TestCase):
    """Test cases for taxation utility functions"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create default excise duty
        self.default_excise = ExciseDuty.objects.create(
            terms='Default Excise',
            value=Decimal('12.00'),
            accessible_value=Decimal('100.00'),
            edu_cess=Decimal('2.00'),
            she_cess=Decimal('1.00'),
            is_default_excise=True,
            is_active=True,
            created_by=self.user
        )
        
        # Create default service tax
        self.default_service_tax = ExciseDuty.objects.create(
            terms='Default Service Tax',
            value=Decimal('14.00'),
            accessible_value=Decimal('100.00'),
            edu_cess=Decimal('2.00'),
            she_cess=Decimal('1.00'),
            is_default_service_tax=True,
            is_active=True,
            created_by=self.user
        )
    
    def test_calculate_invoice_taxes_with_excise_and_vat(self):
        """Test calculate_invoice_taxes utility function"""
        base_amount = Decimal('10000.00')
        
        result = calculate_invoice_taxes(
            base_amount=base_amount,
            excise_duty_id=self.default_excise.id,
            vat_rate=Decimal('5.00'),
            calculation_method=CalculationMethod.EXCLUSIVE
        )
        
        self.assertIsInstance(result, TaxCalculationResult)
        self.assertEqual(result.base_amount, base_amount)
        self.assertGreater(result.total_tax_amount, 0)
        self.assertGreater(result.total_amount, base_amount)
        
        # Should have excise and VAT components
        tax_types = [component.tax_type for component in result.tax_components]
        self.assertIn(TaxType.EXCISE, tax_types)
        self.assertIn(TaxType.VAT, tax_types)
    
    def test_calculate_invoice_taxes_with_tds(self):
        """Test calculate_invoice_taxes utility function with TDS"""
        base_amount = Decimal('50000.00')
        
        result = calculate_invoice_taxes(
            base_amount=base_amount,
            excise_duty_id=self.default_excise.id,
            vat_rate=Decimal('5.00'),
            tds_rate=Decimal('10.00'),
            calculation_method=CalculationMethod.EXCLUSIVE
        )
        
        # Should have TDS component
        tax_types = [component.tax_type for component in result.tax_components]
        self.assertIn(TaxType.TDS, tax_types)
        
        # Final amount should be less than base + other taxes due to TDS deduction
        non_tds_taxes = sum(
            comp.amount for comp in result.tax_components 
            if comp.tax_type != TaxType.TDS
        )
        expected_before_tds = base_amount + non_tds_taxes
        self.assertLess(result.total_amount, expected_before_tds)
    
    def test_calculate_invoice_taxes_invalid_excise_id(self):
        """Test calculate_invoice_taxes with invalid excise ID"""
        base_amount = Decimal('10000.00')
        
        result = calculate_invoice_taxes(
            base_amount=base_amount,
            excise_duty_id=9999,  # Non-existent ID
            vat_rate=Decimal('5.00')
        )
        
        # Should still calculate VAT but skip excise
        tax_types = [component.tax_type for component in result.tax_components]
        self.assertNotIn(TaxType.EXCISE, tax_types)
        self.assertIn(TaxType.VAT, tax_types)
    
    def test_get_default_tax_rates(self):
        """Test get_default_tax_rates utility function"""
        defaults = get_default_tax_rates()
        
        self.assertIn('default_excise', defaults)
        self.assertIn('default_service_tax', defaults)
        
        # Check default excise
        default_excise = defaults['default_excise']
        self.assertEqual(default_excise['id'], self.default_excise.id)
        self.assertEqual(default_excise['rate'], 12.0)
        self.assertEqual(default_excise['edu_cess'], 2.0)
        self.assertEqual(default_excise['she_cess'], 1.0)
        
        # Check default service tax
        default_service_tax = defaults['default_service_tax']
        self.assertEqual(default_service_tax['id'], self.default_service_tax.id)
        self.assertEqual(default_service_tax['rate'], 14.0)


class TaxationEdgeCasesTestCase(TestCase):
    """Test cases for edge cases and error handling in taxation module"""
    
    def setUp(self):
        """Set up test data"""
        self.engine = TaxCalculationEngine()
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_zero_amount_calculations(self):
        """Test tax calculations with zero amounts"""
        excise_duty = ExciseDuty.objects.create(
            terms='Test Excise',
            value=Decimal('12.00'),
            is_active=True
        )
        
        result = self.engine.calculate_excise_duty(
            Decimal('0.00'), 
            excise_duty, 
            CalculationMethod.EXCLUSIVE
        )
        
        self.assertEqual(result.base_amount, Decimal('0.00'))
        self.assertEqual(result.total_tax_amount, Decimal('0.00'))
        self.assertEqual(result.total_amount, Decimal('0.00'))
        self.assertEqual(result.effective_tax_rate, Decimal('0.00'))
    
    def test_very_small_amounts(self):
        """Test tax calculations with very small amounts"""
        excise_duty = ExciseDuty.objects.create(
            terms='Test Excise',
            value=Decimal('12.00'),
            edu_cess=Decimal('2.00'),
            is_active=True
        )
        
        result = self.engine.calculate_excise_duty(
            Decimal('0.01'), 
            excise_duty, 
            CalculationMethod.EXCLUSIVE
        )
        
        # Should handle rounding properly
        self.assertGreaterEqual(result.total_tax_amount, Decimal('0.00'))
        self.assertLessEqual(result.total_tax_amount, Decimal('0.01'))
    
    def test_very_large_amounts(self):
        """Test tax calculations with very large amounts"""
        excise_duty = ExciseDuty.objects.create(
            terms='Test Excise',
            value=Decimal('12.00'),
            edu_cess=Decimal('2.00'),
            she_cess=Decimal('1.00'),
            is_active=True
        )
        
        large_amount = Decimal('*********.99')
        result = self.engine.calculate_excise_duty(
            large_amount, 
            excise_duty, 
            CalculationMethod.EXCLUSIVE
        )
        
        self.assertEqual(result.base_amount, large_amount)
        self.assertGreater(result.total_tax_amount, 0)
        self.assertGreater(result.total_amount, large_amount)
    
    def test_zero_tax_rates(self):
        """Test calculations with zero tax rates"""
        zero_excise = ExciseDuty.objects.create(
            terms='Zero Excise',
            value=Decimal('0.00'),
            edu_cess=Decimal('0.00'),
            she_cess=Decimal('0.00'),
            is_active=True
        )
        
        result = self.engine.calculate_excise_duty(
            Decimal('10000.00'), 
            zero_excise, 
            CalculationMethod.EXCLUSIVE
        )
        
        self.assertEqual(result.total_tax_amount, Decimal('0.00'))
        self.assertEqual(result.total_amount, Decimal('10000.00'))
        self.assertEqual(len(result.tax_components), 0)
    
    def test_hundred_percent_tax_rates(self):
        """Test calculations with 100% tax rates"""
        hundred_percent_vat = Decimal('100.00')
        
        result = self.engine.calculate_vat(
            Decimal('1000.00'), 
            hundred_percent_vat, 
            CalculationMethod.EXCLUSIVE
        )
        
        self.assertEqual(result.total_tax_amount, Decimal('1000.00'))
        self.assertEqual(result.total_amount, Decimal('2000.00'))
        self.assertEqual(result.effective_tax_rate, hundred_percent_vat)
    
    def test_precision_and_rounding(self):
        """Test precision and rounding in calculations"""
        # Create excise with rates that will create rounding scenarios
        excise_duty = ExciseDuty.objects.create(
            terms='Precision Test',
            value=Decimal('12.345'),  # More than 2 decimal places
            edu_cess=Decimal('2.678'),
            she_cess=Decimal('1.234'),
            is_active=True
        )
        
        result = self.engine.calculate_excise_duty(
            Decimal('333.33'), 
            excise_duty, 
            CalculationMethod.EXCLUSIVE
        )
        
        # All amounts should be rounded to 2 decimal places
        for component in result.tax_components:
            # Check that amount has at most 2 decimal places
            self.assertEqual(component.amount, component.amount.quantize(Decimal('0.01')))
        
        self.assertEqual(result.total_tax_amount, result.total_tax_amount.quantize(Decimal('0.01')))
        self.assertEqual(result.total_amount, result.total_amount.quantize(Decimal('0.01')))
    
    def test_invalid_calculation_method(self):
        """Test handling of invalid calculation methods"""
        excise_duty = ExciseDuty.objects.create(
            terms='Test Excise',
            value=Decimal('12.00'),
            is_active=True
        )
        
        # This should default to EXCLUSIVE
        result = self.engine.calculate_excise_duty(
            Decimal('1000.00'), 
            excise_duty, 
            None  # Invalid method
        )
        
        # Should still work with default behavior
        self.assertIsNotNone(result)
        self.assertEqual(result.base_amount, Decimal('1000.00'))
    
    def test_tds_threshold_edge_cases(self):
        """Test TDS threshold edge cases"""
        # Amount exactly at threshold
        result = self.engine.calculate_tds(
            Decimal('30000.00'),  # Exactly at threshold
            Decimal('10.00'),
            Decimal('30000.00')
        )
        
        # Should calculate TDS as amount meets threshold
        self.assertEqual(len(result.tax_components), 1)
        self.assertEqual(result.total_tax_amount, Decimal('3000.00'))
        
        # Amount just below threshold
        result = self.engine.calculate_tds(
            Decimal('29999.99'),  # Just below threshold
            Decimal('10.00'),
            Decimal('30000.00')
        )
        
        # Should not calculate TDS
        self.assertEqual(len(result.tax_components), 0)
        self.assertEqual(result.total_tax_amount, Decimal('0.00'))
    
    def test_accessible_value_edge_cases(self):
        """Test accessible value edge cases"""
        # Zero accessible value
        zero_accessible = ExciseDuty.objects.create(
            terms='Zero Accessible',
            value=Decimal('12.00'),
            accessible_value=Decimal('0.00'),
            is_active=True
        )
        
        result = self.engine.calculate_excise_duty(
            Decimal('10000.00'), 
            zero_accessible, 
            CalculationMethod.EXCLUSIVE
        )
        
        # Should result in zero tax as accessible value is zero
        self.assertEqual(result.total_tax_amount, Decimal('0.00'))
        
        # Fractional accessible value
        fractional_accessible = ExciseDuty.objects.create(
            terms='Fractional Accessible',
            value=Decimal('12.00'),
            accessible_value=Decimal('33.33'),
            is_active=True
        )
        
        result = self.engine.calculate_excise_duty(
            Decimal('10000.00'), 
            fractional_accessible, 
            CalculationMethod.EXCLUSIVE
        )
        
        # Should calculate on 33.33% of base amount
        expected_accessible = Decimal('3333.00')  # 33.33% of 10000
        self.assertAlmostEqual(
            float(result.calculation_details['accessible_amount']), 
            float(expected_accessible), 
            places=1
        )


class TaxationIntegrationTestCase(TestCase):
    """Integration tests for taxation module components working together"""
    
    def setUp(self):
        """Set up comprehensive test data"""
        self.client = Client()
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create comprehensive tax setup
        self.company = Company.objects.create(
            company_name='Integration Test Company',
            company_short_name='ITC'
        )
        
        self.financial_year = FinancialYear.objects.create(
            financial_year='2024-25',
            start_date=date(2024, 4, 1),
            end_date=date(2025, 3, 31),
            is_current=True
        )
        
        # Create various tax types
        self.excise_duty = ExciseDuty.objects.create(
            terms='Integration Test Excise',
            value=Decimal('12.00'),
            accessible_value=Decimal('100.00'),
            edu_cess=Decimal('2.00'),
            she_cess=Decimal('1.00'),
            is_default_excise=True,
            is_active=True,
            created_by=self.user
        )
        
        self.service_tax = ExciseDuty.objects.create(
            terms='Integration Test Service Tax',
            value=Decimal('14.00'),
            accessible_value=Decimal('100.00'),
            edu_cess=Decimal('2.00'),
            she_cess=Decimal('1.00'),
            is_default_service_tax=True,
            is_active=True,
            created_by=self.user
        )
        
        self.vat_5 = VAT.objects.create(
            vat_percentage=Decimal('5.00'),
            description='Standard VAT'
        )
        
        self.vat_12 = VAT.objects.create(
            vat_percentage=Decimal('12.50'),
            description='Higher VAT'
        )
        
        self.tds_professional = TDSCode.objects.create(
            tds_code='194A',
            description='Professional Services',
            tds_percentage=Decimal('10.00'),
            threshold_amount=Decimal('30000.00'),
            is_active=True
        )
        
        self.octori = Octori.objects.create(
            octori_percentage=Decimal('1.50'),
            description='Local Octori Tax',
            is_active=True
        )
    
    def test_end_to_end_invoice_calculation_workflow(self):
        """Test complete invoice calculation workflow"""
        self.client.login(username='testuser', password='testpass123')
        
        # Step 1: Use composite calculator to calculate taxes
        calculator_url = reverse('accounts:composite_tax_calculator')
        calculation_data = {
            'base_amount': '100000.00',
            'calculation_method': 'exclusive',
            'excise_duty_id': str(self.excise_duty.pk),
            'vat_rate': '5.00',
            'tds_rate': '10.00',
            'tds_threshold': '50000.00'
        }
        
        response = self.client.post(calculator_url, calculation_data)
        self.assertEqual(response.status_code, 200)
        self.assertIn('calculation_result', response.context)
        
        calculation_result = response.context['calculation_result']
        
        # Step 2: Verify calculation components
        tax_types = [comp.tax_type for comp in calculation_result.tax_components]
        self.assertIn(TaxType.EXCISE, tax_types)
        self.assertIn(TaxType.CESS, tax_types)  # Education and SHE cess
        self.assertIn(TaxType.VAT, tax_types)
        self.assertIn(TaxType.TDS, tax_types)
        
        # Step 3: Verify amounts are reasonable
        self.assertEqual(calculation_result.base_amount, Decimal('100000.00'))
        self.assertGreater(calculation_result.total_tax_amount, 0)
        
        # Excise should be 12% + cess
        excise_components = [comp for comp in calculation_result.tax_components if comp.tax_type == TaxType.EXCISE]
        self.assertEqual(len(excise_components), 1)
        basic_excise_amount = excise_components[0].amount
        self.assertEqual(basic_excise_amount, Decimal('12000.00'))  # 12% of 100000
        
        # TDS should be deducted from final amount
        tds_components = [comp for comp in calculation_result.tax_components if comp.tax_type == TaxType.TDS]
        self.assertEqual(len(tds_components), 1)
        tds_amount = tds_components[0].amount
        self.assertEqual(tds_amount, Decimal('10000.00'))  # 10% of 100000
    
    def test_vat_register_integration_with_calculations(self):
        """Test VAT register views integration with tax calculations"""
        self.client.login(username='testuser', password='testpass123')
        
        # Setup session
        session = self.client.session
        session['company_id'] = self.company.id
        session['financial_year_id'] = self.financial_year.id
        session.save()
        
        # Create invoice data that matches our tax calculations
        sales_invoice = SalesInvoiceMaster.objects.create(
            invoice_no='INT001',
            invoice_date=date.today(),
            customer_name='Integration Test Customer',
            basic_amount=Decimal('100000.00'),
            vat_amount=Decimal('5610.00'),  # VAT on excise-inclusive amount
            excise_amount=Decimal('12360.00'),  # Excise + cess
            total_amount=Decimal('117970.00'),  # Base + excise + VAT
            status='approved',
            company=self.company,
            financial_year=self.financial_year
        )
        
        # Test VAT register shows our invoice
        vat_register_url = reverse('accounts:sales_vat_register')
        response = self.client.get(vat_register_url, {
            'from_date': date.today().strftime('%Y-%m-%d'),
            'to_date': date.today().strftime('%Y-%m-%d')
        })
        
        self.assertEqual(response.status_code, 200)
        
        # Check that our invoice appears in the register
        register_data = response.context['vat_register_data']
        self.assertEqual(len(register_data), 1)
        self.assertEqual(register_data[0]['invoice'].invoice_no, 'INT001')
        
        # Test AJAX VAT summary
        ajax_url = reverse('accounts:ajax_get_vat_summary')
        response = self.client.get(ajax_url, {
            'from_date': date.today().strftime('%Y-%m-%d'),
            'to_date': date.today().strftime('%Y-%m-%d')
        })
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        
        self.assertTrue(data['success'])
        self.assertEqual(data['sales_vat']['basic_amount'], 100000.0)
        self.assertEqual(data['sales_vat']['vat_amount'], 5610.0)
    
    def test_excise_calculator_integration_with_engine(self):
        """Test excise calculator view integration with calculation engine"""
        self.client.login(username='testuser', password='testpass123')
        
        # Test calculator view
        calculator_url = reverse('accounts:excise_calculator')
        response = self.client.post(calculator_url, {
            'excise_duty': self.excise_duty.pk,
            'base_amount': '50000.00',
            'calculation_type': 'exclusive'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('calculation_result', response.context)
        
        calculation_result = response.context['calculation_result']
        
        # Verify integration with engine
        self.assertEqual(calculation_result['original_amount'], 50000.0)
        self.assertEqual(calculation_result['basic_excise_amount'], 6000.0)  # 12% of 50000
        self.assertEqual(calculation_result['edu_cess_amount'], 120.0)  # 2% of 6000
        self.assertEqual(calculation_result['she_cess_amount'], 60.0)  # 1% of 6000
        self.assertEqual(calculation_result['total_excise'], 6180.0)  # 6000 + 120 + 60
        
        # Test AJAX calculation
        ajax_url = reverse('accounts:calculate_excise_amount')
        response = self.client.get(ajax_url, {
            'duty_id': self.excise_duty.pk,
            'amount': '50000.00',
            'type': 'exclusive'
        })
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        
        self.assertIn('calculation', data)
        calc_data = data['calculation']
        self.assertEqual(calc_data['total_excise'], 6180.0)
    
    def test_reverse_calculation_integration(self):
        """Test reverse calculation integration across views and engine"""
        self.client.login(username='testuser', password='testpass123')
        
        # First, calculate forward to get a target amount
        engine = TaxCalculationEngine()
        tax_config = {
            'excise': {
                'rate': 12.0,
                'accessible_value': 100.0,
                'edu_cess': 2.0,
                'she_cess': 1.0
            },
            'vat': {'rate': 5.0}
        }
        
        forward_result = engine.calculate_composite_tax(
            Decimal('10000.00'), 
            tax_config, 
            CalculationMethod.EXCLUSIVE
        )
        
        target_amount = forward_result.total_amount
        
        # Now test reverse calculation through view
        reverse_url = reverse('accounts:reverse_tax_calculator')
        response = self.client.post(reverse_url, {
            'total_amount': str(target_amount),
            'excise_duty_id': str(self.excise_duty.pk),
            'vat_rate': '5.00'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('calculation_result', response.context)
        
        reverse_result = response.context['calculation_result']
        
        # Base amount should be close to original 10000
        self.assertAlmostEqual(
            float(reverse_result.base_amount), 
            10000.0, 
            places=0  # Within 1 unit due to rounding
        )
        
        # Total should match target
        self.assertAlmostEqual(
            float(reverse_result.total_amount), 
            float(target_amount), 
            places=1
        )
    
    def test_tax_comparison_integration(self):
        """Test tax comparison view integration with multiple scenarios"""
        self.client.login(username='testuser', password='testpass123')
        
        comparison_url = reverse('accounts:tax_comparison')
        response = self.client.post(comparison_url, {
            'base_amount': '100000.00',
            'scenario1_name': 'Current Tax Structure',
            'scenario1_excise_duty_id': str(self.excise_duty.pk),
            'scenario1_vat_rate': '5.00',
            'scenario2_name': 'Higher VAT Structure',
            'scenario2_excise_duty_id': str(self.excise_duty.pk),
            'scenario2_vat_rate': '12.50'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('scenarios', response.context)
        
        scenarios = response.context['scenarios']
        self.assertGreaterEqual(len(scenarios), 2)
        
        # Verify scenario results
        scenario1 = scenarios[0]
        scenario2 = scenarios[1]
        
        self.assertEqual(scenario1['name'], 'Current Tax Structure')
        self.assertEqual(scenario2['name'], 'Higher VAT Structure')
        
        # Scenario 2 should have higher total tax due to higher VAT
        self.assertGreater(scenario2['result'].total_tax_amount, scenario1['result'].total_tax_amount)
        
        # Check difference calculation if available
        if len(scenarios) >= 3 and 'diff' in scenarios[2]:
            diff = scenarios[2]['diff']
            self.assertGreater(diff['tax_amount_diff'], 0)
            self.assertGreater(diff['percentage_change'], 0)
    
    def test_form_validation_integration_with_models(self):
        """Test form validation integration with model constraints"""
        self.client.login(username='testuser', password='testpass123')
        
        # Test creating excise duty with duplicate default flags
        create_url = reverse('accounts:excise_duty_create')
        
        # Try to create another default excise (should handle gracefully)
        response = self.client.post(create_url, {
            'terms': 'Another Default Excise',
            'value': '10.00',
            'accessible_value': '100.00',
            'edu_cess': '2.00',
            'she_cess': '1.00',
            'is_default_excise': True,  # Conflict with existing default
            'is_active': True
        })
        
        # Should either succeed (overriding previous default) or show validation error
        # Implementation depends on business rules
        self.assertIn(response.status_code, [200, 302])
        
        # Test TDS code uniqueness through form
        TDSCode.objects.create(
            tds_code='TEST123',
            description='Existing Code',
            tds_percentage=Decimal('5.00')
        )
        
        # This would be handled by TDS form create view if it existed
        # For now, just test direct model constraint
        with self.assertRaises(Exception):  # Could be IntegrityError or ValidationError
            TDSCode.objects.create(
                tds_code='TEST123',  # Duplicate
                description='Another Code',
                tds_percentage=Decimal('10.00')
            )


class TaxationPerformanceTestCase(TestCase):
    """Performance tests for taxation calculations"""
    
    def setUp(self):
        """Set up test data"""
        self.engine = TaxCalculationEngine()
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create excise duty for performance testing
        self.excise_duty = ExciseDuty.objects.create(
            terms='Performance Test Excise',
            value=Decimal('12.00'),
            accessible_value=Decimal('100.00'),
            edu_cess=Decimal('2.00'),
            she_cess=Decimal('1.00'),
            is_active=True,
            created_by=self.user
        )
    
    def test_bulk_calculation_performance(self):
        """Test performance of bulk tax calculations"""
        import time
        
        amounts = [Decimal(str(i * 1000)) for i in range(1, 101)]  # 100 different amounts
        
        start_time = time.time()
        
        results = []
        for amount in amounts:
            result = self.engine.calculate_excise_duty(
                amount, 
                self.excise_duty, 
                CalculationMethod.EXCLUSIVE
            )
            results.append(result)
        
        end_time = time.time()
        calculation_time = end_time - start_time
        
        # Should complete 100 calculations in reasonable time (less than 1 second)
        self.assertLess(calculation_time, 1.0, "Bulk calculations taking too long")
        
        # Verify all calculations completed
        self.assertEqual(len(results), 100)
        
        # Verify calculations are correct
        for i, result in enumerate(results):
            expected_base = amounts[i]
            self.assertEqual(result.base_amount, expected_base)
            self.assertGreater(result.total_tax_amount, 0)
    
    def test_complex_composite_calculation_performance(self):
        """Test performance of complex composite calculations"""
        import time
        
        tax_config = {
            'excise': {
                'rate': 12.0,
                'accessible_value': 100.0,
                'edu_cess': 2.0,
                'she_cess': 1.0
            },
            'vat': {'rate': 5.0},
            'tds': {'rate': 10.0, 'threshold': 5000.0}
        }
        
        start_time = time.time()
        
        # Perform 50 complex calculations
        results = []
        for i in range(1, 51):
            amount = Decimal(str(i * 10000))
            result = self.engine.calculate_composite_tax(amount, tax_config)
            results.append(result)
        
        end_time = time.time()
        calculation_time = end_time - start_time
        
        # Should complete in reasonable time
        self.assertLess(calculation_time, 2.0, "Complex calculations taking too long")
        
        # Verify all calculations completed correctly
        self.assertEqual(len(results), 50)
        for result in results:
            self.assertGreater(len(result.tax_components), 0)
            self.assertGreater(result.total_tax_amount, 0)
    
    def test_reverse_calculation_performance(self):
        """Test performance of reverse calculations"""
        import time
        
        tax_config = {
            'excise': {'rate': 10.0},
            'vat': {'rate': 5.0}
        }
        
        start_time = time.time()
        
        # Perform 20 reverse calculations (these are more intensive)
        results = []
        for i in range(1, 21):
            total_amount = Decimal(str(i * 11500))  # Amounts with taxes included
            result = self.engine.reverse_calculate_base_amount(total_amount, tax_config)
            results.append(result)
        
        end_time = time.time()
        calculation_time = end_time - start_time
        
        # Reverse calculations are more complex, allow more time
        self.assertLess(calculation_time, 5.0, "Reverse calculations taking too long")
        
        # Verify calculations converged
        self.assertEqual(len(results), 20)
        for result in results:
            self.assertIsNotNone(result.base_amount)
            self.assertGreater(result.base_amount, 0)