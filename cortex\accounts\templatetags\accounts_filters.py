# accounts/templatetags/custom_filters.py
# Custom template filters for Accounts module

from django import template
from decimal import Decimal

register = template.Library()

@register.filter
def mul(value, arg):
    """
    Multiplies the value by the argument.
    Usage: {{ value|mul:arg }}
    """
    try:
        if value is None or arg is None:
            return 0
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def percentage(value, total):
    """
    Calculates percentage of value from total.
    Usage: {{ value|percentage:total }}
    """
    try:
        if not total or total == 0:
            return 0
        return (float(value) / float(total)) * 100
    except (ValueError, TypeError, ZeroDivisionError):
        return 0

@register.filter
def currency(value):
    """
    Formats value as currency with Indian Rupee symbol.
    Usage: {{ value|currency }}
    """
    try:
        if value is None:
            return "₹0.00"
        return f"₹{float(value):,.2f}"
    except (ValueError, TypeError):
        return "₹0.00"

@register.filter
def abs_value(value):
    """
    Returns absolute value.
    Usage: {{ value|abs_value }}
    """
    try:
        return abs(float(value))
    except (ValueError, TypeError):
        return 0