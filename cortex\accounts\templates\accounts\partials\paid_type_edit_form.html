<!-- accounts/partials/paid_type_edit_form.html -->
<!-- HTMX partial for PaidType edit form - SAP S/4HANA inspired -->

{% load static %}

<div class="bg-sap-indigo-50 border border-sap-indigo-200 rounded-lg p-4 mb-4" id="paid-type-edit-form-{{ paid_type.id }}">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-sap-indigo-100 rounded-lg flex items-center justify-center">
                <i data-lucide="edit" class="w-4 h-4 text-sap-indigo-600"></i>
            </div>
            <div>
                <h4 class="text-lg font-medium text-sap-gray-800">Edit Paid Type</h4>
                <p class="text-sm text-sap-gray-600">Update paid type information (ID: {{ paid_type.id }})</p>
            </div>
        </div>
        <button type="button" 
                hx-get="{% url 'accounts:paid_type_list' %}"
                hx-target="#paid-type-table"
                hx-swap="outerHTML"
                class="inline-flex items-center px-3 py-1.5 border border-sap-gray-300 rounded text-xs font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
            <i data-lucide="x" class="w-3 h-3 mr-1"></i>
            Cancel
        </button>
    </div>
    
    <form hx-put="{% url 'accounts:paid_type_edit' paid_type.id %}" 
          hx-target="#paid-type-edit-form-{{ paid_type.id }}" 
          hx-swap="outerHTML"
          hx-trigger="submit"
          class="space-y-4">
        {% csrf_token %}
        
        <div>
            <label for="{{ form.particulars.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                Particulars <span class="text-red-500">*</span>
            </label>
            {{ form.particulars }}
            {% if form.particulars.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.particulars.errors.0 }}</p>
            {% endif %}
        </div>
        
        <div class="flex justify-end space-x-3 pt-4 border-t border-sap-indigo-200">
            <button type="button" 
                    hx-get="{% url 'accounts:paid_type_list' %}"
                    hx-target="#paid-type-table"
                    hx-swap="outerHTML"
                    class="px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                Cancel
            </button>
            <button type="submit" 
                    class="px-4 py-2 bg-sap-indigo-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-sap-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-indigo-500">
                <i data-lucide="save" class="w-4 h-4 mr-2 inline"></i>
                Update Paid Type
            </button>
        </div>
    </form>
</div>