"""
Work Order Forms - Complete implementation matching ASP.NET functionality
Replaces Module/SalesDistribution/Transactions/WorkOrder_New_Details.aspx
"""
from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
import re

from ..models import WorkOrder, WorkOrderCategory, WorkorderSubcategory, Customer, Enquiry
from sys_admin.models import Country, State, City
from human_resource.models import BusinessGroup, OfficeStaff


class WorkOrderCreateForm(forms.ModelForm):
    """
    Comprehensive Work Order Creation Form - Tab 1: Task Execution
    Matches ASP.NET WorkOrder_New_Details.aspx functionality exactly
    """
    
    # Tab 1: Task Execution Fields
    work_order_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'sap-input',
            'type': 'date',
            'required': True
        }),
        label='Date of WO',
        initial=timezone.now().date()
    )
    
    project_title = forms.CharField(
        max_length=400,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Enter Project Title',
            'required': True
        }),
        label='Project Title'
    )
    
    project_leader = forms.CharField(
        max_length=300,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Enter Project Leader Name',
            'required': True
        }),
        label='Project Leader'
    )
    
    category = forms.ModelChoiceField(
        queryset=WorkOrderCategory.objects.none(),
        empty_label="Select Category",
        widget=forms.Select(attrs={
            'class': 'sap-input',
            'required': True,
            'onchange': 'loadSubcategories(this.value)'
        }),
        label='Category'
    )
    
    subcategory = forms.ModelChoiceField(
        queryset=WorkorderSubcategory.objects.none(),
        empty_label="Select Subcategory",
        widget=forms.Select(attrs={
            'class': 'sap-input',
            'required': True
        }),
        label='Subcategory',
        required=False
    )
    
    business_group = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.none(),
        empty_label="Select Business Group",
        widget=forms.Select(attrs={
            'class': 'sap-input',
            'required': True
        }),
        label='Business Group'
    )
    
    buyer = forms.ModelChoiceField(
        queryset=OfficeStaff.objects.none(),
        empty_label="Select Buyer",
        widget=forms.Select(attrs={
            'class': 'sap-input',
            'required': True
        }),
        label='Buyer'
    )
    
    # Target Date Fields (From/To pairs)
    target_dap_from_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'sap-input',
            'type': 'date',
            'required': True
        }),
        label='Target DAP Date From'
    )
    
    target_dap_to_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'sap-input',
            'type': 'date',
            'required': True
        }),
        label='Target DAP Date To'
    )
    
    design_finalization_from_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'sap-input',
            'type': 'date',
            'required': True
        }),
        label='Design Finalization Date From'
    )
    
    design_finalization_to_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'sap-input',
            'type': 'date',
            'required': True
        }),
        label='Design Finalization Date To'
    )
    
    target_manufacturing_from_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'sap-input',
            'type': 'date',
            'required': True
        }),
        label='Target Manufacturing Date From'
    )
    
    target_manufacturing_to_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'sap-input',
            'type': 'date',
            'required': True
        }),
        label='Target Manufacturing Date To'
    )
    
    target_tryout_from_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'sap-input',
            'type': 'date',
            'required': True
        }),
        label='Target Try-out Date From'
    )
    
    target_tryout_to_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'sap-input',
            'type': 'date',
            'required': True
        }),
        label='Target Try-out Date To'
    )
    
    target_despatch_from_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'sap-input',
            'type': 'date',
            'required': True
        }),
        label='Target Despatch Date From'
    )
    
    target_despatch_to_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'sap-input',
            'type': 'date',
            'required': True
        }),
        label='Target Despatch Date To'
    )
    
    target_assembly_from_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'sap-input',
            'type': 'date',
            'required': True
        }),
        label='Target Assembly Date From'
    )
    
    target_assembly_to_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'sap-input',
            'type': 'date',
            'required': True
        }),
        label='Target Assembly Date To'
    )
    
    target_installation_from_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'sap-input',
            'type': 'date',
            'required': True
        }),
        label='Target Installation Date From'
    )
    
    target_installation_to_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'sap-input',
            'type': 'date',
            'required': True
        }),
        label='Target Installation Date To'
    )
    
    customer_inspection_from_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'sap-input',
            'type': 'date',
            'required': True
        }),
        label='Customer Inspection Date From'
    )
    
    customer_inspection_to_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'sap-input',
            'type': 'date',
            'required': True
        }),
        label='Customer Inspection Date To'
    )
    
    # Material Procurement Fields
    manufacturing_material_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'sap-input',
            'type': 'date',
            'required': True
        }),
        label='Manufacturing Material Date'
    )
    
    boughtout_material_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'sap-input',
            'type': 'date',
            'required': True
        }),
        label='Boughtout Material Date'
    )

    class Meta:
        model = WorkOrder
        fields = []  # We'll handle field mapping manually

    def __init__(self, *args, **kwargs):
        company_id = kwargs.pop('company_id', None)
        super().__init__(*args, **kwargs)
        
        if company_id:
            # Populate dropdowns based on company
            self.fields['category'].queryset = WorkOrderCategory.objects.filter(
                compid=company_id
            ).order_by('cname')
            
            # BusinessGroup doesn't have company filtering in this system
            self.fields['business_group'].queryset = BusinessGroup.objects.all().order_by('name')
            
            try:
                self.fields['buyer'].queryset = OfficeStaff.objects.filter(
                    compid=company_id
                ).order_by('employeename')
            except:
                self.fields['buyer'].queryset = OfficeStaff.objects.all().order_by('employeename')

    def clean(self):
        cleaned_data = super().clean()
        
        # Validate date ranges
        date_pairs = [
            ('target_dap_from_date', 'target_dap_to_date', 'Target DAP'),
            ('design_finalization_from_date', 'design_finalization_to_date', 'Design Finalization'),
            ('target_manufacturing_from_date', 'target_manufacturing_to_date', 'Target Manufacturing'),
            ('target_tryout_from_date', 'target_tryout_to_date', 'Target Try-out'),
            ('target_despatch_from_date', 'target_despatch_to_date', 'Target Despatch'),
            ('target_assembly_from_date', 'target_assembly_to_date', 'Target Assembly'),
            ('target_installation_from_date', 'target_installation_to_date', 'Target Installation'),
            ('customer_inspection_from_date', 'customer_inspection_to_date', 'Customer Inspection'),
        ]
        
        for from_field, to_field, field_name in date_pairs:
            from_date = cleaned_data.get(from_field)
            to_date = cleaned_data.get(to_field)
            
            if from_date and to_date and from_date > to_date:
                raise ValidationError(f'{field_name} "From" date cannot be later than "To" date.')
        
        return cleaned_data


class WorkOrderShippingForm(forms.Form):
    """
    Work Order Shipping Form - Tab 2: Shipping
    Matches ASP.NET shipping address functionality
    """
    
    shipping_address = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'sap-input',
            'rows': 4,
            'placeholder': 'Enter complete shipping address',
            'required': True
        }),
        label='Shipping Address'
    )
    
    shipping_country = forms.ModelChoiceField(
        queryset=Country.objects.all(),
        empty_label="Select Country",
        widget=forms.Select(attrs={
            'class': 'sap-input',
            'required': True,
            'onchange': 'loadStates(this.value)'
        }),
        label='Country'
    )
    
    shipping_state = forms.ModelChoiceField(
        queryset=State.objects.none(),
        empty_label="Select State",
        widget=forms.Select(attrs={
            'class': 'sap-input',
            'required': True,
            'onchange': 'loadCities(this.value)'
        }),
        label='State'
    )
    
    shipping_city = forms.ModelChoiceField(
        queryset=City.objects.none(),
        empty_label="Select City",
        widget=forms.Select(attrs={
            'class': 'sap-input',
            'required': True
        }),
        label='City'
    )
    
    # Contact Person 1
    contact_person_1 = forms.CharField(
        max_length=255,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Enter Contact Person 1 Name',
            'required': True
        }),
        label='Contact Person 1'
    )
    
    contact_no_1 = forms.CharField(
        max_length=50,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Enter Contact Number 1',
            'required': True
        }),
        label='Contact No 1'
    )
    
    email_1 = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Enter Email 1',
            'required': True
        }),
        label='Email 1'
    )
    
    # Contact Person 2
    contact_person_2 = forms.CharField(
        max_length=255,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Enter Contact Person 2 Name',
            'required': True
        }),
        label='Contact Person 2'
    )
    
    contact_no_2 = forms.CharField(
        max_length=50,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Enter Contact Number 2',
            'required': True
        }),
        label='Contact No 2'
    )
    
    email_2 = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Enter Email 2',
            'required': True
        }),
        label='Email 2'
    )
    
    # Additional Details
    fax_no = forms.CharField(
        max_length=50,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Enter Fax Number',
            'required': True
        }),
        label='Fax No'
    )
    
    ecc_no = forms.CharField(
        max_length=50,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Enter ECC Number',
            'required': True
        }),
        label='ECC No'
    )
    
    tin_cst_no = forms.CharField(
        max_length=50,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Enter TIN/CST Number',
            'required': True
        }),
        label='TIN/CST No'
    )
    
    tin_vat_no = forms.CharField(
        max_length=50,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Enter TIN/VAT Number',
            'required': True
        }),
        label='TIN/VAT No'
    )

    def clean_contact_no_1(self):
        contact_no = self.cleaned_data.get('contact_no_1')
        if contact_no and not re.match(r'^[0-9+\-\s\(\)]+$', contact_no):
            raise ValidationError('Please enter a valid contact number.')
        return contact_no

    def clean_contact_no_2(self):
        contact_no = self.cleaned_data.get('contact_no_2')
        if contact_no and not re.match(r'^[0-9+\-\s\(\)]+$', contact_no):
            raise ValidationError('Please enter a valid contact number.')
        return contact_no


class WorkOrderProductForm(forms.Form):
    """
    Work Order Product Form - Tab 3: Products
    For adding individual products to work order
    """
    
    item_code = forms.CharField(
        max_length=180,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Enter Item Code',
            'required': True
        }),
        label='Item Code'
    )
    
    description = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'sap-input',
            'rows': 3,
            'placeholder': 'Enter Item Description',
            'required': True
        }),
        label='Description of Item'
    )
    
    quantity = forms.DecimalField(
        max_digits=15,
        decimal_places=3,
        widget=forms.NumberInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Enter Quantity',
            'step': '0.001',
            'min': '0.001',
            'required': True
        }),
        label='Quantity'
    )

    def clean_quantity(self):
        quantity = self.cleaned_data.get('quantity')
        if quantity and quantity <= 0:
            raise ValidationError('Quantity must be greater than zero.')
        return quantity


class WorkOrderInstructionsForm(forms.Form):
    """
    Work Order Instructions Form - Tab 4: Instructions
    Matches ASP.NET instructions and special requirements
    """
    
    primer_painting = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'sap-checkbox'
        }),
        label='Primer Painting to be done'
    )
    
    painting = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'sap-checkbox'
        }),
        label='Painting to be done'
    )
    
    self_certification_report = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'sap-checkbox'
        }),
        label='Self Certification Report to be submitted'
    )
    
    other_instructions = forms.CharField(
        max_length=500,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Enter other instructions',
            'required': True
        }),
        label='Other Instructions'
    )
    
    export_case_mark = forms.CharField(
        max_length=255,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Enter Export Case Mark',
            'required': True
        }),
        label='Export Case Mark'
    )
    
    # Note: File attachment will be handled separately
    attach_annexure = forms.CharField(
        max_length=500,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Attach Annexure (optional)'
        }),
        label='Attach Annexure'
    )


class PurchaseOrderSelectionForm(forms.Form):
    """
    Purchase Order Selection Form - Tab 0: PO Selection
    Matches ASP.NET WorkOrder_New.aspx functionality
    """
    
    SEARCH_CHOICES = [
        ('0', 'Customer Name'),
        ('1', 'Enquiry No'),
        ('2', 'PO No'),
    ]
    
    search_by = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        initial='0',
        widget=forms.Select(attrs={
            'class': 'sap-input',
            'onchange': 'toggleSearchFields()'
        }),
        label='Search By'
    )
    
    search_value = forms.CharField(
        max_length=350,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Search Customer Name',
            'list': 'customerList'
        }),
        label='Search Value'
    )
    
    enquiry_po_value = forms.CharField(
        max_length=150,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Enter Value'
        }),
        label='Enquiry/PO Value'
    )

    def clean(self):
        cleaned_data = super().clean()
        search_by = cleaned_data.get('search_by')
        search_value = cleaned_data.get('search_value')
        enquiry_po_value = cleaned_data.get('enquiry_po_value')
        
        if search_by == '0' and not search_value:
            raise ValidationError('Please enter a customer name to search.')
        elif search_by in ['1', '2'] and not enquiry_po_value:
            field_name = 'Enquiry No' if search_by == '1' else 'PO No'
            raise ValidationError(f'Please enter a {field_name} to search.')
        
        return cleaned_data


class WorkOrderVerbalForm(forms.Form):
    """
    Work Order Verbal Approval Form
    For work orders created based on verbal approval without formal PO
    """
    
    customer = forms.ModelChoiceField(
        queryset=Customer.objects.all(),
        empty_label="Select Customer",
        widget=forms.Select(attrs={
            'class': 'sap-input',
            'required': True
        }),
        label='Customer',
        help_text='Select the customer for this work order'
    )
    
    enquiry = forms.ModelChoiceField(
        queryset=Enquiry.objects.all(),
        empty_label="Select Enquiry (Optional)",
        required=False,
        widget=forms.Select(attrs={
            'class': 'sap-input'
        }),
        label='Related Enquiry',
        help_text='Optional: Link to existing enquiry'
    )
    
    reference_po_number = forms.CharField(
        max_length=150,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Reference PO Number (if available)'
        }),
        label='Reference PO Number',
        help_text='Future PO number or reference (can be updated later)'
    )
    
    approval_by = forms.CharField(
        max_length=200,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Name of person giving verbal approval',
            'required': True
        }),
        label='Verbal Approval By',
        help_text='Name and designation of the person who gave verbal approval'
    )
    
    approval_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'sap-input',
            'type': 'date',
            'required': True
        }),
        label='Approval Date',
        help_text='Date when verbal approval was given'
    )
    
    approval_note = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'sap-input',
            'rows': 4,
            'placeholder': 'Details about the verbal approval, conditions, timeline etc.',
            'required': True
        }),
        label='Approval Details',
        help_text='Record the details of verbal approval, any conditions, expected PO timeline, etc.'
    )

    def __init__(self, *args, **kwargs):
        company_id = kwargs.pop('company_id', None)
        super().__init__(*args, **kwargs)
        
        if company_id:
            # Filter customers and enquiries by company
            self.fields['customer'].queryset = Customer.objects.filter(compid=company_id)
            self.fields['enquiry'].queryset = Enquiry.objects.filter(compid=company_id)

    def clean_approval_date(self):
        approval_date = self.cleaned_data.get('approval_date')
        if approval_date:
            from datetime import date
            if approval_date > date.today():
                raise ValidationError('Approval date cannot be in the future.')
        return approval_date