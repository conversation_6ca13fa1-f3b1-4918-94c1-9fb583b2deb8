<!-- sales_distribution/templates/sales_distribution/partials/wotypes_table.html -->
<!-- WO Types Table Partial - Used by HTMX for dynamic updates -->

<div class="overflow-x-auto">
    <table class="w-full">
        <thead class="bg-sap-gray-50 border-b border-sap-gray-200">
            <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    ID
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    WO Type Name
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    Usage Status
                </th>
                <th class="px-6 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    Actions
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-sap-gray-200">
            {% for category in categories %}
            <tr class="hover:bg-sap-gray-50 transition-colors duration-200" id="row-{{ category.cid }}">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-sap-gray-900">
                    {{ category.cid }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="text-sm font-medium text-sap-gray-900">{{ category.cname }}</div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    {% if category.is_used %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                            In Use
                        </span>
                    {% else %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            <i data-lucide="circle" class="w-3 h-3 mr-1"></i>
                            Available
                        </span>
                    {% endif %}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                    <div class="flex items-center justify-center space-x-2">
                        <!-- Edit Button -->
                        <button type="button" 
                                hx-get="{% url 'sales_distribution:wo_types_edit' category.cid %}"
                                hx-target="#row-{{ category.cid }}"
                                hx-swap="outerHTML"
                                class="inline-flex items-center px-2 py-1 text-xs font-medium text-sap-blue-600 hover:text-sap-blue-800 transition-colors duration-200"
                                title="Edit WO Type">
                            <i data-lucide="edit-3" class="w-4 h-4"></i>
                        </button>
                        
                        <!-- Delete Button (only if not in use) -->
                        {% if not category.is_used %}
                        <button type="button" 
                                hx-delete="{% url 'sales_distribution:wo_types_delete' category.cid %}"
                                hx-target="#row-{{ category.cid }}"
                                hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to delete this WO Type? This action cannot be undone."
                                class="inline-flex items-center px-2 py-1 text-xs font-medium text-red-600 hover:text-red-800 transition-colors duration-200"
                                title="Delete WO Type">
                            <i data-lucide="trash-2" class="w-4 h-4"></i>
                        </button>
                        {% else %}
                        <span class="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-400"
                              title="Cannot delete - WO Type is in use">
                            <i data-lucide="lock" class="w-4 h-4"></i>
                        </span>
                        {% endif %}
                    </div>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="4" class="px-6 py-12 text-center">
                    <div class="flex flex-col items-center">
                        <i data-lucide="clipboard-list" class="w-12 h-12 text-sap-gray-400 mb-4"></i>
                        <h3 class="text-sm font-medium text-sap-gray-900 mb-2">No WO Types Found</h3>
                        <p class="text-sm text-sap-gray-500 mb-4">Get started by creating your first WO type.</p>
                        <button type="button" 
                                onclick="document.getElementById('toggle-add-form').click()"
                                class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-sap-blue-600 border border-transparent rounded-lg hover:bg-sap-blue-700 transition-colors duration-200">
                            <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                            Add First WO Type
                        </button>
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>