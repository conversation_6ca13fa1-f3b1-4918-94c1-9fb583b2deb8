﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="GIN" targetNamespace="http://tempuri.org/GIN.xsd" xmlns:mstns="http://tempuri.org/GIN.xsd" xmlns="http://tempuri.org/GIN.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="GIN" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="GIN" msprop:Generator_DataSetName="GIN">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ItemCode" msprop:Generator_UserColumnName="ItemCode" msprop:Generator_ColumnVarNameInTable="columnItemCode" msprop:Generator_ColumnPropNameInRow="ItemCode" msprop:Generator_ColumnPropNameInTable="ItemCodeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Description" msprop:Generator_UserColumnName="Description" msprop:Generator_ColumnVarNameInTable="columnDescription" msprop:Generator_ColumnPropNameInRow="Description" msprop:Generator_ColumnPropNameInTable="DescriptionColumn" type="xs:string" minOccurs="0" />
              <xs:element name="UOM" msprop:Generator_UserColumnName="UOM" msprop:Generator_ColumnVarNameInTable="columnUOM" msprop:Generator_ColumnPropNameInRow="UOM" msprop:Generator_ColumnPropNameInTable="UOMColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Id" msprop:Generator_UserColumnName="Id" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnPropNameInTable="IdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="poqty" msprop:Generator_UserColumnName="poqty" msprop:Generator_ColumnVarNameInTable="columnpoqty" msprop:Generator_ColumnPropNameInRow="poqty" msprop:Generator_ColumnPropNameInTable="poqtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="RecedQty" msprop:Generator_UserColumnName="RecedQty" msprop:Generator_ColumnVarNameInTable="columnRecedQty" msprop:Generator_ColumnPropNameInRow="RecedQty" msprop:Generator_ColumnPropNameInTable="RecedQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ChallanQty" msprop:Generator_UserColumnName="ChallanQty" msprop:Generator_ColumnVarNameInTable="columnChallanQty" msprop:Generator_ColumnPropNameInRow="ChallanQty" msprop:Generator_ColumnPropNameInTable="ChallanQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="GateEntryNo" msprop:Generator_UserColumnName="GateEntryNo" msprop:Generator_ColumnVarNameInTable="columnGateEntryNo" msprop:Generator_ColumnPropNameInRow="GateEntryNo" msprop:Generator_ColumnPropNameInTable="GateEntryNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="GDate" msprop:Generator_UserColumnName="GDate" msprop:Generator_ColumnVarNameInTable="columnGDate" msprop:Generator_ColumnPropNameInRow="GDate" msprop:Generator_ColumnPropNameInTable="GDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="GTime" msprop:Generator_UserColumnName="GTime" msprop:Generator_ColumnVarNameInTable="columnGTime" msprop:Generator_ColumnPropNameInRow="GTime" msprop:Generator_ColumnPropNameInTable="GTimeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ModeofTransport" msprop:Generator_UserColumnName="ModeofTransport" msprop:Generator_ColumnVarNameInTable="columnModeofTransport" msprop:Generator_ColumnPropNameInRow="ModeofTransport" msprop:Generator_ColumnPropNameInTable="ModeofTransportColumn" type="xs:string" minOccurs="0" />
              <xs:element name="VehicleNo" msprop:Generator_UserColumnName="VehicleNo" msprop:Generator_ColumnVarNameInTable="columnVehicleNo" msprop:Generator_ColumnPropNameInRow="VehicleNo" msprop:Generator_ColumnPropNameInTable="VehicleNoColumn" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>