from django.urls import path
from . import views
from . import api_views
from .views import min_mrn_views, closing_stock_views, mcn_authorization_views

app_name = 'inventory'

urlpatterns = [
    # Task Group 1: Location & Setup Management URLs
    
    # Item Location Management
    path('locations/', views.ItemLocationListView.as_view(), name='location_list'),
    path('locations/new/', views.ItemLocationCreateView.as_view(), name='location_create'),
    path('locations/<int:pk>/', views.ItemLocationDetailView.as_view(), name='location_detail'),
    path('locations/<int:pk>/edit/', views.ItemLocationUpdateView.as_view(), name='location_update'),
    path('locations/<int:pk>/delete/', views.ItemLocationDeleteView.as_view(), name='location_delete'),
    
    # WIS Time Configuration Management
    path('wis-config/', views.WISTimeConfigurationListView.as_view(), name='wis_config_list'),
    path('wis-config/new/', views.WISTimeConfigurationCreateView.as_view(), name='wis_config_create'),
    path('wis-config/<int:pk>/edit/', views.WISTimeConfigurationUpdateView.as_view(), name='wis_config_update'),
    path('wis-config/<int:pk>/delete/', views.WISTimeConfigurationDeleteView.as_view(), name='wis_config_delete'),
    
    # Task Group 2: Material Requisition Management URLs
    
    # Material Requisition Slip (MRS) Management
    path('mrs/', views.MRSListView.as_view(), name='mrs_list'),
    path('mrs-table/', views.MRSTableListView.as_view(), name='mrs_table_list'),
    path('mrs/<int:pk>/', views.MRSDetailView.as_view(), name='mrs_detail'),
    path('mrs/new/', views.MRSCreateView.as_view(), name='mrs_create'),
    path('mrs/<int:pk>/edit/', views.MRSUpdateView.as_view(), name='mrs_update'),
    path('mrs/<int:pk>/delete/', views.MRSDeleteView.as_view(), name='mrs_delete'),
    path('mrs/<int:pk>/approve/', views.MRSApprovalView.as_view(), name='mrs_approve'),
    path('mrs/<int:pk>/submit/', views.mrs_submit_view, name='mrs_submit'),
    path('mrs/<int:pk>/print/', views.mrs_print_view, name='mrs_print'),
    path('mrs/<int:mrs_pk>/items/new/', views.MRSLineItemCreateView.as_view(), name='mrs_line_item_create'),
    
    # Task Group 3: Material Issue & Return Management URLs
    
    # Material Issue Note (MIN) Management
    path('min/', min_mrn_views.MINListView.as_view(), name='min_list'),
    path('min/<int:pk>/', min_mrn_views.MINDetailView.as_view(), name='min_detail'),
    path('min/new/', min_mrn_views.MINCreateView.as_view(), name='min_create'),
    path('min/<int:pk>/edit/', min_mrn_views.MINUpdateView.as_view(), name='min_update'),
    path('min/<int:pk>/delete/', min_mrn_views.MINDeleteView.as_view(), name='min_delete'),
    path('min/<int:pk>/issue/', min_mrn_views.min_issue_view, name='min_issue'),
    path('min/<int:pk>/print/', min_mrn_views.min_print_view, name='min_print'),
    
    # MIN New Views (matching ASP.NET interface)
    path('min-new/', min_mrn_views.MINNewSearchView.as_view(), name='min_new_search'),
    path('min-new/<int:mrs_id>/', min_mrn_views.MINNewDetailsView.as_view(), name='min_new_details'),
    
    # MIN Edit Views (matching ASP.NET interface)
    path('min-edit/', min_mrn_views.MINEditListView.as_view(), name='min_edit_list'),
    
    # Material Return Note (MRN) Management
    path('mrn/', min_mrn_views.MRNListView.as_view(), name='mrn_list'),
    path('mrn/<int:pk>/', min_mrn_views.MRNDetailView.as_view(), name='mrn_detail'),
    path('mrn/new/', min_mrn_views.MRNCreateView.as_view(), name='mrn_create'),
    path('mrn/<int:pk>/edit/', min_mrn_views.MRNUpdateView.as_view(), name='mrn_update'),
    path('mrn/<int:pk>/delete/', min_mrn_views.MRNDeleteView.as_view(), name='mrn_delete'),
    path('mrn/<int:pk>/process/', min_mrn_views.mrn_process_view, name='mrn_process'),
    path('mrn/<int:pk>/print/', min_mrn_views.mrn_print_view, name='mrn_print'),
    
    # MRN Edit Views (matching ASP.NET interface)
    path('transactions/mrn/', min_mrn_views.MRNListView.as_view(), name='mrn_edit_list'),
    path('mrn/<int:pk>/edit-details/', min_mrn_views.MRNEditDetailsView.as_view(), name='mrn_edit_details'),
    
    # Task Group 5: Goods Inward Processing URLs
    
    # Goods Inward Note (GIN) Management
    path('gin/', views.GINListView.as_view(), name='gin_list'),
    path('gin/<int:pk>/', views.GINDetailView.as_view(), name='gin_detail'),
    path('gin/new/', views.GINCreateView.as_view(), name='gin_create'),
    path('gin/<int:pk>/edit/', views.GINEditDetailView.as_view(), name='gin_update'),
    path('gin/<int:pk>/delete/', views.GINDeleteView.as_view(), name='gin_delete'),
    path('gin/<int:pk>/receive/', views.gin_receive_view, name='gin_receive'),
    path('gin/<int:pk>/print/', views.gin_print_view, name='gin_print'),
    
    # GIN New Views (matching ASP.NET interface)
    path('gin-new/', views.GINNewSearchView.as_view(), name='gin_new_search'),
    path('gin-new/<int:po_id>/', views.GINNewPODetailsView.as_view(), name='gin_new_po_details'),
    
    # GIN Edit Views (matching ASP.NET interface)
    path('gin-edit/', views.GINEditListView.as_view(), name='gin_edit_list'),
    path('gin-edit/<int:pk>/', views.GINEditDetailView.as_view(), name='gin_edit_detail'),
    
    # Goods Received Receipt (GRR) Management
    path('grr/', views.GRRListView.as_view(), name='grr_list'),
    path('grr/<int:pk>/', views.GRRDetailView, name='grr_detail'),
    path('grr/new/', views.GRRCreateView.as_view(), name='grr_create'),
    path('grr/<int:pk>/edit/', views.GRRUpdateView.as_view(), name='grr_update'),
    path('grr/<int:pk>/delete/', views.GRRDeleteView.as_view(), name='grr_delete'),
    path('grr/<int:pk>/quality-check/', views.grr_quality_check_view, name='grr_quality_check'),
    path('grr/<int:pk>/approve/', views.grr_approve_view, name='grr_approve'),
    path('grr/<int:pk>/print/', views.grr_print_view, name='grr_print'),
    
    # GRR Edit Views (matching ASP.NET interface)
    path('grr-edit/', views.GRREditListView.as_view(), name='grr_edit_list'),
    path('grr-edit/<int:pk>/', views.GRREditDetailView.as_view(), name='grr_edit_detail'),
    
    # Inward Dashboard
    path('inward-dashboard/', views.inward_dashboard_view, name='inward_dashboard'),
    
    # Task Group 7: Challan Management URLs
    
    # Customer Challan Management
    path('customer-challans/', views.CustomerChallanListView.as_view(), name='customer_challan_list'),
    path('customer-challans/<int:pk>/', views.CustomerChallanDetailView.as_view(), name='customer_challan_detail'),
    path('customer-challans/new/', views.CustomerChallanCreateView.as_view(), name='customer_challan_create'),
    path('customer-challans/<int:pk>/edit/', views.CustomerChallanUpdateView.as_view(), name='customer_challan_update'),
    path('customer-challans/<int:pk>/delete/', views.CustomerChallanDeleteView.as_view(), name='customer_challan_delete'),
    path('customer-challans/<int:pk>/dispatch/', views.customer_challan_dispatch_view, name='customer_challan_dispatch'),
    path('customer-challans/<int:pk>/print/', views.customer_challan_print_view, name='customer_challan_print'),
    
    # Supplier Challan Management
    path('supplier-challans/', views.SupplierChallanListView.as_view(), name='supplier_challan_list'),
    path('supplier-challans/<int:pk>/', views.SupplierChallanDetailView.as_view(), name='supplier_challan_detail'),
    path('supplier-challans/new/', views.SupplierChallanCreateView.as_view(), name='supplier_challan_create'),
    path('supplier-challans/<int:pk>/edit/', views.SupplierChallanUpdateView.as_view(), name='supplier_challan_update'),
    path('supplier-challans/<int:pk>/delete/', views.SupplierChallanDeleteView.as_view(), name='supplier_challan_delete'),
    path('supplier-challans/<int:pk>/receive/', views.supplier_challan_receive_view, name='supplier_challan_receive'),
    path('supplier-challans/<int:pk>/print/', views.supplier_challan_print_view, name='supplier_challan_print'),
    
    # Challan Clearance Management
    path('challan-clearances/', views.ChallanClearanceListView.as_view(), name='challan_clearance_list'),
    path('challan-clearances/<int:pk>/', views.ChallanClearanceDetailView.as_view(), name='challan_clearance_detail'),
    path('challan-clearances/new/', views.ChallanClearanceCreateView.as_view(), name='challan_clearance_create'),
    
    # Task Group 8: Work-in-Progress (WIS) Management URLs
    
    # WIS Dry Run Management (Working functionality)
    path('wis-dry-runs/', views.WISDryRunListView.as_view(), name='wis_dry_run_list'),
    path('wis-dry-runs/new/', views.WISDryRunCreateView.as_view(), name='wis_dry_run_create'),
    path('wis-dry-runs/<int:pk>/', views.WISDryRunDetailView.as_view(), name='wis_dry_run_detail'),
    
    # WIS Assembly BOM Management (Working functionality)
    path('wis/assembly-bom/<str:work_order>/', views.wis_assembly_bom_view, name='wis_assembly_bom'),
    
    # WIS Release Management
    path('wis-release/', views.WISReleaseListView.as_view(), name='wis_release_list'),
    
    # Task Group 10: Inventory Movement Tracking URLs
    
    # Inventory Movement Master Management
    path('movements/', views.InventoryMovementMasterListView.as_view(), name='movement_master_list'),
    path('movements/<int:pk>/', views.InventoryMovementMasterDetailView.as_view(), name='movement_master_detail'),
    path('movements/new/', views.InventoryMovementMasterCreateView.as_view(), name='movement_master_create'),
    path('movements/<int:pk>/edit/', views.InventoryMovementMasterUpdateView.as_view(), name='movement_master_update'),
    path('movements/<int:pk>/delete/', views.InventoryMovementMasterDeleteView.as_view(), name='movement_master_delete'),
    path('movements/<int:pk>/authorize/', views.movement_authorize_view, name='movement_authorize'),
    path('movements/<int:pk>/process/', views.movement_process_view, name='movement_process'),
    path('movements/<int:pk>/complete/', views.movement_complete_view, name='movement_complete'),
    path('movements/<int:pk>/cancel/', views.movement_cancel_view, name='movement_cancel'),
    
    # Stock Ledger Management
    path('stock-ledger/', views.StockLedgerListView.as_view(), name='stock_ledger_list'),
    
    # Alias for navigation compatibility
    path('stock-ledger-main/', views.StockLedgerListView.as_view(), name='stock_ledger'),
    path('stock-ledger/<int:pk>/', views.StockLedgerDetailView.as_view(), name='stock_ledger_detail'),
    path('stock-ledger/new/', views.StockLedgerCreateView.as_view(), name='stock_ledger_create'),
    
    # Inventory Snapshot Management
    path('inventory-snapshots/', views.InventorySnapshotListView.as_view(), name='inventory_snapshot_list'),
    path('inventory-snapshots/<int:pk>/', views.InventorySnapshotDetailView.as_view(), name='inventory_snapshot_detail'),
    path('inventory-snapshots/new/', views.InventorySnapshotCreateView.as_view(), name='inventory_snapshot_create'),
    
    # Movement Tracking Dashboard
    path('movement-tracking-dashboard/', views.movement_tracking_dashboard_view, name='movement_tracking_dashboard'),
    
    # Task Group 4: Material Credit Note Management URLs
    
    # Material Credit Note (MCN) Management
    path('mcn/', views.MCNListView.as_view(), name='mcn_list'),
    path('mcn/<int:pk>/', views.MCNDetailView.as_view(), name='mcn_detail'),
    path('mcn/new/', views.MCNCreateView.as_view(), name='mcn_create'),
    path('mcn/<int:pk>/edit/', views.MCNUpdateView.as_view(), name='mcn_update'),
    path('mcn/<int:pk>/delete/', views.MCNDeleteView.as_view(), name='mcn_delete'),
    path('mcn/<int:pk>/approval/', views.MCNApprovalView.as_view(), name='mcn_approval'),
    path('mcn/<int:pk>/submit/', views.mcn_submit_view, name='mcn_submit'),
    path('mcn/<int:pk>/print/', views.mcn_print_view, name='mcn_print'),
    
    # MCN Authorization Management - replaces ASP.NET AuthorizedMCN functionality
    path('mcn-authorization/', mcn_authorization_views.MCNAuthorizationListView.as_view(), name='mcn_authorization_list'),
    path('mcn-authorization/<int:pk>/', mcn_authorization_views.MCNAuthorizationDetailView.as_view(), name='mcn_authorization_detail'),
    path('mcn-authorization/create/<str:work_order_no>/', mcn_authorization_views.AuthorizedMCNCreateView.as_view(), name='mcn_authorization_create'),
    path('mcn-authorization/stats/', mcn_authorization_views.MCNAuthorizationStatsView.as_view(), name='mcn_authorization_stats'),
    path('mcn-authorization/ajax/', mcn_authorization_views.MCNAuthorizationAjaxView.as_view(), name='mcn_authorization_ajax'),
    
    # Task Group 6: Service Notes Management URLs
    
    # Material Service Note Management
    path('service-notes/', views.ServiceNoteListView.as_view(), name='service_note_list'),
    path('service-notes/<int:pk>/', views.ServiceNoteDetailView.as_view(), name='service_note_detail'),
    path('service-notes/new/', views.ServiceNoteCreateView.as_view(), name='service_note_create'),
    path('service-notes/<int:pk>/edit/', views.ServiceNoteUpdateView.as_view(), name='service_note_update'),
    path('service-notes/<int:pk>/delete/', views.ServiceNoteDeleteView.as_view(), name='service_note_delete'),
    path('service-notes/<int:pk>/submit/', views.service_note_submit_view, name='service_note_submit'),
    path('service-notes/<int:pk>/approve/', views.service_note_approve_view, name='service_note_approve'),
    path('service-notes/<int:pk>/start-work/', views.service_note_start_work_view, name='service_note_start_work'),
    path('service-notes/<int:pk>/complete/', views.service_note_complete_view, name='service_note_complete'),
    path('service-notes/<int:pk>/print/', views.service_note_print_view, name='service_note_print'),
    
    # GSN (Goods Service Note) Management URLs - matching ASP.NET structure
    path('gsn/', views.GSNNewListView.as_view(), name='gsn_new_list'),
    path('gsn/new/<int:gin_id>/<int:supplier_id>/<str:gin_no>/<str:po_no>/<int:fy_id>/', views.GSNNewDetailsView.as_view(), name='gsn_new_details'),
    path('gsn/edit/', views.GSNEditListView.as_view(), name='gsn_edit_list'),
    path('gsn/edit/<int:pk>/', views.GSNEditDetailsView.as_view(), name='gsn_edit_details'),
    path('gsn/delete/', views.GSNDeleteListView.as_view(), name='gsn_delete_list'),
    path('gsn/delete/<int:pk>/', views.GSNDeleteDetailsView.as_view(), name='gsn_delete_details'),
    path('gsn/delete/<int:gsn_id>/item/<int:detail_id>/', views.gsn_delete_detail_item, name='gsn_delete_detail_item'),
    path('gsn/print/', views.GSNPrintListView.as_view(), name='gsn_print_list'),
    path('gsn/print/<int:pk>/', views.gsn_print_view, name='gsn_print'),
    
    # Task Group 9: Stock Analysis & Reporting URLs
    
    # Reports Dashboard
    path('reports/', views.InventoryReportsDashboardView.as_view(), name='reports_dashboard'),
    
    # ABC Analysis
    path('reports/abc-analysis/', views.ABCAnalysisView.as_view(), name='abc_analysis'),
    
    # Moving/Non-moving Analysis
    path('reports/moving-nonmoving-analysis/', views.MovingNonMovingAnalysisView.as_view(), name='moving_nonmoving_analysis'),
    
    # Stock Statement
    path('reports/stock-statement/', views.StockStatementView.as_view(), name='stock_statement'),
    
    # Export Reports
    path('reports/export/', views.export_report_view, name='export_report'),
    
    # Closing Stock Management
    path('closing-stocks/', closing_stock_views.ClosingStockListView.as_view(), name='closing_stock_list'),
    path('closing-stocks/<int:pk>/', closing_stock_views.ClosingStockDetailView.as_view(), name='closing_stock_detail'),
    path('closing-stocks/new/', closing_stock_views.ClosingStockCreateView.as_view(), name='closing_stock_create'),
    path('closing-stocks/<int:pk>/edit/', closing_stock_views.ClosingStockUpdateView.as_view(), name='closing_stock_update'),
    path('closing-stocks/<int:pk>/delete/', closing_stock_views.ClosingStockDeleteView.as_view(), name='closing_stock_delete'),
    
    # Task Group 11: Universal Search URLs
    path('search/', views.UniversalSearchView.as_view(), name='universal_search'),
    
    # API endpoints
    path('api/mrs-statistics/', views.mrs_statistics_api, name='mrs_statistics_api'),
    path('api/min-mrn-statistics/', min_mrn_views.min_mrn_statistics_api, name='min_mrn_statistics_api'),
    path('api/gin-grr-statistics/', views.gin_grr_statistics_api, name='gin_grr_statistics_api'),
    path('api/challan-statistics/', views.challan_statistics_api, name='challan_statistics_api'),
    # path('api/wis-statistics/', views.wis_statistics_api, name='wis_statistics_api'),
    path('api/movement-tracking-statistics/', views.movement_tracking_statistics_api, name='movement_tracking_statistics_api'),
    path('api/generate-inventory-snapshot/', views.generate_inventory_snapshot_api, name='generate_inventory_snapshot_api'),
    path('api/mcn-statistics/', views.mcn_statistics_api, name='mcn_statistics_api'),
    path('api/mcn-line-item-search/', views.mcn_line_item_search_api, name='mcn_line_item_search_api'),
    path('api/service-note-statistics/', views.service_note_statistics_api, name='service_note_statistics_api'),
    path('api/service-item-search/', views.service_item_search_api, name='service_item_search_api'),
    path('api/inventory-analytics/', views.inventory_analytics_api, name='inventory_analytics_api'),
    path('api/search-suggestions/', views.search_suggestions_api, name='search_suggestions_api'),
    path('api/quick-search/', views.quick_search_api, name='quick_search_api'),
    path('api/save-search/', views.save_search_api, name='save_search_api'),
    path('api/closing-stock-statistics/', closing_stock_views.closing_stock_statistics_api, name='closing_stock_statistics_api'),
    path('api/closing-stock-quick-add/', closing_stock_views.closing_stock_quick_add_api, name='closing_stock_quick_add_api'),
    
    # New API endpoints for HTMX integration
    path('api/items/search/', api_views.item_search_api, name='item_search_api'),
    path('api/item-details/', api_views.item_details_api, name='item_details_api'),
    path('api/categories/', api_views.categories_api, name='categories_api'),
    path('api/locations/', api_views.locations_api, name='locations_api'),
    path('api/stock-availability/', api_views.stock_availability_api, name='stock_availability_api'),
    path('api/suppliers/', views.suppliers_api, name='suppliers_api'),
    path('api/supplier-details/<str:supplier_id>/', views.supplier_details_api, name='supplier_details_api'),
    path('api/purchase-orders/', views.purchase_orders_api, name='purchase_orders_api'),
    path('api/validate-mrs/', api_views.validate_mrs_submission, name='validate_mrs_submission'),
    path('api/mrs-temp-items/add/', api_views.add_mrs_temp_item, name='add_mrs_temp_item'),
    path('api/mrs-temp-items/', api_views.get_mrs_temp_items, name='get_mrs_temp_items'),
    path('api/mrs-temp-items/remove/', api_views.remove_mrs_temp_item, name='remove_mrs_temp_item'),
    path('api/gin-search/', views.gin_search_api, name='gin_search_api'),
    path('api/eligible-pos/', views.eligible_pos_api, name='eligible_pos_api'),
    path('api/item-autocomplete/', api_views.ItemAutocompleteAPI.as_view(), name='item_autocomplete_api'),
]