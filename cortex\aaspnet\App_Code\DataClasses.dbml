﻿<?xml version="1.0" encoding="utf-8"?>
<Database Name="D:\INETPUB\WWWROOT\NEWERP\APP_DATA\ERP_DB.MDF" Class="DataClassesDataContext" xmlns="http://schemas.microsoft.com/linqtosql/dbml/2007">
  <Connection Mode="WebSettings" ConnectionString="Data Source=server;Initial Catalog=D:\INETPUB\WWWROOT\NEWERP\APP_DATA\ERP_DB.MDF;Integrated Security=True" SettingsObjectName="System.Configuration.ConfigurationManager.ConnectionStrings" SettingsPropertyName="D:\INETPUB\WWWROOT\NEWERP\APP_DATA\ERP_DB.MDFConnectionString" Provider="System.Data.SqlClient" />
  <Table Name="dbo.tblMM_Rate_Register" Member="tblMM_Rate_Registers">
    <Type Name="tblMM_Rate_Register">
      <Column Name="Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="SysDate" Type="System.String" DbType="VarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="SysTime" Type="System.String" DbType="VarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="CompId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="FinYearId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="SessionId" Type="System.String" DbType="VarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="AmendmentNo" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="POId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PRId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SPRId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PONo" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Rate" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="Discount" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="PF" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ExST" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="VAT" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="IndirectCost" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="DirectCost" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="Old_ItemCode" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
</Database>