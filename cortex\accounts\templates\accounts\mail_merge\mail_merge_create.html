<!-- accounts/templates/accounts/mail_merge/mail_merge_create.html -->
<!-- Enhanced Mail Merge Creation Form with HTMX -->
<!-- Task Group 11: Financial Reporting & Analysis - Mail Merge Functionality (Task 11.4) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Mail Merge - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-purple-600 to-sap-purple-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="mail-merge" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Mail Merge</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Generate batch documents and communications with personalized data</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:mail_merge_template_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="template" class="w-4 h-4 inline mr-2"></i>
                    Manage Templates
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6" x-data="mailMergeManager()">
    
    <!-- Mail Merge Guidelines -->
    <div class="mb-6 bg-sap-purple-50 border border-sap-purple-200 rounded-lg p-4">
        <div class="flex">
            <i data-lucide="info" class="w-5 h-5 text-sap-purple-400 mr-3"></i>
            <div class="text-sm text-sap-purple-800">
                <p class="font-medium mb-1">Mail Merge Process</p>
                <p>Select a template, choose your data source, customize the content, and generate personalized documents for multiple recipients.</p>
            </div>
        </div>
    </div>

    <form method="post" class="space-y-8" @submit="submitMailMerge">
        {% csrf_token %}
        
        <!-- Error Messages -->
        {% if form.non_field_errors %}
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <i data-lucide="alert-circle" class="w-5 h-5 text-red-400 mr-3"></i>
                <div>
                    <h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                    <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                        {% for error in form.non_field_errors %}
                        <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
            
            <!-- Mail Merge Configuration -->
            <div class="xl:col-span-2 space-y-6">
                
                <!-- Template Selection -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                            <i data-lucide="template" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                            Template Selection
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Template Type -->
                            <div>
                                <label for="template_type" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    Template Type
                                    <span class="text-red-500">*</span>
                                </label>
                                <select x-model="templateType" @change="loadTemplatePreview" 
                                        class="block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500">
                                    <option value="">Select Template Type</option>
                                    <option value="invoice">Sales Invoice</option>
                                    <option value="payment_reminder">Payment Reminder</option>
                                    <option value="customer_statement">Customer Statement</option>
                                    <option value="supplier_payment">Supplier Payment Notice</option>
                                    <option value="credit_note">Credit Note</option>
                                    <option value="debit_note">Debit Note</option>
                                    <option value="welcome_letter">Welcome Letter</option>
                                    <option value="account_summary">Account Summary Report</option>
                                </select>
                            </div>

                            <!-- Output Format -->
                            <div>
                                <label for="output_format" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    Output Format
                                    <span class="text-red-500">*</span>
                                </label>
                                <select x-model="outputFormat"
                                        class="block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500">
                                    <option value="pdf">PDF Documents</option>
                                    <option value="email">Email Messages</option>
                                    <option value="word">Word Documents</option>
                                    <option value="html">HTML Files</option>
                                </select>
                            </div>

                            <!-- Custom Subject (for emails) -->
                            <div x-show="outputFormat === 'email'" class="md:col-span-2">
                                <label for="email_subject" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    Email Subject
                                </label>
                                <input type="text" x-model="emailSubject"
                                       class="block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500"
                                       placeholder="Enter email subject with variables like {customer_name}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Data Source Selection -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                            <i data-lucide="database" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                            Data Source
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Data Source Type -->
                            <div>
                                <label for="data_source" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                    Data Source
                                    <span class="text-red-500">*</span>
                                </label>
                                <select x-model="dataSource" @change="loadDataSourceRecords"
                                        class="block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500">
                                    <option value="">Select Data Source</option>
                                    <option value="customers">Customers</option>
                                    <option value="suppliers">Suppliers</option>
                                    <option value="invoices">Sales Invoices</option>
                                    <option value="credit_notes">Credit Notes</option>
                                    <option value="debit_notes">Debit Notes</option>
                                    <option value="outstanding_invoices">Outstanding Invoices</option>
                                </select>
                            </div>

                            <!-- Filter Criteria -->
                            <div>
                                <label class="block text-sm font-medium text-sap-gray-700 mb-2">Filter Criteria</label>
                                <div class="space-y-2">
                                    <div class="flex items-center space-x-2">
                                        <input type="checkbox" x-model="filters.activeOnly" class="rounded border-sap-gray-300">
                                        <label class="text-sm text-sap-gray-700">Active records only</label>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <input type="checkbox" x-model="filters.currentMonth" class="rounded border-sap-gray-300">
                                        <label class="text-sm text-sap-gray-700">Current month data</label>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <input type="checkbox" x-model="filters.outstandingOnly" class="rounded border-sap-gray-300">
                                        <label class="text-sm text-sap-gray-700">Outstanding balances only</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Record Selection -->
                <div x-show="dataSource" class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                                <i data-lucide="list-checks" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                                Select Records
                            </h3>
                            <div class="flex items-center space-x-2">
                                <button type="button" @click="selectAllRecords"
                                        class="text-sm text-sap-blue-600 hover:text-sap-blue-700 font-medium">
                                    Select All
                                </button>
                                <span class="text-sap-gray-300">|</span>
                                <button type="button" @click="clearSelection"
                                        class="text-sm text-sap-red-600 hover:text-sap-red-700 font-medium">
                                    Clear Selection
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <!-- Loading State -->
                        <div x-show="loadingRecords" class="text-center py-8">
                            <div class="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm text-sap-purple-600">
                                <div class="animate-spin -ml-1 mr-3 h-5 w-5 text-sap-purple-600">
                                    <i data-lucide="loader-2" class="w-5 h-5"></i>
                                </div>
                                Loading records...
                            </div>
                        </div>
                        
                        <!-- Records Grid -->
                        <div x-show="!loadingRecords && availableRecords.length > 0" 
                             hx-get="{% url 'accounts:mail_merge_data_source' %}"
                             hx-trigger="load"
                             hx-vals="js:{data_source: this.dataSource, filters: JSON.stringify(this.filters)}"
                             hx-target="#records-container"
                             id="records-container">
                            <!-- Records will be loaded via HTMX -->
                        </div>
                        
                        <!-- Empty State -->
                        <div x-show="!loadingRecords && availableRecords.length === 0" class="text-center py-8">
                            <i data-lucide="inbox" class="w-12 h-12 text-sap-gray-400 mx-auto mb-4"></i>
                            <h3 class="text-sm font-medium text-sap-gray-900 mb-2">No records found</h3>
                            <p class="text-sm text-sap-gray-600">Try adjusting your filter criteria or select a different data source.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mail Merge Preview and Summary -->
            <div class="space-y-6">
                
                <!-- Template Preview -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                            <i data-lucide="eye" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                            Template Preview
                        </h3>
                    </div>
                    <div class="p-6">
                        <div x-show="templateType" class="bg-sap-gray-50 rounded-lg p-4 border">
                            <div class="text-xs font-medium text-sap-gray-700 mb-2">Template Variables Available:</div>
                            <div class="text-sm text-sap-gray-600 space-y-1">
                                <div x-show="templateType === 'invoice'">• {customer_name}, {invoice_no}, {amount}, {due_date}</div>
                                <div x-show="templateType === 'payment_reminder'">• {customer_name}, {outstanding_amount}, {days_overdue}</div>
                                <div x-show="templateType === 'customer_statement'">• {customer_name}, {statement_period}, {balance}</div>
                                <div x-show="templateType === 'supplier_payment'">• {supplier_name}, {payment_amount}, {payment_date}</div>
                                <div x-show="templateType === 'credit_note'">• {customer_name}, {credit_amount}, {reason}</div>
                                <div x-show="templateType === 'debit_note'">• {customer_name}, {debit_amount}, {reason}</div>
                            </div>
                        </div>
                        
                        <div x-show="!templateType" class="text-center py-4 text-sm text-sap-gray-500">
                            Select a template type to see preview
                        </div>
                    </div>
                </div>

                <!-- Merge Summary -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                            <i data-lucide="bar-chart" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                            Merge Summary
                        </h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <!-- Selected Records Count -->
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-sap-gray-600">Selected Records:</span>
                            <span class="font-medium" x-text="selectedRecords.length">0</span>
                        </div>
                        
                        <!-- Output Format -->
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-sap-gray-600">Output Format:</span>
                            <span class="font-medium capitalize" x-text="outputFormat || 'Not selected'"></span>
                        </div>
                        
                        <!-- Estimated Size -->
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-sap-gray-600">Estimated Size:</span>
                            <span class="font-medium" x-text="getEstimatedSize()">-</span>
                        </div>
                        
                        <!-- Processing Time -->
                        <div class="border-t border-sap-gray-200 pt-4">
                            <div class="bg-sap-blue-50 rounded-lg p-3">
                                <div class="text-xs font-medium text-sap-blue-800 mb-1">Processing Time</div>
                                <div class="text-sm text-sap-blue-700" x-text="getEstimatedTime()">Estimated: 2-5 minutes</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                    <div class="px-6 py-4 border-b border-sap-gray-100">
                        <h3 class="text-lg font-medium text-sap-gray-800">Quick Actions</h3>
                    </div>
                    <div class="p-6 space-y-3">
                        <button type="button" @click="previewFirstRecord"
                                :disabled="selectedRecords.length === 0"
                                class="w-full bg-sap-blue-100 hover:bg-sap-blue-200 text-sap-blue-800 px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                            <i data-lucide="eye" class="w-4 h-4 inline mr-2"></i>
                            Preview Sample
                        </button>
                        <button type="button" @click="saveAsTemplate"
                                :disabled="!templateType"
                                class="w-full bg-sap-green-100 hover:bg-sap-green-200 text-sap-green-800 px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                            <i data-lucide="save" class="w-4 h-4 inline mr-2"></i>
                            Save as Template
                        </button>
                        <button type="button" @click="scheduleMailMerge"
                                :disabled="selectedRecords.length === 0"
                                class="w-full bg-sap-yellow-100 hover:bg-sap-yellow-200 text-sap-yellow-800 px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                            <i data-lucide="clock" class="w-4 h-4 inline mr-2"></i>
                            Schedule for Later
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
            <div class="flex items-center space-x-4">
                <a href="{% url 'accounts:dashboard' %}" 
                   class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-6 py-2 rounded-lg font-medium transition-colors">
                    Cancel
                </a>
                <button type="button" @click="saveDraft"
                        class="bg-sap-yellow-600 hover:bg-sap-yellow-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="save" class="w-4 h-4 inline mr-2"></i>
                    Save Draft
                </button>
            </div>
            
            <div class="flex items-center space-x-3">
                <button type="button" @click="testMailMerge"
                        :disabled="selectedRecords.length === 0"
                        class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                    <i data-lucide="test-tube" class="w-4 h-4 inline mr-2"></i>
                    Test Run
                </button>
                <button type="submit" 
                        class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                        :disabled="selectedRecords.length === 0 || !templateType">
                    <i data-lucide="mail-send" class="w-4 h-4 inline mr-2"></i>
                    Start Mail Merge
                </button>
            </div>
        </div>

        <!-- Hidden Fields for Mail Merge Data -->
        <input type="hidden" name="template_type" :value="templateType">
        <input type="hidden" name="data_source" :value="dataSource">
        <input type="hidden" name="output_format" :value="outputFormat">
        <input type="hidden" name="selected_records" :value="JSON.stringify(selectedRecords)">
        <input type="hidden" name="email_subject" :value="emailSubject">
        <input type="hidden" name="filters" :value="JSON.stringify(filters)">
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
function mailMergeManager() {
    return {
        templateType: '',
        dataSource: '',
        outputFormat: 'pdf',
        emailSubject: '',
        
        filters: {
            activeOnly: true,
            currentMonth: false,
            outstandingOnly: false
        },
        
        availableRecords: [],
        selectedRecords: [],
        loadingRecords: false,
        
        init() {
            lucide.createIcons();
        },
        
        loadTemplatePreview() {
            console.log('Loading template preview for:', this.templateType);
            // Implementation would load template preview
        },
        
        loadDataSourceRecords() {
            if (!this.dataSource) return;
            
            this.loadingRecords = true;
            this.availableRecords = [];
            this.selectedRecords = [];
            
            // Simulate loading
            setTimeout(() => {
                this.loadingRecords = false;
                // This would be replaced with actual HTMX call
            }, 1000);
        },
        
        selectAllRecords() {
            this.selectedRecords = [...this.availableRecords];
        },
        
        clearSelection() {
            this.selectedRecords = [];
        },
        
        getEstimatedSize() {
            if (this.selectedRecords.length === 0) return '-';
            const sizePerRecord = this.outputFormat === 'pdf' ? 0.5 : 0.1; // MB
            const totalSize = (this.selectedRecords.length * sizePerRecord).toFixed(1);
            return `${totalSize} MB`;
        },
        
        getEstimatedTime() {
            if (this.selectedRecords.length === 0) return 'Select records to estimate';
            const timePerRecord = this.outputFormat === 'pdf' ? 2 : 1; // seconds
            const totalSeconds = this.selectedRecords.length * timePerRecord;
            const minutes = Math.ceil(totalSeconds / 60);
            return `Estimated: ${minutes} minute${minutes > 1 ? 's' : ''}`;
        },
        
        previewFirstRecord() {
            if (this.selectedRecords.length === 0) {
                alert('Please select at least one record to preview.');
                return;
            }
            console.log('Previewing first record...');
            // Implementation for preview modal
        },
        
        saveAsTemplate() {
            console.log('Saving as template...');
            // Implementation for saving template
        },
        
        scheduleMailMerge() {
            console.log('Scheduling mail merge...');
            // Implementation for scheduling
        },
        
        saveDraft() {
            console.log('Saving draft...');
            // Implementation for saving draft
        },
        
        testMailMerge() {
            if (this.selectedRecords.length === 0) {
                alert('Please select at least one record for testing.');
                return;
            }
            console.log('Running test mail merge...');
            // Implementation for test run with first 3 records
        },
        
        submitMailMerge(event) {
            if (this.selectedRecords.length === 0) {
                event.preventDefault();
                alert('Please select at least one record for mail merge.');
                return false;
            }
            
            if (!this.templateType) {
                event.preventDefault();
                alert('Please select a template type.');
                return false;
            }
            
            if (this.outputFormat === 'email' && !this.emailSubject) {
                event.preventDefault();
                alert('Please enter an email subject.');
                return false;
            }
            
            return confirm(`Start mail merge for ${this.selectedRecords.length} records?`);
        }
    }
}

// Initialize lucide icons on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}