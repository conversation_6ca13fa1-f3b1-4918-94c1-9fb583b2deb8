<!-- accounts/templates/accounts/transactions/bank_voucher_list.html -->
<!-- Bank Voucher List View Template -->
<!-- Task Group 2: Banking & Cash Management - Bank Voucher List (Task 2.7) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Bank Vouchers - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-blue-600 to-sap-blue-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="building-2" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Bank Vouchers</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage bank receipts, payments, and transfers</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to Dashboard
                </a>
                <a href="{% url 'accounts:bank_voucher_create' %}" 
                   class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    New Bank Voucher
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Search and Filter Section -->
    <div class="mb-6 bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4">
            <form method="get" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4">
                <!-- Search -->
                <div class="flex-1">
                    <label for="search" class="block text-sm font-medium text-sap-gray-700 mb-1">Search</label>
                    <input type="text" name="search" value="{{ request.GET.search }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"
                           placeholder="Search by voucher number, cheque number, description...">
                </div>
                
                <!-- Bank Filter -->
                <div>
                    <label for="bank" class="block text-sm font-medium text-sap-gray-700 mb-1">Bank</label>
                    <select name="bank" 
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        <option value="">All Banks</option>
                        {% for bank in banks %}
                        <option value="{{ bank.id }}" {% if request.GET.bank == bank.id|stringformat:"s" %}selected{% endif %}>
                            {{ bank.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Voucher Type Filter -->
                <div>
                    <label for="voucher_type" class="block text-sm font-medium text-sap-gray-700 mb-1">Type</label>
                    <select name="voucher_type" 
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        <option value="">All Types</option>
                        <option value="receipt" {% if request.GET.voucher_type == 'receipt' %}selected{% endif %}>Receipt</option>
                        <option value="payment" {% if request.GET.voucher_type == 'payment' %}selected{% endif %}>Payment</option>
                        <option value="transfer" {% if request.GET.voucher_type == 'transfer' %}selected{% endif %}>Transfer</option>
                    </select>
                </div>
                
                <!-- Date Range -->
                <div>
                    <label for="date_from" class="block text-sm font-medium text-sap-gray-700 mb-1">From Date</label>
                    <input type="date" name="date_from" value="{{ request.GET.date_from }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                </div>
                
                <div>
                    <label for="date_to" class="block text-sm font-medium text-sap-gray-700 mb-1">To Date</label>
                    <input type="date" name="date_to" value="{{ request.GET.date_to }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                </div>
                
                <!-- Search Button -->
                <div>
                    <button type="submit" 
                            class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="search" class="w-4 h-4 inline mr-2"></i>
                        Search
                    </button>
                </div>
                
                <!-- Clear Button -->
                {% if request.GET %}
                <div>
                    <a href="{% url 'accounts:bank_voucher_list' %}" 
                       class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                        Clear
                    </a>
                </div>
                {% endif %}
            </form>
        </div>
    </div>

    <!-- Bank Voucher Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="arrow-down-circle" class="w-6 h-6 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Receipts</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ receipt_count|default:0 }}</p>
                    <p class="text-xs text-sap-green-600 mt-1">₹{{ receipt_amount|default:0|floatformat:2 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-red-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="arrow-up-circle" class="w-6 h-6 text-sap-red-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Payments</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ payment_count|default:0 }}</p>
                    <p class="text-xs text-sap-red-600 mt-1">₹{{ payment_amount|default:0|floatformat:2 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="repeat" class="w-6 h-6 text-sap-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Transfers</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ transfer_count|default:0 }}</p>
                    <p class="text-xs text-sap-purple-600 mt-1">₹{{ transfer_amount|default:0|floatformat:2 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="building-2" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Net Bank Flow</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ net_amount|default:0|floatformat:2 }}</p>
                    <p class="text-xs text-sap-blue-600 mt-1">Today</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bank Vouchers Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Bank Vouchers</h3>
                <div class="flex items-center space-x-2">
                    <button type="button" onclick="exportVouchers()" 
                            class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                        Export
                    </button>
                    <button type="button" onclick="reconcileBank()" 
                            class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="check-square" class="w-4 h-4 inline mr-2"></i>
                        Reconcile
                    </button>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Voucher #
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Date
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Bank
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Type
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Cheque #
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Amount
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for voucher in bank_vouchers %}
                    <tr class="hover:bg-sap-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-sap-gray-900">
                                {{ voucher.voucher_no }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-sap-gray-900">{{ voucher.voucher_date|date:"d M Y" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-sap-gray-900">{{ voucher.bank.name|default:"-" }}</div>
                            <div class="text-sm text-sap-gray-500">{{ voucher.bank.account_number|default:"-" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if voucher.voucher_type == 'receipt' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-green-100 text-sap-green-800">
                                <i data-lucide="arrow-down-circle" class="w-3 h-3 mr-1"></i>
                                Receipt
                            </span>
                            {% elif voucher.voucher_type == 'payment' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-red-100 text-sap-red-800">
                                <i data-lucide="arrow-up-circle" class="w-3 h-3 mr-1"></i>
                                Payment
                            </span>
                            {% else %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-purple-100 text-sap-purple-800">
                                <i data-lucide="repeat" class="w-3 h-3 mr-1"></i>
                                Transfer
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-sap-gray-900">{{ voucher.cheque_no|default:"-" }}</div>
                            {% if voucher.cheque_date %}
                            <div class="text-sm text-sap-gray-500">{{ voucher.cheque_date|date:"d M Y" }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-sap-gray-900">₹{{ voucher.amount|floatformat:2 }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if voucher.is_reconciled %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-green-100 text-sap-green-800">
                                Reconciled
                            </span>
                            {% elif voucher.is_cleared %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-blue-100 text-sap-blue-800">
                                Cleared
                            </span>
                            {% else %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-yellow-100 text-sap-yellow-800">
                                Pending
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <a href="{% url 'accounts:bank_voucher_edit' voucher.pk %}" 
                                   class="text-sap-blue-600 hover:text-sap-blue-900" title="Edit">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </a>
                                <button type="button" onclick="printVoucher({{ voucher.id }})" 
                                        class="text-sap-green-600 hover:text-sap-green-900" title="Print">
                                    <i data-lucide="printer" class="w-4 h-4"></i>
                                </button>
                                {% if not voucher.is_reconciled %}
                                <button type="button" onclick="markCleared({{ voucher.id }})" 
                                        class="text-sap-orange-600 hover:text-sap-orange-900" title="Mark Cleared">
                                    <i data-lucide="check" class="w-4 h-4"></i>
                                </button>
                                {% endif %}
                                <button type="button" onclick="deleteVoucher({{ voucher.id }})" 
                                        class="text-sap-red-600 hover:text-sap-red-900" title="Delete">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="px-6 py-8 text-center">
                            <div class="text-center">
                                <i data-lucide="building-2" class="w-12 h-12 text-sap-gray-400 mx-auto mb-4"></i>
                                <h3 class="text-sm font-medium text-sap-gray-900 mb-2">No bank vouchers found</h3>
                                <p class="text-sm text-sap-gray-600 mb-4">Get started by creating your first bank voucher.</p>
                                <a href="{% url 'accounts:bank_voucher_create' %}" 
                                   class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                                    Create Bank Voucher
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="px-6 py-4 border-t border-sap-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-sap-gray-700">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} entries
                </div>
                <div class="flex space-x-1">
                    {% if page_obj.has_previous %}
                    <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ page_obj.previous_page_number }}" 
                       class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-l-lg hover:bg-sap-gray-50">
                        Previous
                    </a>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <span class="px-3 py-2 text-sm font-medium text-sap-blue-600 bg-sap-blue-50 border border-sap-blue-300">
                            {{ num }}
                        </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ num }}" 
                           class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 hover:bg-sap-gray-50">
                            {{ num }}
                        </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ page_obj.next_page_number }}" 
                       class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-r-lg hover:bg-sap-gray-50">
                        Next
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function exportVouchers() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = '?' + params.toString();
}

function reconcileBank() {
    window.location.href = '{% url "accounts:bank_reconciliation_list" %}';
}

function printVoucher(id) {
    window.open(`/accounts/transactions/bank-vouchers/${id}/print/`, '_blank');
}

function markCleared(id) {
    if (confirm('Mark this voucher as cleared by the bank?')) {
        fetch(`/accounts/transactions/bank-vouchers/${id}/mark-cleared/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        }).then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Error marking voucher as cleared');
            }
        });
    }
}

function deleteVoucher(id) {
    if (confirm('Are you sure you want to delete this bank voucher? This action cannot be undone.')) {
        fetch(`/accounts/transactions/bank-vouchers/${id}/delete/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        }).then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Error deleting bank voucher');
            }
        });
    }
}

// Initialize lucide icons on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}