<!-- accounts/templates/accounts/invoices/proforma_invoice_edit.html -->
<!-- Proforma Invoice Edit Form Template -->
<!-- ASP.NET Source: Module/Accounts/Transactions/ProformaInvoice_Edit.aspx -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Edit Proforma Invoice - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-orange-600 to-sap-orange-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="edit" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">
                        Edit Proforma Invoice
                    </h1>
                    <p class="text-sm text-sap-gray-600 mt-1">
                        Modify proforma invoice details and track amendments
                    </p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <!-- Status Badge -->
                <span class="px-3 py-1 rounded-full text-xs font-medium
                    {% if object.status == 'draft' %}bg-sap-gray-100 text-sap-gray-800{% endif %}
                    {% if object.status == 'submitted' %}bg-sap-blue-100 text-sap-blue-800{% endif %}
                    {% if object.status == 'approved' %}bg-sap-emerald-100 text-sap-emerald-800{% endif %}
                    {% if object.status == 'cancelled' %}bg-sap-red-100 text-sap-red-800{% endif %}">
                    {{ object.get_status_display|default:"Draft" }}
                </span>
                <a href="{% url 'accounts:proforma_invoice_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Edit Proforma Invoice Form -->
    <div class="max-w-7xl mx-auto">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                            <i data-lucide="edit" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                            Proforma Invoice Information
                        </h3>
                        <p class="text-sm text-sap-gray-600 mt-1">Update invoice details and track changes</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        {% if object.status == 'draft' %}
                        <button type="button" onclick="convertToSalesInvoice()" 
                                class="bg-sap-emerald-600 hover:bg-sap-emerald-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="arrow-right" class="w-4 h-4 inline mr-2"></i>
                            Convert to Sales Invoice
                        </button>
                        {% endif %}
                        <button type="button" onclick="showAmendmentHistory()" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="history" class="w-4 h-4 inline mr-2"></i>
                            Amendment History
                        </button>
                    </div>
                </div>
            </div>
            
            <form method="post" id="proforma-edit-form" class="p-6" x-data="proformaEditForm()" hx-post="{% url 'accounts:proforma_invoice_edit' object.id %}" hx-target="#form-messages">
                {% csrf_token %}
                
                <!-- Form Messages -->
                <div id="form-messages" class="mb-4"></div>
                
                <!-- Amendment Tracking -->
                <div class="mb-6 bg-sap-yellow-50 border border-sap-yellow-200 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-sap-yellow-800 mb-2 flex items-center">
                        <i data-lucide="alert-triangle" class="w-4 h-4 mr-2"></i>
                        Amendment Tracking
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                            <span class="text-sap-yellow-600">Created:</span>
                            <span class="font-medium text-sap-yellow-800">{{ object.created_at|date:"M d, Y H:i" }}</span>
                        </div>
                        <div>
                            <span class="text-sap-yellow-600">Last Modified:</span>
                            <span class="font-medium text-sap-yellow-800">{{ object.updated_at|date:"M d, Y H:i" }}</span>
                        </div>
                        <div>
                            <span class="text-sap-yellow-600">Version:</span>
                            <span class="font-medium text-sap-yellow-800">{{ object.version|default:"1.0" }}</span>
                        </div>
                    </div>
                </div>
                
                <!-- Basic Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="info" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                        Basic Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Customer Name -->
                        <div>
                            <label for="{{ form.customer_name.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Customer Name *
                            </label>
                            {{ form.customer_name|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.customer_name.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.customer_name.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- PO Number -->
                        <div>
                            <label for="{{ form.po_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                PO Number
                            </label>
                            {{ form.po_no|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.po_no.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.po_no.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- PO Date -->
                        <div>
                            <label for="{{ form.po_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                PO Date
                            </label>
                            {{ form.po_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.po_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.po_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Work Order Number -->
                        <div>
                            <label for="{{ form.work_order_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Work Order Number
                            </label>
                            {{ form.work_order_no|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.work_order_no.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.work_order_no.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Validity Days -->
                        <div>
                            <label for="{{ form.validity_days.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Validity (Days)
                            </label>
                            {{ form.validity_days|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.validity_days.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.validity_days.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Status -->
                        <div>
                            <label for="{{ form.status.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Status *
                            </label>
                            {{ form.status|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500" }}
                            {% if form.status.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.status.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Amount Details Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="calculator" class="w-5 h-5 mr-2 text-sap-emerald-600"></i>
                        Amount Breakdown
                        <button type="button" @click="recalculateAmounts()" 
                                class="ml-auto bg-sap-emerald-100 hover:bg-sap-emerald-200 text-sap-emerald-700 px-3 py-1 rounded text-sm font-medium transition-colors">
                            <i data-lucide="refresh-cw" class="w-3 h-3 inline mr-1"></i>
                            Recalculate
                        </button>
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <!-- Basic Amount -->
                        <div>
                            <label for="{{ form.basic_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Basic Amount *
                            </label>
                            {{ form.basic_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.basic_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.basic_amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- VAT Amount -->
                        <div>
                            <label for="{{ form.vat_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                VAT Amount
                            </label>
                            {{ form.vat_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.vat_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.vat_amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- CST Amount -->
                        <div>
                            <label for="{{ form.cst_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                CST Amount
                            </label>
                            {{ form.cst_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.cst_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.cst_amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Excise Amount -->
                        <div>
                            <label for="{{ form.excise_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Excise Amount
                            </label>
                            {{ form.excise_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.excise_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.excise_amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Service Tax Amount -->
                        <div>
                            <label for="{{ form.service_tax_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Service Tax Amount
                            </label>
                            {{ form.service_tax_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.service_tax_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.service_tax_amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- P&F Amount -->
                        <div>
                            <label for="{{ form.pf_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                P&F Amount
                            </label>
                            {{ form.pf_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.pf_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.pf_amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Freight Amount -->
                        <div>
                            <label for="{{ form.freight_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Freight Amount
                            </label>
                            {{ form.freight_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.freight_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.freight_amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Discount Amount -->
                        <div>
                            <label for="{{ form.discount_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Discount Amount
                            </label>
                            {{ form.discount_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.discount_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.discount_amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Total Summary -->
                <div class="mb-8 bg-sap-orange-50 border border-sap-orange-200 rounded-lg p-6">
                    <h4 class="text-lg font-medium text-sap-orange-800 mb-4 flex items-center">
                        <i data-lucide="receipt" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                        Updated Invoice Summary
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
                        <div>
                            <p class="text-sm text-sap-orange-600">Gross Amount</p>
                            <p class="text-xl font-bold text-sap-orange-800" x-text="'₹' + grossAmount.toFixed(2)"></p>
                        </div>
                        <div>
                            <p class="text-sm text-sap-orange-600">Total Tax</p>
                            <p class="text-xl font-bold text-sap-orange-800" x-text="'₹' + totalTax.toFixed(2)"></p>
                        </div>
                        <div>
                            <p class="text-sm text-sap-orange-600">Discount</p>
                            <p class="text-xl font-bold text-sap-orange-800" x-text="'₹' + totalDiscount.toFixed(2)"></p>
                        </div>
                        <div>
                            <p class="text-sm text-sap-orange-600">Net Amount</p>
                            <p class="text-2xl font-bold text-sap-orange-800" x-text="'₹' + netAmount.toFixed(2)"></p>
                        </div>
                    </div>
                </div>
                
                <!-- Amendment Comments -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="message-square" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                        Amendment Comments
                    </h4>
                    <div class="bg-sap-gray-50 border border-sap-gray-200 rounded-lg p-4">
                        <textarea rows="3" name="amendment_comments"
                                  placeholder="Enter comments for this amendment (optional)..."
                                  class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500 resize-none"></textarea>
                        <p class="text-xs text-sap-gray-500 mt-2">These comments will be tracked in the amendment history</p>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="resetForm()" 
                                class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="rotate-ccw" class="w-4 h-4 inline mr-2"></i>
                            Reset Changes
                        </button>
                        <button type="button" @click="previewProforma()" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="eye" class="w-4 h-4 inline mr-2"></i>
                            Preview
                        </button>
                        <button type="button" @click="generatePDF()" 
                                class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                            Generate PDF
                        </button>
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="{% url 'accounts:proforma_invoice_list' %}" 
                           class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                            Update Proforma Invoice
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Amendment Guidelines -->
        <div class="mt-6 bg-sap-orange-50 border border-sap-orange-200 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i data-lucide="info" class="w-5 h-5 text-sap-orange-600"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-sap-orange-800">Proforma Invoice Edit Guidelines</h3>
                    <div class="mt-2 text-sm text-sap-orange-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Changes are tracked automatically with timestamp and user information</li>
                            <li>Amendment comments help maintain clear change documentation</li>
                            <li>Status updates trigger workflow notifications</li>
                            <li>Only draft invoices can be converted to sales invoices</li>
                            <li>Amount recalculation maintains tax compliance</li>
                            <li>Version control ensures audit trail integrity</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Amendment History Modal -->
<div id="amendment-history-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-96 overflow-hidden">
            <div class="px-6 py-4 border-b border-sap-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-sap-gray-800">Amendment History</h3>
                    <button type="button" onclick="closeAmendmentHistory()" class="text-sap-gray-400 hover:text-sap-gray-600">
                        <i data-lucide="x" class="w-5 h-5"></i>
                    </button>
                </div>
            </div>
            <div class="p-6 overflow-y-auto max-h-80">
                <div class="space-y-4" id="amendment-history-content">
                    <!-- Amendment history will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function proformaEditForm() {
    return {
        grossAmount: 0,
        totalTax: 0,
        totalDiscount: 0,
        netAmount: 0,
        
        init() {
            this.calculateTotals();
            this.$watch('basic_amount', () => this.calculateTotals());
            this.$watch('vat_amount', () => this.calculateTotals());
            this.$watch('cst_amount', () => this.calculateTotals());
            this.$watch('excise_amount', () => this.calculateTotals());
            this.$watch('service_tax_amount', () => this.calculateTotals());
            this.$watch('discount_amount', () => this.calculateTotals());
        },
        
        calculateTotals() {
            const basicAmount = parseFloat(document.getElementById('{{ form.basic_amount.id_for_label }}')?.value) || 0;
            const vatAmount = parseFloat(document.getElementById('{{ form.vat_amount.id_for_label }}')?.value) || 0;
            const cstAmount = parseFloat(document.getElementById('{{ form.cst_amount.id_for_label }}')?.value) || 0;
            const exciseAmount = parseFloat(document.getElementById('{{ form.excise_amount.id_for_label }}')?.value) || 0;
            const serviceTaxAmount = parseFloat(document.getElementById('{{ form.service_tax_amount.id_for_label }}')?.value) || 0;
            const pfAmount = parseFloat(document.getElementById('{{ form.pf_amount.id_for_label }}')?.value) || 0;
            const freightAmount = parseFloat(document.getElementById('{{ form.freight_amount.id_for_label }}')?.value) || 0;
            const discountAmount = parseFloat(document.getElementById('{{ form.discount_amount.id_for_label }}')?.value) || 0;
            
            this.grossAmount = basicAmount + pfAmount + freightAmount;
            this.totalTax = vatAmount + cstAmount + exciseAmount + serviceTaxAmount;
            this.totalDiscount = discountAmount;
            this.netAmount = this.grossAmount + this.totalTax - this.totalDiscount;
        },
        
        recalculateAmounts() {
            const basicAmount = parseFloat(document.getElementById('{{ form.basic_amount.id_for_label }}').value) || 0;
            if (basicAmount > 0) {
                // Auto-calculate VAT (18% of basic amount)
                const vatAmount = basicAmount * 0.18;
                document.getElementById('{{ form.vat_amount.id_for_label }}').value = vatAmount.toFixed(2);
                
                // Auto-calculate Service Tax (12% of basic amount)
                const serviceTaxAmount = basicAmount * 0.12;
                document.getElementById('{{ form.service_tax_amount.id_for_label }}').value = serviceTaxAmount.toFixed(2);
                
                this.calculateTotals();
                alert('Tax amounts recalculated based on basic amount.');
            } else {
                alert('Please enter a valid basic amount first.');
            }
        },
        
        previewProforma() {
            if (this.netAmount <= 0) {
                alert('Please enter valid amounts to preview the proforma invoice.');
                return;
            }
            alert('Proforma invoice preview functionality would open in a new window.');
        },
        
        generatePDF() {
            if (this.netAmount <= 0) {
                alert('Please enter valid amounts to generate PDF.');
                return;
            }
            alert('PDF generation functionality would be implemented here.');
        }
    }
}

function convertToSalesInvoice() {
    if (confirm('Are you sure you want to convert this proforma invoice to a sales invoice? This action cannot be undone.')) {
        alert('Convert to sales invoice functionality would be implemented here.');
    }
}

function showAmendmentHistory() {
    document.getElementById('amendment-history-modal').classList.remove('hidden');
    // Load amendment history via HTMX or AJAX
    document.getElementById('amendment-history-content').innerHTML = `
        <div class="text-center py-8">
            <div class="inline-flex items-center px-4 py-2 font-semibold text-sm text-sap-blue-600">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Loading amendment history...
            </div>
        </div>
    `;
    
    // Simulate loading
    setTimeout(() => {
        document.getElementById('amendment-history-content').innerHTML = `
            <div class="space-y-3">
                <div class="flex items-start space-x-3 bg-sap-gray-50 p-3 rounded-lg">
                    <div class="w-2 h-2 bg-sap-emerald-500 rounded-full mt-2"></div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between">
                            <h4 class="text-sm font-medium text-sap-gray-800">Created</h4>
                            <span class="text-xs text-sap-gray-500">{{ object.created_at|date:"M d, Y H:i" }}</span>
                        </div>
                        <p class="text-sm text-sap-gray-600">Initial proforma invoice creation</p>
                    </div>
                </div>
                <div class="flex items-start space-x-3 bg-sap-blue-50 p-3 rounded-lg">
                    <div class="w-2 h-2 bg-sap-blue-500 rounded-full mt-2"></div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between">
                            <h4 class="text-sm font-medium text-sap-gray-800">Status Updated</h4>
                            <span class="text-xs text-sap-gray-500">{{ object.updated_at|date:"M d, Y H:i" }}</span>
                        </div>
                        <p class="text-sm text-sap-gray-600">Status changed to {{ object.get_status_display|default:"Draft" }}</p>
                    </div>
                </div>
            </div>
        `;
    }, 1000);
}

function closeAmendmentHistory() {
    document.getElementById('amendment-history-modal').classList.add('hidden');
}

function resetForm() {
    if (confirm('Are you sure you want to reset all changes? This will reload the original invoice data.')) {
        location.reload();
    }
}

// Auto-calculation setup
document.addEventListener('DOMContentLoaded', function() {
    // Add change listeners for auto-calculation
    const amountFields = [
        '{{ form.basic_amount.id_for_label }}',
        '{{ form.vat_amount.id_for_label }}',
        '{{ form.cst_amount.id_for_label }}',
        '{{ form.excise_amount.id_for_label }}',
        '{{ form.service_tax_amount.id_for_label }}',
        '{{ form.pf_amount.id_for_label }}',
        '{{ form.freight_amount.id_for_label }}',
        '{{ form.discount_amount.id_for_label }}'
    ];
    
    amountFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('input', function() {
                // Trigger Alpine.js recalculation
                const event = new CustomEvent('alpine:recalculate');
                document.dispatchEvent(event);
            });
        }
    });
    
    lucide.createIcons();
});
</script>
{% endblock %}