{% extends 'core/base.html' %}
{% load static %}

{% block title %}Goods Received Receipt [GRR] - Edit{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <div class="bg-blue-600 text-white px-4 py-2">
        <h1 class="text-lg font-bold">Goods Received Receipt [GRR] - Edit</h1>
    </div>
    
    <div class="p-4">
        <!-- Search Section -->
        <div class="bg-white rounded-lg shadow-sm border mb-4">
            <div class="p-4 space-y-4">
                <form method="get" class="flex items-center gap-4">
                    <select name="search_type" class="border border-gray-300 rounded px-3 py-2 text-sm w-48">
                        <option value="0" {% if request.GET.search_type == "0" %}selected{% endif %}>Supplier Name</option>
                        <option value="1" {% if request.GET.search_type == "1" %}selected{% endif %}>PO No</option>
                        <option value="2" {% if request.GET.search_type == "2" %}selected{% endif %}>GRR No</option>
                    </select>
                    
                    <input type="text" name="search_query" value="{{ request.GET.search_query }}" 
                           placeholder="Enter search value" 
                           class="border border-gray-300 rounded px-3 py-2 text-sm w-40">
                           
                    <input type="text" name="supplier_name" value="{{ request.GET.supplier_name }}" 
                           placeholder="Supplier name..." 
                           class="border border-gray-300 rounded px-3 py-2 text-sm flex-1">
                           
                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded text-sm font-medium">
                        Search
                    </button>
                </form>
            </div>
        </div>

        <!-- Results Table -->
        <div class="bg-white rounded-lg shadow-sm border">
            <div class="overflow-x-auto">
                <table class="w-full table-auto divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-8">SN</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">Select</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">Fin Year</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">GRR No</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">Date</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">GIN No</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">PONo</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Supplier</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">Challan No</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">Challan Date</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for grr in grr_list %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-4 py-3 text-sm text-gray-900 text-right">{{ forloop.counter }}</td>
                            <td class="px-4 py-3 text-sm">
                                <a href="{% url 'inventory:grr_edit_detail' grr.Id %}?GRRNo={{ grr.GRRNo }}&GINNo={{ grr.GINNo }}&GINId={{ grr.GINId }}&PONo={{ grr.PONo }}&SupId={{ grr.SupId }}&FyId={{ grr.FinYearId }}" 
                                   class="text-blue-600 hover:text-blue-800 underline">Select</a>
                            </td>
                            <td class="px-4 py-3 text-sm text-gray-900 text-center">{{ grr.FinYear|default:"N/A" }}</td>
                            <td class="px-4 py-3 text-sm text-gray-900 text-center">{{ grr.GRRNo|default:"N/A" }}</td>
                            <td class="px-4 py-3 text-sm text-gray-900 text-center">{{ grr.SysDate|default:"N/A" }}</td>
                            <td class="px-4 py-3 text-sm text-gray-900 text-center">{{ grr.GINNo|default:"N/A" }}</td>
                            <td class="px-4 py-3 text-sm text-gray-900 text-center">{{ grr.PONo|default:"N/A" }}</td>
                            <td class="px-4 py-3 text-sm text-gray-900">{{ grr.Supplier|default:"" }}</td>
                            <td class="px-4 py-3 text-sm text-gray-900 text-center">{{ grr.ChNO|default:"" }}</td>
                            <td class="px-4 py-3 text-sm text-gray-900 text-center">{{ grr.ChDT|default:"" }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="10" class="px-4 py-8 text-center">
                                <div class="text-gray-500">
                                    <p class="text-lg font-medium text-red-900">No data to display !</p>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
            <div class="bg-white px-4 py-3 border-t border-gray-200">
                <div class="flex justify-center">
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                        {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search_type %}&search_type={{ request.GET.search_type }}{% endif %}{% if request.GET.search_query %}&search_query={{ request.GET.search_query }}{% endif %}{% if request.GET.supplier_name %}&supplier_name={{ request.GET.supplier_name }}{% endif %}" 
                               class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">Previous</a>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">{{ num }}</span>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <a href="?page={{ num }}{% if request.GET.search_type %}&search_type={{ request.GET.search_type }}{% endif %}{% if request.GET.search_query %}&search_query={{ request.GET.search_query }}{% endif %}{% if request.GET.supplier_name %}&supplier_name={{ request.GET.supplier_name }}{% endif %}" 
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">{{ num }}</a>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search_type %}&search_type={{ request.GET.search_type }}{% endif %}{% if request.GET.search_query %}&search_query={{ request.GET.search_query }}{% endif %}{% if request.GET.supplier_name %}&supplier_name={{ request.GET.supplier_name }}{% endif %}" 
                               class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">Next</a>
                        {% endif %}
                    </nav>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}