{% extends "core/base.html" %}
{% load static %}

{% block title %}Customer PO - Edit{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Header Section -->
        <div class="sap-card mb-6">
            <div class="px-6 py-4 bg-sap-blue-600 text-white rounded-t-xl flex justify-between items-center">
                <h1 class="text-xl font-bold">Customer PO - Edit</h1>
                <a href="{% url 'sales_distribution:customer_po_create' %}" 
                   class="inline-flex items-center px-4 py-2 bg-white text-sap-blue-600 font-medium rounded-lg hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-sap-blue-600 transition-all duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    New PO
                </a>
            </div>
            
            <!-- Search Section -->
            <div class="px-6 py-4 bg-sap-gray-50 border-b border-sap-gray-200">
                <form method="get" class="flex flex-wrap items-center gap-4" x-data="searchForm()">
                    <!-- Search Type Dropdown -->
                    <div class="flex items-center space-x-2">
                        <select name="search_type" id="search_type" 
                                class="sap-input w-auto"
                                x-model="searchType"
                                @change="toggleSearchFields()">
                            <option value="Select" {% if search_type == "Select" %}selected{% endif %}>Select</option>
                            <option value="0" {% if search_type == "0" %}selected{% endif %}>Customer Name</option>
                            <option value="1" {% if search_type == "1" %}selected{% endif %}>Enquiry No</option>
                            <option value="2" {% if search_type == "2" %}selected{% endif %}>PO No</option>
                        </select>
                        
                        <!-- Enquiry/PO Number Input -->
                        <input type="text" name="enq_id" id="enq_id" value="{{ enq_id }}"
                               class="sap-input w-32"
                               placeholder="Enter ID"
                               x-show="searchType !== '0'"
                               x-transition>
                        
                        <!-- Customer Name Search Input with Real-time Search -->
                        <input type="text" name="search_value" id="search_value" value="{{ search_value }}"
                               class="sap-input w-80"
                               placeholder="Search customer name..."
                               x-show="searchType === '0'"
                               x-transition
                               x-model="searchValue"
                               hx-get="{% url 'sales_distribution:customer_po_list' %}"
                               hx-target="#po-table-container"
                               hx-trigger="input changed delay:500ms"
                               hx-vals='{"search_type": "0"}'
                               hx-include="[name='search_type']">
                    </div>
                    
                    <!-- Search Button -->
                    <button type="submit" name="search" value="1"
                            class="sap-button-primary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Search
                    </button>
                    
                    {% if msg %}
                    <div class="sap-status-error animate-fade-in">{{ msg }}</div>
                    {% endif %}
                </form>
            </div>
        </div>

        <!-- Purchase Orders Table -->
        <div class="sap-card">
            <div id="po-table-container">
            {% if enriched_pos %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-sap-gray-200">
                    <thead class="bg-sap-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sap-gray-700 uppercase tracking-wider">
                                SN
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">
                                Fin Yrs
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">
                                Customer Name
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">
                                Code
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">
                                PO No
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">
                                Enquiry No
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">
                                Gen Date
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-700 uppercase tracking-wider">
                                Gen By
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-sap-gray-200">
                        {% for po_data in enriched_pos %}
                        <tr class="hover:bg-sap-gray-50 transition-colors duration-200">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900 text-right">
                                {{ forloop.counter }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">
                                {{ po_data.fin_year|default:"-" }}
                            </td>
                            <td class="px-6 py-4 text-sm text-sap-gray-900">
                                {{ po_data.customer_name|default:"-" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">
                                {{ po_data.customer_id|default:"-" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <a href="{% url 'sales_distribution:customer_po_update' po_data.po.poid %}?CustomerId={{ po_data.customer_id }}&PONo={{ po_data.po.pono }}&EnqId={{ po_data.po.enqid_id }}&POId={{ po_data.po.poid }}&ModId=2&SubModId=11" 
                                   class="text-sap-blue-600 hover:text-sap-blue-800 font-medium transition-colors duration-200">
                                    {{ po_data.po.pono|default:"-" }}
                                </a>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">
                                {{ po_data.po.enqid_id|default:"-" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">
                                {{ po_data.formatted_date|default:"-" }}
                            </td>
                            <td class="px-6 py-4 text-sm text-sap-gray-900">
                                {{ po_data.employee_name|default:"-" }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    {% if page_obj.has_previous %}
                        <a href="?{% if search_type %}search_type={{ search_type }}&{% endif %}{% if search_value %}search_value={{ search_value }}&{% endif %}{% if enq_id %}enq_id={{ enq_id }}&{% endif %}page={{ page_obj.previous_page_number }}" 
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Previous
                        </a>
                    {% endif %}
                    {% if page_obj.has_next %}
                        <a href="?{% if search_type %}search_type={{ search_type }}&{% endif %}{% if search_value %}search_value={{ search_value }}&{% endif %}{% if enq_id %}enq_id={{ enq_id }}&{% endif %}page={{ page_obj.next_page_number }}" 
                           class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Next
                        </a>
                    {% endif %}
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Showing
                            <span class="font-medium">{{ page_obj.start_index }}</span>
                            to
                            <span class="font-medium">{{ page_obj.end_index }}</span>
                            of
                            <span class="font-medium">{{ paginator.count }}</span>
                            results
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            {% if page_obj.has_previous %}
                                <a href="?{% if search_type %}search_type={{ search_type }}&{% endif %}{% if search_value %}search_value={{ search_value }}&{% endif %}{% if enq_id %}enq_id={{ enq_id }}&{% endif %}page={{ page_obj.previous_page_number }}" 
                                   class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Previous</span>
                                    <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                    </svg>
                                </a>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                                        {{ num }}
                                    </span>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <a href="?{% if search_type %}search_type={{ search_type }}&{% endif %}{% if search_value %}search_value={{ search_value }}&{% endif %}{% if enq_id %}enq_id={{ enq_id }}&{% endif %}page={{ num }}" 
                                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                        {{ num }}
                                    </a>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                                <a href="?{% if search_type %}search_type={{ search_type }}&{% endif %}{% if search_value %}search_value={{ search_value }}&{% endif %}{% if enq_id %}enq_id={{ enq_id }}&{% endif %}page={{ page_obj.next_page_number }}" 
                                   class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Next</span>
                                    <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                    </svg>
                                </a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            </div>
            {% endif %}
            
            {% else %}
            <!-- Empty State -->
            <div class="text-center py-12 animate-fade-in">
                <div class="mx-auto h-12 w-12 text-sap-gray-400">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h3 class="mt-2 text-lg font-medium text-sap-gray-700">No data to display !</h3>
                <p class="mt-1 text-sm text-sap-gray-500">Try adjusting your search criteria or create a new purchase order.</p>
            </div>
            {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
// Alpine.js data function for search form
function searchForm() {
    return {
        searchType: '{{ search_type }}',
        searchValue: '{{ search_value }}',
        
        toggleSearchFields() {
            // No need to manipulate DOM - Alpine handles with x-show
        },
        
        performSearch() {
            if (this.searchType === '0' && this.searchValue.length >= 2) {
                // Real-time search for customer names
                const form = document.querySelector('form');
                // Auto-submit form with debounced input
                setTimeout(() => {
                    form.submit();
                }, 100);
            }
        }
    }
}

// Legacy function for backward compatibility
function toggleSearchFields() {
    // Function kept for any legacy calls but Alpine handles this now
}
</script>
{% endblock %}
