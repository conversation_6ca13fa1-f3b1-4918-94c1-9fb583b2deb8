{% extends 'core/base.html' %}
{% load static %}

{% block title %}Print GRR {{ grr.grr_number }}{% endblock %}

{% block extra_css %}
<style>
    @media print {
        .no-print { display: none !important; }
        body { background: white !important; }
        .print-container { 
            padding: 0 !important; 
            margin: 0 !important;
            box-shadow: none !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Print Controls -->
    <div class="no-print bg-gray-700 text-white px-4 py-2 flex justify-between items-center">
        <h1 class="text-lg font-bold">Print GRR {{ grr.grr_number }}</h1>
        <div class="flex space-x-2">
            <button onclick="window.print()" 
                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm">
                Print
            </button>
            <a href="{% url 'inventory:grr_detail' grr.Id %}" 
               class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded text-sm">
                Back to Details
            </a>
        </div>
    </div>
    
    <!-- Printable Content -->
    <div class="print-container bg-white p-8 max-w-4xl mx-auto">
        <!-- Company Header -->
        <div class="text-center border-b-2 border-gray-300 pb-4 mb-6">
            <h1 class="text-2xl font-bold text-gray-900">Synergytech Automation Pvt. Ltd.</h1>
            <p class="text-sm text-gray-600">Goods Received Receipt (GRR)</p>
        </div>

        <!-- GRR Header Information -->
        <div class="grid grid-cols-2 gap-8 mb-6">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">GRR Information</h3>
                <div class="space-y-2">
                    <div class="flex">
                        <span class="w-32 text-sm font-medium text-gray-500">GRR Number:</span>
                        <span class="text-sm text-gray-900">{{ grr.grr_number }}</span>
                    </div>
                    <div class="flex">
                        <span class="w-32 text-sm font-medium text-gray-500">GRR Date:</span>
                        <span class="text-sm text-gray-900">{{ grr.grr_date|date:"d M Y" }}</span>
                    </div>
                    <div class="flex">
                        <span class="w-32 text-sm font-medium text-gray-500">Financial Year:</span>
                        <span class="text-sm text-gray-900">{{ grr.fin_year|default:"N/A" }}</span>
                    </div>
                    <div class="flex">
                        <span class="w-32 text-sm font-medium text-gray-500">Status:</span>
                        <span class="text-sm text-gray-900">
                            {% if grr.status == 'QUALITY_PENDING' %}
                                Quality Pending
                            {% elif grr.status == 'QUALITY_COMPLETED' %}
                                Quality Completed
                            {% else %}
                                {{ grr.status|default:"Unknown" }}
                            {% endif %}
                        </span>
                    </div>
                </div>
            </div>
            
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Reference Information</h3>
                <div class="space-y-2">
                    <div class="flex">
                        <span class="w-32 text-sm font-medium text-gray-500">GIN Number:</span>
                        <span class="text-sm text-gray-900">{{ grr.gin_number|default:"N/A" }}</span>
                    </div>
                    <div class="flex">
                        <span class="w-32 text-sm font-medium text-gray-500">PO Number:</span>
                        <span class="text-sm text-gray-900">{{ grr.po_number|default:"N/A" }}</span>
                    </div>
                    <div class="flex">
                        <span class="w-32 text-sm font-medium text-gray-500">Supplier:</span>
                        <span class="text-sm text-gray-900">{{ grr.supplier_name }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Invoice & Challan Information -->
        <div class="grid grid-cols-2 gap-8 mb-6">
            <div class="border border-gray-200 p-4 rounded">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Invoice Details</h3>
                <div class="space-y-2">
                    <div class="flex">
                        <span class="w-32 text-sm font-medium text-gray-500">Invoice No:</span>
                        <span class="text-sm text-gray-900">{{ grr.tax_invoice_number|default:"Not provided" }}</span>
                    </div>
                    <div class="flex">
                        <span class="w-32 text-sm font-medium text-gray-500">Invoice Date:</span>
                        <span class="text-sm text-gray-900">
                            {% if grr.tax_invoice_date %}
                                {{ grr.tax_invoice_date|date:"d M Y" }}
                            {% else %}
                                Not provided
                            {% endif %}
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="border border-gray-200 p-4 rounded">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Challan Details</h3>
                <div class="space-y-2">
                    <div class="flex">
                        <span class="w-32 text-sm font-medium text-gray-500">Challan No:</span>
                        <span class="text-sm text-gray-900">{{ grr.challan_number|default:"Not provided" }}</span>
                    </div>
                    <div class="flex">
                        <span class="w-32 text-sm font-medium text-gray-500">Challan Date:</span>
                        <span class="text-sm text-gray-900">
                            {% if grr.challan_date %}
                                {{ grr.challan_date|date:"d M Y" }}
                            {% else %}
                                Not provided
                            {% endif %}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quality Check Information -->
        {% if grr.quality_checked_by_id %}
        <div class="border border-gray-200 p-4 rounded mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">Quality Check Information</h3>
            <div class="grid grid-cols-3 gap-4">
                <div class="flex">
                    <span class="w-32 text-sm font-medium text-gray-500">Checked By:</span>
                    <span class="text-sm text-gray-900">User {{ grr.quality_checked_by_id }}</span>
                </div>
                <div class="flex">
                    <span class="w-32 text-sm font-medium text-gray-500">Check Date:</span>
                    <span class="text-sm text-gray-900">
                        {% if grr.quality_check_date %}
                            {{ grr.quality_check_date|date:"d M Y" }}
                        {% else %}
                            Not recorded
                        {% endif %}
                    </span>
                </div>
                <div class="flex">
                    <span class="w-32 text-sm font-medium text-gray-500">QC Number:</span>
                    <span class="text-sm text-gray-900">{{ grr.quality_note_number|default:"N/A" }}</span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Footer -->
        <div class="mt-12 pt-6 border-t border-gray-300">
            <div class="grid grid-cols-3 gap-8 text-center">
                <div>
                    <div class="border-t border-gray-400 pt-2 mt-8">
                        <p class="text-sm font-medium">Received By</p>
                    </div>
                </div>
                <div>
                    <div class="border-t border-gray-400 pt-2 mt-8">
                        <p class="text-sm font-medium">Quality Checked By</p>
                    </div>
                </div>
                <div>
                    <div class="border-t border-gray-400 pt-2 mt-8">
                        <p class="text-sm font-medium">Approved By</p>
                    </div>
                </div>
            </div>
            
            <div class="text-center text-xs text-gray-500 mt-6">
                <p>Printed on {{ print_date|date:"d M Y \a\\t H:i" }}</p>
                <p>This is a system generated document</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}