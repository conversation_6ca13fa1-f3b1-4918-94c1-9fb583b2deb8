<!-- accounts/templates/accounts/invoices/service_tax_invoice_form.html -->
<!-- Service Tax Invoice Create Form Template -->
<!-- ASP.NET Source: Module/Accounts/Transactions/ServiceTaxInvoice_New.aspx -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}New Service Tax Invoice - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-purple-600 to-sap-purple-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="file-check" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">
                        New Service Tax Invoice
                    </h1>
                    <p class="text-sm text-sap-gray-600 mt-1">
                        Create service tax invoice with compliance reporting
                    </p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:service_tax_invoice_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Service Tax Invoice Form -->
    <div class="max-w-7xl mx-auto">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="edit" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                    Service Tax Invoice Information
                </h3>
                <p class="text-sm text-sap-gray-600 mt-1">Complete service details with compliance requirements</p>
            </div>
            
            <form method="post" id="service-tax-invoice-form" class="p-6" x-data="serviceTaxInvoiceForm()" hx-post="{% url 'accounts:service_tax_invoice_create' %}" hx-target="#form-messages">
                {% csrf_token %}
                
                <!-- Form Messages -->
                <div id="form-messages" class="mb-4"></div>
                
                <!-- Service Provider Information -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="building" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                        Service Provider Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Service Provider Name -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Service Provider Name *
                            </label>
                            <input type="text" name="service_provider_name" required
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500"
                                   placeholder="Enter service provider name">
                        </div>
                        
                        <!-- Service Category -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Service Category *
                            </label>
                            <select name="service_category" required
                                    class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500">
                                <option value="">Select service category</option>
                                <option value="consulting">Consulting Services</option>
                                <option value="technical">Technical Services</option>
                                <option value="maintenance">Maintenance Services</option>
                                <option value="transport">Transport Services</option>
                                <option value="construction">Construction Services</option>
                                <option value="manpower">Manpower Services</option>
                                <option value="other">Other Services</option>
                            </select>
                        </div>
                        
                        <!-- Place of Service -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Place of Service *
                            </label>
                            <input type="text" name="place_of_service" required
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500"
                                   placeholder="Enter place where service is provided">
                        </div>
                        
                        <!-- Service Registration Number -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Service Tax Registration Number
                            </label>
                            <input type="text" name="service_tax_reg_no"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500"
                                   placeholder="Service tax registration number">
                        </div>
                        
                        <!-- Service Date -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Service Date *
                            </label>
                            <input type="date" name="service_date" required
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500">
                        </div>
                        
                        <!-- Invoice Date -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Invoice Date *
                            </label>
                            <input type="date" name="invoice_date" required
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500">
                        </div>
                    </div>
                </div>
                
                <!-- Service Details Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="clipboard-list" class="w-5 h-5 mr-2 text-sap-emerald-600"></i>
                        Service Details
                    </h4>
                    
                    <!-- Services Table -->
                    <div class="border border-sap-gray-300 rounded-lg overflow-hidden">
                        <table class="min-w-full divide-y divide-sap-gray-200" id="services-table">
                            <thead class="bg-sap-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Service Description</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Service Value</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Service Tax Rate</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Service Tax Amount</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Total Amount</th>
                                    <th class="px-4 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Action</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-sap-gray-200" x-ref="servicesTableBody">
                                <template x-for="(service, index) in services" :key="index">
                                    <tr>
                                        <td class="px-4 py-3">
                                            <input type="text" x-model="service.description" 
                                                   class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-purple-500" 
                                                   placeholder="Service description">
                                        </td>
                                        <td class="px-4 py-3">
                                            <input type="number" x-model="service.value" @input="calculateServiceTax(index)" 
                                                   class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-purple-500" 
                                                   placeholder="Service value" min="0" step="0.01">
                                        </td>
                                        <td class="px-4 py-3">
                                            <select x-model="service.tax_rate" @change="calculateServiceTax(index)" 
                                                    class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-purple-500">
                                                <option value="12.36">12.36%</option>
                                                <option value="14">14%</option>
                                                <option value="15">15%</option>
                                                <option value="18">18%</option>
                                            </select>
                                        </td>
                                        <td class="px-4 py-3">
                                            <span class="text-sm font-medium text-sap-gray-900" x-text="'₹' + (service.tax_amount || 0).toFixed(2)"></span>
                                        </td>
                                        <td class="px-4 py-3">
                                            <span class="text-sm font-medium text-sap-gray-900" x-text="'₹' + (service.total_amount || 0).toFixed(2)"></span>
                                        </td>
                                        <td class="px-4 py-3 text-center">
                                            <button type="button" @click="removeService(index)" 
                                                    class="text-sap-red-600 hover:text-sap-red-900">
                                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                        
                        <!-- Add Service Button -->
                        <div class="px-4 py-3 bg-sap-gray-50 border-t border-sap-gray-200">
                            <button type="button" @click="addService()" 
                                    class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                                Add Service
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Reverse Charge Mechanism -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="arrow-right-left" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                        Reverse Charge Mechanism
                    </h4>
                    <div class="bg-sap-orange-50 border border-sap-orange-200 rounded-lg p-4">
                        <div class="flex items-start space-x-3">
                            <input type="checkbox" id="reverse_charge" name="reverse_charge" x-model="reverseCharge" @change="toggleReverseCharge()"
                                   class="mt-1 h-4 w-4 text-sap-orange-600 border-sap-orange-300 rounded focus:ring-sap-orange-500">
                            <div class="flex-1">
                                <label for="reverse_charge" class="text-sm font-medium text-sap-orange-800">
                                    Apply Reverse Charge Mechanism
                                </label>
                                <p class="text-xs text-sap-orange-600 mt-1">
                                    Service tax liability shifts to the service recipient (applicable for specific service categories)
                                </p>
                            </div>
                        </div>
                        
                        <div x-show="reverseCharge" class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-sap-orange-700 mb-1">
                                    Reason for Reverse Charge
                                </label>
                                <select name="reverse_charge_reason"
                                        class="block w-full px-3 py-2 border border-sap-orange-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
                                    <option value="">Select reason</option>
                                    <option value="goods_transport">Goods Transport Agency Services</option>
                                    <option value="construction">Construction Services</option>
                                    <option value="manpower">Manpower Supply Services</option>
                                    <option value="security">Security Services</option>
                                    <option value="other">Other Specified Services</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-sap-orange-700 mb-1">
                                    Service Recipient Registration No.
                                </label>
                                <input type="text" name="recipient_reg_no"
                                       class="block w-full px-3 py-2 border border-sap-orange-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500"
                                       placeholder="Registration number of service recipient">
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Compliance Information -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="shield-check" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Compliance Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Service Tax Period -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Service Tax Period *
                            </label>
                            <select name="service_tax_period" required
                                    class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                                <option value="">Select period</option>
                                <option value="Q1">Quarter 1 (Apr-Jun)</option>
                                <option value="Q2">Quarter 2 (Jul-Sep)</option>
                                <option value="Q3">Quarter 3 (Oct-Dec)</option>
                                <option value="Q4">Quarter 4 (Jan-Mar)</option>
                            </select>
                        </div>
                        
                        <!-- Financial Year -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Financial Year *
                            </label>
                            <select name="financial_year" required
                                    class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                                <option value="">Select financial year</option>
                                <option value="2023-24">2023-24</option>
                                <option value="2024-25">2024-25</option>
                                <option value="2025-26">2025-26</option>
                            </select>
                        </div>
                        
                        <!-- Compliance Status -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Compliance Status
                            </label>
                            <select name="compliance_status"
                                    class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                                <option value="pending">Pending Review</option>
                                <option value="compliant">Compliant</option>
                                <option value="under_review">Under Review</option>
                                <option value="exempted">Exempted</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- Total Summary -->
                <div class="mb-8 bg-sap-purple-50 border border-sap-purple-200 rounded-lg p-6">
                    <h4 class="text-lg font-medium text-sap-purple-800 mb-4 flex items-center">
                        <i data-lucide="receipt" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                        Service Tax Invoice Summary
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
                        <div>
                            <p class="text-sm text-sap-purple-600">Total Service Value</p>
                            <p class="text-xl font-bold text-sap-purple-800" x-text="'₹' + totalServiceValue.toFixed(2)"></p>
                        </div>
                        <div>
                            <p class="text-sm text-sap-purple-600">Total Service Tax</p>
                            <p class="text-xl font-bold text-sap-purple-800" x-text="'₹' + totalServiceTax.toFixed(2)"></p>
                        </div>
                        <div>
                            <p class="text-sm text-sap-purple-600">Reverse Charge</p>
                            <p class="text-xl font-bold text-sap-purple-800" x-text="reverseCharge ? 'Yes' : 'No'"></p>
                        </div>
                        <div>
                            <p class="text-sm text-sap-purple-600">Net Amount</p>
                            <p class="text-2xl font-bold text-sap-purple-800" x-text="'₹' + netAmount.toFixed(2)"></p>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="resetForm()" 
                                class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="rotate-ccw" class="w-4 h-4 inline mr-2"></i>
                            Reset
                        </button>
                        <button type="button" @click="validateCompliance()" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="shield-check" class="w-4 h-4 inline mr-2"></i>
                            Validate Compliance
                        </button>
                        <button type="button" @click="previewInvoice()" 
                                class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="eye" class="w-4 h-4 inline mr-2"></i>
                            Preview
                        </button>
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="{% url 'accounts:service_tax_invoice_list' %}" 
                           class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                            Save Service Tax Invoice
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Service Tax Guidelines -->
        <div class="mt-6 bg-sap-purple-50 border border-sap-purple-200 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i data-lucide="info" class="w-5 h-5 text-sap-purple-600"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-sap-purple-800">Service Tax Invoice Guidelines</h3>
                    <div class="mt-2 text-sm text-sap-purple-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Service tax rates vary by service category and applicable regulations</li>
                            <li>Reverse charge mechanism applies to specific service categories</li>
                            <li>Place of service determines jurisdiction and applicable rates</li>
                            <li>Compliance reporting is mandatory for quarterly filings</li>
                            <li>Service registration numbers must be validated</li>
                            <li>All invoices are automatically included in service tax registers</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function serviceTaxInvoiceForm() {
    return {
        services: [
            {
                description: '',
                value: 0,
                tax_rate: 12.36,
                tax_amount: 0,
                total_amount: 0
            }
        ],
        reverseCharge: false,
        totalServiceValue: 0,
        totalServiceTax: 0,
        netAmount: 0,
        
        addService() {
            this.services.push({
                description: '',
                value: 0,
                tax_rate: 12.36,
                tax_amount: 0,
                total_amount: 0
            });
        },
        
        removeService(index) {
            if (this.services.length > 1) {
                this.services.splice(index, 1);
                this.calculateTotals();
            }
        },
        
        calculateServiceTax(index) {
            const service = this.services[index];
            const value = parseFloat(service.value) || 0;
            const taxRate = parseFloat(service.tax_rate) || 0;
            
            service.tax_amount = value * (taxRate / 100);
            service.total_amount = value + service.tax_amount;
            
            this.calculateTotals();
        },
        
        calculateTotals() {
            this.totalServiceValue = 0;
            this.totalServiceTax = 0;
            
            this.services.forEach(service => {
                const value = parseFloat(service.value) || 0;
                const taxAmount = parseFloat(service.tax_amount) || 0;
                
                this.totalServiceValue += value;
                this.totalServiceTax += taxAmount;
            });
            
            this.netAmount = this.reverseCharge ? this.totalServiceValue : (this.totalServiceValue + this.totalServiceTax);
        },
        
        toggleReverseCharge() {
            this.calculateTotals();
        },
        
        validateCompliance() {
            const serviceProvider = document.querySelector('input[name="service_provider_name"]').value;
            const placeOfService = document.querySelector('input[name="place_of_service"]').value;
            const serviceTaxPeriod = document.querySelector('select[name="service_tax_period"]').value;
            
            if (!serviceProvider || !placeOfService || !serviceTaxPeriod) {
                alert('Please fill all mandatory compliance fields to validate.');
                return;
            }
            
            if (this.totalServiceValue <= 0) {
                alert('Please add at least one service with valid amount.');
                return;
            }
            
            alert('Compliance validation passed. Invoice meets regulatory requirements.');
        },
        
        previewInvoice() {
            if (this.services.length === 0 || !this.services[0].description) {
                alert('Please add at least one service to preview the invoice.');
                return;
            }
            
            if (this.netAmount <= 0) {
                alert('Please enter valid service amounts to preview.');
                return;
            }
            
            alert('Service tax invoice preview functionality would open in a new window.');
        }
    }
}

function resetForm() {
    if (confirm('Are you sure you want to reset the form? All unsaved changes will be lost.')) {
        location.reload();
    }
}

// Auto-setup on page load
document.addEventListener('DOMContentLoaded', function() {
    // Set default service date to today
    const serviceDateField = document.querySelector('input[name="service_date"]');
    const invoiceDateField = document.querySelector('input[name="invoice_date"]');
    const today = new Date().toISOString().split('T')[0];
    
    if (serviceDateField && !serviceDateField.value) {
        serviceDateField.value = today;
    }
    
    if (invoiceDateField && !invoiceDateField.value) {
        invoiceDateField.value = today;
    }
    
    // Auto-select current financial year
    const financialYearField = document.querySelector('select[name="financial_year"]');
    if (financialYearField && !financialYearField.value) {
        const currentYear = new Date().getFullYear();
        const currentMonth = new Date().getMonth();
        
        let fyStart, fyEnd;
        if (currentMonth >= 3) { // April onwards
            fyStart = currentYear;
            fyEnd = currentYear + 1;
        } else { // January to March
            fyStart = currentYear - 1;
            fyEnd = currentYear;
        }
        
        const fyString = `${fyStart}-${fyEnd.toString().slice(-2)}`;
        financialYearField.value = fyString;
    }
    
    lucide.createIcons();
});
</script>
{% endblock %}