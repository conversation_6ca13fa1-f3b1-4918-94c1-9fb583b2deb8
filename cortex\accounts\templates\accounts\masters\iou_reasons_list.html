<!-- accounts/templates/accounts/masters/iou_reasons_list.html -->
<!-- IOU Reasons Management List Template -->
<!-- Simplified version matching actual database structure -->

{% extends 'core/base.html' %}

{% block title %}IOU Reasons - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-red-600 to-sap-red-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="clipboard-list" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">IOU Reasons Management</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage IOU reason codes and terms</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Accounts
                </a>
                <a href="{% url 'accounts:iou_reasons_create' %}" 
                   class="bg-sap-red-600 hover:bg-sap-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    Add IOU Reason
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Statistics Card -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Total IOU Reasons</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ reasons.count }}</p>
                </div>
                <div class="w-12 h-12 bg-sap-red-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="clipboard-list" class="w-6 h-6 text-sap-red-600"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Quick Actions</p>
                    <p class="text-sm text-sap-gray-500">Manage IOU configurations</p>
                </div>
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="settings" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Search -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm mb-6">
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-sap-gray-700 mb-2">Search</label>
                    <form method="get" class="flex">
                        <input type="text" name="search" id="search" value="{{ request.GET.search }}"
                               placeholder="Search by terms..."
                               class="flex-1 px-3 py-2 border border-sap-gray-300 rounded-l-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-red-500 focus:border-sap-red-500">
                        <button type="submit" class="bg-sap-red-600 hover:bg-sap-red-700 text-white px-4 py-2 rounded-r-lg">
                            <i data-lucide="search" class="w-4 h-4"></i>
                        </button>
                    </form>
                </div>
                <div class="flex items-end">
                    <a href="{% url 'accounts:iou_reasons_list' %}" 
                       class="w-full bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors text-center">
                        <i data-lucide="refresh-cw" class="w-4 h-4 inline mr-2"></i>
                        Reset
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- IOU Reasons Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">IOU Reasons</h3>
                <span class="text-sm text-sap-gray-600">{{ reasons.count }} reason{{ reasons.count|pluralize }}</span>
            </div>
        </div>
        
        {% if reasons %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            ID
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Terms
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for reason in reasons %}
                    <tr class="hover:bg-sap-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-sap-gray-900">{{ reason.id }}</div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-sap-gray-900">{{ reason.terms }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <a href="{% url 'accounts:iou_reasons_edit' reason.id %}" 
                                   class="text-sap-red-600 hover:text-sap-red-700 p-1" title="Edit">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </a>
                                <a href="{% url 'accounts:iou_reasons_delete' reason.id %}" 
                                   class="text-red-600 hover:text-red-700 p-1" title="Delete"
                                   onclick="return confirm('Are you sure you want to delete this IOU reason?')">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="w-24 h-24 bg-sap-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i data-lucide="clipboard-list" class="w-12 h-12 text-sap-red-600"></i>
            </div>
            <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No IOU Reasons Found</h3>
            <p class="text-sap-gray-600 mb-6">Get started by adding your first IOU reason code.</p>
            <a href="{% url 'accounts:iou_reasons_create' %}" 
               class="bg-sap-red-600 hover:bg-sap-red-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                Add First IOU Reason
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}