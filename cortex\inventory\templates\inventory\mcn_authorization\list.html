{% extends 'core/base.html' %}
{% load custom_filters %}

{% block title %}MCN Authorization - Work Orders{% endblock %}

{% block extra_css %}
<style>
    .sap-stats-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .sap-stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .sap-stats-item {
        text-align: center;
        padding: 1rem;
        background: white;
        border-radius: 6px;
        border: 1px solid #e9ecef;
    }
    
    .sap-stats-value {
        font-size: 2rem;
        font-weight: bold;
        color: #0070f3;
        display: block;
    }
    
    .sap-stats-label {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.5rem;
    }
    
    .work-order-card {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .work-order-card:hover {
        border-color: #0070f3;
        box-shadow: 0 4px 8px rgba(0,112,243,0.1);
        transform: translateY(-2px);
    }
    
    .wo-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 0.5rem;
    }
    
    .wo-number {
        font-size: 1.125rem;
        font-weight: bold;
        color: #0070f3;
        text-decoration: none;
    }
    
    .wo-number:hover {
        text-decoration: underline;
    }
    
    .wo-date {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .wo-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .wo-detail-item {
        font-size: 0.875rem;
    }
    
    .wo-detail-label {
        font-weight: 600;
        color: #495057;
    }
    
    .wo-detail-value {
        color: #6c757d;
    }
    
    .mcn-badges {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
    }
    
    .mcn-badge {
        background: #e3f2fd;
        color: #1976d2;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .search-section {
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #dee2e6;
        margin-bottom: 2rem;
    }
    
    .search-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        align-items: end;
    }
    
    @media (max-width: 768px) {
        .sap-stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .wo-details {
            grid-template-columns: 1fr;
        }
        
        .search-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">MCN Authorization</h1>
            <p class="text-muted mb-0">Authorize Material Credit Notes for Work Orders</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{% url 'inventory:mcn_authorization_stats' %}" class="btn btn-outline-primary">
                <i class="bi bi-graph-up"></i> Statistics
            </a>
            <a href="{% url 'project_management:material_credit_note_list' %}" class="btn btn-outline-secondary">
                <i class="bi bi-list-ul"></i> View MCNs
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="sap-stats-grid">
        <div class="sap-stats-item">
            <span class="sap-stats-value">{{ stats.total_work_orders }}</span>
            <div class="sap-stats-label">Work Orders with MCN</div>
        </div>
        <div class="sap-stats-item">
            <span class="sap-stats-value">{{ stats.pending_authorization }}</span>
            <div class="sap-stats-label">Pending Authorization</div>
        </div>
        <div class="sap-stats-item">
            <span class="sap-stats-value">{{ stats.today_authorizations }}</span>
            <div class="sap-stats-label">Today's Authorizations</div>
        </div>
    </div>

    <!-- Search Section -->
    <div class="search-section">
        <h5 class="mb-3">Search Work Orders</h5>
        <form method="get" class="search-form">
            <div class="search-grid">
                <div class="form-group">
                    {{ search_form.wo_no.label_tag }}
                    {{ search_form.wo_no }}
                </div>
                <div class="form-group">
                    {{ search_form.customer_name.label_tag }}
                    {{ search_form.customer_name }}
                </div>
                <div class="form-group">
                    {{ search_form.project_title.label_tag }}
                    {{ search_form.project_title }}
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search"></i> Search
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Work Orders List -->
    <div class="row">
        <div class="col-12">
            {% if work_orders %}
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0">Work Orders ({{ page_obj.paginator.count }} total)</h5>
                    <div class="text-muted">
                        {% if page_obj.has_previous or page_obj.has_next %}
                            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                        {% endif %}
                    </div>
                </div>

                <!-- Work Orders Grid -->
                <div class="row">
                    {% for work_order in work_orders %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="work-order-card" onclick="location.href='{% url 'inventory:mcn_authorization_detail' work_order.pk %}'">
                            <div class="wo-header">
                                <a href="{% url 'inventory:mcn_authorization_detail' work_order.pk %}" class="wo-number">
                                    {{ work_order.wono }}
                                </a>
                                <span class="wo-date">{{ work_order.sysdate|default:'-' }}</span>
                            </div>
                            
                            <div class="wo-title mb-2">
                                <strong>{{ work_order.taskprojecttitle|truncatechars:50 }}</strong>
                            </div>
                            
                            <div class="wo-details">
                                <div class="wo-detail-item">
                                    <div class="wo-detail-label">Customer</div>
                                    <div class="wo-detail-value">{{ work_order.customer_name }}</div>
                                </div>
                                <div class="wo-detail-item">
                                    <div class="wo-detail-label">Customer Code</div>
                                    <div class="wo-detail-value">{{ work_order.customer_code }}</div>
                                </div>
                            </div>
                            
                            <div class="mcn-badges">
                                <span class="mcn-badge">
                                    <i class="bi bi-file-text"></i> {{ work_order.mcn_count }} MCN{{ work_order.mcn_count|pluralize }}
                                </span>
                                <span class="mcn-badge">
                                    <i class="bi bi-box"></i> {{ work_order.mcn_items_count }} Item{{ work_order.mcn_items_count|pluralize }}
                                </span>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if page_obj.has_previous or page_obj.has_next %}
                <nav aria-label="Work Orders pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?{% if request.GET.wo_no %}wo_no={{ request.GET.wo_no }}&{% endif %}{% if request.GET.customer_name %}customer_name={{ request.GET.customer_name }}&{% endif %}{% if request.GET.project_title %}project_title={{ request.GET.project_title }}&{% endif %}page={{ page_obj.previous_page_number }}">
                                    <i class="bi bi-chevron-left"></i> Previous
                                </a>
                            </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?{% if request.GET.wo_no %}wo_no={{ request.GET.wo_no }}&{% endif %}{% if request.GET.customer_name %}customer_name={{ request.GET.customer_name }}&{% endif %}{% if request.GET.project_title %}project_title={{ request.GET.project_title }}&{% endif %}page={{ num }}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?{% if request.GET.wo_no %}wo_no={{ request.GET.wo_no }}&{% endif %}{% if request.GET.customer_name %}customer_name={{ request.GET.customer_name }}&{% endif %}{% if request.GET.project_title %}project_title={{ request.GET.project_title }}&{% endif %}page={{ page_obj.next_page_number }}">
                                    Next <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

            {% else %}
                <!-- No Work Orders Found -->
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="bi bi-inbox" style="font-size: 4rem; color: #dee2e6;"></i>
                    </div>
                    <h5 class="text-muted mb-3">No Work Orders Found</h5>
                    <p class="text-muted mb-4">
                        {% if request.GET.wo_no or request.GET.customer_name or request.GET.project_title %}
                            No work orders match your search criteria. Try adjusting your search terms.
                        {% else %}
                            No work orders with MCN records are available for authorization.
                        {% endif %}
                    </p>
                    {% if request.GET.wo_no or request.GET.customer_name or request.GET.project_title %}
                        <a href="{% url 'inventory:mcn_authorization_list' %}" class="btn btn-primary">
                            <i class="bi bi-arrow-left"></i> Clear Search
                        </a>
                    {% else %}
                        <a href="{% url 'project_management:material_credit_note_list' %}" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> Create MCN
                        </a>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// Enhanced search functionality
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form on input change (with debounce)
    const searchInputs = document.querySelectorAll('.search-form input[type="text"]');
    let searchTimeout;
    
    searchInputs.forEach(input => {
        input.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                // Auto-submit could be implemented here if desired
                // this.form.submit();
            }, 500);
        });
    });
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl+F to focus on work order search
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            document.getElementById('id_wo_no').focus();
        }
        
        // Escape to clear search
        if (e.key === 'Escape') {
            searchInputs.forEach(input => input.value = '');
        }
    });
    
    // Card click handling
    document.querySelectorAll('.work-order-card').forEach(card => {
        card.addEventListener('click', function(e) {
            // Prevent navigation if clicking on a link
            if (e.target.tagName === 'A') {
                return;
            }
            
            const url = this.getAttribute('onclick').match(/location\.href='([^']+)'/);
            if (url) {
                window.location.href = url[1];
            }
        });
    });
});
</script>
{% endblock %}