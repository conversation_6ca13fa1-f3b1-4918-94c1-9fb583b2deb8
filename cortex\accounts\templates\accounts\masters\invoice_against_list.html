<!-- accounts/templates/accounts/masters/invoice_against_list.html -->
<!-- Invoice Against Management List Template -->
<!-- Task Package 1: Master Data Templates - Invoice Against Management -->

{% extends 'core/base.html' %}

{% block title %}Invoice Against - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-purple-600 to-sap-purple-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="file-check" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Invoice Against Management</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Configure invoice categories and type classifications</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Accounts
                </a>
                <a href="{% url 'accounts:invoice_against_create' %}" 
                   class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    Add Invoice Category
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6" x-data="invoiceAgainstManagement()">
    
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Total Categories</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ invoice_categories.count }}</p>
                </div>
                <div class="w-12 h-12 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="file-check" class="w-6 h-6 text-sap-purple-600"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Active Categories</p>
                    <p class="text-2xl font-bold text-sap-green-600">{{ active_count|default:0 }}</p>
                </div>
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="check-circle" class="w-6 h-6 text-sap-green-600"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Sales Categories</p>
                    <p class="text-2xl font-bold text-sap-blue-600">{{ sales_count|default:0 }}</p>
                </div>
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="shopping-cart" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-gray-600">Service Categories</p>
                    <p class="text-2xl font-bold text-sap-orange-600">{{ service_count|default:0 }}</p>
                </div>
                <div class="w-12 h-12 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="settings" class="w-6 h-6 text-sap-orange-600"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Search and Filters -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm mb-6">
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-sap-gray-700 mb-2">Search</label>
                    <input type="text" x-model="searchTerm" @input="filterCategories" id="search"
                           placeholder="Search by category, description..."
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500">
                </div>
                <div>
                    <label for="invoice_type" class="block text-sm font-medium text-sap-gray-700 mb-2">Invoice Type</label>
                    <select x-model="typeFilter" @change="filterCategories" id="invoice_type"
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500">
                        <option value="">All Types</option>
                        <option value="Sales">Sales Invoice</option>
                        <option value="Service">Service Invoice</option>
                        <option value="Purchase">Purchase Invoice</option>
                        <option value="Credit">Credit Note</option>
                        <option value="Debit">Debit Note</option>
                    </select>
                </div>
                <div>
                    <label for="usage_frequency" class="block text-sm font-medium text-sap-gray-700 mb-2">Usage Frequency</label>
                    <select x-model="usageFilter" @change="filterCategories" id="usage_frequency"
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500">
                        <option value="">All Frequencies</option>
                        <option value="High">High Usage</option>
                        <option value="Medium">Medium Usage</option>
                        <option value="Low">Low Usage</option>
                        <option value="Rare">Rarely Used</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button @click="resetFilters" 
                            class="w-full bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="refresh-cw" class="w-4 h-4 inline mr-2"></i>
                        Reset
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Invoice Categories Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Invoice Categories</h3>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-sap-gray-600" x-text="`${filteredCategories} of ${allCategories} categories`"></span>
                    <div class="flex items-center space-x-1">
                        <button @click="exportCategories" 
                                class="text-sap-gray-600 hover:text-sap-gray-800 p-1" title="Export Categories">
                            <i data-lucide="download" class="w-4 h-4"></i>
                        </button>
                        <button @click="refreshData" 
                                class="text-sap-gray-600 hover:text-sap-gray-800 p-1" title="Refresh Data">
                            <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        {% if invoice_categories %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            <button @click="sortBy('category')" class="flex items-center space-x-1">
                                <span>Category</span>
                                <i data-lucide="chevron-up-down" class="w-3 h-3"></i>
                            </button>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Description</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Invoice Type</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Usage Tracking</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for category in invoice_categories %}
                    <tr class="hover:bg-sap-gray-50" x-show="isVisible({{ category.id }})">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-sap-purple-100 rounded-lg flex items-center justify-center mr-3">
                                    <i data-lucide="file-check" class="w-5 h-5 text-sap-purple-600"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-sap-gray-900">{{ category.against|default:"Product Sales" }}</div>
                                    <div class="text-xs text-sap-gray-500">ID: {{ category.id }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-sap-gray-900">
                                {{ category.description|default:"Standard invoice category for business transactions"|truncatechars:60 }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if 'sales' in category.against|lower %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-blue-100 text-sap-blue-800">
                                <i data-lucide="shopping-cart" class="w-3 h-3 mr-1"></i>
                                Sales
                            </span>
                            {% elif 'service' in category.against|lower %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-orange-100 text-sap-orange-800">
                                <i data-lucide="settings" class="w-3 h-3 mr-1"></i>
                                Service
                            </span>
                            {% elif 'purchase' in category.against|lower %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-green-100 text-sap-green-800">
                                <i data-lucide="shopping-bag" class="w-3 h-3 mr-1"></i>
                                Purchase
                            </span>
                            {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-gray-100 text-sap-gray-800">
                                <i data-lucide="file" class="w-3 h-3 mr-1"></i>
                                General
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-sap-gray-900">
                                <div class="font-medium">{{ category.usage_count|default:0 }} times used</div>
                                <div class="text-xs text-sap-gray-500">
                                    {% if category.usage_count > 100 %}High frequency
                                    {% elif category.usage_count > 50 %}Medium frequency
                                    {% elif category.usage_count > 10 %}Low frequency
                                    {% else %}Rarely used{% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if category.is_active|default:True %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-green-100 text-sap-green-800">
                                <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                Active
                            </span>
                            {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-gray-100 text-sap-gray-800">
                                <i data-lucide="pause-circle" class="w-3 h-3 mr-1"></i>
                                Inactive
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <button @click="viewUsage('{{ category.against|default:"Category" }}')" 
                                        class="text-sap-blue-600 hover:text-sap-blue-700 p-1" title="View Usage">
                                    <i data-lucide="bar-chart" class="w-4 h-4"></i>
                                </button>
                                <a href="{% url 'accounts:invoice_against_edit' category.id %}" 
                                   class="text-sap-purple-600 hover:text-sap-purple-700 p-1" title="Edit">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </a>
                                <a href="{% url 'accounts:invoice_against_delete' category.id %}" 
                                   class="text-red-600 hover:text-red-700 p-1" title="Delete"
                                   onclick="return confirm('Are you sure you want to delete this invoice category?')">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="w-24 h-24 bg-sap-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i data-lucide="file-check" class="w-12 h-12 text-sap-purple-600"></i>
            </div>
            <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No Invoice Categories Configured</h3>
            <p class="text-sap-gray-600 mb-6">Get started by adding your first invoice category.</p>
            <a href="{% url 'accounts:invoice_against_create' %}" 
               class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                Add First Category
            </a>
        </div>
        {% endif %}
    </div>
    
    <!-- Usage Analytics Modal -->
    <div x-show="showUsage" x-transition 
         class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
         @click.self="showUsage = false">
        <div class="bg-white rounded-lg p-6 w-full max-w-lg mx-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-sap-gray-800" x-text="usageTitle"></h3>
                <button @click="showUsage = false" class="text-sap-gray-400 hover:text-sap-gray-600">
                    <i data-lucide="x" class="w-5 h-5"></i>
                </button>
            </div>
            
            <div class="space-y-4">
                <div class="bg-sap-purple-50 rounded-lg p-4">
                    <h4 class="font-medium text-sap-purple-800 mb-3">Usage Analytics</h4>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-sap-gray-600">Total Usage:</span>
                            <div class="font-bold text-sap-purple-600">234 times</div>
                        </div>
                        <div>
                            <span class="text-sap-gray-600">This Month:</span>
                            <div class="font-bold text-sap-green-600">18 times</div>
                        </div>
                        <div>
                            <span class="text-sap-gray-600">Last Invoice:</span>
                            <div class="font-medium text-sap-gray-800">2 days ago</div>
                        </div>
                        <div>
                            <span class="text-sap-gray-600">Average/Month:</span>
                            <div class="font-medium text-sap-blue-600">15.6 times</div>
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center justify-end">
                    <button @click="showUsage = false" 
                            class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function invoiceAgainstManagement() {
    return {
        searchTerm: '',
        typeFilter: '',
        usageFilter: '',
        sortField: '',
        sortDirection: 'asc',
        allCategories: 0,
        filteredCategories: 0,
        showUsage: false,
        usageTitle: '',
        
        init() {
            lucide.createIcons();
            this.allCategories = document.querySelectorAll('tbody tr').length;
            this.filteredCategories = this.allCategories;
        },
        
        filterCategories() {
            const rows = document.querySelectorAll('tbody tr');
            let visibleCount = 0;
            
            rows.forEach(row => {
                const category = row.querySelector('td:first-child').textContent.toLowerCase();
                const description = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                const type = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
                const usage = row.querySelector('td:nth-child(4)').textContent.toLowerCase();
                
                let visible = true;
                
                // Search filter
                if (this.searchTerm && 
                    !category.includes(this.searchTerm.toLowerCase()) && 
                    !description.includes(this.searchTerm.toLowerCase())) {
                    visible = false;
                }
                
                // Type filter
                if (this.typeFilter && !type.includes(this.typeFilter.toLowerCase())) {
                    visible = false;
                }
                
                // Usage filter
                if (this.usageFilter && !usage.includes(this.usageFilter.toLowerCase())) {
                    visible = false;
                }
                
                row.style.display = visible ? '' : 'none';
                if (visible) visibleCount++;
            });
            
            this.filteredCategories = visibleCount;
        },
        
        resetFilters() {
            this.searchTerm = '';
            this.typeFilter = '';
            this.usageFilter = '';
            this.filterCategories();
        },
        
        sortBy(field) {
            console.log('Sorting by:', field);
        },
        
        isVisible(id) {
            return true;
        },
        
        viewUsage(category) {
            this.usageTitle = `Usage Analytics - ${category}`;
            this.showUsage = true;
        },
        
        exportCategories() {
            console.log('Exporting invoice categories...');
        },
        
        refreshData() {
            window.location.reload();
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %>