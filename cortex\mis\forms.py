# mis/forms.py
# Django forms for MIS Budget Management and Tax Computation modules
# Task Groups 1, 2, and 6: Budget Management, Departmental Budgets, and Tax Computation

from django import forms
from django.core.exceptions import ValidationError
from django.contrib.auth.models import User
from decimal import Decimal
from .models import (
    BudgetCode, BudgetPeriod, BudgetAllocation, BudgetDistribution,
    DepartmentBudgetMaster, DepartmentBudgetDetails, DepartmentBudgetTimeTracking,
    DepartmentBudgetTransfer,
    TaxConfiguration, TaxComputation, TaxComputationDetails, TaxCompliance, TaxReport
)
from sys_admin.models import FinancialYear


class BudgetCodeForm(forms.ModelForm):
    """
    Form for Budget Code management
    Replaces ASP.NET Budget_Code.aspx functionality
    """
    
    class Meta:
        model = BudgetCode
        fields = ['description', 'symbol']
        widgets = {
            'description': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': 'Enter budget code description',
                'required': True,
                'maxlength': 255
            }),
            'symbol': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': 'Enter budget symbol (max 2 characters)',
                'required': True,
                'maxlength': 2
            }),
        }
        labels = {
            'description': 'Budget Code Description',
            'symbol': 'Budget Symbol',
        }

    def clean_symbol(self):
        """Validate budget symbol"""
        symbol = self.cleaned_data.get('symbol')
        if symbol:
            symbol = symbol.upper().strip()
            if len(symbol) > 2:
                raise ValidationError("Budget symbol cannot exceed 2 characters.")
            
            # Check for uniqueness
            if BudgetCode.objects.filter(symbol=symbol).exclude(pk=self.instance.pk).exists():
                raise ValidationError("Budget symbol must be unique.")
        return symbol

    def clean_description(self):
        """Validate description"""
        description = self.cleaned_data.get('description')
        if description:
            description = description.strip()
            if len(description) < 3:
                raise ValidationError("Description must be at least 3 characters long.")
            
            # Check for uniqueness
            if BudgetCode.objects.filter(description__iexact=description).exclude(pk=self.instance.pk).exists():
                raise ValidationError("Budget code description must be unique.")
        return description


class BudgetPeriodForm(forms.ModelForm):
    """
    Form for Budget Period management
    """
    
    class Meta:
        model = BudgetPeriod
        fields = ['name', 'start_date', 'end_date', 'financial_year', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': 'e.g., Q1 2024, H1 2024',
                'required': True
            }),
            'start_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
            'end_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
            'financial_year': forms.Select(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'w-4 h-4 text-sap-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-sap-blue-500 focus:ring-2'
            }),
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)
        
        if company:
            self.fields['financial_year'].queryset = FinancialYear.objects.filter(company=company)

    def clean(self):
        """Validate budget period dates"""
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        financial_year = cleaned_data.get('financial_year')

        if start_date and end_date:
            if start_date >= end_date:
                raise ValidationError("End date must be after start date.")
            
            # Check for overlapping periods
            overlapping = BudgetPeriod.objects.filter(
                financial_year=financial_year,
                start_date__lt=end_date,
                end_date__gt=start_date
            ).exclude(pk=self.instance.pk)
            
            if overlapping.exists():
                raise ValidationError("Budget period overlaps with existing period.")

        return cleaned_data


class BudgetAllocationForm(forms.ModelForm):
    """
    Form for Budget Allocation management
    Replaces ASP.NET BudgetCode_Allocation.aspx functionality
    """
    
    class Meta:
        model = BudgetAllocation
        fields = [
            'budget_code', 'budget_period', 'allocation_type', 'allocated_amount',
            'department_id', 'project_id', 'work_order_id', 'notes'
        ]
        widgets = {
            'budget_code': forms.Select(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
            'budget_period': forms.Select(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
            'allocation_type': forms.Select(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True,
                'hx-get': '/mis/budgets/allocation-type-fields/',
                'hx-target': '#allocation-type-fields',
                'hx-trigger': 'change'
            }),
            'allocated_amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0',
                'required': True
            }),
            'department_id': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': 'Department ID'
            }),
            'project_id': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': 'Project ID'
            }),
            'work_order_id': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': 'Work Order ID'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'rows': 3,
                'placeholder': 'Additional notes or justification'
            }),
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if company:
            self.fields['budget_period'].queryset = BudgetPeriod.objects.filter(
                company=company, is_active=True
            )
        
        # Set initial user
        if user and not self.instance.pk:
            self.instance.requested_by = user

    def clean(self):
        """Validate budget allocation"""
        cleaned_data = super().clean()
        allocation_type = cleaned_data.get('allocation_type')
        department_id = cleaned_data.get('department_id')
        project_id = cleaned_data.get('project_id')
        work_order_id = cleaned_data.get('work_order_id')
        allocated_amount = cleaned_data.get('allocated_amount')

        # Validate type-specific requirements
        if allocation_type == 'department' and not department_id:
            raise ValidationError("Department ID is required for department budget allocations.")
        elif allocation_type == 'project' and not project_id:
            raise ValidationError("Project ID is required for project budget allocations.")
        elif allocation_type == 'work_order' and not work_order_id:
            raise ValidationError("Work Order ID is required for work order budget allocations.")

        # Validate amount
        if allocated_amount and allocated_amount <= 0:
            raise ValidationError("Allocated amount must be greater than zero.")

        return cleaned_data


class BudgetDistributionForm(forms.ModelForm):
    """
    Form for Budget Distribution management
    Replaces ASP.NET Budget_Dist.aspx functionality
    """
    
    class Meta:
        model = BudgetDistribution
        fields = ['category', 'allocated_amount', 'percentage', 'description']
        widgets = {
            'category': forms.Select(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
            'allocated_amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0',
                'required': True
            }),
            'percentage': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0',
                'max': '100',
                'required': True
            }),
            'description': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': 'Distribution description'
            }),
        }

    def clean_percentage(self):
        """Validate percentage"""
        percentage = self.cleaned_data.get('percentage')
        if percentage and (percentage < 0 or percentage > 100):
            raise ValidationError("Percentage must be between 0 and 100.")
        return percentage


class BudgetDistributionFormSet(forms.BaseInlineFormSet):
    """
    Formset for managing multiple budget distributions
    """
    
    def clean(self):
        """Validate that total percentage doesn't exceed 100%"""
        if any(self.errors):
            return
        
        total_percentage = Decimal('0')
        for form in self.forms:
            if not form.cleaned_data.get('DELETE', False):
                percentage = form.cleaned_data.get('percentage', 0)
                if percentage:
                    total_percentage += Decimal(str(percentage))
        
        if total_percentage > 100:
            raise ValidationError("Total percentage cannot exceed 100%.")


class BudgetSearchForm(forms.Form):
    """
    Form for searching and filtering budget records
    """
    
    budget_code = forms.ModelChoiceField(
        queryset=BudgetCode.objects.all(),
        required=False,
        empty_label="All Budget Codes",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'hx-get': '/mis/budgets/search/',
            'hx-trigger': 'change',
            'hx-target': '#budget-results',
            'hx-include': '[name^="search"]'
        })
    )
    
    allocation_type = forms.ChoiceField(
        choices=[('', 'All Types')] + BudgetAllocation.ALLOCATION_TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'hx-get': '/mis/budgets/search/',
            'hx-trigger': 'change',
            'hx-target': '#budget-results',
            'hx-include': '[name^="search"]'
        })
    )
    
    status = forms.ChoiceField(
        choices=[('', 'All Status')] + BudgetAllocation.ALLOCATION_STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'hx-get': '/mis/budgets/search/',
            'hx-trigger': 'change',
            'hx-target': '#budget-results',
            'hx-include': '[name^="search"]'
        })
    )
    
    budget_period = forms.ModelChoiceField(
        queryset=BudgetPeriod.objects.filter(is_active=True),
        required=False,
        empty_label="All Periods",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'hx-get': '/mis/budgets/search/',
            'hx-trigger': 'change',
            'hx-target': '#budget-results',
            'hx-include': '[name^="search"]'
        })
    )
    
    amount_min = forms.DecimalField(
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Min amount',
            'step': '0.01',
            'min': '0'
        })
    )
    
    amount_max = forms.DecimalField(
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Max amount',
            'step': '0.01',
            'min': '0'
        })
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
        })
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
        })
    )


class BudgetApprovalForm(forms.Form):
    """
    Form for budget approval workflow
    """
    
    APPROVAL_ACTIONS = [
        ('approve', 'Approve'),
        ('reject', 'Reject'),
        ('request_changes', 'Request Changes'),
    ]
    
    action = forms.ChoiceField(
        choices=APPROVAL_ACTIONS,
        widget=forms.RadioSelect(attrs={
            'class': 'w-4 h-4 text-sap-blue-600 bg-gray-100 border-gray-300 focus:ring-sap-blue-500 focus:ring-2'
        })
    )
    
    comments = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
            'rows': 3,
            'placeholder': 'Approval comments or feedback'
        })
    )

    def clean(self):
        """Validate approval form"""
        cleaned_data = super().clean()
        action = cleaned_data.get('action')
        comments = cleaned_data.get('comments')

        if action in ['reject', 'request_changes'] and not comments:
            raise ValidationError("Comments are required when rejecting or requesting changes.")

        return cleaned_data


# Budget Allocation Inline Formset
BudgetDistributionInlineFormSet = forms.inlineformset_factory(
    BudgetAllocation,
    BudgetDistribution,
    form=BudgetDistributionForm,
    formset=BudgetDistributionFormSet,
    extra=3,
    min_num=1,
    max_num=10,
    can_delete=True,
    fields=['category', 'allocated_amount', 'percentage', 'description']
)


# ============================================================================
# Task Group 2: Departmental Budget Management Forms
# ============================================================================

class DepartmentBudgetMasterForm(forms.ModelForm):
    """
    Enhanced form for Department Budget management
    Replaces ASP.NET Budget_Dist_Dept.aspx functionality
    """
    
    class Meta:
        model = DepartmentBudgetMaster
        fields = [
            'budget_period', 'department_id', 'department_name', 'budget_type',
            'total_budget_amount', 'priority', 'business_justification',
            'expected_outcomes', 'risk_assessment'
        ]
        widgets = {
            'budget_period': forms.Select(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True,
                'hx-get': '/mis/departments/budget-period-info/',
                'hx-target': '#budget-period-info',
                'hx-trigger': 'change'
            }),
            'department_id': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True,
                'hx-get': '/mis/departments/department-info/',
                'hx-target': '#department-info',
                'hx-trigger': 'change',
                'placeholder': 'Enter department ID'
            }),
            'department_name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True,
                'placeholder': 'Department name'
            }),
            'budget_type': forms.Select(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
            'total_budget_amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0',
                'required': True,
                'hx-get': '/mis/departments/budget-validation/',
                'hx-target': '#budget-validation-info',
                'hx-trigger': 'keyup changed delay:500ms'
            }),
            'priority': forms.Select(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500'
            }),
            'business_justification': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'rows': 4,
                'placeholder': 'Provide detailed business justification for this budget request',
                'required': True
            }),
            'expected_outcomes': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'rows': 3,
                'placeholder': 'Expected outcomes and measurable benefits'
            }),
            'risk_assessment': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'rows': 3,
                'placeholder': 'Risk assessment and mitigation strategies'
            }),
        }
        labels = {
            'budget_period': 'Budget Period',
            'department_id': 'Department ID',
            'department_name': 'Department Name',
            'budget_type': 'Budget Type',
            'total_budget_amount': 'Total Budget Amount (₹)',
            'priority': 'Priority',
            'business_justification': 'Business Justification',
            'expected_outcomes': 'Expected Outcomes',
            'risk_assessment': 'Risk Assessment',
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if company:
            self.fields['budget_period'].queryset = BudgetPeriod.objects.filter(
                company=company, is_active=True
            ).order_by('-start_date')
        
        # Set initial user
        if user and not self.instance.pk:
            self.instance.requested_by = user
            self.instance.company = company

    def clean_total_budget_amount(self):
        """Validate budget amount"""
        amount = self.cleaned_data.get('total_budget_amount')
        if amount and amount <= 0:
            raise ValidationError("Budget amount must be greater than zero.")
        if amount and amount > 10000000:  # 1 crore limit
            raise ValidationError("Budget amount cannot exceed ₹1,00,00,000.")
        return amount

    def clean_department_id(self):
        """Validate department ID"""
        dept_id = self.cleaned_data.get('department_id')
        if dept_id and dept_id <= 0:
            raise ValidationError("Department ID must be a positive number.")
        return dept_id

    def clean(self):
        """Validate form data"""
        cleaned_data = super().clean()
        department_id = cleaned_data.get('department_id')
        budget_period = cleaned_data.get('budget_period')
        budget_type = cleaned_data.get('budget_type')
        
        # Check for duplicate budget for same department, period, and type
        if department_id and budget_period and budget_type:
            existing = DepartmentBudgetMaster.objects.filter(
                department_id=department_id,
                budget_period=budget_period,
                budget_type=budget_type
            ).exclude(pk=self.instance.pk)
            
            if existing.exists():
                raise ValidationError(
                    f"A {budget_type} budget already exists for this department in the selected period."
                )
        
        return cleaned_data


class DepartmentBudgetDetailsForm(forms.ModelForm):
    """
    Form for Department Budget line items
    Replaces ASP.NET Budget_Dist_Dept_Details.aspx functionality
    """
    
    class Meta:
        model = DepartmentBudgetDetails
        fields = [
            'line_number', 'expense_category', 'description', 'detailed_description',
            'requested_amount', 'planned_start_date', 'planned_end_date',
            'business_justification', 'success_metrics', 'is_critical', 'is_recurring'
        ]
        widgets = {
            'line_number': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'min': '1',
                'required': True
            }),
            'expense_category': forms.Select(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
            'description': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': 'Brief description of expense',
                'required': True,
                'maxlength': 255
            }),
            'detailed_description': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'rows': 2,
                'placeholder': 'Detailed description (optional)'
            }),
            'requested_amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0',
                'required': True
            }),
            'planned_start_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500'
            }),
            'planned_end_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500'
            }),
            'business_justification': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'rows': 3,
                'placeholder': 'Business justification for this line item',
                'required': True
            }),
            'success_metrics': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'rows': 2,
                'placeholder': 'How will success be measured?'
            }),
            'is_critical': forms.CheckboxInput(attrs={
                'class': 'w-4 h-4 text-sap-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-sap-blue-500 focus:ring-2'
            }),
            'is_recurring': forms.CheckboxInput(attrs={
                'class': 'w-4 h-4 text-sap-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-sap-blue-500 focus:ring-2'
            }),
        }

    def clean(self):
        """Validate line item data"""
        cleaned_data = super().clean()
        start_date = cleaned_data.get('planned_start_date')
        end_date = cleaned_data.get('planned_end_date')
        
        if start_date and end_date and start_date >= end_date:
            raise ValidationError("End date must be after start date.")
        
        return cleaned_data


class DepartmentBudgetDetailsFormSet(forms.BaseInlineFormSet):
    """
    Formset for managing multiple department budget line items
    """
    
    def clean(self):
        """Validate that line numbers are unique and amounts sum correctly"""
        if any(self.errors):
            return
        
        line_numbers = []
        total_amount = Decimal('0')
        
        for form in self.forms:
            if not form.cleaned_data.get('DELETE', False):
                line_number = form.cleaned_data.get('line_number')
                amount = form.cleaned_data.get('requested_amount', 0)
                
                if line_number:
                    if line_number in line_numbers:
                        raise ValidationError(f"Line number {line_number} is duplicated.")
                    line_numbers.append(line_number)
                
                if amount:
                    total_amount += Decimal(str(amount))
        
        # Store total for parent form validation
        self.total_detail_amount = total_amount


class DepartmentBudgetTimeTrackingForm(forms.ModelForm):
    """
    Form for time-based budget tracking
    Replaces ASP.NET Budget_Dist_Dept_Details_Time.aspx functionality
    """
    
    class Meta:
        model = DepartmentBudgetTimeTracking
        fields = [
            'budget_detail', 'tracking_period', 'period_start', 'period_end',
            'planned_amount', 'actual_amount', 'committed_amount',
            'planned_milestones', 'achieved_milestones', 'performance_notes',
            'forecast_amount', 'forecast_notes'
        ]
        widgets = {
            'budget_detail': forms.Select(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500'
            }),
            'tracking_period': forms.Select(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
            'period_start': forms.DateInput(attrs={
                'type': 'date',
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
            'period_end': forms.DateInput(attrs={
                'type': 'date',
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
            'planned_amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0',
                'required': True
            }),
            'actual_amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'committed_amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'planned_milestones': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'rows': 3,
                'placeholder': 'Planned milestones for this period'
            }),
            'achieved_milestones': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'rows': 3,
                'placeholder': 'Actually achieved milestones'
            }),
            'performance_notes': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'rows': 3,
                'placeholder': 'Performance notes and observations'
            }),
            'forecast_amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'forecast_notes': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'rows': 2,
                'placeholder': 'Forecasting assumptions and notes'
            }),
        }

    def __init__(self, *args, **kwargs):
        department_budget = kwargs.pop('department_budget', None)
        super().__init__(*args, **kwargs)
        
        if department_budget:
            self.fields['budget_detail'].queryset = DepartmentBudgetDetails.objects.filter(
                department_budget=department_budget
            )

    def clean(self):
        """Validate time tracking data"""
        cleaned_data = super().clean()
        start_date = cleaned_data.get('period_start')
        end_date = cleaned_data.get('period_end')
        
        if start_date and end_date and start_date >= end_date:
            raise ValidationError("Period end date must be after start date.")
        
        return cleaned_data


class DepartmentBudgetTransferForm(forms.ModelForm):
    """
    Form for budget transfers between departments
    """
    
    class Meta:
        model = DepartmentBudgetTransfer
        fields = [
            'from_department_budget', 'to_department_budget', 'transfer_amount',
            'transfer_reason', 'urgency', 'business_impact', 'expected_completion_date'
        ]
        widgets = {
            'from_department_budget': forms.Select(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
            'to_department_budget': forms.Select(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
            'transfer_amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0',
                'required': True
            }),
            'transfer_reason': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'rows': 3,
                'placeholder': 'Reason for budget transfer',
                'required': True
            }),
            'urgency': forms.Select(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500'
            }),
            'business_impact': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'rows': 3,
                'placeholder': 'Impact on business operations',
                'required': True
            }),
            'expected_completion_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if company:
            # Filter budgets by company and approved status
            approved_budgets = DepartmentBudgetMaster.objects.filter(
                company=company,
                status__in=['approved', 'active']
            )
            self.fields['from_department_budget'].queryset = approved_budgets
            self.fields['to_department_budget'].queryset = approved_budgets
        
        if user and not self.instance.pk:
            self.instance.requested_by = user

    def clean(self):
        """Validate transfer data"""
        cleaned_data = super().clean()
        from_budget = cleaned_data.get('from_department_budget')
        to_budget = cleaned_data.get('to_department_budget')
        transfer_amount = cleaned_data.get('transfer_amount')
        
        if from_budget == to_budget:
            raise ValidationError("Source and destination budgets cannot be the same.")
        
        if from_budget and transfer_amount:
            if transfer_amount > from_budget.available_amount:
                raise ValidationError(
                    f"Transfer amount (₹{transfer_amount:,.2f}) exceeds available budget "
                    f"(₹{from_budget.available_amount:,.2f}) in source department."
                )
        
        return cleaned_data


class DepartmentBudgetSearchForm(forms.Form):
    """
    Form for searching and filtering department budgets
    """
    
    department_id = forms.IntegerField(
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Department ID',
            'hx-get': '/mis/departments/search/',
            'hx-trigger': 'keyup changed delay:300ms',
            'hx-target': '#department-budget-results',
            'hx-include': '[name^="search"]'
        })
    )
    
    department_name = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Department Name',
            'hx-get': '/mis/departments/search/',
            'hx-trigger': 'keyup changed delay:300ms',
            'hx-target': '#department-budget-results',
            'hx-include': '[name^="search"]'
        })
    )
    
    budget_type = forms.ChoiceField(
        choices=[('', 'All Types')] + DepartmentBudgetMaster.BUDGET_TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'hx-get': '/mis/departments/search/',
            'hx-trigger': 'change',
            'hx-target': '#department-budget-results',
            'hx-include': '[name^="search"]'
        })
    )
    
    status = forms.ChoiceField(
        choices=[('', 'All Status')] + DepartmentBudgetMaster.BUDGET_STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'hx-get': '/mis/departments/search/',
            'hx-trigger': 'change',
            'hx-target': '#department-budget-results',
            'hx-include': '[name^="search"]'
        })
    )
    
    priority = forms.ChoiceField(
        choices=[('', 'All Priorities'), ('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')],
        required=False,
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'hx-get': '/mis/departments/search/',
            'hx-trigger': 'change',
            'hx-target': '#department-budget-results',
            'hx-include': '[name^="search"]'
        })
    )
    
    amount_min = forms.DecimalField(
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Min amount',
            'step': '0.01',
            'min': '0'
        })
    )
    
    amount_max = forms.DecimalField(
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Max amount',
            'step': '0.01',
            'min': '0'
        })
    )


class DepartmentBudgetApprovalForm(forms.Form):
    """
    Form for department budget approval workflow
    """
    
    APPROVAL_ACTIONS = [
        ('dept_approve', 'Department Approve'),
        ('finance_review', 'Finance Review'),
        ('final_approve', 'Final Approve'),
        ('reject', 'Reject'),
        ('request_changes', 'Request Changes'),
    ]
    
    action = forms.ChoiceField(
        choices=APPROVAL_ACTIONS,
        widget=forms.RadioSelect(attrs={
            'class': 'w-4 h-4 text-sap-blue-600 bg-gray-100 border-gray-300 focus:ring-sap-blue-500 focus:ring-2'
        })
    )
    
    comments = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
            'rows': 4,
            'placeholder': 'Approval comments, feedback, or requested changes'
        })
    )
    
    approved_amount = forms.DecimalField(
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
            'placeholder': '0.00',
            'step': '0.01',
            'min': '0'
        }),
        help_text="Leave blank to approve full requested amount"
    )

    def clean(self):
        """Validate approval form"""
        cleaned_data = super().clean()
        action = cleaned_data.get('action')
        comments = cleaned_data.get('comments')

        if action in ['reject', 'request_changes'] and not comments:
            raise ValidationError("Comments are required when rejecting or requesting changes.")

        return cleaned_data


# Department Budget Detail Inline Formset
DepartmentBudgetDetailsInlineFormSet = forms.inlineformset_factory(
    DepartmentBudgetMaster,
    DepartmentBudgetDetails,
    form=DepartmentBudgetDetailsForm,
    formset=DepartmentBudgetDetailsFormSet,
    extra=5,
    min_num=1,
    max_num=50,
    can_delete=True,
    fields=[
        'line_number', 'expense_category', 'description', 'detailed_description',
        'requested_amount', 'planned_start_date', 'planned_end_date',
        'business_justification', 'success_metrics', 'is_critical', 'is_recurring'
    ]
)


# ============================================================================
# Task Group 6: Tax Computation & Compliance Forms
# ============================================================================

class TaxConfigurationForm(forms.ModelForm):
    """
    Form for Tax Configuration management
    Replaces ASP.NET tax setup functionality
    """
    
    class Meta:
        model = TaxConfiguration
        fields = [
            'tax_type', 'tax_code', 'tax_name', 'description',
            'calculation_method', 'tax_rate', 'fixed_amount', 'minimum_amount', 'maximum_amount',
            'applicable_from', 'applicable_to', 'is_active',
            'formula', 'exemption_threshold'
        ]
        widgets = {
            'tax_type': forms.Select(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True,
                'hx-get': '/mis/taxes/tax-type-fields/',
                'hx-target': '#tax-type-fields',
                'hx-trigger': 'change'
            }),
            'tax_code': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': 'e.g., EXCISE_12.5, VAT_18.0',
                'required': True,
                'maxlength': 20
            }),
            'tax_name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': 'Display name for tax',
                'required': True,
                'maxlength': 100
            }),
            'description': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'rows': 3,
                'placeholder': 'Detailed description of tax',
                'required': True
            }),
            'calculation_method': forms.Select(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True,
                'hx-get': '/mis/taxes/calculation-method-fields/',
                'hx-target': '#calculation-method-fields',
                'hx-trigger': 'change'
            }),
            'tax_rate': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': '12.5000',
                'step': '0.0001',
                'min': '0',
                'max': '100'
            }),
            'fixed_amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'minimum_amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'maximum_amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'applicable_from': forms.DateInput(attrs={
                'type': 'date',
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
            'applicable_to': forms.DateInput(attrs={
                'type': 'date',
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'w-4 h-4 text-sap-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-sap-blue-500 focus:ring-2'
            }),
            'formula': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'rows': 3,
                'placeholder': 'Custom calculation formula (for formula-based taxes)'
            }),
            'exemption_threshold': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
        }
        labels = {
            'tax_type': 'Tax Type',
            'tax_code': 'Tax Code',
            'tax_name': 'Tax Name',
            'description': 'Description',
            'calculation_method': 'Calculation Method',
            'tax_rate': 'Tax Rate (%)',
            'fixed_amount': 'Fixed Amount (₹)',
            'minimum_amount': 'Minimum Amount (₹)',
            'maximum_amount': 'Maximum Amount (₹)',
            'applicable_from': 'Applicable From',
            'applicable_to': 'Applicable To',
            'is_active': 'Active',
            'formula': 'Custom Formula',
            'exemption_threshold': 'Exemption Threshold (₹)',
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user and not self.instance.pk:
            self.instance.created_by = user
        if company and not self.instance.pk:
            self.instance.company = company

    def clean_tax_code(self):
        """Validate tax code uniqueness"""
        tax_code = self.cleaned_data.get('tax_code')
        if tax_code:
            tax_code = tax_code.upper().strip()
            if TaxConfiguration.objects.filter(tax_code=tax_code).exclude(pk=self.instance.pk).exists():
                raise ValidationError("Tax code must be unique.")
        return tax_code

    def clean_tax_rate(self):
        """Validate tax rate"""
        tax_rate = self.cleaned_data.get('tax_rate')
        calculation_method = self.cleaned_data.get('calculation_method')
        
        if calculation_method == 'percentage' and not tax_rate:
            raise ValidationError("Tax rate is required for percentage-based calculations.")
        if tax_rate and (tax_rate < 0 or tax_rate > 100):
            raise ValidationError("Tax rate must be between 0 and 100.")
        return tax_rate

    def clean_fixed_amount(self):
        """Validate fixed amount"""
        fixed_amount = self.cleaned_data.get('fixed_amount')
        calculation_method = self.cleaned_data.get('calculation_method')
        
        if calculation_method == 'fixed_amount' and not fixed_amount:
            raise ValidationError("Fixed amount is required for fixed amount calculations.")
        if fixed_amount and fixed_amount < 0:
            raise ValidationError("Fixed amount cannot be negative.")
        return fixed_amount

    def clean(self):
        """Validate form data"""
        cleaned_data = super().clean()
        applicable_from = cleaned_data.get('applicable_from')
        applicable_to = cleaned_data.get('applicable_to')
        
        if applicable_from and applicable_to and applicable_from >= applicable_to:
            raise ValidationError("Applicable to date must be after applicable from date.")
        
        return cleaned_data


class TaxComputationForm(forms.ModelForm):
    """
    Form for Tax Computation management
    Replaces ASP.NET Excise_VAT_CST_Compute.aspx functionality
    """
    
    class Meta:
        model = TaxComputation
        fields = [
            'transaction_type', 'transaction_number', 'transaction_date',
            'party_name', 'party_address', 'party_gstin', 'party_state_code',
            'base_amount', 'discount_amount', 'remarks'
        ]
        widgets = {
            'transaction_type': forms.Select(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
            'transaction_number': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': 'Source transaction number',
                'required': True,
                'maxlength': 50
            }),
            'transaction_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
            'party_name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': 'Party/Customer/Supplier name',
                'required': True,
                'maxlength': 255
            }),
            'party_address': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'rows': 3,
                'placeholder': 'Party address',
                'required': True
            }),
            'party_gstin': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': 'GSTIN/PAN of party',
                'maxlength': 15,
                'pattern': '[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}[Z]{1}[0-9A-Z]{1}'
            }),
            'party_state_code': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': 'State code (e.g., 27 for Maharashtra)',
                'maxlength': 2,
                'pattern': '[0-9]{2}'
            }),
            'base_amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0',
                'required': True,
                'hx-get': '/mis/taxes/calculate-preview/',
                'hx-target': '#tax-calculation-preview',
                'hx-trigger': 'keyup changed delay:500ms',
                'hx-include': '[name="transaction_date"], [name="party_state_code"], [name="discount_amount"]'
            }),
            'discount_amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0',
                'hx-get': '/mis/taxes/calculate-preview/',
                'hx-target': '#tax-calculation-preview',
                'hx-trigger': 'keyup changed delay:500ms',
                'hx-include': '[name="base_amount"], [name="transaction_date"], [name="party_state_code"]'
            }),
            'remarks': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'rows': 3,
                'placeholder': 'Additional remarks or notes'
            }),
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user and not self.instance.pk:
            self.instance.computed_by = user
        if company and not self.instance.pk:
            self.instance.company = company

    def clean_party_gstin(self):
        """Validate GSTIN format"""
        gstin = self.cleaned_data.get('party_gstin')
        if gstin:
            gstin = gstin.upper().strip()
            if len(gstin) == 15:
                # Basic GSTIN format validation
                if not gstin[:2].isdigit():
                    raise ValidationError("GSTIN must start with 2-digit state code.")
                if not gstin[2:7].isalpha():
                    raise ValidationError("GSTIN characters 3-7 must be alphabetic.")
                if not gstin[7:11].isdigit():
                    raise ValidationError("GSTIN characters 8-11 must be numeric.")
        return gstin

    def clean_party_state_code(self):
        """Validate state code"""
        state_code = self.cleaned_data.get('party_state_code')
        if state_code:
            state_code = state_code.strip()
            if not state_code.isdigit() or len(state_code) != 2:
                raise ValidationError("State code must be a 2-digit number.")
            state_code_int = int(state_code)
            if state_code_int < 1 or state_code_int > 37:
                raise ValidationError("State code must be between 01 and 37.")
        return state_code

    def clean(self):
        """Validate form data"""
        cleaned_data = super().clean()
        base_amount = cleaned_data.get('base_amount')
        discount_amount = cleaned_data.get('discount_amount', 0)
        
        if base_amount and discount_amount and discount_amount > base_amount:
            raise ValidationError("Discount amount cannot exceed base amount.")
        
        return cleaned_data


class TaxComputationDetailsForm(forms.ModelForm):
    """
    Form for Tax Computation line item details
    """
    
    class Meta:
        model = TaxComputationDetails
        fields = [
            'line_number', 'item_code', 'item_description', 'hsn_code',
            'quantity', 'unit_price', 'discount_amount'
        ]
        widgets = {
            'line_number': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'min': '1',
                'required': True
            }),
            'item_code': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': 'Item code',
                'maxlength': 50
            }),
            'item_description': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': 'Item description',
                'required': True,
                'maxlength': 255
            }),
            'hsn_code': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': 'HSN/SAC Code',
                'maxlength': 10
            }),
            'quantity': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': '1.000',
                'step': '0.001',
                'min': '0',
                'required': True
            }),
            'unit_price': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0',
                'required': True
            }),
            'discount_amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
        }

    def clean(self):
        """Validate line item data"""
        cleaned_data = super().clean()
        quantity = cleaned_data.get('quantity', 0)
        unit_price = cleaned_data.get('unit_price', 0)
        discount_amount = cleaned_data.get('discount_amount', 0)
        
        if quantity and unit_price:
            line_amount = quantity * unit_price
            if discount_amount > line_amount:
                raise ValidationError("Discount amount cannot exceed line amount.")
        
        return cleaned_data


class TaxComputationDetailsFormSet(forms.BaseInlineFormSet):
    """
    Formset for managing multiple tax computation line items
    """
    
    def clean(self):
        """Validate that line numbers are unique"""
        if any(self.errors):
            return
        
        line_numbers = []
        total_amount = Decimal('0')
        
        for form in self.forms:
            if not form.cleaned_data.get('DELETE', False):
                line_number = form.cleaned_data.get('line_number')
                quantity = form.cleaned_data.get('quantity', 0)
                unit_price = form.cleaned_data.get('unit_price', 0)
                discount_amount = form.cleaned_data.get('discount_amount', 0)
                
                if line_number:
                    if line_number in line_numbers:
                        raise ValidationError(f"Line number {line_number} is duplicated.")
                    line_numbers.append(line_number)
                
                if quantity and unit_price:
                    line_amount = (quantity * unit_price) - discount_amount
                    total_amount += line_amount
        
        # Store total for parent form validation
        self.total_line_amount = total_amount


class TaxComplianceForm(forms.ModelForm):
    """
    Form for Tax Compliance tracking
    """
    
    class Meta:
        model = TaxCompliance
        fields = [
            'compliance_type', 'tax_type', 'period_from', 'period_to', 'due_date',
            'assigned_to', 'tax_liability', 'compliance_notes'
        ]
        widgets = {
            'compliance_type': forms.Select(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
            'tax_type': forms.Select(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
            'period_from': forms.DateInput(attrs={
                'type': 'date',
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
            'period_to': forms.DateInput(attrs={
                'type': 'date',
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
            'due_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
            'assigned_to': forms.Select(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
            'tax_liability': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'compliance_notes': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'rows': 3,
                'placeholder': 'Compliance notes and remarks'
            }),
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)
        
        if company:
            # Filter users by company if needed
            self.fields['assigned_to'].queryset = User.objects.filter(is_active=True)

    def clean(self):
        """Validate compliance data"""
        cleaned_data = super().clean()
        period_from = cleaned_data.get('period_from')
        period_to = cleaned_data.get('period_to')
        due_date = cleaned_data.get('due_date')
        
        if period_from and period_to and period_from >= period_to:
            raise ValidationError("Period to date must be after period from date.")
        
        if period_to and due_date and due_date <= period_to:
            raise ValidationError("Due date should be after the period end date.")
        
        return cleaned_data


class TaxReportForm(forms.ModelForm):
    """
    Form for Tax Report generation
    Replaces ASP.NET ServiceTaxReport.aspx functionality
    """
    
    class Meta:
        model = TaxReport
        fields = [
            'report_type', 'report_name', 'period_from', 'period_to',
            'tax_types', 'report_format'
        ]
        widgets = {
            'report_type': forms.Select(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True,
                'hx-get': '/mis/taxes/report-type-fields/',
                'hx-target': '#report-type-fields',
                'hx-trigger': 'change'
            }),
            'report_name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'placeholder': 'Custom report name',
                'required': True,
                'maxlength': 255
            }),
            'period_from': forms.DateInput(attrs={
                'type': 'date',
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
            'period_to': forms.DateInput(attrs={
                'type': 'date',
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
            'report_format': forms.Select(attrs={
                'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
                'required': True
            }),
        }

    tax_types = forms.MultipleChoiceField(
        choices=TaxConfiguration.TAX_TYPE_CHOICES,
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'w-4 h-4 text-sap-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-sap-blue-500 focus:ring-2'
        }),
        required=False,
        help_text="Select tax types to include in report (leave blank for all)"
    )

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user and not self.instance.pk:
            self.instance.generated_by = user
        if company and not self.instance.pk:
            self.instance.company = company

    def clean(self):
        """Validate report data"""
        cleaned_data = super().clean()
        period_from = cleaned_data.get('period_from')
        period_to = cleaned_data.get('period_to')
        
        if period_from and period_to and period_from >= period_to:
            raise ValidationError("Period to date must be after period from date.")
        
        # Validate report period is not too long
        if period_from and period_to:
            period_diff = (period_to - period_from).days
            if period_diff > 365:
                raise ValidationError("Report period cannot exceed 365 days.")
        
        return cleaned_data


class TaxSearchForm(forms.Form):
    """
    Form for searching and filtering tax records
    """
    
    tax_type = forms.ChoiceField(
        choices=[('', 'All Tax Types')] + TaxConfiguration.TAX_TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'hx-get': '/mis/taxes/search/',
            'hx-trigger': 'change',
            'hx-target': '#tax-results',
            'hx-include': '[name^="search"]'
        })
    )
    
    computation_status = forms.ChoiceField(
        choices=[('', 'All Status')] + TaxComputation.COMPUTATION_STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'hx-get': '/mis/taxes/search/',
            'hx-trigger': 'change',
            'hx-target': '#tax-results',
            'hx-include': '[name^="search"]'
        })
    )
    
    transaction_type = forms.ChoiceField(
        choices=[('', 'All Transaction Types')] + TaxComputation.TRANSACTION_TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'hx-get': '/mis/taxes/search/',
            'hx-trigger': 'change',
            'hx-target': '#tax-results',
            'hx-include': '[name^="search"]'
        })
    )
    
    party_name = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Party/Customer/Supplier name',
            'hx-get': '/mis/taxes/search/',
            'hx-trigger': 'keyup changed delay:300ms',
            'hx-target': '#tax-results',
            'hx-include': '[name^="search"]'
        })
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
        })
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
        })
    )
    
    amount_min = forms.DecimalField(
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Min amount',
            'step': '0.01',
            'min': '0'
        })
    )
    
    amount_max = forms.DecimalField(
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Max amount',
            'step': '0.01',
            'min': '0'
        })
    )


class TaxApprovalForm(forms.Form):
    """
    Form for tax computation approval workflow
    """
    
    APPROVAL_ACTIONS = [
        ('verify', 'Verify Computation'),
        ('approve', 'Approve for Posting'),
        ('reject', 'Reject Computation'),
        ('request_changes', 'Request Changes'),
    ]
    
    action = forms.ChoiceField(
        choices=APPROVAL_ACTIONS,
        widget=forms.RadioSelect(attrs={
            'class': 'w-4 h-4 text-sap-blue-600 bg-gray-100 border-gray-300 focus:ring-sap-blue-500 focus:ring-2'
        })
    )
    
    comments = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'block w-full px-3 py-2.5 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500',
            'rows': 3,
            'placeholder': 'Approval comments or feedback'
        })
    )

    def clean(self):
        """Validate approval form"""
        cleaned_data = super().clean()
        action = cleaned_data.get('action')
        comments = cleaned_data.get('comments')

        if action in ['reject', 'request_changes'] and not comments:
            raise ValidationError("Comments are required when rejecting or requesting changes.")

        return cleaned_data


# Tax Computation Detail Inline Formset
TaxComputationDetailsInlineFormSet = forms.inlineformset_factory(
    TaxComputation,
    TaxComputationDetails,
    form=TaxComputationDetailsForm,
    formset=TaxComputationDetailsFormSet,
    extra=3,
    min_num=1,
    max_num=100,
    can_delete=True,
    fields=[
        'line_number', 'item_code', 'item_description', 'hsn_code',
        'quantity', 'unit_price', 'discount_amount'
    ]
)