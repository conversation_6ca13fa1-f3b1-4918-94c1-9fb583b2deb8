{% extends 'core/base.html' %}
{% load static %}

{% block title %}Goods Inward Note [GIN] - New{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50" x-data="ginNewApp()">
    <!-- Header -->
    <div class="bg-gray-700 text-white px-4 py-2">
        <h1 class="text-lg font-bold">Goods Inward Note [GIN] - New</h1>
    </div>
    
    <div class="p-4">
        <!-- Search Section -->
        <div class="bg-white rounded-lg shadow-sm border mb-4">
            <div class="p-4">
                <form @submit.prevent="searchPOs" class="flex items-center gap-4">
                    <!-- Search Type Dropdown -->
                    <select x-model="searchType" 
                            @change="onSearchTypeChange"
                            class="border border-gray-300 rounded px-3 py-2 text-sm w-48 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="0">Supplier Name</option>
                        <option value="1">PO No</option>
                    </select>
                    
                    <!-- PO Number Input - Only visible for PO No -->
                    <input type="text" 
                           x-show="searchType === '1'"
                           x-model="poSearch"
                           placeholder="Enter PO number" 
                           class="border border-gray-300 rounded px-3 py-2 text-sm w-40 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    
                    <!-- Supplier Autocomplete Input - Only visible for Supplier Name -->
                    <div class="relative flex-1" x-show="searchType === '0'">
                        <input type="text" 
                               x-model="supplierSearch"
                               @input="searchSuppliers"
                               @focus="showSupplierDropdown = true"
                               @keydown.arrow-down.prevent="navigateSuppliers(1)"
                               @keydown.arrow-up.prevent="navigateSuppliers(-1)"
                               @keydown.enter.prevent="selectSupplier(highlightedSupplierIndex)"
                               @keydown.escape="showSupplierDropdown = false"
                               placeholder="Select supplier..." 
                               autocomplete="off"
                               class="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                        
                        <!-- Supplier Dropdown -->
                        <div x-show="showSupplierDropdown && suppliers.length > 0" 
                             x-transition
                             @click.outside="showSupplierDropdown = false"
                             class="absolute top-full left-0 right-0 mt-1 bg-teal-500 border border-gray-300 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
                            <template x-for="(supplier, index) in suppliers" :key="supplier.id">
                                <div @click="selectSupplierById(supplier.id)"
                                     :class="{'bg-teal-600': index === highlightedSupplierIndex, 'bg-teal-500': index !== highlightedSupplierIndex}"
                                     class="px-3 py-2 text-white text-sm cursor-pointer hover:bg-teal-600 border-b border-teal-400 last:border-b-0">
                                    <span x-text="supplier.display_text"></span>
                                </div>
                            </template>
                        </div>
                    </div>
                           
                    <!-- Search Button -->
                    <button type="submit" 
                            class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded text-sm font-medium transition-colors">
                        Search
                    </button>
                    
                    <!-- Back Button -->
                    <a href="{% url 'inventory:gin_list' %}" 
                       class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded text-sm font-medium transition-colors">
                        Back to GIN List
                    </a>
                </form>
            </div>
        </div>

        <!-- Results Table -->
        <div class="bg-white rounded-lg shadow-sm border" x-show="poRecords.length > 0 || searchPerformed">
            <div class="p-4 border-b">
                <h2 class="text-lg font-semibold text-gray-900">Eligible Purchase Orders</h2>
                <p class="text-sm text-gray-600 mt-1">Select a PO to create GIN for received materials</p>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full table-auto divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">SN</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">Fin Year</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">PO No</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">PO Date</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Supplier</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">Challan No</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">Challan Date</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">Action</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <template x-for="(po, index) in poRecords" :key="po.id">
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-3 text-sm text-gray-900 text-center" x-text="index + 1"></td>
                                <td class="px-4 py-3 text-sm text-gray-900 text-center" x-text="po.fin_year || 'N/A'"></td>
                                <td class="px-4 py-3 text-sm text-gray-900 text-center" x-text="po.po_number || 'N/A'"></td>
                                <td class="px-4 py-3 text-sm text-gray-900 text-center" x-text="po.po_date || 'N/A'"></td>
                                <td class="px-4 py-3 text-sm text-gray-900" x-text="po.supplier_name || 'UNKNOWN'"></td>
                                <td class="px-4 py-3 text-sm text-gray-900 text-center">
                                    <input type="text" 
                                           x-model="po.challan_no"
                                           placeholder="Challan No" 
                                           class="w-full border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-900 text-center">
                                    <input type="date" 
                                           x-model="po.challan_date"
                                           class="w-full border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-900 text-center">
                                    <button @click="selectPO(po)"
                                            :disabled="!po.challan_no || !po.challan_date"
                                            :class="po.challan_no && po.challan_date ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-400 cursor-not-allowed'"
                                            class="text-white px-3 py-1 rounded text-xs font-medium transition-colors">
                                        Select
                                    </button>
                                </td>
                            </tr>
                        </template>
                        
                        <!-- No Data Row -->
                        <tr x-show="poRecords.length === 0 && searchPerformed">
                            <td colspan="8" class="px-4 py-8 text-center">
                                <div class="text-gray-500">
                                    <p class="text-lg font-medium text-red-900">No eligible POs found!</p>
                                    <p class="text-sm mt-2">Try searching for a different supplier or PO number.</p>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Initial State -->
        <div x-show="!searchPerformed" class="bg-white rounded-lg shadow-sm border p-8">
            <div class="text-center text-gray-500">
                <p class="text-lg font-medium">Search for eligible Purchase Orders</p>
                <p class="text-sm mt-2">Use the search options above to find POs that are ready for goods inward</p>
            </div>
        </div>
    </div>
</div>

<script>
function ginNewApp() {
    return {
        // Search parameters
        searchType: '0',
        poSearch: '',
        supplierSearch: '',
        selectedSupplierId: null,
        
        // Supplier autocomplete
        suppliers: [],
        showSupplierDropdown: false,
        highlightedSupplierIndex: -1,
        
        // PO records
        poRecords: [],
        searchPerformed: false,
        loading: false,
        
        init() {
            // Initialize any needed data
        },
        
        onSearchTypeChange() {
            // Clear search values when switching search types
            if (this.searchType === '0') {
                // Supplier Name selected - clear PO search
                this.poSearch = '';
            } else {
                // PO No selected - clear supplier search
                this.supplierSearch = '';
                this.selectedSupplierId = null;
                this.showSupplierDropdown = false;
            }
        },
        
        async searchSuppliers() {
            if (this.supplierSearch.length < 2) {
                this.suppliers = [];
                this.showSupplierDropdown = false;
                return;
            }
            
            try {
                const response = await fetch(`/inventory/api/suppliers/?q=${encodeURIComponent(this.supplierSearch)}`);
                if (response.ok) {
                    this.suppliers = await response.json();
                    this.showSupplierDropdown = true;
                    this.highlightedSupplierIndex = -1;
                }
            } catch (error) {
                console.error('Error searching suppliers:', error);
            }
        },
        
        navigateSuppliers(direction) {
            if (this.suppliers.length === 0) return;
            
            const newIndex = this.highlightedSupplierIndex + direction;
            if (newIndex >= 0 && newIndex < this.suppliers.length) {
                this.highlightedSupplierIndex = newIndex;
            }
        },
        
        selectSupplier(index) {
            if (index >= 0 && index < this.suppliers.length) {
                this.selectSupplierById(this.suppliers[index].id);
            }
        },
        
        selectSupplierById(supplierId) {
            const supplier = this.suppliers.find(s => s.id === supplierId);
            if (supplier) {
                this.supplierSearch = supplier.display_text;
                this.selectedSupplierId = supplier.id;
                this.showSupplierDropdown = false;
                this.highlightedSupplierIndex = -1;
            }
        },
        
        async searchPOs() {
            this.loading = true;
            this.searchPerformed = true;
            
            try {
                const params = new URLSearchParams();
                params.append('search_type', this.searchType);
                if (this.poSearch) params.append('po_search', this.poSearch);
                if (this.selectedSupplierId) params.append('supplier_id', this.selectedSupplierId);
                
                const response = await fetch(`/inventory/api/eligible-pos/?${params.toString()}`);
                if (response.ok) {
                    this.poRecords = await response.json();
                    // Initialize challan fields for each PO
                    this.poRecords.forEach(po => {
                        po.challan_no = '';
                        po.challan_date = '';
                    });
                } else {
                    console.error('Failed to fetch eligible POs');
                    this.poRecords = [];
                }
            } catch (error) {
                console.error('Error searching eligible POs:', error);
                this.poRecords = [];
            } finally {
                this.loading = false;
            }
        },
        
        selectPO(po) {
            if (!po.challan_no || !po.challan_date) {
                alert('Please enter both Challan Number and Challan Date');
                return;
            }
            
            // Navigate to PO details page for GIN creation
            const url = `/inventory/gin-new/${po.id}/?challan_no=${encodeURIComponent(po.challan_no)}&challan_date=${encodeURIComponent(po.challan_date)}`;
            window.location.href = url;
        }
    }
}
</script>
{% endblock %}