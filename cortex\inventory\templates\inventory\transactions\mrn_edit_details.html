{% extends "core/base.html" %}
{% load static %}

{% block title %}Material Return Note [MRN] - New{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Material Return Note [MRN] - New</h1>
            </div>
            <div class="flex space-x-2">
                <a href="{% url 'inventory:mrn_list' %}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm">
                    Back to List
                </a>
            </div>
        </div>
    </div>

    <!-- Tab Navigation -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6">
                <button x-data="{ active: true }" 
                        @click="$dispatch('tab-change', 'item-master'); active = true"
                        class="py-3 px-1 border-b-2 font-medium text-sm"
                        :class="active ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                        id="item-master-tab">
                    Item Master
                </button>
                <button x-data="{ active: false }" 
                        @click="$dispatch('tab-change', 'selected-items'); active = true"
                        class="py-3 px-1 border-b-2 font-medium text-sm"
                        :class="active ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                        id="selected-items-tab">
                    Selected Items
                </button>
            </nav>
        </div>

        <!-- Item Master Tab -->
        <div id="item-master-content" class="p-6">
            <div class="mb-4">
                <div class="flex items-center space-x-4">
                    <div class="flex-1">
                        <select name="search_field" 
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                            <option value="item_code">Select</option>
                            <option value="item_name">Item Name</option>
                            <option value="category">Category</option>
                        </select>
                    </div>
                    <div>
                        <button type="button" 
                                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded text-sm font-medium">
                            Search
                        </button>
                    </div>
                </div>
            </div>

            <!-- Sample Item Master Table -->
            <div class="overflow-x-auto">
                <table class="w-full table-auto divide-y divide-gray-200 border border-gray-300">
                    <thead class="bg-gray-100">
                        <tr>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                                Item Code
                            </th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                                Description
                            </th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                                UOM
                            </th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                                Business Group
                            </th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-700">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- Sample items -->
                        <tr class="hover:bg-gray-50 border-b border-gray-200">
                            <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">00901-02-010</td>
                            <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">DIABOLA ROLLER</td>
                            <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">KGS</td>
                            <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">Production</td>
                            <td class="px-4 py-2 text-sm">
                                <button type="button" 
                                        class="text-blue-600 hover:text-blue-900 underline text-sm"
                                        onclick="addToSelectedItems('00901-02-010', 'DIABOLA ROLLER', 'KGS', 'Production')">
                                    Add
                                </button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 border-b border-gray-200">
                            <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">00902-03-020</td>
                            <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">STEEL PLATE</td>
                            <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">KGS</td>
                            <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">Materials</td>
                            <td class="px-4 py-2 text-sm">
                                <button type="button" 
                                        class="text-blue-600 hover:text-blue-900 underline text-sm"
                                        onclick="addToSelectedItems('00902-03-020', 'STEEL PLATE', 'KGS', 'Materials')">
                                    Add
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Selected Items Tab -->
        <div id="selected-items-content" class="p-6 hidden">
            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <div class="overflow-x-auto">
                    <table class="w-full table-auto divide-y divide-gray-200 border border-gray-300" id="selected-items-table">
                        <thead class="bg-gray-100">
                            <tr>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                                    Item Code
                                </th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                                    Description
                                </th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                                    UOM
                                </th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                                    BG Group/WONo
                                </th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                                    Work Order No
                                </th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                                    Return Qty
                                </th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-700 border-r border-gray-300">
                                    Remarks
                                </th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-700">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200" id="selected-items-body">
                            {% for item in mrn_items %}
                            <tr class="hover:bg-gray-50 border-b border-gray-200">
                                <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">
                                    {{ item.item_code }}
                                </td>
                                <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">
                                    {{ item.description }}
                                </td>
                                <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">
                                    {{ item.uom }}
                                </td>
                                <td class="px-4 py-2 border-r border-gray-300">
                                    <select name="bg_group_{{ item.detail.id }}" 
                                            class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                                        <option value="">Select/BG Group/WONo</option>
                                        <option value="BG Group" {% if item.bg_group == 'BG Group' %}selected{% endif %}>BG Group</option>
                                        <option value="WONo" {% if item.bg_group == 'WONo' %}selected{% endif %}>WONo</option>
                                    </select>
                                </td>
                                <td class="px-4 py-2 border-r border-gray-300">
                                    <input type="text" name="wo_no_{{ item.detail.id }}" value="{{ item.wo_no }}"
                                           class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                                </td>
                                <td class="px-4 py-2 border-r border-gray-300">
                                    <input type="number" name="return_qty_{{ item.detail.id }}" value="{{ item.return_qty }}" step="0.001"
                                           class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                                </td>
                                <td class="px-4 py-2 border-r border-gray-300">
                                    <input type="text" name="remarks_{{ item.detail.id }}" value="{{ item.remarks }}"
                                           class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                                </td>
                                <td class="px-4 py-2 text-sm">
                                    <button type="button" 
                                            class="text-red-600 hover:text-red-900 text-sm"
                                            onclick="removeSelectedItem(this)">
                                        Remove
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-4 pt-4 border-t border-gray-200">
                    <a href="{% url 'inventory:mrn_list' %}" 
                       class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm">
                        Update MRN
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
<script>
// Tab switching functionality
document.addEventListener('DOMContentLoaded', function() {
    // Listen for tab change events
    document.addEventListener('tab-change', function(e) {
        const tabName = e.detail;
        
        // Hide all tab contents
        document.getElementById('item-master-content').classList.add('hidden');
        document.getElementById('selected-items-content').classList.add('hidden');
        
        // Show selected tab content
        if (tabName === 'item-master') {
            document.getElementById('item-master-content').classList.remove('hidden');
        } else if (tabName === 'selected-items') {
            document.getElementById('selected-items-content').classList.remove('hidden');
        }
        
        // Update tab button states
        document.querySelectorAll('[id$="-tab"]').forEach(btn => {
            btn.classList.remove('border-blue-500', 'text-blue-600');
            btn.classList.add('border-transparent', 'text-gray-500');
        });
        
        document.getElementById(tabName + '-tab').classList.remove('border-transparent', 'text-gray-500');
        document.getElementById(tabName + '-tab').classList.add('border-blue-500', 'text-blue-600');
    });
});

// Add item to selected items
function addToSelectedItems(itemCode, description, uom, bgGroup) {
    const tbody = document.getElementById('selected-items-body');
    const rowCount = tbody.children.length;
    
    const row = document.createElement('tr');
    row.className = 'hover:bg-gray-50 border-b border-gray-200';
    row.innerHTML = `
        <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">${itemCode}</td>
        <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">${description}</td>
        <td class="px-4 py-2 text-sm text-gray-900 border-r border-gray-300">${uom}</td>
        <td class="px-4 py-2 border-r border-gray-300">
            <select name="bg_group_new_${rowCount}" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
                <option value="">Select/BG Group/WONo</option>
                <option value="BG Group">BG Group</option>
                <option value="WONo">WONo</option>
            </select>
        </td>
        <td class="px-4 py-2 border-r border-gray-300">
            <input type="text" name="wo_no_new_${rowCount}" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
        </td>
        <td class="px-4 py-2 border-r border-gray-300">
            <input type="number" name="return_qty_new_${rowCount}" step="0.001" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
        </td>
        <td class="px-4 py-2 border-r border-gray-300">
            <input type="text" name="remarks_new_${rowCount}" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm">
        </td>
        <td class="px-4 py-2 text-sm">
            <button type="button" class="text-red-600 hover:text-red-900 text-sm" onclick="removeSelectedItem(this)">Remove</button>
        </td>
    `;
    
    tbody.appendChild(row);
    
    // Switch to selected items tab
    document.dispatchEvent(new CustomEvent('tab-change', { detail: 'selected-items' }));
}

// Remove item from selected items
function removeSelectedItem(button) {
    const row = button.closest('tr');
    row.remove();
}
</script>
{% endblock %}