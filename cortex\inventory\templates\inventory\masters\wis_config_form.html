{% extends "core/base.html" %}
{% load static %}

{% block title %}{{ title }} - Inventory Management{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center space-x-4">
            <a href="{% url 'inventory:wis_config_list' %}" 
               class="text-gray-500 hover:text-gray-700">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </a>
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ title }}</h1>
                <p class="text-gray-600 mt-1">Configure automated WIS processing schedule</p>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white shadow rounded-lg">
        <form method="post" class="space-y-6 p-6">
            {% csrf_token %}
            
            <!-- Configuration Name -->
            <div>
                <label for="{{ form.config_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    Configuration Name
                </label>
                <div class="mt-1">
                    {{ form.config_name }}
                    {% if form.config_name.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.config_name.errors }}
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Time Configuration Row -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="{{ form.auto_issue_time.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Auto Issue Time
                    </label>
                    <div class="mt-1">
                        {{ form.auto_issue_time }}
                        {% if form.auto_issue_time.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.auto_issue_time.errors }}
                            </div>
                        {% endif %}
                    </div>
                    <p class="mt-1 text-sm text-gray-500">Time when automatic material issue should run daily</p>
                </div>

                <div>
                    <label for="{{ form.auto_return_time.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Auto Return Time
                    </label>
                    <div class="mt-1">
                        {{ form.auto_return_time }}
                        {% if form.auto_return_time.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.auto_return_time.errors }}
                            </div>
                        {% endif %}
                    </div>
                    <p class="mt-1 text-sm text-gray-500">Time when automatic material return should run daily</p>
                </div>
            </div>

            <!-- Processing Configuration Row -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="{{ form.batch_size.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Batch Size
                    </label>
                    <div class="mt-1">
                        {{ form.batch_size }}
                        {% if form.batch_size.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.batch_size.errors }}
                            </div>
                        {% endif %}
                    </div>
                    <p class="mt-1 text-sm text-gray-500">Number of items to process in each batch</p>
                </div>

                <div>
                    <label for="{{ form.retry_interval_minutes.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Retry Interval (Minutes)
                    </label>
                    <div class="mt-1">
                        {{ form.retry_interval_minutes }}
                        {% if form.retry_interval_minutes.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.retry_interval_minutes.errors }}
                            </div>
                        {% endif %}
                    </div>
                    <p class="mt-1 text-sm text-gray-500">Minutes to wait before retrying failed operations</p>
                </div>

                <div>
                    <label for="{{ form.max_retry_attempts.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Max Retry Attempts
                    </label>
                    <div class="mt-1">
                        {{ form.max_retry_attempts }}
                        {% if form.max_retry_attempts.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.max_retry_attempts.errors }}
                            </div>
                        {% endif %}
                    </div>
                    <p class="mt-1 text-sm text-gray-500">Maximum number of retry attempts for failed operations</p>
                </div>
            </div>

            <!-- Notification Configuration -->
            <div>
                <label for="{{ form.notification_email.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    Notification Email
                </label>
                <div class="mt-1">
                    {{ form.notification_email }}
                    {% if form.notification_email.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.notification_email.errors }}
                        </div>
                    {% endif %}
                </div>
                <p class="mt-1 text-sm text-gray-500">Email address to receive notifications about WIS processing status</p>
            </div>

            <!-- Status -->
            <div class="flex items-center">
                <div class="flex items-center h-5">
                    {{ form.is_enabled }}
                    {% if form.is_enabled.errors %}
                        <div class="ml-2 text-sm text-red-600">
                            {{ form.is_enabled.errors }}
                        </div>
                    {% endif %}
                </div>
                <div class="ml-3 text-sm">
                    <label for="{{ form.is_enabled.id_for_label }}" class="font-medium text-gray-700">
                        Enable Configuration
                    </label>
                    <p class="text-gray-500">Enable this configuration for automatic processing</p>
                </div>
            </div>

            <!-- Form Errors -->
            {% if form.non_field_errors %}
                <div class="rounded-md bg-red-50 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">
                                Please correct the following errors:
                            </h3>
                            <div class="mt-2 text-sm text-red-700">
                                <ul class="list-disc pl-5 space-y-1">
                                    {% for error in form.non_field_errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Actions -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{% url 'inventory:wis_config_list' %}" 
                   class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                    Cancel
                </a>
                <button type="submit" 
                        class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    {{ submit_text }}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}