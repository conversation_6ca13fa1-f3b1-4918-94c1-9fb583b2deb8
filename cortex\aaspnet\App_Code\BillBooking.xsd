﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="BillBooking" targetNamespace="http://tempuri.org/BillBooking.xsd" xmlns:mstns="http://tempuri.org/BillBooking.xsd" xmlns="http://tempuri.org/BillBooking.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="BillBooking" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="BillBooking" msprop:Generator_DataSetName="BillBooking">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" msprop:Generator_UserColumnName="Id" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInTable="IdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="GQNNo" msprop:Generator_UserColumnName="GQNNo" msprop:Generator_ColumnPropNameInRow="GQNNo" msprop:Generator_ColumnVarNameInTable="columnGQNNo" msprop:Generator_ColumnPropNameInTable="GQNNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="GSNNo" msprop:Generator_UserColumnName="GSNNo" msprop:Generator_ColumnPropNameInRow="GSNNo" msprop:Generator_ColumnVarNameInTable="columnGSNNo" msprop:Generator_ColumnPropNameInTable="GSNNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ItemCode" msprop:Generator_UserColumnName="ItemCode" msprop:Generator_ColumnPropNameInRow="ItemCode" msprop:Generator_ColumnVarNameInTable="columnItemCode" msprop:Generator_ColumnPropNameInTable="ItemCodeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Descr" msprop:Generator_UserColumnName="Descr" msprop:Generator_ColumnPropNameInRow="Descr" msprop:Generator_ColumnVarNameInTable="columnDescr" msprop:Generator_ColumnPropNameInTable="DescrColumn" type="xs:string" minOccurs="0" />
              <xs:element name="UOM" msprop:Generator_UserColumnName="UOM" msprop:Generator_ColumnPropNameInRow="UOM" msprop:Generator_ColumnVarNameInTable="columnUOM" msprop:Generator_ColumnPropNameInTable="UOMColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Amt" msprop:Generator_UserColumnName="Amt" msprop:Generator_ColumnPropNameInRow="Amt" msprop:Generator_ColumnVarNameInTable="columnAmt" msprop:Generator_ColumnPropNameInTable="AmtColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PFAmt" msprop:Generator_UserColumnName="PFAmt" msprop:Generator_ColumnPropNameInRow="PFAmt" msprop:Generator_ColumnVarNameInTable="columnPFAmt" msprop:Generator_ColumnPropNameInTable="PFAmtColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ExStBasicInPer" msprop:Generator_UserColumnName="ExStBasicInPer" msprop:Generator_ColumnPropNameInRow="ExStBasicInPer" msprop:Generator_ColumnVarNameInTable="columnExStBasicInPer" msprop:Generator_ColumnPropNameInTable="ExStBasicInPerColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ExStEducessInPer" msprop:Generator_UserColumnName="ExStEducessInPer" msprop:Generator_ColumnPropNameInRow="ExStEducessInPer" msprop:Generator_ColumnVarNameInTable="columnExStEducessInPer" msprop:Generator_ColumnPropNameInTable="ExStEducessInPerColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ExStShecessInPer" msprop:Generator_UserColumnName="ExStShecessInPer" msprop:Generator_ColumnPropNameInRow="ExStShecessInPer" msprop:Generator_ColumnVarNameInTable="columnExStShecessInPer" msprop:Generator_ColumnPropNameInTable="ExStShecessInPerColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ExStBasic" msprop:Generator_UserColumnName="ExStBasic" msprop:Generator_ColumnPropNameInRow="ExStBasic" msprop:Generator_ColumnVarNameInTable="columnExStBasic" msprop:Generator_ColumnPropNameInTable="ExStBasicColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ExStEducess" msprop:Generator_UserColumnName="ExStEducess" msprop:Generator_ColumnPropNameInRow="ExStEducess" msprop:Generator_ColumnVarNameInTable="columnExStEducess" msprop:Generator_ColumnPropNameInTable="ExStEducessColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ExStShecess" msprop:Generator_UserColumnName="ExStShecess" msprop:Generator_ColumnPropNameInRow="ExStShecess" msprop:Generator_ColumnVarNameInTable="columnExStShecess" msprop:Generator_ColumnPropNameInTable="ExStShecessColumn" type="xs:double" minOccurs="0" />
              <xs:element name="CustomDuty" msprop:Generator_UserColumnName="CustomDuty" msprop:Generator_ColumnPropNameInRow="CustomDuty" msprop:Generator_ColumnVarNameInTable="columnCustomDuty" msprop:Generator_ColumnPropNameInTable="CustomDutyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="VAT" msprop:Generator_UserColumnName="VAT" msprop:Generator_ColumnPropNameInRow="VAT" msprop:Generator_ColumnVarNameInTable="columnVAT" msprop:Generator_ColumnPropNameInTable="VATColumn" type="xs:double" minOccurs="0" />
              <xs:element name="CST" msprop:Generator_UserColumnName="CST" msprop:Generator_ColumnPropNameInRow="CST" msprop:Generator_ColumnVarNameInTable="columnCST" msprop:Generator_ColumnPropNameInTable="CSTColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Freight" msprop:Generator_UserColumnName="Freight" msprop:Generator_ColumnPropNameInRow="Freight" msprop:Generator_ColumnVarNameInTable="columnFreight" msprop:Generator_ColumnPropNameInTable="FreightColumn" type="xs:double" minOccurs="0" />
              <xs:element name="TarrifNo" msprop:Generator_UserColumnName="TarrifNo" msprop:Generator_ColumnPropNameInRow="TarrifNo" msprop:Generator_ColumnVarNameInTable="columnTarrifNo" msprop:Generator_ColumnPropNameInTable="TarrifNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="FinYear" msprop:Generator_UserColumnName="FinYear" msprop:Generator_ColumnPropNameInRow="FinYear" msprop:Generator_ColumnVarNameInTable="columnFinYear" msprop:Generator_ColumnPropNameInTable="FinYearColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PVEVNo" msprop:Generator_UserColumnName="PVEVNo" msprop:Generator_ColumnPropNameInRow="PVEVNo" msprop:Generator_ColumnVarNameInTable="columnPVEVNo" msprop:Generator_ColumnPropNameInTable="PVEVNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="SysDate" msprop:Generator_UserColumnName="SysDate" msprop:Generator_ColumnPropNameInRow="SysDate" msprop:Generator_ColumnVarNameInTable="columnSysDate" msprop:Generator_ColumnPropNameInTable="SysDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PONo" msprop:Generator_UserColumnName="PONo" msprop:Generator_ColumnPropNameInRow="PONo" msprop:Generator_ColumnVarNameInTable="columnPONo" msprop:Generator_ColumnPropNameInTable="PONoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PODate" msprop:Generator_UserColumnName="PODate" msprop:Generator_ColumnPropNameInRow="PODate" msprop:Generator_ColumnVarNameInTable="columnPODate" msprop:Generator_ColumnPropNameInTable="PODateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="SupplierName" msprop:Generator_UserColumnName="SupplierName" msprop:Generator_ColumnPropNameInRow="SupplierName" msprop:Generator_ColumnVarNameInTable="columnSupplierName" msprop:Generator_ColumnPropNameInTable="SupplierNameColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="SupplierId" msprop:Generator_UserColumnName="SupplierId" msprop:Generator_ColumnPropNameInRow="SupplierId" msprop:Generator_ColumnVarNameInTable="columnSupplierId" msprop:Generator_ColumnPropNameInTable="SupplierIdColumn" type="xs:string" minOccurs="0" />
              <xs:element name="OtherCharges" msprop:Generator_UserColumnName="OtherCharges" msprop:Generator_ColumnPropNameInRow="OtherCharges" msprop:Generator_ColumnVarNameInTable="columnOtherCharges" msprop:Generator_ColumnPropNameInTable="OtherChargesColumn" type="xs:double" minOccurs="0" />
              <xs:element name="OtherChaDesc" msprop:Generator_UserColumnName="OtherChaDesc" msprop:Generator_ColumnPropNameInRow="OtherChaDesc" msprop:Generator_ColumnVarNameInTable="columnOtherChaDesc" msprop:Generator_ColumnPropNameInTable="OtherChaDescColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Narration" msprop:Generator_UserColumnName="Narration" msprop:Generator_ColumnPropNameInRow="Narration" msprop:Generator_ColumnVarNameInTable="columnNarration" msprop:Generator_ColumnPropNameInTable="NarrationColumn" type="xs:string" minOccurs="0" />
              <xs:element name="DebitAmt" msprop:Generator_UserColumnName="DebitAmt" msprop:Generator_ColumnPropNameInRow="DebitAmt" msprop:Generator_ColumnVarNameInTable="columnDebitAmt" msprop:Generator_ColumnPropNameInTable="DebitAmtColumn" type="xs:double" minOccurs="0" />
              <xs:element name="DiscountType" msprop:Generator_UserColumnName="DiscountType" msprop:Generator_ColumnPropNameInRow="DiscountType" msprop:Generator_ColumnVarNameInTable="columnDiscountType" msprop:Generator_ColumnPropNameInTable="DiscountTypeColumn" type="xs:int" minOccurs="0" />
              <xs:element name="Discount" msprop:Generator_UserColumnName="Discount" msprop:Generator_ColumnPropNameInRow="Discount" msprop:Generator_ColumnVarNameInTable="columnDiscount" msprop:Generator_ColumnPropNameInTable="DiscountColumn" type="xs:double" minOccurs="0" />
              <xs:element name="BillNo" msprop:Generator_UserColumnName="BillNo" msprop:Generator_ColumnVarNameInTable="columnBillNo" msprop:Generator_ColumnPropNameInRow="BillNo" msprop:Generator_ColumnPropNameInTable="BillNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="BillDate" msprop:Generator_UserColumnName="BillDate" msprop:Generator_ColumnVarNameInTable="columnBillDate" msprop:Generator_ColumnPropNameInRow="BillDate" msprop:Generator_ColumnPropNameInTable="BillDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CENVATEntryNo" msprop:Generator_UserColumnName="CENVATEntryNo" msprop:Generator_ColumnVarNameInTable="columnCENVATEntryNo" msprop:Generator_ColumnPropNameInRow="CENVATEntryNo" msprop:Generator_ColumnPropNameInTable="CENVATEntryNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CENVATEntryDate" msprop:Generator_UserColumnName="CENVATEntryDate" msprop:Generator_ColumnVarNameInTable="columnCENVATEntryDate" msprop:Generator_ColumnPropNameInRow="CENVATEntryDate" msprop:Generator_ColumnPropNameInTable="CENVATEntryDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="AccHead" msprop:Generator_UserColumnName="AccHead" msprop:Generator_ColumnPropNameInRow="AccHead" msprop:Generator_ColumnVarNameInTable="columnAccHead" msprop:Generator_ColumnPropNameInTable="AccHeadColumn" type="xs:string" minOccurs="0" />
              <xs:element name="WODept" msprop:Generator_UserColumnName="WODept" msprop:Generator_ColumnPropNameInRow="WODept" msprop:Generator_ColumnVarNameInTable="columnWODept" msprop:Generator_ColumnPropNameInTable="WODeptColumn" type="xs:string" minOccurs="0" />
              <xs:element name="POQty" msprop:Generator_UserColumnName="POQty" msprop:Generator_ColumnVarNameInTable="columnPOQty" msprop:Generator_ColumnPropNameInRow="POQty" msprop:Generator_ColumnPropNameInTable="POQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PORate" msprop:Generator_UserColumnName="PORate" msprop:Generator_ColumnVarNameInTable="columnPORate" msprop:Generator_ColumnPropNameInRow="PORate" msprop:Generator_ColumnPropNameInTable="PORateColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Disc" msprop:Generator_UserColumnName="Disc" msprop:Generator_ColumnVarNameInTable="columnDisc" msprop:Generator_ColumnPropNameInRow="Disc" msprop:Generator_ColumnPropNameInTable="DiscColumn" type="xs:double" minOccurs="0" />
              <xs:element name="AccQty" msprop:Generator_UserColumnName="AccQty" msprop:Generator_ColumnPropNameInRow="AccQty" msprop:Generator_ColumnVarNameInTable="columnAccQty" msprop:Generator_ColumnPropNameInTable="AccQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ExciseTerm" msprop:Generator_UserColumnName="ExciseTerm" msprop:Generator_ColumnVarNameInTable="columnExciseTerm" msprop:Generator_ColumnPropNameInRow="ExciseTerm" msprop:Generator_ColumnPropNameInTable="ExciseTermColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PFTerm" msprop:Generator_UserColumnName="PFTerm" msprop:Generator_ColumnVarNameInTable="columnPFTerm" msprop:Generator_ColumnPropNameInRow="PFTerm" msprop:Generator_ColumnPropNameInTable="PFTermColumn" type="xs:string" minOccurs="0" />
              <xs:element name="VATTerm" msprop:Generator_UserColumnName="VATTerm" msprop:Generator_ColumnPropNameInRow="VATTerm" msprop:Generator_ColumnVarNameInTable="columnVATTerm" msprop:Generator_ColumnPropNameInTable="VATTermColumn" type="xs:string" minOccurs="0" />
              <xs:element name="IsVATCST" msprop:Generator_UserColumnName="IsVATCST" msprop:Generator_ColumnVarNameInTable="columnIsVATCST" msprop:Generator_ColumnPropNameInRow="IsVATCST" msprop:Generator_ColumnPropNameInTable="IsVATCSTColumn" type="xs:string" minOccurs="0" />
              <xs:element name="DebitType" msprop:Generator_UserColumnName="DebitType" msprop:Generator_ColumnVarNameInTable="columnDebitType" msprop:Generator_ColumnPropNameInRow="DebitType" msprop:Generator_ColumnPropNameInTable="DebitTypeColumn" type="xs:int" minOccurs="0" />
              <xs:element name="DebitValue" msprop:Generator_UserColumnName="DebitValue" msprop:Generator_ColumnVarNameInTable="columnDebitValue" msprop:Generator_ColumnPropNameInRow="DebitValue" msprop:Generator_ColumnPropNameInTable="DebitValueColumn" type="xs:double" minOccurs="0" />
              <xs:element name="BasicAmt" msprop:Generator_UserColumnName="BasicAmt" msprop:Generator_ColumnPropNameInRow="BasicAmt" msprop:Generator_ColumnVarNameInTable="columnBasicAmt" msprop:Generator_ColumnPropNameInTable="BasicAmtColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PFid" msprop:Generator_UserColumnName="PFid" msprop:Generator_ColumnPropNameInRow="PFid" msprop:Generator_ColumnVarNameInTable="columnPFid" msprop:Generator_ColumnPropNameInTable="PFidColumn" type="xs:int" minOccurs="0" />
              <xs:element name="ExciseId" msprop:Generator_UserColumnName="ExciseId" msprop:Generator_ColumnPropNameInRow="ExciseId" msprop:Generator_ColumnVarNameInTable="columnExciseId" msprop:Generator_ColumnPropNameInTable="ExciseIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="VATCSTid" msprop:Generator_UserColumnName="VATCSTid" msprop:Generator_ColumnPropNameInRow="VATCSTid" msprop:Generator_ColumnVarNameInTable="columnVATCSTid" msprop:Generator_ColumnPropNameInTable="VATCSTidColumn" type="xs:int" minOccurs="0" />
              <xs:element name="BCDOpt" msprop:Generator_UserColumnName="BCDOpt" msprop:Generator_ColumnVarNameInTable="columnBCDOpt" msprop:Generator_ColumnPropNameInRow="BCDOpt" msprop:Generator_ColumnPropNameInTable="BCDOptColumn" type="xs:string" minOccurs="0" />
              <xs:element name="BCD" msprop:Generator_UserColumnName="BCD" msprop:Generator_ColumnVarNameInTable="columnBCD" msprop:Generator_ColumnPropNameInRow="BCD" msprop:Generator_ColumnPropNameInTable="BCDColumn" type="xs:double" minOccurs="0" />
              <xs:element name="BCDValue" msprop:Generator_UserColumnName="BCDValue" msprop:Generator_ColumnVarNameInTable="columnBCDValue" msprop:Generator_ColumnPropNameInRow="BCDValue" msprop:Generator_ColumnPropNameInTable="BCDValueColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ValueForCVD" msprop:Generator_UserColumnName="ValueForCVD" msprop:Generator_ColumnVarNameInTable="columnValueForCVD" msprop:Generator_ColumnPropNameInRow="ValueForCVD" msprop:Generator_ColumnPropNameInTable="ValueForCVDColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ValueForEdCessCD" msprop:Generator_UserColumnName="ValueForEdCessCD" msprop:Generator_ColumnVarNameInTable="columnValueForEdCessCD" msprop:Generator_ColumnPropNameInRow="ValueForEdCessCD" msprop:Generator_ColumnPropNameInTable="ValueForEdCessCDColumn" type="xs:double" minOccurs="0" />
              <xs:element name="EdCessOnCDOpt" msprop:Generator_UserColumnName="EdCessOnCDOpt" msprop:Generator_ColumnVarNameInTable="columnEdCessOnCDOpt" msprop:Generator_ColumnPropNameInRow="EdCessOnCDOpt" msprop:Generator_ColumnPropNameInTable="EdCessOnCDOptColumn" type="xs:string" minOccurs="0" />
              <xs:element name="EdCessOnCD" msprop:Generator_UserColumnName="EdCessOnCD" msprop:Generator_ColumnVarNameInTable="columnEdCessOnCD" msprop:Generator_ColumnPropNameInRow="EdCessOnCD" msprop:Generator_ColumnPropNameInTable="EdCessOnCDColumn" type="xs:double" minOccurs="0" />
              <xs:element name="EdCessOnCDValue" msprop:Generator_UserColumnName="EdCessOnCDValue" msprop:Generator_ColumnVarNameInTable="columnEdCessOnCDValue" msprop:Generator_ColumnPropNameInRow="EdCessOnCDValue" msprop:Generator_ColumnPropNameInTable="EdCessOnCDValueColumn" type="xs:double" minOccurs="0" />
              <xs:element name="SHEDCessOpt" msprop:Generator_UserColumnName="SHEDCessOpt" msprop:Generator_ColumnVarNameInTable="columnSHEDCessOpt" msprop:Generator_ColumnPropNameInRow="SHEDCessOpt" msprop:Generator_ColumnPropNameInTable="SHEDCessOptColumn" type="xs:string" minOccurs="0" />
              <xs:element name="SHEDCess" msprop:Generator_UserColumnName="SHEDCess" msprop:Generator_ColumnVarNameInTable="columnSHEDCess" msprop:Generator_ColumnPropNameInRow="SHEDCess" msprop:Generator_ColumnPropNameInTable="SHEDCessColumn" type="xs:double" minOccurs="0" />
              <xs:element name="SHEDCessValue" msprop:Generator_UserColumnName="SHEDCessValue" msprop:Generator_ColumnVarNameInTable="columnSHEDCessValue" msprop:Generator_ColumnPropNameInRow="SHEDCessValue" msprop:Generator_ColumnPropNameInTable="SHEDCessValueColumn" type="xs:double" minOccurs="0" />
              <xs:element name="TotDuty" msprop:Generator_UserColumnName="TotDuty" msprop:Generator_ColumnVarNameInTable="columnTotDuty" msprop:Generator_ColumnPropNameInRow="TotDuty" msprop:Generator_ColumnPropNameInTable="TotDutyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="TotDutyEDSHED" msprop:Generator_UserColumnName="TotDutyEDSHED" msprop:Generator_ColumnVarNameInTable="columnTotDutyEDSHED" msprop:Generator_ColumnPropNameInRow="TotDutyEDSHED" msprop:Generator_ColumnPropNameInTable="TotDutyEDSHEDColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Insurance" msprop:Generator_UserColumnName="Insurance" msprop:Generator_ColumnVarNameInTable="columnInsurance" msprop:Generator_ColumnPropNameInRow="Insurance" msprop:Generator_ColumnPropNameInTable="InsuranceColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ValueWithDuty" msprop:Generator_UserColumnName="ValueWithDuty" msprop:Generator_ColumnVarNameInTable="columnValueWithDuty" msprop:Generator_ColumnPropNameInRow="ValueWithDuty" msprop:Generator_ColumnPropNameInTable="ValueWithDutyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="SectionNo" msprop:Generator_UserColumnName="SectionNo" msprop:Generator_ColumnPropNameInRow="SectionNo" msprop:Generator_ColumnVarNameInTable="columnSectionNo" msprop:Generator_ColumnPropNameInTable="SectionNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="TDSPerCentage" msprop:Generator_UserColumnName="TDSPerCentage" msprop:Generator_ColumnPropNameInRow="TDSPerCentage" msprop:Generator_ColumnVarNameInTable="columnTDSPerCentage" msprop:Generator_ColumnPropNameInTable="TDSPerCentageColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PaymentRange" msprop:Generator_UserColumnName="PaymentRange" msprop:Generator_ColumnPropNameInRow="PaymentRange" msprop:Generator_ColumnVarNameInTable="columnPaymentRange" msprop:Generator_ColumnPropNameInTable="PaymentRangeColumn" type="xs:double" minOccurs="0" />
              <xs:element name="TDSCode" msprop:Generator_UserColumnName="TDSCode" msprop:Generator_ColumnPropNameInRow="TDSCode" msprop:Generator_ColumnVarNameInTable="columnTDSCode" msprop:Generator_ColumnPropNameInTable="TDSCodeColumn" type="xs:int" minOccurs="0" />
              <xs:element name="BookedBillTotal" msprop:Generator_UserColumnName="BookedBillTotal" msprop:Generator_ColumnPropNameInRow="BookedBillTotal" msprop:Generator_ColumnVarNameInTable="columnBookedBillTotal" msprop:Generator_ColumnPropNameInTable="BookedBillTotalColumn" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="DataTable2" msprop:Generator_UserTableName="DataTable2" msprop:Generator_RowDeletedName="DataTable2RowDeleted" msprop:Generator_RowChangedName="DataTable2RowChanged" msprop:Generator_RowClassName="DataTable2Row" msprop:Generator_RowChangingName="DataTable2RowChanging" msprop:Generator_RowEvArgName="DataTable2RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable2RowChangeEventHandler" msprop:Generator_TableClassName="DataTable2DataTable" msprop:Generator_TableVarName="tableDataTable2" msprop:Generator_RowDeletingName="DataTable2RowDeleting" msprop:Generator_TablePropName="DataTable2">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Basic" msprop:Generator_UserColumnName="Basic" msprop:Generator_ColumnPropNameInRow="Basic" msprop:Generator_ColumnVarNameInTable="columnBasic" msprop:Generator_ColumnPropNameInTable="BasicColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PF" msprop:Generator_UserColumnName="PF" msprop:Generator_ColumnPropNameInRow="PF" msprop:Generator_ColumnVarNameInTable="columnPF" msprop:Generator_ColumnPropNameInTable="PFColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PFAmt" msprop:Generator_UserColumnName="PFAmt" msprop:Generator_ColumnPropNameInRow="PFAmt" msprop:Generator_ColumnVarNameInTable="columnPFAmt" msprop:Generator_ColumnPropNameInTable="PFAmtColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ExSerTax" msprop:Generator_UserColumnName="ExSerTax" msprop:Generator_ColumnPropNameInRow="ExSerTax" msprop:Generator_ColumnVarNameInTable="columnExSerTax" msprop:Generator_ColumnPropNameInTable="ExSerTaxColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ExSerAmt" msprop:Generator_UserColumnName="ExSerAmt" msprop:Generator_ColumnPropNameInRow="ExSerAmt" msprop:Generator_ColumnVarNameInTable="columnExSerAmt" msprop:Generator_ColumnPropNameInTable="ExSerAmtColumn" type="xs:double" minOccurs="0" />
              <xs:element name="EDU" msprop:Generator_UserColumnName="EDU" msprop:Generator_ColumnPropNameInRow="EDU" msprop:Generator_ColumnVarNameInTable="columnEDU" msprop:Generator_ColumnPropNameInTable="EDUColumn" type="xs:double" minOccurs="0" />
              <xs:element name="SHE" msprop:Generator_UserColumnName="SHE" msprop:Generator_ColumnPropNameInRow="SHE" msprop:Generator_ColumnVarNameInTable="columnSHE" msprop:Generator_ColumnPropNameInTable="SHEColumn" type="xs:double" minOccurs="0" />
              <xs:element name="VATCST" msprop:Generator_UserColumnName="VATCST" msprop:Generator_ColumnPropNameInRow="VATCST" msprop:Generator_ColumnVarNameInTable="columnVATCST" msprop:Generator_ColumnPropNameInTable="VATCSTColumn" type="xs:string" minOccurs="0" />
              <xs:element name="VATCSTAmt" msprop:Generator_UserColumnName="VATCSTAmt" msprop:Generator_ColumnPropNameInRow="VATCSTAmt" msprop:Generator_ColumnVarNameInTable="columnVATCSTAmt" msprop:Generator_ColumnPropNameInTable="VATCSTAmtColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Freight" msprop:Generator_UserColumnName="Freight" msprop:Generator_ColumnPropNameInRow="Freight" msprop:Generator_ColumnVarNameInTable="columnFreight" msprop:Generator_ColumnPropNameInTable="FreightColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Total" msprop:Generator_UserColumnName="Total" msprop:Generator_ColumnPropNameInRow="Total" msprop:Generator_ColumnVarNameInTable="columnTotal" msprop:Generator_ColumnPropNameInTable="TotalColumn" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>