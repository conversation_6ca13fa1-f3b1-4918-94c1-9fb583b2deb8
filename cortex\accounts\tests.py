# accounts/tests.py
# Main test module for accounts app
# Import comprehensive test suites from tests package


# Import all test cases from the comprehensive test suite
from .tests.test_taxation import (
    TaxationModelsTestCase,
    TaxCalculationEngineTestCase,
    VATRegisterViewsTestCase,
    ExciseDutyViewsTestCase,
    TaxCalculatorViewsTestCase,
    TaxationFormsTestCase,
    TaxationUtilityFunctionsTestCase,
    TaxationEdgeCasesTestCase,
    TaxationIntegrationTestCase,
    TaxationPerformanceTestCase,
)

# Re-export all test cases for discovery
__all__ = [
    'TaxationModelsTestCase',
    'TaxCalculationEngineTestCase', 
    'VATRegisterViewsTestCase',
    'ExciseDutyViewsTestCase',
    'TaxCalculatorViewsTestCase',
    'TaxationFormsTestCase',
    'TaxationUtilityFunctionsTestCase',
    'TaxationEdgeCasesTestCase',
    'TaxationIntegrationTestCase',
    'TaxationPerformanceTestCase',
]
