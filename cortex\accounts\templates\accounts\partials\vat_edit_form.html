<!-- accounts/partials/vat_edit_form.html -->
<!-- HTMX partial for VAT edit form - SAP S/4HANA inspired -->

{% load static %}

<div class="bg-sap-red-50 border border-sap-red-200 rounded-lg p-4 mb-4" id="vat-edit-form-{{ vat.id }}">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-sap-red-100 rounded-lg flex items-center justify-center">
                <i data-lucide="edit" class="w-4 h-4 text-sap-red-600"></i>
            </div>
            <div>
                <h4 class="text-lg font-medium text-sap-gray-800">Edit VAT</h4>
                <p class="text-sm text-sap-gray-600">Update VAT information (ID: {{ vat.id }})</p>
            </div>
        </div>
        <button type="button" 
                hx-get="{% url 'accounts:vat_list' %}"
                hx-target="#vat-table"
                hx-swap="outerHTML"
                class="inline-flex items-center px-3 py-1.5 border border-sap-gray-300 rounded text-xs font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
            <i data-lucide="x" class="w-3 h-3 mr-1"></i>
            Cancel
        </button>
    </div>
    
    <form hx-put="{% url 'accounts:vat_edit' vat.id %}" 
          hx-target="#vat-edit-form-{{ vat.id }}" 
          hx-swap="outerHTML"
          hx-trigger="submit"
          class="space-y-4">
        {% csrf_token %}
        
        <div>
            <label for="{{ form.percentage.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                VAT Percentage <span class="text-red-500">*</span>
            </label>
            {{ form.percentage }}
            {% if form.percentage.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.percentage.errors.0 }}</p>
            {% endif %}
        </div>
        
        <div class="flex justify-end space-x-3 pt-4 border-t border-sap-red-200">
            <button type="button" 
                    hx-get="{% url 'accounts:vat_list' %}"
                    hx-target="#vat-table"
                    hx-swap="outerHTML"
                    class="px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                Cancel
            </button>
            <button type="submit" 
                    class="px-4 py-2 bg-sap-red-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-sap-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-red-500">
                <i data-lucide="save" class="w-4 h-4 mr-2 inline"></i>
                Update VAT
            </button>
        </div>
    </form>
</div>