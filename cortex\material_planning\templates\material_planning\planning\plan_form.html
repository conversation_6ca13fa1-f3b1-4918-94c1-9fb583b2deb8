{% extends 'material_planning/base.html' %}

{% block title %}Edit Material Plan{% endblock %}

{% block content %}
<div class="space-y-6 animate-fade-in">
    <!-- Header Section -->
    <div class="sap-card">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-sap-gray-900 mb-2">Edit Material Plan</h1>
                <p class="text-sap-gray-600">Update the plan details below</p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'material_planning:plan_detail' object.pk %}" class="sap-button-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Plan
                </a>
            </div>
        </div>
    </div>

    <!-- Form -->
    <form method="post" class="space-y-6">
        {% csrf_token %}
        
        <!-- Display any form errors -->
        {% if form.errors %}
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-circle text-red-400"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                        <div class="mt-2 text-sm text-red-700">
                            <ul class="list-disc list-inside space-y-1">
                                {% for field, errors in form.errors.items %}
                                    {% for error in errors %}
                                        <li>{{ field|title }}: {{ error }}</li>
                                    {% endfor %}
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        <!-- Basic Information -->
        <div class="sap-card">
            <h2 class="text-xl font-semibold text-sap-gray-900 mb-4">Basic Information</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="{{ form.plno.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">Plan Number</label>
                    {{ form.plno }}
                    {% if form.plno.help_text %}
                        <p class="mt-1 text-sm text-sap-gray-500">{{ form.plno.help_text }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.wono.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">Work Order Number</label>
                    {{ form.wono }}
                    {% if form.wono.help_text %}
                        <p class="mt-1 text-sm text-sap-gray-500">{{ form.wono.help_text }}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Read-only Information -->
        <div class="sap-card">
            <h2 class="text-xl font-semibold text-sap-gray-900 mb-4">System Information (Read-only)</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-sap-gray-700 mb-1">Company</label>
                    <p class="mt-1 p-3 bg-gray-50 border border-gray-200 rounded-md text-sm text-sap-gray-900">
                        {{ object.company.companyname|default:"N/A" }}
                    </p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-sap-gray-700 mb-1">Financial Year</label>
                    <p class="mt-1 p-3 bg-gray-50 border border-gray-200 rounded-md text-sm text-sap-gray-900">
                        {{ object.finyear.finyear|default:"N/A" }}
                    </p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-sap-gray-700 mb-1">Created Date</label>
                    <p class="mt-1 p-3 bg-gray-50 border border-gray-200 rounded-md text-sm text-sap-gray-900">
                        {{ object.sysdate|default:"N/A" }}
                    </p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-sap-gray-700 mb-1">Created By</label>
                    <p class="mt-1 p-3 bg-gray-50 border border-gray-200 rounded-md text-sm text-sap-gray-900">
                        {{ object.session.username|default:"N/A" }}
                    </p>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="sap-card">
            <div class="flex justify-end space-x-4">
                <a href="{% url 'material_planning:plan_detail' object.pk %}" class="sap-button-secondary">
                    <i class="fas fa-times mr-2"></i>
                    Cancel
                </a>
                <button type="submit" class="sap-button-primary">
                    <i class="fas fa-save mr-2"></i>
                    Save Changes
                </button>
            </div>
        </div>
    </form>
</div>

<script>
// Add any required form validation or interactivity here
document.addEventListener('DOMContentLoaded', function() {
    // Form validation logic can be added here
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            // Add validation if needed
        });
    }
});
</script>
{% endblock %}