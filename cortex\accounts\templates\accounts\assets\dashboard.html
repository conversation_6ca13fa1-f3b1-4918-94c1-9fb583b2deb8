<!-- accounts/templates/accounts/assets/dashboard.html -->
<!-- Asset Management Dashboard -->
<!-- Task Group 8: Asset Management Dashboard (Task 8.1) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Asset Management - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-teal-600 to-sap-teal-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="building" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Asset Management</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Comprehensive asset lifecycle management and tracking</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to Accounts
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Asset Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Assets -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="building" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Assets</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ total_assets_count|default:0 }}</p>
                    <p class="text-xs text-sap-blue-600 mt-1">₹{{ total_asset_value|default:0|floatformat:2 }}</p>
                </div>
            </div>
        </div>
        
        <!-- Active Assets -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="check-circle" class="w-6 h-6 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Active Assets</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ active_assets_count|default:0 }}</p>
                    <p class="text-xs text-sap-green-600 mt-1">Operational</p>
                </div>
            </div>
        </div>
        
        <!-- Under Maintenance -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="wrench" class="w-6 h-6 text-sap-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Under Maintenance</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ under_maintenance_count|default:0 }}</p>
                    <p class="text-xs text-sap-orange-600 mt-1">Needs attention</p>
                </div>
            </div>
        </div>
        
        <!-- Total Depreciation -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-red-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="trending-down" class="w-6 h-6 text-sap-red-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Depreciation</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ total_depreciation|default:0|floatformat:2 }}</p>
                    <p class="text-xs text-sap-red-600 mt-1">Accumulated</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Asset Management Categories -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        
        <!-- Fixed Assets Management -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="building" class="w-5 h-5 mr-2 text-sap-teal-600"></i>
                    Fixed Assets Management
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:asset_create' %}" 
                       class="flex items-center w-full bg-sap-teal-600 hover:bg-sap-teal-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Add New Asset
                    </a>
                    <a href="{% url 'accounts:asset_list' %}" 
                       class="flex items-center w-full bg-sap-teal-100 hover:bg-sap-teal-200 text-sap-teal-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                        View All Assets
                    </a>
                    <button type="button" onclick="generateAssetReport()" 
                            class="flex items-center w-full bg-sap-gray-100 hover:bg-sap-gray-200 text-sap-gray-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="file-text" class="w-4 h-4 mr-2"></i>
                        Asset Summary Report
                    </button>
                </div>
            </div>
        </div>

        <!-- Current Assets Management -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="dollar-sign" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                    Current Assets Management
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:current_assets_create' %}" 
                       class="flex items-center w-full bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Add Current Asset Entry
                    </a>
                    <a href="{% url 'accounts:current_assets_list' %}" 
                       class="flex items-center w-full bg-sap-green-100 hover:bg-sap-green-200 text-sap-green-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                        View Current Assets
                    </a>
                    <button type="button" onclick="generateCurrentAssetReport()" 
                            class="flex items-center w-full bg-sap-gray-100 hover:bg-sap-gray-200 text-sap-gray-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="pie-chart" class="w-4 h-4 mr-2"></i>
                        Liquidity Analysis
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Asset Register and Reports -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        
        <!-- Asset Register -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="document-text" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                    Asset Register
                </h3>
                <p class="text-sm text-sap-gray-600 mt-1">{{ asset_register_entries|default:0 }} entries</p>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:asset_register_create' %}" 
                       class="flex items-center w-full bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        New Register Entry
                    </a>
                    <a href="{% url 'accounts:asset_register_list' %}" 
                       class="flex items-center w-full bg-sap-blue-100 hover:bg-sap-blue-200 text-sap-blue-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                        View Register
                    </a>
                </div>
            </div>
        </div>

        <!-- Depreciation Reports -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="trending-down" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                    Depreciation Analysis
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:asset_depreciation_report' %}" 
                       class="flex items-center w-full bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="bar-chart" class="w-4 h-4 mr-2"></i>
                        Depreciation Report
                    </a>
                    <button type="button" onclick="generateDepreciationSchedule()" 
                            class="flex items-center w-full bg-sap-purple-100 hover:bg-sap-purple-200 text-sap-purple-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="calendar" class="w-4 h-4 mr-2"></i>
                        Depreciation Schedule
                    </button>
                </div>
            </div>
        </div>

        <!-- Maintenance & Warranty -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="wrench" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                    Maintenance & Warranty
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:asset_maintenance_report' %}" 
                       class="flex items-center w-full bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="shield" class="w-4 h-4 mr-2"></i>
                        Maintenance Report
                    </a>
                    <button type="button" onclick="showExpiringWarranties()" 
                            class="flex items-center w-full bg-sap-orange-100 hover:bg-sap-orange-200 text-sap-orange-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="alert-triangle" class="w-4 h-4 mr-2"></i>
                        Expiring Warranties ({{ warranty_expiring_soon|default:0 }})
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Status Overview -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        
        <!-- Asset Status Distribution -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="pie-chart" class="w-5 h-5 mr-2 text-sap-teal-600"></i>
                    Asset Status Distribution
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-sap-green-500 rounded-full mr-3"></div>
                            <span class="text-sm text-sap-gray-700">Active Assets</span>
                        </div>
                        <div class="text-right">
                            <span class="text-sm font-medium text-sap-gray-900">{{ active_assets_count|default:0 }}</span>
                            <span class="text-xs text-sap-gray-500 ml-2">
                                {% if total_assets_count > 0 %}
                                    ({{ active_assets_count|default:0|floatformat:0 }}%)
                                {% else %}
                                    (0%)
                                {% endif %}
                            </span>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-sap-orange-500 rounded-full mr-3"></div>
                            <span class="text-sm text-sap-gray-700">Under Maintenance</span>
                        </div>
                        <div class="text-right">
                            <span class="text-sm font-medium text-sap-gray-900">{{ under_maintenance_count|default:0 }}</span>
                            <span class="text-xs text-sap-gray-500 ml-2">
                                {% if total_assets_count > 0 %}
                                    ({{ under_maintenance_count|default:0|floatformat:0 }}%)
                                {% else %}
                                    (0%)
                                {% endif %}
                            </span>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-sap-red-500 rounded-full mr-3"></div>
                            <span class="text-sm text-sap-gray-700">Disposed Assets</span>
                        </div>
                        <div class="text-right">
                            <span class="text-sm font-medium text-sap-gray-900">{{ disposed_assets_count|default:0 }}</span>
                            <span class="text-xs text-sap-gray-500 ml-2">
                                {% if total_assets_count > 0 %}
                                    ({{ disposed_assets_count|default:0|floatformat:0 }}%)
                                {% else %}
                                    (0%)
                                {% endif %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Critical Alerts -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="alert-triangle" class="w-5 h-5 mr-2 text-sap-red-600"></i>
                    Critical Alerts
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    {% if warranty_expiring_soon > 0 %}
                    <div class="flex items-center p-3 bg-sap-yellow-50 border border-sap-yellow-200 rounded-lg">
                        <i data-lucide="shield-alert" class="w-5 h-5 text-sap-yellow-600 mr-3"></i>
                        <div>
                            <p class="text-sm font-medium text-sap-yellow-800">Warranties Expiring Soon</p>
                            <p class="text-xs text-sap-yellow-600">{{ warranty_expiring_soon }} asset(s) warranty expiring in 30 days</p>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if insurance_expiring_soon > 0 %}
                    <div class="flex items-center p-3 bg-sap-orange-50 border border-sap-orange-200 rounded-lg">
                        <i data-lucide="umbrella" class="w-5 h-5 text-sap-orange-600 mr-3"></i>
                        <div>
                            <p class="text-sm font-medium text-sap-orange-800">Insurance Expiring Soon</p>
                            <p class="text-xs text-sap-orange-600">{{ insurance_expiring_soon }} asset(s) insurance expiring in 30 days</p>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if under_maintenance_count > 0 %}
                    <div class="flex items-center p-3 bg-sap-red-50 border border-sap-red-200 rounded-lg">
                        <i data-lucide="wrench" class="w-5 h-5 text-sap-red-600 mr-3"></i>
                        <div>
                            <p class="text-sm font-medium text-sap-red-800">Assets Under Maintenance</p>
                            <p class="text-xs text-sap-red-600">{{ under_maintenance_count }} asset(s) currently under maintenance</p>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if warranty_expiring_soon == 0 and insurance_expiring_soon == 0 and under_maintenance_count == 0 %}
                    <div class="flex items-center p-3 bg-sap-green-50 border border-sap-green-200 rounded-lg">
                        <i data-lucide="check-circle" class="w-5 h-5 text-sap-green-600 mr-3"></i>
                        <div>
                            <p class="text-sm font-medium text-sap-green-800">All Good!</p>
                            <p class="text-xs text-sap-green-600">No critical alerts at this time</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function generateAssetReport() {
    alert('Asset summary report generation functionality would be implemented here.');
}

function generateCurrentAssetReport() {
    alert('Current asset liquidity analysis functionality would be implemented here.');
}

function generateDepreciationSchedule() {
    alert('Depreciation schedule generation functionality would be implemented here.');
}

function showExpiringWarranties() {
    window.location.href = '{% url "accounts:asset_maintenance_report" %}';
}

// Initialize lucide icons on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}