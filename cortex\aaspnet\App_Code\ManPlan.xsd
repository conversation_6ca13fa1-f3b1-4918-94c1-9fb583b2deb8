﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="ManPlan" targetNamespace="http://tempuri.org/ManPlan.xsd" xmlns:mstns="http://tempuri.org/ManPlan.xsd" xmlns="http://tempuri.org/ManPlan.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="ManPlan" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="ManPlan" msprop:Generator_DataSetName="ManPlan">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="EmployeeName" msprop:Generator_UserColumnName="EmployeeName" msprop:Generator_ColumnVarNameInTable="columnEmployeeName" msprop:Generator_ColumnPropNameInRow="EmployeeName" msprop:Generator_ColumnPropNameInTable="EmployeeNameColumn" type="xs:string" minOccurs="0" />
              <xs:element name="EmpId" msprop:Generator_UserColumnName="EmpId" msprop:Generator_ColumnVarNameInTable="columnEmpId" msprop:Generator_ColumnPropNameInRow="EmpId" msprop:Generator_ColumnPropNameInTable="EmpIdColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Designation" msprop:Generator_UserColumnName="Designation" msprop:Generator_ColumnVarNameInTable="columnDesignation" msprop:Generator_ColumnPropNameInRow="Designation" msprop:Generator_ColumnPropNameInTable="DesignationColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Date" msprop:Generator_UserColumnName="Date" msprop:Generator_ColumnVarNameInTable="columnDate" msprop:Generator_ColumnPropNameInRow="Date" msprop:Generator_ColumnPropNameInTable="DateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="WONo" msprop:Generator_UserColumnName="WONo" msprop:Generator_ColumnVarNameInTable="columnWONo" msprop:Generator_ColumnPropNameInRow="WONo" msprop:Generator_ColumnPropNameInTable="WONoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Dept" msprop:Generator_UserColumnName="Dept" msprop:Generator_ColumnVarNameInTable="columnDept" msprop:Generator_ColumnPropNameInRow="Dept" msprop:Generator_ColumnPropNameInTable="DeptColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Types" msprop:Generator_UserColumnName="Types" msprop:Generator_ColumnVarNameInTable="columnTypes" msprop:Generator_ColumnPropNameInRow="Types" msprop:Generator_ColumnPropNameInTable="TypesColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Id" msprop:Generator_UserColumnName="Id" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInTable="IdColumn" type="xs:int" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="DataTable2" msprop:Generator_UserTableName="DataTable2" msprop:Generator_RowDeletedName="DataTable2RowDeleted" msprop:Generator_TableClassName="DataTable2DataTable" msprop:Generator_RowChangedName="DataTable2RowChanged" msprop:Generator_RowClassName="DataTable2Row" msprop:Generator_RowChangingName="DataTable2RowChanging" msprop:Generator_RowEvArgName="DataTable2RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable2RowChangeEventHandler" msprop:Generator_TablePropName="DataTable2" msprop:Generator_TableVarName="tableDataTable2" msprop:Generator_RowDeletingName="DataTable2RowDeleting">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="EquipNo" msprop:Generator_UserColumnName="EquipNo" msprop:Generator_ColumnPropNameInRow="EquipNo" msprop:Generator_ColumnVarNameInTable="columnEquipNo" msprop:Generator_ColumnPropNameInTable="EquipNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Description" msprop:Generator_UserColumnName="Description" msprop:Generator_ColumnPropNameInRow="Description" msprop:Generator_ColumnVarNameInTable="columnDescription" msprop:Generator_ColumnPropNameInTable="DescriptionColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Cate" msprop:Generator_UserColumnName="Cate" msprop:Generator_ColumnPropNameInRow="Cate" msprop:Generator_ColumnVarNameInTable="columnCate" msprop:Generator_ColumnPropNameInTable="CateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="SubCate" msprop:Generator_UserColumnName="SubCate" msprop:Generator_ColumnPropNameInRow="SubCate" msprop:Generator_ColumnVarNameInTable="columnSubCate" msprop:Generator_ColumnPropNameInTable="SubCateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Planned" msprop:Generator_UserColumnName="Planned" msprop:Generator_ColumnPropNameInRow="Planned" msprop:Generator_ColumnVarNameInTable="columnPlanned" msprop:Generator_ColumnPropNameInTable="PlannedColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Actual" msprop:Generator_UserColumnName="Actual" msprop:Generator_ColumnPropNameInRow="Actual" msprop:Generator_ColumnVarNameInTable="columnActual" msprop:Generator_ColumnPropNameInTable="ActualColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Hrs" msprop:Generator_UserColumnName="Hrs" msprop:Generator_ColumnPropNameInRow="Hrs" msprop:Generator_ColumnVarNameInTable="columnHrs" msprop:Generator_ColumnPropNameInTable="HrsColumn" type="xs:double" minOccurs="0" />
              <xs:element name="MId" msprop:Generator_UserColumnName="MId" msprop:Generator_ColumnPropNameInRow="MId" msprop:Generator_ColumnVarNameInTable="columnMId" msprop:Generator_ColumnPropNameInTable="MIdColumn" type="xs:int" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
  <xs:annotation>
    <xs:appinfo>
      <msdata:Relationship name="DataTable1_DataTable2" msdata:parent="DataTable1" msdata:child="DataTable2" msdata:parentkey="Id" msdata:childkey="MId" msprop:Generator_UserRelationName="DataTable1_DataTable2" msprop:Generator_RelationVarName="relationDataTable1_DataTable2" msprop:Generator_UserChildTable="DataTable2" msprop:Generator_UserParentTable="DataTable1" msprop:Generator_ParentPropName="DataTable1Row" msprop:Generator_ChildPropName="GetDataTable2Rows" />
    </xs:appinfo>
  </xs:annotation>
</xs:schema>