{% extends 'material_planning/base.html' %}

{% block title %}Process Categories - Debug{% endblock %}

{% block extra_head %}
<!-- FontAwesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
.debug-info {
    background: #f0f0f0;
    padding: 10px;
    margin: 10px 0;
    border-radius: 5px;
    font-family: monospace;
}
</style>
{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto p-6" x-data="debugManager()">
    <h1 class="text-2xl font-bold mb-4">Process Categories - Debug Mode</h1>
    
    <!-- Debug Information -->
    <div class="debug-info">
        <strong>Debug Info:</strong><br>
        Processes loaded: <span x-text="processes.length"></span><br>
        Show delete modal: <span x-text="showDeleteModal"></span><br>
        Process to delete: <span x-text="processToDelete ? processToDelete.processname : 'null'"></span><br>
    </div>
    
    <!-- Manual Modal Control -->
    <div class="mb-4">
        <button @click="showDeleteModal = true; processToDelete = {processname: 'Test Process'}" 
                class="bg-red-500 text-white px-4 py-2 rounded mr-2">
            Show Modal (Test)
        </button>
        <button @click="showDeleteModal = false; processToDelete = null" 
                class="bg-green-500 text-white px-4 py-2 rounded">
            Hide Modal
        </button>
    </div>
    
    <!-- Process Table -->
    <div class="bg-white rounded-lg shadow">
        <table class="min-w-full">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">ID</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Process Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Symbol</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                </tr>
            </thead>
            <tbody>
                <template x-for="process in processes" :key="process.id">
                    <tr>
                        <td class="px-6 py-4 text-sm" x-text="process.id"></td>
                        <td class="px-6 py-4 text-sm" x-text="process.processname"></td>
                        <td class="px-6 py-4 text-sm" x-text="process.symbol"></td>
                        <td class="px-6 py-4 text-sm">
                            <button @click="deleteProcess(process)" 
                                    class="bg-red-500 text-white px-2 py-1 rounded text-xs">
                                Delete
                            </button>
                        </td>
                    </tr>
                </template>
            </tbody>
        </table>
        
        <div x-show="processes.length === 0" class="p-8 text-center text-gray-500">
            No processes found
        </div>
    </div>
    
    <!-- Simple Modal -->
    <div x-show="showDeleteModal" 
         class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
         style="display: none;"
         @click.self="showDeleteModal = false">
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 class="text-lg font-bold mb-4">Confirm Deletion</h3>
            <p class="mb-4">
                Are you sure you want to delete: 
                <strong x-text="processToDelete ? processToDelete.processname : 'Unknown'"></strong>?
            </p>
            <div class="flex justify-end space-x-2">
                <button @click="showDeleteModal = false" 
                        class="bg-gray-300 px-4 py-2 rounded">
                    Cancel
                </button>
                <button @click="confirmDelete()" 
                        class="bg-red-500 text-white px-4 py-2 rounded">
                    Delete
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function debugManager() {
    // Parse the processes data
    let processesData;
    try {
        processesData = {{ processes|safe }};
        console.log('Raw processes data:', processesData);
    } catch (e) {
        console.error('Error parsing processes data:', e);
        processesData = [];
    }
    
    return {
        processes: processesData,
        showDeleteModal: false,
        processToDelete: null,
        
        init() {
            console.log('Debug manager initialized');
            console.log('Processes:', this.processes);
            console.log('Modal state:', this.showDeleteModal);
            
            // Ensure modal is hidden
            this.showDeleteModal = false;
        },
        
        deleteProcess(process) {
            console.log('Delete clicked for:', process);
            this.processToDelete = process;
            this.showDeleteModal = true;
        },
        
        confirmDelete() {
            console.log('Delete confirmed for:', this.processToDelete);
            alert('Delete functionality not implemented in debug mode');
            this.showDeleteModal = false;
            this.processToDelete = null;
        }
    };
}
</script>
{% endblock %}