from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView
from django.http import JsonResponse
from django.contrib import messages
from django.urls import reverse_lazy
from django.db.models import Q, Sum, Count, F
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.db import transaction
from datetime import datetime
import json

from project_management.models import MaterialCreditNote, MaterialCreditNoteDetail
from sales_distribution.models import WorkOrder, Customer
from ..forms import (
    MCNAuthorizationSearchForm, MCNAuthorizationForm,
    AuthorizedMCNCreateForm
)
from core.context_processors.company_context import company_context


class MCNAuthorizationListView(LoginRequiredMixin, ListView):
    """List view for work orders available for MCN authorization - replaces AuthorizedMCN.aspx"""
    model = WorkOrder
    template_name = 'inventory/mcn_authorization/list.html'
    context_object_name = 'work_orders'
    paginate_by = 20
    
    def get_company_id(self):
        """Helper method to get company_id from context processor"""
        context_data = company_context(self.request)
        return context_data.get('global_company', {}).get('company_id')

    def get_queryset(self):
        company_id = self.get_company_id()
        
        # Get work orders that have MCN records available for authorization
        queryset = WorkOrder.objects.select_related('finyearid', 'cid')
        if company_id:
            queryset = queryset.filter(compid=company_id)
        
        # Filter work orders that have associated MCN records
        queryset = queryset.filter(
            wono__in=MaterialCreditNote.objects.values_list('work_order_no', flat=True)
        ).distinct()
        
        queryset = queryset.order_by('-id')
        
        # Apply search filters
        search_term = self.request.GET.get('wo_no', '').strip()
        customer_search = self.request.GET.get('customer_name', '').strip()
        project_search = self.request.GET.get('project_title', '').strip()
        
        if search_term:
            queryset = queryset.filter(wono__icontains=search_term)
        
        if customer_search:
            queryset = queryset.filter(
                Q(cid__customer_name__icontains=customer_search) |
                Q(cid__customerid__icontains=customer_search)
            )
        
        if project_search:
            queryset = queryset.filter(taskprojecttitle__icontains=project_search)
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update(company_context(self.request))
        
        # Add search form
        context['search_form'] = MCNAuthorizationSearchForm(self.request.GET)
        
        # Add enumeration for serial numbers and customer information
        if context.get('work_orders'):
            company_id = self.get_company_id()
            work_orders_with_info = []
            
            # Get all customers for this company
            customers_dict = {}
            if company_id:
                customers = Customer.objects.filter(compid=company_id)
                for customer in customers:
                    if customer.customerid:
                        customers_dict[customer.customerid] = customer
            
            for idx, wo in enumerate(context['work_orders'], 1):
                wo.serial_number = idx + (context.get('page_obj').start_index() - 1 if context.get('page_obj') else 0)
                
                # Get customer information
                wo.customer_obj = None
                wo.customer_name = "-"
                wo.customer_code = "-"
                
                if wo.customerid and wo.customerid in customers_dict:
                    customer = customers_dict[wo.customerid]
                    wo.customer_obj = customer
                    wo.customer_name = customer.customer_name or "-"
                    wo.customer_code = customer.customerid or "-"
                elif wo.enqid:
                    # Try to get customer info from enquiry
                    try:
                        enquiry = wo.enqid
                        if enquiry and enquiry.customername:
                            wo.customer_name = enquiry.customername
                        if enquiry and enquiry.customerid:
                            wo.customer_code = enquiry.customerid
                    except:
                        pass
                
                # Get MCN statistics for this work order
                mcn_stats = MaterialCreditNote.objects.filter(
                    work_order_no=wo.wono,
                    company_id=company_id
                ).aggregate(
                    total_mcns=Count('id'),
                    total_items=Count('materialcreditnotedetail__id')
                )
                
                wo.mcn_count = mcn_stats['total_mcns'] or 0
                wo.mcn_items_count = mcn_stats['total_items'] or 0
                
                work_orders_with_info.append(wo)
            
            context['work_orders'] = work_orders_with_info
            
        # Calculate summary statistics
        total_work_orders = context['work_orders'].count() if context.get('work_orders') else 0
        context['stats'] = {
            'total_work_orders': total_work_orders,
            'pending_authorization': self.get_pending_authorization_count(),
            'today_authorizations': self.get_today_authorization_count(),
        }
        
        return context
    
    def get_pending_authorization_count(self):
        """Get count of MCN items pending authorization"""
        company_id = self.get_company_id()
        if not company_id:
            return 0
        
        # Count MCN details that haven't been fully authorized yet
        # This would be replaced with actual authorization tracking table
        return MaterialCreditNoteDetail.objects.filter(
            master__company_id=company_id
        ).count()
    
    def get_today_authorization_count(self):
        """Get count of authorizations done today"""
        company_id = self.get_company_id()
        if not company_id:
            return 0
        
        today = datetime.now().strftime('%m/%d/%Y')
        return MaterialCreditNote.objects.filter(
            company_id=company_id,
            sysdate=today
        ).count()


class MCNAuthorizationDetailView(LoginRequiredMixin, DetailView):
    """Detail view for MCN authorization - replaces AuthorizedMCN_Details.aspx"""
    model = WorkOrder
    template_name = 'inventory/mcn_authorization/detail.html'
    context_object_name = 'work_order'
    
    def get_company_id(self):
        """Helper method to get company_id from context processor"""
        context_data = company_context(self.request)
        return context_data.get('global_company', {}).get('company_id')

    def get_queryset(self):
        company_id = self.get_company_id()
        queryset = WorkOrder.objects.select_related('finyearid', 'cid')
        if company_id:
            queryset = queryset.filter(compid=company_id)
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update(company_context(self.request))
        
        work_order = self.object
        company_id = self.get_company_id()
        
        # Get customer information
        context['customer'] = None
        context['customer_name'] = "-"
        context['customer_code'] = "-"
        
        if work_order.customerid:
            try:
                customer = Customer.objects.filter(
                    customerid=work_order.customerid,
                    compid=company_id
                ).first()
                if customer:
                    context['customer'] = customer
                    context['customer_name'] = customer.customer_name or "-"
                    context['customer_code'] = customer.customerid or "-"
            except:
                pass
        
        # Get MCN records for this work order
        mcn_records = MaterialCreditNote.objects.filter(
            work_order_no=work_order.wono,
            company_id=company_id
        ).prefetch_related('materialcreditnotedetail_set')
        
        # Prepare MCN items with authorization information
        mcn_items = []
        for mcn in mcn_records:
            for detail in mcn.materialcreditnotedetail_set.all():
                # Get BOM information (simulated - would come from design module)
                bom_info = self.get_bom_info(work_order.wono, detail.project_id, detail.customer_id)
                
                # Calculate authorization status
                # In real implementation, this would query authorization table
                available_qty = detail.mcn_quantity or 0
                authorized_qty = 0  # Would be from authorization table
                pending_qty = available_qty - authorized_qty
                
                mcn_item = {
                    'mcn_id': mcn.id,
                    'mcn_no': mcn.mcn_no,
                    'detail_id': detail.id,
                    'project_id': detail.project_id,
                    'customer_id': detail.customer_id,
                    'mcn_quantity': available_qty,
                    'authorized_quantity': authorized_qty,
                    'pending_quantity': pending_qty,
                    'bom_info': bom_info,
                    'can_authorize': pending_qty > 0,
                }
                mcn_items.append(mcn_item)
        
        context['mcn_items'] = mcn_items
        context['authorization_form'] = MCNAuthorizationForm()
        
        # Calculate totals
        context['totals'] = {
            'total_mcn_qty': sum(item['mcn_quantity'] for item in mcn_items),
            'total_authorized_qty': sum(item['authorized_quantity'] for item in mcn_items),
            'total_pending_qty': sum(item['pending_quantity'] for item in mcn_items),
            'total_items': len(mcn_items),
        }
        
        return context
    
    def get_bom_info(self, work_order_no, project_id, customer_id):
        """Get BOM information for validation - simulated data"""
        # In real implementation, this would query design module BOM tables
        return {
            'item_code': f'ITM-{project_id}',
            'description': f'Material for Project {project_id}',
            'uom': 'PCS',
            'bom_qty': 10.0,
            'issued_qty': 7.0,
            'balance_qty': 3.0,
        }
    
    def post(self, request, *args, **kwargs):
        """Handle MCN authorization submission"""
        if request.headers.get('HX-Request'):
            return self.handle_authorization(request)
        return super().get(request, *args, **kwargs)
    
    def handle_authorization(self, request):
        """Handle HTMX authorization request"""
        work_order = self.get_object()
        company_id = self.get_company_id()
        
        # Get selected items and authorized quantities
        selected_items = request.POST.getlist('selected_items')
        authorization_successful = False
        authorized_count = 0
        
        try:
            with transaction.atomic():
                for item_data in selected_items:
                    try:
                        item_info = json.loads(item_data)
                        auth_quantity = float(request.POST.get(f'auth_quantity_{item_info["detail_id"]}', 0))
                        
                        if auth_quantity > 0:
                            # Validate authorization quantity
                            detail = MaterialCreditNoteDetail.objects.get(
                                id=item_info['detail_id'],
                                master__company_id=company_id
                            )
                            
                            if auth_quantity <= detail.mcn_quantity:
                                # In real implementation, create authorization record
                                # For now, just simulate successful authorization
                                authorized_count += 1
                                authorization_successful = True
                            else:
                                messages.error(
                                    request, 
                                    f'Authorization quantity {auth_quantity} exceeds available MCN quantity {detail.mcn_quantity}'
                                )
                                
                    except (json.JSONDecodeError, ValueError, KeyError, MaterialCreditNoteDetail.DoesNotExist):
                        continue

            if authorization_successful and authorized_count > 0:
                messages.success(
                    request, 
                    f'{authorized_count} item(s) authorized successfully for Work Order {work_order.wono}'
                )
            elif authorized_count == 0:
                messages.warning(request, 'No valid items were authorized.')
                
        except Exception as e:
            messages.error(request, f'Authorization failed: {str(e)}')
        
        # Return updated authorization interface
        return render(request, 'inventory/mcn_authorization/authorization_results.html', {
            'work_order': work_order,
            'authorized_count': authorized_count,
            'success': authorization_successful,
        })


class AuthorizedMCNCreateView(LoginRequiredMixin, CreateView):
    """Create view for new MCN authorization records"""
    template_name = 'inventory/mcn_authorization/create.html'
    form_class = AuthorizedMCNCreateForm
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update(company_context(self.request))
        
        # Get work order from URL parameter
        work_order_no = self.kwargs.get('work_order_no')
        if work_order_no:
            context['work_order_no'] = work_order_no
            
        return context
    
    def form_valid(self, form):
        """Process authorization creation"""
        company_context_data = company_context(self.request)
        company_id = company_context_data.get('global_company', {}).get('company_id')
        
        # In real implementation, create authorization record in proper table
        # For now, simulate successful creation
        
        messages.success(
            self.request, 
            f'MCN Authorization created successfully for Work Order {form.cleaned_data.get("work_order_no")}'
        )
        
        return redirect('inventory:mcn_authorization_list')


class MCNAuthorizationStatsView(LoginRequiredMixin, ListView):
    """Statistics view for MCN authorization dashboard"""
    template_name = 'inventory/mcn_authorization/stats.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        company_context_data = company_context(self.request)
        context.update(company_context_data)
        
        company_id = company_context_data.get('global_company', {}).get('company_id')
        
        # Calculate authorization statistics
        total_work_orders = WorkOrder.objects.filter(compid=company_id).count()
        
        work_orders_with_mcn = WorkOrder.objects.filter(
            compid=company_id,
            wono__in=MaterialCreditNote.objects.values_list('work_order_no', flat=True)
        ).count()
        
        total_mcn_items = MaterialCreditNoteDetail.objects.filter(
            master__company_id=company_id
        ).count()
        
        total_mcn_quantity = MaterialCreditNoteDetail.objects.filter(
            master__company_id=company_id
        ).aggregate(total=Sum('mcn_quantity'))['total'] or 0
        
        # Get today's activity
        today = datetime.now().strftime('%m/%d/%Y')
        today_mcns = MaterialCreditNote.objects.filter(
            company_id=company_id,
            sysdate=today
        ).count()
        
        # Get recent activity (last 7 days)
        from datetime import timedelta
        week_ago = (datetime.now() - timedelta(days=7)).strftime('%m/%d/%Y')
        recent_mcns = MaterialCreditNote.objects.filter(
            company_id=company_id,
            sysdate__gte=week_ago
        ).count()
        
        context.update({
            'total_work_orders': total_work_orders,
            'work_orders_with_mcn': work_orders_with_mcn,
            'total_mcn_items': total_mcn_items,
            'total_mcn_quantity': total_mcn_quantity,
            'today_mcns': today_mcns,
            'recent_mcns': recent_mcns,
            'authorization_rate': round((work_orders_with_mcn / total_work_orders * 100) if total_work_orders > 0 else 0, 1),
        })
        
        return context


@method_decorator(csrf_exempt, name='dispatch')
class MCNAuthorizationAjaxView(LoginRequiredMixin, ListView):
    """AJAX view for MCN authorization operations"""
    
    def post(self, request):
        """Handle AJAX requests for authorization operations"""
        action = request.POST.get('action')
        
        if action == 'validate_quantity':
            return self.validate_authorization_quantity(request)
        elif action == 'get_mcn_details':
            return self.get_mcn_details(request)
        elif action == 'bulk_authorize':
            return self.handle_bulk_authorization(request)
        
        return JsonResponse({'success': False, 'message': 'Invalid action'})
    
    def validate_authorization_quantity(self, request):
        """Validate authorization quantity against MCN availability"""
        detail_id = request.POST.get('detail_id')
        auth_quantity = request.POST.get('auth_quantity', 0)
        
        try:
            auth_quantity = float(auth_quantity)
            detail = MaterialCreditNoteDetail.objects.get(id=detail_id)
            
            if auth_quantity <= 0:
                return JsonResponse({
                    'valid': False,
                    'message': 'Authorization quantity must be greater than zero'
                })
            
            if auth_quantity > detail.mcn_quantity:
                return JsonResponse({
                    'valid': False,
                    'message': f'Authorization quantity cannot exceed MCN quantity {detail.mcn_quantity}'
                })
            
            return JsonResponse({
                'valid': True,
                'message': 'Valid authorization quantity',
                'available_qty': detail.mcn_quantity
            })
            
        except (ValueError, MaterialCreditNoteDetail.DoesNotExist):
            return JsonResponse({
                'valid': False,
                'message': 'Invalid detail ID or quantity'
            })
    
    def get_mcn_details(self, request):
        """Get MCN details for a work order"""
        work_order_no = request.POST.get('work_order_no')
        company_context_data = company_context(request)
        company_id = company_context_data.get('global_company', {}).get('company_id')
        
        if not work_order_no:
            return JsonResponse({'success': False, 'message': 'Work Order No is required'})
        
        mcn_records = MaterialCreditNote.objects.filter(
            work_order_no=work_order_no,
            company_id=company_id
        ).prefetch_related('materialcreditnotedetail_set')
        
        mcn_data = []
        for mcn in mcn_records:
            for detail in mcn.materialcreditnotedetail_set.all():
                mcn_data.append({
                    'mcn_no': mcn.mcn_no,
                    'detail_id': detail.id,
                    'project_id': detail.project_id,
                    'customer_id': detail.customer_id,
                    'mcn_quantity': detail.mcn_quantity,
                })
        
        return JsonResponse({
            'success': True,
            'mcn_items': mcn_data,
            'count': len(mcn_data)
        })
    
    def handle_bulk_authorization(self, request):
        """Handle bulk authorization operations"""
        selected_items = request.POST.getlist('selected_items')
        auth_remarks = request.POST.get('remarks', '')
        
        if not selected_items:
            return JsonResponse({
                'success': False,
                'message': 'No items selected for authorization'
            })
        
        try:
            with transaction.atomic():
                authorized_count = 0
                for item_id in selected_items:
                    # In real implementation, create authorization records
                    # For now, just count successful authorizations
                    authorized_count += 1
                
                return JsonResponse({
                    'success': True,
                    'message': f'{authorized_count} item(s) authorized successfully',
                    'authorized_count': authorized_count
                })
                
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'Bulk authorization failed: {str(e)}'
            })