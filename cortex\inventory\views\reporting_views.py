from django.shortcuts import render
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.views.generic import TemplateView
from django.http import JsonResponse, HttpResponse
from django.db.models import Sum, Count, F, Value, Max
from django.utils import timezone
from datetime import date, timedelta
import csv

from ..models import (
    StockLedger, MaterialIssueNote, MaterialReturnNote
)
from ..forms import (
    ABCAnalysisForm, MovingNonMovingAnalysisForm, StockStatementForm,
    ReportExportForm
)


class InventoryReportsDashboardView(LoginRequiredMixin, TemplateView):
    """Main dashboard for inventory reports and analytics"""
    template_name = 'inventory/reports/dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Inventory Reports & Analytics'
        
        # Quick stats for dashboard
        context['quick_stats'] = self.get_quick_stats()
        
        return context
    
    def get_quick_stats(self):
        """Get quick statistics for dashboard"""
        try:
            # Basic inventory metrics
            total_items = StockLedger.objects.values('item_code').distinct().count()
            total_stock_value = StockLedger.objects.aggregate(
                total=Sum(F('available_stock') * F('unit_rate'))
            )['total'] or 0
            
            # Recent transactions
            today = date.today()
            last_30_days = today - timedelta(days=30)
            
            recent_issues = MaterialIssueNote.objects.filter(
                min_date__gte=last_30_days
            ).count()
            
            recent_returns = MaterialReturnNote.objects.filter(
                mrn_date__gte=last_30_days
            ).count()
            
            return {
                'total_items': total_items,
                'total_stock_value': float(total_stock_value),
                'recent_issues': recent_issues,
                'recent_returns': recent_returns,
            }
        except Exception:
            return {
                'total_items': 0,
                'total_stock_value': 0,
                'recent_issues': 0,
                'recent_returns': 0,
            }


class ABCAnalysisView(LoginRequiredMixin, TemplateView):
    """ABC Analysis for inventory classification"""
    template_name = 'inventory/reports/abc_analysis.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'ABC Analysis'
        context['form'] = ABCAnalysisForm()
        
        # If this is a form submission, generate analysis
        if self.request.GET:
            form = ABCAnalysisForm(self.request.GET)
            if form.is_valid():
                context['form'] = form
                context['analysis_results'] = self.generate_abc_analysis(form.cleaned_data)
                context['show_results'] = True
        
        return context
    
    def generate_abc_analysis(self, params):
        """Generate ABC analysis based on parameters"""
        try:
            # Determine date range
            date_range = self.get_date_range(params)
            
            # Get base queryset
            queryset = StockLedger.objects.filter(
                created_date__range=date_range
            )
            
            # Apply filters
            if params.get('category_filter'):
                queryset = queryset.filter(
                    item_code__icontains=params['category_filter']
                )
            
            # Calculate analysis metric based on criteria
            criteria = params['classification_criteria']
            
            if criteria == 'value':
                # ABC by stock value
                items_data = queryset.values('item_code', 'item_description').annotate(
                    analysis_value=Sum(F('available_stock') * F('unit_rate')),
                    current_stock=Sum('available_stock'),
                    avg_rate=Sum('unit_rate') / Count('unit_rate')
                ).order_by('-analysis_value')
                
            elif criteria == 'quantity':
                # ABC by quantity consumed/issued
                items_data = queryset.values('item_code', 'item_description').annotate(
                    analysis_value=Sum('issued_quantity'),
                    current_stock=Sum('available_stock'),
                    avg_rate=Sum('unit_rate') / Count('unit_rate')
                ).order_by('-analysis_value')
                
            else:  # frequency
                # ABC by transaction frequency
                items_data = queryset.values('item_code', 'item_description').annotate(
                    analysis_value=Count('id'),
                    current_stock=Sum('available_stock'),
                    avg_rate=Sum('unit_rate') / Count('unit_rate')
                ).order_by('-analysis_value')
            
            # Filter out zero movement if not included
            if not params.get('include_zero_movement'):
                items_data = items_data.filter(analysis_value__gt=0)
            
            # Convert to list for processing
            items_list = list(items_data)
            
            if not items_list:
                return {
                    'items': [],
                    'summary': {'A': 0, 'B': 0, 'C': 0},
                    'total_value': 0,
                    'criteria': criteria
                }
            
            # Calculate cumulative values and percentages
            total_value = sum(item['analysis_value'] or 0 for item in items_list)
            cumulative_value = 0
            
            for item in items_list:
                cumulative_value += item['analysis_value'] or 0
                item['cumulative_value'] = cumulative_value
                item['cumulative_percentage'] = (cumulative_value / total_value * 100) if total_value > 0 else 0
                item['value_percentage'] = (item['analysis_value'] / total_value * 100) if total_value > 0 else 0
            
            # Classify items into A, B, C categories
            a_threshold = params['a_percentage']
            b_threshold = params['a_percentage'] + params['b_percentage']
            
            classified_items = []
            summary = {'A': 0, 'B': 0, 'C': 0}
            
            for item in items_list:
                if item['cumulative_percentage'] <= a_threshold:
                    item['abc_category'] = 'A'
                    summary['A'] += 1
                elif item['cumulative_percentage'] <= b_threshold:
                    item['abc_category'] = 'B'
                    summary['B'] += 1
                else:
                    item['abc_category'] = 'C'
                    summary['C'] += 1
                
                classified_items.append(item)
            
            return {
                'items': classified_items,
                'summary': summary,
                'total_value': total_value,
                'criteria': criteria,
                'parameters': params
            }
            
        except Exception as e:
            return {
                'error': f"Error generating ABC analysis: {str(e)}",
                'items': [],
                'summary': {'A': 0, 'B': 0, 'C': 0},
                'total_value': 0
            }
    
    def get_date_range(self, params):
        """Get date range based on analysis period"""
        if params['analysis_period'] == 'custom':
            return [params['date_from'], params['date_to']]
        
        end_date = date.today()
        
        if params['analysis_period'] == '3_months':
            start_date = end_date - timedelta(days=90)
        elif params['analysis_period'] == '6_months':
            start_date = end_date - timedelta(days=180)
        else:  # 12_months
            start_date = end_date - timedelta(days=365)
        
        return [start_date, end_date]


class MovingNonMovingAnalysisView(LoginRequiredMixin, TemplateView):
    """Moving and Non-moving items analysis"""
    template_name = 'inventory/reports/moving_nonmoving_analysis.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Moving/Non-moving Items Analysis'
        context['form'] = MovingNonMovingAnalysisForm()
        
        # If this is a form submission, generate analysis
        if self.request.GET:
            form = MovingNonMovingAnalysisForm(self.request.GET)
            if form.is_valid():
                context['form'] = form
                context['analysis_results'] = self.generate_movement_analysis(form.cleaned_data)
                context['show_results'] = True
        
        return context
    
    def generate_movement_analysis(self, params):
        """Generate moving/non-moving analysis"""
        try:
            # Determine date range
            date_range = self.get_movement_date_range(params)
            
            # Get stock ledger data
            stock_data = StockLedger.objects.values(
                'item_code', 'item_description', 'location_code'
            ).annotate(
                current_stock=Sum('available_stock'),
                total_issued=Sum('issued_quantity'),
                total_received=Sum('received_quantity'),
                stock_value=Sum(F('available_stock') * F('unit_rate')),
                transaction_count=Count('id'),
                last_transaction_date=Max('created_date')
            )
            
            # Apply filters
            if params.get('category_filter'):
                stock_data = stock_data.filter(
                    item_code__icontains=params['category_filter']
                )
            
            if params.get('location_filter'):
                stock_data = stock_data.filter(
                    location_code__icontains=params['location_filter']
                )
            
            if params.get('minimum_stock_value'):
                stock_data = stock_data.filter(
                    stock_value__gte=params['minimum_stock_value']
                )
            
            # Classify items based on movement
            classified_items = []
            summary = {
                'moving': 0,
                'non_moving': 0,
                'slow_moving': 0,
                'total_value': 0
            }
            
            for item in stock_data:
                # Determine movement classification
                transaction_count = item['transaction_count'] or 0
                last_transaction = item['last_transaction_date']
                
                # Check if item moved in the analysis period
                has_recent_movement = False
                if last_transaction:
                    days_since_last = (timezone.now().date() - last_transaction.date()).days
                    period_days = self.get_period_days(params['movement_period'])
                    has_recent_movement = days_since_last <= period_days
                
                if transaction_count == 0 or not has_recent_movement:
                    movement_type = 'non_moving'
                elif transaction_count <= params.get('slow_moving_threshold', 5):
                    movement_type = 'slow_moving'
                else:
                    movement_type = 'moving'
                
                # Apply analysis type filter
                analysis_type = params['analysis_type']
                if analysis_type != 'all' and movement_type != analysis_type:
                    continue
                
                # Include/exclude zero stock items
                if not params.get('include_zero_stock') and (item['current_stock'] or 0) <= 0:
                    continue
                
                item_data = {
                    'item_code': item['item_code'],
                    'item_description': item['item_description'],
                    'location_code': item['location_code'],
                    'current_stock': item['current_stock'] or 0,
                    'stock_value': item['stock_value'] or 0,
                    'transaction_count': transaction_count,
                    'last_transaction_date': last_transaction,
                    'movement_type': movement_type,
                    'total_issued': item['total_issued'] or 0,
                    'total_received': item['total_received'] or 0
                }
                
                classified_items.append(item_data)
                summary[movement_type] += 1
                summary['total_value'] += item['stock_value'] or 0
            
            # Sort by stock value descending
            classified_items.sort(key=lambda x: x['stock_value'], reverse=True)
            
            return {
                'items': classified_items,
                'summary': summary,
                'parameters': params,
                'date_range': date_range
            }
            
        except Exception as e:
            return {
                'error': f"Error generating movement analysis: {str(e)}",
                'items': [],
                'summary': {'moving': 0, 'non_moving': 0, 'slow_moving': 0, 'total_value': 0}
            }
    
    def get_movement_date_range(self, params):
        """Get date range for movement analysis"""
        if params['movement_period'] == 'custom':
            return [params['date_from'], params['date_to']]
        
        end_date = date.today()
        period_days = self.get_period_days(params['movement_period'])
        start_date = end_date - timedelta(days=period_days)
        
        return [start_date, end_date]
    
    def get_period_days(self, period):
        """Convert period string to days"""
        period_map = {
            '30_days': 30,
            '60_days': 60,
            '90_days': 90,
            '180_days': 180,
            '365_days': 365
        }
        return period_map.get(period, 90)


class StockStatementView(LoginRequiredMixin, TemplateView):
    """Stock Statement generation"""
    template_name = 'inventory/reports/stock_statement.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Stock Statement'
        context['form'] = StockStatementForm()
        
        # If this is a form submission, generate statement
        if self.request.GET:
            form = StockStatementForm(self.request.GET)
            if form.is_valid():
                context['form'] = form
                context['statement_data'] = self.generate_stock_statement(form.cleaned_data)
                context['show_results'] = True
        
        return context
    
    def generate_stock_statement(self, params):
        """Generate stock statement based on parameters"""
        try:
            as_on_date = params['as_on_date']
            statement_type = params['statement_type']
            
            # Base query for stock as on specific date
            base_query = StockLedger.objects.filter(
                created_date__lte=as_on_date
            )
            
            # Apply filters
            if params.get('category_filter'):
                base_query = base_query.filter(
                    item_code__icontains=params['category_filter']
                )
            
            if params.get('location_filter'):
                base_query = base_query.filter(
                    location_code__icontains=params['location_filter']
                )
            
            # Generate statement based on type
            if statement_type == 'summary':
                return self.generate_summary_statement(base_query, params)
            elif statement_type == 'detailed':
                return self.generate_detailed_statement(base_query, params)
            elif statement_type == 'category_wise':
                return self.generate_category_statement(base_query, params)
            elif statement_type == 'location_wise':
                return self.generate_location_statement(base_query, params)
            elif statement_type == 'valuation':
                return self.generate_valuation_statement(base_query, params)
            
        except Exception as e:
            return {
                'error': f"Error generating stock statement: {str(e)}",
                'data': [],
                'summary': {}
            }
    
    def generate_summary_statement(self, query, params):
        """Generate summary stock statement"""
        summary_data = query.values('item_code').annotate(
            item_description=F('item_description'),
            total_stock=Sum('available_stock'),
            total_value=Sum(F('available_stock') * F('unit_rate')),
            avg_rate=Sum('unit_rate') / Count('unit_rate')
        ).order_by('item_code')
        
        # Filter based on stock inclusion preferences
        if not params.get('include_zero_stock'):
            summary_data = summary_data.filter(total_stock__gt=0)
        
        if not params.get('include_negative_stock'):
            summary_data = summary_data.filter(total_stock__gte=0)
        
        data_list = list(summary_data)
        
        # Calculate totals
        total_items = len(data_list)
        total_stock_value = sum(item['total_value'] or 0 for item in data_list)
        total_quantity = sum(item['total_stock'] or 0 for item in data_list)
        
        return {
            'data': data_list,
            'summary': {
                'total_items': total_items,
                'total_quantity': total_quantity,
                'total_value': total_stock_value,
                'statement_type': 'Summary'
            },
            'parameters': params
        }
    
    def generate_detailed_statement(self, query, params):
        """Generate detailed stock statement"""
        detailed_data = query.values(
            'item_code', 'item_description', 'location_code',
            'available_stock', 'unit_rate', 'batch_number',
            'expiry_date', 'last_transaction_date'
        ).annotate(
            stock_value=F('available_stock') * F('unit_rate')
        ).order_by('item_code', 'location_code')
        
        # Apply filters
        if not params.get('include_zero_stock'):
            detailed_data = detailed_data.filter(available_stock__gt=0)
        
        if not params.get('include_negative_stock'):
            detailed_data = detailed_data.filter(available_stock__gte=0)
        
        return {
            'data': list(detailed_data),
            'summary': {
                'statement_type': 'Detailed'
            },
            'parameters': params
        }
    
    def generate_category_statement(self, query, params):
        """Generate category-wise stock statement"""
        # This would require a category field in the stock ledger
        # For now, group by first part of item code (assuming format like CAT-ITEM)
        category_data = query.extra(
            select={'category': "SUBSTR(item_code, 1, INSTR(item_code || '-', '-') - 1)"}
        ).values('category').annotate(
            total_items=Count('item_code', distinct=True),
            total_stock=Sum('available_stock'),
            total_value=Sum(F('available_stock') * F('unit_rate'))
        ).order_by('category')
        
        return {
            'data': list(category_data),
            'summary': {
                'statement_type': 'Category-wise'
            },
            'parameters': params
        }
    
    def generate_location_statement(self, query, params):
        """Generate location-wise stock statement"""
        location_data = query.values('location_code').annotate(
            total_items=Count('item_code', distinct=True),
            total_stock=Sum('available_stock'),
            total_value=Sum(F('available_stock') * F('unit_rate'))
        ).order_by('location_code')
        
        return {
            'data': list(location_data),
            'summary': {
                'statement_type': 'Location-wise'
            },
            'parameters': params
        }
    
    def generate_valuation_statement(self, query, params):
        """Generate stock valuation statement"""
        valuation_method = params['valuation_method']
        
        # For this example, we'll use average cost method
        # In a real implementation, you'd implement FIFO, LIFO, etc.
        valuation_data = query.values('item_code', 'item_description').annotate(
            total_stock=Sum('available_stock'),
            avg_cost=Sum('unit_rate') / Count('unit_rate'),
            total_value=Sum(F('available_stock') * F('unit_rate')),
            valuation_method=Value(valuation_method)
        ).order_by('item_code')
        
        return {
            'data': list(valuation_data),
            'summary': {
                'statement_type': f'Valuation ({valuation_method.upper()})',
                'valuation_method': valuation_method
            },
            'parameters': params
        }


@login_required
def export_report_view(request):
    """Export reports in various formats"""
    if request.method == 'POST':
        form = ReportExportForm(request.POST)
        if form.is_valid():
            export_format = form.cleaned_data['export_format']
            
            # Get report data from session or regenerate
            report_data = request.session.get('last_report_data', {})
            
            if export_format == 'csv':
                return export_csv_report(report_data, form.cleaned_data)
            elif export_format == 'excel':
                return export_excel_report(report_data, form.cleaned_data)
            elif export_format == 'pdf':
                return export_pdf_report(report_data, form.cleaned_data)
    
    form = ReportExportForm()
    return render(request, 'inventory/reports/export_form.html', {
        'form': form,
        'page_title': 'Export Report'
    })


def export_csv_report(report_data, export_params):
    """Export report as CSV"""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="inventory_report.csv"'
    
    writer = csv.writer(response)
    
    # Write headers
    if report_data.get('data'):
        headers = list(report_data['data'][0].keys()) if report_data['data'] else []
        writer.writerow(headers)
        
        # Write data
        for row in report_data['data']:
            writer.writerow([row.get(header, '') for header in headers])
    
    return response


def export_excel_report(report_data, export_params):
    """Export report as Excel (placeholder - would need openpyxl)"""
    # This would require openpyxl library
    # For now, return CSV format
    return export_csv_report(report_data, export_params)


def export_pdf_report(report_data, export_params):
    """Export report as PDF (placeholder - would need reportlab)"""
    # This would require reportlab library
    # For now, return a simple text response
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = 'attachment; filename="inventory_report.pdf"'
    response.write(b"PDF export functionality would be implemented here using reportlab")
    return response


@login_required
def inventory_analytics_api(request):
    """API endpoint for inventory analytics data"""
    analytics_type = request.GET.get('type', 'overview')
    
    if analytics_type == 'overview':
        return JsonResponse(get_overview_analytics())
    elif analytics_type == 'trends':
        return JsonResponse(get_trend_analytics())
    elif analytics_type == 'categories':
        return JsonResponse(get_category_analytics())
    
    return JsonResponse({'error': 'Invalid analytics type'})


def get_overview_analytics():
    """Get overview analytics data"""
    try:
        # Basic metrics
        total_items = StockLedger.objects.values('item_code').distinct().count()
        total_locations = StockLedger.objects.values('location_code').distinct().count()
        total_stock_value = StockLedger.objects.aggregate(
            total=Sum(F('available_stock') * F('unit_rate'))
        )['total'] or 0
        
        # Stock distribution
        positive_stock = StockLedger.objects.filter(available_stock__gt=0).count()
        zero_stock = StockLedger.objects.filter(available_stock=0).count()
        negative_stock = StockLedger.objects.filter(available_stock__lt=0).count()
        
        return {
            'total_items': total_items,
            'total_locations': total_locations,
            'total_stock_value': float(total_stock_value),
            'stock_distribution': {
                'positive': positive_stock,
                'zero': zero_stock,
                'negative': negative_stock
            }
        }
    except Exception as e:
        return {'error': str(e)}


def get_trend_analytics():
    """Get trend analytics data"""
    try:
        # Monthly trends for last 12 months
        end_date = date.today()
        start_date = end_date - timedelta(days=365)
        
        monthly_data = StockLedger.objects.filter(
            created_date__range=[start_date, end_date]
        ).extra(
            select={'month': "strftime('%%Y-%%m', created_date)"}
        ).values('month').annotate(
            total_transactions=Count('id'),
            total_value=Sum(F('available_stock') * F('unit_rate'))
        ).order_by('month')
        
        return {
            'monthly_trends': list(monthly_data)
        }
    except Exception as e:
        return {'error': str(e)}


def get_category_analytics():
    """Get category-wise analytics"""
    try:
        # Group by item category (first part of item code)
        category_data = StockLedger.objects.extra(
            select={'category': "SUBSTR(item_code, 1, INSTR(item_code || '-', '-') - 1)"}
        ).values('category').annotate(
            item_count=Count('item_code', distinct=True),
            total_stock=Sum('available_stock'),
            total_value=Sum(F('available_stock') * F('unit_rate'))
        ).order_by('-total_value')
        
        return {
            'categories': list(category_data)
        }
    except Exception as e:
        return {'error': str(e)}