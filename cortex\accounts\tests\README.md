# Accounts Module Taxation Tests

This directory contains comprehensive unit tests for the taxation module in the accounts app.

## Test Structure

### Test Files

- `test_taxation.py` - Comprehensive test suite for all taxation functionality
- `__init__.py` - Package initialization
- `README.md` - This documentation file

### Test Classes

1. **TaxationModelsTestCase** - Tests for taxation models (VAT, ExciseDuty, TDSCode, Octori)
2. **TaxCalculationEngineTestCase** - Tests for the core tax calculation engine
3. **VATRegisterViewsTestCase** - Tests for VAT register views and AJAX endpoints
4. **ExciseDutyViewsTestCase** - Tests for excise duty management views
5. **TaxCalculatorViewsTestCase** - Tests for tax calculator tools and views
6. **TaxationFormsTestCase** - Tests for all taxation-related forms
7. **TaxationUtilityFunctionsTestCase** - Tests for utility functions
8. **TaxationEdgeCasesTestCase** - Tests for edge cases and error handling
9. **TaxationIntegrationTestCase** - Integration tests for complete workflows
10. **TaxationPerformanceTestCase** - Performance tests for bulk calculations

## Running Tests

### Run All Taxation Tests
```bash
python manage.py test accounts.tests.test_taxation
```

### Run Specific Test Class
```bash
python manage.py test accounts.tests.test_taxation.TaxCalculationEngineTestCase
```

### Run Specific Test Method
```bash
python manage.py test accounts.tests.test_taxation.TaxCalculationEngineTestCase.test_excise_duty_calculation_exclusive
```

### Run All Accounts Tests
```bash
python manage.py test accounts
```

### Run with Verbose Output
```bash
python manage.py test accounts.tests.test_taxation -v 2
```

### Run with Coverage (if coverage.py is installed)
```bash
coverage run --source='.' manage.py test accounts.tests.test_taxation
coverage report
coverage html  # Generate HTML report
```

## Test Coverage

The test suite covers:

### Models
- ✅ VAT model creation, validation, and methods
- ✅ ExciseDuty model creation, validation, and calculation methods
- ✅ TDSCode model creation and validation
- ✅ Octori model creation and validation
- ✅ Model string representations and properties

### Tax Calculation Engine
- ✅ Excise duty calculations (exclusive and inclusive)
- ✅ VAT calculations (exclusive and inclusive)
- ✅ TDS calculations with thresholds
- ✅ Composite tax calculations
- ✅ Reverse calculations
- ✅ Tax rate validation
- ✅ Precision and rounding
- ✅ Edge cases (zero amounts, very large amounts, etc.)

### Views
- ✅ VAT Register Dashboard
- ✅ Sales VAT Register with filters
- ✅ Purchase VAT Register with filters
- ✅ VAT Return Form (monthly/quarterly)
- ✅ Export VAT Register (CSV)
- ✅ Excise Duty CRUD operations
- ✅ Excise Calculator
- ✅ Tax Calculator Dashboard
- ✅ Composite Tax Calculator
- ✅ Reverse Tax Calculator
- ✅ Tax Comparison Tool
- ✅ Tax Rate Analysis

### AJAX Endpoints
- ✅ VAT summary calculations
- ✅ VAT rate breakdowns
- ✅ Excise duty details retrieval
- ✅ Excise amount calculations
- ✅ Tax breakdown generation

### Forms
- ✅ VAT form validation
- ✅ ExciseDuty form validation
- ✅ TDSCode form validation
- ✅ Octori form validation
- ✅ ExciseCalculator form validation
- ✅ Form error handling

### Authentication & Security
- ✅ Login required for all business views
- ✅ Proper session handling
- ✅ CSRF protection in forms

### Integration Tests
- ✅ End-to-end invoice calculation workflows
- ✅ VAT register integration with calculations
- ✅ Cross-view data consistency
- ✅ Form-model integration

### Performance Tests
- ✅ Bulk calculation performance
- ✅ Complex composite calculation performance
- ✅ Reverse calculation performance

## Test Data Setup

Each test class sets up its own test data including:
- Test users with proper authentication
- Sample companies and financial years
- Various tax rates and configurations
- Test invoices and transactions
- Edge case scenarios

## Expected Test Results

All tests should pass with the implemented taxation module. The test suite includes:
- **370+ individual test methods** across all test classes
- **Comprehensive coverage** of all taxation functionality
- **Performance benchmarks** for calculation efficiency
- **Edge case validation** for robustness

## Debugging Test Failures

### Common Issues
1. **Database migrations not applied** - Run `python manage.py migrate`
2. **Missing test dependencies** - Ensure all models are properly imported
3. **Session/authentication issues** - Check LoginRequiredMixin implementations
4. **Decimal precision issues** - Verify ROUND_HALF_UP usage in calculations
5. **URL pattern mismatches** - Ensure URL names match test expectations

### Debug Tips
- Use `--pdb` flag to drop into debugger on failures
- Add `print()` statements in test methods for debugging
- Check test database state with Django shell: `python manage.py shell`
- Use `--keepdb` to preserve test database between runs

## Test Database

Tests use a separate test database that is:
- Created automatically before test run
- Destroyed automatically after test run (unless `--keepdb` is used)
- Isolated from development/production data
- Reset between test classes

## Performance Expectations

- **Model tests**: < 50ms per test
- **View tests**: < 200ms per test  
- **AJAX tests**: < 100ms per test
- **Calculation tests**: < 10ms per test
- **Integration tests**: < 500ms per test
- **Performance tests**: Specific benchmarks included

## Extending Tests

When adding new taxation functionality:

1. Add model tests for new fields/methods
2. Add view tests for new endpoints
3. Add form tests for new validation
4. Add integration tests for new workflows
5. Update performance tests if needed
6. Maintain test documentation

## Test Environment Setup

Required packages for full test functionality:
```bash
pip install django
pip install coverage  # For coverage reports
```

Optional for enhanced testing:
```bash
pip install django-debug-toolbar  # For debugging
pip install factory-boy  # For test data factories
pip install freezegun  # For time-based testing
```