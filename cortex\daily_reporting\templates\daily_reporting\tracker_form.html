{% extends 'core/base.html' %}
{% load static %}

{% block title %}{% if page_title %}{{ page_title }}{% else %}Daily Report Form{% endif %}{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        background: #f8f9fc;
        border-radius: 0.35rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid #4e73df;
    }
    .section-title {
        color: #5a5c69;
        font-weight: 600;
        margin-bottom: 1rem;
    }
    .required-field::after {
        content: " *";
        color: #e74a3b;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">{{ page_title|default:"Daily Report Form" }}</h1>
            <p class="text-muted">Complete daily activity reporting and tracking</p>
        </div>
        <div>
            <a href="{% url 'daily_reporting:tracker_list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to List
            </a>
        </div>
    </div>

    <!-- Form -->
    <form method="post" hx-post="{% if object %}{% url 'daily_reporting:tracker_edit' object.pk %}{% else %}{% url 'daily_reporting:tracker_create' %}{% endif %}" 
          hx-target="#form-container" hx-swap="innerHTML">
        {% csrf_token %}
        
        <div id="form-container">
            <!-- Employee Information Section -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-user me-2"></i>Employee Information
                </h5>
                <div class="row">
                    <div class="col-md-4">
                        <label for="{{ form.employee_name.id_for_label }}" class="form-label required-field">Employee Name</label>
                        {{ form.employee_name }}
                        {% if form.employee_name.errors %}
                            <div class="text-danger small">{{ form.employee_name.errors.0 }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-4">
                        <label for="{{ form.designation.id_for_label }}" class="form-label required-field">Designation</label>
                        {{ form.designation }}
                        {% if form.designation.errors %}
                            <div class="text-danger small">{{ form.designation.errors.0 }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-4">
                        <label for="{{ form.department.id_for_label }}" class="form-label required-field">Department</label>
                        {{ form.department }}
                        {% if form.department.errors %}
                            <div class="text-danger small">{{ form.department.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-4">
                        <label for="{{ form.date_of_reporting.id_for_label }}" class="form-label required-field">Date of Reporting</label>
                        {{ form.date_of_reporting }}
                        {% if form.date_of_reporting.errors %}
                            <div class="text-danger small">{{ form.date_of_reporting.errors.0 }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-8" id="employee-details">
                        <!-- Employee details will be loaded here via HTMX -->
                    </div>
                </div>
            </div>

            <!-- Weekly Reporting Section -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-calendar-week me-2"></i>Weekly Summary
                </h5>
                <div class="row">
                    <div class="col-md-6">
                        <label for="{{ form.significant_achievements_last_week.id_for_label }}" class="form-label required-field">
                            Significant Achievements Last Week
                        </label>
                        {{ form.significant_achievements_last_week }}
                        {% if form.significant_achievements_last_week.errors %}
                            <div class="text-danger small">{{ form.significant_achievements_last_week.errors.0 }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <label for="{{ form.activities_task_current_week.id_for_label }}" class="form-label required-field">
                            Activities/Tasks for Current Week
                        </label>
                        {{ form.activities_task_current_week }}
                        {% if form.activities_task_current_week.errors %}
                            <div class="text-danger small">{{ form.activities_task_current_week.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-4">
                        <label for="{{ form.activities_planned_completed.id_for_label }}" class="form-label required-field">
                            Activities Planned and Completed
                        </label>
                        {{ form.activities_planned_completed }}
                        {% if form.activities_planned_completed.errors %}
                            <div class="text-danger small">{{ form.activities_planned_completed.errors.0 }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-4">
                        <label for="{{ form.activities_planned_not_completed.id_for_label }}" class="form-label required-field">
                            Activities Planned but Not Completed
                        </label>
                        {{ form.activities_planned_not_completed }}
                        {% if form.activities_planned_not_completed.errors %}
                            <div class="text-danger small">{{ form.activities_planned_not_completed.errors.0 }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-4">
                        <label for="{{ form.activities_unplanned_completed.id_for_label }}" class="form-label required-field">
                            Activities Unplanned but Completed
                        </label>
                        {{ form.activities_unplanned_completed }}
                        {% if form.activities_unplanned_completed.errors %}
                            <div class="text-danger small">{{ form.activities_unplanned_completed.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <label for="{{ form.plan_next_week.id_for_label }}" class="form-label required-field">
                            Plan for Next Week
                        </label>
                        {{ form.plan_next_week }}
                        {% if form.plan_next_week.errors %}
                            <div class="text-danger small">{{ form.plan_next_week.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Daily Activity Section -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-tasks me-2"></i>Daily Activity Tracking
                </h5>
                <div class="row">
                    <div class="col-md-3">
                        <label for="{{ form.activity_date.id_for_label }}" class="form-label required-field">Activity Date</label>
                        {{ form.activity_date }}
                        {% if form.activity_date.errors %}
                            <div class="text-danger small">{{ form.activity_date.errors.0 }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-3">
                        <label for="{{ form.wo_number.id_for_label }}" class="form-label required-field">Work Order Number</label>
                        {{ form.wo_number }}
                        {% if form.wo_number.errors %}
                            <div class="text-danger small">{{ form.wo_number.errors.0 }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-3">
                        <label for="{{ form.estimated_time.id_for_label }}" class="form-label required-field">Estimated Time</label>
                        {{ form.estimated_time }}
                        {% if form.estimated_time.errors %}
                            <div class="text-danger small">{{ form.estimated_time.errors.0 }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-3">
                        <label for="{{ form.percentage_completed.id_for_label }}" class="form-label required-field">% Completed</label>
                        {{ form.percentage_completed }}
                        {% if form.percentage_completed.errors %}
                            <div class="text-danger small">{{ form.percentage_completed.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <label for="{{ form.activity.id_for_label }}" class="form-label required-field">Activity Description</label>
                        {{ form.activity }}
                        {% if form.activity.errors %}
                            <div class="text-danger small">{{ form.activity.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <label for="{{ form.status.id_for_label }}" class="form-label required-field">Status</label>
                        {{ form.status }}
                        {% if form.status.errors %}
                            <div class="text-danger small">{{ form.status.errors.0 }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <label for="{{ form.remarks.id_for_label }}" class="form-label">Remarks</label>
                        {{ form.remarks }}
                        {% if form.remarks.errors %}
                            <div class="text-danger small">{{ form.remarks.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Form Errors -->
            {% if form.non_field_errors %}
                <div class="alert alert-danger">
                    {{ form.non_field_errors }}
                </div>
            {% endif %}

            <!-- Submit Buttons -->
            <div class="d-flex justify-content-between align-items-center">
                <a href="{% url 'daily_reporting:tracker_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Cancel
                </a>
                <div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>
                        {% if object %}Update Report{% else %}Create Report{% endif %}
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/htmx.org@1.9.10"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-populate current date for date fields if empty
        const reportingDate = document.getElementById('id_date_of_reporting');
        const activityDate = document.getElementById('id_activity_date');
        
        if (reportingDate && !reportingDate.value) {
            reportingDate.value = new Date().toISOString().split('T')[0];
        }
        
        if (activityDate && !activityDate.value) {
            activityDate.value = new Date().toISOString().split('T')[0];
        }

        // Real-time validation feedback
        const form = document.querySelector('form');
        const inputs = form.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                if (this.hasAttribute('required') && !this.value.trim()) {
                    this.classList.add('is-invalid');
                } else {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                }
            });
        });

        // Progress bar update preview
        const percentageSelect = document.getElementById('id_percentage_completed');
        if (percentageSelect) {
            percentageSelect.addEventListener('change', function() {
                const value = this.value;
                let color = 'bg-secondary';
                
                if (value >= 75) color = 'bg-success';
                else if (value >= 50) color = 'bg-info';
                else if (value >= 25) color = 'bg-warning';
                else if (value > 0) color = 'bg-danger';
                
                console.log(`Progress updated to ${value}% (${color})`);
            });
        }
    });
</script>
{% endblock %}