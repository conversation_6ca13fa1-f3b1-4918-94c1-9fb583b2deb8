<!-- accounts/templates/accounts/invoices/partials/invoice_list_table.html -->
<!-- Sales Invoice List Table Partial Template for HTMX -->
<!-- Task Group 5: Invoicing & Billing - Invoice List Table (Task 5.14) -->

<div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
    <div class="px-6 py-4 border-b border-sap-gray-100">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-sap-gray-800">Sales Invoices</h3>
            <div class="flex items-center space-x-2">
                <button onclick="selectAllInvoices()" 
                        class="text-sap-blue-600 hover:text-sap-blue-700 text-sm font-medium">
                    Select All
                </button>
                <span class="text-sap-gray-400">|</span>
                <button onclick="deselectAllInvoices()" 
                        class="text-sap-gray-600 hover:text-sap-gray-700 text-sm font-medium">
                    Deselect All
                </button>
                <span class="text-sap-gray-400">|</span>
                <button onclick="exportInvoices()" 
                        class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-3 py-1.5 rounded text-sm font-medium transition-colors">
                    <i data-lucide="download" class="w-4 h-4 inline mr-1"></i>
                    Export
                </button>
            </div>
        </div>
    </div>
    
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-sap-gray-200">
            <thead class="bg-sap-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        <input type="checkbox" id="select-all" 
                               class="rounded border-sap-gray-300 text-sap-green-600 focus:ring-sap-green-500"
                               onchange="toggleAllInvoices(this)">
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        Invoice Details
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        Customer
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        Amount
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        Status
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        Due Date
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-sap-gray-200">
                {% for invoice in invoices %}
                <tr class="hover:bg-sap-gray-50" x-data="{ showQuickActions: false }">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <input type="checkbox" name="invoice_ids" value="{{ invoice.id }}" 
                               class="rounded border-sap-gray-300 text-sap-green-600 focus:ring-sap-green-500"
                               @change="toggleInvoiceSelection({{ invoice.id }})">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div>
                            <div class="flex items-center">
                                <div class="text-sm font-medium text-sap-gray-900">{{ invoice.invoice_number }}</div>
                                {% if invoice.is_recurring %}
                                <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-sap-blue-100 text-sap-blue-800">
                                    <i data-lucide="repeat" class="w-3 h-3 mr-1"></i>
                                    Recurring
                                </span>
                                {% endif %}
                            </div>
                            <div class="text-sm text-sap-gray-500">{{ invoice.invoice_date|date:"M d, Y" }}</div>
                            {% if invoice.po_number %}
                            <div class="text-xs text-sap-gray-400">PO: {{ invoice.po_number }}</div>
                            {% endif %}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div>
                            <div class="text-sm font-medium text-sap-gray-900">{{ invoice.customer_name|default:"N/A" }}</div>
                            {% if invoice.customer_email %}
                            <div class="text-sm text-sap-gray-500">{{ invoice.customer_email }}</div>
                            {% endif %}
                            {% if invoice.customer_gstin %}
                            <div class="text-xs text-sap-gray-400">GSTIN: {{ invoice.customer_gstin }}</div>
                            {% endif %}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div>
                            <div class="text-sm font-medium text-sap-gray-900">₹{{ invoice.total_amount|floatformat:2 }}</div>
                            {% if invoice.paid_amount > 0 %}
                            <div class="text-xs text-sap-green-600">Paid: ₹{{ invoice.paid_amount|floatformat:2 }}</div>
                            {% endif %}
                            {% if invoice.outstanding_amount > 0 %}
                            <div class="text-xs text-sap-orange-600">Due: ₹{{ invoice.outstanding_amount|floatformat:2 }}</div>
                            {% endif %}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="relative" @click.away="showQuickActions = false">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium cursor-pointer
                                {% if invoice.status == 'PAID' %}bg-sap-green-100 text-sap-green-800
                                {% elif invoice.status == 'SENT' %}bg-sap-blue-100 text-sap-blue-800
                                {% elif invoice.status == 'OVERDUE' %}bg-sap-red-100 text-sap-red-800
                                {% elif invoice.status == 'DRAFT' %}bg-sap-gray-100 text-sap-gray-800
                                {% else %}bg-sap-yellow-100 text-sap-yellow-800{% endif %}"
                                @click="showQuickActions = !showQuickActions">
                                {% if invoice.status == 'PAID' %}
                                    <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                {% elif invoice.status == 'SENT' %}
                                    <i data-lucide="mail" class="w-3 h-3 mr-1"></i>
                                {% elif invoice.status == 'OVERDUE' %}
                                    <i data-lucide="alert-circle" class="w-3 h-3 mr-1"></i>
                                {% elif invoice.status == 'DRAFT' %}
                                    <i data-lucide="edit" class="w-3 h-3 mr-1"></i>
                                {% else %}
                                    <i data-lucide="clock" class="w-3 h-3 mr-1"></i>
                                {% endif %}
                                {{ invoice.get_status_display|default:invoice.status }}
                                <i data-lucide="chevron-down" class="w-3 h-3 ml-1"></i>
                            </span>
                            
                            <!-- Quick Status Update Dropdown -->
                            <div x-show="showQuickActions" 
                                 x-transition:enter="transition ease-out duration-100"
                                 x-transition:enter-start="transform opacity-0 scale-95"
                                 x-transition:enter-end="transform opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-75"
                                 x-transition:leave-start="transform opacity-100 scale-100"
                                 x-transition:leave-end="transform opacity-0 scale-95"
                                 class="absolute z-10 mt-1 w-32 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                                <div class="py-1">
                                    <button onclick="quickStatusUpdate({{ invoice.id }}, 'DRAFT')" 
                                            class="block w-full text-left px-4 py-2 text-xs text-sap-gray-700 hover:bg-sap-gray-100">
                                        Draft
                                    </button>
                                    <button onclick="quickStatusUpdate({{ invoice.id }}, 'SENT')" 
                                            class="block w-full text-left px-4 py-2 text-xs text-sap-gray-700 hover:bg-sap-gray-100">
                                        Sent
                                    </button>
                                    <button onclick="quickStatusUpdate({{ invoice.id }}, 'PAID')" 
                                            class="block w-full text-left px-4 py-2 text-xs text-sap-gray-700 hover:bg-sap-gray-100">
                                        Paid
                                    </button>
                                    <button onclick="quickStatusUpdate({{ invoice.id }}, 'CANCELLED')" 
                                            class="block w-full text-left px-4 py-2 text-xs text-sap-gray-700 hover:bg-sap-gray-100">
                                        Cancelled
                                    </button>
                                </div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if invoice.due_date %}
                        <div class="text-sm text-sap-gray-900">{{ invoice.due_date|date:"M d, Y" }}</div>
                        {% if invoice.is_overdue %}
                        <div class="text-xs text-sap-red-600">{{ invoice.days_overdue }} days overdue</div>
                        {% else %}
                        <div class="text-xs text-sap-green-600">{{ invoice.days_until_due }} days left</div>
                        {% endif %}
                        {% else %}
                        <div class="text-sm text-sap-gray-400">No due date</div>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex items-center space-x-2">
                            <!-- View/Edit -->
                            <a href="{% url 'accounts:sales_invoice_detail' invoice.id %}" 
                               class="text-sap-blue-600 hover:text-sap-blue-900" title="View Details">
                                <i data-lucide="eye" class="w-4 h-4"></i>
                            </a>
                            
                            <!-- Edit -->
                            {% if invoice.status == 'DRAFT' %}
                            <a href="{% url 'accounts:sales_invoice_edit' invoice.id %}" 
                               class="text-sap-green-600 hover:text-sap-green-900" title="Edit">
                                <i data-lucide="edit" class="w-4 h-4"></i>
                            </a>
                            {% endif %}
                            
                            <!-- Email -->
                            <button onclick="emailInvoice({{ invoice.id }})" 
                                    class="text-sap-purple-600 hover:text-sap-purple-900" title="Email">
                                <i data-lucide="mail" class="w-4 h-4"></i>
                            </button>
                            
                            <!-- Print -->
                            <button onclick="printInvoice({{ invoice.id }})" 
                                    class="text-sap-orange-600 hover:text-sap-orange-900" title="Print">
                                <i data-lucide="printer" class="w-4 h-4"></i>
                            </button>
                            
                            <!-- Duplicate -->
                            <button onclick="duplicateInvoice({{ invoice.id }})" 
                                    class="text-sap-indigo-600 hover:text-sap-indigo-900" title="Duplicate">
                                <i data-lucide="copy" class="w-4 h-4"></i>
                            </button>
                            
                            <!-- Delete (only for draft invoices) -->
                            {% if invoice.status == 'DRAFT' %}
                            <button onclick="deleteInvoice({{ invoice.id }})" 
                                    class="text-sap-red-600 hover:text-sap-red-900" title="Delete">
                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                            </button>
                            {% endif %}
                            
                            <!-- More Actions Dropdown -->
                            <div class="relative" x-data="{ showMore: false }" @click.away="showMore = false">
                                <button @click="showMore = !showMore" 
                                        class="text-sap-gray-600 hover:text-sap-gray-900" title="More Actions">
                                    <i data-lucide="more-horizontal" class="w-4 h-4"></i>
                                </button>
                                
                                <div x-show="showMore" 
                                     x-transition:enter="transition ease-out duration-100"
                                     x-transition:enter-start="transform opacity-0 scale-95"
                                     x-transition:enter-end="transform opacity-100 scale-100"
                                     x-transition:leave="transition ease-in duration-75"
                                     x-transition:leave-start="transform opacity-100 scale-100"
                                     x-transition:leave-end="transform opacity-0 scale-95"
                                     class="absolute right-0 z-10 mt-1 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                                    <div class="py-1">
                                        <a href="{% url 'accounts:sales_invoice_detail' invoice.id %}" 
                                           class="block px-4 py-2 text-xs text-sap-gray-700 hover:bg-sap-gray-100">
                                            <i data-lucide="file-text" class="w-3 h-3 inline mr-2"></i>
                                            View Details
                                        </a>
                                        <button onclick="convertToProforma({{ invoice.id }})" 
                                                class="block w-full text-left px-4 py-2 text-xs text-sap-gray-700 hover:bg-sap-gray-100">
                                            <i data-lucide="file-plus" class="w-3 h-3 inline mr-2"></i>
                                            Convert to Proforma
                                        </button>
                                        <button onclick="createRecurring({{ invoice.id }})" 
                                                class="block w-full text-left px-4 py-2 text-xs text-sap-gray-700 hover:bg-sap-gray-100">
                                            <i data-lucide="repeat" class="w-3 h-3 inline mr-2"></i>
                                            Create Recurring
                                        </button>
                                        <button onclick="addPayment({{ invoice.id }})" 
                                                class="block w-full text-left px-4 py-2 text-xs text-sap-gray-700 hover:bg-sap-gray-100">
                                            <i data-lucide="credit-card" class="w-3 h-3 inline mr-2"></i>
                                            Record Payment
                                        </button>
                                        <button onclick="generateDebitNote({{ invoice.id }})" 
                                                class="block w-full text-left px-4 py-2 text-xs text-sap-gray-700 hover:bg-sap-gray-100">
                                            <i data-lucide="plus-circle" class="w-3 h-3 inline mr-2"></i>
                                            Create Debit Note
                                        </button>
                                        <button onclick="generateCreditNote({{ invoice.id }})" 
                                                class="block w-full text-left px-4 py-2 text-xs text-sap-gray-700 hover:bg-sap-gray-100">
                                            <i data-lucide="minus-circle" class="w-3 h-3 inline mr-2"></i>
                                            Create Credit Note
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="7" class="px-6 py-12 text-center">
                        <div class="text-sap-gray-400">
                            <i data-lucide="inbox" class="w-12 h-12 mx-auto mb-4"></i>
                            <p class="text-lg font-medium">No invoices found</p>
                            <p class="text-sm">Create your first sales invoice to get started</p>
                            <a href="{% url 'accounts:sales_invoice_create' %}" 
                               class="inline-flex items-center mt-4 bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                Create Sales Invoice
                            </a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    {% if is_paginated %}
    <div class="bg-white px-4 py-3 border-t border-sap-gray-200 sm:px-6">
        <div class="flex items-center justify-between">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if page_obj.has_previous %}
                <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" 
                   class="relative inline-flex items-center px-4 py-2 border border-sap-gray-300 text-sm font-medium rounded-md text-sap-gray-700 bg-white hover:bg-sap-gray-50"
                   hx-get="{% url 'accounts:sales_invoice_list' %}?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}"
                   hx-target="#invoice-list-container"
                   hx-indicator="#search-spinner">
                    Previous
                </a>
                {% endif %}
                {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" 
                   class="ml-3 relative inline-flex items-center px-4 py-2 border border-sap-gray-300 text-sm font-medium rounded-md text-sap-gray-700 bg-white hover:bg-sap-gray-50"
                   hx-get="{% url 'accounts:sales_invoice_list' %}?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}"
                   hx-target="#invoice-list-container"
                   hx-indicator="#search-spinner">
                    Next
                </a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-sap-gray-700">
                        Showing <span class="font-medium">{{ page_obj.start_index }}</span> to 
                        <span class="font-medium">{{ page_obj.end_index }}</span> of 
                        <span class="font-medium">{{ page_obj.paginator.count }}</span> results
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                        {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" 
                           class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-500 hover:bg-sap-gray-50"
                           hx-get="{% url 'accounts:sales_invoice_list' %}?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}"
                           hx-target="#invoice-list-container"
                           hx-indicator="#search-spinner">
                            <i data-lucide="chevron-left" class="w-5 h-5"></i>
                        </a>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-sap-green-500 bg-sap-green-50 text-sm font-medium text-sap-green-600">
                            {{ num }}
                        </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?page={{ num }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" 
                           class="relative inline-flex items-center px-4 py-2 border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-700 hover:bg-sap-gray-50"
                           hx-get="{% url 'accounts:sales_invoice_list' %}?page={{ num }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}"
                           hx-target="#invoice-list-container"
                           hx-indicator="#search-spinner">
                            {{ num }}
                        </a>
                        {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" 
                           class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-500 hover:bg-sap-gray-50"
                           hx-get="{% url 'accounts:sales_invoice_list' %}?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}"
                           hx-target="#invoice-list-container"
                           hx-indicator="#search-spinner">
                            <i data-lucide="chevron-right" class="w-5 h-5"></i>
                        </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
function toggleAllInvoices(selectAllCheckbox) {
    const checkboxes = document.querySelectorAll('input[name="invoice_ids"]');
    const alpine = Alpine.$data(document.querySelector('[x-data="salesInvoiceList()"]'));
    
    if (selectAllCheckbox.checked) {
        alpine.selectedInvoices = Array.from(checkboxes).map(cb => {
            cb.checked = true;
            return cb.value;
        });
    } else {
        alpine.selectedInvoices = [];
        checkboxes.forEach(cb => cb.checked = false);
    }
}

function selectAllInvoices() {
    const selectAllCheckbox = document.getElementById('select-all');
    selectAllCheckbox.checked = true;
    toggleAllInvoices(selectAllCheckbox);
}

function deselectAllInvoices() {
    const selectAllCheckbox = document.getElementById('select-all');
    selectAllCheckbox.checked = false;
    toggleAllInvoices(selectAllCheckbox);
}

function exportInvoices() {
    const alpine = Alpine.$data(document.querySelector('[x-data="salesInvoiceList()"]'));
    if (alpine.selectedInvoices.length === 0) {
        showNotification('Please select invoices to export', 'warning');
        return;
    }
    
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    params.set('selected_ids', alpine.selectedInvoices.join(','));
    
    window.location.href = '{% url "accounts:sales_invoice_list" %}?' + params.toString();
}

// Additional action functions
function convertToProforma(invoiceId) {
    if (confirm('Convert this invoice to a proforma invoice?')) {
        window.location.href = `/accounts/invoices/sales/${invoiceId}/convert-to-proforma/`;
    }
}

function createRecurring(invoiceId) {
    window.location.href = `/accounts/invoices/sales/${invoiceId}/create-recurring/`;
}

function addPayment(invoiceId) {
    window.open(`/accounts/invoices/sales/${invoiceId}/add-payment/`, '_blank', 'width=800,height=600');
}

function generateDebitNote(invoiceId) {
    window.location.href = `/accounts/transactions/debit-notes/create/?invoice_id=${invoiceId}`;
}

function generateCreditNote(invoiceId) {
    window.location.href = `/accounts/transactions/credit-notes/create/?invoice_id=${invoiceId}`;
}
</script>