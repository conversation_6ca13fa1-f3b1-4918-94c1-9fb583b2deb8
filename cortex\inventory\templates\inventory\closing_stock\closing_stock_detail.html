{% extends "core/base.html" %}
{% load static %}

{% block title %}Closing Stock Details - Record #{{ closing_stock.id }}{% endblock %}

{% block content %}
<div class="sap-page">
    <!-- Page Header -->
    <div class="sap-page-header">
        <div class="sap-page-header-content">
            <div class="sap-page-title">
                <h1>Closing Stock Record #{{ closing_stock.id }}</h1>
                <p>Detailed view of closing stock information</p>
            </div>
            <div class="sap-page-actions">
                <a href="{% url 'inventory:closing_stock_list' %}" class="sap-button sap-button--transparent">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to List
                </a>
                <a href="{% url 'inventory:closing_stock_update' closing_stock.pk %}" class="sap-button sap-button--emphasized">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit Record
                </a>
                <button class="sap-button sap-button--transparent" onclick="window.print()">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                    </svg>
                    Print
                </button>
            </div>
        </div>
    </div>

    <!-- Main Information Panel -->
    <div class="sap-panel">
        <div class="sap-panel-header">
            <h3 class="sap-panel-title">Record Information</h3>
            <div class="flex items-center space-x-2">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Active
                </span>
            </div>
        </div>
        <div class="sap-panel-content">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Record ID -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">Record ID</label>
                    <p class="mt-1 text-lg font-semibold text-gray-900">#{{ closing_stock.id }}</p>
                </div>

                <!-- From Date -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">From Date</label>
                    {% if from_date_formatted %}
                        <p class="mt-1 text-sm text-gray-900">{{ from_date_formatted }}</p>
                        <p class="text-xs text-gray-500">{{ closing_stock.fromdt }}</p>
                    {% else %}
                        <p class="mt-1 text-sm text-gray-500">Not specified</p>
                    {% endif %}
                </div>

                <!-- To Date -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">To Date</label>
                    {% if to_date_formatted %}
                        <p class="mt-1 text-sm text-gray-900">{{ to_date_formatted }}</p>
                        <p class="text-xs text-gray-500">{{ closing_stock.todt }}</p>
                    {% else %}
                        <p class="mt-1 text-sm text-gray-500">Not specified</p>
                    {% endif %}
                </div>

                <!-- Period Duration -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">Period Duration</label>
                    {% if period_days %}
                        <p class="mt-1 text-sm text-gray-900">{{ period_days }} days</p>
                    {% else %}
                        <p class="mt-1 text-sm text-gray-500">Cannot calculate</p>
                    {% endif %}
                </div>

                <!-- Closing Stock Value -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">Closing Stock Value</label>
                    {% if closing_stock.clstock %}
                        <p class="mt-1 text-2xl font-bold text-green-600">₹{{ closing_stock.clstock|floatformat:2 }}</p>
                    {% else %}
                        <p class="mt-1 text-2xl font-bold text-gray-400">₹0.00</p>
                    {% endif %}
                </div>

                <!-- Date Range Visual -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">Period Overview</label>
                    <div class="mt-1">
                        {% if closing_stock.fromdt and closing_stock.todt %}
                            <div class="flex items-center space-x-2 text-sm">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ closing_stock.fromdt }}
                                </span>
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                </svg>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    {{ closing_stock.todt }}
                                </span>
                            </div>
                        {% else %}
                            <p class="text-sm text-gray-500">Incomplete date range</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Information Panel -->
    <div class="sap-panel">
        <div class="sap-panel-header">
            <h3 class="sap-panel-title">Additional Information</h3>
        </div>
        <div class="sap-panel-content">
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Database Information -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Database Information</h4>
                        <dl class="space-y-2">
                            <div class="flex justify-between text-sm">
                                <dt class="text-gray-600">Table:</dt>
                                <dd class="text-gray-900 font-mono">tblInv_ClosingStck</dd>
                            </div>
                            <div class="flex justify-between text-sm">
                                <dt class="text-gray-600">Primary Key:</dt>
                                <dd class="text-gray-900">{{ closing_stock.id }}</dd>
                            </div>
                            <div class="flex justify-between text-sm">
                                <dt class="text-gray-600">From Date (Raw):</dt>
                                <dd class="text-gray-900 font-mono">{{ closing_stock.fromdt|default:"NULL" }}</dd>
                            </div>
                            <div class="flex justify-between text-sm">
                                <dt class="text-gray-600">To Date (Raw):</dt>
                                <dd class="text-gray-900 font-mono">{{ closing_stock.todt|default:"NULL" }}</dd>
                            </div>
                            <div class="flex justify-between text-sm">
                                <dt class="text-gray-600">Stock Value (Raw):</dt>
                                <dd class="text-gray-900 font-mono">{{ closing_stock.clstock|default:"NULL" }}</dd>
                            </div>
                        </dl>
                    </div>

                    <!-- Statistics -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Record Statistics</h4>
                        <dl class="space-y-2">
                            {% if period_days %}
                            <div class="flex justify-between text-sm">
                                <dt class="text-gray-600">Period Length:</dt>
                                <dd class="text-gray-900">{{ period_days }} days</dd>
                            </div>
                            {% if closing_stock.clstock and period_days %}
                            <div class="flex justify-between text-sm">
                                <dt class="text-gray-600">Daily Average:</dt>
                                <dd class="text-gray-900">₹{{ closing_stock.clstock|floatformat:2 }}</dd>
                            </div>
                            {% endif %}
                            {% endif %}
                            <div class="flex justify-between text-sm">
                                <dt class="text-gray-600">Value Format:</dt>
                                <dd class="text-gray-900">Indian Rupees (₹)</dd>
                            </div>
                            <div class="flex justify-between text-sm">
                                <dt class="text-gray-600">Date Format:</dt>
                                <dd class="text-gray-900">DD-MM-YYYY</dd>
                            </div>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Panel -->
    <div class="flex justify-center space-x-4 mt-6">
        <a href="{% url 'inventory:closing_stock_update' closing_stock.pk %}" class="sap-button sap-button--emphasized">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            Edit This Record
        </a>
        
        <a href="{% url 'inventory:closing_stock_create' %}" class="sap-button sap-button--transparent">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Create New Record
        </a>
        
        <a href="{% url 'inventory:closing_stock_delete' closing_stock.pk %}" 
           class="sap-button sap-button--transparent text-red-600 hover:text-red-800"
           onclick="return confirm('Are you sure you want to delete this closing stock record? This action cannot be undone.')">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
            Delete Record
        </a>
    </div>
</div>

<style>
@media print {
    .sap-page-actions,
    .print\\:hidden {
        display: none !important;
    }
    
    .sap-page {
        margin: 0;
        box-shadow: none;
    }
    
    .sap-panel {
        border: 1px solid #ddd;
        margin-bottom: 20px;
    }
}
</style>
{% endblock %}