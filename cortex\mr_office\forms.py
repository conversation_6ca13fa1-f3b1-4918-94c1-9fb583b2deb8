from django import forms
from django.core.exceptions import ValidationError
from .models import MROffice, ModuleMaster


class MROfficeForm(forms.ModelForm):
    """Form for creating/editing MR Office documents."""
    
    formodule = forms.ModelChoiceField(
        queryset=ModuleMaster.objects.all(),
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent',
            'hx-trigger': 'change'
        }),
        empty_label="Select Module",
        required=True
    )
    
    format = forms.CharField(
        max_length=200,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent',
            'placeholder': 'Enter format/document name'
        }),
        required=True
    )
    
    attachment = forms.FileField(
        widget=forms.FileInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent',
            'accept': '.pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png'
        }),
        required=True,
        help_text="Supported formats: PDF, DOC, DOCX, XLS, XLSX, JPG, JPEG, PNG"
    )

    class Meta:
        model = MROffice
        fields = ['formodule', 'format']

    def clean_attachment(self):
        """Validate file upload."""
        attachment = self.cleaned_data.get('attachment')
        if attachment:
            # Check file size (max 10MB)
            if attachment.size > 10 * 1024 * 1024:
                raise ValidationError("File size must be less than 10MB.")
            
            # Check file type
            allowed_types = [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'image/jpeg',
                'image/jpg',
                'image/png'
            ]
            
            if attachment.content_type not in allowed_types:
                raise ValidationError("File type not supported. Please upload PDF, DOC, DOCX, XLS, XLSX, JPG, JPEG, or PNG files.")
        
        return attachment

    def clean_format(self):
        """Validate format field."""
        format_text = self.cleaned_data.get('format')
        if format_text:
            format_text = format_text.strip()
            if len(format_text) < 3:
                raise ValidationError("Format/Document name must be at least 3 characters long.")
        return format_text


class MROfficeSearchForm(forms.Form):
    """Form for searching MR Office documents."""
    
    module = forms.ModelChoiceField(
        queryset=ModuleMaster.objects.all(),
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent',
            'hx-trigger': 'change',
            'hx-get': '',
            'hx-target': '#documents-table'
        }),
        empty_label="All Modules",
        required=False
    )
    
    format_search = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent',
            'placeholder': 'Search by format/document name...',
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-get': '',
            'hx-target': '#documents-table'
        }),
        required=False
    )