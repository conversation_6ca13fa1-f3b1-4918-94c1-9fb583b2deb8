"""
Tests for Products functionality.
Based on ASP.NET Product.aspx and tests the Django implementation.
Tests product CRUD operations, inline editing, and HTMX functionality.
"""

from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model

from ..models import Product


class ProductFunctionalityBaseTestCase(TestCase):
    """Base test case with common setup for product tests"""
    
    def setUp(self):
        """Set up test data"""
        self.client = Client()
        
        # Create test user
        User = get_user_model()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            email='<EMAIL>'
        )
        
        # Login user
        self.client.login(username='testuser', password='testpass123')
        
        # Create test products
        self.product1 = Product.objects.create(
            name='Test Product 1'
        )
        
        self.product2 = Product.objects.create(
            name='Test Product 2'
        )


class ProductModelTestCase(ProductFunctionalityBaseTestCase):
    """Test Product model functionality"""
    
    def test_product_creation(self):
        """Test product model creation"""
        product = Product.objects.create(
            name='New Test Product'
        )
        
        self.assertEqual(str(product), 'New Test Product')
        self.assertTrue(Product.objects.filter(name='New Test Product').exists())
    
    def test_product_str_representation(self):
        """Test product string representation"""
        # Test with product name
        product_with_name = Product.objects.create(name='Named Product')
        self.assertEqual(str(product_with_name), 'Named Product')
        
        # Test with empty name
        product_without_name = Product.objects.create(name='')
        self.assertTrue(str(product_without_name).startswith('Product'))


class ProductViewTestCase(ProductFunctionalityBaseTestCase):
    """Test Product views and functionality"""
    
    def test_product_list_view(self):
        """Test product list view"""
        response = self.client.get(reverse('sales_distribution:product_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Product Master')
        self.assertContains(response, 'Test Product 1')
        self.assertContains(response, 'Test Product 2')
    
    def test_product_list_search(self):
        """Test product list search functionality"""
        response = self.client.get(
            reverse('sales_distribution:product_list'),
            {'search': 'Test Product 1'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Product 1')
        self.assertNotContains(response, 'Test Product 2')
    
    def test_product_list_htmx_search(self):
        """Test product list HTMX search functionality"""
        response = self.client.get(
            reverse('sales_distribution:product_list'),
            {'search': 'Test Product 1'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        # Should return partial template for HTMX
        self.assertContains(response, 'Test Product 1')
    
    def test_product_create_view_get(self):
        """Test product create view GET request"""
        response = self.client.get(reverse('sales_distribution:product_create'))
        # Should redirect to list with form (inline creation)
        self.assertEqual(response.status_code, 302)
    
    def test_product_create_view_post_valid(self):
        """Test product creation with valid data"""
        product_data = {
            'name': 'New Product via Test'
        }
        
        response = self.client.post(
            reverse('sales_distribution:product_create'),
            data=product_data
        )
        
        # Should redirect on success or return HTMX response
        self.assertIn(response.status_code, [200, 302])
        
        # Check product was created
        self.assertTrue(Product.objects.filter(name='New Product via Test').exists())
    
    def test_product_create_view_post_invalid(self):
        """Test product creation with invalid data"""
        product_data = {
            'name': ''  # Empty name should be invalid
        }
        
        response = self.client.post(
            reverse('sales_distribution:product_create'),
            data=product_data
        )
        
        # Should return form with errors
        self.assertIn(response.status_code, [200, 400])
        
        # No product should be created with empty name
        self.assertFalse(Product.objects.filter(name='').exists())
    
    def test_product_update_view_get(self):
        """Test product update view GET request"""
        response = self.client.get(
            reverse('sales_distribution:product_edit', kwargs={'id': self.product1.id})
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.product1.name)
    
    def test_product_update_view_post_valid(self):
        """Test product update with valid data"""
        product_data = {
            'name': 'Updated Product Name'
        }
        
        response = self.client.post(
            reverse('sales_distribution:product_edit', kwargs={'id': self.product1.id}),
            data=product_data
        )
        
        # Should redirect on success
        self.assertIn(response.status_code, [200, 302])
        
        # Check product was updated
        self.product1.refresh_from_db()
        self.assertEqual(self.product1.name, 'Updated Product Name')
    
    def test_product_delete_view(self):
        """Test product deletion"""
        product_id = self.product1.id
        
        response = self.client.post(
            reverse('sales_distribution:product_delete', kwargs={'id': product_id})
        )
        
        # Should redirect on success
        self.assertIn(response.status_code, [200, 302])
        
        # Check product was deleted
        self.assertFalse(Product.objects.filter(id=product_id).exists())
    
    def test_product_edit_row_view(self):
        """Test inline editing row view (HTMX)"""
        response = self.client.get(
            reverse('sales_distribution:product_edit_row', kwargs={'id': self.product1.id})
        )
        self.assertEqual(response.status_code, 200)
        # Should return edit form partial
        self.assertContains(response, 'input')
        self.assertContains(response, self.product1.name)
    
    def test_product_cancel_edit_view(self):
        """Test cancel inline edit view (HTMX)"""
        response = self.client.get(
            reverse('sales_distribution:product_cancel_edit', kwargs={'id': self.product1.id})
        )
        self.assertEqual(response.status_code, 200)
        # Should return normal row view
        self.assertContains(response, self.product1.name)


class ProductHTMXTestCase(ProductFunctionalityBaseTestCase):
    """Test HTMX functionality for products"""
    
    def test_htmx_product_create(self):
        """Test HTMX product creation"""
        product_data = {
            'name': 'HTMX Created Product'
        }
        
        response = self.client.post(
            reverse('sales_distribution:product_create'),
            data=product_data,
            HTTP_HX_REQUEST='true'
        )
        
        # HTMX requests should get appropriate response
        self.assertIn(response.status_code, [200, 201])
        
        # Verify product was created
        self.assertTrue(Product.objects.filter(name='HTMX Created Product').exists())
    
    def test_htmx_product_update(self):
        """Test HTMX product update"""
        product_data = {
            'name': 'HTMX Updated Product'
        }
        
        response = self.client.post(
            reverse('sales_distribution:product_edit', kwargs={'id': self.product1.id}),
            data=product_data,
            HTTP_HX_REQUEST='true'
        )
        
        # HTMX requests should get appropriate response
        self.assertIn(response.status_code, [200, 202])
        
        # Verify product was updated
        self.product1.refresh_from_db()
        self.assertEqual(self.product1.name, 'HTMX Updated Product')
    
    def test_htmx_product_delete(self):
        """Test HTMX product deletion"""
        product_id = self.product1.id
        
        response = self.client.post(
            reverse('sales_distribution:product_delete', kwargs={'id': product_id}),
            HTTP_HX_REQUEST='true'
        )
        
        # HTMX delete should return success response
        self.assertIn(response.status_code, [200, 204])
        
        # Verify product was deleted
        self.assertFalse(Product.objects.filter(id=product_id).exists())
    
    def test_htmx_search_real_time(self):
        """Test real-time search via HTMX"""
        # Test search with existing product
        response = self.client.get(
            reverse('sales_distribution:product_list'),
            {'search': 'Test Product 1'},
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Product 1')
        self.assertNotContains(response, 'Test Product 2')
        
        # Test search with no results
        response = self.client.get(
            reverse('sales_distribution:product_list'),
            {'search': 'NonExistent Product'},
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertNotContains(response, 'Test Product 1')
        self.assertNotContains(response, 'Test Product 2')


class ProductFormTestCase(ProductFunctionalityBaseTestCase):
    """Test Product form functionality"""
    
    def test_product_form_fields(self):
        """Test product form has required fields"""
        from ..forms.main_forms import ProductForm
        
        form = ProductForm()
        self.assertIn('name', form.fields)
    
    def test_product_form_validation(self):
        """Test product form validation"""
        from ..forms.main_forms import ProductForm
        
        # Test valid form
        form = ProductForm(data={'name': 'Valid Product Name'})
        self.assertTrue(form.is_valid())
        
        # Test invalid form (empty name)
        form = ProductForm(data={'name': ''})
        self.assertFalse(form.is_valid())
    
    def test_product_form_save(self):
        """Test product form save functionality"""
        from ..forms.main_forms import ProductForm
        
        form = ProductForm(data={'name': 'Form Saved Product'})
        if form.is_valid():
            product = form.save()
            self.assertEqual(product.name, 'Form Saved Product')
            self.assertTrue(Product.objects.filter(name='Form Saved Product').exists())


class ProductIntegrationTestCase(ProductFunctionalityBaseTestCase):
    """Integration tests for product functionality"""
    
    def test_complete_product_lifecycle(self):
        """Test complete product CRUD lifecycle"""
        # 1. Create product
        create_response = self.client.post(
            reverse('sales_distribution:product_create'),
            data={'name': 'Lifecycle Test Product'}
        )
        self.assertIn(create_response.status_code, [200, 302])
        
        # Verify creation
        product = Product.objects.get(name='Lifecycle Test Product')
        self.assertIsNotNone(product)
        
        # 2. Read/View product (in list)
        list_response = self.client.get(reverse('sales_distribution:product_list'))
        self.assertEqual(list_response.status_code, 200)
        self.assertContains(list_response, 'Lifecycle Test Product')
        
        # 3. Update product
        update_response = self.client.post(
            reverse('sales_distribution:product_edit', kwargs={'id': product.id}),
            data={'name': 'Updated Lifecycle Product'}
        )
        self.assertIn(update_response.status_code, [200, 302])
        
        # Verify update
        product.refresh_from_db()
        self.assertEqual(product.name, 'Updated Lifecycle Product')
        
        # 4. Delete product
        delete_response = self.client.post(
            reverse('sales_distribution:product_delete', kwargs={'id': product.id})
        )
        self.assertIn(delete_response.status_code, [200, 302])
        
        # Verify deletion
        self.assertFalse(Product.objects.filter(id=product.id).exists())
    
    def test_product_pagination(self):
        """Test product list pagination"""
        # Create enough products to trigger pagination
        for i in range(25):  # More than the 20 per page limit
            Product.objects.create(name=f'Pagination Test Product {i}')
        
        response = self.client.get(reverse('sales_distribution:product_list'))
        self.assertEqual(response.status_code, 200)
        
        # Should have pagination controls
        self.assertContains(response, 'page')
        
        # Test second page
        response = self.client.get(reverse('sales_distribution:product_list'), {'page': 2})
        self.assertEqual(response.status_code, 200)
    
    def test_product_search_edge_cases(self):
        """Test product search with edge cases"""
        # Create products with special characters
        Product.objects.create(name='Product with "quotes"')
        Product.objects.create(name='Product with & ampersand')
        Product.objects.create(name='Product with <tags>')
        
        # Test search with special characters
        response = self.client.get(
            reverse('sales_distribution:product_list'),
            {'search': 'quotes'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Product with "quotes"')
        
        # Test search with empty string
        response = self.client.get(
            reverse('sales_distribution:product_list'),
            {'search': ''}
        )
        self.assertEqual(response.status_code, 200)
        # Should show all products
        self.assertContains(response, 'Test Product 1')


if __name__ == '__main__':
    import unittest
    unittest.main()