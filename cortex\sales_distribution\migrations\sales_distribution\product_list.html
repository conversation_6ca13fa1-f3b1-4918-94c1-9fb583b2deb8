{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }} - Cortex{% endblock %}

{% block content %}
<!-- SAP S/4 HANA Inspired Design for Product Management -->
<div class="min-h-screen bg-gray-50">
    <div class="max-w-full px-6 py-6">
        
        <!-- SAP S/4 HANA Style Page Header -->
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <!-- Page Title with SAP Typography -->
                    <h1 class="text-2xl font-normal text-gray-800 tracking-tight">Product Master</h1>
                    <nav class="flex items-center text-sm text-gray-500 mt-1">
                        <span class="flex items-center">
                            <i data-lucide="home" class="w-4 h-4 mr-1"></i>
                            Home
                        </span>
                        <i data-lucide="chevron-right" class="w-4 h-4 mx-2"></i>
                        <span>Sales Distribution</span>
                        <i data-lucide="chevron-right" class="w-4 h-4 mx-2"></i>
                        <span>Masters</span>
                        <i data-lucide="chevron-right" class="w-4 h-4 mx-2"></i>
                        <span class="text-blue-600 font-medium">Products</span>
                    </nav>
                </div>
                <div class="flex items-center space-x-3">
                    <!-- SAP Style Action Buttons -->
                    <button hx-get="{% url 'sales_distribution:product_create' %}" 
                            hx-target="#product-form-container"
                            hx-swap="innerHTML"
                            class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Add Product
                    </button>
                    <button class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                        Export
                    </button>
                    <a href="{% url 'sales_distribution:dashboard' %}" 
                       class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>

        <!-- Add Form Container -->
        <div id="product-form-container" class="mb-6"></div>

        <!-- SAP S/4 HANA Style Table Container -->
        <div class="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
            <!-- Table Header with SAP Styling -->
            <div class="border-b border-gray-200 bg-gray-50 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <h2 class="text-lg font-medium text-gray-800">Products ({{ page_obj.paginator.count|default:products.count }})</h2>
                        <div class="text-sm text-gray-500">
                            {% if page_obj.has_other_pages %}
                                Showing {{ page_obj.start_index }}-{{ page_obj.end_index }} of {{ page_obj.paginator.count }}
                            {% endif %}
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <!-- Search Box - SAP Style -->
                        <form method="get" class="flex items-center">
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i data-lucide="search" class="w-4 h-4 text-gray-400"></i>
                                </div>
                                <input type="text" 
                                       name="search"
                                       value="{{ request.GET.search|default:'' }}"
                                       placeholder="Search products..." 
                                       hx-get="{% url 'sales_distribution:product_list' %}"
                                       hx-target="#product-table-container"
                                       hx-swap="innerHTML"
                                       hx-trigger="keyup changed delay:300ms, search"
                                       class="pl-10 pr-4 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       style="width: 250px;">
                            </div>
                        </form>
                        <!-- Filter Button -->
                        <button class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded hover:bg-gray-50">
                            <i data-lucide="filter" class="w-4 h-4 mr-1"></i>
                            Filter
                        </button>
                    </div>
                </div>
            </div>

            <!-- SAP S/4 HANA Style Table -->
            <div id="product-table-container">
                {% include 'sales_distribution/partials/product_table.html' %}
            </div>
        </div>
        
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons after HTMX updates
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        lucide.createIcons();
    });
    
    // SAP-style search functionality
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                this.form.submit();
            }
        });
    }
});
</script>
{% endblock %}