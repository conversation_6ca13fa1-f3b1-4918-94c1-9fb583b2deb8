﻿using System;
using System.Collections;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Xml.Linq;
using System.Web.SessionState;
using System.Data.SqlClient;
using System.Text;
using EnCryptDecrypt;

public partial class Admin_OwnerLogin : System.Web.UI.Page
{
    clsFunctions fun = new clsFunctions();
    protected void Page_Load(object sender, EventArgs e)
    {
        /* To create encrypted owner password*/
        //Response.Write(CryptorEngine.Encrypt("erp#123$sys*~",true));
    }
    
    protected void Button1_Click(object sender, EventArgs e)
    {
        string connStr = fun.Connection();
        SqlConnection con = new SqlConnection(connStr);
        try
        {
            con.Open();

            DataSet ds1 = new DataSet();
            string defLog1 = fun.select("Ownerpassword", "aspnet_Users", "UserName='" + txtUserName.Text + "'");
            SqlCommand cmd1 = new SqlCommand(defLog1, con);
            SqlDataAdapter Da1 = new SqlDataAdapter(cmd1);
            Da1.Fill(ds1, "aspnet_Users");
            string encrypted = ds1.Tables[0].Rows[0][0].ToString();           
            string decryptedText = CryptorEngine.Decrypt(encrypted, true);

            if (decryptedText ==txtPassword.Text)
            {
                Page.Response.Redirect("~/Admin/Menu.aspx");
            }
            else
            {
                Page.Response.Redirect("~/Admin/OwnerLogin.aspx");
            }

        }
        catch (Exception ex)
        {

        }
        finally
        {
            con.Close();

        }

    }


}
