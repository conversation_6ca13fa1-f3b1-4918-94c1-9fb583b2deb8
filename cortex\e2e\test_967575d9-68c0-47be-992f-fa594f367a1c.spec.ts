
import { test } from '@playwright/test';
import { expect } from '@playwright/test';

test('Test_2025-06-13', async ({ page, context }) => {
  
    // Navigate to URL
    await page.goto('http://127.0.0.1:8000/');

    // Fill input field
    await page.fill('input[name="username"]', 'admin');

    // Take screenshot
    await page.screenshot({ path: 'login_page_check.png', { fullPage: true } });

    // Navigate to URL
    await page.goto('http://127.0.0.1:8000/inventory/min-edit/');

    // Take screenshot
    await page.screenshot({ path: 'min_edit_page_styled.png', { fullPage: true } });

    // Navigate to URL
    await page.goto('http://127.0.0.1:8000/inventory/min/');

    // Take screenshot
    await page.screenshot({ path: 'min_main_list_styled.png', { fullPage: true } });
});