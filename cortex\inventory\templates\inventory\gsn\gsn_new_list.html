{% extends 'base.html' %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="bg-white shadow-lg rounded-lg p-6">
    <!-- Header -->
    <div class="bg-blue-600 text-white py-3 px-4 rounded-t-lg mb-6">
        <h1 class="text-xl font-bold">{{ page_title }}</h1>
    </div>

    <!-- Search Form -->
    <div class="mb-6 bg-gray-50 p-4 rounded-lg">
        <form method="get" class="flex items-center space-x-4">
            <div class="flex-1">
                <label for="supplier" class="block text-sm font-medium text-gray-700 mb-1">Supplier:</label>
                <input type="text" 
                       name="supplier" 
                       id="supplier"
                       value="{{ supplier_filter }}"
                       placeholder="Enter supplier name"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <button type="submit" 
                    class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-md font-medium">
                Search
            </button>
        </form>
    </div>

    <!-- GIN Records Table -->
    <div class="overflow-x-auto">
        <table class="w-full table-auto bg-white border border-gray-200">
            <thead class="bg-gray-100">
                <tr>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">SN</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Action</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Fin Year</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">GIN No</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Date</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">PO No</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Name of Supplier</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Challan No</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Challan Date</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
                {% for record in gin_records %}
                <tr class="hover:bg-gray-50">
                    <td class="px-4 py-3 text-sm text-right">{{ forloop.counter }}</td>
                    <td class="px-4 py-3 text-sm text-center">
                        <a href="{% url 'inventory:gsn_new_details' record.id record.supplier_id record.gin_no record.po_no record.fin_year_id %}" 
                           class="text-blue-600 hover:text-blue-800 font-medium">
                            Select
                        </a>
                    </td>
                    <td class="px-4 py-3 text-sm text-center">{{ record.fin_year }}</td>
                    <td class="px-4 py-3 text-sm text-center">{{ record.gin_no }}</td>
                    <td class="px-4 py-3 text-sm text-center">{{ record.gin_date }}</td>
                    <td class="px-4 py-3 text-sm text-center">{{ record.po_no }}</td>
                    <td class="px-4 py-3 text-sm">{{ record.supplier_name }}</td>
                    <td class="px-4 py-3 text-sm text-center">{{ record.challan_no|default:"-" }}</td>
                    <td class="px-4 py-3 text-sm text-center">{{ record.challan_date|default:"-" }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="9" class="px-4 py-8 text-center text-gray-500">
                        <div class="text-lg font-medium">No data to display!</div>
                        <div class="text-sm">No GIN records available for GSN creation.</div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="mt-6 flex justify-center">
        <nav class="flex space-x-2">
            {% if page_obj.has_previous %}
                <a href="?page=1{% if supplier_filter %}&supplier={{ supplier_filter }}{% endif %}" 
                   class="px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">First</a>
                <a href="?page={{ page_obj.previous_page_number }}{% if supplier_filter %}&supplier={{ supplier_filter }}{% endif %}" 
                   class="px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">Previous</a>
            {% endif %}

            <span class="px-3 py-2 bg-blue-600 text-white rounded-md">
                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
            </span>

            {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}{% if supplier_filter %}&supplier={{ supplier_filter }}{% endif %}" 
                   class="px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">Next</a>
                <a href="?page={{ page_obj.paginator.num_pages }}{% if supplier_filter %}&supplier={{ supplier_filter }}{% endif %}" 
                   class="px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">Last</a>
            {% endif %}
        </nav>
    </div>
    {% endif %}
</div>
{% endblock %}