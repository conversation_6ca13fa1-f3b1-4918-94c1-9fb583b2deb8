<!-- sales_distribution/templates/sales_distribution/customer_new.html -->
<!-- Professional Customer New - SAP S/4HANA Inspired Design -->
<!-- Replaces ASP.NET CustomerMaster_New.aspx functionality -->

{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }} - Cortex{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-sap-green-500 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="user-plus" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Customer Master - New</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Register new customers with comprehensive address and contact details</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'sales_distribution:customer_list' %}" 
                   class="inline-flex items-center px-4 py-2.5 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 hover:border-sap-blue-300 transition-all duration-200">
                    <i data-lucide="users" class="w-4 h-4 mr-2"></i>
                    View All Customers
                </a>
                <a href="{% url 'sales_distribution:dashboard' %}" 
                   class="inline-flex items-center px-4 py-2.5 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 hover:border-sap-blue-300 transition-all duration-200">
                    <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-2 py-4 space-y-6">
    
    <!-- Customer Registration Form -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="user-plus" class="w-4 h-4 text-sap-green-600"></i>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-sap-gray-800">Customer Registration</h3>
                    <p class="text-sm text-sap-gray-600">Complete all required fields to register a new customer</p>
                </div>
            </div>
        </div>
        
        <div class="px-6 py-6">
            {% if messages %}
                {% for message in messages %}
                    <div class="mb-4 {% if message.tags == 'error' %}bg-red-50 border border-red-200{% else %}bg-green-50 border border-green-200{% endif %} rounded-lg p-4">
                        <div class="flex">
                            {% if message.tags == 'error' %}
                                <i data-lucide="alert-circle" class="w-5 h-5 text-red-400 mr-2 mt-0.5"></i>
                                <div class="text-sm text-red-600">{{ message }}</div>
                            {% else %}
                                <i data-lucide="check-circle" class="w-5 h-5 text-green-400 mr-2 mt-0.5"></i>
                                <div class="text-sm text-green-600">{{ message }}</div>
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
            
            <form method="post" class="space-y-8">
                {% csrf_token %}
                
                <!-- Customer Name Section -->
                <div class="border-b border-sap-gray-100 pb-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 items-end">
                        <div class="md:col-span-2">
                            <label for="{{ form.customer_name.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                Customer's Name <span class="text-red-500">*</span>
                            </label>
                            {{ form.customer_name }}
                            {% if form.customer_name.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.customer_name.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div class="flex justify-end">
                            <button type="submit" 
                                    onclick="return confirmationAdd()"
                                    class="inline-flex items-center px-6 py-3 bg-sap-green-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-sap-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-green-500 transition-colors duration-200">
                                <i data-lucide="save" class="w-4 h-4 mr-2"></i>
                                Submit
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Address Sections Header -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-4">
                    <div class="text-center">
                        <div class="bg-sap-blue-50 border border-sap-blue-200 rounded-lg p-3">
                            <h4 class="text-sm font-medium text-sap-blue-800 uppercase tracking-wider">REGD. OFFICE</h4>
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="bg-sap-orange-50 border border-sap-orange-200 rounded-lg p-3">
                            <h4 class="text-sm font-medium text-sap-orange-800 uppercase tracking-wider">WORKS/FACTORY</h4>
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="bg-sap-purple-50 border border-sap-purple-200 rounded-lg p-3">
                            <h4 class="text-sm font-medium text-sap-purple-800 uppercase tracking-wider">MATERIAL DELIVERY</h4>
                        </div>
                    </div>
                </div>

                <!-- Address Fields -->
                <div class="space-y-6">
                    <!-- Address -->
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-3">Address <span class="text-red-500">*</span></label>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                {{ form.registered_address }}
                                {% if form.registered_address.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.registered_address.errors.0 }}</p>
                                {% endif %}
                            </div>
                            <div>
                                {{ form.works_address }}
                                {% if form.works_address.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.works_address.errors.0 }}</p>
                                {% endif %}
                            </div>
                            <div>
                                {{ form.material_address }}
                                {% if form.material_address.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.material_address.errors.0 }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Country -->
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-3">Country <span class="text-red-500">*</span></label>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                {{ form.registered_country }}
                                {% if form.registered_country.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.registered_country.errors.0 }}</p>
                                {% endif %}
                            </div>
                            <div>
                                {{ form.works_country }}
                                {% if form.works_country.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.works_country.errors.0 }}</p>
                                {% endif %}
                            </div>
                            <div>
                                {{ form.material_country }}
                                {% if form.material_country.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.material_country.errors.0 }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- State -->
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-3">State <span class="text-red-500">*</span></label>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                {{ form.registered_state }}
                                {% if form.registered_state.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.registered_state.errors.0 }}</p>
                                {% endif %}
                            </div>
                            <div>
                                {{ form.works_state }}
                                {% if form.works_state.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.works_state.errors.0 }}</p>
                                {% endif %}
                            </div>
                            <div>
                                {{ form.material_state }}
                                {% if form.material_state.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.material_state.errors.0 }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- City -->
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-3">City <span class="text-red-500">*</span></label>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                {{ form.registered_city }}
                                {% if form.registered_city.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.registered_city.errors.0 }}</p>
                                {% endif %}
                            </div>
                            <div>
                                {{ form.works_city }}
                                {% if form.works_city.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.works_city.errors.0 }}</p>
                                {% endif %}
                            </div>
                            <div>
                                {{ form.material_city }}
                                {% if form.material_city.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.material_city.errors.0 }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- PIN No. -->
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-3">PIN No. <span class="text-red-500">*</span></label>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                {{ form.registered_pin }}
                                {% if form.registered_pin.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.registered_pin.errors.0 }}</p>
                                {% endif %}
                            </div>
                            <div>
                                {{ form.works_pin }}
                                {% if form.works_pin.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.works_pin.errors.0 }}</p>
                                {% endif %}
                            </div>
                            <div>
                                {{ form.material_pin }}
                                {% if form.material_pin.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.material_pin.errors.0 }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Contact No. -->
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-3">Contact No. <span class="text-red-500">*</span></label>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                {{ form.registered_contact_no }}
                                {% if form.registered_contact_no.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.registered_contact_no.errors.0 }}</p>
                                {% endif %}
                            </div>
                            <div>
                                {{ form.works_contact_no }}
                                {% if form.works_contact_no.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.works_contact_no.errors.0 }}</p>
                                {% endif %}
                            </div>
                            <div>
                                {{ form.material_contact_no }}
                                {% if form.material_contact_no.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.material_contact_no.errors.0 }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Fax No. -->
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-3">Fax No. <span class="text-red-500">*</span></label>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                {{ form.regdfaxno }}
                                {% if form.regdfaxno.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.regdfaxno.errors.0 }}</p>
                                {% endif %}
                            </div>
                            <div>
                                {{ form.workfaxno }}
                                {% if form.workfaxno.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.workfaxno.errors.0 }}</p>
                                {% endif %}
                            </div>
                            <div>
                                {{ form.materialdelfaxno }}
                                {% if form.materialdelfaxno.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.materialdelfaxno.errors.0 }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Details Section -->
                <div class="border-t border-sap-gray-100 pt-6">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-6">Additional Details</h4>
                    
                    <!-- Contact & Communication -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                        <div>
                            <label for="{{ form.contact_person.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                Contact Person <span class="text-red-500">*</span>
                            </label>
                            {{ form.contact_person }}
                            {% if form.contact_person.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.contact_person.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                E-mail <span class="text-red-500">*</span>
                            </label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.email.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.contact_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                Contact No. <span class="text-red-500">*</span>
                            </label>
                            {{ form.contact_no }}
                            {% if form.contact_no.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.contact_no.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Legal & Taxation Details -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                        <div>
                            <label for="{{ form.juridictioncode.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                Jurisdiction Code <span class="text-red-500">*</span>
                            </label>
                            {{ form.juridictioncode }}
                            {% if form.juridictioncode.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.juridictioncode.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.eccno.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                ECC No. <span class="text-red-500">*</span>
                            </label>
                            {{ form.eccno }}
                            {% if form.eccno.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.eccno.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.range.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                Range <span class="text-red-500">*</span>
                            </label>
                            {{ form.range }}
                            {% if form.range.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.range.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                        <div>
                            <label for="{{ form.commissionurate.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                Commission Rate <span class="text-red-500">*</span>
                            </label>
                            {{ form.commissionurate }}
                            {% if form.commissionurate.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.commissionurate.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.divn.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                Division <span class="text-red-500">*</span>
                            </label>
                            {{ form.divn }}
                            {% if form.divn.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.divn.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.panno.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                PAN No. <span class="text-red-500">*</span>
                            </label>
                            {{ form.panno }}
                            {% if form.panno.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.panno.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                        <div>
                            <label for="{{ form.tinvatno.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                TIN/VAT No. <span class="text-red-500">*</span>
                            </label>
                            {{ form.tinvatno }}
                            {% if form.tinvatno.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.tinvatno.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.tincstno.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                TIN/CST No. <span class="text-red-500">*</span>
                            </label>
                            {{ form.tincstno }}
                            {% if form.tincstno.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.tincstno.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.tdscode.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                TDS Code <span class="text-red-500">*</span>
                            </label>
                            {{ form.tdscode }}
                            {% if form.tdscode.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.tdscode.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Remarks Section -->
                <div class="border-t border-sap-gray-100 pt-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="md:col-span-2">
                            <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                                Remarks
                            </label>
                            {{ form.remarks }}
                            {% if form.remarks.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.remarks.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
            </form>
        </div>
    </div>

</div>

<script>
// Confirmation dialog - matches ASP.NET OnClientClick="return confirmationAdd()"
function confirmationAdd() {
    return confirm('Are you sure you want to register this customer?');
}

// Auto-focus on customer name field when page loads
document.addEventListener('DOMContentLoaded', function() {
    const customerNameField = document.getElementById('{{ form.customer_name.id_for_label }}');
    if (customerNameField) {
        customerNameField.focus();
    }
});
</script>
{% endblock %}