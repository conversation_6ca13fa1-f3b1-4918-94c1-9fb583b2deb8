<!-- accounts/templates/accounts/transactions/loan_master_form.html -->
<!-- Loan Master Create/Edit Form Template -->
<!-- Task Group 7: Capital & Loans Management - Loan Master Form (Task 7.3) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{% if object %}Edit Loan{% else %}New Loan{% endif %} - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-blue-600 to-sap-blue-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="credit-card" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">
                        {% if object %}Edit Loan{% else %}New Loan{% endif %}
                    </h1>
                    <p class="text-sm text-sap-gray-600 mt-1">
                        {% if object %}Modify loan details and EMI structure{% else %}Register a new loan with payment schedule{% endif %}
                    </p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:loan_master_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Loan Form -->
    <div class="max-w-6xl mx-auto">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="edit" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                    Loan Information
                </h3>
                <p class="text-sm text-sap-gray-600 mt-1">Complete all required fields to register the loan</p>
            </div>
            
            <form method="post" id="loan-form" class="p-6" x-data="loanForm()">
                {% csrf_token %}
                
                <!-- Basic Loan Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="info" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Basic Loan Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Loan Number -->
                        <div>
                            <label for="{{ form.loan_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Loan Number *
                            </label>
                            {{ form.loan_no|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.loan_no.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.loan_no.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Auto-generated if left blank</p>
                        </div>
                        
                        <!-- Loan Type -->
                        <div>
                            <label for="{{ form.loan_type.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Loan Type *
                            </label>
                            {{ form.loan_type|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.loan_type.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.loan_type.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Loan Account Number -->
                        <div>
                            <label for="{{ form.loan_account_number.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Loan Account Number
                            </label>
                            {{ form.loan_account_number|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.loan_account_number.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.loan_account_number.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Lender Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="building" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                        Lender Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Lender Name -->
                        <div>
                            <label for="{{ form.lender_name.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Lender Name *
                            </label>
                            {{ form.lender_name|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.lender_name.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.lender_name.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Lender Contact -->
                        <div>
                            <label for="{{ form.lender_contact.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Lender Contact
                            </label>
                            {{ form.lender_contact|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.lender_contact.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.lender_contact.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Lender Branch -->
                        <div>
                            <label for="{{ form.lender_branch.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Lender Branch
                            </label>
                            {{ form.lender_branch|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.lender_branch.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.lender_branch.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Lender Address -->
                        <div>
                            <label for="{{ form.lender_address.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Lender Address
                            </label>
                            {{ form.lender_address|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.lender_address.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.lender_address.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Loan Amount and Interest Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="dollar-sign" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                        Loan Amount and Interest
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Loan Amount -->
                        <div>
                            <label for="{{ form.loan_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Loan Amount (₹) *
                            </label>
                            {{ form.loan_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.loan_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.loan_amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Interest Rate -->
                        <div>
                            <label for="{{ form.interest_rate.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Interest Rate (%) *
                            </label>
                            {{ form.interest_rate|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.interest_rate.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.interest_rate.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Interest Type -->
                        <div>
                            <label for="{{ form.interest_type.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Interest Type *
                            </label>
                            {{ form.interest_type|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.interest_type.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.interest_type.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Loan Term and Payment Structure Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="calendar" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                        Loan Term and Payment Structure
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <!-- Loan Tenure -->
                        <div>
                            <label for="{{ form.loan_tenure_months.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Loan Tenure (Months) *
                            </label>
                            {{ form.loan_tenure_months|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.loan_tenure_months.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.loan_tenure_months.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- EMI Amount -->
                        <div>
                            <label for="{{ form.emi_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                EMI Amount (₹)
                            </label>
                            {{ form.emi_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.emi_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.emi_amount.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Auto-calculated if left blank</p>
                        </div>
                        
                        <!-- Installment Frequency -->
                        <div>
                            <label for="{{ form.installment_frequency.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Frequency
                            </label>
                            {{ form.installment_frequency|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.installment_frequency.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.installment_frequency.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- EMI Day -->
                        <div>
                            <label for="{{ form.emi_day.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                EMI Day of Month
                            </label>
                            {{ form.emi_day|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.emi_day.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.emi_day.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Loan Dates Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="clock" class="w-5 h-5 mr-2 text-sap-indigo-600"></i>
                        Important Dates
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <!-- Loan Start Date -->
                        <div>
                            <label for="{{ form.loan_start_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Loan Start Date *
                            </label>
                            {{ form.loan_start_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.loan_start_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.loan_start_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Loan End Date -->
                        <div>
                            <label for="{{ form.loan_end_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Loan End Date
                            </label>
                            {{ form.loan_end_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.loan_end_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.loan_end_date.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Auto-calculated based on tenure</p>
                        </div>
                        
                        <!-- First EMI Date -->
                        <div>
                            <label for="{{ form.first_emi_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                First EMI Date
                            </label>
                            {{ form.first_emi_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.first_emi_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.first_emi_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Next EMI Date -->
                        <div>
                            <label for="{{ form.next_emi_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Next EMI Date
                            </label>
                            {{ form.next_emi_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.next_emi_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.next_emi_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Current Status Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="activity" class="w-5 h-5 mr-2 text-sap-red-600"></i>
                        Current Status and Tracking
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <!-- Loan Status -->
                        <div>
                            <label for="{{ form.status.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Loan Status *
                            </label>
                            {{ form.status|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.status.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.status.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Outstanding Principal -->
                        <div>
                            <label for="{{ form.outstanding_principal.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Outstanding Principal (₹)
                            </label>
                            {{ form.outstanding_principal|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.outstanding_principal.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.outstanding_principal.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Outstanding Interest -->
                        <div>
                            <label for="{{ form.outstanding_interest.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Outstanding Interest (₹)
                            </label>
                            {{ form.outstanding_interest|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.outstanding_interest.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.outstanding_interest.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Paid EMIs -->
                        <div>
                            <label for="{{ form.paid_emis.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Paid EMIs
                            </label>
                            {{ form.paid_emis|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.paid_emis.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.paid_emis.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Additional Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="file-text" class="w-5 h-5 mr-2 text-sap-gray-600"></i>
                        Additional Information
                    </h4>
                    <div class="grid grid-cols-1 gap-6">
                        <!-- Loan Purpose -->
                        <div>
                            <label for="{{ form.loan_purpose.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Loan Purpose
                            </label>
                            {{ form.loan_purpose|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.loan_purpose.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.loan_purpose.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Brief description of loan purpose and usage</p>
                        </div>
                        
                        <!-- Remarks -->
                        <div>
                            <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Remarks
                            </label>
                            {{ form.remarks|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.remarks.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.remarks.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Additional notes or special conditions</p>
                        </div>
                    </div>
                </div>
                
                <!-- EMI Calculation Display -->
                <div class="mb-8 bg-sap-blue-50 border border-sap-blue-200 rounded-lg p-6" x-show="calculatedEMI > 0">
                    <h4 class="text-lg font-medium text-sap-blue-800 mb-4 flex items-center">
                        <i data-lucide="calculator" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        EMI Calculation Summary
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="text-center">
                            <p class="text-sm text-sap-blue-600">Monthly EMI</p>
                            <p class="text-xl font-bold text-sap-blue-800" x-text="'₹' + calculatedEMI.toFixed(2)"></p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-blue-600">Total Interest</p>
                            <p class="text-xl font-bold text-sap-blue-800" x-text="'₹' + totalInterest.toFixed(2)"></p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-blue-600">Total Amount</p>
                            <p class="text-xl font-bold text-sap-blue-800" x-text="'₹' + totalAmount.toFixed(2)"></p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-blue-600">Interest/Principal Ratio</p>
                            <p class="text-xl font-bold text-sap-blue-800" x-text="interestRatio + '%'"></p>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="resetForm()" 
                                class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="rotate-ccw" class="w-4 h-4 inline mr-2"></i>
                            Reset
                        </button>
                        <button type="button" @click="calculateEMI()" 
                                class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="calculator" class="w-4 h-4 inline mr-2"></i>
                            Calculate EMI
                        </button>
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="{% url 'accounts:loan_master_list' %}" 
                           class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                            {% if object %}Update Loan{% else %}Create Loan{% endif %}
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Loan Registration Guidelines -->
        <div class="mt-6 bg-sap-green-50 border border-sap-green-200 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i data-lucide="info" class="w-5 h-5 text-sap-green-600"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-sap-green-800">Loan Registration Guidelines</h3>
                    <div class="mt-2 text-sm text-sap-green-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Ensure all mandatory fields are completed accurately</li>
                            <li>Loan number will be auto-generated if not provided</li>
                            <li>Use the EMI calculator to verify payment amounts</li>
                            <li>Set appropriate interest rates based on loan type</li>
                            <li>Keep lender contact information up to date</li>
                            <li>Monitor outstanding amounts and EMI schedules regularly</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function loanForm() {
    return {
        calculatedEMI: 0,
        totalInterest: 0,
        totalAmount: 0,
        interestRatio: 0,
        
        calculateEMI() {
            const loanAmount = parseFloat(document.getElementById('{{ form.loan_amount.id_for_label }}').value) || 0;
            const interestRate = parseFloat(document.getElementById('{{ form.interest_rate.id_for_label }}').value) || 0;
            const tenureMonths = parseFloat(document.getElementById('{{ form.loan_tenure_months.id_for_label }}').value) || 0;
            
            if (loanAmount && interestRate && tenureMonths) {
                // EMI Calculation using formula: EMI = [P * r * (1+r)^n] / [(1+r)^n-1]
                const monthlyRate = interestRate / (12 * 100);
                const emi = (loanAmount * monthlyRate * Math.pow(1 + monthlyRate, tenureMonths)) / 
                           (Math.pow(1 + monthlyRate, tenureMonths) - 1);
                
                this.calculatedEMI = emi;
                this.totalAmount = emi * tenureMonths;
                this.totalInterest = this.totalAmount - loanAmount;
                this.interestRatio = ((this.totalInterest / loanAmount) * 100).toFixed(1);
                
                // Update EMI amount field
                const emiField = document.getElementById('{{ form.emi_amount.id_for_label }}');
                if (emiField) {
                    emiField.value = emi.toFixed(2);
                }
                
                // Update loan end date
                this.updateLoanEndDate();
            } else {
                alert('Please enter loan amount, interest rate, and tenure to calculate EMI.');
            }
        },
        
        updateLoanEndDate() {
            const startDate = document.getElementById('{{ form.loan_start_date.id_for_label }}').value;
            const tenureMonths = document.getElementById('{{ form.loan_tenure_months.id_for_label }}').value;
            
            if (startDate && tenureMonths) {
                const start = new Date(startDate);
                const endDate = new Date(start.setMonth(start.getMonth() + parseInt(tenureMonths)));
                
                const endDateField = document.getElementById('{{ form.loan_end_date.id_for_label }}');
                if (endDateField) {
                    endDateField.value = endDate.toISOString().split('T')[0];
                }
            }
        }
    }
}

function resetForm() {
    if (confirm('Are you sure you want to reset the form? All unsaved changes will be lost.')) {
        document.getElementById('loan-form').reset();
    }
}

// Auto-generate loan number if creating new loan
document.addEventListener('DOMContentLoaded', function() {
    const loanNoInput = document.getElementById('{{ form.loan_no.id_for_label }}');
    if (loanNoInput && !loanNoInput.value) {
        // Generate loan number based on current timestamp
        const today = new Date();
        const year = today.getFullYear().toString().substr(-2);
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        const sequence = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        
        loanNoInput.value = `LN-${year}${month}${day}${sequence}`;
    }
    
    // Auto-calculate loan end date when start date or tenure changes
    const startDateField = document.getElementById('{{ form.loan_start_date.id_for_label }}');
    const tenureField = document.getElementById('{{ form.loan_tenure_months.id_for_label }}');
    
    [startDateField, tenureField].forEach(field => {
        if (field) {
            field.addEventListener('change', function() {
                updateLoanEndDate();
            });
        }
    });
    
    // Auto-calculate outstanding principal
    const loanAmountField = document.getElementById('{{ form.loan_amount.id_for_label }}');
    const outstandingPrincipalField = document.getElementById('{{ form.outstanding_principal.id_for_label }}');
    
    if (loanAmountField && outstandingPrincipalField && !outstandingPrincipalField.value) {
        loanAmountField.addEventListener('change', function() {
            outstandingPrincipalField.value = this.value;
        });
    }
    
    lucide.createIcons();
});

function updateLoanEndDate() {
    const startDate = document.getElementById('{{ form.loan_start_date.id_for_label }}').value;
    const tenureMonths = document.getElementById('{{ form.loan_tenure_months.id_for_label }}').value;
    
    if (startDate && tenureMonths) {
        const start = new Date(startDate);
        const endDate = new Date(start.setMonth(start.getMonth() + parseInt(tenureMonths)));
        
        const endDateField = document.getElementById('{{ form.loan_end_date.id_for_label }}');
        if (endDateField) {
            endDateField.value = endDate.toISOString().split('T')[0];
        }
    }
}
</script>
{% endblock %}