# accounts/tax_calculator_views.py
# Django views for Tax Calculator tools in Accounts module
# Task Group 4: Taxation Management - Tax Calculator Views

from django.shortcuts import get_object_or_404
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import TemplateView
from django.http import JsonResponse
from decimal import Decimal, InvalidOperation
import json

from .models import ExciseDuty, VAT, TDSCode
from .tax_calculation_engine import (
    TaxCalculationEngine, CalculationMethod,
    get_default_tax_rates
)


class TaxCalculatorDashboardView(LoginRequiredMixin, TemplateView):
    """
    Main dashboard for all tax calculation tools
    """
    template_name = 'accounts/tax_calculator_dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get available tax types and their counts
        context['available_taxes'] = {
            'excise_duties': ExciseDuty.objects.filter(is_active=True).count(),
            'vat_rates': VAT.objects.count(),
            'tds_codes': TDSCode.objects.count(),
        }
        
        # Get default tax rates
        context['default_rates'] = get_default_tax_rates()
        
        # Get recent calculations (if we stored them)
        # For now, just provide sample data
        context['recent_calculations'] = []
        
        return context


class CompositeInvoiceTaxCalculatorView(LoginRequiredMixin, TemplateView):
    """
    Comprehensive invoice tax calculator with all tax types
    """
    template_name = 'accounts/composite_tax_calculator.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get available options
        context['excise_duties'] = ExciseDuty.objects.filter(is_active=True).order_by('terms')
        context['vat_rates'] = VAT.objects.all().order_by('vat_percentage')
        context['tds_codes'] = TDSCode.objects.all().order_by('tds_code')
        
        # Get defaults
        context['default_rates'] = get_default_tax_rates()
        
        return context
    
    def post(self, request, *args, **kwargs):
        """Handle tax calculation request"""
        try:
            # Parse request data
            base_amount = Decimal(request.POST.get('base_amount', '0'))
            calculation_method = request.POST.get('calculation_method', 'exclusive')
            
            # Build tax configuration
            tax_config = {}
            
            # Excise duty configuration
            excise_duty_id = request.POST.get('excise_duty_id')
            if excise_duty_id and excise_duty_id != '':
                try:
                    excise_duty = ExciseDuty.objects.get(id=int(excise_duty_id))
                    tax_config['excise'] = {
                        'rate': float(excise_duty.value or 0),
                        'accessible_value': float(excise_duty.accessible_value or 100),
                        'edu_cess': float(excise_duty.edu_cess or 0),
                        'she_cess': float(excise_duty.she_cess or 0)
                    }
                except (ExciseDuty.DoesNotExist, ValueError):
                    pass
            
            # VAT configuration
            vat_rate = request.POST.get('vat_rate')
            if vat_rate and vat_rate != '':
                try:
                    tax_config['vat'] = {'rate': float(vat_rate)}
                except ValueError:
                    pass
            
            # TDS configuration
            tds_rate = request.POST.get('tds_rate')
            tds_threshold = request.POST.get('tds_threshold')
            if tds_rate and tds_rate != '':
                try:
                    tds_config = {'rate': float(tds_rate)}
                    if tds_threshold and tds_threshold != '':
                        tds_config['threshold'] = float(tds_threshold)
                    tax_config['tds'] = tds_config
                except ValueError:
                    pass
            
            # Calculate taxes
            engine = TaxCalculationEngine()
            
            # Validate configuration
            validation_errors = engine.validate_tax_rates(tax_config)
            if validation_errors:
                context = self.get_context_data(**kwargs)
                context['calculation_errors'] = validation_errors
                return self.render_to_response(context)
            
            # Perform calculation
            calc_method = CalculationMethod.INCLUSIVE if calculation_method == 'inclusive' else CalculationMethod.EXCLUSIVE
            result = engine.calculate_composite_tax(base_amount, tax_config, calc_method)
            
            # Get summary
            summary = engine.get_tax_breakdown_summary(result)
            
            context = self.get_context_data(**kwargs)
            context['calculation_result'] = result
            context['calculation_summary'] = summary
            context['form_data'] = request.POST  # To preserve form values
            
            return self.render_to_response(context)
            
        except (ValueError, InvalidOperation) as e:
            context = self.get_context_data(**kwargs)
            context['calculation_errors'] = [f"Invalid input: {str(e)}"]
            return self.render_to_response(context)
        
        except Exception as e:
            context = self.get_context_data(**kwargs)
            context['calculation_errors'] = [f"Calculation error: {str(e)}"]
            return self.render_to_response(context)


class ReverseCalculatorView(LoginRequiredMixin, TemplateView):
    """
    Reverse tax calculator - calculate base amount from total amount
    """
    template_name = 'accounts/reverse_tax_calculator.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get available options
        context['excise_duties'] = ExciseDuty.objects.filter(is_active=True).order_by('terms')
        context['vat_rates'] = VAT.objects.all().order_by('vat_percentage')
        context['tds_codes'] = TDSCode.objects.all().order_by('tds_code')
        
        return context
    
    def post(self, request, *args, **kwargs):
        """Handle reverse calculation request"""
        try:
            # Parse request data
            total_amount = Decimal(request.POST.get('total_amount', '0'))
            
            # Build tax configuration (same as composite calculator)
            tax_config = {}
            
            # Excise duty configuration
            excise_duty_id = request.POST.get('excise_duty_id')
            if excise_duty_id and excise_duty_id != '':
                try:
                    excise_duty = ExciseDuty.objects.get(id=int(excise_duty_id))
                    tax_config['excise'] = {
                        'rate': float(excise_duty.value or 0),
                        'accessible_value': float(excise_duty.accessible_value or 100),
                        'edu_cess': float(excise_duty.edu_cess or 0),
                        'she_cess': float(excise_duty.she_cess or 0)
                    }
                except (ExciseDuty.DoesNotExist, ValueError):
                    pass
            
            # VAT configuration
            vat_rate = request.POST.get('vat_rate')
            if vat_rate and vat_rate != '':
                try:
                    tax_config['vat'] = {'rate': float(vat_rate)}
                except ValueError:
                    pass
            
            # Calculate reverse
            engine = TaxCalculationEngine()
            result = engine.reverse_calculate_base_amount(total_amount, tax_config)
            
            # Get summary
            summary = engine.get_tax_breakdown_summary(result)
            
            context = self.get_context_data(**kwargs)
            context['calculation_result'] = result
            context['calculation_summary'] = summary
            context['form_data'] = request.POST
            
            return self.render_to_response(context)
            
        except Exception as e:
            context = self.get_context_data(**kwargs)
            context['calculation_errors'] = [f"Calculation error: {str(e)}"]
            return self.render_to_response(context)


class TaxComparisonView(LoginRequiredMixin, TemplateView):
    """
    Compare tax calculations across different scenarios
    """
    template_name = 'accounts/tax_comparison.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get available options
        context['excise_duties'] = ExciseDuty.objects.filter(is_active=True).order_by('terms')
        context['vat_rates'] = VAT.objects.all().order_by('vat_percentage')
        
        return context
    
    def post(self, request, *args, **kwargs):
        """Handle comparison request"""
        try:
            base_amount = Decimal(request.POST.get('base_amount', '0'))
            
            # Get comparison scenarios
            scenarios = []
            
            # Scenario 1: Current rates
            scenario1_config = self._build_tax_config_from_request(request, 'scenario1_')
            if scenario1_config:
                engine = TaxCalculationEngine()
                result1 = engine.calculate_composite_tax(base_amount, scenario1_config)
                scenarios.append({
                    'name': request.POST.get('scenario1_name', 'Scenario 1'),
                    'config': scenario1_config,
                    'result': result1,
                    'summary': engine.get_tax_breakdown_summary(result1)
                })
            
            # Scenario 2: Alternative rates
            scenario2_config = self._build_tax_config_from_request(request, 'scenario2_')
            if scenario2_config:
                engine = TaxCalculationEngine()
                result2 = engine.calculate_composite_tax(base_amount, scenario2_config)
                scenarios.append({
                    'name': request.POST.get('scenario2_name', 'Scenario 2'),
                    'config': scenario2_config,
                    'result': result2,
                    'summary': engine.get_tax_breakdown_summary(result2)
                })
            
            # Calculate differences
            if len(scenarios) == 2:
                diff = {
                    'tax_amount_diff': scenarios[1]['result'].total_tax_amount - scenarios[0]['result'].total_tax_amount,
                    'total_amount_diff': scenarios[1]['result'].total_amount - scenarios[0]['result'].total_amount,
                    'effective_rate_diff': scenarios[1]['result'].effective_tax_rate - scenarios[0]['result'].effective_tax_rate,
                    'percentage_change': (
                        (scenarios[1]['result'].total_tax_amount - scenarios[0]['result'].total_tax_amount) /
                        scenarios[0]['result'].total_tax_amount * 100
                    ) if scenarios[0]['result'].total_tax_amount > 0 else 0
                }
                scenarios.append({'name': 'Difference', 'diff': diff})
            
            context = self.get_context_data(**kwargs)
            context['scenarios'] = scenarios
            context['base_amount'] = base_amount
            context['form_data'] = request.POST
            
            return self.render_to_response(context)
            
        except Exception as e:
            context = self.get_context_data(**kwargs)
            context['calculation_errors'] = [f"Comparison error: {str(e)}"]
            return self.render_to_response(context)
    
    def _build_tax_config_from_request(self, request, prefix):
        """Build tax configuration from request data with prefix"""
        tax_config = {}
        
        # Excise duty
        excise_duty_id = request.POST.get(f'{prefix}excise_duty_id')
        if excise_duty_id and excise_duty_id != '':
            try:
                excise_duty = ExciseDuty.objects.get(id=int(excise_duty_id))
                tax_config['excise'] = {
                    'rate': float(excise_duty.value or 0),
                    'accessible_value': float(excise_duty.accessible_value or 100),
                    'edu_cess': float(excise_duty.edu_cess or 0),
                    'she_cess': float(excise_duty.she_cess or 0)
                }
            except (ExciseDuty.DoesNotExist, ValueError):
                pass
        
        # VAT
        vat_rate = request.POST.get(f'{prefix}vat_rate')
        if vat_rate and vat_rate != '':
            try:
                tax_config['vat'] = {'rate': float(vat_rate)}
            except ValueError:
                pass
        
        return tax_config


class TaxRateAnalysisView(LoginRequiredMixin, TemplateView):
    """
    Analysis view for tax rate impacts and statistics
    """
    template_name = 'accounts/tax_rate_analysis.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get tax rate statistics
        context['excise_stats'] = self._get_excise_statistics()
        context['vat_stats'] = self._get_vat_statistics()
        context['tds_stats'] = self._get_tds_statistics()
        
        # Sample calculation examples
        context['sample_calculations'] = self._get_sample_calculations()
        
        return context
    
    def _get_excise_statistics(self):
        """Get excise duty statistics"""
        excise_duties = ExciseDuty.objects.filter(is_active=True)
        
        if not excise_duties.exists():
            return None
        
        rates = [float(duty.value or 0) for duty in excise_duties]
        return {
            'count': excise_duties.count(),
            'min_rate': min(rates),
            'max_rate': max(rates),
            'avg_rate': sum(rates) / len(rates),
            'default_excise': excise_duties.filter(is_default_excise=True).first(),
            'default_service_tax': excise_duties.filter(is_default_service_tax=True).first()
        }
    
    def _get_vat_statistics(self):
        """Get VAT statistics"""
        vat_rates = VAT.objects.all()
        
        if not vat_rates.exists():
            return None
        
        rates = [float(vat.vat_percentage or 0) for vat in vat_rates]
        return {
            'count': vat_rates.count(),
            'min_rate': min(rates),
            'max_rate': max(rates),
            'avg_rate': sum(rates) / len(rates)
        }
    
    def _get_tds_statistics(self):
        """Get TDS statistics"""
        tds_codes = TDSCode.objects.all()
        
        if not tds_codes.exists():
            return None
        
        rates = [float(tds.tds_percentage or 0) for tds in tds_codes]
        return {
            'count': tds_codes.count(),
            'min_rate': min(rates),
            'max_rate': max(rates),
            'avg_rate': sum(rates) / len(rates)
        }
    
    def _get_sample_calculations(self):
        """Get sample calculations for different amount ranges"""
        engine = TaxCalculationEngine()
        samples = []
        
        # Get default tax configuration
        default_rates = get_default_tax_rates()
        if not default_rates:
            return samples
        
        tax_config = {}
        if 'default_excise' in default_rates:
            tax_config['excise'] = default_rates['default_excise']
        
        # Sample amounts
        amounts = [1000, 10000, 100000, 1000000]
        
        for amount in amounts:
            try:
                result = engine.calculate_composite_tax(amount, tax_config)
                samples.append({
                    'amount': amount,
                    'tax_amount': float(result.total_tax_amount),
                    'total_amount': float(result.total_amount),
                    'effective_rate': float(result.effective_tax_rate)
                })
            except:
                continue
        
        return samples


# AJAX Views for Tax Calculator

def ajax_calculate_tax(request):
    """
    AJAX endpoint for quick tax calculations
    """
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)
    
    try:
        data = json.loads(request.body)
        
        base_amount = Decimal(str(data.get('base_amount', 0)))
        calculation_method = data.get('calculation_method', 'exclusive')
        tax_config = data.get('tax_config', {})
        
        engine = TaxCalculationEngine()
        
        # Validate configuration
        validation_errors = engine.validate_tax_rates(tax_config)
        if validation_errors:
            return JsonResponse({'error': 'Validation failed', 'details': validation_errors}, status=400)
        
        # Calculate
        calc_method = CalculationMethod.INCLUSIVE if calculation_method == 'inclusive' else CalculationMethod.EXCLUSIVE
        result = engine.calculate_composite_tax(base_amount, tax_config, calc_method)
        
        # Return summary
        summary = engine.get_tax_breakdown_summary(result)
        
        return JsonResponse({
            'success': True,
            'result': summary,
            'effective_rate': float(result.effective_tax_rate),
            'total_amount': float(result.total_amount),
            'total_tax': float(result.total_tax_amount)
        })
    
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


def ajax_get_tax_breakdown(request):
    """
    AJAX endpoint to get detailed tax breakdown
    """
    if request.method != 'GET':
        return JsonResponse({'error': 'Method not allowed'}, status=405)
    
    try:
        excise_duty_id = request.GET.get('excise_duty_id')
        amount = Decimal(request.GET.get('amount', '0'))
        
        if not excise_duty_id or amount <= 0:
            return JsonResponse({'error': 'Invalid parameters'}, status=400)
        
        excise_duty = get_object_or_404(ExciseDuty, id=excise_duty_id)
        
        engine = TaxCalculationEngine()
        result = engine.calculate_excise_duty(amount, excise_duty)
        
        breakdown = {
            'base_amount': float(result.base_amount),
            'components': [],
            'total_tax': float(result.total_tax_amount),
            'total_amount': float(result.total_amount),
            'effective_rate': float(result.effective_tax_rate)
        }
        
        for component in result.tax_components:
            breakdown['components'].append({
                'name': component.name,
                'rate': float(component.rate),
                'amount': float(component.amount),
                'base_amount': float(component.base_amount),
                'is_compound': component.is_compound
            })
        
        return JsonResponse({'success': True, 'breakdown': breakdown})
    
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


def ajax_compare_tax_scenarios(request):
    """
    AJAX endpoint for tax scenario comparison
    """
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)
    
    try:
        data = json.loads(request.body)
        
        base_amount = Decimal(str(data.get('base_amount', 0)))
        scenarios = data.get('scenarios', [])
        
        if len(scenarios) < 2:
            return JsonResponse({'error': 'At least 2 scenarios required'}, status=400)
        
        engine = TaxCalculationEngine()
        results = []
        
        for i, scenario in enumerate(scenarios):
            tax_config = scenario.get('tax_config', {})
            result = engine.calculate_composite_tax(base_amount, tax_config)
            
            results.append({
                'scenario_name': scenario.get('name', f'Scenario {i+1}'),
                'total_tax': float(result.total_tax_amount),
                'total_amount': float(result.total_amount),
                'effective_rate': float(result.effective_tax_rate),
                'breakdown': engine.get_tax_breakdown_summary(result)
            })
        
        # Calculate differences
        if len(results) >= 2:
            comparison = {
                'tax_difference': results[1]['total_tax'] - results[0]['total_tax'],
                'amount_difference': results[1]['total_amount'] - results[0]['total_amount'],
                'rate_difference': results[1]['effective_rate'] - results[0]['effective_rate'],
                'percentage_change': (
                    (results[1]['total_tax'] - results[0]['total_tax']) / results[0]['total_tax'] * 100
                ) if results[0]['total_tax'] > 0 else 0
            }
        else:
            comparison = {}
        
        return JsonResponse({
            'success': True,
            'scenarios': results,
            'comparison': comparison
        })
    
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)