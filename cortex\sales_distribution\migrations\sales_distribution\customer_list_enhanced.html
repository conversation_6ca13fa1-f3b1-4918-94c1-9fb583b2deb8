{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }} - Cortex{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-sap-blue-500 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="users" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Customer Master</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Search and manage customer database with real-time filtering</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <div class="text-right mr-4 px-4 py-2 bg-sap-gray-50 rounded-lg border border-sap-gray-200">
                    <p class="text-2xs font-medium text-sap-gray-500 uppercase tracking-wide">Total Customers</p>
                    <p class="text-lg font-semibold text-sap-blue-600" id="customer-count">{{ paginator.count|default:customers.count }}</p>
                </div>
                <a href="{% url 'sales_distribution:customer_new' %}" 
                   class="inline-flex items-center px-4 py-2.5 border border-sap-blue-300 rounded-lg text-sm font-medium text-white bg-sap-blue-500 hover:bg-sap-blue-600 hover:border-sap-blue-400 transition-all duration-200">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    Add Customer
                </a>
                <a href="{% url 'sales_distribution:dashboard' %}" 
                   class="inline-flex items-center px-4 py-2.5 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 hover:border-sap-blue-300 transition-all duration-200">
                    <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-2 py-2 space-y-2">
    
    <!-- Real-time Search Card - SAP S/4HANA Style -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-4 py-2 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-6 h-6 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="search" class="w-3 h-3 text-sap-blue-600"></i>
                    </div>
                    <div>
                        <h2 class="text-base font-semibold text-sap-gray-800">Customer Search</h2>
                        <p class="text-sap-gray-600 text-xs">Search customers by name, ID, or address</p>
                    </div>
                </div>
                <div class="text-sm text-sap-gray-600" id="search-status">
                    {% if request.GET.search %}
                        Showing {{ customers|length }} customers matching "{{ request.GET.search }}"
                    {% else %}
                        Showing {{ customers|length }} customers
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="p-3">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-3">
                <!-- Search Input -->
                <div class="md:col-span-2">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i data-lucide="search" class="w-4 h-4 text-sap-gray-400"></i>
                        </div>
                        <input type="text" 
                               name="search" 
                               id="customer-search"
                               value="{{ request.GET.search|default:'' }}"
                               hx-get="{% url 'sales_distribution:customer_list' %}"
                               hx-target="#customer-table-container"
                               hx-swap="innerHTML"
                               hx-trigger="keyup changed delay:300ms, search"
                               class="pl-10 w-full px-3 py-2 border border-sap-gray-300 rounded text-sm focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"
                               placeholder="Search customers by name, code, or address...">
                    </div>
                </div>
                
                <!-- Quick Filters -->
                <div>
                    <select name="filter" 
                            id="filter"
                            hx-get="{% url 'sales_distribution:customer_list' %}"
                            hx-target="#customer-table-container"
                            hx-swap="innerHTML"
                            hx-trigger="change"
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded text-sm focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        <option value="">All Customers</option>
                        <option value="recent" {% if request.GET.filter == 'recent' %}selected{% endif %}>Recent (Last 30 days)</option>
                        <option value="active" {% if request.GET.filter == 'active' %}selected{% endif %}>Active Only</option>
                    </select>
                </div>
                
                <!-- Clear Filters -->
                <div class="flex items-center">
                    {% if request.GET.search or request.GET.filter %}
                        <a href="{% url 'sales_distribution:customer_list' %}" 
                           class="w-full inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-sap-gray-600 bg-white border border-sap-gray-300 rounded hover:bg-sap-gray-50">
                            <i data-lucide="x" class="w-4 h-4 mr-1"></i>
                            Clear
                        </a>
                    {% else %}
                        <button class="w-full inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-sap-gray-600 bg-white border border-sap-gray-300 rounded hover:bg-sap-gray-50">
                            <i data-lucide="filter" class="w-4 h-4 mr-1"></i>
                            Filter
                        </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Table - SAP S/4HANA Style -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="bg-sap-gray-50 px-4 py-2 border-b border-sap-gray-200 rounded-t-lg">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-6 h-6 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="users" class="w-3 h-3 text-sap-blue-600"></i>
                    </div>
                    <div>
                        <h2 class="text-base font-semibold text-sap-gray-800">Customer Directory ({{ paginator.count|default:customers.count }})</h2>
                        <p class="text-sap-gray-600 text-xs">Manage existing customers and settings</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="text-xs text-sap-gray-500">
                        {% if is_paginated %}
                            Showing {{ page_obj.start_index }}-{{ page_obj.end_index }} of {{ page_obj.paginator.count }}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Table Content -->
        <div id="customer-table-container">
            {% include 'sales_distribution/partials/customer_table_enhanced.html' %}
        </div>
    </div>

    <!-- Pagination - SAP Style -->
    {% if is_paginated %}
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-4 py-2">
            <div class="flex items-center justify-between">
                <div class="text-sm text-sap-gray-500">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ paginator.count }} results
                </div>
                <div class="flex items-center space-x-2">
                    <!-- Previous Page -->
                    {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.filter %}&filter={{ request.GET.filter }}{% endif %}" 
                           class="inline-flex items-center px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded hover:bg-sap-gray-50 hover:text-sap-gray-700">
                            <i data-lucide="chevron-left" class="w-4 h-4 mr-1"></i>
                            Previous
                        </a>
                    {% else %}
                        <span class="inline-flex items-center px-3 py-2 text-sm font-medium text-sap-gray-300 bg-white border border-sap-gray-300 rounded cursor-not-allowed">
                            <i data-lucide="chevron-left" class="w-4 h-4 mr-1"></i>
                            Previous
                        </span>
                    {% endif %}
                    
                    <!-- Page Numbers -->
                    <div class="flex items-center space-x-1">
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <span class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-sap-blue-600 border border-sap-blue-600 rounded">
                                    {{ num }}
                                </span>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <a href="?page={{ num }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.filter %}&filter={{ request.GET.filter }}{% endif %}" 
                                   class="inline-flex items-center px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded hover:bg-sap-gray-50 hover:text-sap-gray-700">
                                    {{ num }}
                                </a>
                            {% endif %}
                        {% endfor %}
                    </div>
                    
                    <!-- Next Page -->
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.filter %}&filter={{ request.GET.filter }}{% endif %}" 
                           class="inline-flex items-center px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded hover:bg-sap-gray-50 hover:text-sap-gray-700">
                            Next
                            <i data-lucide="chevron-right" class="w-4 h-4 ml-1"></i>
                        </a>
                    {% else %}
                        <span class="inline-flex items-center px-3 py-2 text-sm font-medium text-sap-gray-300 bg-white border border-sap-gray-300 rounded cursor-not-allowed">
                            Next
                            <i data-lucide="chevron-right" class="w-4 h-4 ml-1"></i>
                        </span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

</div>

<!-- Enhanced HTMX Configuration -->
<script>
// Enhanced HTMX event handling with SAP styling
document.body.addEventListener('htmx:beforeRequest', function(evt) {        
    const tableContainer = document.getElementById('customer-table-container');
    if (tableContainer && evt.detail.requestConfig.target === '#customer-table-container') {
        tableContainer.style.position = 'relative';
        tableContainer.insertAdjacentHTML('beforeend', 
            '<div class="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-10">' +
            '<div class="flex items-center space-x-2 text-sap-gray-600 bg-white px-4 py-2 rounded-lg border border-sap-gray-200">' +
            '<i data-lucide="loader-2" class="w-5 h-5 animate-spin text-sap-blue-500"></i>' +
            '<span class="text-sm font-medium">Updating...</span>' +
            '</div></div>'
        );
    }
});

document.body.addEventListener('htmx:afterRequest', function(evt) {
    document.querySelectorAll('.absolute.inset-0.bg-white').forEach(overlay => {
        overlay.remove();
    });
    
    lucide.createIcons();
    updateCustomerCounter();
    
    // Update search status
    if (evt.target.id === 'customer-table-container') {
        const searchValue = document.getElementById('customer-search').value;
        const statusElement = document.getElementById('search-status');
        const rows = evt.target.querySelectorAll('tbody tr:not(.empty-state)');
        
        if (searchValue) {
            statusElement.textContent = `Showing ${rows.length} customers matching "${searchValue}"`;
        } else {
            statusElement.textContent = `Showing ${rows.length} customers`;
        }
    }
});

function updateCustomerCounter() {
    const rows = document.querySelectorAll('#customer-table-container tbody tr:not(.empty-state)');
    const counter = document.getElementById('customer-count');
    if (counter) {
        counter.textContent = rows.length;
    }
}

document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
    updateCustomerCounter();
});
</script>
{% endblock %}