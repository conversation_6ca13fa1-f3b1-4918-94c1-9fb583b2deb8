from django import forms
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from ..models import MaterialCreditNote, MCNLineItem, MCNApprovalHistory


class MaterialCreditNoteForm(forms.ModelForm):
    """Form for creating and editing Material Credit Notes"""
    
    class Meta:
        model = MaterialCreditNote
        fields = [
            'mcn_date', 'adjustment_type', 'reference_number', 'reference_date',
            'priority', 'department_id', 'reason_code', 'reason_description',
            'remarks', 'is_emergency'
        ]
        widgets = {
            'mcn_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'required': True
            }),
            'adjustment_type': forms.Select(attrs={
                'class': 'form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'required': True
            }),
            'reference_number': forms.TextInput(attrs={
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'placeholder': 'Enter reference number'
            }),
            'reference_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500'
            }),
            'priority': forms.Select(attrs={
                'class': 'form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500'
            }),
            'department_id': forms.NumberInput(attrs={
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'placeholder': 'Department ID'
            }),
            'reason_code': forms.TextInput(attrs={
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'placeholder': 'Reason code'
            }),
            'reason_description': forms.Textarea(attrs={
                'class': 'form-textarea rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'rows': 3,
                'placeholder': 'Detailed reason for adjustment'
            }),
            'remarks': forms.Textarea(attrs={
                'class': 'form-textarea rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'rows': 2,
                'placeholder': 'Additional remarks'
            }),
            'is_emergency': forms.CheckboxInput(attrs={
                'class': 'form-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500'
            })
        }
        labels = {
            'mcn_date': 'MCN Date',
            'adjustment_type': 'Adjustment Type',
            'reference_number': 'Reference Number',
            'reference_date': 'Reference Date',
            'priority': 'Priority',
            'department_id': 'Department ID',
            'reason_code': 'Reason Code',
            'reason_description': 'Reason Description',
            'remarks': 'Remarks',
            'is_emergency': 'Emergency MCN'
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Set required fields
        self.fields['mcn_date'].required = True
        self.fields['adjustment_type'].required = True
        
    def clean_mcn_date(self):
        """Validate MCN date"""
        mcn_date = self.cleaned_data.get('mcn_date')
        if mcn_date:
            from datetime import date
            if mcn_date > date.today():
                raise ValidationError("MCN date cannot be in the future.")
        return mcn_date
        
    def clean_reference_date(self):
        """Validate reference date"""
        reference_date = self.cleaned_data.get('reference_date')
        if reference_date:
            from datetime import date
            if reference_date > date.today():
                raise ValidationError("Reference date cannot be in the future.")
        return reference_date
        
    def clean(self):
        """Cross-field validation"""
        cleaned_data = super().clean()
        reference_number = cleaned_data.get('reference_number')
        reference_date = cleaned_data.get('reference_date')
        
        # If reference number is provided, reference date should also be provided
        if reference_number and not reference_date:
            raise ValidationError("Reference date is required when reference number is provided.")
            
        return cleaned_data


class MCNLineItemForm(forms.ModelForm):
    """Form for MCN line items"""
    
    class Meta:
        model = MCNLineItem
        fields = [
            'line_number', 'item_code', 'item_description', 'unit_of_measure',
            'current_stock', 'adjustment_quantity', 'adjustment_action',
            'unit_rate', 'location_code', 'batch_number', 'serial_number',
            'expiry_date', 'reason_code', 'line_remarks'
        ]
        widgets = {
            'line_number': forms.NumberInput(attrs={
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'min': '1',
                'required': True
            }),
            'item_code': forms.TextInput(attrs={
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'placeholder': 'Item code',
                'required': True
            }),
            'item_description': forms.Textarea(attrs={
                'class': 'form-textarea rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'rows': 2,
                'placeholder': 'Item description',
                'required': True
            }),
            'unit_of_measure': forms.TextInput(attrs={
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'placeholder': 'UOM',
                'required': True
            }),
            'current_stock': forms.NumberInput(attrs={
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'step': '0.001',
                'min': '0',
                'readonly': True
            }),
            'adjustment_quantity': forms.NumberInput(attrs={
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'step': '0.001',
                'min': '0.001',
                'required': True
            }),
            'adjustment_action': forms.Select(attrs={
                'class': 'form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'required': True
            }),
            'unit_rate': forms.NumberInput(attrs={
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'step': '0.01',
                'min': '0'
            }),
            'location_code': forms.TextInput(attrs={
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'placeholder': 'Location code'
            }),
            'batch_number': forms.TextInput(attrs={
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'placeholder': 'Batch number'
            }),
            'serial_number': forms.TextInput(attrs={
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'placeholder': 'Serial number'
            }),
            'expiry_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500'
            }),
            'reason_code': forms.TextInput(attrs={
                'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'placeholder': 'Reason code'
            }),
            'line_remarks': forms.Textarea(attrs={
                'class': 'form-textarea rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'rows': 2,
                'placeholder': 'Line-specific remarks'
            })
        }
        labels = {
            'line_number': 'Line No.',
            'item_code': 'Item Code',
            'item_description': 'Item Description',
            'unit_of_measure': 'UOM',
            'current_stock': 'Current Stock',
            'adjustment_quantity': 'Adjustment Qty',
            'adjustment_action': 'Action',
            'unit_rate': 'Unit Rate',
            'location_code': 'Location',
            'batch_number': 'Batch No.',
            'serial_number': 'Serial No.',
            'expiry_date': 'Expiry Date',
            'reason_code': 'Reason Code',
            'line_remarks': 'Line Remarks'
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set required fields
        self.fields['line_number'].required = True
        self.fields['item_code'].required = True
        self.fields['item_description'].required = True
        self.fields['unit_of_measure'].required = True
        self.fields['adjustment_quantity'].required = True
        self.fields['adjustment_action'].required = True
        
    def clean_adjustment_quantity(self):
        """Validate adjustment quantity"""
        adjustment_quantity = self.cleaned_data.get('adjustment_quantity')
        if adjustment_quantity and adjustment_quantity <= 0:
            raise ValidationError("Adjustment quantity must be greater than zero.")
        return adjustment_quantity
        
    def clean_unit_rate(self):
        """Validate unit rate"""
        unit_rate = self.cleaned_data.get('unit_rate')
        if unit_rate and unit_rate < 0:
            raise ValidationError("Unit rate cannot be negative.")
        return unit_rate
        
    def clean(self):
        """Cross-field validation"""
        cleaned_data = super().clean()
        current_stock = cleaned_data.get('current_stock', 0)
        adjustment_quantity = cleaned_data.get('adjustment_quantity', 0)
        adjustment_action = cleaned_data.get('adjustment_action')
        
        # Validate subtract/write-off actions against current stock
        if adjustment_action in ['SUBTRACT', 'TRANSFER_OUT']:
            if adjustment_quantity > current_stock:
                raise ValidationError(
                    f"Cannot {adjustment_action.lower()} {adjustment_quantity} "
                    f"when current stock is only {current_stock}."
                )
                
        return cleaned_data


class MCNLineItemFormSet(forms.BaseInlineFormSet):
    """Formset for MCN line items"""
    
    def clean(self):
        """Validate formset"""
        if any(self.errors):
            return
            
        if not any(form.cleaned_data for form in self.forms if form.cleaned_data):
            raise ValidationError("At least one line item is required.")
            
        # Check for duplicate line numbers
        line_numbers = []
        for form in self.forms:
            if form.cleaned_data and not form.cleaned_data.get('DELETE'):
                line_number = form.cleaned_data.get('line_number')
                if line_number:
                    if line_number in line_numbers:
                        raise ValidationError(f"Duplicate line number: {line_number}")
                    line_numbers.append(line_number)


class MCNApprovalForm(forms.ModelForm):
    """Form for MCN approval actions"""
    
    class Meta:
        model = MCNApprovalHistory
        fields = ['action', 'comments']
        widgets = {
            'action': forms.Select(attrs={
                'class': 'form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'required': True
            }),
            'comments': forms.Textarea(attrs={
                'class': 'form-textarea rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
                'rows': 3,
                'placeholder': 'Enter approval comments',
                'required': True
            })
        }
        labels = {
            'action': 'Action',
            'comments': 'Comments'
        }

    def __init__(self, *args, **kwargs):
        mcn = kwargs.pop('mcn', None)
        super().__init__(*args, **kwargs)
        
        # Restrict action choices based on MCN status
        if mcn:
            if mcn.status == 'DRAFT':
                self.fields['action'].choices = [
                    ('SUBMITTED', 'Submit for Approval'),
                    ('CANCELLED', 'Cancel')
                ]
            elif mcn.status == 'PENDING_APPROVAL':
                self.fields['action'].choices = [
                    ('APPROVED', 'Approve'),
                    ('REJECTED', 'Reject'),
                    ('RETURNED', 'Return for Revision')
                ]
            elif mcn.status == 'APPROVED':
                self.fields['action'].choices = [
                    ('PROCESSED', 'Process')
                ]
            else:
                self.fields['action'].choices = []
                
        self.fields['comments'].required = True


class MCNSearchForm(forms.Form):
    """Form for searching MCNs"""
    
    mcn_number = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500',
            'placeholder': 'MCN Number'
        })
    )
    
    status = forms.ChoiceField(
        required=False,
        choices=[('', 'All Statuses')] + MaterialCreditNote.STATUS_CHOICES,
        widget=forms.Select(attrs={
            'class': 'form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500'
        })
    )
    
    adjustment_type = forms.ChoiceField(
        required=False,
        choices=[('', 'All Types')] + MaterialCreditNote.ADJUSTMENT_TYPE_CHOICES,
        widget=forms.Select(attrs={
            'class': 'form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500'
        })
    )
    
    priority = forms.ChoiceField(
        required=False,
        choices=[('', 'All Priorities')] + MaterialCreditNote.PRIORITY_CHOICES,
        widget=forms.Select(attrs={
            'class': 'form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500'
        })
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500'
        })
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500'
        })
    )
    
    requested_by = forms.ModelChoiceField(
        queryset=User.objects.filter(is_active=True),
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500'
        })
    )
    
    def clean(self):
        """Validate date range"""
        cleaned_data = super().clean()
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        
        if date_from and date_to and date_from > date_to:
            raise ValidationError("Start date cannot be after end date.")
            
        return cleaned_data


# Inline formsets
MCNLineItemInlineFormSet = forms.inlineformset_factory(
    MaterialCreditNote,
    MCNLineItem,
    form=MCNLineItemForm,
    formset=MCNLineItemFormSet,
    extra=1,
    min_num=1,
    validate_min=True,
    can_delete=True
)