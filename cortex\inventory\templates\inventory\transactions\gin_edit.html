{% extends 'core/base.html' %}
{% load static %}

{% block title %}Goods Inward Note [GIN] - Edit{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50" x-data="ginEditApp()">
    <!-- Header -->
    <div class="bg-gray-700 text-white px-4 py-2">
        <h1 class="text-lg font-bold">Goods Inward Note [GIN] - Edit</h1>
    </div>
    
    <div class="p-4">
        <!-- Search Section -->
        <div class="bg-white rounded-lg shadow-sm border mb-4">
            <div class="p-4">
                <form @submit.prevent="searchGIN" class="flex items-center gap-4">
                    <!-- Search Type Dropdown -->
                    <select x-model="searchType" 
                            class="border border-gray-300 rounded px-3 py-2 text-sm w-48 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="0">Supplier Name</option>
                        <option value="1">PO No</option>
                        <option value="2">GIN No</option>
                    </select>
                    
                    <!-- Search Query Input -->
                    <input type="text" 
                           x-model="searchQuery"
                           placeholder="Enter search value" 
                           class="border border-gray-300 rounded px-3 py-2 text-sm w-40 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    
                    <!-- Supplier Autocomplete Input -->
                    <div class="relative flex-1">
                        <input type="text" 
                               x-model="supplierSearch"
                               @input="searchSuppliers"
                               @focus="showSupplierDropdown = true"
                               @keydown.arrow-down.prevent="navigateSuppliers(1)"
                               @keydown.arrow-up.prevent="navigateSuppliers(-1)"
                               @keydown.enter.prevent="selectSupplier(highlightedSupplierIndex)"
                               @keydown.escape="showSupplierDropdown = false"
                               placeholder="Select supplier..." 
                               autocomplete="off"
                               class="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                        
                        <!-- Supplier Dropdown -->
                        <div x-show="showSupplierDropdown && suppliers.length > 0" 
                             x-transition
                             @click.outside="showSupplierDropdown = false"
                             class="absolute top-full left-0 right-0 mt-1 bg-teal-500 border border-gray-300 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
                            <template x-for="(supplier, index) in suppliers" :key="supplier.id">
                                <div @click="selectSupplierById(supplier.id)"
                                     :class="{'bg-teal-600': index === highlightedSupplierIndex, 'bg-teal-500': index !== highlightedSupplierIndex}"
                                     class="px-3 py-2 text-white text-sm cursor-pointer hover:bg-teal-600 border-b border-teal-400 last:border-b-0">
                                    <span x-text="supplier.display_text"></span>
                                </div>
                            </template>
                        </div>
                    </div>
                           
                    <!-- Search Button -->
                    <button type="submit" 
                            class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded text-sm font-medium transition-colors">
                        Search
                    </button>
                </form>
            </div>
        </div>

        <!-- Results Table -->
        <div class="bg-white rounded-lg shadow-sm border" x-show="ginRecords.length > 0 || searchPerformed">
            <!-- Table Header with Instructions -->
            <div class="px-4 py-2 border-b border-gray-200 bg-gray-50" x-show="ginRecords.length > 0">
                <p class="text-sm text-gray-600">
                    <strong>Click on any row</strong> to view detailed supplier-wise information and line items for that GIN record.
                </p>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full table-auto divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">SN</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">Fin Year</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">PONo</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">GIN No</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">Date</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Supplier</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">Challan No</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">Challan Date</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <template x-for="(gin, index) in ginRecords" :key="gin.id">
                            <tr class="hover:bg-blue-50 cursor-pointer" @click="navigateToGINDetail(gin.id)">
                                <td class="px-4 py-3 text-sm text-gray-900 text-center" x-text="index + 1"></td>
                                <td class="px-4 py-3 text-sm text-gray-900 text-center" x-text="gin.fin_year || 'N/A'"></td>
                                <td class="px-4 py-3 text-sm text-gray-900 text-center" x-text="gin.po_number || 'N/A'"></td>
                                <td class="px-4 py-3 text-sm text-gray-900 text-center" x-text="gin.gin_number || 'N/A'"></td>
                                <td class="px-4 py-3 text-sm text-gray-900 text-center" x-text="gin.gin_date || 'N/A'"></td>
                                <td class="px-4 py-3 text-sm text-gray-900" x-text="gin.supplier_name || 'UNKNOWN'"></td>
                                <td class="px-4 py-3 text-sm text-gray-900 text-center" x-text="gin.challan_number || ''"></td>
                                <td class="px-4 py-3 text-sm text-gray-900 text-center" x-text="gin.challan_date || ''"></td>
                            </tr>
                        </template>
                        
                        <!-- No Data Row -->
                        <tr x-show="ginRecords.length === 0 && searchPerformed">
                            <td colspan="8" class="px-4 py-8 text-center">
                                <div class="text-gray-500">
                                    <p class="text-lg font-medium text-red-900">No data to display !</p>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Initial State -->
        <div x-show="!searchPerformed" class="bg-white rounded-lg shadow-sm border p-8">
            <div class="text-center text-gray-500">
                <p class="text-lg font-medium">Select a supplier to view GIN records</p>
                <p class="text-sm mt-2">Use the supplier dropdown above to filter GIN records</p>
            </div>
        </div>
    </div>
</div>

<script>
function ginEditApp() {
    return {
        // Search parameters
        searchType: '0',
        searchQuery: '',
        supplierSearch: '',
        selectedSupplierId: null,
        
        // Supplier autocomplete
        suppliers: [],
        showSupplierDropdown: false,
        highlightedSupplierIndex: -1,
        
        // GIN records
        ginRecords: [],
        searchPerformed: false,
        loading: false,
        
        init() {
            // Initialize any needed data
        },
        
        async searchSuppliers() {
            if (this.supplierSearch.length < 2) {
                this.suppliers = [];
                this.showSupplierDropdown = false;
                return;
            }
            
            try {
                const response = await fetch(`/inventory/api/suppliers/?q=${encodeURIComponent(this.supplierSearch)}`);
                if (response.ok) {
                    this.suppliers = await response.json();
                    this.showSupplierDropdown = true;
                    this.highlightedSupplierIndex = -1;
                }
            } catch (error) {
                console.error('Error searching suppliers:', error);
            }
        },
        
        navigateSuppliers(direction) {
            if (this.suppliers.length === 0) return;
            
            const newIndex = this.highlightedSupplierIndex + direction;
            if (newIndex >= 0 && newIndex < this.suppliers.length) {
                this.highlightedSupplierIndex = newIndex;
            }
        },
        
        selectSupplier(index) {
            if (index >= 0 && index < this.suppliers.length) {
                this.selectSupplierById(this.suppliers[index].id);
            }
        },
        
        selectSupplierById(supplierId) {
            const supplier = this.suppliers.find(s => s.id === supplierId);
            if (supplier) {
                this.supplierSearch = supplier.display_text;
                this.selectedSupplierId = supplier.id;
                this.showSupplierDropdown = false;
                this.highlightedSupplierIndex = -1;
                
                // Automatically search for GIN records for this supplier
                this.searchGIN();
            }
        },
        
        async searchGIN() {
            this.loading = true;
            this.searchPerformed = true;
            
            try {
                const params = new URLSearchParams();
                params.append('search_type', this.searchType);
                if (this.searchQuery) params.append('search_query', this.searchQuery);
                if (this.selectedSupplierId) params.append('supplier_id', this.selectedSupplierId);
                
                const response = await fetch(`/inventory/api/gin-search/?${params.toString()}`);
                if (response.ok) {
                    this.ginRecords = await response.json();
                } else {
                    console.error('Failed to fetch GIN records');
                    this.ginRecords = [];
                }
            } catch (error) {
                console.error('Error searching GIN records:', error);
                this.ginRecords = [];
            } finally {
                this.loading = false;
            }
        },
        
        clearSearch() {
            this.searchQuery = '';
            this.supplierSearch = '';
            this.selectedSupplierId = null;
            this.ginRecords = [];
            this.searchPerformed = false;
            this.suppliers = [];
            this.showSupplierDropdown = false;
        },
        
        navigateToGINDetail(ginId) {
            // Navigate to the GIN edit detail page
            window.location.href = `/inventory/gin-edit/${ginId}/`;
        }
    }
}
</script>
{% endblock %}