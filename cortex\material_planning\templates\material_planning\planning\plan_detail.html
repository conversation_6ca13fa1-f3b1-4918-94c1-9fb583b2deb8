{% extends 'material_planning/base.html' %}

{% block title %}Material Plan Details{% endblock %}

{% block content %}
<div class="space-y-6 animate-fade-in">
    <!-- Header Section -->
    <div class="sap-card">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-sap-gray-900 mb-2">Material Plan Details</h1>
                <p class="text-sap-gray-600">Plan Number: {{ plan.plno|default:"N/A" }}</p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'material_planning:plan_edit' plan.pk %}" class="sap-button-secondary">
                    <i class="fas fa-edit mr-2"></i>
                    Edit Plan
                </a>
                <a href="{% url 'material_planning:plan_copy' plan.pk %}" class="sap-button-primary">
                    <i class="fas fa-copy mr-2"></i>
                    Copy Plan
                </a>
                <a href="{% url 'material_planning:plan_list' %}" class="sap-button-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>

    <!-- Plan Information -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Basic Information -->
        <div class="sap-card">
            <h2 class="text-xl font-semibold text-sap-gray-900 mb-4">Basic Information</h2>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-sap-gray-700">Plan Number</label>
                    <p class="mt-1 text-sm text-sap-gray-900">{{ plan.plno|default:"N/A" }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-sap-gray-700">Work Order Number</label>
                    <p class="mt-1 text-sm text-sap-gray-900">{{ plan.wono|default:"N/A" }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-sap-gray-700">Company</label>
                    <p class="mt-1 text-sm text-sap-gray-900">{{ plan.company.companyname|default:"N/A" }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-sap-gray-700">Financial Year</label>
                    <p class="mt-1 text-sm text-sap-gray-900">{{ plan.finyear.finyear|default:"N/A" }}</p>
                </div>
            </div>
        </div>

        <!-- System Information -->
        <div class="sap-card">
            <h2 class="text-xl font-semibold text-sap-gray-900 mb-4">System Information</h2>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-sap-gray-700">Created Date</label>
                    <p class="mt-1 text-sm text-sap-gray-900">{{ plan.sysdate|default:"N/A" }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-sap-gray-700">Created Time</label>
                    <p class="mt-1 text-sm text-sap-gray-900">{{ plan.systime|default:"N/A" }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-sap-gray-700">Created by</label>
                    <p class="mt-1 text-sm text-sap-gray-900">{{ plan.session.username|default:"N/A" }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Material Details -->
    {% if material_details %}
    <div class="sap-card">
        <h2 class="text-xl font-semibold text-sap-gray-900 mb-4">Material Details</h2>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item ID</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">RM</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PRO</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">FIN</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for detail in material_details %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ detail.itemid|default:"N/A" }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ detail.rm|default:"N/A" }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ detail.pro|default:"N/A" }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ detail.fin|default:"N/A" }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% else %}
    <div class="sap-card">
        <h2 class="text-xl font-semibold text-sap-gray-900 mb-4">Material Details</h2>
        <p class="text-gray-500">No material details found for this plan.</p>
    </div>
    {% endif %}
</div>
{% endblock %}