{% if processes %}
    {% for process in processes %}
    <div class="flex items-center justify-between py-2 border-b border-gray-200 last:border-b-0">
        <div>
            <p class="text-sm font-medium text-gray-900">[{{ process.process.symbol }}] {{ process.process.processname }}</p>
        </div>
        <button type="button" 
                hx-delete="{% url 'machinery:remove_process' process.id %}"
                hx-target="#processes-list"
                class="text-red-600 hover:text-red-800">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    </div>
    {% endfor %}
{% else %}
    <div class="text-center py-8">
        <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
        </svg>
        <p class="text-gray-500">No processes selected</p>
        <p class="text-sm text-gray-400">Select processes from the left panel</p>
    </div>
{% endif %}