<!-- MCN Results Table -->
<div class="bg-white shadow overflow-hidden sm:rounded-md">
    <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
            Material Credit Notes
            {% if mcns %}
                <span class="text-sm font-normal text-gray-500">({{ page_obj.paginator.count }} total)</span>
            {% endif %}
        </h3>
    </div>
    
    {% if mcns %}
        <ul class="divide-y divide-gray-200">
            {% for mcn in mcns %}
            <li>
                <div class="px-4 py-4 sm:px-6 hover:bg-gray-50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="flex items-center">
                                    <a href="{% url 'inventory:mcn_detail' mcn.pk %}" class="text-sm font-medium text-indigo-600 hover:text-indigo-500">
                                        {{ mcn.mcn_number }}
                                    </a>
                                    {% if mcn.is_emergency %}
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            Emergency
                                        </span>
                                    {% endif %}
                                </div>
                                <div class="mt-1 flex items-center text-sm text-gray-500">
                                    <span>{{ mcn.mcn_date|date:"M d, Y" }}</span>
                                    <span class="mx-1">•</span>
                                    <span>{{ mcn.get_adjustment_type_display }}</span>
                                    <span class="mx-1">•</span>
                                    <span>{{ mcn.requested_by.get_full_name|default:mcn.requested_by.username }}</span>
                                </div>
                                {% if mcn.reason_description %}
                                <div class="mt-1 text-sm text-gray-600">
                                    {{ mcn.reason_description|truncatechars:100 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-4">
                            <!-- Priority Badge -->
                            {% if mcn.priority == 'URGENT' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    {{ mcn.get_priority_display }}
                                </span>
                            {% elif mcn.priority == 'HIGH' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                    {{ mcn.get_priority_display }}
                                </span>
                            {% elif mcn.priority == 'MEDIUM' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    {{ mcn.get_priority_display }}
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    {{ mcn.get_priority_display }}
                                </span>
                            {% endif %}
                            
                            <!-- Status Badge -->
                            {% if mcn.status == 'DRAFT' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    {{ mcn.get_status_display }}
                                </span>
                            {% elif mcn.status == 'PENDING_APPROVAL' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    {{ mcn.get_status_display }}
                                </span>
                            {% elif mcn.status == 'APPROVED' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ mcn.get_status_display }}
                                </span>
                            {% elif mcn.status == 'PROCESSED' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    {{ mcn.get_status_display }}
                                </span>
                            {% elif mcn.status == 'REJECTED' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    {{ mcn.get_status_display }}
                                </span>
                            {% elif mcn.status == 'CANCELLED' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    {{ mcn.get_status_display }}
                                </span>
                            {% endif %}
                            
                            <!-- Summary Stats -->
                            <div class="text-right text-sm">
                                <div class="text-gray-900 font-medium">{{ mcn.total_items }} items</div>
                                <div class="text-gray-500">₹{{ mcn.total_adjustment_value|floatformat:2 }}</div>
                            </div>
                            
                            <!-- Actions -->
                            <div class="flex items-center space-x-2">
                                <a href="{% url 'inventory:mcn_detail' mcn.pk %}" 
                                   class="text-indigo-600 hover:text-indigo-900" 
                                   title="View Details">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </a>
                                
                                {% if mcn.can_be_edited %}
                                <a href="{% url 'inventory:mcn_update' mcn.pk %}" 
                                   class="text-blue-600 hover:text-blue-900" 
                                   title="Edit MCN">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </a>
                                {% endif %}
                                
                                <a href="{% url 'inventory:mcn_print' mcn.pk %}" 
                                   class="text-green-600 hover:text-green-900" 
                                   title="Print MCN"
                                   target="_blank">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </li>
            {% endfor %}
        </ul>
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}" 
                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:text-gray-500">
                        Previous
                    </a>
                {% endif %}
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}" 
                       class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:text-gray-500">
                        Next
                    </a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing
                        <span class="font-medium">{{ page_obj.start_index }}</span>
                        to
                        <span class="font-medium">{{ page_obj.end_index }}</span>
                        of
                        <span class="font-medium">{{ page_obj.paginator.count }}</span>
                        results
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}" 
                               class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">Previous</span>
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </a>
                        {% endif %}
                        
                        {% for page_num in page_obj.paginator.page_range %}
                            {% if page_num == page_obj.number %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-indigo-50 text-sm font-medium text-indigo-600">
                                    {{ page_num }}
                                </span>
                            {% elif page_num == page_obj.paginator.ELLIPSIS %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                    ...
                                </span>
                            {% else %}
                                <a href="?page={{ page_num }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}" 
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    {{ page_num }}
                                </a>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}" 
                               class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">Next</span>
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                </svg>
                            </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
        {% endif %}
    {% else %}
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No material credit notes found</h3>
            <p class="mt-1 text-sm text-gray-500">Get started by creating a new MCN for stock adjustments.</p>
            <div class="mt-6">
                <a href="{% url 'inventory:mcn_create' %}" 
                   class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                    </svg>
                    New Material Credit Note
                </a>
            </div>
        </div>
    {% endif %}
</div>