<!-- accounts/partials/currency_edit_form.html -->
<!-- HTMX partial for Currency edit form - SAP S/4HANA inspired -->
<!-- Inline/Modal edit form for currency records -->

{% load static %}

<div class="bg-sap-orange-50 border border-sap-orange-200 rounded-lg p-4 mb-4" id="currency-edit-form-{{ currency.id }}">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                <i data-lucide="edit" class="w-4 h-4 text-sap-orange-600"></i>
            </div>
            <div>
                <h4 class="text-lg font-medium text-sap-gray-800">Edit Currency</h4>
                <p class="text-sm text-sap-gray-600">Update currency information (ID: {{ currency.id }})</p>
            </div>
        </div>
        <button type="button" 
                hx-get="{% url 'accounts:currency_list' %}"
                hx-target="#currency-table"
                hx-swap="outerHTML"
                class="inline-flex items-center px-3 py-1.5 border border-sap-gray-300 rounded text-xs font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
            <i data-lucide="x" class="w-3 h-3 mr-1"></i>
            Cancel
        </button>
    </div>
    
    <form hx-put="{% url 'accounts:currency_edit' currency.id %}" 
          hx-target="#currency-edit-form-{{ currency.id }}" 
          hx-swap="outerHTML"
          hx-trigger="submit"
          class="space-y-4">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label for="{{ form.country.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                    Country <span class="text-red-500">*</span>
                </label>
                {{ form.country }}
                {% if form.country.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.country.errors.0 }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                    Currency Name <span class="text-red-500">*</span>
                </label>
                {{ form.name }}
                {% if form.name.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.name.errors.0 }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.symbol.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-2">
                    Symbol <span class="text-red-500">*</span>
                </label>
                {{ form.symbol }}
                {% if form.symbol.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.symbol.errors.0 }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="flex justify-end space-x-3 pt-4 border-t border-sap-orange-200">
            <button type="button" 
                    hx-get="{% url 'accounts:currency_list' %}"
                    hx-target="#currency-table"
                    hx-swap="outerHTML"
                    class="px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50">
                Cancel
            </button>
            <button type="submit" 
                    class="px-4 py-2 bg-sap-orange-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-sap-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-orange-500">
                <i data-lucide="save" class="w-4 h-4 mr-2 inline"></i>
                Update Currency
            </button>
        </div>
    </form>
</div>