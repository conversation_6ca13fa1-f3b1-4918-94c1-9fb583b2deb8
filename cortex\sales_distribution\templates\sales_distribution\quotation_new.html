{% extends "core/base.html" %}
{% load static %}

{% block title %}{{ page_title }} - {{ global_company.company_name }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-slate-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- Breadcrumb Navigation -->
        <nav class="flex items-center space-x-2 text-sm mb-8" aria-label="Breadcrumb">
            <div class="flex items-center space-x-2 bg-white/80 backdrop-blur-sm rounded-lg px-4 py-2 border border-slate-200/60 shadow-sm">
                <a href="{% url 'sales_distribution:dashboard' %}" 
                   class="text-blue-600 hover:text-blue-700 transition-colors duration-200 font-medium flex items-center space-x-1">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                    </svg>
                    <span>Sales Distribution</span>
                </a>
                <svg class="w-4 h-4 text-slate-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
                <a href="{% url 'sales_distribution:quotation_list' %}" 
                   class="text-blue-600 hover:text-blue-700 transition-colors duration-200 font-medium flex items-center space-x-1">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    <span>Quotations</span>
                </a>
                <svg class="w-4 h-4 text-slate-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
                <span class="text-slate-700 font-semibold">Create Quotation</span>
            </div>
        </nav>

        <!-- Main Header Card -->
        <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200/60 mb-8 overflow-hidden">
            <div class="bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 px-8 py-10">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="mb-6 lg:mb-0">
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                </svg>
                            </div>
                            <h1 class="text-4xl font-bold text-white tracking-tight">
                                {{ page_title }}
                            </h1>
                        </div>
                        <p class="text-blue-100 text-lg">Enquiry #{{ enquiry.enqid }} - {{ enquiry.customername }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Display Messages -->
        {% if messages %}
        <div class="mb-6">
            {% for message in messages %}
            <div class="{% if message.tags == 'error' %}bg-red-50 border border-red-200 text-red-800{% elif message.tags == 'warning' %}bg-yellow-50 border border-yellow-200 text-yellow-800{% elif message.tags == 'success' %}bg-green-50 border border-green-200 text-green-800{% else %}bg-blue-50 border border-blue-200 text-blue-800{% endif %} px-4 py-3 rounded-lg mb-3">
                <div class="flex items-center">
                    {% if message.tags == 'error' %}
                    <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                    </svg>
                    {% elif message.tags == 'success' %}
                    <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                    {% else %}
                    <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                    </svg>
                    {% endif %}
                    {{ message }}
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- Enquiry Information Card -->
        <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 mb-8 overflow-hidden">
            <div class="px-8 py-6 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-slate-50">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    Enquiry Information
                </h3>
            </div>
            <div class="px-8 py-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="bg-blue-50 rounded-lg p-4">
                        <div class="text-sm font-medium text-blue-600 mb-1">Enquiry ID</div>
                        <div class="text-lg font-bold text-blue-900">#{{ enquiry.enqid }}</div>
                    </div>
                    <div class="bg-green-50 rounded-lg p-4">
                        <div class="text-sm font-medium text-green-600 mb-1">Customer</div>
                        <div class="text-lg font-bold text-green-900">{{ enquiry.customername }}</div>
                    </div>
                    <div class="bg-purple-50 rounded-lg p-4">
                        <div class="text-sm font-medium text-purple-600 mb-1">Contact Person</div>
                        <div class="text-lg font-bold text-purple-900">{{ enquiry.contactperson }}</div>
                    </div>
                    <div class="bg-orange-50 rounded-lg p-4">
                        <div class="text-sm font-medium text-orange-600 mb-1">Enquiry Date</div>
                        <div class="text-lg font-bold text-orange-900">{{ enquiry.sysdate }}</div>
                    </div>
                </div>
                {% if enquiry.enquiryfor %}
                <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                    <div class="text-sm font-medium text-gray-600 mb-2">Enquiry Description</div>
                    <div class="text-gray-900">{{ enquiry.enquiryfor }}</div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Tab Navigation -->
        <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 overflow-hidden">
            <div class="flex border-b border-gray-200">
                <button type="button" 
                        onclick="showTab(0)" 
                        id="tab-0"
                        class="tab-button flex-1 px-6 py-4 text-sm font-medium text-center border-b-2 {% if current_tab == 0 %}border-blue-500 text-blue-600 bg-blue-50{% else %}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300{% endif %} transition-all duration-200">
                    <div class="flex items-center justify-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                        </svg>
                        <span>Customer Information</span>
                    </div>
                </button>
                <button type="button" 
                        onclick="showTab(1)" 
                        id="tab-1"
                        class="tab-button flex-1 px-6 py-4 text-sm font-medium text-center border-b-2 {% if current_tab == 1 %}border-blue-500 text-blue-600 bg-blue-50{% else %}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300{% endif %} transition-all duration-200">
                    <div class="flex items-center justify-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                        </svg>
                        <span>Item Details</span>
                        {% if temp_items %}
                        <span class="ml-2 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">{{ temp_items|length }}</span>
                        {% endif %}
                    </div>
                </button>
                <button type="button" 
                        onclick="showTab(2)" 
                        id="tab-2"
                        class="tab-button flex-1 px-6 py-4 text-sm font-medium text-center border-b-2 {% if current_tab == 2 %}border-blue-500 text-blue-600 bg-blue-50{% else %}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300{% endif %} transition-all duration-200">
                    <div class="flex items-center justify-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        <span>Terms & Conditions</span>
                    </div>
                </button>
            </div>

            <!-- Tab Content -->
            <div class="p-8">
                <!-- Tab 0: Customer Information -->
                <div id="tab-content-0" class="tab-content {% if current_tab != 0 %}hidden{% endif %}">
                    <div class="space-y-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-6">Customer Information</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="font-medium text-gray-900 mb-4">Basic Information</h4>
                                <div class="space-y-3">
                                    <div>
                                        <span class="text-sm font-medium text-gray-600">Customer Name:</span>
                                        <span class="ml-2 text-gray-900">{{ enquiry.customername }}</span>
                                    </div>
                                    <div>
                                        <span class="text-sm font-medium text-gray-600">Customer ID:</span>
                                        <span class="ml-2 text-gray-900">{{ enquiry.customerid|default:"Auto-generated" }}</span>
                                    </div>
                                    <div>
                                        <span class="text-sm font-medium text-gray-600">Contact Person:</span>
                                        <span class="ml-2 text-gray-900">{{ enquiry.contactperson }}</span>
                                    </div>
                                    <div>
                                        <span class="text-sm font-medium text-gray-600">Email:</span>
                                        <span class="ml-2 text-gray-900">{{ enquiry.email }}</span>
                                    </div>
                                    <div>
                                        <span class="text-sm font-medium text-gray-600">Contact Number:</span>
                                        <span class="ml-2 text-gray-900">{{ enquiry.contactno }}</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="font-medium text-gray-900 mb-4">Address Information</h4>
                                <div class="space-y-3">
                                    <div>
                                        <span class="text-sm font-medium text-gray-600">Registered Office:</span>
                                        <div class="mt-1 text-gray-900 text-sm">
                                            {{ enquiry.regdaddress }}<br>
                                            {{ enquiry.regdcity.cityname }}, {{ enquiry.regdstate.statename }}<br>
                                            {{ enquiry.regdcountry.countryname }} - {{ enquiry.regdpinno }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex justify-end space-x-4">
                            <button type="button" onclick="showTab(1)" 
                                    class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                                Next: Item Details
                                <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Tab 1: Item Details -->
                <div id="tab-content-1" class="tab-content {% if current_tab != 1 %}hidden{% endif %}">
                    <div class="space-y-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-6">Item Details</h3>

                        <!-- Add Item Form -->
                        <div class="bg-blue-50 rounded-lg p-6 mb-6">
                            <h4 class="font-medium text-blue-900 mb-4">Add New Item</h4>
                            <form method="post" action="">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="add_item">
                                <input type="hidden" name="current_tab" value="1">
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
                                    <div class="lg:col-span-2">
                                        <label for="item_desc" class="block text-sm font-medium text-gray-700 mb-1">Item Description *</label>
                                        <textarea name="item_desc" id="item_desc" rows="3" required 
                                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm resize-none"
                                                  placeholder="Enter detailed item description..."></textarea>
                                    </div>
                                    <div>
                                        <label for="quantity" class="block text-sm font-medium text-gray-700 mb-1">Quantity *</label>
                                        <input type="number" name="quantity" id="quantity" step="0.01" min="0" required 
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                                               placeholder="0.00">
                                    </div>
                                    <div>
                                        <label for="unit" class="block text-sm font-medium text-gray-700 mb-1">Unit *</label>
                                        <select name="unit" id="unit" required 
                                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
                                            <option value="">Select Unit</option>
                                            {% for unit in units %}
                                            <option value="{{ unit.id }}">{{ unit.unitname }}{% if unit.symbol %} ({{ unit.symbol }}){% endif %}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div>
                                        <label for="rate" class="block text-sm font-medium text-gray-700 mb-1">Rate *</label>
                                        <input type="number" name="rate" id="rate" step="0.01" min="0" required 
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                                               placeholder="0.00">
                                    </div>
                                </div>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <label for="discount" class="block text-sm font-medium text-gray-700 mb-1">Discount</label>
                                        <input type="number" name="discount" id="discount" step="0.01" min="0" value="0" 
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                                               placeholder="0.00">
                                    </div>
                                </div>
                                
                                <button type="submit" 
                                        class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                                    </svg>
                                    Add Item
                                </button>
                            </form>
                        </div>

                        <!-- Items List -->
                        {% if temp_items %}
                        <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
                            <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                                <h4 class="font-medium text-gray-900">Added Items ({{ temp_items|length }})</h4>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Description</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Discount</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Line Total</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        {% for item in temp_items %}
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 text-sm text-gray-900">{{ item.item_desc }}</td>
                                            <td class="px-6 py-4 text-sm text-gray-900">{{ item.quantity }}</td>
                                            <td class="px-6 py-4 text-sm text-gray-900">{{ item.unit_symbol }}</td>
                                            <td class="px-6 py-4 text-sm text-gray-900">₹{{ item.rate }}</td>
                                            <td class="px-6 py-4 text-sm text-gray-900">₹{{ item.discount }}</td>
                                            <td class="px-6 py-4 text-sm font-medium text-gray-900">₹{{ item.line_total }}</td>
                                            <td class="px-6 py-4">
                                                <form method="post" class="inline">
                                                    {% csrf_token %}
                                                    <input type="hidden" name="action" value="delete_item">
                                                    <input type="hidden" name="item_index" value="{{ forloop.counter0 }}">
                                                    <input type="hidden" name="current_tab" value="1">
                                                    <button type="submit" 
                                                            class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                                        </svg>
                                                        Delete
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <!-- Total Summary -->
                        <div class="bg-blue-50 rounded-lg p-6">
                            <div class="flex justify-between items-center">
                                <span class="text-lg font-medium text-blue-900">Total Items:</span>
                                <span class="text-xl font-bold text-blue-900">{{ temp_items|length }}</span>
                            </div>
                        </div>
                        {% else %}
                        <div class="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No items added</h3>
                            <p class="mt-1 text-sm text-gray-500">Get started by adding your first item to the quotation.</p>
                        </div>
                        {% endif %}
                        
                        <div class="flex justify-between">
                            <button type="button" onclick="showTab(0)" 
                                    class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                                <svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                                </svg>
                                Previous: Customer Info
                            </button>
                            <button type="button" onclick="showTab(2)" 
                                    class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                                Next: Terms & Conditions
                                <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Tab 2: Terms & Conditions -->
                <div id="tab-content-2" class="tab-content {% if current_tab != 2 %}hidden{% endif %}">
                    <form method="post" action="">
                        {% csrf_token %}
                        <input type="hidden" name="action" value="save_quotation">
                        <input type="hidden" name="current_tab" value="2">
                        
                        <div class="space-y-6">
                            <h3 class="text-xl font-semibold text-gray-900 mb-6">Terms & Conditions</h3>
                            
                            <!-- Quotation Form Fields -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="{{ quotation_form.quotationno.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                        Quotation Number
                                    </label>
                                    {{ quotation_form.quotationno }}
                                </div>
                                
                                <div>
                                    <label for="{{ quotation_form.paymentterms.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                        Payment Terms
                                    </label>
                                    {{ quotation_form.paymentterms }}
                                </div>
                                
                                <div>
                                    <label for="{{ quotation_form.validity.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                        Validity
                                    </label>
                                    {{ quotation_form.validity }}
                                </div>
                                
                                <div>
                                    <label for="{{ quotation_form.warrenty.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                        Warranty
                                    </label>
                                    {{ quotation_form.warrenty }}
                                </div>
                                
                                <div>
                                    <label for="{{ quotation_form.deliveryterms.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                        Delivery Terms
                                    </label>
                                    {{ quotation_form.deliveryterms }}
                                </div>
                                
                                <div>
                                    <label for="{{ quotation_form.transport.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                        Transport
                                    </label>
                                    {{ quotation_form.transport }}
                                </div>
                            </div>

                            <!-- Financial Terms -->
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="font-medium text-gray-900 mb-4">Financial Terms</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    <div>
                                        <label for="{{ quotation_form.pf.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                            P&F (%)
                                        </label>
                                        {{ quotation_form.pf }}
                                    </div>
                                    
                                    <div>
                                        <label for="{{ quotation_form.freight.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                            Freight
                                        </label>
                                        {{ quotation_form.freight }}
                                    </div>
                                    
                                    <div>
                                        <label for="{{ quotation_form.insurance.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                            Insurance
                                        </label>
                                        {{ quotation_form.insurance }}
                                    </div>
                                    
                                    <div>
                                        <label for="{{ quotation_form.othercharges.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                            Other Charges
                                        </label>
                                        {{ quotation_form.othercharges }}
                                    </div>
                                </div>
                            </div>


                            <div>
                                <label for="{{ quotation_form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                    Remarks
                                </label>
                                {{ quotation_form.remarks }}
                            </div>

                            <!-- Hidden Fields -->
                            {{ quotation_form.customerid }}
                            {{ quotation_form.enqid }}
                            
                            <div class="flex justify-between">
                                <button type="button" onclick="showTab(1)" 
                                        class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                                    <svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                                    </svg>
                                    Previous: Item Details
                                </button>
                                <button type="submit" 
                                        class="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors">
                                    <svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    Create Quotation
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showTab(tabIndex) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.classList.add('hidden');
    });
    
    // Remove active classes from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('border-blue-500', 'text-blue-600', 'bg-blue-50');
        button.classList.add('border-transparent', 'text-gray-500');
    });
    
    // Show the selected tab content
    document.getElementById(`tab-content-${tabIndex}`).classList.remove('hidden');
    
    // Add active classes to the selected tab button
    const activeButton = document.getElementById(`tab-${tabIndex}`);
    activeButton.classList.remove('border-transparent', 'text-gray-500');
    activeButton.classList.add('border-blue-500', 'text-blue-600', 'bg-blue-50');
}

// Initialize the correct tab on page load
document.addEventListener('DOMContentLoaded', function() {
    const currentTab = {{ current_tab }};
    showTab(currentTab);
});
</script>

{% endblock %}
