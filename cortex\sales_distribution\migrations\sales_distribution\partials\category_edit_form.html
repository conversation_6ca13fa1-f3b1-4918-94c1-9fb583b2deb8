<!-- sales_distribution/templates/sales_distribution/partials/category_edit_form.html -->
<!-- Category inline edit form - replaces ASP.NET GridView EditItemTemplate -->

<tr id="category-row-{{ category.cid }}" class="bg-sap-blue-50 border-l-4 border-sap-blue-500">
    <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900 text-right">
        {{ forloop.counter }}
    </td>
    <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
        <div class="flex space-x-1">
            <button type="submit" form="edit-form-{{ category.cid }}"
                    onclick="return confirmUpdate();"
                    class="inline-flex items-center p-1 border border-transparent rounded text-green-600 hover:text-green-900 hover:bg-green-50"
                    title="Save Changes">
                <i data-lucide="check" class="w-4 h-4"></i>
            </button>
            <button hx-get="{% url 'sales_distribution:category_cancel_edit' category.cid %}" 
                    hx-target="#category-row-{{ category.cid }}" 
                    hx-swap="outerHTML"
                    class="inline-flex items-center p-1 border border-transparent rounded text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                    title="Cancel Edit">
                <i data-lucide="x" class="w-4 h-4"></i>
            </button>
        </div>
    </td>
    <td class="px-6 py-4">
        <form id="edit-form-{{ category.cid }}" 
              hx-post="{% url 'sales_distribution:category_edit' category.cid %}" 
              hx-target="#category-row-{{ category.cid }}" 
              hx-swap="outerHTML">
            {% csrf_token %}
            <div class="space-y-2">
                {{ form.cname }}
                {% if form.cname.errors %}
                    <p class="text-xs text-red-600">{{ form.cname.errors.0 }}</p>
                {% endif %}
            </div>
        </form>
    </td>
    <td class="px-6 py-4 whitespace-nowrap text-center">
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-gray-100 text-sap-gray-800">
            {{ category.symbol|default:"-" }}
        </span>
    </td>
    <td class="px-6 py-4 whitespace-nowrap text-center">
        <div class="flex justify-center">
            {{ form.hassubcat }}
        </div>
        {% if form.hassubcat.errors %}
            <p class="text-xs text-red-600 mt-1">{{ form.hassubcat.errors.0 }}</p>
        {% endif %}
    </td>
    <td class="px-6 py-4 whitespace-nowrap text-center">
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <i data-lucide="edit" class="w-3 h-3 mr-1"></i>
            Editing
        </span>
    </td>
</tr>