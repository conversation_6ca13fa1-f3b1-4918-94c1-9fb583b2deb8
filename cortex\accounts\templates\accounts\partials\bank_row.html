<!-- accounts/partials/bank_row.html -->
<!-- HTMX partial for single Bank row - SAP S/4HANA inspired -->
<!-- Used for updating individual bank rows after edit/create -->

{% load static %}

<tr class="hover:bg-sap-gray-50 transition-colors duration-150" id="bank-row-{{ bank.id }}">
    <td class="px-6 py-4 whitespace-nowrap">
        <div class="flex items-center">
            <div class="w-8 h-8 bg-sap-green-100 rounded-lg flex items-center justify-center mr-3">
                <i data-lucide="landmark" class="w-4 h-4 text-sap-green-600"></i>
            </div>
            <div>
                <div class="text-sm font-medium text-sap-gray-900">{{ bank.name|default:"N/A" }}</div>
                <div class="text-xs text-sap-gray-500">ID: {{ bank.id }}</div>
            </div>
        </div>
    </td>
    <td class="px-6 py-4">
        <div class="text-sm text-sap-gray-900 max-w-xs">
            {{ bank.address|default:"N/A"|truncatechars:80 }}
        </div>
        {% if bank.pin_no %}
        <div class="text-xs text-sap-gray-500">PIN: {{ bank.pin_no }}</div>
        {% endif %}
    </td>
    <td class="px-6 py-4 whitespace-nowrap">
        <div class="text-sm text-sap-gray-900">
            {% if bank.city %}{{ bank.city.city_name }}{% else %}N/A{% endif %}
        </div>
        <div class="text-xs text-sap-gray-500">
            {% if bank.state %}{{ bank.state.state_name }}, {% endif %}
            {% if bank.country %}{{ bank.country.country_name }}{% else %}N/A{% endif %}
        </div>
    </td>
    <td class="px-6 py-4 whitespace-nowrap">
        <div class="text-sm text-sap-gray-900">
            {% if bank.contact_no %}
                <div class="flex items-center">
                    <i data-lucide="phone" class="w-3 h-3 mr-1 text-sap-gray-400"></i>
                    {{ bank.contact_no }}
                </div>
            {% endif %}
        </div>
        {% if bank.fax_no %}
        <div class="text-xs text-sap-gray-500 flex items-center">
            <i data-lucide="printer" class="w-3 h-3 mr-1 text-sap-gray-400"></i>
            {{ bank.fax_no }}
        </div>
        {% endif %}
    </td>
    <td class="px-6 py-4 whitespace-nowrap">
        <span class="inline-flex px-2 py-1 text-xs font-mono bg-sap-blue-100 text-sap-blue-800 rounded">
            {{ bank.ifsc|default:"N/A" }}
        </span>
    </td>
    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div class="flex justify-end space-x-2">
            <!-- Edit Button -->
            <button type="button"
                    hx-get="{% url 'accounts:bank_edit' bank.id %}"
                    hx-target="#bank-row-{{ bank.id }}"
                    hx-swap="outerHTML"
                    class="inline-flex items-center px-3 py-1.5 border border-sap-blue-300 rounded text-xs font-medium text-sap-blue-700 bg-sap-blue-50 hover:bg-sap-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-blue-500 transition-colors duration-200">
                <i data-lucide="edit" class="w-3 h-3 mr-1"></i>
                Edit
            </button>
            <!-- Delete Button -->
            <button type="button"
                    hx-delete="{% url 'accounts:bank_delete' bank.id %}"
                    hx-target="#bank-row-{{ bank.id }}"
                    hx-swap="outerHTML"
                    hx-confirm="Are you sure you want to delete this bank? This action cannot be undone."
                    class="inline-flex items-center px-3 py-1.5 border border-sap-red-300 rounded text-xs font-medium text-sap-red-700 bg-sap-red-50 hover:bg-sap-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-red-500 transition-colors duration-200">
                <i data-lucide="trash-2" class="w-3 h-3 mr-1"></i>
                Delete
            </button>
        </div>
    </td>
</tr>