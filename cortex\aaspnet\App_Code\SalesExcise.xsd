﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="SalesExcise" targetNamespace="http://tempuri.org/SalesExcise.xsd" xmlns:mstns="http://tempuri.org/SalesExcise.xsd" xmlns="http://tempuri.org/SalesExcise.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="SalesExcise" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="SalesExcise" msprop:Generator_DataSetName="SalesExcise">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" msprop:Generator_UserColumnName="Id" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnPropNameInTable="IdColumn" type="xs:int" />
              <xs:element name="SysDate" msprop:Generator_UserColumnName="SysDate" msprop:Generator_ColumnVarNameInTable="columnSysDate" msprop:Generator_ColumnPropNameInRow="SysDate" msprop:Generator_ColumnPropNameInTable="SysDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="Commodity" msprop:Generator_UserColumnName="Commodity" msprop:Generator_ColumnVarNameInTable="columnCommodity" msprop:Generator_ColumnPropNameInRow="Commodity" msprop:Generator_ColumnPropNameInTable="CommodityColumn" type="xs:string" minOccurs="0" />
              <xs:element name="InvoiceNo" msprop:Generator_UserColumnName="InvoiceNo" msprop:Generator_ColumnVarNameInTable="columnInvoiceNo" msprop:Generator_ColumnPropNameInRow="InvoiceNo" msprop:Generator_ColumnPropNameInTable="InvoiceNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="TarrifNo" msprop:Generator_UserColumnName="TarrifNo" msprop:Generator_ColumnVarNameInTable="columnTarrifNo" msprop:Generator_ColumnPropNameInRow="TarrifNo" msprop:Generator_ColumnPropNameInTable="TarrifNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="UOM" msprop:Generator_UserColumnName="UOM" msprop:Generator_ColumnPropNameInRow="UOM" msprop:Generator_ColumnVarNameInTable="columnUOM" msprop:Generator_ColumnPropNameInTable="UOMColumn" type="xs:string" minOccurs="0" />
              <xs:element name="MFG" msprop:Generator_UserColumnName="MFG" msprop:Generator_ColumnPropNameInRow="MFG" msprop:Generator_ColumnVarNameInTable="columnMFG" msprop:Generator_ColumnPropNameInTable="MFGColumn" type="xs:double" minOccurs="0" />
              <xs:element name="CLR" msprop:Generator_UserColumnName="CLR" msprop:Generator_ColumnPropNameInRow="CLR" msprop:Generator_ColumnVarNameInTable="columnCLR" msprop:Generator_ColumnPropNameInTable="CLRColumn" type="xs:double" minOccurs="0" />
              <xs:element name="CLO" msprop:Generator_UserColumnName="CLO" msprop:Generator_ColumnPropNameInRow="CLO" msprop:Generator_ColumnVarNameInTable="columnCLO" msprop:Generator_ColumnPropNameInTable="CLOColumn" type="xs:double" minOccurs="0" />
              <xs:element name="AssValue" msprop:Generator_UserColumnName="AssValue" msprop:Generator_ColumnPropNameInRow="AssValue" msprop:Generator_ColumnVarNameInTable="columnAssValue" msprop:Generator_ColumnPropNameInTable="AssValueColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PF" msprop:Generator_UserColumnName="PF" msprop:Generator_ColumnPropNameInRow="PF" msprop:Generator_ColumnVarNameInTable="columnPF" msprop:Generator_ColumnPropNameInTable="PFColumn" type="xs:double" minOccurs="0" />
              <xs:element name="CENVAT" msprop:Generator_UserColumnName="CENVAT" msprop:Generator_ColumnPropNameInRow="CENVAT" msprop:Generator_ColumnVarNameInTable="columnCENVAT" msprop:Generator_ColumnPropNameInTable="CENVATColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Freight" msprop:Generator_UserColumnName="Freight" msprop:Generator_ColumnPropNameInRow="Freight" msprop:Generator_ColumnVarNameInTable="columnFreight" msprop:Generator_ColumnPropNameInTable="FreightColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Sn" msprop:Generator_UserColumnName="Sn" msprop:Generator_ColumnVarNameInTable="columnSn" msprop:Generator_ColumnPropNameInRow="Sn" msprop:Generator_ColumnPropNameInTable="SnColumn" type="xs:int" minOccurs="0" />
              <xs:element name="Edu" msprop:Generator_UserColumnName="Edu" msprop:Generator_ColumnPropNameInRow="Edu" msprop:Generator_ColumnVarNameInTable="columnEdu" msprop:Generator_ColumnPropNameInTable="EduColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Excise" msprop:Generator_UserColumnName="Excise" msprop:Generator_ColumnPropNameInRow="Excise" msprop:Generator_ColumnVarNameInTable="columnExcise" msprop:Generator_ColumnPropNameInTable="ExciseColumn" type="xs:double" minOccurs="0" />
              <xs:element name="She" msprop:Generator_UserColumnName="She" msprop:Generator_ColumnPropNameInRow="She" msprop:Generator_ColumnVarNameInTable="columnShe" msprop:Generator_ColumnPropNameInTable="SheColumn" type="xs:double" minOccurs="0" />
              <xs:element name="VATCST" msprop:Generator_UserColumnName="VATCST" msprop:Generator_ColumnPropNameInRow="VATCST" msprop:Generator_ColumnVarNameInTable="columnVATCST" msprop:Generator_ColumnPropNameInTable="VATCSTColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Total" msprop:Generator_UserColumnName="Total" msprop:Generator_ColumnPropNameInRow="Total" msprop:Generator_ColumnVarNameInTable="columnTotal" msprop:Generator_ColumnPropNameInTable="TotalColumn" type="xs:double" minOccurs="0" />
              <xs:element name="OtherAmt" msprop:Generator_UserColumnName="OtherAmt" msprop:Generator_ColumnPropNameInRow="OtherAmt" msprop:Generator_ColumnVarNameInTable="columnOtherAmt" msprop:Generator_ColumnPropNameInTable="OtherAmtColumn" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="DataTable2" msprop:Generator_UserTableName="DataTable2" msprop:Generator_RowDeletedName="DataTable2RowDeleted" msprop:Generator_RowChangedName="DataTable2RowChanged" msprop:Generator_RowClassName="DataTable2Row" msprop:Generator_RowChangingName="DataTable2RowChanging" msprop:Generator_RowEvArgName="DataTable2RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable2RowChangeEventHandler" msprop:Generator_TableClassName="DataTable2DataTable" msprop:Generator_TableVarName="tableDataTable2" msprop:Generator_RowDeletingName="DataTable2RowDeleting" msprop:Generator_TablePropName="DataTable2">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="Commodity" msprop:Generator_UserColumnName="Commodity" msprop:Generator_ColumnVarNameInTable="columnCommodity" msprop:Generator_ColumnPropNameInRow="Commodity" msprop:Generator_ColumnPropNameInTable="CommodityColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CETSHNo" msdata:Caption="TarrifNo" msprop:Generator_UserColumnName="CETSHNo" msprop:Generator_ColumnVarNameInTable="columnCETSHNo" msprop:Generator_ColumnPropNameInRow="CETSHNo" msprop:Generator_ColumnPropNameInTable="CETSHNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="UOM" msprop:Generator_UserColumnName="UOM" msprop:Generator_ColumnVarNameInTable="columnUOM" msprop:Generator_ColumnPropNameInRow="UOM" msprop:Generator_ColumnPropNameInTable="UOMColumn" type="xs:string" minOccurs="0" />
              <xs:element name="MFG" msprop:Generator_UserColumnName="MFG" msprop:Generator_ColumnVarNameInTable="columnMFG" msprop:Generator_ColumnPropNameInRow="MFG" msprop:Generator_ColumnPropNameInTable="MFGColumn" type="xs:double" minOccurs="0" />
              <xs:element name="CLR" msprop:Generator_UserColumnName="CLR" msprop:Generator_ColumnVarNameInTable="columnCLR" msprop:Generator_ColumnPropNameInRow="CLR" msprop:Generator_ColumnPropNameInTable="CLRColumn" type="xs:double" minOccurs="0" />
              <xs:element name="CLO" msprop:Generator_UserColumnName="CLO" msprop:Generator_ColumnVarNameInTable="columnCLO" msprop:Generator_ColumnPropNameInRow="CLO" msprop:Generator_ColumnPropNameInTable="CLOColumn" type="xs:double" minOccurs="0" />
              <xs:element name="AssValue" msprop:Generator_UserColumnName="AssValue" msprop:Generator_ColumnVarNameInTable="columnAssValue" msprop:Generator_ColumnPropNameInRow="AssValue" msprop:Generator_ColumnPropNameInTable="AssValueColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PF" msprop:Generator_UserColumnName="PF" msprop:Generator_ColumnVarNameInTable="columnPF" msprop:Generator_ColumnPropNameInRow="PF" msprop:Generator_ColumnPropNameInTable="PFColumn" type="xs:double" minOccurs="0" />
              <xs:element name="BasicAmt" msprop:Generator_UserColumnName="BasicAmt" msprop:Generator_ColumnPropNameInRow="BasicAmt" msprop:Generator_ColumnVarNameInTable="columnBasicAmt" msprop:Generator_ColumnPropNameInTable="BasicAmtColumn" type="xs:double" minOccurs="0" />
              <xs:element name="CENVAT" msprop:Generator_UserColumnName="CENVAT" msprop:Generator_ColumnVarNameInTable="columnCENVAT" msprop:Generator_ColumnPropNameInRow="CENVAT" msprop:Generator_ColumnPropNameInTable="CENVATColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Freight" msprop:Generator_UserColumnName="Freight" msprop:Generator_ColumnVarNameInTable="columnFreight" msprop:Generator_ColumnPropNameInRow="Freight" msprop:Generator_ColumnPropNameInTable="FreightColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Sn" msprop:Generator_UserColumnName="Sn" msprop:Generator_ColumnVarNameInTable="columnSn" msprop:Generator_ColumnPropNameInRow="Sn" msprop:Generator_ColumnPropNameInTable="SnColumn" type="xs:int" minOccurs="0" />
              <xs:element name="Edu" msprop:Generator_UserColumnName="Edu" msprop:Generator_ColumnVarNameInTable="columnEdu" msprop:Generator_ColumnPropNameInRow="Edu" msprop:Generator_ColumnPropNameInTable="EduColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Excise" msprop:Generator_UserColumnName="Excise" msprop:Generator_ColumnVarNameInTable="columnExcise" msprop:Generator_ColumnPropNameInRow="Excise" msprop:Generator_ColumnPropNameInTable="ExciseColumn" type="xs:double" minOccurs="0" />
              <xs:element name="She" msprop:Generator_UserColumnName="She" msprop:Generator_ColumnVarNameInTable="columnShe" msprop:Generator_ColumnPropNameInRow="She" msprop:Generator_ColumnPropNameInTable="SheColumn" type="xs:double" minOccurs="0" />
              <xs:element name="VATCST" msprop:Generator_UserColumnName="VATCST" msprop:Generator_ColumnVarNameInTable="columnVATCST" msprop:Generator_ColumnPropNameInRow="VATCST" msprop:Generator_ColumnPropNameInTable="VATCSTColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Total" msprop:Generator_UserColumnName="Total" msprop:Generator_ColumnVarNameInTable="columnTotal" msprop:Generator_ColumnPropNameInRow="Total" msprop:Generator_ColumnPropNameInTable="TotalColumn" type="xs:double" minOccurs="0" />
              <xs:element name="s" msprop:Generator_UserColumnName="s" msprop:Generator_ColumnPropNameInRow="s" msprop:Generator_ColumnVarNameInTable="columns" msprop:Generator_ColumnPropNameInTable="sColumn" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>