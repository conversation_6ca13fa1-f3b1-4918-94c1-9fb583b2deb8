/* Splitter Container */
.ob_spl_panel
{

}

/* Vertical Splitter - Left Panel classes */
.ob_spl_leftpanel 
{
}
.ob_spl_leftpanelheader 
{
	box-sizing:border-box;
	-moz-box-sizing:border-box;
	background-color:white;
	overflow:auto;
}
.ob_spl_leftpanelcontent 
{
	box-sizing:border-box;
	-moz-box-sizing:border-box;
	background-color:white;
	overflow:auto;
	position: relative;
}
.ob_spl_leftpanelfooter 
{
	box-sizing:border-box;
	-moz-box-sizing:border-box;
	background-color:white;
	overflow:auto;
}

/* Vertical Splitter - Right Panel classes */
.ob_spl_rightpanel 
{
}
.ob_spl_rightpanelheader 
{
	box-sizing:border-box;
	-moz-box-sizing:border-box;
	background-color:white;
	overflow:auto;
	background-image:url(pagebg.jpg);background-repeat:repeat-y;
	
}
.ob_spl_rightpanelcontent 
{
	box-sizing:border-box;
	-moz-box-sizing:border-box;
	background-color:white;
	overflow:auto;
	background-image:url(pagebg.jpg);background-repeat:repeat-y;
	position: relative;
	
}
.ob_spl_rightpanelfooter 
{
	box-sizing:border-box;
	-moz-box-sizing:border-box;
	background-color:white;
	overflow:auto;
	background-image:url(pagebg.jpg);background-repeat:repeat-y;
	
}

/* Horizontal Splitter - Top Panel classes */
.ob_spl_toppanel
{
	
}
.ob_spl_toppanelheader 
{
	box-sizing:border-box;
	-moz-box-sizing:border-box;
	background-color:white;
	overflow:auto;
}
.ob_spl_toppanelcontent 
{
	box-sizing:border-box;
	-moz-box-sizing:border-box;
	background-color:white;
	overflow:auto;
	position: relative;
}
.ob_spl_toppanelfooter 
{
	box-sizing:border-box;
	-moz-box-sizing:border-box;
	background-color:white;
	overflow:auto;
}

/* Horizontal Splitter - Bottom Panel classes */
.ob_spl_bottompanel
{

}
.ob_spl_bottompanelheader 
{
	box-sizing:border-box;
	-moz-box-sizing:border-box;
	background-color:white;
	overflow:auto;
	background-image:url(pagebg.jpg);background-repeat:repeat-y
}
.ob_spl_bottompanelcontent 
{
	box-sizing:border-box;
	-moz-box-sizing:border-box;
	background-color:white;
	overflow:auto;
	background-image:url(pagebg.jpg);background-repeat:repeat-y;
	position: relative;
	
}
.ob_spl_bottompanelfooter 
{
	box-sizing:border-box;
	-moz-box-sizing:border-box;
	background-color:white;
	overflow:auto;
	background-image:url(pagebg.jpg);background-repeat:repeat-y
}

/* Splitter Divider */
.ob_spl_dividervertical
{
	width:6px;
	height:6px;
	background-color:#6B89AF;
	font-size:1px;
}
.ob_spl_dividerhorizontal
{
	width:6px;
	height:6px;
	background-color:#6B89AF;
	font-size:1px;
}

/* Splitter ResizeBar */
.ob_spl_resizebarvertical 
{
	border-left:3px solid #336699;
}
.ob_spl_resizebarhorizontal 
{
	border-top:3px solid #336699;
}

/* Splitter Collapse/Expand */
.ob_spl_collapseleft
{
	width:5px;
	height:40px;
	cursor:pointer;
	z-index:101;
	background-image:url(arrow_left.gif);background-repeat:no-repeat;background-position:center center;
	border:0px;
}
.ob_spl_collapseright
{
	width:5px;
	height:40px;
	cursor:pointer;
	z-index:101;
	background-image:url(arrow_right.gif);background-repeat:no-repeat;background-position:center center;
	border:0px;
}

.ob_spl_collapsetop
{
	width:40px;
	height:5px;
	cursor:pointer;
	z-index:101;
	background-image:url(arrow_top.gif);background-repeat:no-repeat;background-position:center center;
	border:0px;
}

.ob_spl_collapsebottom
{
	width:40px;
	height:5px;
	cursor:pointer;
	z-index:101;
	background-image:url(arrow_bottom.gif);background-repeat:no-repeat;background-position:center center;
	border:0px;
}