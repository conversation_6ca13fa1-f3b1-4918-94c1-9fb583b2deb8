<!-- accounts/templates/accounts/reports/partials/vat_summary_cards.html -->
<!-- VAT Summary Cards Partial Template for HTMX Updates -->

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <!-- Current Month Sales VAT -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm overflow-hidden">
        <div class="bg-gradient-to-r from-sap-blue-500 to-sap-blue-600 px-6 py-4">
            <div class="flex items-center">
                <i data-lucide="trending-up" class="w-8 h-8 text-white mr-3"></i>
                <div class="text-white">
                    <p class="text-sm font-medium opacity-90">Sales VAT (Output)</p>
                    <p class="text-xs opacity-75">{{ period_description|default:"Selected Period" }}</p>
                </div>
            </div>
        </div>
        <div class="px-6 py-4">
            <div class="text-2xl font-bold text-sap-gray-900 mb-2">
                ₹{{ sales_vat.total_vat|floatformat:2|default:"0.00" }}
            </div>
            <div class="text-sm text-sap-gray-600">
                {{ sales_vat.count|default:0 }} invoices
            </div>
            <div class="text-xs text-sap-gray-500 mt-1">
                Basic: ₹{{ sales_vat.total_basic|floatformat:2|default:"0.00" }}
            </div>
        </div>
        <div class="px-6 py-3 bg-sap-gray-50 border-t border-sap-gray-100">
            <a href="{% url 'accounts:sales_vat_register' %}" 
               class="text-sap-blue-600 hover:text-sap-blue-700 text-sm font-medium">
                View Details <i data-lucide="arrow-right" class="w-3 h-3 inline ml-1"></i>
            </a>
        </div>
    </div>

    <!-- Current Month Purchase VAT -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm overflow-hidden">
        <div class="bg-gradient-to-r from-sap-green-500 to-sap-green-600 px-6 py-4">
            <div class="flex items-center">
                <i data-lucide="trending-down" class="w-8 h-8 text-white mr-3"></i>
                <div class="text-white">
                    <p class="text-sm font-medium opacity-90">Purchase VAT (Input)</p>
                    <p class="text-xs opacity-75">{{ period_description|default:"Selected Period" }}</p>
                </div>
            </div>
        </div>
        <div class="px-6 py-4">
            <div class="text-2xl font-bold text-sap-gray-900 mb-2">
                ₹{{ purchase_vat.total_vat|floatformat:2|default:"0.00" }}
            </div>
            <div class="text-sm text-sap-gray-600">
                {{ purchase_vat.count|default:0 }} bills
            </div>
            <div class="text-xs text-sap-gray-500 mt-1">
                Basic: ₹{{ purchase_vat.total_basic|floatformat:2|default:"0.00" }}
            </div>
        </div>
        <div class="px-6 py-3 bg-sap-gray-50 border-t border-sap-gray-100">
            <a href="{% url 'accounts:purchase_vat_register' %}" 
               class="text-sap-green-600 hover:text-sap-green-700 text-sm font-medium">
                View Details <i data-lucide="arrow-right" class="w-3 h-3 inline ml-1"></i>
            </a>
        </div>
    </div>

    <!-- Net VAT Liability -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm overflow-hidden">
        <div class="bg-gradient-to-r from-sap-orange-500 to-sap-orange-600 px-6 py-4">
            <div class="flex items-center">
                <i data-lucide="calculator" class="w-8 h-8 text-white mr-3"></i>
                <div class="text-white">
                    <p class="text-sm font-medium opacity-90">Net VAT Liability</p>
                    <p class="text-xs opacity-75">{{ period_description|default:"Selected Period" }}</p>
                </div>
            </div>
        </div>
        <div class="px-6 py-4">
            <div class="text-2xl font-bold mb-2 {% if net_vat_liability >= 0 %}text-sap-red-600{% else %}text-sap-green-600{% endif %}">
                ₹{{ net_vat_liability|floatformat:2|default:"0.00" }}
            </div>
            <div class="text-sm text-sap-gray-600">
                {% if net_vat_liability >= 0 %}
                    Payable to Government
                {% else %}
                    Refundable from Government
                {% endif %}
            </div>
            <div class="text-xs text-sap-gray-500 mt-1">
                Turnover: ₹{{ total_turnover|floatformat:2|default:"0.00" }}
            </div>
        </div>
        <div class="px-6 py-3 bg-sap-gray-50 border-t border-sap-gray-100">
            <a href="{% url 'accounts:vat_return_form' %}" 
               class="text-sap-orange-600 hover:text-sap-orange-700 text-sm font-medium">
                File Return <i data-lucide="arrow-right" class="w-3 h-3 inline ml-1"></i>
            </a>
        </div>
    </div>

    <!-- Effective Tax Rate -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm overflow-hidden">
        <div class="bg-gradient-to-r from-sap-gray-600 to-sap-gray-700 px-6 py-4">
            <div class="flex items-center">
                <i data-lucide="percent" class="w-8 h-8 text-white mr-3"></i>
                <div class="text-white">
                    <p class="text-sm font-medium opacity-90">Effective VAT Rate</p>
                    <p class="text-xs opacity-75">{{ period_description|default:"Selected Period" }}</p>
                </div>
            </div>
        </div>
        <div class="px-6 py-4">
            <div class="text-2xl font-bold text-sap-gray-900 mb-2">
                {% if total_turnover and total_turnover > 0 %}
                    {% widthratio net_vat_liability total_turnover 100 %}%
                {% else %}
                    0.00%
                {% endif %}
            </div>
            <div class="text-sm text-sap-gray-600">
                {% if net_vat_liability >= 0 %}Tax burden{% else %}Tax benefit{% endif %} rate
            </div>
            <div class="text-xs text-sap-gray-500 mt-1">
                {{ sales_vat.count|add:purchase_vat.count|default:0 }} transactions
            </div>
        </div>
        <div class="px-6 py-3 bg-sap-gray-50 border-t border-sap-gray-100">
            <button onclick="analyzeEffectiveRate()" 
                    class="text-sap-gray-600 hover:text-sap-gray-700 text-sm font-medium">
                Analyze Rate <i data-lucide="arrow-right" class="w-3 h-3 inline ml-1"></i>
            </button>
        </div>
    </div>
</div>

<script>
// Re-initialize icons after HTMX update
lucide.createIcons();

// Function to analyze effective rate
function analyzeEffectiveRate() {
    // Implementation for effective rate analysis
    console.log('Analyzing effective VAT rate...');
}
</script>