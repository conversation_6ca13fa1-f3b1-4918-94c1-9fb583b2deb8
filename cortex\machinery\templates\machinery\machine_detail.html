{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Machine Details{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="sm:flex sm:items-center sm:justify-between mb-8">
        <div class="sm:flex-auto">
            <h1 class="text-2xl font-semibold text-gray-900">Machine Details</h1>
            <p class="mt-2 text-sm text-gray-700">Complete information about this machine.</p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none space-x-3">
            <a href="{% url 'machinery:machine_edit' machine.pk %}" 
               class="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                Edit Machine
            </a>
            <a href="{% url 'machinery:machine_list' %}" 
               class="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                Back to List
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Basic Information</h3>
                    
                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Machine Code</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ machine.itemid|default:"Not specified" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Make</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ machine.make|default:"Not specified" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Model</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ machine.model|default:"Not specified" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Capacity</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ machine.capacity|default:"Not specified" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Location</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ machine.location|default:"Not specified" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Person In-charge</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ machine.incharge|default:"Not assigned" }}</dd>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Purchase & Financial Information -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Purchase & Financial Information</h3>
                    
                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Purchase Date</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ machine.purchasedate|default:"Not specified" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Cost</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                {% if machine.cost %}₹{{ machine.cost|floatformat:2 }}{% else %}Not specified{% endif %}
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Supplier</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ machine.suppliername|default:"Not specified" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Received Date</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ machine.receiveddate|default:"Not specified" }}</dd>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Warranty & Insurance -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Warranty & Insurance</h3>
                    
                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Warranty Expiry</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ machine.warrantyexpirydate|default:"Not specified" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Life Date</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ machine.lifedate|default:"Not specified" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Insurance Status</dt>
                            <dd class="mt-1">
                                {% if machine.insurance == 1 %}
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        Insured
                                    </span>
                                {% else %}
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                        Not Insured
                                    </span>
                                {% endif %}
                            </dd>
                        </div>
                        {% if machine.insurance == 1 %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Insurance Expiry</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ machine.insuranceexpirydate|default:"Not specified" }}</dd>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Maintenance Information -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Maintenance Information</h3>
                    
                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Put to Use Date</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ machine.puttouse|default:"Not specified" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">PM Interval (Days)</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ machine.pmdays|default:"Not set" }}</dd>
                        </div>
                    </div>
                </div>
            </div>

            <!-- File Attachment -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Attachments</h3>
                    
                    {% if machine.filename and machine.filedata %}
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <svg class="h-5 w-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                </svg>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ machine.filename }}</p>
                                    <p class="text-xs text-gray-500">
                                        {% if machine.filesize %}
                                            Size: {{ machine.filesize|filesizeformat }}
                                        {% endif %}
                                        {% if machine.contenttype %}
                                            | Type: {{ machine.contenttype }}
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                            <a href="{% url 'machinery:machine_download' machine.id %}" 
                               class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Download
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-6">
                            <svg class="h-8 w-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <p class="text-sm text-gray-500">No files attached</p>
                            <p class="text-xs text-gray-400">Edit machine to add attachments</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Actions -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
                    
                    <div class="space-y-3">
                        <a href="{% url 'machinery:maintenance_create' %}?machine={{ machine.id }}" 
                           class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            Schedule Maintenance
                        </a>
                        
                        <a href="{% url 'machinery:schedule_create' %}?machine={{ machine.id }}" 
                           class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Create Job Schedule
                        </a>
                    </div>
                </div>
            </div>

            <!-- Machine Status -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Machine Status</h3>
                    
                    <div class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Created Date</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ machine.sysdate|default:"Not available" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ machine.sysdate|default:"Not available" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Updated By</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ machine.user.username|default:"System" }}</dd>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Maintenance History Summary -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Maintenance Summary</h3>
                    
                    <div class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Total Maintenance Records</dt>
                            <dd class="mt-1 text-lg font-semibold text-gray-900">{{ maintenance_history.count }}</dd>
                        </div>
                        
                        {% if maintenance_history.first %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Last Maintenance</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ maintenance_history.first.sysdate|default:"Never" }}</dd>
                        </div>
                        {% endif %}
                    </div>
                    
                    {% if maintenance_history %}
                    <div class="mt-4">
                        <a href="{% url 'machinery:maintenance_list' %}?machine={{ machine.id }}" 
                           class="text-indigo-600 hover:text-indigo-900 text-sm">
                            View all maintenance records →
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Machine Spares -->
    {% if machine_spares %}
    <div class="mt-8">
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Machine Spares</h3>
                
                <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                    <table class="min-w-full divide-y divide-gray-300">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for spare in machine_spares %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    Item ID: {{ spare.itemid }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ spare.qty }}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Machine Processes -->
    {% if machine_processes %}
    <div class="mt-8">
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Machine Processes</h3>
                
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    {% for process in machine_processes %}
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-sm font-medium text-gray-900">Process ID: {{ process.pid }}</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}