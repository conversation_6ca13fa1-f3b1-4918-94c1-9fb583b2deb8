{% extends 'core/base.html' %}
{% load static %}

{% block title %}Goods Inward Note [GIN]{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50" x-data="ginMainApp()">
    <!-- Header -->
    <div class="bg-gray-700 text-white px-4 py-2">
        <h1 class="text-lg font-bold">Goods Inward Note [GIN]</h1>
    </div>
    
    <div class="p-4">
        <!-- Search Section -->
        <div class="bg-white rounded-lg shadow-sm border mb-4">
            <div class="p-4">
                <form @submit.prevent="searchGIN" class="flex items-center gap-4">
                    <!-- Search Type Dropdown -->
                    <select x-model="searchType" 
                            @change="onSearchTypeChange"
                            class="border border-gray-300 rounded px-3 py-2 text-sm w-48 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="0">Supplier Name</option>
                        <option value="1">PO No</option>
                        <option value="2">GIN No</option>
                    </select>
                    
                    <!-- Search Query Input - Only visible for PO No and GIN No -->
                    <input type="text" 
                           x-show="searchType === '1' || searchType === '2'"
                           x-model="searchQuery"
                           placeholder="Enter search value" 
                           class="border border-gray-300 rounded px-3 py-2 text-sm w-40 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    
                    <!-- Supplier Autocomplete Input - Only visible for Supplier Name -->
                    <div class="relative flex-1" x-show="searchType === '0'">
                        <input type="text" 
                               x-model="supplierSearch"
                               @input="searchSuppliers"
                               @focus="showSupplierDropdown = true"
                               @keydown.arrow-down.prevent="navigateSuppliers(1)"
                               @keydown.arrow-up.prevent="navigateSuppliers(-1)"
                               @keydown.enter.prevent="selectSupplier(highlightedSupplierIndex)"
                               @keydown.escape="showSupplierDropdown = false"
                               placeholder="Select supplier..." 
                               autocomplete="off"
                               class="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                        
                        <!-- Supplier Dropdown -->
                        <div x-show="showSupplierDropdown && suppliers.length > 0" 
                             x-transition
                             @click.outside="showSupplierDropdown = false"
                             class="absolute top-full left-0 right-0 mt-1 bg-teal-500 border border-gray-300 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
                            <template x-for="(supplier, index) in suppliers" :key="supplier.id">
                                <div @click="selectSupplierById(supplier.id)"
                                     :class="{'bg-teal-600': index === highlightedSupplierIndex, 'bg-teal-500': index !== highlightedSupplierIndex}"
                                     class="px-3 py-2 text-white text-sm cursor-pointer hover:bg-teal-600 border-b border-teal-400 last:border-b-0">
                                    <span x-text="supplier.display_text"></span>
                                </div>
                            </template>
                        </div>
                    </div>
                           
                    <!-- Search Button -->
                    <button type="submit" 
                            class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded text-sm font-medium transition-colors">
                        Search
                    </button>
                    
                    <!-- Create New GIN Button -->
                    <a href="{% url 'inventory:gin_new_search' %}" 
                       class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded text-sm font-medium transition-colors inline-flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        New GIN
                    </a>
                </form>
            </div>
        </div>

        <!-- Results Table -->
        <div class="bg-white rounded-lg shadow-sm border" x-show="ginRecords.length > 0 || searchPerformed">
            <div class="overflow-x-auto">
                <table class="w-full table-auto divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">SN</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">Fin Year</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">PONo</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">GIN No</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">Date</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Supplier</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">Challan No</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">Challan Date</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <template x-for="(gin, index) in ginRecords" :key="gin.id">
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-3 text-sm text-gray-900 text-center" x-text="index + 1"></td>
                                <td class="px-4 py-3 text-sm text-gray-900 text-center" x-text="gin.fin_year || 'N/A'"></td>
                                <td class="px-4 py-3 text-sm text-gray-900 text-center" x-text="gin.po_number || 'N/A'"></td>
                                <td class="px-4 py-3 text-sm text-gray-900 text-center" x-text="gin.gin_number || 'N/A'"></td>
                                <td class="px-4 py-3 text-sm text-gray-900 text-center" x-text="gin.gin_date || 'N/A'"></td>
                                <td class="px-4 py-3 text-sm text-gray-900" x-text="gin.supplier_name || 'UNKNOWN'"></td>
                                <td class="px-4 py-3 text-sm text-gray-900 text-center" x-text="gin.challan_number || ''"></td>
                                <td class="px-4 py-3 text-sm text-gray-900 text-center" x-text="gin.challan_date || ''"></td>
                                <td class="px-4 py-3 text-sm text-gray-900 text-center">
                                    <div class="flex items-center justify-center space-x-2">
                                        <button @click="selectGINRecord(gin)" 
                                                class="text-blue-600 hover:text-blue-900 text-xs font-medium">
                                            Edit
                                        </button>
                                        <button @click="viewGINRecord(gin)" 
                                                class="text-indigo-600 hover:text-indigo-900 text-xs font-medium">
                                            View
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </template>
                        
                        <!-- No Data Row -->
                        <tr x-show="ginRecords.length === 0 && searchPerformed">
                            <td colspan="9" class="px-4 py-8 text-center">
                                <div class="text-gray-500">
                                    <p class="text-lg font-medium text-red-900">No data to display !</p>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Initial State -->
        <div x-show="!searchPerformed" class="bg-white rounded-lg shadow-sm border p-8">
            <div class="text-center text-gray-500">
                <p class="text-lg font-medium">Select a supplier to view GIN records</p>
                <p class="text-sm mt-2">Use the supplier dropdown above to filter GIN records</p>
            </div>
        </div>
    </div>
</div>

<script>
function ginMainApp() {
    return {
        // Search parameters
        searchType: '0',
        searchQuery: '',
        supplierSearch: '',
        selectedSupplierId: null,
        
        // Supplier autocomplete
        suppliers: [],
        showSupplierDropdown: false,
        highlightedSupplierIndex: -1,
        
        // GIN records
        ginRecords: [],
        searchPerformed: false,
        loading: false,
        
        init() {
            // Initialize any needed data
        },
        
        onSearchTypeChange() {
            // Clear search values when switching search types (like ASP.NET)
            if (this.searchType === '0') {
                // Supplier Name selected - clear text search
                this.searchQuery = '';
            } else {
                // PO No or GIN No selected - clear supplier search
                this.supplierSearch = '';
                this.selectedSupplierId = null;
                this.showSupplierDropdown = false;
            }
        },
        
        async searchSuppliers() {
            if (this.supplierSearch.length < 2) {
                this.suppliers = [];
                this.showSupplierDropdown = false;
                return;
            }
            
            try {
                const response = await fetch(`/inventory/api/suppliers/?q=${encodeURIComponent(this.supplierSearch)}`);
                if (response.ok) {
                    this.suppliers = await response.json();
                    this.showSupplierDropdown = true;
                    this.highlightedSupplierIndex = -1;
                }
            } catch (error) {
                console.error('Error searching suppliers:', error);
            }
        },
        
        navigateSuppliers(direction) {
            if (this.suppliers.length === 0) return;
            
            const newIndex = this.highlightedSupplierIndex + direction;
            if (newIndex >= 0 && newIndex < this.suppliers.length) {
                this.highlightedSupplierIndex = newIndex;
            }
        },
        
        selectSupplier(index) {
            if (index >= 0 && index < this.suppliers.length) {
                this.selectSupplierById(this.suppliers[index].id);
            }
        },
        
        selectSupplierById(supplierId) {
            const supplier = this.suppliers.find(s => s.id === supplierId);
            if (supplier) {
                this.supplierSearch = supplier.display_text;
                this.selectedSupplierId = supplier.id;
                this.showSupplierDropdown = false;
                this.highlightedSupplierIndex = -1;
                
                // Automatically search for GIN records for this supplier
                this.searchGIN();
            }
        },
        
        async searchGIN() {
            this.loading = true;
            this.searchPerformed = true;
            
            try {
                const params = new URLSearchParams();
                params.append('search_type', this.searchType);
                if (this.searchQuery) params.append('search_query', this.searchQuery);
                if (this.selectedSupplierId) params.append('supplier_id', this.selectedSupplierId);
                
                const response = await fetch(`/inventory/api/gin-search/?${params.toString()}`);
                if (response.ok) {
                    this.ginRecords = await response.json();
                } else {
                    console.error('Failed to fetch GIN records');
                    this.ginRecords = [];
                }
            } catch (error) {
                console.error('Error searching GIN records:', error);
                this.ginRecords = [];
            } finally {
                this.loading = false;
            }
        },
        
        selectGINRecord(gin) {
            // Navigate to GIN edit page
            if (gin.id) {
                window.location.href = `/inventory/gin/${gin.id}/edit/`;
            }
        },
        
        viewGINRecord(gin) {
            // Navigate to GIN detail page
            if (gin.id) {
                window.location.href = `/inventory/gin/${gin.id}/`;
            }
        },
        
        clearSearch() {
            this.searchQuery = '';
            this.supplierSearch = '';
            this.selectedSupplierId = null;
            this.ginRecords = [];
            this.searchPerformed = false;
            this.suppliers = [];
            this.showSupplierDropdown = false;
        }
    }
}
</script>
{% endblock %}