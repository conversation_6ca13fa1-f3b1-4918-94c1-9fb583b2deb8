# Package 5: Missing Implementation Tests

from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from django.utils import timezone
from decimal import Decimal

from .models import (
    ExcisableCommodityMaster, OctoriMaster, PaymentAgainst, AssetRegisterPrimary, AssetRegisterSecondary, CapitalShareClass, LoanMasterDefinition
)
from .package5_forms import (
    ExcisableCommodityMasterForm, OctoriMasterForm, AssetRegisterPrimaryForm, PurchaseReportForm
)


class Package5ModelsTestCase(TestCase):
    """Test cases for Package 5 models"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_excisable_commodity_master_creation(self):
        """Test creating an excisable commodity master"""
        commodity = ExcisableCommodityMaster.objects.create(
            terms="Test Commodity for Electronics"
        )
        self.assertEqual(str(commodity), "Test Commodity for Electronics")
        self.assertIsNotNone(commodity.id)
    
    def test_octori_master_creation(self):
        """Test creating an octori master"""
        octori = OctoriMaster.objects.create(
            terms="Local Area Tax",
            value="2.5"
        )
        self.assertEqual(str(octori), "Local Area Tax - 2.5")
        self.assertIsNotNone(octori.id)
    
    def test_payment_against_creation(self):
        """Test creating payment against category"""
        payment_against = PaymentAgainst.objects.create(
            description="Advance Payment for Materials"
        )
        self.assertEqual(str(payment_against), "Advance Payment for Materials")
        self.assertIsNotNone(payment_against.id)
    
    def test_asset_register_primary_creation(self):
        """Test creating asset register primary"""
        asset = AssetRegisterPrimary.objects.create(
            asset_code="AST-000001",
            asset_name="Dell Laptop",
            asset_category="Computer",
            acquisition_cost=Decimal('50000.00'),
            useful_life_years=5,
            depreciation_method="Straight Line",
            depreciation_rate=Decimal('20.00'),
            status="Active",
            created_by=self.user
        )
        self.assertEqual(str(asset), "AST-000001 - Dell Laptop")
        self.assertEqual(asset.status, "Active")
        self.assertEqual(asset.acquisition_cost, Decimal('50000.00'))
    
    def test_capital_particulars_creation(self):
        """Test creating capital particulars"""
        capital = CapitalShareClass.objects.create(
            share_class="Equity",
            share_type="Common",
            face_value=Decimal('10.00'),
            authorized_shares=100000,
            issued_shares=75000,
            voting_rights="Yes"
        )
        self.assertEqual(str(capital), "Equity - Common - 10.00")
        self.assertEqual(capital.authorized_shares, 100000)
    
    def test_loan_particulars_creation(self):
        """Test creating loan particulars"""
        loan = LoanMasterDefinition.objects.create(
            loan_type="Term Loan",
            loan_category="Secured",
            loan_amount=Decimal('1000000.00'),
            interest_rate=Decimal('12.50'),
            interest_type="Fixed",
            tenure_months=60,
            loan_status="Active"
        )
        self.assertEqual(str(loan), "Term Loan - 1000000.00 - 12.50%")
        self.assertEqual(loan.tenure_months, 60)


class Package5FormsTestCase(TestCase):
    """Test cases for Package 5 forms"""
    
    def test_excisable_commodity_form_valid(self):
        """Test valid excisable commodity form"""
        form_data = {
            'terms': 'Electronics Components for Manufacturing'
        }
        form = ExcisableCommodityMasterForm(data=form_data)
        self.assertTrue(form.is_valid())
    
    def test_excisable_commodity_form_invalid(self):
        """Test invalid excisable commodity form"""
        form_data = {
            'terms': 'AB'  # Too short
        }
        form = ExcisableCommodityMasterForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('terms', form.errors)
    
    def test_octori_form_valid(self):
        """Test valid octori form"""
        form_data = {
            'terms': 'City Development Tax',
            'value': '1.5'
        }
        form = OctoriMasterForm(data=form_data)
        self.assertTrue(form.is_valid())
    
    def test_octori_form_invalid_negative_rate(self):
        """Test octori form with negative rate"""
        form_data = {
            'terms': 'Invalid Tax',
            'value': '-1.0'
        }
        form = OctoriMasterForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('value', form.errors)
    
    def test_asset_register_form_valid(self):
        """Test valid asset register form"""
        form_data = {
            'asset_name': 'Office Printer',
            'asset_category': 'Equipment',
            'acquisition_cost': '15000.00',
            'useful_life_years': 3,
            'depreciation_method': 'Straight Line',
            'depreciation_rate': '33.33',
            'status': 'Active'
        }
        form = AssetRegisterPrimaryForm(data=form_data)
        self.assertTrue(form.is_valid())
    
    def test_purchase_report_form_valid(self):
        """Test valid purchase report form"""
        form_data = {
            'from_date': '2024-01-01',
            'to_date': '2024-12-31',
            'report_type': 'summary'
        }
        form = PurchaseReportForm(data=form_data)
        self.assertTrue(form.is_valid())
    
    def test_purchase_report_form_invalid_dates(self):
        """Test purchase report form with invalid date range"""
        form_data = {
            'from_date': '2024-12-31',
            'to_date': '2024-01-01',  # From date after to date
            'report_type': 'summary'
        }
        form = PurchaseReportForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('__all__', form.errors)


class Package5ViewsTestCase(TestCase):
    """Test cases for Package 5 views"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.login(username='testuser', password='testpass123')
        
        # Create test data
        self.commodity = ExcisableCommodityMaster.objects.create(
            terms="Test Commodity"
        )
        self.octori = OctoriMaster.objects.create(
            terms="Test Tax",
            value="2.0"
        )
        self.asset = AssetRegisterPrimary.objects.create(
            asset_code="TST-001",
            asset_name="Test Asset",
            asset_category="Equipment",
            acquisition_cost=Decimal('10000.00'),
            useful_life_years=5,
            depreciation_rate=Decimal('20.00'),
            status="Active",
            created_by=self.user
        )
    
    def test_excisable_commodity_list_view(self):
        """Test excisable commodity list view"""
        url = reverse('accounts:excisable_commodity_list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Commodity')
        self.assertContains(response, 'Excisable Commodity Management')
    
    def test_excisable_commodity_create_view_get(self):
        """Test excisable commodity create view GET"""
        url = reverse('accounts:excisable_commodity_create')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Add New Excisable Commodity')
    
    def test_excisable_commodity_create_view_post(self):
        """Test excisable commodity create view POST"""
        url = reverse('accounts:excisable_commodity_create')
        data = {
            'terms': 'New Test Commodity'
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 302)  # Redirect after successful creation
        self.assertTrue(
            ExcisableCommodityMaster.objects.filter(terms='New Test Commodity').exists()
        )
    
    def test_octori_list_view(self):
        """Test octori list view"""
        url = reverse('accounts:octori_list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Tax')
        self.assertContains(response, 'Octori Tax Management')
    
    def test_asset_register_primary_list_view(self):
        """Test asset register primary list view"""
        url = reverse('accounts:asset_register_primary_list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'TST-001')
        self.assertContains(response, 'Test Asset')
        self.assertContains(response, 'Asset Register - Primary')
    
    def test_asset_register_primary_create_view_get(self):
        """Test asset register primary create view GET"""
        url = reverse('accounts:asset_register_primary_create')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Register New Asset')
    
    def test_purchase_report_view(self):
        """Test purchase report view"""
        url = reverse('accounts:purchase_report')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Purchase Report')
    
    def test_search_functionality(self):
        """Test search functionality in list views"""
        # Test excisable commodity search
        url = reverse('accounts:excisable_commodity_list')
        response = self.client.get(url, {'search': 'Test'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Commodity')
        
        # Test octori search
        url = reverse('accounts:octori_list')
        response = self.client.get(url, {'search': 'Tax'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Tax')
        
        # Test asset search
        url = reverse('accounts:asset_register_primary_list')
        response = self.client.get(url, {'search': 'TST'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'TST-001')
    
    def test_unauthorized_access(self):
        """Test unauthorized access to views"""
        self.client.logout()
        
        url = reverse('accounts:excisable_commodity_list')
        response = self.client.get(url)
        self.assertRedirects(response, f'/login/?next={url}')


class Package5AjaxViewsTestCase(TestCase):
    """Test cases for Package 5 AJAX views"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.login(username='testuser', password='testpass123')
        
        self.asset = AssetRegisterPrimary.objects.create(
            asset_code="TST-001",
            asset_name="Test Asset",
            asset_category="Equipment",
            acquisition_cost=Decimal('100000.00'),
            useful_life_years=5,
            depreciation_rate=Decimal('20.00'),
            status="Active",
            created_by=self.user
        )
    
    def test_asset_depreciation_schedule_ajax(self):
        """Test asset depreciation schedule AJAX endpoint"""
        url = reverse('accounts:ajax_asset_depreciation_schedule', args=[self.asset.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertTrue(data['success'])
        self.assertIn('schedule', data)
        self.assertEqual(len(data['schedule']), 5)  # 5 years
        
        # Check first year calculation
        first_year = data['schedule'][0]
        self.assertEqual(first_year['year'], 1)
        self.assertEqual(first_year['depreciation'], 20000.0)  # 20% of 100,000
    
    def test_loan_emi_calculator_ajax(self):
        """Test loan EMI calculator AJAX endpoint"""
        url = reverse('accounts:ajax_loan_emi_calculator')
        response = self.client.get(url, {
            'loan_amount': '1000000',
            'interest_rate': '12',
            'tenure_months': '60'
        })
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertTrue(data['success'])
        self.assertIn('emi', data)
        self.assertIn('total_amount', data)
        self.assertIn('total_interest', data)
        
        # Basic calculation check
        self.assertGreater(data['emi'], 0)
        self.assertGreater(data['total_amount'], 1000000)  # Should be more than principal
    
    def test_loan_emi_calculator_invalid_params(self):
        """Test loan EMI calculator with invalid parameters"""
        url = reverse('accounts:ajax_loan_emi_calculator')
        response = self.client.get(url, {
            'loan_amount': '0',
            'interest_rate': '12',
            'tenure_months': '60'
        })
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertFalse(data['success'])
        self.assertIn('error', data)


class Package5IntegrationTestCase(TestCase):
    """Integration test cases for Package 5 functionality"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.login(username='testuser', password='testpass123')
    
    def test_complete_asset_management_workflow(self):
        """Test complete asset management workflow"""
        # Step 1: Create a new asset
        create_url = reverse('accounts:asset_register_primary_create')
        asset_data = {
            'asset_name': 'Integration Test Asset',
            'asset_category': 'Equipment',
            'acquisition_cost': '50000.00',
            'useful_life_years': 4,
            'depreciation_method': 'Straight Line',
            'depreciation_rate': '25.00',
            'status': 'Active',
            'location': 'Main Office'
        }
        response = self.client.post(create_url, asset_data)
        self.assertEqual(response.status_code, 302)
        
        # Step 2: Verify asset was created
        asset = AssetRegisterPrimary.objects.get(asset_name='Integration Test Asset')
        self.assertIsNotNone(asset)
        self.assertEqual(asset.acquisition_cost, Decimal('50000.00'))
        
        # Step 3: Check depreciation schedule
        schedule_url = reverse('accounts:ajax_asset_depreciation_schedule', args=[asset.id])
        response = self.client.get(schedule_url)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertEqual(len(data['schedule']), 4)
        
        # Step 4: Create secondary transaction
        secondary_url = reverse('accounts:asset_register_secondary_create')
        secondary_data = {
            'asset': asset.id,
            'transaction_date': timezone.now().date(),
            'transaction_type': 'Maintenance',
            'maintenance_cost': '5000.00',
            'remarks': 'Annual maintenance',
            'authorized_by': 'Test Manager'
        }
        response = self.client.post(secondary_url, secondary_data)
        self.assertEqual(response.status_code, 302)
        
        # Step 5: Verify secondary transaction
        secondary = AssetRegisterSecondary.objects.get(asset=asset)
        self.assertEqual(secondary.transaction_type, 'Maintenance')
        self.assertEqual(secondary.maintenance_cost, Decimal('5000.00'))
    
    def test_complete_tax_management_workflow(self):
        """Test complete tax management workflow"""
        # Step 1: Create excisable commodity
        commodity_url = reverse('accounts:excisable_commodity_create')
        commodity_data = {
            'terms': 'Integration Test Commodity'
        }
        response = self.client.post(commodity_url, commodity_data)
        self.assertEqual(response.status_code, 302)
        
        # Step 2: Create octori tax rate
        octori_url = reverse('accounts:octori_create')
        octori_data = {
            'terms': 'Integration Test Tax',
            'value': '1.75'
        }
        response = self.client.post(octori_url, octori_data)
        self.assertEqual(response.status_code, 302)
        
        # Step 3: Create payment category
        payment_url = reverse('accounts:payment_against_create')
        payment_data = {
            'description': 'Integration Test Payment Category'
        }
        response = self.client.post(payment_url, payment_data)
        self.assertEqual(response.status_code, 302)
        
        # Step 4: Verify all items exist
        self.assertTrue(
            ExcisableCommodityMaster.objects.filter(
                terms='Integration Test Commodity'
            ).exists()
        )
        self.assertTrue(
            OctoriMaster.objects.filter(
                terms='Integration Test Tax'
            ).exists()
        )
        self.assertTrue(
            PaymentAgainst.objects.filter(
                description='Integration Test Payment Category'
            ).exists()
        )