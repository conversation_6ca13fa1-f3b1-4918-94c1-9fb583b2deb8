"""
Simple Process Forms for ASP.NET compatibility
Matches the original ItemProcess.aspx GridView functionality
"""

from django import forms
from ..models import Process


class ProcessInlineForm(forms.ModelForm):
    """Form for inline editing of processes - matches ASP.NET GridView behavior"""
    
    class Meta:
        model = Process
        fields = ['processname', 'symbol']
        
        widgets = {
            'processname': forms.TextInput(attrs={
                'class': 'sap-input w-full',
                'placeholder': 'Process Name',
                'required': True,
                'x-model': 'editingProcess.processname'
            }),
            'symbol': forms.TextInput(attrs={
                'class': 'sap-input w-full',
                'placeholder': 'Symbol',
                'required': True,
                'x-model': 'editingProcess.symbol'
            }),
        }

    def clean_processname(self):
        processname = self.cleaned_data.get('processname')
        if not processname or processname.strip() == '':
            raise forms.ValidationError("Process name is required.")
        return processname.strip()

    def clean_symbol(self):
        symbol = self.cleaned_data.get('symbol')
        if not symbol or symbol.strip() == '':
            raise forms.ValidationError("Symbol is required.")
        return symbol.strip()


class ProcessCreateForm(forms.ModelForm):
    """Form for creating new processes - footer template equivalent"""
    
    class Meta:
        model = Process
        fields = ['processname', 'symbol']
        
        widgets = {
            'processname': forms.TextInput(attrs={
                'class': 'sap-input w-full',
                'placeholder': 'Enter process name',
                'required': True,
                'id': 'new_processname'
            }),
            'symbol': forms.TextInput(attrs={
                'class': 'sap-input w-full',
                'placeholder': 'Enter symbol',
                'required': True,
                'id': 'new_symbol'
            }),
        }

    def clean_processname(self):
        processname = self.cleaned_data.get('processname')
        if not processname or processname.strip() == '':
            raise forms.ValidationError("Process name is required.")
        return processname.strip()

    def clean_symbol(self):
        symbol = self.cleaned_data.get('symbol')
        if not symbol or symbol.strip() == '':
            raise forms.ValidationError("Symbol is required.")
        return symbol.strip()