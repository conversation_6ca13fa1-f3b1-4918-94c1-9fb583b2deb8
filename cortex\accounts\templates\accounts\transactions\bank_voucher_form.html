<!-- accounts/templates/accounts/transactions/bank_voucher_form.html -->
<!-- Bank Voucher Create/Edit Form Template -->
<!-- Task Group 2: Banking & Cash Management - Bank Voucher Form (Task 2.8) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{% if object %}Edit Bank Voucher{% else %}New Bank Voucher{% endif %} - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-blue-600 to-sap-blue-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="building-2" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">
                        {% if object %}Edit Bank Voucher{% else %}New Bank Voucher{% endif %}
                    </h1>
                    <p class="text-sm text-sap-gray-600 mt-1">
                        {% if object %}Modify bank transaction details{% else %}Create a new bank receipt, payment, or transfer voucher{% endif %}
                    </p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:bank_voucher_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Bank Voucher Form -->
    <div class="max-w-5xl mx-auto">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="edit" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                    Bank Voucher Details
                </h3>
                <p class="text-sm text-sap-gray-600 mt-1">Enter the bank transaction information below</p>
            </div>
            
            <form method="post" id="bank-voucher-form" class="p-6" x-data="bankVoucherForm()">
                {% csrf_token %}
                
                <!-- Basic Information Section -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <!-- Voucher Number -->
                    <div>
                        <label for="{{ form.voucher_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                            Voucher Number *
                        </label>
                        {{ form.voucher_no|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                        {% if form.voucher_no.errors %}
                        <p class="text-red-600 text-xs mt-1">{{ form.voucher_no.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Voucher Date -->
                    <div>
                        <label for="{{ form.voucher_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                            Voucher Date *
                        </label>
                        {{ form.voucher_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                        {% if form.voucher_date.errors %}
                        <p class="text-red-600 text-xs mt-1">{{ form.voucher_date.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Voucher Type -->
                    <div>
                        <label for="{{ form.voucher_type.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                            Voucher Type *
                        </label>
                        {{ form.voucher_type|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                        {% if form.voucher_type.errors %}
                        <p class="text-red-600 text-xs mt-1">{{ form.voucher_type.errors.0 }}</p>
                        {% endif %}
                        <p class="text-xs text-sap-gray-500 mt-1">Receipt, Payment, or Transfer</p>
                    </div>
                </div>
                
                <!-- Bank and Account Information Section -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <!-- Bank -->
                    <div>
                        <label for="{{ form.bank.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                            Bank Account *
                        </label>
                        {{ form.bank|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                        {% if form.bank.errors %}
                        <p class="text-red-600 text-xs mt-1">{{ form.bank.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Account Head -->
                    <div>
                        <label for="{{ form.account_head.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                            Account Head *
                        </label>
                        {{ form.account_head|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                        {% if form.account_head.errors %}
                        <p class="text-red-600 text-xs mt-1">{{ form.account_head.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Cheque Information Section -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <!-- Cheque Number -->
                    <div>
                        <label for="{{ form.cheque_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                            Cheque Number
                        </label>
                        {{ form.cheque_no|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                        {% if form.cheque_no.errors %}
                        <p class="text-red-600 text-xs mt-1">{{ form.cheque_no.errors.0 }}</p>
                        {% endif %}
                        <p class="text-xs text-sap-gray-500 mt-1">Leave blank for online transfers</p>
                    </div>
                    
                    <!-- Cheque Date -->
                    <div>
                        <label for="{{ form.cheque_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                            Cheque Date
                        </label>
                        {{ form.cheque_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                        {% if form.cheque_date.errors %}
                        <p class="text-red-600 text-xs mt-1">{{ form.cheque_date.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Amount -->
                    <div>
                        <label for="{{ form.amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                            Amount (₹) *
                        </label>
                        {{ form.amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                        {% if form.amount.errors %}
                        <p class="text-red-600 text-xs mt-1">{{ form.amount.errors.0 }}</p>
                        {% endif %}
                        <p class="text-xs text-sap-gray-500 mt-1" x-text="formatAmount(amount)"></p>
                    </div>
                </div>
                
                <!-- Description and Reference Section -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <!-- Description -->
                    <div>
                        <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                            Description
                        </label>
                        {{ form.description|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                        {% if form.description.errors %}
                        <p class="text-red-600 text-xs mt-1">{{ form.description.errors.0 }}</p>
                        {% endif %}
                        <p class="text-xs text-sap-gray-500 mt-1">Brief description of the transaction</p>
                    </div>
                    
                    <!-- Reference Number -->
                    <div>
                        <label for="{{ form.reference_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                            Reference Number
                        </label>
                        {{ form.reference_no|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                        {% if form.reference_no.errors %}
                        <p class="text-red-600 text-xs mt-1">{{ form.reference_no.errors.0 }}</p>
                        {% endif %}
                        <p class="text-xs text-sap-gray-500 mt-1">Bank reference or transaction ID</p>
                    </div>
                </div>
                
                <!-- Additional Information Section -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <!-- Payment Mode -->
                    <div>
                        <label for="{{ form.payment_mode.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                            Payment Mode
                        </label>
                        {{ form.payment_mode|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                        {% if form.payment_mode.errors %}
                        <p class="text-red-600 text-xs mt-1">{{ form.payment_mode.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- TDS Applicable -->
                    <div>
                        <label for="{{ form.tds_applicable.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                            TDS Applicable
                        </label>
                        <div class="flex items-center mt-2">
                            {{ form.tds_applicable|add_class:"w-4 h-4 text-sap-blue-600 border-sap-gray-300 rounded focus:ring-sap-blue-500" }}
                            <label for="{{ form.tds_applicable.id_for_label }}" class="ml-2 text-sm text-sap-gray-700">
                                Apply TDS to this transaction
                            </label>
                        </div>
                        {% if form.tds_applicable.errors %}
                        <p class="text-red-600 text-xs mt-1">{{ form.tds_applicable.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Reconciliation Status -->
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                            Status Flags
                        </label>
                        <div class="space-y-2">
                            <div class="flex items-center">
                                {{ form.is_cleared|add_class:"w-4 h-4 text-sap-green-600 border-sap-gray-300 rounded focus:ring-sap-green-500" }}
                                <label for="{{ form.is_cleared.id_for_label }}" class="ml-2 text-sm text-sap-gray-700">
                                    Cleared by bank
                                </label>
                            </div>
                            <div class="flex items-center">
                                {{ form.is_reconciled|add_class:"w-4 h-4 text-sap-blue-600 border-sap-gray-300 rounded focus:ring-sap-blue-500" }}
                                <label for="{{ form.is_reconciled.id_for_label }}" class="ml-2 text-sm text-sap-gray-700">
                                    Reconciled
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="resetForm()" 
                                class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="rotate-ccw" class="w-4 h-4 inline mr-2"></i>
                            Reset
                        </button>
                        <button type="button" onclick="saveDraft()" 
                                class="bg-sap-yellow-600 hover:bg-sap-yellow-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="save" class="w-4 h-4 inline mr-2"></i>
                            Save Draft
                        </button>
                        <button type="button" onclick="getNextCheque()" 
                                class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="hash" class="w-4 h-4 inline mr-2"></i>
                            Get Next Cheque
                        </button>
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="{% url 'accounts:bank_voucher_list' %}" 
                           class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                            {% if object %}Update Voucher{% else %}Create Voucher{% endif %}
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Bank Voucher Guidelines -->
        <div class="mt-6 bg-sap-blue-50 border border-sap-blue-200 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i data-lucide="info" class="w-5 h-5 text-sap-blue-600"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-sap-blue-800">Bank Voucher Guidelines</h3>
                    <div class="mt-2 text-sm text-sap-blue-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <li><strong>Receipt:</strong> Money received into bank account (customer payments, interest, etc.)</li>
                            <li><strong>Payment:</strong> Money paid from bank account (supplier payments, expenses, etc.)</li>
                            <li><strong>Transfer:</strong> Money moved between different bank accounts</li>
                            <li>Always enter cheque number for cheque transactions</li>
                            <li>Use appropriate payment modes (Cheque, NEFT, RTGS, UPI, etc.)</li>
                            <li>Reconcile regularly with bank statements</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function bankVoucherForm() {
    return {
        amount: 0,
        
        formatAmount(amount) {
            if (!amount || amount == 0) return '';
            return `Amount in words: ${numberToWords(amount)} only`;
        }
    }
}

function numberToWords(amount) {
    // Simple number to words conversion for Indian currency
    if (amount == 0) return 'Zero';
    
    const ones = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
    const teens = ['Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
    const tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];
    
    let words = '';
    let crores = Math.floor(amount / ********);
    let lakhs = Math.floor((amount % ********) / 100000);
    let thousands = Math.floor((amount % 100000) / 1000);
    let hundreds = Math.floor((amount % 1000) / 100);
    let remainder = amount % 100;
    
    if (crores > 0) {
        words += convertTwoDigits(crores) + ' Crore ';
    }
    if (lakhs > 0) {
        words += convertTwoDigits(lakhs) + ' Lakh ';
    }
    if (thousands > 0) {
        words += convertTwoDigits(thousands) + ' Thousand ';
    }
    if (hundreds > 0) {
        words += ones[hundreds] + ' Hundred ';
    }
    if (remainder > 0) {
        words += convertTwoDigits(remainder);
    }
    
    return words.trim() + ' Rupees';
    
    function convertTwoDigits(num) {
        if (num < 10) {
            return ones[num];
        } else if (num < 20) {
            return teens[num - 10];
        } else {
            return tens[Math.floor(num / 10)] + (num % 10 > 0 ? ' ' + ones[num % 10] : '');
        }
    }
}

function resetForm() {
    if (confirm('Are you sure you want to reset the form? All unsaved changes will be lost.')) {
        document.getElementById('bank-voucher-form').reset();
    }
}

function saveDraft() {
    // Add hidden field to indicate draft save
    const form = document.getElementById('bank-voucher-form');
    const draftInput = document.createElement('input');
    draftInput.type = 'hidden';
    draftInput.name = 'save_draft';
    draftInput.value = '1';
    form.appendChild(draftInput);
    form.submit();
}

function getNextCheque() {
    const bankSelect = document.getElementById('{{ form.bank.id_for_label }}');
    if (!bankSelect.value) {
        alert('Please select a bank first');
        return;
    }
    
    fetch(`/accounts/ajax/next-available-cheque/?bank_id=${bankSelect.value}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('{{ form.cheque_no.id_for_label }}').value = data.cheque_no;
            } else {
                alert(data.message || 'Error getting next cheque number');
            }
        })
        .catch(error => {
            alert('Error fetching next cheque number');
        });
}

// Auto-generate voucher number if creating new
document.addEventListener('DOMContentLoaded', function() {
    const voucherNoInput = document.getElementById('{{ form.voucher_no.id_for_label }}');
    if (voucherNoInput && !voucherNoInput.value) {
        // Generate voucher number based on current date and type
        const today = new Date();
        const year = today.getFullYear().toString().substr(-2);
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const voucherType = document.getElementById('{{ form.voucher_type.id_for_label }}').value;
        let prefix = 'BV';
        if (voucherType === 'receipt') prefix = 'BR';
        else if (voucherType === 'payment') prefix = 'BP';
        else if (voucherType === 'transfer') prefix = 'BT';
        
        // This would typically come from the server to ensure uniqueness
        const sequence = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        voucherNoInput.value = `${prefix}${year}${month}${sequence}`;
    }
    
    lucide.createIcons();
});
</script>
{% endblock %}