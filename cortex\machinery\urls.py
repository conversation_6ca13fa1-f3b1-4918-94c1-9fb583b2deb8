from django.urls import path
from . import views

app_name = 'machinery'

urlpatterns = [
    # Main machinery dashboard
    path('', views.MachineListView.as_view(), name='machine_list'),
    
    # Machine management URLs (replicating ASP.NET structure)
    path('machines/', views.MachineListView.as_view(), name='machine_list'),
    path('machines/select-item/', views.MachineItemSelectionView.as_view(), name='machine_item_selection'),
    path('machines/create/<int:item_id>/', views.MachineCreateDetailView.as_view(), name='machine_create_detail'),
    path('machines/create/', views.MachineCreateView.as_view(), name='machine_create'),
    path('machines/<int:pk>/', views.MachineDetailView.as_view(), name='machine_detail'),
    path('machines/<int:pk>/edit/', views.MachineUpdateView.as_view(), name='machine_edit'),
    path('machines/<int:machine_id>/download/', views.download_machine_file, name='machine_download'),
    
    # PMBM (Preventive/Breakdown Maintenance) URLs
    path('pmbm/', views.PMBMListView.as_view(), name='pmbm_list'),
    path('pmbm/<int:machine_id>/<int:item_id>/', views.PMBMCreateDetailView.as_view(), name='pmbm_create'),
    
    # Preventive Maintenance URLs
    path('maintenance/', views.PreventiveMaintenanceListView.as_view(), name='maintenance_list'),
    path('maintenance/create/', views.PreventiveMaintenanceCreateView.as_view(), name='maintenance_create'),
    path('maintenance/<int:pk>/', views.PreventiveMaintenanceUpdateView.as_view(), name='maintenance_edit'),
    
    # Job Schedule URLs
    path('schedules/', views.JobScheduleListView.as_view(), name='schedule_list'),
    path('schedules/create/', views.JobScheduleCreateView.as_view(), name='schedule_create'),
    path('schedules/create/<str:wono>/', views.JobScheduleCreateDetailView.as_view(), name='schedule_create_detail'),
    path('schedules/items/<str:wono>/<int:item_id>/', views.JobScheduleItemsView.as_view(), name='schedule_items'),
    path('schedules/split/<str:wono>/<int:bom_id>/', views.JobScheduleSplitItemsView.as_view(), name='schedule_split_items'),
    path('schedules/output/', views.JobScheduleOutputView.as_view(), name='schedule_output'),
    path('schedules/output/<str:wono>/<int:item_id>/', views.JobScheduleOutputDetailView.as_view(), name='schedule_output_detail'),
    path('schedules/<int:pk>/', views.JobScheduleDetailView.as_view(), name='schedule_detail'),
    path('schedules/<int:pk>/edit/', views.JobScheduleUpdateView.as_view(), name='schedule_edit'),
    
    # HTMX URLs for dynamic content
    path('ajax/subcategories/', views.get_subcategories, name='get_subcategories'),
    path('ajax/items/', views.get_available_items, name='get_available_items'),
    path('ajax/search-machines/', views.search_machines, name='search_machines'),
    path('ajax/machine-details/<int:machine_id>/', views.get_machine_details, name='get_machine_details'),
    path('ajax/insurance-fields/', views.check_insurance_fields, name='check_insurance_fields'),
    path('ajax/work-orders/', views.search_work_orders, name='search_work_orders'),
    
    # HTMX URLs for spare parts and processes management
    path('ajax/add-spare-parts/', views.add_spare_parts, name='add_spare_parts'),
    path('ajax/remove-spare-part/<int:spare_id>/', views.remove_spare_part, name='remove_spare_part'),
    path('ajax/add-processes/', views.add_processes, name='add_processes'),
    path('ajax/remove-process/<int:process_id>/', views.remove_process, name='remove_process'),
    
    # HTMX URLs for job scheduling
    path('ajax/add-schedule-detail/', views.add_schedule_detail, name='add_schedule_detail'),
    path('ajax/remove-schedule-detail/<int:detail_id>/', views.remove_schedule_detail, name='remove_schedule_detail'),
    path('ajax/submit-schedule-items/', views.submit_schedule_items, name='submit_schedule_items'),
    path('ajax/machine-processes/', views.get_machine_processes, name='get_machine_processes'),
    path('ajax/last-machine-schedule/', views.get_last_machine_schedule, name='get_last_machine_schedule'),
    path('ajax/record-job-output/', views.record_job_output, name='record_job_output'),
    
    # Dashboard and Reports URLs
    path('dashboard/', views.MachineryDashboardView.as_view(), name='dashboard'),
    path('reports/', views.MachineryReportsView.as_view(), name='reports_list'),
    path('reports/utilization/', views.MachineUtilizationReportView.as_view(), name='utilization_report'),
    path('reports/maintenance-schedule/', views.MaintenanceScheduleReportView.as_view(), name='maintenance_schedule_report'),
    path('reports/cost-analysis/', views.MachineCostAnalysisReportView.as_view(), name='cost_analysis_report'),
    path('insurance-tracking/', views.InsuranceExpiryTrackingView.as_view(), name='insurance_tracking'),
]