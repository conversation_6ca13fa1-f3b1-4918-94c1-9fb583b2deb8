﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="Challan_Clear" targetNamespace="http://tempuri.org/Challan_Clear.xsd" xmlns:mstns="http://tempuri.org/Challan_Clear.xsd" xmlns="http://tempuri.org/Challan_Clear.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="Challan_Clear" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="Challan_Clear" msprop:Generator_DataSetName="Challan_Clear">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TablePropName="DataTable1" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" msprop:Generator_UserColumnName="Id" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInTable="IdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="PRNo" msprop:Generator_UserColumnName="PRNo" msprop:Generator_ColumnPropNameInRow="PRNo" msprop:Generator_ColumnVarNameInTable="columnPRNo" msprop:Generator_ColumnPropNameInTable="PRNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="WONo" msprop:Generator_UserColumnName="WONo" msprop:Generator_ColumnPropNameInRow="WONo" msprop:Generator_ColumnVarNameInTable="columnWONo" msprop:Generator_ColumnPropNameInTable="WONoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="SCNo" msprop:Generator_UserColumnName="SCNo" msprop:Generator_ColumnPropNameInRow="SCNo" msprop:Generator_ColumnVarNameInTable="columnSCNo" msprop:Generator_ColumnPropNameInTable="SCNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PRDate" msdata:Caption="SCDate" msprop:Generator_UserColumnName="PRDate" msprop:Generator_ColumnPropNameInRow="PRDate" msprop:Generator_ColumnVarNameInTable="columnPRDate" msprop:Generator_ColumnPropNameInTable="PRDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ItemCode" msprop:Generator_UserColumnName="ItemCode" msprop:Generator_ColumnPropNameInRow="ItemCode" msprop:Generator_ColumnVarNameInTable="columnItemCode" msprop:Generator_ColumnPropNameInTable="ItemCodeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Descr" msdata:Caption="ManfDesc" msprop:Generator_UserColumnName="Descr" msprop:Generator_ColumnPropNameInRow="Descr" msprop:Generator_ColumnVarNameInTable="columnDescr" msprop:Generator_ColumnPropNameInTable="DescrColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Symbol" msprop:Generator_UserColumnName="Symbol" msprop:Generator_ColumnPropNameInRow="Symbol" msprop:Generator_ColumnVarNameInTable="columnSymbol" msprop:Generator_ColumnPropNameInTable="SymbolColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ClearedQty" msdata:Caption="PRQty" msprop:Generator_UserColumnName="ClearedQty" msprop:Generator_ColumnPropNameInRow="ClearedQty" msprop:Generator_ColumnVarNameInTable="columnClearedQty" msprop:Generator_ColumnPropNameInTable="ClearedQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ChallanQty" msprop:Generator_UserColumnName="ChallanQty" msprop:Generator_ColumnPropNameInRow="ChallanQty" msprop:Generator_ColumnVarNameInTable="columnChallanQty" msprop:Generator_ColumnPropNameInTable="ChallanQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="SysTime" msprop:Generator_UserColumnName="SysTime" msprop:Generator_ColumnPropNameInRow="SysTime" msprop:Generator_ColumnVarNameInTable="columnSysTime" msprop:Generator_ColumnPropNameInTable="SysTimeColumn" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>