<!-- accounts/templates/accounts/transactions/creditors_debitors_detail.html -->
<!-- Creditors & Debitors Detail View Template -->
<!-- Replaces ASP.NET functionality for detailed creditor/debitor record view -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{{ record.party_name }} - Creditor/Debitor Details{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-purple-600 to-sap-purple-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="user-check" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">{{ record.party_name }}</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Creditor/Debitor Record Details</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:creditors_debitors_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
                <a href="{% url 'accounts:creditors_debitors_edit' record.id %}" 
                   class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="edit" class="w-4 h-4 inline mr-2"></i>
                    Edit Record
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Record Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="user" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Party Type</p>
                    <p class="text-lg font-bold text-sap-gray-900">{{ record.party_type|title|default:"N/A" }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="credit-card" class="w-6 h-6 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Amount</p>
                    <p class="text-lg font-bold text-sap-gray-900">₹{{ record.amount|floatformat:2|default:"0.00" }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 {% if record.amount_type == 'debit' %}bg-sap-red-100{% else %}bg-sap-green-100{% endif %} rounded-lg flex items-center justify-center">
                    <i data-lucide="{% if record.amount_type == 'debit' %}minus-circle{% else %}plus-circle{% endif %}" class="w-6 h-6 {% if record.amount_type == 'debit' %}text-sap-red-600{% else %}text-sap-green-600{% endif %}"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Type</p>
                    <p class="text-lg font-bold {% if record.amount_type == 'debit' %}text-sap-red-600{% else %}text-sap-green-600{% endif %}">{{ record.amount_type|title|default:"N/A" }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="calendar" class="w-6 h-6 text-sap-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Created Date</p>
                    <p class="text-lg font-bold text-sap-gray-900">{{ record.created_date|date:"M d, Y"|default:"N/A" }}</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Detailed Information -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm mb-6">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <h3 class="text-lg font-medium text-sap-gray-800">Record Details</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Basic Information -->
                <div class="space-y-4">
                    <h4 class="text-sm font-medium text-sap-gray-700 uppercase tracking-wider">Basic Information</h4>
                    
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-500 mb-1">Party Name</label>
                        <p class="text-base text-sap-gray-900">{{ record.party_name|default:"N/A" }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-500 mb-1">Reference Number</label>
                        <p class="text-base text-sap-gray-900">{{ record.reference_no|default:"N/A" }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-500 mb-1">Party Type</label>
                        <p class="text-base text-sap-gray-900">{{ record.party_type|title|default:"N/A" }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-500 mb-1">Amount Type</label>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if record.amount_type == 'debit' %}bg-sap-red-100 text-sap-red-800{% else %}bg-sap-green-100 text-sap-green-800{% endif %}">
                            {{ record.amount_type|title|default:"N/A" }}
                        </span>
                    </div>
                </div>
                
                <!-- Financial Information -->
                <div class="space-y-4">
                    <h4 class="text-sm font-medium text-sap-gray-700 uppercase tracking-wider">Financial Information</h4>
                    
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-500 mb-1">Amount</label>
                        <p class="text-xl font-bold text-sap-gray-900">₹{{ record.amount|floatformat:2|default:"0.00" }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-500 mb-1">Created Date</label>
                        <p class="text-base text-sap-gray-900">{{ record.created_date|date:"F d, Y, g:i A"|default:"N/A" }}</p>
                    </div>
                    
                    {% if record.updated_date %}
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-500 mb-1">Last Updated</label>
                        <p class="text-base text-sap-gray-900">{{ record.updated_date|date:"F d, Y, g:i A" }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
            
            {% if record.particulars %}
            <div class="mt-6 pt-6 border-t border-sap-gray-200">
                <h4 class="text-sm font-medium text-sap-gray-700 uppercase tracking-wider mb-3">Particulars</h4>
                <div class="bg-sap-gray-50 rounded-lg p-4">
                    <p class="text-base text-sap-gray-900">{{ record.particulars }}</p>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Action Section -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <h3 class="text-lg font-medium text-sap-gray-800">Actions</h3>
        </div>
        <div class="p-6">
            <div class="flex flex-wrap gap-3">
                <a href="{% url 'accounts:creditors_debitors_edit' record.id %}" 
                   class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="edit" class="w-4 h-4 inline mr-2"></i>
                    Edit Record
                </a>
                
                <button type="button" onclick="printRecord()" 
                        class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="printer" class="w-4 h-4 inline mr-2"></i>
                    Print Record
                </button>
                
                <button type="button" onclick="exportRecord()" 
                        class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                    Export Data
                </button>
                
                <button type="button" onclick="deleteRecord()" 
                        class="bg-sap-red-600 hover:bg-sap-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="trash-2" class="w-4 h-4 inline mr-2"></i>
                    Delete Record
                </button>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Actions -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    window.printRecord = function() {
        window.print();
    };
    
    window.exportRecord = function() {
        // Implementation would export record data
        alert('Export functionality would be implemented here');
    };
    
    window.deleteRecord = function() {
        if (confirm('Are you sure you want to delete this record? This action cannot be undone.')) {
            window.location.href = '{% url "accounts:creditors_debitors_delete" record.id %}';
        }
    };
    
    // Initialize Lucide icons
    lucide.createIcons();
});
</script>
{% endblock %}