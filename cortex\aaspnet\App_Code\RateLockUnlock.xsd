﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="RateLockUnlock" targetNamespace="http://tempuri.org/RateLockUnlock.xsd" xmlns:mstns="http://tempuri.org/RateLockUnlock.xsd" xmlns="http://tempuri.org/RateLockUnlock.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="RateLockUnlock" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="RateLockUnlock" msprop:Generator_DataSetName="RateLockUnlock">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" msprop:Generator_UserColumnName="Id" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInTable="IdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="SysDate" msprop:Generator_UserColumnName="SysDate" msprop:Generator_ColumnPropNameInRow="SysDate" msprop:Generator_ColumnVarNameInTable="columnSysDate" msprop:Generator_ColumnPropNameInTable="SysDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="SysTime" msprop:Generator_UserColumnName="SysTime" msprop:Generator_ColumnPropNameInRow="SysTime" msprop:Generator_ColumnVarNameInTable="columnSysTime" msprop:Generator_ColumnPropNameInTable="SysTimeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="SessionId" msprop:Generator_UserColumnName="SessionId" msprop:Generator_ColumnPropNameInRow="SessionId" msprop:Generator_ColumnVarNameInTable="columnSessionId" msprop:Generator_ColumnPropNameInTable="SessionIdColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Type" msprop:Generator_UserColumnName="Type" msprop:Generator_ColumnPropNameInRow="Type" msprop:Generator_ColumnVarNameInTable="columnType" msprop:Generator_ColumnPropNameInTable="TypeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="LockedbyTranaction" msprop:Generator_UserColumnName="LockedbyTranaction" msprop:Generator_ColumnPropNameInRow="LockedbyTranaction" msprop:Generator_ColumnVarNameInTable="columnLockedbyTranaction" msprop:Generator_ColumnPropNameInTable="LockedbyTranactionColumn" type="xs:string" minOccurs="0" />
              <xs:element name="LockDate" msprop:Generator_UserColumnName="LockDate" msprop:Generator_ColumnPropNameInRow="LockDate" msprop:Generator_ColumnVarNameInTable="columnLockDate" msprop:Generator_ColumnPropNameInTable="LockDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="LockTime" msprop:Generator_UserColumnName="LockTime" msprop:Generator_ColumnPropNameInRow="LockTime" msprop:Generator_ColumnVarNameInTable="columnLockTime" msprop:Generator_ColumnPropNameInTable="LockTimeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ItemCode" msprop:Generator_UserColumnName="ItemCode" msprop:Generator_ColumnPropNameInRow="ItemCode" msprop:Generator_ColumnVarNameInTable="columnItemCode" msprop:Generator_ColumnPropNameInTable="ItemCodeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ManfDesc" msprop:Generator_UserColumnName="ManfDesc" msprop:Generator_ColumnPropNameInRow="ManfDesc" msprop:Generator_ColumnVarNameInTable="columnManfDesc" msprop:Generator_ColumnPropNameInTable="ManfDescColumn" type="xs:string" minOccurs="0" />
              <xs:element name="UomBasic" msprop:Generator_UserColumnName="UomBasic" msprop:Generator_ColumnPropNameInRow="UomBasic" msprop:Generator_ColumnVarNameInTable="columnUomBasic" msprop:Generator_ColumnPropNameInTable="UomBasicColumn" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>