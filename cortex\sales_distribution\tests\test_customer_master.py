"""
Tests for Customer Master functionality.
Based on ASP.NET CustomerMaster_New.aspx and CustomerMaster_Edit.aspx.
Tests form validation, customer creation, and HTMX endpoints.
"""

from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
import json

from sys_admin.models import Country, State, City, Company, FinancialYear
from ..models import Customer
from ..forms.customer_forms import CustomerForm


class CustomerMasterBaseTestCase(TestCase):
    """Base test case with common setup for customer tests"""
    
    def setUp(self):
        """Set up test data"""
        self.client = Client()
        
        # Create test user
        User = get_user_model()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            email='<EMAIL>'
        )
        
        # Create test geographic data
        self.country = Country.objects.create(
            countryname='India',
            currency='INR',
            symbol='₹',
            keyshortcut='IN'
        )
        
        self.state = State.objects.create(
            statename='Maharashtra',
            cid=self.country
        )
        
        self.city = City.objects.create(
            cityname='Mumbai',
            sid=self.state
        )
        
        # Create test company and financial year
        self.company = Company.objects.create(
            sysdate='2023-12-01',
            systime='10:00:00',
            companyname='Test Company',
            regdaddress='Test Address',
            regdcity=self.city,
            regdstate=1,
            regdcountry=1,
            plantaddress='Plant Address',
            plantcity=self.city,
            plantstate=1,
            plantcountry=1
        )
        
        self.financial_year = FinancialYear.objects.create(
            sysdate='2023-12-01',
            systime='10:00:00',
            sessionid='testuser',
            compid=self.company.compid,
            finyear='2023-24'
        )
        
        # Login user
        self.client.login(username='testuser', password='testpass123')
        
        # Set session data like ASP.NET
        session = self.client.session
        session['company'] = self.company.compid
        session['financial_year'] = self.financial_year.finyearid
        session.save()


class CustomerFormTestCase(CustomerMasterBaseTestCase):
    """Test CustomerForm validation and functionality"""
    
    def test_form_valid_data(self):
        """Test form with valid data"""
        form_data = {
            'customer_name': 'Test Customer Pvt Ltd',
            'contact_person': 'John Doe',
            'email': '<EMAIL>',
            'contact_no': '+91-9876543210',
            
            # Registered office
            'registered_address': '123 Business Park, Andheri',
            'registered_country': self.country.cid,
            'registered_state': self.state.sid,
            'registered_city': self.city.cityid,
            'registered_pin': '400069',
            'registered_contact_no': '+91-9876543211',
            'regdfaxno': '+91-22-26854321',
            
            # Works address
            'works_address': '456 Industrial Estate, Powai',
            'works_country': self.country.cid,
            'works_state': self.state.sid,
            'works_city': self.city.cityid,
            'works_pin': '400076',
            'works_contact_no': '+91-9876543212',
            'workfaxno': '+91-22-26854322',
            
            # Material delivery address
            'material_address': '789 Logistics Hub, Navi Mumbai',
            'material_country': self.country.cid,
            'material_state': self.state.sid,
            'material_city': self.city.cityid,
            'material_pin': '400701',
            'material_contact_no': '+91-9876543213',
            'materialdelfaxno': '+91-22-26854323',
            
            # Tax information
            'juridictioncode': 'MUMBAI-I',
            'eccno': '**********MN001',
            'range': 'CENTRAL-1',
            'commissionurate': 'MUMBAI-I',
            'divn': 'DIV-1',
            'panno': '**********',
            'tinvatno': '27**********1Z5',
            'tincstno': '27**********1Z6',
            'tdscode': 'TDS001',
            
            'remarks': 'Test customer created via Django test'
        }
        
        form = CustomerForm(data=form_data)
        self.assertTrue(form.is_valid(), f"Form errors: {form.errors}")
    
    def test_form_required_fields(self):
        """Test form validation for required fields"""
        form_data = {
            'customer_name': '',  # Required field left empty
        }
        
        form = CustomerForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('customer_name', form.errors)
    
    def test_form_email_validation(self):
        """Test email field validation"""
        # Test invalid email
        form_data = {
            'customer_name': 'Test Customer',
            'email': 'invalid-email'
        }
        
        form = CustomerForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('email', form.errors)
        
        # Test valid email
        form_data['email'] = '<EMAIL>'
        form = CustomerForm(data=form_data)
        # Still invalid due to other required fields, but email should be valid
        self.assertNotIn('email', form.errors.get('email', []))
    
    def test_form_pan_validation(self):
        """Test PAN number validation"""
        form_data = {
            'customer_name': 'Test Customer',
            'panno': 'INVALID'  # Invalid PAN format
        }
        
        form = CustomerForm(data=form_data)
        self.assertFalse(form.is_valid())
        
        # Test valid PAN
        form_data['panno'] = '**********'
        form = CustomerForm(data=form_data)
        # Check that PAN field specifically doesn't have errors
        if 'panno' in form.errors:
            self.assertNotIn('PAN number must be in format **********', str(form.errors['panno']))
    
    def test_form_pin_validation(self):
        """Test PIN code validation"""
        form_data = {
            'customer_name': 'Test Customer',
            'registered_pin': '12345'  # Invalid 5-digit PIN
        }
        
        form = CustomerForm(data=form_data)
        self.assertFalse(form.is_valid())
        
        # Test valid PIN
        form_data['registered_pin'] = '400069'
        form = CustomerForm(data=form_data)
        # Check PIN field validation specifically
        if 'registered_pin' in form.errors:
            self.assertNotIn('Enter a valid 6-digit PIN code', str(form.errors['registered_pin']))


class CustomerViewTestCase(CustomerMasterBaseTestCase):
    """Test Customer views and functionality"""
    
    def test_customer_list_view(self):
        """Test customer list view"""
        response = self.client.get(reverse('sales_distribution:customer_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Customer')
    
    def test_customer_create_view_get(self):
        """Test customer create view GET request"""
        response = self.client.get(reverse('sales_distribution:customer_new'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Customer Master - New')
        self.assertContains(response, 'Customer\'s Name')
    
    def test_customer_create_view_post_valid(self):
        """Test customer creation with valid data"""
        customer_data = {
            'customer_name': 'Test Customer Pvt Ltd',
            'contact_person': 'John Doe',
            'email': '<EMAIL>',
            'contact_no': '+91-9876543210',
            
            # Registered office
            'registered_address': '123 Business Park, Andheri',
            'registered_country': self.country.cid,
            'registered_state': self.state.sid,
            'registered_city': self.city.cityid,
            'registered_pin': '400069',
            'registered_contact_no': '+91-9876543211',
            'regdfaxno': '+91-22-26854321',
            
            # Works address
            'works_address': '456 Industrial Estate, Powai',
            'works_country': self.country.cid,
            'works_state': self.state.sid,
            'works_city': self.city.cityid,
            'works_pin': '400076',
            'works_contact_no': '+91-9876543212',
            'workfaxno': '+91-22-26854322',
            
            # Material delivery address
            'material_address': '789 Logistics Hub, Navi Mumbai',
            'material_country': self.country.cid,
            'material_state': self.state.sid,
            'material_city': self.city.cityid,
            'material_pin': '400701',
            'material_contact_no': '+91-9876543213',
            'materialdelfaxno': '+91-22-26854323',
            
            # Tax information
            'juridictioncode': 'MUMBAI-I',
            'eccno': '**********MN001',
            'range': 'CENTRAL-1',
            'commissionurate': 'MUMBAI-I',
            'divn': 'DIV-1',
            'panno': '**********',
            'tinvatno': '27**********1Z5',
            'tincstno': '27**********1Z6',
            'tdscode': 'TDS001',
            
            'remarks': 'Test customer created via Django test'
        }
        
        response = self.client.post(
            reverse('sales_distribution:customer_new'),
            data=customer_data
        )
        
        # Should redirect on success
        self.assertEqual(response.status_code, 302)
        
        # Check customer was created
        self.assertTrue(Customer.objects.filter(customer_name='TEST CUSTOMER PVT LTD').exists())
        
        # Check customer ID generation
        customer = Customer.objects.get(customer_name='TEST CUSTOMER PVT LTD')
        self.assertTrue(customer.customerid.startswith('TES'))  # First 3 chars from name
        self.assertEqual(len(customer.customerid), 6)  # Should be TES001
    
    def test_customer_create_view_post_invalid(self):
        """Test customer creation with invalid data"""
        customer_data = {
            'customer_name': '',  # Missing required field
            'email': 'invalid-email'
        }
        
        response = self.client.post(
            reverse('sales_distribution:customer_new'),
            data=customer_data
        )
        
        # Should return form with errors
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'error')  # Form should show errors
        
        # No customer should be created
        self.assertEqual(Customer.objects.count(), 0)
    
    def test_customer_id_generation(self):
        """Test customer ID generation logic (matching ASP.NET getCustChar)"""
        test_cases = [
            ('Tata Motors Limited', 'TAT'),
            ('Mahindra & Mahindra', 'MAH'),
            ('Reliance Industries', 'REL'),
            ('ABC', 'ABC'),
            ('A', 'AXX'),  # Should pad with X
            ('The Big Company', 'BIG'),  # Should skip 'THE'
        ]
        
        for customer_name, expected_prefix in test_cases:
            customer_data = {
                'customer_name': customer_name,
                'contact_person': 'Test Person',
                'email': '<EMAIL>',
                'contact_no': '+91-9876543210',
                'registered_address': 'Test Address',
                'registered_country': self.country.id,
                'registered_state': self.state.id,
                'registered_city': self.city.id,
                'registered_pin': '400069',
                'registered_contact_no': '+91-9876543211',
                'regdfaxno': '+91-22-26854321',
                'works_address': 'Test Address',
                'works_country': self.country.id,
                'works_state': self.state.id,
                'works_city': self.city.id,
                'works_pin': '400076',
                'works_contact_no': '+91-9876543212',
                'workfaxno': '+91-22-26854322',
                'material_address': 'Test Address',
                'material_country': self.country.id,
                'material_state': self.state.id,
                'material_city': self.city.id,
                'material_pin': '400701',
                'material_contact_no': '+91-9876543213',
                'materialdelfaxno': '+91-22-26854323',
                'juridictioncode': 'TEST',
                'eccno': 'TEST001',
                'range': 'TEST',
                'commissionurate': 'TEST',
                'divn': 'TEST',
                'panno': '**********',
                'tinvatno': '27**********1Z5',
                'tincstno': '27**********1Z6',
                'tdscode': 'TDS001',
            }
            
            response = self.client.post(
                reverse('sales_distribution:customer_new'),
                data=customer_data
            )
            
            if response.status_code == 302:  # Success
                customer = Customer.objects.get(customer_name=customer_name.upper())
                self.assertTrue(customer.customerid.startswith(expected_prefix))
                
                # Clean up for next test
                customer.delete()


class CustomerAjaxTestCase(CustomerMasterBaseTestCase):
    """Test AJAX endpoints for cascading dropdowns"""
    
    def test_ajax_states_endpoint(self):
        """Test states AJAX endpoint"""
        response = self.client.get(
            reverse('sales_distribution:ajax_states'),
            {'country_id': self.country.cid}
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.state.statename)
        self.assertContains(response, 'Select State')
    
    def test_ajax_cities_endpoint(self):
        """Test cities AJAX endpoint"""
        response = self.client.get(
            reverse('sales_distribution:ajax_cities'),
            {'state_id': self.state.sid}
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.city.cityname)
        self.assertContains(response, 'Select City')
    
    def test_ajax_customer_search(self):
        """Test customer search AJAX endpoint"""
        # Create test customer
        customer = Customer.objects.create(
            customer_name='Test Customer',
            customerid='TES001',
            sysdate='2023-12-01',
            systime='10:00:00',
            sessionid='testuser',
            compid=self.company,
            finyearid=self.financial_year
        )
        
        response = self.client.get(
            reverse('sales_distribution:customer_search_ajax'),
            {'q': 'Test'}
        )
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.content)
        self.assertIn('customers', data)
        self.assertEqual(len(data['customers']), 1)
        self.assertEqual(data['customers'][0]['customer_name'], 'Test Customer')


class CustomerModelTestCase(CustomerMasterBaseTestCase):
    """Test Customer model functionality"""
    
    def test_customer_creation(self):
        """Test customer model creation"""
        customer = Customer.objects.create(
            customer_name='Test Customer Ltd',
            customerid='TES001',
            sysdate='2023-12-01',
            systime='10:00:00',
            sessionid='testuser',
            compid=self.company,
            finyearid=self.financial_year,
            registered_country=self.country,
            registered_state=self.state,
            registered_city=self.city
        )
        
        self.assertEqual(str(customer), 'Test Customer Ltd')
        self.assertEqual(customer.customerid, 'TES001')
        self.assertEqual(customer.compid, self.company)
    
    def test_customer_str_representation(self):
        """Test customer string representation"""
        # Test with customer name
        customer_with_name = Customer.objects.create(
            customer_name='Test Customer',
            customerid='TES001',
            sysdate='2023-12-01',
            systime='10:00:00',
            sessionid='testuser',
            compid=self.company
        )
        self.assertEqual(str(customer_with_name), 'Test Customer')
        
        # Test with only customer ID
        customer_with_id = Customer.objects.create(
            customer_name='',
            customerid='TES002',
            sysdate='2023-12-01',
            systime='10:00:00',
            sessionid='testuser',
            compid=self.company
        )
        self.assertEqual(str(customer_with_id), 'Customer TES002')
        
        # Test with neither name nor ID
        customer_minimal = Customer.objects.create(
            customer_name='',
            customerid='',
            sysdate='2023-12-01',
            systime='10:00:00',
            sessionid='testuser',
            compid=self.company
        )
        self.assertTrue(str(customer_minimal).startswith('Customer'))


class CustomerHTMXTestCase(CustomerMasterBaseTestCase):
    """Test HTMX functionality for customer forms"""
    
    def test_htmx_form_submission(self):
        """Test HTMX form submission"""
        customer_data = {
            'customer_name': 'HTMX Test Customer',
            'contact_person': 'John Doe',
            'email': '<EMAIL>',
            'contact_no': '+91-9876543210',
            'registered_address': 'Test Address',
            'registered_country': self.country.cid,
            'registered_state': self.state.sid,
            'registered_city': self.city.cityid,
            'registered_pin': '400069',
            'registered_contact_no': '+91-9876543211',
            'regdfaxno': '+91-22-26854321',
            'works_address': 'Test Address',
            'works_country': self.country.cid,
            'works_state': self.state.sid,
            'works_city': self.city.cityid,
            'works_pin': '400076',
            'works_contact_no': '+91-9876543212',
            'workfaxno': '+91-22-26854322',
            'material_address': 'Test Address',
            'material_country': self.country.cid,
            'material_state': self.state.sid,
            'material_city': self.city.cityid,
            'material_pin': '400701',
            'material_contact_no': '+91-9876543213',
            'materialdelfaxno': '+91-22-26854323',
            'juridictioncode': 'TEST',
            'eccno': 'TEST001',
            'range': 'TEST',
            'commissionurate': 'TEST',
            'divn': 'TEST',
            'panno': '**********',
            'tinvatno': '27**********1Z5',
            'tincstno': '27**********1Z6',
            'tdscode': 'TDS001',
        }
        
        response = self.client.post(
            reverse('sales_distribution:customer_new'),
            data=customer_data,
            HTTP_HX_REQUEST='true'
        )
        
        # HTMX requests should get redirect header
        if response.status_code == 200 and 'HX-Redirect' in response:
            self.assertIn('HX-Redirect', response)
        elif response.status_code == 302:
            # Standard redirect is also acceptable
            pass
        else:
            self.fail(f"Unexpected response: {response.status_code}")


if __name__ == '__main__':
    import unittest
    unittest.main()