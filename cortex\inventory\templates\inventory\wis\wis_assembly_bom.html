{% extends "core/base.html" %}
{% load static %}

{% block title %}WIS Dry Run of Assembly for Work Order No.: {{ work_order }} - Inventory Management{% endblock %}

{% block content %}
<div class="sap-page">
    <!-- Page Header -->
    <div class="sap-page-header">
        <div class="sap-page-header-content">
            <div class="sap-page-title">
                <h1>WIS Dry Run of Assembly for Work Order No.: {{ work_order }}</h1>
                <p>Material and assembly requirement analysis</p>
            </div>
            <div class="sap-page-actions">
                <label class="sap-checkbox">
                    <input type="checkbox" id="expand_tree" checked onchange="toggleTreeExpansion()">
                    <span class="sap-checkbox-checkmark"></span>
                    Expand Tree
                </label>
                <button class="sap-button sap-button--emphasized">Dry Run of Material</button>
                <button class="sap-button sap-button--reject">Cancel</button>
            </div>
        </div>
    </div>

    <!-- Assembly BOM Tree -->
    <div class="sap-panel">
        <div class="sap-panel-header">
            <h3 class="sap-panel-title">Bill of Materials - Assembly Hierarchy</h3>
        </div>
        <div class="sap-panel-content">
            <div class="sap-table-container">
                <table class="sap-table sap-table--tree">
                    <thead>
                        <tr>
                            <th class="sap-table-header">Item Code</th>
                            <th class="sap-table-header">Description</th>
                            <th class="sap-table-header">UOM</th>
                            <th class="sap-table-header">Unit Qty</th>
                            <th class="sap-table-header">BOM Qty</th>
                            <th class="sap-table-header">Stock Qty</th>
                            <th class="sap-table-header">Tot. WIS Qty</th>
                            <th class="sap-table-header">Dry Run Qty</th>
                            <th class="sap-table-header">Balance BOM Qty</th>
                            <th class="sap-table-header">After Stock Qty</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if bom_items %}
                            {% for item in bom_items %}
                            <tr class="sap-table-row sap-table-row--level-{{ item.level|default:0 }}">
                                <td class="sap-table-cell sap-table-cell--tree">
                                    <div class="sap-tree-node" style="margin-left: {{ item.level|default:0|add:0 }}rem;">
                                        {% if item.has_children %}
                                            <button class="sap-tree-toggle" onclick="toggleNode(this)">
                                                <i class="sap-icon sap-icon--expand"></i>
                                            </button>
                                        {% endif %}
                                        <span class="sap-tree-text">{{ item.item_code }}</span>
                                    </div>
                                </td>
                                <td class="sap-table-cell">{{ item.description }}</td>
                                <td class="sap-table-cell">{{ item.uom }}</td>
                                <td class="sap-table-cell text-right">{{ item.unit_qty|floatformat:0 }}</td>
                                <td class="sap-table-cell text-right">{{ item.bom_qty|floatformat:0 }}</td>
                                <td class="sap-table-cell text-right">{{ item.stock_qty|floatformat:0 }}</td>
                                <td class="sap-table-cell text-right">{{ item.total_wis_qty|floatformat:0 }}</td>
                                <td class="sap-table-cell text-right">{{ item.dry_run_qty|floatformat:0 }}</td>
                                <td class="sap-table-cell text-right">{{ item.balance_bom_qty|floatformat:0 }}</td>
                                <td class="sap-table-cell text-right">{{ item.after_stock_qty|floatformat:0 }}</td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <!-- Sample data matching the screenshot -->
                            <tr class="sap-table-row sap-table-row--level-0">
                                <td class="sap-table-cell sap-table-cell--tree">
                                    <div class="sap-tree-node">
                                        <button class="sap-tree-toggle" onclick="toggleNode(this)">
                                            <i class="sap-icon sap-icon--expand"></i>
                                        </button>
                                        <span class="sap-tree-text">00861-00-00</span>
                                    </div>
                                </td>
                                <td class="sap-table-cell">ASD-12, 3PHASE UNIT</td>
                                <td class="sap-table-cell">NOS</td>
                                <td class="sap-table-cell text-right">1</td>
                                <td class="sap-table-cell text-right">1</td>
                                <td class="sap-table-cell text-right">0</td>
                                <td class="sap-table-cell text-right">0</td>
                                <td class="sap-table-cell text-right">0</td>
                                <td class="sap-table-cell text-right">1</td>
                                <td class="sap-table-cell text-right">0</td>
                            </tr>
                            <tr class="sap-table-row sap-table-row--level-0">
                                <td class="sap-table-cell sap-table-cell--tree">
                                    <div class="sap-tree-node">
                                        <button class="sap-tree-toggle" onclick="toggleNode(this)">
                                            <i class="sap-icon sap-icon--expand"></i>
                                        </button>
                                        <span class="sap-tree-text">00862-00-00</span>
                                    </div>
                                </td>
                                <td class="sap-table-cell">TP-03, 3PHASE PANEL</td>
                                <td class="sap-table-cell">NOS</td>
                                <td class="sap-table-cell text-right">1</td>
                                <td class="sap-table-cell text-right">1</td>
                                <td class="sap-table-cell text-right">0</td>
                                <td class="sap-table-cell text-right">0</td>
                                <td class="sap-table-cell text-right">0</td>
                                <td class="sap-table-cell text-right">1</td>
                                <td class="sap-table-cell text-right">0</td>
                            </tr>
                            <tr class="sap-table-row sap-table-row--level-0">
                                <td class="sap-table-cell sap-table-cell--tree">
                                    <div class="sap-tree-node">
                                        <button class="sap-tree-toggle" onclick="toggleNode(this)">
                                            <i class="sap-icon sap-icon--expand"></i>
                                        </button>
                                        <span class="sap-tree-text">00863-00-00</span>
                                    </div>
                                </td>
                                <td class="sap-table-cell">SPS-D 17 SINGLE PHASE SUBMERSIBLE STARTER</td>
                                <td class="sap-table-cell">NOS</td>
                                <td class="sap-table-cell text-right">1</td>
                                <td class="sap-table-cell text-right">1</td>
                                <td class="sap-table-cell text-right">0</td>
                                <td class="sap-table-cell text-right">0</td>
                                <td class="sap-table-cell text-right">0</td>
                                <td class="sap-table-cell text-right">1</td>
                                <td class="sap-table-cell text-right">0</td>
                            </tr>
                            <tr class="sap-table-row sap-table-row--level-0">
                                <td class="sap-table-cell sap-table-cell--tree">
                                    <div class="sap-tree-node">
                                        <button class="sap-tree-toggle" onclick="toggleNode(this)">
                                            <i class="sap-icon sap-icon--expand"></i>
                                        </button>
                                        <span class="sap-tree-text">00864-00-00</span>
                                    </div>
                                </td>
                                <td class="sap-table-cell">SPS-C15 SINGLE PHASE DIGITAL STARTER WITH PROTECTIONS & AUTO SWITCH</td>
                                <td class="sap-table-cell">NOS</td>
                                <td class="sap-table-cell text-right">1</td>
                                <td class="sap-table-cell text-right">1</td>
                                <td class="sap-table-cell text-right">0</td>
                                <td class="sap-table-cell text-right">0</td>
                                <td class="sap-table-cell text-right">0</td>
                                <td class="sap-table-cell text-right">1</td>
                                <td class="sap-table-cell text-right">0</td>
                            </tr>
                            <tr class="sap-table-row sap-table-row--level-0">
                                <td class="sap-table-cell sap-table-cell--tree">
                                    <div class="sap-tree-node">
                                        <button class="sap-tree-toggle" onclick="toggleNode(this)">
                                            <i class="sap-icon sap-icon--expand"></i>
                                        </button>
                                        <span class="sap-tree-text">00865-00-00</span>
                                    </div>
                                </td>
                                <td class="sap-table-cell">SPS D-13</td>
                                <td class="sap-table-cell">NOS</td>
                                <td class="sap-table-cell text-right">1</td>
                                <td class="sap-table-cell text-right">1</td>
                                <td class="sap-table-cell text-right">0</td>
                                <td class="sap-table-cell text-right">0</td>
                                <td class="sap-table-cell text-right">0</td>
                                <td class="sap-table-cell text-right">1</td>
                                <td class="sap-table-cell text-right">0</td>
                            </tr>
                            <tr class="sap-table-row sap-table-row--level-0">
                                <td class="sap-table-cell sap-table-cell--tree">
                                    <div class="sap-tree-node">
                                        <button class="sap-tree-toggle" onclick="toggleNode(this)">
                                            <i class="sap-icon sap-icon--expand"></i>
                                        </button>
                                        <span class="sap-tree-text">00869-00-00</span>
                                    </div>
                                </td>
                                <td class="sap-table-cell">C-15 SINGLE PHASE ,30 AMP SUBMERSIBLE STARTER</td>
                                <td class="sap-table-cell">NOS</td>
                                <td class="sap-table-cell text-right">1</td>
                                <td class="sap-table-cell text-right">1</td>
                                <td class="sap-table-cell text-right">0</td>
                                <td class="sap-table-cell text-right">0</td>
                                <td class="sap-table-cell text-right">0</td>
                                <td class="sap-table-cell text-right">1</td>
                                <td class="sap-table-cell text-right">0</td>
                            </tr>
                            <tr class="sap-table-row sap-table-row--level-0">
                                <td class="sap-table-cell sap-table-cell--tree">
                                    <div class="sap-tree-node">
                                        <button class="sap-tree-toggle" onclick="toggleNode(this)">
                                            <i class="sap-icon sap-icon--expand"></i>
                                        </button>
                                        <span class="sap-tree-text">00870-00-00</span>
                                    </div>
                                </td>
                                <td class="sap-table-cell">HARDWARE</td>
                                <td class="sap-table-cell">NOS</td>
                                <td class="sap-table-cell text-right">1</td>
                                <td class="sap-table-cell text-right">1</td>
                                <td class="sap-table-cell text-right">0</td>
                                <td class="sap-table-cell text-right">0</td>
                                <td class="sap-table-cell text-right">0</td>
                                <td class="sap-table-cell text-right">1</td>
                                <td class="sap-table-cell text-right">0</td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* Tree table specific styles */
.sap-table--tree {
    border-collapse: collapse;
}

.sap-table-cell--tree {
    padding-left: 0 !important;
}

.sap-tree-node {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    white-space: nowrap;
}

.sap-tree-toggle {
    background: none;
    border: none;
    padding: 0;
    margin-right: 0.5rem;
    cursor: pointer;
    width: 1rem;
    height: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    transition: background-color 0.1s ease;
}

.sap-tree-toggle:hover {
    background-color: var(--sap-button-hover-background);
}

.sap-tree-toggle .sap-icon {
    font-size: 0.75rem;
    transition: transform 0.2s ease;
}

.sap-tree-toggle.expanded .sap-icon {
    transform: rotate(90deg);
}

.sap-tree-text {
    font-weight: 500;
    color: var(--sap-content-foreground-color);
}

.sap-table-row--level-0 {
    background-color: var(--sap-list-background);
}

.sap-table-row--level-1 {
    background-color: var(--sap-list-alternate-background);
}

.sap-table-row--level-2 {
    background-color: var(--sap-list-background);
}

.sap-table-row--expanded {
    border-bottom: none;
}

.sap-table-row--collapsed {
    display: none;
}

/* Checkbox styling */
.sap-checkbox {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    margin-right: 1rem;
}

.sap-checkbox input[type="checkbox"] {
    margin: 0;
    margin-right: 0.5rem;
}

.sap-checkbox-checkmark {
    font-size: 0.875rem;
    color: var(--sap-content-foreground-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .sap-table-container {
        overflow-x: auto;
    }
    
    .sap-tree-node {
        min-width: 200px;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function toggleNode(button) {
    const icon = button.querySelector('.sap-icon');
    const row = button.closest('tr');
    const isExpanded = button.classList.contains('expanded');
    
    if (isExpanded) {
        button.classList.remove('expanded');
        icon.classList.remove('sap-icon--collapse');
        icon.classList.add('sap-icon--expand');
        // Hide child nodes (implement based on your tree structure)
        hideChildNodes(row);
    } else {
        button.classList.add('expanded');
        icon.classList.remove('sap-icon--expand');
        icon.classList.add('sap-icon--collapse');
        // Show child nodes (implement based on your tree structure)
        showChildNodes(row);
    }
}

function hideChildNodes(parentRow) {
    // Implementation depends on your tree data structure
    // This is a placeholder for actual tree node hiding logic
    console.log('Hiding child nodes for:', parentRow);
}

function showChildNodes(parentRow) {
    // Implementation depends on your tree data structure
    // This is a placeholder for actual tree node showing logic
    console.log('Showing child nodes for:', parentRow);
}

function toggleTreeExpansion() {
    const checkbox = document.getElementById('expand_tree');
    const allToggleButtons = document.querySelectorAll('.sap-tree-toggle');
    
    allToggleButtons.forEach(button => {
        if (checkbox.checked) {
            // Expand all nodes
            if (!button.classList.contains('expanded')) {
                toggleNode(button);
            }
        } else {
            // Collapse all nodes
            if (button.classList.contains('expanded')) {
                toggleNode(button);
            }
        }
    });
}

// Initialize tree expansion state
document.addEventListener('DOMContentLoaded', function() {
    const expandCheckbox = document.getElementById('expand_tree');
    if (expandCheckbox && expandCheckbox.checked) {
        toggleTreeExpansion();
    }
});
</script>
{% endblock %}