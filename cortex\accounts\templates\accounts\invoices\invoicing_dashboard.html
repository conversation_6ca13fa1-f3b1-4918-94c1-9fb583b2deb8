<!-- accounts/templates/accounts/invoices/invoicing_dashboard.html -->
<!-- Invoicing Dashboard Template -->
<!-- Task Group 5: Invoicing & Billing - Invoicing Dashboard (Task 5.14) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Invoicing Dashboard - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-blue-600 to-sap-blue-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Invoicing Dashboard</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage sales invoices, proforma invoices, and billing</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="refreshDashboard()" 
                        class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="refresh-cw" class="w-4 h-4 inline mr-2"></i>
                    Refresh
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6" x-data="invoicingDashboard()">
    
    <!-- Quick Actions -->
    <div class="mb-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <h3 class="text-lg font-medium text-sap-gray-800 mb-4">Quick Actions</h3>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <a href="{% url 'accounts:sales_invoice_create' %}" 
                   class="flex items-center p-4 bg-sap-green-50 border border-sap-green-200 rounded-lg hover:bg-sap-green-100 transition-colors">
                    <div class="w-10 h-10 bg-sap-green-600 rounded-lg flex items-center justify-center mr-3">
                        <i data-lucide="plus" class="w-5 h-5 text-white"></i>
                    </div>
                    <div>
                        <p class="font-medium text-sap-green-800">New Sales Invoice</p>
                        <p class="text-sm text-sap-green-600">Create customer invoice</p>
                    </div>
                </a>
                
                <a href="{% url 'accounts:proforma_invoice_create' %}" 
                   class="flex items-center p-4 bg-sap-blue-50 border border-sap-blue-200 rounded-lg hover:bg-sap-blue-100 transition-colors">
                    <div class="w-10 h-10 bg-sap-blue-600 rounded-lg flex items-center justify-center mr-3">
                        <i data-lucide="file-plus" class="w-5 h-5 text-white"></i>
                    </div>
                    <div>
                        <p class="font-medium text-sap-blue-800">New Proforma Invoice</p>
                        <p class="text-sm text-sap-blue-600">Create quotation</p>
                    </div>
                </a>
                
                <a href="{% url 'accounts:service_tax_invoice_create' %}" 
                   class="flex items-center p-4 bg-sap-purple-50 border border-sap-purple-200 rounded-lg hover:bg-sap-purple-100 transition-colors">
                    <div class="w-10 h-10 bg-sap-purple-600 rounded-lg flex items-center justify-center mr-3">
                        <i data-lucide="percent" class="w-5 h-5 text-white"></i>
                    </div>
                    <div>
                        <p class="font-medium text-sap-purple-800">Service Tax Invoice</p>
                        <p class="text-sm text-sap-purple-600">Create service bill</p>
                    </div>
                </a>
                
                <a href="{% url 'accounts:bill_booking_create' %}" 
                   class="flex items-center p-4 bg-sap-orange-50 border border-sap-orange-200 rounded-lg hover:bg-sap-orange-100 transition-colors">
                    <div class="w-10 h-10 bg-sap-orange-600 rounded-lg flex items-center justify-center mr-3">
                        <i data-lucide="book-open" class="w-5 h-5 text-white"></i>
                    </div>
                    <div>
                        <p class="font-medium text-sap-orange-800">Bill Booking</p>
                        <p class="text-sm text-sap-orange-600">Book vendor bills</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
    
    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="trending-up" class="w-5 h-5 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-sap-gray-600">Total Sales</p>
                    <p class="text-xl font-semibold text-sap-gray-800" x-text="'₹' + formatCurrency(summary.total_sales)">₹{{ summary.total_sales|floatformat:2 }}</p>
                    <p class="text-xs text-sap-green-600" x-text="summary.sales_count + ' invoices'">{{ summary.sales_count }} invoices</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="file-text" class="w-5 h-5 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-sap-gray-600">Proforma Value</p>
                    <p class="text-xl font-semibold text-sap-gray-800" x-text="'₹' + formatCurrency(summary.proforma_value)">₹{{ summary.proforma_value|floatformat:2 }}</p>
                    <p class="text-xs text-sap-blue-600" x-text="summary.proforma_count + ' quotes'">{{ summary.proforma_count }} quotes</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="credit-card" class="w-5 h-5 text-sap-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-sap-gray-600">Outstanding</p>
                    <p class="text-xl font-semibold text-sap-gray-800" x-text="'₹' + formatCurrency(summary.outstanding_amount)">₹{{ summary.outstanding_amount|floatformat:2 }}</p>
                    <p class="text-xs text-sap-purple-600" x-text="summary.outstanding_count + ' pending'">{{ summary.outstanding_count }} pending</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="clock" class="w-5 h-5 text-sap-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-sap-gray-600">Overdue</p>
                    <p class="text-xl font-semibold text-sap-gray-800" x-text="'₹' + formatCurrency(summary.overdue_amount)">₹{{ summary.overdue_amount|floatformat:2 }}</p>
                    <p class="text-xs text-sap-orange-600" x-text="summary.overdue_count + ' overdue'">{{ summary.overdue_count }} overdue</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-red-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="alert-circle" class="w-5 h-5 text-sap-red-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-sap-gray-600">Draft Invoices</p>
                    <p class="text-xl font-semibold text-sap-gray-800" x-text="summary.draft_count">{{ summary.draft_count }}</p>
                    <p class="text-xs text-sap-red-600">Pending approval</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Monthly Sales Chart -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800">Monthly Sales Trend</h3>
            </div>
            <div class="p-6">
                <canvas id="salesChart" width="400" height="200"></canvas>
            </div>
        </div>
        
        <!-- Invoice Status Distribution -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800">Invoice Status Distribution</h3>
            </div>
            <div class="p-6">
                <canvas id="statusChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Recent Activity & Quick Lists -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Recent Invoices -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100 flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Recent Invoices</h3>
                <a href="{% url 'accounts:sales_invoice_list' %}" class="text-sap-blue-600 hover:text-sap-blue-700 text-sm">
                    View All
                </a>
            </div>
            <div class="divide-y divide-sap-gray-100" id="recent-invoices">
                {% for invoice in recent_invoices %}
                <div class="p-4 hover:bg-sap-gray-50">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-sap-gray-900">{{ invoice.invoice_number }}</p>
                            <p class="text-xs text-sap-gray-500">{{ invoice.customer_name }}</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-medium text-sap-gray-900">₹{{ invoice.total_amount|floatformat:2 }}</p>
                            <p class="text-xs text-sap-gray-500">{{ invoice.invoice_date|date:"M d" }}</p>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="p-4 text-center text-sap-gray-400">
                    <i data-lucide="inbox" class="w-8 h-8 mx-auto mb-2"></i>
                    <p class="text-sm">No recent invoices</p>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <!-- Pending Approvals -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100 flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Pending Approvals</h3>
                <span class="bg-sap-orange-100 text-sap-orange-800 text-xs px-2 py-1 rounded-full">{{ pending_approvals.count }}</span>
            </div>
            <div class="divide-y divide-sap-gray-100" id="pending-approvals">
                {% for approval in pending_approvals %}
                <div class="p-4 hover:bg-sap-gray-50">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-sap-gray-900">{{ approval.document_number }}</p>
                            <p class="text-xs text-sap-gray-500">{{ approval.document_type }}</p>
                        </div>
                        <div class="text-right">
                            <button onclick="approveDocument('{{ approval.id }}', '{{ approval.document_type }}')" 
                                    class="text-sap-green-600 hover:text-sap-green-700 text-xs">
                                Approve
                            </button>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="p-4 text-center text-sap-gray-400">
                    <i data-lucide="check-circle" class="w-8 h-8 mx-auto mb-2"></i>
                    <p class="text-sm">No pending approvals</p>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <!-- Top Customers -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800">Top Customers (This Month)</h3>
            </div>
            <div class="divide-y divide-sap-gray-100" id="top-customers">
                {% for customer in top_customers %}
                <div class="p-4 hover:bg-sap-gray-50">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-sap-gray-900">{{ customer.name }}</p>
                            <p class="text-xs text-sap-gray-500">{{ customer.invoice_count }} invoices</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-medium text-sap-gray-900">₹{{ customer.total_amount|floatformat:2 }}</p>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="p-4 text-center text-sap-gray-400">
                    <i data-lucide="users" class="w-8 h-8 mx-auto mb-2"></i>
                    <p class="text-sm">No customer data</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <!-- Invoice Management Grid -->
    <div class="mt-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800">Invoice Management</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Sales Invoices -->
                    <div class="text-center">
                        <div class="w-16 h-16 bg-sap-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <i data-lucide="file-text" class="w-8 h-8 text-sap-green-600"></i>
                        </div>
                        <h4 class="text-lg font-medium text-sap-gray-800 mb-2">Sales Invoices</h4>
                        <p class="text-sm text-sap-gray-600 mb-4">Manage customer invoices and billing</p>
                        <a href="{% url 'accounts:sales_invoice_list' %}" 
                           class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            Manage Sales
                        </a>
                    </div>
                    
                    <!-- Proforma Invoices -->
                    <div class="text-center">
                        <div class="w-16 h-16 bg-sap-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <i data-lucide="file-plus" class="w-8 h-8 text-sap-blue-600"></i>
                        </div>
                        <h4 class="text-lg font-medium text-sap-gray-800 mb-2">Proforma Invoices</h4>
                        <p class="text-sm text-sap-gray-600 mb-4">Create and manage quotations</p>
                        <a href="{% url 'accounts:proforma_invoice_list' %}" 
                           class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            Manage Proforma
                        </a>
                    </div>
                    
                    <!-- Bill Booking -->
                    <div class="text-center">
                        <div class="w-16 h-16 bg-sap-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <i data-lucide="book-open" class="w-8 h-8 text-sap-orange-600"></i>
                        </div>
                        <h4 class="text-lg font-medium text-sap-gray-800 mb-2">Bill Booking</h4>
                        <p class="text-sm text-sap-gray-600 mb-4">Record vendor bills and expenses</p>
                        <a href="{% url 'accounts:bill_booking_list' %}" 
                           class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            Manage Bills
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
function invoicingDashboard() {
    return {
        summary: {
            total_sales: {{ summary.total_sales|default:0 }},
            sales_count: {{ summary.sales_count|default:0 }},
            proforma_value: {{ summary.proforma_value|default:0 }},
            proforma_count: {{ summary.proforma_count|default:0 }},
            outstanding_amount: {{ summary.outstanding_amount|default:0 }},
            outstanding_count: {{ summary.outstanding_count|default:0 }},
            overdue_amount: {{ summary.overdue_amount|default:0 }},
            overdue_count: {{ summary.overdue_count|default:0 }},
            draft_count: {{ summary.draft_count|default:0 }}
        },
        
        formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount);
        },
        
        init() {
            this.initCharts();
        },
        
        initCharts() {
            // Sales Trend Chart
            const salesCtx = document.getElementById('salesChart').getContext('2d');
            new Chart(salesCtx, {
                type: 'line',
                data: {
                    labels: {{ monthly_sales.labels|safe }},
                    datasets: [{
                        label: 'Sales Amount',
                        data: {{ monthly_sales.data|safe }},
                        borderColor: '#22c55e',
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '₹' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
            
            // Status Distribution Chart
            const statusCtx = document.getElementById('statusChart').getContext('2d');
            new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: {{ invoice_status.labels|safe }},
                    datasets: [{
                        data: {{ invoice_status.data|safe }},
                        backgroundColor: [
                            '#22c55e', // Paid
                            '#f59e0b', // Pending
                            '#ef4444', // Overdue
                            '#6b7280'  // Draft
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    }
}

function refreshDashboard() {
    // Show loading state
    const refreshBtn = document.querySelector('[onclick="refreshDashboard()"]');
    const originalContent = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 inline mr-2 animate-spin"></i>Refreshing...';
    refreshBtn.disabled = true;
    
    // Simulate refresh with HTMX
    fetch(window.location.href, {
        headers: {
            'HX-Request': 'true'
        }
    })
    .then(response => response.text())
    .then(html => {
        // Update specific sections without full page reload
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        
        // Update summary cards
        const summaryCards = document.querySelectorAll('.grid.grid-cols-1.md\\:grid-cols-5 .bg-white');
        const newSummaryCards = doc.querySelectorAll('.grid.grid-cols-1.md\\:grid-cols-5 .bg-white');
        
        summaryCards.forEach((card, index) => {
            if (newSummaryCards[index]) {
                card.innerHTML = newSummaryCards[index].innerHTML;
            }
        });
        
        // Update recent activities
        const recentInvoices = document.getElementById('recent-invoices');
        const newRecentInvoices = doc.getElementById('recent-invoices');
        if (recentInvoices && newRecentInvoices) {
            recentInvoices.innerHTML = newRecentInvoices.innerHTML;
        }
        
        // Reset button
        setTimeout(() => {
            refreshBtn.innerHTML = originalContent;
            refreshBtn.disabled = false;
            lucide.createIcons();
        }, 1000);
    })
    .catch(error => {
        console.error('Error refreshing dashboard:', error);
        refreshBtn.innerHTML = originalContent;
        refreshBtn.disabled = false;
    });
}

function approveDocument(documentId, documentType) {
    if (confirm(`Are you sure you want to approve this ${documentType}?`)) {
        fetch(`/accounts/approve/${documentType}/${documentId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => {
            if (response.ok) {
                refreshDashboard();
                alert('Document approved successfully');
            } else {
                alert('Error approving document. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error approving document. Please try again.');
        });
    }
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}