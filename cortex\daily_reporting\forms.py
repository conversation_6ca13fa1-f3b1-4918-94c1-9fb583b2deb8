from django import forms
from django.core.exceptions import ValidationError
from .models import (
    DailyReportingTracker, DesignPlan, ManufacturingPlan, VendorPlan,
    ManPowerPlanning, ManPowerPlanningDetails
)


class DailyReportingTrackerForm(forms.ModelForm):
    """Form for Daily Reporting Tracker System"""
    
    class Meta:
        model = DailyReportingTracker
        fields = [
            'employee_name', 'designation', 'department', 'date_of_reporting',
            'significant_achievements_last_week', 'activities_task_current_week',
            'activities_planned_completed', 'activities_planned_not_completed',
            'activities_unplanned_completed', 'plan_next_week',
            'activity_date', 'wo_number', 'activity', 'estimated_time',
            'status', 'percentage_completed', 'remarks'
        ]
        
        widgets = {
            'employee_name': forms.Select(attrs={
                'class': 'form-select',
                'hx-get': '/daily-reporting/get-employee-details/',
                'hx-target': '#employee-details',
                'hx-trigger': 'change'
            }),
            'designation': forms.Select(attrs={'class': 'form-select'}),
            'department': forms.Select(attrs={'class': 'form-select'}),
            'date_of_reporting': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'significant_achievements_last_week': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Describe significant achievements from last week...'
            }),
            'activities_task_current_week': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'List activities/tasks for current week...'
            }),
            'activities_planned_completed': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Activities that were planned and completed...'
            }),
            'activities_planned_not_completed': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Activities that were planned but not completed...'
            }),
            'activities_unplanned_completed': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Activities that were unplanned but completed...'
            }),
            'plan_next_week': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Plans for next week...'
            }),
            'activity_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'wo_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Work Order Number'
            }),
            'activity': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'Describe the activity...'
            }),
            'estimated_time': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Estimated time (e.g., 2 hours, 1 day)'
            }),
            'status': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Current status of the activity'
            }),
            'percentage_completed': forms.Select(
                choices=[(i, f"{i}%") for i in range(0, 101, 5)],
                attrs={'class': 'form-select'}
            ),
            'remarks': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'Additional remarks...'
            })
        }

    def clean_percentage_completed(self):
        """Validate percentage completed is between 0 and 100"""
        percentage = self.cleaned_data.get('percentage_completed')
        if percentage < 0 or percentage > 100:
            raise ValidationError("Percentage completed must be between 0 and 100")
        return percentage

    def clean(self):
        """Custom validation for the form"""
        cleaned_data = super().clean()
        date_of_reporting = cleaned_data.get('date_of_reporting')
        activity_date = cleaned_data.get('activity_date')
        
        if date_of_reporting and activity_date:
            if activity_date > date_of_reporting:
                raise ValidationError(
                    "Activity date cannot be later than the reporting date"
                )
        
        return cleaned_data


class DesignPlanForm(forms.ModelForm):
    """Form for Design Plan"""
    
    class Meta:
        model = DesignPlan
        fields = ['activities']
        
        widgets = {
            'activities': forms.Textarea(attrs={
                'class': 'form-control',
                'placeholder': 'Design Activities',
                'rows': 5
            })
        }


class ManufacturingPlanForm(forms.ModelForm):
    """Form for Manufacturing Plan"""
    
    class Meta:
        model = ManufacturingPlan
        fields = [
            'wo_number', 'fixture_number', 'item_number', 'description', 'quantity',
            'detailing', 'tpl_entry', 'flame_cut', 'cutting_flame_cut', 'channel',
            'raw_material_list', 'raw_material_receive', 'fabrication', 'sr',
            'machining', 'tapping', 'painting'
        ]
        
        widgets = {
            'wo_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Work Order Number',
                'required': True
            }),
            'fixture_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Fixture Number'
            }),
            'item_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Item Number'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Item Description'
            }),
            'quantity': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Quantity'
            }),
            'detailing': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Detailing Status'
            }),
            'tpl_entry': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'TPL Entry Status'
            }),
            'flame_cut': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Flame Cut Status'
            }),
            'cutting_flame_cut': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Cutting Flame Cut Status'
            }),
            'channel': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Channel Status'
            }),
            'raw_material_list': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Raw Material List Status'
            }),
            'raw_material_receive': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Raw Material Receive Status'
            }),
            'fabrication': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Fabrication Status'
            }),
            'sr': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'SR Status'
            }),
            'machining': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Machining Status'
            }),
            'tapping': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Tapping Status'
            }),
            'painting': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Painting Status'
            })
        }


class VendorPlanForm(forms.ModelForm):
    """Form for Vendor Plan"""
    
    class Meta:
        model = VendorPlan
        fields = [
            'wo_number', 'serial_number', 'fixture_number', 'number_parts_manufacturing',
            'planning', 'flame_cut_loading', 'premach_ineing', 'weldment_fabrication',
            'weldment_loading', 'number_parts_received', 'number_accepted_parts',
            'pending_mfg_parts', 'brought_parts', 'pending_bo_parts',
            'number_pending_challan', 'number_parts_received_after_processing'
        ]
        
        widgets = {
            'wo_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Work Order Number',
                'required': True
            }),
            'serial_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Serial Number'
            }),
            'fixture_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Fixture Number'
            }),
            'number_parts_manufacturing': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Number of Parts for Manufacturing'
            }),
            'planning': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Planning Status'
            }),
            'flame_cut_loading': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Flame Cut Loading Status'
            }),
            'premach_ineing': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Premach Ineing Status'
            }),
            'weldment_fabrication': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Weldment Fabrication Status'
            }),
            'weldment_loading': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Weldment Loading Status'
            }),
            'number_parts_received': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Number of Parts Received'
            }),
            'number_accepted_parts': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Number of Accepted Parts'
            }),
            'pending_mfg_parts': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Pending MFG Parts'
            }),
            'brought_parts': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Brought Parts'
            }),
            'pending_bo_parts': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Pending BO Parts'
            }),
            'number_pending_challan': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Number of Pending Challan'
            }),
            'number_parts_received_after_processing': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Number of Parts Received After Processing'
            })
        }


class ManPowerPlanningForm(forms.ModelForm):
    """Form for ManPower Planning"""
    
    class Meta:
        model = ManPowerPlanning
        fields = ['emp_id', 'date', 'wo_no', 'dept', 'types', 'amendment_no']
        
        widgets = {
            'emp_id': forms.Select(attrs={
                'class': 'form-select',
                'hx-get': '/daily-reporting/get-employee-info/',
                'hx-target': '#employee-info',
                'hx-trigger': 'change'
            }),
            'date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'wo_no': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Work Order Number'
            }),
            'dept': forms.Select(attrs={'class': 'form-select'}),
            'types': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Type of Work'
            }),
            'amendment_no': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'placeholder': 'Amendment Number'
            })
        }


class ManPowerPlanningDetailsForm(forms.ModelForm):
    """Form for ManPower Planning Details"""
    
    class Meta:
        model = ManPowerPlanningDetails
        fields = [
            'equip_id', 'category', 'sub_category', 'planned_desc', 
            'actual_desc', 'hour'
        ]
        
        widgets = {
            'equip_id': forms.Select(attrs={'class': 'form-select'}),
            'category': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Category'
            }),
            'sub_category': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Sub Category'
            }),
            'planned_desc': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Planned Description'
            }),
            'actual_desc': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Actual Description'
            }),
            'hour': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Hours (e.g., 8, 4.5)'
            })
        }


class DashboardFilterForm(forms.Form):
    """Form for dashboard filtering and search"""
    
    SEARCH_CHOICES = [
        ('wo_no', 'Work Order Number'),
        ('employee', 'Employee Name'),
        ('department', 'Department'),
        ('date', 'Date'),
    ]
    
    search_type = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    search_value = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter search value...',
            'hx-get': '/daily-reporting/search/',
            'hx-trigger': 'keyup changed delay:300ms',
            'hx-target': '#search-results'
        })
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    department = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )


class ProjectReportFilterForm(forms.Form):
    """Form for project report filtering"""
    
    REPORT_TYPES = [
        ('departmental_plan', 'Departmental Working Plan'),
        ('individual_plan', 'Individual Working Plan'),
        ('project_summary', 'Project Summary'),
        ('wo_wise', 'Work Order Wise'),
    ]
    
    report_type = forms.ChoiceField(
        choices=REPORT_TYPES,
        widget=forms.Select(attrs={
            'class': 'form-select',
            'hx-get': '/daily-reporting/report-options/',
            'hx-target': '#report-options',
            'hx-trigger': 'change'
        })
    )
    
    work_order = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Work Order Number'
        })
    )
    
    employee_name = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Employee Name'
        })
    )
    
    date_range_start = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    date_range_end = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )