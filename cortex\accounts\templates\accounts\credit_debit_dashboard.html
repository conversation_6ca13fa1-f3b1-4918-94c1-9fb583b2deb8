<!-- accounts/templates/accounts/credit_debit_dashboard.html -->
<!-- Credit & Debit Management Dashboard -->
<!-- Task Group 6: Credit & Debit Management Dashboard (Task 6.1) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Credit & Debit Management - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-purple-600 to-sap-purple-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="scale" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Credit & Debit Management</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Comprehensive financial adjustments and customer account management</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to Accounts
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Credit Notes Summary -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-indigo-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="minus-circle" class="w-6 h-6 text-sap-indigo-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Credit Notes</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ credit_notes_count|default:0 }}</p>
                    <p class="text-xs text-sap-indigo-600 mt-1">₹{{ credit_notes_amount|default:0|floatformat:2 }}</p>
                </div>
            </div>
        </div>

        <!-- Debit Notes Summary -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="plus-circle" class="w-6 h-6 text-sap-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Debit Notes</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ debit_notes_count|default:0 }}</p>
                    <p class="text-xs text-sap-orange-600 mt-1">₹{{ debit_notes_amount|default:0|floatformat:2 }}</p>
                </div>
            </div>
        </div>

        <!-- Sundry Creditors -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="users" class="w-6 h-6 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Sundry Creditors</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ sundry_creditors_count|default:0 }}</p>
                    <p class="text-xs text-sap-green-600 mt-1">₹{{ sundry_creditors_balance|default:0|floatformat:2 }}</p>
                </div>
            </div>
        </div>

        <!-- Pending Approvals -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-yellow-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="clock" class="w-6 h-6 text-sap-yellow-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Pending Approvals</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ pending_approvals|default:0 }}</p>
                    <p class="text-xs text-sap-yellow-600 mt-1">Needs Review</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        
        <!-- Credit & Debit Operations -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="file-text" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                    Credit & Debit Operations
                </h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <!-- Credit Note Actions -->
                    <div class="space-y-3">
                        <h4 class="font-medium text-sap-gray-800 text-sm">Credit Notes</h4>
                        <a href="{% url 'accounts:credit_note_create' %}" 
                           class="flex items-center w-full bg-sap-indigo-600 hover:bg-sap-indigo-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                            <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                            New Credit Note
                        </a>
                        <a href="{% url 'accounts:credit_note_list' %}" 
                           class="flex items-center w-full bg-sap-indigo-100 hover:bg-sap-indigo-200 text-sap-indigo-800 px-4 py-3 rounded-lg font-medium transition-colors">
                            <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                            View All Credit Notes
                        </a>
                    </div>
                    
                    <!-- Debit Note Actions -->
                    <div class="space-y-3">
                        <h4 class="font-medium text-sap-gray-800 text-sm">Debit Notes</h4>
                        <a href="{% url 'accounts:debit_note_create' %}" 
                           class="flex items-center w-full bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                            <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                            New Debit Note
                        </a>
                        <a href="{% url 'accounts:debit_note_list' %}" 
                           class="flex items-center w-full bg-sap-orange-100 hover:bg-sap-orange-200 text-sap-orange-800 px-4 py-3 rounded-lg font-medium transition-colors">
                            <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                            View All Debit Notes
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Management -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="users" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                    Customer Management
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:sundry_creditor_list' %}" 
                       class="flex items-center w-full bg-sap-green-100 hover:bg-sap-green-200 text-sap-green-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="building" class="w-4 h-4 mr-2"></i>
                        Sundry Creditors
                    </a>
                    <a href="{% url 'accounts:sundry_customer_list' %}" 
                       class="flex items-center w-full bg-sap-blue-100 hover:bg-sap-blue-200 text-sap-blue-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="user" class="w-4 h-4 mr-2"></i>
                        Sundry Customers
                    </a>
                    <a href="{% url 'accounts:creditors_debitors_list' %}" 
                       class="flex items-center w-full bg-sap-purple-100 hover:bg-sap-purple-200 text-sap-purple-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="scale" class="w-4 h-4 mr-2"></i>
                        Creditors/Debitors Analysis
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity and Reports -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        
        <!-- Recent Credit Notes -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-sap-gray-800">Recent Credit Notes</h3>
                    <a href="{% url 'accounts:credit_note_list' %}" class="text-sap-indigo-600 hover:text-sap-indigo-700 text-sm font-medium">
                        View All
                    </a>
                </div>
            </div>
            <div class="p-6">
                {% if recent_credit_notes %}
                <div class="space-y-4">
                    {% for credit_note in recent_credit_notes %}
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="text-sm font-medium text-sap-gray-900">
                                <a href="{% url 'accounts:credit_note_detail' credit_note.id %}" class="text-sap-indigo-600 hover:text-sap-indigo-700">
                                    {{ credit_note.credit_note_no }}
                                </a>
                            </div>
                            <div class="text-xs text-sap-gray-500">{{ credit_note.customer_name }}</div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-sap-gray-900">₹{{ credit_note.credit_amount|floatformat:2 }}</div>
                            <div class="text-xs text-sap-gray-500">{{ credit_note.credit_date|date:"d M" }}</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i data-lucide="minus-circle" class="w-8 h-8 text-sap-gray-400 mx-auto mb-2"></i>
                    <p class="text-sm text-sap-gray-500">No recent credit notes</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Recent Debit Notes -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-sap-gray-800">Recent Debit Notes</h3>
                    <a href="{% url 'accounts:debit_note_list' %}" class="text-sap-orange-600 hover:text-sap-orange-700 text-sm font-medium">
                        View All
                    </a>
                </div>
            </div>
            <div class="p-6">
                {% if recent_debit_notes %}
                <div class="space-y-4">
                    {% for debit_note in recent_debit_notes %}
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="text-sm font-medium text-sap-gray-900">
                                <a href="{% url 'accounts:debit_note_detail' debit_note.id %}" class="text-sap-orange-600 hover:text-sap-orange-700">
                                    {{ debit_note.debit_note_no }}
                                </a>
                            </div>
                            <div class="text-xs text-sap-gray-500">{{ debit_note.customer_name }}</div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-sap-gray-900">₹{{ debit_note.debit_amount|floatformat:2 }}</div>
                            <div class="text-xs text-sap-gray-500">{{ debit_note.debit_date|date:"d M" }}</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i data-lucide="plus-circle" class="w-8 h-8 text-sap-gray-400 mx-auto mb-2"></i>
                    <p class="text-sm text-sap-gray-500">No recent debit notes</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Reports and Analytics -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="bar-chart" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                    Reports & Analytics
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:outstanding_amounts_report' %}" 
                       class="flex items-center w-full bg-sap-red-100 hover:bg-sap-red-200 text-sap-red-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="alert-triangle" class="w-4 h-4 mr-2"></i>
                        Outstanding Amounts
                    </a>
                    <a href="{% url 'accounts:credit_debit_summary_report' %}" 
                       class="flex items-center w-full bg-sap-blue-100 hover:bg-sap-blue-200 text-sap-blue-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="pie-chart" class="w-4 h-4 mr-2"></i>
                        Credit/Debit Summary
                    </a>
                    <a href="{% url 'accounts:aging_analysis_report' %}" 
                       class="flex items-center w-full bg-sap-yellow-100 hover:bg-sap-yellow-200 text-sap-yellow-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="calendar" class="w-4 h-4 mr-2"></i>
                        Aging Analysis
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats Chart Section -->
    <div class="mt-8">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="trending-up" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                    Monthly Credit/Debit Trends
                </h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Current Month -->
                    <div class="text-center">
                        <p class="text-sm font-medium text-sap-gray-600 mb-2">This Month</p>
                        <div class="space-y-2">
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-sap-indigo-600">Credits:</span>
                                <span class="text-sm font-medium">₹{{ current_month_credits|default:0|floatformat:2 }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-sap-orange-600">Debits:</span>
                                <span class="text-sm font-medium">₹{{ current_month_debits|default:0|floatformat:2 }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Previous Month -->
                    <div class="text-center">
                        <p class="text-sm font-medium text-sap-gray-600 mb-2">Last Month</p>
                        <div class="space-y-2">
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-sap-indigo-600">Credits:</span>
                                <span class="text-sm font-medium">₹{{ last_month_credits|default:0|floatformat:2 }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-sap-orange-600">Debits:</span>
                                <span class="text-sm font-medium">₹{{ last_month_debits|default:0|floatformat:2 }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Year to Date -->
                    <div class="text-center">
                        <p class="text-sm font-medium text-sap-gray-600 mb-2">Year to Date</p>
                        <div class="space-y-2">
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-sap-indigo-600">Credits:</span>
                                <span class="text-sm font-medium">₹{{ ytd_credits|default:0|floatformat:2 }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-sap-orange-600">Debits:</span>
                                <span class="text-sm font-medium">₹{{ ytd_debits|default:0|floatformat:2 }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Initialize lucide icons on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}