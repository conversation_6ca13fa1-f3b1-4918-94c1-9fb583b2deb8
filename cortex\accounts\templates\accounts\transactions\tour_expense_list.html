<!-- accounts/templates/accounts/transactions/tour_expense_list.html -->
<!-- Tour Expense List View Template -->
<!-- Task Group 9: Tour/Expense Management - Tour Expense List (Task 9.4) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Tour Expense Management - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-orange-600 to-sap-orange-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="receipt" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Tour Expense Management</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Track and manage tour-related expenses and reimbursements</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:expenses_dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to Dashboard
                </a>
                <a href="{% url 'accounts:tour_expense_create' %}" 
                   class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    Add Expense
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Search and Filter Section -->
    <div class="mb-6 bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4">
            <form method="get" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4">
                <!-- Search by Tour/Employee -->
                <div class="flex-1">
                    <label for="search" class="block text-sm font-medium text-sap-gray-700 mb-1">Search</label>
                    <input type="text" name="search" value="{{ request.GET.search }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500"
                           placeholder="Search by tour number, employee, or description...">
                </div>
                
                <!-- Expense Type Filter -->
                <div>
                    <label for="expense_type" class="block text-sm font-medium text-sap-gray-700 mb-1">Expense Type</label>
                    <select name="expense_type" 
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
                        <option value="">All Types</option>
                        {% for expense_type in expense_types %}
                        <option value="{{ expense_type.id }}" {% if request.GET.expense_type == expense_type.id|stringformat:"s" %}selected{% endif %}>
                            {{ expense_type.expense_type_description }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-sap-gray-700 mb-1">Status</label>
                    <select name="status" 
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
                        <option value="">All Status</option>
                        {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if request.GET.status == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Date Range -->
                <div>
                    <label for="date_from" class="block text-sm font-medium text-sap-gray-700 mb-1">From Date</label>
                    <input type="date" name="date_from" value="{{ request.GET.date_from }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
                </div>
                
                <!-- Search Button -->
                <div>
                    <button type="submit" 
                            class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="search" class="w-4 h-4 inline mr-2"></i>
                        Search
                    </button>
                </div>
                
                <!-- Clear Button -->
                {% if request.GET %}
                <div>
                    <a href="{% url 'accounts:tour_expense_list' %}" 
                       class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                        Clear
                    </a>
                </div>
                {% endif %}
            </form>
        </div>
    </div>

    <!-- Expense Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="receipt" class="w-6 h-6 text-sap-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Expenses</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ expenses.count|default:0 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-yellow-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="clock" class="w-6 h-6 text-sap-yellow-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Pending Approval</p>
                    <p class="text-2xl font-bold text-sap-gray-900">0</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="check-circle" class="w-6 h-6 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Approved</p>
                    <p class="text-2xl font-bold text-sap-gray-900">0</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="dollar-sign" class="w-6 h-6 text-sap-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Amount</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹0.00</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tour Expenses Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Tour Expenses</h3>
                <div class="flex items-center space-x-2">
                    <button type="button" onclick="exportExpenses()" 
                            class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                        Export
                    </button>
                    <button type="button" onclick="generateExpenseReport()" 
                            class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="bar-chart" class="w-4 h-4 inline mr-2"></i>
                        Expense Report
                    </button>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Expense Details
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Tour & Employee
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Amount & Receipt
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Date & Category
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for expense in expenses %}
                    <tr class="hover:bg-sap-gray-50">
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-sap-orange-100 rounded-lg flex items-center justify-center mr-3">
                                    <i data-lucide="receipt" class="w-5 h-5 text-sap-orange-600"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-sap-gray-900">
                                        <a href="{% url 'accounts:tour_expense_detail' expense.id %}" class="text-sap-orange-600 hover:text-sap-orange-900">
                                            {{ expense.expense_number|default:"Expense" }}
                                        </a>
                                    </div>
                                    <div class="text-sm text-sap-gray-500">{{ expense.expense_description|truncatechars:40|default:"-" }}</div>
                                    {% if expense.vendor_name %}
                                    <div class="text-xs text-sap-gray-400">Vendor: {{ expense.vendor_name }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            {% if expense.tour_voucher %}
                            <div class="text-sm text-sap-gray-900">{{ expense.tour_voucher.tour_number|default:"-" }}</div>
                            <div class="text-sm text-sap-gray-500">{{ expense.tour_voucher.employee_name|default:"-" }}</div>
                            {% else %}
                            <div class="text-sm text-sap-gray-900">{{ expense.employee_name|default:"-" }}</div>
                            {% endif %}
                            {% if expense.destination %}
                            <div class="text-xs text-sap-gray-400">{{ expense.destination }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-sap-gray-900">₹{{ expense.expense_amount|floatformat:2 }}</div>
                            {% if expense.receipt_number %}
                            <div class="text-sm text-sap-gray-500">Receipt: {{ expense.receipt_number }}</div>
                            {% endif %}
                            {% if expense.has_receipt %}
                            <div class="text-xs text-sap-green-600">Receipt Available</div>
                            {% else %}
                            <div class="text-xs text-sap-red-600">No Receipt</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            {% if expense.expense_date %}
                            <div class="text-sm text-sap-gray-900">{{ expense.expense_date|date:"d M Y" }}</div>
                            {% endif %}
                            {% if expense.expense_type %}
                            <div class="text-sm text-sap-gray-500">{{ expense.expense_type.expense_type_description }}</div>
                            {% endif %}
                            {% if expense.category %}
                            <div class="text-xs text-sap-gray-400">{{ expense.get_category_display }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if expense.status == 'approved' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-green-100 text-sap-green-800">
                                Approved
                            </span>
                            {% elif expense.status == 'pending' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-yellow-100 text-sap-yellow-800">
                                Pending
                            </span>
                            {% elif expense.status == 'rejected' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-red-100 text-sap-red-800">
                                Rejected
                            </span>
                            {% elif expense.status == 'reimbursed' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-blue-100 text-sap-blue-800">
                                Reimbursed
                            </span>
                            {% else %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-gray-100 text-sap-gray-800">
                                {{ expense.get_status_display|default:"Unknown" }}
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <a href="{% url 'accounts:tour_expense_detail' expense.id %}" 
                                   class="text-sap-orange-600 hover:text-sap-orange-900" title="View Details">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                </a>
                                <a href="{% url 'accounts:tour_expense_edit' expense.id %}" 
                                   class="text-sap-blue-600 hover:text-sap-blue-900" title="Edit">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </a>
                                {% if expense.has_receipt %}
                                <button type="button" onclick="viewReceipt({{ expense.id }})" 
                                        class="text-sap-green-600 hover:text-sap-green-900" title="View Receipt">
                                    <i data-lucide="file-image" class="w-4 h-4"></i>
                                </button>
                                {% else %}
                                <button type="button" onclick="uploadReceipt({{ expense.id }})" 
                                        class="text-sap-purple-600 hover:text-sap-purple-900" title="Upload Receipt">
                                    <i data-lucide="upload" class="w-4 h-4"></i>
                                </button>
                                {% endif %}
                                {% if expense.status == 'pending' %}
                                <button type="button" onclick="approveExpense({{ expense.id }})" 
                                        class="text-sap-green-600 hover:text-sap-green-900" title="Approve">
                                    <i data-lucide="check" class="w-4 h-4"></i>
                                </button>
                                {% endif %}
                                <button type="button" onclick="printExpense({{ expense.id }})" 
                                        class="text-sap-gray-600 hover:text-sap-gray-900" title="Print">
                                    <i data-lucide="printer" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="px-6 py-8 text-center">
                            <div class="text-center">
                                <i data-lucide="receipt" class="w-12 h-12 text-sap-gray-400 mx-auto mb-4"></i>
                                <h3 class="text-sm font-medium text-sap-gray-900 mb-2">No tour expenses found</h3>
                                <p class="text-sm text-sap-gray-600 mb-4">Get started by adding your first tour expense.</p>
                                <a href="{% url 'accounts:tour_expense_create' %}" 
                                   class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                                    Add Expense
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="px-6 py-4 border-t border-sap-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-sap-gray-700">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} entries
                </div>
                <div class="flex space-x-1">
                    {% if page_obj.has_previous %}
                    <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ page_obj.previous_page_number }}" 
                       class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-l-lg hover:bg-sap-gray-50">
                        Previous
                    </a>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <span class="px-3 py-2 text-sm font-medium text-sap-orange-600 bg-sap-orange-50 border border-sap-orange-300">
                            {{ num }}
                        </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ num }}" 
                           class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 hover:bg-sap-gray-50">
                            {{ num }}
                        </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ page_obj.next_page_number }}" 
                       class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-r-lg hover:bg-sap-gray-50">
                        Next
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function exportExpenses() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = '?' + params.toString();
}

function generateExpenseReport() {
    alert('Expense report generation functionality would be implemented here.');
}

function viewReceipt(expenseId) {
    window.open(`/accounts/transactions/tour-expenses/${expenseId}/receipt/`, '_blank');
}

function uploadReceipt(expenseId) {
    // This would open a modal or redirect to receipt upload page
    window.location.href = `/accounts/transactions/tour-expenses/${expenseId}/upload-receipt/`;
}

function approveExpense(expenseId) {
    if (confirm('Approve this expense?')) {
        // This would make an AJAX call to approve the expense
        alert(`Expense approval functionality for expense ID ${expenseId} would be implemented here.`);
    }
}

function printExpense(expenseId) {
    window.open(`/accounts/transactions/tour-expenses/${expenseId}/print/`, '_blank');
}

// Initialize lucide icons on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %>