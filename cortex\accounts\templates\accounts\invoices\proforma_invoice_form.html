<!-- accounts/templates/accounts/invoices/proforma_invoice_form.html -->
<!-- Proforma Invoice Create Form Template -->
<!-- ASP.NET Source: Module/Accounts/Transactions/ProformaInvoice_New.aspx -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}New Proforma Invoice - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-blue-600 to-sap-blue-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">
                        New Proforma Invoice
                    </h1>
                    <p class="text-sm text-sap-gray-600 mt-1">
                        Create a proforma invoice for customer quotation
                    </p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:proforma_invoice_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Proforma Invoice Form -->
    <div class="max-w-7xl mx-auto">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="edit" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                    Proforma Invoice Information
                </h3>
                <p class="text-sm text-sap-gray-600 mt-1">Complete all required fields to create the proforma invoice</p>
            </div>
            
            <form method="post" id="proforma-invoice-form" class="p-6" x-data="proformaInvoiceForm()" hx-post="{% url 'accounts:proforma_invoice_create' %}" hx-target="#form-messages">
                {% csrf_token %}
                
                <!-- Form Messages -->
                <div id="form-messages" class="mb-4"></div>
                
                <!-- Basic Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="info" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Basic Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Customer Name -->
                        <div>
                            <label for="{{ form.customer_name.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Customer Name *
                            </label>
                            {{ form.customer_name|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.customer_name.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.customer_name.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- PO Number -->
                        <div>
                            <label for="{{ form.po_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                PO Number
                            </label>
                            {{ form.po_no|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.po_no.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.po_no.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- PO Date -->
                        <div>
                            <label for="{{ form.po_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                PO Date
                            </label>
                            {{ form.po_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.po_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.po_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Work Order Number -->
                        <div>
                            <label for="{{ form.work_order_no.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Work Order Number
                            </label>
                            {{ form.work_order_no|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.work_order_no.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.work_order_no.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Validity Days -->
                        <div>
                            <label for="{{ form.validity_days.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Validity (Days)
                            </label>
                            {{ form.validity_days|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.validity_days.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.validity_days.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Default: 30 days</p>
                        </div>
                        
                        <!-- Status -->
                        <div>
                            <label for="{{ form.status.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Status
                            </label>
                            {{ form.status|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500" }}
                            {% if form.status.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.status.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Amount Details Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="calculator" class="w-5 h-5 mr-2 text-sap-emerald-600"></i>
                        Amount Breakdown
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <!-- Basic Amount -->
                        <div>
                            <label for="{{ form.basic_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Basic Amount *
                            </label>
                            {{ form.basic_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.basic_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.basic_amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- VAT Amount -->
                        <div>
                            <label for="{{ form.vat_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                VAT Amount
                            </label>
                            {{ form.vat_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.vat_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.vat_amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- CST Amount -->
                        <div>
                            <label for="{{ form.cst_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                CST Amount
                            </label>
                            {{ form.cst_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.cst_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.cst_amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Excise Amount -->
                        <div>
                            <label for="{{ form.excise_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Excise Amount
                            </label>
                            {{ form.excise_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.excise_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.excise_amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Service Tax Amount -->
                        <div>
                            <label for="{{ form.service_tax_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Service Tax Amount
                            </label>
                            {{ form.service_tax_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.service_tax_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.service_tax_amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- P&F Amount -->
                        <div>
                            <label for="{{ form.pf_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                P&F Amount
                            </label>
                            {{ form.pf_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.pf_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.pf_amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Freight Amount -->
                        <div>
                            <label for="{{ form.freight_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Freight Amount
                            </label>
                            {{ form.freight_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.freight_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.freight_amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Discount Amount -->
                        <div>
                            <label for="{{ form.discount_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Discount Amount
                            </label>
                            {{ form.discount_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.discount_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.discount_amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Total Summary -->
                <div class="mb-8 bg-sap-blue-50 border border-sap-blue-200 rounded-lg p-6">
                    <h4 class="text-lg font-medium text-sap-blue-800 mb-4 flex items-center">
                        <i data-lucide="receipt" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Invoice Summary
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
                        <div>
                            <p class="text-sm text-sap-blue-600">Gross Amount</p>
                            <p class="text-xl font-bold text-sap-blue-800" x-text="'₹' + grossAmount.toFixed(2)"></p>
                        </div>
                        <div>
                            <p class="text-sm text-sap-blue-600">Total Tax</p>
                            <p class="text-xl font-bold text-sap-blue-800" x-text="'₹' + totalTax.toFixed(2)"></p>
                        </div>
                        <div>
                            <p class="text-sm text-sap-blue-600">Discount</p>
                            <p class="text-xl font-bold text-sap-blue-800" x-text="'₹' + totalDiscount.toFixed(2)"></p>
                        </div>
                        <div>
                            <p class="text-sm text-sap-blue-600">Net Amount</p>
                            <p class="text-2xl font-bold text-sap-blue-800" x-text="'₹' + netAmount.toFixed(2)"></p>
                        </div>
                    </div>
                </div>
                
                <!-- Terms and Conditions -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="file-text" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                        Terms and Conditions
                    </h4>
                    <div class="bg-sap-gray-50 border border-sap-gray-200 rounded-lg p-4">
                        <textarea rows="4" 
                                  placeholder="Enter terms and conditions for this proforma invoice..."
                                  class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500 resize-none"></textarea>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="resetForm()" 
                                class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="rotate-ccw" class="w-4 h-4 inline mr-2"></i>
                            Reset
                        </button>
                        <button type="button" @click="previewProforma()" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="eye" class="w-4 h-4 inline mr-2"></i>
                            Preview
                        </button>
                        <button type="button" @click="generatePDF()" 
                                class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                            Generate PDF
                        </button>
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="{% url 'accounts:proforma_invoice_list' %}" 
                           class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-sap-emerald-600 hover:bg-sap-emerald-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                            Save Proforma Invoice
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Proforma Guidelines -->
        <div class="mt-6 bg-sap-blue-50 border border-sap-blue-200 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i data-lucide="info" class="w-5 h-5 text-sap-blue-600"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-sap-blue-800">Proforma Invoice Guidelines</h3>
                    <div class="mt-2 text-sm text-sap-blue-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Proforma invoices are used for customer quotations and estimates</li>
                            <li>All tax amounts are calculated automatically based on basic amount</li>
                            <li>Validity period helps customers understand quotation timeline</li>
                            <li>Include comprehensive terms and conditions</li>
                            <li>Can be converted to sales invoice once approved</li>
                            <li>PDF generation available for customer distribution</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function proformaInvoiceForm() {
    return {
        grossAmount: 0,
        totalTax: 0,
        totalDiscount: 0,
        netAmount: 0,
        
        init() {
            this.$watch('basic_amount', () => this.calculateTotals());
            this.$watch('vat_amount', () => this.calculateTotals());
            this.$watch('cst_amount', () => this.calculateTotals());
            this.$watch('excise_amount', () => this.calculateTotals());
            this.$watch('service_tax_amount', () => this.calculateTotals());
            this.$watch('discount_amount', () => this.calculateTotals());
            this.calculateTotals();
        },
        
        calculateTotals() {
            const basicAmount = parseFloat(document.getElementById('{{ form.basic_amount.id_for_label }}')?.value) || 0;
            const vatAmount = parseFloat(document.getElementById('{{ form.vat_amount.id_for_label }}')?.value) || 0;
            const cstAmount = parseFloat(document.getElementById('{{ form.cst_amount.id_for_label }}')?.value) || 0;
            const exciseAmount = parseFloat(document.getElementById('{{ form.excise_amount.id_for_label }}')?.value) || 0;
            const serviceTaxAmount = parseFloat(document.getElementById('{{ form.service_tax_amount.id_for_label }}')?.value) || 0;
            const pfAmount = parseFloat(document.getElementById('{{ form.pf_amount.id_for_label }}')?.value) || 0;
            const freightAmount = parseFloat(document.getElementById('{{ form.freight_amount.id_for_label }}')?.value) || 0;
            const discountAmount = parseFloat(document.getElementById('{{ form.discount_amount.id_for_label }}')?.value) || 0;
            
            this.grossAmount = basicAmount + pfAmount + freightAmount;
            this.totalTax = vatAmount + cstAmount + exciseAmount + serviceTaxAmount;
            this.totalDiscount = discountAmount;
            this.netAmount = this.grossAmount + this.totalTax - this.totalDiscount;
        },
        
        previewProforma() {
            const customerName = document.getElementById('{{ form.customer_name.id_for_label }}').value;
            if (!customerName.trim()) {
                alert('Please enter customer name to preview the proforma invoice.');
                return;
            }
            
            if (this.netAmount <= 0) {
                alert('Please enter valid amounts to preview the proforma invoice.');
                return;
            }
            
            alert('Proforma invoice preview functionality would open in a new window.');
        },
        
        generatePDF() {
            const customerName = document.getElementById('{{ form.customer_name.id_for_label }}').value;
            if (!customerName.trim()) {
                alert('Please enter customer name to generate PDF.');
                return;
            }
            
            if (this.netAmount <= 0) {
                alert('Please enter valid amounts to generate PDF.');
                return;
            }
            
            alert('PDF generation functionality would be implemented here.');
        }
    }
}

function resetForm() {
    if (confirm('Are you sure you want to reset the form? All unsaved changes will be lost.')) {
        location.reload();
    }
}

// Auto-calculation setup
document.addEventListener('DOMContentLoaded', function() {
    // Set default validity days
    const validityField = document.getElementById('{{ form.validity_days.id_for_label }}');
    if (validityField && !validityField.value) {
        validityField.value = 30;
    }
    
    // Auto-set PO date to today if not provided
    const poDateField = document.getElementById('{{ form.po_date.id_for_label }}');
    if (poDateField && !poDateField.value) {
        const today = new Date().toISOString().split('T')[0];
        poDateField.value = today;
    }
    
    // Add change listeners for auto-calculation
    const amountFields = [
        '{{ form.basic_amount.id_for_label }}',
        '{{ form.vat_amount.id_for_label }}',
        '{{ form.cst_amount.id_for_label }}',
        '{{ form.excise_amount.id_for_label }}',
        '{{ form.service_tax_amount.id_for_label }}',
        '{{ form.pf_amount.id_for_label }}',
        '{{ form.freight_amount.id_for_label }}',
        '{{ form.discount_amount.id_for_label }}'
    ];
    
    amountFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('input', function() {
                // Trigger Alpine.js recalculation
                const event = new CustomEvent('alpine:recalculate');
                document.dispatchEvent(event);
            });
        }
    });
    
    lucide.createIcons();
});
</script>
{% endblock %}