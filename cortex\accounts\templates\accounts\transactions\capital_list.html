<!-- accounts/templates/accounts/transactions/capital_list.html -->
<!-- Capital List View Template -->
<!-- Task Group 7: Capital & Loans Management - Capital List (Task 7.5) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Capital Structure Management - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-green-600 to-sap-green-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="dollar-sign" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Capital Structure Management</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage company capital entries, equity, and ownership structure</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:capital_loans_dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to Dashboard
                </a>
                <a href="{% url 'accounts:capital_create' %}" 
                   class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    New Capital Entry
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Search and Filter Section -->
    <div class="mb-6 bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4">
            <form method="get" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4">
                <!-- Search by Capital Type -->
                <div class="flex-1">
                    <label for="capital_type" class="block text-sm font-medium text-sap-gray-700 mb-1">Search Capital Type</label>
                    <input type="text" name="capital_type" value="{{ request.GET.capital_type }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500"
                           placeholder="Search by capital type...">
                </div>
                
                <!-- Capital Category Filter -->
                <div>
                    <label for="category" class="block text-sm font-medium text-sap-gray-700 mb-1">Category</label>
                    <select name="category" 
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                        <option value="">All Categories</option>
                        {% for value, label in category_choices %}
                        <option value="{{ value }}" {% if request.GET.category == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-sap-gray-700 mb-1">Status</label>
                    <select name="status" 
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                        <option value="">All Status</option>
                        {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if request.GET.status == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Search Button -->
                <div>
                    <button type="submit" 
                            class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="search" class="w-4 h-4 inline mr-2"></i>
                        Search
                    </button>
                </div>
                
                <!-- Clear Button -->
                {% if request.GET %}
                <div>
                    <a href="{% url 'accounts:capital_list' %}" 
                       class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                        Clear
                    </a>
                </div>
                {% endif %}
            </form>
        </div>
    </div>

    <!-- Capital Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="dollar-sign" class="w-6 h-6 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Capital</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ capital_entries.count|default:0 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="users" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Equity</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹0.00</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="trending-up" class="w-6 h-6 text-sap-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Reserves</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹0.00</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="pie-chart" class="w-6 h-6 text-sap-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Amount</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹0.00</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Capital Entries Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Capital Structure</h3>
                <div class="flex items-center space-x-2">
                    <button type="button" onclick="exportCapital()" 
                            class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                        Export
                    </button>
                    <button type="button" onclick="generateCapitalReport()" 
                            class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="file-text" class="w-4 h-4 inline mr-2"></i>
                        Structure Report
                    </button>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Capital Details
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Category
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Amount
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Particulars
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Dates
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for capital in capital_entries %}
                    <tr class="hover:bg-sap-gray-50">
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-sap-green-100 rounded-lg flex items-center justify-center mr-3">
                                    <i data-lucide="dollar-sign" class="w-5 h-5 text-sap-green-600"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-sap-gray-900">
                                        <a href="{% url 'accounts:capital_detail' capital.id %}" class="text-sap-green-600 hover:text-sap-green-900">
                                            {{ capital.capital_code|default:"Capital Entry" }}
                                        </a>
                                    </div>
                                    <div class="text-sm text-sap-gray-500">{{ capital.capital_type|default:"-" }}</div>
                                    {% if capital.reference_number %}
                                    <div class="text-xs text-sap-gray-400">Ref: {{ capital.reference_number }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-sap-gray-900">{{ capital.get_category_display|default:"-" }}</div>
                            {% if capital.subcategory %}
                            <div class="text-sm text-sap-gray-500">{{ capital.subcategory }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-sap-gray-900">₹{{ capital.capital_amount|floatformat:2 }}</div>
                            {% if capital.authorized_amount %}
                            <div class="text-sm text-sap-gray-500">Auth: ₹{{ capital.authorized_amount|floatformat:2 }}</div>
                            {% endif %}
                            {% if capital.issued_amount %}
                            <div class="text-xs text-sap-gray-400">Issued: ₹{{ capital.issued_amount|floatformat:2 }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            {% if capital.shares_count %}
                            <div class="text-sm text-sap-gray-900">{{ capital.shares_count|floatformat:0 }} shares</div>
                            {% endif %}
                            {% if capital.par_value %}
                            <div class="text-sm text-sap-gray-500">Par: ₹{{ capital.par_value|floatformat:2 }}</div>
                            {% endif %}
                            {% if capital.shareholder_name %}
                            <div class="text-xs text-sap-gray-400">{{ capital.shareholder_name }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            {% if capital.issue_date %}
                            <div class="text-sm text-sap-gray-900">Issue: {{ capital.issue_date|date:"d M Y" }}</div>
                            {% endif %}
                            {% if capital.effective_date %}
                            <div class="text-sm text-sap-gray-500">Effective: {{ capital.effective_date|date:"d M Y" }}</div>
                            {% endif %}
                            {% if capital.created_at %}
                            <div class="text-xs text-sap-gray-400">Created: {{ capital.created_at|date:"d M Y" }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if capital.status == 'active' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-green-100 text-sap-green-800">
                                Active
                            </span>
                            {% elif capital.status == 'pending' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-yellow-100 text-sap-yellow-800">
                                Pending
                            </span>
                            {% elif capital.status == 'cancelled' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-red-100 text-sap-red-800">
                                Cancelled
                            </span>
                            {% else %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-blue-100 text-sap-blue-800">
                                {{ capital.get_status_display|default:"Unknown" }}
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <a href="{% url 'accounts:capital_detail' capital.id %}" 
                                   class="text-sap-green-600 hover:text-sap-green-900" title="View Details">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                </a>
                                <a href="{% url 'accounts:capital_edit' capital.id %}" 
                                   class="text-sap-blue-600 hover:text-sap-blue-900" title="Edit">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </a>
                                <button type="button" onclick="generateCertificate({{ capital.id }})" 
                                        class="text-sap-purple-600 hover:text-sap-purple-900" title="Generate Certificate">
                                    <i data-lucide="award" class="w-4 h-4"></i>
                                </button>
                                <button type="button" onclick="printCapitalDetails({{ capital.id }})" 
                                        class="text-sap-orange-600 hover:text-sap-orange-900" title="Print">
                                    <i data-lucide="printer" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="px-6 py-8 text-center">
                            <div class="text-center">
                                <i data-lucide="dollar-sign" class="w-12 h-12 text-sap-gray-400 mx-auto mb-4"></i>
                                <h3 class="text-sm font-medium text-sap-gray-900 mb-2">No capital entries found</h3>
                                <p class="text-sm text-sap-gray-600 mb-4">Get started by adding your first capital entry.</p>
                                <a href="{% url 'accounts:capital_create' %}" 
                                   class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                                    Add Capital Entry
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="px-6 py-4 border-t border-sap-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-sap-gray-700">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} entries
                </div>
                <div class="flex space-x-1">
                    {% if page_obj.has_previous %}
                    <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ page_obj.previous_page_number }}" 
                       class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-l-lg hover:bg-sap-gray-50">
                        Previous
                    </a>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <span class="px-3 py-2 text-sm font-medium text-sap-green-600 bg-sap-green-50 border border-sap-green-300">
                            {{ num }}
                        </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ num }}" 
                           class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 hover:bg-sap-gray-50">
                            {{ num }}
                        </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ page_obj.next_page_number }}" 
                       class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-r-lg hover:bg-sap-gray-50">
                        Next
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function exportCapital() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = '?' + params.toString();
}

function generateCapitalReport() {
    alert('Capital structure report generation functionality would be implemented here.');
}

function generateCertificate(capitalId) {
    window.open(`/accounts/transactions/capital/${capitalId}/certificate/`, '_blank');
}

function printCapitalDetails(capitalId) {
    window.open(`/accounts/transactions/capital/${capitalId}/print/`, '_blank');
}

// Initialize lucide icons on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %>