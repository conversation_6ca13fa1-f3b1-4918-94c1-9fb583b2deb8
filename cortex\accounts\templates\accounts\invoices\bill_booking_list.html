<!-- accounts/templates/accounts/invoices/bill_booking_list.html -->
<!-- Bill Booking List Template with HTMX -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Bill Booking - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-green-600 to-sap-green-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="folder" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Bill Booking</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage supplier bill bookings</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:bill_booking_create' %}" 
                   class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    New Bill Booking
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6" x-data="billBookingList()">
    
    <!-- Search and Filter Section -->
    <div class="mb-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-sap-gray-800">Search & Filter</h3>
                <button type="button" @click="toggleFilters()" class="text-sap-blue-600 hover:text-sap-blue-700">
                    <i data-lucide="filter" class="w-5 h-5"></i>
                </button>
            </div>
            
            <form hx-get="{% url 'accounts:bill_booking_list' %}" 
                  hx-target="#bill-booking-list-container" 
                  hx-trigger="submit, input delay:500ms changed"
                  hx-indicator="#search-spinner"
                  class="space-y-4" 
                  id="filter-form">
                
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <!-- Real-time Search -->
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-1">Search</label>
                        <div class="relative">
                            <input type="text" name="search_value" value="{{ request.GET.search_value }}" 
                                   placeholder="Supplier name, bill no..."
                                   class="block w-full px-3 py-2 pl-10 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500"
                                   hx-get="{% url 'accounts:bill_booking_list' %}"
                                   hx-target="#bill-booking-list-container"
                                   hx-trigger="keyup changed delay:300ms"
                                   hx-indicator="#search-spinner">
                            <i data-lucide="search" class="w-4 h-4 absolute left-3 top-3 text-sap-gray-400"></i>
                            <div id="search-spinner" class="htmx-indicator absolute right-3 top-3">
                                <i data-lucide="loader-2" class="w-4 h-4 animate-spin text-sap-blue-500"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Status Filter -->
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-1">Status</label>
                        <select name="status" class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500"
                                hx-get="{% url 'accounts:bill_booking_list' %}"
                                hx-target="#bill-booking-list-container"
                                hx-trigger="change"
                                hx-indicator="#search-spinner">
                            <option value="">All Status</option>
                            <option value="draft" {% if request.GET.status == "draft" %}selected{% endif %}>Draft</option>
                            <option value="submitted" {% if request.GET.status == "submitted" %}selected{% endif %}>Submitted</option>
                            <option value="authorized" {% if request.GET.status == "authorized" %}selected{% endif %}>Authorized</option>
                            <option value="cancelled" {% if request.GET.status == "cancelled" %}selected{% endif %}>Cancelled</option>
                        </select>
                    </div>
                    
                    <!-- Date Range -->
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-1">From Date</label>
                        <input type="date" name="from_date" value="{{ request.GET.from_date }}"
                               class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500"
                               hx-get="{% url 'accounts:bill_booking_list' %}"
                               hx-target="#bill-booking-list-container"
                               hx-trigger="change"
                               hx-indicator="#search-spinner">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-1">To Date</label>
                        <input type="date" name="to_date" value="{{ request.GET.to_date }}"
                               class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500"
                               hx-get="{% url 'accounts:bill_booking_list' %}"
                               hx-target="#bill-booking-list-container"
                               hx-trigger="change"
                               hx-indicator="#search-spinner">
                    </div>
                </div>
                
                <div class="flex items-center space-x-3" x-show="showAdvancedFilters">
                    <div class="flex-1">
                        <label class="block text-sm font-medium text-sap-gray-700 mb-1">Amount Range</label>
                        <div class="flex space-x-2">
                            <input type="number" name="min_amount" value="{{ request.GET.min_amount }}" placeholder="Min"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                            <input type="number" name="max_amount" value="{{ request.GET.max_amount }}" placeholder="Max"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                        </div>
                    </div>
                    
                    <div class="flex items-end space-x-3">
                        <button type="submit" class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="search" class="w-4 h-4 inline mr-2"></i>
                            Search
                        </button>
                        <a href="{% url 'accounts:bill_booking_list' %}" 
                           class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="x" class="w-4 h-4 inline mr-2"></i>
                            Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6" id="summary-cards">
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="file-text" class="w-5 h-5 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-sap-gray-600">Total Bills</p>
                    <p class="text-xl font-semibold text-sap-gray-800">{{ bill_bookings.count }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="clock" class="w-5 h-5 text-sap-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-sap-gray-600">Pending</p>
                    <p class="text-xl font-semibold text-sap-gray-800">{{ bill_bookings|length }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="check-circle" class="w-5 h-5 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-sap-gray-600">Authorized</p>
                    <p class="text-xl font-semibold text-sap-gray-800">0</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-red-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="x-circle" class="w-5 h-5 text-sap-red-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-sap-gray-600">Cancelled</p>
                    <p class="text-xl font-semibold text-sap-gray-800">0</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bill Booking List Container -->
    <div id="bill-booking-list-container">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-sap-gray-200">
                    <thead class="bg-sap-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Bill No</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Supplier</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Bill Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-sap-gray-200">
                        {% for bill_booking in bill_bookings %}
                        <tr class="hover:bg-sap-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-sap-gray-900">
                                {{ bill_booking.bill_no|default:"--" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-700">
                                {{ bill_booking.supplier_name|default:"--" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-700">
                                {{ bill_booking.bill_date|date:"d/m/Y"|default:"--" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-700">
                                ₹{{ bill_booking.total_amount|floatformat:2|default:"0.00" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if bill_booking.status == 'draft' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-gray-100 text-sap-gray-800">
                                        Draft
                                    </span>
                                {% elif bill_booking.status == 'submitted' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-orange-100 text-sap-orange-800">
                                        Submitted
                                    </span>
                                {% elif bill_booking.status == 'authorized' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-green-100 text-sap-green-800">
                                        Authorized
                                    </span>
                                {% elif bill_booking.status == 'cancelled' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-red-100 text-sap-red-800">
                                        Cancelled
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sap-gray-100 text-sap-gray-800">
                                        {{ bill_booking.status|default:"Unknown" }}
                                    </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-700">
                                <div class="flex items-center space-x-2">
                                    {% if bill_booking.status == 'submitted' %}
                                        <a href="{% url 'accounts:bill_booking_authorize' bill_booking.id %}" 
                                           class="text-sap-green-600 hover:text-sap-green-700" title="Authorize">
                                            <i data-lucide="check-circle" class="w-4 h-4"></i>
                                        </a>
                                    {% endif %}
                                    
                                    <a href="#" onclick="editBillBooking({{ bill_booking.id }})" 
                                       class="text-sap-blue-600 hover:text-sap-blue-700" title="Edit">
                                        <i data-lucide="edit" class="w-4 h-4"></i>
                                    </a>
                                    
                                    <a href="#" onclick="deleteBillBooking({{ bill_booking.id }})" 
                                       class="text-sap-red-600 hover:text-sap-red-700" title="Delete">
                                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center text-sap-gray-500">
                                <i data-lucide="folder-x" class="w-12 h-12 mx-auto mb-4 text-sap-gray-300"></i>
                                <p class="text-lg font-medium mb-2">No bill bookings found</p>
                                <p class="text-sm">Create your first bill booking to get started.</p>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
            <div class="bg-white px-4 py-3 border-t border-sap-gray-200 sm:px-6">
                <div class="flex items-center justify-between">
                    <div class="flex-1 flex justify-between sm:hidden">
                        {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-sap-gray-300 text-sm font-medium rounded-md text-sap-gray-700 bg-white hover:bg-sap-gray-50">Previous</a>
                        {% endif %}
                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-sap-gray-300 text-sm font-medium rounded-md text-sap-gray-700 bg-white hover:bg-sap-gray-50">Next</a>
                        {% endif %}
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-sap-gray-700">
                                Showing
                                <span class="font-medium">{{ page_obj.start_index }}</span>
                                to
                                <span class="font-medium">{{ page_obj.end_index }}</span>
                                of
                                <span class="font-medium">{{ paginator.count }}</span>
                                results
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                {% if page_obj.has_previous %}
                                    <a href="?page=1" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-500 hover:bg-sap-gray-50">
                                        <span class="sr-only">First</span>
                                        <i data-lucide="chevrons-left" class="w-5 h-5"></i>
                                    </a>
                                    <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-500 hover:bg-sap-gray-50">
                                        <span class="sr-only">Previous</span>
                                        <i data-lucide="chevron-left" class="w-5 h-5"></i>
                                    </a>
                                {% endif %}
                                
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <span class="relative inline-flex items-center px-4 py-2 border border-sap-green-500 bg-sap-green-50 text-sm font-medium text-sap-green-600">{{ num }}</span>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <a href="?page={{ num }}" class="relative inline-flex items-center px-4 py-2 border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-700 hover:bg-sap-gray-50">{{ num }}</a>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-2 py-2 border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-500 hover:bg-sap-gray-50">
                                        <span class="sr-only">Next</span>
                                        <i data-lucide="chevron-right" class="w-5 h-5"></i>
                                    </a>
                                    <a href="?page={{ paginator.num_pages }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-sap-gray-300 bg-white text-sm font-medium text-sap-gray-500 hover:bg-sap-gray-50">
                                        <span class="sr-only">Last</span>
                                        <i data-lucide="chevrons-right" class="w-5 h-5"></i>
                                    </a>
                                {% endif %}
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- HTMX -->
<script src="https://unpkg.com/htmx.org@1.9.10"></script>

<script>
function billBookingList() {
    return {
        showAdvancedFilters: false,
        
        toggleFilters() {
            this.showAdvancedFilters = !this.showAdvancedFilters;
        }
    }
}

function editBillBooking(billBookingId) {
    // Navigate to edit page
    window.location.href = `/accounts/invoices/bill-booking/${billBookingId}/edit/`;
}

function deleteBillBooking(billBookingId) {
    if (confirm('Are you sure you want to delete this bill booking? This action cannot be undone.')) {
        fetch(`/accounts/invoices/bill-booking/${billBookingId}/delete/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            },
        })
        .then(response => {
            if (response.ok) {
                location.reload();
                showNotification('Bill booking deleted successfully', 'success');
            } else {
                showNotification('Error deleting bill booking', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error deleting bill booking', 'error');
        });
    }
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-sap-green-600 text-white' :
        type === 'error' ? 'bg-sap-red-600 text-white' :
        'bg-sap-yellow-600 text-white'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// HTMX event listeners
document.addEventListener('htmx:afterRequest', function(event) {
    if (event.detail.successful) {
        lucide.createIcons();
    }
});

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}