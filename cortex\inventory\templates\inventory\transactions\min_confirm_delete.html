{% extends 'core/base.html' %}
{% load static %}

{% block title %}Delete MIN {{ object.min_no|default:object.id }}{% endblock %}

{% block content %}
<div class="bg-white shadow-sm">
    <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
        <div class="flex justify-between items-center">
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                    Delete Material Issue Note
                </h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                    Confirm deletion of MIN {{ object.min_no|default:object.id }}
                </p>
            </div>
            <a href="{% url 'inventory:min_detail' object.pk %}" 
               class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Cancel
            </a>
        </div>
    </div>

    <div class="px-4 py-5 sm:px-6">
        <div class="rounded-md bg-red-50 p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Warning</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <p>Are you sure you want to delete this Material Issue Note? This action cannot be undone.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- MIN Details -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <dl class="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
                <div>
                    <dt class="text-sm font-medium text-gray-500">MIN Number</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ object.min_no|default:"N/A" }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">MRS Number</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ object.mrs_no|default:"N/A" }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">System Date</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ object.sysdate|default:"N/A" }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Session ID</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ object.sessionid|default:"N/A" }}</dd>
                </div>
            </dl>
        </div>

        <!-- Confirmation Form -->
        <form method="post">
            {% csrf_token %}
            <div class="flex justify-end space-x-3">
                <a href="{% url 'inventory:min_detail' object.pk %}" 
                   class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Cancel
                </a>
                <button type="submit" 
                        class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Delete MIN
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}