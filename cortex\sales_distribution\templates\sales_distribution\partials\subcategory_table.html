<!-- Sub-Category Table with HTMX inline editing -->
<div class="overflow-hidden">
    {% if subcategories %}
    <table class="min-w-full divide-y divide-sap-gray-200">
        <thead class="bg-sap-gray-50">
            <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    Sub-Category ID
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    Parent Category
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    Sub-Category Name
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    Created By
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    Created Date
                </th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                    Actions
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-sap-gray-200">
            {% for subcategory in subcategories %}
            <tr id="subcategory-row-{{ subcategory.sub_cid }}" class="hover:bg-sap-gray-50 transition-colors duration-150">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-sap-gray-900">
                    {{ subcategory.sub_cid }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">
                    <div class="flex items-center">
                        <div class="w-2 h-2 bg-sap-blue-500 rounded-full mr-2"></div>
                        {{ subcategory.cid.cname|default:"No Category" }}
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-sap-gray-900">
                    {{ subcategory.sub_c_name }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-600">
                    {{ subcategory.sessionid|default:"System" }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-600">
                    {{ subcategory.sysdate|default:"N/A" }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex items-center justify-end space-x-2">
                        <a href="{% url 'sales_distribution:subcategory_edit' subcategory.sub_cid %}" 
                           class="inline-flex items-center px-3 py-1.5 border border-sap-blue-300 rounded-md text-xs font-medium text-sap-blue-700 bg-sap-blue-50 hover:bg-sap-blue-100 transition-colors duration-150">
                            <i data-lucide="edit" class="w-3 h-3 mr-1"></i>
                            Edit
                        </a>
                        <a href="{% url 'sales_distribution:subcategory_delete' subcategory.sub_cid %}"
                           onclick="return confirmDelete()"
                           class="inline-flex items-center px-3 py-1.5 border border-red-300 rounded-md text-xs font-medium text-red-700 bg-red-50 hover:bg-red-100 transition-colors duration-150">
                            <i data-lucide="trash-2" class="w-3 h-3 mr-1"></i>
                            Delete
                        </a>
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div class="px-6 py-12 text-center">
        <div class="w-16 h-16 mx-auto mb-4 bg-sap-gray-100 rounded-full flex items-center justify-center">
            <i data-lucide="git-branch" class="w-8 h-8 text-sap-gray-400"></i>
        </div>
        <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No Sub-Categories Found</h3>
        <p class="text-sm text-sap-gray-600 mb-6">Create your first work order sub-category to get started.</p>
        <button type="button" 
                onclick="showForm()"
                class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-sap-purple-600 hover:bg-sap-purple-700">
            <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
            Add First Sub-Category
        </button>
    </div>
    {% endif %}
</div>