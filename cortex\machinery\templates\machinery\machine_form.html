{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{{ title|default:"Machine Form" }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-2xl font-semibold text-gray-900">{{ title|default:"Machine Form" }}</h1>
        <p class="mt-2 text-sm text-gray-700">Fill in the machine details below.</p>
    </div>

    <!-- Form -->
    <form method="post" enctype="multipart/form-data" class="space-y-8">
        {% csrf_token %}
        
        <!-- Basic Information -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Basic Information</h3>
                
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <!-- Item Selection for Create -->
                    {% if available_items %}
                    <div class="sm:col-span-2">
                        <label class="block text-sm font-medium text-gray-700">Select Item</label>
                        <select name="itemid" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <option value="">Select an item...</option>
                            {% for item in available_items %}
                            <option value="{{ item.id }}" {% if form.itemid.value == item.id %}selected{% endif %}>
                                {{ item.itemcode }} - {{ item.description }}
                            </option>
                            {% endfor %}
                        </select>
                        {% if form.itemid.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.itemid.errors.0 }}</p>
                        {% endif %}
                    </div>
                    {% endif %}
                    
                    <div>
                        <label for="{{ form.make.id_for_label }}" class="block text-sm font-medium text-gray-700">Make</label>
                        {{ form.make|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.make.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.make.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.model.id_for_label }}" class="block text-sm font-medium text-gray-700">Model</label>
                        {{ form.model|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.model.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.model.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.capacity.id_for_label }}" class="block text-sm font-medium text-gray-700">Capacity</label>
                        {{ form.capacity|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.capacity.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.capacity.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.location.id_for_label }}" class="block text-sm font-medium text-gray-700">Location</label>
                        {{ form.location|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.location.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.location.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Purchase & Financial Information -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Purchase & Financial Information</h3>
                
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label for="{{ form.purchasedate.id_for_label }}" class="block text-sm font-medium text-gray-700">Purchase Date</label>
                        {{ form.purchasedate|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.purchasedate.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.purchasedate.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.cost.id_for_label }}" class="block text-sm font-medium text-gray-700">Cost</label>
                        {{ form.cost|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.cost.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.cost.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.suppliername.id_for_label }}" class="block text-sm font-medium text-gray-700">Supplier</label>
                        {{ form.suppliername|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.suppliername.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.suppliername.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.receiveddate.id_for_label }}" class="block text-sm font-medium text-gray-700">Received Date</label>
                        {{ form.receiveddate|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.receiveddate.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.receiveddate.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Warranty & Insurance Information -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Warranty & Insurance Information</h3>
                
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label for="{{ form.warrantyexpirydate.id_for_label }}" class="block text-sm font-medium text-gray-700">Warranty Expiry Date</label>
                        {{ form.warrantyexpirydate|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.warrantyexpirydate.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.warrantyexpirydate.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.lifedate.id_for_label }}" class="block text-sm font-medium text-gray-700">Life Date</label>
                        {{ form.lifedate|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.lifedate.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.lifedate.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.insurance.id_for_label }}" class="block text-sm font-medium text-gray-700">Insurance</label>
                        {{ form.insurance|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.insurance.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.insurance.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div id="insurance-fields">
                        {% if form.insurance.value == 1 %}
                        <div>
                            <label for="{{ form.insuranceexpirydate.id_for_label }}" class="block text-sm font-medium text-gray-700">Insurance Expiry Date</label>
                            {{ form.insuranceexpirydate|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                            {% if form.insuranceexpirydate.errors %}
                                <p class="mt-2 text-sm text-red-600">{{ form.insuranceexpirydate.errors.0 }}</p>
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Maintenance & Operations -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Maintenance & Operations</h3>
                
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label for="{{ form.puttouse.id_for_label }}" class="block text-sm font-medium text-gray-700">Put to Use Date</label>
                        {{ form.puttouse|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.puttouse.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.puttouse.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.pmdays.id_for_label }}" class="block text-sm font-medium text-gray-700">Preventive Maintenance Days</label>
                        {{ form.pmdays|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.pmdays.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.pmdays.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.incharge.id_for_label }}" class="block text-sm font-medium text-gray-700">Person In-charge</label>
                        {{ form.incharge|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                        {% if form.incharge.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.incharge.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.filedata.id_for_label }}" class="block text-sm font-medium text-gray-700">Attachment</label>
                        {{ form.filedata|add_class:"mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100" }}
                        {% if form.filedata.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.filedata.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3">
            <a href="{% url 'machinery:machine_list' %}" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Cancel
            </a>
            <button type="submit" 
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Save Machine
            </button>
        </div>
    </form>
</div>

<script>
// Handle insurance field visibility
document.addEventListener('DOMContentLoaded', function() {
    const insuranceSelect = document.getElementById('{{ form.insurance.id_for_label }}');
    const insuranceFields = document.getElementById('insurance-fields');
    
    function toggleInsuranceFields() {
        if (insuranceSelect.value === '1') {
            insuranceFields.innerHTML = `
                <div>
                    <label for="{{ form.insuranceexpirydate.id_for_label }}" class="block text-sm font-medium text-gray-700">Insurance Expiry Date</label>
                    {{ form.insuranceexpirydate|add_class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" }}
                </div>
            `;
        } else {
            insuranceFields.innerHTML = '';
        }
    }
    
    insuranceSelect.addEventListener('change', toggleInsuranceFields);
});
</script>
{% endblock %}