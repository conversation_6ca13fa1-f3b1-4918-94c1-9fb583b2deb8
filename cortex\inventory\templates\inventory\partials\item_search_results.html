{% if results %}
    <div class="space-y-2">
        <div class="text-sm text-gray-600 mb-2">
            Found {{ total_results }} item{{ total_results|pluralize }}{% if query %} for "{{ query }}"{% endif %}
        </div>
        {% for item in results %}
            <div class="border border-gray-200 rounded-lg p-3 hover:bg-gray-50 cursor-pointer"
                 onclick="selectItem({
                     id: {{ item.id }},
                     item_code: '{{ item.item_code|escapejs }}',
                     description: '{{ item.description|escapejs }}',
                     available_stock: {{ item.available_stock }},
                     category_name: '{{ item.category_name|escapejs }}',
                     uom: '{{ item.uom|default:"PCS"|escapejs }}',
                     stock_status: '{{ item.stock_status|escapejs }}'
                 })">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <span class="font-medium text-gray-900">{{ item.item_code }}</span>
                            <span class="text-xs px-2 py-1 rounded-full bg-blue-100 text-blue-800">{{ item.category_name }}</span>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">{{ item.description }}</p>
                        <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                            <span>UOM: {{ item.uom|default:"PCS" }}</span>
                            {% if item.location %}
                                <span>Location: {{ item.location }}</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="text-right">
                        {% if item.stock_status == 'out_of_stock' %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Out of Stock
                            </span>
                        {% elif item.stock_status == 'low_stock' %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                Low Stock
                            </span>
                        {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                In Stock
                            </span>
                        {% endif %}
                        <div class="text-sm font-medium text-gray-900 mt-1">
                            Stock: {{ item.available_stock }}
                        </div>
                        {% if item.stock_warning %}
                            <div class="text-xs text-red-600 mt-1">
                                {{ item.stock_warning }}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% elif query and query|length >= 2 %}
    <div class="text-center py-8">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No items found</h3>
        <p class="mt-1 text-sm text-gray-500">
            No items match your search "{{ query }}". Try adjusting your search terms or category filter.
        </p>
    </div>
{% else %}
    <div class="text-sm text-gray-500 text-center py-4">
        Type at least 2 characters to search for items
    </div>
{% endif %}