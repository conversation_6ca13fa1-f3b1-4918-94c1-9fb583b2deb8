from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import datetime
from ..models import ClosingStock


class ClosingStockForm(forms.ModelForm):
    """Form for creating and editing Closing Stock records"""
    
    # Use date fields for better UX
    from_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'sap-input',
            'placeholder': 'From Date'
        }),
        help_text="Start date of the closing period"
    )
    
    to_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'sap-input',
            'placeholder': 'To Date'
        }),
        help_text="End date of the closing period"
    )
    
    closing_stock_value = forms.DecimalField(
        max_digits=15,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'sap-input',
            'placeholder': '0.00',
            'step': '0.01',
            'min': '0'
        }),
        help_text="Closing stock value for the period"
    )
    
    class Meta:
        model = ClosingStock
        fields = []  # We'll handle the mapping manually
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # If editing an existing record, populate the form fields
        if self.instance and self.instance.pk:
            # Convert string dates to date objects for display
            if self.instance.fromdt:
                try:
                    self.fields['from_date'].initial = datetime.strptime(self.instance.fromdt, '%d-%m-%Y').date()
                except (ValueError, TypeError):
                    pass
            
            if self.instance.todt:
                try:
                    self.fields['to_date'].initial = datetime.strptime(self.instance.todt, '%d-%m-%Y').date()
                except (ValueError, TypeError):
                    pass
            
            if self.instance.clstock:
                self.fields['closing_stock_value'].initial = self.instance.clstock
    
    def clean(self):
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')
        closing_stock_value = cleaned_data.get('closing_stock_value')
        
        # Validate date range
        if from_date and to_date:
            if from_date > to_date:
                raise ValidationError("From date must be before or equal to the To date.")
            
            # Check if from_date is not too far in the future
            if from_date > timezone.now().date():
                raise ValidationError("From date cannot be in the future.")
        
        # Validate closing stock value
        if closing_stock_value is not None and closing_stock_value < 0:
            raise ValidationError("Closing stock value cannot be negative.")
        
        return cleaned_data
    
    def clean_from_date(self):
        from_date = self.cleaned_data.get('from_date')
        if not from_date:
            raise ValidationError("From date is required.")
        return from_date
    
    def clean_to_date(self):
        to_date = self.cleaned_data.get('to_date')
        if not to_date:
            raise ValidationError("To date is required.")
        return to_date
    
    def clean_closing_stock_value(self):
        closing_stock_value = self.cleaned_data.get('closing_stock_value')
        if closing_stock_value is None:
            raise ValidationError("Closing stock value is required.")
        if closing_stock_value < 0:
            raise ValidationError("Closing stock value must be a positive number.")
        return closing_stock_value
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        
        # Map form fields to model fields
        from_date = self.cleaned_data.get('from_date')
        to_date = self.cleaned_data.get('to_date')
        closing_stock_value = self.cleaned_data.get('closing_stock_value')
        
        # Convert date objects to string format expected by legacy database
        if from_date:
            instance.fromdt = from_date.strftime('%d-%m-%Y')
        
        if to_date:
            instance.todt = to_date.strftime('%d-%m-%Y')
        
        if closing_stock_value is not None:
            instance.clstock = float(closing_stock_value)
        
        if commit:
            instance.save()
        
        return instance


class ClosingStockSearchForm(forms.Form):
    """Search form for Closing Stock records"""
    
    search = forms.CharField(
        required=False,
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Search by date range or stock value...',
            'hx-get': '/inventory/closing-stocks/',
            'hx-trigger': 'keyup changed delay:300ms',
            'hx-target': '#closing-stock-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    from_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'sap-input',
            'hx-get': '/inventory/closing-stocks/',
            'hx-trigger': 'change',
            'hx-target': '#closing-stock-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    to_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'sap-input',
            'hx-get': '/inventory/closing-stocks/',
            'hx-trigger': 'change',
            'hx-target': '#closing-stock-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    min_value = forms.DecimalField(
        required=False,
        max_digits=15,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Min value',
            'step': '0.01',
            'min': '0',
            'hx-get': '/inventory/closing-stocks/',
            'hx-trigger': 'change',
            'hx-target': '#closing-stock-results',
            'hx-swap': 'innerHTML'
        })
    )
    
    max_value = forms.DecimalField(
        required=False,
        max_digits=15,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Max value',
            'step': '0.01',
            'min': '0',
            'hx-get': '/inventory/closing-stocks/',
            'hx-trigger': 'change',
            'hx-target': '#closing-stock-results',
            'hx-swap': 'innerHTML'
        })
    )