{% extends "core/base.html" %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-6 py-8" x-data="enquiryList()">
    <!-- Header Section -->
    <div class="bg-gradient-to-br from-blue-600 to-blue-800 text-white py-8 px-8 mb-6 rounded-xl shadow-lg">
        <div class="text-center">
            <h1 class="text-3xl font-bold mb-2">{{ page_title }}</h1>
            <p class="text-blue-100">{{ page_subtitle }}</p>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div class="bg-white rounded-xl p-5 shadow-sm border border-gray-200 text-center hover:-translate-y-1 transition-transform duration-200">
            <span class="text-3xl block mb-2">📊</span>
            <div class="text-2xl font-bold text-gray-900">{{ stats.total_enquiries }}</div>
            <div class="text-sm text-gray-500 uppercase tracking-wide font-medium mt-1">Total Enquiries</div>
        </div>
        
        <div class="bg-white rounded-xl p-5 shadow-sm border border-gray-200 text-center hover:-translate-y-1 transition-transform duration-200">
            <span class="text-3xl block mb-2">🆕</span>
            <div class="text-2xl font-bold text-gray-900">{{ stats.new_enquiries }}</div>
            <div class="text-sm text-gray-500 uppercase tracking-wide font-medium mt-1">New Enquiries</div>
        </div>
        
        <div class="bg-white rounded-xl p-5 shadow-sm border border-gray-200 text-center hover:-translate-y-1 transition-transform duration-200">
            <span class="text-3xl block mb-2">💰</span>
            <div class="text-2xl font-bold text-gray-900">{{ stats.quoted_enquiries }}</div>
            <div class="text-sm text-gray-500 uppercase tracking-wide font-medium mt-1">Quoted</div>
        </div>
        
        <div class="bg-white rounded-xl p-5 shadow-sm border border-gray-200 text-center hover:-translate-y-1 transition-transform duration-200">
            <span class="text-3xl block mb-2">✅</span>
            <div class="text-2xl font-bold text-gray-900">{{ stats.converted_enquiries }}</div>
            <div class="text-sm text-gray-500 uppercase tracking-wide font-medium mt-1">Converted</div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6 overflow-hidden">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h3 class="flex items-center gap-2 font-semibold text-gray-900">
                <span>🔍</span>
                Search & Filters
            </h3>
        </div>
        <div class="p-6">
            <form method="get" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 items-end">
                <div class="flex flex-col gap-1.5">
                    <label for="{{ filter_form.search.id_for_label }}" class="text-sm font-medium text-gray-700">Search</label>
                    <input type="text" name="search" value="{{ request.GET.search|default:'' }}" 
                           class="px-3 py-2 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                           placeholder="Search enquiries...">
                </div>
                
                <div class="flex flex-col gap-1.5">
                    <label for="{{ filter_form.status.id_for_label }}" class="text-sm font-medium text-gray-700">Status</label>
                    {{ filter_form.status }}
                </div>
                
                <div class="flex flex-col gap-1.5">
                    <label for="{{ filter_form.country.id_for_label }}" class="text-sm font-medium text-gray-700">Country</label>
                    {{ filter_form.country }}
                </div>
                
                <div class="flex flex-col gap-1.5">
                    <label for="{{ filter_form.date_from.id_for_label }}" class="text-sm font-medium text-gray-700">From Date</label>
                    {{ filter_form.date_from }}
                </div>
                
                <div class="flex flex-col gap-1.5">
                    <label for="{{ filter_form.date_to.id_for_label }}" class="text-sm font-medium text-gray-700">To Date</label>
                    {{ filter_form.date_to }}
                </div>
                
                <div>
                    <button type="submit" class="w-full bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-2 rounded-lg font-medium hover:-translate-y-0.5 hover:shadow-lg transition-all duration-200 flex items-center justify-center gap-2">
                        🔍 Apply Filters
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Data Table -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="bg-gray-50 px-6 py-5 border-b border-gray-200 flex justify-between items-center">
            <h3 class="flex items-center gap-2 font-semibold text-gray-900">
                <span>📋</span>
                Enquiries List
            </h3>
            <a href="{% url 'sales_distribution:enquiry_create' %}" 
               class="bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-2 rounded-lg font-medium hover:-translate-y-0.5 hover:shadow-lg transition-all duration-200 flex items-center gap-2">
                ➕ Create New Enquiry
            </a>
        </div>
        
        <div class="overflow-x-auto" id="enquiry-table">
            {% include "sales_distribution/enquiry/partials/enquiry_table.html" %}
        </div>
    </div>
</div>

<script>
function enquiryList() {
    return {
        filters: {
            search: '{{ request.GET.search|default:"" }}',
            status: '{{ request.GET.status|default:"" }}',
            country: '{{ request.GET.country|default:"" }}',
            dateFrom: '{{ request.GET.date_from|default:"" }}',
            dateTo: '{{ request.GET.date_to|default:"" }}'
        },

        applyFilters() {
            // Build URL with current filters
            const params = new URLSearchParams();
            
            if (this.filters.search) params.set('search', this.filters.search);
            if (this.filters.status) params.set('status', this.filters.status);
            if (this.filters.country) params.set('country', this.filters.country);
            if (this.filters.dateFrom) params.set('date_from', this.filters.dateFrom);
            if (this.filters.dateTo) params.set('date_to', this.filters.dateTo);
            
            // Update URL and trigger HTMX request
            const url = `${window.location.pathname}?${params.toString()}`;
            
            // Use HTMX to update table
            htmx.ajax('GET', url, {
                target: '#enquiry-table',
                swap: 'innerHTML'
            });
            
            // Update browser URL without reload
            history.pushState(null, '', url);
        },

        clearFilters() {
            this.filters = {
                search: '',
                status: '',
                country: '',
                dateFrom: '',
                dateTo: ''
            };
            this.applyFilters();
        }
    }
}
</script>
{% endblock %}
