from django.db import models


class ProjectPlanningMaster(models.Model):
    """Main project planning document storage"""
    id = models.AutoField(db_column='Id', primary_key=True)
    sys_date = models.TextField(db_column='SysDate', blank=True, null=True)
    sys_time = models.TextField(db_column='SysTime', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    session_id = models.TextField(db_column='SessionId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    wo_no = models.TextField(db_column='WONo', blank=True, null=True)
    file_name = models.TextField(db_column='FileName', blank=True, null=True)
    file_size = models.TextField(db_column='FileSize', blank=True, null=True)
    content_type = models.TextField(db_column='ContentType', blank=True, null=True)
    file_data = models.BinaryField(db_column='FileData', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblPM_ProjectPlanning_Master'

    def __str__(self):
        return f"Project Plan - WO: {self.wo_no}"


class ProjectPlanningDesign(models.Model):
    """Design activities planning"""
    id = models.IntegerField(db_column='Id', primary_key=True)
    activities = models.TextField(db_column='Activities', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblPM_ProjectPlanning_Design'

    def __str__(self):
        return f"Design Activity {self.id}: {self.activities[:50] if self.activities else 'N/A'}"


class ProjectPlanningDesigner(models.Model):
    """Designer-specific project planning"""
    id = models.IntegerField(db_column='Id', primary_key=True)
    name_proj = models.TextField(db_column='Name_Proj', blank=True, null=True)
    wo_no = models.TextField(db_column='Wo_No', blank=True, null=True)
    no_fix_rqu = models.TextField(db_column='No_Fix_Rqu', blank=True, null=True)
    des_lea = models.TextField(db_column='Des_Lea', blank=True, null=True)
    des_mem = models.TextField(db_column='Des_Mem', blank=True, null=True)
    sr_no = models.TextField(db_column='Sr_No', blank=True, null=True)
    name_act = models.TextField(db_column='Name_Act', blank=True, null=True)
    rev_no = models.TextField(db_column='Rev_No', blank=True, null=True)
    no_days = models.TextField(db_column='No_Days', blank=True, null=True)
    as_plan_from = models.TextField(db_column='As_Plan_From', blank=True, null=True)
    as_plan_to = models.TextField(db_column='As_Plan_To', blank=True, null=True)
    ac_from = models.TextField(db_column='Ac_From', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblPM_ProjectPlanning_Designer'

    def __str__(self):
        return f"Designer Plan - {self.name_proj} (WO: {self.wo_no})"


class ProjectManufacturingPlanDetail(models.Model):
    """Manufacturing planning details"""
    id = models.IntegerField(db_column='Id', primary_key=True)
    prjctno = models.TextField(db_column='PRJCTNO', blank=True, null=True)
    item_code = models.TextField(db_column='ItemCode', blank=True, null=True)
    description = models.TextField(db_column='Description', blank=True, null=True)
    uom = models.TextField(db_column='UOM', blank=True, null=True)
    bomq = models.TextField(db_column='BOMQ', blank=True, null=True)
    design = models.TextField(db_column='Design', blank=True, null=True)
    vendor_plan_date = models.TextField(db_column='VendorPlanDate', blank=True, null=True)
    vendor_act = models.TextField(db_column='VendorAct', blank=True, null=True)
    wo_no = models.TextField(db_column='WONo', blank=True, null=True)
    vendor_plan = models.TextField(db_column='VendorPlan', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblPM_Project_Manufacturing_Plan_Detail'

    def __str__(self):
        return f"Manufacturing Plan - {self.item_code}: {self.description[:30] if self.description else 'N/A'}"


class ProjectManufacturingAssemblyMaster(models.Model):
    """Manufacturing assembly planning master"""
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.TextField(db_column='SysDate', blank=True, null=True)
    sys_time = models.TextField(db_column='SysTime', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    session_id = models.TextField(db_column='SessionId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    wo_no = models.TextField(db_column='WONo', blank=True, null=True)
    assembly_date = models.TextField(db_column='AssemblyDate', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblPM_Project_Manufacturing_Assemly_Master'

    def __str__(self):
        return f"Assembly Master - WO: {self.wo_no} ({self.assembly_date})"


class ProjectManufacturingAssemblyDetail(models.Model):
    """Manufacturing assembly planning detail"""
    id = models.IntegerField(db_column='Id', primary_key=True)
    m_id = models.IntegerField(db_column='MId', blank=True, null=True)
    fixture_no = models.TextField(db_column='FixtureNo', blank=True, null=True)
    item_no = models.TextField(db_column='ItemNo', blank=True, null=True)
    description = models.TextField(db_column='Description', blank=True, null=True)
    qty = models.TextField(db_column='Qty', blank=True, null=True)
    detailing = models.TextField(db_column='Detailing', blank=True, null=True)
    tpl_entry = models.TextField(db_column='TplEntry', blank=True, null=True)
    flame_cut = models.TextField(db_column='FlameCut', blank=True, null=True)
    c_flame_cut = models.TextField(db_column='CFlameCut', blank=True, null=True)
    channel = models.TextField(db_column='Channlel', blank=True, null=True)
    list_field = models.TextField(db_column='List', blank=True, null=True)
    receive = models.TextField(db_column='Receive', blank=True, null=True)
    fabrication = models.TextField(db_column='Fabrication', blank=True, null=True)
    c_sr = models.TextField(db_column='CSR', blank=True, null=True)
    mc_ing = models.TextField(db_column='MCIng', blank=True, null=True)
    tapping = models.TextField(db_column='Tapping', blank=True, null=True)
    painting = models.TextField(db_column='Painting', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblPM_Project_Manufacturing_Assemly_Detail'

    def __str__(self):
        return f"Assembly Detail - {self.fixture_no}: {self.item_no}"


class ProjectVendorPlanDetail(models.Model):
    """Vendor planning and tracking"""
    id = models.IntegerField(db_column='Id', primary_key=True)
    m_id = models.IntegerField(db_column='MId', blank=True, null=True)
    sr_no = models.TextField(db_column='SrNo', blank=True, null=True)
    fixture_no = models.TextField(db_column='FixtureNo', blank=True, null=True)
    no_parts_manufacturing = models.TextField(db_column='NoPartsManufacturing', blank=True, null=True)
    planning = models.TextField(db_column='Planning', blank=True, null=True)
    flame_cut_loading = models.TextField(db_column='FlameCutLoading', blank=True, null=True)
    premach_ineing = models.TextField(db_column='PremachIneing', blank=True, null=True)
    weldment_fabrication = models.TextField(db_column='WeldmentFabrication', blank=True, null=True)
    weldment_loading = models.TextField(db_column='WeldmentLoading', blank=True, null=True)
    no_parts_received = models.TextField(db_column='NoPartsReceived', blank=True, null=True)
    no_accepted_parts = models.TextField(db_column='NoAcceptedParts', blank=True, null=True)
    pending_mfg_parts = models.TextField(db_column='PendingMfgParts', blank=True, null=True)
    brought_parts = models.TextField(db_column='BroughoutParts', blank=True, null=True)
    pending_bo_parts = models.TextField(db_column='PendingBOParts', blank=True, null=True)
    no_pending_challan = models.TextField(db_column='NoPendingChallan', blank=True, null=True)
    no_parts_received_after_processing = models.TextField(db_column='NoPartsReceivedAfterProcessing', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblPM_Project_Vendor_Plan_Detail'

    def __str__(self):
        return f"Vendor Plan - {self.fixture_no} (Sr: {self.sr_no})"


class ManPowerPlanning(models.Model):
    """Daily workforce planning and tracking"""
    id = models.AutoField(db_column='Id', primary_key=True)
    sys_date = models.TextField(db_column='SysDate', blank=True, null=True)
    sys_time = models.TextField(db_column='SysTime', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    session_id = models.TextField(db_column='SessionId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    emp_id = models.IntegerField(db_column='EmpId', blank=True, null=True)
    date = models.TextField(db_column='Date', blank=True, null=True)
    wo_no = models.TextField(db_column='WONo', blank=True, null=True)
    dept = models.TextField(db_column='Dept', blank=True, null=True)
    types = models.TextField(db_column='Types', blank=True, null=True)
    amendment_no = models.IntegerField(db_column='AmendmentNo', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblPM_ManPowerPlanning'

    def __str__(self):
        return f"ManPower Plan - Emp: {self.emp_id}, WO: {self.wo_no} ({self.date})"


class ManPowerPlanningDetails(models.Model):
    """Detailed activity tracking per employee"""
    id = models.AutoField(db_column='Id', primary_key=True)
    m_id = models.IntegerField(db_column='MId', blank=True, null=True)
    equip_id = models.IntegerField(db_column='EquipId', blank=True, null=True)
    category = models.TextField(db_column='Category', blank=True, null=True)
    sub_category = models.TextField(db_column='SubCategory', blank=True, null=True)
    planned_desc = models.TextField(db_column='PlannedDesc', blank=True, null=True)
    actual_desc = models.TextField(db_column='ActualDesc', blank=True, null=True)
    hour = models.TextField(db_column='Hour', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblPM_ManPowerPlanning_Details'

    def __str__(self):
        return f"ManPower Detail - {self.category}: {self.planned_desc[:30] if self.planned_desc else 'N/A'}"


class ProjectPlanningMainSheet(models.Model):
    """Main project tracking sheet"""
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.TextField(db_column='SysDate', blank=True, null=True)
    sys_time = models.TextField(db_column='SysTime', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    session_id = models.TextField(db_column='SessionId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    project_no = models.TextField(db_column='ProjectNo', blank=True, null=True)
    project_leader = models.TextField(db_column='ProjectLeader', blank=True, null=True)
    customer_name = models.TextField(db_column='CustomerName', blank=True, null=True)
    project_title = models.TextField(db_column='ProjectTitle', blank=True, null=True)
    activity_1 = models.TextField(db_column='Activity1', blank=True, null=True)
    activity_2 = models.TextField(db_column='Activity2', blank=True, null=True)
    activity_3 = models.TextField(db_column='Activity3', blank=True, null=True)
    activity_4 = models.TextField(db_column='Activity4', blank=True, null=True)
    activity_5 = models.TextField(db_column='Activity5', blank=True, null=True)
    delay_reason = models.TextField(db_column='DelayReason', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblPM_ProjectPlanning_MainSheet'

    def __str__(self):
        return f"Project Main Sheet - {self.project_no}: {self.project_title[:30] if self.project_title else 'N/A'}"


class ProjectStatus(models.Model):
    """Overall project status tracking"""
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.TextField(db_column='SysDate', blank=True, null=True)
    sys_time = models.TextField(db_column='SysTime', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    session_id = models.TextField(db_column='SessionId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    wo_no = models.TextField(db_column='WONo', blank=True, null=True)
    activity = models.TextField(db_column='Activity', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblPM_ProjectStatus'

    def __str__(self):
        return f"Project Status - WO: {self.wo_no}, Activity: {self.activity[:30] if self.activity else 'N/A'}"


# Additional models for the daily reporting tracker based on ASP.NET code

class DailyReportingTracker(models.Model):
    """Daily Reporting Tracker System - equivalent to DRT_Sys_New table"""
    id = models.AutoField(primary_key=True)
    sys_date = models.DateTimeField(auto_now_add=True)
    sys_time = models.TimeField(auto_now_add=True)
    comp_id = models.IntegerField(blank=True, null=True)
    session_id = models.CharField(max_length=255, blank=True, null=True)
    fin_year_id = models.IntegerField(blank=True, null=True)
    
    # Employee information
    employee_name = models.CharField(max_length=255)
    designation = models.CharField(max_length=255)
    department = models.CharField(max_length=255)
    date_of_reporting = models.DateField()
    
    # Weekly reporting fields
    significant_achievements_last_week = models.TextField()
    activities_task_current_week = models.TextField()
    activities_planned_completed = models.TextField()
    activities_planned_not_completed = models.TextField()
    activities_unplanned_completed = models.TextField()
    plan_next_week = models.TextField()
    
    # Daily activity tracking
    activity_date = models.DateField()
    wo_number = models.CharField(max_length=50)
    activity = models.TextField()
    estimated_time = models.CharField(max_length=50)
    status = models.CharField(max_length=255)
    percentage_completed = models.IntegerField(default=0)
    remarks = models.TextField()

    class Meta:
        managed = False
        db_table = 'daily_reporting_tracker'
        ordering = ['-sys_date']

    def __str__(self):
        return f"Daily Report - {self.employee_name} ({self.date_of_reporting})"


class DesignPlan(models.Model):
    """Design Plan - Maps to tblPM_ProjectPlanning_Design table"""
    id = models.AutoField(db_column="Id", primary_key=True)
    activities = models.TextField(db_column="Activities")

    class Meta:
        managed = False
        db_table = "tblPM_ProjectPlanning_Design"

    def __str__(self):
        return f"Design Plan {self.id} - {self.activities[:50]}"


class ManufacturingPlan(models.Model):
    """Manufacturing Plan - equivalent to DRTS_Manufacturing_Plan_New table"""
    id = models.AutoField(primary_key=True)
    sys_date = models.DateTimeField(auto_now_add=True)
    wo_number = models.CharField(max_length=50)
    
    # Item Details
    fixture_number = models.CharField(max_length=50)
    item_number = models.CharField(max_length=100)
    description = models.TextField()
    quantity = models.CharField(max_length=50)
    
    # Drawing Release
    detailing = models.CharField(max_length=255)
    tpl_entry = models.CharField(max_length=255)
    flame_cut = models.CharField(max_length=255)
    
    # Manufacturing Process
    cutting_flame_cut = models.CharField(max_length=255)
    channel = models.CharField(max_length=255)
    raw_material_list = models.CharField(max_length=255)
    raw_material_receive = models.CharField(max_length=255)
    fabrication = models.CharField(max_length=255)
    sr = models.CharField(max_length=255)
    machining = models.CharField(max_length=255)
    tapping = models.CharField(max_length=255)
    painting = models.CharField(max_length=255)

    class Meta:
        db_table = 'manufacturing_plan'
        ordering = ['-sys_date']

    def __str__(self):
        return f"Manufacturing Plan - WO: {self.wo_number}, Item: {self.item_number}"


class VendorPlan(models.Model):
    """Vendor Plan - equivalent to DRTS_VENDOR_PLAN table"""
    id = models.AutoField(primary_key=True)
    sys_date = models.DateTimeField(auto_now_add=True)
    wo_number = models.CharField(max_length=50)
    
    # Basic Information
    serial_number = models.CharField(max_length=50)
    fixture_number = models.CharField(max_length=50)
    number_parts_manufacturing = models.CharField(max_length=50)
    planning = models.CharField(max_length=255)
    flame_cut_loading = models.CharField(max_length=255)
    premach_ineing = models.CharField(max_length=255)
    weldment_fabrication = models.CharField(max_length=255)
    
    # Processing Information
    weldment_loading = models.CharField(max_length=255)
    number_parts_received = models.CharField(max_length=50)
    number_accepted_parts = models.CharField(max_length=50)
    pending_mfg_parts = models.CharField(max_length=50)
    brought_parts = models.CharField(max_length=50)
    pending_bo_parts = models.CharField(max_length=50)
    
    # Final Processing
    number_pending_challan = models.CharField(max_length=50)
    number_parts_received_after_processing = models.CharField(max_length=50)

    class Meta:
        db_table = 'vendor_plan'
        ordering = ['-sys_date']

    def __str__(self):
        return f"Vendor Plan - WO: {self.wo_number}, Fixture: {self.fixture_number}"