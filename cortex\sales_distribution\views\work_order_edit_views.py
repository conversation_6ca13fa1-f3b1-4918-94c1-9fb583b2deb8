"""
Work Order Edit Views - Comprehensive implementation matching ASP.NET functionality
Replaces Module/SalesDistribution/Transactions/WorkOrder_Edit*.aspx
"""
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView
from django.views import View
from django.contrib import messages
from django.http import JsonResponse
from django.db import transaction, connection
from datetime import datetime

from ..models import (
    WorkOrder, WorkOrderCategory
)
from ..forms.work_order_forms import (
    WorkOrderCreateForm, WorkOrderShippingForm, WorkOrderProductForm, 
    WorkOrderInstructionsForm
)


class WorkOrderEditListView(LoginRequiredMixin, ListView):
    """
    Work Order Edit List View - matches ASP.NET WorkOrder_Edit.aspx exactly
    Shows search interface and list of work orders available for editing
    """
    model = WorkOrder
    template_name = 'sales_distribution/work_order_edit_list.html'
    context_object_name = 'work_orders'
    paginate_by = 20  # Match ASP.NET PageSize="20"

    def get_queryset(self):
        """Filter work orders based on search criteria - matches ASP.NET logic"""
        # Get current financial year and company ID from session
        finyear_id = self.request.session.get('finyear', 13)
        comp_id = self.request.session.get('compid', 1)
        
        # Build complex query similar to ASP.NET logic
        search_type = self.request.GET.get('search_by', '0')
        search_value = self.request.GET.get('search_value', '')
        search_text = self.request.GET.get('search_text', '')
        wo_category = self.request.GET.get('wo_category', '')
        
        # Base queryset - only work orders not closed (CloseOpen='0' means open)
        queryset = WorkOrder.objects.filter(
            compid_id=comp_id,
            finyearid__lte=finyear_id,
            closeopen=0  # Only open work orders can be edited
        ).order_by('-id')
        
        # Apply search filters based on type
        if search_type == '0' and search_value:  # Customer Name search
            # Extract customer code from search value if in format "Name [Code]"
            if '[' in search_value and ']' in search_value:
                customer_code = search_value.split('[')[1].replace(']', '')
                queryset = queryset.filter(customerid=customer_code)
            else:
                # Direct customer name search via raw SQL for performance
                queryset = queryset.extra(
                    where=["""EXISTS (
                        SELECT 1 FROM SD_Cust_master 
                        WHERE SD_Cust_master.CustomerId = SD_Cust_WorkOrder_Master.CustomerId 
                        AND SD_Cust_master.CustomerName LIKE %s
                        AND SD_Cust_master.CompId = %s
                    )"""],
                    params=[f'%{search_value}%', comp_id]
                )
        elif search_type == '1' and search_text:  # Enquiry No search
            queryset = queryset.filter(enqid=search_text)
        elif search_type == '2' and search_text:  # PO No search
            queryset = queryset.filter(pono=search_text)
        elif search_type == '3' and search_text:  # WO No search
            queryset = queryset.filter(wono=search_text)
        
        # Filter by work order category if selected
        if wo_category and wo_category != 'WO Category':
            queryset = queryset.filter(cid_id=wo_category)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Work Order - Edit"
        
        # Get current company and financial year
        comp_id = self.request.session.get('compid', 1)
        
        # Get work order categories for dropdown
        context['work_order_categories'] = WorkOrderCategory.objects.filter(
            compid_id=comp_id
        ).values('cid', 'symbol', 'cname')
        
        # Enhance work orders with additional data (matching ASP.NET logic)
        wos_with_data = []
        for wo in context['work_orders']:
            # Get customer name and details
            wo.customer_name = self.get_customer_name(wo.customerid, comp_id)
            wo.financial_year = self.get_financial_year(wo.finyearid_id if wo.finyearid_id else None)
            wo.employee_name = self.get_employee_name(wo.sessionid, comp_id)
            wo.formatted_date = self.format_date(wo.sysdate)
            wo.category_name = self.get_category_name(wo.cid_id, comp_id)
            wos_with_data.append(wo)
        
        context['work_orders'] = wos_with_data
        context['search_by'] = self.request.GET.get('search_by', '0')
        context['search_value'] = self.request.GET.get('search_value', '')
        context['search_text'] = self.request.GET.get('search_text', '')
        context['wo_category'] = self.request.GET.get('wo_category', '')
        
        return context

    def get_customer_name(self, customer_id, comp_id):
        """Get customer name - matches ASP.NET logic"""
        if not customer_id:
            return ''
        
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT CustomerName FROM SD_Cust_master 
                WHERE CustomerId = %s AND CompId = %s
            """, [customer_id, comp_id])
            row = cursor.fetchone()
            return row[0] if row else customer_id

    def get_financial_year(self, fin_year_id):
        """Get financial year display"""
        if not fin_year_id:
            return ''
        
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT FinYear FROM tblFinancial_master 
                WHERE FinYearId = %s
            """, [fin_year_id])
            row = cursor.fetchone()
            return row[0] if row else ''

    def get_employee_name(self, session_id, comp_id):
        """Get employee name from HR table"""
        if not session_id:
            return ''
        
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT Title || '.' || EmployeeName AS EmployeeName 
                FROM tblHR_OfficeStaff 
                WHERE EmpId = %s AND CompId = %s
            """, [session_id, comp_id])
            row = cursor.fetchone()
            return row[0] if row else ''

    def get_category_name(self, category_id, comp_id):
        """Get category name"""
        if not category_id:
            return ''
        
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT CName FROM tblSD_WO_Category 
                WHERE CId = %s AND CompId = %s
            """, [category_id, comp_id])
            row = cursor.fetchone()
            return row[0] if row else ''

    def format_date(self, date_str):
        """Format date from DD-MM-YYYY format"""
        if not date_str:
            return ''
        
        try:
            # Handle DD-MM-YYYY format from database
            parts = date_str.split('-')
            if len(parts) == 3:
                return f"{parts[0]}-{parts[1]}-{parts[2]}"
            return date_str
        except:
            return date_str


class WorkOrderEditDetailsView(LoginRequiredMixin, View):
    """
    Work Order Edit Details View - matches ASP.NET WorkOrder_Edit_Details.aspx exactly
    Multi-tab interface for comprehensive work order editing
    """
    template_name = 'sales_distribution/work_order_edit_details.html'

    def get(self, request, pk):
        """Display the work order edit form with all tabs"""
        
        # Get work order
        comp_id = request.session.get('compid', 1)
        work_order = get_object_or_404(WorkOrder, pk=pk, compid_id=comp_id)
        
        # Get customer details
        customer = self.get_customer_details(work_order.customerid, comp_id)
        
        # Get PO details if exists
        po_details = self.get_po_details(work_order.poid_id, comp_id) if work_order.poid_id else None
        
        # Get work order products
        products = self.get_work_order_products(work_order.id, comp_id)
        
        # Initialize forms with current data
        task_form = WorkOrderCreateForm(
            instance=work_order, 
            company_id=comp_id,
            initial=self.get_task_form_initial(work_order)
        )
        shipping_form = WorkOrderShippingForm(
            initial=self.get_shipping_form_initial(work_order)
        )
        product_form = WorkOrderProductForm()
        instructions_form = WorkOrderInstructionsForm(
            initial=self.get_instructions_form_initial(work_order)
        )
        
        context = {
            'page_title': f'Work Order - Edit #{work_order.wono}',
            'work_order': work_order,
            'customer': customer,
            'po_details': po_details,
            'products': products,
            'task_form': task_form,
            'shipping_form': shipping_form,
            'product_form': product_form,
            'instructions_form': instructions_form,
            'active_tab': request.GET.get('tab', '0'),
        }
        
        return render(request, self.template_name, context)

    def post(self, request, pk):
        """Handle form submission for any tab"""
        
        action = request.POST.get('action')
        comp_id = request.session.get('compid', 1)
        work_order = get_object_or_404(WorkOrder, pk=pk, compid_id=comp_id)
        
        if action == 'add_product':
            return self.handle_add_product(request, work_order)
        elif action == 'delete_product':
            return self.handle_delete_product(request, work_order)
        elif action == 'update_work_order':
            return self.handle_update_work_order(request, work_order)
        
        # Handle tab navigation
        active_tab = request.POST.get('next_tab', '0')
        return redirect(f"{request.path}?tab={active_tab}")

    def handle_add_product(self, request, work_order):
        """Add product to work order"""
        product_form = WorkOrderProductForm(request.POST)
        
        if product_form.is_valid():
            # Insert into work order products table
            with connection.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO SD_Cust_WorkOrder_Products_Details 
                    (MId, SessionId, CompId, FinYearId, ItemCode, Description, Qty)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, [
                    work_order.id,
                    request.session.get('username', ''),
                    request.session.get('compid', 1),
                    request.session.get('finyear', 13),
                    product_form.cleaned_data['item_code'],
                    product_form.cleaned_data['description'],
                    float(product_form.cleaned_data['quantity'])
                ])
            
            messages.success(request, 'Product added successfully.')
        else:
            for field, errors in product_form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')
        
        return redirect(f"{request.path}?tab=2")  # Stay on products tab

    def handle_delete_product(self, request, work_order):
        """Delete product from work order"""
        product_id = request.POST.get('product_id')
        
        if product_id:
            with connection.cursor() as cursor:
                cursor.execute("""
                    DELETE FROM SD_Cust_WorkOrder_Products_Details
                    WHERE Id = %s AND MId = %s
                """, [product_id, work_order.id])
            
            messages.success(request, 'Product removed successfully.')
        
        return redirect(f"{request.path}?tab=2")  # Stay on products tab

    def handle_update_work_order(self, request, work_order):
        """Update complete work order - matches ASP.NET logic exactly"""
        
        comp_id = request.session.get('compid', 1)
        
        # Initialize all forms with POST data
        task_form = WorkOrderCreateForm(request.POST, instance=work_order, company_id=comp_id)
        shipping_form = WorkOrderShippingForm(request.POST)
        instructions_form = WorkOrderInstructionsForm(request.POST)
        
        # Validate all forms
        if not all([task_form.is_valid(), shipping_form.is_valid(), instructions_form.is_valid()]):
            messages.error(request, 'Please correct the errors in the form.')
            # Show errors for debugging
            for form in [task_form, shipping_form, instructions_form]:
                for field, errors in form.errors.items():
                    for error in errors:
                        messages.error(request, f'{field}: {error}')
            return redirect(f"{request.path}?tab=0")
        
        try:
            with transaction.atomic():
                # Update work order record using raw SQL to match ASP.NET exactly
                self.update_work_order_record(
                    work_order, task_form, shipping_form, instructions_form
                )
                
                messages.success(request, f'Work Order "{work_order.wono}" updated successfully.')
                return redirect('sales_distribution:work_order_edit_list')
                
        except Exception as e:
            messages.error(request, f'Error updating work order: {str(e)}')
            return redirect(f"{request.path}?tab=0")

    def update_work_order_record(self, work_order, task_form, shipping_form, instructions_form):
        """Update work order record - matches ASP.NET update exactly"""
        
        # Convert form data to database format
        task_data = task_form.cleaned_data
        shipping_data = shipping_form.cleaned_data
        instructions_data = instructions_form.cleaned_data
        
        # Format dates
        def format_date(date_obj):
            return date_obj.strftime('%d-%m-%Y') if date_obj else ''
        
        # Update work order using raw SQL to match ASP.NET exactly
        with connection.cursor() as cursor:
            cursor.execute("""
                UPDATE SD_Cust_WorkOrder_Master SET
                    TaskWorkOrderDate = %s,
                    TaskProjectTitle = %s,
                    TaskProjectLeader = %s,
                    CId = %s,
                    SCId = %s,
                    TaskBusinessGroup = %s,
                    TaskTargetDAP_FDate = %s,
                    TaskTargetDAP_TDate = %s,
                    TaskDesignFinalization_FDate = %s,
                    TaskDesignFinalization_TDate = %s,
                    TaskTargetManufg_FDate = %s,
                    TaskTargetManufg_TDate = %s,
                    TaskTargetTryOut_FDate = %s,
                    TaskTargetTryOut_TDate = %s,
                    TaskTargetDespach_FDate = %s,
                    TaskTargetDespach_TDate = %s,
                    TaskTargetAssembly_FDate = %s,
                    TaskTargetAssembly_TDate = %s,
                    TaskTargetInstalation_FDate = %s,
                    TaskTargetInstalation_TDate = %s,
                    TaskCustInspection_FDate = %s,
                    TaskCustInspection_TDate = %s,
                    ShippingAdd = %s,
                    ShippingCountry = %s,
                    ShippingState = %s,
                    ShippingCity = %s,
                    ShippingContactPerson1 = %s,
                    ShippingContactNo1 = %s,
                    ShippingEmail1 = %s,
                    ShippingContactPerson2 = %s,
                    ShippingContactNo2 = %s,
                    ShippingEmail2 = %s,
                    ShippingFaxNo = %s,
                    ShippingEccNo = %s,
                    ShippingTinCstNo = %s,
                    ShippingTinVatNo = %s,
                    InstractionPrimerPainting = %s,
                    InstractionPainting = %s,
                    InstractionSelfCertRept = %s,
                    InstractionOther = %s,
                    InstractionExportCaseMark = %s,
                    ManufMaterialDate = %s,
                    BoughtoutMaterialDate = %s,
                    Buyer = %s
                WHERE Id = %s
            """, [
                format_date(task_data['work_order_date']),
                task_data['project_title'],
                task_data['project_leader'],
                task_data['category'].cid,
                task_data['subcategory'].sub_cid if task_data.get('subcategory') else 0,
                task_data['business_group'].id,
                format_date(task_data['target_dap_from_date']),
                format_date(task_data['target_dap_to_date']),
                format_date(task_data['design_finalization_from_date']),
                format_date(task_data['design_finalization_to_date']),
                format_date(task_data['target_manufacturing_from_date']),
                format_date(task_data['target_manufacturing_to_date']),
                format_date(task_data['target_tryout_from_date']),
                format_date(task_data['target_tryout_to_date']),
                format_date(task_data['target_despatch_from_date']),
                format_date(task_data['target_despatch_to_date']),
                format_date(task_data['target_assembly_from_date']),
                format_date(task_data['target_assembly_to_date']),
                format_date(task_data['target_installation_from_date']),
                format_date(task_data['target_installation_to_date']),
                format_date(task_data['customer_inspection_from_date']),
                format_date(task_data['customer_inspection_to_date']),
                shipping_data['shipping_address'],
                shipping_data['shipping_country'].countryid,
                shipping_data['shipping_state'].stateid,
                shipping_data['shipping_city'].cityid,
                shipping_data['contact_person_1'],
                shipping_data['contact_no_1'],
                shipping_data['email_1'],
                shipping_data['contact_person_2'],
                shipping_data['contact_no_2'],
                shipping_data['email_2'],
                shipping_data['fax_no'],
                shipping_data['ecc_no'],
                shipping_data['tin_cst_no'],
                shipping_data['tin_vat_no'],
                1 if instructions_data['primer_painting'] else 0,
                1 if instructions_data['painting'] else 0,
                1 if instructions_data['self_certification_report'] else 0,
                instructions_data['other_instructions'],
                instructions_data['export_case_mark'],
                format_date(task_data['manufacturing_material_date']),
                format_date(task_data['boughtout_material_date']),
                task_data['buyer'].userid,
                work_order.id
            ])

    def get_customer_details(self, customer_id, comp_id):
        """Get customer details"""
        if not customer_id:
            return None
            
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT CustomerName, CustomerId
                FROM SD_Cust_master
                WHERE CustomerId = %s AND CompId = %s
            """, [customer_id, comp_id])
            
            row = cursor.fetchone()
            if row:
                return {
                    'customer_name': row[0],
                    'customer_id': row[1]
                }
            return None

    def get_po_details(self, po_id, comp_id):
        """Get PO details"""
        if not po_id:
            return None
            
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT PONo, PODate 
                FROM SD_Cust_PO_Master
                WHERE POId = %s AND CompId = %s
            """, [po_id, comp_id])
            
            row = cursor.fetchone()
            if row:
                return {
                    'po_no': row[0],
                    'po_date': row[1]
                }
            return None

    def get_work_order_products(self, work_order_id, comp_id):
        """Get work order products"""
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT Id, ItemCode, Description, Qty
                FROM SD_Cust_WorkOrder_Products_Details
                WHERE MId = %s AND CompId = %s
                ORDER BY Id
            """, [work_order_id, comp_id])
            
            products = []
            for row in cursor.fetchall():
                products.append({
                    'id': row[0],
                    'item_code': row[1],
                    'description': row[2],
                    'quantity': row[3]
                })
            
            return products

    def get_task_form_initial(self, work_order):
        """Get initial data for task form"""
        def parse_date(date_str):
            if not date_str:
                return None
            try:
                # Parse DD-MM-YYYY format
                return datetime.strptime(date_str, '%d-%m-%Y').date()
            except:
                return None

        return {
            'work_order_date': parse_date(work_order.taskworkorderdate),
            'project_title': work_order.taskprojecttitle,
            'project_leader': work_order.taskprojectleader,
            'target_dap_from_date': parse_date(work_order.tasktargetdap_fdate),
            'target_dap_to_date': parse_date(work_order.tasktargetdap_tdate),
            'design_finalization_from_date': parse_date(work_order.taskdesignfinalization_fdate),
            'design_finalization_to_date': parse_date(work_order.taskdesignfinalization_tdate),
            'target_manufacturing_from_date': parse_date(work_order.tasktargetmanufg_fdate),
            'target_manufacturing_to_date': parse_date(work_order.tasktargetmanufg_tdate),
            'target_tryout_from_date': parse_date(work_order.tasktargettryout_fdate),
            'target_tryout_to_date': parse_date(work_order.tasktargettryout_tdate),
            'target_despatch_from_date': parse_date(work_order.tasktargetdespach_fdate),
            'target_despatch_to_date': parse_date(work_order.tasktargetdespach_tdate),
            'target_assembly_from_date': parse_date(work_order.tasktargetassembly_fdate),
            'target_assembly_to_date': parse_date(work_order.tasktargetassembly_tdate),
            'target_installation_from_date': parse_date(work_order.tasktargetinstalation_fdate),
            'target_installation_to_date': parse_date(work_order.tasktargetinstalation_tdate),
            'customer_inspection_from_date': parse_date(work_order.taskcustinspection_fdate),
            'customer_inspection_to_date': parse_date(work_order.taskcustinspection_tdate),
            'manufacturing_material_date': parse_date(work_order.manufmaterialdate),
            'boughtout_material_date': parse_date(work_order.boughtoutmaterialdate),
        }

    def get_shipping_form_initial(self, work_order):
        """Get initial data for shipping form"""
        return {
            'shipping_address': work_order.shippingadd,
            'contact_person_1': work_order.shippingcontactperson1,
            'contact_no_1': work_order.shippingcontactno1,
            'email_1': work_order.shippingemail1,
            'contact_person_2': work_order.shippingcontactperson2,
            'contact_no_2': work_order.shippingcontactno2,
            'email_2': work_order.shippingemail2,
            'fax_no': work_order.shippingfaxno,
            'ecc_no': work_order.shippingeccno,
            'tin_cst_no': work_order.shippingtincstno,
            'tin_vat_no': work_order.shippingtinvatno,
        }

    def get_instructions_form_initial(self, work_order):
        """Get initial data for instructions form"""
        return {
            'primer_painting': bool(work_order.instractionprimerpainting),
            'painting': bool(work_order.instractionpainting),
            'self_certification_report': bool(work_order.instractionselfcertrept),
            'other_instructions': work_order.instractionother,
            'export_case_mark': work_order.instractionexportcasemark,
        }


class WorkOrderProductEditView(LoginRequiredMixin, View):
    """AJAX view for editing work order products inline"""
    
    def post(self, request, work_order_id, product_id):
        try:
            item_code = request.POST.get('item_code')
            description = request.POST.get('description')
            quantity = float(request.POST.get('quantity', 0))
            
            with connection.cursor() as cursor:
                cursor.execute("""
                    UPDATE SD_Cust_WorkOrder_Products_Details
                    SET ItemCode = %s, Description = %s, Qty = %s
                    WHERE Id = %s AND MId = %s
                """, [item_code, description, quantity, product_id, work_order_id])
            
            return JsonResponse({'status': 'success', 'message': 'Product updated successfully.'})
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)})


class WorkOrderCustomerAutocompleteEditView(LoginRequiredMixin, View):
    """Customer autocomplete for work order edit search"""
    
    def get(self, request):
        query = request.GET.get('q', '')
        comp_id = request.session.get('compid', 1)
        
        if len(query) < 1:
            return JsonResponse({'customers': []})
        
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT CustomerName, CustomerId
                    FROM SD_Cust_master
                    WHERE CompId = %s AND CustomerName LIKE %s
                    ORDER BY CustomerName ASC
                    LIMIT 10
                """, [comp_id, f'%{query}%'])
                
                customers_data = []
                for row in cursor.fetchall():
                    suggestion = f"{row[0]} [{row[1]}]"
                    customers_data.append({'text': suggestion})
                
                return JsonResponse({'customers': customers_data})
                
        except Exception as e:
            return JsonResponse({'customers': [], 'error': str(e)})
