import pytest
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission
from unittest.mock import patch, MagicMock

User = get_user_model()


class GINNewTestCase(TestCase):
    """Test cases for GIN New functionality"""

    def setUp(self):
        """Set up test environment"""
        self.client = Client()
        
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            email='<EMAIL>'
        )
        
        # Add necessary permissions
        permission = Permission.objects.get(codename='add_goodsinwardnote')
        self.user.user_permissions.add(permission)
        
        # Login the user
        self.client.login(username='testuser', password='testpass123')
        
        # Set session data (company and financial year)
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 9
        session.save()

    def test_gin_new_search_get(self):
        """Test GET request to GIN New search page"""
        url = reverse('inventory:gin_new_search')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Goods Inward Note [GIN] - New')
        self.assertContains(response, 'Search for eligible Purchase Orders')
        self.assertContains(response, 'Supplier Name')
        self.assertContains(response, 'PO No')

    def test_gin_new_search_template_elements(self):
        """Test that template contains correct elements"""
        url = reverse('inventory:gin_new_search')
        response = self.client.get(url)
        
        # Check for dropdown options
        self.assertContains(response, '<option value="0">Supplier Name</option>')
        self.assertContains(response, '<option value="1">PO No</option>')
        
        # Check for input fields
        self.assertContains(response, 'name="supplier_search"')
        self.assertContains(response, 'name="po_search"')
        
        # Check for search button
        self.assertContains(response, 'Search')

    def test_gin_new_search_post_supplier_valid(self):
        """Test POST request with valid supplier search"""
        url = reverse('inventory:gin_new_search')
        
        # Mock the database cursor and query results
        with patch('inventory.views.gin_grr_views.connection') as mock_connection:
            mock_cursor = MagicMock()
            mock_connection.cursor.return_value.__enter__.return_value = mock_cursor
            
            # Mock query results for supplier search
            mock_cursor.fetchall.return_value = [
                (1, '0406', '2022-08-04', 'A105', 'A 1 TRANSMISSION', 'A105', 9, '2021-2022', 1, 1.0)
            ]
            mock_cursor.description = [
                ('POId',), ('PONo',), ('PODate',), ('SupplierId',), ('SupplierName',),
                ('SupplierCode',), ('FinYearId',), ('FinYear',), ('ItemCount',), ('TotalQty',)
            ]
            
            response = self.client.post(url, {
                'search_type': '0',
                'supplier_search': 'A 1 TRANSMISSION [A105]'
            })
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'search_performed": true')

    def test_gin_new_search_post_po_valid(self):
        """Test POST request with valid PO number search"""
        url = reverse('inventory:gin_new_search')
        
        with patch('inventory.views.gin_grr_views.connection') as mock_connection:
            mock_cursor = MagicMock()
            mock_connection.cursor.return_value.__enter__.return_value = mock_cursor
            
            # Mock query results for PO search
            mock_cursor.fetchall.return_value = [
                (1, '0406', '2022-08-04', 'A105', 'A 1 TRANSMISSION', 'A105', 9, '2021-2022', 1, 1.0)
            ]
            mock_cursor.description = [
                ('POId',), ('PONo',), ('PODate',), ('SupplierId',), ('SupplierName',),
                ('SupplierCode',), ('FinYearId',), ('FinYear',), ('ItemCount',), ('TotalQty',)
            ]
            
            response = self.client.post(url, {
                'search_type': '1',
                'po_search': '0406'
            })
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'search_performed": true')

    def test_gin_new_search_post_no_criteria(self):
        """Test POST request with no search criteria"""
        url = reverse('inventory:gin_new_search')
        
        response = self.client.post(url, {
            'search_type': '0',
            'supplier_search': ''
        })
        
        self.assertEqual(response.status_code, 200)
        # Should still render but with no results
        self.assertContains(response, 'search_performed": true')

    def test_gin_new_search_context_data(self):
        """Test context data in GET request"""
        url = reverse('inventory:gin_new_search')
        response = self.client.get(url)
        
        self.assertIn('search_type', response.context)
        self.assertIn('po_search', response.context)
        self.assertIn('supplier_search', response.context)
        self.assertIn('po_records', response.context)
        self.assertIn('search_performed', response.context)
        
        # Check default values
        self.assertEqual(response.context['search_type'], '0')
        self.assertEqual(response.context['po_search'], '')
        self.assertEqual(response.context['supplier_search'], '')
        self.assertEqual(response.context['po_records'], [])
        self.assertFalse(response.context['search_performed'])

    def test_gin_new_po_details_get(self):
        """Test GET request to GIN New PO details page"""
        url = reverse('inventory:gin_new_po_details', kwargs={'po_id': 1})
        
        with patch('inventory.views.gin_grr_views.connection') as mock_connection:
            mock_cursor = MagicMock()
            mock_connection.cursor.return_value.__enter__.return_value = mock_cursor
            
            # Mock PO header query
            mock_cursor.fetchone.side_effect = [
                (1, '0406', '2022-08-04', 'A105', 'A 1 TRANSMISSION', '2021-2022'),  # PO header
                []  # PO details (empty for simplicity)
            ]
            mock_cursor.fetchall.return_value = []  # PO line items
            mock_cursor.description = []
            
            response = self.client.get(url, {
                'challan_no': 'CH001',
                'challan_date': '2022-08-05'
            })
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'GIN Header Information')
        self.assertContains(response, 'Purchase Order Line Items')

    def test_gin_new_authentication_required(self):
        """Test that authentication is required for GIN New pages"""
        self.client.logout()
        
        # Test search page
        url = reverse('inventory:gin_new_search')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect to login
        
        # Test details page
        url = reverse('inventory:gin_new_po_details', kwargs={'po_id': 1})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_gin_new_url_patterns(self):
        """Test URL pattern resolution"""
        # Test search URL
        search_url = reverse('inventory:gin_new_search')
        self.assertEqual(search_url, '/inventory/gin-new/')
        
        # Test details URL
        details_url = reverse('inventory:gin_new_po_details', kwargs={'po_id': 123})
        self.assertEqual(details_url, '/inventory/gin-new/123/')

    def test_supplier_id_extraction(self):
        """Test supplier ID extraction from autocomplete format"""
        url = reverse('inventory:gin_new_search')
        
        with patch('inventory.views.gin_grr_views.connection') as mock_connection:
            mock_cursor = MagicMock()
            mock_connection.cursor.return_value.__enter__.return_value = mock_cursor
            mock_cursor.fetchall.return_value = []
            mock_cursor.description = []
            
            # Test with proper format
            response = self.client.post(url, {
                'search_type': '0',
                'supplier_search': 'A 1 TRANSMISSION [A105]'
            })
            
            # Should call the query with extracted supplier ID
            mock_cursor.execute.assert_called()
            args = mock_cursor.execute.call_args[0]
            self.assertIn('A105', args[1])  # Supplier ID should be in parameters

    @patch('inventory.views.gin_grr_views.connection')
    def test_database_error_handling(self, mock_connection):
        """Test handling of database errors"""
        # Simulate database error
        mock_connection.cursor.side_effect = Exception("Database connection failed")
        
        url = reverse('inventory:gin_new_search')
        response = self.client.post(url, {
            'search_type': '0',
            'supplier_search': 'A 1 TRANSMISSION [A105]'
        })
        
        self.assertEqual(response.status_code, 200)
        # Should still render the page, possibly with error message


class GINNewViewMethodTests(TestCase):
    """Test individual view methods"""

    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.client.login(username='testuser', password='testpass123')

    def test_supplier_search_method(self):
        """Test _get_eligible_pos_by_supplier method"""
        from inventory.views.gin_grr_views import GINNewSearchView
        
        view = GINNewSearchView()
        
        with patch('inventory.views.gin_grr_views.connection') as mock_connection:
            mock_cursor = MagicMock()
            mock_cursor.fetchall.return_value = [
                (1, '0406', '2022-08-04', 'A105', 'A 1 TRANSMISSION', 'A105', 9, '2021-2022', 1, 5.0)
            ]
            mock_cursor.description = [
                ('POId',), ('PONo',), ('PODate',), ('SupplierId',), ('SupplierName',),
                ('SupplierCode',), ('FinYearId',), ('FinYear',), ('ItemCount',), ('TotalQty',)
            ]
            
            result = view._get_eligible_pos_by_supplier(mock_cursor, 1, 9, 'A105')
            
            self.assertEqual(len(result), 1)
            self.assertEqual(result[0]['PONo'], '0406')
            self.assertEqual(result[0]['PendingQty'], 5.0)
            self.assertEqual(result[0]['challan_no'], '')
            self.assertEqual(result[0]['challan_date'], '')

    def test_po_search_method(self):
        """Test _get_eligible_pos_by_po_number method"""
        from inventory.views.gin_grr_views import GINNewSearchView
        
        view = GINNewSearchView()
        
        with patch('inventory.views.gin_grr_views.connection') as mock_connection:
            mock_cursor = MagicMock()
            mock_cursor.fetchall.return_value = [
                (1, '0406', '2022-08-04', 'A105', 'A 1 TRANSMISSION', 'A105', 9, '2021-2022', 1, 3.0)
            ]
            mock_cursor.description = [
                ('POId',), ('PONo',), ('PODate',), ('SupplierId',), ('SupplierName',),
                ('SupplierCode',), ('FinYearId',), ('FinYear',), ('ItemCount',), ('TotalQty',)
            ]
            
            result = view._get_eligible_pos_by_po_number(mock_cursor, 1, 9, '0406')
            
            self.assertEqual(len(result), 1)
            self.assertEqual(result[0]['PONo'], '0406')
            self.assertEqual(result[0]['PendingQty'], 3.0)


@pytest.mark.django_db
class TestGINNewPytest:
    """Pytest-style tests for GIN New functionality"""

    def test_gin_new_search_url_resolves(self):
        """Test URL resolution using pytest"""
        url = reverse('inventory:gin_new_search')
        assert url == '/inventory/gin-new/'

    def test_gin_new_po_details_url_resolves(self):
        """Test PO details URL resolution using pytest"""
        url = reverse('inventory:gin_new_po_details', kwargs={'po_id': 42})
        assert url == '/inventory/gin-new/42/'

    def test_gin_new_requires_authentication(self, client):
        """Test authentication requirement using pytest"""
        url = reverse('inventory:gin_new_search')
        response = client.get(url)
        assert response.status_code == 302  # Redirect to login

    def test_gin_new_template_rendering(self, client, django_user_model):
        """Test template rendering using pytest"""
        user = django_user_model.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        client.force_login(user)
        
        url = reverse('inventory:gin_new_search')
        response = client.get(url)
        
        assert response.status_code == 200
        assert 'Goods Inward Note [GIN] - New' in response.content.decode()
        assert 'Search for eligible Purchase Orders' in response.content.decode()