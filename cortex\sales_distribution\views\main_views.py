# sales_distribution/views.py
# Django class-based views for Sales Distribution module
# Replaces ASP.NET CategoryEdit.aspx functionality and other SD operations

from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.db.models import Q
from django.urls import reverse_lazy, reverse
from django.views import View
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_protect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db import transaction
import datetime
from ..models import WorkOrderCategory, WorkorderSubcategory, Customer, WorkOrder, Product, Enquiry, EnquiryAttachment, Quotation, QuotationDetails, Unit, WOType, PurchaseOrder, PurchaseOrderDetails
from ..forms.main_forms import CustomerPOForm, CustomerPODetailFormSet
from ..forms import (
    WorkOrderCategoryForm,
    WorkOrderCategoryEditForm,
    WorkOrderCategoryFilterForm,
    WorkOrderSubcategoryForm,
    WorkOrderSubcategoryEditForm,
    SubCategoryFilterForm,
    WOTypeForm,
    WOTypeEditForm,
    WOTypeFilterForm,
    CustomerForm,
    CustomerFilterForm,
    ProductForm,
    ProductFilterForm,
    EnquiryForm,
    QuotationForm,
    EnquirySelectionForm,
    QuotationFilterForm,
    CustomerPOForm,
)


class CustomerPOListView(LoginRequiredMixin, ListView):
    """
    Customer Purchase Order list view - replaces ASP.NET CustPO_Edit.aspx functionality
    Provides search, filtering, and display of customer purchase orders with proper joins
    """
    model = PurchaseOrder
    template_name = 'sales_distribution/customer_po_list.html'
    context_object_name = 'customer_pos'
    paginate_by = 20

    def get_queryset(self):
        """Get PO data with joins to show customer names, financial years, and employee info"""
        from django.db import connection
        
        # Get current company and financial year from global context
        company_id = getattr(self.request.user, 'compid', 1) if hasattr(self.request.user, 'compid') else 1
        
        # Get current financial year from database (Flag=1 indicates current year)
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT FinYearId FROM tblFinancial_master 
                WHERE CompId = %s AND Flag = 1
                ORDER BY FinYearId DESC LIMIT 1
            """, [company_id])
            current_fin_year = cursor.fetchone()
            current_fin_year_id = current_fin_year[0] if current_fin_year else 13  # Default to latest (2025-2026)
        
        # Build complex query similar to ASP.NET logic
        search_type = self.request.GET.get('search_type', 'Select')
        search_value = self.request.GET.get('search_value', '')
        enq_id = self.request.GET.get('enq_id', '')
        
        # Base queryset - show all POs up to current financial year instead of limiting to old year
        queryset = PurchaseOrder.objects.filter(
            compid_id=company_id,
            finyearid_id__lte=current_fin_year_id
        ).order_by('-poid')
        
        # Apply search filters based on type
        if search_type == '0' and search_value:  # Customer Name search
            # Extract customer code from search value if in format "Name [Code]"
            if '[' in search_value and ']' in search_value:
                customer_code = search_value.split('[')[1].replace(']', '')
                queryset = queryset.filter(customerid=customer_code)
            else:
                # Direct customer name search - need to join with customer master
                queryset = queryset.extra(
                    where=["""EXISTS (
                        SELECT 1 FROM SD_Cust_master 
                        WHERE SD_Cust_master.CustomerId = SD_Cust_PO_Master.CustomerId 
                        AND SD_Cust_master.CustomerName LIKE %s
                    )"""],
                    params=[f'%{search_value}%']
                )
        elif search_type == '1' and enq_id:  # Enquiry No search
            queryset = queryset.filter(enqid=enq_id)
        elif search_type == '2' and enq_id:  # PO No search
            queryset = queryset.filter(pono=enq_id)
        
        # Filter out closed work orders (similar to ASP.NET logic)
        queryset = queryset.extra(
            where=["""NOT EXISTS (
                SELECT 1 FROM SD_Cust_WorkOrder_Master 
                WHERE SD_Cust_WorkOrder_Master.PONo = SD_Cust_PO_Master.PONo 
                AND SD_Cust_WorkOrder_Master.CloseOpen = 1
            )"""]
        )
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Add search form data
        context['search_type'] = self.request.GET.get('search_type', 'Select')
        context['search_value'] = self.request.GET.get('search_value', '')
        context['enq_id'] = self.request.GET.get('enq_id', '')
        context['page_title'] = "Customer PO - Edit"
        
        # Get additional data for each PO (similar to ASP.NET logic)
        enriched_pos = []
        for po in context['customer_pos']:
            po_data = {
                'po': po,
                'customer_name': self.get_customer_name(po.customerid),
                'customer_id': po.customerid,
                'fin_year': self.get_financial_year(po.finyearid_id),
                'employee_name': self.get_employee_name(po.sessionid),
                'formatted_date': self.format_date(po.sysdate) if po.sysdate else '',
            }
            enriched_pos.append(po_data)
        
        context['enriched_pos'] = enriched_pos
        context['msg'] = self.request.GET.get('msg', '')
        
        return context

    def get_customer_name(self, customer_id):
        """Get customer name from customer master"""
        if not customer_id:
            return ''
        
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT CustomerName FROM SD_Cust_master 
                WHERE CustomerId = %s
            """, [customer_id])
            row = cursor.fetchone()
            return row[0] if row else ''

    def get_financial_year(self, fin_year_id):
        """Get financial year display - now dynamic based on FinYearId"""
        if not fin_year_id:
            return ''
        
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT FinYear FROM tblFinancial_master 
                WHERE FinYearId = %s
            """, [fin_year_id])
            row = cursor.fetchone()
            return row[0] if row else ''

    def get_employee_name(self, session_id):
        """Get employee name from HR table using SessionId (like Sapl0005)"""
        if not session_id:
            return ''
        
        from django.db import connection
        
        with connection.cursor() as cursor:
            # Use SQLite concatenation syntax and match SessionId with EmpId
            cursor.execute("""
                SELECT Title || '.' || EmployeeName AS EmployeeName 
                FROM tblHR_OfficeStaff 
                WHERE EmpId = %s
            """, [session_id])
            row = cursor.fetchone()
            return row[0] if row else ''

    def format_date(self, date_str):
        """Format date from DD-MM-YYYY to display format"""
        if not date_str:
            return ''
        
        try:
            # Handle DD-MM-YYYY format from database
            parts = date_str.split('-')
            if len(parts) == 3:
                # Convert to DD-MM-YYYY display format
                return f"{parts[0]}-{parts[1]}-{parts[2]}"
            return date_str
        except:
            return date_str

    def get(self, request, *args, **kwargs):
        """Handle GET requests with search functionality and HTMX support"""
        response = super().get(request, *args, **kwargs)
        
        # For HTMX requests, return only the table content
        if request.headers.get('HX-Request'):
            self.template_name = 'sales_distribution/partials/customer_po_table.html'
        
        return response

class CustomerPOCreateView(LoginRequiredMixin, CreateView):
    """
    Customer PO Create View - replicates ASP.NET CustPO_New and CustPO_New_Details functionality
    Handles both enquiry selection (Step 1) and PO creation (Step 2)
    """
    model = PurchaseOrder
    form_class = CustomerPOForm
    template_name = 'sales_distribution/customer_po_form.html'
    success_url = reverse_lazy('sales_distribution:customer_po_list')

    def get_enquiry_from_id(self, enq_id):
        """Get enquiry object from ID"""
        try:
            return Enquiry.objects.get(enqid=enq_id)
        except Enquiry.DoesNotExist:
            return None

    def get_customer_details(self, customer_id):
        """Get customer details including formatted address"""
        from django.db import connection
        
        customer_name = ""
        customer_address = ""
        
        with connection.cursor() as cursor:
            # Get customer details
            cursor.execute("""
                SELECT c.CustomerName, c.RegdAddress,
                       city.cityname, state.statename, country.countryname, c.RegdPinNo
                FROM SD_Cust_master c
                LEFT JOIN tblCity city ON c.RegdCity = city.cityid
                LEFT JOIN tblState state ON c.RegdState = state.sid
                LEFT JOIN tblCountry country ON c.RegdCountry = country.cid
                WHERE c.CustomerId = %s
            """, [customer_id])
            
            row = cursor.fetchone()
            if row:
                customer_name = row[0] or ""
                
                # Format address (ASP.NET style)
                address_parts = []
                if row[1]: address_parts.append(row[1])  # Registered Address
                if row[2]: address_parts.append(row[2])  # City
                if row[3]: address_parts.append(row[3])  # State
                if row[4]: address_parts.append(row[4])  # Country
                if row[5]: address_parts.append(f"PIN: {row[5]}")  # PIN
                
                customer_address = ", ".join(address_parts)
        
        return customer_name, customer_address

    def get_available_enquiries(self):
        """Get enquiries available for PO creation (POStatus = 0)"""
        from django.db import connection
        
        company_id = getattr(self.request.user, 'compid', 1) if hasattr(self.request.user, 'compid') else 1
        
        # Get current financial year
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT FinYearId FROM tblFinancial_master 
                WHERE CompId = %s AND Flag = 1
                ORDER BY FinYearId DESC LIMIT 1
            """, [company_id])
            current_fin_year = cursor.fetchone()
            current_fin_year_id = current_fin_year[0] if current_fin_year else 13
        
        # Get enquiries without POs and not having closed work orders
        queryset = Enquiry.objects.filter(
            compid_id=company_id,
            finyearid_id__lte=current_fin_year_id,
            postatus=0  # No PO created yet
        ).extra(
            where=["""NOT EXISTS (
                SELECT 1 FROM SD_Cust_WorkOrder_Master 
                WHERE SD_Cust_WorkOrder_Master.EnqId = SD_Cust_Enquiry_Master.EnqId 
                AND SD_Cust_WorkOrder_Master.CloseOpen = 1
            )"""]
        ).order_by('-enqid')
        
        # Apply search filters
        search_type = self.request.GET.get('search_type', '')
        customer_search = self.request.GET.get('customer_search', '')
        enquiry_search = self.request.GET.get('enquiry_search', '')
        
        if search_type == 'customer_name' and customer_search:
            queryset = queryset.extra(
                where=["""EXISTS (
                    SELECT 1 FROM SD_Cust_master 
                    WHERE SD_Cust_master.CustomerId = SD_Cust_Enquiry_Master.CustomerId 
                    AND SD_Cust_master.CustomerName LIKE %s
                )"""],
                params=[f'%{customer_search}%']
            )
        elif search_type == 'enquiry_no' and enquiry_search:
            queryset = queryset.filter(enqid__icontains=enquiry_search)
        
        # Enrich with additional data
        enriched_enquiries = []
        for enquiry in queryset[:50]:  # Limit results for performance
            customer_name, _ = self.get_customer_details(enquiry.customerid)
            
            # Get financial year
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT FinYear FROM tblFinancial_master 
                    WHERE FinYearId = %s
                """, [enquiry.finyearid_id])
                fin_year_row = cursor.fetchone()
                fin_year = fin_year_row[0] if fin_year_row else ''
            
            # Get employee name
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT Title || '.' || EmployeeName AS EmployeeName 
                    FROM tblHR_OfficeStaff 
                    WHERE EmpId = %s
                """, [enquiry.sessionid])
                emp_row = cursor.fetchone()
                employee_name = emp_row[0] if emp_row else ''
            
            enriched_enquiries.append({
                'enquiry': enquiry,
                'customer_name': customer_name,
                'fin_year': fin_year,
                'employee_name': employee_name,
                'formatted_date': enquiry.sysdate if enquiry.sysdate else ''
            })
        
        return enriched_enquiries

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        enq_id = self.request.GET.get('enq_id')
        if enq_id:
            enquiry = self.get_enquiry_from_id(enq_id)
            if enquiry:
                kwargs['enq_id'] = enq_id
                kwargs['company_id'] = enquiry.compid_id
                kwargs['fin_year_id'] = enquiry.finyearid_id
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Check if enquiry is selected
        enq_id = self.request.GET.get('enq_id')
        if enq_id:
            enquiry = self.get_enquiry_from_id(enq_id)
            if enquiry:
                context['enquiry'] = enquiry
                customer_name, customer_address = self.get_customer_details(enquiry.customerid)
                context['customer_name'] = customer_name
                context['customer_address'] = customer_address
                
                # Add formset for goods details
                if self.request.POST:
                    context['formset'] = CustomerPODetailFormSet(self.request.POST)
                else:
                    context['formset'] = CustomerPODetailFormSet()
            else:
                messages.error(self.request, 'Selected enquiry not found.')
                context['enquiry'] = None
        else:
            # Show enquiry selection page
            context['enquiries'] = self.get_available_enquiries()
            context['enquiry'] = None
        
        return context

    def form_valid(self, form):
        """Handle form submission with file upload and session management"""
        from datetime import datetime
        
        enq_id = self.request.GET.get('enq_id')
        if not enq_id:
            messages.error(self.request, 'No enquiry selected.')
            return self.form_invalid(form)
        
        enquiry = self.get_enquiry_from_id(enq_id)
        if not enquiry:
            messages.error(self.request, 'Selected enquiry not found.')
            return self.form_invalid(form)
        
        context = self.get_context_data()
        formset = context['formset']
        
        if not formset.is_valid():
            messages.error(self.request, 'Please correct the errors in goods details.')
            return self.form_invalid(form)
        
        try:
            with transaction.atomic():
                # Set system fields (replicating ASP.NET logic)
                po = form.save(commit=False)
                po.sysdate = datetime.now().strftime('%Y-%m-%d')
                po.systime = datetime.now().strftime('%H:%M:%S')
                po.sessionid = getattr(self.request.user, 'username', 'system')
                po.compid_id = enquiry.compid_id
                po.finyearid_id = enquiry.finyearid_id
                po.customerid = enquiry.customerid
                po.enqid = enquiry
                
                # Handle file upload
                attachment = self.request.FILES.get('attachment')
                if attachment:
                    po.filename = attachment.name
                    po.filesize = attachment.size
                    po.contenttype = attachment.content_type
                    po.filedata = attachment.read()
                
                po.save()
                
                # Save formset
                formset.instance = po
                formset.save()
                
                # Update enquiry POStatus (ASP.NET equivalent)
                enquiry.postatus = 1
                enquiry.save()
                
                messages.success(self.request, f'Purchase Order {po.pono} created successfully.')
                return redirect(self.success_url)
                
        except Exception as e:
            messages.error(self.request, f'Error creating purchase order: {str(e)}')
            return self.form_invalid(form)

    def form_invalid(self, form):
        """Handle form validation errors"""
        context = self.get_context_data(form=form)
        
        # Re-add formset to context if it exists
        if 'formset' in context:
            formset = context['formset']
            if not formset.is_valid():
                for form_errors in formset.errors:
                    for field, errors in form_errors.items():
                        for error in errors:
                            messages.error(self.request, f'Goods Details - {field}: {error}')
        
        return self.render_to_response(context)

class CustomerPOUpdateView(LoginRequiredMixin, UpdateView):
    model = PurchaseOrder
    form_class = CustomerPOForm
    template_name = 'sales_distribution/customer_po_form.html'
    success_url = reverse_lazy('sales_distribution:customer_po_list')
    pk_url_kwarg = 'poid'

    def get_context_data(self, **kwargs):
        data = super().get_context_data(**kwargs)
        if self.request.POST:
            data['formset'] = CustomerPODetailFormSet(self.request.POST, instance=self.object)
        else:
            data['formset'] = CustomerPODetailFormSet(instance=self.object)
        return data

    def form_valid(self, form):
        context = self.get_context_data()
        formset = context['formset']
        with transaction.atomic():
            self.object = form.save()
            if formset.is_valid():
                formset.instance = self.object
                formset.save()
                return redirect(self.get_success_url())
        return self.render_to_response(self.get_context_data(form=form))

class CustomerPODetailView(LoginRequiredMixin, DetailView):
    """
    Customer PO Detail View - replaces ASP.NET CustPO_Edit_Details.aspx functionality
    Shows comprehensive purchase order information with line items
    """
    model = PurchaseOrder
    template_name = 'sales_distribution/customer_po_detail.html'
    context_object_name = 'po'
    pk_url_kwarg = 'poid'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get related purchase order details
        context['po_details'] = PurchaseOrderDetails.objects.filter(
            poid=self.object
        ).select_related('unit').order_by('id')
        
        # Get customer information
        if self.object.customerid:
            context['customer_name'] = self.get_customer_name(self.object.customerid)
            context['customer_address'] = self.get_customer_address(self.object.customerid)
        
        # Get financial year display
        context['financial_year'] = self.get_financial_year(self.object.finyearid_id)
        
        # Get employee name
        context['employee_name'] = self.get_employee_name(self.object.sessionid)
        
        return context
    
    def get_customer_name(self, customer_id):
        """Get customer name from customer master"""
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT CustomerName FROM SD_Cust_master 
                WHERE CustomerId = %s
            """, [customer_id])
            row = cursor.fetchone()
            return row[0] if row else 'Unknown Customer'
    
    def get_customer_address(self, customer_id):
        """Get formatted customer address"""
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT c.RegdAddress, city.cityname, state.statename, country.countryname, c.RegdPinNo
                FROM SD_Cust_master c
                LEFT JOIN tblCity city ON c.RegdCity = city.cityid
                LEFT JOIN tblState state ON c.RegdState = state.sid
                LEFT JOIN tblCountry country ON c.RegdCountry = country.cid
                WHERE c.CustomerId = %s
            """, [customer_id])
            row = cursor.fetchone()
            if row:
                address_parts = []
                if row[0]: address_parts.append(row[0])  # Address
                if row[1]: address_parts.append(row[1])  # City
                if row[2]: address_parts.append(row[2])  # State
                if row[3]: address_parts.append(row[3])  # Country
                if row[4]: address_parts.append(f"PIN: {row[4]}")  # PIN
                return ", ".join(address_parts)
            return 'Address not available'
    
    def get_financial_year(self, fin_year_id):
        """Get financial year display"""
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT FinYear FROM tblFinancial_master 
                WHERE FinYearId = %s
            """, [fin_year_id])
            row = cursor.fetchone()
            return row[0] if row else 'Unknown'
    
    def get_employee_name(self, session_id):
        """Get employee name from HR table"""
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT Title || '.' || EmployeeName AS EmployeeName 
                FROM tblHR_OfficeStaff 
                WHERE EmpId = %s
            """, [session_id])
            row = cursor.fetchone()
            return row[0] if row else 'Unknown Employee'




class WorkOrderCategoryListView(LoginRequiredMixin, ListView):
    """
    WorkOrder Category list view - replaces ASP.NET CategoryEdit.aspx GridView functionality
    Provides pagination, inline editing, and add/delete operations via HTMX
    """

    model = WorkOrderCategory
    template_name = "sales_distribution/workorder_category_list.html"
    context_object_name = "categories"
    paginate_by = 17  # Matches ASP.NET PageSize="17"

    def get_queryset(self):
        # Order by ID descending to match ASP.NET behavior
        queryset = WorkOrderCategory.objects.all().order_by("-cid")

        # Apply search filter if provided
        search = self.request.GET.get("search")
        if search:
            queryset = queryset.filter(Q(cname__icontains=search) | Q(symbol__icontains=search))

        # Apply subcategory filter if provided
        has_subcategory = self.request.GET.get("has_subcategory")
        if has_subcategory:
            queryset = queryset.filter(hassubcat=has_subcategory)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["category_form"] = WorkOrderCategoryForm()
        context["filter_form"] = WorkOrderCategoryFilterForm(self.request.GET)
        context["page_title"] = "Work Order Category Management"

        # Add usage check for each category (similar to ASP.NET code)
        for category in context["categories"]:
            category.is_used = WorkOrder.objects.filter(cid=category).exists()

        return context

    def get(self, request, *args, **kwargs):
        """Override to handle HTMX requests for real-time search"""
        response = super().get(request, *args, **kwargs)

        # If it's an HTMX request, return only the table partial
        if request.headers.get("HX-Request"):
            categories = self.get_queryset()
            return render(request, "sales_distribution/partials/category_table.html", {"categories": categories})

        return response


@method_decorator(csrf_protect, name="dispatch")
class WorkOrderCategoryCreateView(LoginRequiredMixin, CreateView):
    """
    WorkOrder Category creation view - replaces ASP.NET CategoryEdit.aspx Add functionality
    Uses HTMX for seamless form submission without page refresh
    """

    model = WorkOrderCategory
    form_class = WorkOrderCategoryForm
    template_name = "sales_distribution/partials/category_form.html"

    def form_valid(self, form):
        try:
            with transaction.atomic():
                # Auto-populate system fields
                category = form.save(commit=False)

                # Set company and financial year from session context
                from sys_admin.models import FinancialYear, Company
                
                # Get the current financial year (where Flag=1)
                current_fin_year = FinancialYear.objects.filter(is_current=True).first()
                if not current_fin_year:
                    # Fallback to the latest financial year
                    current_fin_year = FinancialYear.objects.order_by('-finyearid').first()
                
                # Get the default company (where Flag=1 or defaultcomp=1)
                current_company = Company.objects.filter(defaultcomp=1).first()
                if not current_company:
                    # Fallback to the first company
                    current_company = Company.objects.first()
                
                if current_company:
                    category.compid = current_company
                else:
                    category.compid_id = 1  # Fallback
                    
                if current_fin_year:
                    category.finyearid = current_fin_year
                else:
                    category.finyearid_id = 1  # Fallback

                # Set session details
                category.sessionid = self.request.session.get(
                    "username", self.request.user.username if self.request.user.is_authenticated else ""
                )

                # Auto-convert symbol to uppercase (matches ASP.NET behavior)
                if category.symbol:
                    category.symbol = category.symbol.upper()

                category.save()

                if self.request.headers.get("HX-Request"):
                    # Return updated table for HTMX
                    categories = WorkOrderCategory.objects.all().order_by("-cid")
                    return render(
                        self.request, "sales_distribution/partials/category_table.html", {"categories": categories}
                    )
                else:
                    messages.success(self.request, "Category created successfully!")
                    return redirect("sales_distribution:category_list")

        except Exception as e:
            if self.request.headers.get("HX-Request"):
                return render(
                    self.request, "sales_distribution/partials/category_form.html", {"form": form, "error": str(e)}
                )
            else:
                messages.error(self.request, f"Error creating category: {str(e)}")
                return self.form_invalid(form)

    def form_invalid(self, form):
        if self.request.headers.get("HX-Request"):
            return render(self.request, "sales_distribution/partials/category_form.html", {"form": form})
        return super().form_invalid(form)


class CategoryNewView(LoginRequiredMixin, View):
    """
    Dedicated Category New view - replaces ASP.NET CategoryNew.aspx
    Shows existing categories with ability to add new ones
    """

    template_name = "sales_distribution/category_new.html"

    def get(self, request):
        """Display category list with add form"""
        categories = WorkOrderCategory.objects.all().order_by("-cid")
        category_form = WorkOrderCategoryForm()

        # Add pagination
        paginator = Paginator(categories, 17)  # Match ASP.NET PageSize="17"
        page_number = request.GET.get("page")
        page_obj = paginator.get_page(page_number)

        context = {
            "categories": page_obj,
            "category_form": category_form,
            "page_title": "Category Of Work Order - New",
            "is_paginated": page_obj.has_other_pages(),
            "page_obj": page_obj,
            "paginator": paginator,
        }
        return render(request, self.template_name, context)

    def post(self, request):
        """Handle category creation via POST"""
        form = WorkOrderCategoryForm(request.POST)

        if form.is_valid():
            try:
                with transaction.atomic():
                    category = form.save(commit=False)

                    # Set company and financial year from session context
                    from sys_admin.models import FinancialYear, Company
                    
                    # Get the current financial year (where Flag=1)
                    current_fin_year = FinancialYear.objects.filter(is_current=True).first()
                    if not current_fin_year:
                        # Fallback to the latest financial year
                        current_fin_year = FinancialYear.objects.order_by('-finyearid').first()
                    
                    # Get the default company (where Flag=1 or defaultcomp=1)
                    current_company = Company.objects.filter(defaultcomp=1).first()
                    if not current_company:
                        # Fallback to the first company
                        current_company = Company.objects.first()
                    
                    if current_company:
                        category.compid = current_company
                    else:
                        category.compid_id = 1  # Fallback
                        
                    if current_fin_year:
                        category.finyearid = current_fin_year
                    else:
                        category.finyearid_id = 1  # Fallback

                    # Set session details (matches ASP.NET logic)
                    category.sessionid = request.session.get(
                        "username", request.user.username if request.user.is_authenticated else ""
                    )

                    # Auto-convert symbol to uppercase (matches ASP.NET behavior)
                    if category.symbol:
                        category.symbol = category.symbol.upper()

                    category.save()

                    messages.success(request, "Category added successfully!")
                    return redirect("sales_distribution:category_new")

            except Exception as e:
                messages.error(request, f"Error creating category: {str(e)}")

        # If form invalid, redisplay with errors
        categories = WorkOrderCategory.objects.all().order_by("-cid")
        paginator = Paginator(categories, 17)
        page_number = request.GET.get("page")
        page_obj = paginator.get_page(page_number)

        context = {
            "categories": page_obj,
            "category_form": form,
            "page_title": "Category Of Work Order - New",
            "is_paginated": page_obj.has_other_pages(),
            "page_obj": page_obj,
            "paginator": paginator,
        }
        return render(request, self.template_name, context)


@method_decorator(csrf_protect, name="dispatch")
class WorkOrderCategoryUpdateView(LoginRequiredMixin, UpdateView):
    """
    WorkOrder Category update view - replaces ASP.NET CategoryEdit.aspx Update functionality
    Supports both modal and inline editing via HTMX
    """

    model = WorkOrderCategory
    form_class = WorkOrderCategoryEditForm
    template_name = "sales_distribution/partials/category_edit_form.html"
    pk_url_kwarg = "cid"

    def get_object(self):
        return get_object_or_404(WorkOrderCategory, cid=self.kwargs["cid"])

    def form_valid(self, form):
        try:
            with transaction.atomic():
                category = form.save(commit=False)

                # Check if category is being used (like ASP.NET validation)
                if self.object.hassubcat == "1":  # If it had subcategories
                    subcategory_count = WorkorderSubcategory.objects.filter(cid=self.object).count()
                    if subcategory_count > 0 and category.hassubcat == "0":
                        if self.request.headers.get("HX-Request"):
                            return HttpResponse(
                                '<div class="text-red-600 text-sm">Cannot disable subcategories - subcategories exist for this category.</div>',
                                status=400,
                            )
                        else:
                            messages.error(
                                self.request, "Cannot disable subcategories - subcategories exist for this category."
                            )
                            return self.form_invalid(form)

                # Check if category is used in work orders
                if WorkOrder.objects.filter(cid=self.object).exists():
                    if self.request.headers.get("HX-Request"):
                        return HttpResponse(
                            '<div class="text-red-600 text-sm">You cannot edit this record, it is being used.</div>',
                            status=400,
                        )
                    else:
                        messages.error(self.request, "You cannot edit this record, it is being used.")
                        return self.form_invalid(form)

                category.save()

                if self.request.headers.get("HX-Request"):
                    # Return updated row for HTMX
                    return render(self.request, "sales_distribution/partials/category_row.html", {"category": category})
                else:
                    messages.success(self.request, "Category updated successfully!")
                    return redirect("sales_distribution:category_list")

        except Exception as e:
            if self.request.headers.get("HX-Request"):
                return HttpResponse(f'<div class="text-red-600 text-sm">Error: {str(e)}</div>', status=400)
            else:
                messages.error(self.request, f"Error updating category: {str(e)}")
                return self.form_invalid(form)

    def form_invalid(self, form):
        if self.request.headers.get("HX-Request"):
            return render(
                self.request,
                "sales_distribution/partials/category_edit_form.html",
                {"form": form, "category": self.object},
            )
        return super().form_invalid(form)


class WorkOrderCategoryDeleteView(LoginRequiredMixin, DeleteView):
    """
    WorkOrder Category delete view with usage validation
    """

    model = WorkOrderCategory
    pk_url_kwarg = "cid"
    success_url = reverse_lazy("sales_distribution:category_list")

    def get_object(self):
        return get_object_or_404(WorkOrderCategory, cid=self.kwargs["cid"])

    def delete(self, request, *args, **kwargs):
        category = self.get_object()

        # Check if category is being used
        if WorkOrder.objects.filter(cid=category).exists():
            if request.headers.get("HX-Request"):
                return HttpResponse(
                    '<div class="text-red-600 text-sm">Cannot delete - category is being used in work orders.</div>',
                    status=400,
                )
            else:
                messages.error(request, "Cannot delete - category is being used in work orders.")
                return redirect("sales_distribution:category_list")

        # Check if category has subcategories
        if WorkorderSubcategory.objects.filter(cid=category).exists():
            if request.headers.get("HX-Request"):
                return HttpResponse(
                    '<div class="text-red-600 text-sm">Cannot delete - category has subcategories.</div>', status=400
                )
            else:
                messages.error(request, "Cannot delete - category has subcategories.")
                return redirect("sales_distribution:category_list")

        try:
            category.delete()
            if request.headers.get("HX-Request"):
                return HttpResponse('<div class="text-green-600 text-sm">Category deleted successfully.</div>')
            else:
                messages.success(request, "Category deleted successfully!")
                return redirect("sales_distribution:category_list")
        except Exception as e:
            if request.headers.get("HX-Request"):
                return HttpResponse(f'<div class="text-red-600 text-sm">Error: {str(e)}</div>', status=400)
            else:
                messages.error(request, f"Error deleting category: {str(e)}")
                return redirect("sales_distribution:category_list")


class WorkOrderCategoryEditRowView(LoginRequiredMixin, View):
    """
    HTMX view for inline editing of category rows
    """

    def get(self, request, cid):
        category = get_object_or_404(WorkOrderCategory, cid=cid)
        form = WorkOrderCategoryEditForm(instance=category)
        return render(
            request, "sales_distribution/partials/category_edit_form.html", {"form": form, "category": category}
        )


class WorkOrderCategoryCancelEditView(LoginRequiredMixin, View):
    """
    HTMX view for canceling inline edit and returning to normal row view
    """

    def get(self, request, cid):
        category = get_object_or_404(WorkOrderCategory, cid=cid)
        return render(request, "sales_distribution/partials/category_row.html", {"category": category})


# Subcategory Views
class WorkOrderSubcategoryListView(LoginRequiredMixin, ListView):
    """
    WorkOrder Subcategory list view
    """

    model = WorkorderSubcategory
    template_name = "sales_distribution/subcategory_list.html"
    context_object_name = "subcategories"
    paginate_by = 20

    def get_queryset(self):
        queryset = WorkorderSubcategory.objects.select_related("cid").order_by("-sub_cid")
        
        search = self.request.GET.get("search")
        if search:
            queryset = queryset.filter(Q(sub_c_name__icontains=search) | Q(cid__cname__icontains=search))

        category = self.request.GET.get("category")
        if category:
            queryset = queryset.filter(cid=category)
            
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["subcategory_form"] = WorkOrderSubcategoryForm()
        context["filter_form"] = SubCategoryFilterForm(self.request.GET)
        context["page_title"] = "Work Order Sub-Category Management"
        context['all_categories'] = WorkOrderCategory.objects.filter(hassubcat='1')
        context['total_subcategories'] = WorkorderSubcategory.objects.count()
        
        # Add usage check for each subcategory
        for subcategory in context["subcategories"]:
            subcategory.is_used = WorkOrder.objects.filter(scid=subcategory).exists()

        return context

    def get(self, request, *args, **kwargs):
        """Override to handle HTMX requests for real-time search"""
        response = super().get(request, *args, **kwargs)

        if request.headers.get("HX-Request"):
            subcategories = self.get_queryset()
            total_subcategories = WorkorderSubcategory.objects.count()
            for subcategory in subcategories:
                subcategory.is_used = WorkOrder.objects.filter(scid=subcategory).exists()
            
            return render(request, "sales_distribution/partials/subcategory_table.html", {
                "subcategories": subcategories,
                "total_subcategories": total_subcategories
            })

        return response


@method_decorator(csrf_protect, name="dispatch")
class WorkOrderSubcategoryCreateView(LoginRequiredMixin, CreateView):
    """
    WorkOrder Subcategory creation view
    """
    model = WorkorderSubcategory
    form_class = WorkOrderSubcategoryForm
    template_name = "sales_distribution/partials/subcategory_form.html"

    def form_valid(self, form):
        try:
            with transaction.atomic():
                form.save()
                if self.request.headers.get("HX-Request"):
                    subcategories = WorkorderSubcategory.objects.select_related("cid").order_by("-sub_cid")
                    total_subcategories = WorkorderSubcategory.objects.count()
                    return render(
                        self.request, "sales_distribution/partials/subcategory_table.html", {
                            "subcategories": subcategories,
                            "total_subcategories": total_subcategories
                        }
                    )
                else:
                    messages.success(self.request, "Sub-category created successfully!")
                    return redirect("sales_distribution:subcategory_list")
        except Exception as e:
            if self.request.headers.get("HX-Request"):
                return render(
                    self.request, "sales_distribution/partials/subcategory_form.html", {"form": form, "error": str(e)}
                )
            else:
                messages.error(self.request, f"Error creating sub-category: {str(e)}")
                return self.form_invalid(form)

    def form_invalid(self, form):
        if self.request.headers.get("HX-Request"):
            return render(self.request, "sales_distribution/partials/subcategory_form.html", {"form": form})
        return super().form_invalid(form)


@method_decorator(csrf_protect, name="dispatch")
class WorkOrderSubcategoryUpdateView(LoginRequiredMixin, UpdateView):
    """
    WorkOrder Subcategory update view
    """
    model = WorkorderSubcategory
    form_class = WorkOrderSubcategoryEditForm
    template_name = "sales_distribution/partials/subcategory_edit_form.html"
    pk_url_kwarg = "sub_cid"

    def get_object(self):
        return get_object_or_404(WorkorderSubcategory, sub_cid=self.kwargs["sub_cid"])

    def form_valid(self, form):
        try:
            with transaction.atomic():
                subcategory = form.save()
                if self.request.headers.get("HX-Request"):
                    return render(self.request, "sales_distribution/partials/subcategory_row.html", {"subcategory": subcategory})
                else:
                    messages.success(self.request, "Sub-category updated successfully!")
                    return redirect("sales_distribution:subcategory_list")
        except Exception as e:
            if self.request.headers.get("HX-Request"):
                return HttpResponse(f'<div class="text-red-600 text-sm">Error: {str(e)}</div>', status=400)
            else:
                messages.error(self.request, f"Error updating sub-category: {str(e)}")
                return self.form_invalid(form)

    def form_invalid(self, form):
        if self.request.headers.get("HX-Request"):
            return render(
                self.request,
                "sales_distribution/partials/subcategory_edit_form.html",
                {"form": form, "subcategory": self.object},
            )
        return super().form_invalid(form)


class WorkOrderSubcategoryDeleteView(LoginRequiredMixin, DeleteView):
    """
    WorkOrder Subcategory delete view
    """
    model = WorkorderSubcategory
    pk_url_kwarg = "sub_cid"
    success_url = reverse_lazy("sales_distribution:subcategory_list")

    def get_object(self):
        return get_object_or_404(WorkorderSubcategory, sub_cid=self.kwargs["sub_cid"])

    def delete(self, request, *args, **kwargs):
        subcategory = self.get_object()
        if WorkOrder.objects.filter(scid=subcategory).exists():
            if request.headers.get("HX-Request"):
                return HttpResponse(
                    '<div class="text-red-600 text-sm">Cannot delete - sub-category is being used in work orders.</div>',
                    status=400,
                )
            else:
                messages.error(request, "Cannot delete - sub-category is being used in work orders.")
                return redirect("sales_distribution:subcategory_list")
        try:
            subcategory.delete()
            if request.headers.get("HX-Request"):
                return HttpResponse("") # Empty response removes the row
            else:
                messages.success(request, "Sub-category deleted successfully!")
                return redirect("sales_distribution:subcategory_list")
        except Exception as e:
            if request.headers.get("HX-Request"):
                return HttpResponse(f'<div class="text-red-600 text-sm">Error: {str(e)}</div>', status=400)
            else:
                messages.error(request, f"Error deleting sub-category: {str(e)}")
                return redirect("sales_distribution:subcategory_list")


class WorkOrderSubcategoryEditRowView(LoginRequiredMixin, View):
    """
    HTMX view for inline editing of subcategory rows
    """
    def get(self, request, sub_cid):
        subcategory = get_object_or_404(WorkorderSubcategory, sub_cid=sub_cid)
        form = WorkOrderSubcategoryEditForm(instance=subcategory)
        return render(
            request, "sales_distribution/partials/subcategory_edit_form.html", {"form": form, "subcategory": subcategory}
        )


class WorkOrderSubcategoryCancelEditView(LoginRequiredMixin, View):
    """
    HTMX view for canceling inline edit and returning to normal row view
    """
    def get(self, request, sub_cid):
        subcategory = get_object_or_404(WorkorderSubcategory, sub_cid=sub_cid)
        return render(request, "sales_distribution/partials/subcategory_row.html", {"subcategory": subcategory})


# WOType Views
class WOTypeListView(LoginRequiredMixin, ListView):
    model = WOType
    template_name = "sales_distribution/wotype_list.html"
    context_object_name = "wotypes"
    paginate_by = 20

    def get_queryset(self):
        queryset = WOType.objects.all().order_by("-id")
        search = self.request.GET.get("search")
        if search:
            queryset = queryset.filter(wo_type__icontains=search)
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["wotype_form"] = WOTypeForm()
        context["filter_form"] = WOTypeFilterForm(self.request.GET)
        context["page_title"] = "Work Order Type Management"
        context['total_wotypes'] = WOType.objects.count()
        return context

    def get(self, request, *args, **kwargs):
        response = super().get(request, *args, **kwargs)
        if request.headers.get("HX-Request"):
            wotypes = self.get_queryset()
            total_wotypes = WOType.objects.count()
            return render(request, "sales_distribution/partials/wotype_table.html", {
                "wotypes": wotypes,
                "total_wotypes": total_wotypes
            })
        return response

@method_decorator(csrf_protect, name="dispatch")
class WOTypeCreateView(LoginRequiredMixin, CreateView):
    model = WOType
    form_class = WOTypeForm
    template_name = "sales_distribution/partials/wotype_form.html"

    def form_valid(self, form):
        try:
            with transaction.atomic():
                form.save()
                if self.request.headers.get("HX-Request"):
                    wotypes = WOType.objects.all().order_by("-id")
                    total_wotypes = WOType.objects.count()
                    return render(
                        self.request, "sales_distribution/partials/wotype_table.html", {
                            "wotypes": wotypes,
                            "total_wotypes": total_wotypes
                        }
                    )
                else:
                    messages.success(self.request, "WO Type created successfully!")
                    return redirect("sales_distribution:wotype_list")
        except Exception as e:
            if self.request.headers.get("HX-Request"):
                return render(
                    self.request, "sales_distribution/partials/wotype_form.html", {"form": form, "error": str(e)}
                )
            else:
                messages.error(self.request, f"Error creating WO Type: {str(e)}")
                return self.form_invalid(form)

    def form_invalid(self, form):
        if self.request.headers.get("HX-Request"):
            return render(self.request, "sales_distribution/partials/wotype_form.html", {"form": form})
        return super().form_invalid(form)

@method_decorator(csrf_protect, name="dispatch")
class WOTypeUpdateView(LoginRequiredMixin, UpdateView):
    model = WOType
    form_class = WOTypeEditForm
    template_name = "sales_distribution/partials/wotype_edit_form.html"
    pk_url_kwarg = "id"

    def get_object(self):
        return get_object_or_404(WOType, id=self.kwargs["id"])

    def form_valid(self, form):
        try:
            with transaction.atomic():
                wotype = form.save()
                if self.request.headers.get("HX-Request"):
                    return render(self.request, "sales_distribution/partials/wotype_row.html", {"wotype": wotype})
                else:
                    messages.success(self.request, "WO Type updated successfully!")
                    return redirect("sales_distribution:wotype_list")
        except Exception as e:
            if self.request.headers.get("HX-Request"):
                return HttpResponse(f'<div class="text-red-600 text-sm">Error: {str(e)}</div>', status=400)
            else:
                messages.error(self.request, f"Error updating WO Type: {str(e)}")
                return self.form_invalid(form)

    def form_invalid(self, form):
        if self.request.headers.get("HX-Request"):
            return render(
                self.request,
                "sales_distribution/partials/wotype_edit_form.html",
                {"form": form, "wotype": self.object},
            )
        return super().form_invalid(form)

class WOTypeDeleteView(LoginRequiredMixin, DeleteView):
    model = WOType
    pk_url_kwarg = "id"
    success_url = reverse_lazy("sales_distribution:wotype_list")

    def get_object(self):
        return get_object_or_404(WOType, id=self.kwargs["id"])

    def delete(self, request, *args, **kwargs):
        wotype = self.get_object()
        try:
            wotype.delete()
            if request.headers.get("HX-Request"):
                return HttpResponse("")
            else:
                messages.success(request, "WO Type deleted successfully!")
                return redirect("sales_distribution:wotype_list")
        except Exception as e:
            if request.headers.get("HX-Request"):
                return HttpResponse(f'<div class="text-red-600 text-sm">Error: {str(e)}</div>', status=400)
            else:
                messages.error(request, f"Error deleting WO Type: {str(e)}")
                return redirect("sales_distribution:wotype_list")

class WOTypeEditRowView(LoginRequiredMixin, View):
    def get(self, request, id):
        wotype = get_object_or_404(WOType, id=id)
        form = WOTypeEditForm(instance=wotype)
        return render(
            request, "sales_distribution/partials/wotype_edit_form.html", {"form": form, "wotype": wotype}
        )

class WOTypeCancelEditView(LoginRequiredMixin, View):
    def get(self, request, id):
        wotype = get_object_or_404(WOType, id=id)
        return render(request, "sales_distribution/partials/wotype_row.html", {"wotype": wotype})


# Customer Views - Enhanced Implementation


# Dashboard View
class SalesDistributionDashboardView(LoginRequiredMixin, View):
    """
    Sales Distribution module dashboard
    """

    def get(self, request):
        context = {
            "page_title": "Sales Distribution Dashboard",
            "category_count": WorkOrderCategory.objects.count(),
            "subcategory_count": WorkorderSubcategory.objects.count(),
            "customer_count": Customer.objects.count(),
            "workorder_count": WorkOrder.objects.count(),
            "product_count": Product.objects.count(),
        }
        return render(request, "sales_distribution/dashboard.html", context)


class CustomerListView(LoginRequiredMixin, ListView):
    """
    Customer list view - replaces ASP.NET CustomerMaster_Edit.aspx functionality
    Provides real-time search, pagination, and customer management features
    """

    model = Customer
    template_name = "sales_distribution/customer_list_enhanced.html"
    context_object_name = "customers"
    paginate_by = 25  # Increased for better UX

    def get_queryset(self):
        # Order by salesid (primary key) descending to match ASP.NET behavior
        queryset = Customer.objects.select_related(
            "registered_country", "registered_state", "registered_city", "finyearid", "compid"
        ).order_by("-salesid")

        # Apply search filter if provided - matches ASP.NET TxtSearchValue functionality
        search = self.request.GET.get("search")
        if search:
            # Search across customer name, ID, and address (like ASP.NET AutoComplete)
            queryset = queryset.filter(
                Q(customer_name__icontains=search)
                | Q(customerid__icontains=search)
                | Q(registered_address__icontains=search)
            )

        # Apply quick filters
        filter_type = self.request.GET.get("filter")
        if filter_type == "recent":
            from datetime import datetime, timedelta
            thirty_days_ago = datetime.now() - timedelta(days=30)
            # Assuming sysdate is in DD/MM/YYYY format, we need to filter appropriately
            # This might need adjustment based on your actual date format
            queryset = queryset.extra(
                where=["STR_TO_DATE(sysdate, '%%d/%%m/%%Y') >= %s"],
                params=[thirty_days_ago.strftime('%Y-%m-%d')]
            )
        elif filter_type == "active":
            # Filter for active customers (you might need to define what "active" means)
            queryset = queryset.exclude(customer_name__isnull=True).exclude(customer_name__exact='')

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["filter_form"] = CustomerFilterForm(self.request.GET)
        context["page_title"] = "Customer Master - Edit"

        # Add formatted address for each customer (matches ASP.NET address concatenation)
        for customer in context["customers"]:
            address_parts = []
            if customer.registered_address:
                address_parts.append(customer.registered_address)
            if customer.registered_city:
                address_parts.append(customer.registered_city.cityname)
            if customer.registered_state:
                address_parts.append(customer.registered_state.statename)
            if customer.registered_country:
                address_parts.append(customer.registered_country.countryname)

            customer.full_address = ", ".join(filter(None, address_parts)) + "." if address_parts else ""

        return context

    def get(self, request, *args, **kwargs):
        """Override to handle HTMX requests for real-time search"""
        response = super().get(request, *args, **kwargs)

        # If it's an HTMX request, return only the table partial
        if request.headers.get("HX-Request"):
            customers = self.get_queryset()

            # Add formatted address for HTMX response
            for customer in customers:
                address_parts = []
                if customer.registered_address:
                    address_parts.append(customer.registered_address)
                if customer.registered_city:
                    address_parts.append(customer.registered_city.cityname)
                if customer.registered_state:
                    address_parts.append(customer.registered_state.statename)
                if customer.registered_country:
                    address_parts.append(customer.registered_country.countryname)

                customer.full_address = ", ".join(filter(None, address_parts)) + "." if address_parts else ""

            return render(request, "sales_distribution/partials/customer_table_enhanced.html", {
                "customers": customers,
                "request": request,
            })

        return response


class CustomerUpdateView(LoginRequiredMixin, View):
    """
    Customer update view - replaces ASP.NET CustomerMaster_Edit_Details.aspx functionality
    Provides comprehensive customer editing with pre-populated form data
    """

    template_name = "sales_distribution/customer_edit.html"

    def get(self, request, salesid):
        """Display customer edit form with pre-populated data"""
        customer = get_object_or_404(Customer, salesid=salesid)
        form = CustomerForm(instance=customer)

        context = {
            "form": form,
            "customer": customer,
            "page_title": "Customer Master - Edit",
        }
        return render(request, self.template_name, context)

    def post(self, request, salesid):
        """Handle customer update via POST - matches ASP.NET Update_Click logic"""
        customer = get_object_or_404(Customer, salesid=salesid)
        form = CustomerForm(request.POST, instance=customer)

        if form.is_valid():
            try:
                with transaction.atomic():
                    updated_customer = form.save(commit=False)

                    # Update system fields (matches ASP.NET code)
                    from datetime import datetime

                    updated_customer.sysdate = datetime.now().strftime("%d-%m-%Y")  # ASP.NET format
                    updated_customer.systime = datetime.now().strftime("%H:%M:%S")
                    updated_customer.sessionid = request.user.username if hasattr(request, "user") else "system"

                    # Keep original customer ID and other system fields
                    updated_customer.customerid = customer.customerid
                    updated_customer.compid = customer.compid
                    updated_customer.finyearid = customer.finyearid

                    updated_customer.save()

                    # Show success message and redirect (matches ASP.NET redirect)
                    messages.success(request, "Customer updated successfully")
                    return redirect("sales_distribution:customer_list")

            except Exception as e:
                messages.error(request, f"Error updating customer: {str(e)}")

        context = {
            "form": form,
            "customer": customer,
            "page_title": "Customer Master - Edit",
        }
        return render(request, self.template_name, context)


class CustomerCreateView(LoginRequiredMixin, View):
    """
    Customer creation view - replaces ASP.NET CustomerMaster_New.aspx functionality
    Provides comprehensive customer registration with auto ID generation
    """

    template_name = "sales_distribution/customer_new.html"

    def get(self, request):
        """Display customer creation form"""
        form = CustomerForm()
        context = {
            "form": form,
            "page_title": "Customer Master - New",
        }
        return render(request, self.template_name, context)

    def post(self, request):
        """Handle customer creation via POST - matches ASP.NET Submit_Click logic"""
        form = CustomerForm(request.POST)

        if form.is_valid():
            try:
                with transaction.atomic():
                    customer = form.save(commit=False)

                    # Auto-populate system fields (matches ASP.NET code)
                    from datetime import datetime

                    customer.sysdate = datetime.now().strftime("%d-%m-%Y")  # ASP.NET format
                    customer.systime = datetime.now().strftime("%H:%M:%S")
                    customer.sessionid = request.user.username if hasattr(request, "user") else "system"

                    # Set company and financial year from session (matches ASP.NET Session variables)
                    from sys_admin.models import FinancialYear, Company
                    
                    # Get the current financial year (where Flag=1)
                    current_fin_year = FinancialYear.objects.filter(is_current=True).first()
                    if not current_fin_year:
                        # Fallback to the latest financial year
                        current_fin_year = FinancialYear.objects.order_by('-finyearid').first()
                    
                    # Get the default company (where Flag=1 or defaultcomp=1)
                    current_company = Company.objects.filter(defaultcomp=1).first()
                    if not current_company:
                        # Fallback to the first company
                        current_company = Company.objects.first()
                    
                    if current_company:
                        customer.compid_id = current_company.compid
                    else:
                        customer.compid_id = 1  # Fallback
                        
                    if current_fin_year:
                        customer.finyearid_id = current_fin_year.finyearid
                    else:
                        customer.finyearid_id = 1  # Fallback

                    # Generate customer ID automatically (matches ASP.NET getCustChar logic)
                    customer_id = self.generate_customer_id(customer.customer_name)
                    customer.customerid = customer_id

                    customer.save()

                    # Show success message (matches ASP.NET redirect with message)
                    messages.success(request, "Customer is registered successfully")
                    return redirect("sales_distribution:customer_list")

            except Exception as e:
                messages.error(request, f"Error creating customer: {str(e)}")

        context = {
            "form": form,
            "page_title": "Customer Master - New",
        }
        return render(request, self.template_name, context)

    def generate_customer_id(self, customer_name):
        """
        Generate customer ID based on customer name - matches ASP.NET getCustChar and auto-increment logic
        """
        # Extract character prefix from customer name (like ASP.NET getCustChar)
        if customer_name:
            char_str = customer_name[0].upper()  # First character
        else:
            char_str = "C"  # Default

        # Find the highest existing customer ID with this prefix (matches ASP.NET logic)
        existing_customers = Customer.objects.filter(
            customerid__startswith=char_str,
            # TODO: Add CompId filter when session context is available
        ).order_by("-customerid")

        if existing_customers.exists():
            # Extract number from existing ID and increment (matches ASP.NET substring logic)
            last_id = existing_customers.first().customerid
            if len(last_id) > 1:
                try:
                    number_part = int(last_id[1:])  # Remove prefix, get number
                    new_number = number_part + 1
                except (ValueError, IndexError):
                    new_number = 1
            else:
                new_number = 1
        else:
            new_number = 1

        # Format with 3-digit padding (matches ASP.NET "D3" format)
        return f"{char_str}{new_number:03d}"


# HTMX helper views for cascading dropdowns
class GetStatesAjaxView(LoginRequiredMixin, View):
    """HTMX view for getting states based on country selection"""

    def get(self, request):
        # Check which address section is being updated
        country_id = None
        address_type = None

        # Customer form parameters
        if request.GET.get("registered_country"):
            country_id = request.GET.get("registered_country")
            address_type = "registered"
        elif request.GET.get("works_country"):
            country_id = request.GET.get("works_country")
            address_type = "works"
        elif request.GET.get("material_country"):
            country_id = request.GET.get("material_country")
            address_type = "material"
        # Enquiry form parameters
        elif request.GET.get("regdcountry"):
            country_id = request.GET.get("regdcountry")
            address_type = "registered"
        elif request.GET.get("workcountry"):
            country_id = request.GET.get("workcountry")
            address_type = "works"
        elif request.GET.get("materialdelcountry"):
            country_id = request.GET.get("materialdelcountry")
            address_type = "material"

        states = []
        if country_id and country_id != "" and country_id != "Select":
            from sys_admin.models import State

            states = State.objects.filter(cid=country_id).order_by("statename")

        # Return HTML options for the state dropdown
        html = '<option value="">Select State</option>'
        for state in states:
            html += f'<option value="{state.sid}">{state.statename}</option>'

        return HttpResponse(html)


class GetCitiesAjaxView(LoginRequiredMixin, View):
    """HTMX view for getting cities based on state selection"""

    def get(self, request):
        # Check which address section is being updated
        state_id = None
        address_type = None

        # Customer form parameters
        if request.GET.get("registered_state"):
            state_id = request.GET.get("registered_state")
            address_type = "registered"
        elif request.GET.get("works_state"):
            state_id = request.GET.get("works_state")
            address_type = "works"
        elif request.GET.get("material_state"):
            state_id = request.GET.get("material_state")
            address_type = "material"
        # Enquiry form parameters
        elif request.GET.get("regdstate"):
            state_id = request.GET.get("regdstate")
            address_type = "registered"
        elif request.GET.get("workstate"):
            state_id = request.GET.get("workstate")
            address_type = "works"
        elif request.GET.get("materialdelstate"):
            state_id = request.GET.get("materialdelstate")
            address_type = "material"

        cities = []
        if state_id and state_id != "" and state_id != "Select":
            from sys_admin.models import City

            cities = City.objects.filter(sid=state_id).order_by("cityname")

        # Return HTML options for the city dropdown
        html = '<option value="">Select City</option>'
        for city in cities:
            html += f'<option value="{city.cityid}">{city.cityname}</option>'

        return HttpResponse(html)


# Product Views - Replaces ASP.NET Product.aspx functionality
class ProductListView(LoginRequiredMixin, ListView):
    """
    Product list view - replaces ASP.NET Product.aspx functionality
    Simple product category management with inline editing
    """

    model = Product
    template_name = "sales_distribution/product_list.html"
    context_object_name = "products"
    paginate_by = 20  # Matches ASP.NET PageSize="20"

    def get_queryset(self):
        # Order by ID descending to match ASP.NET behavior
        queryset = Product.objects.all().order_by("-id")

        # Apply search filter if provided
        search = self.request.GET.get("search")
        if search:
            queryset = queryset.filter(name__icontains=search)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["product_form"] = ProductForm()
        context["filter_form"] = ProductFilterForm(self.request.GET)
        context["page_title"] = "Product Master"
        return context

    def get(self, request, *args, **kwargs):
        """Override to handle HTMX requests for real-time search"""
        response = super().get(request, *args, **kwargs)

        # If it's an HTMX request, return only the table partial
        if request.headers.get("HX-Request"):
            products = self.get_queryset()
            return render(request, "sales_distribution/partials/product_table.html", {"products": products})

        return response


@method_decorator(csrf_protect, name="dispatch")
class ProductCreateView(LoginRequiredMixin, CreateView):
    """
    Product creation view - replaces ASP.NET Product.aspx Add functionality
    Uses HTMX for seamless form submission without page refresh
    """

    model = Product
    form_class = ProductForm
    template_name = "sales_distribution/partials/product_form.html"

    def form_valid(self, form):
        try:
            with transaction.atomic():
                product = form.save()

                if self.request.headers.get("HX-Request"):
                    # Return updated table for HTMX
                    products = Product.objects.all().order_by("-id")
                    return render(
                        self.request, "sales_distribution/partials/product_table.html", {"products": products}
                    )
                else:
                    messages.success(self.request, "Product created successfully!")
                    return redirect("sales_distribution:product_list")

        except Exception as e:
            if self.request.headers.get("HX-Request"):
                return render(
                    self.request, "sales_distribution/partials/product_form.html", {"form": form, "error": str(e)}
                )
            else:
                messages.error(self.request, f"Error creating product: {str(e)}")
                return self.form_invalid(form)

    def form_invalid(self, form):
        if self.request.headers.get("HX-Request"):
            return render(self.request, "sales_distribution/partials/product_form.html", {"form": form})
        return super().form_invalid(form)


@method_decorator(csrf_protect, name="dispatch")
class ProductUpdateView(LoginRequiredMixin, UpdateView):
    """
    Product update view - replaces ASP.NET Product.aspx Update functionality
    Supports both modal and inline editing via HTMX
    """

    model = Product
    form_class = ProductForm
    template_name = "sales_distribution/partials/product_edit_form.html"
    pk_url_kwarg = "id"

    def get_object(self):
        return get_object_or_404(Product, id=self.kwargs["id"])

    def form_valid(self, form):
        try:
            with transaction.atomic():
                product = form.save()

                if self.request.headers.get("HX-Request"):
                    # Return updated row for HTMX
                    return render(self.request, "sales_distribution/partials/product_row.html", {"product": product})
                else:
                    messages.success(self.request, "Product updated successfully!")
                    return redirect("sales_distribution:product_list")

        except Exception as e:
            if self.request.headers.get("HX-Request"):
                return HttpResponse(f'<div class="text-red-600 text-sm">Error: {str(e)}</div>', status=400)
            else:
                messages.error(self.request, f"Error updating product: {str(e)}")
                return self.form_invalid(form)

    def form_invalid(self, form):
        if self.request.headers.get("HX-Request"):
            return render(
                self.request,
                "sales_distribution/partials/product_edit_form.html",
                {"form": form, "product": self.object},
            )
        return super().form_invalid(form)


class ProductDeleteView(LoginRequiredMixin, DeleteView):
    """
    Product delete view with usage validation
    """

    model = Product
    pk_url_kwarg = "id"
    success_url = reverse_lazy("sales_distribution:product_list")

    def get_object(self):
        return get_object_or_404(Product, id=self.kwargs["id"])

    def delete(self, request, *args, **kwargs):
        product = self.get_object()

        try:
            product.delete()
            if request.headers.get("HX-Request"):
                return HttpResponse('<div class="text-green-600 text-sm">Product deleted successfully.</div>')
            else:
                messages.success(request, "Product deleted successfully!")
                return redirect("sales_distribution:product_list")
        except Exception as e:
            if request.headers.get("HX-Request"):
                return HttpResponse(f'<div class="text-red-600 text-sm">Error: {str(e)}</div>', status=400)
            else:
                messages.error(request, f"Error deleting product: {str(e)}")
                return redirect("sales_distribution:product_list")


class ProductEditRowView(LoginRequiredMixin, View):
    """
    HTMX view for inline editing of product rows
    """

    def get(self, request, id):
        product = get_object_or_404(Product, id=id)
        form = ProductForm(instance=product)
        return render(
            request, "sales_distribution/partials/product_edit_form.html", {"form": form, "product": product}
        )


class ProductCancelEditView(LoginRequiredMixin, View):
    """
    HTMX view for canceling inline edit and returning to normal row view
    """

    def get(self, request, id):
        product = get_object_or_404(Product, id=id)
        return render(request, "sales_distribution/partials/product_row.html", {"product": product})


# Enquiry Views - Replaces ASP.NET CustEnquiry_New.aspx functionality
class EnquiryCreateView(LoginRequiredMixin, View):
    """
    Customer Enquiry creation view - replaces ASP.NET CustEnquiry_New.aspx functionality
    Supports both new customer enquiries and existing customer enquiries
    """

    template_name = "sales_distribution/enquiry_new.html"

    def get(self, request):
        """Display enquiry creation form"""
        form = EnquiryForm()
        context = {
            "form": form,
            "page_title": "Customer Enquiry - New",
        }
        return render(request, self.template_name, context)

    def post(self, request):
        """Handle enquiry creation via POST - matches ASP.NET Submit_Click logic"""
        form = EnquiryForm(request.POST)

        if form.is_valid():
            try:
                with transaction.atomic():
                    enquiry = form.save(commit=False)

                    # Auto-populate system fields (matches ASP.NET code)
                    from datetime import datetime

                    enquiry.sysdate = datetime.now().strftime("%d-%m-%Y")  # ASP.NET format
                    enquiry.systime = datetime.now().strftime("%H:%M:%S")
                    enquiry.sessionid = request.user.username if hasattr(request, "user") else "system"

                    # Set company and financial year from session (matches ASP.NET Session variables)
                    from sys_admin.models import FinancialYear, Company
                    
                    # Get the current financial year (where Flag=1)
                    current_fin_year = FinancialYear.objects.filter(is_current=True).first()
                    if not current_fin_year:
                        # Fallback to the latest financial year
                        current_fin_year = FinancialYear.objects.order_by('-finyearid').first()
                    
                    # Get the default company (where Flag=1 or defaultcomp=1)
                    current_company = Company.objects.filter(defaultcomp=1).first()
                    if not current_company:
                        # Fallback to the first company
                        current_company = Company.objects.first()
                    
                    if current_company:
                        enquiry.compid_id = current_company.compid
                    else:
                        enquiry.compid_id = 1  # Fallback
                        
                    if current_fin_year:
                        enquiry.finyearid_id = current_fin_year.finyearid
                    else:
                        enquiry.finyearid_id = 1  # Fallback

                    # Handle existing vs new customer logic (matches ASP.NET Radio button logic)
                    customer_type = request.POST.get('customer_type', 'new')
                    
                    if customer_type == 'existing':
                        # For existing customer, validate customer exists and populate customerid
                        if enquiry.customername:
                            # Extract customer ID from name (matches ASP.NET getCode logic)
                            customer_code = self.extract_customer_code(enquiry.customername)
                            enquiry.customerid = customer_code
                            enquiry.flag = 1  # Existing customer flag
                    else:
                        # New customer enquiry
                        enquiry.flag = 0  # New customer flag

                    enquiry.save()

                    # Handle file attachments (matches ASP.NET file upload logic)
                    self.handle_file_attachments(request, enquiry)

                    # Show success message (matches ASP.NET redirect with message)
                    messages.success(request, "Enquiry is generated successfully")
                    return redirect("sales_distribution:enquiry_new")

            except Exception as e:
                messages.error(request, f"Error creating enquiry: {str(e)}")

        context = {
            "form": form,
            "page_title": "Customer Enquiry - New",
        }
        return render(request, self.template_name, context)

    def extract_customer_code(self, customer_text):
        """
        Extract customer code from customer name text - matches ASP.NET getCode logic
        Expected format: "Customer Name [CUSTOMERCODE]"
        """
        if '[' in customer_text and ']' in customer_text:
            # Split by '[' and get the last part, then remove ']'
            parts = customer_text.split('[')
            if len(parts) > 1:
                code_part = parts[-1].replace(']', '').strip()
                return code_part
        return customer_text  # Return as-is if no code found

    def handle_file_attachments(self, request, enquiry):
        """
        Handle file attachments for enquiry - matches ASP.NET file upload logic
        """
        files = request.FILES.getlist('attachments')
        for uploaded_file in files:
            if uploaded_file:
                try:
                    # Create attachment record
                    attachment = EnquiryAttachment()
                    attachment.enqid = enquiry
                    attachment.compid_id = enquiry.compid_id
                    attachment.sessionid = enquiry.sessionid
                    attachment.finyearid_id = enquiry.finyearid_id
                    attachment.filename = uploaded_file.name
                    attachment.filesize = uploaded_file.size
                    attachment.contenttype = uploaded_file.content_type
                    attachment.filedata = uploaded_file.read()
                    attachment.save()
                except Exception as e:
                    # Log error but don't fail the enquiry creation
                    print(f"Error saving attachment: {str(e)}")


class EnquiryListView(LoginRequiredMixin, ListView):
    """
    Enquiry list view - provides overview of all enquiries
    """

    model = Enquiry
    template_name = "sales_distribution/enquiry_list.html"
    context_object_name = "enquiries"
    paginate_by = 25

    def get_queryset(self):
        # Order by enquiry ID descending to show latest first
        queryset = Enquiry.objects.select_related(
            "regdcountry", "regdstate", "regdcity", "compid", "finyearid"
        ).order_by("-enqid")

        # Apply search filter if provided
        search = self.request.GET.get("search")
        if search:
            queryset = queryset.filter(
                Q(customername__icontains=search)
                | Q(customerid__icontains=search)
                | Q(enquiryfor__icontains=search)
                | Q(contactperson__icontains=search)
            )

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Customer Enquiries"
        return context


class EnquiryDetailView(LoginRequiredMixin, View):
    """
    Enquiry detail view - displays full enquiry information
    """

    template_name = "sales_distribution/enquiry_detail.html"

    def get(self, request, enqid):
        """Display enquiry details"""
        enquiry = get_object_or_404(Enquiry, enqid=enqid)
        attachments = EnquiryAttachment.objects.filter(enqid=enquiry)

        context = {
            "enquiry": enquiry,
            "attachments": attachments,
            "page_title": f"Enquiry Details - {enquiry.customername}",
        }
        return render(request, self.template_name, context)


class EnquiryUpdateView(LoginRequiredMixin, View):
    """
    Enquiry update view - replaces ASP.NET CustEnquiry_Edit.aspx functionality
    """

    template_name = "sales_distribution/enquiry_edit.html"

    def get(self, request, enqid):
        """Display enquiry edit form with pre-populated data"""
        enquiry = get_object_or_404(Enquiry, enqid=enqid)
        form = EnquiryForm(instance=enquiry)

        context = {
            "form": form,
            "enquiry": enquiry,
            "page_title": "Customer Enquiry - Edit",
        }
        return render(request, self.template_name, context)

    def post(self, request, enqid):
        """Handle enquiry update via POST"""
        enquiry = get_object_or_404(Enquiry, enqid=enqid)
        form = EnquiryForm(request.POST, instance=enquiry)

        if form.is_valid():
            try:
                with transaction.atomic():
                    updated_enquiry = form.save(commit=False)

                    # Update system fields
                    from datetime import datetime

                    updated_enquiry.sysdate = datetime.now().strftime("%d-%m-%Y")
                    updated_enquiry.systime = datetime.now().strftime("%H:%M:%S")
                    updated_enquiry.sessionid = request.user.username if hasattr(request, "user") else "system"

                    # Keep original system fields
                    updated_enquiry.compid = enquiry.compid
                    updated_enquiry.finyearid = enquiry.finyearid

                    updated_enquiry.save()

                    # Handle new file attachments
                    self.handle_file_attachments(request, updated_enquiry)

                    messages.success(request, "Enquiry updated successfully")
                    return redirect("sales_distribution:enquiry_detail", enqid=enquiry.enqid)

            except Exception as e:
                messages.error(request, f"Error updating enquiry: {str(e)}")

        context = {
            "form": form,
            "enquiry": enquiry,
            "page_title": "Customer Enquiry - Edit",
        }
        return render(request, self.template_name, context)

    def handle_file_attachments(self, request, enquiry):
        """Handle new file attachments during update"""
        files = request.FILES.getlist('new_attachments')
        for uploaded_file in files:
            if uploaded_file:
                try:
                    attachment = EnquiryAttachment()
                    attachment.enqid = enquiry
                    attachment.compid = enquiry.compid
                    attachment.sessionid = enquiry.sessionid
                    attachment.finyearid = enquiry.finyearid
                    attachment.filename = uploaded_file.name
                    attachment.filesize = uploaded_file.size
                    attachment.contenttype = uploaded_file.content_type
                    attachment.filedata = uploaded_file.read()
                    attachment.save()
                except Exception as e:
                    print(f"Error saving attachment: {str(e)}")


# HTMX helper views for customer autocomplete
class CustomerAutocompleteView(LoginRequiredMixin, View):
    """
    HTMX view for customer autocomplete - replaces ASP.NET WebMethod sql
    """

    def get(self, request):
        search_term = request.GET.get('q', '')
        customers = []

        if search_term and len(search_term) >= 2:
            # Search existing customers (matches ASP.NET logic)
            customer_queryset = Customer.objects.filter(
                customer_name__icontains=search_term
            ).order_by('customer_name')[:10]  # Limit to 10 results

            for customer in customer_queryset:
                customers.append({
                    'id': customer.customerid,
                    'text': f"{customer.customer_name} [{customer.customerid}]"
                })

        return JsonResponse({
            'customers': customers
        })


class GetCustomerDetailsView(LoginRequiredMixin, View):
    """
    HTMX view for getting customer details - replaces ASP.NET btnView_Click functionality
    """

    def get(self, request):
        customer_text = request.GET.get('customer_name', '')
        
        if '[' in customer_text and ']' in customer_text:
            # Extract customer code from text like "Customer Name [CODE]"
            parts = customer_text.split('[')
            if len(parts) > 1:
                customer_code = parts[-1].replace(']', '').strip()
                
                try:
                    customer = Customer.objects.get(customerid=customer_code)
                    
                    # Return customer data as JSON (matches ASP.NET logic)
                    customer_data = {
                        'success': True,
                        'customer': {
                            'registered_address': customer.registered_address or '',
                            'registered_country': customer.registered_country_id if customer.registered_country else '',
                            'registered_state': customer.registered_state_id if customer.registered_state else '',
                            'registered_city': customer.registered_city_id if customer.registered_city else '',
                            'registered_pin': customer.registered_pin or '',
                            'registered_contact_no': customer.registered_contact_no or '',
                            'regdfaxno': customer.regdfaxno or '',
                            'works_address': customer.works_address or '',
                            'works_country': customer.works_country_id if customer.works_country else '',
                            'works_state': customer.works_state_id if customer.works_state else '',
                            'works_city': customer.works_city_id if customer.works_city else '',
                            'works_pin': customer.works_pin or '',
                            'works_contact_no': customer.works_contact_no or '',
                            'workfaxno': customer.workfaxno or '',
                            'material_address': customer.material_address or '',
                            'material_country': customer.material_country_id if customer.material_country else '',
                            'material_state': customer.material_state_id if customer.material_state else '',
                            'material_city': customer.material_city_id if customer.material_city else '',
                            'material_pin': customer.material_pin or '',
                            'material_contact_no': customer.material_contact_no or '',
                            'materialdelfaxno': customer.materialdelfaxno or '',
                            'contact_person': customer.contact_person or '',
                            'email': customer.email or '',
                            'contact_no': customer.contact_no or '',
                            'juridictioncode': customer.juridictioncode or '',
                            'commissionurate': customer.commissionurate or '',
                            'tinvatno': customer.tinvatno or '',
                            'eccno': customer.eccno or '',
                            'divn': customer.divn or '',
                            'tincstno': customer.tincstno or '',
                            'range': customer.range or '',
                            'panno': customer.panno or '',
                            'tdscode': customer.tdscode or '',
                        }
                    }
                    
                    return JsonResponse(customer_data)
                    
                except Customer.DoesNotExist:
                    pass
        
        return JsonResponse({
            'success': False,
            'error': 'Customer not found'
        })


# Quotation Views - Replaces ASP.NET Quotation_New.aspx functionality
class QuotationSelectionView(LoginRequiredMixin, View):
    """
    Quotation enquiry selection view - replaces ASP.NET Quotation_New.aspx functionality
    Shows enquiries that can be converted to quotations
    """

    template_name = "sales_distribution/quotation_selection.html"

    def get(self, request):
        """Display enquiry selection form and results"""
        form = EnquirySelectionForm(request.GET or None)
        
        # Build base query - show ALL enquiries (users can create multiple quotations for same enquiry)
        queryset = Enquiry.objects.select_related(
            'regdcountry', 'regdstate', 'regdcity', 'compid', 'finyearid'
        ).order_by('-enqid')
        
        # Apply search filters if form is valid and has data
        if form.is_valid():
            enquiry_id = form.cleaned_data.get('enquiry_id')
            customer_id = form.cleaned_data.get('customer_id')
            
            if enquiry_id:
                queryset = queryset.filter(enqid=enquiry_id)
            
            if customer_id:
                queryset = queryset.filter(customerid__icontains=customer_id)
        
        # Limit to recent enquiries for performance (last 50)
        queryset = queryset[:50]
        
        # Get enquiries with quotation count (matches ASP.NET logic)
        enquiries = []
        for enquiry in queryset:
            quotation_count = Quotation.objects.filter(enqid=enquiry).count()
            enquiry.quotation_count = quotation_count
            enquiries.append(enquiry)

        context = {
            "form": form,
            "enquiries": enquiries,
            "page_title": "Select Enquiry for Quotation",
        }
        return render(request, self.template_name, context)


class QuotationCreateFromEnquiryView(LoginRequiredMixin, View):
    """
    Handle quotation creation from enquiry list with from_enquiry parameter
    Redirects to appropriate quotation creation view
    """
    
    def get(self, request):
        """Handle GET request with from_enquiry parameter"""
        from_enquiry = request.GET.get('from_enquiry')
        
        if from_enquiry:
            try:
                enquiry_id = int(from_enquiry)
                # Verify enquiry exists
                enquiry = get_object_or_404(Enquiry, enqid=enquiry_id)
                
                # Redirect to quotation creation with enquiry ID
                return redirect('sales_distribution:quotation_create', enqid=enquiry_id)
            except (ValueError, TypeError):
                messages.error(request, "❌ Invalid enquiry ID provided")
        else:
            messages.error(request, "❌ No enquiry specified for quotation creation")
        
        # Fallback to enquiry list
        return redirect('sales_distribution:enquiry_list')


class QuotationCreateView(LoginRequiredMixin, View):
    """
    Quotation creation view - replaces ASP.NET Quotation_New_Details.aspx functionality
    Three-step process: Customer Info → Item Details → Terms & Conditions
    Uses session-based temporary storage like ASP.NET temporary table
    """

    template_name = "sales_distribution/quotation_new.html"

    def get(self, request, enqid):
        """Display quotation creation form for selected enquiry"""
        enquiry = get_object_or_404(Enquiry, enqid=enqid)
        customer = None
        
        # Get customer details if customerid exists (matches ASP.NET logic)
        if enquiry.customerid:
            try:
                customer = Customer.objects.get(customerid=enquiry.customerid)
            except Customer.DoesNotExist:
                pass
        
        # Initialize forms
        quotation_form = QuotationForm()
        
        # Pre-populate enquiry and customer data (matches ASP.NET)
        quotation_form.initial = {
            'enqid': enquiry.enqid,
            'customerid': enquiry.customerid,
            'quotationno': self.generate_quotation_number(),
        }
        
        # Generate session key for temporary items
        temp_items_key = f"quotation_temp_items_{request.user.username}_{enquiry.enqid}"
        
        # Clear temporary items only when starting fresh (no tab parameter)
        # This allows items to persist when navigating between tabs
        current_tab = request.GET.get('tab', '0')  # Default to first tab
        if current_tab == '0' and 'clear' not in request.GET:
            # Only clear if we're on the first tab and not explicitly told to preserve items
            # This prevents clearing items when redirecting after adding an item
            pass
        elif 'clear' in request.GET:
            # Explicit request to clear items
            if temp_items_key in request.session:
                del request.session[temp_items_key]

        context = {
            "enquiry": enquiry,
            "customer": customer,
            "quotation_form": quotation_form,
            "page_title": f"Create Quotation for Enquiry #{enquiry.enqid}",
            "units": Unit.objects.all().order_by("unitname"),
            "current_tab": int(current_tab),
            "temp_items": request.session.get(temp_items_key, []),
        }
        return render(request, self.template_name, context)

    def post(self, request, enqid):
        """Handle quotation operations - add items, save quotation, etc."""
        enquiry = get_object_or_404(Enquiry, enqid=enqid)
        customer = None
        
        if enquiry.customerid:
            try:
                customer = Customer.objects.get(customerid=enquiry.customerid)
            except Customer.DoesNotExist:
                pass
        
        action = request.POST.get('action', 'save_quotation')
        temp_items_key = f"quotation_temp_items_{request.user.username}_{enquiry.enqid}"
        current_tab = int(request.POST.get('current_tab', '0'))
        
        # Handle different actions (matches ASP.NET button clicks)
        if action == 'add_item':
            return self.handle_add_item(request, enquiry, temp_items_key, current_tab)
        elif action == 'delete_item':
            return self.handle_delete_item(request, enquiry, temp_items_key, current_tab)
        elif action == 'save_quotation':
            return self.handle_save_quotation(request, enquiry, temp_items_key)
        else:
            # Default to quotation creation
            return self.handle_save_quotation(request, enquiry, temp_items_key)
    
    def handle_add_item(self, request, enquiry, temp_items_key, current_tab):
        """Add item to temporary storage - matches ASP.NET Button5_Click"""
        # Get form data
        item_desc = request.POST.get('item_desc', '').strip()
        quantity = request.POST.get('quantity', '').strip()
        unit_id = request.POST.get('unit', '').strip()
        rate = request.POST.get('rate', '').strip()
        discount = request.POST.get('discount', '0').strip() or '0'
        
        # Validation (matches ASP.NET validation)
        errors = []
        if not item_desc:
            errors.append("Item description is required")
        if not quantity:
            errors.append("Quantity is required")
        elif not self.is_valid_number(quantity):
            errors.append("Quantity must be a valid number")
        if not unit_id or unit_id == 'Select':
            errors.append("Unit is required")
        if not rate:
            errors.append("Rate is required")
        elif not self.is_valid_number(rate):
            errors.append("Rate must be a valid number")
        if not self.is_valid_number(discount):
            errors.append("Discount must be a valid number")
        
        if not errors:
            # Get unit details
            try:
                unit = Unit.objects.get(id=unit_id)
                unit_symbol = unit.symbol or unit.unitname
            except Unit.DoesNotExist:
                errors.append("Invalid unit selected")
            
            if not errors:
                # Add to temporary storage (matches ASP.NET temp table)
                temp_items = request.session.get(temp_items_key, [])
                temp_items.append({
                    'item_desc': item_desc,
                    'quantity': float(quantity),
                    'unit_id': int(unit_id),
                    'unit_symbol': unit_symbol,
                    'rate': float(rate),
                    'discount': float(discount),
                    'line_total': (float(quantity) * float(rate)) - float(discount)
                })
                request.session[temp_items_key] = temp_items
                request.session.modified = True
                
                messages.success(request, "Item added successfully")
                # Redirect to items tab (tab 1)
                return redirect(f"{reverse('sales_distribution:quotation_create', args=[enquiry.enqid])}?tab=1")
        
        # If errors, show form with errors
        for error in errors:
            messages.error(request, error)
        
        context = self.get_context_with_errors(request, enquiry, current_tab, temp_items_key)
        return render(request, self.template_name, context)
    
    def handle_delete_item(self, request, enquiry, temp_items_key, current_tab):
        """Delete item from temporary storage"""
        item_index = request.POST.get('item_index')
        if item_index is not None:
            try:
                index = int(item_index)
                temp_items = request.session.get(temp_items_key, [])
                if 0 <= index < len(temp_items):
                    temp_items.pop(index)
                    request.session[temp_items_key] = temp_items
                    request.session.modified = True
                    messages.success(request, "Item deleted successfully")
            except (ValueError, IndexError):
                messages.error(request, "Invalid item index")
        
        # Redirect to items tab
        return redirect(f"{reverse('sales_distribution:quotation_create', args=[enquiry.enqid])}?tab=1")
    
    def handle_save_quotation(self, request, enquiry, temp_items_key):
        """Save final quotation - matches ASP.NET Button6_Click"""
        quotation_form = QuotationForm(request.POST)
        temp_items = request.session.get(temp_items_key, [])
        
        # Check if we have items (matches ASP.NET validation)
        if not temp_items:
            messages.error(request, "Please add at least one item to the quotation")
            context = self.get_context_with_errors(request, enquiry, 1, temp_items_key)  # Show items tab
            return render(request, self.template_name, context)
        
        if quotation_form.is_valid():
            try:
                with transaction.atomic():
                    quotation = quotation_form.save(commit=False)

                    # Auto-populate system fields (matches ASP.NET code)
                    from datetime import datetime

                    quotation.sysdate = datetime.now().strftime("%d-%m-%Y")  # ASP.NET format
                    quotation.systime = datetime.now().strftime("%H:%M:%S")
                    quotation.sessionid = request.user.username if hasattr(request, "user") else "system"

                    # Set company and financial year from session
                    from sys_admin.models import FinancialYear, Company
                    
                    # Get the current financial year (where Flag=1)
                    current_fin_year = FinancialYear.objects.filter(is_current=True).first()
                    if not current_fin_year:
                        # Fallback to the latest financial year
                        current_fin_year = FinancialYear.objects.order_by('-finyearid').first()
                    
                    # Get the default company (where Flag=1 or defaultcomp=1)
                    current_company = Company.objects.filter(defaultcomp=1).first()
                    if not current_company:
                        # Fallback to the first company
                        current_company = Company.objects.first()
                    
                    if current_company:
                        quotation.compid_id = current_company.compid
                    else:
                        quotation.compid_id = 1  # Fallback
                        
                    if current_fin_year:
                        quotation.finyearid_id = current_fin_year.finyearid
                    else:
                        quotation.finyearid_id = 1  # Fallback

                    # Set enquiry and customer
                    quotation.enqid = enquiry
                    quotation.customerid = enquiry.customerid

                    # Generate quotation number if not set
                    if not quotation.quotationno:
                        quotation.quotationno = self.generate_quotation_number()

                    # Initialize approval fields
                    quotation.checked = 0
                    quotation.approve = 0
                    quotation.authorize = 0

                    quotation.save()

                    # Save quotation details from temporary storage
                    self.save_quotation_details_from_temp(quotation, temp_items)

                    # Clear temporary storage
                    if temp_items_key in request.session:
                        del request.session[temp_items_key]

                    # Update enquiry status (matches ASP.NET POStatus=1)
                    enquiry.postatus = 1
                    enquiry.save()

                    messages.success(request, "Quotation is generated.")
                    return redirect("sales_distribution:quotation_selection")

            except Exception as e:
                messages.error(request, f"Error creating quotation: {str(e)}")
        
        # If form invalid, show terms tab with errors
        context = self.get_context_with_errors(request, enquiry, 2, temp_items_key, quotation_form)
        return render(request, self.template_name, context)
    
    def get_context_with_errors(self, request, enquiry, current_tab, temp_items_key, quotation_form=None):
        """Get context for rendering form with errors"""
        customer = None
        if enquiry.customerid:
            try:
                customer = Customer.objects.get(customerid=enquiry.customerid)
            except Customer.DoesNotExist:
                pass
        
        if not quotation_form:
            quotation_form = QuotationForm()
            quotation_form.initial = {
                'enqid': enquiry.enqid,
                'customerid': enquiry.customerid,
                'quotationno': self.generate_quotation_number(),
            }
        
        return {
            "enquiry": enquiry,
            "customer": customer,
            "quotation_form": quotation_form,
            "page_title": f"Create Quotation for Enquiry #{enquiry.enqid}",
            "units": Unit.objects.all().order_by("unitname"),
            "current_tab": current_tab,
            "temp_items": request.session.get(temp_items_key, []),
        }
    
    def is_valid_number(self, value):
        """Check if value is a valid number - matches ASP.NET NumberValidationQty"""
        try:
            float(value)
            return float(value) >= 0
        except (ValueError, TypeError):
            return False

    def generate_quotation_number(self):
        """
        Generate quotation number - matches ASP.NET 4-digit sequential logic
        """
        # Get the highest existing quotation number for current company/year
        # TODO: Add company and financial year filtering when session context is available
        last_quotation = Quotation.objects.filter(
            # compid=company_id,
            # finyearid=financial_year_id
        ).order_by('-quotationno').first()

        if last_quotation and last_quotation.quotationno:
            try:
                # Extract number and increment (matches ASP.NET logic)
                last_number = int(last_quotation.quotationno)
                new_number = last_number + 1
            except (ValueError, TypeError):
                new_number = 1
        else:
            new_number = 1

        # Format as 4-digit number (matches ASP.NET "D4" format)
        return f"{new_number:04d}"

    def save_quotation_details_from_temp(self, quotation, temp_items):
        """
        Save quotation line items from temporary storage to database
        Matches ASP.NET logic of moving from temp table to actual table
        """
        for item in temp_items:
            try:
                detail = QuotationDetails()
                detail.sessionid = quotation.sessionid
                detail.compid = quotation.compid
                detail.finyearid = quotation.finyearid
                detail.mid = quotation
                detail.itemdesc = item['item_desc']
                detail.totalqty = item['quantity']
                detail.unit_id = item['unit_id']
                detail.rate = item['rate']
                detail.discount = item['discount']
                detail.save()
            except Exception as e:
                # Log error but don't fail the quotation creation
                print(f"Error saving quotation detail: {str(e)}")


class QuotationListView(LoginRequiredMixin, ListView):
    """
    Quotation list view - provides overview of all quotations with filtering
    """

    model = Quotation
    template_name = "sales_distribution/quotation_list.html"
    context_object_name = "quotations"
    paginate_by = 25

    def get_queryset(self):
        # Order by quotation ID descending to show latest first
        queryset = Quotation.objects.select_related(
            "enqid", "compid", "finyearid"
        ).order_by("-id")

        # Apply search filter if provided
        search = self.request.GET.get("search")
        if search:
            queryset = queryset.filter(
                Q(quotationno__icontains=search)
                | Q(customerid__icontains=search)
                | Q(enqid__customername__icontains=search)
                | Q(enqid__enqid__icontains=search)
            )

        # Apply status filter
        status = self.request.GET.get("status")
        if status == "draft":
            queryset = queryset.filter(checked=0)
        elif status == "checked":
            queryset = queryset.filter(checked=1, approve=0)
        elif status == "approved":
            queryset = queryset.filter(checked=1, approve=1, authorize=0)
        elif status == "authorized":
            queryset = queryset.filter(checked=1, approve=1, authorize=1)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["filter_form"] = QuotationFilterForm(self.request.GET)
        context["page_title"] = "Quotations"
        
        # Add status info for each quotation
        for quotation in context["quotations"]:
            quotation.status_display = self.get_quotation_status(quotation)
            
        return context

    def get_quotation_status(self, quotation):
        """Get human-readable status for quotation"""
        if quotation.authorize == 1:
            return {"status": "Authorized", "class": "bg-green-100 text-green-800"}
        elif quotation.approve == 1:
            return {"status": "Approved", "class": "bg-blue-100 text-blue-800"}
        elif quotation.checked == 1:
            return {"status": "Checked", "class": "bg-yellow-100 text-yellow-800"}
        else:
            return {"status": "Draft", "class": "bg-gray-100 text-gray-800"}


class QuotationDetailView(LoginRequiredMixin, View):
    """
    Quotation detail view - displays full quotation information with line items
    """

    template_name = "sales_distribution/quotation_detail.html"

    def get(self, request, quotation_id):
        """Display quotation details"""
        quotation = get_object_or_404(Quotation, id=quotation_id)
        quotation_details = QuotationDetails.objects.filter(mid=quotation).select_related("unit")

        # Calculate totals
        line_total = 0
        for detail in quotation_details:
            detail.line_amount = (detail.totalqty * detail.rate) - (detail.discount or 0)
            line_total += detail.line_amount

        context = {
            "quotation": quotation,
            "quotation_details": quotation_details,
            "line_total": line_total,
            "page_title": f"Quotation #{quotation.quotationno}",
            "status_display": self.get_quotation_status(quotation),
        }
        return render(request, self.template_name, context)

    def get_quotation_status(self, quotation):
        """Get human-readable status for quotation"""
        if quotation.authorize == 1:
            return {"status": "Authorized", "class": "bg-green-100 text-green-800"}
        elif quotation.approve == 1:
            return {"status": "Approved", "class": "bg-blue-100 text-blue-800"}
        elif quotation.checked == 1:
            return {"status": "Checked", "class": "bg-yellow-100 text-yellow-800"}
        else:
            return {"status": "Draft", "class": "bg-gray-100 text-gray-800"}


class QuotationUpdateView(LoginRequiredMixin, View):
    """
    Quotation update view - replaces ASP.NET Quotation_Edit.aspx functionality
    """

    template_name = "sales_distribution/quotation_edit.html"

    def get(self, request, quotation_id):
        """Display quotation edit form with pre-populated data"""
        quotation = get_object_or_404(Quotation, id=quotation_id)
        quotation_details = QuotationDetails.objects.filter(mid=quotation).select_related("unit")
        
        form = QuotationForm(instance=quotation)

        context = {
            "form": form,
            "quotation": quotation,
            "quotation_details": quotation_details,
            "page_title": f"Edit Quotation #{quotation.quotationno}",
            "units": Unit.objects.all().order_by("unitname"),
        }
        return render(request, self.template_name, context)

    def post(self, request, quotation_id):
        """Handle quotation update via POST"""
        quotation = get_object_or_404(Quotation, id=quotation_id)
        form = QuotationForm(request.POST, instance=quotation)

        if form.is_valid():
            try:
                with transaction.atomic():
                    updated_quotation = form.save(commit=False)

                    # Update system fields
                    from datetime import datetime

                    updated_quotation.sysdate = datetime.now().strftime("%d-%m-%Y")
                    updated_quotation.systime = datetime.now().strftime("%H:%M:%S")
                    updated_quotation.sessionid = request.user.username if hasattr(request, "user") else "system"

                    # Keep original system fields
                    updated_quotation.compid = quotation.compid
                    updated_quotation.finyearid = quotation.finyearid
                    updated_quotation.enqid = quotation.enqid
                    updated_quotation.customerid = quotation.customerid

                    updated_quotation.save()

                    # Update quotation details
                    self.update_quotation_details(request, updated_quotation)

                    messages.success(request, "Quotation updated successfully")
                    return redirect("sales_distribution:quotation_detail", quotation_id=quotation.id)

            except Exception as e:
                messages.error(request, f"Error updating quotation: {str(e)}")

        quotation_details = QuotationDetails.objects.filter(mid=quotation).select_related("unit")
        
        context = {
            "form": form,
            "quotation": quotation,
            "quotation_details": quotation_details,
            "page_title": f"Edit Quotation #{quotation.quotationno}",
            "units": Unit.objects.all().order_by("unitname"),
        }
        return render(request, self.template_name, context)

    def update_quotation_details(self, request, quotation):
        """Update quotation line items - handles both new and existing items"""
        # Delete existing details and recreate (matches ASP.NET behavior)
        QuotationDetails.objects.filter(mid=quotation).delete()
        
        # Save new details
        self.save_quotation_details(request, quotation)

    def save_quotation_details(self, request, quotation):
        """Save quotation line items from POST data"""
        # Get line item data from POST
        item_descs = request.POST.getlist('item_desc[]')
        quantities = request.POST.getlist('quantity[]')
        units = request.POST.getlist('unit[]')
        rates = request.POST.getlist('rate[]')
        discounts = request.POST.getlist('discount[]')

        # Create quotation details
        for i in range(len(item_descs)):
            if item_descs[i].strip():  # Only save non-empty items
                try:
                    detail = QuotationDetails()
                    detail.sessionid = quotation.sessionid
                    detail.compid = quotation.compid
                    detail.finyearid = quotation.finyearid
                    detail.mid = quotation
                    detail.itemdesc = item_descs[i].strip()
                    detail.totalqty = float(quantities[i])
                    detail.unit_id = int(units[i])
                    detail.rate = float(rates[i])
                    detail.discount = float(discounts[i]) if discounts[i] else 0
                    detail.save()
                except (ValueError, IndexError) as e:
                    print(f"Error saving quotation detail: {str(e)}")


# HTMX Views for Quotation Line Items (for future enhancement)
class QuotationItemAddView(LoginRequiredMixin, View):
    """HTMX view for adding quotation line items - currently using session-based approach"""

    def post(self, request):
        """Add a new line item row"""
        units = Unit.objects.all().order_by("unitname")
        row_id = request.POST.get('row_id', '0')
        
        context = {
            'units': units,
            'row_id': row_id,
        }
        return render(request, 'sales_distribution/partials/quotation_item_row.html', context)


class QuotationItemDeleteView(LoginRequiredMixin, View):
    """HTMX view for deleting quotation line items"""

    def delete(self, request, detail_id):
        """Delete a quotation detail item"""
        try:
            detail = get_object_or_404(QuotationDetails, id=detail_id)
            detail.delete()
            return HttpResponse('<div class="text-green-600 text-sm">Item deleted successfully.</div>')
        except Exception as e:
            return HttpResponse(f'<div class="text-red-600 text-sm">Error: {str(e)}</div>', status=400)


class QuotationCheckListView(LoginRequiredMixin, ListView):
    """
    Quotation Check list view - replaces ASP.NET Quotation_Check.aspx
    Shows quotations that need to be checked (unchecked quotations)
    """
    model = Quotation
    template_name = "sales_distribution/quotation_check_list.html"
    context_object_name = "quotations"
    paginate_by = 20

    def get_queryset(self):
        # Show only unchecked quotations (matches ASP.NET logic)
        queryset = Quotation.objects.select_related(
            "enqid", "compid", "finyearid"
        ).filter(
            checked__isnull=True  # OR checked=0 - show only unchecked quotations
        ).order_by("-id")

        # Apply search filters (matches ASP.NET dropdown logic)
        search_field = self.request.GET.get("search_field", "0")  # 0=Quotation No, 1=Customer
        search_value = self.request.GET.get("search_value", "")

        if search_value:
            if search_field == "0":  # Quotation No
                queryset = queryset.filter(quotationno__icontains=search_value)
            elif search_field == "1":  # Customer
                # Extract customer code from format "Customer Name [CODE]"
                if '[' in search_value and ']' in search_value:
                    customer_code = search_value.split('[')[-1].replace(']', '').strip()
                    queryset = queryset.filter(customerid=customer_code)
                else:
                    queryset = queryset.filter(customerid__icontains=search_value)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Quotation Check"
        context["search_field"] = self.request.GET.get("search_field", "0")
        context["search_value"] = self.request.GET.get("search_value", "")
        
        # Add workflow status info
        for quotation in context["quotations"]:
            quotation.can_check = True  # All quotations in this list can be checked
            
        return context

    def post(self, request, *args, **kwargs):
        """Handle bulk check operations (matches ASP.NET checkbox logic)"""
        selected_quotations = request.POST.getlist('selected_quotations')
        
        if selected_quotations:
            from datetime import datetime
            
            # Bulk update selected quotations
            Quotation.objects.filter(
                id__in=selected_quotations,
                checked__isnull=True  # Only check unchecked quotations
            ).update(
                checked=1,
                checkedby=request.user.username,
                checkeddate=datetime.now().strftime('%d-%m-%Y'),
                checkedtime=datetime.now().strftime('%H:%M:%S')
            )
            
            messages.success(request, f"Successfully checked {len(selected_quotations)} quotation(s).")
        
        return redirect('sales_distribution:quotation_check_list')


class QuotationApproveListView(LoginRequiredMixin, ListView):
    """
    Quotation Approve list view - replaces ASP.NET Quotation_Approve.aspx
    Shows quotations that are checked but not approved
    """
    model = Quotation
    template_name = "sales_distribution/quotation_approve_list.html"
    context_object_name = "quotations"
    paginate_by = 20

    def get_queryset(self):
        # Show only checked but not approved quotations (matches ASP.NET logic)
        queryset = Quotation.objects.select_related(
            "enqid", "compid", "finyearid"
        ).filter(
            checked=1,  # Must be checked
            approve__isnull=True  # Not yet approved
        ).order_by("-id")

        # Apply search filters (same logic as check view)
        search_field = self.request.GET.get("search_field", "0")
        search_value = self.request.GET.get("search_value", "")

        if search_value:
            if search_field == "0":  # Quotation No
                queryset = queryset.filter(quotationno__icontains=search_value)
            elif search_field == "1":  # Customer
                if '[' in search_value and ']' in search_value:
                    customer_code = search_value.split('[')[-1].replace(']', '').strip()
                    queryset = queryset.filter(customerid=customer_code)
                else:
                    queryset = queryset.filter(customerid__icontains=search_value)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Quotation Approve"
        context["search_field"] = self.request.GET.get("search_field", "0")
        context["search_value"] = self.request.GET.get("search_value", "")
        
        # Add workflow status info
        for quotation in context["quotations"]:
            quotation.can_approve = True  # All quotations in this list can be approved
            
        return context

    def post(self, request, *args, **kwargs):
        """Handle bulk approve operations"""
        selected_quotations = request.POST.getlist('selected_quotations')
        
        if selected_quotations:
            from datetime import datetime
            
            # Bulk update selected quotations
            Quotation.objects.filter(
                id__in=selected_quotations,
                checked=1,
                approve__isnull=True
            ).update(
                approve=1,
                approvedby=request.user.username,
                approvedate=datetime.now().strftime('%d-%m-%Y'),
                approvetime=datetime.now().strftime('%H:%M:%S')
            )
            
            messages.success(request, f"Successfully approved {len(selected_quotations)} quotation(s).")
        
        return redirect('sales_distribution:quotation_approve_list')


class QuotationAuthorizeListView(LoginRequiredMixin, ListView):
    """
    Quotation Authorize list view - replaces ASP.NET Quotation_Authorize.aspx
    Shows quotations that are approved but not authorized
    """
    model = Quotation
    template_name = "sales_distribution/quotation_authorize_list.html"
    context_object_name = "quotations"
    paginate_by = 20

    def get_queryset(self):
        # Show only approved but not authorized quotations (matches ASP.NET logic)
        queryset = Quotation.objects.select_related(
            "enqid", "compid", "finyearid"
        ).filter(
            approve=1,  # Must be approved
            authorize__isnull=True  # Not yet authorized
        ).order_by("-id")

        # Apply search filters (same logic as other views)
        search_field = self.request.GET.get("search_field", "0")
        search_value = self.request.GET.get("search_value", "")

        if search_value:
            if search_field == "0":  # Quotation No
                queryset = queryset.filter(quotationno__icontains=search_value)
            elif search_field == "1":  # Customer
                if '[' in search_value and ']' in search_value:
                    customer_code = search_value.split('[')[-1].replace(']', '').strip()
                    queryset = queryset.filter(customerid=customer_code)
                else:
                    queryset = queryset.filter(customerid__icontains=search_value)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Quotation Authorize"
        context["search_field"] = self.request.GET.get("search_field", "0")
        context["search_value"] = self.request.GET.get("search_value", "")
        
        # Add workflow status info
        for quotation in context["quotations"]:
            quotation.can_authorize = True  # All quotations in this list can be authorized
            
        return context

    def post(self, request, *args, **kwargs):
        """Handle bulk authorize operations"""
        selected_quotations = request.POST.getlist('selected_quotations')
        
        if selected_quotations:
            from datetime import datetime
            
            # Bulk update selected quotations
            Quotation.objects.filter(
                id__in=selected_quotations,
                approve=1,
                authorize__isnull=True
            ).update(
                authorize=1,
                authorizedby=request.user.username,
                authorizedate=datetime.now().strftime('%d-%m-%Y'),
                authorizetime=datetime.now().strftime('%H:%M:%S')
            )
            
            messages.success(request, f"Successfully authorized {len(selected_quotations)} quotation(s).")
        
        return redirect('sales_distribution:quotation_authorize_list')


# Keep individual action views for single quotation operations
class QuotationCheckView(LoginRequiredMixin, View):
    """View to mark a single quotation as checked"""
    
    def get(self, request, quotation_id):
        """Redirect GET requests to the quotation detail page"""
        messages.info(request, "Use the 'Check' button to mark quotation as checked.")
        return redirect("sales_distribution:quotation_detail", quotation_id=quotation_id)
    
    def post(self, request, quotation_id):
        """Handle POST request to check quotation"""
        quotation = get_object_or_404(Quotation, id=quotation_id)
        quotation.checked = 1
        quotation.checkedby = request.user.username
        quotation.checkeddate = datetime.date.today().strftime('%d-%m-%Y')
        quotation.checkedtime = datetime.datetime.now().strftime('%H:%M:%S')
        quotation.save()
        messages.success(request, "Quotation marked as checked.")
        return redirect("sales_distribution:quotation_detail", quotation_id=quotation.id)

class QuotationApproveView(LoginRequiredMixin, View):
    """View to mark a single quotation as approved"""
    
    def get(self, request, quotation_id):
        """Redirect GET requests to the quotation detail page"""
        messages.info(request, "Use the 'Approve' button to approve the quotation.")
        return redirect("sales_distribution:quotation_detail", quotation_id=quotation_id)
    
    def post(self, request, quotation_id):
        """Handle POST request to approve quotation"""
        quotation = get_object_or_404(Quotation, id=quotation_id)
        if quotation.checked != 1:
            messages.error(request, "Quotation must be checked before approval.")
            return redirect("sales_distribution:quotation_detail", quotation_id=quotation.id)
            
        quotation.approve = 1
        quotation.approvedby = request.user.username
        quotation.approvedate = datetime.date.today().strftime('%d-%m-%Y')
        quotation.approvetime = datetime.datetime.now().strftime('%H:%M:%S')
        quotation.save()
        messages.success(request, "Quotation approved.")
        return redirect("sales_distribution:quotation_detail", quotation_id=quotation.id)

class QuotationAuthorizeView(LoginRequiredMixin, View):
    """View to mark a single quotation as authorized"""
    
    def get(self, request, quotation_id):
        """Redirect GET requests to the quotation detail page"""
        messages.info(request, "Use the 'Authorize' button to authorize the quotation.")
        return redirect("sales_distribution:quotation_detail", quotation_id=quotation_id)
    
    def post(self, request, quotation_id):
        """Handle POST request to authorize quotation"""
        quotation = get_object_or_404(Quotation, id=quotation_id)
        if quotation.approve != 1:
            messages.error(request, "Quotation must be approved before authorization.")
            return redirect("sales_distribution:quotation_detail", quotation_id=quotation.id)
            
        quotation.authorize = 1
        quotation.authorizedby = request.user.username
        quotation.authorizedate = datetime.date.today().strftime('%d-%m-%Y')
        quotation.authorizetime = datetime.datetime.now().strftime('%H:%M:%S')
        quotation.save()
        messages.success(request, "Quotation authorized.")
        return redirect("sales_distribution:quotation_detail", quotation_id=quotation.id)


# Work Order Views

class WorkOrderListView(LoginRequiredMixin, ListView):
    """Work Order list view - matches ASP.NET WorkOrder_Edit.aspx functionality"""
    model = WorkOrder
    template_name = "sales_distribution/work_order_list.html"
    context_object_name = "work_orders"
    paginate_by = 20

    def get_queryset(self):
        """Enhanced queryset matching ASP.NET logic with search and filtering"""
        from ..models import Customer
        
        # Get current financial year and company ID from session (like ASP.NET)
        finyear_id = self.request.session.get('finyear', 13)  # Use latest financial year (2025-2026) as default
        comp_id = self.request.session.get('compid', 1)
        
        # Base queryset matching ASP.NET: FinYearId<='finyear' AND CloseOpen='0' AND CompId='compid'
        queryset = WorkOrder.objects.select_related(
            "enqid", "poid", "cid", "scid", "compid", "finyearid"
        ).filter(
            finyearid__lte=finyear_id,  # FinYearId<='finyear' in ASP.NET
            closeopen='0',              # CloseOpen='0' in ASP.NET
            compid=comp_id              # CompId='compid' in ASP.NET
        ).order_by("wono")              # Order by WONo ASC as in ASP.NET

        # Apply search filters based on search_field (matches ASP.NET DropDownList1)
        search_field = self.request.GET.get('search_field', '0')
        search_value = self.request.GET.get('search_value', '')
        enq_id = self.request.GET.get('enq_id', '')
        
        # Search logic matching ASP.NET exactly
        if search_field == '0' and search_value:  # Customer Name
            # In ASP.NET: getCode(TxtSearchValue.Text) to extract customer ID
            # Then filter with CustomerId='custid'
            try:
                # Extract customer ID from autocomplete format "Customer Name [ID]"
                if '[' in search_value and ']' in search_value:
                    cust_id = search_value.split('[')[1].split(']')[0]
                else:
                    # Direct search in customer name
                    customer = Customer.objects.filter(
                        customer_name__icontains=search_value, 
                        compid=comp_id
                    ).first()
                    cust_id = customer.customerid if customer else search_value
                
                queryset = queryset.filter(customerid=cust_id)
            except:
                queryset = queryset.filter(customerid__icontains=search_value)
        
        elif search_field == '1' and enq_id:  # Enquiry No
            # ASP.NET: AND EnqId='txtEnqId.Text'
            queryset = queryset.filter(enqid=enq_id)
        
        elif search_field == '2' and enq_id:  # PO No
            # ASP.NET: AND PONo='txtEnqId.Text'
            queryset = queryset.filter(pono=enq_id)
        
        elif search_field == '3' and enq_id:  # WO No
            # ASP.NET: AND WONo='txtEnqId.Text'
            queryset = queryset.filter(wono=enq_id)

        # Apply WO Category filter (matches ASP.NET DDLTaskWOType)
        wo_category = self.request.GET.get('wo_category', '')
        if wo_category and wo_category != 'WO Category':
            # ASP.NET: AND CId='DDLTaskWOType.SelectedValue'
            queryset = queryset.filter(cid=wo_category)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Work Order - Edit"  # Exact title from ASP.NET
        
        # Add search parameters to context
        context['search_field'] = self.request.GET.get('search_field', '0')
        context['search_value'] = self.request.GET.get('search_value', '')
        context['enq_id'] = self.request.GET.get('enq_id', '')
        context['wo_category'] = self.request.GET.get('wo_category', '')
        
        # Get company ID from session
        comp_id = self.request.session.get('compid', 1)
        
        # Add WO Categories for dropdown (matches ASP.NET DDLTaskWOType)
        from ..models import WorkOrderCategory
        context['wo_categories'] = WorkOrderCategory.objects.filter(
            compid=comp_id
        ).order_by('cname')
        
        # Enhance work orders with additional data (matching ASP.NET logic exactly)
        work_orders_with_data = []
        for work_order in context['work_orders']:
            # Get customer name and ID from Customer master (matching ASP.NET query)
            try:
                from ..models import Customer
                customer = Customer.objects.filter(
                    compid=comp_id,
                    customerid=work_order.customerid
                ).first()
                if customer:
                    work_order.customer_name = customer.customer_name
                    work_order.customer_code = customer.customerid
                else:
                    work_order.customer_name = work_order.customerid
                    work_order.customer_code = work_order.customerid
            except:
                work_order.customer_name = work_order.customerid
                work_order.customer_code = work_order.customerid
            
            # Get financial year (matching ASP.NET query)
            try:
                from sys_admin.models import FinancialYear
                # Handle case where finyearid might be 0 or None
                if work_order.finyearid and work_order.finyearid != 0:
                    finyear = FinancialYear.objects.filter(
                        finyearid=work_order.finyearid
                    ).first()
                    work_order.financial_year = finyear.finyear if finyear else f"FY-{work_order.finyearid}"
                else:
                    work_order.financial_year = "N/A"
            except Exception:
                work_order.financial_year = f"FY-{work_order.finyearid}" if work_order.finyearid else "N/A"
            
            # Get employee name from HR staff table (matching ASP.NET query)
            try:
                from human_resource.models import OfficeStaff
                employee = OfficeStaff.objects.filter(
                    compid=comp_id,
                    empid=work_order.sessionid
                ).first()
                if employee:
                    work_order.employee_name = f"{employee.title}.{employee.employeename}"
                else:
                    work_order.employee_name = ""
            except:
                work_order.employee_name = ""
            
            # Format date matching ASP.NET (REPLACE(CONVERT(...), '/', '-'))
            if work_order.sysdate:
                try:
                    # Convert date format to match ASP.NET display
                    work_order.formatted_date = work_order.sysdate.strftime('%d-%m-%Y') if hasattr(work_order.sysdate, 'strftime') else str(work_order.sysdate)
                except:
                    work_order.formatted_date = str(work_order.sysdate)
            else:
                work_order.formatted_date = ""
            
            work_orders_with_data.append(work_order)
        
        context['work_orders'] = work_orders_with_data
        return context


class CustomerAutocompleteView(LoginRequiredMixin, View):
    """Customer autocomplete API matching ASP.NET WebMethod functionality"""
    
    def get(self, request):
        """Return customer suggestions in JSON format"""
        from django.http import JsonResponse
        from ..models import Customer
        
        query = request.GET.get('q', '')
        comp_id = request.session.get('compid', 1)
        
        if len(query) < 1:
            return JsonResponse({'customers': []})
        
        try:
            # Query customers matching ASP.NET logic
            customers = Customer.objects.filter(
                compid=comp_id,
                customer_name__icontains=query
            ).order_by('customer_name')[:10]  # Limit to 10 suggestions
            
            # Format suggestions like ASP.NET: "CustomerName [CustomerID]"
            customers_data = []
            for customer in customers:
                suggestion = f"{customer.customer_name} [{customer.customerid}]"
                customers_data.append({'text': suggestion})
            
            return JsonResponse({'customers': customers_data})
            
        except Exception as e:
            return JsonResponse({'customers': [], 'error': str(e)})


class WorkOrderCreateView(LoginRequiredMixin, CreateView):
    """Work Order creation view"""
    model = WorkOrder
    template_name = "sales_distribution/work_order_form.html"
    fields = ["customerid", "enqid", "poid", "pono", "cid", "scid", "wono", "taskworkorderdate", "taskprojecttitle"]
    success_url = reverse_lazy("sales_distribution:work_order_list")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Create Work Order"
        return context


class WorkOrderDetailView(LoginRequiredMixin, DetailView):
    """Work Order detail view"""
    model = WorkOrder
    template_name = "sales_distribution/work_order_detail.html"
    context_object_name = "work_order"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = f"Work Order #{self.object.wono}"
        return context


class WorkOrderUpdateView(LoginRequiredMixin, UpdateView):
    """Work Order update view"""
    model = WorkOrder
    template_name = "sales_distribution/work_order_form.html"
    fields = ["customerid", "enqid", "poid", "pono", "cid", "scid", "wono", "taskworkorderdate", "taskprojecttitle"]
    success_url = reverse_lazy("sales_distribution:work_order_list")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Edit Work Order"
        return context


# Work Order Release Views

class WorkOrderReleaseListView(LoginRequiredMixin, ListView):
    """Work Order Release list view"""
    model = WorkOrder
    template_name = "sales_distribution/work_order_release_list.html"
    context_object_name = "work_orders"
    paginate_by = 20

    def get_queryset(self):
        # Show work orders that are ready for release
        return WorkOrder.objects.select_related(
            "enqid", "poid", "cid", "scid", "compid", "finyearid"
        ).filter(
            wono__isnull=False  # Must have work order number
        ).order_by("-id")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Work Order Release"
        return context


class WorkOrderReleaseView(LoginRequiredMixin, View):
    """Work Order release action view"""
    
    def get(self, request, pk):
        work_order = get_object_or_404(WorkOrder, pk=pk)
        return render(request, "sales_distribution/work_order_release_detail.html", {
            "work_order": work_order,
            "page_title": f"Release Work Order #{work_order.wono}"
        })
    
    def post(self, request, pk):
        work_order = get_object_or_404(WorkOrder, pk=pk)
        # Add release logic here
        messages.success(request, f"Work Order #{work_order.wono} released successfully.")
        return redirect("sales_distribution:work_order_release_list")


# Work Order Dispatch Views

class WorkOrderDispatchListView(LoginRequiredMixin, ListView):
    """Work Order Dispatch list view"""
    model = WorkOrder
    template_name = "sales_distribution/work_order_dispatch_list.html"
    context_object_name = "work_orders"
    paginate_by = 20

    def get_queryset(self):
        # Show work orders that are ready for dispatch
        return WorkOrder.objects.select_related(
            "enqid", "poid", "cid", "scid", "compid", "finyearid"
        ).filter(
            wono__isnull=False  # Must have work order number
        ).order_by("-id")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Work Order Dispatch"
        return context


class WorkOrderDispatchView(LoginRequiredMixin, View):
    """Work Order dispatch action view"""
    
    def get(self, request, pk):
        work_order = get_object_or_404(WorkOrder, pk=pk)
        return render(request, "sales_distribution/work_order_dispatch_detail.html", {
            "work_order": work_order,
            "page_title": f"Dispatch Work Order #{work_order.wono}"
        })
    
    def post(self, request, pk):
        work_order = get_object_or_404(WorkOrder, pk=pk)
        # Add dispatch logic here
        messages.success(request, f"Work Order #{work_order.wono} dispatched successfully.")
        return redirect("sales_distribution:work_order_dispatch_list")


# Dispatch GunRail Views

class DispatchGunRailListView(LoginRequiredMixin, ListView):
    """Dispatch GunRail list view"""
    model = WorkOrder  # Assuming we use WorkOrder for gun rail dispatch
    template_name = "sales_distribution/dispatch_gunrail_list.html"
    context_object_name = "dispatch_items"
    paginate_by = 20

    def get_queryset(self):
        return WorkOrder.objects.select_related(
            "enqid", "poid", "cid", "scid", "compid", "finyearid"
        ).order_by("-id")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Dispatch GunRail"
        return context


class DispatchGunRailCreateView(LoginRequiredMixin, CreateView):
    """Dispatch GunRail creation view"""
    model = WorkOrder
    template_name = "sales_distribution/dispatch_gunrail_form.html"
    fields = ["customerid", "wono", "taskprojecttitle"]
    success_url = reverse_lazy("sales_distribution:dispatch_gunrail_list")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Create GunRail Dispatch"
        return context


# Work Order Open/Close Views

class WorkOrderOpenCloseListView(LoginRequiredMixin, ListView):
    """Work Order Open/Close list view"""
    model = WorkOrder
    template_name = "sales_distribution/work_order_open_close_list.html"
    context_object_name = "work_orders"
    paginate_by = 20

    def get_queryset(self):
        return WorkOrder.objects.select_related(
            "enqid", "poid", "cid", "scid", "compid", "finyearid"
        ).order_by("-id")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Work Order Open/Close"
        return context


class WorkOrderOpenCloseView(LoginRequiredMixin, View):
    """Work Order open/close action view"""
    
    def get(self, request, pk):
        work_order = get_object_or_404(WorkOrder, pk=pk)
        return render(request, "sales_distribution/work_order_open_close_detail.html", {
            "work_order": work_order,
            "page_title": f"Open/Close Work Order #{work_order.wono}"
        })
    
    def post(self, request, pk):
        work_order = get_object_or_404(WorkOrder, pk=pk)
        action = request.POST.get("action")
        if action == "open":
            messages.success(request, f"Work Order #{work_order.wono} opened successfully.")
        elif action == "close":
            messages.success(request, f"Work Order #{work_order.wono} closed successfully.")
        return redirect("sales_distribution:work_order_open_close_list")


# WO Release & Dispatch Authority Views

class WOReleaseDispatchAuthorityListView(LoginRequiredMixin, ListView):
    """WO Release & Dispatch Authority list view"""
    model = WorkOrder  # Placeholder - this might need a separate Authority model
    template_name = "sales_distribution/wo_release_dispatch_authority_list.html"
    context_object_name = "authorities"
    paginate_by = 20

    def get_queryset(self):
        return WorkOrder.objects.select_related(
            "enqid", "poid", "cid", "scid", "compid", "finyearid"
        ).order_by("-id")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = "WO Release & Dispatch Authority"
        return context


class WOReleaseDispatchAuthorityCreateView(LoginRequiredMixin, CreateView):
    """WO Release & Dispatch Authority creation view"""
    model = WorkOrder
    template_name = "sales_distribution/wo_release_dispatch_authority_form.html"
    fields = ["customerid", "wono", "taskprojecttitle"]
    success_url = reverse_lazy("sales_distribution:wo_release_dispatch_authority_list")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Create Release & Dispatch Authority"
        return context
