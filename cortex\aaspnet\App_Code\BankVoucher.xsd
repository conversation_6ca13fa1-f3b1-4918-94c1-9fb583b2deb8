﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="BankVoucher" targetNamespace="http://tempuri.org/BankVoucher.xsd" xmlns:mstns="http://tempuri.org/BankVoucher.xsd" xmlns="http://tempuri.org/BankVoucher.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="BankVoucher" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="BankVoucher" msprop:Generator_DataSetName="BankVoucher">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="PaidTo" msprop:Generator_UserColumnName="PaidTo" msprop:Generator_ColumnVarNameInTable="columnPaidTo" msprop:Generator_ColumnPropNameInRow="PaidTo" msprop:Generator_ColumnPropNameInTable="PaidToColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="ChequeDate" msprop:Generator_UserColumnName="ChequeDate" msprop:Generator_ColumnVarNameInTable="columnChequeDate" msprop:Generator_ColumnPropNameInRow="ChequeDate" msprop:Generator_ColumnPropNameInTable="ChequeDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Amount" msprop:Generator_UserColumnName="Amount" msprop:Generator_ColumnVarNameInTable="columnAmount" msprop:Generator_ColumnPropNameInRow="Amount" msprop:Generator_ColumnPropNameInTable="AmountColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Address" msprop:Generator_UserColumnName="Address" msprop:Generator_ColumnVarNameInTable="columnAddress" msprop:Generator_ColumnPropNameInRow="Address" msprop:Generator_ColumnPropNameInTable="AddressColumn" type="xs:string" minOccurs="0" />
              <xs:element name="BVPNo" msprop:Generator_UserColumnName="BVPNo" msprop:Generator_ColumnPropNameInRow="BVPNo" msprop:Generator_ColumnVarNameInTable="columnBVPNo" msprop:Generator_ColumnPropNameInTable="BVPNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ChequeNo" msprop:Generator_UserColumnName="ChequeNo" msprop:Generator_ColumnPropNameInRow="ChequeNo" msprop:Generator_ColumnVarNameInTable="columnChequeNo" msprop:Generator_ColumnPropNameInTable="ChequeNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="SysDate" msprop:Generator_UserColumnName="SysDate" msprop:Generator_ColumnPropNameInRow="SysDate" msprop:Generator_ColumnVarNameInTable="columnSysDate" msprop:Generator_ColumnPropNameInTable="SysDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="BillNo" msprop:Generator_UserColumnName="BillNo" msprop:Generator_ColumnPropNameInRow="BillNo" msprop:Generator_ColumnVarNameInTable="columnBillNo" msprop:Generator_ColumnPropNameInTable="BillNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="TypeECS" msprop:Generator_UserColumnName="TypeECS" msprop:Generator_ColumnPropNameInRow="TypeECS" msprop:Generator_ColumnVarNameInTable="columnTypeECS" msprop:Generator_ColumnPropNameInTable="TypeECSColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ECS" msprop:Generator_UserColumnName="ECS" msprop:Generator_ColumnVarNameInTable="columnECS" msprop:Generator_ColumnPropNameInRow="ECS" msprop:Generator_ColumnPropNameInTable="ECSColumn" type="xs:string" minOccurs="0" />
              <xs:element name="InvoiceNo" msprop:Generator_UserColumnName="InvoiceNo" msprop:Generator_ColumnPropNameInRow="InvoiceNo" msprop:Generator_ColumnVarNameInTable="columnInvoiceNo" msprop:Generator_ColumnPropNameInTable="InvoiceNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Particular" msprop:Generator_UserColumnName="Particular" msprop:Generator_ColumnVarNameInTable="columnParticular" msprop:Generator_ColumnPropNameInRow="Particular" msprop:Generator_ColumnPropNameInTable="ParticularColumn" type="xs:string" minOccurs="0" />
              <xs:element name="InvDate" msprop:Generator_UserColumnName="InvDate" msprop:Generator_ColumnPropNameInRow="InvDate" msprop:Generator_ColumnVarNameInTable="columnInvDate" msprop:Generator_ColumnPropNameInTable="InvDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="D1" msprop:Generator_UserColumnName="D1" msprop:Generator_ColumnVarNameInTable="columnD1" msprop:Generator_ColumnPropNameInRow="D1" msprop:Generator_ColumnPropNameInTable="D1Column" type="xs:string" minOccurs="0" />
              <xs:element name="D2" msprop:Generator_UserColumnName="D2" msprop:Generator_ColumnVarNameInTable="columnD2" msprop:Generator_ColumnPropNameInRow="D2" msprop:Generator_ColumnPropNameInTable="D2Column" type="xs:string" minOccurs="0" />
              <xs:element name="M1" msprop:Generator_UserColumnName="M1" msprop:Generator_ColumnVarNameInTable="columnM1" msprop:Generator_ColumnPropNameInRow="M1" msprop:Generator_ColumnPropNameInTable="M1Column" type="xs:string" minOccurs="0" />
              <xs:element name="M2" msprop:Generator_UserColumnName="M2" msprop:Generator_ColumnVarNameInTable="columnM2" msprop:Generator_ColumnPropNameInRow="M2" msprop:Generator_ColumnPropNameInTable="M2Column" type="xs:string" minOccurs="0" />
              <xs:element name="Y1" msprop:Generator_UserColumnName="Y1" msprop:Generator_ColumnVarNameInTable="columnY1" msprop:Generator_ColumnPropNameInRow="Y1" msprop:Generator_ColumnPropNameInTable="Y1Column" type="xs:string" minOccurs="0" />
              <xs:element name="Y2" msprop:Generator_UserColumnName="Y2" msprop:Generator_ColumnVarNameInTable="columnY2" msprop:Generator_ColumnPropNameInRow="Y2" msprop:Generator_ColumnPropNameInTable="Y2Column" type="xs:string" minOccurs="0" />
              <xs:element name="Y3" msprop:Generator_UserColumnName="Y3" msprop:Generator_ColumnVarNameInTable="columnY3" msprop:Generator_ColumnPropNameInRow="Y3" msprop:Generator_ColumnPropNameInTable="Y3Column" type="xs:string" minOccurs="0" />
              <xs:element name="Y4" msprop:Generator_UserColumnName="Y4" msprop:Generator_ColumnVarNameInTable="columnY4" msprop:Generator_ColumnPropNameInRow="Y4" msprop:Generator_ColumnPropNameInTable="Y4Column" type="xs:string" minOccurs="0" />
              <xs:element name="PayAmt" msprop:Generator_UserColumnName="PayAmt" msprop:Generator_ColumnPropNameInRow="PayAmt" msprop:Generator_ColumnVarNameInTable="columnPayAmt" msprop:Generator_ColumnPropNameInTable="PayAmtColumn" type="xs:double" minOccurs="0" />
              <xs:element name="AddAmt" msprop:Generator_UserColumnName="AddAmt" msprop:Generator_ColumnPropNameInRow="AddAmt" msprop:Generator_ColumnVarNameInTable="columnAddAmt" msprop:Generator_ColumnPropNameInTable="AddAmtColumn" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>