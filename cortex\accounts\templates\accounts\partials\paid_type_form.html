<!-- Paid Type Add Form -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Add New Paid Type</h3>
        <button 
            onclick="document.getElementById('paid-type-form-container').innerHTML = ''"
            class="text-gray-400 hover:text-gray-600 transition-colors">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
        </button>
    </div>

    <form 
        hx-post="{% url 'accounts:paid_type_create' %}"
        hx-target="#paid-type-table-container"
        hx-swap="innerHTML"
        class="space-y-4">
        {% csrf_token %}
        
        {% if error %}
        <div class="rounded-md bg-red-50 p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Error occurred</h3>
                    <div class="mt-2 text-sm text-red-700">{{ error }}</div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Display form errors -->
        {% if form.non_field_errors %}
        <div class="rounded-md bg-red-50 p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                    <div class="mt-2 text-sm text-red-700">
                        {{ form.non_field_errors }}
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="grid grid-cols-1 gap-4">
            <div>
                <label for="{{ form.particulars.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.particulars.label }}
                </label>
                {{ form.particulars }}
                {% if form.particulars.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.particulars.errors.0 }}</p>
                {% endif %}
            </div>
        </div>

        <div class="flex justify-end space-x-3 pt-4">
            <button 
                type="button"
                onclick="document.getElementById('paid-type-form-container').innerHTML = ''"
                class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                Cancel
            </button>
            <button 
                type="submit"
                class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
                Add Paid Type
            </button>
        </div>
    </form>
</div>