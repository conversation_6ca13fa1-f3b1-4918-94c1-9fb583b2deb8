<!-- accounts/partials/account_head_edit_form.html -->
<!-- HTMX partial for inline editing Account Head row -->
<!-- Matches ASP.NET GridView EditItemTemplate functionality -->

<tr class="bg-sap-blue-50 border border-sap-blue-200" id="account-head-row-{{ account_head.id }}">
    <!-- Inline Edit Form using POST method to match Django form handling -->
    <form hx-post="{% url 'accounts:account_head_edit' account_head.id %}" 
          hx-target="#account-head-row-{{ account_head.id }}"
          hx-swap="outerHTML"
          hx-trigger="submit"
          hx-include="this"
          class="contents"
          id="edit-form-{{ account_head.id }}">
        {% csrf_token %}
        
        <!-- Update Button Column -->
        <td class="px-6 py-4">
            <button type="submit" 
                    onclick="return confirm('Are you sure you want to update this account head?')"
                    class="text-sap-green-600 hover:text-sap-green-900 underline">
                Update
            </button>
        </td>
        
        <!-- Cancel Button Column -->
        <td class="px-6 py-4">
            <button type="button"
                    hx-get="{% url 'accounts:account_head_cancel_edit' account_head.id %}"
                    hx-target="#account-head-row-{{ account_head.id }}"
                    hx-swap="outerHTML"
                    class="text-sap-gray-600 hover:text-sap-gray-900 underline">
                Cancel
            </button>
        </td>
        
        <!-- Serial Number Column -->
        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900 text-right">
            {{ account_head.id }}
        </td>
        
        <!-- Category Dropdown -->
        <td class="px-6 py-4">
            <select name="category" 
                    class="block w-full px-3 py-2 border border-sap-blue-300 rounded-md shadow-sm text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500 bg-white"
                    required>
                <option value="Labour" {% if form.category.value == "Labour" or account_head.category == "Labour" %}selected{% endif %}>Labour</option>
                <option value="With Material" {% if form.category.value == "With Material" or account_head.category == "With Material" %}selected{% endif %}>With Material</option>
                <option value="Expenses" {% if form.category.value == "Expenses" or account_head.category == "Expenses" %}selected{% endif %}>Expenses</option>
                <option value="Service Provider" {% if form.category.value == "Service Provider" or account_head.category == "Service Provider" %}selected{% endif %}>Service Provider</option>
            </select>
            {% if form.category.errors %}
                <p class="mt-1 text-xs text-red-600">*</p>
            {% endif %}
        </td>
        
        <!-- Description Input -->
        <td class="px-6 py-4">
            <input type="text" 
                   name="description" 
                   value="{{ form.description.value|default:account_head.description }}" 
                   class="block w-full px-3 py-2 border border-sap-blue-300 rounded-md shadow-sm text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"
                   required>
            {% if form.description.errors %}
                <p class="mt-1 text-xs text-red-600">*</p>
            {% endif %}
        </td>
        
        <!-- Symbol Input -->
        <td class="px-6 py-4">
            <input type="text" 
                   name="symbol" 
                   value="{{ form.symbol.value|default:account_head.symbol }}" 
                   class="block w-full px-3 py-2 border border-sap-blue-300 rounded-md shadow-sm text-sm font-mono focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"
                   required>
            {% if form.symbol.errors %}
                <p class="mt-1 text-xs text-red-600">*</p>
            {% endif %}
        </td>
        
        <!-- Abbreviation Input -->
        <td class="px-6 py-4">
            <input type="text" 
                   name="abbreviation" 
                   value="{{ form.abbreviation.value|default:account_head.abbreviation }}" 
                   class="block w-full px-3 py-2 border border-sap-blue-300 rounded-md shadow-sm text-sm font-mono focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500"
                   required>
            {% if form.abbreviation.errors %}
                <p class="mt-1 text-xs text-red-600">*</p>
            {% endif %}
        </td>
    </form>
</tr>