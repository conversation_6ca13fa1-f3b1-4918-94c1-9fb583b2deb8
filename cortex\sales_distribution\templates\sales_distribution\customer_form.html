{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ title }} - {{ global_company.company_name }}{% endblock %}

{% block content %}
<div class="h-full overflow-y-auto">
    <div class="p-6 space-y-6">
        <!-- Page Header -->
        <div class="bg-white rounded-xl shadow-sm border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-sap-green-500 rounded-lg flex items-center justify-center">
                        <i data-lucide="user-plus" class="w-6 h-6 text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-sap-gray-900">{{ title }}</h1>
                        <p class="text-sm text-sap-gray-600">
                            {% if object %}Edit customer information{% else %}Create a new customer record{% endif %}
                        </p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="{% url 'sales_distribution:customer_list' %}" 
                       class="px-4 py-2 border border-sap-gray-300 rounded-lg text-sm text-sap-gray-700 hover:bg-sap-gray-50 transition-colors">
                        <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                        Back to List
                    </a>
                </div>
            </div>
        </div>

        <!-- Customer Form -->
        <div class="bg-white rounded-xl shadow-sm border border-sap-gray-200 overflow-hidden">
            <!-- Header Row -->
            <div class="bg-gradient-to-r from-sap-gray-700 to-sap-gray-800 px-6 py-3">
                <h3 class="text-lg font-semibold text-white">Customer Master - {{ title }}</h3>
            </div>
            
            <form method="post" class="divide-y divide-sap-gray-200" 
                  hx-post="{% if object %}{% url 'sales_distribution:customer_edit' object.pk %}{% else %}{% url 'sales_distribution:customer_new' %}{% endif %}"
                  hx-target="body">
                {% csrf_token %}
                
                <!-- Form Errors Display -->
                {% if form.non_field_errors %}
                    <div class="bg-sap-red-50 border-l-4 border-sap-red-400 p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i data-lucide="alert-circle" class="h-5 w-5 text-sap-red-400"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-sap-red-700">
                                    {% for error in form.non_field_errors %}{{ error }}{% endfor %}
                                </p>
                            </div>
                        </div>
                    </div>
                {% endif %}
                
                <!-- Customer Name Row -->
                <div class="grid grid-cols-4 gap-6 p-6">
                    <div class="flex items-center">
                        <label class="text-sm font-medium text-sap-gray-700">
                            Customer's Name <span class="text-sap-red-500">*</span>
                        </label>
                    </div>
                    <div class="col-span-2">
                        {{ form.customer_name }}
                        {% if form.customer_name.errors %}
                            <div class="mt-1 text-sm text-sap-red-600">
                                {% for error in form.customer_name.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div class="flex justify-end">
                        <button type="submit" 
                                class="sap-button-primary"
                                onclick="return confirm('Are you sure you want to {% if object %}update{% else %}add{% endif %} this customer?')">
                            <i data-lucide="save" class="w-4 h-4 mr-2"></i>
                            {% if object %}Update{% else %}Submit{% endif %}
                        </button>
                    </div>
                </div>
                
                <!-- Address Headers Row -->
                <div class="grid grid-cols-4 gap-6 bg-sap-gray-100 p-4">
                    <div class="flex items-center justify-center">
                        <span class="text-sm font-medium text-sap-gray-700">Address/Details</span>
                    </div>
                    <div class="flex items-center justify-center">
                        <span class="text-sm font-medium text-sap-gray-700">REGD. OFFICE</span>
                    </div>
                    <div class="flex items-center justify-center">
                        <span class="text-sm font-medium text-sap-gray-700">WORKS/FACTORY</span>
                    </div>
                    <div class="flex items-center justify-center">
                        <span class="text-sm font-medium text-sap-gray-700">MATERIAL DELIVERY</span>
                    </div>
                </div>
                
                <!-- Address Row -->
                <div class="grid grid-cols-4 gap-6 p-4">
                    <div class="flex items-start pt-2">
                        <label class="text-sm font-medium text-sap-gray-700">
                            Address <span class="text-sap-red-500">*</span>
                        </label>
                    </div>
                    <div>
                        {{ form.registered_address }}
                        {% if form.registered_address.errors %}
                            <div class="mt-1 text-sm text-sap-red-600">
                                {% for error in form.registered_address.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div>
                        {{ form.works_address }}
                        {% if form.works_address.errors %}
                            <div class="mt-1 text-sm text-sap-red-600">
                                {% for error in form.works_address.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div>
                        {{ form.material_address }}
                        {% if form.material_address.errors %}
                            <div class="mt-1 text-sm text-sap-red-600">
                                {% for error in form.material_address.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Country Row -->
                <div class="grid grid-cols-4 gap-6 p-4">
                    <div class="flex items-center">
                        <label class="text-sm font-medium text-sap-gray-700">
                            Country <span class="text-sap-red-500">*</span>
                        </label>
                    </div>
                    <div>
                        <select name="registered_country" id="id_registered_country" 
                                class="form-select block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                hx-post="{% url 'sales_distribution:ajax_states' %}"
                                hx-target="#id_registered_state"
                                hx-vals='{"address_type": "registered"}'
                                hx-trigger="change">
                            <option value="">Select Country</option>
                            {% for country in countries %}
                                <option value="{{ country.cid }}" {% if form.registered_country.value == country.cid %}selected{% endif %}>
                                    {{ country.countryname }}
                                </option>
                            {% endfor %}
                        </select>
                        {% if form.registered_country.errors %}
                            <div class="mt-1 text-sm text-sap-red-600">
                                {% for error in form.registered_country.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div>
                        <select name="works_country" id="id_works_country" 
                                class="form-select block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                hx-post="{% url 'sales_distribution:ajax_states' %}"
                                hx-target="#id_works_state"
                                hx-vals='{"address_type": "works"}'
                                hx-trigger="change">
                            <option value="">Select Country</option>
                            {% for country in countries %}
                                <option value="{{ country.cid }}" {% if form.works_country.value == country.cid %}selected{% endif %}>
                                    {{ country.countryname }}
                                </option>
                            {% endfor %}
                        </select>
                        {% if form.works_country.errors %}
                            <div class="mt-1 text-sm text-sap-red-600">
                                {% for error in form.works_country.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div>
                        <select name="material_country" id="id_material_country" 
                                class="form-select block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                hx-post="{% url 'sales_distribution:ajax_states' %}"
                                hx-target="#id_material_state"
                                hx-vals='{"address_type": "material"}'
                                hx-trigger="change">
                            <option value="">Select Country</option>
                            {% for country in countries %}
                                <option value="{{ country.cid }}" {% if form.material_country.value == country.cid %}selected{% endif %}>
                                    {{ country.countryname }}
                                </option>
                            {% endfor %}
                        </select>
                        {% if form.material_country.errors %}
                            <div class="mt-1 text-sm text-sap-red-600">
                                {% for error in form.material_country.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- State Row -->
                <div class="grid grid-cols-4 gap-6 p-4">
                    <div class="flex items-center">
                        <label class="text-sm font-medium text-sap-gray-700">
                            State <span class="text-sap-red-500">*</span>
                        </label>
                    </div>
                    <div>
                        <select name="registered_state" id="id_registered_state" 
                                class="form-select block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                hx-post="{% url 'sales_distribution:ajax_cities' %}"
                                hx-target="#id_registered_city"
                                hx-vals='{"address_type": "registered"}'
                                hx-trigger="change">
                            <option value="">Select State</option>
                            {% if form.registered_state.field.queryset %}
                                {% for state in form.registered_state.field.queryset %}
                                    <option value="{{ state.sid }}" {% if form.registered_state.value == state.sid %}selected{% endif %}>
                                        {{ state.statename }}
                                    </option>
                                {% endfor %}
                            {% endif %}
                        </select>
                        {% if form.registered_state.errors %}
                            <div class="mt-1 text-sm text-sap-red-600">
                                {% for error in form.registered_state.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div>
                        <select name="works_state" id="id_works_state" 
                                class="form-select block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                hx-post="{% url 'sales_distribution:ajax_cities' %}"
                                hx-target="#id_works_city"
                                hx-vals='{"address_type": "works"}'
                                hx-trigger="change">
                            <option value="">Select State</option>
                            {% if form.works_state.field.queryset %}
                                {% for state in form.works_state.field.queryset %}
                                    <option value="{{ state.sid }}" {% if form.works_state.value == state.sid %}selected{% endif %}>
                                        {{ state.statename }}
                                    </option>
                                {% endfor %}
                            {% endif %}
                        </select>
                        {% if form.works_state.errors %}
                            <div class="mt-1 text-sm text-sap-red-600">
                                {% for error in form.works_state.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div>
                        <select name="material_state" id="id_material_state" 
                                class="form-select block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                hx-post="{% url 'sales_distribution:ajax_cities' %}"
                                hx-target="#id_material_city"
                                hx-vals='{"address_type": "material"}'
                                hx-trigger="change">
                            <option value="">Select State</option>
                            {% if form.material_state.field.queryset %}
                                {% for state in form.material_state.field.queryset %}
                                    <option value="{{ state.sid }}" {% if form.material_state.value == state.sid %}selected{% endif %}>
                                        {{ state.statename }}
                                    </option>
                                {% endfor %}
                            {% endif %}
                        </select>
                        {% if form.material_state.errors %}
                            <div class="mt-1 text-sm text-sap-red-600">
                                {% for error in form.material_state.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- City Row -->
                <div class="grid grid-cols-4 gap-6 p-4">
                    <div class="flex items-center">
                        <label class="text-sm font-medium text-sap-gray-700">
                            City <span class="text-sap-red-500">*</span>
                        </label>
                    </div>
                    <div>
                        <select name="registered_city" id="id_registered_city" 
                                class="form-select block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select City</option>
                            {% if form.registered_city.field.queryset %}
                                {% for city in form.registered_city.field.queryset %}
                                    <option value="{{ city.cityid }}" {% if form.registered_city.value == city.cityid %}selected{% endif %}>
                                        {{ city.cityname }}
                                    </option>
                                {% endfor %}
                            {% endif %}
                        </select>
                        {% if form.registered_city.errors %}
                            <div class="mt-1 text-sm text-sap-red-600">
                                {% for error in form.registered_city.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div>
                        <select name="works_city" id="id_works_city" 
                                class="form-select block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select City</option>
                            {% if form.works_city.field.queryset %}
                                {% for city in form.works_city.field.queryset %}
                                    <option value="{{ city.cityid }}" {% if form.works_city.value == city.cityid %}selected{% endif %}>
                                        {{ city.cityname }}
                                    </option>
                                {% endfor %}
                            {% endif %}
                        </select>
                        {% if form.works_city.errors %}
                            <div class="mt-1 text-sm text-sap-red-600">
                                {% for error in form.works_city.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div>
                        <select name="material_city" id="id_material_city" 
                                class="form-select block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select City</option>
                            {% if form.material_city.field.queryset %}
                                {% for city in form.material_city.field.queryset %}
                                    <option value="{{ city.cityid }}" {% if form.material_city.value == city.cityid %}selected{% endif %}>
                                        {{ city.cityname }}
                                    </option>
                                {% endfor %}
                            {% endif %}
                        </select>
                        {% if form.material_city.errors %}
                            <div class="mt-1 text-sm text-sap-red-600">
                                {% for error in form.material_city.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- PIN Number Row -->
                <div class="grid grid-cols-4 gap-6 p-4">
                    <div class="flex items-center">
                        <label class="text-sm font-medium text-sap-gray-700">
                            PIN No. <span class="text-sap-red-500">*</span>
                        </label>
                    </div>
                    <div>
                        {{ form.registered_pin }}
                        {% if form.registered_pin.errors %}
                            <div class="mt-1 text-sm text-sap-red-600">
                                {% for error in form.registered_pin.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div>
                        {{ form.works_pin }}
                        {% if form.works_pin.errors %}
                            <div class="mt-1 text-sm text-sap-red-600">
                                {% for error in form.works_pin.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div>
                        {{ form.material_pin }}
                        {% if form.material_pin.errors %}
                            <div class="mt-1 text-sm text-sap-red-600">
                                {% for error in form.material_pin.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Contact Number Row -->
                <div class="grid grid-cols-4 gap-6 p-4">
                    <div class="flex items-center">
                        <label class="text-sm font-medium text-sap-gray-700">
                            Contact No. <span class="text-sap-red-500">*</span>
                        </label>
                    </div>
                    <div>
                        {{ form.registered_contact_no }}
                        {% if form.registered_contact_no.errors %}
                            <div class="mt-1 text-sm text-sap-red-600">
                                {% for error in form.registered_contact_no.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div>
                        {{ form.works_contact_no }}
                        {% if form.works_contact_no.errors %}
                            <div class="mt-1 text-sm text-sap-red-600">
                                {% for error in form.works_contact_no.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div>
                        {{ form.material_contact_no }}
                        {% if form.material_contact_no.errors %}
                            <div class="mt-1 text-sm text-sap-red-600">
                                {% for error in form.material_contact_no.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- TIN/CST Number Row -->
                <div class="grid grid-cols-4 gap-6 p-4">
                    <div class="flex items-center">
                        <label class="text-sm font-medium text-sap-gray-700">
                            TIN/CST No. <span class="text-sap-red-500">*</span>
                        </label>
                    </div>
                    <div>
                        {{ form.tincstno }}
                        {% if form.tincstno.errors %}
                            <div class="mt-1 text-sm text-sap-red-600">
                                {% for error in form.tincstno.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div>
                        <!-- Empty column for works/factory -->
                    </div>
                    <div>
                        <!-- Empty column for material delivery -->
                    </div>
                </div>
                
                <!-- Additional Details Section -->
                <div class="p-6 space-y-4">
                    <!-- Contact Person, Email, Contact No Row -->
                    <div class="grid grid-cols-6 gap-4 items-center">
                        <div>
                            <label class="text-sm font-medium text-sap-gray-700">
                                Contact person <span class="text-sap-red-500">*</span>
                            </label>
                        </div>
                        <div>
                            {{ form.contact_person }}
                            {% if form.contact_person.errors %}
                                <div class="mt-1 text-sm text-sap-red-600">
                                    {% for error in form.contact_person.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div>
                            <label class="text-sm font-medium text-sap-gray-700">
                                E-mail <span class="text-sap-red-500">*</span>
                            </label>
                        </div>
                        <div>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="mt-1 text-sm text-sap-red-600">
                                    {% for error in form.email.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div>
                            <label class="text-sm font-medium text-sap-gray-700">
                                Contact No. <span class="text-sap-red-500">*</span>
                            </label>
                        </div>
                        <div>
                            {{ form.contact_no }}
                            {% if form.contact_no.errors %}
                                <div class="mt-1 text-sm text-sap-red-600">
                                    {% for error in form.contact_no.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Tax Information Rows - Hidden -->
                    <div class="grid grid-cols-6 gap-4 items-center" style="display: none;">
                        <div>
                            <label class="text-sm font-medium text-sap-gray-700">
                                Jurisdiction Code <span class="text-sap-red-500">*</span>
                            </label>
                        </div>
                        <div>
                            {{ form.juridictioncode }}
                            {% if form.juridictioncode.errors %}
                                <div class="mt-1 text-sm text-sap-red-600">
                                    {% for error in form.juridictioncode.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div>
                            <label class="text-sm font-medium text-sap-gray-700">
                                ECC No. <span class="text-sap-red-500">*</span>
                            </label>
                        </div>
                        <div>
                            {{ form.eccno }}
                            {% if form.eccno.errors %}
                                <div class="mt-1 text-sm text-sap-red-600">
                                    {% for error in form.eccno.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div>
                            <label class="text-sm font-medium text-sap-gray-700">
                                Range <span class="text-sap-red-500">*</span>
                            </label>
                        </div>
                        <div>
                            {{ form.range }}
                            {% if form.range.errors %}
                                <div class="mt-1 text-sm text-sap-red-600">
                                    {% for error in form.range.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-6 gap-4 items-center" style="display: none;">
                        <div>
                            <label class="text-sm font-medium text-sap-gray-700">
                                Commissionerate <span class="text-sap-red-500">*</span>
                            </label>
                        </div>
                        <div>
                            {{ form.commissionurate }}
                            {% if form.commissionurate.errors %}
                                <div class="mt-1 text-sm text-sap-red-600">
                                    {% for error in form.commissionurate.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div>
                            <label class="text-sm font-medium text-sap-gray-700">
                                Division <span class="text-sap-red-500">*</span>
                            </label>
                        </div>
                        <div>
                            {{ form.divn }}
                            {% if form.divn.errors %}
                                <div class="mt-1 text-sm text-sap-red-600">
                                    {% for error in form.divn.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div>
                            <label class="text-sm font-medium text-sap-gray-700">
                                PAN No. <span class="text-sap-red-500">*</span>
                            </label>
                        </div>
                        <div>
                            {{ form.panno }}
                            {% if form.panno.errors %}
                                <div class="mt-1 text-sm text-sap-red-600">
                                    {% for error in form.panno.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-6 gap-4 items-center" style="display: none;">
                        <div>
                            <label class="text-sm font-medium text-sap-gray-700">
                                TIN/VAT No. <span class="text-sap-red-500">*</span>
                            </label>
                        </div>
                        <div>
                            {{ form.tinvatno }}
                            {% if form.tinvatno.errors %}
                                <div class="mt-1 text-sm text-sap-red-600">
                                    {% for error in form.tinvatno.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div>
                            <label class="text-sm font-medium text-sap-gray-700">
                                TIN/CST No. <span class="text-sap-red-500">*</span>
                            </label>
                        </div>
                        <div>
                            {{ form.tincstno }}
                            {% if form.tincstno.errors %}
                                <div class="mt-1 text-sm text-sap-red-600">
                                    {% for error in form.tincstno.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div>
                            <label class="text-sm font-medium text-sap-gray-700">
                                TDS Code <span class="text-sap-red-500">*</span>
                            </label>
                        </div>
                        <div>
                            {{ form.tdscode }}
                            {% if form.tdscode.errors %}
                                <div class="mt-1 text-sm text-sap-red-600">
                                    {% for error in form.tdscode.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Remarks Row -->
                <div class="grid grid-cols-4 gap-6 p-6">
                    <div class="flex items-start pt-2">
                        <label class="text-sm font-medium text-sap-gray-700">Remarks</label>
                    </div>
                    <div class="col-span-2">
                        {{ form.remarks }}
                        {% if form.remarks.errors %}
                            <div class="mt-1 text-sm text-sap-red-600">
                                {% for error in form.remarks.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <div></div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize icons
        setTimeout(() => {
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        }, 100);
        
        // Debug HTMX requests
        document.body.addEventListener('htmx:beforeRequest', function(evt) {
            console.log('HTMX Request:', evt.detail.requestConfig.verb, evt.detail.requestConfig.url);
        });
        
        document.body.addEventListener('htmx:afterRequest', function(evt) {
            console.log('HTMX Response:', evt.detail.xhr.status, evt.detail.xhr.responseText);
            // Reinitialize icons after HTMX updates
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        });
        
        console.log('✅ Customer form initialized with cascading dropdowns');
    });
</script>
{% endblock %}