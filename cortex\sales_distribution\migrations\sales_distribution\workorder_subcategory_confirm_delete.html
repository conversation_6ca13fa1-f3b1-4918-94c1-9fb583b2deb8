<!-- sales_distribution/templates/sales_distribution/workorder_subcategory_confirm_delete.html -->
<!-- Work Order Sub-Category Delete Confirmation - SAP S/4HANA Inspired Design -->

{% extends 'core/base.html' %}
{% load static %}

{% block title %}Delete Sub-Category - Cortex{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="trash-2" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Delete Sub-Category</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Confirm deletion of work order sub-category</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'sales_distribution:subcategory_list' %}" 
                   class="inline-flex items-center px-4 py-2.5 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 hover:border-sap-blue-300 transition-all duration-200">
                    <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                    Back to Sub-Categories
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-2 py-4">
    <div class="max-w-2xl mx-auto">
        
        <!-- Delete Confirmation Card -->
        <div class="bg-white rounded-lg border border-red-200 shadow-sm">
            <div class="px-6 py-4 border-b border-red-100 bg-red-50">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="alert-triangle" class="w-4 h-4 text-red-600"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-red-800">Confirm Deletion</h3>
                        <p class="text-sm text-red-600">This action cannot be undone</p>
                    </div>
                </div>
            </div>
            
            <div class="px-6 py-6">
                <div class="mb-6">
                    <p class="text-sm text-sap-gray-600 mb-4">
                        Are you sure you want to delete the following sub-category?
                    </p>
                    
                    <!-- Sub-Category Details -->
                    <div class="bg-sap-gray-50 rounded-lg p-4 border border-sap-gray-200">
                        <dl class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                            <div>
                                <dt class="text-xs font-medium text-sap-gray-500 uppercase tracking-wide">Sub-Category ID</dt>
                                <dd class="mt-1 text-sm font-medium text-sap-gray-900">{{ object.scid }}</dd>
                            </div>
                            <div>
                                <dt class="text-xs font-medium text-sap-gray-500 uppercase tracking-wide">Parent Category</dt>
                                <dd class="mt-1 text-sm font-medium text-sap-gray-900">{{ object.cid.cname|default:"No Category" }}</dd>
                            </div>
                            <div class="sm:col-span-2">
                                <dt class="text-xs font-medium text-sap-gray-500 uppercase tracking-wide">Sub-Category Name</dt>
                                <dd class="mt-1 text-sm font-medium text-sap-gray-900">{{ object.scname }}</dd>
                            </div>
                            <div>
                                <dt class="text-xs font-medium text-sap-gray-500 uppercase tracking-wide">Created By</dt>
                                <dd class="mt-1 text-sm text-sap-gray-600">{{ object.sessionid|default:"System" }}</dd>
                            </div>
                            <div>
                                <dt class="text-xs font-medium text-sap-gray-500 uppercase tracking-wide">Created Date</dt>
                                <dd class="mt-1 text-sm text-sap-gray-600">{{ object.sysdate|default:"N/A" }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>
                
                <!-- Warning Message -->
                <div class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i data-lucide="alert-triangle" class="w-5 h-5 text-yellow-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">
                                Warning
                            </h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p>
                                    Deleting this sub-category may affect other related records in the system. 
                                    Please ensure that this sub-category is not being used in any work orders or other transactions.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Confirmation Form -->
                <form method="post" class="space-y-4">
                    {% csrf_token %}
                    
                    <!-- Form Actions -->
                    <div class="flex items-center justify-end space-x-3 pt-4 border-t border-sap-gray-100">
                        <a href="{% url 'sales_distribution:subcategory_list' %}" 
                           class="px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 transition-colors duration-200">
                            <i data-lucide="x" class="w-4 h-4 mr-2 inline"></i>
                            Cancel
                        </a>
                        <button type="submit" 
                                onclick="return confirmDeletion()"
                                class="px-6 py-2 bg-red-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                            <i data-lucide="trash-2" class="w-4 h-4 mr-2 inline"></i>
                            Delete Sub-Category
                        </button>
                    </div>
                    
                </form>
            </div>
        </div>
        
    </div>
</div>

<script>
function confirmDeletion() {
    return confirm('Are you absolutely sure you want to delete this sub-category? This action cannot be undone and may affect related records.');
}

// Focus management
document.addEventListener('DOMContentLoaded', function() {
    // Focus on cancel button by default for safety
    const cancelBtn = document.querySelector('a[href*="subcategory_list"]');
    if (cancelBtn) {
        cancelBtn.focus();
    }
});
</script>
{% endblock %}