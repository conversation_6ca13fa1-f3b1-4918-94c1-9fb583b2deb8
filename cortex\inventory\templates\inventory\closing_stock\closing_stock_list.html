{% extends "core/base.html" %}
{% load static %}

{% block title %}Closing Stock Management - Inventory{% endblock %}

{% block content %}
<div class="sap-page">
    <!-- Page Header -->
    <div class="sap-page-header">
        <div class="sap-page-header-content">
            <div class="sap-page-title">
                <h1>Closing Stock Management</h1>
                <p>Manage inventory closing stock records and calculations</p>
            </div>
            <div class="sap-page-actions">
                <a href="{% url 'inventory:closing_stock_create' %}" class="sap-button sap-button--emphasized">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add New Record
                </a>
                <button id="refresh-data" class="sap-button sap-button--transparent">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Refresh
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div class="sap-card">
            <div class="sap-card-content">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Records</p>
                        <p class="text-2xl font-bold text-gray-900">{{ stats.total_records }}</p>
                    </div>
                    <div class="p-3 bg-blue-100 rounded-full">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <div class="sap-card">
            <div class="sap-card-content">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Value</p>
                        <p class="text-2xl font-bold text-gray-900">₹{{ stats.total_value|floatformat:2 }}</p>
                    </div>
                    <div class="p-3 bg-green-100 rounded-full">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <div class="sap-card">
            <div class="sap-card-content">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Min Value</p>
                        <p class="text-2xl font-bold text-gray-900">₹{{ stats.min_value|floatformat:2 }}</p>
                    </div>
                    <div class="p-3 bg-yellow-100 rounded-full">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <div class="sap-card">
            <div class="sap-card-content">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Max Value</p>
                        <p class="text-2xl font-bold text-gray-900">₹{{ stats.max_value|floatformat:2 }}</p>
                    </div>
                    <div class="p-3 bg-purple-100 rounded-full">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Panel -->
    <div class="sap-panel mb-6">
        <div class="sap-panel-header">
            <h3 class="sap-panel-title">Search & Filter</h3>
        </div>
        <div class="sap-panel-content">
            <form method="get" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                    <div class="sap-form-group">
                        <label class="sap-form-label">Search</label>
                        {{ search_form.search }}
                    </div>
                    <div class="sap-form-group">
                        <label class="sap-form-label">From Date</label>
                        {{ search_form.from_date }}
                    </div>
                    <div class="sap-form-group">
                        <label class="sap-form-label">To Date</label>
                        {{ search_form.to_date }}
                    </div>
                    <div class="sap-form-group">
                        <label class="sap-form-label">Min Value</label>
                        {{ search_form.min_value }}
                    </div>
                    <div class="sap-form-group">
                        <label class="sap-form-label">Max Value</label>
                        {{ search_form.max_value }}
                    </div>
                </div>
                <div class="flex space-x-3">
                    <button type="submit" class="sap-button sap-button--emphasized">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Search
                    </button>
                    <a href="{% url 'inventory:closing_stock_list' %}" class="sap-button sap-button--transparent">
                        Clear Filters
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Results Panel -->
    <div class="sap-panel">
        <div class="sap-panel-header">
            <h3 class="sap-panel-title">Closing Stock Records</h3>
            <div class="sap-panel-actions">
                <span class="text-sm text-gray-600">{{ page_obj.paginator.count }} total records</span>
            </div>
        </div>
        <div class="sap-panel-content">
            <div id="closing-stock-results">
                {% include 'inventory/closing_stock/partials/closing_stock_list_partial.html' %}
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('refresh-data').addEventListener('click', function() {
    window.location.reload();
});
</script>
{% endblock %}