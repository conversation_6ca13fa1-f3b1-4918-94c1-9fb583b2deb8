﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="PlanPrint" targetNamespace="http://tempuri.org/PlanPrint.xsd" xmlns:mstns="http://tempuri.org/PlanPrint.xsd" xmlns="http://tempuri.org/PlanPrint.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="PlanPrint" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="PlanPrint" msprop:Generator_DataSetName="PlanPrint">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="PLDate" msprop:Generator_UserColumnName="PLDate" msprop:Generator_ColumnVarNameInTable="columnPLDate" msprop:Generator_ColumnPropNameInRow="PLDate" msprop:Generator_ColumnPropNameInTable="PLDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PLNo" msprop:Generator_UserColumnName="PLNo" msprop:Generator_ColumnVarNameInTable="columnPLNo" msprop:Generator_ColumnPropNameInRow="PLNo" msprop:Generator_ColumnPropNameInTable="PLNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ItemCode" msprop:Generator_UserColumnName="ItemCode" msprop:Generator_ColumnVarNameInTable="columnItemCode" msprop:Generator_ColumnPropNameInRow="ItemCode" msprop:Generator_ColumnPropNameInTable="ItemCodeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Symbol" msprop:Generator_UserColumnName="Symbol" msprop:Generator_ColumnVarNameInTable="columnSymbol" msprop:Generator_ColumnPropNameInRow="Symbol" msprop:Generator_ColumnPropNameInTable="SymbolColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ManfDesc" msprop:Generator_UserColumnName="ManfDesc" msprop:Generator_ColumnVarNameInTable="columnManfDesc" msprop:Generator_ColumnPropNameInRow="ManfDesc" msprop:Generator_ColumnPropNameInTable="ManfDescColumn" type="xs:string" minOccurs="0" />
              <xs:element name="SupplierName" msprop:Generator_UserColumnName="SupplierName" msprop:Generator_ColumnVarNameInTable="columnSupplierName" msprop:Generator_ColumnPropNameInRow="SupplierName" msprop:Generator_ColumnPropNameInTable="SupplierNameColumn" type="xs:string" minOccurs="0" />
              <xs:element name="DelDateFinish" msprop:Generator_UserColumnName="DelDateFinish" msprop:Generator_ColumnVarNameInTable="columnDelDateFinish" msprop:Generator_ColumnPropNameInRow="DelDateFinish" msprop:Generator_ColumnPropNameInTable="DelDateFinishColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="FinishQty" msprop:Generator_UserColumnName="FinishQty" msprop:Generator_ColumnVarNameInTable="columnFinishQty" msprop:Generator_ColumnPropNameInRow="FinishQty" msprop:Generator_ColumnPropNameInTable="FinishQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Rate" msprop:Generator_UserColumnName="Rate" msprop:Generator_ColumnVarNameInTable="columnRate" msprop:Generator_ColumnPropNameInRow="Rate" msprop:Generator_ColumnPropNameInTable="RateColumn" type="xs:double" minOccurs="0" />
              <xs:element name="ItemCodeRMPR" msprop:Generator_UserColumnName="ItemCodeRMPR" msprop:Generator_ColumnPropNameInRow="ItemCodeRMPR" msprop:Generator_ColumnVarNameInTable="columnItemCodeRMPR" msprop:Generator_ColumnPropNameInTable="ItemCodeRMPRColumn" type="xs:string" minOccurs="0" />
              <xs:element name="BOMQty" msprop:Generator_UserColumnName="BOMQty" msprop:Generator_ColumnVarNameInTable="columnBOMQty" msprop:Generator_ColumnPropNameInRow="BOMQty" msprop:Generator_ColumnPropNameInTable="BOMQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="PLNType" msprop:Generator_UserColumnName="PLNType" msprop:Generator_ColumnVarNameInTable="columnPLNType" msprop:Generator_ColumnPropNameInRow="PLNType" msprop:Generator_ColumnPropNameInTable="PLNTypeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Discount" msprop:Generator_UserColumnName="Discount" msprop:Generator_ColumnPropNameInRow="Discount" msprop:Generator_ColumnVarNameInTable="columnDiscount" msprop:Generator_ColumnPropNameInTable="DiscountColumn" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>