﻿using System;
using System.Collections;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Xml.Linq;
using System.Data.SqlClient;

public partial class Admin_Access_add_user : System.Web.UI.Page
{
    clsFunctions fun = new clsFunctions();
    int CompId = 0;
    
    protected void Page_Load(object sender, EventArgs e)
    {
        string connStr = fun.Connection();
        SqlConnection con = new SqlConnection(connStr);
        con.Open();

        try
        {           
            if (!IsPostBack)
            {
                fun.dropdownCompany(DropDownList1);
                DropDownList1.SelectedIndex=1;
                DropDownList2.Enabled = true;
                fun.dropdownFinYear(DropDownList2, DropDownList1);
                DropDownList2.SelectedIndex = 1;
                this.Employees();
                
                string LicNo = fun.select("LicenceNos", "tblCompany_master", "CompId='" + DropDownList1.SelectedValue + "'");
                SqlCommand LicCommand = new SqlCommand(LicNo, con);
                //SqlDataAdapter LicDa = new SqlDataAdapter(LicCommand);
                //DataSet LicDS = new DataSet();
                //LicDa.Fill(LicDS, "tblCompany_master");
                SqlDataReader LicDS = LicCommand.ExecuteReader();
                LicDS.Read();

                int LicCount = Convert.ToInt32(LicDS["LicenceNos"].ToString());
                string LicUser = fun.select("UserName", "aspnet_Users", "CompId='" + DropDownList1.SelectedValue + "'");
                SqlCommand UserCommand = new SqlCommand(LicUser, con);
                SqlDataAdapter UserDa = new SqlDataAdapter(UserCommand);
                DataSet UserDS = new DataSet();
                UserDa.Fill(UserDS, "aspnet_Users");
                
                int UserCount = Convert.ToInt32(UserDS.Tables[0].Rows.Count);
                
                if (UserCount >= LicCount)
                {
                    CreateUserWizard1.Enabled = false;
                    lblmsg.Text = "Licence limitations are exceed!";
                }
                else
                {
                    CreateUserWizard1.Enabled = true;
                    lblmsg.Text = " ";
                }
            }
        }
        catch (Exception ex)
        {
        }
        finally
        {
            con.Close();
        }
    }
   
   
    protected void CreateUserWizard1_CreatedUser(object sender, EventArgs e)
    {
        string connStr = fun.Connection();
        SqlConnection con = new SqlConnection(connStr);
       try
        {

            if (DropDownList1.SelectedItem.Text != "Select")
            {
                con.Open();
                string EmpName = ((TextBox)CreateUserWizard1.CreateUserStep.ContentTemplateContainer.FindControl("UserName")).Text;
                string update = fun.update("aspnet_Users", "CompId='" + DropDownList1.SelectedValue + "', FinYearId='" + DropDownList2.SelectedValue + "'", "UserName='" + EmpName + "'");
                SqlCommand cmd = new SqlCommand(update, con);
                cmd.ExecuteNonQuery();


            }
           
            
        }
      catch(Exception ex)
        {
          
        }
        finally
        {
            con.Close();
        }
    }
    protected void DropDownList1_SelectedIndexChanged(object sender, EventArgs e)
    {
        string connStr = fun.Connection();
        SqlConnection con = new SqlConnection(connStr);
        con.Open();
        try
        {
            if (DropDownList1.SelectedItem.Text != "Select")
            {
                if (IsPostBack)
                {
                    DropDownList2.Enabled = true;
                    fun.dropdownFinYear(DropDownList2, DropDownList1);
                }
                
            }

            else
            {
                DropDownList2.Enabled = false;

            }

        }
       catch (Exception ex)
        {
        }
       finally
        {
            con.Close();
        }

    }
    protected void DropDownList2_SelectedIndexChanged(object sender, EventArgs e)
    {
        this.Employees();
    }

    public void Employees()
    {
        try
        {
            string connStr = fun.Connection();
            SqlConnection con = new SqlConnection(connStr);
            con.Open();

            if (DropDownList2.SelectedItem.Text != "Select")
            {
                string StrComp = fun.select("tblHR_OfficeStaff.EmployeeName + ' ['+tblHR_OfficeStaff.EmpId+' ]'As EmpName,tblHR_OfficeStaff.EmpId", "tblHR_OfficeStaff", "tblHR_OfficeStaff.CompId='" + DropDownList1.SelectedValue + "'And tblHR_OfficeStaff.EmpId Not In(select aspnet_Users.UserName from aspnet_Users where aspnet_Users.CompId='" + DropDownList1.SelectedValue + "') Order by tblHR_OfficeStaff.EmployeeName ASC");
                
                SqlCommand CompCommand = new SqlCommand(StrComp, con);
                SqlDataAdapter CompDa = new SqlDataAdapter(CompCommand);
                DataSet CompDS = new DataSet();
                CompDa.Fill(CompDS);
                
                drpEmpName.DataSource = CompDS.Tables[0];
                drpEmpName.DataTextField = "EmpName";
                drpEmpName.DataValueField = "EmpId";
                drpEmpName.DataBind();
                drpEmpName.Items.Insert(0, "Select");
            }
            else
            {
                drpEmpName.Items.Clear();
            }

            con.Close();
        }
        catch (Exception et)
        {

        }
    }

    protected void drpEmpName_SelectedIndexChanged(object sender, EventArgs e)
    {
        try
        {
            CompId = Convert.ToInt32(Session["compid"]);
            string connStr = fun.Connection();
            SqlConnection con = new SqlConnection(connStr);

            TextBox userName = (TextBox)CreateUserWizard1.CreateUserStep.ContentTemplateContainer.FindControl("UserName");
            TextBox email = (TextBox)CreateUserWizard1.CreateUserStep.ContentTemplateContainer.FindControl("Email");
            TextBox password = (TextBox)CreateUserWizard1.CreateUserStep.ContentTemplateContainer.FindControl("Password");
            
            userName.Text = "";
            email.Text = "";
            password.Text = "";
            
            string sqlMail = fun.select("ErpSysmail", "tblCompany_master", "CompId='" + CompId + "'");

            SqlCommand cmdMail = new SqlCommand(sqlMail, con);
            SqlDataAdapter daMail = new SqlDataAdapter(cmdMail);
            DataSet DSMail = new DataSet();
            daMail.Fill(DSMail);

            string ErpSysmail = DSMail.Tables[0].Rows[0]["ErpSysmail"].ToString();            
            email.Text = ErpSysmail;

            string EmpName = drpEmpName.SelectedValue.ToString();                      
            userName.Text = EmpName;
                      
        }
        catch(Exception ex){}        

    }
}
