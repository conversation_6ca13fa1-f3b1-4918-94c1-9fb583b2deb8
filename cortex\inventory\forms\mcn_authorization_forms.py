from django import forms
from django.core.exceptions import ValidationError
from django.db.models import Q
from datetime import datetime

from project_management.models import MaterialCreditNote, MaterialCreditNoteDetail
from sales_distribution.models import WorkOrder, Customer


class MCNAuthorizationSearchForm(forms.Form):
    """Search form for MCN authorization list view"""
    wo_no = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Enter Work Order Number',
            'autocomplete': 'off'
        }),
        label='Work Order No'
    )
    
    customer_name = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Enter Customer Name',
            'autocomplete': 'off'
        }),
        label='Customer Name'
    )
    
    project_title = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Enter Project Title',
            'autocomplete': 'off'
        }),
        label='Project Title'
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add SAP styling to all fields
        for field_name, field in self.fields.items():
            field.widget.attrs.update({'class': 'sap-input'})


class MCNAuthorizationForm(forms.Form):
    """Form for authorizing MCN quantities"""
    selected_items = forms.MultipleChoiceField(
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'sap-checkbox'
        }),
        label='Select Items for Authorization'
    )
    
    authorization_remarks = forms.CharField(
        max_length=500,
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'sap-textarea',
            'rows': 3,
            'placeholder': 'Enter authorization remarks (optional)',
        }),
        label='Authorization Remarks'
    )
    
    authorize_all = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'sap-checkbox',
            'id': 'authorize_all'
        }),
        label='Authorize All Items'
    )
    
    def __init__(self, mcn_items=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        if mcn_items:
            # Dynamic quantity fields for each MCN item
            for item in mcn_items:
                field_name = f'auth_quantity_{item["detail_id"]}'
                self.fields[field_name] = forms.DecimalField(
                    max_digits=10,
                    decimal_places=2,
                    required=False,
                    min_value=0,
                    widget=forms.NumberInput(attrs={
                        'class': 'sap-input',
                        'placeholder': '0.00',
                        'step': '0.01',
                        'data-max-value': item.get('pending_quantity', 0),
                        'data-detail-id': item['detail_id']
                    }),
                    label=f'Authorize Qty (Max: {item.get("pending_quantity", 0)})'
                )
    
    def clean(self):
        cleaned_data = super().clean()
        
        # Validate authorization quantities
        for field_name, value in cleaned_data.items():
            if field_name.startswith('auth_quantity_') and value:
                if value <= 0:
                    raise ValidationError(f'Authorization quantity must be greater than zero for {field_name}')
        
        return cleaned_data


class AuthorizedMCNCreateForm(forms.Form):
    """Form for creating new MCN authorization records"""
    work_order_no = forms.CharField(
        max_length=50,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Enter Work Order Number',
            'required': True
        }),
        label='Work Order Number'
    )
    
    authorization_date = forms.DateField(
        initial=datetime.now().date(),
        widget=forms.DateInput(attrs={
            'class': 'sap-input',
            'type': 'date',
            'required': True
        }),
        label='Authorization Date'
    )
    
    authorized_by = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Authorized By',
            'required': True
        }),
        label='Authorized By'
    )
    
    authorization_notes = forms.CharField(
        max_length=1000,
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'sap-textarea',
            'rows': 4,
            'placeholder': 'Enter authorization notes and comments',
        }),
        label='Authorization Notes'
    )
    
    def clean_work_order_no(self):
        work_order_no = self.cleaned_data.get('work_order_no')
        
        if work_order_no:
            # Validate that work order exists and has MCN records
            if not WorkOrder.objects.filter(wono=work_order_no).exists():
                raise ValidationError('Work Order does not exist.')
            
            # Check if work order has MCN records
            if not MaterialCreditNote.objects.filter(work_order_no=work_order_no).exists():
                raise ValidationError('No MCN records found for this Work Order.')
        
        return work_order_no
    
    def clean_authorization_date(self):
        auth_date = self.cleaned_data.get('authorization_date')
        
        if auth_date and auth_date > datetime.now().date():
            raise ValidationError('Authorization date cannot be in the future.')
        
        return auth_date


class MCNItemAuthorizationForm(forms.Form):
    """Individual MCN item authorization form"""
    detail_id = forms.IntegerField(
        widget=forms.HiddenInput()
    )
    
    mcn_quantity = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'sap-input',
            'readonly': True
        }),
        label='MCN Quantity'
    )
    
    authorized_quantity = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        min_value=0,
        widget=forms.NumberInput(attrs={
            'class': 'sap-input',
            'placeholder': '0.00',
            'step': '0.01'
        }),
        label='Authorize Quantity'
    )
    
    authorization_remarks = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Item-specific remarks (optional)'
        }),
        label='Remarks'
    )
    
    def __init__(self, *args, **kwargs):
        mcn_detail = kwargs.pop('mcn_detail', None)
        super().__init__(*args, **kwargs)
        
        if mcn_detail:
            self.fields['detail_id'].initial = mcn_detail.id
            self.fields['mcn_quantity'].initial = mcn_detail.mcn_quantity
            self.fields['authorized_quantity'].widget.attrs['data-max-value'] = mcn_detail.mcn_quantity
    
    def clean(self):
        cleaned_data = super().clean()
        mcn_quantity = cleaned_data.get('mcn_quantity', 0)
        authorized_quantity = cleaned_data.get('authorized_quantity', 0)
        
        if authorized_quantity > mcn_quantity:
            raise ValidationError(
                f'Authorized quantity ({authorized_quantity}) cannot exceed MCN quantity ({mcn_quantity})'
            )
        
        return cleaned_data


class BulkMCNAuthorizationForm(forms.Form):
    """Form for bulk MCN authorization operations"""
    selected_items = forms.MultipleChoiceField(
        required=True,
        widget=forms.CheckboxSelectMultiple(),
        label='Select Items'
    )
    
    action = forms.ChoiceField(
        choices=[
            ('authorize', 'Authorize Selected'),
            ('reject', 'Reject Selected'),
            ('hold', 'Put on Hold'),
            ('export', 'Export Selected'),
        ],
        widget=forms.Select(attrs={
            'class': 'sap-select'
        }),
        label='Action'
    )
    
    bulk_remarks = forms.CharField(
        max_length=1000,
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'sap-textarea',
            'rows': 3,
            'placeholder': 'Enter remarks for bulk action'
        }),
        label='Bulk Action Remarks'
    )
    
    authorization_priority = forms.ChoiceField(
        choices=[
            ('normal', 'Normal'),
            ('high', 'High'),
            ('urgent', 'Urgent'),
        ],
        initial='normal',
        widget=forms.Select(attrs={
            'class': 'sap-select'
        }),
        label='Priority Level'
    )
    
    def clean_selected_items(self):
        selected_items = self.cleaned_data.get('selected_items')
        
        if not selected_items:
            raise ValidationError('Please select at least one item for bulk action.')
        
        return selected_items


class MCNAuthorizationReportForm(forms.Form):
    """Form for generating MCN authorization reports"""
    report_type = forms.ChoiceField(
        choices=[
            ('summary', 'Authorization Summary'),
            ('detailed', 'Detailed Authorization Report'),
            ('by_wo', 'By Work Order'),
            ('by_customer', 'By Customer'),
            ('by_date', 'By Date Range'),
            ('pending', 'Pending Authorizations'),
        ],
        widget=forms.Select(attrs={
            'class': 'sap-select'
        }),
        label='Report Type'
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'sap-input',
            'type': 'date'
        }),
        label='From Date'
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'sap-input',
            'type': 'date'
        }),
        label='To Date'
    )
    
    work_order_no = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Enter Work Order Number'
        }),
        label='Work Order No'
    )
    
    customer_filter = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'sap-input',
            'placeholder': 'Enter Customer Name'
        }),
        label='Customer Filter'
    )
    
    authorization_status = forms.ChoiceField(
        choices=[
            ('all', 'All Statuses'),
            ('authorized', 'Authorized'),
            ('pending', 'Pending'),
            ('rejected', 'Rejected'),
            ('on_hold', 'On Hold'),
        ],
        initial='all',
        widget=forms.Select(attrs={
            'class': 'sap-select'
        }),
        label='Authorization Status'
    )
    
    export_format = forms.ChoiceField(
        choices=[
            ('html', 'HTML Report'),
            ('pdf', 'PDF Export'),
            ('excel', 'Excel Export'),
            ('csv', 'CSV Export'),
        ],
        initial='html',
        widget=forms.Select(attrs={
            'class': 'sap-select'
        }),
        label='Export Format'
    )
    
    def clean(self):
        cleaned_data = super().clean()
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        
        if date_from and date_to:
            if date_from > date_to:
                raise ValidationError('From date cannot be later than To date.')
        
        return cleaned_data


class MCNAuthorizationValidationForm(forms.Form):
    """Form for validating MCN authorization data"""
    work_order_no = forms.CharField(max_length=50)
    mcn_detail_id = forms.IntegerField()
    authorization_quantity = forms.DecimalField(max_digits=10, decimal_places=2, min_value=0)
    
    def clean(self):
        cleaned_data = super().clean()
        work_order_no = cleaned_data.get('work_order_no')
        mcn_detail_id = cleaned_data.get('mcn_detail_id')
        auth_quantity = cleaned_data.get('authorization_quantity')
        
        if work_order_no and mcn_detail_id and auth_quantity:
            try:
                # Validate MCN detail exists and belongs to work order
                mcn_detail = MaterialCreditNoteDetail.objects.select_related('master').get(
                    id=mcn_detail_id,
                    master__work_order_no=work_order_no
                )
                
                # Validate quantity doesn't exceed available
                if auth_quantity > mcn_detail.mcn_quantity:
                    raise ValidationError(
                        f'Authorization quantity ({auth_quantity}) exceeds available MCN quantity ({mcn_detail.mcn_quantity})'
                    )
                
                # Store the validated MCN detail for use in view
                cleaned_data['mcn_detail'] = mcn_detail
                
            except MaterialCreditNoteDetail.DoesNotExist:
                raise ValidationError('Invalid MCN detail or work order combination.')
        
        return cleaned_data