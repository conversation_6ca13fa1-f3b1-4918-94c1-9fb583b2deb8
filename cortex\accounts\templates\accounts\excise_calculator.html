<!-- accounts/templates/accounts/excise_calculator.html -->
<!-- Excise Tax Calculator Template -->
<!-- Task Group 4: Taxation Management - Tax Calculator Templates -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Excise Tax Calculator{% endblock %}

{% block extra_css %}
<style>
    .calculator-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .result-card {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        border-radius: 1rem;
        padding: 1.5rem;
        margin: 1rem 0;
    }
    
    .breakdown-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid rgba(0,0,0,0.1);
    }
    
    .breakdown-item:last-child {
        border-bottom: none;
        font-weight: bold;
        font-size: 1.1em;
    }
    
    .rate-badge {
        background: rgba(99, 102, 241, 0.1);
        color: #4f46e5;
        padding: 0.25rem 0.75rem;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-6 py-8">
    <!-- Header Section -->
    <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Excise Tax Calculator</h1>
                <p class="mt-2 text-gray-600">Calculate excise duty and cess amounts with precision</p>
            </div>
            <div class="mt-4 sm:mt-0 flex space-x-3">
                <a href="{% url 'accounts:excise_duty_list' %}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    Back to List
                </a>
                <a href="{% url 'accounts:composite_tax_calculator' %}" 
                   class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    Composite Calculator
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Calculator Input Section -->
        <div class="bg-white shadow-lg rounded-lg p-6">
            <h2 class="text-xl font-bold text-gray-900 mb-6">Tax Calculator</h2>
            
            <form method="post" id="excise-calculator-form">
                {% csrf_token %}
                
                <!-- Excise Duty Selection -->
                <div class="mb-6">
                    <label for="{{ calculator_form.excise_duty.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Select Excise/Service Tax
                    </label>
                    {{ calculator_form.excise_duty|add_class:"block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" }}
                    {% if calculator_form.excise_duty.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ calculator_form.excise_duty.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Base Amount -->
                <div class="mb-6">
                    <label for="{{ calculator_form.base_amount.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Base Amount (₹)
                    </label>
                    {{ calculator_form.base_amount|add_class:"block w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" }}
                    {% if calculator_form.base_amount.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ calculator_form.base_amount.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Calculation Type -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">Calculation Type</label>
                    <div class="space-y-2">
                        {% for radio in calculator_form.calculation_type %}
                            <div class="flex items-center">
                                {{ radio.tag }}
                                <label for="{{ radio.id_for_label }}" class="ml-2 text-sm text-gray-700">
                                    {{ radio.choice_label }}
                                </label>
                            </div>
                        {% endfor %}
                    </div>
                    {% if calculator_form.calculation_type.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ calculator_form.calculation_type.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Quick Amount Buttons -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">Quick Amounts</label>
                    <div class="grid grid-cols-2 sm:grid-cols-4 gap-2">
                        <button type="button" onclick="setAmount(1000)" 
                                class="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50">
                            ₹1,000
                        </button>
                        <button type="button" onclick="setAmount(10000)" 
                                class="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50">
                            ₹10,000
                        </button>
                        <button type="button" onclick="setAmount(100000)" 
                                class="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50">
                            ₹1,00,000
                        </button>
                        <button type="button" onclick="setAmount(1000000)" 
                                class="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50">
                            ₹10,00,000
                        </button>
                    </div>
                </div>

                <!-- Calculate Button -->
                <button type="submit" 
                        class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium transition-colors">
                    <i class="fas fa-calculator mr-2"></i>Calculate Tax
                </button>
            </form>
        </div>

        <!-- Results Section -->
        <div class="space-y-6">
            {% if calculation_result %}
            <!-- Tax Calculation Result -->
            <div class="result-card">
                <h3 class="text-xl font-bold text-gray-900 mb-4">
                    <i class="fas fa-chart-line mr-2"></i>Calculation Result
                </h3>
                
                <div class="space-y-3">
                    {% if calculation_result.calculation_type == 'inclusive' %}
                    <div class="breakdown-item">
                        <span>Total Amount (Input):</span>
                        <span class="font-medium">₹{{ calculation_result.original_amount|floatformat:2 }}</span>
                    </div>
                    <div class="breakdown-item">
                        <span>Assessable Amount:</span>
                        <span class="font-medium">₹{{ calculation_result.assessable_amount|floatformat:2 }}</span>
                    </div>
                    {% else %}
                    <div class="breakdown-item">
                        <span>Base Amount:</span>
                        <span class="font-medium">₹{{ calculation_result.original_amount|floatformat:2 }}</span>
                    </div>
                    {% endif %}
                    
                    <div class="breakdown-item">
                        <span>Basic Excise ({{ calculation_result.excise_duty.value }}%):</span>
                        <span class="font-medium">₹{{ calculation_result.basic_excise|floatformat:2 }}</span>
                    </div>
                    
                    {% if calculation_result.edu_cess > 0 %}
                    <div class="breakdown-item">
                        <span>Education Cess ({{ calculation_result.excise_duty.edu_cess }}%):</span>
                        <span class="font-medium">₹{{ calculation_result.edu_cess|floatformat:2 }}</span>
                    </div>
                    {% endif %}
                    
                    {% if calculation_result.she_cess > 0 %}
                    <div class="breakdown-item">
                        <span>SHE Cess ({{ calculation_result.excise_duty.she_cess }}%):</span>
                        <span class="font-medium">₹{{ calculation_result.she_cess|floatformat:2 }}</span>
                    </div>
                    {% endif %}
                    
                    <div class="breakdown-item">
                        <span>Total Excise Tax:</span>
                        <span class="font-bold text-green-600">₹{{ calculation_result.total_excise|floatformat:2 }}</span>
                    </div>
                    
                    {% if calculation_result.calculation_type == 'exclusive' %}
                    <div class="breakdown-item">
                        <span>Total Amount:</span>
                        <span class="font-bold text-blue-600">₹{{ calculation_result.total_amount|floatformat:2 }}</span>
                    </div>
                    {% endif %}
                </div>
                
                <!-- Rate Information -->
                <div class="mt-4 p-3 bg-white bg-opacity-50 rounded-lg">
                    <div class="flex justify-between items-center">
                        <span class="text-sm">Effective Rate:</span>
                        <span class="rate-badge">{{ calculation_result.excise_duty.effective_rate|floatformat:3 }}%</span>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Available Tax Rates -->
            <div class="bg-white shadow-lg rounded-lg p-6">
                <h3 class="text-lg font-bold text-gray-900 mb-4">
                    <i class="fas fa-list mr-2"></i>Available Tax Rates
                </h3>
                
                {% if active_duties %}
                <div class="space-y-3 max-h-96 overflow-y-auto">
                    {% for duty in active_duties %}
                    <div class="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                         onclick="selectDuty({{ duty.id }}, '{{ duty.terms|escapejs }}')">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900">{{ duty.terms }}</h4>
                                <div class="text-sm text-gray-600 mt-1">
                                    Basic: {{ duty.value|floatformat:3 }}%
                                    {% if duty.edu_cess %} | EDU: {{ duty.edu_cess|floatformat:3 }}%{% endif %}
                                    {% if duty.she_cess %} | SHE: {{ duty.she_cess|floatformat:3 }}%{% endif %}
                                </div>
                            </div>
                            <div class="ml-3">
                                <span class="rate-badge">{{ duty.total_excise_rate|floatformat:3 }}%</span>
                                {% if duty.is_default_excise %}
                                    <span class="inline-block ml-1 w-2 h-2 bg-green-500 rounded-full" title="Default Excise"></span>
                                {% endif %}
                                {% if duty.is_default_service_tax %}
                                    <span class="inline-block ml-1 w-2 h-2 bg-blue-500 rounded-full" title="Default Service Tax"></span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-exclamation-circle text-4xl mb-3"></i>
                    <p>No active excise duties found.</p>
                    <a href="{% url 'accounts:excise_duty_create' %}" 
                       class="text-blue-600 hover:text-blue-800 font-medium">
                        Create one now
                    </a>
                </div>
                {% endif %}
            </div>

            <!-- Tax Rate Comparison -->
            {% if active_duties %}
            <div class="bg-white shadow-lg rounded-lg p-6">
                <h3 class="text-lg font-bold text-gray-900 mb-4">
                    <i class="fas fa-chart-bar mr-2"></i>Rate Comparison
                </h3>
                
                <div class="space-y-2">
                    {% for duty in active_duties|slice:":5" %}
                    <div class="flex items-center">
                        <div class="w-32 text-sm text-gray-600 truncate">{{ duty.terms|truncatechars:15 }}</div>
                        <div class="flex-1 mx-3">
                            <div class="bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full" 
                                     style="width: {{ duty.total_excise_rate|floatformat:1 }}%"></div>
                            </div>
                        </div>
                        <div class="w-16 text-sm text-right font-medium">{{ duty.total_excise_rate|floatformat:1 }}%</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Calculation History -->
    <div class="mt-8 bg-white shadow-lg rounded-lg p-6">
        <h3 class="text-lg font-bold text-gray-900 mb-4">
            <i class="fas fa-history mr-2"></i>Recent Calculations
        </h3>
        
        <div id="calculation-history" class="space-y-3">
            <!-- History items will be populated here -->
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-clock text-4xl mb-3"></i>
                <p>Your calculation history will appear here</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let calculationHistory = JSON.parse(localStorage.getItem('excise_calculation_history') || '[]');

// Set quick amount
function setAmount(amount) {
    document.getElementById('id_base_amount').value = amount;
    
    // Auto-calculate if duty is selected
    if (document.getElementById('id_excise_duty').value) {
        autoCalculate();
    }
}

// Select duty from list
function selectDuty(dutyId, dutyName) {
    document.getElementById('id_excise_duty').value = dutyId;
    
    // Auto-calculate if amount is entered
    if (document.getElementById('id_base_amount').value) {
        autoCalculate();
    }
}

// Auto-calculate on input change
function autoCalculate() {
    const dutyId = document.getElementById('id_excise_duty').value;
    const amount = document.getElementById('id_base_amount').value;
    const calculationType = document.querySelector('input[name="calculation_type"]:checked').value;
    
    if (dutyId && amount && amount > 0) {
        // Perform AJAX calculation
        fetch(`{% url 'accounts:ajax_calculate_excise' %}?duty_id=${dutyId}&amount=${amount}&type=${calculationType}`)
            .then(response => response.json())
            .then(data => {
                if (data.calculation) {
                    updateResultDisplay(data.calculation, data.duty_info);
                    addToHistory(data.calculation, data.duty_info, amount, calculationType);
                }
            })
            .catch(error => console.error('Auto-calculation error:', error));
    }
}

// Update result display
function updateResultDisplay(calculation, dutyInfo) {
    // This would update the results section without form submission
    // Implementation depends on the specific HTML structure
}

// Add calculation to history
function addToHistory(calculation, dutyInfo, amount, type) {
    const historyItem = {
        timestamp: new Date().toISOString(),
        dutyName: dutyInfo.terms,
        amount: parseFloat(amount),
        calculationType: type,
        totalTax: calculation.total_excise,
        effectiveRate: dutyInfo.effective_rate
    };
    
    calculationHistory.unshift(historyItem);
    calculationHistory = calculationHistory.slice(0, 10); // Keep only last 10
    
    localStorage.setItem('excise_calculation_history', JSON.stringify(calculationHistory));
    updateHistoryDisplay();
}

// Update history display
function updateHistoryDisplay() {
    const historyDiv = document.getElementById('calculation-history');
    
    if (calculationHistory.length === 0) {
        historyDiv.innerHTML = `
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-clock text-4xl mb-3"></i>
                <p>Your calculation history will appear here</p>
            </div>
        `;
        return;
    }
    
    historyDiv.innerHTML = calculationHistory.map(item => `
        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
            <div class="flex-1">
                <div class="font-medium text-gray-900">${item.dutyName}</div>
                <div class="text-sm text-gray-600">
                    ${new Date(item.timestamp).toLocaleString()} | 
                    ${item.calculationType.charAt(0).toUpperCase() + item.calculationType.slice(1)}
                </div>
            </div>
            <div class="text-right">
                <div class="font-medium">₹${item.amount.toLocaleString('en-IN')}</div>
                <div class="text-sm text-green-600">Tax: ₹${item.totalTax.toLocaleString('en-IN', {minimumFractionDigits: 2})}</div>
            </div>
        </div>
    `).join('');
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    updateHistoryDisplay();
    
    // Auto-calculate on input changes
    document.getElementById('id_excise_duty').addEventListener('change', autoCalculate);
    document.getElementById('id_base_amount').addEventListener('input', debounce(autoCalculate, 500));
    document.querySelectorAll('input[name="calculation_type"]').forEach(radio => {
        radio.addEventListener('change', autoCalculate);
    });
});

// Debounce function
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
{% endblock %}