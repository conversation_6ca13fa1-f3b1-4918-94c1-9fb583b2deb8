from django.urls import path
from . import views

app_name = 'daily_reporting'

urlpatterns = [
    # Dashboard
    path('', views.DashboardView.as_view(), name='dashboard'),
    
    # Daily Reporting Tracker URLs
    path('tracker/', views.DailyReportingTrackerListView.as_view(), name='tracker_list'),
    path('tracker/new/', views.DailyReportingTrackerCreateView.as_view(), name='tracker_create'),
    path('tracker/<int:pk>/', views.DailyReportingTrackerDetailView.as_view(), name='tracker_detail'),
    path('tracker/<int:pk>/edit/', views.DailyReportingTrackerUpdateView.as_view(), name='tracker_edit'),
    path('tracker/<int:pk>/delete/', views.DailyReportingTrackerDeleteView.as_view(), name='tracker_delete'),
    
    # Design Plan URLs
    path('design-plan/', views.DesignPlanListView.as_view(), name='design_plan_list'),
    path('design-plan/new/', views.DesignPlanCreateView.as_view(), name='design_plan_create'),
    path('design-plan/<int:pk>/', views.DesignPlanDetailView.as_view(), name='design_plan_detail'),
    path('design-plan/<int:pk>/edit/', views.DesignPlanUpdateView.as_view(), name='design_plan_edit'),
    
    # Manufacturing Plan URLs
    path('manufacturing-plan/', views.ManufacturingPlanListView.as_view(), name='manufacturing_plan_list'),
    path('manufacturing-plan/new/', views.ManufacturingPlanCreateView.as_view(), name='manufacturing_plan_create'),
    path('manufacturing-plan/<int:pk>/', views.ManufacturingPlanDetailView.as_view(), name='manufacturing_plan_detail'),
    path('manufacturing-plan/<int:pk>/edit/', views.ManufacturingPlanUpdateView.as_view(), name='manufacturing_plan_edit'),
    
    # Vendor Plan URLs
    path('vendor-plan/', views.VendorPlanListView.as_view(), name='vendor_plan_list'),
    path('vendor-plan/new/', views.VendorPlanCreateView.as_view(), name='vendor_plan_create'),
    path('vendor-plan/<int:pk>/', views.VendorPlanDetailView.as_view(), name='vendor_plan_detail'),
    path('vendor-plan/<int:pk>/edit/', views.VendorPlanUpdateView.as_view(), name='vendor_plan_edit'),
    
    # ManPower Planning URLs
    path('manpower-planning/', views.ManPowerPlanningListView.as_view(), name='manpower_planning_list'),
    path('manpower-planning/new/', views.ManPowerPlanningCreateView.as_view(), name='manpower_planning_create'),
    
    # Reports URLs
    path('reports/', views.ProjectReportView.as_view(), name='project_reports'),
    
    # HTMX and AJAX endpoints
    path('search/', views.search_reports, name='search_reports'),
    path('get-employee-details/', views.get_employee_details, name='get_employee_details'),
    path('get-work-order-activities/', views.get_work_order_activities, name='get_work_order_activities'),
    path('api/dashboard-stats/', views.dashboard_stats_api, name='dashboard_stats_api'),
    path('export/', views.export_reports, name='export_reports'),
]