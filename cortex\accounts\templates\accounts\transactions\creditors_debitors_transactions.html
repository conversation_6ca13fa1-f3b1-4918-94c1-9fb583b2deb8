<!-- accounts/templates/accounts/transactions/creditors_debitors_transactions.html -->
<!-- Creditors & Debitors Transaction List Template -->
<!-- Shows all transactions for a specific creditor/debitor -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{{ creditor_debitor.party_name }} - Transactions{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-blue-600 to-sap-blue-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="list" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">{{ creditor_debitor.party_name }}</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Transaction History</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:creditors_debitors_detail' creditor_debitor.id %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to Details
                </a>
                <button type="button" onclick="exportTransactions()" 
                        class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                    Export
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Party Summary -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm mb-6">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <h3 class="text-lg font-medium text-sap-gray-800">Party Summary</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="text-center p-4 bg-sap-blue-50 rounded-lg">
                    <p class="text-sm text-sap-gray-600">Party Name</p>
                    <p class="text-lg font-semibold text-sap-blue-600">{{ creditor_debitor.party_name }}</p>
                </div>
                <div class="text-center p-4 bg-sap-green-50 rounded-lg">
                    <p class="text-sm text-sap-gray-600">Party Type</p>
                    <p class="text-lg font-semibold text-sap-green-600">{{ creditor_debitor.party_type|title }}</p>
                </div>
                <div class="text-center p-4 bg-sap-purple-50 rounded-lg">
                    <p class="text-sm text-sap-gray-600">Total Transactions</p>
                    <p class="text-lg font-semibold text-sap-purple-600">{{ transactions|length }}</p>
                </div>
                <div class="text-center p-4 bg-sap-orange-50 rounded-lg">
                    <p class="text-sm text-sap-gray-600">Reference No</p>
                    <p class="text-lg font-semibold text-sap-orange-600">{{ creditor_debitor.reference_no|default:"N/A" }}</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Search and Filter -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm mb-6">
        <div class="p-6">
            <form method="GET" class="flex gap-4">
                <div class="flex-1">
                    <input type="text" 
                           name="search" 
                           value="{{ request.GET.search }}"
                           placeholder="Search transactions..."
                           class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500">
                </div>
                <div>
                    <select name="amount_type" 
                            class="px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500">
                        <option value="">All Types</option>
                        <option value="debit" {% if request.GET.amount_type == 'debit' %}selected{% endif %}>Debit</option>
                        <option value="credit" {% if request.GET.amount_type == 'credit' %}selected{% endif %}>Credit</option>
                    </select>
                </div>
                <button type="submit" 
                        class="bg-sap-blue-600 text-white px-6 py-2 rounded-lg hover:bg-sap-blue-700">
                    Search
                </button>
                {% if request.GET %}
                <a href="{% url 'accounts:creditors_debitors_transactions' creditor_debitor.id %}" 
                   class="bg-sap-gray-300 text-sap-gray-700 px-6 py-2 rounded-lg hover:bg-sap-gray-400">
                    Clear
                </a>
                {% endif %}
            </form>
        </div>
    </div>
    
    <!-- Transactions Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <h3 class="text-lg font-medium text-sap-gray-800">Transaction History</h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Date
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Reference
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Particulars
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Type
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Amount
                        </th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for transaction in transactions %}
                    <tr class="hover:bg-sap-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">
                            {{ transaction.created_date|date:"M d, Y" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">
                            {{ transaction.reference_no|default:"N/A" }}
                        </td>
                        <td class="px-6 py-4 text-sm text-sap-gray-900">
                            {{ transaction.particulars|default:"N/A"|truncatechars:50 }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full 
                                {% if transaction.amount_type == 'debit' %}bg-sap-red-100 text-sap-red-800{% else %}bg-sap-green-100 text-sap-green-800{% endif %}">
                                {{ transaction.amount_type|title }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-right font-medium 
                            {% if transaction.amount_type == 'debit' %}text-sap-red-600{% else %}text-sap-green-600{% endif %}">
                            ₹{{ transaction.amount|floatformat:2 }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                            <div class="flex items-center justify-center space-x-2">
                                <a href="{% url 'accounts:creditors_debitors_detail' transaction.id %}" 
                                   class="text-sap-blue-600 hover:text-sap-blue-800" title="View Details">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                </a>
                                <a href="{% url 'accounts:creditors_debitors_edit' transaction.id %}" 
                                   class="text-sap-green-600 hover:text-sap-green-800" title="Edit">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </a>
                                <button type="button" onclick="deleteTransaction({{ transaction.id }})" 
                                        class="text-sap-red-600 hover:text-sap-red-800" title="Delete">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="px-6 py-12 text-center">
                            <div class="flex flex-col items-center justify-center">
                                <i data-lucide="inbox" class="w-12 h-12 text-sap-gray-400 mb-4"></i>
                                <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No transactions found</h3>
                                <p class="text-sap-gray-500">No transaction history available for this party.</p>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="px-6 py-4 border-t border-sap-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <p class="text-sm text-sap-gray-700">
                        Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} transactions
                    </p>
                </div>
                <div class="flex items-center space-x-2">
                    {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}"
                           class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-3 py-2 rounded-lg text-sm font-medium">
                            Previous
                        </a>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <span class="bg-sap-blue-600 text-white px-3 py-2 rounded-lg text-sm font-medium">{{ num }}</span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <a href="?page={{ num }}"
                               class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-3 py-2 rounded-lg text-sm font-medium">{{ num }}</a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}"
                           class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-3 py-2 rounded-lg text-sm font-medium">
                            Next
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    window.exportTransactions = function() {
        // Export functionality would be implemented here
        alert('Export functionality would be implemented here');
    };
    
    window.deleteTransaction = function(transactionId) {
        if (confirm('Are you sure you want to delete this transaction? This action cannot be undone.')) {
            window.location.href = `/accounts/transactions/creditors-debitors/${transactionId}/delete/`;
        }
    };
    
    // Initialize Lucide icons
    lucide.createIcons();
});
</script>
{% endblock %}