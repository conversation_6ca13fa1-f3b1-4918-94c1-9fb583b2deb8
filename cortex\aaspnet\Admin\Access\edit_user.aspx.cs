﻿using System;
using System.Collections;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Xml.Linq;
using System.Data.SqlClient;

public partial class Admin_Access_edit_user : System.Web.UI.Page
{
    clsFunctions fun = new clsFunctions();
    string username = "";
    protected void Page_Load(object sender, EventArgs e)
    {

        try
        {
            username = Request.QueryString["username"];
            string connStr = fun.Connection();
            SqlConnection con = new SqlConnection(connStr);
            con.Open();
            string Sql = fun.select("UserId", "aspnet_Users", "UserName='" + username + "'");
            SqlCommand cmd = new SqlCommand(Sql, con);
            SqlDataAdapter Da = new SqlDataAdapter(cmd);
            DataSet Ds = new DataSet();
            Da.Fill(Ds);
            if (Ds.Tables[0].Rows.Count > 0)
            {
                string Sql2 = fun.select("IsLockedOut", "aspnet_Membership", "UserId='" + Ds.Tables[0].Rows[0]["UserId"].ToString() + "'");
                SqlCommand cmd2 = new SqlCommand(Sql2, con);
                SqlDataAdapter Da2 = new SqlDataAdapter(cmd2);
                DataSet Ds2 = new DataSet();
                Da2.Fill(Ds2);
                if (Ds2.Tables[0].Rows.Count > 0)
                {

                    if (Ds2.Tables[0].Rows[0]["IsLockedOut"].ToString() == "True")
                    {
                        Button1.Visible = true;
                    }

                    else
                    {
                        Button3.Visible = true;
                    }

                }
            }
        }
        catch (Exception ex)
        {

        }
    }
    

    protected void UnlockUser(object sender, EventArgs e)
    {
       
        try
        {
            string connStr = fun.Connection();
        SqlConnection con = new SqlConnection(connStr);
        con.Open();
        string Sql = fun.select("UserId", "aspnet_Users", "UserName='"+username+"'");
        SqlCommand cmd = new SqlCommand(Sql,con);
        SqlDataAdapter Da = new SqlDataAdapter(cmd);
        DataSet Ds = new DataSet();
        Da.Fill(Ds);

        if (Ds.Tables[0].Rows.Count > 0)
        {
            string StrSql = fun.update("aspnet_Membership", "IsLockedOut='false'", " UserId='" + Ds.Tables[0].Rows[0]["UserId"].ToString() + "'");

            SqlCommand cmdSql = new SqlCommand(StrSql, con);
            cmdSql.ExecuteNonQuery();
            con.Close();
            Page.Response.Redirect(Page.Request.Url.ToString(),true);

        }
            

        }
        catch(Exception ex)
        {

        }
    }


    protected void LockUser(object sender, EventArgs e)
    {

        try
        {
            string connStr = fun.Connection();
            SqlConnection con = new SqlConnection(connStr);
            con.Open();
            string Sql = fun.select("UserId", "aspnet_Users", "UserName='" + username + "'");
            SqlCommand cmd = new SqlCommand(Sql, con);
            SqlDataAdapter Da = new SqlDataAdapter(cmd);
            DataSet Ds = new DataSet();
            Da.Fill(Ds);
            if (Ds.Tables[0].Rows.Count > 0)
            {
                string StrSql = fun.update("aspnet_Membership", "IsLockedOut='True'", " UserId='" + Ds.Tables[0].Rows[0]["UserId"].ToString() + "'");

                SqlCommand cmdSql = new SqlCommand(StrSql, con);
                cmdSql.ExecuteNonQuery();
                con.Close();
                Page.Response.Redirect(Page.Request.Url.ToString(), true);

            }            

        }
        catch (Exception ex)
        {

        }
    }
}
