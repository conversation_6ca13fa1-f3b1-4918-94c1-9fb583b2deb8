{% extends "core/base.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-gradient-to-r from-purple-600 to-purple-800 rounded-lg shadow-lg mb-6">
            <div class="px-6 py-4">
                <h1 class="text-2xl font-bold text-white">{{ title }}</h1>
                <p class="text-purple-100 mt-1">WO No: {{ work_order.wono }} - {{ work_order.customer_name }}</p>
            </div>
        </div>

        <!-- Work Order Items -->
        <div class="bg-white rounded-lg shadow-lg mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">Work Order Items</h2>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                SN
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Item Code
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Description
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Quantity
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Type
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Machine
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Shift
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                From Date
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                To Date
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for item in work_order_items %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ forloop.counter }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {{ item.itemcode.itemcode|default:"-" }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                                {{ item.description|default:"-"|truncatechars:60 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                                {{ item.qty|default:"0" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <select class="text-sm border-gray-300 rounded-md">
                                    <option value="0">Finished</option>
                                    <option value="1">Semi Finished</option>
                                    <option value="2">Raw Material</option>
                                </select>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <select class="text-sm border-gray-300 rounded-md w-32">
                                    <option value="">Select Machine</option>
                                    <!-- Machines will be populated via HTMX -->
                                </select>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <select class="text-sm border-gray-300 rounded-md">
                                    <option value="1">1st Shift</option>
                                    <option value="2">2nd Shift</option>
                                    <option value="3">3rd Shift</option>
                                </select>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="date" class="text-sm border-gray-300 rounded-md">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="date" class="text-sm border-gray-300 rounded-md">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button type="button" class="text-green-600 hover:text-green-900 mr-2">
                                    Add
                                </button>
                                <button type="button" class="text-red-600 hover:text-red-900">
                                    Remove
                                </button>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="10" class="px-6 py-12 text-center text-gray-500">
                                <div class="flex flex-col items-center">
                                    <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    <p class="text-lg font-medium text-gray-900 mb-1">No items found!</p>
                                    <p class="text-gray-500">This work order has no items to schedule</p>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Job Schedule Form -->
        <form method="post" class="bg-white rounded-lg shadow-lg p-6">
            {% csrf_token %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Job Number -->
                <div>
                    <label for="{{ form.jobno.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Job Number
                    </label>
                    {{ form.jobno }}
                    {% if form.jobno.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.jobno.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Work Order Number (readonly) -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        Work Order Number
                    </label>
                    <input type="text" 
                           value="{{ work_order.wono }}" 
                           readonly
                           class="mt-1 block w-full rounded-md border-gray-300 bg-gray-50 shadow-sm">
                </div>

                <!-- Item -->
                <div>
                    <label for="{{ form.itemid.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Item
                    </label>
                    {{ form.itemid }}
                    {% if form.itemid.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.itemid.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>

            <!-- Form Actions -->
            <div class="mt-6 flex justify-between">
                <a href="{% url 'machinery:schedule_create' %}" 
                   class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">
                    Back to Work Orders
                </a>
                <div class="space-x-2">
                    <button type="button" 
                            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Save Schedule Details
                    </button>
                    <button type="submit" 
                            class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                        Create Schedule
                    </button>
                </div>
            </div>
        </form>

        <!-- Scheduled Items -->
        <div class="mt-6 bg-white rounded-lg shadow-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">Scheduled Items</h2>
            </div>
            <div id="scheduled-items" class="p-6">
                <div class="text-center py-8">
                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <p class="text-gray-500">No items scheduled yet</p>
                    <p class="text-sm text-gray-400">Add schedule details for work order items above</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="{% static 'js/htmx.min.js' %}"></script>
<script>
// Add interactivity for scheduling
document.addEventListener('DOMContentLoaded', function() {
    // Initialize form functionality
    console.log('Schedule detail form initialized');
});
</script>
{% endblock %}