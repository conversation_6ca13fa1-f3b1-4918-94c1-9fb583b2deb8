<!-- accounts/templates/accounts/vouchers/contra_entry_form.html -->
<!-- Contra Entry Create/Edit Form Template -->
<!-- Task Group 2: Banking & Cash Management - Contra Entry Form (Task 2.4) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{% if object %}Edit Contra Entry{% else %}New Contra Entry{% endif %} - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-purple-600 to-sap-purple-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="repeat" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">
                        {% if object %}Edit Contra Entry{% else %}New Contra Entry{% endif %}
                    </h1>
                    <p class="text-sm text-sap-gray-600 mt-1">
                        {% if object %}Modify contra entry details{% else %}Record a transfer between cash and bank accounts{% endif %}
                    </p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:contra_entry_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Contra Entry Form -->
    <div class="max-w-6xl mx-auto">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="edit" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                    Contra Entry Information
                </h3>
                <p class="text-sm text-sap-gray-600 mt-1">Complete all required fields to record the fund transfer</p>
            </div>
            
            <form method="post" id="contra-entry-form" class="p-6" x-data="contraEntryForm()">
                {% csrf_token %}
                
                <!-- Entry Header Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="info" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                        Entry Header
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <!-- Entry Number -->
                        <div>
                            <label for="{{ form.entry_number.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Entry Number
                            </label>
                            {{ form.entry_number|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500" }}
                            {% if form.entry_number.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.entry_number.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Auto-generated if left blank</p>
                        </div>
                        
                        <!-- Entry Date -->
                        <div>
                            <label for="{{ form.entry_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Entry Date *
                            </label>
                            {{ form.entry_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500" }}
                            {% if form.entry_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.entry_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Transfer Type -->
                        <div>
                            <label for="{{ form.transfer_type.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Transfer Type *
                            </label>
                            {{ form.transfer_type|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500" }}
                            {% if form.transfer_type.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.transfer_type.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Amount -->
                        <div>
                            <label for="{{ form.amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Amount (₹) *
                            </label>
                            {{ form.amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500" }}
                            {% if form.amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Source Account Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="arrow-up-circle" class="w-5 h-5 mr-2 text-sap-red-600"></i>
                        Source Account (From)
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- From Account Type -->
                        <div>
                            <label for="{{ form.from_account_type.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                From Account Type *
                            </label>
                            {{ form.from_account_type|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500" }}
                            {% if form.from_account_type.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.from_account_type.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- From Account -->
                        <div>
                            <label for="{{ form.from_account.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                From Account *
                            </label>
                            {{ form.from_account|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500" }}
                            {% if form.from_account.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.from_account.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Destination Account Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="arrow-down-circle" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                        Destination Account (To)
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- To Account Type -->
                        <div>
                            <label for="{{ form.to_account_type.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                To Account Type *
                            </label>
                            {{ form.to_account_type|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500" }}
                            {% if form.to_account_type.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.to_account_type.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- To Account -->
                        <div>
                            <label for="{{ form.to_account.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                To Account *
                            </label>
                            {{ form.to_account|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500" }}
                            {% if form.to_account.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.to_account.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Transaction Details Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="file-text" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Transaction Details
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Reference Number -->
                        <div>
                            <label for="{{ form.reference_number.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Reference Number
                            </label>
                            {{ form.reference_number|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500" }}
                            {% if form.reference_number.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.reference_number.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Bank transaction reference or cheque number</p>
                        </div>
                        
                        <!-- Transfer Mode -->
                        <div>
                            <label for="{{ form.transfer_mode.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Transfer Mode
                            </label>
                            {{ form.transfer_mode|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500" }}
                            {% if form.transfer_mode.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.transfer_mode.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Description -->
                        <div class="md:col-span-2">
                            <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Description *
                            </label>
                            {{ form.description|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500" }}
                            {% if form.description.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.description.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Detailed description of the fund transfer</p>
                        </div>
                    </div>
                </div>
                
                <!-- Additional Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="sticky-note" class="w-5 h-5 mr-2 text-sap-gray-600"></i>
                        Additional Information
                    </h4>
                    <div class="grid grid-cols-1 gap-6">
                        <!-- Remarks -->
                        <div>
                            <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Remarks
                            </label>
                            {{ form.remarks|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-purple-500 focus:border-sap-purple-500" }}
                            {% if form.remarks.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.remarks.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Additional notes for this contra entry</p>
                        </div>
                    </div>
                </div>
                
                <!-- Transfer Summary Display -->
                <div class="mb-8 bg-sap-purple-50 border border-sap-purple-200 rounded-lg p-6" x-show="amount > 0">
                    <h4 class="text-lg font-medium text-sap-purple-800 mb-4 flex items-center">
                        <i data-lucide="transfer" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                        Transfer Summary
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="text-center p-4 bg-sap-red-50 border border-sap-red-200 rounded-lg">
                            <p class="text-sm text-sap-red-600 mb-2">From Account</p>
                            <p class="text-lg font-bold text-sap-red-800" x-text="fromAccount"></p>
                            <p class="text-sm text-sap-red-600" x-text="fromAccountType"></p>
                        </div>
                        <div class="text-center p-4 bg-sap-purple-100 border border-sap-purple-300 rounded-lg flex items-center justify-center">
                            <div>
                                <i data-lucide="arrow-right" class="w-8 h-8 text-sap-purple-600 mx-auto mb-2"></i>
                                <p class="text-xl font-bold text-sap-purple-800" x-text="'₹' + amount.toFixed(2)"></p>
                                <p class="text-sm text-sap-purple-600" x-text="transferType"></p>
                            </div>
                        </div>
                        <div class="text-center p-4 bg-sap-green-50 border border-sap-green-200 rounded-lg">
                            <p class="text-sm text-sap-green-600 mb-2">To Account</p>
                            <p class="text-lg font-bold text-sap-green-800" x-text="toAccount"></p>
                            <p class="text-sm text-sap-green-600" x-text="toAccountType"></p>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="resetForm()" 
                                class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="rotate-ccw" class="w-4 h-4 inline mr-2"></i>
                            Reset
                        </button>
                        <button type="button" @click="validateTransfer()" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check-circle" class="w-4 h-4 inline mr-2"></i>
                            Validate Transfer
                        </button>
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="{% url 'accounts:contra_entry_list' %}" 
                           class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                            {% if object %}Update Entry{% else %}Save Entry{% endif %}
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Contra Entry Guidelines -->
        <div class="mt-6 bg-sap-blue-50 border border-sap-blue-200 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i data-lucide="info" class="w-5 h-5 text-sap-blue-600"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-sap-blue-800">Contra Entry Guidelines</h3>
                    <div class="mt-2 text-sm text-sap-blue-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Contra entries are used for fund transfers between cash and bank accounts</li>
                            <li>Source and destination accounts cannot be the same</li>
                            <li>Entry number will be auto-generated if not provided</li>
                            <li>Include reference numbers for bank transfers or cheques</li>
                            <li>Ensure sufficient balance in the source account</li>
                            <li>Both accounts will be updated automatically after entry confirmation</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function contraEntryForm() {
    return {
        amount: 0,
        transferType: '',
        fromAccount: '',
        toAccount: '',
        fromAccountType: '',
        toAccountType: '',
        
        init() {
            this.updateValues();
        },
        
        updateValues() {
            const amountField = document.getElementById('{{ form.amount.id_for_label }}');
            const transferTypeField = document.getElementById('{{ form.transfer_type.id_for_label }}');
            const fromAccountField = document.getElementById('{{ form.from_account.id_for_label }}');
            const toAccountField = document.getElementById('{{ form.to_account.id_for_label }}');
            const fromTypeField = document.getElementById('{{ form.from_account_type.id_for_label }}');
            const toTypeField = document.getElementById('{{ form.to_account_type.id_for_label }}');
            
            if (amountField) this.amount = parseFloat(amountField.value) || 0;
            if (transferTypeField) this.transferType = transferTypeField.options[transferTypeField.selectedIndex]?.text || '';
            if (fromAccountField) this.fromAccount = fromAccountField.options[fromAccountField.selectedIndex]?.text || '';
            if (toAccountField) this.toAccount = toAccountField.options[toAccountField.selectedIndex]?.text || '';
            if (fromTypeField) this.fromAccountType = fromTypeField.options[fromTypeField.selectedIndex]?.text || '';
            if (toTypeField) this.toAccountType = toTypeField.options[toTypeField.selectedIndex]?.text || '';
        },
        
        validateTransfer() {
            if (!this.amount || this.amount <= 0) {
                alert('Please enter a valid transfer amount.');
                return;
            }
            
            if (!this.fromAccount || !this.toAccount) {
                alert('Please select both source and destination accounts.');
                return;
            }
            
            if (this.fromAccount === this.toAccount) {
                alert('Source and destination accounts cannot be the same.');
                return;
            }
            
            alert('Transfer validation passed. You can proceed to save the entry.');
        }
    }
}

function resetForm() {
    if (confirm('Are you sure you want to reset the form? All unsaved changes will be lost.')) {
        document.getElementById('contra-entry-form').reset();
    }
}

// Auto-generate entry number if creating new entry
document.addEventListener('DOMContentLoaded', function() {
    const entryNumberInput = document.getElementById('{{ form.entry_number.id_for_label }}');
    if (entryNumberInput && !entryNumberInput.value) {
        // Generate entry number based on current timestamp
        const today = new Date();
        const year = today.getFullYear().toString().substr(-2);
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        const sequence = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        
        entryNumberInput.value = `CE-${year}${month}${day}${sequence}`;
    }
    
    // Auto-update summary display
    const amountField = document.getElementById('{{ form.amount.id_for_label }}');
    const transferTypeField = document.getElementById('{{ form.transfer_type.id_for_label }}');
    const fromAccountField = document.getElementById('{{ form.from_account.id_for_label }}');
    const toAccountField = document.getElementById('{{ form.to_account.id_for_label }}');
    const fromTypeField = document.getElementById('{{ form.from_account_type.id_for_label }}');
    const toTypeField = document.getElementById('{{ form.to_account_type.id_for_label }}');
    
    [amountField, transferTypeField, fromAccountField, toAccountField, fromTypeField, toTypeField].forEach(field => {
        if (field) {
            field.addEventListener('change', function() {
                // Trigger Alpine.js update
                const alpineData = Alpine.$data(document.querySelector('[x-data="contraEntryForm()"]'));
                if (alpineData) {
                    alpineData.updateValues();
                }
            });
        }
    });
    
    lucide.createIcons();
});
</script>
{% endblock %}