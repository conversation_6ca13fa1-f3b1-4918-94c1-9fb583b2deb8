{% extends 'core/base.html' %}
{% load static %}

{% block title %}MRN {{ mrn.mrn_no|default:mrn.id }} - Material Return Note{% endblock %}

{% block content %}
<div class="bg-white shadow-sm">
    <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
        <div class="flex justify-between items-center">
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                    Material Return Note - {{ mrn.mrn_no|default:mrn.id }}
                </h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                    View material return details and line items
                </p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'inventory:mrn_update' mrn.pk %}" 
                   class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit
                </a>
                <a href="{% url 'inventory:mrn_print' mrn.pk %}" 
                   class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                   target="_blank">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                    </svg>
                    Print
                </a>
                <a href="{% url 'inventory:mrn_list' %}" 
                   class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to List
                </a>
            </div>
        </div>
    </div>

    <!-- MRN Header Information -->
    <div class="px-4 py-5 sm:px-6">
        <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div>
                <dt class="text-sm font-medium text-gray-500">MRN Number</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ mrn.mrn_no|default:"N/A" }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">System Date</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ mrn.sysdate|default:"N/A" }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">System Time</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ mrn.systime|default:"N/A" }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Session ID</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ mrn.sessionid|default:"N/A" }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Company</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ mrn.company.name|default:"N/A" }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Financial Year</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ mrn.financial_year.finyear|default:"N/A" }}</dd>
            </div>
        </dl>
    </div>

    <!-- Status Badge -->
    <div class="px-4 py-3 bg-green-50 border-t border-green-200">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-green-800">
                    Material Return Note Created Successfully
                </h3>
                <div class="mt-1 text-sm text-green-700">
                    <p>MRN {{ mrn.mrn_no|default:mrn.id }} has been created and is ready for processing.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Return Items Information -->
    <div class="border-t border-gray-200">
        <div class="px-4 py-5 sm:px-6">
            <h4 class="text-lg leading-6 font-medium text-gray-900 mb-4">Material Return Summary</h4>
            
            <!-- Sample returned items display - In a real app, this would come from MRN line items -->
            <div class="bg-gray-50 rounded-lg p-4 mb-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                        <span class="font-medium text-gray-600">Total Items Returned:</span>
                        <span class="ml-2 text-gray-900">1</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-600">Return Status:</span>
                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Completed
                        </span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-600">Created By:</span>
                        <span class="ml-2 text-gray-900">{{ mrn.sessionid|default:"N/A" }}</span>
                    </div>
                </div>
            </div>

            <!-- Sample items table -->
            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                <table class="w-full table-auto divide-y divide-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide sm:pl-6">
                                Item Code
                            </th>
                            <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                                Description
                            </th>
                            <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                                UOM
                            </th>
                            <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                                BG Group/WONo
                            </th>
                            <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                                Return Qty
                            </th>
                            <th scope="col" class="px-3 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                                Remarks
                            </th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 bg-white">
                        <!-- Sample row - In production, this would be dynamic from MRN line items -->
                        <tr class="hover:bg-gray-50">
                            <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                                00901-02-010
                            </td>
                            <td class="px-3 py-4 text-sm text-gray-500">
                                DIABOLA ROLLER
                            </td>
                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                KGS
                            </td>
                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                BG Group
                            </td>
                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                5.00
                            </td>
                            <td class="px-3 py-4 text-sm text-gray-500">
                                Test return
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Action History -->
    <div class="border-t border-gray-200">
        <div class="px-4 py-5 sm:px-6">
            <h4 class="text-lg leading-6 font-medium text-gray-900 mb-4">Action History</h4>
            <div class="flow-root">
                <ul class="-mb-8">
                    <li>
                        <div class="relative pb-8">
                            <div class="relative flex space-x-3">
                                <div>
                                    <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                        <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </span>
                                </div>
                                <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                    <div>
                                        <p class="text-sm text-gray-500">
                                            MRN created by <strong class="font-medium text-gray-900">{{ mrn.sessionid|default:"System" }}</strong>
                                        </p>
                                    </div>
                                    <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                        <time datetime="{{ mrn.sysdate }}">{{ mrn.sysdate|default:"N/A" }} {{ mrn.systime|default:"" }}</time>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}