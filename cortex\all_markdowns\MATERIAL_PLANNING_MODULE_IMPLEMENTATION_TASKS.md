# Material Planning Module Implementation Tasks

This document outlines the complete implementation plan for converting the legacy ASP.NET Material Planning module to Django. The module contains 14 files organized into 4 functional groups that need to be systematically converted.

## Overview
- **Total Files**: 14 ASP.NET files (.aspx/.aspx.cs pairs + Web.config files)
- **Django App**: `material_planning/`
- **Implementation Strategy**: Convert each functional group as a cohesive unit
- **Priority**: Start with process definition, then core planning functionality

## Module Characteristics
- **Focused Scope**: Smallest module with specific material planning focus
- **Manufacturing Bridge**: Connects Design (BOM) to Procurement (Material Management)
- **Supplier-Centric**: Multi-category supplier management (Raw Material, Process, Finish)
- **Work Order Driven**: Planning based on manufacturing work orders
- **Procurement Planning**: Detailed procurement planning with rates and delivery schedules
- **BOM Integration**: Heavy integration with Bill of Materials from Design module

---

## Task Group 1: Process Definition and Management
**Priority**: HIGH - Foundation for planning processes
**Files**: 2 files (1 .aspx + 1 .aspx.cs)

### Files to Convert:
**Masters:**
- `Module/MaterialPlanning/Masters/ItemProcess.aspx` + `.aspx.cs`

### Implementation Tasks:
- [ ] **Task 1.1**: Create Process Definition models in `material_planning/models.py`
  - `ItemProcess` (manufacturing process master data)
  - `ProcessCategory` (process classification - Raw Material, Processing, Finishing)
  - `ProcessSequence` (process order and dependencies)
  - `ProcessParameter` (process-specific parameters and configurations)
  - `ProcessCapability` (process capacity and capability metrics)
  - `ProcessCost` (process cost structures and rates)
- [ ] **Task 1.2**: Create Process forms with validation
  - `ItemProcessForm` with process validation rules
  - `ProcessCategoryForm` with category management
  - `ProcessSequenceForm` with dependency validation
  - `ProcessParameterForm` with parameter configuration
- [ ] **Task 1.3**: Implement Process CRUD views
  - `ItemProcessListView` - list all manufacturing processes
  - `ItemProcessCreateView` - create new process definitions
  - `ItemProcessUpdateView` - edit existing processes
  - `ItemProcessDetailView` - detailed process information
  - `ItemProcessDeleteView` - remove obsolete processes
  - `ProcessCategoryListView` / `CreateView` / `UpdateView`
- [ ] **Task 1.4**: Add process validation system
  - Process name uniqueness validation
  - Process symbol standardization
  - Process category consistency checking
  - Process capability validation
- [ ] **Task 1.5**: Create process hierarchy management
  - Parent-child process relationships
  - Process dependency mapping
  - Process sequence validation
  - Alternative process routing
- [ ] **Task 1.6**: Add process standardization
  - Standard process templates
  - Process best practices library
  - Process certification tracking
  - Process optimization recommendations
- [ ] **Task 1.7**: Create process analytics
  - Process utilization metrics
  - Process efficiency tracking
  - Process cost analysis
  - Process bottleneck identification
- [ ] **Task 1.8**: Integrate with Design module
  - Link processes to BOM items
  - Process requirement specification
  - Process routing in BOM structure
  - Engineering change impact on processes
- [ ] **Task 1.9**: Create process templates with HTMX
  - Process definition interface
  - Process hierarchy visualization
  - Process parameter configuration
  - Process analytics dashboard
- [ ] **Task 1.10**: Add process URL patterns
- [ ] **Task 1.11**: Write process management tests

---

## Task Group 2: Material Planning Creation and Management
**Priority**: HIGH - Core planning functionality
**Files**: 6 files (3 .aspx + 3 .aspx.cs)

### Files to Convert:
**Transactions:**
- `Module/MaterialPlanning/Transactions/Planning_New.aspx` + `.aspx.cs`
- `Module/MaterialPlanning/Transactions/Planning_Edit.aspx` + `.aspx.cs`
- `Module/MaterialPlanning/Transactions/Planning_Edit_Details.aspx` + `.aspx.cs`

### Implementation Tasks:
- [ ] **Task 2.1**: Create Material Planning models
  - `MaterialPlan` (main planning document header)
  - `PlanningLineItem` (individual items in planning)
  - `PlanningSupplier` (supplier assignments by category)
  - `PlanningSchedule` (delivery schedule and milestones)
  - `PlanningRevision` (planning change history)
  - `PlanningApproval` (approval workflow for plans)
  - `PlanningExecution` (planning execution tracking)
  - `PlanningClosure` (planning completion records)
- [ ] **Task 2.2**: Create planning forms with validation
  - `MaterialPlanForm` with work order validation
  - `PlanningLineItemFormSet` for multiple items
  - `PlanningSupplierFormSet` for supplier categories
  - `PlanningScheduleForm` with date validation
  - Advanced search forms with multiple filters
- [ ] **Task 2.3**: Implement Planning CRUD views
  - `MaterialPlanListView` with advanced filtering (Customer, Enquiry, PO, WO)
  - `MaterialPlanCreateView` with work order integration
  - `MaterialPlanUpdateView` with revision tracking
  - `MaterialPlanDetailView` with comprehensive planning information
  - `MaterialPlanCopyView` for copying existing plans
  - `PlanningRevisionView` for managing plan changes
- [ ] **Task 2.4**: Add work order integration
  - Automatic plan creation from work orders
  - Work order requirement analysis
  - BOM integration for material requirements
  - Engineering change impact on planning
- [ ] **Task 2.5**: Create planning workflow
  - Draft → Under Review → Approved → Execution → Completed → Closed
  - Multi-level approval based on plan value
  - Revision approval for plan changes
  - Emergency planning fast-track process
- [ ] **Task 2.6**: Add planning search and filtering
  - Advanced search by Customer Name, Enquiry No, PO No, WO No
  - Date range filtering for planning periods
  - Status-based filtering (Draft, Approved, Executing, etc.)
  - Supplier-based filtering
  - Priority-based filtering
- [ ] **Task 2.7**: Create planning analytics
  - Planning turnaround time analysis
  - Planning accuracy metrics
  - Resource utilization analysis
  - Cost optimization opportunities
- [ ] **Task 2.8**: Add planning collaboration
  - Multi-user planning collaboration
  - Planning comments and notes
  - Planning approval notifications
  - Supplier communication integration
- [ ] **Task 2.9**: Implement planning validation
  - Resource availability checking
  - Capacity constraint validation
  - Lead time feasibility analysis
  - Budget availability verification
- [ ] **Task 2.10**: Create planning templates with HTMX
  - Dynamic planning creation interface
  - Real-time work order integration
  - Interactive planning editing with live updates
  - Planning comparison and analysis views
- [ ] **Task 2.11**: Add planning URL patterns
- [ ] **Task 2.12**: Write planning functionality tests

---

## Task Group 3: Planning Detail Management and Supplier Coordination
**Priority**: HIGH - Detailed procurement planning
**Files**: 2 files (1 .aspx + 1 .aspx.cs)

### Files to Convert:
**Transactions:**
- `Module/MaterialPlanning/Transactions/pdt.aspx` + `.aspx.cs` (Planning Detail Transaction)

### Implementation Tasks:
- [ ] **Task 3.1**: Create Planning Detail models
  - `PlanningDetail` (detailed item-level planning)
  - `BOMMaterialPlan` (BOM-based material planning)
  - `SupplierCategoryPlan` (planning by supplier category)
  - `RawMaterialSupplier` (Category A suppliers)
  - `ProcessSupplier` (Category O suppliers)
  - `FinishSupplier` (Category F suppliers)
  - `SupplierQuotation` (supplier rate and terms)
  - `DeliverySchedule` (delivery planning and tracking)
  - `PlanningDocument` (drawings, specs, attachments)
  - `QuantityBreakdown` (BOM Qty, PR Qty, WIS Qty, GQN Qty analysis)
- [ ] **Task 3.2**: Create detailed planning forms
  - `PlanningDetailForm` with BOM integration
  - `BOMMaterialPlanForm` with quantity analysis
  - `SupplierCategoryPlanFormSet` for A/O/F suppliers
  - `SupplierQuotationForm` with rate negotiations
  - `DeliveryScheduleForm` with milestone tracking
  - `PlanningDocumentForm` with file attachments
- [ ] **Task 3.3**: Implement Planning Detail views
  - `PlanningDetailView` with BOM breakdown display
  - `BOMMaterialPlanView` with quantity analysis
  - `SupplierPlanningView` with category-wise supplier management
  - `SupplierQuotationView` with rate comparison
  - `DeliveryPlanningView` with schedule optimization
  - `PlanningDocumentView` with document management
  - `QuantityAnalysisView` with detailed quantity breakdown
- [ ] **Task 3.4**: Add BOM integration engine
  - Automatic BOM data retrieval from Design module
  - Multi-level BOM explosion for planning
  - BOM quantity analysis (Required vs Available vs Planned)
  - BOM change impact on existing plans
  - Alternative BOM support for planning scenarios
- [ ] **Task 3.5**: Create supplier categorization system
  - **Category A (Raw Material Suppliers)**:
    - Material supplier qualification and selection
    - Raw material rate negotiation and contracts
    - Material delivery schedule planning
    - Material quality specification management
  - **Category O (Process Suppliers)**:
    - Process service provider management
    - Process rate and capacity planning
    - Process delivery schedule coordination
    - Process quality and capability tracking
  - **Category F (Finish Suppliers)**:
    - Finishing service provider management
    - Finishing process rate planning
    - Finishing delivery coordination
    - Final product quality specifications
- [ ] **Task 3.6**: Add quantity breakdown analysis
  - **BOM Qty**: Required quantities from BOM
  - **PR Qty**: Quantities already in Purchase Requisitions
  - **WIS Qty**: Work-in-Progress quantities
  - **GQN Qty**: Quality-approved quantities
  - **Net Requirement**: Calculated net planning requirement
  - **Safety Stock**: Safety stock considerations
- [ ] **Task 3.7**: Create rate and pricing management
  - Supplier rate comparison across categories
  - Rate negotiation tracking and history
  - Discount structure management
  - Price escalation and indexation
  - Total cost of ownership analysis
- [ ] **Task 3.8**: Add delivery planning optimization
  - Delivery schedule optimization across suppliers
  - Critical path analysis for delivery dependencies
  - Delivery risk assessment and mitigation
  - Expediting and follow-up management
  - Delivery performance tracking
- [ ] **Task 3.9**: Implement document management
  - Technical drawings and specifications
  - Supplier documentation and certificates
  - Quality specifications and standards
  - Process instructions and procedures
  - Version control and change management
- [ ] **Task 3.10**: Create planning execution tracking
  - Real-time planning execution status
  - Supplier performance against plan
  - Delivery milestone tracking
  - Quality achievement monitoring
  - Cost performance analysis
- [ ] **Task 3.11**: Add planning optimization algorithms
  - Supplier selection optimization
  - Delivery schedule optimization
  - Cost minimization algorithms
  - Risk-adjusted planning scenarios
  - Resource allocation optimization
- [ ] **Task 3.12**: Create detailed planning templates with HTMX
  - Interactive BOM breakdown interface
  - Dynamic supplier category management
  - Real-time quantity calculations
  - Supplier rate comparison tools
  - Delivery schedule visualization
  - Document management interface
- [ ] **Task 3.13**: Add detailed planning URL patterns
- [ ] **Task 3.14**: Write comprehensive planning detail tests

---

## Task Group 4: Planning Reporting and Analytics
**Priority**: MEDIUM - Business intelligence and performance tracking
**Files**: 2 files (Web.config only - reports to be implemented)

### Files to Convert:
**Reports:**
- Reports directory currently contains only Web.config - business reports need to be designed and implemented

### Implementation Tasks:
- [ ] **Task 4.1**: Create Planning Reporting models
  - `PlanningReport` (report templates and configurations)
  - `PlanningKPI` (key performance indicators)
  - `PlanningAnalytics` (analytical calculations)
  - `PlanningTrend` (trend analysis data)
  - `SupplierPerformance` (supplier performance metrics)
  - `PlanningException` (exception and variance tracking)
- [ ] **Task 4.2**: Create reporting forms
  - `PlanningReportForm` with date range and filters
  - `PlanningAnalyticsForm` with KPI selection
  - `SupplierPerformanceForm` with supplier filters
  - `PlanningExceptionForm` with variance criteria
- [ ] **Task 4.3**: Implement Planning reporting views
  - `PlanningDashboardView` - executive planning dashboard
  - `PlanningPerformanceView` - planning performance metrics
  - `SupplierPerformanceView` - supplier performance analysis
  - `PlanningTrendView` - planning trend analysis
  - `PlanningExceptionView` - exception and variance reports
  - `PlanningUtilizationView` - resource utilization analysis
- [ ] **Task 4.4**: Add planning analytics engine
  - Planning accuracy calculations
  - Planning cycle time analysis
  - Cost variance analysis
  - Schedule adherence metrics
  - Quality achievement tracking
- [ ] **Task 4.5**: Create supplier performance analytics
  - Supplier delivery performance
  - Supplier quality performance
  - Supplier cost performance
  - Supplier relationship scoring
  - Supplier risk assessment
- [ ] **Task 4.6**: Add planning trend analysis
  - Planning volume trends
  - Planning complexity trends
  - Cost trend analysis
  - Supplier performance trends
  - Resource utilization trends
- [ ] **Task 4.7**: Implement planning exception reporting
  - Planning variance identification
  - Cost overrun alerts
  - Schedule delay notifications
  - Quality issue tracking
  - Supplier performance exceptions
- [ ] **Task 4.8**: Create interactive planning dashboards
  - Real-time planning status dashboard
  - Supplier performance scorecard
  - Planning KPI monitoring
  - Exception alerting system
  - Trend visualization tools
- [ ] **Task 4.9**: Add planning report automation
  - Scheduled report generation
  - Automated report distribution
  - Alert and notification system
  - Self-service reporting tools
- [ ] **Task 4.10**: Create reporting templates
  - Interactive dashboard interfaces
  - Report visualization components
  - Export and sharing capabilities
  - Mobile-responsive analytics
- [ ] **Task 4.11**: Add reporting URL patterns
- [ ] **Task 4.12**: Write reporting functionality tests

---

## Implementation Guidelines

### Technical Requirements:
1. **Models**: Use existing database with `managed = False`
2. **Views**: Only class-based views (ListView, CreateView, UpdateView, DeleteView)
3. **Frontend**: Django templates + HTMX + Alpine.js + Tailwind CSS + Chart.js for analytics
4. **Forms**: Include CSRF tokens, proper validation, dynamic form handling
5. **Authentication**: All views require `LoginRequiredMixin`
6. **Testing**: Both unit tests and Playwright end-to-end tests

### Implementation Order:
1. **Phase 1**: Task Group 1 (Process Definition Foundation)
2. **Phase 2**: Task Groups 2, 3 (Core Planning Functionality)
3. **Phase 3**: Task Group 4 (Reporting and Analytics)

### Key Models to Create:
```python
# Core Material Planning Models
class ItemProcess(models.Model):
    # Manufacturing process definitions
    
class MaterialPlan(models.Model):
    # Main planning document
    
class PlanningDetail(models.Model):
    # Detailed item-level planning
    
class SupplierCategoryPlan(models.Model):
    # Category-wise supplier planning
    
class BOMMaterialPlan(models.Model):
    # BOM-based material planning
    
class DeliverySchedule(models.Model):
    # Delivery planning and tracking
    
class SupplierQuotation(models.Model):
    # Supplier rates and terms
```

### Material Planning Workflow:
```
Work Order → BOM Analysis → Material Planning Creation →
Supplier Category Assignment (A/O/F) → Rate Negotiation →
Delivery Schedule Planning → Plan Approval →
Plan Execution → Supplier Coordination →
Delivery Tracking → Performance Analysis
```

### Integration Architecture:
```
Material Planning Integration Map:
├── Design Module → BOM Data & Engineering Changes
├── Material Management Module → Supplier Data & Purchase Orders
├── Inventory Module → Stock Levels & WIS Quantities
├── Quality Control Module → GQN Quantities & Quality Specs
├── Project Management Module → Work Orders & Project Schedules
├── MIS Module → Planning Analytics & KPIs
└── Accounts Module → Budget Validation & Cost Tracking
```

### Supplier Category Framework:
```
Category A (Raw Material Suppliers):
├── Material procurement
├── Raw material quality
├── Material delivery
└── Material cost optimization

Category O (Process Suppliers):
├── Process services
├── Process capability
├── Process delivery
└── Process cost management

Category F (Finish Suppliers):
├── Finishing services
├── Final product quality
├── Finishing delivery
└── Finishing cost control
```

### File Structure:
```
material_planning/
├── models.py
├── forms.py
├── views/
│   ├── __init__.py
│   ├── process_views.py
│   ├── planning_views.py
│   ├── detail_views.py
│   └── reporting_views.py
├── engines/
│   ├── __init__.py
│   ├── bom_engine.py
│   ├── planning_engine.py
│   ├── supplier_engine.py
│   └── analytics_engine.py
├── analytics/
│   ├── __init__.py
│   ├── planning_analytics.py
│   ├── supplier_analytics.py
│   └── performance_metrics.py
├── templates/material_planning/
│   ├── processes/
│   ├── planning/
│   ├── details/
│   ├── suppliers/
│   ├── reports/
│   └── partials/
├── urls.py
├── admin.py
└── tests.py
```

### Success Criteria:
- [ ] All 14 ASP.NET files successfully converted
- [ ] Complete material planning workflow automation
- [ ] BOM-integrated planning system
- [ ] Multi-category supplier management (A/O/F)
- [ ] Comprehensive planning analytics
- [ ] Real-time planning execution tracking
- [ ] Mobile-responsive planning interface
- [ ] Performance optimization completed
- [ ] Security best practices implemented
- [ ] Documentation completed

### Business Benefits:
- Automated material planning from BOMs
- Systematic supplier category management
- Optimized delivery scheduling
- Real-time planning execution tracking
- Comprehensive planning analytics
- Improved supplier coordination
- Cost optimization through better planning
- Risk mitigation through systematic planning
- Enhanced planning accuracy and efficiency

**Total Estimated Tasks**: 49 individual implementation tasks across 4 functional groups

### Key Performance Indicators:
- Planning accuracy percentage
- Planning cycle time reduction
- Supplier delivery performance
- Cost variance minimization
- Schedule adherence rates
- Planning execution efficiency
- Supplier performance improvement
- Resource utilization optimization
- Quality achievement rates
- Overall planning effectiveness

### Unique Module Features:
- **Smallest but Strategic**: Despite being the smallest module (14 files), it's strategically important as the bridge between Design and Procurement
- **Supplier Category Innovation**: Unique A/O/F supplier categorization (Raw Material/Process/Finish)
- **BOM-Driven Planning**: Deep integration with BOM for automatic material requirement calculation
- **Multi-Dimensional Planning**: Combines material, process, and finishing requirements in single planning view
- **Quantity Analysis Engine**: Sophisticated quantity breakdown (BOM/PR/WIS/GQN quantities)
- **Delivery Optimization**: Advanced delivery schedule optimization across multiple supplier categories