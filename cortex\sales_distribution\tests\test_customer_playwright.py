"""
Playwright end-to-end tests for Customer Master functionality.
Tests the complete user workflow from navigation to form submission.
"""

import pytest
from playwright.sync_api import Page, expect
from django.test import LiveServerTestCase
from django.contrib.auth import get_user_model

from sys_admin.models import Country, State, City, Company, FinancialYear
from ..models import Customer


class CustomerMasterPlaywrightTestCase(LiveServerTestCase):
    """Base class for Playwright tests with Django Live Server"""
    
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        
        # Create test user
        User = get_user_model()
        cls.user = User.objects.create_user(
            username='admin',
            password='admin',
            email='<EMAIL>'
        )
        
        # Create test geographic data
        cls.country = Country.objects.create(
            id=1,
            name='India',
            country_code='IN'
        )
        
        cls.state = State.objects.create(
            id=1,
            name='Maharashtra', 
            state_code='MH',
            country=cls.country
        )
        
        cls.city = City.objects.create(
            id=1,
            name='Mumbai',
            city_code='MUM',
            state=cls.state
        )
        
        # Create test company and financial year
        cls.company = Company.objects.create(
            id=1,
            company_name='Test Company',
            registered_city=cls.city,
            plant_city=cls.city
        )
        
        cls.financial_year = FinancialYear.objects.create(
            id=1,
            financial_year='2023-24',
            company=cls.company
        )


@pytest.mark.django_db(transaction=True)
class TestCustomerMasterWorkflow:
    """Test complete customer master workflow using Playwright"""
    
    def test_customer_creation_workflow(self, page: Page, live_server):
        """Test complete customer creation workflow"""
        
        # Navigate to login page
        page.goto(f"{live_server.url}/login/")
        
        # Login
        page.fill('input[name="username"]', 'admin')
        page.fill('input[name="password"]', 'admin')
        page.click('button[type="submit"]')
        
        # Wait for dashboard to load
        expect(page).to_have_url(f"{live_server.url}/")
        
        # Navigate to Sales Distribution
        page.click('text=Sales Distribution')
        
        # Navigate to Customer Master
        page.click('text=Customer')
        page.click('text=New')
        
        # Should be on customer new page
        expect(page).to_have_url(f"{live_server.url}/sales-distribution/customers/new/")
        expect(page.locator('h1')).to_contain_text('Customer Master - New')
        
        # Fill customer basic information
        page.fill('input[name="customer_name"]', 'Playwright Test Customer Pvt Ltd')
        page.fill('input[name="contact_person"]', 'John Doe')
        page.fill('input[name="email"]', '<EMAIL>')
        page.fill('input[name="contact_no"]', '+91-9876543210')
        
        # Fill registered address
        page.fill('textarea[name="registered_address"]', '123 Business Park, Andheri East')
        page.select_option('select[name="registered_country"]', '1')  # India
        
        # Wait for state dropdown to populate (HTMX)
        expect(page.locator('select[name="registered_state"] option:nth-child(2)')).to_be_visible()
        page.select_option('select[name="registered_state"]', '1')  # Maharashtra
        
        # Wait for city dropdown to populate (HTMX)
        expect(page.locator('select[name="registered_city"] option:nth-child(2)')).to_be_visible()
        page.select_option('select[name="registered_city"]', '1')  # Mumbai
        
        page.fill('input[name="registered_pin"]', '400069')
        page.fill('input[name="registered_contact_no"]', '+91-22-26854321')
        page.fill('input[name="regdfaxno"]', '+91-22-26854321')
        
        # Fill works address
        page.fill('textarea[name="works_address"]', '456 Industrial Estate, Powai')
        page.select_option('select[name="works_country"]', '1')
        
        # Wait for works state dropdown
        expect(page.locator('select[name="works_state"] option:nth-child(2)')).to_be_visible()
        page.select_option('select[name="works_state"]', '1')
        
        # Wait for works city dropdown
        expect(page.locator('select[name="works_city"] option:nth-child(2)')).to_be_visible()
        page.select_option('select[name="works_city"]', '1')
        
        page.fill('input[name="works_pin"]', '400076')
        page.fill('input[name="works_contact_no"]', '+91-22-26854322')
        page.fill('input[name="workfaxno"]', '+91-22-26854322')
        
        # Fill material delivery address
        page.fill('textarea[name="material_address"]', '789 Logistics Hub, Navi Mumbai')
        page.select_option('select[name="material_country"]', '1')
        
        # Wait for material state dropdown
        expect(page.locator('select[name="material_state"] option:nth-child(2)')).to_be_visible()
        page.select_option('select[name="material_state"]', '1')
        
        # Wait for material city dropdown
        expect(page.locator('select[name="material_city"] option:nth-child(2)')).to_be_visible()
        page.select_option('select[name="material_city"]', '1')
        
        page.fill('input[name="material_pin"]', '400701')
        page.fill('input[name="material_contact_no"]', '+91-22-26854323')
        page.fill('input[name="materialdelfaxno"]', '+91-22-26854323')
        
        # Fill tax information
        page.fill('input[name="juridictioncode"]', 'MUMBAI-I')
        page.fill('input[name="eccno"]', '**********MN001')
        page.fill('input[name="range"]', 'CENTRAL-1')
        page.fill('input[name="commissionurate"]', 'MUMBAI-I')
        page.fill('input[name="divn"]', 'DIV-1')
        page.fill('input[name="panno"]', '**********')
        page.fill('input[name="tinvatno"]', '27**********1Z5')
        page.fill('input[name="tincstno"]', '27**********1Z6')
        page.fill('input[name="tdscode"]', 'TDS001')
        
        # Fill remarks
        page.fill('textarea[name="remarks"]', 'Customer created via Playwright test')
        
        # Submit form with confirmation
        page.on('dialog', lambda dialog: dialog.accept())
        page.click('button[type="submit"]')
        
        # Should redirect to customer list with success message
        expect(page).to_have_url(f"{live_server.url}/sales-distribution/customers/")
        expect(page.locator('.alert-success, .success, [class*="success"]')).to_be_visible()
        
        # Verify customer appears in list
        expect(page.locator('tbody')).to_contain_text('PLAYWRIGHT TEST CUSTOMER PVT LTD')
    
    def test_customer_form_validation(self, page: Page, live_server):
        """Test form validation for required fields"""
        
        # Login
        page.goto(f"{live_server.url}/login/")
        page.fill('input[name="username"]', 'admin')
        page.fill('input[name="password"]', 'admin')
        page.click('button[type="submit"]')
        
        # Navigate to customer new page
        page.goto(f"{live_server.url}/sales-distribution/customers/new/")
        
        # Try to submit empty form
        page.on('dialog', lambda dialog: dialog.accept())
        page.click('button[type="submit"]')
        
        # Should show validation errors
        expect(page.locator('.text-sap-red-600, .error, [class*="error"]')).to_be_visible()
        
        # Should still be on the same page
        expect(page).to_have_url(f"{live_server.url}/sales-distribution/customers/new/")
    
    def test_customer_cascading_dropdowns(self, page: Page, live_server):
        """Test cascading dropdown functionality with HTMX"""
        
        # Login
        page.goto(f"{live_server.url}/login/")
        page.fill('input[name="username"]', 'admin')
        page.fill('input[name="password"]', 'admin')
        page.click('button[type="submit"]')
        
        # Navigate to customer new page
        page.goto(f"{live_server.url}/sales-distribution/customers/new/")
        
        # Initially state and city dropdowns should be empty/disabled
        expect(page.locator('select[name="registered_state"] option')).to_have_count(1)  # Only "Select State"
        expect(page.locator('select[name="registered_city"] option')).to_have_count(1)   # Only "Select City"
        
        # Select country
        page.select_option('select[name="registered_country"]', '1')  # India
        
        # State dropdown should populate via HTMX
        expect(page.locator('select[name="registered_state"] option')).to_have_count_greater_than(1)
        
        # Select state
        page.select_option('select[name="registered_state"]', '1')  # Maharashtra
        
        # City dropdown should populate via HTMX
        expect(page.locator('select[name="registered_city"] option')).to_have_count_greater_than(1)
        
        # Verify the same works for all three address sections
        for address_type in ['works', 'material']:
            page.select_option(f'select[name="{address_type}_country"]', '1')
            expect(page.locator(f'select[name="{address_type}_state"] option')).to_have_count_greater_than(1)
            
            page.select_option(f'select[name="{address_type}_state"]', '1')
            expect(page.locator(f'select[name="{address_type}_city"] option')).to_have_count_greater_than(1)
    
    def test_customer_list_search(self, page: Page, live_server):
        """Test customer list search functionality"""
        
        # Create test customer first
        Customer.objects.create(
            customer_name='Searchable Test Customer',
            customerid='STC001',
            sysdate='2023-12-01',
            systime='10:00:00',
            sessionid='admin',
            compid_id=1,
            finyearid_id=1
        )
        
        # Login
        page.goto(f"{live_server.url}/login/")
        page.fill('input[name="username"]', 'admin')
        page.fill('input[name="password"]', 'admin')
        page.click('button[type="submit"]')
        
        # Navigate to customer list
        page.goto(f"{live_server.url}/sales-distribution/customers/")
        
        # Verify customer appears in list
        expect(page.locator('tbody')).to_contain_text('Searchable Test Customer')
        
        # Test search functionality
        search_box = page.locator('input[name="search"], input[placeholder*="search"]')
        if search_box.is_visible():
            search_box.fill('Searchable')
            
            # Should filter results
            expect(page.locator('tbody')).to_contain_text('Searchable Test Customer')
            
            # Clear search
            search_box.fill('')
            
            # All customers should be visible again
            expect(page.locator('tbody tr')).to_have_count_greater_than_or_equal(1)
    
    def test_customer_edit_workflow(self, page: Page, live_server):
        """Test customer edit functionality"""
        
        # Create test customer
        customer = Customer.objects.create(
            customer_name='Editable Test Customer',
            customerid='ETC001',
            sysdate='2023-12-01',
            systime='10:00:00',
            sessionid='admin',
            compid_id=1,
            finyearid_id=1,
            email='<EMAIL>'
        )
        
        # Login
        page.goto(f"{live_server.url}/login/")
        page.fill('input[name="username"]', 'admin')
        page.fill('input[name="password"]', 'admin')
        page.click('button[type="submit"]')
        
        # Navigate to customer edit page
        page.goto(f"{live_server.url}/sales-distribution/customers/{customer.pk}/edit/")
        
        # Should show edit form with existing data
        expect(page.locator('input[name="customer_name"]')).to_have_value('Editable Test Customer')
        expect(page.locator('input[name="email"]')).to_have_value('<EMAIL>')
        
        # Update email
        page.fill('input[name="email"]', '<EMAIL>')
        
        # Submit form
        page.on('dialog', lambda dialog: dialog.accept())
        page.click('button[type="submit"]')
        
        # Should redirect with success
        expect(page).to_have_url(f"{live_server.url}/sales-distribution/customers/")
        
        # Verify update in database
        customer.refresh_from_db()
        assert customer.email == '<EMAIL>'
    
    def test_customer_detail_view(self, page: Page, live_server):
        """Test customer detail view"""
        
        # Create test customer with full details
        customer = Customer.objects.create(
            customer_name='Detailed Test Customer',
            customerid='DTC001',
            sysdate='2023-12-01',
            systime='10:00:00',
            sessionid='admin',
            compid_id=1,
            finyearid_id=1,
            email='<EMAIL>',
            contact_person='Jane Doe',
            registered_address='123 Test Street',
            registered_country_id=1,
            registered_state_id=1,
            registered_city_id=1
        )
        
        # Login
        page.goto(f"{live_server.url}/login/")
        page.fill('input[name="username"]', 'admin')
        page.fill('input[name="password"]', 'admin')
        page.click('button[type="submit"]')
        
        # Navigate to customer detail page
        page.goto(f"{live_server.url}/sales-distribution/customers/{customer.pk}/")
        
        # Should show customer details
        expect(page.locator('body')).to_contain_text('Detailed Test Customer')
        expect(page.locator('body')).to_contain_text('DTC001')
        expect(page.locator('body')).to_contain_text('<EMAIL>')
        expect(page.locator('body')).to_contain_text('Jane Doe')


if __name__ == '__main__':
    pytest.main([__file__])