{% extends 'material_planning/base.html' %}

{% block title %}Process Categories{% endblock %}

{% block content %}
<div class="space-y-6 animate-fade-in">
    <!-- Header Section with SAP Card Design -->
    <div class="sap-card">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-sap-gray-900 mb-2">Process Categories</h1>
                <p class="text-sap-gray-600">Manage manufacturing process categories and workflows</p>
                <div class="flex items-center mt-3 space-x-4">
                    <span class="sap-status-info">{{ total_processes }} Total Processes</span>
                    <span class="text-sm text-sap-gray-500">Last updated: {{ "now"|date:"M d, Y H:i" }}</span>
                </div>
            </div>
            <div class="flex space-x-3">
                <button disabled class="sap-button-secondary cursor-not-allowed opacity-50">
                    <i class="fas fa-plus mr-2"></i>
                    Create New Category
                </button>
                <button class="sap-button-primary">
                    <i class="fas fa-download mr-2"></i>
                    Export
                </button>
            </div>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="sap-card">
        <div class="flex flex-col md:flex-row gap-4">
            <div class="flex-1">
                <label for="search" class="block text-sm font-medium text-sap-gray-700 mb-1">Search Processes</label>
                <div class="relative">
                    <input type="text" 
                           id="search" 
                           name="search" 
                           value="{{ search_query }}"
                           placeholder="Search by name or code..."
                           class="sap-input pl-10">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-sap-gray-400"></i>
                    </div>
                </div>
            </div>
            <div class="md:w-48">
                <label for="status-filter" class="block text-sm font-medium text-sap-gray-700 mb-1">Status</label>
                <select id="status-filter" class="sap-input">
                    <option>All Status</option>
                    <option>Active</option>
                    <option>Inactive</option>
                </select>
            </div>
            <div class="flex items-end">
                <button type="button" class="sap-button-primary">
                    <i class="fas fa-filter mr-2"></i>
                    Apply Filters
                </button>
            </div>
        </div>
    </div>

    <!-- Process Categories Table -->
    <div class="sap-card p-0 overflow-hidden">
        <div class="px-6 py-4 border-b border-sap-gray-200 bg-sap-gray-50">
            <h3 class="text-lg font-semibold text-sap-gray-900">Process Categories Overview</h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full">
                <thead class="bg-sap-gray-50 border-b border-sap-gray-200">
                    <tr>
                        <th class="px-6 py-4 text-left text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">
                            <div class="flex items-center space-x-1">
                                <span>Process Code</span>
                                <i class="fas fa-sort text-sap-gray-400 text-xs"></i>
                            </div>
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">
                            <div class="flex items-center space-x-1">
                                <span>Process Name</span>
                                <i class="fas fa-sort text-sap-gray-400 text-xs"></i>
                            </div>
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">Category Type</th>
                        <th class="px-6 py-4 text-left text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-4 text-left text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">Last Modified</th>
                        <th class="px-6 py-4 text-right text-xs font-semibold text-sap-gray-700 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-100">
                    {% for category in categories %}
                        <tr class="hover:bg-sap-gray-50 transition-colors duration-150">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 flex-shrink-0 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-cogs text-sap-blue-600"></i>
                                    </div>
                                    <div class="ml-3">
                                        <div class="text-sm font-semibold text-sap-gray-900">
                                            {{ category.symbol|default:category.id }}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-sap-gray-900">
                                    {{ category.processname|default:'Unnamed Process' }}
                                </div>
                                <div class="text-sm text-sap-gray-500">Process ID: {{ category.id }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-sap-blue-400 rounded-full mr-2"></div>
                                    <span class="text-sm text-sap-gray-900">Manufacturing Process</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="sap-status-success">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    Active
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-500">
                                {{ "now"|date:"M d, Y" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <button class="text-sap-blue-600 hover:text-sap-blue-700 transition-colors duration-150" 
                                            title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-sap-gray-600 hover:text-sap-gray-700 transition-colors duration-150 opacity-50 cursor-not-allowed" 
                                            title="Edit (Coming Soon)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-red-600 hover:text-red-700 transition-colors duration-150 opacity-50 cursor-not-allowed" 
                                            title="Delete (Coming Soon)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    {% empty %}
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center">
                                    <div class="w-16 h-16 bg-sap-gray-100 rounded-full flex items-center justify-center mb-4">
                                        <i class="fas fa-inbox text-sap-gray-400 text-2xl"></i>
                                    </div>
                                    <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No Process Categories Found</h3>
                                    <p class="text-sap-gray-500 mb-4">Get started by creating your first process category.</p>
                                    <button disabled class="sap-button-primary opacity-50 cursor-not-allowed">
                                        <i class="fas fa-plus mr-2"></i>
                                        Create First Category
                                    </button>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
        <div class="px-6 py-4 border-t border-sap-gray-200 bg-sap-gray-50">
            <div class="flex items-center justify-between">
                <div class="text-sm text-sap-gray-700">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} results
                </div>
                <div class="flex space-x-2">
                    {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}" class="sap-button-secondary py-2 px-3">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    {% endif %}
                    
                    <span class="flex items-center px-3 py-2 text-sm text-sap-gray-700">
                        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                    </span>
                    
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}" class="sap-button-secondary py-2 px-3">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- HTMX Script for Real-time Search -->
<script>
document.getElementById('search').addEventListener('input', function(e) {
    // Real-time search implementation would go here
    // For now, just visual feedback
    if (e.target.value.length > 0) {
        e.target.classList.add('border-sap-blue-500', 'ring-2', 'ring-sap-blue-200');
    } else {
        e.target.classList.remove('border-sap-blue-500', 'ring-2', 'ring-sap-blue-200');
    }
});
</script>
{% endblock %}