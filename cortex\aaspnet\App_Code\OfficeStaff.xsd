﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="OfficeStaff" targetNamespace="http://tempuri.org/OfficeStaff.xsd" xmlns:mstns="http://tempuri.org/OfficeStaff.xsd" xmlns="http://tempuri.org/OfficeStaff.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="OfficeStaff" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="OfficeStaff" msprop:Generator_DataSetName="OfficeStaff">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="CompanyName" msprop:Generator_UserColumnName="CompanyName" msprop:Generator_ColumnVarNameInTable="columnCompanyName" msprop:Generator_ColumnPropNameInRow="CompanyName" msprop:Generator_ColumnPropNameInTable="CompanyNameColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PhotoData" msprop:Generator_UserColumnName="PhotoData" msprop:Generator_ColumnVarNameInTable="columnPhotoData" msprop:Generator_ColumnPropNameInRow="PhotoData" msprop:Generator_ColumnPropNameInTable="PhotoDataColumn" type="xs:base64Binary" minOccurs="0" />
              <xs:element name="Address" msprop:Generator_UserColumnName="Address" msprop:Generator_ColumnVarNameInTable="columnAddress" msprop:Generator_ColumnPropNameInRow="Address" msprop:Generator_ColumnPropNameInTable="AddressColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CardNo" msprop:Generator_UserColumnName="CardNo" msprop:Generator_ColumnVarNameInTable="columnCardNo" msprop:Generator_ColumnPropNameInRow="CardNo" msprop:Generator_ColumnPropNameInTable="CardNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="EmpName" msprop:Generator_UserColumnName="EmpName" msprop:Generator_ColumnVarNameInTable="columnEmpName" msprop:Generator_ColumnPropNameInRow="EmpName" msprop:Generator_ColumnPropNameInTable="EmpNameColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Department" msprop:Generator_UserColumnName="Department" msprop:Generator_ColumnVarNameInTable="columnDepartment" msprop:Generator_ColumnPropNameInRow="Department" msprop:Generator_ColumnPropNameInTable="DepartmentColumn" type="xs:string" minOccurs="0" />
              <xs:element name="BussinessGroup" msprop:Generator_UserColumnName="BussinessGroup" msprop:Generator_ColumnVarNameInTable="columnBussinessGroup" msprop:Generator_ColumnPropNameInRow="BussinessGroup" msprop:Generator_ColumnPropNameInTable="BussinessGroupColumn" type="xs:string" minOccurs="0" />
              <xs:element name="DeptDirector" msprop:Generator_UserColumnName="DeptDirector" msprop:Generator_ColumnVarNameInTable="columnDeptDirector" msprop:Generator_ColumnPropNameInRow="DeptDirector" msprop:Generator_ColumnPropNameInTable="DeptDirectorColumn" type="xs:string" minOccurs="0" />
              <xs:element name="DeptHead" msprop:Generator_UserColumnName="DeptHead" msprop:Generator_ColumnVarNameInTable="columnDeptHead" msprop:Generator_ColumnPropNameInRow="DeptHead" msprop:Generator_ColumnPropNameInTable="DeptHeadColumn" type="xs:string" minOccurs="0" />
              <xs:element name="GroupLeader" msprop:Generator_UserColumnName="GroupLeader" msprop:Generator_ColumnVarNameInTable="columnGroupLeader" msprop:Generator_ColumnPropNameInRow="GroupLeader" msprop:Generator_ColumnPropNameInTable="GroupLeaderColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Designation" msprop:Generator_UserColumnName="Designation" msprop:Generator_ColumnVarNameInTable="columnDesignation" msprop:Generator_ColumnPropNameInRow="Designation" msprop:Generator_ColumnPropNameInTable="DesignationColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Grade" msprop:Generator_UserColumnName="Grade" msprop:Generator_ColumnVarNameInTable="columnGrade" msprop:Generator_ColumnPropNameInRow="Grade" msprop:Generator_ColumnPropNameInTable="GradeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="MobileNo" msprop:Generator_UserColumnName="MobileNo" msprop:Generator_ColumnVarNameInTable="columnMobileNo" msprop:Generator_ColumnPropNameInRow="MobileNo" msprop:Generator_ColumnPropNameInTable="MobileNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="extNo" msprop:Generator_UserColumnName="extNo" msprop:Generator_ColumnVarNameInTable="columnextNo" msprop:Generator_ColumnPropNameInRow="extNo" msprop:Generator_ColumnPropNameInTable="extNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="joindate" msprop:Generator_UserColumnName="joindate" msprop:Generator_ColumnVarNameInTable="columnjoindate" msprop:Generator_ColumnPropNameInRow="joindate" msprop:Generator_ColumnPropNameInTable="joindateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="birthdate" msprop:Generator_UserColumnName="birthdate" msprop:Generator_ColumnVarNameInTable="columnbirthdate" msprop:Generator_ColumnPropNameInRow="birthdate" msprop:Generator_ColumnPropNameInTable="birthdateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="resigndate" msprop:Generator_UserColumnName="resigndate" msprop:Generator_ColumnVarNameInTable="columnresigndate" msprop:Generator_ColumnPropNameInRow="resigndate" msprop:Generator_ColumnPropNameInTable="resigndateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="martialstatus" msprop:Generator_UserColumnName="martialstatus" msprop:Generator_ColumnVarNameInTable="columnmartialstatus" msprop:Generator_ColumnPropNameInRow="martialstatus" msprop:Generator_ColumnPropNameInTable="martialstatusColumn" type="xs:string" minOccurs="0" />
              <xs:element name="physicalstatus" msprop:Generator_UserColumnName="physicalstatus" msprop:Generator_ColumnVarNameInTable="columnphysicalstatus" msprop:Generator_ColumnPropNameInRow="physicalstatus" msprop:Generator_ColumnPropNameInTable="physicalstatusColumn" type="xs:string" minOccurs="0" />
              <xs:element name="padd" msprop:Generator_UserColumnName="padd" msprop:Generator_ColumnVarNameInTable="columnpadd" msprop:Generator_ColumnPropNameInRow="padd" msprop:Generator_ColumnPropNameInTable="paddColumn" type="xs:string" minOccurs="0" />
              <xs:element name="cadd" msprop:Generator_UserColumnName="cadd" msprop:Generator_ColumnVarNameInTable="columncadd" msprop:Generator_ColumnPropNameInRow="cadd" msprop:Generator_ColumnPropNameInTable="caddColumn" type="xs:string" minOccurs="0" />
              <xs:element name="email" msprop:Generator_UserColumnName="email" msprop:Generator_ColumnVarNameInTable="columnemail" msprop:Generator_ColumnPropNameInRow="email" msprop:Generator_ColumnPropNameInTable="emailColumn" type="xs:string" minOccurs="0" />
              <xs:element name="gender" msprop:Generator_UserColumnName="gender" msprop:Generator_ColumnVarNameInTable="columngender" msprop:Generator_ColumnPropNameInRow="gender" msprop:Generator_ColumnPropNameInTable="genderColumn" type="xs:string" minOccurs="0" />
              <xs:element name="bgp" msprop:Generator_UserColumnName="bgp" msprop:Generator_ColumnVarNameInTable="columnbgp" msprop:Generator_ColumnPropNameInRow="bgp" msprop:Generator_ColumnPropNameInTable="bgpColumn" type="xs:string" minOccurs="0" />
              <xs:element name="height" msprop:Generator_UserColumnName="height" msprop:Generator_ColumnVarNameInTable="columnheight" msprop:Generator_ColumnPropNameInRow="height" msprop:Generator_ColumnPropNameInTable="heightColumn" type="xs:string" minOccurs="0" />
              <xs:element name="weight" msprop:Generator_UserColumnName="weight" msprop:Generator_ColumnVarNameInTable="columnweight" msprop:Generator_ColumnPropNameInRow="weight" msprop:Generator_ColumnPropNameInTable="weightColumn" type="xs:string" minOccurs="0" />
              <xs:element name="religion" msprop:Generator_UserColumnName="religion" msprop:Generator_ColumnVarNameInTable="columnreligion" msprop:Generator_ColumnPropNameInRow="religion" msprop:Generator_ColumnPropNameInTable="religionColumn" type="xs:string" minOccurs="0" />
              <xs:element name="cast" msprop:Generator_UserColumnName="cast" msprop:Generator_ColumnVarNameInTable="columncast" msprop:Generator_ColumnPropNameInRow="cast" msprop:Generator_ColumnPropNameInTable="castColumn" type="xs:string" minOccurs="0" />
              <xs:element name="edu" msprop:Generator_UserColumnName="edu" msprop:Generator_ColumnVarNameInTable="columnedu" msprop:Generator_ColumnPropNameInRow="edu" msprop:Generator_ColumnPropNameInTable="eduColumn" type="xs:string" minOccurs="0" />
              <xs:element name="adq" msprop:Generator_UserColumnName="adq" msprop:Generator_ColumnVarNameInTable="columnadq" msprop:Generator_ColumnPropNameInRow="adq" msprop:Generator_ColumnPropNameInTable="adqColumn" type="xs:string" minOccurs="0" />
              <xs:element name="lc" msprop:Generator_UserColumnName="lc" msprop:Generator_ColumnVarNameInTable="columnlc" msprop:Generator_ColumnPropNameInRow="lc" msprop:Generator_ColumnPropNameInTable="lcColumn" type="xs:string" minOccurs="0" />
              <xs:element name="wd" msprop:Generator_UserColumnName="wd" msprop:Generator_ColumnVarNameInTable="columnwd" msprop:Generator_ColumnPropNameInRow="wd" msprop:Generator_ColumnPropNameInTable="wdColumn" type="xs:string" minOccurs="0" />
              <xs:element name="te" msprop:Generator_UserColumnName="te" msprop:Generator_ColumnVarNameInTable="columnte" msprop:Generator_ColumnPropNameInRow="te" msprop:Generator_ColumnPropNameInTable="teColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ctc" msprop:Generator_UserColumnName="ctc" msprop:Generator_ColumnVarNameInTable="columnctc" msprop:Generator_ColumnPropNameInRow="ctc" msprop:Generator_ColumnPropNameInTable="ctcColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ba" msprop:Generator_UserColumnName="ba" msprop:Generator_ColumnVarNameInTable="columnba" msprop:Generator_ColumnPropNameInRow="ba" msprop:Generator_ColumnPropNameInTable="baColumn" type="xs:string" minOccurs="0" />
              <xs:element name="pf" msprop:Generator_UserColumnName="pf" msprop:Generator_ColumnVarNameInTable="columnpf" msprop:Generator_ColumnPropNameInRow="pf" msprop:Generator_ColumnPropNameInTable="pfColumn" type="xs:string" minOccurs="0" />
              <xs:element name="pa" msprop:Generator_UserColumnName="pa" msprop:Generator_ColumnVarNameInTable="columnpa" msprop:Generator_ColumnPropNameInRow="pa" msprop:Generator_ColumnPropNameInTable="paColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ps" msprop:Generator_UserColumnName="ps" msprop:Generator_ColumnVarNameInTable="columnps" msprop:Generator_ColumnPropNameInRow="ps" msprop:Generator_ColumnPropNameInTable="psColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ex" msprop:Generator_UserColumnName="ex" msprop:Generator_ColumnVarNameInTable="columnex" msprop:Generator_ColumnPropNameInRow="ex" msprop:Generator_ColumnPropNameInTable="exColumn" type="xs:string" minOccurs="0" />
              <xs:element name="inf" msprop:Generator_UserColumnName="inf" msprop:Generator_ColumnVarNameInTable="columninf" msprop:Generator_ColumnPropNameInRow="inf" msprop:Generator_ColumnPropNameInTable="infColumn" type="xs:string" minOccurs="0" />
              <xs:element name="LogoImage" msprop:Generator_UserColumnName="LogoImage" msprop:Generator_ColumnVarNameInTable="columnLogoImage" msprop:Generator_ColumnPropNameInRow="LogoImage" msprop:Generator_ColumnPropNameInTable="LogoImageColumn" type="xs:base64Binary" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>