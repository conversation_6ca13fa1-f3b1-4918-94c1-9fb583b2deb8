<!-- accounts/templates/accounts/invoices/sales_invoice_form.html -->
<!-- Sales Invoice Create/Edit Form Template -->
<!-- Task Group 5: Invoicing & Billing - Sales Invoice Form (Task 5.8) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{% if object %}Edit Sales Invoice{% else %}New Sales Invoice{% endif %} - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-emerald-600 to-sap-emerald-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="receipt" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">
                        {% if object %}Edit Sales Invoice{% else %}New Sales Invoice{% endif %}
                    </h1>
                    <p class="text-sm text-sap-gray-600 mt-1">
                        {% if object %}Modify invoice details and items{% else %}Create a new sales invoice for customer billing{% endif %}
                    </p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:sales_invoice_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Sales Invoice Form -->
    <div class="max-w-7xl mx-auto">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="edit" class="w-5 h-5 mr-2 text-sap-emerald-600"></i>
                    Sales Invoice Information
                </h3>
                <p class="text-sm text-sap-gray-600 mt-1">Complete all required fields to create the sales invoice</p>
            </div>
            
            <form method="post" id="sales-invoice-form" class="p-6" x-data="salesInvoiceForm()">
                {% csrf_token %}
                
                <!-- Invoice Header Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="info" class="w-5 h-5 mr-2 text-sap-emerald-600"></i>
                        Invoice Header
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <!-- Invoice Number -->
                        <div>
                            <label for="{{ form.invoice_number.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Invoice Number
                            </label>
                            {{ form.invoice_number|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.invoice_number.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.invoice_number.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Auto-generated if left blank</p>
                        </div>
                        
                        <!-- Invoice Date -->
                        <div>
                            <label for="{{ form.invoice_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Invoice Date *
                            </label>
                            {{ form.invoice_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.invoice_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.invoice_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Due Date -->
                        <div>
                            <label for="{{ form.due_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Due Date
                            </label>
                            {{ form.due_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.due_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.due_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Invoice Type -->
                        <div>
                            <label for="{{ form.invoice_type.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Invoice Type *
                            </label>
                            {{ form.invoice_type|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.invoice_type.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.invoice_type.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Customer Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="user" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Customer Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Customer Name -->
                        <div>
                            <label for="{{ form.customer_name.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Customer Name *
                            </label>
                            {{ form.customer_name|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.customer_name.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.customer_name.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Customer Code -->
                        <div>
                            <label for="{{ form.customer_code.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Customer Code
                            </label>
                            {{ form.customer_code|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.customer_code.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.customer_code.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Customer Address -->
                        <div class="md:col-span-2">
                            <label for="{{ form.customer_address.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Customer Address
                            </label>
                            {{ form.customer_address|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.customer_address.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.customer_address.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Customer GST/VAT Number -->
                        <div>
                            <label for="{{ form.customer_gst_number.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                GST/VAT Number
                            </label>
                            {{ form.customer_gst_number|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.customer_gst_number.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.customer_gst_number.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Customer PAN -->
                        <div>
                            <label for="{{ form.customer_pan.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                PAN Number
                            </label>
                            {{ form.customer_pan|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.customer_pan.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.customer_pan.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Invoice Items Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="package" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                        Invoice Items
                    </h4>
                    
                    <!-- Items Table -->
                    <div class="border border-sap-gray-300 rounded-lg overflow-hidden">
                        <table class="min-w-full divide-y divide-sap-gray-200" id="items-table">
                            <thead class="bg-sap-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Item/Service</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Quantity</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Unit Price</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Discount</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Tax Rate</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Amount</th>
                                    <th class="px-4 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Action</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-sap-gray-200" x-ref="itemsTableBody">
                                <template x-for="(item, index) in items" :key="index">
                                    <tr>
                                        <td class="px-4 py-3">
                                            <input type="text" x-model="item.description" 
                                                   class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-emerald-500" 
                                                   placeholder="Item description">
                                        </td>
                                        <td class="px-4 py-3">
                                            <input type="number" x-model="item.quantity" @input="calculateItemAmount(index)" 
                                                   class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-emerald-500" 
                                                   placeholder="Qty" min="0" step="0.01">
                                        </td>
                                        <td class="px-4 py-3">
                                            <input type="number" x-model="item.unit_price" @input="calculateItemAmount(index)" 
                                                   class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-emerald-500" 
                                                   placeholder="Price" min="0" step="0.01">
                                        </td>
                                        <td class="px-4 py-3">
                                            <input type="number" x-model="item.discount_percent" @input="calculateItemAmount(index)" 
                                                   class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-emerald-500" 
                                                   placeholder="%" min="0" max="100" step="0.01">
                                        </td>
                                        <td class="px-4 py-3">
                                            <select x-model="item.tax_rate" @change="calculateItemAmount(index)" 
                                                    class="block w-full px-2 py-1 border border-sap-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-sap-emerald-500">
                                                <option value="0">0%</option>
                                                <option value="5">5%</option>
                                                <option value="12">12%</option>
                                                <option value="18">18%</option>
                                                <option value="28">28%</option>
                                            </select>
                                        </td>
                                        <td class="px-4 py-3">
                                            <span class="text-sm font-medium text-sap-gray-900" x-text="'₹' + (item.total_amount || 0).toFixed(2)"></span>
                                        </td>
                                        <td class="px-4 py-3 text-center">
                                            <button type="button" @click="removeItem(index)" 
                                                    class="text-sap-red-600 hover:text-sap-red-900">
                                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                        
                        <!-- Add Item Button -->
                        <div class="px-4 py-3 bg-sap-gray-50 border-t border-sap-gray-200">
                            <button type="button" @click="addItem()" 
                                    class="bg-sap-emerald-600 hover:bg-sap-emerald-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                                Add Item
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Payment Terms Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="credit-card" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                        Payment Terms & Notes
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Payment Terms -->
                        <div>
                            <label for="{{ form.payment_terms.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Payment Terms
                            </label>
                            {{ form.payment_terms|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.payment_terms.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.payment_terms.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Delivery Terms -->
                        <div>
                            <label for="{{ form.delivery_terms.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Delivery Terms
                            </label>
                            {{ form.delivery_terms|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.delivery_terms.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.delivery_terms.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Special Instructions -->
                        <div class="md:col-span-2">
                            <label for="{{ form.special_instructions.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Special Instructions
                            </label>
                            {{ form.special_instructions|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500" }}
                            {% if form.special_instructions.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.special_instructions.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Invoice Totals Display -->
                <div class="mb-8 bg-sap-emerald-50 border border-sap-emerald-200 rounded-lg p-6">
                    <h4 class="text-lg font-medium text-sap-emerald-800 mb-4 flex items-center">
                        <i data-lucide="calculator" class="w-5 h-5 mr-2 text-sap-emerald-600"></i>
                        Invoice Summary
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="text-center">
                            <p class="text-sm text-sap-emerald-600">Subtotal</p>
                            <p class="text-xl font-bold text-sap-emerald-800" x-text="'₹' + subtotal.toFixed(2)"></p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-emerald-600">Discount</p>
                            <p class="text-xl font-bold text-sap-emerald-800" x-text="'₹' + totalDiscount.toFixed(2)"></p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-emerald-600">Tax Amount</p>
                            <p class="text-xl font-bold text-sap-emerald-800" x-text="'₹' + totalTax.toFixed(2)"></p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-emerald-600">Total Amount</p>
                            <p class="text-2xl font-bold text-sap-emerald-800" x-text="'₹' + grandTotal.toFixed(2)"></p>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="resetForm()" 
                                class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="rotate-ccw" class="w-4 h-4 inline mr-2"></i>
                            Reset
                        </button>
                        <button type="button" @click="previewInvoice()" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="eye" class="w-4 h-4 inline mr-2"></i>
                            Preview
                        </button>
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="{% url 'accounts:sales_invoice_list' %}" 
                           class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-sap-emerald-600 hover:bg-sap-emerald-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                            {% if object %}Update Invoice{% else %}Save Invoice{% endif %}
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Invoice Guidelines -->
        <div class="mt-6 bg-sap-blue-50 border border-sap-blue-200 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i data-lucide="info" class="w-5 h-5 text-sap-blue-600"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-sap-blue-800">Sales Invoice Guidelines</h3>
                    <div class="mt-2 text-sm text-sap-blue-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Invoice number will be auto-generated if not provided</li>
                            <li>Ensure customer GST/VAT number is accurate for tax compliance</li>
                            <li>Add multiple items using the "Add Item" button</li>
                            <li>Tax amounts are calculated automatically based on rates</li>
                            <li>Payment and delivery terms help with customer clarity</li>
                            <li>All invoices are automatically included in VAT registers</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function salesInvoiceForm() {
    return {
        items: [
            {
                description: '',
                quantity: 0,
                unit_price: 0,
                discount_percent: 0,
                tax_rate: 18,
                total_amount: 0
            }
        ],
        subtotal: 0,
        totalDiscount: 0,
        totalTax: 0,
        grandTotal: 0,
        
        addItem() {
            this.items.push({
                description: '',
                quantity: 0,
                unit_price: 0,
                discount_percent: 0,
                tax_rate: 18,
                total_amount: 0
            });
        },
        
        removeItem(index) {
            if (this.items.length > 1) {
                this.items.splice(index, 1);
                this.calculateTotals();
            }
        },
        
        calculateItemAmount(index) {
            const item = this.items[index];
            const quantity = parseFloat(item.quantity) || 0;
            const unitPrice = parseFloat(item.unit_price) || 0;
            const discountPercent = parseFloat(item.discount_percent) || 0;
            const taxRate = parseFloat(item.tax_rate) || 0;
            
            const lineTotal = quantity * unitPrice;
            const discountAmount = lineTotal * (discountPercent / 100);
            const taxableAmount = lineTotal - discountAmount;
            const taxAmount = taxableAmount * (taxRate / 100);
            
            item.total_amount = taxableAmount + taxAmount;
            
            this.calculateTotals();
        },
        
        calculateTotals() {
            this.subtotal = 0;
            this.totalDiscount = 0;
            this.totalTax = 0;
            
            this.items.forEach(item => {
                const quantity = parseFloat(item.quantity) || 0;
                const unitPrice = parseFloat(item.unit_price) || 0;
                const discountPercent = parseFloat(item.discount_percent) || 0;
                const taxRate = parseFloat(item.tax_rate) || 0;
                
                const lineTotal = quantity * unitPrice;
                const discountAmount = lineTotal * (discountPercent / 100);
                const taxableAmount = lineTotal - discountAmount;
                const taxAmount = taxableAmount * (taxRate / 100);
                
                this.subtotal += lineTotal;
                this.totalDiscount += discountAmount;
                this.totalTax += taxAmount;
            });
            
            this.grandTotal = this.subtotal - this.totalDiscount + this.totalTax;
        },
        
        previewInvoice() {
            if (this.items.length === 0 || !this.items[0].description) {
                alert('Please add at least one item to preview the invoice.');
                return;
            }
            
            alert('Invoice preview functionality would be implemented here.');
        }
    }
}

function resetForm() {
    if (confirm('Are you sure you want to reset the form? All unsaved changes will be lost.')) {
        location.reload();
    }
}

// Auto-generate invoice number if creating new invoice
document.addEventListener('DOMContentLoaded', function() {
    const invoiceNumberInput = document.getElementById('{{ form.invoice_number.id_for_label }}');
    if (invoiceNumberInput && !invoiceNumberInput.value) {
        // Generate invoice number based on current timestamp
        const today = new Date();
        const year = today.getFullYear().toString();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        const sequence = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        
        invoiceNumberInput.value = `SI-${year}${month}${day}${sequence}`;
    }
    
    // Auto-set due date to 30 days from invoice date
    const invoiceDateField = document.getElementById('{{ form.invoice_date.id_for_label }}');
    const dueDateField = document.getElementById('{{ form.due_date.id_for_label }}');
    
    if (invoiceDateField && dueDateField) {
        invoiceDateField.addEventListener('change', function() {
            if (!dueDateField.value && this.value) {
                const invoiceDate = new Date(this.value);
                const dueDate = new Date(invoiceDate);
                dueDate.setDate(dueDate.getDate() + 30);
                
                dueDateField.value = dueDate.toISOString().split('T')[0];
            }
        });
    }
    
    lucide.createIcons();
});
</script>
{% endblock %}