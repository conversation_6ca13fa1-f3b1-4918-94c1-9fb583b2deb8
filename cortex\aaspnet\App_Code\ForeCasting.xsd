﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="ForeCasting" targetNamespace="http://tempuri.org/ForeCasting.xsd" xmlns:mstns="http://tempuri.org/ForeCasting.xsd" xmlns="http://tempuri.org/ForeCasting.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="ForeCasting" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="ForeCasting" msprop:Generator_DataSetName="ForeCasting">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ItemCode" msprop:Generator_UserColumnName="ItemCode" msprop:Generator_ColumnVarNameInTable="columnItemCode" msprop:Generator_ColumnPropNameInRow="ItemCode" msprop:Generator_ColumnPropNameInTable="ItemCodeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Description" msprop:Generator_UserColumnName="Description" msprop:Generator_ColumnVarNameInTable="columnDescription" msprop:Generator_ColumnPropNameInRow="Description" msprop:Generator_ColumnPropNameInTable="DescriptionColumn" type="xs:string" minOccurs="0" />
              <xs:element name="UOM" msprop:Generator_UserColumnName="UOM" msprop:Generator_ColumnVarNameInTable="columnUOM" msprop:Generator_ColumnPropNameInRow="UOM" msprop:Generator_ColumnPropNameInTable="UOMColumn" type="xs:string" minOccurs="0" />
              <xs:element name="BOMQty" msprop:Generator_UserColumnName="BOMQty" msprop:Generator_ColumnVarNameInTable="columnBOMQty" msprop:Generator_ColumnPropNameInRow="BOMQty" msprop:Generator_ColumnPropNameInTable="BOMQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="Id" msprop:Generator_UserColumnName="Id" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnPropNameInTable="IdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="Sn" msprop:Generator_UserColumnName="Sn" msprop:Generator_ColumnVarNameInTable="columnSn" msprop:Generator_ColumnPropNameInRow="Sn" msprop:Generator_ColumnPropNameInTable="SnColumn" type="xs:int" minOccurs="0" />
              <xs:element name="ShortQty" msprop:Generator_UserColumnName="ShortQty" msprop:Generator_ColumnVarNameInTable="columnShortQty" msprop:Generator_ColumnPropNameInRow="ShortQty" msprop:Generator_ColumnPropNameInTable="ShortQtyColumn" type="xs:double" minOccurs="0" />
              <xs:element name="WONo" msprop:Generator_UserColumnName="WONo" msprop:Generator_ColumnVarNameInTable="columnWONo" msprop:Generator_ColumnPropNameInRow="WONo" msprop:Generator_ColumnPropNameInTable="WONoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="BOMDate" msprop:Generator_UserColumnName="BOMDate" msprop:Generator_ColumnVarNameInTable="columnBOMDate" msprop:Generator_ColumnPropNameInRow="BOMDate" msprop:Generator_ColumnPropNameInTable="BOMDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PRNo" msprop:Generator_UserColumnName="PRNo" msprop:Generator_ColumnVarNameInTable="columnPRNo" msprop:Generator_ColumnPropNameInRow="PRNo" msprop:Generator_ColumnPropNameInTable="PRNoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PRQty" msprop:Generator_UserColumnName="PRQty" msprop:Generator_ColumnVarNameInTable="columnPRQty" msprop:Generator_ColumnPropNameInRow="PRQty" msprop:Generator_ColumnPropNameInTable="PRQtyColumn" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>