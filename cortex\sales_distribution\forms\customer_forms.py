from django import forms
from django.core.exceptions import ValidationError
from django.core.validators import EmailValidator, RegexValidator
from sys_admin.models import Country, State, City
from ..models import Customer
import re


class CustomerForm(forms.ModelForm):
    """
    Form for creating and editing customer master records.
    Based on ASP.NET CustomerMaster_New.aspx form structure.
    """
    
    # Customer basic information
    customer_name = forms.CharField(
        max_length=255,
        widget=forms.TextInput(attrs={
            'class': 'form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Enter customer name'
        }),
        help_text='Full legal name of the customer'
    )
    
    # Contact Person Information
    contact_person = forms.CharField(
        max_length=255,
        widget=forms.TextInput(attrs={
            'class': 'form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Contact person name'
        }),
        help_text='Primary contact person at customer location'
    )
    
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'placeholder': '<EMAIL>'
        }),
        validators=[EmailValidator()],
        help_text='Primary email contact'
    )
    
    contact_no = forms.CharField(
        max_length=50,
        widget=forms.TextInput(attrs={
            'class': 'form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'placeholder': '+91-9876543210'
        }),
        help_text='Primary contact number'
    )
    
    # Registered Office Address
    registered_address = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-textarea block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'rows': 3,
            'placeholder': 'Registered office address'
        }),
        help_text='Complete registered address'
    )
    
    registered_country = forms.ModelChoiceField(
        queryset=Country.objects.all(),
        empty_label="Select Country",
        widget=forms.Select(attrs={
            'class': 'form-select block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'hx-get': '/sales-distribution/ajax/states/',
            'hx-target': '#id_registered_state',
            'hx-trigger': 'change'
        })
    )
    
    registered_state = forms.ModelChoiceField(
        queryset=State.objects.none(),
        empty_label="Select State",
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'hx-get': '/sales-distribution/ajax/cities/',
            'hx-target': '#id_registered_city',
            'hx-trigger': 'change'
        })
    )
    
    registered_city = forms.ModelChoiceField(
        queryset=City.objects.none(),
        empty_label="Select City",
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'
        })
    )
    
    registered_pin = forms.CharField(
        max_length=10,
        widget=forms.TextInput(attrs={
            'class': 'form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'PIN Code'
        }),
        validators=[RegexValidator(r'^\d{6}$', 'Enter a valid 6-digit PIN code')],
        help_text='6-digit PIN code'
    )
    
    registered_contact_no = forms.CharField(
        max_length=50,
        widget=forms.TextInput(attrs={
            'class': 'form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Contact number'
        }),
        help_text='Registered office contact number'
    )
    
    regdfaxno = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Fax number'
        }),
        help_text='Registered office fax number'
    )
    
    # Works/Factory Address
    works_address = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-textarea block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'rows': 3,
            'placeholder': 'Works/Factory address'
        }),
        help_text='Complete works/factory address'
    )
    
    works_country = forms.ModelChoiceField(
        queryset=Country.objects.all(),
        empty_label="Select Country",
        widget=forms.Select(attrs={
            'class': 'form-select block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'hx-get': '/sales-distribution/ajax/states/',
            'hx-target': '#id_works_state',
            'hx-trigger': 'change'
        })
    )
    
    works_state = forms.ModelChoiceField(
        queryset=State.objects.none(),
        empty_label="Select State",
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'hx-get': '/sales-distribution/ajax/cities/',
            'hx-target': '#id_works_city',
            'hx-trigger': 'change'
        })
    )
    
    works_city = forms.ModelChoiceField(
        queryset=City.objects.none(),
        empty_label="Select City",
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'
        })
    )
    
    works_pin = forms.CharField(
        max_length=10,
        widget=forms.TextInput(attrs={
            'class': 'form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'PIN Code'
        }),
        validators=[RegexValidator(r'^\d{6}$', 'Enter a valid 6-digit PIN code')],
        help_text='6-digit PIN code'
    )
    
    works_contact_no = forms.CharField(
        max_length=50,
        widget=forms.TextInput(attrs={
            'class': 'form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Contact number'
        }),
        help_text='Works/factory contact number'
    )
    
    workfaxno = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Fax number'
        }),
        help_text='Works/factory fax number'
    )
    
    # Material Delivery Address
    material_address = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-textarea block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'rows': 3,
            'placeholder': 'Material delivery address'
        }),
        help_text='Complete material delivery address'
    )
    
    material_country = forms.ModelChoiceField(
        queryset=Country.objects.all(),
        empty_label="Select Country",
        widget=forms.Select(attrs={
            'class': 'form-select block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'hx-get': '/sales-distribution/ajax/states/',
            'hx-target': '#id_material_state',
            'hx-trigger': 'change'
        })
    )
    
    material_state = forms.ModelChoiceField(
        queryset=State.objects.none(),
        empty_label="Select State",
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'hx-get': '/sales-distribution/ajax/cities/',
            'hx-target': '#id_material_city',
            'hx-trigger': 'change'
        })
    )
    
    material_city = forms.ModelChoiceField(
        queryset=City.objects.none(),
        empty_label="Select City",
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'
        })
    )
    
    material_pin = forms.CharField(
        max_length=10,
        widget=forms.TextInput(attrs={
            'class': 'form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'PIN Code'
        }),
        validators=[RegexValidator(r'^\d{6}$', 'Enter a valid 6-digit PIN code')],
        help_text='6-digit PIN code'
    )
    
    material_contact_no = forms.CharField(
        max_length=50,
        widget=forms.TextInput(attrs={
            'class': 'form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Contact number'
        }),
        help_text='Material delivery contact number'
    )
    
    materialdelfaxno = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Fax number'
        }),
        help_text='Material delivery fax number'
    )
    
    # Tax and Legal Information - Made optional/hidden
    juridictioncode = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Jurisdiction code',
            'style': 'display: none;'
        }),
        help_text='Tax jurisdiction code'
    )
    
    eccno = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'ECC Number',
            'style': 'display: none;'
        }),
        help_text='Excise Central Code Number'
    )
    
    range = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Range',
            'style': 'display: none;'
        }),
        help_text='Tax range'
    )
    
    commissionurate = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Commissionerate',
            'style': 'display: none;'
        }),
        help_text='Tax commissionerate'
    )
    
    divn = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Division',
            'style': 'display: none;'
        }),
        help_text='Tax division'
    )
    
    panno = forms.CharField(
        max_length=10,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'placeholder': '**********',
            'style': 'display: none;'
        }),
        validators=[RegexValidator(r'^[A-Z]{5}[0-9]{4}[A-Z]{1}$', 'Enter a valid PAN number format (**********)')],
        help_text='10-character PAN number'
    )
    
    tinvatno = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'TIN/VAT Number',
            'style': 'display: none;'
        }),
        help_text='Tax Identification/VAT Number'
    )
    
    tincstno = forms.CharField(
        max_length=20,
        widget=forms.TextInput(attrs={
            'class': 'form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'TIN/CST Number'
        }),
        help_text='Tax Identification/CST Number'
    )
    
    tdscode = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'TDS Code',
            'style': 'display: none;'
        }),
        help_text='Tax Deducted at Source Code'
    )
    
    # Additional Information
    remarks = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-textarea block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'rows': 3,
            'placeholder': 'Additional remarks or notes'
        }),
        help_text='Optional remarks or additional information'
    )

    class Meta:
        model = Customer
        fields = [
            'customer_name', 'contact_person', 'email', 'contact_no',
            'registered_address', 'registered_country', 'registered_state', 'registered_city', 
            'registered_pin', 'registered_contact_no', 'regdfaxno',
            'works_address', 'works_country', 'works_state', 'works_city', 
            'works_pin', 'works_contact_no', 'workfaxno',
            'material_address', 'material_country', 'material_state', 'material_city', 
            'material_pin', 'material_contact_no', 'materialdelfaxno',
            'juridictioncode', 'eccno', 'range', 'commissionurate', 'divn', 
            'panno', 'tinvatno', 'tincstno', 'tdscode', 'remarks'
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # If we have an instance (editing), populate state and city dropdowns
        if self.instance and self.instance.pk:
            # Registered address dropdowns
            if self.instance.registered_country:
                self.fields['registered_state'].queryset = State.objects.filter(
                    country=self.instance.registered_country
                )
                if self.instance.registered_state:
                    self.fields['registered_city'].queryset = City.objects.filter(
                        state=self.instance.registered_state
                    )
            
            # Works address dropdowns
            if self.instance.works_country:
                self.fields['works_state'].queryset = State.objects.filter(
                    country=self.instance.works_country
                )
                if self.instance.works_state:
                    self.fields['works_city'].queryset = City.objects.filter(
                        state=self.instance.works_state
                    )
            
            # Material delivery address dropdowns
            if self.instance.material_country:
                self.fields['material_state'].queryset = State.objects.filter(
                    country=self.instance.material_country
                )
                if self.instance.material_state:
                    self.fields['material_city'].queryset = City.objects.filter(
                        state=self.instance.material_state
                    )

    def clean_email(self):
        """Validate email format using regex like in ASP.NET"""
        email = self.cleaned_data.get('email')
        if email:
            # Fixed regex pattern (original ASP.NET had syntax errors)
            pattern = r'\w+([-+.\']?\w+)*@\w+([-.]?\w+)*\.\w+([-.]?\w+)*'
            if not re.match(pattern, email):
                raise ValidationError('Please enter a valid email address.')
        return email

    def clean_panno(self):
        """Validate PAN number format"""
        pan = self.cleaned_data.get('panno')
        if pan:
            pan = pan.upper()
            if not re.match(r'^[A-Z]{5}[0-9]{4}[A-Z]{1}$', pan):
                raise ValidationError('PAN number must be in format **********')
        return pan

    def clean(self):
        """Additional form-wide validation"""
        cleaned_data = super().clean()
        
        # Validate that all address sections have required fields (removed fax numbers)
        self._validate_address_section(
            cleaned_data, 'registered', 
            ['registered_address', 'registered_country', 'registered_state', 
             'registered_city', 'registered_pin', 'registered_contact_no']
        )
        
        self._validate_address_section(
            cleaned_data, 'works',
            ['works_address', 'works_country', 'works_state', 
             'works_city', 'works_pin', 'works_contact_no']
        )
        
        self._validate_address_section(
            cleaned_data, 'material',
            ['material_address', 'material_country', 'material_state', 
             'material_city', 'material_pin', 'material_contact_no']
        )
        
        return cleaned_data

    def _validate_address_section(self, cleaned_data, section_name, required_fields):
        """Validate that address sections have all required fields"""
        missing_fields = []
        for field in required_fields:
            if not cleaned_data.get(field):
                missing_fields.append(field)
        
        if missing_fields:
            for field in missing_fields:
                self.add_error(field, f'This field is required for {section_name} address.')