# Package 2: Invoice Management Templates Implementation

## Overview
**Priority**: HIGH  
**Effort**: 2-3 days  
**Impact**: Completes invoice workflow functionality  
**Type**: Template creation only (Views already exist)  

## Verification Method
Before starting any template, verify the view exists:
```bash
# Check view exists in views.py
grep -n "class.*View" accounts/views.py | grep -i [ViewName]

# Check URL pattern exists  
grep -n "path.*/" accounts/urls.py | grep -i [url_name]

# Verify no template already exists
find accounts/templates -name "*[template_name]*" -type f
```

## Task List (8 Templates)

### 1. Proforma Invoice Creation
**ASP.NET File**: `Module/Accounts/Transactions/ProformaInvoice_New.aspx`  
**View**: `ProformaInvoiceCreateView` (VERIFIED EXISTS)  
**URL**: `invoices/proforma/create/` (VERIFIED EXISTS)  
**Template**: `accounts/templates/accounts/invoices/proforma_invoice_form.html`  
**Form**: `ProformaInvoiceForm` (VERIFIED EXISTS)  

**Features Required**:
- Customer selection with search
- Product/service line items
- Tax calculations (VAT/GST)
- Terms and conditions
- Preview functionality
- PDF generation capability

### 2. Proforma Invoice Editing  
**ASP.NET File**: `Module/Accounts/Transactions/ProformaInvoice_Edit.aspx`  
**View**: `ProformaInvoiceUpdateView` (VERIFIED EXISTS)  
**URL**: `invoices/proforma/<int:id>/edit/` (VERIFIED EXISTS)  
**Template**: `accounts/templates/accounts/invoices/proforma_invoice_edit.html`  
**Form**: `ProformaInvoiceForm` (VERIFIED EXISTS)  

**Features Required**:
- Edit existing proforma details
- Item modification with recalculation
- Status updates (Draft/Sent/Converted)
- Conversion to sales invoice
- Amendment tracking

### 3. Service Tax Invoice Creation
**ASP.NET File**: `Module/Accounts/Transactions/ServiceTaxInvoice_New.aspx`  
**View**: `ServiceTaxInvoiceCreateView` (VERIFIED EXISTS)  
**URL**: `invoices/service-tax/create/` (VERIFIED EXISTS)  
**Template**: `accounts/templates/accounts/invoices/service_tax_invoice_form.html`  
**Form**: `ServiceTaxInvoiceForm` (VERIFIED EXISTS)  

**Features Required**:
- Service-specific fields
- Service tax calculations
- Place of service validation
- Reverse charge mechanism
- Compliance reporting integration

### 4. Service Tax Invoice Editing
**ASP.NET File**: `Module/Accounts/Transactions/ServiceTaxInvoice_Edit.aspx`  
**View**: `ServiceTaxInvoiceUpdateView` (VERIFIED EXISTS)  
**URL**: `invoices/service-tax/<int:id>/edit/` (VERIFIED EXISTS)  
**Template**: `accounts/templates/accounts/invoices/service_tax_invoice_edit.html`  
**Form**: `ServiceTaxInvoiceForm` (VERIFIED EXISTS)  

**Features Required**:
- Service tax amendment process
- Tax rate changes handling
- Compliance impact assessment
- Amendment documentation

### 5. Sales Invoice Editing
**ASP.NET File**: `Module/Accounts/Transactions/SalesInvoice_Edit.aspx`  
**View**: `SalesInvoiceUpdateView` (VERIFIED EXISTS)  
**URL**: `invoices/sales/<int:id>/edit/` (VERIFIED EXISTS)  
**Template**: `accounts/templates/accounts/invoices/sales_invoice_edit.html`  
**Form**: `SalesInvoiceForm` (VERIFIED EXISTS)  

**Features Required**:
- Edit sales invoice details
- Item line modifications
- Tax recalculations
- Credit note generation link
- Payment status tracking

### 6. Bill Booking Creation
**ASP.NET File**: `Module/Accounts/Transactions/BillBooking_New.aspx`  
**View**: `BillBookingCreateView` (VERIFIED EXISTS)  
**URL**: `invoices/bill-booking/create/` (VERIFIED EXISTS)  
**Template**: `accounts/templates/accounts/invoices/bill_booking_form.html`  
**Form**: `BillBookingForm` (VERIFIED EXISTS)  

**Features Required**:
- Vendor bill entry
- Purchase order reference
- Approval workflow
- Account head mapping
- TDS/advance adjustments

### 7. Bill Booking Editing
**ASP.NET File**: `Module/Accounts/Transactions/BillBooking_Edit.aspx`  
**View**: `BillBookingUpdateView` (VERIFIED EXISTS)  
**URL**: `invoices/bill-booking/<int:id>/edit/` (VERIFIED EXISTS)  
**Template**: `accounts/templates/accounts/invoices/bill_booking_edit.html`  
**Form**: `BillBookingForm` (VERIFIED EXISTS)  

**Features Required**:
- Modify bill details
- Account allocation changes
- Approval status updates
- Payment scheduling
- Aging analysis integration

### 8. Bill Booking Authorization
**ASP.NET File**: `Module/Accounts/Transactions/BillBooking_Authorize.aspx`  
**View**: `BillBookingAuthorizeView` (VERIFIED EXISTS)  
**URL**: `invoices/bill-booking/<int:id>/authorize/` (VERIFIED EXISTS)  
**Template**: `accounts/templates/accounts/invoices/bill_booking_authorize.html`  
**Form**: `BillBookingApprovalForm` (VERIFIED EXISTS)  

**Features Required**:
- Multi-level approval workflow
- Authorization comments
- Approval hierarchy display
- Reject with reasons
- Email notifications

## Template Requirements

### Standard Features for All Templates:
1. **SAP-inspired UI** with consistent styling
2. **HTMX integration** for dynamic operations
3. **Real-time calculations** for tax and totals
4. **Form validation** with error handling
5. **Responsive design** for all screen sizes
6. **Auto-save** functionality for long forms
7. **PDF preview** and generation

### Template Structure:
```
accounts/templates/accounts/invoices/
├── proforma_invoice_form.html
├── proforma_invoice_edit.html  
├── service_tax_invoice_form.html
├── service_tax_invoice_edit.html
├── sales_invoice_edit.html
├── bill_booking_form.html
├── bill_booking_edit.html
└── bill_booking_authorize.html
```

## Quality Assurance Checklist

### Before Starting:
- [ ] Verify view class exists in accounts/views.py
- [ ] Verify URL pattern exists in accounts/urls.py  
- [ ] Verify form class exists in accounts/forms.py
- [ ] Confirm no existing template for this functionality

### During Development:
- [ ] Follow SAP-inspired design patterns
- [ ] Include HTMX attributes for dynamic operations
- [ ] Add proper form validation and error handling
- [ ] Ensure responsive design
- [ ] Test all calculation functions
- [ ] Implement auto-save where appropriate

### After Completion:
- [ ] Template renders without errors
- [ ] All form operations work correctly
- [ ] Tax calculations are accurate
- [ ] PDF generation works (if applicable)
- [ ] Mobile responsive design verified
- [ ] Approval workflows function properly

## Success Criteria
- All 8 templates functional and integrated
- Invoice management workflows complete
- Tax calculations working accurately
- Approval processes streamlined
- PDF generation capabilities working
- Ready for production use

## Dependencies
- Existing view classes (already verified)
- Existing form classes (already verified)  
- Existing URL patterns (already verified)
- SAP-inspired CSS framework (already available)
- HTMX library (already integrated)
- PDF generation library (if not available, needs setup)

## Special Considerations
- **Tax Compliance**: Ensure all tax calculations meet regulatory requirements
- **Approval Workflows**: Implement proper authorization chains
- **Data Integrity**: Maintain referential integrity across invoice types
- **Performance**: Optimize for large invoice datasets
- **Security**: Implement proper access controls for sensitive financial data