from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from datetime import date, timedelta

from .models import (
    DailyReportingTracker, DesignPlan
)
from .forms import (
    DailyReportingTrackerForm
)


class DailyReportingTrackerModelTest(TestCase):
    """Test cases for DailyReportingTracker model"""

    def setUp(self):
        self.tracker = DailyReportingTracker.objects.create(
            employee_name="<PERSON>",
            designation="Software Engineer",
            department="IT",
            date_of_reporting=date.today(),
            significant_achievements_last_week="Completed feature X",
            activities_task_current_week="Working on feature Y",
            activities_planned_completed="Task A completed",
            activities_planned_not_completed="Task B pending",
            activities_unplanned_completed="Bug fix Z",
            plan_next_week="Start feature Z",
            activity_date=date.today(),
            wo_number="WO123",
            activity="Database optimization",
            estimated_time="4 hours",
            status="In Progress",
            percentage_completed=75,
            remarks="Good progress made"
        )

    def test_str_representation(self):
        """Test string representation of DailyReportingTracker"""
        expected = f"Daily Report - <PERSON> ({date.today()})"
        self.assertEqual(str(self.tracker), expected)

    def test_model_fields(self):
        """Test model field values"""
        self.assertEqual(self.tracker.employee_name, "John Doe")
        self.assertEqual(self.tracker.department, "IT")
        self.assertEqual(self.tracker.wo_number, "WO123")
        self.assertEqual(self.tracker.percentage_completed, 75)


class DesignPlanModelTest(TestCase):
    """Test cases for DesignPlan model"""

    def setUp(self):
        self.design_plan = DesignPlan.objects.create(
            wo_number="WO123",
            fixture_number="FIX001",
            concept_design="Completed",
            internal_review="In Progress",
            dap_send="Pending",
            dap_received="Not Started",
            correction="Not Required",
            final_dap="Pending",
            bought_list="Completed",
            drawing_release_detailing="In Progress",
            tpl_entry="Completed",
            flame_cut="Pending",
            cnc_data="Completed",
            cmm_data="In Progress",
            fit_list="Completed",
            manual="Pending"
        )

    def test_str_representation(self):
        """Test string representation of DesignPlan"""
        expected = "Design Plan - WO: WO123, Fixture: FIX001"
        self.assertEqual(str(self.design_plan), expected)


class DailyReportingTrackerFormTest(TestCase):
    """Test cases for DailyReportingTrackerForm"""

    def test_valid_form(self):
        """Test form with valid data"""
        form_data = {
            'employee_name': 'Jane Smith',
            'designation': 'Project Manager',
            'department': 'Engineering',
            'date_of_reporting': date.today(),
            'significant_achievements_last_week': 'Completed project phase 1',
            'activities_task_current_week': 'Starting phase 2',
            'activities_planned_completed': 'Design review completed',
            'activities_planned_not_completed': 'Code review pending',
            'activities_unplanned_completed': 'Emergency bug fix',
            'plan_next_week': 'Continue with phase 2',
            'activity_date': date.today(),
            'wo_number': 'WO456',
            'activity': 'System testing',
            'estimated_time': '6 hours',
            'status': 'In Progress',
            'percentage_completed': 50,
            'remarks': 'On track'
        }
        form = DailyReportingTrackerForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_invalid_form_missing_required_fields(self):
        """Test form with missing required fields"""
        form_data = {
            'employee_name': '',  # Required field missing
            'department': 'Engineering',
        }
        form = DailyReportingTrackerForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('employee_name', form.errors)

    def test_percentage_validation(self):
        """Test percentage completed validation"""
        form_data = {
            'employee_name': 'Jane Smith',
            'designation': 'Project Manager',
            'department': 'Engineering',
            'date_of_reporting': date.today(),
            'significant_achievements_last_week': 'Test',
            'activities_task_current_week': 'Test',
            'activities_planned_completed': 'Test',
            'activities_planned_not_completed': 'Test',
            'activities_unplanned_completed': 'Test',
            'plan_next_week': 'Test',
            'activity_date': date.today(),
            'wo_number': 'WO456',
            'activity': 'Testing',
            'estimated_time': '2 hours',
            'status': 'Testing',
            'percentage_completed': 150,  # Invalid percentage
            'remarks': 'Test'
        }
        form = DailyReportingTrackerForm(data=form_data)
        self.assertFalse(form.is_valid())

    def test_date_validation(self):
        """Test date validation logic"""
        future_date = date.today() + timedelta(days=1)
        form_data = {
            'employee_name': 'Jane Smith',
            'designation': 'Project Manager',
            'department': 'Engineering',
            'date_of_reporting': date.today(),
            'significant_achievements_last_week': 'Test',
            'activities_task_current_week': 'Test',
            'activities_planned_completed': 'Test',
            'activities_planned_not_completed': 'Test',
            'activities_unplanned_completed': 'Test',
            'plan_next_week': 'Test',
            'activity_date': future_date,  # Future date
            'wo_number': 'WO456',
            'activity': 'Testing',
            'estimated_time': '2 hours',
            'status': 'Testing',
            'percentage_completed': 50,
            'remarks': 'Test'
        }
        form = DailyReportingTrackerForm(data=form_data)
        self.assertFalse(form.is_valid())


class DailyReportingViewsTest(TestCase):
    """Test cases for Daily Reporting views"""

    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            email='<EMAIL>'
        )
        self.client.login(username='testuser', password='testpass123')
        
        self.tracker = DailyReportingTracker.objects.create(
            employee_name="Test Employee",
            designation="Test Designation",
            department="Test Department",
            date_of_reporting=date.today(),
            significant_achievements_last_week="Test achievement",
            activities_task_current_week="Test activity",
            activities_planned_completed="Test completed",
            activities_planned_not_completed="Test not completed",
            activities_unplanned_completed="Test unplanned",
            plan_next_week="Test plan",
            activity_date=date.today(),
            wo_number="TEST123",
            activity="Test activity description",
            estimated_time="2 hours",
            status="Test Status",
            percentage_completed=50,
            remarks="Test remarks"
        )

    def test_dashboard_view(self):
        """Test dashboard view loads correctly"""
        response = self.client.get(reverse('daily_reporting:dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Daily Reporting System')

    def test_tracker_list_view(self):
        """Test tracker list view"""
        response = self.client.get(reverse('daily_reporting:tracker_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Employee')

    def test_tracker_detail_view(self):
        """Test tracker detail view"""
        response = self.client.get(
            reverse('daily_reporting:tracker_detail', kwargs={'pk': self.tracker.pk})
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Employee')

    def test_tracker_create_view_get(self):
        """Test tracker create view GET request"""
        response = self.client.get(reverse('daily_reporting:tracker_create'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Daily Report Form')

    def test_unauthorized_access(self):
        """Test unauthorized access redirects to login"""
        self.client.logout()
        response = self.client.get(reverse('daily_reporting:dashboard'))
        self.assertEqual(response.status_code, 302)  # Redirect to login
