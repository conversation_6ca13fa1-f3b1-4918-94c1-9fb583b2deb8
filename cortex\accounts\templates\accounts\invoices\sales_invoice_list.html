<!-- accounts/templates/accounts/invoices/sales_invoice_list.html -->
<!-- Sales Invoice List Template with HTMX -->
<!-- Task Group 5: Invoicing & Billing - Sales Invoice List (Task 5.14) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Sales Invoices - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-green-600 to-sap-green-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Sales Invoices</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage customer invoices and billing</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="bulkOperations()" 
                        class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="layers" class="w-4 h-4 inline mr-2"></i>
                    Bulk Actions
                </button>
                <a href="{% url 'accounts:sales_invoice_create' %}" 
                   class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    New Invoice
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6" x-data="salesInvoiceList()">
    
    <!-- Search and Filter Section -->
    <div class="mb-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-sap-gray-800">Search & Filter</h3>
                <button type="button" @click="toggleFilters()" class="text-sap-blue-600 hover:text-sap-blue-700">
                    <i data-lucide="filter" class="w-5 h-5"></i>
                </button>
            </div>
            
            <form hx-get="{% url 'accounts:sales_invoice_list' %}" 
                  hx-target="#invoice-list-container" 
                  hx-trigger="submit, input delay:500ms changed"
                  hx-indicator="#search-spinner"
                  class="space-y-4" 
                  id="filter-form">
                
                <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <!-- Real-time Search -->
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-1">Search</label>
                        <div class="relative">
                            <input type="text" name="search" value="{{ request.GET.search }}" 
                                   placeholder="Invoice no, customer..."
                                   class="block w-full px-3 py-2 pl-10 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500"
                                   hx-get="{% url 'accounts:sales_invoice_list' %}"
                                   hx-target="#invoice-list-container"
                                   hx-trigger="keyup changed delay:300ms"
                                   hx-indicator="#search-spinner">
                            <i data-lucide="search" class="w-4 h-4 absolute left-3 top-3 text-sap-gray-400"></i>
                            <div id="search-spinner" class="htmx-indicator absolute right-3 top-3">
                                <i data-lucide="loader-2" class="w-4 h-4 animate-spin text-sap-blue-500"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Status Filter -->
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-1">Status</label>
                        <select name="status" class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500"
                                hx-get="{% url 'accounts:sales_invoice_list' %}"
                                hx-target="#invoice-list-container"
                                hx-trigger="change"
                                hx-indicator="#search-spinner">
                            <option value="">All Status</option>
                            <option value="DRAFT" {% if request.GET.status == "DRAFT" %}selected{% endif %}>Draft</option>
                            <option value="SENT" {% if request.GET.status == "SENT" %}selected{% endif %}>Sent</option>
                            <option value="PAID" {% if request.GET.status == "PAID" %}selected{% endif %}>Paid</option>
                            <option value="OVERDUE" {% if request.GET.status == "OVERDUE" %}selected{% endif %}>Overdue</option>
                            <option value="CANCELLED" {% if request.GET.status == "CANCELLED" %}selected{% endif %}>Cancelled</option>
                        </select>
                    </div>
                    
                    <!-- Customer Filter -->
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-1">Customer</label>
                        <select name="customer" class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500"
                                hx-get="{% url 'accounts:sales_invoice_list' %}"
                                hx-target="#invoice-list-container"
                                hx-trigger="change"
                                hx-indicator="#search-spinner">
                            <option value="">All Customers</option>
                            {% for customer in customers %}
                            <option value="{{ customer.id }}" {% if request.GET.customer == customer.id|stringformat:"s" %}selected{% endif %}>
                                {{ customer.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <!-- Date Range -->
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-1">From Date</label>
                        <input type="date" name="from_date" value="{{ request.GET.from_date }}"
                               class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500"
                               hx-get="{% url 'accounts:sales_invoice_list' %}"
                               hx-target="#invoice-list-container"
                               hx-trigger="change"
                               hx-indicator="#search-spinner">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-sap-gray-700 mb-1">To Date</label>
                        <input type="date" name="to_date" value="{{ request.GET.to_date }}"
                               class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500"
                               hx-get="{% url 'accounts:sales_invoice_list' %}"
                               hx-target="#invoice-list-container"
                               hx-trigger="change"
                               hx-indicator="#search-spinner">
                    </div>
                </div>
                
                <div class="flex items-center space-x-3" x-show="showAdvancedFilters">
                    <div class="flex-1">
                        <label class="block text-sm font-medium text-sap-gray-700 mb-1">Amount Range</label>
                        <div class="flex space-x-2">
                            <input type="number" name="min_amount" value="{{ request.GET.min_amount }}" placeholder="Min"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                            <input type="number" name="max_amount" value="{{ request.GET.max_amount }}" placeholder="Max"
                                   class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                        </div>
                    </div>
                    
                    <div class="flex items-end space-x-3">
                        <button type="submit" class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="search" class="w-4 h-4 inline mr-2"></i>
                            Search
                        </button>
                        <a href="{% url 'accounts:sales_invoice_list' %}" 
                           class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="x" class="w-4 h-4 inline mr-2"></i>
                            Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6" id="summary-cards">
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="file-text" class="w-5 h-5 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-sap-gray-600">Total Invoices</p>
                    <p class="text-xl font-semibold text-sap-gray-800">{{ total_invoices }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="dollar-sign" class="w-5 h-5 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-sap-gray-600">Total Amount</p>
                    <p class="text-xl font-semibold text-sap-gray-800">₹{{ total_amount|floatformat:2 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="clock" class="w-5 h-5 text-sap-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-sap-gray-600">Outstanding</p>
                    <p class="text-xl font-semibold text-sap-gray-800">₹{{ outstanding_amount|floatformat:2 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-sap-red-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="alert-circle" class="w-5 h-5 text-sap-red-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-sap-gray-600">Overdue</p>
                    <p class="text-xl font-semibold text-sap-gray-800">₹{{ overdue_amount|floatformat:2 }}</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Invoice List Container -->
    <div id="invoice-list-container">
        {% include 'accounts/invoices/partials/invoice_list_table.html' %}
    </div>
    
    <!-- Bulk Operations Modal -->
    <div x-show="showBulkModal" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 bg-black bg-opacity-50 z-50" 
         @click="showBulkModal = false">
        <div class="fixed inset-0 flex items-center justify-center p-4">
            <div class="bg-white rounded-lg border border-sap-gray-200 shadow-xl max-w-md w-full" @click.stop>
                <div class="px-6 py-4 border-b border-sap-gray-100">
                    <h3 class="text-lg font-medium text-sap-gray-800">Bulk Operations</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-3">
                        <button onclick="bulkExport()" 
                                class="w-full flex items-center px-4 py-2 text-left hover:bg-sap-gray-50 rounded-lg">
                            <i data-lucide="download" class="w-4 h-4 mr-3 text-sap-blue-600"></i>
                            Export Selected
                        </button>
                        <button onclick="bulkEmail()" 
                                class="w-full flex items-center px-4 py-2 text-left hover:bg-sap-gray-50 rounded-lg">
                            <i data-lucide="mail" class="w-4 h-4 mr-3 text-sap-green-600"></i>
                            Email Selected
                        </button>
                        <button onclick="bulkPrint()" 
                                class="w-full flex items-center px-4 py-2 text-left hover:bg-sap-gray-50 rounded-lg">
                            <i data-lucide="printer" class="w-4 h-4 mr-3 text-sap-purple-600"></i>
                            Print Selected
                        </button>
                        <button onclick="bulkStatusUpdate()" 
                                class="w-full flex items-center px-4 py-2 text-left hover:bg-sap-gray-50 rounded-lg">
                            <i data-lucide="edit" class="w-4 h-4 mr-3 text-sap-orange-600"></i>
                            Update Status
                        </button>
                    </div>
                </div>
                <div class="px-6 py-4 border-t border-sap-gray-100 flex justify-end">
                    <button @click="showBulkModal = false" 
                            class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- HTMX -->
<script src="https://unpkg.com/htmx.org@1.9.10"></script>

<script>
function salesInvoiceList() {
    return {
        showAdvancedFilters: false,
        showBulkModal: false,
        selectedInvoices: [],
        
        toggleFilters() {
            this.showAdvancedFilters = !this.showAdvancedFilters;
        },
        
        toggleInvoiceSelection(invoiceId) {
            const index = this.selectedInvoices.indexOf(invoiceId);
            if (index > -1) {
                this.selectedInvoices.splice(index, 1);
            } else {
                this.selectedInvoices.push(invoiceId);
            }
        },
        
        selectAllInvoices() {
            const checkboxes = document.querySelectorAll('input[name="invoice_ids"]');
            this.selectedInvoices = Array.from(checkboxes).map(cb => cb.value);
            checkboxes.forEach(cb => cb.checked = true);
        },
        
        deselectAllInvoices() {
            this.selectedInvoices = [];
            document.querySelectorAll('input[name="invoice_ids"]').forEach(cb => cb.checked = false);
        }
    }
}

function bulkOperations() {
    const alpine = Alpine.$data(document.querySelector('[x-data="salesInvoiceList()"]'));
    alpine.showBulkModal = true;
}

function quickStatusUpdate(invoiceId, newStatus) {
    fetch(`/accounts/invoices/sales/${invoiceId}/quick-status/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus })
    })
    .then(response => {
        if (response.ok) {
            // Trigger HTMX refresh
            htmx.trigger('#invoice-list-container', 'refresh');
            showNotification(`Invoice status updated to ${newStatus}`, 'success');
        } else {
            showNotification('Error updating status', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error updating status', 'error');
    });
}

function duplicateInvoice(invoiceId) {
    if (confirm('Create a copy of this invoice?')) {
        fetch(`/accounts/invoices/sales/${invoiceId}/duplicate/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            },
        })
        .then(response => {
            if (response.ok) {
                return response.json();
            }
            throw new Error('Network response was not ok');
        })
        .then(data => {
            window.location.href = `/accounts/invoices/sales/${data.new_invoice_id}/edit/`;
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error duplicating invoice', 'error');
        });
    }
}

function emailInvoice(invoiceId) {
    // Open email modal or redirect to email page
    window.open(`/accounts/invoices/sales/${invoiceId}/email/`, '_blank', 'width=800,height=600');
}

function printInvoice(invoiceId) {
    window.open(`/accounts/invoices/sales/${invoiceId}/print/`, '_blank');
}

function deleteInvoice(invoiceId) {
    if (confirm('Are you sure you want to delete this invoice? This action cannot be undone.')) {
        fetch(`/accounts/invoices/sales/${invoiceId}/delete/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            },
        })
        .then(response => {
            if (response.ok) {
                htmx.trigger('#invoice-list-container', 'refresh');
                showNotification('Invoice deleted successfully', 'success');
            } else {
                showNotification('Error deleting invoice', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error deleting invoice', 'error');
        });
    }
}

function bulkExport() {
    const alpine = Alpine.$data(document.querySelector('[x-data="salesInvoiceList()"]'));
    if (alpine.selectedInvoices.length === 0) {
        showNotification('Please select invoices to export', 'warning');
        return;
    }
    
    const ids = alpine.selectedInvoices.join(',');
    window.location.href = `/accounts/invoices/sales/bulk-export/?ids=${ids}`;
    alpine.showBulkModal = false;
}

function bulkEmail() {
    const alpine = Alpine.$data(document.querySelector('[x-data="salesInvoiceList()"]'));
    if (alpine.selectedInvoices.length === 0) {
        showNotification('Please select invoices to email', 'warning');
        return;
    }
    
    const ids = alpine.selectedInvoices.join(',');
    window.open(`/accounts/invoices/sales/bulk-email/?ids=${ids}`, '_blank', 'width=800,height=600');
    alpine.showBulkModal = false;
}

function bulkPrint() {
    const alpine = Alpine.$data(document.querySelector('[x-data="salesInvoiceList()"]'));
    if (alpine.selectedInvoices.length === 0) {
        showNotification('Please select invoices to print', 'warning');
        return;
    }
    
    const ids = alpine.selectedInvoices.join(',');
    window.open(`/accounts/invoices/sales/bulk-print/?ids=${ids}`, '_blank');
    alpine.showBulkModal = false;
}

function bulkStatusUpdate() {
    const alpine = Alpine.$data(document.querySelector('[x-data="salesInvoiceList()"]'));
    if (alpine.selectedInvoices.length === 0) {
        showNotification('Please select invoices to update', 'warning');
        return;
    }
    
    const newStatus = prompt('Enter new status (DRAFT, SENT, PAID, OVERDUE, CANCELLED):');
    if (newStatus) {
        fetch('/accounts/invoices/sales/bulk-status-update/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                invoice_ids: alpine.selectedInvoices,
                status: newStatus.toUpperCase()
            })
        })
        .then(response => {
            if (response.ok) {
                htmx.trigger('#invoice-list-container', 'refresh');
                showNotification(`Status updated for ${alpine.selectedInvoices.length} invoices`, 'success');
                alpine.selectedInvoices = [];
            } else {
                showNotification('Error updating status', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error updating status', 'error');
        });
    }
    
    alpine.showBulkModal = false;
}

function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-sap-green-600 text-white' :
        type === 'error' ? 'bg-sap-red-600 text-white' :
        'bg-sap-yellow-600 text-white'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// HTMX event listeners
document.addEventListener('htmx:afterRequest', function(event) {
    if (event.detail.successful) {
        lucide.createIcons();
    }
});

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}