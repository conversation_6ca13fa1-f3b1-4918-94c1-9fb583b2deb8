{% if schedule_details %}
<div class="overflow-x-auto">
    <table class="min-w-full border border-gray-300">
        <thead class="bg-gray-50">
            <tr>
                <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">SN</th>
                <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">Machine Name</th>
                <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">Process</th>
                <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">Type</th>
                <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">Shift</th>
                <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">Batch No</th>
                <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">Batch Qty</th>
                <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">From Date</th>
                <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">To Date</th>
                <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">From Time</th>
                <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">To Time</th>
                <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">Incharge</th>
                <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">Operator</th>
                <th class="px-2 py-2 text-xs font-medium text-gray-500 uppercase border-b">Action</th>
            </tr>
        </thead>
        <tbody>
            {% for detail in schedule_details %}
            <tr class="border-b hover:bg-gray-50">
                <td class="px-2 py-2 text-center">{{ forloop.counter }}</td>
                <td class="px-2 py-2">
                    {% for machine in available_machines %}
                        {% if machine.item_id == detail.machineid %}
                            {{ machine.machine.make }} {{ machine.machine.model }}
                        {% endif %}
                    {% endfor %}
                </td>
                <td class="px-2 py-2 text-center">{{ detail.process }}</td>
                <td class="px-2 py-2 text-center">
                    {% if detail.type == 0 %}Fresh{% else %}Rework{% endif %}
                </td>
                <td class="px-2 py-2 text-center">
                    {% if detail.shift == 0 %}Day{% else %}Night{% endif %}
                </td>
                <td class="px-2 py-2 text-center">{{ detail.batchno }}</td>
                <td class="px-2 py-2 text-center">{{ detail.qty }}</td>
                <td class="px-2 py-2 text-center">{{ detail.fromdate }}</td>
                <td class="px-2 py-2 text-center">{{ detail.todate }}</td>
                <td class="px-2 py-2 text-center">{{ detail.fromtime }}</td>
                <td class="px-2 py-2 text-center">{{ detail.totime }}</td>
                <td class="px-2 py-2">{{ detail.incharge }}</td>
                <td class="px-2 py-2">{{ detail.operator }}</td>
                <td class="px-2 py-2 text-center">
                    <button hx-delete="{% url 'machinery:remove_schedule_detail' detail.id %}"
                            hx-target="#schedule-details-list"
                            hx-confirm="Are you sure you want to delete this schedule detail?"
                            class="text-red-600 hover:text-red-800">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
<div class="text-center py-8">
    <p class="text-gray-500 text-lg">No schedule details added yet.</p>
    <p class="text-gray-400 text-sm">Add schedule details using the form above.</p>
</div>
{% endif %}