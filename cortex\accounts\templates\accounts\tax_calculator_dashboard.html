<!-- accounts/templates/accounts/tax_calculator_dashboard.html -->
<!-- Tax Calculator Dashboard Template -->
<!-- Task Group 4: Taxation Management - Tax Calculator Dashboard -->

{% extends 'core/base.html' %}
{% load static %}

{% block title %}Tax Calculator Dashboard - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-gray-600 to-sap-gray-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="calculator" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Tax Calculator Dashboard</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Comprehensive tax calculation tools and utilities</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Accounts
                </a>
                <a href="{% url 'accounts:composite_tax_calculator' %}" 
                   class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="calculator" class="w-4 h-4 inline mr-2"></i>
                    Quick Calculator
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6 space-y-6" x-data="taxCalculatorDashboard()">
    
    <!-- Tax Types Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Excise Duty -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-sap-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <i data-lucide="industry" class="w-5 h-5 text-sap-blue-600"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-sap-gray-800">Excise Duty</h3>
                            <p class="text-sm text-sap-gray-600">Manufacturing taxes</p>
                        </div>
                    </div>
                    <span class="bg-sap-blue-100 text-sap-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                        {{ available_taxes.excise_duties }} rates
                    </span>
                </div>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    {% if default_rates.default_excise %}
                    <div class="flex items-center justify-between p-3 bg-sap-blue-50 rounded-lg">
                        <div>
                            <p class="text-sm font-medium text-sap-gray-900">Default Excise</p>
                            <p class="text-xs text-sap-gray-600">{{ default_rates.default_excise.terms }}</p>
                        </div>
                        <span class="text-sm font-bold text-sap-blue-600">{{ default_rates.default_excise.total_excise_rate|floatformat:2 }}%</span>
                    </div>
                    {% endif %}
                    {% if default_rates.default_service_tax %}
                    <div class="flex items-center justify-between p-3 bg-sap-green-50 rounded-lg">
                        <div>
                            <p class="text-sm font-medium text-sap-gray-900">Default Service Tax</p>
                            <p class="text-xs text-sap-gray-600">{{ default_rates.default_service_tax.terms }}</p>
                        </div>
                        <span class="text-sm font-bold text-sap-green-600">{{ default_rates.default_service_tax.total_excise_rate|floatformat:2 }}%</span>
                    </div>
                    {% endif %}
                </div>
                <div class="mt-4">
                    <a href="{% url 'accounts:excise_calculator' %}" 
                       class="w-full bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors text-center block">
                        Excise Calculator
                    </a>
                </div>
            </div>
        </div>

        <!-- VAT -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-sap-orange-100 rounded-lg flex items-center justify-center mr-3">
                            <i data-lucide="percent" class="w-5 h-5 text-sap-orange-600"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-sap-gray-800">VAT</h3>
                            <p class="text-sm text-sap-gray-600">Value Added Tax</p>
                        </div>
                    </div>
                    <span class="bg-sap-orange-100 text-sap-orange-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                        {{ available_taxes.vat_rates }} rates
                    </span>
                </div>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <div class="grid grid-cols-2 gap-2">
                        <button @click="calculateVAT(5)" 
                                class="text-xs bg-sap-orange-100 text-sap-orange-800 px-3 py-2 rounded-lg hover:bg-sap-orange-200 transition-colors">
                            5% VAT
                        </button>
                        <button @click="calculateVAT(12.5)" 
                                class="text-xs bg-sap-orange-100 text-sap-orange-800 px-3 py-2 rounded-lg hover:bg-sap-orange-200 transition-colors">
                            12.5% VAT
                        </button>
                        <button @click="calculateVAT(14.5)" 
                                class="text-xs bg-sap-orange-100 text-sap-orange-800 px-3 py-2 rounded-lg hover:bg-sap-orange-200 transition-colors">
                            14.5% VAT
                        </button>
                        <button @click="calculateVAT(20)" 
                                class="text-xs bg-sap-orange-100 text-sap-orange-800 px-3 py-2 rounded-lg hover:bg-sap-orange-200 transition-colors">
                            20% VAT
                        </button>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="{% url 'accounts:vat_register_dashboard' %}" 
                       class="w-full bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors text-center block">
                        VAT Register
                    </a>
                </div>
            </div>
        </div>

        <!-- TDS -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-sap-green-100 rounded-lg flex items-center justify-center mr-3">
                            <i data-lucide="minus-circle" class="w-5 h-5 text-sap-green-600"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-sap-gray-800">TDS</h3>
                            <p class="text-sm text-sap-gray-600">Tax Deducted at Source</p>
                        </div>
                    </div>
                    <span class="bg-sap-green-100 text-sap-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                        {{ available_taxes.tds_codes }} codes
                    </span>
                </div>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <div class="grid grid-cols-2 gap-2">
                        <button @click="calculateTDS(1)" 
                                class="text-xs bg-sap-green-100 text-sap-green-800 px-3 py-2 rounded-lg hover:bg-sap-green-200 transition-colors">
                            1% TDS
                        </button>
                        <button @click="calculateTDS(2)" 
                                class="text-xs bg-sap-green-100 text-sap-green-800 px-3 py-2 rounded-lg hover:bg-sap-green-200 transition-colors">
                            2% TDS
                        </button>
                        <button @click="calculateTDS(10)" 
                                class="text-xs bg-sap-green-100 text-sap-green-800 px-3 py-2 rounded-lg hover:bg-sap-green-200 transition-colors">
                            10% TDS
                        </button>
                        <button @click="calculateTDS(20)" 
                                class="text-xs bg-sap-green-100 text-sap-green-800 px-3 py-2 rounded-lg hover:bg-sap-green-200 transition-colors">
                            20% TDS
                        </button>
                    </div>
                </div>
                <div class="mt-4">
                    <button @click="showTDSCalculator()" 
                            class="w-full bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        TDS Calculator
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Calculator Tools -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <h3 class="text-lg font-medium text-sap-gray-800">Quick Calculation Tools</h3>
            <p class="text-sm text-sap-gray-600 mt-1">Fast tax calculations for common scenarios</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <a href="{% url 'accounts:composite_tax_calculator' %}" 
                   class="flex items-center p-4 border border-sap-gray-200 rounded-lg hover:bg-sap-blue-50 hover:border-sap-blue-300 transition-all duration-200">
                    <div class="w-10 h-10 bg-sap-blue-100 rounded-lg flex items-center justify-center mr-3">
                        <i data-lucide="calculator" class="w-5 h-5 text-sap-blue-600"></i>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-sap-gray-900">Composite Calculator</h4>
                        <p class="text-xs text-sap-gray-600">All taxes in one tool</p>
                    </div>
                </a>

                <a href="{% url 'accounts:reverse_tax_calculator' %}" 
                   class="flex items-center p-4 border border-sap-gray-200 rounded-lg hover:bg-sap-purple-50 hover:border-sap-purple-300 transition-all duration-200">
                    <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                        <i data-lucide="rotate-ccw" class="w-5 h-5 text-purple-600"></i>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-sap-gray-900">Reverse Calculator</h4>
                        <p class="text-xs text-sap-gray-600">Calculate base from total</p>
                    </div>
                </a>

                <a href="{% url 'accounts:tax_comparison' %}" 
                   class="flex items-center p-4 border border-sap-gray-200 rounded-lg hover:bg-sap-green-50 hover:border-sap-green-300 transition-all duration-200">
                    <div class="w-10 h-10 bg-sap-green-100 rounded-lg flex items-center justify-center mr-3">
                        <i data-lucide="compare" class="w-5 h-5 text-sap-green-600"></i>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-sap-gray-900">Tax Comparison</h4>
                        <p class="text-xs text-sap-gray-600">Compare scenarios</p>
                    </div>
                </a>

                <a href="{% url 'accounts:tax_rate_analysis' %}" 
                   class="flex items-center p-4 border border-sap-gray-200 rounded-lg hover:bg-sap-orange-50 hover:border-sap-orange-300 transition-all duration-200">
                    <div class="w-10 h-10 bg-sap-orange-100 rounded-lg flex items-center justify-center mr-3">
                        <i data-lucide="trending-up" class="w-5 h-5 text-sap-orange-600"></i>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-sap-gray-900">Rate Analysis</h4>
                        <p class="text-xs text-sap-gray-600">Tax rate statistics</p>
                    </div>
                </a>
            </div>
        </div>
    </div>

    <!-- Quick Calculator Widget -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Quick Calculator -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800">Quick Tax Calculator</h3>
                <p class="text-sm text-sap-gray-600 mt-1">Instant calculations without page navigation</p>
            </div>
            <div class="p-6" x-data="quickCalculator()">
                <form @submit.prevent="calculate">
                    <div class="space-y-4">
                        <div>
                            <label for="quick_amount" class="block text-sm font-medium text-sap-gray-700 mb-2">Amount (₹)</label>
                            <input type="number" x-model="amount" id="quick_amount" 
                                   step="0.01" min="0" placeholder="100000"
                                   class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="quick_vat" class="block text-sm font-medium text-sap-gray-700 mb-2">VAT (%)</label>
                                <input type="number" x-model="vatRate" id="quick_vat" 
                                       step="0.001" min="0" max="100" placeholder="5"
                                       class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-orange-500 focus:border-sap-orange-500">
                            </div>
                            <div>
                                <label for="quick_excise" class="block text-sm font-medium text-sap-gray-700 mb-2">Excise (%)</label>
                                <input type="number" x-model="exciseRate" id="quick_excise" 
                                       step="0.001" min="0" max="100" placeholder="12.5"
                                       class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                            </div>
                        </div>
                        
                        <button type="submit" 
                                class="w-full bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            Calculate
                        </button>
                    </div>
                </form>
                
                <!-- Results -->
                <div x-show="result" x-transition class="mt-6 p-4 bg-sap-gray-50 rounded-lg">
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-sm text-sap-gray-600">Base Amount:</span>
                            <span class="font-medium" x-text="formatCurrency(amount)"></span>
                        </div>
                        <div x-show="vatAmount > 0" class="flex justify-between">
                            <span class="text-sm text-sap-gray-600">VAT Amount:</span>
                            <span class="font-medium text-sap-orange-600" x-text="formatCurrency(vatAmount)"></span>
                        </div>
                        <div x-show="exciseAmount > 0" class="flex justify-between">
                            <span class="text-sm text-sap-gray-600">Excise Amount:</span>
                            <span class="font-medium text-sap-blue-600" x-text="formatCurrency(exciseAmount)"></span>
                        </div>
                        <div class="flex justify-between border-t border-sap-gray-200 pt-2">
                            <span class="font-medium text-sap-gray-800">Total Amount:</span>
                            <span class="font-bold text-sap-gray-900" x-text="formatCurrency(totalAmount)"></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-sap-gray-600">Effective Rate:</span>
                            <span class="text-sm font-medium text-sap-gray-700" x-text="effectiveRate + '%'"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Calculations -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-medium text-sap-gray-800">Recent Calculations</h3>
                        <p class="text-sm text-sap-gray-600 mt-1">Your calculation history</p>
                    </div>
                    <button @click="clearHistory()" 
                            class="text-sap-gray-600 hover:text-sap-gray-800 text-sm">
                        Clear All
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div id="calculation-history" class="space-y-3">
                    <!-- History will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Tax Rate Reference -->
    {% if default_rates %}
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <h3 class="text-lg font-medium text-sap-gray-800">Tax Rate Reference</h3>
            <p class="text-sm text-sap-gray-600 mt-1">Current tax rates and information</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% if default_rates.default_excise %}
                <div>
                    <h5 class="text-sm font-medium text-sap-gray-800 mb-3">Default Excise Duty</h5>
                    <div class="bg-sap-blue-50 rounded-lg p-3">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm text-sap-gray-700">Basic Rate:</span>
                            <span class="font-medium">{{ default_rates.default_excise.value }}%</span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm text-sap-gray-700">Education Cess:</span>
                            <span class="font-medium">{{ default_rates.default_excise.edu_cess }}%</span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm text-sap-gray-700">SHE Cess:</span>
                            <span class="font-medium">{{ default_rates.default_excise.she_cess }}%</span>
                        </div>
                        <div class="flex justify-between items-center border-t border-sap-blue-200 pt-2">
                            <span class="text-sm font-medium text-sap-gray-800">Total Rate:</span>
                            <span class="font-bold text-sap-blue-600">{{ default_rates.default_excise.total_excise_rate }}%</span>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                {% if default_rates.default_service_tax %}
                <div>
                    <h5 class="text-sm font-medium text-sap-gray-800 mb-3">Default Service Tax</h5>
                    <div class="bg-sap-green-50 rounded-lg p-3">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm text-sap-gray-700">Basic Rate:</span>
                            <span class="font-medium">{{ default_rates.default_service_tax.value }}%</span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm text-sap-gray-700">Education Cess:</span>
                            <span class="font-medium">{{ default_rates.default_service_tax.edu_cess }}%</span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm text-sap-gray-700">SHE Cess:</span>
                            <span class="font-medium">{{ default_rates.default_service_tax.she_cess }}%</span>
                        </div>
                        <div class="flex justify-between items-center border-t border-sap-green-200 pt-2">
                            <span class="text-sm font-medium text-sap-gray-800">Total Rate:</span>
                            <span class="font-bold text-sap-green-600">{{ default_rates.default_service_tax.total_excise_rate }}%</span>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <div>
                    <h5 class="text-sm font-medium text-sap-gray-800 mb-3">Common VAT Rates</h5>
                    <div class="bg-sap-orange-50 rounded-lg p-3">
                        <div class="space-y-2">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-sap-gray-700">Essential Goods:</span>
                                <span class="font-medium">4% - 5%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-sap-gray-700">Standard Rate:</span>
                                <span class="font-medium">12.5% - 14.5%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-sap-gray-700">Luxury Items:</span>
                                <span class="font-medium">20%</span>
                            </div>
                            <div class="flex justify-between items-center border-t border-sap-orange-200 pt-2">
                                <span class="text-sm font-medium text-sap-gray-800">Configured Rates:</span>
                                <span class="font-bold text-sap-orange-600">{{ available_taxes.vat_rates }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
function taxCalculatorDashboard() {
    return {
        init() {
            lucide.createIcons();
            this.loadCalculationHistory();
        },
        
        calculateVAT(rate) {
            // Quick VAT calculation
            const amount = prompt('Enter amount for VAT calculation:');
            if (amount && !isNaN(amount)) {
                const vatAmount = parseFloat(amount) * parseFloat(rate) / 100;
                const total = parseFloat(amount) + vatAmount;
                
                alert(`Amount: ₹${parseFloat(amount).toFixed(2)}\nVAT (${rate}%): ₹${vatAmount.toFixed(2)}\nTotal: ₹${total.toFixed(2)}`);
                
                this.saveCalculation({
                    type: 'VAT',
                    rate: rate,
                    amount: parseFloat(amount),
                    tax: vatAmount,
                    total: total
                });
            }
        },
        
        calculateTDS(rate) {
            // Quick TDS calculation
            const amount = prompt('Enter amount for TDS calculation:');
            if (amount && !isNaN(amount)) {
                const tdsAmount = parseFloat(amount) * parseFloat(rate) / 100;
                const net = parseFloat(amount) - tdsAmount;
                
                alert(`Gross Amount: ₹${parseFloat(amount).toFixed(2)}\nTDS (${rate}%): ₹${tdsAmount.toFixed(2)}\nNet Payable: ₹${net.toFixed(2)}`);
                
                this.saveCalculation({
                    type: 'TDS',
                    rate: rate,
                    amount: parseFloat(amount),
                    tax: tdsAmount,
                    total: net
                });
            }
        },
        
        showTDSCalculator() {
            // Navigate to TDS calculator (would be implemented)
            alert('TDS Calculator will be implemented as a separate tool.');
        },
        
        saveCalculation(calc) {
            let history = JSON.parse(localStorage.getItem('tax_calculation_history') || '[]');
            calc.timestamp = new Date().toISOString();
            history.unshift(calc);
            history = history.slice(0, 10); // Keep last 10
            
            localStorage.setItem('tax_calculation_history', JSON.stringify(history));
            this.loadCalculationHistory();
        },
        
        loadCalculationHistory() {
            const history = JSON.parse(localStorage.getItem('tax_calculation_history') || '[]');
            const container = document.getElementById('calculation-history');
            
            if (history.length === 0) {
                container.innerHTML = '<p class="text-sap-gray-500 text-center py-4">No recent calculations</p>';
                return;
            }
            
            container.innerHTML = history.map(calc => `
                <div class="flex items-center justify-between p-3 border border-sap-gray-200 rounded-lg hover:bg-sap-gray-50">
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <span class="text-sm font-medium text-sap-gray-900">${calc.type} ${calc.rate}%</span>
                            <span class="text-xs text-sap-gray-500">${new Date(calc.timestamp).toLocaleString()}</span>
                        </div>
                        <div class="text-xs text-sap-gray-600">
                            ₹${calc.amount.toFixed(0)} → ₹${calc.total.toFixed(0)}
                        </div>
                    </div>
                    <button onclick="this.parentElement.remove()" 
                            class="text-sap-gray-400 hover:text-sap-gray-600 ml-2">
                        <i data-lucide="x" class="w-4 h-4"></i>
                    </button>
                </div>
            `).join('');
            
            // Re-initialize icons
            lucide.createIcons();
        },
        
        clearHistory() {
            if (confirm('Clear all calculation history?')) {
                localStorage.removeItem('tax_calculation_history');
                this.loadCalculationHistory();
            }
        }
    }
}

function quickCalculator() {
    return {
        amount: '',
        vatRate: '',
        exciseRate: '',
        result: false,
        vatAmount: 0,
        exciseAmount: 0,
        totalAmount: 0,
        effectiveRate: 0,
        
        calculate() {
            if (!this.amount || this.amount <= 0) {
                alert('Please enter a valid amount');
                return;
            }
            
            const base = parseFloat(this.amount);
            const vat = parseFloat(this.vatRate) || 0;
            const excise = parseFloat(this.exciseRate) || 0;
            
            this.vatAmount = base * vat / 100;
            this.exciseAmount = base * excise / 100;
            this.totalAmount = base + this.vatAmount + this.exciseAmount;
            this.effectiveRate = ((this.vatAmount + this.exciseAmount) / base * 100).toFixed(2);
            
            this.result = true;
            
            // Save to history
            const calc = {
                type: 'Mixed',
                rate: `VAT:${vat}%, Excise:${excise}%`,
                amount: base,
                tax: this.vatAmount + this.exciseAmount,
                total: this.totalAmount
            };
            
            let history = JSON.parse(localStorage.getItem('tax_calculation_history') || '[]');
            calc.timestamp = new Date().toISOString();
            history.unshift(calc);
            history = history.slice(0, 10);
            localStorage.setItem('tax_calculation_history', JSON.stringify(history));
        },
        
        formatCurrency(amount) {
            return '₹' + parseFloat(amount).toFixed(2);
        }
    }
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}