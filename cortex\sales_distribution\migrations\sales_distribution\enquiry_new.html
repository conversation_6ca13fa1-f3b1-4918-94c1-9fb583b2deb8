{% extends "core/base.html" %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .section-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 12px 20px;
        border-radius: 8px 8px 0 0;
        font-weight: 600;
        margin-bottom: 0;
    }
    
    .section-body {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-top: none;
        border-radius: 0 0 8px 8px;
        padding: 24px;
    }
    
    .radio-group {
        display: flex;
        gap: 24px;
        margin-bottom: 24px;
        padding: 16px;
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
    }
    
    .radio-item {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .radio-item input[type="radio"] {
        width: 18px;
        height: 18px;
        accent-color: #3b82f6;
    }
    
    .radio-item label {
        font-weight: 500;
        color: #374151;
        cursor: pointer;
    }
    
    .grid-2 {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }
    
    .grid-3 {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 16px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-label {
        display: block;
        font-weight: 500;
        color: #374151;
        margin-bottom: 6px;
    }
    
    .required {
        color: #ef4444;
    }
    
    .btn-view {
        background: #10b981;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        cursor: pointer;
        font-weight: 500;
        transition: background-color 0.2s;
    }
    
    .btn-view:hover {
        background: #059669;
    }
    
    .btn-view:disabled {
        background: #9ca3af;
        cursor: not-allowed;
    }
    
    .autocomplete-container {
        position: relative;
    }
    
    .autocomplete-results {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #d1d5db;
        border-top: none;
        border-radius: 0 0 6px 6px;
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
    }
    
    .autocomplete-item {
        padding: 10px 12px;
        cursor: pointer;
        border-bottom: 1px solid #f3f4f6;
    }
    
    .autocomplete-item:hover {
        background: #f3f4f6;
    }
    
    .file-upload-area {
        border: 2px dashed #d1d5db;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        background: #fafafa;
        transition: border-color 0.3s;
    }
    
    .file-upload-area:hover {
        border-color: #3b82f6;
    }
    
    .file-upload-area.dragover {
        border-color: #3b82f6;
        background: #eff6ff;
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{{ page_title }}</h1>
                        <p class="text-gray-600 mt-1">Create a new customer enquiry</p>
                    </div>
                    <div class="flex space-x-3">
                        <a href="{% url 'sales_distribution:enquiry_list' %}" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                            View All Enquiries
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Form -->
        <form method="post" enctype="multipart/form-data" id="enquiry-form">
            {% csrf_token %}
            
            <!-- Customer Type Selection -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="section-header">
                    Customer Type Selection
                </div>
                <div class="section-body">
                    <div class="radio-group">
                        <div class="radio-item">
                            <input type="radio" id="radio_new" name="customer_type" value="new" checked>
                            <label for="radio_new">New Customer</label>
                        </div>
                        <div class="radio-item">
                            <input type="radio" id="radio_existing" name="customer_type" value="existing">
                            <label for="radio_existing">Existing Customer</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="section-header">
                    Customer Information
                </div>
                <div class="section-body">
                    <div class="grid-2">
                        <!-- New Customer Name -->
                        <div class="form-group" id="new-customer-name">
                            <label class="form-label" for="{{ form.customername.id_for_label }}">
                                Customer Name <span class="required">*</span>
                            </label>
                            {{ form.customername }}
                            {% if form.customername.errors %}
                                <div class="text-red-600 text-sm mt-1">
                                    {% for error in form.customername.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Existing Customer Selection -->
                        <div class="form-group autocomplete-container" id="existing-customer-select" style="display: none;">
                            <label class="form-label">
                                Select Existing Customer <span class="required">*</span>
                            </label>
                            <div class="flex gap-2">
                                <input type="text" 
                                       id="customer-search" 
                                       class="flex-1 px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="Type customer name to search..."
                                       autocomplete="off">
                                <button type="button" 
                                        id="btn-view" 
                                        class="btn-view"
                                        disabled>
                                    View Details
                                </button>
                            </div>
                            <div class="autocomplete-results" id="autocomplete-results"></div>
                        </div>

                        <!-- Contact Person -->
                        <div class="form-group">
                            <label class="form-label" for="{{ form.contactperson.id_for_label }}">
                                Contact Person
                            </label>
                            {{ form.contactperson }}
                        </div>

                        <!-- Email -->
                        <div class="form-group">
                            <label class="form-label" for="{{ form.email.id_for_label }}">
                                Email Address
                            </label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="text-red-600 text-sm mt-1">
                                    {% for error in form.email.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Customer ID (hidden, populated automatically) -->
                        <div class="form-group" style="display: none;">
                            {{ form.customerid }}
                        </div>

                        <!-- Contact Number -->
                        <div class="form-group">
                            <label class="form-label" for="{{ form.contactno.id_for_label }}">
                                Contact Number
                            </label>
                            {{ form.contactno }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Registered Office Address -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="section-header">
                    Registered Office Address
                </div>
                <div class="section-body">
                    <div class="grid-2 mb-4">
                        <div class="form-group">
                            <label class="form-label" for="{{ form.regdaddress.id_for_label }}">
                                Address <span class="required">*</span>
                            </label>
                            {{ form.regdaddress }}
                        </div>
                    </div>

                    <div class="grid-3 mb-4">
                        <div class="form-group">
                            <label class="form-label" for="{{ form.regdcountry.id_for_label }}">
                                Country
                            </label>
                            {{ form.regdcountry }}
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.regdstate.id_for_label }}">
                                State
                            </label>
                            {{ form.regdstate }}
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.regdcity.id_for_label }}">
                                City
                            </label>
                            {{ form.regdcity }}
                        </div>
                    </div>

                    <div class="grid-3">
                        <div class="form-group">
                            <label class="form-label" for="{{ form.regdpinno.id_for_label }}">
                                PIN Code
                            </label>
                            {{ form.regdpinno }}
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.regdcontactno.id_for_label }}">
                                Contact Number
                            </label>
                            {{ form.regdcontactno }}
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.regdfaxno.id_for_label }}">
                                Fax Number
                            </label>
                            {{ form.regdfaxno }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Works/Factory Address -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="section-header">
                    Works/Factory Address
                </div>
                <div class="section-body">
                    <div class="grid-2 mb-4">
                        <div class="form-group">
                            <label class="form-label" for="{{ form.workaddress.id_for_label }}">
                                Address
                            </label>
                            {{ form.workaddress }}
                        </div>
                    </div>

                    <div class="grid-3 mb-4">
                        <div class="form-group">
                            <label class="form-label" for="{{ form.workcountry.id_for_label }}">
                                Country
                            </label>
                            {{ form.workcountry }}
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.workstate.id_for_label }}">
                                State
                            </label>
                            {{ form.workstate }}
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.workcity.id_for_label }}">
                                City
                            </label>
                            {{ form.workcity }}
                        </div>
                    </div>

                    <div class="grid-3">
                        <div class="form-group">
                            <label class="form-label" for="{{ form.workpinno.id_for_label }}">
                                PIN Code
                            </label>
                            {{ form.workpinno }}
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.workcontactno.id_for_label }}">
                                Contact Number
                            </label>
                            {{ form.workcontactno }}
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.workfaxno.id_for_label }}">
                                Fax Number
                            </label>
                            {{ form.workfaxno }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Material Delivery Address -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="section-header">
                    Material Delivery Address
                </div>
                <div class="section-body">
                    <div class="grid-2 mb-4">
                        <div class="form-group">
                            <label class="form-label" for="{{ form.materialdeladdress.id_for_label }}">
                                Address
                            </label>
                            {{ form.materialdeladdress }}
                        </div>
                    </div>

                    <div class="grid-3 mb-4">
                        <div class="form-group">
                            <label class="form-label" for="{{ form.materialdelcountry.id_for_label }}">
                                Country
                            </label>
                            {{ form.materialdelcountry }}
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.materialdelstate.id_for_label }}">
                                State
                            </label>
                            {{ form.materialdelstate }}
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.materialdelcity.id_for_label }}">
                                City
                            </label>
                            {{ form.materialdelcity }}
                        </div>
                    </div>

                    <div class="grid-3">
                        <div class="form-group">
                            <label class="form-label" for="{{ form.materialdelpinno.id_for_label }}">
                                PIN Code
                            </label>
                            {{ form.materialdelpinno }}
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.materialdelcontactno.id_for_label }}">
                                Contact Number
                            </label>
                            {{ form.materialdelcontactno }}
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.materialdelfaxno.id_for_label }}">
                                Fax Number
                            </label>
                            {{ form.materialdelfaxno }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Business Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="section-header">
                    Business Information
                </div>
                <div class="section-body">
                    <div class="grid-3">
                        <div class="form-group">
                            <label class="form-label" for="{{ form.juridictioncode.id_for_label }}">
                                Jurisdiction Code
                            </label>
                            {{ form.juridictioncode }}
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.commissionurate.id_for_label }}">
                                Commission Rate
                            </label>
                            {{ form.commissionurate }}
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.tinvatno.id_for_label }}">
                                TIN/VAT Number
                            </label>
                            {{ form.tinvatno }}
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.eccno.id_for_label }}">
                                ECC Number
                            </label>
                            {{ form.eccno }}
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.divn.id_for_label }}">
                                Division
                            </label>
                            {{ form.divn }}
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.tincstno.id_for_label }}">
                                TIN/CST Number
                            </label>
                            {{ form.tincstno }}
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.range.id_for_label }}">
                                Range
                            </label>
                            {{ form.range }}
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.panno.id_for_label }}">
                                PAN Number
                            </label>
                            {{ form.panno }}
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.tdscode.id_for_label }}">
                                TDS Code
                            </label>
                            {{ form.tdscode }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enquiry Details -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="section-header">
                    Enquiry Details
                </div>
                <div class="section-body">
                    <div class="grid-2">
                        <div class="form-group">
                            <label class="form-label" for="{{ form.enquiryfor.id_for_label }}">
                                Enquiry Description <span class="required">*</span>
                            </label>
                            {{ form.enquiryfor }}
                            {% if form.enquiryfor.errors %}
                                <div class="text-red-600 text-sm mt-1">
                                    {% for error in form.enquiryfor.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.remark.id_for_label }}">
                                Remarks
                            </label>
                            {{ form.remark }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- File Attachments -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="section-header">
                    File Attachments
                </div>
                <div class="section-body">
                    <div class="file-upload-area" id="file-upload-area">
                        <svg class="w-12 h-12 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        <p class="text-gray-600 text-sm mb-2">Drop files here or click to browse</p>
                        <input type="file" name="attachments" multiple id="file-input" class="hidden" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif">
                        <button type="button" onclick="document.getElementById('file-input').click()" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                            Choose Files
                        </button>
                    </div>
                    <div id="file-list" class="mt-4 space-y-2"></div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 flex justify-between items-center">
                    <a href="{% url 'sales_distribution:dashboard' %}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        Cancel
                    </a>
                    
                    <button type="submit" 
                            class="inline-flex items-center px-6 py-2 bg-blue-600 border border-transparent rounded-lg font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Submit Enquiry
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const radioNew = document.getElementById('radio_new');
    const radioExisting = document.getElementById('radio_existing');
    const newCustomerName = document.getElementById('new-customer-name');
    const existingCustomerSelect = document.getElementById('existing-customer-select');
    const customerSearch = document.getElementById('customer-search');
    const autocompleteResults = document.getElementById('autocomplete-results');
    const btnView = document.getElementById('btn-view');

    // Handle radio button changes
    radioNew.addEventListener('change', function() {
        if (this.checked) {
            newCustomerName.style.display = 'block';
            existingCustomerSelect.style.display = 'none';
            clearCustomerData();
        }
    });

    radioExisting.addEventListener('change', function() {
        if (this.checked) {
            newCustomerName.style.display = 'none';
            existingCustomerSelect.style.display = 'block';
            document.getElementById('{{ form.customername.id_for_label }}').value = '';
        }
    });

    // Customer search autocomplete
    let searchTimeout;
    customerSearch.addEventListener('input', function() {
        const searchTerm = this.value.trim();
        
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            if (searchTerm.length >= 2) {
                fetch(`{% url 'sales_distribution:customer_autocomplete' %}?q=${encodeURIComponent(searchTerm)}`)
                    .then(response => response.json())
                    .then(data => {
                        showAutocompleteResults(data.customers);
                    })
                    .catch(error => {
                        console.error('Error fetching customers:', error);
                    });
            } else {
                hideAutocompleteResults();
            }
        }, 300);
    });

    function showAutocompleteResults(customers) {
        autocompleteResults.innerHTML = '';
        
        if (customers.length > 0) {
            customers.forEach(customer => {
                const item = document.createElement('div');
                item.className = 'autocomplete-item';
                item.textContent = customer.text;
                item.addEventListener('click', function() {
                    customerSearch.value = customer.text;
                    hideAutocompleteResults();
                    btnView.disabled = false;
                    
                    // Set the customer name in the hidden field
                    document.getElementById('{{ form.customername.id_for_label }}').value = customer.text;
                });
                autocompleteResults.appendChild(item);
            });
            autocompleteResults.style.display = 'block';
        } else {
            hideAutocompleteResults();
        }
    }

    function hideAutocompleteResults() {
        autocompleteResults.style.display = 'none';
        autocompleteResults.innerHTML = '';
    }

    // View customer details
    btnView.addEventListener('click', function() {
        const customerName = customerSearch.value;
        if (customerName) {
            fetch(`{% url 'sales_distribution:get_customer_details' %}?customer_name=${encodeURIComponent(customerName)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        populateCustomerData(data.customer);
                    } else {
                        alert('Customer not found');
                    }
                })
                .catch(error => {
                    console.error('Error fetching customer details:', error);
                    alert('Error fetching customer details');
                });
        }
    });

    function populateCustomerData(customer) {
        // Registered address
        if (customer.registered_address) document.getElementById('{{ form.regdaddress.id_for_label }}').value = customer.registered_address;
        if (customer.registered_country) document.getElementById('{{ form.regdcountry.id_for_label }}').value = customer.registered_country;
        // Note: State and city dropdowns would need additional HTMX calls to populate properly
        
        // Works address
        if (customer.works_address) document.getElementById('{{ form.workaddress.id_for_label }}').value = customer.works_address;
        
        // Material delivery address
        if (customer.material_address) document.getElementById('{{ form.materialdeladdress.id_for_label }}').value = customer.material_address;
        
        // Contact information
        if (customer.contact_person) document.getElementById('{{ form.contactperson.id_for_label }}').value = customer.contact_person;
        if (customer.email) document.getElementById('{{ form.email.id_for_label }}').value = customer.email;
        if (customer.contact_no) document.getElementById('{{ form.contactno.id_for_label }}').value = customer.contact_no;
        
        // Business information
        if (customer.juridictioncode) document.getElementById('{{ form.juridictioncode.id_for_label }}').value = customer.juridictioncode;
        if (customer.commissionurate) document.getElementById('{{ form.commissionurate.id_for_label }}').value = customer.commissionurate;
        if (customer.tinvatno) document.getElementById('{{ form.tinvatno.id_for_label }}').value = customer.tinvatno;
        if (customer.eccno) document.getElementById('{{ form.eccno.id_for_label }}').value = customer.eccno;
        if (customer.divn) document.getElementById('{{ form.divn.id_for_label }}').value = customer.divn;
        if (customer.tincstno) document.getElementById('{{ form.tincstno.id_for_label }}').value = customer.tincstno;
        if (customer.range) document.getElementById('{{ form.range.id_for_label }}').value = customer.range;
        if (customer.panno) document.getElementById('{{ form.panno.id_for_label }}').value = customer.panno;
        if (customer.tdscode) document.getElementById('{{ form.tdscode.id_for_label }}').value = customer.tdscode;
    }

    function clearCustomerData() {
        // Clear all form fields when switching to new customer
        const formElements = document.querySelectorAll('#enquiry-form input[type="text"], #enquiry-form textarea, #enquiry-form select');
        formElements.forEach(element => {
            if (element.id !== '{{ form.customername.id_for_label }}' && element.id !== 'customer-search') {
                element.value = '';
            }
        });
    }

    // File upload handling
    const fileInput = document.getElementById('file-input');
    const fileList = document.getElementById('file-list');
    const fileUploadArea = document.getElementById('file-upload-area');

    fileInput.addEventListener('change', function() {
        displayFileList();
    });

    // Drag and drop functionality
    fileUploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });

    fileUploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });

    fileUploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        fileInput.files = files;
        displayFileList();
    });

    function displayFileList() {
        fileList.innerHTML = '';
        
        if (fileInput.files.length > 0) {
            Array.from(fileInput.files).forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg border';
                fileItem.innerHTML = `
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span class="text-sm text-gray-700">${file.name}</span>
                        <span class="text-xs text-gray-500 ml-2">(${formatFileSize(file.size)})</span>
                    </div>
                    <button type="button" onclick="removeFile(${index})" class="text-red-600 hover:text-red-700">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                `;
                fileList.appendChild(fileItem);
            });
        }
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Make removeFile function global
    window.removeFile = function(index) {
        const dt = new DataTransfer();
        const files = Array.from(fileInput.files);
        
        files.forEach((file, i) => {
            if (i !== index) {
                dt.items.add(file);
            }
        });
        
        fileInput.files = dt.files;
        displayFileList();
    };

    // Hide autocomplete when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.autocomplete-container')) {
            hideAutocompleteResults();
        }
    });
});
</script>
{% endblock %}