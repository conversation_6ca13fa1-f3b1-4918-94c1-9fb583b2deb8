from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from ..models import Process, Material


@login_required
def index(request):
    """Material Planning dashboard with key metrics"""
    # Get all material plans
    plans = Material.objects.all()
    
    context = {
        'total_plans': plans.count(),
        'active_plans': plans.count(),  # Simplified for existing data
        'draft_plans': 0,
        'overdue_plans': 0,
        
        'process_categories': 0,
        'total_processes': Process.objects.count(),
        'critical_processes': 0,
        
        # Recent processes
        'recent_processes': Process.objects.all()[:5],
        
        # Simplified stats
        'category_breakdown': [],
    }
    return render(request, 'material_planning/index.html', context)
