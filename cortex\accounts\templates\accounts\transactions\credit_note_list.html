<!-- accounts/templates/accounts/transactions/credit_note_list.html -->
<!-- Credit Note List View Template -->
<!-- Task Group 6: Credit & Debit Management - Credit Note List (Task 6.3) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Credit Notes - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-indigo-600 to-sap-indigo-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="minus-circle" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Credit Notes</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage credit notes for customer adjustments and refunds</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:credit_debit_dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to Dashboard
                </a>
                <a href="{% url 'accounts:credit_note_create' %}" 
                   class="bg-sap-indigo-600 hover:bg-sap-indigo-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    New Credit Note
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Search and Filter Section -->
    <div class="mb-6 bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4">
            <form method="get" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4">
                <!-- Search -->
                <div class="flex-1">
                    <label for="search" class="block text-sm font-medium text-sap-gray-700 mb-1">Search</label>
                    <input type="text" name="search" value="{{ request.GET.search }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-indigo-500 focus:border-sap-indigo-500"
                           placeholder="Search by credit note number, customer name...">
                </div>
                
                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-sap-gray-700 mb-1">Status</label>
                    <select name="status" 
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-indigo-500 focus:border-sap-indigo-500">
                        <option value="">All Status</option>
                        <option value="draft" {% if request.GET.status == 'draft' %}selected{% endif %}>Draft</option>
                        <option value="approved" {% if request.GET.status == 'approved' %}selected{% endif %}>Approved</option>
                        <option value="rejected" {% if request.GET.status == 'rejected' %}selected{% endif %}>Rejected</option>
                    </select>
                </div>
                
                <!-- Date Range -->
                <div>
                    <label for="date_from" class="block text-sm font-medium text-sap-gray-700 mb-1">From Date</label>
                    <input type="date" name="date_from" value="{{ request.GET.date_from }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-indigo-500 focus:border-sap-indigo-500">
                </div>
                
                <div>
                    <label for="date_to" class="block text-sm font-medium text-sap-gray-700 mb-1">To Date</label>
                    <input type="date" name="date_to" value="{{ request.GET.date_to }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-indigo-500 focus:border-sap-indigo-500">
                </div>
                
                <!-- Search Button -->
                <div>
                    <button type="submit" 
                            class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="search" class="w-4 h-4 inline mr-2"></i>
                        Search
                    </button>
                </div>
                
                <!-- Clear Button -->
                {% if request.GET %}
                <div>
                    <a href="{% url 'accounts:credit_note_list' %}" 
                       class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                        Clear
                    </a>
                </div>
                {% endif %}
            </form>
        </div>
    </div>

    <!-- Credit Notes Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-indigo-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="file-text" class="w-6 h-6 text-sap-indigo-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Credit Notes</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ total_count|default:0 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="check-circle" class="w-6 h-6 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Approved</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ approved_count|default:0 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-yellow-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="clock" class="w-6 h-6 text-sap-yellow-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Pending</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ pending_count|default:0 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="dollar-sign" class="w-6 h-6 text-sap-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Amount</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ total_amount|default:0|floatformat:2 }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Credit Notes Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Credit Notes</h3>
                <div class="flex items-center space-x-2">
                    <button type="button" onclick="exportCreditNotes()" 
                            class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                        Export
                    </button>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Credit Note #
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Customer
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Amount
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Date
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Reason
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for credit_note in credit_notes %}
                    <tr class="hover:bg-sap-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-sap-gray-900">
                                <a href="{% url 'accounts:credit_note_detail' credit_note.id %}" 
                                   class="text-sap-indigo-600 hover:text-sap-indigo-900">
                                    {{ credit_note.credit_note_no }}
                                </a>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-sap-gray-900">{{ credit_note.customer_name|default:"-" }}</div>
                            <div class="text-sm text-sap-gray-500">{{ credit_note.customer_code|default:"-" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-sap-gray-900">₹{{ credit_note.credit_amount|floatformat:2 }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-sap-gray-900">{{ credit_note.credit_date|date:"d M Y" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if credit_note.status == 'approved' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-green-100 text-sap-green-800">
                                Approved
                            </span>
                            {% elif credit_note.status == 'rejected' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-red-100 text-sap-red-800">
                                Rejected
                            </span>
                            {% else %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-yellow-100 text-sap-yellow-800">
                                {{ credit_note.status|default:"Draft"|title }}
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-sap-gray-900 max-w-xs truncate" title="{{ credit_note.reason }}">
                                {{ credit_note.reason|default:"-" }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <a href="{% url 'accounts:credit_note_detail' credit_note.id %}" 
                                   class="text-sap-blue-600 hover:text-sap-blue-900" title="View Details">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                </a>
                                {% if credit_note.status == 'draft' %}
                                <a href="{% url 'accounts:credit_note_edit' credit_note.id %}" 
                                   class="text-sap-green-600 hover:text-sap-green-900" title="Edit">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </a>
                                <button type="button" onclick="approveCreditNote({{ credit_note.id }})" 
                                        class="text-sap-purple-600 hover:text-sap-purple-900" title="Approve">
                                    <i data-lucide="check" class="w-4 h-4"></i>
                                </button>
                                <button type="button" onclick="deleteCreditNote({{ credit_note.id }})" 
                                        class="text-sap-red-600 hover:text-sap-red-900" title="Delete">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="px-6 py-8 text-center">
                            <div class="text-center">
                                <i data-lucide="minus-circle" class="w-12 h-12 text-sap-gray-400 mx-auto mb-4"></i>
                                <h3 class="text-sm font-medium text-sap-gray-900 mb-2">No credit notes found</h3>
                                <p class="text-sm text-sap-gray-600 mb-4">Get started by creating your first credit note.</p>
                                <a href="{% url 'accounts:credit_note_create' %}" 
                                   class="bg-sap-indigo-600 hover:bg-sap-indigo-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                                    Create Credit Note
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="px-6 py-4 border-t border-sap-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-sap-gray-700">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} entries
                </div>
                <div class="flex space-x-1">
                    {% if page_obj.has_previous %}
                    <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ page_obj.previous_page_number }}" 
                       class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-l-lg hover:bg-sap-gray-50">
                        Previous
                    </a>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <span class="px-3 py-2 text-sm font-medium text-sap-indigo-600 bg-sap-indigo-50 border border-sap-indigo-300">
                            {{ num }}
                        </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ num }}" 
                           class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 hover:bg-sap-gray-50">
                            {{ num }}
                        </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ page_obj.next_page_number }}" 
                       class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-r-lg hover:bg-sap-gray-50">
                        Next
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function exportCreditNotes() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = '?' + params.toString();
}

function approveCreditNote(id) {
    if (confirm('Are you sure you want to approve this credit note?')) {
        fetch(`/accounts/transactions/credit-notes/${id}/approve/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        }).then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Error approving credit note');
            }
        });
    }
}

function deleteCreditNote(id) {
    if (confirm('Are you sure you want to delete this credit note? This action cannot be undone.')) {
        fetch(`/accounts/transactions/credit-notes/${id}/delete/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        }).then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Error deleting credit note');
            }
        });
    }
}

// Initialize lucide icons on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}