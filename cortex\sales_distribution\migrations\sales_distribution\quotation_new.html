{% extends "core/base.html" %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{{ page_title }}</h1>
                        <p class="text-gray-600 mt-1">Create quotation from enquiry #{{ enquiry.enqid }}</p>
                    </div>
                    <div class="flex space-x-3">
                        <a href="{% url 'sales_distribution:quotation_selection' %}" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                            </svg>
                            Back to Selection
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tab Navigation (matches ASP.NET TabContainer) -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                    <button type="button" onclick="showTab(0)" 
                            class="tab-button {% if current_tab == 0 %}border-blue-500 text-blue-600{% else %}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300{% endif %} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        Customer Information
                    </button>
                    <button type="button" onclick="showTab(1)" 
                            class="tab-button {% if current_tab == 1 %}border-blue-500 text-blue-600{% else %}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300{% endif %} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        Item Details
                    </button>
                    <button type="button" onclick="showTab(2)" 
                            class="tab-button {% if current_tab == 2 %}border-blue-500 text-blue-600{% else %}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300{% endif %} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        Terms & Conditions
                    </button>
                </nav>
            </div>

            <!-- Tab Content -->
            <div class="px-6 py-6">
                <!-- Tab 0: Customer Information -->
                <div id="tab-0" class="tab-content" {% if current_tab != 0 %}style="display: none;"{% endif %}>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Enquiry ID</label>
                            <p class="text-lg font-semibold text-gray-900">{{ enquiry.enqid }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Customer Name</label>
                            <p class="text-lg font-semibold text-gray-900">{{ enquiry.customername|default:"—" }}</p>
                        </div>
                        {% if customer %}
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Customer Address</label>
                            <div class="bg-gray-50 rounded p-4">
                                <p class="text-gray-900">{{ customer.registered_address|default:"—" }}</p>
                                {% if customer.registered_city %}
                                <p class="text-gray-700">
                                    {{ customer.registered_city.cityname|default:"" }}{% if customer.registered_state %}, {{ customer.registered_state.statename }}{% endif %}{% if customer.registered_country %}, {{ customer.registered_country.countryname }}{% endif %}
                                </p>
                                {% endif %}
                                {% if customer.registered_pin %}
                                <p class="text-gray-700">PIN: {{ customer.registered_pin }}</p>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Enquiry For</label>
                            <p class="text-gray-900">{{ enquiry.enquiryfor|default:"—" }}</p>
                        </div>
                    </div>
                    
                    <div class="mt-6 flex justify-end">
                        <button type="button" onclick="nextTab()" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            Next: Item Details
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Tab 1: Item Details -->
                <div id="tab-1" class="tab-content" {% if current_tab != 1 %}style="display: none;"{% endif %}>
                    <!-- Add Item Form -->
                    <form method="post" class="bg-gray-50 rounded-lg p-4 mb-6">
                        {% csrf_token %}
                        <input type="hidden" name="action" value="add_item">
                        <input type="hidden" name="current_tab" value="1">
                        
                        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Item Description *</label>
                                <textarea name="item_desc" rows="2" required
                                          class="block w-full px-3 py-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                          placeholder="Enter item description"></textarea>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Quantity *</label>
                                <input type="number" name="quantity" step="0.001" min="0.001" required
                                       class="block w-full px-3 py-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="Qty">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Unit *</label>
                                <select name="unit" required class="block w-full px-3 py-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Select</option>
                                    {% for unit in units %}
                                        <option value="{{ unit.id }}">{{ unit.symbol|default:unit.unitname }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Rate *</label>
                                <input type="number" name="rate" step="0.01" min="0.01" required
                                       class="block w-full px-3 py-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="Rate">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Discount</label>
                                <input type="number" name="discount" step="0.01" min="0" value="0"
                                       class="block w-full px-3 py-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="Discount">
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <button type="submit" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded text-sm font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                </svg>
                                Add Item
                            </button>
                        </div>
                    </form>

                    <!-- Items List -->
                    {% if temp_items %}
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 bg-white rounded-lg shadow">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Description</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Discount</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for item in temp_items %}
                                <tr>
                                    <td class="px-4 py-3 text-sm text-gray-900">{{ item.item_desc }}</td>
                                    <td class="px-4 py-3 text-sm text-gray-900">{{ item.quantity|floatformat:3 }}</td>
                                    <td class="px-4 py-3 text-sm text-gray-900">{{ item.unit_symbol }}</td>
                                    <td class="px-4 py-3 text-sm text-gray-900">₹{{ item.rate|floatformat:2 }}</td>
                                    <td class="px-4 py-3 text-sm text-gray-900">₹{{ item.discount|floatformat:2 }}</td>
                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">₹{{ item.line_total|floatformat:2 }}</td>
                                    <td class="px-4 py-3 text-right">
                                        <form method="post" class="inline">
                                            {% csrf_token %}
                                            <input type="hidden" name="action" value="delete_item">
                                            <input type="hidden" name="current_tab" value="1">
                                            <input type="hidden" name="item_index" value="{{ forloop.counter0 }}">
                                            <button type="submit" class="text-red-600 hover:text-red-700" onclick="return confirm('Are you sure you want to delete this item?')">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-8 text-gray-500 bg-white rounded-lg shadow">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <p class="mt-2">No items added yet. Please add items using the form above.</p>
                    </div>
                    {% endif %}
                    
                    <div class="mt-6 flex justify-between">
                        <button type="button" onclick="prevTab()" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                            Previous
                        </button>
                        {% if temp_items %}
                        <button type="button" onclick="nextTab()" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            Next: Terms & Conditions
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </button>
                        {% endif %}
                    </div>
                </div>

                <!-- Tab 2: Terms & Conditions -->
                <div id="tab-2" class="tab-content" {% if current_tab != 2 %}style="display: none;"{% endif %}>
                    <form method="post" class="space-y-6">
                        {% csrf_token %}
                        <input type="hidden" name="action" value="save_quotation">
                        <input type="hidden" name="current_tab" value="2">
                        
                        <!-- Basic Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-group">
                                <label for="{{ quotation_form.quotationno.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                    Quotation Number
                                </label>
                                {{ quotation_form.quotationno }}
                                {% if quotation_form.quotationno.errors %}
                                    <div class="text-red-600 text-sm mt-1">
                                        {% for error in quotation_form.quotationno.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="form-group">
                                <label for="{{ quotation_form.duedate.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                    Due Date *
                                </label>
                                {{ quotation_form.duedate }}
                                {% if quotation_form.duedate.errors %}
                                    <div class="text-red-600 text-sm mt-1">
                                        {% for error in quotation_form.duedate.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Terms -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-group">
                                <label for="{{ quotation_form.paymentterms.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                    Payment Terms *
                                </label>
                                {{ quotation_form.paymentterms }}
                                {% if quotation_form.paymentterms.errors %}
                                    <div class="text-red-600 text-sm mt-1">
                                        {% for error in quotation_form.paymentterms.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="form-group">
                                <label for="{{ quotation_form.deliveryterms.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                    Delivery Terms *
                                </label>
                                {{ quotation_form.deliveryterms }}
                                {% if quotation_form.deliveryterms.errors %}
                                    <div class="text-red-600 text-sm mt-1">
                                        {% for error in quotation_form.deliveryterms.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="form-group">
                                <label for="{{ quotation_form.validity.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                    Validity *
                                </label>
                                {{ quotation_form.validity }}
                                {% if quotation_form.validity.errors %}
                                    <div class="text-red-600 text-sm mt-1">
                                        {% for error in quotation_form.validity.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="form-group">
                                <label for="{{ quotation_form.warrenty.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                    Warranty *
                                </label>
                                {{ quotation_form.warrenty }}
                                {% if quotation_form.warrenty.errors %}
                                    <div class="text-red-600 text-sm mt-1">
                                        {% for error in quotation_form.warrenty.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Financial Details -->
                        <div class="border-t pt-6">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">Financial Details</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                <!-- PF -->
                                <div class="form-group">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">PF *</label>
                                    <div class="flex space-x-2">
                                        <div class="w-24">
                                            {{ quotation_form.pftype }}
                                        </div>
                                        <div class="flex-1">
                                            {{ quotation_form.pf }}
                                        </div>
                                    </div>
                                    {% if quotation_form.pf.errors %}
                                        <div class="text-red-600 text-sm mt-1">
                                            {% for error in quotation_form.pf.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <!-- VAT/CST -->
                                <div class="form-group">
                                    <label for="{{ quotation_form.vatcst.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                        VAT/CST (%)
                                    </label>
                                    {{ quotation_form.vatcst }}
                                    {% if quotation_form.vatcst.errors %}
                                        <div class="text-red-600 text-sm mt-1">
                                            {% for error in quotation_form.vatcst.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <!-- Excise -->
                                <div class="form-group">
                                    <label for="{{ quotation_form.excise.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                        Excise (%)
                                    </label>
                                    {{ quotation_form.excise }}
                                    {% if quotation_form.excise.errors %}
                                        <div class="text-red-600 text-sm mt-1">
                                            {% for error in quotation_form.excise.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <!-- Octroi -->
                                <div class="form-group">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Octroi *</label>
                                    <div class="flex space-x-2">
                                        <div class="w-24">
                                            {{ quotation_form.octroitype }}
                                        </div>
                                        <div class="flex-1">
                                            {{ quotation_form.octroi }}
                                        </div>
                                    </div>
                                    {% if quotation_form.octroi.errors %}
                                        <div class="text-red-600 text-sm mt-1">
                                            {% for error in quotation_form.octroi.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <!-- Insurance -->
                                <div class="form-group">
                                    <label for="{{ quotation_form.insurance.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                        Insurance *
                                    </label>
                                    {{ quotation_form.insurance }}
                                    {% if quotation_form.insurance.errors %}
                                        <div class="text-red-600 text-sm mt-1">
                                            {% for error in quotation_form.insurance.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <!-- Freight -->
                                <div class="form-group">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Freight *</label>
                                    <div class="flex space-x-2">
                                        <div class="w-24">
                                            {{ quotation_form.freighttype }}
                                        </div>
                                        <div class="flex-1">
                                            {{ quotation_form.freight }}
                                        </div>
                                    </div>
                                    {% if quotation_form.freight.errors %}
                                        <div class="text-red-600 text-sm mt-1">
                                            {% for error in quotation_form.freight.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <!-- Other Charges -->
                                <div class="form-group">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Other Charges *</label>
                                    <div class="flex space-x-2">
                                        <div class="w-24">
                                            {{ quotation_form.otherchargestype }}
                                        </div>
                                        <div class="flex-1">
                                            {{ quotation_form.othercharges }}
                                        </div>
                                    </div>
                                    {% if quotation_form.othercharges.errors %}
                                        <div class="text-red-600 text-sm mt-1">
                                            {% for error in quotation_form.othercharges.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <!-- Transport -->
                                <div class="form-group">
                                    <label for="{{ quotation_form.transport.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                        Transport *
                                    </label>
                                    {{ quotation_form.transport }}
                                    {% if quotation_form.transport.errors %}
                                        <div class="text-red-600 text-sm mt-1">
                                            {% for error in quotation_form.transport.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <!-- Note Number -->
                                <div class="form-group">
                                    <label for="{{ quotation_form.noteno.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                        Note Number *
                                    </label>
                                    {{ quotation_form.noteno }}
                                    {% if quotation_form.noteno.errors %}
                                        <div class="text-red-600 text-sm mt-1">
                                            {% for error in quotation_form.noteno.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="mt-6">
                                <div class="form-group">
                                    <label for="{{ quotation_form.registrationno.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                        Registration Number
                                    </label>
                                    {{ quotation_form.registrationno }}
                                    {% if quotation_form.registrationno.errors %}
                                        <div class="text-red-600 text-sm mt-1">
                                            {% for error in quotation_form.registrationno.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Remarks -->
                        <div class="border-t pt-6">
                            <div class="form-group">
                                <label for="{{ quotation_form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                    Remarks
                                </label>
                                {{ quotation_form.remarks }}
                                {% if quotation_form.remarks.errors %}
                                    <div class="text-red-600 text-sm mt-1">
                                        {% for error in quotation_form.remarks.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Form Errors -->
                        {% if quotation_form.non_field_errors %}
                            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                                <div class="text-red-600 text-sm">
                                    {% for error in quotation_form.non_field_errors %}{{ error }}{% endfor %}
                                </div>
                            </div>
                        {% endif %}

                        <!-- Submit Buttons -->
                        <div class="flex justify-between pt-6 border-t">
                            <button type="button" onclick="prevTab()" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                                Previous
                            </button>
                            
                            <div class="flex space-x-3">
                                <a href="{% url 'sales_distribution:quotation_selection' %}" 
                                   class="inline-flex items-center px-6 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                    Cancel
                                </a>
                                
                                <button type="submit" 
                                        class="inline-flex items-center px-6 py-2 bg-blue-600 border border-transparent rounded-lg font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Create Quotation
                                </button>
                            </div>
                        </div>
                        
                        <!-- Hidden fields -->
                        {{ quotation_form.customerid }}
                        {{ quotation_form.enqid }}
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 0.625rem 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    
    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    }
</style>

<script>
let currentTab = {{ current_tab }};

function showTab(tabIndex) {
    // Hide all tabs
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.style.display = 'none';
    });
    
    // Remove active class from all buttons
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('border-blue-500', 'text-blue-600');
        btn.classList.add('border-transparent', 'text-gray-500');
    });
    
    // Show selected tab
    document.getElementById('tab-' + tabIndex).style.display = 'block';
    
    // Add active class to selected button
    const buttons = document.querySelectorAll('.tab-button');
    if (buttons[tabIndex]) {
        buttons[tabIndex].classList.remove('border-transparent', 'text-gray-500');
        buttons[tabIndex].classList.add('border-blue-500', 'text-blue-600');
    }
    
    currentTab = tabIndex;
}

function nextTab() {
    if (currentTab < 2) {
        showTab(currentTab + 1);
    }
}

function prevTab() {
    if (currentTab > 0) {
        showTab(currentTab - 1);
    }
}

// Initialize tab on page load
document.addEventListener('DOMContentLoaded', function() {
    showTab(currentTab);
});
</script>
{% endblock %}