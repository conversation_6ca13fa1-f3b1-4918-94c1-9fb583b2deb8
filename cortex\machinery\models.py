from django.db import models

# Create your models here.

from django.contrib.auth.models import User
from sys_admin.models import Company, FinancialYear


class JobCompletion(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    job_schedule = models.ForeignKey("JobSchedule", models.DO_NOTHING, db_column="MId")
    job_schedule_detail = models.ForeignKey("JobScheduleDetail", models.DO_NOTHING, db_column="DId")
    outputqty = models.FloatField(db_column="OutputQty")
    uom = models.IntegerField(db_column="UOM", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblMS_JobCompletion"


class JobCompletionTemp(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    mid = models.IntegerField(db_column="MId")
    did = models.IntegerField(db_column="DId")
    outputqty = models.FloatField(db_column="OutputQty")
    uom = models.IntegerField(db_column="UOM", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblMS_JobCompletion_Temp"


class JobScheduleDetail(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    job_schedule = models.ForeignKey("JobSchedule", models.DO_NOTHING, db_column="MId")
    shift = models.IntegerField(db_column="Shift", blank=True, null=True)
    machineid = models.IntegerField(db_column="MachineId")
    type = models.IntegerField(db_column="Type")
    batchno = models.FloatField(db_column="BatchNo", blank=True, null=True)
    fromdate = models.TextField(db_column="FromDate", blank=True, null=True)
    todate = models.TextField(db_column="ToDate", blank=True, null=True)
    fromtime = models.TextField(db_column="FromTime", blank=True, null=True)
    totime = models.TextField(db_column="ToTime", blank=True, null=True)
    process = models.IntegerField(db_column="Process", blank=True, null=True)
    qty = models.FloatField(db_column="Qty", blank=True, null=True)
    incharge = models.TextField(db_column="Incharge", blank=True, null=True)
    operator = models.TextField(db_column="Operator", blank=True, null=True)
    released = models.IntegerField(db_column="Released", blank=True, null=True)
    releaseddate = models.TextField(db_column="ReleasedDate", blank=True, null=True)
    releasedtime = models.TextField(db_column="ReleasedTime", blank=True, null=True)
    releasedby = models.TextField(db_column="ReleasedBy", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblMS_JobSchedule_Details"


class JobScheduleDetailTemp(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    user = models.ForeignKey(User, models.DO_NOTHING, db_column="SessionId")
    machineid = models.IntegerField(db_column="MachineId", blank=True, null=True)
    shift = models.IntegerField(db_column="Shift", blank=True, null=True)
    type = models.IntegerField(db_column="Type")
    batchno = models.FloatField(db_column="BatchNo", blank=True, null=True)
    fromdate = models.TextField(db_column="FromDate", blank=True, null=True)
    todate = models.TextField(db_column="ToDate", blank=True, null=True)
    fromtime = models.TextField(db_column="FromTime", blank=True, null=True)
    totime = models.TextField(db_column="ToTime", blank=True, null=True)
    process = models.IntegerField(db_column="Process", blank=True, null=True)
    qty = models.FloatField(db_column="Qty", blank=True, null=True)
    incharge = models.TextField(db_column="Incharge", blank=True, null=True)
    operator = models.TextField(db_column="Operator", blank=True, null=True)
    itemid = models.IntegerField(db_column="ItemId", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblMS_JobSchedule_Details_Temp"


class JobSchedule(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate")
    systime = models.TextField(db_column="SysTime")
    user = models.ForeignKey(User, models.DO_NOTHING, db_column="SessionId")
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    jobno = models.TextField(db_column="JobNo", blank=True, null=True)
    wono = models.TextField(db_column="WONo", blank=True, null=True)
    itemid = models.IntegerField(db_column="ItemId", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblMS_JobShedule_Master"


class Machine(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate")
    systime = models.TextField(db_column="SysTime")
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    user = models.ForeignKey(User, models.DO_NOTHING, db_column="SessionId")
    itemid = models.IntegerField(db_column="ItemId", blank=True, null=True)
    make = models.TextField(db_column="Make", blank=True, null=True)
    model = models.TextField(db_column="Model", blank=True, null=True)
    capacity = models.TextField(db_column="Capacity", blank=True, null=True)
    purchasedate = models.TextField(db_column="PurchaseDate", blank=True, null=True)
    suppliername = models.TextField(db_column="SupplierName", blank=True, null=True)
    cost = models.FloatField(db_column="Cost", blank=True, null=True)
    warrantyexpirydate = models.TextField(db_column="WarrantyExpiryDate", blank=True, null=True)
    lifedate = models.TextField(db_column="LifeDate", blank=True, null=True)
    receiveddate = models.TextField(db_column="ReceivedDate", blank=True, null=True)
    insurance = models.IntegerField(db_column="Insurance", blank=True, null=True)
    insuranceexpirydate = models.TextField(db_column="InsuranceExpiryDate", blank=True, null=True)
    puttouse = models.TextField(db_column="Puttouse", blank=True, null=True)
    incharge = models.TextField(db_column="Incharge", blank=True, null=True)
    location = models.TextField(db_column="Location", blank=True, null=True)
    pmdays = models.TextField(db_column="PMDays", blank=True, null=True)
    filename = models.TextField(db_column="FileName", blank=True, null=True)
    filesize = models.FloatField(db_column="FileSize", blank=True, null=True)
    contenttype = models.TextField(db_column="ContentType", blank=True, null=True)
    filedata = models.BinaryField(db_column="FileData", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblMS_Master"


class PreventiveMaintenanceDetail(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    preventive_maintenance = models.ForeignKey("PreventiveMaintenance", models.DO_NOTHING, db_column="MId")
    spareid = models.IntegerField(db_column="SpareId", blank=True, null=True)
    qty = models.FloatField(db_column="Qty", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblMS_PMBM_Details"


class PreventiveMaintenance(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate")
    systime = models.TextField(db_column="SysTime")
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId")
    user = models.ForeignKey(User, models.DO_NOTHING, db_column="SessionId", blank=True, null=True)
    machineid = models.IntegerField(db_column="MachineId")
    pmbm = models.IntegerField(db_column="PMBM", blank=True, null=True)
    fromdate = models.TextField(db_column="FromDate", blank=True, null=True)
    todate = models.TextField(db_column="ToDate", blank=True, null=True)
    fromtime = models.TextField(db_column="FromTime", blank=True, null=True)
    totime = models.TextField(db_column="ToTime", blank=True, null=True)
    nameofagency = models.TextField(db_column="NameOfAgency", blank=True, null=True)
    nameofengineer = models.TextField(db_column="NameOfEngineer", blank=True, null=True)
    nextpmdueon = models.TextField(db_column="NextPMDueOn", blank=True, null=True)
    nextbmdueon = models.TextField(db_column="NextBMDueOn", blank=True, null=True)
    remarks = models.TextField(db_column="Remarks", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblMS_PMBM_Master"


class MachineProcess(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    machine = models.ForeignKey("Machine", models.DO_NOTHING, db_column="MId")
    pid = models.IntegerField(db_column="PId")

    class Meta:
        managed = False
        db_table = "tblMS_Process"


class MachineSpare(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    machine = models.ForeignKey("Machine", models.DO_NOTHING, db_column="MId", blank=True, null=True)
    itemid = models.IntegerField(db_column="ItemId")
    qty = models.FloatField(db_column="Qty")

    class Meta:
        managed = False
        db_table = "tblMS_Spares"


class MachineSpareTemp(models.Model):
    """Temporary table for machine spare parts during creation"""
    id = models.AutoField(db_column="Id", primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    sessionid = models.CharField(db_column="SessionId", max_length=255)
    itemid = models.IntegerField(db_column="ItemId")
    qty = models.FloatField(db_column="Qty")

    class Meta:
        managed = False
        db_table = "tblMS_Spares_Temp"


class MachineProcessTemp(models.Model):
    """Temporary table for machine processes during creation"""
    id = models.AutoField(db_column="Id", primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId")
    sessionid = models.CharField(db_column="SessionId", max_length=255)
    pid = models.IntegerField(db_column="PId")

    class Meta:
        managed = False
        db_table = "tblMS_Process_Temp"
