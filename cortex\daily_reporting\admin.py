from django.contrib import admin
from .models import (
    DailyReportingTracker, DesignPlan, ManufacturingPlan, VendorPlan,
    ProjectPlanningMaster, ManPowerPlanning, ManPowerPlanningDetails,
    ProjectPlanningMainSheet, ProjectStatus
)


@admin.register(DailyReportingTracker)
class DailyReportingTrackerAdmin(admin.ModelAdmin):
    list_display = (
        'employee_name', 'department', 'wo_number', 'date_of_reporting',
        'percentage_completed', 'status', 'sys_date'
    )
    list_filter = (
        'department', 'date_of_reporting', 'percentage_completed', 'sys_date'
    )
    search_fields = (
        'employee_name', 'wo_number', 'department', 'activity', 'status'
    )
    ordering = ['-sys_date']
    date_hierarchy = 'date_of_reporting'
    
    fieldsets = (
        ('Employee Information', {
            'fields': ('employee_name', 'designation', 'department', 'date_of_reporting')
        }),
        ('Weekly Summary', {
            'fields': (
                'significant_achievements_last_week', 'activities_task_current_week',
                'activities_planned_completed', 'activities_planned_not_completed',
                'activities_unplanned_completed', 'plan_next_week'
            )
        }),
        ('Daily Activity', {
            'fields': (
                'activity_date', 'wo_number', 'activity', 'estimated_time',
                'status', 'percentage_completed', 'remarks'
            )
        }),
        ('System Fields', {
            'fields': ('comp_id', 'session_id', 'fin_year_id'),
            'classes': ('collapse',)
        })
    )
    
    readonly_fields = ('sys_date', 'sys_time')


@admin.register(DesignPlan)
class DesignPlanAdmin(admin.ModelAdmin):
    list_display = ('id', 'activities')
    search_fields = ('activities',)
    ordering = ['-id']


@admin.register(ManufacturingPlan)
class ManufacturingPlanAdmin(admin.ModelAdmin):
    list_display = (
        'wo_number', 'fixture_number', 'item_number', 
        'description', 'quantity', 'sys_date'
    )
    list_filter = ('sys_date',)
    search_fields = ('wo_number', 'item_number', 'description')
    ordering = ['-sys_date']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('wo_number', 'fixture_number', 'item_number', 'description', 'quantity')
        }),
        ('Drawing Release', {
            'fields': ('detailing', 'tpl_entry', 'flame_cut')
        }),
        ('Manufacturing Process', {
            'fields': (
                'cutting_flame_cut', 'channel', 'raw_material_list',
                'raw_material_receive', 'fabrication', 'sr', 'machining',
                'tapping', 'painting'
            )
        })
    )


@admin.register(VendorPlan)
class VendorPlanAdmin(admin.ModelAdmin):
    list_display = (
        'wo_number', 'fixture_number', 'serial_number',
        'number_parts_manufacturing', 'planning', 'sys_date'
    )
    list_filter = ('sys_date',)
    search_fields = ('wo_number', 'fixture_number', 'serial_number')
    ordering = ['-sys_date']
    
    fieldsets = (
        ('Basic Information', {
            'fields': (
                'wo_number', 'serial_number', 'fixture_number',
                'number_parts_manufacturing', 'planning'
            )
        }),
        ('Manufacturing Process', {
            'fields': (
                'flame_cut_loading', 'premach_ineing', 'weldment_fabrication',
                'weldment_loading'
            )
        }),
        ('Parts Tracking', {
            'fields': (
                'number_parts_received', 'number_accepted_parts',
                'pending_mfg_parts', 'brought_parts', 'pending_bo_parts'
            )
        }),
        ('Final Processing', {
            'fields': (
                'number_pending_challan', 'number_parts_received_after_processing'
            )
        })
    )


@admin.register(ManPowerPlanning)
class ManPowerPlanningAdmin(admin.ModelAdmin):
    list_display = (
        'emp_id', 'date', 'wo_no', 'dept', 'types', 'amendment_no'
    )
    list_filter = ('date', 'dept', 'types')
    search_fields = ('wo_no', 'dept')
    ordering = ['-sys_date']


@admin.register(ManPowerPlanningDetails)
class ManPowerPlanningDetailsAdmin(admin.ModelAdmin):
    list_display = (
        'm_id', 'category', 'sub_category', 'planned_desc', 'actual_desc', 'hour'
    )
    list_filter = ('category', 'sub_category')
    search_fields = ('planned_desc', 'actual_desc')


# Read-only admin for existing Project Management tables
@admin.register(ProjectPlanningMaster)
class ProjectPlanningMasterAdmin(admin.ModelAdmin):
    list_display = ('wo_no', 'sys_date', 'comp_id', 'fin_year_id', 'file_name')
    list_filter = ('sys_date', 'comp_id')
    search_fields = ('wo_no', 'file_name')
    # readonly_fields handled by has_change_permission = False
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
    
    def has_delete_permission(self, request, obj=None):
        return False


@admin.register(ProjectPlanningMainSheet)
class ProjectPlanningMainSheetAdmin(admin.ModelAdmin):
    list_display = (
        'project_no', 'project_title', 'customer_name', 
        'project_leader', 'sys_date'
    )
    list_filter = ('sys_date',)
    search_fields = ('project_no', 'project_title', 'customer_name')
    # readonly_fields handled by has_change_permission = False
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
    
    def has_delete_permission(self, request, obj=None):
        return False


@admin.register(ProjectStatus)
class ProjectStatusAdmin(admin.ModelAdmin):
    list_display = ('wo_no', 'activity', 'sys_date')
    list_filter = ('sys_date',)
    search_fields = ('wo_no', 'activity')
    # readonly_fields handled by has_change_permission = False
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
    
    def has_delete_permission(self, request, obj=None):
        return False