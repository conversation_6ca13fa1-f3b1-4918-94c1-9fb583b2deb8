<!-- SAP S/4 HANA Inspired Customer Table -->
<div class="overflow-hidden">
    <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <!-- Serial Number -->
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div class="flex items-center space-x-1">
                        <span>SN</span>
                    </div>
                </th>
                <!-- Financial Year -->
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div class="flex items-center space-x-1">
                        <span>Fin Year</span>
                        <i data-lucide="arrow-up-down" class="w-3 h-3 text-gray-400 cursor-pointer hover:text-gray-600"></i>
                    </div>
                </th>
                <!-- Customer Name -->
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div class="flex items-center space-x-1">
                        <span>Customer Name</span>
                        <i data-lucide="arrow-up-down" class="w-3 h-3 text-gray-400 cursor-pointer hover:text-gray-600"></i>
                    </div>
                </th>
                <!-- Customer Code -->
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div class="flex items-center space-x-1">
                        <span>Code</span>
                        <i data-lucide="arrow-up-down" class="w-3 h-3 text-gray-400 cursor-pointer hover:text-gray-600"></i>
                    </div>
                </th>
                <!-- Address -->
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div class="flex items-center space-x-1">
                        <span>Address</span>
                    </div>
                </th>
                <!-- Created Date -->
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div class="flex items-center space-x-1">
                        <span>Created</span>
                        <i data-lucide="arrow-up-down" class="w-3 h-3 text-gray-400 cursor-pointer hover:text-gray-600"></i>
                    </div>
                </th>
                <!-- Created By -->
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div class="flex items-center space-x-1">
                        <span>Created By</span>
                    </div>
                </th>
                <!-- Actions -->
                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <span>Actions</span>
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for customer in customers %}
            <tr class="hover:bg-gray-50 transition-colors duration-150">
                <!-- Serial Number -->
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                    {% if page_obj %}
                        {{ forloop.counter|add:page_obj.start_index|add:"-1" }}
                    {% else %}
                        {{ forloop.counter }}
                    {% endif %}
                </td>
                
                <!-- Financial Year -->
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                    {% if customer.finyearid %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {{ customer.finyearid.finyear }}
                        </span>
                    {% else %}
                        <span class="text-gray-400">-</span>
                    {% endif %}
                </td>
                
                <!-- Customer Name -->
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <i data-lucide="user" class="w-4 h-4 text-blue-600"></i>
                        </div>
                        <div>
                            {% if customer.customer_name %}
                                <a href="{% url 'sales_distribution:customer_edit' customer.salesid %}" 
                                   class="text-sm font-medium text-blue-600 hover:text-blue-700 hover:underline"
                                   title="Edit Customer: {{ customer.customer_name }}">
                                    {{ customer.customer_name }}
                                </a>
                                <div class="text-xs text-gray-500">ID: {{ customer.salesid }}</div>
                            {% else %}
                                <span class="text-sm text-gray-400">Unnamed Customer</span>
                            {% endif %}
                        </div>
                    </div>
                </td>
                
                <!-- Customer Code -->
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                    {% if customer.customerid %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                            {{ customer.customerid }}
                        </span>
                    {% else %}
                        <span class="text-gray-400">-</span>
                    {% endif %}
                </td>
                
                <!-- Address -->
                <td class="px-6 py-4 text-sm text-gray-600">
                    <div class="max-w-xs truncate" title="{{ customer.full_address }}">
                        {% if customer.full_address %}
                            {{ customer.full_address }}
                        {% else %}
                            <span class="text-gray-400">No address provided</span>
                        {% endif %}
                    </div>
                </td>
                
                <!-- Created Date -->
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                    {% if customer.sysdate %}
                        {{ customer.sysdate }}
                    {% else %}
                        <span class="text-gray-400">-</span>
                    {% endif %}
                </td>
                
                <!-- Created By -->
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                    {% if customer.sessionid %}
                        <div class="max-w-24 truncate" title="{{ customer.sessionid }}">
                            {{ customer.sessionid }}
                        </div>
                    {% else %}
                        <span class="text-gray-400">-</span>
                    {% endif %}
                </td>
                
                <!-- Actions -->
                <td class="px-6 py-4 whitespace-nowrap text-center">
                    <div class="flex items-center justify-center space-x-2">
                        <!-- View Button -->
                        <button type="button"
                                title="View Customer Details"
                                class="inline-flex items-center p-1.5 border border-green-300 rounded-lg text-green-600 bg-green-50 hover:bg-green-100 transition-colors duration-200">
                            <i data-lucide="eye" class="w-3 h-3"></i>
                        </button>
                        
                        <!-- Edit Button -->
                        <a href="{% url 'sales_distribution:customer_edit' customer.salesid %}"
                           title="Edit Customer"
                           class="inline-flex items-center p-1.5 border border-blue-300 rounded-lg text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors duration-200">
                            <i data-lucide="edit-2" class="w-3 h-3"></i>
                        </a>
                        
                        <!-- Delete Button -->
                        <button type="button"
                                onclick="confirmDelete('{{ customer.customer_name|default:'this customer' }}', '{{ customer.salesid }}')"
                                title="Delete Customer"
                                class="inline-flex items-center p-1.5 border border-red-300 rounded-lg text-red-600 bg-red-50 hover:bg-red-100 transition-colors duration-200">
                            <i data-lucide="trash-2" class="w-3 h-3"></i>
                        </button>
                    </div>
                </td>
            </tr>
            {% empty %}
            <!-- Empty State - SAP Style -->
            <tr class="empty-state">
                <td colspan="8" class="px-6 py-16 text-center">
                    <div class="flex flex-col items-center justify-center">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                            <i data-lucide="users" class="w-8 h-8 text-gray-400"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No Customers Found</h3>
                        <p class="text-sm text-gray-500 mb-6">
                            {% if request.GET.search %}
                                No customers match your search criteria "{{ request.GET.search }}". Try adjusting your search terms.
                            {% else %}
                                Get started by adding your first customer to the system.
                            {% endif %}
                        </p>
                        {% if request.GET.search %}
                            <a href="{% url 'sales_distribution:customer_list' %}" 
                               class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <i data-lucide="x" class="w-4 h-4 mr-2"></i>
                                Clear Search
                            </a>
                        {% else %}
                            <a href="{% url 'sales_distribution:customer_new' %}" 
                               class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                Add First Customer
                            </a>
                        {% endif %}
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- SAP Style Pagination -->
{% if page_obj.has_other_pages %}
    <div class="bg-white border-t border-gray-200 px-6 py-3">
        <div class="flex items-center justify-between">
            <div class="flex items-center text-sm text-gray-500">
                <span>
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} entries
                </span>
            </div>
            <div class="flex items-center space-x-2">
                <!-- Previous Page -->
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.filter %}&filter={{ request.GET.filter }}{% endif %}" 
                       class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded hover:bg-gray-50 hover:text-gray-700">
                        <i data-lucide="chevron-left" class="w-4 h-4 mr-1"></i>
                        Previous
                    </a>
                {% else %}
                    <span class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-white border border-gray-300 rounded cursor-not-allowed">
                        <i data-lucide="chevron-left" class="w-4 h-4 mr-1"></i>
                        Previous
                    </span>
                {% endif %}
                
                <!-- Page Numbers -->
                <div class="flex items-center space-x-1">
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <span class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600 rounded">
                                {{ num }}
                            </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <a href="?page={{ num }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.filter %}&filter={{ request.GET.filter }}{% endif %}" 
                               class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded hover:bg-gray-50 hover:text-gray-700">
                                {{ num }}
                            </a>
                        {% endif %}
                    {% endfor %}
                </div>
                
                <!-- Next Page -->
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.filter %}&filter={{ request.GET.filter }}{% endif %}" 
                       class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded hover:bg-gray-50 hover:text-gray-700">
                        Next
                        <i data-lucide="chevron-right" class="w-4 h-4 ml-1"></i>
                    </a>
                {% else %}
                    <span class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-white border border-gray-300 rounded cursor-not-allowed">
                        Next
                        <i data-lucide="chevron-right" class="w-4 h-4 ml-1"></i>
                    </span>
                {% endif %}
            </div>
        </div>
    </div>
{% endif %}

<script>
// Reinitialize Lucide icons for dynamically loaded content
if (typeof lucide !== 'undefined') {
    lucide.createIcons();
}

// Confirm delete function
function confirmDelete(customerName, customerId) {
    if (confirm(`Are you sure you want to delete customer "${customerName}"? This action cannot be undone.`)) {
        // Add actual delete functionality here
        console.log('Delete customer:', customerId);
        // You can implement HTMX delete request here
    }
}
</script>