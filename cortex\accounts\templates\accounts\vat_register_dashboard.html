<!-- accounts/templates/accounts/vat_register_dashboard.html -->
<!-- VAT Register Dashboard Template -->
<!-- Task Group 4: Taxation Management - VAT Register Dashboard -->

{% extends 'core/base.html' %}
{% load static %}

{% block title %}VAT Register Dashboard - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-blue-500 to-sap-blue-600 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="receipt-text" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">VAT Register Dashboard</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Comprehensive VAT reporting and analysis</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:sales_vat_register' %}" 
                   class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="trending-up" class="w-4 h-4 inline mr-2"></i>
                    Sales VAT Register
                </a>
                <a href="{% url 'accounts:purchase_vat_register' %}" 
                   class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="trending-down" class="w-4 h-4 inline mr-2"></i>
                    Purchase VAT Register
                </a>
                <a href="{% url 'accounts:vat_return_form' %}" 
                   class="bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="file-text" class="w-4 h-4 inline mr-2"></i>
                    VAT Return
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6 space-y-6" x-data="vatDashboard()">
    
    <!-- Period Selection -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <h3 class="text-lg font-medium text-sap-gray-800">Period Selection</h3>
            <p class="text-sm text-sap-gray-600 mt-1">Select period for VAT analysis</p>
        </div>
        <div class="p-6">
            <form hx-get="{% url 'accounts:ajax_get_vat_summary' %}" hx-target="#vat-summary" hx-trigger="change" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                {% csrf_token %}
                <div>
                    <label for="from_date" class="block text-sm font-medium text-sap-gray-700 mb-2">From Date</label>
                    <input type="date" name="from_date" id="from_date" 
                           value="{{ current_month_summary.period|date:'Y-m-01' }}"
                           class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                </div>
                <div>
                    <label for="to_date" class="block text-sm font-medium text-sap-gray-700 mb-2">To Date</label>
                    <input type="date" name="to_date" id="to_date" 
                           value="{{ current_month_summary.period|date:'Y-m-d' }}"
                           class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                </div>
                <div>
                    <label for="company_id" class="block text-sm font-medium text-sap-gray-700 mb-2">Company</label>
                    <select name="company_id" id="company_id" 
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        {% for company in companies %}
                        <option value="{{ company.id }}" {% if company.id == current_company.id %}selected{% endif %}>
                            {{ company.company_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <label for="financial_year_id" class="block text-sm font-medium text-sap-gray-700 mb-2">Financial Year</label>
                    <select name="financial_year_id" id="financial_year_id" 
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        {% for fy in financial_years %}
                        <option value="{{ fy.id }}" {% if fy.id == current_fy.id %}selected{% endif %}>
                            {{ fy.year_code }} ({{ fy.start_date|date:'d/m/Y' }} - {{ fy.end_date|date:'d/m/Y' }})
                        </option>
                        {% endfor %}
                    </select>
                </div>
            </form>
        </div>
    </div>

    <!-- VAT Summary Cards -->
    <div id="vat-summary">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Current Month Sales VAT -->
            <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm overflow-hidden">
                <div class="bg-gradient-to-r from-sap-blue-500 to-sap-blue-600 px-6 py-4">
                    <div class="flex items-center">
                        <i data-lucide="trending-up" class="w-8 h-8 text-white mr-3"></i>
                        <div class="text-white">
                            <p class="text-sm font-medium opacity-90">Sales VAT (Output)</p>
                            <p class="text-xs opacity-75">{{ current_month_summary.period }}</p>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4">
                    <div class="text-2xl font-bold text-sap-gray-900 mb-2">
                        ₹{{ current_month_summary.sales.total_vat|floatformat:2 }}
                    </div>
                    <div class="text-sm text-sap-gray-600">
                        {{ current_month_summary.sales.count }} invoices
                    </div>
                    <div class="text-xs text-sap-gray-500 mt-1">
                        Basic: ₹{{ current_month_summary.sales.total_basic|floatformat:2 }}
                    </div>
                </div>
                <div class="px-6 py-3 bg-sap-gray-50 border-t border-sap-gray-100">
                    <a href="{% url 'accounts:sales_vat_register' %}" 
                       class="text-sap-blue-600 hover:text-sap-blue-700 text-sm font-medium">
                        View Details <i data-lucide="arrow-right" class="w-3 h-3 inline ml-1"></i>
                    </a>
                </div>
            </div>

            <!-- Current Month Purchase VAT -->
            <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm overflow-hidden">
                <div class="bg-gradient-to-r from-sap-green-500 to-sap-green-600 px-6 py-4">
                    <div class="flex items-center">
                        <i data-lucide="trending-down" class="w-8 h-8 text-white mr-3"></i>
                        <div class="text-white">
                            <p class="text-sm font-medium opacity-90">Purchase VAT (Input)</p>
                            <p class="text-xs opacity-75">{{ current_month_summary.period }}</p>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4">
                    <div class="text-2xl font-bold text-sap-gray-900 mb-2">
                        ₹{{ current_month_summary.purchases.total_vat|floatformat:2 }}
                    </div>
                    <div class="text-sm text-sap-gray-600">
                        {{ current_month_summary.purchases.count }} bills
                    </div>
                    <div class="text-xs text-sap-gray-500 mt-1">
                        Basic: ₹{{ current_month_summary.purchases.total_basic|floatformat:2 }}
                    </div>
                </div>
                <div class="px-6 py-3 bg-sap-gray-50 border-t border-sap-gray-100">
                    <a href="{% url 'accounts:purchase_vat_register' %}" 
                       class="text-sap-green-600 hover:text-sap-green-700 text-sm font-medium">
                        View Details <i data-lucide="arrow-right" class="w-3 h-3 inline ml-1"></i>
                    </a>
                </div>
            </div>

            <!-- Net VAT Liability -->
            <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm overflow-hidden">
                <div class="bg-gradient-to-r from-sap-orange-500 to-sap-orange-600 px-6 py-4">
                    <div class="flex items-center">
                        <i data-lucide="calculator" class="w-8 h-8 text-white mr-3"></i>
                        <div class="text-white">
                            <p class="text-sm font-medium opacity-90">Net VAT Liability</p>
                            <p class="text-xs opacity-75">{{ current_month_summary.period }}</p>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4">
                    <div class="text-2xl font-bold mb-2 {% if current_month_summary.net_vat_liability >= 0 %}text-sap-red-600{% else %}text-sap-green-600{% endif %}">
                        ₹{{ current_month_summary.net_vat_liability|floatformat:2 }}
                    </div>
                    <div class="text-sm text-sap-gray-600">
                        {% if current_month_summary.net_vat_liability >= 0 %}
                            Payable to Government
                        {% else %}
                            Refundable from Government
                        {% endif %}
                    </div>
                    <div class="text-xs text-sap-gray-500 mt-1">
                        Turnover: ₹{{ current_month_summary.total_turnover|floatformat:2 }}
                    </div>
                </div>
                <div class="px-6 py-3 bg-sap-gray-50 border-t border-sap-gray-100">
                    <a href="{% url 'accounts:vat_return_form' %}" 
                       class="text-sap-orange-600 hover:text-sap-orange-700 text-sm font-medium">
                        File Return <i data-lucide="arrow-right" class="w-3 h-3 inline ml-1"></i>
                    </a>
                </div>
            </div>

            <!-- Quarterly Summary -->
            <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm overflow-hidden">
                <div class="bg-gradient-to-r from-sap-gray-600 to-sap-gray-700 px-6 py-4">
                    <div class="flex items-center">
                        <i data-lucide="calendar" class="w-8 h-8 text-white mr-3"></i>
                        <div class="text-white">
                            <p class="text-sm font-medium opacity-90">Quarterly VAT</p>
                            <p class="text-xs opacity-75">{{ quarterly_summary.period }}</p>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4">
                    <div class="text-2xl font-bold mb-2 {% if quarterly_summary.net_vat_liability >= 0 %}text-sap-red-600{% else %}text-sap-green-600{% endif %}">
                        ₹{{ quarterly_summary.net_vat_liability|floatformat:2 }}
                    </div>
                    <div class="text-sm text-sap-gray-600">
                        Sales: ₹{{ quarterly_summary.sales.total_vat|floatformat:0 }} | 
                        Purchase: ₹{{ quarterly_summary.purchases.total_vat|floatformat:0 }}
                    </div>
                    <div class="text-xs text-sap-gray-500 mt-1">
                        {{ quarterly_summary.sales.count|add:quarterly_summary.purchases.count }} transactions
                    </div>
                </div>
                <div class="px-6 py-3 bg-sap-gray-50 border-t border-sap-gray-100">
                    <button @click="generateQuarterlyReport" 
                            class="text-sap-gray-600 hover:text-sap-gray-700 text-sm font-medium">
                        Quarterly Report <i data-lucide="arrow-right" class="w-3 h-3 inline ml-1"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <h3 class="text-lg font-medium text-sap-gray-800">Quick Actions</h3>
            <p class="text-sm text-sap-gray-600 mt-1">Common VAT operations</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <a href="{% url 'accounts:sales_vat_register' %}" 
                   class="flex items-center p-4 border border-sap-gray-200 rounded-lg hover:bg-sap-blue-50 hover:border-sap-blue-300 transition-all duration-200">
                    <div class="w-10 h-10 bg-sap-blue-100 rounded-lg flex items-center justify-center mr-3">
                        <i data-lucide="trending-up" class="w-5 h-5 text-sap-blue-600"></i>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-sap-gray-900">Sales VAT Register</h4>
                        <p class="text-xs text-sap-gray-600">Output VAT details</p>
                    </div>
                </a>

                <a href="{% url 'accounts:purchase_vat_register' %}" 
                   class="flex items-center p-4 border border-sap-gray-200 rounded-lg hover:bg-sap-green-50 hover:border-sap-green-300 transition-all duration-200">
                    <div class="w-10 h-10 bg-sap-green-100 rounded-lg flex items-center justify-center mr-3">
                        <i data-lucide="trending-down" class="w-5 h-5 text-sap-green-600"></i>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-sap-gray-900">Purchase VAT Register</h4>
                        <p class="text-xs text-sap-gray-600">Input VAT details</p>
                    </div>
                </a>

                <a href="{% url 'accounts:vat_return_form' %}" 
                   class="flex items-center p-4 border border-sap-gray-200 rounded-lg hover:bg-sap-orange-50 hover:border-sap-orange-300 transition-all duration-200">
                    <div class="w-10 h-10 bg-sap-orange-100 rounded-lg flex items-center justify-center mr-3">
                        <i data-lucide="file-text" class="w-5 h-5 text-sap-orange-600"></i>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-sap-gray-900">VAT Return Form</h4>
                        <p class="text-xs text-sap-gray-600">Prepare VAT return</p>
                    </div>
                </a>

                <a href="{% url 'accounts:composite_tax_calculator' %}" 
                   class="flex items-center p-4 border border-sap-gray-200 rounded-lg hover:bg-sap-gray-50 hover:border-sap-gray-300 transition-all duration-200">
                    <div class="w-10 h-10 bg-sap-gray-100 rounded-lg flex items-center justify-center mr-3">
                        <i data-lucide="calculator" class="w-5 h-5 text-sap-gray-600"></i>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-sap-gray-900">Tax Calculator</h4>
                        <p class="text-xs text-sap-gray-600">Calculate VAT & taxes</p>
                    </div>
                </a>
            </div>
        </div>
    </div>

    <!-- VAT Rate Analysis -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Available VAT Rates -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800">Available VAT Rates</h3>
                <p class="text-sm text-sap-gray-600 mt-1">Configured VAT percentages</p>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    {% for vat_rate in vat_rates %}
                    <div class="flex items-center justify-between p-3 bg-sap-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-sap-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <span class="text-xs font-bold text-sap-blue-600">{{ vat_rate.vat_percentage }}%</span>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-sap-gray-900">{{ vat_rate.vat_percentage }}% VAT</p>
                                <p class="text-xs text-sap-gray-600">Rate ID: {{ vat_rate.id }}</p>
                            </div>
                        </div>
                        <button @click="analyzeVATRate({{ vat_rate.vat_percentage }})" 
                                class="text-sap-blue-600 hover:text-sap-blue-700 text-sm font-medium">
                            Analyze
                        </button>
                    </div>
                    {% empty %}
                    <p class="text-sm text-sap-gray-600 text-center py-4">No VAT rates configured</p>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800">Recent VAT Activity</h3>
                <p class="text-sm text-sap-gray-600 mt-1">Latest VAT transactions</p>
            </div>
            <div class="p-6">
                <div id="recent-activity" class="space-y-3">
                    <!-- Recent activity will be loaded via HTMX -->
                    <div class="text-center py-4 text-sap-gray-500">
                        <i data-lucide="loader" class="w-6 h-6 animate-spin mx-auto mb-2"></i>
                        Loading recent activity...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Options -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <h3 class="text-lg font-medium text-sap-gray-800">Export & Reports</h3>
            <p class="text-sm text-sap-gray-600 mt-1">Download VAT data in various formats</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button @click="exportVATData('csv')" 
                        class="flex items-center justify-center p-4 border border-sap-gray-200 rounded-lg hover:bg-sap-gray-50 transition-colors">
                    <i data-lucide="download" class="w-5 h-5 text-sap-gray-600 mr-3"></i>
                    <div class="text-center">
                        <p class="text-sm font-medium text-sap-gray-900">Export to CSV</p>
                        <p class="text-xs text-sap-gray-600">Spreadsheet format</p>
                    </div>
                </button>

                <button @click="exportVATData('pdf')" 
                        class="flex items-center justify-center p-4 border border-sap-gray-200 rounded-lg hover:bg-sap-gray-50 transition-colors">
                    <i data-lucide="file-text" class="w-5 h-5 text-sap-gray-600 mr-3"></i>
                    <div class="text-center">
                        <p class="text-sm font-medium text-sap-gray-900">Generate PDF</p>
                        <p class="text-xs text-sap-gray-600">Printable report</p>
                    </div>
                </button>

                <button @click="exportVATData('excel')" 
                        class="flex items-center justify-center p-4 border border-sap-gray-200 rounded-lg hover:bg-sap-gray-50 transition-colors">
                    <i data-lucide="table" class="w-5 h-5 text-sap-gray-600 mr-3"></i>
                    <div class="text-center">
                        <p class="text-sm font-medium text-sap-gray-900">Export to Excel</p>
                        <p class="text-xs text-sap-gray-600">Advanced analysis</p>
                    </div>
                </button>
            </div>
        </div>
    </div>

</div>
{% endblock %}

{% block extra_js %}
<script>
function vatDashboard() {
    return {
        init() {
            // Initialize dashboard
            this.loadRecentActivity();
            lucide.createIcons();
        },
        
        loadRecentActivity() {
            // Load recent VAT activity via HTMX
            htmx.ajax('GET', '/accounts/ajax/recent-vat-activity/', {
                target: '#recent-activity'
            });
        },
        
        analyzeVATRate(rate) {
            // Show VAT rate analysis
            console.log('Analyzing VAT rate:', rate);
            // Implementation for VAT rate analysis
        },
        
        generateQuarterlyReport() {
            // Generate quarterly VAT report
            const params = new URLSearchParams({
                'return_period': 'quarterly',
                'period_year': new Date().getFullYear(),
                'period_quarter': Math.ceil((new Date().getMonth() + 1) / 3),
                'company_id': document.getElementById('company_id').value,
                'financial_year_id': document.getElementById('financial_year_id').value
            });
            
            window.open(`{% url 'accounts:vat_return_form' %}?${params.toString()}`, '_blank');
        },
        
        exportVATData(format) {
            // Export VAT data in specified format
            const fromDate = document.getElementById('from_date').value;
            const toDate = document.getElementById('to_date').value;
            const companyId = document.getElementById('company_id').value;
            const financialYearId = document.getElementById('financial_year_id').value;
            
            if (!fromDate || !toDate) {
                alert('Please select a date range for export');
                return;
            }
            
            const params = new URLSearchParams({
                'from_date': fromDate,
                'to_date': toDate,
                'company_id': companyId,
                'financial_year_id': financialYearId,
                'format': format
            });
            
            // For CSV export (existing functionality)
            if (format === 'csv') {
                // Export both sales and purchase VAT
                window.open(`{% url 'accounts:export_vat_register' %}?export_type=sales&${params.toString()}`);
                setTimeout(() => {
                    window.open(`{% url 'accounts:export_vat_register' %}?export_type=purchase&${params.toString()}`);
                }, 1000);
            } else {
                // For PDF and Excel, would need additional endpoint
                alert(`${format.toUpperCase()} export functionality will be implemented.`);
            }
        }
    }
}

// Auto-refresh dashboard every 5 minutes
setInterval(() => {
    htmx.ajax('GET', '{% url "accounts:ajax_get_vat_summary" %}', {
        target: '#vat-summary',
        values: {
            'from_date': document.getElementById('from_date').value,
            'to_date': document.getElementById('to_date').value,
            'company_id': document.getElementById('company_id').value,
            'financial_year_id': document.getElementById('financial_year_id').value
        }
    });
}, 300000); // 5 minutes

// Initialize icons when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}