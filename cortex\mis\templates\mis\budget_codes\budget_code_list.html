{% extends "core/base.html" %}
{% load static %}

{% block title %}Budget Codes - MIS{% endblock %}

{% block extra_css %}
<style>
    .search-container {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        margin-bottom: 1.5rem;
    }
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 8px;
        padding: 1rem;
        text-align: center;
    }
</style>
{% endblock %}

{% block page_header %}
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Budget Codes</h1>
            <p class="text-gray-600">Manage budget classification codes</p>
        </div>
        <div class="flex space-x-3">
            <a href="{% url 'mis:dashboard' %}" 
               class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
            </a>
            <a href="{% url 'mis:budget_code_create' %}" 
               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                <i class="fas fa-plus mr-2"></i>New Budget Code
            </a>
        </div>
    </div>
{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="stats-card">
            <div class="text-2xl font-bold">{{ total_codes }}</div>
            <div class="text-sm opacity-90">Total Budget Codes</div>
        </div>
        <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="text-2xl font-bold">{{ active_allocations }}</div>
            <div class="text-sm opacity-90">Active Allocations</div>
        </div>
        <div class="stats-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            <div class="text-2xl font-bold">{{ budget_codes|length }}</div>
            <div class="text-sm opacity-90">Displayed Codes</div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="search-container">
        <form method="get" class="flex space-x-4">
            <div class="flex-1">
                <input type="text" 
                       name="search" 
                       value="{{ search_query }}"
                       placeholder="Search budget codes by description or symbol..."
                       class="block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       hx-get="{% url 'mis:budget_code_list' %}"
                       hx-trigger="keyup changed delay:500ms"
                       hx-target="#budget-codes-table"
                       hx-include="[name='search']">
            </div>
            <button type="submit" 
                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                <i class="fas fa-search mr-2"></i>Search
            </button>
            {% if search_query %}
            <a href="{% url 'mis:budget_code_list' %}" 
               class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                <i class="fas fa-times mr-2"></i>Clear
            </a>
            {% endif %}
        </form>
    </div>

    <!-- Budget Codes Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden" id="budget-codes-table">
        {% if budget_codes %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Serial No.
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Symbol
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Description
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Allocations
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for budget_code in budget_codes %}
                        <tr class="hover:bg-gray-50" id="budget-code-row-{{ budget_code.id }}">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ forloop.counter0|add:page_obj.start_index }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    {{ budget_code.symbol|default:"-" }}
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">
                                    {{ budget_code.description|default:"-" }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {% with allocation_count=budget_code.allocations.count %}
                                    {% if allocation_count > 0 %}
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                            {{ allocation_count }} allocation{{ allocation_count|pluralize }}
                                        </span>
                                    {% else %}
                                        <span class="text-gray-400">No allocations</span>
                                    {% endif %}
                                {% endwith %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <a href="{% url 'mis:budget_code_update' budget_code.pk %}" 
                                   class="text-blue-600 hover:text-blue-900">
                                    <i class="fas fa-edit mr-1"></i>Edit
                                </a>
                                {% if budget_code.allocations.count == 0 %}
                                <a href="{% url 'mis:budget_code_delete' budget_code.pk %}" 
                                   class="text-red-600 hover:text-red-900"
                                   onclick="return confirm('Are you sure you want to delete this budget code?')">
                                    <i class="fas fa-trash mr-1"></i>Delete
                                </a>
                                {% else %}
                                <span class="text-gray-400" title="Cannot delete: has allocations">
                                    <i class="fas fa-trash mr-1"></i>Delete
                                </span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" 
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Previous
                        </a>
                    {% endif %}
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" 
                           class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Next
                        </a>
                    {% endif %}
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Showing <span class="font-medium">{{ page_obj.start_index }}</span> to 
                            <span class="font-medium">{{ page_obj.end_index }}</span> of 
                            <span class="font-medium">{{ page_obj.paginator.count }}</span> results
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            {% if page_obj.has_previous %}
                                <a href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}" 
                                   class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">First</span>
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                                <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" 
                                   class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Previous</span>
                                    <i class="fas fa-angle-left"></i>
                                </a>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                                        {{ num }}
                                    </span>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <a href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}" 
                                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                        {{ num }}
                                    </a>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                                <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" 
                                   class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Next</span>
                                    <i class="fas fa-angle-right"></i>
                                </a>
                                <a href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}" 
                                   class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Last</span>
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            </div>
            {% endif %}
        {% else %}
            <div class="text-center py-12">
                <div class="text-gray-400">
                    <i class="fas fa-clipboard-list text-6xl mb-4"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Budget Codes Found</h3>
                {% if search_query %}
                    <p class="text-gray-600 mb-4">No budget codes match your search "{{ search_query }}"</p>
                    <a href="{% url 'mis:budget_code_list' %}" 
                       class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                        Clear Search
                    </a>
                {% else %}
                    <p class="text-gray-600 mb-4">Get started by creating your first budget code</p>
                    <a href="{% url 'mis:budget_code_create' %}" 
                       class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                        <i class="fas fa-plus mr-2"></i>Create Budget Code
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}