<!-- accounts/templates/accounts/masters/sundry_customer_detail.html -->
<!-- Sundry Customer Detail View Template -->
<!-- Task Package 4: Customer & Creditor Management Templates - Sundry Customer Detail -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{{ sundry_customer.customer_name }} - Customer Details{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-teal-600 to-sap-teal-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="user-check" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">{{ sundry_customer.customer_name|default:"Unknown Customer" }}</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Customer Code: {{ sundry_customer.customer_code|default:"N/A" }}</p>
                </div>
                <div class="ml-6">
                    {% if sundry_customer.is_active %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-sap-green-100 text-sap-green-800">
                            <i data-lucide="check-circle" class="w-4 h-4 mr-1"></i>
                            Active
                        </span>
                    {% else %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-sap-red-100 text-sap-red-800">
                            <i data-lucide="x-circle" class="w-4 h-4 mr-1"></i>
                            Inactive
                        </span>
                    {% endif %}
                    {% comment %} Customer lifetime value badge {% endcomment %}
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-sap-blue-100 text-sap-blue-800 ml-2">
                        <i data-lucide="trophy" class="w-3 h-3 mr-1"></i>
                        Premium Customer
                    </span>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:sundry_customer_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
                {% if can_edit %}
                <a href="{% url 'accounts:sundry_customer_edit' sundry_customer.id %}" 
                   class="bg-sap-teal-600 hover:bg-sap-teal-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="edit" class="w-4 h-4 inline mr-2"></i>
                    Edit Customer
                </a>
                <button type="button" onclick="createInvoice({{ sundry_customer.id }})"
                        class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="file-text" class="w-4 h-4 inline mr-2"></i>
                    Create Invoice
                </button>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Financial Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-teal-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="credit-card" class="w-6 h-6 text-sap-teal-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Credit Limit</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ sundry_customer.credit_limit|floatformat:2|default:"0.00" }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="trending-up" class="w-6 h-6 text-sap-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Outstanding</p>
                    <p class="text-2xl font-bold {% if sundry_customer.current_balance_type == 'Debit' %}text-sap-red-600{% else %}text-sap-green-600{% endif %}">
                        ₹{{ current_balance|floatformat:2|default:"0.00" }}
                    </p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="check-circle" class="w-6 h-6 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Available Credit</p>
                    <p class="text-2xl font-bold text-sap-green-600">₹{{ available_credit|floatformat:2|default:"0.00" }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="calendar" class="w-6 h-6 text-sap-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Payment Terms</p>
                    <p class="text-lg font-bold text-sap-gray-900">{{ sundry_customer.payment_terms|default:"Net 30" }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="star" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Credit Score</p>
                    <p class="text-2xl font-bold text-sap-blue-600">85</p>
                    <p class="text-xs text-sap-gray-500">Excellent</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        
        <!-- Main Information Panel -->
        <div class="lg:col-span-2 space-y-6">
            
            <!-- Basic Information -->
            <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                <div class="px-6 py-4 border-b border-sap-gray-100">
                    <h3 class="text-lg font-medium text-sap-gray-800">Basic Information</h3>
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">Customer Code</label>
                            <p class="text-sm text-sap-gray-900">{{ sundry_customer.customer_code|default:"N/A" }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">Customer Name</label>
                            <p class="text-sm text-sap-gray-900">{{ sundry_customer.customer_name|default:"N/A" }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">Contact Person</label>
                            <p class="text-sm text-sap-gray-900">{{ sundry_customer.contact_person|default:"N/A" }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">Opening Balance</label>
                            <p class="text-sm font-medium {% if sundry_customer.opening_balance_type == 'Debit' %}text-sap-red-600{% else %}text-sap-green-600{% endif %}">
                                ₹{{ sundry_customer.opening_balance|floatformat:2|default:"0.00" }} 
                                ({{ sundry_customer.opening_balance_type|default:"N/A" }})
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Address Information -->
            <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                <div class="px-6 py-4 border-b border-sap-gray-100">
                    <h3 class="text-lg font-medium text-sap-gray-800">Address Information</h3>
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">Address</label>
                            <p class="text-sm text-sap-gray-900">{{ sundry_customer.address|default:"N/A" }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">City</label>
                            <p class="text-sm text-sap-gray-900">{{ sundry_customer.city.city_name|default:"N/A" }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">State</label>
                            <p class="text-sm text-sap-gray-900">{{ sundry_customer.state.state_name|default:"N/A" }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">Country</label>
                            <p class="text-sm text-sap-gray-900">{{ sundry_customer.country.country_name|default:"N/A" }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">Pincode</label>
                            <p class="text-sm text-sap-gray-900">{{ sundry_customer.pincode|default:"N/A" }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tax Information -->
            <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                <div class="px-6 py-4 border-b border-sap-gray-100">
                    <h3 class="text-lg font-medium text-sap-gray-800">Tax Information</h3>
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">PAN Number</label>
                            <p class="text-sm text-sap-gray-900 font-mono">{{ sundry_customer.pan_no|default:"N/A" }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">GST Number</label>
                            <p class="text-sm text-sap-gray-900 font-mono">{{ sundry_customer.gst_no|default:"N/A" }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sales Performance Chart -->
            <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                <div class="px-6 py-4 border-b border-sap-gray-100">
                    <h3 class="text-lg font-medium text-sap-gray-800">Sales Performance</h3>
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <div class="text-center p-4 bg-sap-blue-50 rounded-lg">
                            <p class="text-2xl font-bold text-sap-blue-600">₹0.00</p>
                            <p class="text-sm text-sap-gray-600">This Month</p>
                        </div>
                        <div class="text-center p-4 bg-sap-green-50 rounded-lg">
                            <p class="text-2xl font-bold text-sap-green-600">₹0.00</p>
                            <p class="text-sm text-sap-gray-600">Last Month</p>
                        </div>
                        <div class="text-center p-4 bg-sap-purple-50 rounded-lg">
                            <p class="text-2xl font-bold text-sap-purple-600">₹0.00</p>
                            <p class="text-sm text-sap-gray-600">This Year</p>
                        </div>
                        <div class="text-center p-4 bg-sap-orange-50 rounded-lg">
                            <p class="text-2xl font-bold text-sap-orange-600">₹0.00</p>
                            <p class="text-sm text-sap-gray-600">Lifetime Value</p>
                        </div>
                    </div>
                    <!-- Placeholder for sales chart -->
                    <div class="h-64 bg-sap-gray-50 rounded-lg flex items-center justify-center">
                        <div class="text-center">
                            <i data-lucide="bar-chart-3" class="w-12 h-12 text-sap-gray-400 mx-auto mb-2"></i>
                            <p class="text-sap-gray-500">Sales Chart Placeholder</p>
                            <p class="text-xs text-sap-gray-400">Chart.js integration required</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                <div class="px-6 py-4 border-b border-sap-gray-100">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-sap-gray-800">Recent Transactions</h3>
                        <a href="{% url 'accounts:sundry_customer_transactions' sundry_customer.id %}" 
                           class="bg-sap-teal-600 hover:bg-sap-teal-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors">
                            <i data-lucide="external-link" class="w-4 h-4 inline mr-1"></i>
                            View All
                        </a>
                    </div>
                </div>
                <div class="px-6 py-4">
                    {% if sundry_customer.customer_details.all %}
                        <div class="overflow-x-auto">
                            <table class="min-w-full">
                                <thead>
                                    <tr class="border-b border-sap-gray-200">
                                        <th class="text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider py-2">Date</th>
                                        <th class="text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider py-2">Type</th>
                                        <th class="text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider py-2">Reference</th>
                                        <th class="text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider py-2">Amount</th>
                                        <th class="text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider py-2">Balance</th>
                                        <th class="text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider py-2">Status</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-sap-gray-100">
                                    {% for transaction in sundry_customer.customer_details.all|slice:":5" %}
                                    <tr>
                                        <td class="py-3 text-sm text-sap-gray-900">{{ transaction.transaction_date|date:"d M Y"|default:"N/A" }}</td>
                                        <td class="py-3 text-sm">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                                {% if transaction.transaction_type == 'Invoice' %}bg-sap-blue-100 text-sap-blue-800
                                                {% elif transaction.transaction_type == 'Payment' %}bg-sap-green-100 text-sap-green-800
                                                {% elif transaction.transaction_type == 'Credit Note' %}bg-sap-orange-100 text-sap-orange-800
                                                {% else %}bg-sap-gray-100 text-sap-gray-800{% endif %}">
                                                {{ transaction.transaction_type|default:"N/A" }}
                                            </span>
                                        </td>
                                        <td class="py-3 text-sm text-sap-gray-900">{{ transaction.reference_no|default:"N/A" }}</td>
                                        <td class="py-3 text-sm text-right">
                                            {% if transaction.debit_amount %}
                                                <span class="text-sap-red-600">₹{{ transaction.debit_amount|floatformat:2 }}</span>
                                            {% elif transaction.credit_amount %}
                                                <span class="text-sap-green-600">₹{{ transaction.credit_amount|floatformat:2 }}</span>
                                            {% else %}
                                                <span class="text-sap-gray-500">₹0.00</span>
                                            {% endif %}
                                        </td>
                                        <td class="py-3 text-sm text-right font-medium">
                                            <span class="{% if transaction.balance_type == 'Debit' %}text-sap-red-600{% else %}text-sap-green-600{% endif %}">
                                                ₹{{ transaction.balance|floatformat:2|default:"0.00" }}
                                            </span>
                                        </td>
                                        <td class="py-3 text-sm text-center">
                                            {% if transaction.transaction_type == 'Invoice' %}
                                                {% if transaction.days_outstanding and transaction.days_outstanding > 30 %}
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-sap-red-100 text-sap-red-800">
                                                        Overdue
                                                    </span>
                                                {% else %}
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-sap-yellow-100 text-sap-yellow-800">
                                                        Due
                                                    </span>
                                                {% endif %}
                                            {% else %}
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-sap-green-100 text-sap-green-800">
                                                    Cleared
                                                </span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-8">
                            <i data-lucide="list" class="w-12 h-12 text-sap-gray-400 mx-auto mb-4"></i>
                            <h3 class="text-lg font-medium text-sap-gray-900 mb-2">No transactions found</h3>
                            <p class="text-sap-gray-500">Transaction history will appear here once created.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            
            <!-- Contact Information -->
            <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                <div class="px-6 py-4 border-b border-sap-gray-100">
                    <h3 class="text-lg font-medium text-sap-gray-800">Contact Information</h3>
                </div>
                <div class="px-6 py-4 space-y-4">
                    {% if sundry_customer.email %}
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-sap-teal-100 rounded-lg flex items-center justify-center mr-3">
                            <i data-lucide="mail" class="w-4 h-4 text-sap-teal-600"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-sap-gray-900">Email</p>
                            <a href="mailto:{{ sundry_customer.email }}" class="text-sm text-sap-teal-600 hover:text-sap-teal-800">{{ sundry_customer.email }}</a>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if sundry_customer.mobile %}
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-sap-green-100 rounded-lg flex items-center justify-center mr-3">
                            <i data-lucide="phone" class="w-4 h-4 text-sap-green-600"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-sap-gray-900">Mobile</p>
                            <a href="tel:{{ sundry_customer.mobile }}" class="text-sm text-sap-green-600 hover:text-sap-green-800">{{ sundry_customer.mobile }}</a>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if sundry_customer.phone %}
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-sap-orange-100 rounded-lg flex items-center justify-center mr-3">
                            <i data-lucide="phone-call" class="w-4 h-4 text-sap-orange-600"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-sap-gray-900">Phone</p>
                            <a href="tel:{{ sundry_customer.phone }}" class="text-sm text-sap-orange-600 hover:text-sap-orange-800">{{ sundry_customer.phone }}</a>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if sundry_customer.fax %}
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-sap-purple-100 rounded-lg flex items-center justify-center mr-3">
                            <i data-lucide="printer" class="w-4 h-4 text-sap-purple-600"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-sap-gray-900">Fax</p>
                            <p class="text-sm text-sap-gray-600">{{ sundry_customer.fax }}</p>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if sundry_customer.website %}
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-sap-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <i data-lucide="globe" class="w-4 h-4 text-sap-blue-600"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-sap-gray-900">Website</p>
                            <a href="{{ sundry_customer.website }}" target="_blank" class="text-sm text-sap-blue-600 hover:text-sap-blue-800">{{ sundry_customer.website }}</a>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                <div class="px-6 py-4 border-b border-sap-gray-100">
                    <h3 class="text-lg font-medium text-sap-gray-800">Quick Actions</h3>
                </div>
                <div class="px-6 py-4 space-y-3">
                    <button type="button" onclick="createInvoice({{ sundry_customer.id }})"
                            class="w-full bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="file-text" class="w-4 h-4 inline mr-2"></i>
                        Create Invoice
                    </button>
                    <button type="button" onclick="recordPayment({{ sundry_customer.id }})"
                            class="w-full bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="credit-card" class="w-4 h-4 inline mr-2"></i>
                        Record Payment
                    </button>
                    <button type="button" onclick="sendEmail({{ sundry_customer.id }})"
                            class="w-full bg-sap-teal-600 hover:bg-sap-teal-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="mail" class="w-4 h-4 inline mr-2"></i>
                        Send Email
                    </button>
                    <a href="{% url 'accounts:sundry_customer_transactions' sundry_customer.id %}"
                       class="w-full bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors inline-block text-center">
                        <i data-lucide="list" class="w-4 h-4 inline mr-2"></i>
                        View Transactions
                    </a>
                    <button type="button" onclick="generateStatement({{ sundry_customer.id }})"
                            class="w-full bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="file-text" class="w-4 h-4 inline mr-2"></i>
                        Generate Statement
                    </button>
                </div>
            </div>

            <!-- Payment Behavior -->
            <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                <div class="px-6 py-4 border-b border-sap-gray-100">
                    <h3 class="text-lg font-medium text-sap-gray-800">Payment Behavior</h3>
                </div>
                <div class="px-6 py-4 space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-sap-gray-600">Average Payment Days</span>
                        <span class="text-sm font-medium text-sap-gray-900">28 days</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-sap-gray-600">Payment Reliability</span>
                        <span class="text-sm font-medium text-sap-green-600">95%</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-sap-gray-600">Late Payments</span>
                        <span class="text-sm font-medium text-sap-orange-600">2</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-sap-gray-600">Preferred Method</span>
                        <span class="text-sm font-medium text-sap-gray-900">Bank Transfer</span>
                    </div>
                </div>
            </div>

            <!-- Record Information -->
            <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
                <div class="px-6 py-4 border-b border-sap-gray-100">
                    <h3 class="text-lg font-medium text-sap-gray-800">Record Information</h3>
                </div>
                <div class="px-6 py-4 space-y-3">
                    <div>
                        <p class="text-sm font-medium text-sap-gray-700">Created Date</p>
                        <p class="text-sm text-sap-gray-600">{{ sundry_customer.created_date|date:"d M Y, g:i A"|default:"N/A" }}</p>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-sap-gray-700">Last Updated</p>
                        <p class="text-sm text-sap-gray-600">{{ sundry_customer.updated_date|date:"d M Y, g:i A"|default:"N/A" }}</p>
                    </div>
                    {% if sundry_customer.created_by %}
                    <div>
                        <p class="text-sm font-medium text-sap-gray-700">Created By</p>
                        <p class="text-sm text-sap-gray-600">{{ sundry_customer.created_by.get_full_name|default:sundry_customer.created_by.username }}</p>
                    </div>
                    {% endif %}
                    {% if sundry_customer.updated_by %}
                    <div>
                        <p class="text-sm font-medium text-sap-gray-700">Updated By</p>
                        <p class="text-sm text-sap-gray-600">{{ sundry_customer.updated_by.get_full_name|default:sundry_customer.updated_by.username }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Interactive Features -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    
    // Create invoice functionality
    window.createInvoice = function(customerId) {
        // Redirect to invoice creation form
        window.location.href = `/accounts/invoices/create/?customer_id=${customerId}`;
    };
    
    // Record payment functionality
    window.recordPayment = function(customerId) {
        // Redirect to payment recording form
        window.location.href = `/accounts/payments/create/?customer_id=${customerId}`;
    };
    
    // Send email functionality
    window.sendEmail = function(customerId) {
        // Redirect to email composition
        window.location.href = `/accounts/communication/email/?customer_id=${customerId}`;
    };
    
    // Generate statement functionality
    window.generateStatement = function(customerId) {
        // Open statement generation dialog or redirect
        window.open(`/accounts/reports/customer-statement/${customerId}/`, '_blank');
    };
});
</script>
{% endblock %}