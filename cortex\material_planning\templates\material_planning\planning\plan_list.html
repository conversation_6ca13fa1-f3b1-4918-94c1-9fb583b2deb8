{% extends 'material_planning/base.html' %}

{% block title %}Material Plans{% endblock %}

{% block content %}
<div class="space-y-6 animate-fade-in">
    <!-- Header Section with SAP Card Design -->
    <div class="sap-card">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-sap-gray-900 mb-2">Material Plans</h1>
                <p class="text-sap-gray-600">Manage all material planning projects and work orders</p>
                <div class="flex items-center mt-3 space-x-4">
                    <span class="sap-status-info">{{ total_plans }} Active Plans</span>
                    <span class="text-sm text-sap-gray-500">Last updated: {{ "now"|date:"M d, Y H:i" }}</span>
                </div>
            </div>
            <div class="flex space-x-3">
                <button disabled class="sap-button-secondary cursor-not-allowed opacity-50">
                    <i class="fas fa-plus mr-2"></i>
                    Create New Plan
                </button>
                <button class="sap-button-primary">
                    <i class="fas fa-chart-line mr-2"></i>
                    Analytics
                </button>
            </div>
        </div>
    </div>

    <!-- SAP-Style Quick Stats Dashboard -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Plans Tile -->
        <div class="sap-tile from-sap-blue-50 to-sap-blue-100 border-sap-blue-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-sap-blue-700 mb-1">Total Plans</p>
                    <p class="text-3xl font-bold text-sap-blue-900">{{ total_plans }}</p>
                    <p class="text-xs text-sap-blue-600 mt-1">All time</p>
                </div>
                <div class="w-12 h-12 bg-sap-blue-500 rounded-xl flex items-center justify-center">
                    <i class="fas fa-clipboard-list text-white text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Active Plans Tile -->
        <div class="sap-tile from-green-50 to-green-100 border-green-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-green-700 mb-1">Active Plans</p>
                    <p class="text-3xl font-bold text-green-900">{{ active_plans }}</p>
                    <p class="text-xs text-green-600 mt-1">In progress</p>
                </div>
                <div class="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center">
                    <i class="fas fa-play-circle text-white text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Work Orders Tile -->
        <div class="sap-tile from-orange-50 to-orange-100 border-orange-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-orange-700 mb-1">Work Orders</p>
                    <p class="text-3xl font-bold text-orange-900">{{ total_plans }}</p>
                    <p class="text-xs text-orange-600 mt-1">Total managed</p>
                </div>
                <div class="w-12 h-12 bg-orange-500 rounded-xl flex items-center justify-center">
                    <i class="fas fa-industry text-white text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Performance Tile -->
        <div class="sap-tile from-purple-50 to-purple-100 border-purple-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-purple-700 mb-1">Efficiency</p>
                    <p class="text-3xl font-bold text-purple-900">98.5%</p>
                    <p class="text-xs text-purple-600 mt-1">Performance score</p>
                </div>
                <div class="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center">
                    <i class="fas fa-chart-line text-white text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Form -->
    <div class="bg-white rounded-lg shadow p-6">
        <form method="get" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                {{ search_form.search_query }}
                {{ search_form.work_order_number }}
                {{ search_form.customer_name }}
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                {{ search_form.plan_type }}
                {{ search_form.status }}
                {{ search_form.priority }}
            </div>
            <div class="flex justify-end">
                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                    Search
                </button>
            </div>
        </form>
    </div>

    <!-- Plans List -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan Number</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan Name</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Work Order</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for plan in plans %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {{ plan.plno|default:'-' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                Material Plan #{{ plan.id }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ plan.wono|default:'-' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ plan.company.companyname|default:'-' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                    Active
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    Normal
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="{% url 'material_planning:plan_detail' plan.id %}" class="text-indigo-600 hover:text-indigo-900 mr-3">View</a>
                                <a href="{% url 'material_planning:plan_edit' plan.id %}" class="text-green-600 hover:text-green-900 mr-3">Edit</a>
                                <a href="{% url 'material_planning:plan_copy' plan.id %}" class="text-blue-600 hover:text-blue-900">Copy</a>
                            </td>
                        </tr>
                    {% empty %}
                        <tr>
                            <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                No plans found.
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            <div class="flex items-center justify-between">
                <div class="flex-1 flex justify-between sm:hidden">
                    {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Previous</a>
                    {% endif %}
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Next</a>
                    {% endif %}
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Showing <span class="font-medium">{{ page_obj.start_index }}</span> to <span class="font-medium">{{ page_obj.end_index }}</span> of <span class="font-medium">{{ page_obj.paginator.count }}</span> results
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                            {% if page_obj.has_previous %}
                                <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">Previous</a>
                            {% endif %}
                            {% if page_obj.has_next %}
                                <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">Next</a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}