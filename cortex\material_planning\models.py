from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from decimal import Decimal
from sys_admin.models import Company, FinancialYear


class MaterialDetail(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    material = models.ForeignKey("Material", models.DO_NOTHING, db_column="Mid", blank=True, null=True)
    itemid = models.IntegerField(db_column="ItemId", blank=True, null=True)
    rm = models.IntegerField(db_column="RM", blank=True, null=True)
    pro = models.IntegerField(db_column="PRO", blank=True, null=True)
    fin = models.IntegerField(db_column="FIN", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblMP_Material_Detail"


class MaterialFinish(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    material_detail = models.ForeignKey(MaterialDetail, models.DO_NOTHING, db_column="DMid")
    itemid = models.IntegerField(db_column="ItemId", blank=True, null=True)
    supplierid = models.TextField(db_column="SupplierId")
    qty = models.FloatField(db_column="Qty")
    rate = models.FloatField(db_column="Rate")
    discount = models.FloatField(db_column="Discount")
    deldate = models.TextField(db_column="DelDate")

    class Meta:
        managed = False
        db_table = "tblMP_Material_Finish"


class Material(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    sysdate = models.TextField(db_column="SysDate")
    systime = models.TextField(db_column="SysTime")
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column="CompId", related_name="material_planning_materials")
    session = models.ForeignKey(User, models.DO_NOTHING, db_column="SessionId", related_name="material_planning_materials")
    finyear = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column="FinYearId", related_name="material_planning_materials")
    plno = models.TextField(db_column="PLNo")
    wono = models.TextField(db_column="WONo")

    class Meta:
        managed = False
        db_table = "tblMP_Material_Master"


class MaterialProcess(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    material_detail = models.ForeignKey(MaterialDetail, models.DO_NOTHING, db_column="DMid", blank=True, null=True)
    itemid = models.IntegerField(db_column="ItemId", blank=True, null=True)
    supplierid = models.TextField(db_column="SupplierId", blank=True, null=True)
    qty = models.FloatField(db_column="Qty", blank=True, null=True)
    rate = models.FloatField(db_column="Rate", blank=True, null=True)
    discount = models.FloatField(db_column="Discount")
    deldate = models.TextField(db_column="DelDate", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblMP_Material_Process"


class MaterialRawMaterial(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    material_detail = models.ForeignKey(MaterialDetail, models.DO_NOTHING, db_column="DMid")
    itemid = models.IntegerField(db_column="ItemId", blank=True, null=True)
    supplierid = models.TextField(db_column="SupplierId")
    qty = models.FloatField(db_column="Qty")
    rate = models.FloatField(db_column="Rate")
    discount = models.FloatField(db_column="Discount")
    deldate = models.TextField(db_column="DelDate")

    class Meta:
        managed = False
        db_table = "tblMP_Material_RawMaterial"


class Process(models.Model):
    id = models.AutoField(db_column="Id", primary_key=True)
    processname = models.TextField(db_column="ProcessName", blank=True, null=True)
    symbol = models.TextField(db_column="Symbol", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblPln_Process_Master"


# =============================================================================
# TASK GROUP 1: PROCESS DEFINITION AND MANAGEMENT
# Enhanced process management with comprehensive validation and analytics
# =============================================================================

class ProcessCategory(models.Model):
    """Process classification categories for material planning"""
    CATEGORY_TYPES = [
        ('raw_material', 'Raw Material Process'),
        ('processing', 'Manufacturing Process'),
        ('finishing', 'Finishing Process'),
        ('assembly', 'Assembly Process'),
        ('testing', 'Testing Process'),
        ('packaging', 'Packaging Process'),
    ]
    
    id = models.AutoField(primary_key=True)
    category_code = models.CharField(max_length=20, unique=True)
    category_name = models.CharField(max_length=100)
    category_type = models.CharField(max_length=20, choices=CATEGORY_TYPES)
    description = models.TextField(blank=True, null=True)
    
    # Category specifications
    process_duration_min = models.IntegerField(default=0, help_text="Minimum process duration in minutes")
    process_duration_max = models.IntegerField(default=0, help_text="Maximum process duration in minutes")
    quality_requirements = models.TextField(blank=True, null=True, help_text="JSON object with quality specs")
    
    # Category flags
    requires_certification = models.BooleanField(default=False)
    requires_special_handling = models.BooleanField(default=False)
    environmental_impact = models.CharField(max_length=20, default='low')
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_process_category"
        ordering = ['category_code']
        
    def __str__(self):
        return f"{self.category_code}: {self.category_name}"


class ItemProcess(models.Model):
    """Enhanced manufacturing process definitions with comprehensive tracking"""
    PROCESS_STATUS = [
        ('draft', 'Draft'),
        ('under_review', 'Under Review'),
        ('approved', 'Approved'),
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('obsolete', 'Obsolete'),
    ]
    
    CAPABILITY_LEVELS = [
        ('basic', 'Basic Capability'),
        ('standard', 'Standard Capability'),
        ('advanced', 'Advanced Capability'),
        ('expert', 'Expert Capability'),
        ('specialized', 'Specialized Capability'),
    ]
    
    PROCESS_COMPLEXITY = [
        ('simple', 'Simple Process'),
        ('moderate', 'Moderate Complexity'),
        ('complex', 'Complex Process'),
        ('highly_complex', 'Highly Complex'),
        ('critical', 'Critical Process'),
    ]
    
    id = models.AutoField(primary_key=True)
    process_code = models.CharField(max_length=30, unique=True)
    process_name = models.CharField(max_length=200)
    process_category = models.ForeignKey(
        ProcessCategory, 
        on_delete=models.CASCADE,
        related_name='item_processes'
    )
    
    # Process specifications
    process_symbol = models.CharField(max_length=10, blank=True, null=True)
    process_description = models.TextField()
    technical_specification = models.TextField(blank=True, null=True)
    quality_standards = models.TextField(blank=True, null=True)
    
    # Process parameters
    standard_duration_minutes = models.IntegerField(default=0)
    setup_time_minutes = models.IntegerField(default=0)
    breakdown_time_minutes = models.IntegerField(default=0)
    process_complexity = models.CharField(max_length=20, choices=PROCESS_COMPLEXITY, default='moderate')
    capability_level = models.CharField(max_length=20, choices=CAPABILITY_LEVELS, default='standard')
    
    # Resource requirements
    required_skill_level = models.CharField(max_length=50, blank=True, null=True)
    required_equipment = models.TextField(blank=True, null=True, help_text="JSON array of required equipment")
    required_tools = models.TextField(blank=True, null=True, help_text="JSON array of required tools")
    required_materials = models.TextField(blank=True, null=True, help_text="JSON array of consumable materials")
    
    # Cost parameters
    setup_cost = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    per_unit_cost = models.DecimalField(max_digits=12, decimal_places=4, default=0)
    overhead_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    labor_cost_per_hour = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Quality parameters
    defect_rate_percentage = models.DecimalField(max_digits=5, decimal_places=3, default=0)
    rework_rate_percentage = models.DecimalField(max_digits=5, decimal_places=3, default=0)
    first_pass_yield = models.DecimalField(max_digits=5, decimal_places=2, default=100)
    quality_control_points = models.TextField(blank=True, null=True, help_text="JSON array of QC checkpoints")
    
    # Safety and environmental
    safety_requirements = models.TextField(blank=True, null=True)
    environmental_impact_level = models.CharField(max_length=20, default='low')
    hazardous_materials = models.TextField(blank=True, null=True, help_text="JSON array of hazardous materials")
    safety_certifications = models.TextField(blank=True, null=True, help_text="JSON array of required certifications")
    
    # Process workflow
    status = models.CharField(max_length=20, choices=PROCESS_STATUS, default='draft')
    parent_process = models.ForeignKey('self', on_delete=models.SET_NULL, blank=True, null=True, related_name='sub_processes')
    process_sequence = models.IntegerField(default=1)
    is_parallel_process = models.BooleanField(default=False)
    is_critical_path = models.BooleanField(default=False)
    
    # Approval and certification
    approved_by = models.CharField(max_length=100, blank=True, null=True)
    approved_date = models.DateTimeField(blank=True, null=True)
    certification_required = models.BooleanField(default=False)
    certification_valid_until = models.DateField(blank=True, null=True)
    
    # Performance tracking
    average_actual_duration = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    process_efficiency = models.DecimalField(max_digits=5, decimal_places=2, default=100)
    utilization_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    last_performance_review = models.DateField(blank=True, null=True)
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_item_process"
        ordering = ['process_sequence', 'process_code']
        
    def __str__(self):
        return f"{self.process_code}: {self.process_name}"
    
    @property
    def total_process_cost(self):
        """Calculate total process cost including setup, per unit, and overhead"""
        base_cost = self.setup_cost + self.per_unit_cost
        overhead_cost = base_cost * (self.overhead_percentage / 100)
        return base_cost + overhead_cost
    
    @property
    def efficiency_rating(self):
        """Calculate efficiency rating based on actual vs standard duration"""
        if self.average_actual_duration > 0 and self.standard_duration_minutes > 0:
            efficiency = (self.standard_duration_minutes / float(self.average_actual_duration)) * 100
            return min(efficiency, 150)  # Cap at 150% for exceptional performance
        return 100
    
    def calculate_process_lead_time(self, quantity=1):
        """Calculate total lead time for given quantity"""
        batch_setup_time = self.setup_time_minutes
        processing_time = self.standard_duration_minutes * quantity
        breakdown_buffer = self.breakdown_time_minutes
        return batch_setup_time + processing_time + breakdown_buffer


class ProcessSequence(models.Model):
    """Process order and dependencies for complex manufacturing workflows"""
    DEPENDENCY_TYPES = [
        ('start_to_start', 'Start to Start'),
        ('start_to_finish', 'Start to Finish'),
        ('finish_to_start', 'Finish to Start'),
        ('finish_to_finish', 'Finish to Finish'),
    ]
    
    id = models.AutoField(primary_key=True)
    sequence_name = models.CharField(max_length=100)
    
    # Process relationships
    predecessor_process = models.ForeignKey(
        ItemProcess, 
        on_delete=models.CASCADE,
        related_name='successor_sequences'
    )
    successor_process = models.ForeignKey(
        ItemProcess, 
        on_delete=models.CASCADE,
        related_name='predecessor_sequences'
    )
    
    # Dependency configuration
    dependency_type = models.CharField(max_length=20, choices=DEPENDENCY_TYPES, default='finish_to_start')
    lag_time_minutes = models.IntegerField(default=0)
    lead_time_minutes = models.IntegerField(default=0)
    
    # Sequence parameters
    is_mandatory = models.BooleanField(default=True)
    is_alternative_path = models.BooleanField(default=False)
    priority_level = models.IntegerField(default=1)
    
    # Conditional parameters
    condition_expression = models.TextField(blank=True, null=True, help_text="JSON object with conditions")
    quality_gate_required = models.BooleanField(default=False)
    approval_required = models.BooleanField(default=False)
    
    # Performance tracking
    sequence_efficiency = models.DecimalField(max_digits=5, decimal_places=2, default=100)
    average_transition_time = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    bottleneck_frequency = models.IntegerField(default=0)
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_process_sequence"
        ordering = ['priority_level', 'successor_process__process_sequence']
        unique_together = ['predecessor_process', 'successor_process']
        
    def __str__(self):
        return f"{self.predecessor_process.process_code} → {self.successor_process.process_code}"
    
    def calculate_critical_path_impact(self):
        """Calculate impact on critical path if this sequence is delayed"""
        if self.predecessor_process.is_critical_path and self.successor_process.is_critical_path:
            return self.lag_time_minutes + self.average_transition_time
        return 0


class ProcessParameter(models.Model):
    """Process-specific parameters and configurations"""
    PARAMETER_TYPES = [
        ('numeric', 'Numeric Value'),
        ('text', 'Text Value'),
        ('boolean', 'Boolean Flag'),
        ('list', 'List of Values'),
        ('range', 'Value Range'),
        ('formula', 'Calculated Formula'),
    ]
    
    PARAMETER_UNITS = [
        ('minutes', 'Minutes'),
        ('hours', 'Hours'),
        ('pieces', 'Pieces'),
        ('kg', 'Kilograms'),
        ('meters', 'Meters'),
        ('temperature', 'Temperature'),
        ('pressure', 'Pressure'),
        ('percentage', 'Percentage'),
        ('currency', 'Currency'),
    ]
    
    id = models.AutoField(primary_key=True)
    process = models.ForeignKey(
        ItemProcess, 
        on_delete=models.CASCADE,
        related_name='process_parameters'
    )
    
    # Parameter definition
    parameter_code = models.CharField(max_length=30)
    parameter_name = models.CharField(max_length=100)
    parameter_type = models.CharField(max_length=20, choices=PARAMETER_TYPES)
    parameter_unit = models.CharField(max_length=20, choices=PARAMETER_UNITS, blank=True, null=True)
    
    # Parameter values
    default_value = models.TextField(blank=True, null=True)
    minimum_value = models.DecimalField(max_digits=15, decimal_places=4, blank=True, null=True)
    maximum_value = models.DecimalField(max_digits=15, decimal_places=4, blank=True, null=True)
    allowed_values = models.TextField(blank=True, null=True, help_text="JSON array of allowed values")
    
    # Parameter configuration
    is_mandatory = models.BooleanField(default=True)
    is_variable = models.BooleanField(default=False)
    is_quality_parameter = models.BooleanField(default=False)
    is_cost_driver = models.BooleanField(default=False)
    
    # Validation rules
    validation_rules = models.TextField(blank=True, null=True, help_text="JSON object with validation rules")
    tolerance_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Parameter tracking
    current_value = models.TextField(blank=True, null=True)
    last_updated_value = models.DateTimeField(blank=True, null=True)
    update_frequency = models.CharField(max_length=20, default='as_needed')
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_process_parameter"
        ordering = ['process', 'parameter_code']
        unique_together = ['process', 'parameter_code']
        
    def __str__(self):
        return f"{self.process.process_code} - {self.parameter_name}"
    
    def validate_value(self, value):
        """Validate parameter value against constraints"""
        try:
            if self.parameter_type == 'numeric':
                numeric_value = Decimal(str(value))
                if self.minimum_value and numeric_value < self.minimum_value:
                    return False, f"Value {value} is below minimum {self.minimum_value}"
                if self.maximum_value and numeric_value > self.maximum_value:
                    return False, f"Value {value} is above maximum {self.maximum_value}"
            
            elif self.parameter_type == 'boolean':
                if value not in [True, False, 'true', 'false', 1, 0]:
                    return False, f"Invalid boolean value: {value}"
            
            return True, "Valid"
            
        except Exception as e:
            return False, f"Validation error: {str(e)}"


class ProcessCapability(models.Model):
    """Process capacity and capability metrics"""
    CAPABILITY_STATUS = [
        ('developing', 'Developing'),
        ('capable', 'Capable'),
        ('mature', 'Mature'),
        ('optimized', 'Optimized'),
        ('world_class', 'World Class'),
    ]
    
    id = models.AutoField(primary_key=True)
    process = models.OneToOneField(
        ItemProcess, 
        on_delete=models.CASCADE,
        related_name='capability_metrics'
    )
    
    # Capacity metrics
    theoretical_capacity_per_hour = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    practical_capacity_per_hour = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    current_utilization_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    maximum_capacity_per_day = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    
    # Quality capability
    process_capability_index = models.DecimalField(max_digits=5, decimal_places=3, default=1.000)
    process_performance_index = models.DecimalField(max_digits=5, decimal_places=3, default=1.000)
    defect_rate_ppm = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    first_pass_yield_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=100)
    
    # Process stability
    variation_coefficient = models.DecimalField(max_digits=5, decimal_places=4, default=0)
    process_stability_index = models.DecimalField(max_digits=5, decimal_places=3, default=1.000)
    control_chart_violations = models.IntegerField(default=0)
    
    # Capability assessment
    capability_status = models.CharField(max_length=20, choices=CAPABILITY_STATUS, default='capable')
    capability_assessment_date = models.DateField(default=timezone.now)
    next_assessment_due = models.DateField(blank=True, null=True)
    capability_trends = models.TextField(blank=True, null=True, help_text="JSON object with trend data")
    
    # Improvement tracking
    improvement_initiatives = models.TextField(blank=True, null=True, help_text="JSON array of improvement projects")
    target_capability_index = models.DecimalField(max_digits=5, decimal_places=3, default=1.330)
    target_achievement_date = models.DateField(blank=True, null=True)
    
    # Benchmarking
    industry_benchmark = models.DecimalField(max_digits=5, decimal_places=3, blank=True, null=True)
    best_in_class_benchmark = models.DecimalField(max_digits=5, decimal_places=3, blank=True, null=True)
    competitive_position = models.CharField(max_length=20, blank=True, null=True)
    
    # Audit fields
    assessed_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_process_capability"
        ordering = ['-capability_assessment_date']
        
    def __str__(self):
        return f"{self.process.process_code} - Cp: {self.process_capability_index}"
    
    @property
    def capacity_utilization_status(self):
        """Determine capacity utilization status"""
        if self.current_utilization_percentage >= 95:
            return 'over_utilized'
        elif self.current_utilization_percentage >= 85:
            return 'well_utilized'
        elif self.current_utilization_percentage >= 70:
            return 'adequately_utilized'
        elif self.current_utilization_percentage >= 50:
            return 'under_utilized'
        else:
            return 'significantly_under_utilized'
    
    def calculate_process_sigma_level(self):
        """Calculate process sigma level from defect rate"""
        if self.defect_rate_ppm <= 0:
            return 6.0
        
        # Simplified sigma calculation
        if self.defect_rate_ppm <= 3.4:
            return 6.0
        elif self.defect_rate_ppm <= 233:
            return 5.0
        elif self.defect_rate_ppm <= 6210:
            return 4.0
        elif self.defect_rate_ppm <= 66807:
            return 3.0
        else:
            return 2.0


class ProcessCost(models.Model):
    """Process cost structures and rates"""
    COST_TYPES = [
        ('fixed', 'Fixed Cost'),
        ('variable', 'Variable Cost'),
        ('semi_variable', 'Semi-Variable Cost'),
        ('step', 'Step Cost'),
        ('mixed', 'Mixed Cost'),
    ]
    
    COST_CATEGORIES = [
        ('labor', 'Labor Cost'),
        ('material', 'Material Cost'),
        ('equipment', 'Equipment Cost'),
        ('overhead', 'Overhead Cost'),
        ('quality', 'Quality Cost'),
        ('setup', 'Setup Cost'),
        ('maintenance', 'Maintenance Cost'),
    ]
    
    id = models.AutoField(primary_key=True)
    process = models.ForeignKey(
        ItemProcess, 
        on_delete=models.CASCADE,
        related_name='process_costs'
    )
    
    # Cost identification
    cost_element_code = models.CharField(max_length=30)
    cost_element_name = models.CharField(max_length=100)
    cost_type = models.CharField(max_length=20, choices=COST_TYPES)
    cost_category = models.CharField(max_length=20, choices=COST_CATEGORIES)
    
    # Cost values
    cost_per_unit = models.DecimalField(max_digits=12, decimal_places=4, default=0)
    cost_per_hour = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    fixed_cost_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    variable_cost_rate = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    
    # Cost allocation
    allocation_basis = models.CharField(max_length=50, blank=True, null=True)
    allocation_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=100)
    cost_center = models.CharField(max_length=50, blank=True, null=True)
    budget_account = models.CharField(max_length=50, blank=True, null=True)
    
    # Cost analysis
    standard_cost = models.DecimalField(max_digits=12, decimal_places=4, default=0)
    actual_cost = models.DecimalField(max_digits=12, decimal_places=4, default=0)
    cost_variance = models.DecimalField(max_digits=12, decimal_places=4, default=0)
    cost_variance_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Cost optimization
    target_cost = models.DecimalField(max_digits=12, decimal_places=4, default=0)
    minimum_achievable_cost = models.DecimalField(max_digits=12, decimal_places=4, default=0)
    cost_reduction_opportunities = models.TextField(blank=True, null=True, help_text="JSON array of opportunities")
    
    # Validity and tracking
    effective_from = models.DateField(default=timezone.now)
    effective_to = models.DateField(blank=True, null=True)
    cost_update_frequency = models.CharField(max_length=20, default='monthly')
    last_cost_review = models.DateField(blank=True, null=True)
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_process_cost"
        ordering = ['process', 'cost_category', 'cost_element_code']
        unique_together = ['process', 'cost_element_code']
        
    def __str__(self):
        return f"{self.process.process_code} - {self.cost_element_name}"
    
    def calculate_total_cost(self, quantity=1, duration_hours=1):
        """Calculate total cost for given quantity and duration"""
        fixed_cost = self.fixed_cost_amount
        variable_cost = self.variable_cost_rate * quantity
        time_based_cost = self.cost_per_hour * duration_hours
        unit_cost = self.cost_per_unit * quantity
        
        total = fixed_cost + variable_cost + time_based_cost + unit_cost
        allocated_cost = total * (self.allocation_percentage / 100)
        
        return allocated_cost
    
    def update_cost_variance(self):
        """Update cost variance calculations"""
        if self.standard_cost > 0:
            self.cost_variance = self.actual_cost - self.standard_cost
            self.cost_variance_percentage = (self.cost_variance / self.standard_cost) * 100
        else:
            self.cost_variance = 0
            self.cost_variance_percentage = 0


# =============================================================================
# TASK GROUP 2: MATERIAL PLANNING CREATION AND MANAGEMENT
# Core planning functionality with work order integration
# =============================================================================

class MaterialPlan(models.Model):
    """Main planning document header with comprehensive workflow management"""
    PLAN_STATUS = [
        ('draft', 'Draft'),
        ('under_review', 'Under Review'),
        ('approved', 'Approved'),
        ('execution', 'Under Execution'),
        ('completed', 'Completed'),
        ('closed', 'Closed'),
        ('cancelled', 'Cancelled'),
    ]
    
    PLAN_PRIORITY = [
        ('low', 'Low Priority'),
        ('normal', 'Normal Priority'),
        ('high', 'High Priority'),
        ('urgent', 'Urgent'),
        ('critical', 'Critical'),
    ]
    
    PLAN_TYPE = [
        ('standard', 'Standard Planning'),
        ('emergency', 'Emergency Planning'),
        ('project', 'Project Planning'),
        ('maintenance', 'Maintenance Planning'),
        ('prototype', 'Prototype Planning'),
    ]
    
    id = models.AutoField(primary_key=True)
    plan_number = models.CharField(max_length=50, unique=True)
    plan_name = models.CharField(max_length=200)
    plan_type = models.CharField(max_length=20, choices=PLAN_TYPE, default='standard')
    
    # Work order integration
    work_order_number = models.CharField(max_length=50)
    customer_name = models.CharField(max_length=200, blank=True, null=True)
    enquiry_number = models.CharField(max_length=50, blank=True, null=True)
    purchase_order_number = models.CharField(max_length=50, blank=True, null=True)
    
    # Planning specifications
    plan_description = models.TextField()
    technical_requirements = models.TextField(blank=True, null=True)
    quality_specifications = models.TextField(blank=True, null=True)
    delivery_requirements = models.TextField(blank=True, null=True)
    
    # Planning timeline
    plan_start_date = models.DateField()
    plan_end_date = models.DateField()
    required_delivery_date = models.DateField()
    planned_delivery_date = models.DateField(blank=True, null=True)
    actual_delivery_date = models.DateField(blank=True, null=True)
    
    # Planning quantities
    total_quantity = models.DecimalField(max_digits=12, decimal_places=3, default=0)
    completed_quantity = models.DecimalField(max_digits=12, decimal_places=3, default=0)
    remaining_quantity = models.DecimalField(max_digits=12, decimal_places=3, default=0)
    
    # Financial planning
    estimated_total_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    approved_budget = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    actual_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    cost_variance = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    
    # Workflow management
    status = models.CharField(max_length=20, choices=PLAN_STATUS, default='draft')
    priority = models.CharField(max_length=20, choices=PLAN_PRIORITY, default='normal')
    completion_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Risk and constraint management
    risk_level = models.CharField(max_length=20, default='medium')
    constraints = models.TextField(blank=True, null=True, help_text="JSON object with constraints")
    critical_path_items = models.TextField(blank=True, null=True, help_text="JSON array of critical items")
    
    # Planning team
    plan_manager = models.CharField(max_length=100)
    planning_team = models.TextField(blank=True, null=True, help_text="JSON array of team members")
    
    # Approval workflow
    submitted_for_approval_date = models.DateTimeField(blank=True, null=True)
    approved_by = models.CharField(max_length=100, blank=True, null=True)
    approved_date = models.DateTimeField(blank=True, null=True)
    approval_comments = models.TextField(blank=True, null=True)
    
    # Execution tracking
    execution_start_date = models.DateField(blank=True, null=True)
    execution_end_date = models.DateField(blank=True, null=True)
    last_progress_update = models.DateTimeField(blank=True, null=True)
    next_review_date = models.DateField(blank=True, null=True)
    
    # Integration references
    bom_reference = models.CharField(max_length=50, blank=True, null=True)
    project_reference = models.CharField(max_length=50, blank=True, null=True)
    contract_reference = models.CharField(max_length=50, blank=True, null=True)
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_material_plan"
        ordering = ['-created_date', 'plan_number']
        
    def __str__(self):
        return f"{self.plan_number}: {self.plan_name}"
    
    @property
    def is_overdue(self):
        """Check if plan is overdue"""
        if self.status in ['completed', 'closed', 'cancelled']:
            return False
        from django.utils import timezone
        return self.plan_end_date < timezone.now().date()
    
    @property
    def cost_variance_percentage(self):
        """Calculate cost variance percentage"""
        if self.approved_budget > 0:
            return ((self.actual_cost - self.approved_budget) / self.approved_budget) * 100
        return 0
    
    @property
    def schedule_variance_days(self):
        """Calculate schedule variance in days"""
        if self.actual_delivery_date and self.planned_delivery_date:
            return (self.actual_delivery_date - self.planned_delivery_date).days
        return 0
    
    def calculate_completion_percentage(self):
        """Calculate overall completion percentage"""
        if self.total_quantity > 0:
            return (self.completed_quantity / self.total_quantity) * 100
        return 0
    
    def update_remaining_quantity(self):
        """Update remaining quantity"""
        self.remaining_quantity = self.total_quantity - self.completed_quantity


class PlanningLineItem(models.Model):
    """Individual items in material planning with detailed specifications"""
    ITEM_TYPE = [
        ('raw_material', 'Raw Material'),
        ('component', 'Component'),
        ('assembly', 'Assembly'),
        ('service', 'Service'),
        ('tooling', 'Tooling'),
        ('consumable', 'Consumable'),
    ]
    
    ITEM_STATUS = [
        ('pending', 'Pending'),
        ('sourcing', 'Under Sourcing'),
        ('quoted', 'Quoted'),
        ('ordered', 'Ordered'),
        ('delivered', 'Delivered'),
        ('accepted', 'Accepted'),
        ('rejected', 'Rejected'),
    ]
    
    id = models.AutoField(primary_key=True)
    material_plan = models.ForeignKey(
        MaterialPlan, 
        on_delete=models.CASCADE,
        related_name='line_items'
    )
    
    # Item identification
    line_number = models.IntegerField()
    item_code = models.CharField(max_length=50)
    item_description = models.TextField()
    item_type = models.CharField(max_length=20, choices=ITEM_TYPE)
    item_category = models.CharField(max_length=50, blank=True, null=True)
    
    # Technical specifications
    technical_specification = models.TextField(blank=True, null=True)
    quality_standards = models.TextField(blank=True, null=True)
    material_grade = models.CharField(max_length=50, blank=True, null=True)
    dimensional_requirements = models.TextField(blank=True, null=True)
    
    # Quantity planning
    required_quantity = models.DecimalField(max_digits=12, decimal_places=3)
    unit_of_measure = models.CharField(max_length=20)
    ordered_quantity = models.DecimalField(max_digits=12, decimal_places=3, default=0)
    delivered_quantity = models.DecimalField(max_digits=12, decimal_places=3, default=0)
    accepted_quantity = models.DecimalField(max_digits=12, decimal_places=3, default=0)
    rejected_quantity = models.DecimalField(max_digits=12, decimal_places=3, default=0)
    
    # Cost planning
    estimated_unit_cost = models.DecimalField(max_digits=12, decimal_places=4, default=0)
    budgeted_total_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    actual_unit_cost = models.DecimalField(max_digits=12, decimal_places=4, default=0)
    actual_total_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    
    # Schedule planning
    required_date = models.DateField()
    planned_delivery_date = models.DateField(blank=True, null=True)
    actual_delivery_date = models.DateField(blank=True, null=True)
    lead_time_days = models.IntegerField(default=0)
    
    # BOM integration
    bom_item_reference = models.CharField(max_length=50, blank=True, null=True)
    bom_level = models.IntegerField(default=1)
    parent_assembly = models.CharField(max_length=50, blank=True, null=True)
    assembly_position = models.CharField(max_length=20, blank=True, null=True)
    
    # Status and tracking
    status = models.CharField(max_length=20, choices=ITEM_STATUS, default='pending')
    is_critical_path = models.BooleanField(default=False)
    is_long_lead_item = models.BooleanField(default=False)
    requires_inspection = models.BooleanField(default=False)
    
    # Risk management
    supply_risk_level = models.CharField(max_length=20, default='medium')
    alternative_sources = models.TextField(blank=True, null=True, help_text="JSON array of alternatives")
    risk_mitigation_plan = models.TextField(blank=True, null=True)
    
    # Documentation
    drawing_references = models.TextField(blank=True, null=True, help_text="JSON array of drawings")
    specification_documents = models.TextField(blank=True, null=True, help_text="JSON array of docs")
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_planning_line_item"
        ordering = ['material_plan', 'line_number']
        unique_together = ['material_plan', 'line_number']
        
    def __str__(self):
        return f"{self.material_plan.plan_number}-{self.line_number}: {self.item_description}"
    
    @property
    def quantity_variance(self):
        """Calculate quantity variance"""
        return self.delivered_quantity - self.required_quantity
    
    @property
    def cost_variance(self):
        """Calculate cost variance"""
        return self.actual_total_cost - self.budgeted_total_cost
    
    @property
    def is_overdue(self):
        """Check if item delivery is overdue"""
        if self.status in ['delivered', 'accepted']:
            return False
        from django.utils import timezone
        return self.required_date < timezone.now().date()


class PlanningSupplier(models.Model):
    """Supplier assignments by category with comprehensive evaluation"""
    SUPPLIER_CATEGORY = [
        ('A', 'Category A - Raw Material'),
        ('O', 'Category O - Process'),
        ('F', 'Category F - Finish'),
    ]
    
    SUPPLIER_STATUS = [
        ('active', 'Active'),
        ('preferred', 'Preferred'),
        ('qualified', 'Qualified'),
        ('blacklisted', 'Blacklisted'),
        ('under_evaluation', 'Under Evaluation'),
    ]
    
    SUPPLIER_RATING = [
        ('excellent', 'Excellent (90-100%)'),
        ('good', 'Good (80-89%)'),
        ('satisfactory', 'Satisfactory (70-79%)'),
        ('needs_improvement', 'Needs Improvement (60-69%)'),
        ('poor', 'Poor (<60%)'),
    ]
    
    id = models.AutoField(primary_key=True)
    material_plan = models.ForeignKey(
        MaterialPlan, 
        on_delete=models.CASCADE,
        related_name='supplier_assignments'
    )
    
    # Supplier identification
    supplier_code = models.CharField(max_length=30)
    supplier_name = models.CharField(max_length=200)
    supplier_category = models.CharField(max_length=1, choices=SUPPLIER_CATEGORY)
    supplier_type = models.CharField(max_length=50, blank=True, null=True)
    
    # Contact information
    contact_person = models.CharField(max_length=100, blank=True, null=True)
    contact_email = models.EmailField(blank=True, null=True)
    contact_phone = models.CharField(max_length=20, blank=True, null=True)
    supplier_address = models.TextField(blank=True, null=True)
    
    # Supplier capabilities
    capability_description = models.TextField(blank=True, null=True)
    capacity_per_month = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    technology_level = models.CharField(max_length=20, default='standard')
    certifications = models.TextField(blank=True, null=True, help_text="JSON array of certifications")
    
    # Performance metrics
    quality_rating = models.CharField(max_length=20, choices=SUPPLIER_RATING, default='satisfactory')
    delivery_rating = models.CharField(max_length=20, choices=SUPPLIER_RATING, default='satisfactory')
    cost_rating = models.CharField(max_length=20, choices=SUPPLIER_RATING, default='satisfactory')
    service_rating = models.CharField(max_length=20, choices=SUPPLIER_RATING, default='satisfactory')
    overall_rating = models.CharField(max_length=20, choices=SUPPLIER_RATING, default='satisfactory')
    
    # Historical performance
    total_orders_placed = models.IntegerField(default=0)
    on_time_delivery_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    quality_acceptance_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    cost_competitiveness_index = models.DecimalField(max_digits=5, decimal_places=2, default=100)
    
    # Risk assessment
    financial_stability = models.CharField(max_length=20, default='stable')
    supply_chain_risk = models.CharField(max_length=20, default='medium')
    geographic_risk = models.CharField(max_length=20, default='low')
    technology_risk = models.CharField(max_length=20, default='low')
    overall_risk_level = models.CharField(max_length=20, default='medium')
    
    # Commercial terms
    payment_terms = models.CharField(max_length=100, blank=True, null=True)
    currency = models.CharField(max_length=10, default='INR')
    incoterms = models.CharField(max_length=20, blank=True, null=True)
    warranty_terms = models.TextField(blank=True, null=True)
    
    # Status and relationship
    status = models.CharField(max_length=20, choices=SUPPLIER_STATUS, default='active')
    is_preferred_supplier = models.BooleanField(default=False)
    is_strategic_partner = models.BooleanField(default=False)
    relationship_start_date = models.DateField(blank=True, null=True)
    last_audit_date = models.DateField(blank=True, null=True)
    next_audit_due = models.DateField(blank=True, null=True)
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_planning_supplier"
        ordering = ['supplier_category', 'supplier_name']
        unique_together = ['material_plan', 'supplier_code', 'supplier_category']
        
    def __str__(self):
        return f"{self.supplier_name} (Category {self.supplier_category})"
    
    def calculate_overall_rating_score(self):
        """Calculate numerical overall rating score"""
        rating_scores = {
            'excellent': 95,
            'good': 85,
            'satisfactory': 75,
            'needs_improvement': 65,
            'poor': 50
        }
        
        quality_score = rating_scores.get(self.quality_rating, 75)
        delivery_score = rating_scores.get(self.delivery_rating, 75)
        cost_score = rating_scores.get(self.cost_rating, 75)
        service_score = rating_scores.get(self.service_rating, 75)
        
        # Weighted average (Quality: 30%, Delivery: 30%, Cost: 25%, Service: 15%)
        overall_score = (quality_score * 0.30 + delivery_score * 0.30 + 
                        cost_score * 0.25 + service_score * 0.15)
        
        return round(overall_score, 1)


class PlanningSchedule(models.Model):
    """Delivery schedule and milestones with advanced tracking"""
    MILESTONE_TYPE = [
        ('material_receipt', 'Material Receipt'),
        ('processing_start', 'Processing Start'),
        ('processing_complete', 'Processing Complete'),
        ('quality_inspection', 'Quality Inspection'),
        ('delivery', 'Final Delivery'),
        ('installation', 'Installation'),
        ('commissioning', 'Commissioning'),
    ]
    
    MILESTONE_STATUS = [
        ('planned', 'Planned'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('delayed', 'Delayed'),
        ('at_risk', 'At Risk'),
        ('cancelled', 'Cancelled'),
    ]
    
    id = models.AutoField(primary_key=True)
    material_plan = models.ForeignKey(
        MaterialPlan, 
        on_delete=models.CASCADE,
        related_name='schedule_milestones'
    )
    
    # Milestone identification
    milestone_code = models.CharField(max_length=30)
    milestone_name = models.CharField(max_length=200)
    milestone_type = models.CharField(max_length=20, choices=MILESTONE_TYPE)
    milestone_description = models.TextField(blank=True, null=True)
    
    # Schedule planning
    planned_start_date = models.DateField()
    planned_end_date = models.DateField()
    baseline_start_date = models.DateField(blank=True, null=True)
    baseline_end_date = models.DateField(blank=True, null=True)
    
    # Actual execution
    actual_start_date = models.DateField(blank=True, null=True)
    actual_end_date = models.DateField(blank=True, null=True)
    completion_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Dependencies
    predecessor_milestones = models.ManyToManyField(
        'self', 
        symmetrical=False,
        related_name='successor_milestones',
        blank=True
    )
    dependency_type = models.CharField(max_length=20, default='finish_to_start')
    lag_days = models.IntegerField(default=0)
    
    # Resource planning
    assigned_resources = models.TextField(blank=True, null=True, help_text="JSON array of resources")
    resource_requirements = models.TextField(blank=True, null=True)
    estimated_effort_hours = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    actual_effort_hours = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    
    # Risk and issues
    risk_factors = models.TextField(blank=True, null=True, help_text="JSON array of risks")
    mitigation_actions = models.TextField(blank=True, null=True)
    current_issues = models.TextField(blank=True, null=True)
    escalation_required = models.BooleanField(default=False)
    
    # Status and tracking
    status = models.CharField(max_length=20, choices=MILESTONE_STATUS, default='planned')
    is_critical_path = models.BooleanField(default=False)
    is_baseline_milestone = models.BooleanField(default=False)
    priority_level = models.IntegerField(default=3)  # 1=High, 2=Medium, 3=Low
    
    # Progress tracking
    last_update_date = models.DateTimeField(blank=True, null=True)
    next_review_date = models.DateField(blank=True, null=True)
    progress_notes = models.TextField(blank=True, null=True)
    responsible_person = models.CharField(max_length=100, blank=True, null=True)
    
    # Quality gates
    quality_gate_required = models.BooleanField(default=False)
    quality_criteria = models.TextField(blank=True, null=True)
    quality_gate_passed = models.BooleanField(default=False)
    quality_gate_date = models.DateField(blank=True, null=True)
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_planning_schedule"
        ordering = ['material_plan', 'planned_start_date', 'milestone_code']
        unique_together = ['material_plan', 'milestone_code']
        
    def __str__(self):
        return f"{self.material_plan.plan_number} - {self.milestone_name}"
    
    @property
    def schedule_variance_days(self):
        """Calculate schedule variance in days"""
        if self.actual_end_date and self.planned_end_date:
            return (self.actual_end_date - self.planned_end_date).days
        return 0
    
    @property
    def is_overdue(self):
        """Check if milestone is overdue"""
        if self.status == 'completed':
            return False
        from django.utils import timezone
        return self.planned_end_date < timezone.now().date()
    
    def calculate_float_days(self):
        """Calculate total float for non-critical path activities"""
        if self.is_critical_path:
            return 0
        # Simplified float calculation - would need more complex logic for full CPM
        return 0


class PlanningRevision(models.Model):
    """Planning change history and version control"""
    REVISION_TYPE = [
        ('initial', 'Initial Version'),
        ('scope_change', 'Scope Change'),
        ('schedule_change', 'Schedule Change'),
        ('cost_change', 'Cost Change'),
        ('requirement_change', 'Requirement Change'),
        ('emergency_change', 'Emergency Change'),
    ]
    
    REVISION_STATUS = [
        ('draft', 'Draft'),
        ('under_review', 'Under Review'),
        ('approved', 'Approved'),
        ('implemented', 'Implemented'),
        ('rejected', 'Rejected'),
    ]
    
    id = models.AutoField(primary_key=True)
    material_plan = models.ForeignKey(
        MaterialPlan, 
        on_delete=models.CASCADE,
        related_name='revisions'
    )
    
    # Revision identification
    revision_number = models.CharField(max_length=20)
    revision_type = models.CharField(max_length=20, choices=REVISION_TYPE)
    revision_title = models.CharField(max_length=200)
    revision_description = models.TextField()
    
    # Change details
    change_reason = models.TextField()
    impact_assessment = models.TextField(blank=True, null=True)
    change_scope = models.TextField(blank=True, null=True, help_text="JSON object with change details")
    
    # Schedule impact
    schedule_impact_days = models.IntegerField(default=0)
    original_end_date = models.DateField(blank=True, null=True)
    revised_end_date = models.DateField(blank=True, null=True)
    
    # Cost impact
    cost_impact_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    original_budget = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    revised_budget = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    
    # Approval workflow
    status = models.CharField(max_length=20, choices=REVISION_STATUS, default='draft')
    requested_by = models.CharField(max_length=100)
    request_date = models.DateTimeField(auto_now_add=True)
    approved_by = models.CharField(max_length=100, blank=True, null=True)
    approval_date = models.DateTimeField(blank=True, null=True)
    approval_comments = models.TextField(blank=True, null=True)
    
    # Implementation
    implemented_by = models.CharField(max_length=100, blank=True, null=True)
    implementation_date = models.DateTimeField(blank=True, null=True)
    implementation_notes = models.TextField(blank=True, null=True)
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        managed = True
        db_table = "mp_planning_revision"
        ordering = ['-created_date', 'revision_number']
        unique_together = ['material_plan', 'revision_number']
        
    def __str__(self):
        return f"{self.material_plan.plan_number} Rev {self.revision_number}: {self.revision_title}"


class PlanningApproval(models.Model):
    """Approval workflow for material planning"""
    APPROVAL_TYPE = [
        ('initial_approval', 'Initial Approval'),
        ('revision_approval', 'Revision Approval'),
        ('budget_approval', 'Budget Approval'),
        ('schedule_approval', 'Schedule Approval'),
        ('closure_approval', 'Closure Approval'),
    ]
    
    APPROVAL_STATUS = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('conditional', 'Conditional Approval'),
        ('withdrawn', 'Withdrawn'),
    ]
    
    APPROVAL_LEVEL = [
        (1, 'Level 1 - Supervisor'),
        (2, 'Level 2 - Manager'),
        (3, 'Level 3 - General Manager'),
        (4, 'Level 4 - Director'),
        (5, 'Level 5 - CEO/President'),
    ]
    
    id = models.AutoField(primary_key=True)
    material_plan = models.ForeignKey(
        MaterialPlan, 
        on_delete=models.CASCADE,
        related_name='approvals'
    )
    planning_revision = models.ForeignKey(
        PlanningRevision, 
        on_delete=models.CASCADE,
        related_name='approvals',
        blank=True, null=True
    )
    
    # Approval details
    approval_type = models.CharField(max_length=20, choices=APPROVAL_TYPE)
    approval_level = models.IntegerField(choices=APPROVAL_LEVEL)
    approval_sequence = models.IntegerField(default=1)
    
    # Approval criteria
    approval_criteria = models.TextField(blank=True, null=True)
    approval_threshold = models.DecimalField(max_digits=15, decimal_places=2, blank=True, null=True)
    required_documents = models.TextField(blank=True, null=True, help_text="JSON array of required docs")
    
    # Approver information
    approver_role = models.CharField(max_length=100)
    approver_name = models.CharField(max_length=100, blank=True, null=True)
    delegate_approver = models.CharField(max_length=100, blank=True, null=True)
    
    # Approval workflow
    status = models.CharField(max_length=20, choices=APPROVAL_STATUS, default='pending')
    submitted_date = models.DateTimeField(blank=True, null=True)
    due_date = models.DateTimeField(blank=True, null=True)
    approval_date = models.DateTimeField(blank=True, null=True)
    
    # Approval decision
    approval_comments = models.TextField(blank=True, null=True)
    conditions = models.TextField(blank=True, null=True)
    rejection_reason = models.TextField(blank=True, null=True)
    
    # Notification
    notification_sent = models.BooleanField(default=False)
    reminder_count = models.IntegerField(default=0)
    escalation_required = models.BooleanField(default=False)
    escalation_date = models.DateTimeField(blank=True, null=True)
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        managed = True
        db_table = "mp_planning_approval"
        ordering = ['approval_level', 'approval_sequence']
        unique_together = ['material_plan', 'approval_type', 'approval_level']
        
    def __str__(self):
        return f"{self.material_plan.plan_number} - {self.get_approval_type_display()} Level {self.approval_level}"


class PlanningExecution(models.Model):
    """Planning execution tracking with detailed progress monitoring"""
    EXECUTION_STATUS = [
        ('not_started', 'Not Started'),
        ('in_progress', 'In Progress'),
        ('on_hold', 'On Hold'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    EXECUTION_PHASE = [
        ('sourcing', 'Sourcing Phase'),
        ('procurement', 'Procurement Phase'),
        ('production', 'Production Phase'),
        ('quality_control', 'Quality Control Phase'),
        ('delivery', 'Delivery Phase'),
        ('installation', 'Installation Phase'),
    ]
    
    id = models.AutoField(primary_key=True)
    material_plan = models.OneToOneField(
        MaterialPlan, 
        on_delete=models.CASCADE,
        related_name='execution_tracking'
    )
    
    # Execution overview
    execution_manager = models.CharField(max_length=100)
    execution_team = models.TextField(blank=True, null=True, help_text="JSON array of team members")
    current_phase = models.CharField(max_length=20, choices=EXECUTION_PHASE, default='sourcing')
    
    # Progress tracking
    overall_progress_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    sourcing_progress = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    procurement_progress = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    production_progress = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    quality_progress = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    delivery_progress = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Timeline tracking
    execution_start_date = models.DateField(blank=True, null=True)
    planned_completion_date = models.DateField()
    forecasted_completion_date = models.DateField(blank=True, null=True)
    actual_completion_date = models.DateField(blank=True, null=True)
    
    # Performance metrics
    schedule_performance_index = models.DecimalField(max_digits=5, decimal_places=3, default=1.000)
    cost_performance_index = models.DecimalField(max_digits=5, decimal_places=3, default=1.000)
    quality_performance_index = models.DecimalField(max_digits=5, decimal_places=3, default=1.000)
    
    # Issues and risks
    active_issues_count = models.IntegerField(default=0)
    resolved_issues_count = models.IntegerField(default=0)
    high_risk_items_count = models.IntegerField(default=0)
    mitigation_actions_count = models.IntegerField(default=0)
    
    # Status and communication
    status = models.CharField(max_length=20, choices=EXECUTION_STATUS, default='not_started')
    last_status_update = models.DateTimeField(blank=True, null=True)
    next_review_date = models.DateField(blank=True, null=True)
    executive_summary = models.TextField(blank=True, null=True)
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_planning_execution"
        
    def __str__(self):
        return f"{self.material_plan.plan_number} - Execution Tracking"
    
    def calculate_schedule_performance_index(self):
        """Calculate SPI (Earned Value / Planned Value)"""
        # Simplified SPI calculation
        if self.overall_progress_percentage > 0:
            from django.utils import timezone
            days_elapsed = (timezone.now().date() - self.execution_start_date).days if self.execution_start_date else 0
            total_planned_days = (self.planned_completion_date - self.execution_start_date).days if self.execution_start_date else 1
            
            if total_planned_days > 0:
                planned_progress = (days_elapsed / total_planned_days) * 100
                if planned_progress > 0:
                    return self.overall_progress_percentage / planned_progress
        return 1.000


class PlanningClosure(models.Model):
    """Planning completion records and lessons learned"""
    CLOSURE_TYPE = [
        ('successful', 'Successful Completion'),
        ('partial', 'Partial Completion'),
        ('cancelled', 'Cancelled'),
        ('terminated', 'Terminated'),
    ]
    
    CLOSURE_STATUS = [
        ('pending', 'Pending Closure'),
        ('under_review', 'Under Review'),
        ('approved', 'Approved'),
        ('closed', 'Closed'),
    ]
    
    id = models.AutoField(primary_key=True)
    material_plan = models.OneToOneField(
        MaterialPlan, 
        on_delete=models.CASCADE,
        related_name='closure_record'
    )
    
    # Closure details
    closure_type = models.CharField(max_length=20, choices=CLOSURE_TYPE)
    closure_reason = models.TextField()
    closure_summary = models.TextField()
    
    # Performance summary
    final_completion_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    final_cost_variance = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    final_schedule_variance_days = models.IntegerField(default=0)
    final_quality_score = models.DecimalField(max_digits=5, decimal_places=2, default=100)
    
    # Deliverables summary
    total_deliverables_planned = models.IntegerField(default=0)
    total_deliverables_completed = models.IntegerField(default=0)
    deliverables_accepted = models.IntegerField(default=0)
    deliverables_rejected = models.IntegerField(default=0)
    
    # Lessons learned
    what_went_well = models.TextField(blank=True, null=True)
    areas_for_improvement = models.TextField(blank=True, null=True)
    lessons_learned = models.TextField(blank=True, null=True)
    recommendations = models.TextField(blank=True, null=True)
    
    # Stakeholder feedback
    customer_satisfaction_score = models.DecimalField(max_digits=3, decimal_places=1, blank=True, null=True)
    team_satisfaction_score = models.DecimalField(max_digits=3, decimal_places=1, blank=True, null=True)
    stakeholder_feedback = models.TextField(blank=True, null=True)
    
    # Closure workflow
    status = models.CharField(max_length=20, choices=CLOSURE_STATUS, default='pending')
    closure_initiated_by = models.CharField(max_length=100)
    closure_initiated_date = models.DateTimeField(auto_now_add=True)
    closure_approved_by = models.CharField(max_length=100, blank=True, null=True)
    closure_approved_date = models.DateTimeField(blank=True, null=True)
    final_closure_date = models.DateTimeField(blank=True, null=True)
    
    # Documentation
    closure_documents = models.TextField(blank=True, null=True, help_text="JSON array of closure documents")
    handover_checklist = models.TextField(blank=True, null=True, help_text="JSON object with checklist items")
    
    # Audit fields
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_planning_closure"
        
    def __str__(self):
        return f"{self.material_plan.plan_number} - Closure Record"
    
    @property
    def completion_success_rate(self):
        """Calculate completion success rate"""
        if self.total_deliverables_planned > 0:
            return (self.deliverables_accepted / self.total_deliverables_planned) * 100
        return 0


# =============================================================================
# TASK GROUP 3: PLANNING DETAIL MANAGEMENT AND SUPPLIER COORDINATION
# Detailed procurement planning with BOM integration and supplier categorization
# =============================================================================

class PlanningDetail(models.Model):
    """Detailed item-level planning with comprehensive analysis"""
    DETAIL_STATUS = [
        ('pending', 'Pending Analysis'),
        ('analyzing', 'Under Analysis'),
        ('planned', 'Planned'),
        ('approved', 'Approved'),
        ('executing', 'Under Execution'),
        ('completed', 'Completed'),
    ]
    
    id = models.AutoField(primary_key=True)
    material_plan = models.ForeignKey(
        MaterialPlan, 
        on_delete=models.CASCADE,
        related_name='planning_details'
    )
    planning_line_item = models.OneToOneField(
        PlanningLineItem,
        on_delete=models.CASCADE,
        related_name='detail_planning'
    )
    
    # Detail planning information
    detail_plan_number = models.CharField(max_length=50, unique=True)
    detail_description = models.TextField()
    planning_methodology = models.CharField(max_length=50, default='standard')
    complexity_level = models.CharField(max_length=20, default='medium')
    
    # BOM integration
    bom_exploded_data = models.TextField(blank=True, null=True, help_text="JSON object with BOM explosion")
    multi_level_bom = models.TextField(blank=True, null=True, help_text="JSON array with BOM levels")
    bom_quantity_analysis = models.TextField(blank=True, null=True, help_text="JSON object with quantity breakdown")
    
    # Detailed quantity analysis
    bom_required_quantity = models.DecimalField(max_digits=12, decimal_places=3, default=0)
    pr_allocated_quantity = models.DecimalField(max_digits=12, decimal_places=3, default=0)
    wis_committed_quantity = models.DecimalField(max_digits=12, decimal_places=3, default=0)
    gqn_approved_quantity = models.DecimalField(max_digits=12, decimal_places=3, default=0)
    net_planning_requirement = models.DecimalField(max_digits=12, decimal_places=3, default=0)
    safety_stock_requirement = models.DecimalField(max_digits=12, decimal_places=3, default=0)
    total_procurement_quantity = models.DecimalField(max_digits=12, decimal_places=3, default=0)
    
    # Alternative planning scenarios
    primary_plan_scenario = models.TextField(blank=True, null=True, help_text="JSON object with primary scenario")
    alternative_scenarios = models.TextField(blank=True, null=True, help_text="JSON array with alternative scenarios")
    recommended_scenario = models.CharField(max_length=50, default='primary')
    scenario_selection_rationale = models.TextField(blank=True, null=True)
    
    # Detailed cost analysis
    material_cost_breakdown = models.TextField(blank=True, null=True, help_text="JSON object with cost details")
    processing_cost_breakdown = models.TextField(blank=True, null=True, help_text="JSON object with processing costs")
    logistics_cost_breakdown = models.TextField(blank=True, null=True, help_text="JSON object with logistics costs")
    total_cost_of_ownership = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    cost_optimization_opportunities = models.TextField(blank=True, null=True, help_text="JSON array of cost savings")
    
    # Detailed scheduling
    detailed_schedule_breakdown = models.TextField(blank=True, null=True, help_text="JSON object with schedule details")
    critical_path_analysis = models.TextField(blank=True, null=True, help_text="JSON object with critical path")
    schedule_optimization_options = models.TextField(blank=True, null=True, help_text="JSON array of optimizations")
    delivery_schedule_confidence = models.DecimalField(max_digits=5, decimal_places=2, default=80)
    
    # Risk and contingency planning
    detailed_risk_assessment = models.TextField(blank=True, null=True, help_text="JSON object with risk analysis")
    contingency_plans = models.TextField(blank=True, null=True, help_text="JSON array with contingency options")
    risk_mitigation_costs = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    probability_of_success = models.DecimalField(max_digits=5, decimal_places=2, default=90)
    
    # Status and tracking
    status = models.CharField(max_length=20, choices=DETAIL_STATUS, default='pending')
    planning_confidence_level = models.DecimalField(max_digits=5, decimal_places=2, default=80)
    last_analysis_date = models.DateTimeField(blank=True, null=True)
    next_review_date = models.DateField(blank=True, null=True)
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_planning_detail"
        ordering = ['material_plan', 'detail_plan_number']
        
    def __str__(self):
        return f"{self.detail_plan_number}: {self.planning_line_item.item_description}"
    
    def calculate_net_requirement(self):
        """Calculate net planning requirement"""
        self.net_planning_requirement = (
            self.bom_required_quantity - 
            self.pr_allocated_quantity - 
            self.wis_committed_quantity - 
            self.gqn_approved_quantity
        )
        if self.net_planning_requirement < 0:
            self.net_planning_requirement = 0
        
        self.total_procurement_quantity = self.net_planning_requirement + self.safety_stock_requirement
        return self.net_planning_requirement


class BOMMaterialPlan(models.Model):
    """BOM-based material planning with multi-level explosion"""
    BOM_LEVEL_TYPE = [
        ('root', 'Root Assembly'),
        ('sub_assembly', 'Sub-Assembly'),
        ('component', 'Component'),
        ('raw_material', 'Raw Material'),
        ('consumable', 'Consumable'),
    ]
    
    id = models.AutoField(primary_key=True)
    planning_detail = models.ForeignKey(
        PlanningDetail,
        on_delete=models.CASCADE,
        related_name='bom_material_plans'
    )
    
    # BOM structure
    bom_item_code = models.CharField(max_length=50)
    bom_item_description = models.TextField()
    bom_level = models.IntegerField(default=1)
    bom_level_type = models.CharField(max_length=20, choices=BOM_LEVEL_TYPE)
    parent_item_code = models.CharField(max_length=50, blank=True, null=True)
    
    # Quantity planning per BOM level
    bom_quantity_per_unit = models.DecimalField(max_digits=12, decimal_places=6, default=1)
    assembly_quantity_required = models.DecimalField(max_digits=12, decimal_places=3, default=0)
    total_quantity_required = models.DecimalField(max_digits=12, decimal_places=3, default=0)
    scrap_allowance_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=5)
    scrap_quantity = models.DecimalField(max_digits=12, decimal_places=3, default=0)
    final_required_quantity = models.DecimalField(max_digits=12, decimal_places=3, default=0)
    
    # BOM attributes
    material_specification = models.TextField(blank=True, null=True)
    drawing_reference = models.CharField(max_length=100, blank=True, null=True)
    revision_level = models.CharField(max_length=20, blank=True, null=True)
    effective_from_date = models.DateField(blank=True, null=True)
    effective_to_date = models.DateField(blank=True, null=True)
    
    # Engineering change impact
    ecn_reference = models.CharField(max_length=50, blank=True, null=True)
    change_impact_analysis = models.TextField(blank=True, null=True)
    implementation_status = models.CharField(max_length=20, default='current')
    
    # Planning attributes
    make_buy_decision = models.CharField(max_length=10, default='buy')  # make/buy
    preferred_supplier_category = models.CharField(max_length=1, blank=True, null=True)  # A/O/F
    sourcing_strategy = models.CharField(max_length=50, default='standard')
    lead_time_weeks = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    
    # Cost planning
    standard_unit_cost = models.DecimalField(max_digits=12, decimal_places=4, default=0)
    target_unit_cost = models.DecimalField(max_digits=12, decimal_places=4, default=0)
    budgeted_total_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    cost_optimization_potential = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    
    # Quality requirements
    quality_specification = models.TextField(blank=True, null=True)
    inspection_requirements = models.TextField(blank=True, null=True)
    quality_control_plan = models.TextField(blank=True, null=True, help_text="JSON object with QC plan")
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_bom_material_plan"
        ordering = ['planning_detail', 'bom_level', 'bom_item_code']
        unique_together = ['planning_detail', 'bom_item_code', 'bom_level']
        
    def __str__(self):
        return f"L{self.bom_level}: {self.bom_item_code} - {self.bom_item_description}"
    
    def calculate_quantities(self):
        """Calculate all quantity requirements"""
        self.scrap_quantity = self.total_quantity_required * (self.scrap_allowance_percentage / 100)
        self.final_required_quantity = self.total_quantity_required + self.scrap_quantity
        self.budgeted_total_cost = self.final_required_quantity * self.standard_unit_cost


class SupplierCategoryPlan(models.Model):
    """Category-wise supplier planning (A/O/F categories)"""
    PLANNING_PHASE = [
        ('identification', 'Supplier Identification'),
        ('evaluation', 'Supplier Evaluation'),
        ('selection', 'Supplier Selection'),
        ('negotiation', 'Rate Negotiation'),
        ('contracting', 'Contract Finalization'),
        ('execution', 'Order Execution'),
    ]
    
    id = models.AutoField(primary_key=True)
    planning_detail = models.ForeignKey(
        PlanningDetail,
        on_delete=models.CASCADE,
        related_name='supplier_category_plans'
    )
    
    # Category planning
    supplier_category = models.CharField(max_length=1, choices=PlanningSupplier.SUPPLIER_CATEGORY)
    category_description = models.TextField()
    category_requirements = models.TextField(blank=True, null=True)
    category_specifications = models.TextField(blank=True, null=True, help_text="JSON object with specs")
    
    # Supplier identification
    identified_suppliers = models.TextField(blank=True, null=True, help_text="JSON array of supplier candidates")
    supplier_evaluation_criteria = models.TextField(blank=True, null=True, help_text="JSON object with criteria")
    evaluation_methodology = models.CharField(max_length=50, default='weighted_scoring')
    
    # Quantity allocation per category
    category_quantity_requirement = models.DecimalField(max_digits=12, decimal_places=3, default=0)
    primary_supplier_allocation = models.DecimalField(max_digits=5, decimal_places=2, default=70)
    secondary_supplier_allocation = models.DecimalField(max_digits=5, decimal_places=2, default=30)
    risk_mitigation_allocation = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Category-specific requirements
    technical_requirements = models.TextField(blank=True, null=True)
    quality_requirements = models.TextField(blank=True, null=True)
    delivery_requirements = models.TextField(blank=True, null=True)
    commercial_requirements = models.TextField(blank=True, null=True)
    
    # Planning timeline
    planning_phase = models.CharField(max_length=20, choices=PLANNING_PHASE, default='identification')
    phase_start_date = models.DateField(blank=True, null=True)
    phase_target_completion = models.DateField(blank=True, null=True)
    phase_actual_completion = models.DateField(blank=True, null=True)
    
    # Risk assessment
    category_risk_level = models.CharField(max_length=20, default='medium')
    supply_market_analysis = models.TextField(blank=True, null=True)
    risk_factors = models.TextField(blank=True, null=True, help_text="JSON array of risks")
    mitigation_strategies = models.TextField(blank=True, null=True, help_text="JSON array of strategies")
    
    # Performance tracking
    category_performance_metrics = models.TextField(blank=True, null=True, help_text="JSON object with KPIs")
    benchmark_performance = models.TextField(blank=True, null=True)
    continuous_improvement_plans = models.TextField(blank=True, null=True)
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_supplier_category_plan"
        ordering = ['planning_detail', 'supplier_category']
        unique_together = ['planning_detail', 'supplier_category']
        
    def __str__(self):
        return f"Category {self.supplier_category}: {self.category_description}"


class RawMaterialSupplier(models.Model):
    """Category A - Raw Material Suppliers with specialized capabilities"""
    MATERIAL_TYPE = [
        ('metals', 'Metals and Alloys'),
        ('plastics', 'Plastics and Polymers'),
        ('composites', 'Composite Materials'),
        ('ceramics', 'Ceramics and Glass'),
        ('textiles', 'Textiles and Fabrics'),
        ('chemicals', 'Chemicals and Compounds'),
        ('electronics', 'Electronic Components'),
    ]
    
    id = models.AutoField(primary_key=True)
    supplier_category_plan = models.ForeignKey(
        SupplierCategoryPlan,
        on_delete=models.CASCADE,
        related_name='raw_material_suppliers',
        limit_choices_to={'supplier_category': 'A'}
    )
    
    # Material specialization
    material_type = models.CharField(max_length=20, choices=MATERIAL_TYPE)
    material_grades_supplied = models.TextField(blank=True, null=True, help_text="JSON array of grades")
    material_specifications = models.TextField(blank=True, null=True)
    material_certifications = models.TextField(blank=True, null=True, help_text="JSON array of certifications")
    
    # Supplier capabilities
    production_capacity_tons_month = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    available_capacity_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    material_testing_facilities = models.TextField(blank=True, null=True)
    quality_certifications = models.TextField(blank=True, null=True, help_text="JSON array of quality certs")
    
    # Material quality management
    incoming_material_inspection = models.BooleanField(default=True)
    material_traceability = models.BooleanField(default=True)
    batch_control_system = models.CharField(max_length=50, blank=True, null=True)
    material_shelf_life_days = models.IntegerField(blank=True, null=True)
    storage_requirements = models.TextField(blank=True, null=True)
    
    # Procurement terms
    minimum_order_quantity = models.DecimalField(max_digits=12, decimal_places=3, default=0)
    standard_packaging_units = models.CharField(max_length=100, blank=True, null=True)
    delivery_terms = models.CharField(max_length=100, blank=True, null=True)
    material_unit_price = models.DecimalField(max_digits=12, decimal_places=4, default=0)
    price_validity_days = models.IntegerField(default=30)
    
    # Performance metrics
    material_conformance_rate = models.DecimalField(max_digits=5, decimal_places=2, default=100)
    delivery_performance_rate = models.DecimalField(max_digits=5, decimal_places=2, default=95)
    material_rejection_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    supplier_responsiveness_score = models.DecimalField(max_digits=5, decimal_places=2, default=80)
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_raw_material_supplier"
        
    def __str__(self):
        return f"Raw Material Supplier: {self.get_material_type_display()}"


class ProcessSupplier(models.Model):
    """Category O - Process Suppliers with manufacturing capabilities"""
    PROCESS_TYPE = [
        ('machining', 'Machining and Fabrication'),
        ('forming', 'Metal Forming and Shaping'),
        ('welding', 'Welding and Joining'),
        ('casting', 'Casting and Molding'),
        ('heat_treatment', 'Heat Treatment'),
        ('surface_treatment', 'Surface Treatment'),
        ('assembly', 'Assembly Services'),
    ]
    
    id = models.AutoField(primary_key=True)
    supplier_category_plan = models.ForeignKey(
        SupplierCategoryPlan,
        on_delete=models.CASCADE,
        related_name='process_suppliers',
        limit_choices_to={'supplier_category': 'O'}
    )
    
    # Process capabilities
    process_type = models.CharField(max_length=20, choices=PROCESS_TYPE)
    process_capabilities = models.TextField(blank=True, null=True, help_text="JSON array of capabilities")
    equipment_list = models.TextField(blank=True, null=True, help_text="JSON array of equipment")
    process_certifications = models.TextField(blank=True, null=True, help_text="JSON array of certifications")
    
    # Capacity planning
    process_capacity_hours_month = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    available_capacity_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    setup_time_hours = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    process_cycle_time_minutes = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    
    # Quality and process control
    process_control_plan = models.TextField(blank=True, null=True)
    statistical_process_control = models.BooleanField(default=False)
    process_capability_index = models.DecimalField(max_digits=5, decimal_places=3, default=1.000)
    first_pass_yield_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=95)
    
    # Process rates and costing
    setup_cost_per_job = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    process_rate_per_hour = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    material_handling_rate = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    tooling_cost_allocation = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Delivery and logistics
    standard_delivery_time_days = models.IntegerField(default=7)
    express_delivery_available = models.BooleanField(default=False)
    pickup_delivery_service = models.BooleanField(default=False)
    packaging_and_protection = models.TextField(blank=True, null=True)
    
    # Performance metrics
    process_quality_score = models.DecimalField(max_digits=5, decimal_places=2, default=90)
    delivery_reliability_score = models.DecimalField(max_digits=5, decimal_places=2, default=90)
    process_efficiency_score = models.DecimalField(max_digits=5, decimal_places=2, default=85)
    customer_satisfaction_score = models.DecimalField(max_digits=5, decimal_places=2, default=85)
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_process_supplier"
        
    def __str__(self):
        return f"Process Supplier: {self.get_process_type_display()}"


class FinishSupplier(models.Model):
    """Category F - Finish Suppliers for final product preparation"""
    FINISH_TYPE = [
        ('painting', 'Painting and Coating'),
        ('plating', 'Plating and Electroplating'),
        ('anodizing', 'Anodizing and Surface Treatment'),
        ('polishing', 'Polishing and Finishing'),
        ('packaging', 'Final Packaging'),
        ('testing', 'Final Testing and QC'),
        ('assembly', 'Final Assembly'),
    ]
    
    id = models.AutoField(primary_key=True)
    supplier_category_plan = models.ForeignKey(
        SupplierCategoryPlan,
        on_delete=models.CASCADE,
        related_name='finish_suppliers',
        limit_choices_to={'supplier_category': 'F'}
    )
    
    # Finishing capabilities
    finish_type = models.CharField(max_length=20, choices=FINISH_TYPE)
    finish_specifications = models.TextField(blank=True, null=True)
    finish_quality_standards = models.TextField(blank=True, null=True)
    finish_certifications = models.TextField(blank=True, null=True, help_text="JSON array of certifications")
    
    # Capacity and throughput
    finish_capacity_units_month = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    available_capacity_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    finish_cycle_time_hours = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    curing_drying_time_hours = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    
    # Quality control and final inspection
    final_inspection_plan = models.TextField(blank=True, null=True)
    quality_control_equipment = models.TextField(blank=True, null=True, help_text="JSON array of equipment")
    final_quality_standards = models.TextField(blank=True, null=True)
    cosmetic_quality_requirements = models.TextField(blank=True, null=True)
    
    # Finish rates and costing
    finish_setup_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    finish_rate_per_unit = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    material_consumption_rate = models.DecimalField(max_digits=8, decimal_places=4, default=0)
    waste_disposal_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Final product handling
    final_packaging_requirements = models.TextField(blank=True, null=True)
    protection_and_preservation = models.TextField(blank=True, null=True)
    final_labeling_requirements = models.TextField(blank=True, null=True)
    shipping_preparation = models.TextField(blank=True, null=True)
    
    # Performance metrics
    finish_quality_score = models.DecimalField(max_digits=5, decimal_places=2, default=95)
    cosmetic_quality_score = models.DecimalField(max_digits=5, decimal_places=2, default=90)
    delivery_performance_score = models.DecimalField(max_digits=5, decimal_places=2, default=90)
    final_product_acceptance_rate = models.DecimalField(max_digits=5, decimal_places=2, default=98)
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_finish_supplier"
        
    def __str__(self):
        return f"Finish Supplier: {self.get_finish_type_display()}"


class SupplierQuotation(models.Model):
    """Supplier rates and terms with comprehensive quotation management"""
    QUOTATION_STATUS = [
        ('requested', 'Quotation Requested'),
        ('received', 'Quotation Received'),
        ('under_evaluation', 'Under Evaluation'),
        ('accepted', 'Accepted'),
        ('rejected', 'Rejected'),
        ('expired', 'Expired'),
    ]
    
    QUOTATION_TYPE = [
        ('standard', 'Standard Quotation'),
        ('volume_discount', 'Volume Discount'),
        ('long_term_contract', 'Long Term Contract'),
        ('spot_pricing', 'Spot Pricing'),
        ('framework_agreement', 'Framework Agreement'),
    ]
    
    id = models.AutoField(primary_key=True)
    supplier_category_plan = models.ForeignKey(
        SupplierCategoryPlan,
        on_delete=models.CASCADE,
        related_name='supplier_quotations'
    )
    planning_supplier = models.ForeignKey(
        PlanningSupplier,
        on_delete=models.CASCADE,
        related_name='quotations'
    )
    
    # Quotation identification
    quotation_number = models.CharField(max_length=50)
    quotation_type = models.CharField(max_length=20, choices=QUOTATION_TYPE, default='standard')
    quotation_title = models.CharField(max_length=200)
    quotation_description = models.TextField()
    
    # Quotation timeline
    quotation_request_date = models.DateField()
    quotation_due_date = models.DateField()
    quotation_received_date = models.DateField(blank=True, null=True)
    quotation_validity_date = models.DateField()
    evaluation_completion_date = models.DateField(blank=True, null=True)
    
    # Pricing details
    base_unit_price = models.DecimalField(max_digits=12, decimal_places=4, default=0)
    volume_discount_tiers = models.TextField(blank=True, null=True, help_text="JSON array of discount tiers")
    total_quotation_value = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    currency = models.CharField(max_length=10, default='INR')
    exchange_rate = models.DecimalField(max_digits=10, decimal_places=6, default=1.000000)
    
    # Terms and conditions
    payment_terms = models.CharField(max_length=100)
    delivery_terms = models.CharField(max_length=100)
    incoterms = models.CharField(max_length=20, blank=True, null=True)
    warranty_terms = models.TextField(blank=True, null=True)
    penalty_clauses = models.TextField(blank=True, null=True)
    
    # Technical and quality terms
    technical_specifications_compliance = models.TextField(blank=True, null=True)
    quality_requirements_compliance = models.TextField(blank=True, null=True)
    testing_and_inspection_terms = models.TextField(blank=True, null=True)
    delivery_schedule_compliance = models.TextField(blank=True, null=True)
    
    # Quotation evaluation
    technical_score = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    commercial_score = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    delivery_score = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    quality_score = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    overall_score = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Status and decisions
    status = models.CharField(max_length=20, choices=QUOTATION_STATUS, default='requested')
    evaluation_comments = models.TextField(blank=True, null=True)
    selection_rationale = models.TextField(blank=True, null=True)
    rejection_reason = models.TextField(blank=True, null=True)
    
    # Negotiation tracking
    negotiation_rounds = models.IntegerField(default=0)
    negotiation_notes = models.TextField(blank=True, null=True)
    final_negotiated_price = models.DecimalField(max_digits=12, decimal_places=4, blank=True, null=True)
    price_improvement_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_supplier_quotation"
        ordering = ['-quotation_received_date', 'overall_score']
        unique_together = ['supplier_category_plan', 'planning_supplier', 'quotation_number']
        
    def __str__(self):
        return f"{self.quotation_number}: {self.planning_supplier.supplier_name}"
    
    def calculate_overall_score(self):
        """Calculate weighted overall score"""
        # Weighted scoring: Technical 30%, Commercial 40%, Delivery 15%, Quality 15%
        self.overall_score = (
            (self.technical_score * 0.30) +
            (self.commercial_score * 0.40) +
            (self.delivery_score * 0.15) +
            (self.quality_score * 0.15)
        )
        return self.overall_score
    
    def calculate_price_improvement(self):
        """Calculate price improvement from negotiation"""
        if self.final_negotiated_price and self.base_unit_price > 0:
            improvement = ((self.base_unit_price - self.final_negotiated_price) / self.base_unit_price) * 100
            self.price_improvement_percentage = improvement
            return improvement
        return 0


class DeliverySchedule(models.Model):
    """Delivery planning and tracking with advanced optimization"""
    DELIVERY_TYPE = [
        ('single_delivery', 'Single Delivery'),
        ('multiple_deliveries', 'Multiple Deliveries'),
        ('milestone_based', 'Milestone Based'),
        ('progressive_delivery', 'Progressive Delivery'),
        ('just_in_time', 'Just In Time'),
    ]
    
    DELIVERY_STATUS = [
        ('planned', 'Planned'),
        ('confirmed', 'Confirmed'),
        ('in_transit', 'In Transit'),
        ('delivered', 'Delivered'),
        ('delayed', 'Delayed'),
        ('rescheduled', 'Rescheduled'),
    ]
    
    id = models.AutoField(primary_key=True)
    planning_detail = models.ForeignKey(
        PlanningDetail,
        on_delete=models.CASCADE,
        related_name='delivery_schedules'
    )
    supplier_quotation = models.ForeignKey(
        SupplierQuotation,
        on_delete=models.CASCADE,
        related_name='delivery_schedules'
    )
    
    # Delivery identification
    delivery_schedule_number = models.CharField(max_length=50)
    delivery_type = models.CharField(max_length=20, choices=DELIVERY_TYPE, default='single_delivery')
    delivery_description = models.TextField()
    
    # Delivery planning
    planned_delivery_date = models.DateField()
    confirmed_delivery_date = models.DateField(blank=True, null=True)
    actual_delivery_date = models.DateField(blank=True, null=True)
    delivery_quantity = models.DecimalField(max_digits=12, decimal_places=3, default=0)
    partial_delivery_allowed = models.BooleanField(default=False)
    
    # Delivery logistics
    delivery_location = models.TextField()
    delivery_contact_person = models.CharField(max_length=100, blank=True, null=True)
    delivery_contact_phone = models.CharField(max_length=20, blank=True, null=True)
    special_delivery_instructions = models.TextField(blank=True, null=True)
    
    # Transportation and logistics
    transportation_mode = models.CharField(max_length=50, blank=True, null=True)
    carrier_name = models.CharField(max_length=100, blank=True, null=True)
    tracking_reference = models.CharField(max_length=100, blank=True, null=True)
    estimated_delivery_time = models.CharField(max_length=50, blank=True, null=True)
    transportation_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Delivery requirements
    packaging_requirements = models.TextField(blank=True, null=True)
    handling_requirements = models.TextField(blank=True, null=True)
    storage_requirements = models.TextField(blank=True, null=True)
    inspection_requirements = models.TextField(blank=True, null=True)
    
    # Risk and contingency
    delivery_risk_factors = models.TextField(blank=True, null=True, help_text="JSON array of risks")
    contingency_plans = models.TextField(blank=True, null=True, help_text="JSON array of contingencies")
    backup_suppliers = models.TextField(blank=True, null=True, help_text="JSON array of backup options")
    
    # Performance tracking
    status = models.CharField(max_length=20, choices=DELIVERY_STATUS, default='planned')
    delivery_performance_score = models.DecimalField(max_digits=5, decimal_places=2, default=100)
    on_time_delivery = models.BooleanField(default=True)
    delivery_variance_days = models.IntegerField(default=0)
    quality_at_delivery = models.DecimalField(max_digits=5, decimal_places=2, default=100)
    
    # Cost tracking
    planned_delivery_cost = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    actual_delivery_cost = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    cost_variance = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    expediting_costs = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_delivery_schedule"
        ordering = ['planned_delivery_date', 'delivery_schedule_number']
        unique_together = ['planning_detail', 'delivery_schedule_number']
        
    def __str__(self):
        return f"{self.delivery_schedule_number}: {self.delivery_description}"
    
    @property
    def is_overdue(self):
        """Check if delivery is overdue"""
        if self.status in ['delivered']:
            return False
        
        from django.utils import timezone
        target_date = self.confirmed_delivery_date or self.planned_delivery_date
        return target_date < timezone.now().date()
    
    def calculate_delivery_variance(self):
        """Calculate delivery variance in days"""
        if self.actual_delivery_date:
            target_date = self.confirmed_delivery_date or self.planned_delivery_date
            self.delivery_variance_days = (self.actual_delivery_date - target_date).days
            self.on_time_delivery = self.delivery_variance_days <= 0
        return self.delivery_variance_days
    
    def update_cost_variance(self):
        """Update cost variance"""
        self.cost_variance = self.actual_delivery_cost - self.planned_delivery_cost


# =============================================================================
# TASK GROUP 4: PLANNING REPORTING AND ANALYTICS
# Business intelligence and performance tracking system
# =============================================================================

class PlanningReport(models.Model):
    """Report templates and configurations for material planning analytics"""
    REPORT_TYPE = [
        ('dashboard', 'Executive Dashboard'),
        ('performance', 'Performance Report'),
        ('supplier', 'Supplier Analysis'),
        ('cost', 'Cost Analysis'),
        ('schedule', 'Schedule Performance'),
        ('exception', 'Exception Report'),
        ('trend', 'Trend Analysis'),
        ('kpi', 'KPI Summary'),
    ]
    
    REPORT_FREQUENCY = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('quarterly', 'Quarterly'),
        ('annually', 'Annually'),
        ('on_demand', 'On Demand'),
    ]
    
    REPORT_FORMAT = [
        ('html', 'HTML Dashboard'),
        ('pdf', 'PDF Document'),
        ('excel', 'Excel Spreadsheet'),
        ('csv', 'CSV Data'),
        ('json', 'JSON Data'),
    ]
    
    id = models.AutoField(primary_key=True)
    report_code = models.CharField(max_length=30, unique=True)
    report_name = models.CharField(max_length=200)
    report_type = models.CharField(max_length=20, choices=REPORT_TYPE)
    report_description = models.TextField()
    
    # Report configuration
    report_template = models.TextField(help_text="JSON object with report template")
    report_parameters = models.TextField(blank=True, null=True, help_text="JSON object with parameters")
    data_sources = models.TextField(help_text="JSON array of data sources")
    filters_configuration = models.TextField(blank=True, null=True, help_text="JSON object with filters")
    
    # Scheduling
    report_frequency = models.CharField(max_length=20, choices=REPORT_FREQUENCY, default='on_demand')
    schedule_configuration = models.TextField(blank=True, null=True, help_text="JSON object with schedule")
    auto_generation_enabled = models.BooleanField(default=False)
    email_distribution_list = models.TextField(blank=True, null=True, help_text="JSON array of email addresses")
    
    # Report output
    report_format = models.CharField(max_length=10, choices=REPORT_FORMAT, default='html')
    output_configuration = models.TextField(blank=True, null=True, help_text="JSON object with output settings")
    retention_days = models.IntegerField(default=90)
    archive_after_days = models.IntegerField(default=365)
    
    # Access control
    access_roles = models.TextField(blank=True, null=True, help_text="JSON array of allowed roles")
    is_public = models.BooleanField(default=False)
    requires_approval = models.BooleanField(default=False)
    
    # Performance
    last_generated = models.DateTimeField(blank=True, null=True)
    generation_duration_seconds = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    average_generation_time = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_planning_report"
        ordering = ['report_type', 'report_name']
        
    def __str__(self):
        return f"{self.report_code}: {self.report_name}"


class PlanningKPI(models.Model):
    """Key performance indicators for material planning"""
    KPI_CATEGORY = [
        ('planning', 'Planning Performance'),
        ('cost', 'Cost Performance'),
        ('schedule', 'Schedule Performance'),
        ('quality', 'Quality Performance'),
        ('supplier', 'Supplier Performance'),
        ('efficiency', 'Efficiency Metrics'),
    ]
    
    KPI_TYPE = [
        ('percentage', 'Percentage'),
        ('ratio', 'Ratio'),
        ('count', 'Count'),
        ('currency', 'Currency Amount'),
        ('days', 'Days'),
        ('score', 'Score (0-100)'),
    ]
    
    KPI_CALCULATION = [
        ('simple', 'Simple Calculation'),
        ('weighted', 'Weighted Average'),
        ('cumulative', 'Cumulative Total'),
        ('trending', 'Trending Analysis'),
        ('benchmark', 'Benchmark Comparison'),
    ]
    
    id = models.AutoField(primary_key=True)
    kpi_code = models.CharField(max_length=30, unique=True)
    kpi_name = models.CharField(max_length=200)
    kpi_category = models.CharField(max_length=20, choices=KPI_CATEGORY)
    kpi_type = models.CharField(max_length=20, choices=KPI_TYPE)
    kpi_description = models.TextField()
    
    # KPI calculation
    calculation_method = models.CharField(max_length=20, choices=KPI_CALCULATION, default='simple')
    calculation_formula = models.TextField(help_text="Formula or SQL for KPI calculation")
    data_source_query = models.TextField(help_text="SQL query for data source")
    
    # Target and thresholds
    target_value = models.DecimalField(max_digits=15, decimal_places=4, default=0)
    warning_threshold = models.DecimalField(max_digits=15, decimal_places=4, default=0)
    critical_threshold = models.DecimalField(max_digits=15, decimal_places=4, default=0)
    unit_of_measure = models.CharField(max_length=20, blank=True, null=True)
    
    # Current values
    current_value = models.DecimalField(max_digits=15, decimal_places=4, default=0)
    previous_value = models.DecimalField(max_digits=15, decimal_places=4, default=0)
    variance_from_target = models.DecimalField(max_digits=15, decimal_places=4, default=0)
    variance_percentage = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    
    # Performance status
    performance_status = models.CharField(max_length=20, default='on_target')  # on_target, warning, critical
    trend_direction = models.CharField(max_length=10, default='stable')  # improving, stable, declining
    last_updated = models.DateTimeField(auto_now=True)
    update_frequency_hours = models.IntegerField(default=24)
    
    # Visualization
    chart_type = models.CharField(max_length=20, default='gauge')
    color_coding = models.TextField(blank=True, null=True, help_text="JSON object with colors")
    display_order = models.IntegerField(default=1)
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_planning_kpi"
        ordering = ['kpi_category', 'display_order']
        
    def __str__(self):
        return f"{self.kpi_code}: {self.kpi_name}"
    
    def calculate_variance(self):
        """Calculate variance from target"""
        if self.target_value != 0:
            self.variance_from_target = self.current_value - self.target_value
            self.variance_percentage = (self.variance_from_target / self.target_value) * 100
        else:
            self.variance_from_target = 0
            self.variance_percentage = 0
    
    def update_performance_status(self):
        """Update performance status based on thresholds"""
        if self.current_value <= self.critical_threshold:
            self.performance_status = 'critical'
        elif self.current_value <= self.warning_threshold:
            self.performance_status = 'warning'
        else:
            self.performance_status = 'on_target'


class PlanningAnalytics(models.Model):
    """Analytical calculations and data aggregations"""
    ANALYTICS_TYPE = [
        ('summary', 'Summary Analytics'),
        ('trend', 'Trend Analysis'),
        ('comparison', 'Comparative Analysis'),
        ('forecast', 'Forecast Analysis'),
        ('variance', 'Variance Analysis'),
        ('correlation', 'Correlation Analysis'),
    ]
    
    AGGREGATION_TYPE = [
        ('count', 'Count'),
        ('sum', 'Sum'),
        ('average', 'Average'),
        ('median', 'Median'),
        ('min', 'Minimum'),
        ('max', 'Maximum'),
        ('standard_deviation', 'Standard Deviation'),
    ]
    
    id = models.AutoField(primary_key=True)
    analytics_code = models.CharField(max_length=30, unique=True)
    analytics_name = models.CharField(max_length=200)
    analytics_type = models.CharField(max_length=20, choices=ANALYTICS_TYPE)
    analytics_description = models.TextField()
    
    # Data configuration
    data_source_tables = models.TextField(help_text="JSON array of source tables")
    aggregation_type = models.CharField(max_length=20, choices=AGGREGATION_TYPE, default='sum')
    grouping_fields = models.TextField(blank=True, null=True, help_text="JSON array of grouping fields")
    filter_conditions = models.TextField(blank=True, null=True, help_text="JSON object with filters")
    
    # Time-based analysis
    time_dimension = models.CharField(max_length=20, default='monthly')  # daily, weekly, monthly, quarterly
    historical_periods = models.IntegerField(default=12)
    baseline_period = models.CharField(max_length=50, blank=True, null=True)
    
    # Results storage
    calculated_results = models.TextField(blank=True, null=True, help_text="JSON object with results")
    calculation_metadata = models.TextField(blank=True, null=True, help_text="JSON object with metadata")
    last_calculation_date = models.DateTimeField(blank=True, null=True)
    calculation_duration_seconds = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    
    # Scheduling
    auto_calculation_enabled = models.BooleanField(default=True)
    calculation_frequency_hours = models.IntegerField(default=24)
    next_calculation_due = models.DateTimeField(blank=True, null=True)
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_planning_analytics"
        ordering = ['analytics_type', 'analytics_name']
        
    def __str__(self):
        return f"{self.analytics_code}: {self.analytics_name}"


class PlanningTrend(models.Model):
    """Trend analysis data for planning metrics"""
    TREND_TYPE = [
        ('linear', 'Linear Trend'),
        ('exponential', 'Exponential Trend'),
        ('seasonal', 'Seasonal Trend'),
        ('cyclical', 'Cyclical Trend'),
        ('irregular', 'Irregular Pattern'),
    ]
    
    TREND_DIRECTION = [
        ('increasing', 'Increasing'),
        ('decreasing', 'Decreasing'),
        ('stable', 'Stable'),
        ('volatile', 'Volatile'),
    ]
    
    id = models.AutoField(primary_key=True)
    planning_kpi = models.ForeignKey(
        PlanningKPI,
        on_delete=models.CASCADE,
        related_name='trend_data'
    )
    
    # Trend period
    trend_date = models.DateField()
    period_type = models.CharField(max_length=20, default='monthly')  # daily, weekly, monthly, quarterly
    period_value = models.CharField(max_length=20)  # e.g., "2024-01", "2024-Q1"
    
    # Trend values
    actual_value = models.DecimalField(max_digits=15, decimal_places=4, default=0)
    trend_value = models.DecimalField(max_digits=15, decimal_places=4, default=0)
    seasonal_component = models.DecimalField(max_digits=15, decimal_places=4, default=0)
    irregular_component = models.DecimalField(max_digits=15, decimal_places=4, default=0)
    
    # Trend analysis
    trend_type = models.CharField(max_length=20, choices=TREND_TYPE, default='linear')
    trend_direction = models.CharField(max_length=20, choices=TREND_DIRECTION, default='stable')
    trend_strength = models.DecimalField(max_digits=5, decimal_places=2, default=0)  # 0-100
    trend_confidence = models.DecimalField(max_digits=5, decimal_places=2, default=0)  # 0-100
    
    # Statistical measures
    moving_average_3 = models.DecimalField(max_digits=15, decimal_places=4, default=0)
    moving_average_6 = models.DecimalField(max_digits=15, decimal_places=4, default=0)
    moving_average_12 = models.DecimalField(max_digits=15, decimal_places=4, default=0)
    standard_deviation = models.DecimalField(max_digits=15, decimal_places=4, default=0)
    
    # Forecasting
    forecast_next_period = models.DecimalField(max_digits=15, decimal_places=4, default=0)
    forecast_confidence_interval = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    forecast_accuracy = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_planning_trend"
        ordering = ['planning_kpi', '-trend_date']
        unique_together = ['planning_kpi', 'trend_date', 'period_type']
        
    def __str__(self):
        return f"{self.planning_kpi.kpi_code} - {self.period_value}"


class SupplierPerformance(models.Model):
    """Supplier performance metrics and evaluation"""
    PERFORMANCE_PERIOD = [
        ('monthly', 'Monthly'),
        ('quarterly', 'Quarterly'),
        ('annually', 'Annually'),
        ('ytd', 'Year to Date'),
    ]
    
    PERFORMANCE_RATING = [
        ('excellent', 'Excellent (90-100%)'),
        ('good', 'Good (80-89%)'),
        ('satisfactory', 'Satisfactory (70-79%)'),
        ('needs_improvement', 'Needs Improvement (60-69%)'),
        ('poor', 'Poor (<60%)'),
    ]
    
    id = models.AutoField(primary_key=True)
    planning_supplier = models.ForeignKey(
        PlanningSupplier,
        on_delete=models.CASCADE,
        related_name='performance_records'
    )
    
    # Performance period
    performance_period = models.CharField(max_length=20, choices=PERFORMANCE_PERIOD, default='monthly')
    period_start_date = models.DateField()
    period_end_date = models.DateField()
    period_label = models.CharField(max_length=50)  # e.g., "2024-01", "2024-Q1"
    
    # Delivery performance
    total_orders_placed = models.IntegerField(default=0)
    orders_delivered_on_time = models.IntegerField(default=0)
    orders_delivered_late = models.IntegerField(default=0)
    average_delivery_delay_days = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    on_time_delivery_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Quality performance
    total_items_received = models.IntegerField(default=0)
    items_accepted = models.IntegerField(default=0)
    items_rejected = models.IntegerField(default=0)
    quality_acceptance_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    defect_rate_ppm = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Cost performance
    total_order_value = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    cost_savings_achieved = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    cost_overruns = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    cost_competitiveness_index = models.DecimalField(max_digits=5, decimal_places=2, default=100)
    
    # Service performance
    response_time_hours = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    query_resolution_time_hours = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    communication_rating = models.DecimalField(max_digits=5, decimal_places=2, default=80)
    collaboration_rating = models.DecimalField(max_digits=5, decimal_places=2, default=80)
    
    # Overall performance
    delivery_rating = models.CharField(max_length=20, choices=PERFORMANCE_RATING, default='satisfactory')
    quality_rating = models.CharField(max_length=20, choices=PERFORMANCE_RATING, default='satisfactory')
    cost_rating = models.CharField(max_length=20, choices=PERFORMANCE_RATING, default='satisfactory')
    service_rating = models.CharField(max_length=20, choices=PERFORMANCE_RATING, default='satisfactory')
    overall_rating = models.CharField(max_length=20, choices=PERFORMANCE_RATING, default='satisfactory')
    overall_score = models.DecimalField(max_digits=5, decimal_places=2, default=75)
    
    # Performance trends
    performance_trend = models.CharField(max_length=20, default='stable')  # improving, stable, declining
    benchmark_comparison = models.CharField(max_length=20, default='average')  # above, average, below
    
    # Improvement areas
    improvement_areas = models.TextField(blank=True, null=True, help_text="JSON array of improvement areas")
    action_items = models.TextField(blank=True, null=True, help_text="JSON array of action items")
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_supplier_performance"
        ordering = ['-period_start_date', 'planning_supplier']
        unique_together = ['planning_supplier', 'performance_period', 'period_start_date']
        
    def __str__(self):
        return f"{self.planning_supplier.supplier_name} - {self.period_label}"
    
    def calculate_overall_score(self):
        """Calculate weighted overall performance score"""
        # Convert ratings to scores
        rating_scores = {
            'excellent': 95,
            'good': 85,
            'satisfactory': 75,
            'needs_improvement': 65,
            'poor': 50
        }
        
        delivery_score = rating_scores.get(self.delivery_rating, 75)
        quality_score = rating_scores.get(self.quality_rating, 75)
        cost_score = rating_scores.get(self.cost_rating, 75)
        service_score = rating_scores.get(self.service_rating, 75)
        
        # Weighted average: Delivery 30%, Quality 35%, Cost 25%, Service 10%
        self.overall_score = (
            (delivery_score * 0.30) +
            (quality_score * 0.35) +
            (cost_score * 0.25) +
            (service_score * 0.10)
        )
        
        # Set overall rating based on score
        if self.overall_score >= 90:
            self.overall_rating = 'excellent'
        elif self.overall_score >= 80:
            self.overall_rating = 'good'
        elif self.overall_score >= 70:
            self.overall_rating = 'satisfactory'
        elif self.overall_score >= 60:
            self.overall_rating = 'needs_improvement'
        else:
            self.overall_rating = 'poor'
        
        return self.overall_score


class PlanningException(models.Model):
    """Exception and variance tracking for planning activities"""
    EXCEPTION_TYPE = [
        ('cost_variance', 'Cost Variance'),
        ('schedule_variance', 'Schedule Variance'),
        ('quality_issue', 'Quality Issue'),
        ('supplier_issue', 'Supplier Issue'),
        ('delivery_delay', 'Delivery Delay'),
        ('requirement_change', 'Requirement Change'),
        ('resource_constraint', 'Resource Constraint'),
    ]
    
    EXCEPTION_SEVERITY = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]
    
    EXCEPTION_STATUS = [
        ('open', 'Open'),
        ('investigating', 'Under Investigation'),
        ('action_plan', 'Action Plan Created'),
        ('resolving', 'Being Resolved'),
        ('resolved', 'Resolved'),
        ('closed', 'Closed'),
    ]
    
    id = models.AutoField(primary_key=True)
    material_plan = models.ForeignKey(
        MaterialPlan,
        on_delete=models.CASCADE,
        related_name='exceptions'
    )
    planning_line_item = models.ForeignKey(
        PlanningLineItem,
        on_delete=models.CASCADE,
        related_name='exceptions',
        blank=True, null=True
    )
    
    # Exception identification
    exception_number = models.CharField(max_length=50, unique=True)
    exception_type = models.CharField(max_length=20, choices=EXCEPTION_TYPE)
    exception_title = models.CharField(max_length=200)
    exception_description = models.TextField()
    
    # Exception details
    exception_severity = models.CharField(max_length=20, choices=EXCEPTION_SEVERITY, default='medium')
    variance_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    variance_percentage = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    impact_assessment = models.TextField(blank=True, null=True)
    
    # Root cause analysis
    root_cause_analysis = models.TextField(blank=True, null=True)
    contributing_factors = models.TextField(blank=True, null=True, help_text="JSON array of factors")
    
    # Resolution
    status = models.CharField(max_length=20, choices=EXCEPTION_STATUS, default='open')
    action_plan = models.TextField(blank=True, null=True)
    assigned_to = models.CharField(max_length=100, blank=True, null=True)
    target_resolution_date = models.DateField(blank=True, null=True)
    actual_resolution_date = models.DateField(blank=True, null=True)
    resolution_summary = models.TextField(blank=True, null=True)
    
    # Prevention
    preventive_actions = models.TextField(blank=True, null=True, help_text="JSON array of preventive actions")
    lessons_learned = models.TextField(blank=True, null=True)
    
    # Dates
    identified_date = models.DateTimeField(auto_now_add=True)
    escalation_date = models.DateTimeField(blank=True, null=True)
    closure_date = models.DateTimeField(blank=True, null=True)
    
    # Audit fields
    is_active = models.BooleanField(default=True)
    identified_by = models.CharField(max_length=100)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=100, blank=True, null=True)
    updated_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        managed = True
        db_table = "mp_planning_exception"
        ordering = ['-identified_date', 'exception_severity']
        
    def __str__(self):
        return f"{self.exception_number}: {self.exception_title}"
    
    @property
    def is_overdue(self):
        """Check if exception resolution is overdue"""
        if self.status in ['resolved', 'closed']:
            return False
        
        if self.target_resolution_date:
            from django.utils import timezone
            return self.target_resolution_date < timezone.now().date()
        
        return False
    
    def calculate_aging_days(self):
        """Calculate how many days the exception has been open"""
        if self.actual_resolution_date:
            end_date = self.actual_resolution_date
        else:
            from django.utils import timezone
            end_date = timezone.now().date()
        
        return (end_date - self.identified_date.date()).days
