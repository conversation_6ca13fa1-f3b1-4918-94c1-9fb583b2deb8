<!-- accounts/templates/accounts/invoices/bill_booking_authorize.html -->
<!-- Bill Booking Authorization Form Template -->
<!-- ASP.NET Source: Module/Accounts/Transactions/BillBooking_Authorize.aspx -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Bill Booking Authorization - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-emerald-600 to-sap-emerald-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="shield-check" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">
                        Bill Booking Authorization
                    </h1>
                    <p class="text-sm text-sap-gray-600 mt-1">
                        Review and authorize bill for payment processing
                    </p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <!-- Current Authorization Level Badge -->
                <span class="px-3 py-1 rounded-full text-xs font-medium bg-sap-blue-100 text-sap-blue-800">
                    Level {{ object.current_authorization_level|default:"1" }} of {{ object.total_authorization_levels|default:"3" }}
                </span>
                <a href="{% url 'accounts:bill_booking_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Bill Authorization Form -->
    <div class="max-w-7xl mx-auto">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                            <i data-lucide="shield-check" class="w-5 h-5 mr-2 text-sap-emerald-600"></i>
                            Multi-Level Approval Workflow
                        </h3>
                        <p class="text-sm text-sap-gray-600 mt-1">Review bill details and provide authorization decision</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="showApprovalHierarchy()" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="users" class="w-4 h-4 inline mr-2"></i>
                            Approval Hierarchy
                        </button>
                        <button type="button" onclick="sendNotification()" 
                                class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="mail" class="w-4 h-4 inline mr-2"></i>
                            Send Notification
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Authorization Status Overview -->
            <div class="px-6 py-4 bg-sap-emerald-50 border-b border-sap-emerald-100">
                <h4 class="text-sm font-medium text-sap-emerald-800 mb-3 flex items-center">
                    <i data-lucide="workflow" class="w-4 h-4 mr-2"></i>
                    Authorization Progress
                </h4>
                <div class="flex items-center space-x-4">
                    <!-- Level 1 -->
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-sap-emerald-500 rounded-full flex items-center justify-center">
                            <i data-lucide="check" class="w-4 h-4 text-white"></i>
                        </div>
                        <div class="text-sm">
                            <div class="font-medium text-sap-emerald-800">Department Head</div>
                            <div class="text-sap-emerald-600">{{ object.level1_approver|default:"John Smith" }}</div>
                        </div>
                    </div>
                    
                    <div class="flex-1 h-0.5 bg-sap-emerald-300"></div>
                    
                    <!-- Level 2 -->
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 
                            {% if object.current_authorization_level >= 2 %}bg-sap-emerald-500{% else %}bg-sap-gray-300{% endif %} 
                            rounded-full flex items-center justify-center">
                            {% if object.current_authorization_level >= 2 %}
                            <i data-lucide="check" class="w-4 h-4 text-white"></i>
                            {% else %}
                            <span class="text-white text-xs font-bold">2</span>
                            {% endif %}
                        </div>
                        <div class="text-sm">
                            <div class="font-medium text-sap-gray-800">Finance Manager</div>
                            <div class="text-sap-gray-600">{{ object.level2_approver|default:"Sarah Johnson" }}</div>
                        </div>
                    </div>
                    
                    <div class="flex-1 h-0.5 bg-sap-gray-300"></div>
                    
                    <!-- Level 3 -->
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 
                            {% if object.current_authorization_level >= 3 %}bg-sap-emerald-500{% else %}bg-sap-gray-300{% endif %} 
                            rounded-full flex items-center justify-center">
                            {% if object.current_authorization_level >= 3 %}
                            <i data-lucide="check" class="w-4 h-4 text-white"></i>
                            {% else %}
                            <span class="text-white text-xs font-bold">3</span>
                            {% endif %}
                        </div>
                        <div class="text-sm">
                            <div class="font-medium text-sap-gray-800">General Manager</div>
                            <div class="text-sap-gray-600">{{ object.level3_approver|default:"Michael Brown" }}</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <form method="post" id="bill-authorization-form" class="p-6" x-data="billAuthorizationForm()" hx-post="{% url 'accounts:bill_booking_authorize' object.id %}" hx-target="#form-messages">
                {% csrf_token %}
                
                <!-- Form Messages -->
                <div id="form-messages" class="mb-4"></div>
                
                <!-- Bill Summary for Review -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="file-text" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Bill Details for Authorization
                    </h4>
                    <div class="bg-sap-gray-50 border border-sap-gray-200 rounded-lg p-6">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-sap-gray-500 mb-1">Bill Number</label>
                                <p class="text-lg font-semibold text-sap-gray-800">{{ object.bill_number|default:"BILL-2024-001" }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-sap-gray-500 mb-1">Vendor</label>
                                <p class="text-lg font-semibold text-sap-gray-800">{{ object.vendor_name|default:"ABC Suppliers Ltd." }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-sap-gray-500 mb-1">Bill Date</label>
                                <p class="text-lg font-semibold text-sap-gray-800">{{ object.bill_date|date:"M d, Y" }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-sap-gray-500 mb-1">Due Date</label>
                                <p class="text-lg font-semibold text-sap-gray-800">{{ object.due_date|date:"M d, Y" }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-sap-gray-500 mb-1">Bill Type</label>
                                <p class="text-lg font-semibold text-sap-gray-800">{{ object.get_bill_type_display|default:"Purchase Bill" }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-sap-gray-500 mb-1">Total Amount</label>
                                <p class="text-xl font-bold text-sap-emerald-700">₹{{ object.total_amount|default:"50,000.00" }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-sap-gray-500 mb-1">PO Reference</label>
                                <p class="text-lg font-semibold text-sap-gray-800">{{ object.po_number|default:"PO-2024-123" }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-sap-gray-500 mb-1">Requested By</label>
                                <p class="text-lg font-semibold text-sap-gray-800">{{ object.created_by|default:"Jane Doe" }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Account Allocations Review -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="layers" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                        Account Allocations Review
                    </h4>
                    <div class="border border-sap-gray-300 rounded-lg overflow-hidden">
                        <table class="min-w-full divide-y divide-sap-gray-200">
                            <thead class="bg-sap-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Account Head</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Description</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Amount</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Tax</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Total</th>
                                    <th class="px-4 py-3 text-center text-xs font-medium text-sap-gray-500 uppercase tracking-wider">Status</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-sap-gray-200">
                                <!-- Sample data - in real implementation, this would be dynamic -->
                                <tr>
                                    <td class="px-4 py-3 text-sm font-medium text-sap-gray-900">Raw Materials</td>
                                    <td class="px-4 py-3 text-sm text-sap-gray-600">Steel pipes and fittings</td>
                                    <td class="px-4 py-3 text-sm text-sap-gray-900">₹40,000.00</td>
                                    <td class="px-4 py-3 text-sm text-sap-gray-900">₹7,200.00</td>
                                    <td class="px-4 py-3 text-sm font-medium text-sap-gray-900">₹47,200.00</td>
                                    <td class="px-4 py-3 text-center">
                                        <span class="inline-flex px-2 py-1 text-xs font-medium bg-sap-emerald-100 text-sap-emerald-800 rounded-full">
                                            Verified
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 text-sm font-medium text-sap-gray-900">Transportation</td>
                                    <td class="px-4 py-3 text-sm text-sap-gray-600">Delivery charges</td>
                                    <td class="px-4 py-3 text-sm text-sap-gray-900">₹2,500.00</td>
                                    <td class="px-4 py-3 text-sm text-sap-gray-900">₹300.00</td>
                                    <td class="px-4 py-3 text-sm font-medium text-sap-gray-900">₹2,800.00</td>
                                    <td class="px-4 py-3 text-center">
                                        <span class="inline-flex px-2 py-1 text-xs font-medium bg-sap-emerald-100 text-sap-emerald-800 rounded-full">
                                            Verified
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Authorization Decision -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="gavel" class="w-5 h-5 mr-2 text-sap-emerald-600"></i>
                        Authorization Decision
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Decision -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Decision *
                            </label>
                            <select name="authorization_decision" required x-model="authorizationDecision" @change="updateDecisionFields()"
                                    class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500">
                                <option value="">Select decision</option>
                                <option value="approve">Approve</option>
                                <option value="reject">Reject</option>
                                <option value="hold">Put on Hold</option>
                                <option value="return">Return for Revision</option>
                            </select>
                        </div>
                        
                        <!-- Priority Level (shown if approved) -->
                        <div x-show="authorizationDecision === 'approve'">
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Payment Priority
                            </label>
                            <select name="payment_priority"
                                    class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-emerald-500 focus:border-sap-emerald-500">
                                <option value="normal">Normal</option>
                                <option value="high">High</option>
                                <option value="urgent">Urgent</option>
                            </select>
                        </div>
                        
                        <!-- Rejection Reason (shown if rejected) -->
                        <div x-show="authorizationDecision === 'reject'" class="md:col-span-2">
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Rejection Reason *
                            </label>
                            <select name="rejection_reason"
                                    class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-red-500 focus:border-sap-red-500">
                                <option value="">Select rejection reason</option>
                                <option value="insufficient_documentation">Insufficient Documentation</option>
                                <option value="budget_exceeded">Budget Exceeded</option>
                                <option value="unauthorized_vendor">Unauthorized Vendor</option>
                                <option value="duplicate_bill">Duplicate Bill</option>
                                <option value="incorrect_amounts">Incorrect Amounts</option>
                                <option value="missing_po_reference">Missing PO Reference</option>
                                <option value="policy_violation">Policy Violation</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- Authorization Comments -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="message-square" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Authorization Comments
                    </h4>
                    <div class="grid grid-cols-1 gap-6">
                        <!-- Comments -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Comments *
                            </label>
                            <textarea rows="4" name="authorization_comments" required
                                      placeholder="Enter detailed comments about your authorization decision..."
                                      class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500 resize-none"></textarea>
                            <p class="text-xs text-sap-gray-500 mt-1">Comments will be included in notification emails and audit trail</p>
                        </div>
                        
                        <!-- Internal Notes -->
                        <div>
                            <label class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Internal Notes (Optional)
                            </label>
                            <textarea rows="2" name="internal_notes"
                                      placeholder="Enter internal notes for future reference (not shared with requestor)..."
                                      class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500 resize-none"></textarea>
                        </div>
                    </div>
                </div>
                
                <!-- Email Notifications -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="mail" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                        Email Notifications
                    </h4>
                    <div class="bg-sap-purple-50 border border-sap-purple-200 rounded-lg p-4">
                        <div class="space-y-3">
                            <div class="flex items-start space-x-3">
                                <input type="checkbox" id="notify_requestor" name="notify_requestor" checked
                                       class="mt-1 h-4 w-4 text-sap-purple-600 border-sap-purple-300 rounded focus:ring-sap-purple-500">
                                <div class="flex-1">
                                    <label for="notify_requestor" class="text-sm font-medium text-sap-purple-800">
                                        Notify Bill Requestor
                                    </label>
                                    <p class="text-xs text-sap-purple-600 mt-1">Send notification to {{ object.created_by|default:"Jane Doe" }}</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start space-x-3">
                                <input type="checkbox" id="notify_next_approver" name="notify_next_approver" 
                                       {% if object.current_authorization_level < object.total_authorization_levels %}checked{% endif %}
                                       class="mt-1 h-4 w-4 text-sap-purple-600 border-sap-purple-300 rounded focus:ring-sap-purple-500">
                                <div class="flex-1">
                                    <label for="notify_next_approver" class="text-sm font-medium text-sap-purple-800">
                                        Notify Next Approver
                                    </label>
                                    <p class="text-xs text-sap-purple-600 mt-1">Send notification to next level approver (if applicable)</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start space-x-3">
                                <input type="checkbox" id="notify_finance_team" name="notify_finance_team"
                                       class="mt-1 h-4 w-4 text-sap-purple-600 border-sap-purple-300 rounded focus:ring-sap-purple-500">
                                <div class="flex-1">
                                    <label for="notify_finance_team" class="text-sm font-medium text-sap-purple-800">
                                        Notify Finance Team
                                    </label>
                                    <p class="text-xs text-sap-purple-600 mt-1">Send notification to finance team for payment processing</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
                    <div class="flex items-center space-x-3">
                        <button type="button" @click="validateAuthorization()" 
                                class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check-circle" class="w-4 h-4 inline mr-2"></i>
                            Validate Decision
                        </button>
                        <button type="button" @click="previewNotification()" 
                                class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="eye" class="w-4 h-4 inline mr-2"></i>
                            Preview Notification
                        </button>
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="{% url 'accounts:bill_booking_list' %}" 
                           class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-sap-emerald-600 hover:bg-sap-emerald-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                            Submit Authorization
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Authorization Guidelines -->
        <div class="mt-6 bg-sap-emerald-50 border border-sap-emerald-200 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i data-lucide="info" class="w-5 h-5 text-sap-emerald-600"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-sap-emerald-800">Bill Authorization Guidelines</h3>
                    <div class="mt-2 text-sm text-sap-emerald-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Review all bill details and account allocations for accuracy</li>
                            <li>Verify vendor information and purchase order references</li>
                            <li>Check budget availability and spending authority limits</li>
                            <li>Provide detailed comments for audit trail and transparency</li>
                            <li>Email notifications ensure workflow continuity</li>
                            <li>Rejected bills require clear reasons for revision</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Approval Hierarchy Modal -->
<div id="approval-hierarchy-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-96 overflow-hidden">
            <div class="px-6 py-4 border-b border-sap-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-sap-gray-800">Approval Hierarchy</h3>
                    <button type="button" onclick="closeApprovalHierarchy()" class="text-sap-gray-400 hover:text-sap-gray-600">
                        <i data-lucide="x" class="w-5 h-5"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div class="space-y-4" id="approval-hierarchy-content">
                    <div class="flex items-center space-x-4 p-4 bg-sap-emerald-50 rounded-lg">
                        <div class="w-10 h-10 bg-sap-emerald-500 rounded-full flex items-center justify-center">
                            <span class="text-white font-bold">1</span>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-medium text-sap-gray-800">Department Head Approval</h4>
                            <p class="text-sm text-sap-gray-600">Technical and operational validation</p>
                            <p class="text-xs text-sap-emerald-600 font-medium">Authority: Up to ₹1,00,000</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4 p-4 bg-sap-blue-50 rounded-lg">
                        <div class="w-10 h-10 bg-sap-blue-500 rounded-full flex items-center justify-center">
                            <span class="text-white font-bold">2</span>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-medium text-sap-gray-800">Finance Manager Approval</h4>
                            <p class="text-sm text-sap-gray-600">Budget verification and financial compliance</p>
                            <p class="text-xs text-sap-blue-600 font-medium">Authority: Up to ₹5,00,000</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4 p-4 bg-sap-purple-50 rounded-lg">
                        <div class="w-10 h-10 bg-sap-purple-500 rounded-full flex items-center justify-center">
                            <span class="text-white font-bold">3</span>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-medium text-sap-gray-800">General Manager Approval</h4>
                            <p class="text-sm text-sap-gray-600">Strategic and policy compliance review</p>
                            <p class="text-xs text-sap-purple-600 font-medium">Authority: Unlimited</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function billAuthorizationForm() {
    return {
        authorizationDecision: '',
        
        updateDecisionFields() {
            // Update form fields based on decision
            console.log('Authorization decision changed to:', this.authorizationDecision);
        },
        
        validateAuthorization() {
            const decision = this.authorizationDecision;
            const comments = document.querySelector('textarea[name="authorization_comments"]').value.trim();
            
            if (!decision) {
                alert('Please select an authorization decision.');
                return;
            }
            
            if (!comments) {
                alert('Please provide authorization comments.');
                return;
            }
            
            if (decision === 'reject') {
                const rejectionReason = document.querySelector('select[name="rejection_reason"]').value;
                if (!rejectionReason) {
                    alert('Please select a rejection reason.');
                    return;
                }
            }
            
            alert('Authorization decision validation passed. All required fields are completed.');
        },
        
        previewNotification() {
            const decision = this.authorizationDecision;
            if (!decision) {
                alert('Please select an authorization decision first.');
                return;
            }
            
            const notifyRequestor = document.querySelector('input[name="notify_requestor"]').checked;
            const notifyNextApprover = document.querySelector('input[name="notify_next_approver"]').checked;
            const notifyFinanceTeam = document.querySelector('input[name="notify_finance_team"]').checked;
            
            let recipients = [];
            if (notifyRequestor) recipients.push('Bill Requestor');
            if (notifyNextApprover) recipients.push('Next Approver');
            if (notifyFinanceTeam) recipients.push('Finance Team');
            
            alert(`Notification Preview:\n\nDecision: ${decision.charAt(0).toUpperCase() + decision.slice(1)}\nRecipients: ${recipients.join(', ')}\nBill: {{ object.bill_number|default:"BILL-2024-001" }}\nAmount: ₹{{ object.total_amount|default:"50,000.00" }}`);
        }
    }
}

function showApprovalHierarchy() {
    document.getElementById('approval-hierarchy-modal').classList.remove('hidden');
}

function closeApprovalHierarchy() {
    document.getElementById('approval-hierarchy-modal').classList.add('hidden');
}

function sendNotification() {
    alert('Send notification functionality would allow sending immediate alerts to stakeholders.');
}

// Auto-setup on page load
document.addEventListener('DOMContentLoaded', function() {
    // Auto-check appropriate notification boxes based on decision
    const decisionSelect = document.querySelector('select[name="authorization_decision"]');
    if (decisionSelect) {
        decisionSelect.addEventListener('change', function() {
            const decision = this.value;
            const notifyFinanceTeam = document.querySelector('input[name="notify_finance_team"]');
            
            if (decision === 'approve' && notifyFinanceTeam) {
                notifyFinanceTeam.checked = true;
            }
        });
    }
    
    lucide.createIcons();
});
</script>
{% endblock %}