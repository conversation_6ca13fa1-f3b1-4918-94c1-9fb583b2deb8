{% extends "core/base.html" %}
{% load static %}

{% block title %}Material Requisition Slips - Inventory{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="md:flex md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                    Material Requisition Slips
                </h2>
                <p class="mt-1 text-sm text-gray-500">
                    Manage material requisitions and track approval workflow
                </p>
            </div>
            <div class="mt-4 flex md:mt-0 md:ml-4">
                <a href="{% url 'inventory:mrs_create' %}" 
                   class="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                    </svg>
                    New MRS
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total MRS</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ stats.total_mrs }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Pending Approval</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ stats.pending_approval }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Approved</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ stats.approved_mrs }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L5.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Urgent</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ stats.urgent_mrs }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <form method="get" class="space-y-4">
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-6">
                    <div class="lg:col-span-2">
                        <label for="{{ search_form.search.id_for_label }}" class="block text-sm font-medium text-gray-700">Search</label>
                        {{ search_form.search }}
                    </div>
                    <div>
                        <label for="{{ search_form.status.id_for_label }}" class="block text-sm font-medium text-gray-700">Status</label>
                        {{ search_form.status }}
                    </div>
                    <div>
                        <label for="{{ search_form.requisition_type.id_for_label }}" class="block text-sm font-medium text-gray-700">Type</label>
                        {{ search_form.requisition_type }}
                    </div>
                    <div>
                        <label for="{{ search_form.priority.id_for_label }}" class="block text-sm font-medium text-gray-700">Priority</label>
                        {{ search_form.priority }}
                    </div>
                    <div>
                        <label for="{{ search_form.date_from.id_for_label }}" class="block text-sm font-medium text-gray-700">From Date</label>
                        {{ search_form.date_from }}
                    </div>
                </div>
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-6">
                    <div>
                        <label for="{{ search_form.date_to.id_for_label }}" class="block text-sm font-medium text-gray-700">To Date</label>
                        {{ search_form.date_to }}
                    </div>
                    <div class="sm:col-span-5 flex items-end">
                        <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Apply Filters
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- MRS Results Table -->
    <div id="mrs-results">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <!-- Table Header -->
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">Material Requisition Records</h3>
                    <span class="text-sm text-gray-600">{{ mrs_list|length }} records found</span>
                </div>
            </div>

            <!-- Table -->
            <div class="overflow-x-auto">
                <table class="w-full table-auto divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                                SN
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                                Select
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                                Fin Year
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                                MRS No
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                                Date
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Gen By
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for mrs in mrs_list %}
                        <tr class="hover:bg-gray-50 transition-colors duration-150 {% cycle 'bg-white' 'bg-gray-25' %}">
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 border-r border-gray-200">
                                {{ forloop.counter }}
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm border-r border-gray-200">
                                <a href="{% url 'inventory:mrs_detail' mrs.pk %}" 
                                   class="text-blue-600 hover:text-blue-800 font-medium hover:underline transition-colors duration-150">
                                    Select
                                </a>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 border-r border-gray-200">
                                {{ mrs.fin_year|default:"2022-2023" }}
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 border-r border-gray-200 font-medium">
                                {{ mrs.mrs_number }}
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 border-r border-gray-200">
                                {% if mrs.sys_date %}
                                    {% if mrs.sys_date|length == 10 %}
                                        {% with mrs.sys_date|slice:"8:10" as day %}
                                        {% with mrs.sys_date|slice:"5:7" as month %}
                                        {% with mrs.sys_date|slice:"0:4" as year %}
                                            {{ day }}-{{ month }}-{{ year }}
                                        {% endwith %}
                                        {% endwith %}
                                        {% endwith %}
                                    {% else %}
                                        {{ mrs.sys_date }}
                                    {% endif %}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                {{ mrs.employee_name|default:"Unknown Employee" }}
                                {% if mrs.department and mrs.department != "N/A" %}
                                    <div class="text-xs text-gray-500">{{ mrs.department }}</div>
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="px-4 py-8 text-center text-sm text-gray-500">
                                <div class="flex flex-col items-center">
                                    <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    <p class="text-gray-500">No Material Requisition Slips found</p>
                                    <p class="text-xs text-gray-400 mt-1">Try adjusting your search criteria</p>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}