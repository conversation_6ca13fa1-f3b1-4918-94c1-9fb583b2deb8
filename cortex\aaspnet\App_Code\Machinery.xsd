﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="Machinery" targetNamespace="http://tempuri.org/Machinery.xsd" xmlns:mstns="http://tempuri.org/Machinery.xsd" xmlns="http://tempuri.org/Machinery.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="Machinery" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="Machinery" msprop:Generator_DataSetName="Machinery">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" msprop:Generator_UserColumnName="Id" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInTable="IdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="SysDate" msprop:Generator_UserColumnName="SysDate" msprop:Generator_ColumnPropNameInRow="SysDate" msprop:Generator_ColumnVarNameInTable="columnSysDate" msprop:Generator_ColumnPropNameInTable="SysDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="ItemCode" msprop:Generator_UserColumnName="ItemCode" msprop:Generator_ColumnPropNameInRow="ItemCode" msprop:Generator_ColumnVarNameInTable="columnItemCode" msprop:Generator_ColumnPropNameInTable="ItemCodeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="UOM" msprop:Generator_UserColumnName="UOM" msprop:Generator_ColumnPropNameInRow="UOM" msprop:Generator_ColumnVarNameInTable="columnUOM" msprop:Generator_ColumnPropNameInTable="UOMColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Name" msprop:Generator_UserColumnName="Name" msprop:Generator_ColumnPropNameInRow="Name" msprop:Generator_ColumnVarNameInTable="columnName" msprop:Generator_ColumnPropNameInTable="NameColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Make" msprop:Generator_UserColumnName="Make" msprop:Generator_ColumnPropNameInRow="Make" msprop:Generator_ColumnVarNameInTable="columnMake" msprop:Generator_ColumnPropNameInTable="MakeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Model" msprop:Generator_UserColumnName="Model" msprop:Generator_ColumnPropNameInRow="Model" msprop:Generator_ColumnVarNameInTable="columnModel" msprop:Generator_ColumnPropNameInTable="ModelColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Capacity" msprop:Generator_UserColumnName="Capacity" msprop:Generator_ColumnPropNameInRow="Capacity" msprop:Generator_ColumnVarNameInTable="columnCapacity" msprop:Generator_ColumnPropNameInTable="CapacityColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PurchaseDate" msprop:Generator_UserColumnName="PurchaseDate" msprop:Generator_ColumnPropNameInRow="PurchaseDate" msprop:Generator_ColumnVarNameInTable="columnPurchaseDate" msprop:Generator_ColumnPropNameInTable="PurchaseDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="SupplierName" msprop:Generator_UserColumnName="SupplierName" msprop:Generator_ColumnPropNameInRow="SupplierName" msprop:Generator_ColumnVarNameInTable="columnSupplierName" msprop:Generator_ColumnPropNameInTable="SupplierNameColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Cost" msprop:Generator_UserColumnName="Cost" msprop:Generator_ColumnPropNameInRow="Cost" msprop:Generator_ColumnVarNameInTable="columnCost" msprop:Generator_ColumnPropNameInTable="CostColumn" type="xs:double" minOccurs="0" />
              <xs:element name="WarrantyExpiryDate" msprop:Generator_UserColumnName="WarrantyExpiryDate" msprop:Generator_ColumnPropNameInRow="WarrantyExpiryDate" msprop:Generator_ColumnVarNameInTable="columnWarrantyExpiryDate" msprop:Generator_ColumnPropNameInTable="WarrantyExpiryDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="LifeDate" msprop:Generator_UserColumnName="LifeDate" msprop:Generator_ColumnPropNameInRow="LifeDate" msprop:Generator_ColumnVarNameInTable="columnLifeDate" msprop:Generator_ColumnPropNameInTable="LifeDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="ReceivedDate" msprop:Generator_UserColumnName="ReceivedDate" msprop:Generator_ColumnPropNameInRow="ReceivedDate" msprop:Generator_ColumnVarNameInTable="columnReceivedDate" msprop:Generator_ColumnPropNameInTable="ReceivedDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Insurance" msprop:Generator_UserColumnName="Insurance" msprop:Generator_ColumnPropNameInRow="Insurance" msprop:Generator_ColumnVarNameInTable="columnInsurance" msprop:Generator_ColumnPropNameInTable="InsuranceColumn" type="xs:string" minOccurs="0" />
              <xs:element name="InsuranceExpiryDate" msprop:Generator_UserColumnName="InsuranceExpiryDate" msprop:Generator_ColumnPropNameInRow="InsuranceExpiryDate" msprop:Generator_ColumnVarNameInTable="columnInsuranceExpiryDate" msprop:Generator_ColumnPropNameInTable="InsuranceExpiryDateColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Puttouse" msprop:Generator_UserColumnName="Puttouse" msprop:Generator_ColumnPropNameInRow="Puttouse" msprop:Generator_ColumnVarNameInTable="columnPuttouse" msprop:Generator_ColumnPropNameInTable="PuttouseColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Incharge" msprop:Generator_UserColumnName="Incharge" msprop:Generator_ColumnPropNameInRow="Incharge" msprop:Generator_ColumnVarNameInTable="columnIncharge" msprop:Generator_ColumnPropNameInTable="InchargeColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Location" msprop:Generator_UserColumnName="Location" msprop:Generator_ColumnPropNameInRow="Location" msprop:Generator_ColumnVarNameInTable="columnLocation" msprop:Generator_ColumnPropNameInTable="LocationColumn" type="xs:string" minOccurs="0" />
              <xs:element name="PMDays" msprop:Generator_UserColumnName="PMDays" msprop:Generator_ColumnPropNameInRow="PMDays" msprop:Generator_ColumnVarNameInTable="columnPMDays" msprop:Generator_ColumnPropNameInTable="PMDaysColumn" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>