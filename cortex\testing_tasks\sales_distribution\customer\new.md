# Customer New - Detailed Flow Diagram

## Exact File Paths Involved

### Primary Files:
1. **`/Users/<USER>/workspace/cortex/aaspnet/Module/SalesDistribution/Masters/CustomerMaster_New.aspx`**
2. **`/Users/<USER>/workspace/cortex/aaspnet/Module/SalesDistribution/Masters/CustomerMaster_New.aspx.cs`**

### Supporting Files:
3. **`~/MasterPage.master`** (Layout template)
4. **`../../../Javascript/PopUpMsg.js`** (Confirmation dialogs)
5. **`../../../Javascript/loadingNotifier.js`** (Loading indicators)
6. **`../../../Css/StyleSheet.css`** (Styling)

---
    
## Visual Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                 CUSTOMER NEW MODULE                                 │
│                     File: CustomerMaster_New.aspx                                  │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                            │
                                            │ inherits
                                            ▼
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                             CODE-BEHIND CLASS                                      │
│                    File: CustomerMaster_New.aspx.cs                               │
│                    Class: Module_SD_Cust_masters_CustomerMaster_New               │
└─────────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                  USER INTERFACE                                    │
│                                                                                     │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                              HEADER SECTION                                │   │
│  │  Customer's Name: [_________________________] [Submit Button]             │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                     │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                           THREE-ADDRESS MATRIX                              │   │
│  │                                                                             │   │
│  │ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐                │   │
│  │ │  REGD. OFFICE   │ │ WORKS/FACTORY   │ │MATERIAL DELIVERY│                │   │
│  │ │                 │ │                 │ │                 │                │   │
│  │ │ Address:        │ │ Address:        │ │ Address:        │                │   │
│  │ │ [____________]  │ │ [____________]  │ │ [____________]  │                │   │
│  │ │                 │ │                 │ │                 │                │   │
│  │ │ Country:        │ │ Country:        │ │ Country:        │                │   │
│  │ │ [▼ Dropdown]    │ │ [▼ Dropdown]    │ │ [▼ Dropdown]    │                │   │
│  │ │      │          │ │      │          │ │      │          │                │   │
│  │ │      ▼          │ │      ▼          │ │      ▼          │                │   │
│  │ │ State:          │ │ State:          │ │ State:          │                │   │
│  │ │ [▼ Dropdown]    │ │ [▼ Dropdown]    │ │ [▼ Dropdown]    │                │   │
│  │ │      │          │ │      │          │ │      │          │                │   │
│  │ │      ▼          │ │      ▼          │ │      ▼          │                │   │
│  │ │ City:           │ │ City:           │ │ City:           │                │   │
│  │ │ [▼ Dropdown]    │ │ [▼ Dropdown]    │ │ [▼ Dropdown]    │                │   │
│  │ │                 │ │                 │ │                 │                │   │
│  │ │ PIN No:         │ │ PIN No:         │ │ PIN No:         │                │   │
│  │ │ [____________]  │ │ [____________]  │ │ [____________]  │                │   │
│  │ │                 │ │                 │ │                 │                │   │
│  │ │ Contact No:     │ │ Contact No:     │ │ Contact No:     │                │   │
│  │ │ [____________]  │ │ [____________]  │ │ [____________]  │                │   │
│  │ │                 │ │                 │ │                 │                │   │
│  │ │ Fax No:         │ │ Fax No:         │ │ Fax No:         │                │   │
│  │ │ [____________]  │ │ [____________]  │ │ [____________]  │                │   │
│  │ └─────────────────┘ └─────────────────┘ └─────────────────┘                │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                     │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                        BUSINESS INFORMATION SECTION                         │   │
│  │                                                                             │   │
│  │ Contact Person: [_______] │ E-mail: [___________] │ Contact No: [_______]   │   │
│  │ Jurisdiction: [_________] │ ECC No: [___________] │ Range: [_____________]   │   │
│  │ Commission: [___________] │ Division: [_________] │ PAN No: [____________]   │   │
│  │ TIN/VAT No: [___________] │ TIN/CST: [__________] │ TDS Code: [__________]   │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                     │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                             REMARKS SECTION                                 │   │
│  │ Remarks: [________________________________________________]                 │   │
│  │          [________________________________________________]                 │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

---

## Process Flow Diagram

```
┌─────────────────┐
│     START       │
│  User Access    │
│ Customer New    │
└─────────────────┘
          │
          ▼
┌─────────────────┐      ┌──────────────────────────────────────┐
│   Page_Load     │ ──── │ File: CustomerMaster_New.aspx.cs    │
│     Event       │      │ Method: Page_Load()                  │
└─────────────────┘      │                                      │
          │               │ 1. sId = Session["username"]        │
          ▼               │ 2. CompId = Session["compid"]        │
┌─────────────────┐      │ 3. FinYearId = Session["finyear"]    │
│  Initialize     │      │                                      │
│  Dropdowns      │ ──── │ 4. fun.dropdownCountry() x3 calls   │
└─────────────────┘      └──────────────────────────────────────┘
          │
          ▼
┌─────────────────┐      ┌──────────────────────────────────────┐
│   User Fills    │      │ FORM CONTROLS:                       │
│   Customer      │ ──── │ • txtNewCustName                     │
│   Information   │      │ • txtNewRegdAdd, txtNewWorkAdd       │
└─────────────────┘      │ • txtNewMaterialDelAdd               │
          │               │ • DDListNew[Regd/Work/Material]      │
          ▼               │   Country/State/City                 │
┌─────────────────┐      │ • Business Info Fields (15+ fields) │
│  Cascading      │      └──────────────────────────────────────┘
│  Dropdown       │
│  Events         │
└─────────────────┘
          │
          ├─ Country Selected ──┐
          │                    ▼
          │           ┌─────────────────┐      ┌──────────────────────────────────────┐
          │           │ AutoPostBack    │ ──── │ DDListNewRegdCountry_               │
          │           │   Triggers      │      │ SelectedIndexChanged()               │
          │           └─────────────────┘      │                                      │
          │                    │               │ fun.dropdownState(                   │
          │                    ▼               │   DDListNewRegdState,                │
          │           ┌─────────────────┐      │   DDListNewRegdCity,                 │
          │           │ State Dropdown  │ ──── │   DDListNewRegdCountry)              │
          │           │  Populated      │      └──────────────────────────────────────┘
          │           └─────────────────┘
          │                    │
          │                    ├─ State Selected ──┐
          │                    │                   ▼
          │                    │          ┌─────────────────┐      ┌──────────────────────────────────────┐
          │                    │          │ AutoPostBack    │ ──── │ DDListNewRegdState_                  │
          │                    │          │   Triggers      │      │ SelectedIndexChanged()               │
          │                    │          └─────────────────┘      │                                      │
          │                    │                   │               │ fun.dropdownCity(                    │
          │                    │                   ▼               │   DDListNewRegdCity,                 │
          │                    │          ┌─────────────────┐      │   DDListNewRegdState)                │
          │                    │          │ City Dropdown   │ ──── │                                      │
          │                    │          │  Populated      │      └──────────────────────────────────────┘
          │                    │          └─────────────────┘
          │                    │
          ▼                    ▼
┌─────────────────┐      ┌─────────────────┐
│   Continue      │      │   Continue      │
│   Filling       │      │   Filling       │
│   Form          │      │   Form          │
└─────────────────┘      └─────────────────┘
          │                    │
          └────────┬───────────┘
                   ▼
┌─────────────────┐      ┌──────────────────────────────────────┐
│   User Clicks   │      │ CLIENT-SIDE:                         │
│     Submit      │ ──── │ OnClientClick="return               │
└─────────────────┘      │ confirmationAdd()"                  │
          │               │                                      │
          ▼               │ File: ../../../Javascript/           │
┌─────────────────┐      │       PopUpMsg.js                    │
│   JavaScript    │ ──── │                                      │
│  Confirmation   │      │ Shows: "Are you sure you want to     │
└─────────────────┘      │         add this record?"            │
          │               └──────────────────────────────────────┘
          ▼ (User Clicks OK)
┌─────────────────┐      ┌──────────────────────────────────────┐
│  Submit_Click   │      │ File: CustomerMaster_New.aspx.cs    │
│     Event       │ ──── │ Method: Submit_Click()               │
└─────────────────┘      │                                      │
          │               │ STEP 1: Auto-Generate Customer ID   │
          ▼               └──────────────────────────────────────┘
┌─────────────────┐
│   Generate      │      ┌──────────────────────────────────────┐
│  Customer ID    │ ──── │ string charstr = fun.getCustChar(    │
└─────────────────┘      │     txtNewCustName.Text)             │
          │               │                                      │
          ▼               │ string cmdStr = fun.select(          │
┌─────────────────┐      │     "CustomerId",                    │
│   Database      │      │     "SD_Cust_master",                │
│   Query for     │ ──── │     "CustomerName like '" +          │
│   Existing      │      │      charstr + "%' And CompId='" +   │
│   Customers     │      │      CompId + "' order by            │
└─────────────────┘      │      CustomerId desc");              │
          │               └──────────────────────────────────────┘
          ▼
┌─────────────────┐      ┌──────────────────────────────────────┐
│   ID Logic      │      │ IF (DS.Tables[0].Rows.Count > 0)     │
│  Processing     │ ──── │     Extract last number + 1          │
└─────────────────┘      │     custIdStr = charstr + "XXX"      │
          │               │ ELSE                                 │
          ▼               │     custIdStr = charstr + "001"      │
┌─────────────────┐      └──────────────────────────────────────┘
│   Validation    │
│    Check        │      ┌──────────────────────────────────────┐
└─────────────────┘      │ MASSIVE IF STATEMENT VALIDATION:     │
          │               │                                      │
          ▼               │ • Email Regex Check                  │
┌─────────────────┐      │ • All Text Fields != ""              │
│   All Fields    │ ──── │ • All Dropdowns != "Select"          │
│   Validated     │      │ • All Required Fields Populated      │
└─────────────────┘      │                                      │
          │               │ 25+ Field Validation in Single IF   │
          ▼               └──────────────────────────────────────┘
┌─────────────────┐
│   Database      │      ┌──────────────────────────────────────┐
│   Insert        │ ──── │ string cmdstr = fun.insert(          │
└─────────────────┘      │     "SD_Cust_master",                │
          │               │     "41 FIELD NAMES",                │
          ▼               │     "41 VALUES FROM FORM");          │
┌─────────────────┐      │                                      │
│   SQL INSERT    │      │ SqlCommand cmd = new SqlCommand(     │
│   Execution     │ ──── │     cmdstr, con);                    │
└─────────────────┘      │ cmd.ExecuteNonQuery();               │
          │               └──────────────────────────────────────┘
          ▼
┌─────────────────┐      ┌──────────────────────────────────────┐
│   Success       │      │ Response.Redirect(                   │
│   Redirect      │ ──── │   "CustomerMaster_New.aspx?          │
└─────────────────┘      │    msg=Customer is registered        │
          │               │    sucessfuly&ModId=2&SubModId=7");  │
          ▼               └──────────────────────────────────────┘
┌─────────────────┐
│      END        │
│  Success Page   │
│  with Message   │
└─────────────────┘
```

---

## Database Insert Field Mapping

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                              DATABASE INSERTION                                    │
│                           Table: SD_Cust_master                                    │
└─────────────────────────────────────────────────────────────────────────────────────┘

SYSTEM FIELDS:
┌──────────────────┬──────────────────────────────────────────────────────────────┐
│ Database Column  │ Source Value                                                 │
├──────────────────┼──────────────────────────────────────────────────────────────┤
│ SysDate          │ fun.getCurrDate()                                            │
│ SysTime          │ fun.getCurrTime()                                            │
│ SessionId        │ Session["username"]                                          │
│ CompId           │ Session["compid"]                                            │
│ FinYearId        │ Session["finyear"]                                           │
│ CustomerId       │ Auto-generated (e.g., "ABC001", "XYZ002")                   │
│ CustomerName     │ txtNewCustName.Text.ToUpper()                               │
└──────────────────┴──────────────────────────────────────────────────────────────┘

REGISTERED OFFICE ADDRESS:
┌──────────────────┬──────────────────────────────────────────────────────────────┐
│ RegdAddress      │ txtNewRegdAdd.Text                                           │
│ RegdCountry      │ DDListNewRegdCountry.SelectedValue                           │
│ RegdState        │ DDListNewRegdState.SelectedValue                             │
│ RegdCity         │ DDListNewRegdCity.SelectedValue                              │
│ RegdPinNo        │ txtNewRegdPinNo.Text                                         │
│ RegdContactNo    │ txtNewRegdContactNo.Text                                     │
│ RegdFaxNo        │ txtNewRegdFaxNo.Text                                         │
└──────────────────┴──────────────────────────────────────────────────────────────┘

WORK/FACTORY ADDRESS:
┌──────────────────┬──────────────────────────────────────────────────────────────┐
│ WorkAddress      │ txtNewWorkAdd.Text                                           │
│ WorkCountry      │ DDListNewWorkCountry.SelectedValue                           │
│ WorkState        │ DDListNewWorkState.SelectedValue                             │
│ WorkCity         │ DDListNewWorkCity.SelectedValue                              │
│ WorkPinNo        │ txtNewWorkPinNo.Text                                         │
│ WorkContactNo    │ txtNewWorkContactNo.Text                                     │
│ WorkFaxNo        │ txtNewWorkFaxNo.Text                                         │
└──────────────────┴──────────────────────────────────────────────────────────────┘

MATERIAL DELIVERY ADDRESS:
┌──────────────────┬──────────────────────────────────────────────────────────────┐
│ MaterialDelAddress│ txtNewMaterialDelAdd.Text                                   │
│ MaterialDelCountry│ DDListNewMaterialDelCountry.SelectedValue                   │
│ MaterialDelState │ DDListNewMaterialDelState.SelectedValue                      │
│ MaterialDelCity  │ DDListNewMaterialDelCity.SelectedValue                       │
│ MaterialDelPinNo │ txtNewMaterialDelPinNo.Text                                  │
│ MaterialDelContactNo│ txtNewMaterialDelContactNo.Text                          │
│ MaterialDelFaxNo │ txtNewMaterialDelFaxNo.Text                                  │
└──────────────────┴──────────────────────────────────────────────────────────────┘

BUSINESS INFORMATION:
┌──────────────────┬──────────────────────────────────────────────────────────────┐
│ ContactPerson    │ txtNewContactPerson.Text                                     │
│ JuridictionCode  │ txtNewJuridictionCode.Text                                   │
│ Commissionurate  │ txtNewCommissionurate.Text                                   │
│ TinVatNo         │ txtNewTinVatNo.Text                                          │
│ Email            │ txtNewEmail.Text                                             │
│ EccNo            │ txtNewEccNo.Text                                             │
│ Divn             │ txtNewDivn.Text                                              │
│ TinCstNo         │ txtNewTinCstNo.Text                                          │
│ ContactNo        │ txtNewContactNo.Text                                         │
│ Range            │ txtNewRange.Text                                             │
│ PanNo            │ txtNewPanNo.Text                                             │
│ TDSCode          │ txtNewTdsCode.Text                                           │
│ Remark           │ txtNewRemark.Text                                            │
└──────────────────┴──────────────────────────────────────────────────────────────┘
```

---

## Validation Rules Applied

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                VALIDATION MATRIX                                   │
└─────────────────────────────────────────────────────────────────────────────────────┘

CLIENT-SIDE VALIDATION (ValidationGroup="A"):
┌──────────────────────────────────────────────────────────────────────────────────┐
│ Control                    │ Validator Type           │ Error Message           │
├────────────────────────────┼─────────────────────────┼─────────────────────────┤
│ txtNewCustName             │ RequiredFieldValidator   │ "*"                     │
│ txtNewRegdAdd              │ RequiredFieldValidator   │ "*"                     │
│ DDListNewRegdCountry       │ RequiredFieldValidator   │ "*" (InitialValue)      │
│ DDListNewRegdState         │ RequiredFieldValidator   │ "*" (InitialValue)      │
│ DDListNewRegdCity          │ RequiredFieldValidator   │ "*" (InitialValue)      │
│ txtNewRegdPinNo            │ RequiredFieldValidator   │ "*"                     │
│ txtNewRegdContactNo        │ RequiredFieldValidator   │ "*"                     │
│ txtNewRegdFaxNo            │ RequiredFieldValidator   │ "*"                     │
│ [Same pattern for Work and Material Delivery addresses]                        │
│ txtNewEmail                │ RequiredFieldValidator   │ "*"                     │
│ txtNewEmail                │ RegularExpressionValidator│ "*" (Email Format)    │
│ [All business fields have RequiredFieldValidator]                              │
└──────────────────────────────────────────────────────────────────────────────────┘

SERVER-SIDE VALIDATION (Submit_Click):
┌──────────────────────────────────────────────────────────────────────────────────┐
│ 1. Email Regex Validation:                                                      │
│    string strRegex = @"\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*";          │
│    Regex re = new Regex(strRegex);                                              │
│    if (re.IsMatch(txtNewEmail.Text))                                            │
│                                                                                  │
│ 2. Massive Combined Validation (25+ conditions in single IF):                  │
│    • All text fields != ""                                                      │
│    • All dropdowns != "Select"                                                  │
│    • Session variables populated                                                │
│    • Auto-generated Customer ID exists                                          │
│                                                                                  │
│ 3. Database Constraint Validation:                                              │
│    • Company ID exists                                                          │
│    • Financial Year ID exists                                                   │
│    • Country/State/City ID relationships valid                                  │
└──────────────────────────────────────────────────────────────────────────────────┘
```

This diagram shows the complete technical flow of the Customer New functionality with exact file paths and detailed process steps.