"""
Material Planning Process Forms - Task Group 1
Comprehensive process definition and management forms with validation
"""

from django import forms
from django.forms import inlineformset_factory
from django.core.validators import RegexValidator
from django.core.exceptions import ValidationError
from datetime import date
import json

from ..models import (
    ProcessCategory, ItemProcess, ProcessSequence, ProcessParameter,
    ProcessCapability, ProcessCost
)


class ProcessCategoryForm(forms.ModelForm):
    """Form for process category management with comprehensive validation"""
    
    class Meta:
        model = ProcessCategory
        fields = [
            'category_code', 'category_name', 'category_type', 'description',
            'process_duration_min', 'process_duration_max', 'quality_requirements',
            'requires_certification', 'requires_special_handling', 'environmental_impact'
        ]
        
        widgets = {
            'category_code': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Category Code (e.g., RAW, PROC, FIN)',
                'required': True,
                'x-model': 'categoryData.category_code'
            }),
            'category_name': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Category Name',
                'required': True,
                'x-model': 'categoryData.category_name'
            }),
            'category_type': forms.Select(attrs={
                'class': 'form-select',
                'x-model': 'categoryData.category_type'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 3,
                'placeholder': 'Category description and specifications'
            }),
            'process_duration_min': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'placeholder': 'Minimum duration (minutes)'
            }),
            'process_duration_max': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'placeholder': 'Maximum duration (minutes)'
            }),
            'quality_requirements': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 3,
                'placeholder': 'Quality requirements (JSON format)'
            }),
            'requires_certification': forms.CheckboxInput(attrs={
                'class': 'form-checkbox'
            }),
            'requires_special_handling': forms.CheckboxInput(attrs={
                'class': 'form-checkbox'
            }),
            'environmental_impact': forms.Select(attrs={
                'class': 'form-select'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add environmental impact choices
        self.fields['environmental_impact'].choices = [
            ('low', 'Low Impact'),
            ('medium', 'Medium Impact'),
            ('high', 'High Impact'),
            ('critical', 'Critical Impact'),
        ]
        
        # Make category code uppercase
        self.fields['category_code'].validators.append(
            RegexValidator(
                regex=r'^[A-Z0-9_]+$',
                message="Category code must contain only uppercase letters, numbers and underscores"
            )
        )

    def clean_category_code(self):
        category_code = self.cleaned_data.get('category_code')
        if category_code:
            category_code = category_code.upper()
        return category_code

    def clean(self):
        cleaned_data = super().clean()
        duration_min = cleaned_data.get('process_duration_min', 0)
        duration_max = cleaned_data.get('process_duration_max', 0)
        
        if duration_max > 0 and duration_min > duration_max:
            raise ValidationError("Maximum duration must be greater than minimum duration.")
        
        # Validate quality requirements JSON
        quality_requirements = cleaned_data.get('quality_requirements')
        if quality_requirements:
            try:
                json.loads(quality_requirements)
            except json.JSONDecodeError:
                raise ValidationError("Quality requirements must be valid JSON format.")
        
        return cleaned_data


class ItemProcessForm(forms.ModelForm):
    """Form for manufacturing process definitions with comprehensive validation"""
    
    class Meta:
        model = ItemProcess
        fields = [
            'process_code', 'process_name', 'process_category', 'process_symbol',
            'process_description', 'technical_specification', 'quality_standards',
            'standard_duration_minutes', 'setup_time_minutes', 'breakdown_time_minutes',
            'process_complexity', 'capability_level', 'required_skill_level',
            'required_equipment', 'required_tools', 'required_materials',
            'setup_cost', 'per_unit_cost', 'overhead_percentage', 'labor_cost_per_hour',
            'defect_rate_percentage', 'rework_rate_percentage', 'first_pass_yield',
            'quality_control_points', 'safety_requirements', 'environmental_impact_level',
            'hazardous_materials', 'safety_certifications', 'parent_process',
            'process_sequence', 'is_parallel_process', 'is_critical_path',
            'certification_required', 'certification_valid_until'
        ]
        
        widgets = {
            'process_code': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Process Code (e.g., PROC001)',
                'required': True,
                'x-model': 'processData.process_code'
            }),
            'process_name': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Process Name',
                'required': True,
                'x-model': 'processData.process_name'
            }),
            'process_category': forms.Select(attrs={
                'class': 'form-select',
                'required': True,
                'x-model': 'processData.process_category'
            }),
            'process_symbol': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Symbol (max 10 chars)',
                'maxlength': '10'
            }),
            'process_description': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 4,
                'placeholder': 'Detailed process description',
                'required': True
            }),
            'technical_specification': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 3,
                'placeholder': 'Technical specifications and requirements'
            }),
            'quality_standards': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 3,
                'placeholder': 'Quality standards and acceptance criteria'
            }),
            'standard_duration_minutes': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'placeholder': 'Standard duration (minutes)',
                'required': True,
                'x-on:change': 'calculateTotalTime'
            }),
            'setup_time_minutes': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'placeholder': 'Setup time (minutes)',
                'x-on:change': 'calculateTotalTime'
            }),
            'breakdown_time_minutes': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'placeholder': 'Breakdown buffer (minutes)',
                'x-on:change': 'calculateTotalTime'
            }),
            'process_complexity': forms.Select(attrs={
                'class': 'form-select',
                'x-model': 'processData.complexity'
            }),
            'capability_level': forms.Select(attrs={
                'class': 'form-select',
                'x-model': 'processData.capability'
            }),
            'required_skill_level': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Required skill level'
            }),
            'required_equipment': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 2,
                'placeholder': 'Required equipment (JSON array)'
            }),
            'required_tools': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 2,
                'placeholder': 'Required tools (JSON array)'
            }),
            'required_materials': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 2,
                'placeholder': 'Consumable materials (JSON array)'
            }),
            'setup_cost': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.01',
                'placeholder': 'Setup cost',
                'x-on:change': 'calculateTotalCost'
            }),
            'per_unit_cost': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.0001',
                'placeholder': 'Per unit cost',
                'x-on:change': 'calculateTotalCost'
            }),
            'overhead_percentage': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'max': '100',
                'step': '0.01',
                'placeholder': 'Overhead %',
                'x-on:change': 'calculateTotalCost'
            }),
            'labor_cost_per_hour': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.01',
                'placeholder': 'Labor cost per hour'
            }),
            'defect_rate_percentage': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'max': '100',
                'step': '0.001',
                'placeholder': 'Defect rate %'
            }),
            'rework_rate_percentage': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'max': '100',
                'step': '0.001',
                'placeholder': 'Rework rate %'
            }),
            'first_pass_yield': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'max': '100',
                'step': '0.01',
                'value': '100',
                'placeholder': 'First pass yield %'
            }),
            'quality_control_points': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 3,
                'placeholder': 'QC checkpoints (JSON array)'
            }),
            'safety_requirements': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 3,
                'placeholder': 'Safety requirements and procedures'
            }),
            'environmental_impact_level': forms.Select(attrs={
                'class': 'form-select'
            }),
            'hazardous_materials': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 2,
                'placeholder': 'Hazardous materials (JSON array)'
            }),
            'safety_certifications': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 2,
                'placeholder': 'Required certifications (JSON array)'
            }),
            'parent_process': forms.Select(attrs={
                'class': 'form-select',
                'x-on:change': 'updateProcessHierarchy'
            }),
            'process_sequence': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '1',
                'value': '1',
                'placeholder': 'Process sequence'
            }),
            'is_parallel_process': forms.CheckboxInput(attrs={
                'class': 'form-checkbox'
            }),
            'is_critical_path': forms.CheckboxInput(attrs={
                'class': 'form-checkbox'
            }),
            'certification_required': forms.CheckboxInput(attrs={
                'class': 'form-checkbox',
                'x-model': 'requiresCertification'
            }),
            'certification_valid_until': forms.DateInput(attrs={
                'class': 'form-input',
                'type': 'date',
                'x-show': 'requiresCertification'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Add environmental impact choices
        self.fields['environmental_impact_level'].choices = [
            ('low', 'Low Impact'),
            ('medium', 'Medium Impact'),
            ('high', 'High Impact'),
            ('critical', 'Critical Impact'),
        ]
        
        # Filter parent process to exclude self
        if self.instance and self.instance.pk:
            self.fields['parent_process'].queryset = ItemProcess.objects.exclude(
                pk=self.instance.pk
            ).filter(is_active=True)
        else:
            self.fields['parent_process'].queryset = ItemProcess.objects.filter(
                is_active=True
            )
        
        # Add process code validation
        self.fields['process_code'].validators.append(
            RegexValidator(
                regex=r'^[A-Z0-9_]+$',
                message="Process code must contain only uppercase letters, numbers and underscores"
            )
        )

    def clean_process_code(self):
        process_code = self.cleaned_data.get('process_code')
        if process_code:
            process_code = process_code.upper()
        return process_code

    def clean(self):
        cleaned_data = super().clean()
        
        # Validate JSON fields
        json_fields = ['required_equipment', 'required_tools', 'required_materials', 
                      'quality_control_points', 'hazardous_materials', 'safety_certifications']
        
        for field_name in json_fields:
            field_value = cleaned_data.get(field_name)
            if field_value:
                try:
                    json.loads(field_value)
                except json.JSONDecodeError:
                    self.add_error(field_name, f"{field_name.replace('_', ' ').title()} must be valid JSON format.")
        
        # Validate certification date
        certification_required = cleaned_data.get('certification_required')
        certification_valid_until = cleaned_data.get('certification_valid_until')
        
        if certification_required and not certification_valid_until:
            self.add_error('certification_valid_until', 
                          "Certification validity date is required when certification is mandatory.")
        
        if certification_valid_until and certification_valid_until < date.today():
            self.add_error('certification_valid_until', 
                          "Certification validity date cannot be in the past.")
        
        # Validate parent process hierarchy
        parent_process = cleaned_data.get('parent_process')
        if parent_process and self.instance and self.instance.pk:
            # Check for circular references
            if self._would_create_circular_reference(parent_process):
                self.add_error('parent_process', 
                              "Selected parent process would create a circular reference.")
        
        return cleaned_data

    def _would_create_circular_reference(self, parent_process):
        """Check if setting parent would create circular reference"""
        current_parent = parent_process
        while current_parent:
            if current_parent == self.instance:
                return True
            current_parent = current_parent.parent_process
        return False


class ProcessSequenceForm(forms.ModelForm):
    """Form for process sequence and dependency management"""
    
    class Meta:
        model = ProcessSequence
        fields = [
            'sequence_name', 'predecessor_process', 'successor_process',
            'dependency_type', 'lag_time_minutes', 'lead_time_minutes',
            'is_mandatory', 'is_alternative_path', 'priority_level',
            'condition_expression', 'quality_gate_required', 'approval_required'
        ]
        
        widgets = {
            'sequence_name': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Sequence name',
                'required': True
            }),
            'predecessor_process': forms.Select(attrs={
                'class': 'form-select',
                'required': True,
                'x-on:change': 'updateSequenceValidation'
            }),
            'successor_process': forms.Select(attrs={
                'class': 'form-select',
                'required': True,
                'x-on:change': 'updateSequenceValidation'
            }),
            'dependency_type': forms.Select(attrs={
                'class': 'form-select',
                'x-model': 'sequenceData.dependency_type'
            }),
            'lag_time_minutes': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'value': '0',
                'placeholder': 'Lag time (minutes)'
            }),
            'lead_time_minutes': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'value': '0',
                'placeholder': 'Lead time (minutes)'
            }),
            'is_mandatory': forms.CheckboxInput(attrs={
                'class': 'form-checkbox'
            }),
            'is_alternative_path': forms.CheckboxInput(attrs={
                'class': 'form-checkbox'
            }),
            'priority_level': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '1',
                'value': '1',
                'placeholder': 'Priority level'
            }),
            'condition_expression': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 3,
                'placeholder': 'Conditional logic (JSON format)'
            }),
            'quality_gate_required': forms.CheckboxInput(attrs={
                'class': 'form-checkbox'
            }),
            'approval_required': forms.CheckboxInput(attrs={
                'class': 'form-checkbox'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['predecessor_process'].queryset = ItemProcess.objects.filter(is_active=True)
        self.fields['successor_process'].queryset = ItemProcess.objects.filter(is_active=True)

    def clean(self):
        cleaned_data = super().clean()
        predecessor = cleaned_data.get('predecessor_process')
        successor = cleaned_data.get('successor_process')
        
        if predecessor and successor and predecessor == successor:
            raise ValidationError("Predecessor and successor processes cannot be the same.")
        
        # Validate condition expression JSON
        condition_expression = cleaned_data.get('condition_expression')
        if condition_expression:
            try:
                json.loads(condition_expression)
            except json.JSONDecodeError:
                self.add_error('condition_expression', "Condition expression must be valid JSON format.")
        
        return cleaned_data


class ProcessParameterForm(forms.ModelForm):
    """Form for process parameter configuration"""
    
    class Meta:
        model = ProcessParameter
        fields = [
            'parameter_code', 'parameter_name', 'parameter_type', 'parameter_unit',
            'default_value', 'minimum_value', 'maximum_value', 'allowed_values',
            'is_mandatory', 'is_variable', 'is_quality_parameter', 'is_cost_driver',
            'validation_rules', 'tolerance_percentage', 'update_frequency'
        ]
        
        widgets = {
            'parameter_code': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Parameter code',
                'required': True
            }),
            'parameter_name': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Parameter name',
                'required': True
            }),
            'parameter_type': forms.Select(attrs={
                'class': 'form-select',
                'x-model': 'parameterData.type'
            }),
            'parameter_unit': forms.Select(attrs={
                'class': 'form-select'
            }),
            'default_value': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Default value'
            }),
            'minimum_value': forms.NumberInput(attrs={
                'class': 'form-input',
                'step': '0.0001',
                'placeholder': 'Minimum value'
            }),
            'maximum_value': forms.NumberInput(attrs={
                'class': 'form-input',
                'step': '0.0001',
                'placeholder': 'Maximum value'
            }),
            'allowed_values': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 2,
                'placeholder': 'Allowed values (JSON array)'
            }),
            'is_mandatory': forms.CheckboxInput(attrs={
                'class': 'form-checkbox'
            }),
            'is_variable': forms.CheckboxInput(attrs={
                'class': 'form-checkbox'
            }),
            'is_quality_parameter': forms.CheckboxInput(attrs={
                'class': 'form-checkbox'
            }),
            'is_cost_driver': forms.CheckboxInput(attrs={
                'class': 'form-checkbox'
            }),
            'validation_rules': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 3,
                'placeholder': 'Validation rules (JSON format)'
            }),
            'tolerance_percentage': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'max': '100',
                'step': '0.01',
                'value': '0',
                'placeholder': 'Tolerance %'
            }),
            'update_frequency': forms.Select(attrs={
                'class': 'form-select'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Add update frequency choices
        self.fields['update_frequency'].choices = [
            ('as_needed', 'As Needed'),
            ('daily', 'Daily'),
            ('weekly', 'Weekly'),
            ('monthly', 'Monthly'),
            ('quarterly', 'Quarterly'),
            ('annually', 'Annually'),
        ]

    def clean(self):
        cleaned_data = super().clean()
        parameter_type = cleaned_data.get('parameter_type')
        minimum_value = cleaned_data.get('minimum_value')
        maximum_value = cleaned_data.get('maximum_value')
        
        # Validate min/max values for numeric parameters
        if parameter_type == 'numeric':
            if minimum_value is not None and maximum_value is not None:
                if minimum_value >= maximum_value:
                    raise ValidationError("Maximum value must be greater than minimum value.")
        
        # Validate JSON fields
        json_fields = ['allowed_values', 'validation_rules']
        for field_name in json_fields:
            field_value = cleaned_data.get(field_name)
            if field_value:
                try:
                    json.loads(field_value)
                except json.JSONDecodeError:
                    self.add_error(field_name, f"{field_name.replace('_', ' ').title()} must be valid JSON format.")
        
        return cleaned_data


class ProcessCapabilityForm(forms.ModelForm):
    """Form for process capability assessment"""
    
    class Meta:
        model = ProcessCapability
        fields = [
            'theoretical_capacity_per_hour', 'practical_capacity_per_hour',
            'current_utilization_percentage', 'maximum_capacity_per_day',
            'process_capability_index', 'process_performance_index',
            'defect_rate_ppm', 'first_pass_yield_percentage',
            'variation_coefficient', 'process_stability_index',
            'control_chart_violations', 'capability_status',
            'next_assessment_due', 'improvement_initiatives',
            'target_capability_index', 'target_achievement_date',
            'industry_benchmark', 'best_in_class_benchmark'
        ]
        
        widgets = {
            'theoretical_capacity_per_hour': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.01',
                'placeholder': 'Theoretical capacity/hour'
            }),
            'practical_capacity_per_hour': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.01',
                'placeholder': 'Practical capacity/hour'
            }),
            'current_utilization_percentage': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'max': '100',
                'step': '0.01',
                'placeholder': 'Current utilization %'
            }),
            'maximum_capacity_per_day': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.01',
                'placeholder': 'Maximum capacity/day'
            }),
            'process_capability_index': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.001',
                'value': '1.000',
                'placeholder': 'Cp index'
            }),
            'process_performance_index': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.001',
                'value': '1.000',
                'placeholder': 'Cpk index'
            }),
            'defect_rate_ppm': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.01',
                'placeholder': 'Defect rate (ppm)'
            }),
            'first_pass_yield_percentage': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'max': '100',
                'step': '0.01',
                'value': '100',
                'placeholder': 'FPY %'
            }),
            'variation_coefficient': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.0001',
                'placeholder': 'Variation coefficient'
            }),
            'process_stability_index': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.001',
                'value': '1.000',
                'placeholder': 'Stability index'
            }),
            'control_chart_violations': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'value': '0',
                'placeholder': 'Control chart violations'
            }),
            'capability_status': forms.Select(attrs={
                'class': 'form-select'
            }),
            'next_assessment_due': forms.DateInput(attrs={
                'class': 'form-input',
                'type': 'date'
            }),
            'improvement_initiatives': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 3,
                'placeholder': 'Improvement initiatives (JSON array)'
            }),
            'target_capability_index': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.001',
                'value': '1.330',
                'placeholder': 'Target Cp'
            }),
            'target_achievement_date': forms.DateInput(attrs={
                'class': 'form-input',
                'type': 'date'
            }),
            'industry_benchmark': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.001',
                'placeholder': 'Industry benchmark'
            }),
            'best_in_class_benchmark': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.001',
                'placeholder': 'Best-in-class benchmark'
            }),
        }

    def clean(self):
        cleaned_data = super().clean()
        
        # Validate improvement initiatives JSON
        improvement_initiatives = cleaned_data.get('improvement_initiatives')
        if improvement_initiatives:
            try:
                json.loads(improvement_initiatives)
            except json.JSONDecodeError:
                self.add_error('improvement_initiatives', 
                              "Improvement initiatives must be valid JSON format.")
        
        return cleaned_data


class ProcessCostForm(forms.ModelForm):
    """Form for process cost configuration"""
    
    class Meta:
        model = ProcessCost
        fields = [
            'cost_element_code', 'cost_element_name', 'cost_type', 'cost_category',
            'cost_per_unit', 'cost_per_hour', 'fixed_cost_amount', 'variable_cost_rate',
            'allocation_basis', 'allocation_percentage', 'cost_center', 'budget_account',
            'standard_cost', 'target_cost', 'minimum_achievable_cost',
            'effective_from', 'effective_to', 'cost_update_frequency'
        ]
        
        widgets = {
            'cost_element_code': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Cost element code',
                'required': True
            }),
            'cost_element_name': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Cost element name',
                'required': True
            }),
            'cost_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'cost_category': forms.Select(attrs={
                'class': 'form-select'
            }),
            'cost_per_unit': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.0001',
                'placeholder': 'Cost per unit'
            }),
            'cost_per_hour': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.01',
                'placeholder': 'Cost per hour'
            }),
            'fixed_cost_amount': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.01',
                'placeholder': 'Fixed cost amount'
            }),
            'variable_cost_rate': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.0001',
                'placeholder': 'Variable cost rate'
            }),
            'allocation_basis': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Allocation basis'
            }),
            'allocation_percentage': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'max': '100',
                'step': '0.01',
                'value': '100',
                'placeholder': 'Allocation %'
            }),
            'cost_center': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Cost center'
            }),
            'budget_account': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Budget account'
            }),
            'standard_cost': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.0001',
                'placeholder': 'Standard cost'
            }),
            'target_cost': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.0001',
                'placeholder': 'Target cost'
            }),
            'minimum_achievable_cost': forms.NumberInput(attrs={
                'class': 'form-input',
                'min': '0',
                'step': '0.0001',
                'placeholder': 'Minimum achievable cost'
            }),
            'effective_from': forms.DateInput(attrs={
                'class': 'form-input',
                'type': 'date',
                'required': True
            }),
            'effective_to': forms.DateInput(attrs={
                'class': 'form-input',
                'type': 'date'
            }),
            'cost_update_frequency': forms.Select(attrs={
                'class': 'form-select'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Add cost update frequency choices
        self.fields['cost_update_frequency'].choices = [
            ('weekly', 'Weekly'),
            ('monthly', 'Monthly'),
            ('quarterly', 'Quarterly'),
            ('annually', 'Annually'),
            ('as_needed', 'As Needed'),
        ]

    def clean(self):
        cleaned_data = super().clean()
        effective_from = cleaned_data.get('effective_from')
        effective_to = cleaned_data.get('effective_to')
        
        if effective_from and effective_to and effective_to <= effective_from:
            raise ValidationError("Effective end date must be after start date.")
        
        return cleaned_data


class ProcessSearchForm(forms.Form):
    """Advanced process search form"""
    
    search_query = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input',
            'placeholder': 'Search processes...',
            'x-model': 'searchQuery'
        })
    )
    
    process_category = forms.ModelChoiceField(
        queryset=ProcessCategory.objects.all(),
        required=False,
        empty_label="All Categories",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    process_complexity = forms.ChoiceField(
        choices=[('', 'All Complexity Levels')] + ItemProcess.PROCESS_COMPLEXITY,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    capability_level = forms.ChoiceField(
        choices=[('', 'All Capability Levels')] + ItemProcess.CAPABILITY_LEVELS,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    status = forms.ChoiceField(
        choices=[('', 'All Statuses')] + ItemProcess.PROCESS_STATUS,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    is_critical_path = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-checkbox'})
    )


# Formsets for inline editing
ProcessParameterFormSet = inlineformset_factory(
    ItemProcess, ProcessParameter,
    form=ProcessParameterForm,
    extra=1,
    can_delete=True
)

ProcessCostFormSet = inlineformset_factory(
    ItemProcess, ProcessCost,
    form=ProcessCostForm,
    extra=1,
    can_delete=True
)

ProcessSequenceFormSet = inlineformset_factory(
    ItemProcess, ProcessSequence,
    form=ProcessSequenceForm,
    fk_name='predecessor_process',
    extra=1,
    can_delete=True
)