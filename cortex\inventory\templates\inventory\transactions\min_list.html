{% extends 'core/base.html' %}
{% load static %}

{% block title %}Material Issue Notes (MIN){% endblock %}

{% block content %}
<div class="sap-card" x-data="{ showStats: false }">
    <!-- Header Section -->
    <div class="px-6 py-5 border-b border-sap-gray-200">
        <div class="flex justify-between items-center">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-sap-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <h1 class="text-xl font-semibold text-sap-gray-900">Material Issue Notes (MIN)</h1>
                    <p class="mt-1 text-sm text-sap-gray-600">
                        Manage material issues and track inventory movement
                    </p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <button @click="showStats = !showStats" 
                        class="sap-button-secondary inline-flex items-center px-4 py-2 text-sm font-medium"
                        :class="showStats ? 'bg-sap-blue-50 text-sap-blue-700 border-sap-blue-200' : ''">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <span>Statistics</span>
                    <svg class="h-4 w-4 ml-2 transition-transform duration-200" 
                         :class="showStats ? 'rotate-180' : ''" 
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <a href="{% url 'inventory:min_edit_list' %}" 
                   class="sap-button-secondary inline-flex items-center px-4 py-2 text-sm font-medium">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit MIN
                </a>
                <a href="{% url 'inventory:min_new_search' %}" 
                   class="sap-button-primary inline-flex items-center px-4 py-2 text-sm font-medium">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    New MIN
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Panel -->
    <div x-show="showStats" 
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 transform -translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         x-transition:leave="transition ease-in duration-150"
         x-transition:leave-start="opacity-100 transform translate-y-0"
         x-transition:leave-end="opacity-0 transform -translate-y-2"
         class="px-6 py-5 bg-sap-gray-25 border-b border-sap-gray-200">
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div class="sap-card p-6 text-center">
                <div class="w-12 h-12 bg-sap-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-sap-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <dt class="text-sm font-medium text-sap-gray-500 mb-1">Total MINs</dt>
                <dd class="text-3xl font-bold text-sap-gray-900">{{ stats.total_min|default:0 }}</dd>
            </div>
            <div class="sap-card p-6 text-center">
                <div class="w-12 h-12 bg-sap-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-sap-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <dt class="text-sm font-medium text-sap-gray-500 mb-1">This Month</dt>
                <dd class="text-3xl font-bold text-sap-gray-900">142</dd>
            </div>
            <div class="sap-card p-6 text-center">
                <div class="w-12 h-12 bg-sap-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-sap-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <dt class="text-sm font-medium text-sap-gray-500 mb-1">Pending</dt>
                <dd class="text-3xl font-bold text-sap-gray-900">23</dd>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="px-6 py-5 bg-white border-b border-sap-gray-200">
        <form method="get" class="flex items-end space-x-4">
            <div class="flex-1">
                <label for="search" class="block text-sm font-medium text-sap-gray-700 mb-2">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Search Material Issue Notes
                </label>
                <input type="text" name="search" id="search" value="{{ request.GET.search }}" 
                       class="sap-input block w-full"
                       placeholder="Search by MIN number, MRS number, or session ID...">
            </div>
            <div>
                <button type="submit" class="sap-button-primary px-6 py-2 text-sm font-medium">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Search
                </button>
            </div>
        </form>
    </div>

    <!-- Results -->
    <div id="min-results">
        {% include 'inventory/transactions/partials/min_results.html' %}
    </div>
</div>
{% endblock %}