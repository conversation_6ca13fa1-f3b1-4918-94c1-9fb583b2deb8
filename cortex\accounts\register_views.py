# accounts/register_views.py
# Django views for Cash/Bank Register reports in Accounts module
# Task Group 2: Banking & Cash Management - Register Reports

from django.shortcuts import render, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.views.generic import View, TemplateView
from django.http import JsonResponse, HttpResponse
from django.db.models import Sum, Count
from decimal import Decimal
from datetime import date, datetime, timedelta
import csv

from .models import (
    Bank, CashVoucherMaster, BankVoucherMaster, ContraEntryMaster
)


class CashBankRegisterView(LoginRequiredMixin, TemplateView):
    """
    Main Cash/Bank Register report view
    Replaces ASP.NET Cash_Bank_Register.aspx functionality
    """
    template_name = 'accounts/cash_bank_register.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Initialize form data
        today = date.today()
        first_day = today.replace(day=1)
        
        context.update({
            'banks': Bank.objects.filter(is_active=True).order_by('name'),
            'date_from': self.request.GET.get('date_from', first_day.strftime('%Y-%m-%d')),
            'date_to': self.request.GET.get('date_to', today.strftime('%Y-%m-%d')),
            'selected_bank': self.request.GET.get('bank'),
            'register_type': self.request.GET.get('register_type', 'cash'),
        })
        
        # Get register data if parameters are provided
        if self.request.GET.get('search'):
            context['register_data'] = self.get_register_data()
        
        return context

    def get_register_data(self):
        """Get cash/bank register data based on search parameters"""
        register_type = self.request.GET.get('register_type', 'cash')
        bank_id = self.request.GET.get('bank')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        
        if not all([date_from, date_to]):
            return None
        
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d').date()
            to_date = datetime.strptime(date_to, '%Y-%m-%d').date()
        except ValueError:
            return None
        
        company_id = self.request.session.get('company_id', 1)
        financial_year_id = self.request.session.get('financial_year_id', 1)
        
        if register_type == 'cash':
            return self.get_cash_register_data(from_date, to_date, company_id, financial_year_id)
        elif register_type == 'bank' and bank_id:
            return self.get_bank_register_data(bank_id, from_date, to_date, company_id, financial_year_id)
        elif register_type == 'contra':
            return self.get_contra_register_data(from_date, to_date, company_id, financial_year_id)
        
        return None

    def get_cash_register_data(self, from_date, to_date, company_id, financial_year_id):
        """Get cash register data for the specified period"""
        
        # Get opening balance (all cash transactions before from_date)
        opening_payments = CashVoucherMaster.objects.filter(
            voucher_type='payment',
            voucher_date__lt=from_date,
            company_id=company_id,
            financial_year_id=financial_year_id
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        
        opening_receipts = CashVoucherMaster.objects.filter(
            voucher_type='receipt',
            voucher_date__lt=from_date,
            company_id=company_id,
            financial_year_id=financial_year_id
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        
        opening_balance = opening_receipts - opening_payments
        
        # Get transactions for the period
        cash_transactions = []
        
        # Cash payments
        payments = CashVoucherMaster.objects.filter(
            voucher_type='payment',
            voucher_date__range=[from_date, to_date],
            company_id=company_id,
            financial_year_id=financial_year_id
        ).select_related('account_head').order_by('voucher_date', 'voucher_no')
        
        for payment in payments:
            cash_transactions.append({
                'date': payment.voucher_date,
                'voucher_type': 'Payment',
                'voucher_no': payment.voucher_no,
                'pay_to': payment.pay_to_name,
                'particulars': payment.particulars or '',
                'debit': payment.amount,
                'credit': Decimal('0.00'),
                'balance_type': 'debit',
                'transaction_type': 'cash_payment'
            })
        
        # Cash receipts
        receipts = CashVoucherMaster.objects.filter(
            voucher_type='receipt',
            voucher_date__range=[from_date, to_date],
            company_id=company_id,
            financial_year_id=financial_year_id
        ).select_related('account_head').order_by('voucher_date', 'voucher_no')
        
        for receipt in receipts:
            cash_transactions.append({
                'date': receipt.voucher_date,
                'voucher_type': 'Receipt',
                'voucher_no': receipt.voucher_no,
                'pay_to': receipt.pay_to_name,
                'particulars': receipt.particulars or '',
                'debit': Decimal('0.00'),
                'credit': receipt.amount,
                'balance_type': 'credit',
                'transaction_type': 'cash_receipt'
            })
        
        # Sort all transactions by date and voucher number
        cash_transactions.sort(key=lambda x: (x['date'], x['voucher_no']))
        
        # Calculate running balance
        running_balance = opening_balance
        for transaction in cash_transactions:
            if transaction['balance_type'] == 'credit':
                running_balance += transaction['credit']
            else:
                running_balance -= transaction['debit']
            transaction['running_balance'] = running_balance
        
        # Calculate period totals
        period_payments = sum(t['debit'] for t in cash_transactions)
        period_receipts = sum(t['credit'] for t in cash_transactions)
        closing_balance = opening_balance + period_receipts - period_payments
        
        return {
            'register_type': 'Cash Register',
            'period': f"{from_date.strftime('%d-%m-%Y')} to {to_date.strftime('%d-%m-%Y')}",
            'opening_balance': opening_balance,
            'closing_balance': closing_balance,
            'period_receipts': period_receipts,
            'period_payments': period_payments,
            'transactions': cash_transactions,
            'transaction_count': len(cash_transactions)
        }

    def get_bank_register_data(self, bank_id, from_date, to_date, company_id, financial_year_id):
        """Get bank register data for the specified bank and period"""
        
        try:
            bank = Bank.objects.get(id=bank_id)
        except Bank.DoesNotExist:
            return None
        
        # Get opening balance
        opening_payments = BankVoucherMaster.objects.filter(
            bank=bank,
            voucher_type='payment',
            voucher_date__lt=from_date,
            company_id=company_id,
            financial_year_id=financial_year_id
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        
        opening_receipts = BankVoucherMaster.objects.filter(
            bank=bank,
            voucher_type='receipt',
            voucher_date__lt=from_date,
            company_id=company_id,
            financial_year_id=financial_year_id
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        
        # Add contra entries affecting this bank
        opening_contra_in = ContraEntryMaster.objects.filter(
            to_bank=bank,
            contra_date__lt=from_date,
            company_id=company_id,
            financial_year_id=financial_year_id
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        
        opening_contra_out = ContraEntryMaster.objects.filter(
            from_bank=bank,
            contra_date__lt=from_date,
            company_id=company_id,
            financial_year_id=financial_year_id
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        
        opening_balance = opening_receipts + opening_contra_in - opening_payments - opening_contra_out
        
        # Get transactions for the period
        bank_transactions = []
        
        # Bank payments
        payments = BankVoucherMaster.objects.filter(
            bank=bank,
            voucher_type='payment',
            voucher_date__range=[from_date, to_date],
            company_id=company_id,
            financial_year_id=financial_year_id
        ).select_related('account_head').order_by('voucher_date', 'voucher_no')
        
        for payment in payments:
            bank_transactions.append({
                'date': payment.voucher_date,
                'voucher_type': 'Payment',
                'voucher_no': payment.voucher_no,
                'pay_to': payment.pay_to_name,
                'particulars': payment.particulars or '',
                'cheque_no': payment.cheque_no or '',
                'cheque_date': payment.cheque_date,
                'debit': payment.amount,
                'credit': Decimal('0.00'),
                'balance_type': 'debit',
                'transaction_type': 'bank_payment'
            })
        
        # Bank receipts
        receipts = BankVoucherMaster.objects.filter(
            bank=bank,
            voucher_type='receipt',
            voucher_date__range=[from_date, to_date],
            company_id=company_id,
            financial_year_id=financial_year_id
        ).select_related('account_head').order_by('voucher_date', 'voucher_no')
        
        for receipt in receipts:
            bank_transactions.append({
                'date': receipt.voucher_date,
                'voucher_type': 'Receipt',
                'voucher_no': receipt.voucher_no,
                'pay_to': receipt.pay_to_name,
                'particulars': receipt.particulars or '',
                'cheque_no': receipt.cheque_no or '',
                'cheque_date': receipt.cheque_date,
                'debit': Decimal('0.00'),
                'credit': receipt.amount,
                'balance_type': 'credit',
                'transaction_type': 'bank_receipt'
            })
        
        # Contra entries
        contra_in = ContraEntryMaster.objects.filter(
            to_bank=bank,
            contra_date__range=[from_date, to_date],
            company_id=company_id,
            financial_year_id=financial_year_id
        ).select_related('from_bank').order_by('contra_date', 'contra_no')
        
        for contra in contra_in:
            bank_transactions.append({
                'date': contra.contra_date,
                'voucher_type': 'Contra',
                'voucher_no': contra.contra_no,
                'pay_to': f"From {contra.from_bank.name}",
                'particulars': contra.particulars or '',
                'cheque_no': contra.cheque_no or '',
                'cheque_date': contra.cheque_date,
                'debit': Decimal('0.00'),
                'credit': contra.amount,
                'balance_type': 'credit',
                'transaction_type': 'contra_in'
            })
        
        contra_out = ContraEntryMaster.objects.filter(
            from_bank=bank,
            contra_date__range=[from_date, to_date],
            company_id=company_id,
            financial_year_id=financial_year_id
        ).select_related('to_bank').order_by('contra_date', 'contra_no')
        
        for contra in contra_out:
            bank_transactions.append({
                'date': contra.contra_date,
                'voucher_type': 'Contra',
                'voucher_no': contra.contra_no,
                'pay_to': f"To {contra.to_bank.name}",
                'particulars': contra.particulars or '',
                'cheque_no': contra.cheque_no or '',
                'cheque_date': contra.cheque_date,
                'debit': contra.amount,
                'credit': Decimal('0.00'),
                'balance_type': 'debit',
                'transaction_type': 'contra_out'
            })
        
        # Sort all transactions by date and voucher number
        bank_transactions.sort(key=lambda x: (x['date'], x['voucher_no']))
        
        # Calculate running balance
        running_balance = opening_balance
        for transaction in bank_transactions:
            if transaction['balance_type'] == 'credit':
                running_balance += transaction['credit']
            else:
                running_balance -= transaction['debit']
            transaction['running_balance'] = running_balance
        
        # Calculate period totals
        period_payments = sum(t['debit'] for t in bank_transactions)
        period_receipts = sum(t['credit'] for t in bank_transactions)
        closing_balance = opening_balance + period_receipts - period_payments
        
        return {
            'register_type': f'Bank Register - {bank.name}',
            'bank': bank,
            'period': f"{from_date.strftime('%d-%m-%Y')} to {to_date.strftime('%d-%m-%Y')}",
            'opening_balance': opening_balance,
            'closing_balance': closing_balance,
            'period_receipts': period_receipts,
            'period_payments': period_payments,
            'transactions': bank_transactions,
            'transaction_count': len(bank_transactions)
        }

    def get_contra_register_data(self, from_date, to_date, company_id, financial_year_id):
        """Get contra register data for the specified period"""
        
        # Get all contra entries for the period
        contra_entries = ContraEntryMaster.objects.filter(
            contra_date__range=[from_date, to_date],
            company_id=company_id,
            financial_year_id=financial_year_id
        ).select_related('from_bank', 'to_bank').order_by('contra_date', 'contra_no')
        
        contra_transactions = []
        
        for contra in contra_entries:
            contra_transactions.append({
                'date': contra.contra_date,
                'voucher_type': 'Contra',
                'voucher_no': contra.contra_no,
                'from_bank': contra.from_bank.name,
                'to_bank': contra.to_bank.name,
                'particulars': contra.particulars or '',
                'cheque_no': contra.cheque_no or '',
                'cheque_date': contra.cheque_date,
                'amount': contra.amount,
                'transaction_type': 'contra_entry'
            })
        
        # Calculate totals
        total_amount = sum(t['amount'] for t in contra_transactions)
        
        return {
            'register_type': 'Contra Register',
            'period': f"{from_date.strftime('%d-%m-%Y')} to {to_date.strftime('%d-%m-%Y')}",
            'total_amount': total_amount,
            'transactions': contra_transactions,
            'transaction_count': len(contra_transactions)
        }


class RegisterSummaryView(LoginRequiredMixin, View):
    """
    View for generating register summary reports
    """
    
    def get(self, request):
        """Display register summary dashboard"""
        company_id = request.session.get('company_id', 1)
        financial_year_id = request.session.get('financial_year_id', 1)
        
        # Get current month data
        today = date.today()
        first_day = today.replace(day=1)
        
        # Cash summary
        cash_summary = self.get_cash_summary(first_day, today, company_id, financial_year_id)
        
        # Bank summary
        bank_summary = self.get_bank_summary(first_day, today, company_id, financial_year_id)
        
        # Contra summary
        contra_summary = self.get_contra_summary(first_day, today, company_id, financial_year_id)
        
        context = {
            'period': f"{first_day.strftime('%d-%m-%Y')} to {today.strftime('%d-%m-%Y')}",
            'cash_summary': cash_summary,
            'bank_summary': bank_summary,
            'contra_summary': contra_summary,
            'banks': Bank.objects.filter(is_active=True).order_by('name'),
        }
        
        return render(request, 'accounts/register_summary.html', context)
    
    def get_cash_summary(self, from_date, to_date, company_id, financial_year_id):
        """Get cash summary for the period"""
        
        payments = CashVoucherMaster.objects.filter(
            voucher_type='payment',
            voucher_date__range=[from_date, to_date],
            company_id=company_id,
            financial_year_id=financial_year_id
        ).aggregate(
            total=Sum('amount'),
            count=Count('id')
        )
        
        receipts = CashVoucherMaster.objects.filter(
            voucher_type='receipt',
            voucher_date__range=[from_date, to_date],
            company_id=company_id,
            financial_year_id=financial_year_id
        ).aggregate(
            total=Sum('amount'),
            count=Count('id')
        )
        
        return {
            'payments': {
                'amount': payments['total'] or Decimal('0.00'),
                'count': payments['count'] or 0
            },
            'receipts': {
                'amount': receipts['total'] or Decimal('0.00'),
                'count': receipts['count'] or 0
            },
            'net_amount': (receipts['total'] or Decimal('0.00')) - (payments['total'] or Decimal('0.00'))
        }
    
    def get_bank_summary(self, from_date, to_date, company_id, financial_year_id):
        """Get bank summary for the period"""
        
        banks = Bank.objects.filter(is_active=True)
        bank_data = []
        
        total_payments = Decimal('0.00')
        total_receipts = Decimal('0.00')
        
        for bank in banks:
            payments = BankVoucherMaster.objects.filter(
                bank=bank,
                voucher_type='payment',
                voucher_date__range=[from_date, to_date],
                company_id=company_id,
                financial_year_id=financial_year_id
            ).aggregate(
                total=Sum('amount'),
                count=Count('id')
            )
            
            receipts = BankVoucherMaster.objects.filter(
                bank=bank,
                voucher_type='receipt',
                voucher_date__range=[from_date, to_date],
                company_id=company_id,
                financial_year_id=financial_year_id
            ).aggregate(
                total=Sum('amount'),
                count=Count('id')
            )
            
            bank_payments = payments['total'] or Decimal('0.00')
            bank_receipts = receipts['total'] or Decimal('0.00')
            
            total_payments += bank_payments
            total_receipts += bank_receipts
            
            bank_data.append({
                'bank': bank,
                'payments': {
                    'amount': bank_payments,
                    'count': payments['count'] or 0
                },
                'receipts': {
                    'amount': bank_receipts,
                    'count': receipts['count'] or 0
                },
                'net_amount': bank_receipts - bank_payments
            })
        
        return {
            'banks': bank_data,
            'totals': {
                'payments': total_payments,
                'receipts': total_receipts,
                'net_amount': total_receipts - total_payments
            }
        }
    
    def get_contra_summary(self, from_date, to_date, company_id, financial_year_id):
        """Get contra summary for the period"""
        
        contra_data = ContraEntryMaster.objects.filter(
            contra_date__range=[from_date, to_date],
            company_id=company_id,
            financial_year_id=financial_year_id
        ).aggregate(
            total=Sum('amount'),
            count=Count('id')
        )
        
        return {
            'total_amount': contra_data['total'] or Decimal('0.00'),
            'transaction_count': contra_data['count'] or 0
        }


class ExportRegisterView(LoginRequiredMixin, View):
    """
    View for exporting register data to CSV/Excel
    """
    
    def get(self, request):
        """Export register data based on parameters"""
        register_type = request.GET.get('register_type', 'cash')
        bank_id = request.GET.get('bank')
        date_from = request.GET.get('date_from')
        date_to = request.GET.get('date_to')
        export_format = request.GET.get('format', 'csv')
        
        if not all([date_from, date_to]):
            messages.error(request, 'Date range is required for export')
            return redirect('accounts:cash_bank_register')
        
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d').date()
            to_date = datetime.strptime(date_to, '%Y-%m-%d').date()
        except ValueError:
            messages.error(request, 'Invalid date format')
            return redirect('accounts:cash_bank_register')
        
        # Get the register view instance to reuse data methods
        register_view = CashBankRegisterView()
        register_view.request = request
        
        # Get register data
        register_data = register_view.get_register_data()
        
        if not register_data:
            messages.error(request, 'No data found for the specified criteria')
            return redirect('accounts:cash_bank_register')
        
        if export_format == 'csv':
            return self.export_csv(register_data, register_type)
        else:
            messages.error(request, 'Only CSV export is currently supported')
            return redirect('accounts:cash_bank_register')
    
    def export_csv(self, register_data, register_type):
        """Export register data as CSV"""
        
        response = HttpResponse(content_type='text/csv')
        filename = f"{register_type}_register_{date.today().strftime('%Y%m%d')}.csv"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        writer = csv.writer(response)
        
        # Write header
        writer.writerow([register_data['register_type']])
        writer.writerow([f"Period: {register_data['period']}"])
        writer.writerow([])  # Empty row
        
        if register_type == 'contra':
            # Contra register format
            writer.writerow([
                'Date', 'Voucher No', 'From Bank', 'To Bank', 
                'Cheque No', 'Cheque Date', 'Particulars', 'Amount'
            ])
            
            for transaction in register_data['transactions']:
                writer.writerow([
                    transaction['date'].strftime('%d-%m-%Y'),
                    transaction['voucher_no'],
                    transaction['from_bank'],
                    transaction['to_bank'],
                    transaction['cheque_no'],
                    transaction['cheque_date'].strftime('%d-%m-%Y') if transaction['cheque_date'] else '',
                    transaction['particulars'],
                    transaction['amount']
                ])
        else:
            # Cash/Bank register format
            writer.writerow([
                'Date', 'Voucher Type', 'Voucher No', 'Pay To', 
                'Cheque No', 'Cheque Date', 'Particulars', 'Debit', 'Credit', 'Balance'
            ])
            
            for transaction in register_data['transactions']:
                writer.writerow([
                    transaction['date'].strftime('%d-%m-%Y'),
                    transaction['voucher_type'],
                    transaction['voucher_no'],
                    transaction['pay_to'],
                    transaction.get('cheque_no', ''),
                    transaction.get('cheque_date', '').strftime('%d-%m-%Y') if transaction.get('cheque_date') else '',
                    transaction['particulars'],
                    transaction['debit'],
                    transaction['credit'],
                    transaction['running_balance']
                ])
        
        # Write summary
        writer.writerow([])  # Empty row
        writer.writerow(['Summary'])
        
        if register_type == 'contra':
            writer.writerow(['Total Amount:', register_data['total_amount']])
            writer.writerow(['Transaction Count:', register_data['transaction_count']])
        else:
            writer.writerow(['Opening Balance:', register_data['opening_balance']])
            writer.writerow(['Period Receipts:', register_data['period_receipts']])
            writer.writerow(['Period Payments:', register_data['period_payments']])
            writer.writerow(['Closing Balance:', register_data['closing_balance']])
            writer.writerow(['Transaction Count:', register_data['transaction_count']])
        
        return response


# AJAX Views for Dynamic Loading

def get_register_summary_data(request):
    """
    AJAX view to get register summary data for dashboard widgets
    """
    period = request.GET.get('period', 'current_month')
    
    try:
        today = date.today()
        
        if period == 'current_month':
            from_date = today.replace(day=1)
            to_date = today
        elif period == 'last_month':
            first_day_this_month = today.replace(day=1)
            last_day_last_month = first_day_this_month - timedelta(days=1)
            from_date = last_day_last_month.replace(day=1)
            to_date = last_day_last_month
        elif period == 'current_year':
            from_date = today.replace(month=1, day=1)
            to_date = today
        else:
            from_date = today.replace(day=1)
            to_date = today
        
        company_id = request.session.get('company_id', 1)
        financial_year_id = request.session.get('financial_year_id', 1)
        
        # Get cash summary
        cash_payments = CashVoucherMaster.objects.filter(
            voucher_type='payment',
            voucher_date__range=[from_date, to_date],
            company_id=company_id,
            financial_year_id=financial_year_id
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        
        cash_receipts = CashVoucherMaster.objects.filter(
            voucher_type='receipt',
            voucher_date__range=[from_date, to_date],
            company_id=company_id,
            financial_year_id=financial_year_id
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        
        # Get bank summary
        bank_payments = BankVoucherMaster.objects.filter(
            voucher_type='payment',
            voucher_date__range=[from_date, to_date],
            company_id=company_id,
            financial_year_id=financial_year_id
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        
        bank_receipts = BankVoucherMaster.objects.filter(
            voucher_type='receipt',
            voucher_date__range=[from_date, to_date],
            company_id=company_id,
            financial_year_id=financial_year_id
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        
        # Get contra summary
        contra_total = ContraEntryMaster.objects.filter(
            contra_date__range=[from_date, to_date],
            company_id=company_id,
            financial_year_id=financial_year_id
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        
        return JsonResponse({
            'period': f"{from_date.strftime('%d-%m-%Y')} to {to_date.strftime('%d-%m-%Y')}",
            'cash': {
                'payments': float(cash_payments),
                'receipts': float(cash_receipts),
                'net': float(cash_receipts - cash_payments)
            },
            'bank': {
                'payments': float(bank_payments),
                'receipts': float(bank_receipts),
                'net': float(bank_receipts - bank_payments)
            },
            'contra': {
                'total': float(contra_total)
            }
        })
    
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


def get_bank_balance_summary(request):
    """
    AJAX view to get current bank balance summary
    """
    try:
        company_id = request.session.get('company_id', 1)
        financial_year_id = request.session.get('financial_year_id', 1)
        
        banks = Bank.objects.filter(is_active=True)
        bank_balances = []
        
        for bank in banks:
            # Calculate bank balance (all transactions up to today)
            payments = BankVoucherMaster.objects.filter(
                bank=bank,
                voucher_type='payment',
                company_id=company_id,
                financial_year_id=financial_year_id
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
            
            receipts = BankVoucherMaster.objects.filter(
                bank=bank,
                voucher_type='receipt',
                company_id=company_id,
                financial_year_id=financial_year_id
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
            
            contra_in = ContraEntryMaster.objects.filter(
                to_bank=bank,
                company_id=company_id,
                financial_year_id=financial_year_id
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
            
            contra_out = ContraEntryMaster.objects.filter(
                from_bank=bank,
                company_id=company_id,
                financial_year_id=financial_year_id
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
            
            balance = receipts + contra_in - payments - contra_out
            
            bank_balances.append({
                'bank_name': bank.name,
                'balance': float(balance),
                'balance_formatted': f"₹ {balance:,.2f}"
            })
        
        return JsonResponse({'bank_balances': bank_balances})
    
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)