<!-- accounts/templates/accounts/vouchers/cash_voucher_list.html -->
<!-- Cash Voucher List View Template -->
<!-- Task Group 2: Banking & Cash Management - Cash Voucher List (Task 2.4) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Cash Voucher Management - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-green-600 to-sap-green-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="banknote" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Cash Voucher Management</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage cash payments, receipts, and transactions</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:vouchers_dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to Dashboard
                </a>
                <a href="{% url 'accounts:cash_voucher_create' %}" 
                   class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                    New Cash Voucher
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Search and Filter Section -->
    <div class="mb-6 bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4">
            <form method="get" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4">
                <!-- Search by Voucher Number/Party -->
                <div class="flex-1">
                    <label for="search" class="block text-sm font-medium text-sap-gray-700 mb-1">Search</label>
                    <input type="text" name="search" value="{{ request.GET.search }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500"
                           placeholder="Search by voucher number, party name, or description...">
                </div>
                
                <!-- Transaction Type Filter -->
                <div>
                    <label for="transaction_type" class="block text-sm font-medium text-sap-gray-700 mb-1">Transaction Type</label>
                    <select name="transaction_type" 
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                        <option value="">All Types</option>
                        {% for value, label in transaction_type_choices %}
                        <option value="{{ value }}" {% if request.GET.transaction_type == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Cash Account Filter -->
                <div>
                    <label for="cash_account" class="block text-sm font-medium text-sap-gray-700 mb-1">Cash Account</label>
                    <select name="cash_account" 
                            class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                        <option value="">All Accounts</option>
                        {% for account in cash_accounts %}
                        <option value="{{ account.id }}" {% if request.GET.cash_account == account.id|stringformat:"s" %}selected{% endif %}>
                            {{ account.account_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Date Range -->
                <div>
                    <label for="date_from" class="block text-sm font-medium text-sap-gray-700 mb-1">From Date</label>
                    <input type="date" name="date_from" value="{{ request.GET.date_from }}"
                           class="block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500">
                </div>
                
                <!-- Search Button -->
                <div>
                    <button type="submit" 
                            class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="search" class="w-4 h-4 inline mr-2"></i>
                        Search
                    </button>
                </div>
                
                <!-- Clear Button -->
                {% if request.GET %}
                <div>
                    <a href="{% url 'accounts:cash_voucher_list' %}" 
                       class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                        Clear
                    </a>
                </div>
                {% endif %}
            </form>
        </div>
    </div>

    <!-- Cash Transaction Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="banknote" class="w-6 h-6 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Total Vouchers</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ vouchers.count|default:0 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="arrow-down-circle" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Cash Receipts</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ total_receipts|default:0|floatformat:2 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-red-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="arrow-up-circle" class="w-6 h-6 text-sap-red-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Cash Payments</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ total_payments|default:0|floatformat:2 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="trending-up" class="w-6 h-6 text-sap-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Net Cash Flow</p>
                    <p class="text-2xl font-bold text-sap-gray-900">₹{{ net_cash_flow|default:0|floatformat:2 }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Cash Vouchers Table -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-sap-gray-800">Cash Vouchers</h3>
                <div class="flex items-center space-x-2">
                    <button type="button" onclick="exportVouchers()" 
                            class="bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                        Export
                    </button>
                    <button type="button" onclick="generateCashReport()" 
                            class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="bar-chart" class="w-4 h-4 inline mr-2"></i>
                        Cash Report
                    </button>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sap-gray-200">
                <thead class="bg-sap-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Voucher Details
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Party Information
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Transaction
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Amount & Date
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-sap-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-sap-gray-200">
                    {% for voucher in vouchers %}
                    <tr class="hover:bg-sap-gray-50">
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-sap-green-100 rounded-lg flex items-center justify-center mr-3">
                                    <i data-lucide="banknote" class="w-5 h-5 text-sap-green-600"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-sap-gray-900">
                                        <a href="{% url 'accounts:cash_voucher_detail' voucher.id %}" class="text-sap-green-600 hover:text-sap-green-900">
                                            {{ voucher.voucher_number|default:"Cash Voucher" }}
                                        </a>
                                    </div>
                                    <div class="text-sm text-sap-gray-500">{{ voucher.description|truncatechars:40|default:"-" }}</div>
                                    {% if voucher.cash_account %}
                                    <div class="text-xs text-sap-gray-400">A/c: {{ voucher.cash_account.account_name }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            {% if voucher.party_name %}
                            <div class="text-sm text-sap-gray-900">{{ voucher.party_name }}</div>
                            <div class="text-sm text-sap-gray-500">{{ voucher.get_party_type_display|default:"-" }}</div>
                            {% else %}
                            <div class="text-sm text-sap-gray-500">No party specified</div>
                            {% endif %}
                            {% if voucher.bill_number %}
                            <div class="text-xs text-sap-gray-400">Bill: {{ voucher.bill_number }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-sap-gray-900">{{ voucher.get_transaction_type_display|default:"-" }}</div>
                            {% if voucher.account_head %}
                            <div class="text-sm text-sap-gray-500">{{ voucher.account_head.account_name }}</div>
                            {% endif %}
                            {% if voucher.payment_mode %}
                            <div class="text-xs text-sap-gray-400">{{ voucher.get_payment_mode_display }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-sap-gray-900">
                                {% if voucher.transaction_type == 'payment' %}
                                <span class="text-sap-red-600">-₹{{ voucher.amount|floatformat:2 }}</span>
                                {% else %}
                                <span class="text-sap-green-600">+₹{{ voucher.amount|floatformat:2 }}</span>
                                {% endif %}
                            </div>
                            {% if voucher.voucher_date %}
                            <div class="text-sm text-sap-gray-500">{{ voucher.voucher_date|date:"d M Y" }}</div>
                            {% endif %}
                            {% if voucher.reference_number %}
                            <div class="text-xs text-sap-gray-400">Ref: {{ voucher.reference_number }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if voucher.status == 'approved' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-green-100 text-sap-green-800">
                                Approved
                            </span>
                            {% elif voucher.status == 'pending' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-yellow-100 text-sap-yellow-800">
                                Pending
                            </span>
                            {% elif voucher.status == 'rejected' %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-red-100 text-sap-red-800">
                                Rejected
                            </span>
                            {% else %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-sap-gray-100 text-sap-gray-800">
                                {{ voucher.get_status_display|default:"Draft" }}
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <a href="{% url 'accounts:cash_voucher_detail' voucher.id %}" 
                                   class="text-sap-green-600 hover:text-sap-green-900" title="View Details">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                </a>
                                <a href="{% url 'accounts:cash_voucher_edit' voucher.id %}" 
                                   class="text-sap-blue-600 hover:text-sap-blue-900" title="Edit">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </a>
                                {% if voucher.status == 'pending' %}
                                <button type="button" onclick="approveVoucher({{ voucher.id }})" 
                                        class="text-sap-green-600 hover:text-sap-green-900" title="Approve">
                                    <i data-lucide="check" class="w-4 h-4"></i>
                                </button>
                                {% endif %}
                                <button type="button" onclick="duplicateVoucher({{ voucher.id }})" 
                                        class="text-sap-purple-600 hover:text-sap-purple-900" title="Duplicate">
                                    <i data-lucide="copy" class="w-4 h-4"></i>
                                </button>
                                <button type="button" onclick="printVoucher({{ voucher.id }})" 
                                        class="text-sap-gray-600 hover:text-sap-gray-900" title="Print">
                                    <i data-lucide="printer" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="px-6 py-8 text-center">
                            <div class="text-center">
                                <i data-lucide="banknote" class="w-12 h-12 text-sap-gray-400 mx-auto mb-4"></i>
                                <h3 class="text-sm font-medium text-sap-gray-900 mb-2">No cash vouchers found</h3>
                                <p class="text-sm text-sap-gray-600 mb-4">Get started by creating your first cash voucher.</p>
                                <a href="{% url 'accounts:cash_voucher_create' %}" 
                                   class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>
                                    Create Cash Voucher
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="px-6 py-4 border-t border-sap-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-sap-gray-700">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} entries
                </div>
                <div class="flex space-x-1">
                    {% if page_obj.has_previous %}
                    <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ page_obj.previous_page_number }}" 
                       class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-l-lg hover:bg-sap-gray-50">
                        Previous
                    </a>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <span class="px-3 py-2 text-sm font-medium text-sap-green-600 bg-sap-green-50 border border-sap-green-300">
                            {{ num }}
                        </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ num }}" 
                           class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 hover:bg-sap-gray-50">
                            {{ num }}
                        </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ page_obj.next_page_number }}" 
                       class="px-3 py-2 text-sm font-medium text-sap-gray-500 bg-white border border-sap-gray-300 rounded-r-lg hover:bg-sap-gray-50">
                        Next
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function exportVouchers() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = '?' + params.toString();
}

function generateCashReport() {
    alert('Cash report generation functionality would be implemented here.');
}

function approveVoucher(voucherId) {
    if (confirm('Approve this cash voucher?')) {
        // This would make an AJAX call to approve the voucher
        alert(`Voucher approval functionality for voucher ID ${voucherId} would be implemented here.`);
    }
}

function duplicateVoucher(voucherId) {
    if (confirm('Create a duplicate of this voucher?')) {
        window.location.href = `/accounts/vouchers/cash/${voucherId}/duplicate/`;
    }
}

function printVoucher(voucherId) {
    window.open(`/accounts/vouchers/cash/${voucherId}/print/`, '_blank');
}

// Initialize lucide icons on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}