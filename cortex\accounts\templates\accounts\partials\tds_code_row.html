<!-- accounts/partials/tds_code_row.html -->
<!-- HTMX partial for single TDSCode row - SAP S/4HANA inspired -->

{% load static %}

<tr class="hover:bg-sap-gray-50 transition-colors duration-150" id="tds-code-row-{{ tds_code.id }}">
    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-sap-gray-900">
        {{ tds_code.id }}
    </td>
    <td class="px-6 py-4">
        <div class="flex items-center">
            <div class="w-8 h-8 bg-sap-yellow-100 rounded-lg flex items-center justify-center mr-3">
                <i data-lucide="file-text" class="w-4 h-4 text-sap-yellow-600"></i>
            </div>
            <div>
                <div class="text-sm font-medium text-sap-gray-900">{{ tds_code.section_code|default:"N/A" }}</div>
                <div class="text-xs text-sap-gray-500">Section Code</div>
            </div>
        </div>
    </td>
    <td class="px-6 py-4">
        <div class="text-sm text-sap-gray-900 max-w-xs">
            {{ tds_code.nature_of_payment|default:"N/A"|truncatechars:50 }}
        </div>
    </td>
    <td class="px-6 py-4 whitespace-nowrap">
        <span class="inline-flex px-3 py-1 text-sm font-semibold bg-sap-yellow-100 text-sap-yellow-800 rounded-full">
            {{ tds_code.percentage|default:"0" }}%
        </span>
    </td>
    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div class="flex justify-end space-x-2">
            <!-- Edit Button -->
            <button type="button"
                    hx-get="{% url 'accounts:tds_code_edit' tds_code.id %}"
                    hx-target="#tds-code-row-{{ tds_code.id }}"
                    hx-swap="outerHTML"
                    class="inline-flex items-center px-3 py-1.5 border border-sap-yellow-300 rounded text-xs font-medium text-sap-yellow-700 bg-sap-yellow-50 hover:bg-sap-yellow-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-yellow-500 transition-colors duration-200">
                <i data-lucide="edit" class="w-3 h-3 mr-1"></i>
                Edit
            </button>
            <!-- Delete Button -->
            <button type="button"
                    hx-delete="{% url 'accounts:tds_code_delete' tds_code.id %}"
                    hx-target="#tds-code-row-{{ tds_code.id }}"
                    hx-swap="outerHTML"
                    hx-confirm="Are you sure you want to delete this TDS code? This action cannot be undone."
                    class="inline-flex items-center px-3 py-1.5 border border-sap-red-300 rounded text-xs font-medium text-sap-red-700 bg-sap-red-50 hover:bg-sap-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sap-red-500 transition-colors duration-200">
                <i data-lucide="trash-2" class="w-3 h-3 mr-1"></i>
                Delete
            </button>
        </div>
    </td>
</tr>