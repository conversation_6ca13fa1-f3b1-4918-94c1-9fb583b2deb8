<!-- accounts/templates/accounts/capital_loans/dashboard.html -->
<!-- Capital & Loans Management Dashboard -->
<!-- Task Group 7: Capital & Loans Management Dashboard (Task 7.1) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Capital & Loans Management - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-indigo-600 to-sap-indigo-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="trending-up" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Capital & Loans Management</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Manage company capital structure, loans, and liabilities</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:dashboard' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to Accounts
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Capital & Loans Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Active Loans -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="credit-card" class="w-6 h-6 text-sap-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Active Loans</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ active_loans_count|default:0 }}</p>
                    <p class="text-xs text-sap-blue-600 mt-1">₹{{ total_loan_amount|default:0|floatformat:2 }}</p>
                </div>
            </div>
        </div>
        
        <!-- Capital Entries -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="dollar-sign" class="w-6 h-6 text-sap-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Capital Entries</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ capital_entries_count|default:0 }}</p>
                    <p class="text-xs text-sap-green-600 mt-1">Total entries</p>
                </div>
            </div>
        </div>
        
        <!-- Current Liabilities -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-orange-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="clipboard-list" class="w-6 h-6 text-sap-orange-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Current Liabilities</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ current_liabilities_count|default:0 }}</p>
                    <p class="text-xs text-sap-orange-600 mt-1">Outstanding</p>
                </div>
            </div>
        </div>
        
        <!-- Overdue Loans -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-sap-red-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="alert-triangle" class="w-6 h-6 text-sap-red-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sap-gray-600">Overdue Loans</p>
                    <p class="text-2xl font-bold text-sap-gray-900">{{ overdue_loans_count|default:0 }}</p>
                    <p class="text-xs text-sap-red-600 mt-1">Requires attention</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Capital Management Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        
        <!-- Capital Structure Management -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="dollar-sign" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                    Capital Structure Management
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:capital_create' %}" 
                       class="flex items-center w-full bg-sap-green-600 hover:bg-sap-green-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Add Capital Entry
                    </a>
                    <a href="{% url 'accounts:capital_list' %}" 
                       class="flex items-center w-full bg-sap-green-100 hover:bg-sap-green-200 text-sap-green-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                        View Capital Entries
                    </a>
                    <button type="button" onclick="generateCapitalReport()" 
                            class="flex items-center w-full bg-sap-gray-100 hover:bg-sap-gray-200 text-sap-gray-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="file-text" class="w-4 h-4 mr-2"></i>
                        Capital Structure Report
                    </button>
                </div>
            </div>
        </div>

        <!-- Loan Management -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="credit-card" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                    Loan Management
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:loan_master_create' %}" 
                       class="flex items-center w-full bg-sap-blue-600 hover:bg-sap-blue-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Add New Loan
                    </a>
                    <a href="{% url 'accounts:loan_master_list' %}" 
                       class="flex items-center w-full bg-sap-blue-100 hover:bg-sap-blue-200 text-sap-blue-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                        View All Loans
                    </a>
                    <button type="button" onclick="generateEMISchedule()" 
                            class="flex items-center w-full bg-sap-gray-100 hover:bg-sap-gray-200 text-sap-gray-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="calendar" class="w-4 h-4 mr-2"></i>
                        EMI Schedule Report
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Master Configuration Section -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        
        <!-- Loan Types Configuration -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="collection" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                    Loan Types
                </h3>
                <p class="text-sm text-sap-gray-600 mt-1">{{ loan_type_count|default:0 }} configured</p>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:loan_type_create' %}" 
                       class="flex items-center w-full bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Add Loan Type
                    </a>
                    <a href="{% url 'accounts:loan_type_list' %}" 
                       class="flex items-center w-full bg-sap-purple-100 hover:bg-sap-purple-200 text-sap-purple-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                        Manage Types
                    </a>
                </div>
            </div>
        </div>

        <!-- Interest Types Configuration -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="calculator" class="w-5 h-5 mr-2 text-sap-yellow-600"></i>
                    Interest Types
                </h3>
                <p class="text-sm text-sap-gray-600 mt-1">{{ interest_type_count|default:0 }} configured</p>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:interest_type_create' %}" 
                       class="flex items-center w-full bg-sap-yellow-600 hover:bg-sap-yellow-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Add Interest Type
                    </a>
                    <a href="{% url 'accounts:interest_type_list' %}" 
                       class="flex items-center w-full bg-sap-yellow-100 hover:bg-sap-yellow-200 text-sap-yellow-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                        Manage Types
                    </a>
                </div>
            </div>
        </div>

        <!-- Current Liabilities -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="clipboard-list" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                    Current Liabilities
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <a href="{% url 'accounts:current_liabilities_create' %}" 
                       class="flex items-center w-full bg-sap-orange-600 hover:bg-sap-orange-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Add Liability
                    </a>
                    <a href="{% url 'accounts:current_liabilities_list' %}" 
                       class="flex items-center w-full bg-sap-orange-100 hover:bg-sap-orange-200 text-sap-orange-800 px-4 py-3 rounded-lg font-medium transition-colors">
                        <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                        View Liabilities
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Critical Alerts and Quick Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        
        <!-- Critical Alerts -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="alert-triangle" class="w-5 h-5 mr-2 text-sap-red-600"></i>
                    Critical Alerts
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    {% if overdue_loans_count > 0 %}
                    <div class="flex items-center p-3 bg-sap-red-50 border border-sap-red-200 rounded-lg">
                        <i data-lucide="credit-card" class="w-5 h-5 text-sap-red-600 mr-3"></i>
                        <div>
                            <p class="text-sm font-medium text-sap-red-800">Overdue Loans</p>
                            <p class="text-xs text-sap-red-600">{{ overdue_loans_count }} loan(s) are overdue</p>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="flex items-center p-3 bg-sap-yellow-50 border border-sap-yellow-200 rounded-lg">
                        <i data-lucide="calendar" class="w-5 h-5 text-sap-yellow-600 mr-3"></i>
                        <div>
                            <p class="text-sm font-medium text-sap-yellow-800">EMI Due This Month</p>
                            <p class="text-xs text-sap-yellow-600">Check upcoming EMI payments</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center p-3 bg-sap-blue-50 border border-sap-blue-200 rounded-lg">
                        <i data-lucide="trending-up" class="w-5 h-5 text-sap-blue-600 mr-3"></i>
                        <div>
                            <p class="text-sm font-medium text-sap-blue-800">Loan Utilization</p>
                            <p class="text-xs text-sap-blue-600">Monitor loan utilization ratios</p>
                        </div>
                    </div>
                    
                    {% if overdue_loans_count == 0 %}
                    <div class="flex items-center p-3 bg-sap-green-50 border border-sap-green-200 rounded-lg">
                        <i data-lucide="check-circle" class="w-5 h-5 text-sap-green-600 mr-3"></i>
                        <div>
                            <p class="text-sm font-medium text-sap-green-800">All Loans Current</p>
                            <p class="text-xs text-sap-green-600">No overdue payments detected</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Quick Analytics -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="bar-chart" class="w-5 h-5 mr-2 text-sap-indigo-600"></i>
                    Financial Overview
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-sap-blue-500 rounded-full mr-3"></div>
                            <span class="text-sm text-sap-gray-700">Total Loan Amount</span>
                        </div>
                        <span class="text-sm font-medium text-sap-gray-900">₹{{ total_loan_amount|default:0|floatformat:2 }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-sap-green-500 rounded-full mr-3"></div>
                            <span class="text-sm text-sap-gray-700">Active Loans</span>
                        </div>
                        <span class="text-sm font-medium text-sap-gray-900">{{ active_loans_count|default:0 }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-sap-orange-500 rounded-full mr-3"></div>
                            <span class="text-sm text-sap-gray-700">Current Liabilities</span>
                        </div>
                        <span class="text-sm font-medium text-sap-gray-900">{{ current_liabilities_count|default:0 }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-sap-purple-500 rounded-full mr-3"></div>
                            <span class="text-sm text-sap-gray-700">Capital Entries</span>
                        </div>
                        <span class="text-sm font-medium text-sap-gray-900">{{ capital_entries_count|default:0 }}</span>
                    </div>
                </div>
                
                <div class="mt-6 pt-4 border-t border-sap-gray-200">
                    <button type="button" onclick="generateFinancialSummary()" 
                            class="w-full bg-sap-indigo-600 hover:bg-sap-indigo-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                        Download Financial Summary
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function generateCapitalReport() {
    alert('Capital structure report generation functionality would be implemented here.');
}

function generateEMISchedule() {
    alert('EMI schedule report generation functionality would be implemented here.');
}

function generateFinancialSummary() {
    alert('Financial summary report generation functionality would be implemented here.');
}

// Initialize lucide icons on page load
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
});
</script>
{% endblock %}