﻿using System;
using System.Collections;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Xml.Linq;
using System.IO;
public partial class Admin_Access_access_rules : System.Web.UI.Page
{
    private const string VirtualImageRoot = "~/";
    string selectedFolderName="";
    protected void Page_Load(object sender, EventArgs e)
    {
      
		if (!IsPostBack)
		{
			PopulateTree();
            FolderTree.ExpandDepth = 2;
		}
	
    }
    private void PopulateTree()
    {
        /*
         * <PERSON>, 4/15/2007.
         * The PopulateTree and AddNodeAndDescendents are taken almost verbatim from <PERSON>'s article
         * "Using the TreeView Control and a DataList to Create an Online Image Gallery", which is located at 
         * http://aspnet.4guysfromrolla.com/articles/083006-1.aspx
         */

        // Populate the tree based on the subfolders of the specified VirtualImageRoot
        DirectoryInfo rootFolder = new DirectoryInfo(Server.MapPath(VirtualImageRoot));
        TreeNode root = AddNodeAndDescendents(rootFolder, null);
        FolderTree.Nodes.Add(root);
        try
        {
            FolderTree.SelectedNode.ImageUrl = "/Simple/i/target.gif";
        }
        catch { }
    }

    private TreeNode AddNodeAndDescendents(DirectoryInfo folder, TreeNode parentNode)
    {
        /*
         * Dan Clem, 4/15/2007.
         * The PopulateTree and AddNodeAndDescendents are taken almost verbatim from Scott Mitchell's article
         * "Using the TreeView Control and a DataList to Create an Online Image Gallery", which is located at 
         * http://aspnet.4guysfromrolla.com/articles/083006-1.aspx
         */

        // Add the TreeNode, displaying the folder's name and storing the full path to the folder as the value...

        string virtualFolderPath;
        if (parentNode == null)
        {
            virtualFolderPath = VirtualImageRoot;
        }
        else
        {
            virtualFolderPath = parentNode.Value + folder.Name + "/";
        }

        TreeNode node = new TreeNode(folder.Name, virtualFolderPath);
        node.Selected = (folder.Name == selectedFolderName.ToString());

        // Recurse through this folder's subfolders
        DirectoryInfo[] subFolders = folder.GetDirectories();
        foreach (DirectoryInfo subFolder in subFolders)
        {
            if (subFolder.Name != "_controls" && subFolder.Name != "App_Data" && subFolder.Name != "Admin" && subFolder.Name != "Css" && subFolder.Name != "App_Code" && subFolder.Name != "BackUp" && subFolder.Name != "Bin" && subFolder.Name != "DB" && subFolder.Name != "Controls" && subFolder.Name != "images" && subFolder.Name != "Javascript" && subFolder.Name != "SysConfig" && subFolder.Name != "Source")
            {
                TreeNode child = AddNodeAndDescendents(subFolder, node);
                node.ChildNodes.Add(child);
            }
        }
        return node; // Return the new TreeNode
    }
	
}
