{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ form_title }} - Material Service Notes{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-gray-700 text-white px-4 py-2">
        <h1 class="text-lg font-bold">{{ form_title }}</h1>
    </div>
    
    <div class="p-4">
        <div class="bg-white rounded-lg shadow-sm border">
            <div class="p-4 border-b">
                <h2 class="text-lg font-semibold text-gray-900">Service Note Information</h2>
            </div>
            
            <form method="post" class="p-4">
                {% csrf_token %}
                
                {% if form.non_field_errors %}
                    <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
                        <div class="text-red-800">
                            {{ form.non_field_errors }}
                        </div>
                    </div>
                {% endif %}
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="{{ form.gsnno.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.gsnno.label }}
                        </label>
                        {{ form.gsnno }}
                        {% if form.gsnno.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.gsnno.errors|first }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.ginid.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.ginid.label }}
                        </label>
                        {{ form.ginid }}
                        {% if form.ginid.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.ginid.errors|first }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.ginno.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.ginno.label }}
                        </label>
                        {{ form.ginno }}
                        {% if form.ginno.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.ginno.errors|first }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.taxinvoiceno.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.taxinvoiceno.label }}
                        </label>
                        {{ form.taxinvoiceno }}
                        {% if form.taxinvoiceno.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.taxinvoiceno.errors|first }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.taxinvoicedate.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.taxinvoicedate.label }}
                        </label>
                        {{ form.taxinvoicedate }}
                        {% if form.taxinvoicedate.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.taxinvoicedate.errors|first }}</p>
                        {% endif %}
                    </div>
                </div>
                
                <div class="mt-6 flex items-center justify-between">
                    <a href="{% url 'inventory:service_note_list' %}" 
                       class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded text-sm font-medium transition-colors">
                        Cancel
                    </a>
                    
                    <button type="submit" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm font-medium transition-colors">
                        {% if object %}Update{% else %}Create{% endif %} Service Note
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}