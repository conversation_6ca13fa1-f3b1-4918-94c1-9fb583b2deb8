<!-- accounts/templates/accounts/reports/balance_sheet.html -->
<!-- Balance Sheet Report - SAP S/4HANA Inspired Design -->
<!-- Replaces ASP.NET BalanceSheet.aspx functionality -->

{% extends 'core/base.html' %}
{% load static %}

{% block title %}Balance Sheet - {{ page_title }} - Cortex{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-sap-blue-500 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="bar-chart-3" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Balance Sheet</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Assets vs Liabilities financial position statement</p>
                </div>
            </div>
            <div class="flex space-x-3">
                <button 
                    hx-get="{% url 'accounts:balance_sheet' %}?export=pdf" 
                    hx-indicator="#export-indicator"
                    class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:ring-offset-2 transition-colors duration-200">
                    <i data-lucide="file-text" class="w-4 h-4 mr-2"></i>
                    Export PDF
                </button>
                <button 
                    hx-get="{% url 'accounts:balance_sheet' %}?export=excel" 
                    hx-indicator="#export-indicator"
                    class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:ring-offset-2 transition-colors duration-200">
                    <i data-lucide="sheet" class="w-4 h-4 mr-2"></i>
                    Export Excel
                </button>
                <button 
                    onclick="window.print()" 
                    class="inline-flex items-center px-4 py-2 bg-sap-blue-500 text-white rounded-lg hover:bg-sap-blue-600 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:ring-offset-2 transition-colors duration-200">
                    <i data-lucide="printer" class="w-4 h-4 mr-2"></i>
                    Print
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-2 py-6 space-y-6" id="balance-sheet-content">
    
    <!-- Export Indicator -->
    <div id="export-indicator" class="htmx-indicator">
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 flex items-center space-x-4">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-sap-blue-500"></div>
                <span class="text-sap-gray-700">Generating export...</span>
            </div>
        </div>
    </div>

    <!-- Company & Period Information -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
        <div class="flex justify-between items-start">
            <div>
                <h2 class="text-lg font-semibold text-sap-gray-800 mb-2">{{ company.company_name|default:"Company Name" }}</h2>
                <p class="text-sm text-sap-gray-600">{{ company.registered_address|default:"Company Address" }}</p>
            </div>
            <div class="text-right">
                <p class="text-sm font-medium text-sap-gray-800">Financial Year: {{ financial_year.year_name|default:"Current Year" }}</p>
                <p class="text-2xs text-sap-gray-600">As of {{ financial_year.end_date|default:"today"|date:"d M Y" }}</p>
            </div>
        </div>
    </div>

    <!-- Balance Sheet Content -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        <!-- Liabilities Section -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-200 bg-sap-gray-50">
                <h3 class="text-lg font-semibold text-sap-gray-800 flex items-center">
                    <i data-lucide="minus-circle" class="w-5 h-5 mr-2 text-sap-red-500"></i>
                    Liabilities
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex justify-between items-center py-3 border-b border-sap-gray-100">
                        <span class="text-sm font-medium text-sap-gray-700">Capital Goods (Interstate)</span>
                        <span class="text-sm font-mono text-sap-gray-900">₹ {{ liabilities.capital_goods|floatformat:2|default:"0.00" }}</span>
                    </div>
                    <div class="flex justify-between items-center py-3 border-b border-sap-gray-100">
                        <span class="text-sm font-medium text-sap-gray-700">Loan (Liability)</span>
                        <span class="text-sm font-mono text-sap-gray-900">₹ {{ liabilities.loan_liability|floatformat:2|default:"0.00" }}</span>
                    </div>
                    <div class="flex justify-between items-center py-3 border-b border-sap-gray-100">
                        <span class="text-sm font-medium text-sap-gray-700">Current Liabilities</span>
                        <span class="text-sm font-mono text-sap-gray-900">₹ {{ liabilities.current_liabilities|floatformat:2|default:"0.00" }}</span>
                    </div>
                    <div class="flex justify-between items-center py-3 border-b border-sap-gray-100">
                        <span class="text-sm font-medium text-sap-gray-700">Branch/Division</span>
                        <span class="text-sm font-mono text-sap-gray-900">₹ {{ liabilities.branch_division|floatformat:2|default:"0.00" }}</span>
                    </div>
                    <div class="flex justify-between items-center py-3 border-b border-sap-gray-100">
                        <span class="text-sm font-medium text-sap-gray-700">Suspense A/c</span>
                        <span class="text-sm font-mono text-sap-gray-900">₹ {{ liabilities.suspense_account|floatformat:2|default:"0.00" }}</span>
                    </div>
                    <div class="flex justify-between items-center py-3 border-b border-sap-gray-100">
                        <span class="text-sm font-medium text-sap-gray-700">Profit & Loss A/c</span>
                        <span class="text-sm font-mono text-sap-gray-900">₹ {{ liabilities.profit_loss|floatformat:2|default:"0.00" }}</span>
                    </div>
                </div>
                <div class="mt-6 pt-4 border-t-2 border-sap-gray-300">
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-bold text-sap-gray-800">Total Liabilities</span>
                        <span class="text-lg font-bold font-mono text-sap-red-600">₹ {{ liabilities.total|floatformat:2|default:"0.00" }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Assets Section -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-200 bg-sap-gray-50">
                <h3 class="text-lg font-semibold text-sap-gray-800 flex items-center">
                    <i data-lucide="plus-circle" class="w-5 h-5 mr-2 text-sap-green-500"></i>
                    Assets
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex justify-between items-center py-3 border-b border-sap-gray-100">
                        <span class="text-sm font-medium text-sap-gray-700">Fixed Assets</span>
                        <span class="text-sm font-mono text-sap-gray-900">₹ {{ assets.fixed_assets|floatformat:2|default:"0.00" }}</span>
                    </div>
                    <div class="flex justify-between items-center py-3 border-b border-sap-gray-100">
                        <span class="text-sm font-medium text-sap-gray-700">Investments</span>
                        <span class="text-sm font-mono text-sap-gray-900">₹ {{ assets.investments|floatformat:2|default:"0.00" }}</span>
                    </div>
                    <div class="flex justify-between items-center py-3 border-b border-sap-gray-100">
                        <span class="text-sm font-medium text-sap-gray-700">Current Assets</span>
                        <span class="text-sm font-mono text-sap-gray-900">₹ {{ assets.current_assets|floatformat:2|default:"0.00" }}</span>
                    </div>
                </div>
                <div class="mt-6 pt-4 border-t-2 border-sap-gray-300">
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-bold text-sap-gray-800">Total Assets</span>
                        <span class="text-lg font-bold font-mono text-sap-green-600">₹ {{ assets.total|floatformat:2|default:"0.00" }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Balance Check & Ratios -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        <!-- Balance Verification -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <h3 class="text-lg font-semibold text-sap-gray-800 mb-4 flex items-center">
                <i data-lucide="balance-scale" class="w-5 h-5 mr-2 text-sap-blue-500"></i>
                Balance Verification
            </h3>
            {% with difference=assets.total|add:liabilities.total|floatformat:2 %}
            <div class="flex justify-between items-center py-3 border-b border-sap-gray-100">
                <span class="text-sm font-medium text-sap-gray-700">Total Assets</span>
                <span class="text-sm font-mono text-sap-gray-900">₹ {{ assets.total|floatformat:2|default:"0.00" }}</span>
            </div>
            <div class="flex justify-between items-center py-3 border-b border-sap-gray-100">
                <span class="text-sm font-medium text-sap-gray-700">Total Liabilities</span>
                <span class="text-sm font-mono text-sap-gray-900">₹ {{ liabilities.total|floatformat:2|default:"0.00" }}</span>
            </div>
            <div class="flex justify-between items-center py-3 mt-4 pt-4 border-t-2 border-sap-gray-300">
                <span class="text-sm font-bold text-sap-gray-800">Difference</span>
                <span class="text-sm font-bold font-mono {% if difference == '0.00' %}text-sap-green-600{% else %}text-sap-red-600{% endif %}">
                    ₹ {{ difference }}
                </span>
            </div>
            {% if difference == '0.00' %}
            <div class="mt-3 p-3 bg-sap-green-50 border border-sap-green-200 rounded-lg">
                <div class="flex items-center">
                    <i data-lucide="check-circle" class="w-4 h-4 text-sap-green-600 mr-2"></i>
                    <span class="text-sm font-medium text-sap-green-800">Balance Sheet is balanced</span>
                </div>
            </div>
            {% else %}
            <div class="mt-3 p-3 bg-sap-red-50 border border-sap-red-200 rounded-lg">
                <div class="flex items-center">
                    <i data-lucide="alert-circle" class="w-4 h-4 text-sap-red-600 mr-2"></i>
                    <span class="text-sm font-medium text-sap-red-800">Balance Sheet is not balanced</span>
                </div>
            </div>
            {% endif %}
            {% endwith %}
        </div>

        <!-- Financial Ratios Chart -->
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm p-6">
            <h3 class="text-lg font-semibold text-sap-gray-800 mb-4 flex items-center">
                <i data-lucide="pie-chart" class="w-5 h-5 mr-2 text-sap-orange-500"></i>
                Asset-Liability Distribution
            </h3>
            <div class="relative">
                <canvas id="balanceSheetChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Detailed Analysis (Expandable) -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm" x-data="{ expanded: false }">
        <div class="px-6 py-4 border-b border-sap-gray-200 cursor-pointer" @click="expanded = !expanded">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-sap-gray-800 flex items-center">
                    <i data-lucide="trending-up" class="w-5 h-5 mr-2 text-sap-blue-500"></i>
                    Financial Analysis & Trends
                </h3>
                <i data-lucide="chevron-down" class="w-5 h-5 text-sap-gray-500 transition-transform duration-200" 
                   :class="{ 'transform rotate-180': expanded }"></i>
            </div>
        </div>
        <div x-show="expanded" x-transition class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-sap-blue-600">{{ assets.total|floatformat:0 }}</div>
                    <div class="text-sm text-sap-gray-600">Total Assets</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-sap-red-600">{{ liabilities.total|floatformat:0 }}</div>
                    <div class="text-sm text-sap-gray-600">Total Liabilities</div>
                </div>
                <div class="text-center">
                    {% with ratio=assets.total|add:liabilities.total|default:"0.00" %}
                    <div class="text-2xl font-bold text-sap-green-600">
                        {% if liabilities.total and liabilities.total > 0 %}
                            {{ assets.total|default:0|div:liabilities.total|floatformat:2 }}
                        {% else %}
                            0.00
                        {% endif %}
                    </div>
                    <div class="text-sm text-sap-gray-600">Asset-Liability Ratio</div>
                    {% endwith %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js for visualizations -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
    
    // Balance Sheet Pie Chart
    const ctx = document.getElementById('balanceSheetChart').getContext('2d');
    const chart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Assets', 'Liabilities'],
            datasets: [{
                data: [{{ assets.total|default:0 }}, {{ liabilities.total|default:0 }}],
                backgroundColor: ['#4caf50', '#f44336'],
                borderColor: ['#43a047', '#e53935'],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.label + ': ₹' + context.parsed.toLocaleString('en-IN');
                        }
                    }
                }
            }
        }
    });
});

// Print Styles
const printStyles = `
    @media print {
        .no-print { display: none !important; }
        body { print-color-adjust: exact; }
        .bg-white { background: white !important; }
        .text-white { color: black !important; }
        .border { border: 1px solid #000 !important; }
    }
`;
const styleSheet = document.createElement("style");
styleSheet.innerText = printStyles;
document.head.appendChild(styleSheet);
</script>
{% endblock %}