{% extends "core/base.html" %}
{% load static %}

{% block title %}Customer <PERSON>llans - Inventory Management{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Customer Challans</h1>
                <p class="text-gray-600 mt-1">Manage outgoing material delivery challans</p>
            </div>
            <a href="{% url 'inventory:customer_challan_create' %}" 
               class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Create Customer <PERSON>
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500">Total Challans</p>
                        <p class="text-lg font-semibold text-gray-900" id="total-challans">{{ total_challans|default:0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500">Pending</p>
                        <p class="text-lg font-semibold text-gray-900" id="pending-challans">{{ pending_challans|default:0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500">Dispatched</p>
                        <p class="text-lg font-semibold text-gray-900" id="dispatched-challans">{{ dispatched_challans|default:0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500">Delivered</p>
                        <p class="text-lg font-semibold text-gray-900" id="delivered-challans">{{ delivered_challans|default:0 }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="p-6">
            <form method="get" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                    <input type="text" name="search" value="{{ request.GET.search }}"
                           placeholder="Challan number, customer..."
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                           hx-get="{% url 'inventory:customer_challan_list' %}"
                           hx-trigger="keyup changed delay:300ms"
                           hx-target="#challan-results"
                           hx-include="form">
                </div>
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                    <select name="status" 
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            hx-get="{% url 'inventory:customer_challan_list' %}"
                            hx-trigger="change"
                            hx-target="#challan-results"
                            hx-include="form">
                        <option value="">All Status</option>
                        <option value="DRAFT" {% if request.GET.status == 'DRAFT' %}selected{% endif %}>Draft</option>
                        <option value="PENDING" {% if request.GET.status == 'PENDING' %}selected{% endif %}>Pending</option>
                        <option value="DISPATCHED" {% if request.GET.status == 'DISPATCHED' %}selected{% endif %}>Dispatched</option>
                        <option value="DELIVERED" {% if request.GET.status == 'DELIVERED' %}selected{% endif %}>Delivered</option>
                        <option value="CANCELLED" {% if request.GET.status == 'CANCELLED' %}selected{% endif %}>Cancelled</option>
                    </select>
                </div>
                <div>
                    <label for="delivery_type" class="block text-sm font-medium text-gray-700">Delivery Type</label>
                    <select name="delivery_type" 
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            hx-get="{% url 'inventory:customer_challan_list' %}"
                            hx-trigger="change"
                            hx-target="#challan-results"
                            hx-include="form">
                        <option value="">All Types</option>
                        <option value="REGULAR" {% if request.GET.delivery_type == 'REGULAR' %}selected{% endif %}>Regular</option>
                        <option value="EXPRESS" {% if request.GET.delivery_type == 'EXPRESS' %}selected{% endif %}>Express</option>
                        <option value="URGENT" {% if request.GET.delivery_type == 'URGENT' %}selected{% endif %}>Urgent</option>
                        <option value="CUSTOMER_PICKUP" {% if request.GET.delivery_type == 'CUSTOMER_PICKUP' %}selected{% endif %}>Customer Pickup</option>
                    </select>
                </div>
                <div>
                    <label for="date_from" class="block text-sm font-medium text-gray-700">Date From</label>
                    <input type="date" name="date_from" value="{{ request.GET.date_from }}"
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                           hx-get="{% url 'inventory:customer_challan_list' %}"
                           hx-trigger="change"
                           hx-target="#challan-results"
                           hx-include="form">
                </div>
                <div>
                    <label for="date_to" class="block text-sm font-medium text-gray-700">Date To</label>
                    <input type="date" name="date_to" value="{{ request.GET.date_to }}"
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                           hx-get="{% url 'inventory:customer_challan_list' %}"
                           hx-trigger="change"
                           hx-target="#challan-results"
                           hx-include="form">
                </div>
            </form>
        </div>
    </div>

    <!-- Challans Table -->
    <div class="bg-white shadow rounded-lg">
        <div id="challan-results">
            {% include 'inventory/challans/partials/customer_challan_results.html' %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/htmx.org@1.9.6"></script>
{% endblock %}