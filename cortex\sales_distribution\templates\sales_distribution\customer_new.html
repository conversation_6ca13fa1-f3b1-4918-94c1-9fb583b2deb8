{% extends 'core/base.html' %}
{% load static %}

{% block title %}Customer Master - New - {{ global_company.company_name }}{% endblock %}

{% block content %}
<div class="h-full overflow-y-auto">
    <div class="p-6 space-y-6">
        <!-- Page Header -->
        <div class="bg-white rounded-xl shadow-sm border border-sap-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-sap-green-500 rounded-lg flex items-center justify-center">
                        <i data-lucide="user-plus" class="w-6 h-6 text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-sap-gray-900">Customer Master - New</h1>
                        <p class="text-sm text-sap-gray-600">Create a new customer record</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="{% url 'sales_distribution:customer_list' %}" 
                       class="px-4 py-2 border border-sap-gray-300 rounded-lg text-sm text-sap-gray-700 hover:bg-sap-gray-50 transition-colors">
                        <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                        Back to List
                    </a>
                </div>
            </div>
        </div>

        <!-- Customer Form - Exact ASP.NET Structure -->
        <div class="bg-white rounded-xl shadow-sm border border-sap-gray-200 overflow-hidden">
            <!-- Header Row - Matching ASP.NET -->
            <div class="bg-gradient-to-r from-sap-gray-700 to-sap-gray-800 px-6 py-3">
                <h3 class="text-lg font-semibold text-white">Customer Master - New</h3>
            </div>
            
            <form method="post" class="divide-y divide-sap-gray-200">
                {% csrf_token %}
                
                <!-- Customer Name Row -->
                <div class="grid grid-cols-4 gap-6 p-6">
                    <div class="flex items-center">
                        <label class="text-sm font-medium text-sap-gray-700">Customer's Name</label>
                    </div>
                    <div class="col-span-2">
                        {{ form.customer_name }}
                        {% if form.customer_name.errors %}
                            <span class="text-sap-red-500 text-sm">*</span>
                        {% endif %}
                    </div>
                    <div class="flex justify-end">
                        <button type="submit" 
                                class="sap-button-primary"
                                onclick="return confirm('Are you sure you want to add this customer?')">
                            <i data-lucide="save" class="w-4 h-4 mr-2"></i>
                            Submit
                        </button>
                    </div>
                </div>
                
                <!-- Address Headers Row - Three Column Layout -->
                <div class="grid grid-cols-4 gap-6 bg-sap-gray-100 p-4">
                    <div class="flex items-center justify-center">
                        <span class="text-sm font-medium text-sap-gray-700">Address/Details</span>
                    </div>
                    <div class="flex items-center justify-center">
                        <span class="text-sm font-medium text-sap-gray-700">REGD. OFFICE</span>
                    </div>
                    <div class="flex items-center justify-center">
                        <span class="text-sm font-medium text-sap-gray-700">WORKS/FACTORY</span>
                    </div>
                    <div class="flex items-center justify-center">
                        <span class="text-sm font-medium text-sap-gray-700">MATERIAL DELIVERY</span>
                    </div>
                </div>
                
                <!-- Address Row -->
                <div class="grid grid-cols-4 gap-6 p-4">
                    <div class="flex items-start pt-2">
                        <label class="text-sm font-medium text-sap-gray-700">Address</label>
                    </div>
                    <div>
                        {{ form.registered_address }}
                        {% if form.registered_address.errors %}
                            <span class="text-sap-red-500 text-sm">*</span>
                        {% endif %}
                    </div>
                    <div>
                        {{ form.works_address }}
                        {% if form.works_address.errors %}
                            <span class="text-sap-red-500 text-sm">*</span>
                        {% endif %}
                    </div>
                    <div>
                        {{ form.material_address }}
                        {% if form.material_address.errors %}
                            <span class="text-sap-red-500 text-sm">*</span>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Country Row -->
                <div class="grid grid-cols-4 gap-6 p-4">
                    <div class="flex items-center">
                        <label class="text-sm font-medium text-sap-gray-700">Country</label>
                    </div>
                    <div>
                        {{ form.registered_country }}
                        {% if form.registered_country.errors %}
                            <span class="text-sap-red-500 text-sm">*</span>
                        {% endif %}
                    </div>
                    <div>
                        {{ form.works_country }}
                        {% if form.works_country.errors %}
                            <span class="text-sap-red-500 text-sm">*</span>
                        {% endif %}
                    </div>
                    <div>
                        {{ form.material_country }}
                        {% if form.material_country.errors %}
                            <span class="text-sap-red-500 text-sm">*</span>
                        {% endif %}
                    </div>
                </div>
                
                <!-- State Row -->
                <div class="grid grid-cols-4 gap-6 p-4">
                    <div class="flex items-center">
                        <label class="text-sm font-medium text-sap-gray-700">State</label>
                    </div>
                    <div>
                        {{ form.registered_state }}
                        {% if form.registered_state.errors %}
                            <span class="text-sap-red-500 text-sm">*</span>
                        {% endif %}
                    </div>
                    <div>
                        {{ form.works_state }}
                        {% if form.works_state.errors %}
                            <span class="text-sap-red-500 text-sm">*</span>
                        {% endif %}
                    </div>
                    <div>
                        {{ form.material_state }}
                        {% if form.material_state.errors %}
                            <span class="text-sap-red-500 text-sm">*</span>
                        {% endif %}
                    </div>
                </div>
                
                <!-- City Row -->
                <div class="grid grid-cols-4 gap-6 p-4">
                    <div class="flex items-center">
                        <label class="text-sm font-medium text-sap-gray-700">City</label>
                    </div>
                    <div>
                        {{ form.registered_city }}
                        {% if form.registered_city.errors %}
                            <span class="text-sap-red-500 text-sm">*</span>
                        {% endif %}
                    </div>
                    <div>
                        {{ form.works_city }}
                        {% if form.works_city.errors %}
                            <span class="text-sap-red-500 text-sm">*</span>
                        {% endif %}
                    </div>
                    <div>
                        {{ form.material_city }}
                        {% if form.material_city.errors %}
                            <span class="text-sap-red-500 text-sm">*</span>
                        {% endif %}
                    </div>
                </div>
                
                <!-- PIN Number Row -->
                <div class="grid grid-cols-4 gap-6 p-4">
                    <div class="flex items-center">
                        <label class="text-sm font-medium text-sap-gray-700">PIN No.</label>
                    </div>
                    <div>
                        {{ form.registered_pin }}
                        {% if form.registered_pin.errors %}
                            <span class="text-sap-red-500 text-sm">*</span>
                        {% endif %}
                    </div>
                    <div>
                        {{ form.works_pin }}
                        {% if form.works_pin.errors %}
                            <span class="text-sap-red-500 text-sm">*</span>
                        {% endif %}
                    </div>
                    <div>
                        {{ form.material_pin }}
                        {% if form.material_pin.errors %}
                            <span class="text-sap-red-500 text-sm">*</span>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Contact Number Row -->
                <div class="grid grid-cols-4 gap-6 p-4">
                    <div class="flex items-center">
                        <label class="text-sm font-medium text-sap-gray-700">Contact No.</label>
                    </div>
                    <div>
                        {{ form.registered_contact_no }}
                        {% if form.registered_contact_no.errors %}
                            <span class="text-sap-red-500 text-sm">*</span>
                        {% endif %}
                    </div>
                    <div>
                        {{ form.works_contact_no }}
                        {% if form.works_contact_no.errors %}
                            <span class="text-sap-red-500 text-sm">*</span>
                        {% endif %}
                    </div>
                    <div>
                        {{ form.material_contact_no }}
                        {% if form.material_contact_no.errors %}
                            <span class="text-sap-red-500 text-sm">*</span>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Additional Details Section -->
                <div class="p-6 space-y-4">
                    <!-- Contact Person, Email, Contact No Row -->
                    <div class="grid grid-cols-6 gap-4 items-center">
                        <div>
                            <label class="text-sm font-medium text-sap-gray-700">Contact person</label>
                        </div>
                        <div>
                            {{ form.contact_person }}
                            {% if form.contact_person.errors %}
                                <span class="text-sap-red-500 text-sm">*</span>
                            {% endif %}
                        </div>
                        <div>
                            <label class="text-sm font-medium text-sap-gray-700">E-mail</label>
                        </div>
                        <div>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <span class="text-sap-red-500 text-sm">*</span>
                            {% endif %}
                        </div>
                        <div>
                            <label class="text-sm font-medium text-sap-gray-700">Contact No.</label>
                        </div>
                        <div>
                            {{ form.contact_no }}
                            {% if form.contact_no.errors %}
                                <span class="text-sap-red-500 text-sm">*</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- TIN/CST Row -->
                    <div class="grid grid-cols-6 gap-4 items-center">
                        <div>
                            <label class="text-sm font-medium text-sap-gray-700">TIN/CST No.</label>
                        </div>
                        <div class="col-span-5">
                            {{ form.tincstno }}
                            {% if form.tincstno.errors %}
                                <span class="text-sap-red-500 text-sm">*</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Remarks Row -->
                <div class="grid grid-cols-4 gap-6 p-6">
                    <div class="flex items-start pt-2">
                        <label class="text-sm font-medium text-sap-gray-700">Remarks</label>
                    </div>
                    <div class="col-span-2">
                        {{ form.remarks }}
                    </div>
                    <div></div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize icons
        setTimeout(() => {
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        }, 100);
        
        // Debug HTMX requests
        document.body.addEventListener('htmx:beforeRequest', function(evt) {
            console.log('HTMX Request:', evt.detail.requestConfig.verb, evt.detail.requestConfig.url);
        });
        
        document.body.addEventListener('htmx:afterRequest', function(evt) {
            console.log('HTMX Response:', evt.detail.xhr.status, evt.detail.xhr.responseText);
            // Reinitialize icons after HTMX updates
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        });
        
        console.log('✅ Customer new form initialized with cascading dropdowns');
    });
</script>
{% endblock %}
