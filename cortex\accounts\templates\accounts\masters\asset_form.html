<!-- accounts/templates/accounts/masters/asset_form.html -->
<!-- Asset Create/Edit Form Template -->
<!-- Task Group 8: Asset Management - Asset Form (Task 8.3) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{% if object %}Edit Asset{% else %}New Asset{% endif %} - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-teal-600 to-sap-teal-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="building" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">
                        {% if object %}Edit Asset{% else %}New Asset{% endif %}
                    </h1>
                    <p class="text-sm text-sap-gray-600 mt-1">
                        {% if object %}Modify asset details and specifications{% else %}Add a new fixed asset to the inventory{% endif %}
                    </p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:asset_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Asset Form -->
    <div class="max-w-6xl mx-auto">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="edit" class="w-5 h-5 mr-2 text-sap-teal-600"></i>
                    Asset Information
                </h3>
                <p class="text-sm text-sap-gray-600 mt-1">Complete all required fields to register the asset</p>
            </div>
            
            <form method="post" id="asset-form" class="p-6" x-data="assetForm()">
                {% csrf_token %}
                
                <!-- Basic Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="info" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Basic Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Asset Name -->
                        <div>
                            <label for="{{ form.asset_name.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Asset Name *
                            </label>
                            {{ form.asset_name|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500" }}
                            {% if form.asset_name.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.asset_name.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Asset Code -->
                        <div>
                            <label for="{{ form.asset_code.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Asset Code
                            </label>
                            {{ form.asset_code|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500" }}
                            {% if form.asset_code.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.asset_code.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Auto-generated if left blank</p>
                        </div>
                        
                        <!-- Serial Number -->
                        <div>
                            <label for="{{ form.serial_number.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Serial Number
                            </label>
                            {{ form.serial_number|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500" }}
                            {% if form.serial_number.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.serial_number.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Asset Type -->
                        <div>
                            <label for="{{ form.asset_type.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Asset Type *
                            </label>
                            {{ form.asset_type|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500" }}
                            {% if form.asset_type.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.asset_type.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Category -->
                        <div>
                            <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Category
                            </label>
                            {{ form.category|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500" }}
                            {% if form.category.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.category.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Status -->
                        <div>
                            <label for="{{ form.status.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Status *
                            </label>
                            {{ form.status|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500" }}
                            {% if form.status.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.status.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Purchase Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="shopping-cart" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                        Purchase Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Purchase Cost -->
                        <div>
                            <label for="{{ form.purchase_cost.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Purchase Cost (₹) *
                            </label>
                            {{ form.purchase_cost|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500" }}
                            {% if form.purchase_cost.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.purchase_cost.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Purchase Date -->
                        <div>
                            <label for="{{ form.purchase_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Purchase Date
                            </label>
                            {{ form.purchase_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500" }}
                            {% if form.purchase_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.purchase_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Supplier -->
                        <div>
                            <label for="{{ form.supplier.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Supplier
                            </label>
                            {{ form.supplier|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500" }}
                            {% if form.supplier.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.supplier.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Invoice Number -->
                        <div>
                            <label for="{{ form.invoice_number.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Invoice Number
                            </label>
                            {{ form.invoice_number|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500" }}
                            {% if form.invoice_number.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.invoice_number.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- PO Number -->
                        <div>
                            <label for="{{ form.po_number.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                PO Number
                            </label>
                            {{ form.po_number|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500" }}
                            {% if form.po_number.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.po_number.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Depreciation Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="trending-down" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                        Depreciation Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <!-- Depreciation Method -->
                        <div>
                            <label for="{{ form.depreciation_method.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Depreciation Method
                            </label>
                            {{ form.depreciation_method|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500" }}
                            {% if form.depreciation_method.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.depreciation_method.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Depreciation Rate -->
                        <div>
                            <label for="{{ form.depreciation_rate.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Depreciation Rate (%)
                            </label>
                            {{ form.depreciation_rate|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500" }}
                            {% if form.depreciation_rate.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.depreciation_rate.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Useful Life -->
                        <div>
                            <label for="{{ form.useful_life_years.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Useful Life (Years)
                            </label>
                            {{ form.useful_life_years|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500" }}
                            {% if form.useful_life_years.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.useful_life_years.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Residual Value -->
                        <div>
                            <label for="{{ form.residual_value.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Residual Value (₹)
                            </label>
                            {{ form.residual_value|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500" }}
                            {% if form.residual_value.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.residual_value.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Location and Assignment Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="map-pin" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                        Location and Assignment
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Location -->
                        <div>
                            <label for="{{ form.location.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Location
                            </label>
                            {{ form.location|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500" }}
                            {% if form.location.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.location.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Department -->
                        <div>
                            <label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Department
                            </label>
                            {{ form.department|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500" }}
                            {% if form.department.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.department.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Assigned To -->
                        <div>
                            <label for="{{ form.assigned_to.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Assigned To
                            </label>
                            {{ form.assigned_to|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500" }}
                            {% if form.assigned_to.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.assigned_to.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Warranty and Insurance Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="shield" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Warranty and Insurance
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <!-- Warranty Start Date -->
                        <div>
                            <label for="{{ form.warranty_start_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Warranty Start Date
                            </label>
                            {{ form.warranty_start_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500" }}
                            {% if form.warranty_start_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.warranty_start_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Warranty End Date -->
                        <div>
                            <label for="{{ form.warranty_end_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Warranty End Date
                            </label>
                            {{ form.warranty_end_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500" }}
                            {% if form.warranty_end_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.warranty_end_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Insurance Start Date -->
                        <div>
                            <label for="{{ form.insurance_start_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Insurance Start Date
                            </label>
                            {{ form.insurance_start_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500" }}
                            {% if form.insurance_start_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.insurance_start_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Insurance End Date -->
                        <div>
                            <label for="{{ form.insurance_end_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Insurance End Date
                            </label>
                            {{ form.insurance_end_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500" }}
                            {% if form.insurance_end_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.insurance_end_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Description Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="file-text" class="w-5 h-5 mr-2 text-sap-gray-600"></i>
                        Additional Information
                    </h4>
                    <div class="grid grid-cols-1 gap-6">
                        <!-- Description -->
                        <div>
                            <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Description
                            </label>
                            {{ form.description|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500" }}
                            {% if form.description.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.description.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Detailed description of the asset including specifications</p>
                        </div>
                        
                        <!-- Notes -->
                        <div>
                            <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Notes
                            </label>
                            {{ form.notes|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-teal-500 focus:border-sap-teal-500" }}
                            {% if form.notes.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.notes.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Additional notes or remarks about the asset</p>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="resetForm()" 
                                class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="rotate-ccw" class="w-4 h-4 inline mr-2"></i>
                            Reset
                        </button>
                        <button type="button" onclick="calculateDepreciation()" 
                                class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="calculator" class="w-4 h-4 inline mr-2"></i>
                            Calculate Depreciation
                        </button>
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="{% url 'accounts:asset_list' %}" 
                           class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-sap-teal-600 hover:bg-sap-teal-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                            {% if object %}Update Asset{% else %}Create Asset{% endif %}
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Asset Guidelines -->
        <div class="mt-6 bg-sap-blue-50 border border-sap-blue-200 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i data-lucide="info" class="w-5 h-5 text-sap-blue-600"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-sap-blue-800">Asset Registration Guidelines</h3>
                    <div class="mt-2 text-sm text-sap-blue-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Ensure all mandatory fields are completed accurately</li>
                            <li>Asset code will be auto-generated if not provided</li>
                            <li>Choose appropriate depreciation method based on asset type</li>
                            <li>Set realistic useful life and residual values</li>
                            <li>Keep warranty and insurance information up to date</li>
                            <li>Assign assets to appropriate departments and employees</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function assetForm() {
    return {
        // Asset form specific data and methods can be added here
    }
}

function resetForm() {
    if (confirm('Are you sure you want to reset the form? All unsaved changes will be lost.')) {
        document.getElementById('asset-form').reset();
    }
}

function calculateDepreciation() {
    const purchaseCost = document.getElementById('{{ form.purchase_cost.id_for_label }}').value;
    const depreciationRate = document.getElementById('{{ form.depreciation_rate.id_for_label }}').value;
    const usefulLife = document.getElementById('{{ form.useful_life_years.id_for_label }}').value;
    const residualValue = document.getElementById('{{ form.residual_value.id_for_label }}').value || 0;
    
    if (!purchaseCost || !depreciationRate || !usefulLife) {
        alert('Please enter purchase cost, depreciation rate, and useful life to calculate depreciation.');
        return;
    }
    
    const annualDepreciation = (purchaseCost - residualValue) * depreciationRate / 100;
    const totalDepreciationOverLife = annualDepreciation * usefulLife;
    const finalBookValue = purchaseCost - totalDepreciationOverLife;
    
    alert(`Depreciation Calculation:
Annual Depreciation: ₹${annualDepreciation.toFixed(2)}
Total Depreciation (${usefulLife} years): ₹${totalDepreciationOverLife.toFixed(2)}
Final Book Value: ₹${finalBookValue.toFixed(2)}`);
}

// Auto-generate asset code if creating new asset
document.addEventListener('DOMContentLoaded', function() {
    const assetCodeInput = document.getElementById('{{ form.asset_code.id_for_label }}');
    if (assetCodeInput && !assetCodeInput.value) {
        // Generate asset code based on current timestamp
        const today = new Date();
        const year = today.getFullYear().toString().substr(-2);
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        const sequence = Math.floor(Math.random() * 100).toString().padStart(2, '0');
        
        assetCodeInput.value = `AST-${year}${month}${day}${sequence}`;
    }
    
    lucide.createIcons();
});
</script>
{% endblock %}