<!-- accounts/templates/accounts/reports/advice_form.html -->
<!-- Payment Advice Form - SAP S/4HANA Inspired Design -->
<!-- Replaces ASP.NET Advice.aspx functionality -->

{% extends 'core/base.html' %}
{% load static %}

{% block title %}Payment Advice - {{ page_title }} - Cortex{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-sap-blue-500 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">Payment Advice</h1>
                    <p class="text-sm text-sap-gray-600 mt-1">Generate payment advice for bank transactions</p>
                </div>
            </div>
            <div class="flex space-x-3">
                <a 
                    href="{% url 'accounts:advice_payment_list' %}"
                    class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-sap-gray-500 focus:ring-offset-2 transition-colors duration-200">
                    <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                    View All
                </a>
                <button 
                    type="button"
                    onclick="printAdvice()"
                    class="inline-flex items-center px-4 py-2 bg-sap-blue-500 text-white rounded-lg hover:bg-sap-blue-600 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:ring-offset-2 transition-colors duration-200">
                    <i data-lucide="printer" class="w-4 h-4 mr-2"></i>
                    Print
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-2 py-6 space-y-6" id="advice-form-content">
    
    <!-- Payment Advice Form -->
    <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-sap-gray-200 bg-sap-gray-50">
            <h3 class="text-lg font-semibold text-sap-gray-800 flex items-center">
                <i data-lucide="edit" class="w-5 h-5 mr-2 text-sap-blue-500"></i>
                Payment Advice Details
            </h3>
        </div>
        
        <form 
            id="advice-form"
            hx-post="{% url 'accounts:advice_payment_create' %}" 
            hx-target="#advice-preview" 
            hx-indicator="#form-indicator"
            class="p-6 space-y-6">
            {% csrf_token %}
            
            <!-- Basic Information -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <h4 class="text-md font-semibold text-sap-gray-800 border-b border-sap-gray-200 pb-2">
                        Basic Information
                    </h4>
                    
                    <!-- Advice Number -->
                    <div>
                        <label for="advice_number" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Advice Number <span class="text-sap-red-500">*</span>
                        </label>
                        <input 
                            type="text" 
                            name="advice_number" 
                            id="advice_number"
                            value="{{ form.advice_number.value|default:'' }}"
                            required
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        {% if form.advice_number.errors %}
                            <p class="mt-1 text-sm text-sap-red-600">{{ form.advice_number.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Advice Date -->
                    <div>
                        <label for="advice_date" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Advice Date <span class="text-sap-red-500">*</span>
                        </label>
                        <input 
                            type="date" 
                            name="advice_date" 
                            id="advice_date"
                            value="{{ form.advice_date.value|default:'' }}"
                            required
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        {% if form.advice_date.errors %}
                            <p class="mt-1 text-sm text-sap-red-600">{{ form.advice_date.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Bank Account -->
                    <div>
                        <label for="bank_account" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Bank Account <span class="text-sap-red-500">*</span>
                        </label>
                        <select 
                            name="bank_account" 
                            id="bank_account"
                            required
                            hx-get="{% url 'accounts:advice_payment_create' %}?get_bank_details=true"
                            hx-target="#bank-details"
                            hx-trigger="change"
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                            <option value="">Select Bank Account</option>
                            {% for bank in bank_accounts %}
                            <option value="{{ bank.id }}" 
                                {% if form.bank_account.value == bank.id|stringformat:"s" %}selected{% endif %}>
                                {{ bank.bank_name }} - {{ bank.account_number }}
                            </option>
                            {% endfor %}
                        </select>
                        {% if form.bank_account.errors %}
                            <p class="mt-1 text-sm text-sap-red-600">{{ form.bank_account.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Payment Mode -->
                    <div>
                        <label for="payment_mode" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Payment Mode <span class="text-sap-red-500">*</span>
                        </label>
                        <select 
                            name="payment_mode" 
                            id="payment_mode"
                            required
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                            <option value="">Select Payment Mode</option>
                            <option value="neft" {% if form.payment_mode.value == "neft" %}selected{% endif %}>NEFT</option>
                            <option value="rtgs" {% if form.payment_mode.value == "rtgs" %}selected{% endif %}>RTGS</option>
                            <option value="imps" {% if form.payment_mode.value == "imps" %}selected{% endif %}>IMPS</option>
                            <option value="cheque" {% if form.payment_mode.value == "cheque" %}selected{% endif %}>Cheque</option>
                            <option value="dd" {% if form.payment_mode.value == "dd" %}selected{% endif %}>Demand Draft</option>
                        </select>
                        {% if form.payment_mode.errors %}
                            <p class="mt-1 text-sm text-sap-red-600">{{ form.payment_mode.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
                
                <div class="space-y-4">
                    <h4 class="text-md font-semibold text-sap-gray-800 border-b border-sap-gray-200 pb-2">
                        Beneficiary Information
                    </h4>
                    
                    <!-- Beneficiary Name -->
                    <div>
                        <label for="beneficiary_name" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Beneficiary Name <span class="text-sap-red-500">*</span>
                        </label>
                        <input 
                            type="text" 
                            name="beneficiary_name" 
                            id="beneficiary_name"
                            value="{{ form.beneficiary_name.value|default:'' }}"
                            required
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        {% if form.beneficiary_name.errors %}
                            <p class="mt-1 text-sm text-sap-red-600">{{ form.beneficiary_name.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Beneficiary Account -->
                    <div>
                        <label for="beneficiary_account" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Beneficiary Account Number <span class="text-sap-red-500">*</span>
                        </label>
                        <input 
                            type="text" 
                            name="beneficiary_account" 
                            id="beneficiary_account"
                            value="{{ form.beneficiary_account.value|default:'' }}"
                            required
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        {% if form.beneficiary_account.errors %}
                            <p class="mt-1 text-sm text-sap-red-600">{{ form.beneficiary_account.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Beneficiary IFSC -->
                    <div>
                        <label for="beneficiary_ifsc" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            IFSC Code <span class="text-sap-red-500">*</span>
                        </label>
                        <input 
                            type="text" 
                            name="beneficiary_ifsc" 
                            id="beneficiary_ifsc"
                            value="{{ form.beneficiary_ifsc.value|default:'' }}"
                            required
                            pattern="[A-Z]{4}0[A-Z0-9]{6}"
                            title="Enter valid IFSC code (e.g., SBIN0001234)"
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        {% if form.beneficiary_ifsc.errors %}
                            <p class="mt-1 text-sm text-sap-red-600">{{ form.beneficiary_ifsc.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Beneficiary Bank -->
                    <div>
                        <label for="beneficiary_bank" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Beneficiary Bank
                        </label>
                        <input 
                            type="text" 
                            name="beneficiary_bank" 
                            id="beneficiary_bank"
                            value="{{ form.beneficiary_bank.value|default:'' }}"
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        {% if form.beneficiary_bank.errors %}
                            <p class="mt-1 text-sm text-sap-red-600">{{ form.beneficiary_bank.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Payment Details -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 pt-6 border-t border-sap-gray-200">
                <div class="space-y-4">
                    <h4 class="text-md font-semibold text-sap-gray-800 border-b border-sap-gray-200 pb-2">
                        Payment Details
                    </h4>
                    
                    <!-- Amount -->
                    <div>
                        <label for="amount" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Amount (₹) <span class="text-sap-red-500">*</span>
                        </label>
                        <input 
                            type="number" 
                            name="amount" 
                            id="amount"
                            value="{{ form.amount.value|default:'' }}"
                            step="0.01"
                            required
                            hx-trigger="input changed delay:500ms"
                            hx-get="{% url 'accounts:advice_payment_create' %}?convert_amount=true"
                            hx-target="#amount-in-words"
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        {% if form.amount.errors %}
                            <p class="mt-1 text-sm text-sap-red-600">{{ form.amount.errors.0 }}</p>
                        {% endif %}
                        <div id="amount-in-words" class="mt-2 text-sm text-sap-gray-600 font-medium">
                            <!-- Amount in words will be loaded here -->
                        </div>
                    </div>
                    
                    <!-- Purpose -->
                    <div>
                        <label for="purpose" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Purpose of Payment <span class="text-sap-red-500">*</span>
                        </label>
                        <textarea 
                            name="purpose" 
                            id="purpose"
                            rows="3"
                            required
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">{{ form.purpose.value|default:'' }}</textarea>
                        {% if form.purpose.errors %}
                            <p class="mt-1 text-sm text-sap-red-600">{{ form.purpose.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Reference Number -->
                    <div>
                        <label for="reference_number" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Reference Number
                        </label>
                        <input 
                            type="text" 
                            name="reference_number" 
                            id="reference_number"
                            value="{{ form.reference_number.value|default:'' }}"
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        {% if form.reference_number.errors %}
                            <p class="mt-1 text-sm text-sap-red-600">{{ form.reference_number.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
                
                <div class="space-y-4">
                    <h4 class="text-md font-semibold text-sap-gray-800 border-b border-sap-gray-200 pb-2">
                        Authorization & Processing
                    </h4>
                    
                    <!-- Priority -->
                    <div>
                        <label for="priority" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Priority
                        </label>
                        <select 
                            name="priority" 
                            id="priority"
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                            <option value="normal" {% if form.priority.value == "normal" %}selected{% endif %}>Normal</option>
                            <option value="high" {% if form.priority.value == "high" %}selected{% endif %}>High</option>
                            <option value="urgent" {% if form.priority.value == "urgent" %}selected{% endif %}>Urgent</option>
                        </select>
                        {% if form.priority.errors %}
                            <p class="mt-1 text-sm text-sap-red-600">{{ form.priority.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Value Date -->
                    <div>
                        <label for="value_date" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Value Date
                        </label>
                        <input 
                            type="date" 
                            name="value_date" 
                            id="value_date"
                            value="{{ form.value_date.value|default:'' }}"
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                        {% if form.value_date.errors %}
                            <p class="mt-1 text-sm text-sap-red-600">{{ form.value_date.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Authorization Level -->
                    <div>
                        <label for="authorization_level" class="block text-sm font-medium text-sap-gray-700 mb-2">
                            Authorization Required
                        </label>
                        <select 
                            name="authorization_level" 
                            id="authorization_level"
                            class="w-full px-3 py-2 border border-sap-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:border-sap-blue-500">
                            <option value="single" {% if form.authorization_level.value == "single" %}selected{% endif %}>Single Authorization</option>
                            <option value="dual" {% if form.authorization_level.value == "dual" %}selected{% endif %}>Dual Authorization</option>
                            <option value="multiple" {% if form.authorization_level.value == "multiple" %}selected{% endif %}>Multiple Authorization</option>
                        </select>
                        {% if form.authorization_level.errors %}
                            <p class="mt-1 text-sm text-sap-red-600">{{ form.authorization_level.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Email Notification -->
                    <div>
                        <label class="flex items-center">
                            <input 
                                type="checkbox" 
                                name="email_notification" 
                                {% if form.email_notification.value %}checked{% endif %}
                                class="rounded border-sap-gray-300 text-sap-blue-600 focus:ring-sap-blue-500">
                            <span class="ml-2 text-sm text-sap-gray-700">Send email notification to beneficiary</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- Form Actions -->
            <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
                <div class="flex space-x-4">
                    <button 
                        type="submit"
                        class="inline-flex items-center px-6 py-2 bg-sap-blue-500 text-white rounded-lg hover:bg-sap-blue-600 focus:outline-none focus:ring-2 focus:ring-sap-blue-500 focus:ring-offset-2 transition-colors duration-200">
                        <i data-lucide="save" class="w-4 h-4 mr-2"></i>
                        Generate Advice
                    </button>
                    <button 
                        type="button"
                        onclick="document.getElementById('advice-form').reset()"
                        class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-sap-gray-500 focus:ring-offset-2 transition-colors duration-200">
                        <i data-lucide="x-circle" class="w-4 h-4 mr-2"></i>
                        Reset
                    </button>
                </div>
                
                <!-- Save as Draft -->
                <button 
                    type="button"
                    hx-post="{% url 'accounts:advice_payment_create' %}?save_draft=true"
                    hx-include="#advice-form"
                    class="inline-flex items-center px-4 py-2 border border-sap-gray-300 rounded-lg text-sm font-medium text-sap-gray-700 bg-white hover:bg-sap-gray-50 focus:outline-none focus:ring-2 focus:ring-sap-gray-500 focus:ring-offset-2 transition-colors duration-200">
                    <i data-lucide="file-plus" class="w-4 h-4 mr-2"></i>
                    Save as Draft
                </button>
            </div>
        </form>
        
        <!-- Form Indicator -->
        <div id="form-indicator" class="htmx-indicator p-6 border-t border-sap-gray-200">
            <div class="flex items-center justify-center py-4">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-sap-blue-500 mr-3"></div>
                <span class="text-sap-gray-600">Processing advice...</span>
            </div>
        </div>
    </div>

    <!-- Bank Details -->
    <div id="bank-details">
        <!-- Bank details will be loaded here when bank account is selected -->
    </div>

    <!-- Payment Advice Preview -->
    <div id="advice-preview">
        <!-- Generated advice preview will appear here -->
    </div>
</div>

<!-- Print Styles -->
<style>
@media print {
    .no-print { 
        display: none !important; 
    }
    
    .print-only {
        display: block !important;
    }
    
    body { 
        print-color-adjust: exact; 
        font-size: 12px;
    }
    
    .bg-white { 
        background: white !important; 
    }
    
    .text-white { 
        color: black !important; 
    }
    
    .border { 
        border: 1px solid #000 !important; 
    }
    
    .px-2 { 
        padding-left: 0.5rem; 
        padding-right: 0.5rem; 
    }
    
    .py-6 { 
        padding-top: 1rem; 
        padding-bottom: 1rem; 
    }
    
    /* Print-specific advice formatting */
    .advice-header {
        text-align: center;
        margin-bottom: 20px;
        border-bottom: 2px solid #000;
        padding-bottom: 10px;
    }
    
    .advice-content {
        margin: 20px 0;
        line-height: 1.6;
    }
    
    .signature-section {
        margin-top: 40px;
        display: flex;
        justify-content: space-between;
    }
    
    .signature-box {
        width: 200px;
        height: 60px;
        border-top: 1px solid #000;
        text-align: center;
        padding-top: 5px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
    
    // Format IFSC code input
    document.getElementById('beneficiary_ifsc').addEventListener('input', function() {
        this.value = this.value.toUpperCase();
    });
    
    // Auto-generate advice number if empty
    if (!document.getElementById('advice_number').value) {
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        document.getElementById('advice_number').value = `ADV${year}${month}${random}`;
    }
    
    // Set today's date as default
    if (!document.getElementById('advice_date').value) {
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('advice_date').value = today;
    }
    
    // Amount validation
    document.getElementById('amount').addEventListener('input', function() {
        const amount = parseFloat(this.value);
        if (amount > 200000) {
            // Show RTGS recommendation for amounts > 2 lacs
            const paymentMode = document.getElementById('payment_mode');
            if (paymentMode.value === 'neft') {
                paymentMode.value = 'rtgs';
                alert('Amount exceeds ₹2,00,000. Payment mode changed to RTGS as recommended.');
            }
        }
    });
});

function printAdvice() {
    const previewContent = document.getElementById('advice-preview').innerHTML;
    if (!previewContent.trim()) {
        alert('Please generate the advice first before printing.');
        return;
    }
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Payment Advice</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .advice-header { text-align: center; margin-bottom: 20px; border-bottom: 2px solid #000; padding-bottom: 10px; }
                .advice-content { margin: 20px 0; line-height: 1.6; }
                .signature-section { margin-top: 40px; display: flex; justify-content: space-between; }
                .signature-box { width: 200px; height: 60px; border-top: 1px solid #000; text-align: center; padding-top: 5px; }
                table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                th, td { border: 1px solid #000; padding: 8px; text-align: left; }
                th { background-color: #f0f0f0; }
                .amount { font-weight: bold; font-size: 1.2em; }
            </style>
        </head>
        <body>
            ${previewContent}
        </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// Email notification toggle
document.querySelector('[name="email_notification"]').addEventListener('change', function() {
    if (this.checked) {
        // You could add an email input field dynamically here
        console.log('Email notification enabled');
    }
});

// Form auto-save to localStorage
function autoSaveForm() {
    const form = document.getElementById('advice-form');
    const formData = new FormData(form);
    const data = {};
    for (let [key, value] of formData.entries()) {
        if (value) data[key] = value;
    }
    localStorage.setItem('adviceFormDraft', JSON.stringify(data));
}

// Load draft from localStorage
function loadDraft() {
    const draft = localStorage.getItem('adviceFormDraft');
    if (draft) {
        const data = JSON.parse(draft);
        const form = document.getElementById('advice-form');
        Object.keys(data).forEach(key => {
            const field = form.querySelector(`[name="${key}"]`);
            if (field) {
                if (field.type === 'checkbox') {
                    field.checked = data[key] === 'on';
                } else {
                    field.value = data[key];
                }
            }
        });
    }
}

// Auto-save on form changes
document.getElementById('advice-form').addEventListener('input', autoSaveForm);

// Load draft on page load
document.addEventListener('DOMContentLoaded', loadDraft);
</script>
{% endblock %}