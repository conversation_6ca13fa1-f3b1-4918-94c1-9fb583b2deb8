<!-- accounts/partials/account_head_row.html -->
<!-- HTMX partial for individual Account Head table row -->
<!-- Supports inline editing like ASP.NET GridView -->

<tr class="hover:bg-sap-gray-50 transition-colors duration-150" id="account-head-row-{{ account_head.id }}">
    <!-- Edit Button Column -->
    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
        {% if account_head.id != 19 and account_head.id != 33 %}
        <button type="button"
                hx-get="{% url 'accounts:account_head_edit_row' account_head.id %}"
                hx-target="#account-head-row-{{ account_head.id }}"
                hx-swap="outerHTML"
                class="text-sap-blue-600 hover:text-sap-blue-900 underline">
            Edit
        </button>
        {% else %}
        <span class="text-sap-gray-400">-</span>
        {% endif %}
    </td>
    
    <!-- Delete Button Column -->
    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
        {% if account_head.id != 19 and account_head.id != 33 %}
        <button type="button"
                hx-delete="{% url 'accounts:account_head_delete' account_head.id %}"
                hx-target="#account-head-row-{{ account_head.id }}"
                hx-swap="outerHTML"
                hx-confirm="Are you sure you want to delete this account head? This action cannot be undone."
                class="text-sap-red-600 hover:text-sap-red-900 underline">
            Delete
        </button>
        {% else %}
        <span class="text-sap-gray-400">-</span>
        {% endif %}
    </td>
    
    <!-- Serial Number Column -->
    <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900 text-right">
        {{ forloop.counter }}
    </td>
    
    <!-- Category Column -->
    <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900">
        {{ account_head.category|default:"N/A" }}
    </td>
    
    <!-- Description Column -->
    <td class="px-6 py-4 text-sm text-sap-gray-900">
        <div class="max-w-xs truncate" title="{{ account_head.description }}">
            {{ account_head.description|default:"N/A" }}
        </div>
    </td>
    
    <!-- Symbol Column -->
    <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900 font-mono">
        {{ account_head.symbol|default:"N/A" }}
    </td>
    
    <!-- Abbreviation Column -->
    <td class="px-6 py-4 whitespace-nowrap text-sm text-sap-gray-900 font-mono">
        {{ account_head.abbreviation|default:"N/A" }}
    </td>
</tr>