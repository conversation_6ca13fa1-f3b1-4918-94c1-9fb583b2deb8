{% extends "core/base.html" %}
{% load static %}

{% block title %}Enquiry #{{ enquiry.enqid }} - {{ enquiry.customername }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-slate-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- Breadcrumb Navigation -->
        <nav class="flex items-center space-x-2 text-sm mb-8" aria-label="Breadcrumb">
            <div class="flex items-center space-x-2 bg-white/80 backdrop-blur-sm rounded-lg px-4 py-2 border border-slate-200/60 shadow-sm">
                <a href="{% url 'sales_distribution:dashboard' %}" 
                   class="text-blue-600 hover:text-blue-700 transition-colors duration-200 font-medium flex items-center space-x-1">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                    </svg>
                    <span>Sales Distribution</span>
                </a>
                <svg class="w-4 h-4 text-slate-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
                <a href="{% url 'sales_distribution:enquiry_list' %}" 
                   class="text-blue-600 hover:text-blue-700 transition-colors duration-200 font-medium flex items-center space-x-1">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                    </svg>
                    <span>Enquiries</span>
                </a>
                <svg class="w-4 h-4 text-slate-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
                <span class="text-slate-700 font-semibold">Enquiry #{{ enquiry.enqid }}</span>
            </div>
        </nav>

        <!-- Main Header Card -->
        <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200/60 mb-8 overflow-hidden">
            <div class="bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 px-8 py-10">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="mb-6 lg:mb-0">
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                </svg>
                            </div>
                            <h1 class="text-4xl font-bold text-white tracking-tight">
                                Enquiry #{{ enquiry.enqid }}
                            </h1>
                        </div>
                        <p class="text-blue-100 text-xl mb-4 font-medium">
                            {{ enquiry.customername|default:"—" }}
                        </p>
                        <div class="flex flex-wrap items-center gap-3">
                            {% if enquiry.flag == 1 %}
                                <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-emerald-100 text-emerald-800 border border-emerald-200">
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                    </svg>
                                    Existing Customer
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-blue-100 text-blue-800 border border-blue-200">
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                                    </svg>
                                    New Customer
                                </span>
                            {% endif %}
                            <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-slate-100 text-slate-800 border border-slate-200">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                                </svg>
                                Active
                            </span>
                        </div>
                    </div>
                    <div class="flex flex-col sm:flex-row gap-3">
                        <a href="{% url 'sales_distribution:enquiry_edit' enquiry.enqid %}" 
                           class="group inline-flex items-center justify-center px-6 py-3 border-2 border-white/30 rounded-xl text-white font-semibold hover:bg-white/10 hover:border-white/50 transition-all duration-300 backdrop-blur-sm">
                            <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                            </svg>
                            Edit Enquiry
                        </a>
                        <a href="{% url 'sales_distribution:quotation_create' enquiry.enqid %}" 
                           class="group inline-flex items-center justify-center px-6 py-3 bg-emerald-500 hover:bg-emerald-600 border-2 border-emerald-500 hover:border-emerald-600 rounded-xl text-white font-semibold transition-all duration-300 shadow-lg hover:shadow-xl">
                            <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                            </svg>
                            Create Quotation
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enquiry Description Card -->
        {% if enquiry.enquiryfor %}
        <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 mb-8 overflow-hidden hover:shadow-xl transition-all duration-300">
            <div class="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-5 border-b border-slate-200/60">
                <h2 class="text-xl font-bold text-slate-800 flex items-center">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                    </div>
                    Enquiry Description
                </h2>
            </div>
            <div class="p-8">
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-blue-500 p-6 rounded-r-xl shadow-sm">
                    <p class="text-slate-800 text-lg leading-relaxed font-medium">
                        {{ enquiry.enquiryfor }}
                    </p>
                    {% if enquiry.remark %}
                        <div class="mt-6 pt-6 border-t border-blue-200/60">
                            <p class="font-bold text-blue-900 mb-3 text-sm uppercase tracking-wide">Additional Remarks:</p>
                            <p class="text-blue-800 text-base leading-relaxed">{{ enquiry.remark }}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Information Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Customer Information -->
            <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 overflow-hidden hover:shadow-xl transition-all duration-300 group">
                <div class="bg-gradient-to-r from-emerald-50 to-green-50 px-6 py-5 border-b border-slate-200/60">
                    <h3 class="text-xl font-bold text-slate-800 flex items-center">
                        <div class="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-200">
                            <svg class="w-5 h-5 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                            </svg>
                        </div>
                        Customer Information
                    </h3>
                </div>
                <div class="p-6 space-y-1">
                    <div class="flex justify-between items-start py-4 border-b border-slate-100 hover:bg-slate-50/50 transition-colors duration-200 rounded-lg px-3 -mx-3">
                        <span class="text-sm font-semibold text-slate-600 w-36">Customer Name</span>
                        <span class="text-sm text-slate-900 font-medium text-right flex-1">
                            {{ enquiry.customername|default:"—" }}
                        </span>
                    </div>
                    <div class="flex justify-between items-start py-4 border-b border-slate-100 hover:bg-slate-50/50 transition-colors duration-200 rounded-lg px-3 -mx-3">
                        <span class="text-sm font-semibold text-slate-600 w-36">Customer ID</span>
                        <span class="text-sm text-slate-900 text-right flex-1 font-mono bg-slate-100 px-2 py-1 rounded">
                            {{ enquiry.customerid|default:"Auto-generated" }}
                        </span>
                    </div>
                    <div class="flex justify-between items-start py-4 border-b border-slate-100 hover:bg-slate-50/50 transition-colors duration-200 rounded-lg px-3 -mx-3">
                        <span class="text-sm font-semibold text-slate-600 w-36">Contact Person</span>
                        <span class="text-sm text-slate-900 text-right flex-1">
                            {{ enquiry.contactperson|default:"—" }}
                        </span>
                    </div>
                    <div class="flex justify-between items-start py-4 border-b border-slate-100 hover:bg-slate-50/50 transition-colors duration-200 rounded-lg px-3 -mx-3">
                        <span class="text-sm font-semibold text-slate-600 w-36">Email</span>
                        <span class="text-sm text-right flex-1">
                            {% if enquiry.email %}
                                <a href="mailto:{{ enquiry.email }}" 
                                   class="text-blue-600 hover:text-blue-800 hover:underline bg-blue-50 px-2 py-1 rounded transition-colors duration-200">
                                    {{ enquiry.email }}
                                </a>
                            {% else %}
                                <span class="text-slate-900">—</span>
                            {% endif %}
                        </span>
                    </div>
                    <div class="flex justify-between items-start py-4 hover:bg-slate-50/50 transition-colors duration-200 rounded-lg px-3 -mx-3">
                        <span class="text-sm font-semibold text-slate-600 w-36">Contact Number</span>
                        <span class="text-sm text-slate-900 text-right flex-1 font-mono">
                            {{ enquiry.contactno|default:"—" }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 overflow-hidden hover:shadow-xl transition-all duration-300 group">
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-5 border-b border-slate-200/60">
                    <h3 class="text-xl font-bold text-slate-800 flex items-center">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-200">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                            </svg>
                        </div>
                        System Information
                    </h3>
                </div>
                <div class="p-6 space-y-1">
                    <div class="flex justify-between items-start py-4 border-b border-slate-100 hover:bg-slate-50/50 transition-colors duration-200 rounded-lg px-3 -mx-3">
                        <span class="text-sm font-semibold text-slate-600 w-36">Enquiry ID</span>
                        <span class="text-sm text-slate-900 text-right flex-1 font-mono font-bold bg-blue-100 px-2 py-1 rounded">
                            #{{ enquiry.enqid }}
                        </span>
                    </div>
                    <div class="flex justify-between items-start py-4 border-b border-slate-100 hover:bg-slate-50/50 transition-colors duration-200 rounded-lg px-3 -mx-3">
                        <span class="text-sm font-semibold text-slate-600 w-36">Created Date</span>
                        <span class="text-sm text-slate-900 text-right flex-1">
                            {{ enquiry.sysdate|default:"—" }}
                        </span>
                    </div>
                    <div class="flex justify-between items-start py-4 border-b border-slate-100 hover:bg-slate-50/50 transition-colors duration-200 rounded-lg px-3 -mx-3">
                        <span class="text-sm font-semibold text-slate-600 w-36">Created Time</span>
                        <span class="text-sm text-slate-900 text-right flex-1 font-mono">
                            {{ enquiry.systime|default:"—" }}
                        </span>
                    </div>
                    <div class="flex justify-between items-start py-4 border-b border-slate-100 hover:bg-slate-50/50 transition-colors duration-200 rounded-lg px-3 -mx-3">
                        <span class="text-sm font-semibold text-slate-600 w-36">Created By</span>
                        <span class="text-sm text-slate-900 text-right flex-1">
                            {{ enquiry.sessionid|default:"System" }}
                        </span>
                    </div>
                    <div class="flex justify-between items-start py-4 hover:bg-slate-50/50 transition-colors duration-200 rounded-lg px-3 -mx-3">
                        <span class="text-sm font-semibold text-slate-600 w-36">Status</span>
                        <span class="text-right flex-1">
                            <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-bold bg-emerald-100 text-emerald-800 border border-emerald-200">
                                <div class="w-2 h-2 bg-emerald-500 rounded-full mr-2 animate-pulse"></div>
                                Active
                            </span>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Address Information -->
        <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 mb-8 overflow-hidden hover:shadow-xl transition-all duration-300">
            <div class="bg-gradient-to-r from-orange-50 to-amber-50 px-6 py-5 border-b border-slate-200/60">
                <h2 class="text-xl font-bold text-slate-800 flex items-center">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                        </svg>
                    </div>
                    Address Information
                </h2>
            </div>
            <div class="p-8">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Registered Office -->
                    <div class="border-2 border-slate-200 rounded-xl overflow-hidden hover:border-blue-300 transition-colors duration-300 group">
                        <div class="bg-gradient-to-r from-blue-50 to-blue-100 px-5 py-4 border-b border-slate-200">
                            <h4 class="text-base font-bold text-slate-800 flex items-center">
                                <div class="w-6 h-6 bg-blue-200 rounded-lg flex items-center justify-center mr-2 group-hover:scale-110 transition-transform duration-200">
                                    <svg class="w-4 h-4 text-blue-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                                    </svg>
                                </div>
                                Registered Office
                            </h4>
                        </div>
                        <div class="p-5">
                            {% if enquiry.regdaddress %}
                                <div class="bg-gradient-to-br from-blue-50 to-slate-50 border-l-4 border-blue-500 p-5 rounded-r-lg">
                                    <div class="font-semibold text-slate-900 mb-3 text-sm leading-relaxed">{{ enquiry.regdaddress }}</div>
                                    {% if enquiry.regdcity or enquiry.regdstate or enquiry.regdcountry %}
                                        <div class="text-sm text-slate-600 mb-3 font-medium">
                                            {% if enquiry.regdcity %}{{ enquiry.regdcity.cityname }}{% endif %}{% if enquiry.regdstate %}, {{ enquiry.regdstate.statename }}{% endif %}{% if enquiry.regdcountry %}, {{ enquiry.regdcountry.countryname }}{% endif %}
                                            {% if enquiry.regdpinno %} - <span class="font-mono">{{ enquiry.regdpinno }}</span>{% endif %}
                                        </div>
                                    {% endif %}
                                    {% if enquiry.regdcontactno %}
                                        <div class="text-sm text-slate-600 flex items-center bg-white/60 px-3 py-2 rounded-lg">
                                            <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                            </svg>
                                            <span class="font-mono">{{ enquiry.regdcontactno }}</span>
                                        </div>
                                    {% endif %}
                                </div>
                            {% else %}
                                <div class="text-center py-12 text-slate-400">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"/>
                                    </svg>
                                    <p class="text-sm font-medium">Not provided</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Works/Factory -->
                    <div class="border-2 border-slate-200 rounded-xl overflow-hidden hover:border-emerald-300 transition-colors duration-300 group">
                        <div class="bg-gradient-to-r from-emerald-50 to-green-100 px-5 py-4 border-b border-slate-200">
                            <h4 class="text-base font-bold text-slate-800 flex items-center">
                                <div class="w-6 h-6 bg-emerald-200 rounded-lg flex items-center justify-center mr-2 group-hover:scale-110 transition-transform duration-200">
                                    <svg class="w-4 h-4 text-emerald-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"/>
                                    </svg>
                                </div>
                                Works/Factory
                            </h4>
                        </div>
                        <div class="p-5">
                            {% if enquiry.workaddress %}
                                <div class="bg-gradient-to-br from-emerald-50 to-slate-50 border-l-4 border-emerald-500 p-5 rounded-r-lg">
                                    <div class="font-semibold text-slate-900 mb-3 text-sm leading-relaxed">{{ enquiry.workaddress }}</div>
                                    {% if enquiry.workcity or enquiry.workstate or enquiry.workcountry %}
                                        <div class="text-sm text-slate-600 mb-3 font-medium">
                                            {% if enquiry.workcity %}{{ enquiry.workcity.cityname }}{% endif %}{% if enquiry.workstate %}, {{ enquiry.workstate.statename }}{% endif %}{% if enquiry.workcountry %}, {{ enquiry.workcountry.countryname }}{% endif %}
                                            {% if enquiry.workpinno %} - <span class="font-mono">{{ enquiry.workpinno }}</span>{% endif %}
                                        </div>
                                    {% endif %}
                                    {% if enquiry.workcontactno %}
                                        <div class="text-sm text-slate-600 flex items-center bg-white/60 px-3 py-2 rounded-lg">
                                            <svg class="w-4 h-4 mr-2 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                            </svg>
                                            <span class="font-mono">{{ enquiry.workcontactno }}</span>
                                        </div>
                                    {% endif %}
                                </div>
                            {% else %}
                                <div class="text-center py-12 text-slate-400">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"/>
                                    </svg>
                                    <p class="text-sm font-medium">Not provided</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Material Delivery -->
                    <div class="border-2 border-slate-200 rounded-xl overflow-hidden hover:border-orange-300 transition-colors duration-300 group">
                        <div class="bg-gradient-to-r from-orange-50 to-amber-100 px-5 py-4 border-b border-slate-200">
                            <h4 class="text-base font-bold text-slate-800 flex items-center">
                                <div class="w-6 h-6 bg-orange-200 rounded-lg flex items-center justify-center mr-2 group-hover:scale-110 transition-transform duration-200">
                                    <svg class="w-4 h-4 text-orange-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
                                    </svg>
                                </div>
                                Material Delivery
                            </h4>
                        </div>
                        <div class="p-5">
                            {% if enquiry.materialdeladdress %}
                                <div class="bg-gradient-to-br from-orange-50 to-slate-50 border-l-4 border-orange-500 p-5 rounded-r-lg">
                                    <div class="font-semibold text-slate-900 mb-3 text-sm leading-relaxed">{{ enquiry.materialdeladdress }}</div>
                                    {% if enquiry.materialdelcity or enquiry.materialdelstate or enquiry.materialdelcountry %}
                                        <div class="text-sm text-slate-600 mb-3 font-medium">
                                            {% if enquiry.materialdelcity %}{{ enquiry.materialdelcity.cityname }}{% endif %}{% if enquiry.materialdelstate %}, {{ enquiry.materialdelstate.statename }}{% endif %}{% if enquiry.materialdelcountry %}, {{ enquiry.materialdelcountry.countryname }}{% endif %}
                                            {% if enquiry.materialdelpinno %} - <span class="font-mono">{{ enquiry.materialdelpinno }}</span>{% endif %}
                                        </div>
                                    {% endif %}
                                    {% if enquiry.materialdelcontactno %}
                                        <div class="text-sm text-slate-600 flex items-center bg-white/60 px-3 py-2 rounded-lg">
                                            <svg class="w-4 h-4 mr-2 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                            </svg>
                                            <span class="font-mono">{{ enquiry.materialdelcontactno }}</span>
                                        </div>
                                    {% endif %}
                                </div>
                            {% else %}
                                <div class="text-center py-12 text-slate-400">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"/>
                                    </svg>
                                    <p class="text-sm font-medium">Not provided</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Business Information -->
        {% if enquiry.juridictioncode or enquiry.eccno or enquiry.panno or enquiry.tinvatno or enquiry.tincstno or enquiry.tdscode %}
        <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 mb-8 overflow-hidden hover:shadow-xl transition-all duration-300">
            <div class="bg-gradient-to-r from-purple-50 to-pink-50 px-6 py-5 border-b border-slate-200/60">
                <h2 class="text-xl font-bold text-slate-800 flex items-center">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 00-2 2H10a2 2 0 00-2-2V6m8 0V8a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0h2a2 2 0 012 2v6a2 2 0 01-2 2h-2m0 0v2a2 2 0 002 2h2a2 2 0 002-2v-2"/>
                        </svg>
                    </div>
                    Business Information
                </h2>
            </div>
            <div class="p-8">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {% if enquiry.juridictioncode %}
                        <div class="bg-gradient-to-br from-purple-50 to-slate-50 p-6 rounded-xl border border-purple-200 hover:shadow-md transition-all duration-200">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-bold text-slate-600 uppercase tracking-wide">Jurisdiction Code</span>
                                <span class="text-lg text-slate-900 font-mono font-bold">{{ enquiry.juridictioncode }}</span>
                            </div>
                        </div>
                    {% endif %}
                    {% if enquiry.eccno %}
                        <div class="bg-gradient-to-br from-blue-50 to-slate-50 p-6 rounded-xl border border-blue-200 hover:shadow-md transition-all duration-200">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-bold text-slate-600 uppercase tracking-wide">ECC Number</span>
                                <span class="text-lg text-slate-900 font-mono font-bold">{{ enquiry.eccno }}</span>
                            </div>
                        </div>
                    {% endif %}
                    {% if enquiry.panno %}
                        <div class="bg-gradient-to-br from-emerald-50 to-slate-50 p-6 rounded-xl border border-emerald-200 hover:shadow-md transition-all duration-200">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-bold text-slate-600 uppercase tracking-wide">PAN Number</span>
                                <span class="text-lg text-slate-900 font-mono font-bold">{{ enquiry.panno }}</span>
                            </div>
                        </div>
                    {% endif %}
                    {% if enquiry.tinvatno %}
                        <div class="bg-gradient-to-br from-orange-50 to-slate-50 p-6 rounded-xl border border-orange-200 hover:shadow-md transition-all duration-200">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-bold text-slate-600 uppercase tracking-wide">TIN/VAT Number</span>
                                <span class="text-lg text-slate-900 font-mono font-bold">{{ enquiry.tinvatno }}</span>
                            </div>
                        </div>
                    {% endif %}
                    {% if enquiry.tincstno %}
                        <div class="bg-gradient-to-br from-red-50 to-slate-50 p-6 rounded-xl border border-red-200 hover:shadow-md transition-all duration-200">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-bold text-slate-600 uppercase tracking-wide">TIN/CST Number</span>
                                <span class="text-lg text-slate-900 font-mono font-bold">{{ enquiry.tincstno }}</span>
                            </div>
                        </div>
                    {% endif %}
                    {% if enquiry.tdscode %}
                        <div class="bg-gradient-to-br from-yellow-50 to-slate-50 p-6 rounded-xl border border-yellow-200 hover:shadow-md transition-all duration-200">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-bold text-slate-600 uppercase tracking-wide">TDS Code</span>
                                <span class="text-lg text-slate-900 font-mono font-bold">{{ enquiry.tdscode }}</span>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Attachments -->
        {% if attachments %}
        <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 mb-8 overflow-hidden hover:shadow-xl transition-all duration-300">
            <div class="bg-gradient-to-r from-teal-50 to-cyan-50 px-6 py-5 border-b border-slate-200/60">
                <h2 class="text-xl font-bold text-slate-800 flex items-center">
                    <div class="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"/>
                        </svg>
                    </div>
                    Attachments ({{ attachments.count }})
                </h2>
            </div>
            <div class="p-8">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {% for attachment in attachments %}
                        <div class="bg-gradient-to-br from-slate-50 to-blue-50 border-2 border-slate-200 rounded-xl p-6 hover:border-blue-300 hover:shadow-lg transition-all duration-300 group">
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                                    {% if ".pdf" in attachment.filename|lower %}
                                        <svg class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
                                        </svg>
                                    {% elif ".doc" in attachment.filename|lower %}
                                        <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"/>
                                        </svg>
                                    {% elif ".xls" in attachment.filename|lower %}
                                        <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clip-rule="evenodd"/>
                                        </svg>
                                    {% elif ".jpg" in attachment.filename|lower or ".png" in attachment.filename|lower %}
                                        <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                                        </svg>
                                    {% else %}
                                        <svg class="w-6 h-6 text-slate-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"/>
                                        </svg>
                                    {% endif %}
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="font-semibold text-slate-900 text-sm mb-2 truncate">
                                        {{ attachment.filename }}
                                    </div>
                                    <div class="text-xs text-slate-500 mb-3">
                                        {% if attachment.filesize %}{{ attachment.filesize|filesizeformat }}{% endif %}
                                    </div>
                                    <div>
                                        <a href="#" onclick="alert('Download feature coming soon!')" 
                                           class="inline-flex items-center px-4 py-2 border-2 border-blue-300 rounded-lg text-xs font-semibold text-blue-700 bg-blue-50 hover:bg-blue-100 hover:border-blue-400 transition-all duration-200 group">
                                            <svg class="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                            </svg>
                                            Download
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Related Quotations -->
        {% if quotations %}
        <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/60 mb-8 overflow-hidden hover:shadow-xl transition-all duration-300">
            <div class="bg-gradient-to-r from-emerald-50 to-green-50 px-6 py-5 border-b border-slate-200/60">
                <h2 class="text-xl font-bold text-slate-800 flex items-center">
                    <div class="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                        </svg>
                    </div>
                    Related Quotations ({{ quotations.count }})
                </h2>
            </div>
            <div class="p-8">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-slate-200">
                        <thead class="bg-gradient-to-r from-slate-50 to-blue-50">
                            <tr>
                                <th class="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">Quotation #</th>
                                <th class="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-slate-200">
                            {% for quotation in quotations %}
                                <tr class="hover:bg-slate-50 transition-colors duration-200">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <a href="{% url 'sales_distribution:quotation_detail' quotation.id %}" 
                                           class="text-blue-600 hover:text-blue-800 font-bold text-base hover:underline">
                                            #{{ quotation.quotationno }}
                                        </a>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-900 font-medium">
                                        {{ quotation.sysdate }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        {% if quotation.authorize == 1 %}
                                            <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-bold bg-emerald-100 text-emerald-800 border border-emerald-200">
                                                <div class="w-2 h-2 bg-emerald-500 rounded-full mr-2"></div>
                                                Authorized
                                            </span>
                                        {% elif quotation.approve == 1 %}
                                            <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-bold bg-yellow-100 text-yellow-800 border border-yellow-200">
                                                <div class="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                                                Approved
                                            </span>
                                        {% elif quotation.checked == 1 %}
                                            <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-bold bg-blue-100 text-blue-800 border border-blue-200">
                                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                                                Checked
                                            </span>
                                        {% else %}
                                            <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-bold bg-slate-100 text-slate-800 border border-slate-200">
                                                <div class="w-2 h-2 bg-slate-500 rounded-full mr-2"></div>
                                                Draft
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="{% url 'sales_distribution:quotation_detail' quotation.id %}" 
                                           class="inline-flex items-center px-4 py-2 border-2 border-blue-300 rounded-lg text-xs font-semibold text-blue-700 bg-blue-50 hover:bg-blue-100 hover:border-blue-400 transition-all duration-200 group">
                                            <svg class="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                            </svg>
                                            View
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Action Footer -->
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 pt-8 border-t border-slate-200">
            <a href="{% url 'sales_distribution:enquiry_list' %}" 
               class="group inline-flex items-center justify-center px-8 py-4 border-2 border-slate-300 rounded-xl text-slate-700 bg-white hover:bg-slate-50 hover:border-slate-400 font-semibold transition-all duration-300 shadow-sm hover:shadow-md">
                <svg class="w-5 h-5 mr-3 group-hover:-translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                Back to Enquiries
            </a>
            
            <div class="flex flex-col sm:flex-row gap-4">
                <a href="{% url 'sales_distribution:enquiry_edit' enquiry.enqid %}" 
                   class="group inline-flex items-center justify-center px-8 py-4 bg-blue-600 hover:bg-blue-700 border-2 border-blue-600 hover:border-blue-700 rounded-xl text-white font-semibold transition-all duration-300 shadow-lg hover:shadow-xl">
                    <svg class="w-5 h-5 mr-3 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                    </svg>
                    Edit Enquiry
                </a>
                <a href="{% url 'sales_distribution:quotation_create' enquiry.enqid %}" 
                   class="group inline-flex items-center justify-center px-8 py-4 bg-emerald-600 hover:bg-emerald-700 border-2 border-emerald-600 hover:border-emerald-700 rounded-xl text-white font-semibold transition-all duration-300 shadow-lg hover:shadow-xl">
                    <svg class="w-5 h-5 mr-3 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                    </svg>
                    Convert to Quotation
                </a>
            </div>
        </div>
    </div>
</div>
<script>
    // Add smooth scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';
    
    // Add loading animation to cards
    document.addEventListener('DOMContentLoaded', function() {
        const cards = document.querySelectorAll('.bg-white\\/95');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            setTimeout(() => {
                card.style.transition = 'all 0.6s ease-out';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    });
</script>
{% endblock %}