# accounts/tests/test_taxation_basic.py
# Basic working tests for taxation module

from decimal import Decimal
from datetime import date

from django.test import TestCase
from django.contrib.auth.models import User

from accounts.models import VAT, ExciseDuty, TDSCode, Octori
from accounts.tax_calculation_engine import TaxCal<PERSON>tionEngine, CalculationMethod
from sys_admin.models import Company, FinancialYear


class BasicTaxationTestCase(TestCase):
    """Basic test cases for taxation functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test company and financial year
        self.company = Company.objects.create(
            company_name='Test Company',
            company_short_name='TEST'
        )
        
        self.financial_year = FinancialYear.objects.create(
            financial_year='2024-25',
            start_date=date(2024, 4, 1),
            end_date=date(2025, 3, 31),
            is_current=True
        )
    
    def test_vat_model_creation(self):
        """Test VAT model creation"""
        vat = VAT.objects.create(
            vat_percentage=5.50,
            description='Standard VAT Rate'
        )
        
        self.assertEqual(vat.vat_percentage, 5.50)
        self.assertEqual(vat.description, 'Standard VAT Rate')
        self.assertIn('5.5%', str(vat))
    
    def test_excise_duty_model_creation(self):
        """Test ExciseDuty model creation"""
        excise = ExciseDuty.objects.create(
            terms='Standard Excise',
            value=Decimal('12.00'),
            accessible_value=Decimal('100.00'),
            edu_cess=Decimal('2.00'),
            she_cess=Decimal('1.00'),
            is_default_excise=True,
            is_active=True,
            created_by=self.user
        )
        
        self.assertEqual(excise.terms, 'Standard Excise')
        self.assertEqual(excise.value, Decimal('12.00'))
        self.assertTrue(excise.is_default_excise)
        self.assertTrue(excise.is_active)
    
    def test_excise_duty_calculation(self):
        """Test ExciseDuty calculation methods"""
        excise = ExciseDuty.objects.create(
            terms='Test Excise',
            value=Decimal('10.00'),
            accessible_value=Decimal('100.00'),
            edu_cess=Decimal('2.00'),
            she_cess=Decimal('1.00'),
            is_active=True
        )
        
        # Test calculation for 1000 amount
        base_amount = 1000
        calculation = excise.calculate_excise_amount(base_amount)
        
        self.assertEqual(calculation['accessible_amount'], 1000.0)
        self.assertEqual(calculation['basic_excise'], 100.0)  # 10% of 1000
        self.assertEqual(calculation['edu_cess'], 2.0)  # 2% of 100
        self.assertEqual(calculation['she_cess'], 1.0)  # 1% of 100
        self.assertEqual(calculation['total_excise'], 103.0)  # 100 + 2 + 1
    
    def test_tds_code_model_creation(self):
        """Test TDSCode model creation"""
        tds = TDSCode.objects.create(
            tds_code='194A',
            description='Professional Services',
            tds_percentage=10.00
        )
        
        self.assertEqual(tds.tds_code, '194A')
        self.assertEqual(tds.description, 'Professional Services')
        self.assertEqual(tds.tds_percentage, 10.00)
    
    def test_octori_model_creation(self):
        """Test Octori model creation"""
        octori = Octori.objects.create(
            octori_percentage=1.50,
            description='Local tax'
        )
        
        self.assertEqual(octori.octori_percentage, 1.50)
        self.assertEqual(octori.description, 'Local tax')
    
    def test_tax_calculation_engine_basic(self):
        """Test basic tax calculation engine functionality"""
        engine = TaxCalculationEngine()
        
        # Test engine initialization
        self.assertEqual(engine.precision, Decimal('0.01'))
        self.assertEqual(engine.tax_precision, Decimal('0.001'))
        
        # Create test excise duty
        excise_duty = ExciseDuty.objects.create(
            terms='Test Excise Engine',
            value=Decimal('12.00'),
            accessible_value=Decimal('100.00'),
            edu_cess=Decimal('2.00'),
            she_cess=Decimal('1.00'),
            is_active=True
        )
        
        # Test excise calculation
        base_amount = Decimal('10000.00')
        result = engine.calculate_excise_duty(
            base_amount, 
            excise_duty, 
            CalculationMethod.EXCLUSIVE
        )
        
        self.assertEqual(result.base_amount, base_amount)
        self.assertGreater(result.total_tax_amount, 0)
        self.assertGreater(len(result.tax_components), 0)
    
    def test_tax_calculation_engine_vat(self):
        """Test VAT calculation in engine"""
        engine = TaxCalculationEngine()
        
        base_amount = Decimal('10000.00')
        vat_rate = Decimal('5.00')
        
        result = engine.calculate_vat(base_amount, vat_rate, CalculationMethod.EXCLUSIVE)
        
        self.assertEqual(result.base_amount, base_amount)
        self.assertEqual(result.total_tax_amount, Decimal('500.00'))  # 5% of 10000
        self.assertEqual(result.total_amount, Decimal('10500.00'))  # 10000 + 500
    
    def test_tax_calculation_engine_tds(self):
        """Test TDS calculation in engine"""
        engine = TaxCalculationEngine()
        
        base_amount = Decimal('50000.00')
        tds_rate = Decimal('10.00')
        threshold = Decimal('30000.00')
        
        result = engine.calculate_tds(base_amount, tds_rate, threshold)
        
        self.assertEqual(result.base_amount, base_amount)
        self.assertEqual(result.total_tax_amount, Decimal('5000.00'))  # 10% of 50000
        self.assertEqual(result.total_amount, Decimal('45000.00'))  # 50000 - 5000 (TDS deducted)
    
    def test_tax_calculation_engine_composite(self):
        """Test composite tax calculation"""
        engine = TaxCalculationEngine()
        
        base_amount = Decimal('10000.00')
        tax_config = {
            'excise': {
                'rate': 12.0,
                'accessible_value': 100.0,
                'edu_cess': 2.0,
                'she_cess': 1.0
            },
            'vat': {
                'rate': 5.0
            }
        }
        
        result = engine.calculate_composite_tax(base_amount, tax_config)
        
        self.assertEqual(result.base_amount, base_amount)
        self.assertGreater(result.total_tax_amount, 0)
        self.assertGreater(result.total_amount, base_amount)
        
        # Should have multiple tax components
        self.assertGreater(len(result.tax_components), 1)
    
    def test_form_basic_validation(self):
        """Test basic form validation"""
        from accounts.forms import VATForm, TDSCodeForm, OctoriForm
        
        # Test VAT form
        vat_data = {
            'vat_percentage': 5.50,
            'description': 'Test VAT Rate'
        }
        vat_form = VATForm(data=vat_data)
        self.assertTrue(vat_form.is_valid(), f"VAT form errors: {vat_form.errors}")
        
        # Test TDS form
        tds_data = {
            'tds_code': '194A',
            'tds_percentage': 10.00,
            'description': 'Test TDS Code'
        }
        tds_form = TDSCodeForm(data=tds_data)
        self.assertTrue(tds_form.is_valid(), f"TDS form errors: {tds_form.errors}")
        
        # Test Octori form
        octori_data = {
            'octori_percentage': 1.50,
            'description': 'Test Octori'
        }
        octori_form = OctoriForm(data=octori_data)
        self.assertTrue(octori_form.is_valid(), f"Octori form errors: {octori_form.errors}")