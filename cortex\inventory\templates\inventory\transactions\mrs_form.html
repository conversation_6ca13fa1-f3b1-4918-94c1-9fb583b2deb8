{% extends "core/base.html" %}
{% load static %}

{% block title %}
    {% if object %}Edit MRS - {{ object.mrs_number }}{% else %}New Material Requisition Slip{% endif %} - Inventory
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-4">
                <li>
                    <div>
                        <a href="{% url 'inventory:mrs_list' %}" class="text-gray-400 hover:text-gray-500">
                            <svg class="flex-shrink-0 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                            </svg>
                            <span class="sr-only">Home</span>
                        </a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                        </svg>
                        <a href="{% url 'inventory:mrs_list' %}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">MRS</a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                        </svg>
                        <span class="ml-4 text-sm font-medium text-gray-500">
                            {% if object %}Edit - {{ object.mrs_number }}{% else %}New MRS{% endif %}
                        </span>
                    </div>
                </li>
            </ol>
        </nav>
        
        <div class="mt-2">
            <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                {% if object %}Edit Material Requisition Slip{% else %}New Material Requisition Slip{% endif %}
            </h1>
            {% if object %}
                <p class="mt-1 text-sm text-gray-500">
                    MRS Number: {{ object.mrs_number }} • Status: {{ object.get_status_display }}
                </p>
            {% endif %}
        </div>
    </div>

    <form method="post" class="space-y-6">
        {% csrf_token %}
        
        <!-- Main Form -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Basic Information</h3>
                
                {% if form.non_field_errors %}
                    <div class="rounded-md bg-red-50 p-4 mb-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                                <div class="mt-2 text-sm text-red-700">
                                    <ul class="list-disc pl-5 space-y-1">
                                        {% for error in form.non_field_errors %}
                                            <li>{{ error }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endif %}

                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label for="{{ form.mrs_date.id_for_label }}" class="block text-sm font-medium text-gray-700">MRS Date *</label>
                        {{ form.mrs_date }}
                        {% if form.mrs_date.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.mrs_date.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.required_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Required Date *</label>
                        {{ form.required_date }}
                        {% if form.required_date.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.required_date.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.requisition_type.id_for_label }}" class="block text-sm font-medium text-gray-700">Requisition Type *</label>
                        {{ form.requisition_type }}
                        {% if form.requisition_type.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.requisition_type.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.priority.id_for_label }}" class="block text-sm font-medium text-gray-700">Priority *</label>
                        {{ form.priority }}
                        {% if form.priority.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.priority.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.department_id.id_for_label }}" class="block text-sm font-medium text-gray-700">Department ID</label>
                        {{ form.department_id }}
                        {% if form.department_id.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.department_id.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.work_order_number.id_for_label }}" class="block text-sm font-medium text-gray-700">Work Order Number</label>
                        {{ form.work_order_number }}
                        {% if form.work_order_number.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.work_order_number.errors.0 }}</p>
                        {% endif %}
                        <p class="mt-1 text-sm text-gray-500">Required for Production requisitions</p>
                    </div>

                    <div class="sm:col-span-2">
                        <label for="{{ form.project_code.id_for_label }}" class="block text-sm font-medium text-gray-700">Project Code</label>
                        {{ form.project_code }}
                        {% if form.project_code.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.project_code.errors.0 }}</p>
                        {% endif %}
                        <p class="mt-1 text-sm text-gray-500">Required for Project requisitions</p>
                    </div>

                    <div class="sm:col-span-2">
                        <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">Remarks</label>
                        {{ form.remarks }}
                        {% if form.remarks.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.remarks.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Line Items Section -->
        {% if not object %}
            <!-- Show line items section only for new MRS -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Line Items</h3>
                    
                    <div class="space-y-6">
                        <!-- Item Search Section -->
                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Category Filter</label>
                                <select id="category_filter" name="category_filter" 
                                        class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                        hx-get="{% url 'inventory:item_search_api' %}"
                                        hx-trigger="change"
                                        hx-target="#item-search-results"
                                        hx-swap="innerHTML"
                                        hx-include="[name='item_search']">
                                    <option value="">All Categories</option>
                                    <!-- Categories will be loaded via API -->
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Search Items</label>
                                <input type="text" id="item_search" name="item_search" 
                                       class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                                       placeholder="Search by item code or description (min 2 chars)"
                                       hx-get="{% url 'inventory:item_search_api' %}"
                                       hx-trigger="keyup changed delay:300ms"
                                       hx-target="#item-search-results"
                                       hx-swap="innerHTML"
                                       hx-include="[name='category_filter']"
                                       autocomplete="off">
                            </div>
                        </div>
                        
                        <!-- Search Results -->
                        <div id="item-search-results" class="space-y-2">
                            <div class="text-sm text-gray-500 text-center py-4">
                                Type at least 2 characters to search for items
                            </div>
                        </div>
                        
                        <!-- Selected Items Table -->
                        <div id="selected-items-section" class="hidden">
                            <h4 class="text-md font-medium text-gray-900 mb-3">Selected Items</h4>
                            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                                <table class="w-full table-auto divide-y divide-gray-300" id="selected-items-table">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Available Stock</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requested Qty</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200" id="selected-items-tbody">
                                        <!-- Selected items will be added here dynamically -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
        
        <!-- Action Buttons -->
        <div class="flex justify-end space-x-3">
            <a href="{% if object %}{% url 'inventory:mrs_detail' object.pk %}{% else %}{% url 'inventory:mrs_list' %}{% endif %}" 
               class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Cancel
            </a>
            <button type="submit" 
                    class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    {% if not object %}disabled{% endif %}>
                {% if object %}Update MRS{% else %}Create MRS with Items{% endif %}
            </button>
        </div>
    </form>
</div>

<script>
// Global variables for managing line items
let selectedItems = [];
let itemCounter = 0;

document.addEventListener('DOMContentLoaded', function() {
    // Auto-populate fields based on requisition type
    const requisitionTypeField = document.getElementById('{{ form.requisition_type.id_for_label }}');
    const workOrderField = document.getElementById('{{ form.work_order_number.id_for_label }}');
    const projectCodeField = document.getElementById('{{ form.project_code.id_for_label }}');
    
    function toggleRequiredFields() {
        const selectedType = requisitionTypeField ? requisitionTypeField.value : '';
        
        if (workOrderField && projectCodeField) {
            // Reset required styling
            workOrderField.parentElement.classList.remove('required-field');
            projectCodeField.parentElement.classList.remove('required-field');
            
            // Add required styling based on type
            if (selectedType === 'PRODUCTION') {
                workOrderField.parentElement.classList.add('required-field');
            } else if (selectedType === 'PROJECT') {
                projectCodeField.parentElement.classList.add('required-field');
            }
        }
    }
    
    if (requisitionTypeField) {
        requisitionTypeField.addEventListener('change', toggleRequiredFields);
        toggleRequiredFields(); // Initial call
    }
    
    // Load categories on page load (only for new MRS)
    {% if not object %}
        loadCategories();
        updateSubmitButtonState();
    {% endif %}
});

// Load categories from API
function loadCategories() {
    fetch('{% url "inventory:categories_api" %}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const categorySelect = document.getElementById('category_filter');
                if (categorySelect) {
                    data.categories.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category.id;
                        option.textContent = category.name;
                        categorySelect.appendChild(option);
                    });
                }
            }
        })
        .catch(error => console.error('Error loading categories:', error));
}

// Handle item selection from search results
function selectItem(itemData) {
    // Check if item is already selected
    if (selectedItems.find(item => item.id === itemData.id)) {
        showNotification('Item already selected', 'warning');
        return;
    }
    
    // Add item to selected items
    selectedItems.push({
        id: itemData.id,
        item_code: itemData.item_code,
        description: itemData.description,
        available_stock: itemData.available_stock,
        category_name: itemData.category_name,
        uom: itemData.uom || 'PCS',
        requested_quantity: 1,
        remarks: '',
        stock_status: itemData.stock_status
    });
    
    updateSelectedItemsTable();
    updateSubmitButtonState();
    
    // Clear search
    const searchInput = document.getElementById('item_search');
    const searchResults = document.getElementById('item-search-results');
    if (searchInput) searchInput.value = '';
    if (searchResults) {
        searchResults.innerHTML = '<div class="text-sm text-gray-500 text-center py-4">Type at least 2 characters to search for items</div>';
    }
}

// Update the selected items table
function updateSelectedItemsTable() {
    const section = document.getElementById('selected-items-section');
    const tbody = document.getElementById('selected-items-tbody');
    
    if (!section || !tbody) return;
    
    if (selectedItems.length === 0) {
        section.classList.add('hidden');
        return;
    }
    
    section.classList.remove('hidden');
    tbody.innerHTML = '';
    
    selectedItems.forEach((item, index) => {
        const row = document.createElement('tr');
        row.className = index % 2 === 0 ? 'bg-white' : 'bg-gray-50';
        
        const stockClass = item.stock_status === 'out_of_stock' ? 'text-red-600' : 
                          item.stock_status === 'low_stock' ? 'text-yellow-600' : 'text-green-600';
        
        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${item.item_code}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.description}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm ${stockClass}">${item.available_stock}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <input type="number" 
                       class="w-20 px-2 py-1 text-sm border border-gray-300 rounded"
                       value="${item.requested_quantity}"
                       min="0.01"
                       step="0.01"
                       max="${item.available_stock}"
                       onchange="updateItemQuantity(${index}, this.value)"
                       onblur="validateQuantity(${index}, this)">
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.uom}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <input type="text" 
                       class="w-32 px-2 py-1 text-sm border border-gray-300 rounded"
                       value="${item.remarks}"
                       placeholder="Remarks"
                       onchange="updateItemRemarks(${index}, this.value)">
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <button type="button" 
                        class="text-red-600 hover:text-red-900"
                        onclick="removeItem(${index})">
                    Remove
                </button>
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// Update item quantity
function updateItemQuantity(index, quantity) {
    const qty = parseFloat(quantity);
    if (qty > 0) {
        selectedItems[index].requested_quantity = qty;
    }
}

// Update item remarks
function updateItemRemarks(index, remarks) {
    selectedItems[index].remarks = remarks;
}

// Validate quantity against available stock
function validateQuantity(index, input) {
    const qty = parseFloat(input.value);
    const item = selectedItems[index];
    
    if (qty > item.available_stock) {
        input.classList.add('border-red-500');
        showNotification(`Requested quantity (${qty}) exceeds available stock (${item.available_stock}) for ${item.item_code}`, 'error');
    } else {
        input.classList.remove('border-red-500');
    }
}

// Remove item from selection
function removeItem(index) {
    selectedItems.splice(index, 1);
    updateSelectedItemsTable();
    updateSubmitButtonState();
}

// Update submit button state
function updateSubmitButtonState() {
    const submitButton = document.querySelector('button[type="submit"]');
    if (!submitButton) return;
    
    if (selectedItems.length > 0) {
        submitButton.disabled = false;
        submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
    } else {
        submitButton.disabled = true;
        submitButton.classList.add('opacity-50', 'cursor-not-allowed');
    }
}

// Show notification
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-md shadow-lg z-50 ${
        type === 'error' ? 'bg-red-100 text-red-800' :
        type === 'warning' ? 'bg-yellow-100 text-yellow-800' :
        type === 'success' ? 'bg-green-100 text-green-800' :
        'bg-blue-100 text-blue-800'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Remove after 5 seconds
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

// Handle form submission
document.addEventListener('submit', function(e) {
    {% if not object %}
        if (selectedItems.length === 0) {
            e.preventDefault();
            showNotification('Please add at least one item to the MRS', 'error');
            return;
        }
        
        // Add selected items as hidden form fields
        selectedItems.forEach((item, index) => {
            const hiddenFields = [
                { name: `line_items[${index}][item_id]`, value: item.id },
                { name: `line_items[${index}][requested_quantity]`, value: item.requested_quantity },
                { name: `line_items[${index}][remarks]`, value: item.remarks }
            ];
            
            hiddenFields.forEach(field => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = field.name;
                input.value = field.value;
                e.target.appendChild(input);
            });
        });
    {% endif %}
});
</script>

{% endblock %}