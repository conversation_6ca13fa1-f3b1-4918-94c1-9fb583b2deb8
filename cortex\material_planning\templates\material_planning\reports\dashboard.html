{% extends 'material_planning/base.html' %}

{% block title %}Planning Dashboard{% endblock %}

{% block content %}
<div class="space-y-6" x-data="dashboardData()" x-init="loadData()">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Planning Dashboard</h1>
                <p class="text-gray-600 mt-1">Executive overview of material planning performance</p>
            </div>
            <button @click="refreshData()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                Refresh Data
            </button>
        </div>
    </div>

    <!-- Planning Overview -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Plans</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ planning_overview.total_plans }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active Plans</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ planning_overview.active_plans }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Draft Plans</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ planning_overview.draft_plans }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Completed</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ planning_overview.completed_plans }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 text-red-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Overdue</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ planning_overview.overdue_plans }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Total Budget</h3>
            <p class="text-2xl font-bold text-green-600">₹{{ financial_metrics.total_budget|floatformat:2 }}</p>
        </div>
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Estimated Cost</h3>
            <p class="text-2xl font-bold text-blue-600">₹{{ financial_metrics.total_estimated_cost|floatformat:2 }}</p>
        </div>
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Actual Cost</h3>
            <p class="text-2xl font-bold text-orange-600">₹{{ financial_metrics.total_actual_cost|floatformat:2 }}</p>
        </div>
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Budget Utilization</h3>
            <p class="text-2xl font-bold {% if financial_metrics.budget_utilization > 100 %}text-red-600{% else %}text-green-600{% endif %}">
                {{ financial_metrics.budget_utilization|floatformat:1 }}%
            </p>
        </div>
    </div>

    <!-- Supplier Summary -->
    <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Supplier Overview</h2>
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-gray-900">{{ supplier_summary.total_suppliers }}</p>
                <p class="text-sm text-gray-600">Total Suppliers</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-red-600">{{ supplier_summary.category_a_suppliers }}</p>
                <p class="text-sm text-gray-600">Category A</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-yellow-600">{{ supplier_summary.category_o_suppliers }}</p>
                <p class="text-sm text-gray-600">Category O</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-green-600">{{ supplier_summary.category_f_suppliers }}</p>
                <p class="text-sm text-gray-600">Category F</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-blue-600">{{ supplier_summary.preferred_suppliers }}</p>
                <p class="text-sm text-gray-600">Preferred</p>
            </div>
        </div>
    </div>

    <!-- Exception Summary -->
    <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Exception Summary</h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-gray-900">{{ exception_summary.total_exceptions }}</p>
                <p class="text-sm text-gray-600">Total Exceptions</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-yellow-600">{{ exception_summary.open_exceptions }}</p>
                <p class="text-sm text-gray-600">Open</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-red-600">{{ exception_summary.critical_exceptions }}</p>
                <p class="text-sm text-gray-600">Critical</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-orange-600">{{ exception_summary.overdue_exceptions }}</p>
                <p class="text-sm text-gray-600">Overdue</p>
            </div>
        </div>
        <div class="mt-4">
            <span class="text-gray-400 font-medium">View All Exceptions (Coming Soon) →</span>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Recent Plans</h2>
            <div class="space-y-3">
                {% for plan in recent_plans %}
                    <div class="flex justify-between items-center py-2 border-b border-gray-200">
                        <div>
                            <p class="font-medium text-gray-900">{{ plan.plan_number }}</p>
                            <p class="text-sm text-gray-600">{{ plan.plan_name }}</p>
                        </div>
                        <span class="px-2 py-1 text-xs rounded-full
                            {% if plan.status == 'draft' %}bg-yellow-100 text-yellow-800
                            {% elif plan.status == 'approved' %}bg-green-100 text-green-800
                            {% else %}bg-blue-100 text-blue-800{% endif %}">
                            {{ plan.get_status_display }}
                        </span>
                    </div>
                {% empty %}
                    <p class="text-gray-500">No recent plans</p>
                {% endfor %}
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Recent Exceptions</h2>
            <div class="space-y-3">
                {% for exception in recent_exceptions %}
                    <div class="flex justify-between items-center py-2 border-b border-gray-200">
                        <div>
                            <p class="font-medium text-gray-900">{{ exception.exception_type }}</p>
                            <p class="text-sm text-gray-600">{{ exception.exception_title }}</p>
                        </div>
                        <span class="px-2 py-1 text-xs rounded-full
                            {% if exception.exception_severity == 'critical' %}bg-red-100 text-red-800
                            {% elif exception.exception_severity == 'high' %}bg-orange-100 text-orange-800
                            {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                            {{ exception.get_exception_severity_display }}
                        </span>
                    </div>
                {% empty %}
                    <p class="text-gray-500">No recent exceptions</p>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="flex items-center p-4 border border-gray-200 rounded-lg bg-gray-50">
                <div class="p-2 bg-gray-200 rounded-lg mr-3">
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                </div>
                <span class="font-medium text-gray-400">Create Plan (Coming Soon)</span>
            </div>

            <div class="flex items-center p-4 border border-gray-200 rounded-lg bg-gray-50">
                <div class="p-2 bg-gray-200 rounded-lg mr-3">
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <span class="font-medium text-gray-400">Performance (Coming Soon)</span>
            </div>

            <div class="flex items-center p-4 border border-gray-200 rounded-lg bg-gray-50">
                <div class="p-2 bg-gray-200 rounded-lg mr-3">
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <span class="font-medium text-gray-400">Suppliers (Coming Soon)</span>
            </div>

            <div class="flex items-center p-4 border border-gray-200 rounded-lg bg-gray-50">
                <div class="p-2 bg-gray-200 rounded-lg mr-3">
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                </div>
                <span class="font-medium text-gray-400">Trends (Coming Soon)</span>
            </div>
        </div>
    </div>
</div>

<script>
function dashboardData() {
    return {
        loadData() {
            // Initialize dashboard data
        },
        refreshData() {
            // Refresh dashboard via HTMX
            htmx.ajax('GET', '/material_planning/reports/htmx/dashboard-refresh/', {
                target: 'body',
                swap: 'none'
            });
        }
    }
}
</script>
{% endblock %}