{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ page_title }} - Sales Distribution{% endblock %}

{% block extra_css %}
<style>
/* SAP Fiori-inspired professional styling */
.search-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    padding: 24px;
    margin-bottom: 20px;
}

.search-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    align-items: end;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
    font-size: 14px;
}

.form-group select,
.form-group input {
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.form-group select:focus,
.form-group input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-btn {
    background: #1d4ed8;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.search-btn:hover {
    background: #1e40af;
}

/* Table styling */
.work-order-table {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
}

.work-order-table table {
    width: 100%;
    border-collapse: collapse;
}

.work-order-table th {
    background: #f8fafc;
    padding: 16px 12px;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 2px solid #e5e7eb;
    font-size: 14px;
}

.work-order-table td {
    padding: 12px;
    border-bottom: 1px solid #e5e7eb;
    font-size: 14px;
}

.work-order-table tr:hover {
    background-color: #f8fafc;
}

.work-order-table tr:last-child td {
    border-bottom: none;
}

/* Work order link styling */
.wo-link {
    color: #1d4ed8;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.wo-link:hover {
    color: #1e40af;
    text-decoration: underline;
}

/* Status indicators */
.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-open {
    background: #dcfdf7;
    color: #065f46;
}

.status-closed {
    background: #fee2e2;
    color: #991b1b;
}

/* Pagination styling */
.pagination-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    padding: 16px 24px;
    margin-top: 20px;
    display: flex;
    justify-content: between;
    align-items: center;
}

.pagination-info {
    color: #6b7280;
    font-size: 14px;
}

.pagination-nav {
    display: flex;
    gap: 8px;
}

.pagination-nav a,
.pagination-nav span {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    text-decoration: none;
    color: #374151;
    font-size: 14px;
    transition: all 0.2s ease;
}

.pagination-nav a:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
}

.pagination-nav .current {
    background: #1d4ed8;
    color: white;
    border-color: #1d4ed8;
}

/* No results styling */
.no-results {
    text-align: center;
    padding: 40px 20px;
    color: #6b7280;
}

.no-results h3 {
    color: #374151;
    margin-bottom: 8px;
}

/* Responsive design */
@media (max-width: 768px) {
    .search-grid {
        grid-template-columns: 1fr;
    }
    
    .work-order-table {
        overflow-x: auto;
    }
    
    .work-order-table table {
        min-width: 800px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-800">{{ page_title }}</h1>
        <p class="text-gray-600 mt-1">Search and edit work orders</p>
    </div>

    <!-- Search Section -->
    <div class="search-section">
        <form method="get" class="space-y-4">
            <div class="search-grid">
                <div class="form-group">
                    <label for="search_by">Search By</label>
                    <select name="search_by" id="search_by" onchange="toggleSearchFields()">
                        <option value="0" {% if search_by == '0' %}selected{% endif %}>Customer Name</option>
                        <option value="1" {% if search_by == '1' %}selected{% endif %}>Enquiry No</option>
                        <option value="2" {% if search_by == '2' %}selected{% endif %}>PO No</option>
                        <option value="3" {% if search_by == '3' %}selected{% endif %}>WO No</option>
                    </select>
                </div>

                <div class="form-group" id="customer_search_field" {% if search_by != '0' %}style="display: none;"{% endif %}>
                    <label for="search_value">Customer Name</label>
                    <input type="text" name="search_value" id="search_value" 
                           value="{{ search_value }}" placeholder="Start typing customer name..."
                           autocomplete="off">
                    <div id="customer-suggestions" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 hidden"></div>
                </div>

                <div class="form-group" id="text_search_field" {% if search_by == '0' %}style="display: none;"{% endif %}>
                    <label for="search_text">Value</label>
                    <input type="text" name="search_text" id="search_text" 
                           value="{{ search_text }}" placeholder="Enter search value...">
                </div>

                <div class="form-group">
                    <label for="wo_category">Category</label>
                    <select name="wo_category" id="wo_category">
                        <option value="">All Categories</option>
                        {% for category in work_order_categories %}
                            <option value="{{ category.cid }}" 
                                    {% if wo_category == category.cid|stringformat:"s" %}selected{% endif %}>
                                {{ category.symbol }} - {{ category.cname }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <div class="form-group">
                    <label>&nbsp;</label>
                    <button type="submit" class="search-btn">Search</button>
                </div>
            </div>
        </form>
    </div>

    <!-- Results Section -->
    <div class="work-order-table">
        {% if work_orders %}
            <table>
                <thead>
                    <tr>
                        <th style="width: 5%;">SN</th>
                        <th style="width: 10%;">Fin Yrs</th>
                        <th style="width: 25%;">Customer Name</th>
                        <th style="width: 8%;">Code</th>
                        <th style="width: 12%;">Enquiry No</th>
                        <th style="width: 12%;">PO No</th>
                        <th style="width: 10%;">WO No</th>
                        <th style="width: 10%;">Gen. Date</th>
                        <th style="width: 8%;">Gen. By</th>
                    </tr>
                </thead>
                <tbody>
                    {% for work_order in work_orders %}
                    <tr>
                        <td class="text-center">{{ forloop.counter }}</td>
                        <td>{{ work_order.financial_year|default:"-" }}</td>
                        <td>{{ work_order.customer_name|default:"-" }}</td>
                        <td class="text-center">{{ work_order.customerid|default:"-" }}</td>
                        <td class="text-center">{{ work_order.enqid.enqid|default:"-" }}</td>
                        <td>{{ work_order.pono|default:"-" }}</td>
                        <td class="text-center">
                            <a href="{% url 'sales_distribution:work_order_edit_details' work_order.pk %}" 
                               class="wo-link">
                                {{ work_order.wono|default:"-" }}
                            </a>
                        </td>
                        <td class="text-center">{{ work_order.formatted_date|default:"-" }}</td>
                        <td>{{ work_order.employee_name|default:"-" }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <div class="no-results">
                <h3>No Work Orders Found</h3>
                <p>No work orders match your search criteria. Try adjusting your search parameters.</p>
            </div>
        {% endif %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="pagination-section">
        <div class="pagination-info">
            Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} entries
        </div>
        
        <div class="pagination-nav">
            {% if page_obj.has_previous %}
                <a href="?page=1{% if request.GET.search_by %}&search_by={{ request.GET.search_by }}{% endif %}{% if request.GET.search_value %}&search_value={{ request.GET.search_value }}{% endif %}{% if request.GET.search_text %}&search_text={{ request.GET.search_text }}{% endif %}{% if request.GET.wo_category %}&wo_category={{ request.GET.wo_category }}{% endif %}">First</a>
                <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search_by %}&search_by={{ request.GET.search_by }}{% endif %}{% if request.GET.search_value %}&search_value={{ request.GET.search_value }}{% endif %}{% if request.GET.search_text %}&search_text={{ request.GET.search_text }}{% endif %}{% if request.GET.wo_category %}&wo_category={{ request.GET.wo_category }}{% endif %}">&laquo; Previous</a>
            {% endif %}
            
            <span class="current">{{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
            
            {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search_by %}&search_by={{ request.GET.search_by }}{% endif %}{% if request.GET.search_value %}&search_value={{ request.GET.search_value }}{% endif %}{% if request.GET.search_text %}&search_text={{ request.GET.search_text }}{% endif %}{% if request.GET.wo_category %}&wo_category={{ request.GET.wo_category }}{% endif %}">Next &raquo;</a>
                <a href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search_by %}&search_by={{ request.GET.search_by }}{% endif %}{% if request.GET.search_value %}&search_value={{ request.GET.search_value }}{% endif %}{% if request.GET.search_text %}&search_text={{ request.GET.search_text }}{% endif %}{% if request.GET.wo_category %}&wo_category={{ request.GET.wo_category }}{% endif %}">Last</a>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>

<script>
// Search field toggle functionality
function toggleSearchFields() {
    const searchBy = document.getElementById('search_by').value;
    const customerField = document.getElementById('customer_search_field');
    const textField = document.getElementById('text_search_field');
    
    if (searchBy === '0') {
        customerField.style.display = 'block';
        textField.style.display = 'none';
        document.getElementById('search_text').value = '';
    } else {
        customerField.style.display = 'none';
        textField.style.display = 'block';
        document.getElementById('search_value').value = '';
    }
}

// Customer autocomplete functionality
let customerAutocompleteTimeout;
const customerInput = document.getElementById('search_value');
const customerSuggestions = document.getElementById('customer-suggestions');

if (customerInput) {
    customerInput.addEventListener('input', function() {
        clearTimeout(customerAutocompleteTimeout);
        const query = this.value.trim();
        
        if (query.length < 2) {
            customerSuggestions.innerHTML = '';
            customerSuggestions.classList.add('hidden');
            return;
        }
        
        customerAutocompleteTimeout = setTimeout(() => {
            fetch(`{% url 'sales_distribution:work_order_customer_autocomplete_edit' %}?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    customerSuggestions.innerHTML = '';
                    
                    if (data.customers && data.customers.length > 0) {
                        data.customers.forEach(customer => {
                            const div = document.createElement('div');
                            div.className = 'px-3 py-2 cursor-pointer hover:bg-gray-100';
                            div.textContent = customer.text;
                            div.addEventListener('click', function() {
                                customerInput.value = customer.text;
                                customerSuggestions.innerHTML = '';
                                customerSuggestions.classList.add('hidden');
                            });
                            customerSuggestions.appendChild(div);
                        });
                        customerSuggestions.classList.remove('hidden');
                    } else {
                        customerSuggestions.classList.add('hidden');
                    }
                })
                .catch(error => {
                    console.error('Error fetching customer suggestions:', error);
                    customerSuggestions.classList.add('hidden');
                });
        }, 300);
    });
    
    // Hide suggestions when clicking outside
    document.addEventListener('click', function(event) {
        if (!customerInput.contains(event.target) && !customerSuggestions.contains(event.target)) {
            customerSuggestions.classList.add('hidden');
        }
    });
    
    // Handle keyboard navigation
    customerInput.addEventListener('keydown', function(event) {
        const suggestions = customerSuggestions.querySelectorAll('div');
        let selectedIndex = Array.from(suggestions).findIndex(s => s.classList.contains('bg-blue-100'));
        
        if (event.key === 'ArrowDown') {
            event.preventDefault();
            if (selectedIndex < suggestions.length - 1) {
                if (selectedIndex >= 0) suggestions[selectedIndex].classList.remove('bg-blue-100');
                suggestions[selectedIndex + 1].classList.add('bg-blue-100');
            }
        } else if (event.key === 'ArrowUp') {
            event.preventDefault();
            if (selectedIndex > 0) {
                suggestions[selectedIndex].classList.remove('bg-blue-100');
                suggestions[selectedIndex - 1].classList.add('bg-blue-100');
            }
        } else if (event.key === 'Enter') {
            event.preventDefault();
            if (selectedIndex >= 0) {
                suggestions[selectedIndex].click();
            } else {
                // Submit form
                this.closest('form').submit();
            }
        } else if (event.key === 'Escape') {
            customerSuggestions.classList.add('hidden');
        }
    });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleSearchFields();
});
</script>
{% endblock %}
