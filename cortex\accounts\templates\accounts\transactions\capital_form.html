<!-- accounts/templates/accounts/transactions/capital_form.html -->
<!-- Capital Create/Edit Form Template -->
<!-- Task Group 7: Capital & Loans Management - Capital Form (Task 7.6) -->

{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}{% if object %}Edit Capital Entry{% else %}New Capital Entry{% endif %} - Accounts{% endblock %}

{% block page_header %}
<div class="bg-white border-b border-sap-gray-200">
    <div class="px-8 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-sap-green-600 to-sap-green-700 rounded-lg flex items-center justify-center shadow-sm">
                    <i data-lucide="dollar-sign" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-semibold text-sap-gray-800">
                        {% if object %}Edit Capital Entry{% else %}New Capital Entry{% endif %}
                    </h1>
                    <p class="text-sm text-sap-gray-600 mt-1">
                        {% if object %}Modify capital structure details{% else %}Add a new capital entry to the company structure{% endif %}
                    </p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'accounts:capital_list' %}" 
                   class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="px-8 py-6">
    
    <!-- Capital Form -->
    <div class="max-w-6xl mx-auto">
        <div class="bg-white rounded-lg border border-sap-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-sap-gray-100">
                <h3 class="text-lg font-medium text-sap-gray-800 flex items-center">
                    <i data-lucide="edit" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                    Capital Information
                </h3>
                <p class="text-sm text-sap-gray-600 mt-1">Complete all required fields to register the capital entry</p>
            </div>
            
            <form method="post" id="capital-form" class="p-6" x-data="capitalForm()">
                {% csrf_token %}
                
                <!-- Basic Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="info" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                        Basic Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Capital Code -->
                        <div>
                            <label for="{{ form.capital_code.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Capital Code
                            </label>
                            {{ form.capital_code|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.capital_code.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.capital_code.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Auto-generated if left blank</p>
                        </div>
                        
                        <!-- Capital Type -->
                        <div>
                            <label for="{{ form.capital_type.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Capital Type *
                            </label>
                            {{ form.capital_type|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.capital_type.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.capital_type.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Category -->
                        <div>
                            <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Category *
                            </label>
                            {{ form.category|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.category.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.category.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Subcategory -->
                        <div>
                            <label for="{{ form.subcategory.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Subcategory
                            </label>
                            {{ form.subcategory|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.subcategory.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.subcategory.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Status -->
                        <div>
                            <label for="{{ form.status.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Status *
                            </label>
                            {{ form.status|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.status.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.status.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Reference Number -->
                        <div>
                            <label for="{{ form.reference_number.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Reference Number
                            </label>
                            {{ form.reference_number|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.reference_number.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.reference_number.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Capital Amount Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="calculator" class="w-5 h-5 mr-2 text-sap-blue-600"></i>
                        Capital Amount Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Authorized Amount -->
                        <div>
                            <label for="{{ form.authorized_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Authorized Amount (₹) *
                            </label>
                            {{ form.authorized_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.authorized_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.authorized_amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Issued Amount -->
                        <div>
                            <label for="{{ form.issued_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Issued Amount (₹) *
                            </label>
                            {{ form.issued_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.issued_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.issued_amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Capital Amount -->
                        <div>
                            <label for="{{ form.capital_amount.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Capital Amount (₹) *
                            </label>
                            {{ form.capital_amount|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.capital_amount.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.capital_amount.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Share Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="pie-chart" class="w-5 h-5 mr-2 text-sap-purple-600"></i>
                        Share Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <!-- Shares Count -->
                        <div>
                            <label for="{{ form.shares_count.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Number of Shares
                            </label>
                            {{ form.shares_count|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.shares_count.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.shares_count.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Par Value -->
                        <div>
                            <label for="{{ form.par_value.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Par Value per Share (₹)
                            </label>
                            {{ form.par_value|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.par_value.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.par_value.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Share Class -->
                        <div>
                            <label for="{{ form.share_class.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Share Class
                            </label>
                            {{ form.share_class|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.share_class.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.share_class.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Voting Rights -->
                        <div>
                            <label for="{{ form.voting_rights.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Voting Rights
                            </label>
                            {{ form.voting_rights|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.voting_rights.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.voting_rights.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Shareholder Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="users" class="w-5 h-5 mr-2 text-sap-orange-600"></i>
                        Shareholder Information
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Shareholder Name -->
                        <div>
                            <label for="{{ form.shareholder_name.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Shareholder Name
                            </label>
                            {{ form.shareholder_name|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.shareholder_name.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.shareholder_name.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Shareholder Type -->
                        <div>
                            <label for="{{ form.shareholder_type.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Shareholder Type
                            </label>
                            {{ form.shareholder_type|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.shareholder_type.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.shareholder_type.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Shareholder Address -->
                        <div class="md:col-span-2">
                            <label for="{{ form.shareholder_address.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Shareholder Address
                            </label>
                            {{ form.shareholder_address|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.shareholder_address.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.shareholder_address.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Important Dates Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="calendar" class="w-5 h-5 mr-2 text-sap-indigo-600"></i>
                        Important Dates
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Issue Date -->
                        <div>
                            <label for="{{ form.issue_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Issue Date
                            </label>
                            {{ form.issue_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.issue_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.issue_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Effective Date -->
                        <div>
                            <label for="{{ form.effective_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Effective Date
                            </label>
                            {{ form.effective_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.effective_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.effective_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Allotment Date -->
                        <div>
                            <label for="{{ form.allotment_date.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Allotment Date
                            </label>
                            {{ form.allotment_date|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.allotment_date.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.allotment_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Additional Information Section -->
                <div class="mb-8">
                    <h4 class="text-lg font-medium text-sap-gray-800 mb-4 flex items-center">
                        <i data-lucide="file-text" class="w-5 h-5 mr-2 text-sap-gray-600"></i>
                        Additional Information
                    </h4>
                    <div class="grid grid-cols-1 gap-6">
                        <!-- Description -->
                        <div>
                            <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Description
                            </label>
                            {{ form.description|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.description.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.description.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Detailed description of the capital entry</p>
                        </div>
                        
                        <!-- Remarks -->
                        <div>
                            <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-sap-gray-700 mb-1">
                                Remarks
                            </label>
                            {{ form.remarks|add_class:"block w-full px-3 py-2 border border-sap-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sap-green-500 focus:border-sap-green-500" }}
                            {% if form.remarks.errors %}
                            <p class="text-red-600 text-xs mt-1">{{ form.remarks.errors.0 }}</p>
                            {% endif %}
                            <p class="text-xs text-sap-gray-500 mt-1">Additional notes or special conditions</p>
                        </div>
                    </div>
                </div>
                
                <!-- Capital Calculation Display -->
                <div class="mb-8 bg-sap-green-50 border border-sap-green-200 rounded-lg p-6" x-show="calculatedAmount > 0">
                    <h4 class="text-lg font-medium text-sap-green-800 mb-4 flex items-center">
                        <i data-lucide="calculator" class="w-5 h-5 mr-2 text-sap-green-600"></i>
                        Capital Calculation Summary
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="text-center">
                            <p class="text-sm text-sap-green-600">Share Value</p>
                            <p class="text-xl font-bold text-sap-green-800" x-text="'₹' + shareValue.toFixed(2)"></p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-green-600">Total Shares</p>
                            <p class="text-xl font-bold text-sap-green-800" x-text="totalShares.toLocaleString()"></p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-green-600">Total Capital</p>
                            <p class="text-xl font-bold text-sap-green-800" x-text="'₹' + calculatedAmount.toFixed(2)"></p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-sap-green-600">Utilization %</p>
                            <p class="text-xl font-bold text-sap-green-800" x-text="utilizationPercentage + '%'"></p>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-sap-gray-200">
                    <div class="flex items-center space-x-3">
                        <button type="button" onclick="resetForm()" 
                                class="bg-sap-gray-300 hover:bg-sap-gray-400 text-sap-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="rotate-ccw" class="w-4 h-4 inline mr-2"></i>
                            Reset
                        </button>
                        <button type="button" @click="calculateCapital()" 
                                class="bg-sap-purple-600 hover:bg-sap-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="calculator" class="w-4 h-4 inline mr-2"></i>
                            Calculate
                        </button>
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="{% url 'accounts:capital_list' %}" 
                           class="bg-sap-gray-600 hover:bg-sap-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-sap-green-600 hover:bg-sap-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                            {% if object %}Update Capital{% else %}Create Capital{% endif %}
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Capital Guidelines -->
        <div class="mt-6 bg-sap-blue-50 border border-sap-blue-200 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i data-lucide="info" class="w-5 h-5 text-sap-blue-600"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-sap-blue-800">Capital Registration Guidelines</h3>
                    <div class="mt-2 text-sm text-sap-blue-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Ensure all mandatory fields are completed accurately</li>
                            <li>Capital code will be auto-generated if not provided</li>
                            <li>Authorized amount should be greater than or equal to issued amount</li>
                            <li>Capital amount should match shares count × par value</li>
                            <li>Keep shareholder information up to date for compliance</li>
                            <li>Set appropriate dates for issue and allotment</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function capitalForm() {
    return {
        calculatedAmount: 0,
        shareValue: 0,
        totalShares: 0,
        utilizationPercentage: 0,
        
        calculateCapital() {
            const sharesCount = parseFloat(document.getElementById('{{ form.shares_count.id_for_label }}').value) || 0;
            const parValue = parseFloat(document.getElementById('{{ form.par_value.id_for_label }}').value) || 0;
            const authorizedAmount = parseFloat(document.getElementById('{{ form.authorized_amount.id_for_label }}').value) || 0;
            const issuedAmount = parseFloat(document.getElementById('{{ form.issued_amount.id_for_label }}').value) || 0;
            
            if (sharesCount && parValue) {
                this.calculatedAmount = sharesCount * parValue;
                this.shareValue = parValue;
                this.totalShares = sharesCount;
                
                // Update capital amount field
                const capitalAmountField = document.getElementById('{{ form.capital_amount.id_for_label }}');
                if (capitalAmountField) {
                    capitalAmountField.value = this.calculatedAmount.toFixed(2);
                }
            }
            
            if (authorizedAmount > 0 && issuedAmount > 0) {
                this.utilizationPercentage = ((issuedAmount / authorizedAmount) * 100).toFixed(1);
            }
            
            if (!sharesCount || !parValue) {
                alert('Please enter number of shares and par value to calculate capital amount.');
                return;
            }
        },
        
        validateAmounts() {
            const authorizedAmount = parseFloat(document.getElementById('{{ form.authorized_amount.id_for_label }}').value) || 0;
            const issuedAmount = parseFloat(document.getElementById('{{ form.issued_amount.id_for_label }}').value) || 0;
            const capitalAmount = parseFloat(document.getElementById('{{ form.capital_amount.id_for_label }}').value) || 0;
            
            if (issuedAmount > authorizedAmount) {
                alert('Issued amount cannot be greater than authorized amount.');
                return false;
            }
            
            if (capitalAmount > issuedAmount) {
                alert('Capital amount should not exceed issued amount.');
                return false;
            }
            
            return true;
        }
    }
}

function resetForm() {
    if (confirm('Are you sure you want to reset the form? All unsaved changes will be lost.')) {
        document.getElementById('capital-form').reset();
    }
}

// Auto-generate capital code if creating new capital entry
document.addEventListener('DOMContentLoaded', function() {
    const capitalCodeInput = document.getElementById('{{ form.capital_code.id_for_label }}');
    if (capitalCodeInput && !capitalCodeInput.value) {
        // Generate capital code based on current timestamp
        const today = new Date();
        const year = today.getFullYear().toString().substr(-2);
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        const sequence = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        
        capitalCodeInput.value = `CAP-${year}${month}${day}${sequence}`;
    }
    
    // Auto-calculate capital amount when shares count or par value changes
    const sharesField = document.getElementById('{{ form.shares_count.id_for_label }}');
    const parValueField = document.getElementById('{{ form.par_value.id_for_label }}');
    const capitalAmountField = document.getElementById('{{ form.capital_amount.id_for_label }}');
    
    [sharesField, parValueField].forEach(field => {
        if (field) {
            field.addEventListener('change', function() {
                const shares = parseFloat(sharesField.value) || 0;
                const parValue = parseFloat(parValueField.value) || 0;
                
                if (shares && parValue && capitalAmountField) {
                    capitalAmountField.value = (shares * parValue).toFixed(2);
                }
            });
        }
    });
    
    // Auto-set effective date when issue date is set
    const issueDateField = document.getElementById('{{ form.issue_date.id_for_label }}');
    const effectiveDateField = document.getElementById('{{ form.effective_date.id_for_label }}');
    
    if (issueDateField && effectiveDateField) {
        issueDateField.addEventListener('change', function() {
            if (!effectiveDateField.value) {
                effectiveDateField.value = this.value;
            }
        });
    }
    
    lucide.createIcons();
});
</script>
{% endblock %>