﻿using System;
using System.Collections;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Xml.Linq;
using System.Data.SqlClient;

public partial class Admin_Access_AccessModule : System.Web.UI.Page
{
    clsFunctions fun = new clsFunctions();

    int CompId = 0;
    string co = "";
    string id = "";
    int FyId = 0;
    int A =0;
    int compid = 0;
    int fyid = 0;

    protected void Page_Load(object sender, EventArgs e)
    {       string connStr = fun.Connection();
            SqlConnection con = new SqlConnection(connStr);
        try
        {
            con.Open();

            FyId = Convert.ToInt32(Session["finyear"]);
            CompId = Convert.ToInt32(Session["compid"]);

            if (!String.IsNullOrEmpty(Request.QueryString["A"]))
            {
                A = Convert.ToInt32(Request.QueryString["A"]);
            }

            if (!String.IsNullOrEmpty(Request.QueryString["CompId"]))
            {
                compid = Convert.ToInt32(Request.QueryString["CompId"]);
            }

            if (!String.IsNullOrEmpty(Request.QueryString["FYId"]))
            {
                fyid = Convert.ToInt32(Request.QueryString["FYId"]);
            }
            if (!IsPostBack)
            {
                fun.dropdownCompany(DrpCompany);
                DrpCompany.SelectedIndex = 1;
                this.fillFinDrp(FyId);
                DrpFinYear.SelectedIndex = 1;

                if (A != 0)
                {
                    DrpCompany.SelectedValue = compid.ToString();
                    DrpFinYear.SelectedValue = fyid.ToString();
                    this.fillFinDrp(fyid);
                    BtnView.Visible = true;
                    DrpField.Visible = true;
                    TxtMrs.Visible = true;
                    TxtEmpName.Visible = true;
                    Button1.Visible = true;
                    TxtMrs.Visible = false;

                }
                this.binddata(co, id);
            }
        }
        catch (Exception ex) { }
         finally
        {
            con.Close();
        }
    }
    public void fillFinDrp(int FinId)
    {
        try
        {
            if (DrpCompany.SelectedIndex != 0)
            {
                DataSet DS = new DataSet();
                string connStr = fun.Connection();
                SqlConnection con = new SqlConnection(connStr);
                string cmdStr = fun.select("FinYear,FinYearId", "tblFinancial_master", "FinYearId='" + FinId + "'");
                SqlCommand cmd = new SqlCommand(cmdStr, con);
                SqlDataAdapter DA = new SqlDataAdapter(cmd);
                DA.Fill(DS, "tblFinancial_master");
                DrpFinYear.DataSource = DS.Tables["tblFinancial_master"];
                DrpFinYear.DataTextField = "FinYear";
                DrpFinYear.DataValueField = "FinYearId";
                DrpFinYear.DataBind();
                
            }
            else
            {
                DrpFinYear.Items.Clear();
            }
            
            DrpFinYear.Items.Insert(0, "Select");
            
        }
        catch (Exception ex) { }
    }
    protected void DrpCompany_SelectedIndexChanged(object sender, EventArgs e)
    {
        try
        {
            this.fillFinDrp(FyId);
            DrpFinYear.SelectedIndex = 1;
        }
        catch (Exception ex){}
        
    }
    protected void DrpFinYear_SelectedIndexChanged(object sender, EventArgs e)
    {
        string connStr = fun.Connection();
        SqlConnection con = new SqlConnection(connStr);
        DataSet DS = new DataSet();
        try
        {

            if (DrpFinYear.SelectedValue.ToString() != "Select")
            {
                if (IsPostBack)
                {

                    //DrpField.Visible = true;
                    //TxtMrs.Visible = true;
                    //TxtEmpName.Visible = true;
                    //Button1.Visible = true;
                    //TxtMrs.Visible = false;
                    //BtnView.Visible = true;
                    this.binddata(co, id);

                }

                if (!string.IsNullOrEmpty(Request.QueryString["msg"]))
                {
                    Label2.Text = Request.QueryString["msg"].ToString();
                }

            }

        }

        catch (Exception ex) { } 
       
    }   
    public void binddata(string Search, string EmpId)
    {
        string connStr = fun.Connection();
        SqlConnection con = new SqlConnection(connStr);

        try
        {
            con.Open();
            string x = "";
            string y = "";
            if (DrpField.SelectedValue == "0")
            {
                if (TxtEmpName.Text != "")
                {
                    y = " AND EmpId='" + fun.getCode(TxtEmpName.Text) + "'";
                }
            }

            if (DrpField.SelectedValue == "Select")
            {
                TxtMrs.Visible = true;
                TxtEmpName.Visible = false;
            }

            if (DrpField.SelectedValue == "1")
            {
                if (TxtMrs.Text != "")
                {
                    string sqlDept = fun.select("Id", "tblHR_Departments", "Symbol like '%" + TxtMrs.Text + "%'");
                    SqlCommand cmdDept = new SqlCommand(sqlDept, con);
                    //SqlDataAdapter daDept = new SqlDataAdapter(cmdDept);
                    //DataSet DSDept = new DataSet();
                    //daDept.Fill(DSDept);
                    SqlDataReader DSDept = cmdDept.ExecuteReader();
                    DSDept.Read();
                    x = " AND Department='" + DSDept["Id"].ToString() + "'";
                }
            }
            else if (DrpField.SelectedValue == "2")
            {
                if (TxtMrs.Text != "")
                {
                    string sqlGrp = fun.select("Id", "BusinessGroup", "Symbol like '%" + TxtMrs.Text + "%'");
                    SqlCommand cmdGrp = new SqlCommand(sqlGrp, con);
                    SqlDataReader DSGrp = cmdGrp.ExecuteReader();
                    DSGrp.Read();
                    //SqlDataAdapter daGrp = new SqlDataAdapter(cmdGrp);
                    //DataSet DSGrp = new DataSet();
                    //daGrp.Fill(DSGrp);
                    while (DSGrp.Read())
                    {
                        x = " AND BGGroup='" + DSGrp["Id"].ToString() + "'";
                    }
                }
            }

            DataTable dt = new DataTable();  //And UserID !='1'
            string sql = fun.select("UserID,BGGroup,Designation,EmpId,Title + '. ' + EmployeeName As EmployeeName,FinYearId,JoiningDate,Department,MobileNo", "tblHR_OfficeStaff", "CompId='" + CompId + "'And ResignationDate='' And FinYearId<='" + FyId + "'" + y + x + "   Order by UserID Desc");

            SqlCommand cmd = new SqlCommand(sql, con);
            SqlDataReader ds = cmd.ExecuteReader();

            //SqlDataAdapter da = new SqlDataAdapter(cmd);
            //DataSet ds = new DataSet();
            //da.Fill(ds);

            dt.Columns.Add(new System.Data.DataColumn("EmpId", typeof(string)));
            dt.Columns.Add(new System.Data.DataColumn("FinYear", typeof(string)));
            dt.Columns.Add(new System.Data.DataColumn("UserId", typeof(string)));
            dt.Columns.Add(new System.Data.DataColumn("EmployeeName", typeof(string)));
            dt.Columns.Add(new System.Data.DataColumn("DeptName", typeof(string)));
            dt.Columns.Add(new System.Data.DataColumn("BGgroup", typeof(string)));
            dt.Columns.Add(new System.Data.DataColumn("Designation", typeof(string)));
            dt.Columns.Add(new System.Data.DataColumn("MobileNo", typeof(string)));
            dt.Columns.Add(new System.Data.DataColumn("JoiningDate", typeof(string)));

            DataRow dr;

            while (ds.Read())
            {
                dr = dt.NewRow();

                //if (ds.Tables[0].Rows.Count > 0)
                {

                    string SysDt = fun.FromDateDMY(ds["JoiningDate"].ToString());

                    string sqlFin = fun.select("FinYear", "tblFinancial_master", "FinYearId='" + ds["FinyearId"] + "'");
                    SqlCommand cmdFinYr = new SqlCommand(sqlFin, con);
                    SqlDataReader DSFin = cmdFinYr.ExecuteReader();
                    
                    //SqlDataAdapter daFin = new SqlDataAdapter(cmdFinYr);
                    //DataSet DSFin = new DataSet();
                    //daFin.Fill(DSFin);

                    while (DSFin.Read())
                    {
                        dr[1] = DSFin["FinYear"].ToString();
                    }

                    string sqlDept = fun.select("Description AS DeptName", "tblHR_Departments", "Id='" + ds["Department"] + "'");
                    SqlCommand cmdDept = new SqlCommand(sqlDept, con);
                    SqlDataReader DSDept = cmdDept.ExecuteReader();
                  
                    //SqlDataAdapter daDept = new SqlDataAdapter(cmdDept);
                    //DataSet DSDept = new DataSet();
                    //daDept.Fill(DSDept);

                    while (DSDept.Read())
                    {
                        dr[4] = DSDept["DeptName"].ToString();
                    }

                    string sqlGrp = fun.select("Symbol AS BGgroup", "BusinessGroup", "Id='" + ds["BGGroup"] + "'");
                    SqlCommand cmdGrp = new SqlCommand(sqlGrp, con);
                    SqlDataReader DSGrp = cmdGrp.ExecuteReader();
                   
                    //SqlDataAdapter daGrp = new SqlDataAdapter(cmdGrp);
                    //DataSet DSGrp = new DataSet();
                    //daGrp.Fill(DSGrp);

                    while (DSGrp.Read())
                    {
                        dr[5] = DSGrp["BGgroup"].ToString();
                    }

                    string sqlDesig = fun.select("Symbol + '-' + Type AS Designation", "tblHR_Designation", "Id='" + ds["Designation"] + "'");
                    SqlCommand cmdDesig = new SqlCommand(sqlDesig, con);
                    SqlDataReader DSDesig = cmdDesig.ExecuteReader();
                   
                    //SqlDataAdapter daDesig = new SqlDataAdapter(cmdDesig);
                    //DataSet DSDesig = new DataSet();
                    //daDesig.Fill(DSDesig);

                    while (DSDesig.Read())
                    {
                        dr[6] = DSDesig["Designation"].ToString();
                    }
                   
                    string jdate = fun.FromDateDMY(ds["JoiningDate"].ToString());
                    dr[0] = ds["EmpId"].ToString();
                    dr[2] = ds["UserId"].ToString();
                    dr[3] = ds["EmployeeName"].ToString();

 string sqlMob = fun.select("MobileNo", "tblHR_CoporateMobileNo", "Id='" + ds["MobileNo"] + "'");
                    SqlCommand cmdMob = new SqlCommand(sqlMob, con);
                    SqlDataReader DSMob = cmdMob.ExecuteReader();
                    
                    //SqlDataAdapter daMob = new SqlDataAdapter(cmdMob);
                    //DataSet DSMob = new DataSet();
                    //daMob.Fill(DSMob);

                    while (DSMob.Read())
                    {
                        dr[7] = DSMob["MobileNo"].ToString();
                    }
                    dr[8] = jdate;
                    dt.Rows.Add(dr);
                    dt.AcceptChanges();
                }

            }
            GridView2.DataSource = dt;
            GridView2.DataBind();
        }
        catch (Exception ex)  {  }
        finally
        {
            con.Close();
        }
    }
    protected void Button1_Click(object sender, EventArgs e)
    {
        try
        {
            string CustId = fun.getCode(TxtEmpName.Text);
            this.binddata(TxtMrs.Text, CustId);
        }
        catch (Exception ex) { }
       
    }
    protected void DrpField_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (DrpField.SelectedValue == "0")
        {
            TxtMrs.Visible = false;
            TxtEmpName.Visible = true;
            TxtEmpName.Text = "";
            this.binddata(co, id);
        }
        else
        {
            TxtMrs.Visible = true;
            TxtMrs.Text = "";
            TxtEmpName.Visible = false;
            this.binddata(co, id);
        }
    }
    [System.Web.Services.WebMethodAttribute(), System.Web.Script.Services.ScriptMethodAttribute()]
    public static string[] GetCompletionList(string prefixText, int count, string contextKey)
    {
        clsFunctions fun = new clsFunctions();
        string connStr = fun.Connection();
        DataSet ds = new DataSet();
        SqlConnection con = new SqlConnection(connStr);
        con.Open();
        int CompId = Convert.ToInt32(HttpContext.Current.Session["compid"]);
        string cmdStr = fun.select("EmpId,EmployeeName", "tblHR_OfficeStaff", "CompId='" + CompId + "' And ResignationDate=''");
        SqlDataAdapter da = new SqlDataAdapter(cmdStr, con);
        da.Fill(ds, "tblHR_OfficeStaff");
        con.Close();
        string[] main = new string[0];
        for (int i = 0; i < ds.Tables[0].Rows.Count; i++)
        {
            if (ds.Tables[0].Rows[i].ItemArray[1].ToString().ToLower().StartsWith(prefixText.ToLower()))
            {
                Array.Resize(ref main, main.Length + 1);
                main[main.Length - 1] = ds.Tables[0].Rows[i].ItemArray[1].ToString() + " [" + ds.Tables[0].Rows[i].ItemArray[0].ToString() + "]";
                if (main.Length == 10)
                    break;
            }
        }
        Array.Sort(main);
        return main;
    }
    protected void GridView2_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        try
        {

                string EmpId = "";
                string modid = "";
                string CompId ="";
                string FYId = "";

            if (e.CommandName == "Select")
            {

                GridViewRow row = (GridViewRow)(((LinkButton)e.CommandSource).NamingContainer);
                 EmpId = ((Label)row.FindControl("lblEmpId")).Text;
                 modid = ((DropDownList)row.FindControl("DrpModule")).SelectedValue;
                 CompId = DrpCompany.SelectedValue;
                 FYId = DrpFinYear.SelectedValue;

                 if (CompId != "Select")
                 {
                     if (FYId != "Select")
                     {
                         if (modid != "0")
                         {
                             Response.Redirect("AccessModule_Details.aspx?EmpId=" + EmpId + "&modid=" + modid + "&CompId=" + CompId + "&FYId=" + FYId + "");

                         }
                         else
                         {
                             string mystring = string.Empty;
                             mystring = "Please select module name.";
                             ClientScript.RegisterStartupScript(this.GetType(), "myalert", "alert('" + mystring + "');", true);
                         }
                     }
                     else
                     {
                         string mystring = string.Empty;
                         mystring = "Please select Financial Year.";
                         ClientScript.RegisterStartupScript(this.GetType(), "myalert", "alert('" + mystring + "');", true);
                     }
                 }
                 else
                 {
                     string mystring = string.Empty;
                     mystring = "Please select Company name.";
                     ClientScript.RegisterStartupScript(this.GetType(), "myalert", "alert('" + mystring + "');", true);
                 }
                
            }

        } 
        catch (Exception es){}      
        
    }
    protected void BtnView_Click(object sender, EventArgs e)
    {
        string compid = DrpCompany.SelectedValue;
        string finyear = DrpFinYear.SelectedValue;
        if (compid != "Select" )
        {
            if (finyear != "Select")
            {
                string getRandomKey = fun.GetRandomAlphaNumeric();

                Response.Redirect("AcessReport.aspx?cid=" + compid + "&fyid=" + finyear + "&Key=" + getRandomKey + "");
            }
            else
            {
                string mystring = string.Empty;
                mystring = "Please select Financial Year.";
                ClientScript.RegisterStartupScript(this.GetType(), "myalert", "alert('" + mystring + "');", true);
            }
        }
        else
        {
            string mystring = string.Empty;
            mystring = "Please select Company name.";
            ClientScript.RegisterStartupScript(this.GetType(), "myalert", "alert('" + mystring + "');", true);
        }
    }
    protected void GridView2_PageIndexChanged(object sender, EventArgs e)
    {

    }
    protected void GridView2_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        try
        {
            GridView2.PageIndex = e.NewPageIndex;
            this.binddata(co, id);
        }
        catch (Exception ex) { }
    }


}