"""
Test Work Order Comprehensive Implementation
Tests the complete work order creation matching ASP.NET functionality
"""
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from unittest.mock import patch, MagicMock

from sys_admin.models import Company, FinancialYear, Country, State, City


class WorkOrderComprehensiveTestCase(TestCase):
    """Test comprehensive work order creation functionality"""
    
    def setUp(self):
        """Set up test data"""
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        # Create test company
        self.company = Company.objects.create(
            companyname='Test Company',
            compid=1
        )
        
        # Create test financial year
        self.financial_year = FinancialYear.objects.create(
            finyearid=13,
            finyear='2023-24'
        )
        
        # Create test country, state, city
        self.country = Country.objects.create(
            countryid=1,
            countryname='India'
        )
        
        self.state = State.objects.create(
            stateid=1,
            statename='Maharashtra',
            countryid=self.country
        )
        
        self.city = City.objects.create(
            cityid=1,
            cityname='Mumbai',
            stateid=self.state
        )
        
        # Setup client and login
        self.client = Client()
        self.client.login(username='testuser', password='testpass123')
        
        # Set session variables
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 13
        session['username'] = 'testuser'
        session.save()

    def test_work_order_po_selection_view(self):
        """Test PO selection view loads correctly"""
        url = reverse('sales_distribution:work_order_po_selection')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Work Order - New')
        self.assertContains(response, 'Search Purchase Orders')

    def test_work_order_po_selection_with_search(self):
        """Test PO selection with search parameters"""
        url = reverse('sales_distribution:work_order_po_selection')
        response = self.client.get(url, {
            'search_by': '0',
            'search_value': 'Test Customer'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Customer')

    def test_work_order_create_view_access(self):
        """Test work order creation view access with parameters"""
        url = reverse('sales_distribution:work_order_create', kwargs={
            'po_no': 'PO001',
            'customer_id': 'CUST001',
            'enq_id': 1,
            'po_id': 1
        })
        
        # This might fail because the PO doesn't exist, but it should at least try to load
        response = self.client.get(url)
        # Should either load the form or redirect with error message
        self.assertIn(response.status_code, [200, 302])

    def test_work_order_customer_autocomplete(self):
        """Test customer autocomplete AJAX endpoint"""
        url = reverse('sales_distribution:work_order_customer_autocomplete')
        response = self.client.get(url, {'q': 'test'})
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('customers', response.json())

    def test_work_order_subcategory_ajax(self):
        """Test subcategory AJAX endpoint"""
        url = reverse('sales_distribution:work_order_subcategory_ajax')
        response = self.client.get(url, {'category_id': '1'})
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('subcategories', response.json())

    def test_work_order_forms_import(self):
        """Test that work order forms can be imported"""
        try:
            from sales_distribution.forms.work_order_forms import (
                WorkOrderCreateForm, WorkOrderShippingForm, 
                WorkOrderProductForm, WorkOrderInstructionsForm,
                PurchaseOrderSelectionForm
            )
            # If we can import, the forms are properly structured
            self.assertTrue(True)
        except ImportError as e:
            self.fail(f"Could not import work order forms: {e}")

    def test_work_order_views_import(self):
        """Test that work order views can be imported"""
        try:
            from sales_distribution.views.work_order_views import (
                WorkOrderPOSelectionView, WorkOrderCreateMultiStepView,
                WorkOrderCustomerAutocompleteView, WorkOrderSubcategoryAjaxView,
                WorkOrderProductDeleteView
            )
            # If we can import, the views are properly structured
            self.assertTrue(True)
        except ImportError as e:
            self.fail(f"Could not import work order views: {e}")

    @patch('sales_distribution.views.work_order_views.connection')
    def test_work_order_number_generation(self, mock_connection):
        """Test work order number generation logic"""
        # Mock database responses
        mock_cursor = MagicMock()
        mock_connection.cursor.return_value.__enter__.return_value = mock_cursor
        
        # Mock category symbol query
        mock_cursor.fetchone.side_effect = [
            ['WO'],  # Symbol query
            ['0'],   # HasSubCat query  
            ['WO0001']  # Last WO number query
        ]
        
        from sales_distribution.views.work_order_views import WorkOrderCreateMultiStepView
        view = WorkOrderCreateMultiStepView()
        
        # Create mock category
        mock_category = MagicMock()
        mock_category.cid = 1
        
        result = view.generate_work_order_number(mock_category, None)
        self.assertEqual(result, 'WO0002')

    def test_work_order_templates_exist(self):
        """Test that work order templates exist"""
        import os
        from django.conf import settings
        
        template_dir = os.path.join(
            settings.BASE_DIR, 
            'sales_distribution/templates/sales_distribution'
        )
        
        po_selection_template = os.path.join(template_dir, 'work_order_po_selection.html')
        create_template = os.path.join(template_dir, 'work_order_create.html')
        
        self.assertTrue(os.path.exists(po_selection_template), 
                       "PO selection template should exist")
        self.assertTrue(os.path.exists(create_template),
                       "Work order create template should exist")

    def test_url_patterns_work(self):
        """Test that all new URL patterns resolve correctly"""
        # Test PO selection URL
        url = reverse('sales_distribution:work_order_po_selection')
        self.assertTrue(url.endswith('/work-orders/po-selection/'))
        
        # Test work order creation URL
        url = reverse('sales_distribution:work_order_create', kwargs={
            'po_no': 'PO001',
            'customer_id': 'CUST001', 
            'enq_id': 1,
            'po_id': 1
        })
        self.assertTrue('/work-orders/create/' in url)
        
        # Test AJAX URLs
        autocomplete_url = reverse('sales_distribution:work_order_customer_autocomplete')
        self.assertTrue(autocomplete_url.endswith('/ajax/work-order-customer-autocomplete/'))
        
        subcategory_url = reverse('sales_distribution:work_order_subcategory_ajax')
        self.assertTrue(subcategory_url.endswith('/ajax/work-order-subcategory/'))

    def test_form_validation_logic(self):
        """Test form validation matches ASP.NET requirements"""
        from sales_distribution.forms.work_order_forms import WorkOrderCreateForm
        from datetime import date, timedelta
        
        # Test invalid date range
        form_data = {
            'work_order_date': date.today(),
            'project_title': 'Test Project',
            'project_leader': 'Test Leader',
            'target_dap_from_date': date.today() + timedelta(days=5),
            'target_dap_to_date': date.today(),  # Invalid: to_date before from_date
        }
        
        form = WorkOrderCreateForm(data=form_data, company_id=1)
        self.assertFalse(form.is_valid())
        self.assertIn('Target DAP', str(form.errors))

    def tearDown(self):
        """Clean up test data"""
        # Clean up any test data
        pass