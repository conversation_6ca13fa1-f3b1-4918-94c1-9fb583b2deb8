﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="QA_POWise" targetNamespace="http://tempuri.org/QA_POWise.xsd" xmlns:mstns="http://tempuri.org/QA_POWise.xsd" xmlns="http://tempuri.org/QA_POWise.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="QA_POWise" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="QA_POWise" msprop:Generator_DataSetName="QA_POWise">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_UserTableName="DataTable1" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_TablePropName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" msprop:Generator_UserColumnName="Id" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnPropNameInTable="IdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="PONo" msprop:Generator_UserColumnName="PONo" msprop:Generator_ColumnVarNameInTable="columnPONo" msprop:Generator_ColumnPropNameInRow="PONo" msprop:Generator_ColumnPropNameInTable="PONoColumn" type="xs:string" minOccurs="0" />
              <xs:element name="Supplier" msprop:Generator_UserColumnName="Supplier" msprop:Generator_ColumnVarNameInTable="columnSupplier" msprop:Generator_ColumnPropNameInRow="Supplier" msprop:Generator_ColumnPropNameInTable="SupplierColumn" type="xs:string" minOccurs="0" />
              <xs:element name="CompId" msprop:Generator_UserColumnName="CompId" msprop:Generator_ColumnPropNameInRow="CompId" msprop:Generator_ColumnVarNameInTable="columnCompId" msprop:Generator_ColumnPropNameInTable="CompIdColumn" type="xs:int" minOccurs="0" />
              <xs:element name="SrNo" msprop:Generator_UserColumnName="SrNo" msprop:Generator_ColumnVarNameInTable="columnSrNo" msprop:Generator_ColumnPropNameInRow="SrNo" msprop:Generator_ColumnPropNameInTable="SrNoColumn" type="xs:long" minOccurs="0" />
              <xs:element name="DataTable2" msprop:Generator_UserTableName="DataTable2" msprop:Generator_RowDeletedName="DataTable2RowDeleted" msprop:Generator_RowChangedName="DataTable2RowChanged" msprop:Generator_RowClassName="DataTable2Row" msprop:Generator_RowChangingName="DataTable2RowChanging" msprop:Generator_RowEvArgName="DataTable2RowChangeEvent" msprop:Generator_RowEvHandlerName="DataTable2RowChangeEventHandler" msprop:Generator_TableClassName="DataTable2DataTable" msprop:Generator_TableVarName="tableDataTable2" msprop:Generator_RowDeletingName="DataTable2RowDeleting" msprop:Generator_TablePropName="DataTable2" minOccurs="0" maxOccurs="unbounded">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="MId" msprop:Generator_UserColumnName="MId" msprop:Generator_ColumnVarNameInTable="columnMId" msprop:Generator_ColumnPropNameInRow="MId" msprop:Generator_ColumnPropNameInTable="MIdColumn" type="xs:int" minOccurs="0" />
                    <xs:element name="ItemCode" msprop:Generator_UserColumnName="ItemCode" msprop:Generator_ColumnVarNameInTable="columnItemCode" msprop:Generator_ColumnPropNameInRow="ItemCode" msprop:Generator_ColumnPropNameInTable="ItemCodeColumn" type="xs:string" minOccurs="0" />
                    <xs:element name="Description" msprop:Generator_UserColumnName="Description" msprop:Generator_ColumnVarNameInTable="columnDescription" msprop:Generator_ColumnPropNameInRow="Description" msprop:Generator_ColumnPropNameInTable="DescriptionColumn" type="xs:string" minOccurs="0" />
                    <xs:element name="UOM" msprop:Generator_UserColumnName="UOM" msprop:Generator_ColumnVarNameInTable="columnUOM" msprop:Generator_ColumnPropNameInRow="UOM" msprop:Generator_ColumnPropNameInTable="UOMColumn" type="xs:string" minOccurs="0" />
                    <xs:element name="POQty" msprop:Generator_UserColumnName="POQty" msprop:Generator_ColumnVarNameInTable="columnPOQty" msprop:Generator_ColumnPropNameInRow="POQty" msprop:Generator_ColumnPropNameInTable="POQtyColumn" type="xs:double" default="0" minOccurs="0" />
                    <xs:element name="AccQty" msprop:Generator_UserColumnName="AccQty" msprop:Generator_ColumnVarNameInTable="columnAccQty" msprop:Generator_ColumnPropNameInRow="AccQty" msprop:Generator_ColumnPropNameInTable="AccQtyColumn" type="xs:double" default="0" minOccurs="0" />
                    <xs:element name="SrNo" msprop:Generator_UserColumnName="SrNo" msprop:Generator_ColumnVarNameInTable="columnSrNo" msprop:Generator_ColumnPropNameInRow="SrNo" msprop:Generator_ColumnPropNameInTable="SrNoColumn" type="xs:long" minOccurs="0" />
                    <xs:element name="AH" msprop:Generator_UserColumnName="AH" msprop:Generator_ColumnVarNameInTable="columnAH" msprop:Generator_ColumnPropNameInRow="AH" msprop:Generator_ColumnPropNameInTable="AHColumn" type="xs:string" minOccurs="0" />
                    <xs:element name="AccNo" msprop:Generator_UserColumnName="AccNo" msprop:Generator_ColumnVarNameInTable="columnAccNo" msprop:Generator_ColumnPropNameInRow="AccNo" msprop:Generator_ColumnPropNameInTable="AccNoColumn" type="xs:string" minOccurs="0" />
                    <xs:element name="PvevNo" msprop:Generator_UserColumnName="PvevNo" msprop:Generator_ColumnPropNameInRow="PvevNo" msprop:Generator_ColumnVarNameInTable="columnPvevNo" msprop:Generator_ColumnPropNameInTable="PvevNoColumn" type="xs:string" minOccurs="0" />
                    <xs:element name="PvevQty" msprop:Generator_UserColumnName="PvevQty" msprop:Generator_ColumnPropNameInRow="PvevQty" msprop:Generator_ColumnVarNameInTable="columnPvevQty" msprop:Generator_ColumnPropNameInTable="PvevQtyColumn" type="xs:double" minOccurs="0" />
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1">
      <xs:selector xpath=".//mstns:DataTable1" />
      <xs:field xpath="mstns:Id" />
    </xs:unique>
    <xs:keyref name="FK_DataTable1_DataTable2" refer="Constraint1" msdata:IsNested="true" msprop:rel_Generator_UserRelationName="FK_DataTable1_DataTable2" msprop:rel_Generator_RelationVarName="relationFK_DataTable1_DataTable2" msprop:rel_Generator_UserChildTable="DataTable2" msprop:rel_Generator_UserParentTable="DataTable1" msprop:rel_Generator_ParentPropName="DataTable1Row" msprop:rel_Generator_ChildPropName="GetDataTable2Rows">
      <xs:selector xpath=".//mstns:DataTable2" />
      <xs:field xpath="mstns:MId" />
    </xs:keyref>
  </xs:element>
</xs:schema>