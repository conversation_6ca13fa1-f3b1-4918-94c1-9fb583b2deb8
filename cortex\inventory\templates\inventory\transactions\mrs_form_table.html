{% extends "core/base.html" %}
{% load static %}

{% block title %}
Material Requisition Slip (MRS) - New - Inventory
{% endblock %}

{% block extra_css %}
<style>
/* SAP Fiori inspired components using Tailwind */
.sap-card {
    @apply bg-white rounded-xl shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300;
}

.sap-button-primary {
    @apply inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200;
}

.sap-button-secondary {
    @apply inline-flex items-center px-6 py-3 bg-white text-blue-600 font-medium rounded-lg border border-blue-600 hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200;
}

.sap-button-danger {
    @apply inline-flex items-center px-4 py-2 bg-red-600 text-white font-medium rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-all duration-200;
}

.sap-input {
    @apply block w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200;
}

.sap-tab-button {
    @apply px-6 py-4 font-medium text-sm border-b-3 border-transparent text-gray-600 hover:text-blue-600 hover:border-blue-200 transition-all duration-200;
}

.sap-tab-button.active {
    @apply border-blue-600 text-blue-600 bg-blue-50;
}

.sap-table-container {
    @apply overflow-hidden shadow-lg ring-1 ring-black ring-opacity-5 rounded-xl;
}

.sap-table {
    @apply min-w-full divide-y divide-gray-200;
}

.sap-table-header {
    @apply bg-gray-50;
}

.sap-table-header th {
    @apply px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider;
}

.sap-table-row {
    @apply bg-white hover:bg-blue-50 transition-colors duration-150;
}

.sap-table-row:nth-child(even) {
    @apply bg-gray-50;
}

.sap-table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.sap-filter-container {
    @apply flex items-center space-x-4 mb-6 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100;
}

.sap-status-success {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800;
}

.sap-status-warning {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800;
}

.sap-status-error {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800;
}

.sap-pagination {
    @apply flex items-center justify-between border-t border-gray-200 bg-white px-6 py-4 rounded-b-xl;
}

.qty-input {
    @apply w-20 px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.sap-add-btn {
    @apply px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200;
}

.sap-form-section {
    @apply bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-8 border border-blue-100;
}

.sap-form-grid {
    @apply grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3;
}

.sap-form-label {
    @apply block text-sm font-semibold text-gray-700 mb-2;
}

.sap-form-icon {
    @apply mr-2 text-blue-600;
}

.sap-submit-section {
    @apply flex justify-between items-center pt-6 border-t border-blue-200;
}

.sap-form-status {
    @apply flex items-center text-sm text-gray-600;
}

.sap-button-group {
    @apply flex space-x-4;
}
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-4">
                <li>
                    <div>
                        <a href="{% url 'inventory:mrs_list' %}" class="text-gray-400 hover:text-gray-500">
                            <svg class="flex-shrink-0 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                            </svg>
                            <span class="sr-only">Home</span>
                        </a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                        </svg>
                        <a href="{% url 'inventory:mrs_list' %}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">MRS</a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                        </svg>
                        <span class="ml-4 text-sm font-medium text-gray-500">New MRS</span>
                    </div>
                </li>
            </ol>
        </nav>
        
        <div class="mt-2">
            <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                Material Requisition Slip (MRS) - New
            </h1>
        </div>
    </div>

    <!-- Main Content -->
    <div class="sap-card">
        <!-- Tab Navigation -->
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-2 px-6">
                <button id="item-master-tab" class="sap-tab-button active" onclick="switchTab('item-master')">
                    <i class="fas fa-boxes mr-2"></i>
                    Item Master
                </button>
                <button id="selected-items-tab" class="sap-tab-button" onclick="switchTab('selected-items')">
                    <i class="fas fa-shopping-cart mr-2"></i>
                    Selected Items (<span id="selected-count" class="sap-status-info">0</span>)
                </button>
            </nav>
        </div>

        <!-- Item Master Tab -->
        <div id="item-master-content" class="p-8">
            <!-- Search Filters -->
            <div class="sap-filter-container">
                <!-- Search Type Selection -->
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 mb-4 border border-blue-200">
                    <h4 class="text-lg font-semibold text-blue-800 mb-3">
                        <i class="fas fa-search mr-2"></i>
                        Search Type Selection
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-white rounded-lg p-4 border border-gray-200">
                            <label class="flex items-center cursor-pointer">
                                <input type="radio" name="search_type" value="category" class="mr-3 text-blue-600 focus:ring-blue-500" checked>
                                <div>
                                    <div class="font-semibold text-gray-800">
                                        <i class="fas fa-layer-group mr-2 text-blue-600"></i>
                                        Category
                                    </div>
                                    <div class="text-sm text-gray-600">Search items by product categories</div>
                                </div>
                            </label>
                        </div>
                        <div class="bg-white rounded-lg p-4 border border-gray-200">
                            <label class="flex items-center cursor-pointer">
                                <input type="radio" name="search_type" value="wo_items" class="mr-3 text-blue-600 focus:ring-blue-500">
                                <div>
                                    <div class="font-semibold text-gray-800">
                                        <i class="fas fa-cogs mr-2 text-green-600"></i>
                                        WO Items
                                    </div>
                                    <div class="text-sm text-gray-600">Search items related to specific work orders</div>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- Search Filters -->
                <div class="flex items-center space-x-6 mb-4">
                    <div class="flex-1">
                        <label class="block text-sm font-semibold text-gray-700 mb-2">
                            <i class="fas fa-layer-group mr-2 text-blue-600"></i>
                            Category
                        </label>
                        <select id="category-filter" class="sap-input w-64">
                            <option value="">All Categories</option>
                            <!-- Categories will be loaded via API -->
                        </select>
                    </div>
                    <div class="flex-1">
                        <label class="block text-sm font-semibold text-gray-700 mb-2">
                            <i class="fas fa-map-marker-alt mr-2 text-blue-600"></i>
                            Location
                        </label>
                        <select id="location-filter" class="sap-input w-64">
                            <option value="">All Locations</option>
                            <!-- Locations will be loaded via API -->
                        </select>
                    </div>
                    <div class="pt-8">
                        <button id="search-btn" class="sap-button-primary">
                            <i class="fas fa-search mr-2"></i>
                            Search Items
                        </button>
                    </div>
                </div>
                
                <!-- Bulk Actions Row -->
                <div class="flex items-center justify-between bg-blue-50 rounded-lg p-4 border border-blue-200">
                    <div class="flex items-center space-x-4">
                        <label class="flex items-center">
                            <input type="checkbox" id="select-all-items" class="rounded text-blue-600 focus:ring-blue-500 mr-2">
                            <span class="text-sm font-medium text-gray-700">Select All Items</span>
                        </label>
                        <span id="selected-items-count" class="text-sm text-gray-600">0 items selected</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="flex items-center space-x-2">
                            <label class="text-sm font-medium text-gray-700">Default Qty:</label>
                            <input type="number" id="bulk-quantity" class="w-20 px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" value="1" min="0.01" step="0.01">
                        </div>
                        <button id="add-selected-items" class="sap-button-primary" disabled>
                            <i class="fas fa-plus mr-2"></i>
                            Add Selected Items
                        </button>
                        <button id="clear-selection" class="sap-button-secondary">
                            <i class="fas fa-times mr-2"></i>
                            Clear Selection
                        </button>
                        <button onclick="showAutomationHelp()" class="sap-button-secondary">
                            <i class="fas fa-question-circle mr-2"></i>
                            Help
                        </button>
                    </div>
                </div>
            </div>

            <!-- Items Table -->
            <div class="sap-table-container">
                <table class="sap-table">
                    <thead class="sap-table-header">
                        <tr>
                            <th class="w-12">
                                <input type="checkbox" id="header-checkbox" class="rounded text-blue-600 focus:ring-blue-500">
                            </th>
                            <th class="w-16">SN</th>
                            <th class="w-32">Item Code</th>
                            <th class="w-64">Description</th>
                            <th class="w-20">UOM</th>
                            <th class="w-24">Stock Qty</th>
                            <th class="w-32">BG/WO Type</th>
                            <th class="w-32">Department/WO</th>
                            <th class="w-24">Req. Qty</th>
                            <th class="w-32">Remarks</th>
                            <th class="w-20">Action</th>
                        </tr>
                    </thead>
                    <tbody id="items-table-body">
                        <!-- Items will be loaded here -->
                        <tr>
                            <td colspan="10" class="sap-table-cell text-center py-12 text-gray-500">
                                <div class="flex flex-col items-center">
                                    <i class="fas fa-search text-4xl text-gray-300 mb-4"></i>
                                    <p class="text-lg font-medium">Click "Search Items" to load inventory</p>
                                    <p class="text-sm">Use filters above to narrow down results</p>
                                    <p class="text-xs mt-2 text-blue-600">💡 Tip: Use checkboxes to select multiple items for bulk addition</p>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div id="pagination-container" class="sap-pagination hidden">
                <div class="flex flex-1 justify-between sm:hidden">
                    <button id="prev-mobile" class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
                        Previous
                    </button>
                    <button id="next-mobile" class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
                        Next
                    </button>
                </div>
                <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Showing <span id="page-start">0</span> to <span id="page-end">0</span> of <span id="total-items">0</span> results
                        </p>
                    </div>
                    <div>
                        <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" id="pagination-links">
                            <!-- Pagination links will be generated here -->
                        </nav>
                    </div>
                </div>
            </div>
        </div>

        <!-- Selected Items Tab -->
        <div id="selected-items-content" class="p-8 hidden">
            <div class="mb-6">
                <h3 class="text-2xl font-bold text-gray-900 flex items-center">
                    <i class="fas fa-clipboard-list mr-3 text-blue-600"></i>
                    Selected Items for MRS
                </h3>
                <p class="text-gray-600 mt-2">Review and modify your selected items before creating the Material Requisition Slip</p>
            </div>

            <!-- Selected Items Table -->
            <div class="sap-table-container mb-8">
                <table class="sap-table">
                    <thead class="sap-table-header">
                        <tr>
                            <th class="w-16">SN</th>
                            <th class="w-32">Item Code</th>
                            <th class="w-64">Description</th>
                            <th class="w-20">UOM</th>
                            <th class="w-24">Available Stock</th>
                            <th class="w-24">BG/WO Type</th>
                            <th class="w-32">Department/WO</th>
                            <th class="w-24">Requested Qty</th>
                            <th class="w-32">Remarks</th>
                            <th class="w-20">Action</th>
                        </tr>
                    </thead>
                    <tbody id="selected-items-table-body">
                        <tr>
                            <td colspan="10" class="sap-table-cell text-center py-12 text-gray-500">
                                <div class="flex flex-col items-center">
                                    <i class="fas fa-shopping-cart text-4xl text-gray-300 mb-4"></i>
                                    <p class="text-lg font-medium">No items selected yet</p>
                                    <p class="text-sm">Go to Item Master tab to add items to your requisition</p>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- MRS Creation Form -->
            <div class="sap-form-section">
                <h4 class="text-xl font-bold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-file-alt sap-form-icon"></i>
                    Material Requisition Information
                </h4>
                
                <form id="mrs-form" method="post" action="{% url 'inventory:mrs_create' %}">
                    {% csrf_token %}
                    
                    <div class="sap-form-grid mb-6">
                        <div>
                            <label class="sap-form-label">
                                <i class="fas fa-calendar sap-form-icon"></i>
                                MRS Date *
                            </label>
                            <input type="date" name="mrs_date" required class="sap-input">
                        </div>
                        <div>
                            <label class="sap-form-label">
                                <i class="fas fa-clock sap-form-icon"></i>
                                Required Date *
                            </label>
                            <input type="date" name="required_date" required class="sap-input">
                        </div>
                        <div>
                            <label class="sap-form-label">
                                <i class="fas fa-exclamation-triangle sap-form-icon"></i>
                                Priority *
                            </label>
                            <select name="priority" required class="sap-input">
                                <option value="">Select Priority</option>
                                <option value="LOW">🟢 Low</option>
                                <option value="NORMAL">🟡 Normal</option>
                                <option value="HIGH">🟠 High</option>
                                <option value="URGENT">🔴 Urgent</option>
                            </select>
                        </div>
                        <div>
                            <label class="sap-form-label">
                                <i class="fas fa-building sap-form-icon"></i>
                                Department ID
                            </label>
                            <input type="text" name="department_id" class="sap-input" placeholder="e.g., PROD-001">
                        </div>
                        <div>
                            <label class="sap-form-label">
                                <i class="fas fa-tasks sap-form-icon"></i>
                                Work Order Number
                            </label>
                            <input type="text" name="work_order_number" class="sap-input" placeholder="e.g., WO-2024-001">
                        </div>
                        <div>
                            <label class="sap-form-label">
                                <i class="fas fa-project-diagram sap-form-icon"></i>
                                Project Code
                            </label>
                            <input type="text" name="project_code" class="sap-input" placeholder="e.g., PROJ-2024-A">
                        </div>
                    </div>

                    <div class="mb-8">
                        <label class="sap-form-label">
                            <i class="fas fa-comment sap-form-icon"></i>
                            Remarks
                        </label>
                        <textarea name="remarks" rows="4" class="sap-input" 
                                  placeholder="Enter any additional notes or special instructions for this material requisition..."></textarea>
                    </div>

                    <!-- Hidden field for line items -->
                    <input type="hidden" id="line-items-data" name="line_items_data" value="">

                    <!-- Action Buttons -->
                    <div class="sap-submit-section">
                        <div class="sap-form-status">
                            <i class="fas fa-info-circle mr-2 text-blue-500"></i>
                            <span id="form-status">Select items from Item Master tab to enable submission</span>
                        </div>
                        
                        <div class="sap-button-group">
                            <a href="{% url 'inventory:mrs_list' %}" class="sap-button-secondary">
                                <i class="fas fa-times mr-2"></i>
                                Cancel
                            </a>
                            <button type="submit" id="create-mrs-btn" disabled class="sap-button-primary disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-plus mr-2"></i>
                                Create Material Requisition
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Global variables
let currentPage = 1;
let totalPages = 1;
let selectedItems = [];
let allItems = [];
let currentSearchType = 'category';

// Tab switching functionality
function switchTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.sap-tab-button').forEach(btn => btn.classList.remove('active'));
    document.getElementById(tabName + '-tab').classList.add('active');
    
    // Update content
    document.getElementById('item-master-content').classList.toggle('hidden', tabName !== 'item-master');
    document.getElementById('selected-items-content').classList.toggle('hidden', tabName !== 'selected-items');
    
    if (tabName === 'selected-items') {
        updateSelectedItemsTable();
        updateFormStatus();
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadCategories();
    loadLocations();
    loadSelectedItems(); // Load existing temp items
    
    // Set default dates
    const today = new Date().toISOString().split('T')[0];
    document.querySelector('input[name="mrs_date"]').value = today;
    document.querySelector('input[name="required_date"]').value = today;
    
    // Bind search button
    document.getElementById('search-btn').addEventListener('click', searchItems);
    
    // Bind form submission
    document.getElementById('mrs-form').addEventListener('submit', handleFormSubmit);
    
    // Bind tab switching
    document.getElementById('item-master-tab').addEventListener('click', () => switchTab('item-master'));
    document.getElementById('selected-items-tab').addEventListener('click', () => switchTab('selected-items'));
    
    // Bind bulk selection events
    document.getElementById('select-all-items').addEventListener('change', toggleSelectAll);
    document.getElementById('add-selected-items').addEventListener('click', addSelectedItems);
    document.getElementById('clear-selection').addEventListener('click', clearSelection);
    document.getElementById('header-checkbox').addEventListener('change', toggleSelectAll);
    
    // Bind search type selection events
    document.querySelectorAll('input[name="search_type"]').forEach(radio => {
        radio.addEventListener('change', handleSearchTypeChange);
    });
});

// Load categories
function loadCategories() {
    fetch('{% url "inventory:categories_api" %}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('category-filter');
                select.innerHTML = '<option value="">All Categories</option>';
                data.categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => console.error('Error loading categories:', error));
}

// Load locations
function loadLocations() {
    fetch('{% url "inventory:locations_api" %}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('location-filter');
                select.innerHTML = '<option value="">All Locations</option>';
                data.locations.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location.id;
                    option.textContent = location.display_name;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading locations:', error);
            // Fallback to basic locations if API fails
            const select = document.getElementById('location-filter');
            select.innerHTML = '<option value="">All Locations</option><option value="1">A-1</option><option value="2">A-2</option>';
        });
}

// Search items
function searchItems(page = 1) {
    const category = document.getElementById('category-filter').value;
    const location = document.getElementById('location-filter').value;
    
    // Ensure page is a valid number
    if (typeof page !== 'number' || isNaN(page) || page < 1) {
        page = 1;
    }
    
    const params = new URLSearchParams();
    params.append('page', page.toString());
    if (category) params.append('category', category);
    if (location) params.append('location', location);
    params.append('limit', '20');
    params.append('search_type', currentSearchType);
    
    const tbody = document.getElementById('items-table-body');
    tbody.innerHTML = '<tr><td colspan="10" class="sap-table-cell text-center py-8 text-gray-500">Loading...</td></tr>';
    
    fetch(`{% url "inventory:item_search_api" %}?${params}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                allItems = data.results || [];
                currentPage = page;
                totalPages = Math.ceil((data.total_count || 0) / 20);
                updateItemsTable();
                updatePagination(data.total_count || 0, page);
            } else {
                throw new Error(data.error || 'Unknown error occurred');
            }
        })
        .catch(error => {
            console.error('Error searching items:', error);
            tbody.innerHTML = '<tr><td colspan="10" class="sap-table-cell text-center py-8 text-red-500">Error loading items. Please try again.</td></tr>';
            showNotification('Error loading items: ' + error.message, 'error');
        });
}

// Update items table
function updateItemsTable() {
    const tbody = document.getElementById('items-table-body');
    tbody.innerHTML = '';
    
    if (allItems.length === 0) {
        tbody.innerHTML = '<tr><td colspan="10" class="sap-table-cell text-center py-8 text-gray-500">No items found</td></tr>';
        return;
    }
    
    allItems.forEach((item, index) => {
        const row = document.createElement('tr');
        row.className = 'sap-table-row';
        
        const stockClass = item.available_stock <= 0 ? 'text-red-600' : 
                          (item.min_stock && item.available_stock <= item.min_stock) ? 'text-yellow-600' : 'text-green-600';
        
        row.innerHTML = `
            <td class="sap-table-cell">
                <input type="checkbox" class="item-checkbox rounded text-blue-600 focus:ring-blue-500" data-item-id="${item.id}">
            </td>
            <td class="sap-table-cell">${((currentPage - 1) * 20) + index + 1}</td>
            <td class="sap-table-cell font-medium">${item.item_code}</td>
            <td class="sap-table-cell">${item.description}</td>
            <td class="sap-table-cell">${item.uom || 'PCS'}</td>
            <td class="sap-table-cell ${stockClass}">${item.available_stock}</td>
            <td class="sap-table-cell">
                <select class="w-28 px-2 py-1 text-sm border border-gray-300 rounded" id="bg-wo-type-${item.id}" onchange="toggleBGWOInputs(${item.id})">
                    <option value="">Select Type</option>
                    <option value="BGGroup">BG Group</option>
                    <option value="WorkOrder">Work Order</option>
                </select>
            </td>
            <td class="sap-table-cell">
                <div id="bg-wo-inputs-${item.id}">
                    <input type="text" class="w-28 px-2 py-1 text-sm border border-gray-300 rounded" placeholder="Select Type First" id="bg-wo-value-${item.id}" disabled>
                </div>
            </td>
            <td class="sap-table-cell">
                <input type="number" class="qty-input" min="0.01" step="0.01" max="${item.available_stock}" value="1" id="qty-${item.id}">
            </td>
            <td class="sap-table-cell">
                <input type="text" class="w-24 px-2 py-1 text-sm border border-gray-300 rounded" placeholder="Remarks" id="remarks-${item.id}">
            </td>
            <td class="sap-table-cell">
                <button class="sap-add-btn" id="add-btn-${item.id}" onclick="addItem(${item.id})">Add</button>
            </td>
        `;
        
        tbody.appendChild(row);
    });
    
    // Bind checkbox events for the new items
    bindCheckboxEvents();
}

// Handle search type selection change
function handleSearchTypeChange(event) {
    currentSearchType = event.target.value;
    
    // Clear current search results when switching search types
    const tbody = document.getElementById('items-table-body');
    tbody.innerHTML = '<tr><td colspan="10" class="sap-table-cell text-center py-12 text-gray-500"><div class="flex flex-col items-center"><i class="fas fa-search text-4xl text-gray-300 mb-4"></i><p class="text-lg font-medium">Click "Search Items" to load inventory</p><p class="text-sm">Search type changed to: ' + (currentSearchType === 'category' ? 'Category' : 'WO Items') + '</p></div></td></tr>';
    
    // Hide pagination
    document.getElementById('pagination-container').classList.add('hidden');
    
    showNotification('Search type changed to: ' + (currentSearchType === 'category' ? 'Category-based search' : 'Work Order Items search'), 'info');
}

// Toggle BG Group/Work Order inputs based on selection
function toggleBGWOInputs(itemId) {
    const typeSelect = document.getElementById(`bg-wo-type-${itemId}`);
    const valueInput = document.getElementById(`bg-wo-value-${itemId}`);
    const selectedType = typeSelect.value;
    
    if (selectedType === 'BGGroup') {
        valueInput.placeholder = 'Enter Department';
        valueInput.disabled = false;
        valueInput.value = '';
    } else if (selectedType === 'WorkOrder') {
        valueInput.placeholder = 'Enter WO Number';
        valueInput.disabled = false;
        valueInput.value = '';
    } else {
        valueInput.placeholder = 'Select Type First';
        valueInput.disabled = true;
        valueInput.value = '';
    }
}

// Bind checkbox events for item selection
function bindCheckboxEvents() {
    document.querySelectorAll('.item-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', handleItemSelection);
    });
}

// Handle individual item checkbox selection
function handleItemSelection() {
    updateSelectionCount();
    updateBulkActionButtons();
}

// Toggle select all items
function toggleSelectAll(event) {
    const isChecked = event.target.checked;
    document.querySelectorAll('.item-checkbox').forEach(checkbox => {
        checkbox.checked = isChecked;
    });
    updateSelectionCount();
    updateBulkActionButtons();
}

// Update selection count display
function updateSelectionCount() {
    const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
    const count = checkedBoxes.length;
    document.getElementById('selected-items-count').textContent = `${count} item${count !== 1 ? 's' : ''} selected`;
    
    // Update header checkbox state
    const allCheckboxes = document.querySelectorAll('.item-checkbox');
    const headerCheckbox = document.getElementById('header-checkbox');
    const selectAllCheckbox = document.getElementById('select-all-items');
    
    if (count === 0) {
        headerCheckbox.indeterminate = false;
        headerCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (count === allCheckboxes.length) {
        headerCheckbox.indeterminate = false;
        headerCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        headerCheckbox.indeterminate = true;
        headerCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
        selectAllCheckbox.checked = false;
    }
}

// Update bulk action button states
function updateBulkActionButtons() {
    const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
    const addSelectedBtn = document.getElementById('add-selected-items');
    
    if (checkedBoxes.length > 0) {
        addSelectedBtn.disabled = false;
        addSelectedBtn.classList.remove('opacity-50', 'cursor-not-allowed');
    } else {
        addSelectedBtn.disabled = true;
        addSelectedBtn.classList.add('opacity-50', 'cursor-not-allowed');
    }
}

// Add multiple selected items simultaneously
function addSelectedItems() {
    const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
    const bulkQuantity = parseFloat(document.getElementById('bulk-quantity').value) || 1;
    let addedCount = 0;
    let skippedCount = 0;
    
    checkedBoxes.forEach(checkbox => {
        const itemId = parseInt(checkbox.dataset.itemId);
        const item = allItems.find(i => i.id === itemId);
        
        if (!item) return;
        
        // Check if already selected
        if (selectedItems.find(si => si.id === itemId)) {
            skippedCount++;
            return;
        }
        
        // Get values from form
        const qtyInput = document.getElementById(`qty-${itemId}`);
        const remarksInput = document.getElementById(`remarks-${itemId}`);
        const bgWOTypeSelect = document.getElementById(`bg-wo-type-${itemId}`);
        const bgWOValueInput = document.getElementById(`bg-wo-value-${itemId}`);
        
        const quantity = qtyInput ? parseFloat(qtyInput.value) : bulkQuantity;
        const remarks = remarksInput ? remarksInput.value : '';
        const bgWOType = bgWOTypeSelect ? bgWOTypeSelect.value : '';
        const bgWOValue = bgWOValueInput ? bgWOValueInput.value.trim() : '';
        
        // Validate BG/WO Type and Value
        if (!bgWOType) {
            showNotification(`Please select BG Group or Work Order type for ${item.item_code}`, 'warning');
            skippedCount++;
            return;
        }
        
        if (!bgWOValue) {
            const fieldName = bgWOType === 'BGGroup' ? 'Department' : 'Work Order Number';
            showNotification(`Please enter ${fieldName} for ${item.item_code}`, 'warning');
            skippedCount++;
            return;
        }
        
        // Validate quantity
        if (isNaN(quantity) || quantity <= 0 || quantity > item.available_stock) {
            showNotification(`Invalid quantity for ${item.item_code}. Skipping item.`, 'warning');
            skippedCount++;
            return;
        }
        
        // Add to selected items
        selectedItems.push({
            id: item.id,
            item_code: item.item_code,
            description: item.description,
            uom: item.uom || 'PCS',
            available_stock: item.available_stock,
            requested_quantity: quantity,
            remarks: remarks,
            bg_wo_type: bgWOType,
            bg_wo_value: bgWOValue
        });
        
        addedCount++;
        
        // Clear the checkbox and reset form values
        checkbox.checked = false;
        if (qtyInput) qtyInput.value = 1;
        if (remarksInput) remarksInput.value = '';
        if (bgWOTypeSelect) bgWOTypeSelect.value = '';
        if (bgWOValueInput) {
            bgWOValueInput.value = '';
            bgWOValueInput.disabled = true;
            bgWOValueInput.placeholder = 'Select Type First';
        }
    });
    
    // Update UI
    updateFormStatus();
    updateSelectionCount();
    updateBulkActionButtons();
    
    // Show notification
    if (addedCount > 0) {
        showNotification(`Successfully added ${addedCount} item${addedCount !== 1 ? 's' : ''} to requisition`, 'success');
        if (skippedCount > 0) {
            showNotification(`${skippedCount} item${skippedCount !== 1 ? 's' : ''} were already selected`, 'warning');
        }
    } else {
        showNotification('No items were added. Please check quantities and selections.', 'warning');
    }
}

// Clear all selections
function clearSelection() {
    document.querySelectorAll('.item-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    document.getElementById('header-checkbox').checked = false;
    document.getElementById('header-checkbox').indeterminate = false;
    document.getElementById('select-all-items').checked = false;
    document.getElementById('select-all-items').indeterminate = false;
    updateSelectionCount();
    updateBulkActionButtons();
    showNotification('Selection cleared', 'info');
}

// Load selected items from server
function loadSelectedItems() {
    fetch('{% url "inventory:get_mrs_temp_items" %}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                selectedItems = data.items || [];
                updateFormStatus();
                if (document.getElementById('selected-items-content').classList.contains('hidden') === false) {
                    updateSelectedItemsTable();
                }
            }
        })
        .catch(error => {
            console.error('Error loading selected items:', error);
        });
}

// Add item to selection via AJAX
function addItem(itemId) {
    try {
        const item = allItems.find(i => i.id === itemId);
        if (!item) {
            showNotification('Item not found', 'error');
            return;
        }
        
        const qtyInput = document.getElementById(`qty-${itemId}`);
        const remarksInput = document.getElementById(`remarks-${itemId}`);
        const bgWOTypeSelect = document.getElementById(`bg-wo-type-${itemId}`);
        const bgWOValueInput = document.getElementById(`bg-wo-value-${itemId}`);
        
        if (!qtyInput) {
            showNotification('Quantity input not found', 'error');
            return;
        }
        
        // Validate BG/WO Type selection
        if (!bgWOTypeSelect.value) {
            showNotification('Please select BG Group or Work Order type', 'error');
            return;
        }
        
        // Validate BG/WO Value input
        if (!bgWOValueInput.value.trim()) {
            const fieldName = bgWOTypeSelect.value === 'BGGroup' ? 'Department' : 'Work Order Number';
            showNotification(`Please enter ${fieldName}`, 'error');
            return;
        }
        
        const quantity = parseFloat(qtyInput.value);
        if (isNaN(quantity) || quantity <= 0 || quantity > item.available_stock) {
            showNotification(`Please enter a valid quantity (0.01 - ${item.available_stock})`, 'error');
            return;
        }
        
        // Prepare data for server
        const itemData = {
            item_id: item.id,
            quantity: quantity,
            remarks: remarksInput ? remarksInput.value : '',
            bg_wo_type: bgWOTypeSelect.value,
            bg_wo_value: bgWOValueInput.value.trim()
        };
        
        // Disable add button during request
        const addButton = document.querySelector(`#add-btn-${itemId}`);
        if (addButton) {
            addButton.disabled = true;
            addButton.textContent = 'Adding...';
        }
        
        // Send AJAX request to add item to temp storage
        fetch('{% url "inventory:add_mrs_temp_item" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify(itemData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Add to local selectedItems array
                selectedItems.push({
                    temp_id: data.item.id,  // Server will return temp ID
                    id: item.id,
                    item_code: item.item_code,
                    description: item.description,
                    uom: item.uom || 'PCS',
                    available_stock: item.available_stock,
                    requested_quantity: quantity,
                    remarks: itemData.remarks,
                    bg_wo_type: itemData.bg_wo_type,
                    bg_wo_value: itemData.bg_wo_value
                });
                
                // Update form status and enable create button
                updateFormStatus();
                
                // Clear inputs
                qtyInput.value = 1;
                if (remarksInput) remarksInput.value = '';
                bgWOTypeSelect.value = '';
                bgWOValueInput.value = '';
                bgWOValueInput.disabled = true;
                bgWOValueInput.placeholder = 'Select Type First';
                
                showNotification(data.message || `${item.item_code} added to selection`, 'success');
            } else {
                showNotification(data.error || 'Failed to add item', 'error');
            }
        })
        .catch(error => {
            console.error('Error adding item:', error);
            showNotification('Error adding item: ' + error.message, 'error');
        })
        .finally(() => {
            // Re-enable add button
            if (addButton) {
                addButton.disabled = false;
                addButton.textContent = 'Add';
            }
        });
        
    } catch (error) {
        console.error('Error adding item:', error);
        showNotification('Error adding item: ' + error.message, 'error');
    }
}

// Update selected items table
function updateSelectedItemsTable() {
    const tbody = document.getElementById('selected-items-table-body');
    tbody.innerHTML = '';
    
    if (selectedItems.length === 0) {
        tbody.innerHTML = '<tr><td colspan="10" class="sap-table-cell text-center py-8 text-gray-500"><div class="flex flex-col items-center"><i class="fas fa-shopping-cart text-4xl text-gray-300 mb-4"></i><p class="text-lg font-medium">No items selected yet</p><p class="text-sm">Go to Item Master tab to add items to your requisition</p></div></td></tr>';
        return;
    }
    
    selectedItems.forEach((item, index) => {
        const row = document.createElement('tr');
        row.className = 'sap-table-row';
        
        const bgWoTypeDisplay = item.bg_wo_type === 'BGGroup' ? 'BG Group' : 'Work Order';
        const bgWoValueDisplay = item.bg_wo_value || '';
        
        row.innerHTML = `
            <td class="sap-table-cell">${index + 1}</td>
            <td class="sap-table-cell font-medium">${item.item_code}</td>
            <td class="sap-table-cell">${item.description}</td>
            <td class="sap-table-cell">${item.uom}</td>
            <td class="sap-table-cell">${item.available_stock}</td>
            <td class="sap-table-cell">
                <span class="px-2 py-1 text-xs font-medium rounded ${item.bg_wo_type === 'BGGroup' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}">${bgWoTypeDisplay}</span>
            </td>
            <td class="sap-table-cell font-medium">${bgWoValueDisplay}</td>
            <td class="sap-table-cell">
                <input type="number" class="qty-input" min="0.01" step="0.01" max="${item.available_stock}" value="${item.requested_quantity}" onchange="updateSelectedQuantity(${index}, this.value)">
            </td>
            <td class="sap-table-cell">
                <input type="text" class="w-24 px-2 py-1 text-sm border border-gray-300 rounded" value="${item.remarks}" onchange="updateSelectedRemarks(${index}, this.value)">
            </td>
            <td class="sap-table-cell">
                <button class="sap-button-danger" onclick="removeSelectedItem(${index})">Remove</button>
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// Update selected item quantity
function updateSelectedQuantity(index, quantity) {
    const qty = parseFloat(quantity);
    if (qty > 0 && qty <= selectedItems[index].available_stock) {
        selectedItems[index].requested_quantity = qty;
    }
}

// Update selected item remarks
function updateSelectedRemarks(index, remarks) {
    selectedItems[index].remarks = remarks;
}

// Remove selected item via AJAX
function removeSelectedItem(index) {
    const item = selectedItems[index];
    if (!item) return;
    
    // If item has temp_id, remove from server
    if (item.temp_id) {
        fetch('{% url "inventory:remove_mrs_temp_item" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({ temp_id: item.temp_id })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove from local array
                selectedItems.splice(index, 1);
                updateFormStatus();
                updateSelectedItemsTable();
                showNotification(data.message || 'Item removed from requisition', 'success');
            } else {
                showNotification(data.error || 'Failed to remove item', 'error');
            }
        })
        .catch(error => {
            console.error('Error removing item:', error);
            showNotification('Error removing item: ' + error.message, 'error');
        });
    } else {
        // Remove from local array only (for items not yet saved to server)
        selectedItems.splice(index, 1);
        updateFormStatus();
        updateSelectedItemsTable();
    }
}

// Update pagination
function updatePagination(totalCount, currentPageNum) {
    const container = document.getElementById('pagination-container');
    container.classList.remove('hidden');
    
    const startItem = ((currentPageNum - 1) * 20) + 1;
    const endItem = Math.min(currentPageNum * 20, totalCount);
    
    document.getElementById('page-start').textContent = startItem;
    document.getElementById('page-end').textContent = endItem;
    document.getElementById('total-items').textContent = totalCount;
    
    // Generate pagination links
    const linksContainer = document.getElementById('pagination-links');
    linksContainer.innerHTML = '';
    
    // Previous button
    if (currentPageNum > 1) {
        const prevBtn = document.createElement('button');
        prevBtn.className = 'relative inline-flex items-center rounded-l-md border border-gray-300 bg-white px-2 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50';
        prevBtn.innerHTML = 'Previous';
        prevBtn.addEventListener('click', function(e) {
            e.preventDefault();
            searchItems(currentPageNum - 1);
        });
        linksContainer.appendChild(prevBtn);
    }
    
    // Page numbers (simplified - show current and adjacent pages)
    const startPage = Math.max(1, currentPageNum - 2);
    const endPage = Math.min(totalPages, currentPageNum + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.className = `relative inline-flex items-center border px-4 py-2 text-sm font-medium ${
            i === currentPageNum ? 'bg-indigo-50 border-indigo-500 text-indigo-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
        }`;
        pageBtn.textContent = i;
        const pageNumber = i; // Capture the page number in closure
        pageBtn.addEventListener('click', function(e) {
            e.preventDefault();
            searchItems(pageNumber);
        });
        linksContainer.appendChild(pageBtn);
    }
    
    // Next button
    if (currentPageNum < totalPages) {
        const nextBtn = document.createElement('button');
        nextBtn.className = 'relative inline-flex items-center rounded-r-md border border-gray-300 bg-white px-2 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50';
        nextBtn.innerHTML = 'Next';
        nextBtn.addEventListener('click', function(e) {
            e.preventDefault();
            searchItems(currentPageNum + 1);
        });
        linksContainer.appendChild(nextBtn);
    }
}

// Handle form submission
function handleFormSubmit(e) {
    e.preventDefault();
    
    if (selectedItems.length === 0) {
        alert('Please select at least one item');
        return;
    }
    
    // Add selected items data to hidden field
    document.getElementById('line-items-data').value = JSON.stringify(selectedItems);
    
    // Submit form
    e.target.submit();
}

// Update form status and button state
function updateFormStatus() {
    const createBtn = document.getElementById('create-mrs-btn');
    const statusSpan = document.getElementById('form-status');
    const selectedCount = document.getElementById('selected-count');
    
    selectedCount.textContent = selectedItems.length;
    
    if (selectedItems.length === 0) {
        createBtn.disabled = true;
        createBtn.classList.add('opacity-50', 'cursor-not-allowed');
        statusSpan.innerHTML = 'Select items from Item Master tab to enable submission';
    } else {
        createBtn.disabled = false;
        createBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        statusSpan.innerHTML = `Ready to create MRS with ${selectedItems.length} item${selectedItems.length > 1 ? 's' : ''}`;
    }
}


// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    const typeClasses = {
        'error': 'sap-status-error bg-red-50 border border-red-200',
        'success': 'sap-status-success bg-green-50 border border-green-200',
        'warning': 'sap-status-warning bg-yellow-50 border border-yellow-200',
        'info': 'sap-status-info bg-blue-50 border border-blue-200'
    };
    
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${typeClasses[type] || typeClasses.info}`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${
                type === 'error' ? 'fa-exclamation-triangle' :
                type === 'success' ? 'fa-check-circle' :
                type === 'warning' ? 'fa-exclamation-triangle' :
                'fa-info-circle'
            } mr-2"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>

{% endblock %}