{% extends "core/base.html" %}
{% load static %}

{% block title %}{{ location.location_code }} - Location Details{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-4">
                <li>
                    <div>
                        <a href="{% url 'inventory:location_list' %}" class="text-gray-400 hover:text-gray-500">
                            <svg class="flex-shrink-0 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                            </svg>
                            <span class="sr-only">Home</span>
                        </a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                        </svg>
                        <a href="{% url 'inventory:location_list' %}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">Locations</a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                        </svg>
                        <span class="ml-4 text-sm font-medium text-gray-500">{{ location.location_code }}</span>
                    </div>
                </li>
            </ol>
        </nav>
        
        <div class="mt-2 md:flex md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                    {{ location.location_code }} - {{ location.location_name }}
                </h2>
                <div class="mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:mt-0 sm:space-x-6">
                    <div class="mt-2 flex items-center text-sm text-gray-500">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {% if location.location_type == 'WAREHOUSE' %}bg-purple-100 text-purple-800
                            {% elif location.location_type == 'ZONE' %}bg-yellow-100 text-yellow-800
                            {% else %}bg-red-100 text-red-800{% endif %}">
                            {{ location.get_location_type_display }}
                        </span>
                    </div>
                    <div class="mt-2 flex items-center text-sm text-gray-500">
                        {% if location.is_active %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Active
                            </span>
                        {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Inactive
                            </span>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="mt-4 flex-shrink-0 flex md:mt-0 md:ml-4">
                <a href="{% url 'inventory:location_update' location.pk %}" 
                   class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Edit
                </a>
                <a href="{% url 'inventory:location_delete' location.pk %}" 
                   class="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    Delete
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <!-- Main Information -->
        <div class="lg:col-span-2">
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Location Information</h3>
                    
                    <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Location Code</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ location.location_code }}</dd>
                        </div>
                        
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Location Name</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ location.location_name }}</dd>
                        </div>
                        
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Location Type</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ location.get_location_type_display }}</dd>
                        </div>
                        
                        {% if location.parent_location %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Parent Location</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                <a href="{% url 'inventory:location_detail' location.parent_location.pk %}" 
                                   class="text-indigo-600 hover:text-indigo-900">
                                    {{ location.parent_location.location_code }} - {{ location.parent_location.location_name }}
                                </a>
                            </dd>
                        </div>
                        {% endif %}
                        
                        {% if location.warehouse_code %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Warehouse Code</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ location.warehouse_code }}</dd>
                        </div>
                        {% endif %}
                        
                        {% if location.zone_code %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Zone Code</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ location.zone_code }}</dd>
                        </div>
                        {% endif %}
                        
                        {% if location.bin_code %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Bin Code</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ location.bin_code }}</dd>
                        </div>
                        {% endif %}
                    </dl>
                </div>
            </div>

            <!-- Hierarchy Path -->
            {% if hierarchy_path %}
            <div class="mt-6 bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Location Hierarchy</h3>
                    <div class="flex items-center space-x-2">
                        {% for path_location in hierarchy_path %}
                            {% if not forloop.first %}
                                <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                                </svg>
                            {% endif %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if path_location.location_type == 'WAREHOUSE' %}bg-purple-100 text-purple-800
                                {% elif path_location.location_type == 'ZONE' %}bg-yellow-100 text-yellow-800
                                {% else %}bg-red-100 text-red-800{% endif %}">
                                {% if path_location.pk == location.pk %}
                                    {{ path_location.location_code }}
                                {% else %}
                                    <a href="{% url 'inventory:location_detail' path_location.pk %}">{{ path_location.location_code }}</a>
                                {% endif %}
                            </span>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Child Locations -->
            {% if child_locations %}
            <div class="mt-6 bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Child Locations</h3>
                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                        {% for child in child_locations %}
                        <div class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400">
                            <div class="flex-shrink-0">
                                {% if child.location_type == 'WAREHOUSE' %}
                                    <div class="h-8 w-8 bg-purple-100 rounded-md flex items-center justify-center">
                                        <svg class="h-4 w-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z"></path>
                                        </svg>
                                    </div>
                                {% elif child.location_type == 'ZONE' %}
                                    <div class="h-8 w-8 bg-yellow-100 rounded-md flex items-center justify-center">
                                        <svg class="h-4 w-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                                        </svg>
                                    </div>
                                {% else %}
                                    <div class="h-8 w-8 bg-red-100 rounded-md flex items-center justify-center">
                                        <svg class="h-4 w-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                        </svg>
                                    </div>
                                {% endif %}
                            </div>
                            <div class="flex-1 min-w-0">
                                <a href="{% url 'inventory:location_detail' child.pk %}" class="focus:outline-none">
                                    <span class="absolute inset-0" aria-hidden="true"></span>
                                    <p class="text-sm font-medium text-gray-900">{{ child.location_code }}</p>
                                    <p class="text-sm text-gray-500 truncate">{{ child.location_name }}</p>
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
            <!-- Capacity & Stock -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Capacity & Stock</h3>
                    
                    <dl class="space-y-4">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Current Stock</dt>
                            <dd class="mt-1 text-2xl font-semibold text-gray-900">{{ location.current_stock|floatformat:2 }}</dd>
                        </div>
                        
                        {% if location.capacity %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Capacity</dt>
                            <dd class="mt-1 text-lg text-gray-900">{{ location.capacity|floatformat:2 }}</dd>
                        </div>
                        
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Utilization</dt>
                            <dd class="mt-1">
                                <div class="flex items-center">
                                    <div class="flex-1">
                                        <div class="bg-gray-200 rounded-full h-2">
                                            <div class="bg-indigo-600 h-2 rounded-full" style="width: {{ location.utilization_percentage|floatformat:1 }}%"></div>
                                        </div>
                                    </div>
                                    <div class="ml-2 text-sm text-gray-900">{{ location.utilization_percentage|floatformat:1 }}%</div>
                                </div>
                            </dd>
                        </div>
                        {% endif %}
                    </dl>
                </div>
            </div>

            <!-- System Information -->
            <div class="mt-6 bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">System Information</h3>
                    
                    <dl class="space-y-4">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Created By</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ location.created_by.get_full_name|default:location.created_by.username }}</dd>
                        </div>
                        
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Created Date</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ location.created_date|date:"M d, Y H:i" }}</dd>
                        </div>
                        
                        {% if location.modified_by %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Modified By</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ location.modified_by.get_full_name|default:location.modified_by.username }}</dd>
                        </div>
                        
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Modified Date</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ location.modified_date|date:"M d, Y H:i" }}</dd>
                        </div>
                        {% endif %}
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}