{% extends "core/base.html" %}
{% load static %}

{% block title %}Machinery - New{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg shadow-lg mb-6">
            <div class="px-6 py-4">
                <h1 class="text-2xl font-bold text-white">Machinery - New</h1>
                <p class="text-blue-100 mt-1">Select an item to convert into machinery</p>
            </div>
        </div>

        <!-- Search Form -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <form method="get" class="grid grid-cols-1 md:grid-cols-4 gap-4" hx-get="{% url 'machinery:get_available_items' %}" hx-target="#items-grid" hx-trigger="change">
                {% csrf_token %}
                
                <!-- Category -->
                <div>
                    <label for="{{ search_form.category.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Category
                    </label>
                    {{ search_form.category }}
                </div>

                <!-- Subcategory -->
                <div id="subcategory-field">
                    <label for="{{ search_form.subcategory.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Sub Category
                    </label>
                    {{ search_form.subcategory }}
                </div>

                <!-- Search Field -->
                <div>
                    <label for="{{ search_form.search_field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Search By
                    </label>
                    {{ search_form.search_field }}
                </div>

                <!-- Search Value -->
                <div id="search-input">
                    <label for="{{ search_form.search_value.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Search Value
                    </label>
                    {{ search_form.search_value }}
                </div>
            </form>
        </div>

        <!-- Items Grid -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div id="items-grid">
                {% include 'machinery/partials/items_grid.html' %}
            </div>
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
        <div class="mt-6 flex justify-center">
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                {% if page_obj.has_previous %}
                    <a href="?page=1" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        First
                    </a>
                    <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        Previous
                    </a>
                {% endif %}

                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                </span>

                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        Next
                    </a>
                    <a href="?page={{ page_obj.paginator.num_pages }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        Last
                    </a>
                {% endif %}
            </nav>
        </div>
        {% endif %}
    </div>
</div>

<script src="{% static 'js/htmx.min.js' %}"></script>
{% endblock %}