{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .search-result-item {
        transition: all 0.2s ease;
    }
    .search-result-item:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    .result-type-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    .advanced-filter-panel {
        display: none;
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
    }
    .advanced-filter-panel.show {
        display: block;
    }
    .search-suggestions {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #e5e7eb;
        border-top: none;
        border-radius: 0 0 0.375rem 0.375rem;
        max-height: 200px;
        overflow-y: auto;
        z-index: 10;
        display: none;
    }
    .search-suggestions.show {
        display: block;
    }
    .suggestion-item {
        padding: 0.5rem 1rem;
        cursor: pointer;
        border-bottom: 1px solid #f3f4f6;
    }
    .suggestion-item:hover {
        background-color: #f3f4f6;
    }
    .search-stats {
        font-size: 0.875rem;
        color: #6b7280;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="mb-6">
        <h1 class="text-3xl font-bold text-gray-900">{{ page_title }}</h1>
        <p class="mt-2 text-sm text-gray-600">
            Search across all inventory data including items, transactions, documents, and locations.
        </p>
    </div>

    <!-- Search Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <form method="get" id="search-form" class="space-y-4">
            <!-- Main Search Input -->
            <div class="relative">
                {{ search_form.query }}
                <div class="search-suggestions" id="search-suggestions"></div>
                <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                    <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Quick Filters -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Search Type</label>
                    {{ search_form.search_type }}
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Document Type</label>
                    {{ search_form.document_type }}
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Sort Order</label>
                    {{ search_form.sort_order }}
                </div>
                <div class="flex items-end">
                    <button type="button" id="toggle-advanced" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        Advanced Filters
                    </button>
                </div>
            </div>

            <!-- Advanced Filters Panel -->
            <div class="advanced-filter-panel" id="advanced-filters">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Advanced Search Options</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Date From</label>
                        {{ search_form.date_from }}
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Date To</label>
                        {{ search_form.date_to }}
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Location Filter</label>
                        {{ search_form.location_filter }}
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Value From</label>
                        {{ search_form.value_from }}
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Value To</label>
                        {{ search_form.value_to }}
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Status Filter</label>
                        {{ search_form.status_filter }}
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div class="flex items-center">
                        {{ search_form.exact_match }}
                        <label class="ml-2 text-sm text-gray-700">{{ search_form.exact_match.help_text }}</label>
                    </div>
                    <div class="flex items-center">
                        {{ search_form.case_sensitive }}
                        <label class="ml-2 text-sm text-gray-700">{{ search_form.case_sensitive.help_text }}</label>
                    </div>
                    <div class="flex items-center">
                        {{ search_form.include_archived }}
                        <label class="ml-2 text-sm text-gray-700">{{ search_form.include_archived.help_text }}</label>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Search Results -->
    {% if show_results %}
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <!-- Results Summary -->
        <div class="border-b border-gray-200 px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="search-stats">
                    <span class="font-medium">{{ search_results.summary.total_results }}</span> results found
                    {% if search_results.summary.total_results > 0 %}
                    (Items: {{ search_results.summary.items_count }}, 
                     Transactions: {{ search_results.summary.transactions_count }}, 
                     Documents: {{ search_results.summary.documents_count }}, 
                     Locations: {{ search_results.summary.locations_count }})
                    {% endif %}
                </div>
                {% if search_results.summary.total_results > 0 %}
                <div class="flex space-x-2">
                    <button type="button" id="save-search-btn" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        Save Search
                    </button>
                    <button type="button" id="export-results-btn" class="text-green-600 hover:text-green-800 text-sm font-medium">
                        Export Results
                    </button>
                </div>
                {% endif %}
            </div>
        </div>

        {% if search_results.error %}
        <!-- Error Message -->
        <div class="p-6">
            <div class="bg-red-50 border border-red-200 rounded-md p-4">
                <div class="flex">
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">Search Error</h3>
                        <div class="mt-2 text-sm text-red-700">
                            {{ search_results.error }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% elif search_results.summary.total_results == 0 %}
        <!-- No Results -->
        <div class="p-6">
            <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.071-2.33"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No results found</h3>
                <p class="mt-1 text-sm text-gray-500">Try adjusting your search criteria or filters.</p>
            </div>
        </div>
        {% else %}
        <!-- Results Tabs -->
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                {% if search_results.items %}
                <button type="button" class="tab-button active border-blue-500 text-blue-600 py-4 px-1 border-b-2 font-medium text-sm" data-tab="items">
                    Items ({{ search_results.summary.items_count }})
                </button>
                {% endif %}
                {% if search_results.transactions %}
                <button type="button" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-4 px-1 border-b-2 font-medium text-sm" data-tab="transactions">
                    Transactions ({{ search_results.summary.transactions_count }})
                </button>
                {% endif %}
                {% if search_results.documents %}
                <button type="button" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-4 px-1 border-b-2 font-medium text-sm" data-tab="documents">
                    Documents ({{ search_results.summary.documents_count }})
                </button>
                {% endif %}
                {% if search_results.locations %}
                <button type="button" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-4 px-1 border-b-2 font-medium text-sm" data-tab="locations">
                    Locations ({{ search_results.summary.locations_count }})
                </button>
                {% endif %}
            </nav>
        </div>

        <!-- Tab Content -->
        <div class="p-6">
            <!-- Items Tab -->
            {% if search_results.items %}
            <div class="tab-content" id="items-tab">
                <div class="grid gap-4">
                    {% for item in search_results.items %}
                    <div class="search-result-item bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-2 mb-2">
                                    <span class="result-type-badge bg-blue-100 text-blue-800 rounded-full">ITEM</span>
                                    <h3 class="text-lg font-medium text-gray-900">{{ item.item_code }}</h3>
                                </div>
                                <p class="text-gray-600 mb-2">{{ item.item_description }}</p>
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                    <div>
                                        <span class="font-medium text-gray-500">Stock:</span>
                                        <span class="text-gray-900">{{ item.total_stock|floatformat:2 }}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-gray-500">Value:</span>
                                        <span class="text-gray-900">₹{{ item.total_value|floatformat:2 }}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-gray-500">Locations:</span>
                                        <span class="text-gray-900">{{ item.location_count }}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-gray-500">Last Transaction:</span>
                                        <span class="text-gray-900">{{ item.last_transaction|date:"M d, Y" }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="ml-4">
                                <a href="{{ item.url }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                    View Details →
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Transactions Tab -->
            {% if search_results.transactions %}
            <div class="tab-content hidden" id="transactions-tab">
                <div class="grid gap-4">
                    {% for transaction in search_results.transactions %}
                    <div class="search-result-item bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-2 mb-2">
                                    <span class="result-type-badge bg-green-100 text-green-800 rounded-full">{{ transaction.document_type }}</span>
                                    <h3 class="text-lg font-medium text-gray-900">{{ transaction.document_number }}</h3>
                                </div>
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                    <div>
                                        <span class="font-medium text-gray-500">Date:</span>
                                        <span class="text-gray-900">{{ transaction.date|date:"M d, Y" }}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-gray-500">Status:</span>
                                        <span class="text-gray-900">{{ transaction.status }}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-gray-500">Value:</span>
                                        <span class="text-gray-900">₹{{ transaction.total_value|floatformat:2 }}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-gray-500">Created By:</span>
                                        <span class="text-gray-900">{{ transaction.created_by|default:"N/A" }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="ml-4">
                                <a href="{{ transaction.url }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                    View Details →
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Documents Tab -->
            {% if search_results.documents %}
            <div class="tab-content hidden" id="documents-tab">
                <div class="grid gap-4">
                    {% for document in search_results.documents %}
                    <div class="search-result-item bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-2 mb-2">
                                    <span class="result-type-badge bg-purple-100 text-purple-800 rounded-full">DOCUMENT</span>
                                    <h3 class="text-lg font-medium text-gray-900">{{ document.title }}</h3>
                                </div>
                                <p class="text-gray-600 mb-2">{{ document.description }}</p>
                            </div>
                            <div class="ml-4">
                                <a href="{{ document.url }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                    View Document →
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Locations Tab -->
            {% if search_results.locations %}
            <div class="tab-content hidden" id="locations-tab">
                <div class="grid gap-4">
                    {% for location in search_results.locations %}
                    <div class="search-result-item bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-2 mb-2">
                                    <span class="result-type-badge bg-orange-100 text-orange-800 rounded-full">LOCATION</span>
                                    <h3 class="text-lg font-medium text-gray-900">{{ location.location_code }}</h3>
                                </div>
                                <p class="text-gray-600 mb-2">{{ location.location_name }}</p>
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                    <div>
                                        <span class="font-medium text-gray-500">Type:</span>
                                        <span class="text-gray-900">{{ location.location_type }}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-gray-500">Warehouse:</span>
                                        <span class="text-gray-900">{{ location.warehouse_code }}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-gray-500">Current Stock:</span>
                                        <span class="text-gray-900">{{ location.current_stock|floatformat:2 }}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-gray-500">Utilization:</span>
                                        <span class="text-gray-900">{{ location.utilization|floatformat:1 }}%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="ml-4">
                                <a href="{{ location.url }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                    View Location →
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
        {% endif %}
    </div>
    {% endif %}
</div>

<!-- Save Search Modal -->
<div id="save-search-modal" class="hidden fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Save Search</h3>
                <form id="save-search-form">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Search Name</label>
                        <input type="text" id="search-name" class="form-input rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 w-full" placeholder="Enter name for this search" required>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <textarea id="search-description" class="form-textarea rounded-md border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 w-full" rows="2" placeholder="Optional description"></textarea>
                    </div>
                    <div class="flex items-center mb-4">
                        <input type="checkbox" id="is-public" class="form-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <label class="ml-2 text-sm text-gray-700">Make this search available to other users</label>
                    </div>
                </form>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="confirm-save-search" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                    Save Search
                </button>
                <button type="button" id="cancel-save-search" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // Update button states
            tabButtons.forEach(btn => {
                btn.classList.remove('active', 'border-blue-500', 'text-blue-600');
                btn.classList.add('border-transparent', 'text-gray-500');
            });
            this.classList.add('active', 'border-blue-500', 'text-blue-600');
            this.classList.remove('border-transparent', 'text-gray-500');
            
            // Update content visibility
            tabContents.forEach(content => content.classList.add('hidden'));
            document.getElementById(targetTab + '-tab').classList.remove('hidden');
        });
    });
    
    // Advanced filters toggle
    const toggleAdvanced = document.getElementById('toggle-advanced');
    const advancedFilters = document.getElementById('advanced-filters');
    
    toggleAdvanced.addEventListener('click', function() {
        advancedFilters.classList.toggle('show');
        this.textContent = advancedFilters.classList.contains('show') ? 'Hide Advanced' : 'Advanced Filters';
    });
    
    // Search suggestions
    const searchInput = document.querySelector('#id_query');
    const suggestionsContainer = document.getElementById('search-suggestions');
    
    searchInput.addEventListener('input', function() {
        const query = this.value.trim();
        if (query.length >= 2) {
            fetchSuggestions(query);
        } else {
            hideSuggestions();
        }
    });
    
    searchInput.addEventListener('blur', function() {
        setTimeout(() => hideSuggestions(), 200);
    });
    
    function fetchSuggestions(query) {
        fetch(`/inventory/api/search-suggestions/?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                displaySuggestions(data.suggestions);
            })
            .catch(error => {
                console.error('Error fetching suggestions:', error);
            });
    }
    
    function displaySuggestions(suggestions) {
        if (suggestions.length === 0) {
            hideSuggestions();
            return;
        }
        
        const html = suggestions.map(suggestion => 
            `<div class="suggestion-item" data-text="${suggestion.text}">
                <div class="flex justify-between items-center">
                    <span>${suggestion.text}</span>
                    <span class="text-xs text-gray-500">${suggestion.category}</span>
                </div>
            </div>`
        ).join('');
        
        suggestionsContainer.innerHTML = html;
        suggestionsContainer.classList.add('show');
        
        // Add click handlers
        suggestionsContainer.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', function() {
                searchInput.value = this.getAttribute('data-text');
                hideSuggestions();
                document.getElementById('search-form').submit();
            });
        });
    }
    
    function hideSuggestions() {
        suggestionsContainer.classList.remove('show');
    }
    
    // Save search functionality
    const saveSearchBtn = document.getElementById('save-search-btn');
    const saveSearchModal = document.getElementById('save-search-modal');
    const confirmSaveBtn = document.getElementById('confirm-save-search');
    const cancelSaveBtn = document.getElementById('cancel-save-search');
    
    if (saveSearchBtn) {
        saveSearchBtn.addEventListener('click', function() {
            saveSearchModal.classList.remove('hidden');
        });
    }
    
    if (cancelSaveBtn) {
        cancelSaveBtn.addEventListener('click', function() {
            saveSearchModal.classList.add('hidden');
        });
    }
    
    if (confirmSaveBtn) {
        confirmSaveBtn.addEventListener('click', function() {
            const formData = {
                search_name: document.getElementById('search-name').value,
                search_description: document.getElementById('search-description').value,
                is_public: document.getElementById('is-public').checked,
                search_params: new URLSearchParams(window.location.search).toString()
            };
            
            fetch('/inventory/api/save-search/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Search saved successfully!');
                    saveSearchModal.classList.add('hidden');
                } else {
                    alert('Error saving search: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error saving search:', error);
                alert('Error saving search');
            });
        });
    }
    
    // Export functionality
    const exportBtn = document.getElementById('export-results-btn');
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            const params = new URLSearchParams(window.location.search);
            params.set('export', 'excel');
            window.location.href = '/inventory/reports/export/?' + params.toString();
        });
    }
    
    // Real-time search (optional)
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            // Uncomment for real-time search
            // if (this.value.trim().length >= 3) {
            //     document.getElementById('search-form').submit();
            // }
        }, 1000);
    });
});
</script>
{% endblock %}